{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000291.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000298.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000306.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000307.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000308.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000309.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000310.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000311.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000312.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000313.png", "low_idx": 45}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "TissueBox", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-7|0|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["tissuebox"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["TissueBox", [-6.660128, -6.660128, -2.5685072, -2.5685072, 2.816065072, 2.816065072]], "coordinateReceptacleObjectId": ["CounterTop", [-11.2, -11.2, -2.376, -2.376, 0.004, 0.004]], "forceVisible": true, "objectId": "TissueBox|-01.67|+00.70|-00.64"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-8|2|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tissuebox", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["TissueBox", [-6.660128, -6.660128, -2.5685072, -2.5685072, 2.816065072, 2.816065072]], "coordinateReceptacleObjectId": ["Drawer", [-5.77998448, -5.77998448, -2.483496664, -2.483496664, 2.1270964, 2.1270964]], "forceVisible": true, "objectId": "TissueBox|-01.67|+00.70|-00.64", "receptacleObjectId": "Drawer|-01.44|+00.53|-00.62"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-11|0|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["tissuebox"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["TissueBox", [-11.21320152, -11.21320152, -2.341014148, -2.341014148, 2.816065072, 2.816065072]], "coordinateReceptacleObjectId": ["CounterTop", [-11.2, -11.2, -2.376, -2.376, 0.004, 0.004]], "forceVisible": true, "objectId": "TissueBox|-02.80|+00.70|-00.59"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-8|2|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tissuebox", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["TissueBox", [-11.21320152, -11.21320152, -2.341014148, -2.341014148, 2.816065072, 2.816065072]], "coordinateReceptacleObjectId": ["Drawer", [-5.77998448, -5.77998448, -2.483496664, -2.483496664, 2.1270964, 2.1270964]], "forceVisible": true, "objectId": "TissueBox|-02.80|+00.70|-00.59", "receptacleObjectId": "Drawer|-01.44|+00.53|-00.62"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "TissueBox|-01.67|+00.70|-00.64"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [114, 92, 141, 140], "mask": [[27417, 8], [27432, 9], [27717, 8], [27732, 9], [28017, 10], [28032, 9], [28317, 10], [28332, 9], [28617, 11], [28631, 10], [28917, 24], [29217, 24], [29516, 25], [29816, 25], [30116, 25], [30416, 25], [30716, 26], [31016, 26], [31316, 26], [31616, 26], [31916, 26], [32216, 26], [32516, 26], [32816, 25], [33115, 26], [33415, 26], [33715, 26], [34015, 26], [34315, 26], [34615, 26], [34915, 26], [35215, 26], [35515, 26], [35815, 26], [36115, 26], [36415, 26], [36714, 27], [37014, 27], [37314, 27], [37614, 27], [37914, 27], [38214, 27], [38514, 27], [38814, 27], [39114, 27], [39415, 26], [39715, 26], [40015, 26], [40315, 26], [40616, 25], [40916, 25], [41216, 25], [41516, 25], [41817, 23]], "point": [127, 115]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.44|+00.53|-00.62"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 151, 90, 182], "mask": [[45000, 84], [45300, 84], [45600, 85], [45900, 85], [46200, 85], [46500, 85], [46800, 86], [47100, 86], [47400, 86], [47700, 86], [48000, 87], [48301, 86], [48601, 86], [48902, 85], [49203, 85], [49503, 85], [49804, 84], [50104, 84], [50405, 84], [50706, 83], [51006, 83], [51307, 82], [51607, 83], [51908, 82], [52209, 81], [52509, 82], [52810, 81], [53110, 81], [53411, 80], [53712, 79], [54012, 79], [54313, 78]], "point": [45, 165]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "TissueBox|-01.67|+00.70|-00.64", "placeStationary": true, "receptacleObjectId": "Drawer|-01.44|+00.53|-00.62"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 151, 88, 248], "mask": [[45001, 81], [45300, 83], [45600, 83], [45900, 83], [46200, 83], [46500, 84], [46800, 84], [47100, 84], [47400, 84], [47700, 85], [48000, 85], [48300, 85], [48600, 85], [48900, 86], [49200, 86], [49500, 86], [49800, 86], [50100, 87], [50400, 87], [50700, 87], [51000, 87], [51300, 88], [51600, 88], [51900, 88], [52200, 89], [52500, 89], [52800, 88], [53100, 88], [53400, 88], [53700, 88], [54000, 87], [54300, 87], [54600, 87], [54900, 87], [55200, 87], [55500, 86], [55800, 86], [56100, 86], [56400, 86], [56700, 86], [57000, 85], [57300, 85], [57600, 85], [57900, 85], [58200, 84], [58500, 84], [58800, 84], [59100, 84], [59400, 84], [59700, 83], [60000, 83], [60300, 83], [60600, 83], [60900, 83], [61200, 82], [61500, 82], [61800, 82], [62100, 82], [62400, 81], [62700, 81], [63000, 81], [63300, 80], [63600, 79], [63900, 79], [64200, 79], [64500, 79], [64800, 79], [65100, 79], [65400, 78], [65700, 78], [66000, 78], [66300, 78], [66600, 78], [66900, 78], [67200, 78], [67500, 77], [67800, 77], [68100, 77], [68400, 77], [68700, 77], [69000, 77], [69300, 76], [69600, 76], [69900, 76], [70200, 76], [70500, 76], [70800, 75], [71100, 75], [71400, 76], [71700, 76], [72000, 75], [72300, 75], [72600, 75], [72900, 75], [73200, 75], [73500, 75], [73800, 75], [74100, 74]], "point": [44, 198]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.44|+00.53|-00.62"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 151, 88, 248], "mask": [[45001, 81], [45300, 83], [45600, 83], [45900, 83], [46200, 83], [46500, 84], [46800, 84], [47100, 84], [47400, 84], [47700, 85], [48000, 85], [48300, 85], [48600, 85], [48900, 86], [49200, 86], [49500, 86], [49800, 86], [50100, 87], [50400, 87], [50700, 87], [51000, 87], [51300, 88], [51600, 17], [51636, 52], [51900, 15], [51936, 52], [52200, 14], [52237, 52], [52500, 13], [52537, 52], [52800, 13], [52838, 50], [53100, 13], [53138, 50], [53400, 12], [53439, 49], [53700, 12], [53739, 49], [54000, 12], [54040, 47], [54300, 12], [54340, 47], [54600, 13], [54640, 47], [54900, 12], [54940, 47], [55200, 12], [55239, 48], [55500, 11], [55539, 47], [55800, 11], [55838, 48], [56100, 10], [56138, 48], [56400, 10], [56438, 48], [56700, 10], [56737, 49], [57000, 9], [57037, 48], [57300, 9], [57336, 49], [57600, 8], [57636, 49], [57900, 8], [57936, 49], [58200, 7], [58235, 49], [58500, 7], [58535, 49], [58800, 6], [58835, 49], [59100, 6], [59134, 50], [59400, 5], [59434, 50], [59700, 5], [59733, 50], [60000, 4], [60033, 50], [60300, 4], [60333, 50], [60600, 5], [60632, 51], [60900, 6], [60932, 51], [61200, 7], [61232, 50], [61500, 7], [61531, 51], [61800, 8], [61831, 51], [62100, 9], [62130, 52], [62400, 9], [62430, 51], [62700, 10], [62730, 51], [63000, 81], [63300, 81], [63600, 81], [63900, 80], [64200, 80], [64500, 80], [64800, 80], [65100, 79], [65400, 79], [65700, 79], [66000, 79], [66300, 79], [66600, 78], [66900, 78], [67200, 78], [67500, 78], [67800, 78], [68100, 77], [68400, 77], [68700, 77], [69000, 77], [69300, 76], [69600, 76], [69900, 76], [70200, 76], [70500, 76], [70800, 75], [71100, 75], [71400, 76], [71700, 76], [72000, 77], [72300, 77], [72600, 78], [72900, 78], [73200, 78], [73500, 78], [73800, 78], [74100, 78]], "point": [44, 198]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "TissueBox|-02.80|+00.70|-00.59"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [151, 103, 177, 152], "mask": [[30751, 25], [31051, 25], [31351, 25], [31651, 25], [31951, 25], [32251, 25], [32551, 25], [32851, 25], [33151, 25], [33451, 25], [33751, 26], [34051, 26], [34351, 26], [34651, 26], [34951, 26], [35251, 26], [35551, 26], [35851, 26], [36151, 26], [36451, 26], [36751, 26], [37051, 26], [37351, 26], [37651, 26], [37951, 26], [38251, 26], [38551, 27], [38851, 27], [39151, 27], [39451, 27], [39751, 27], [40051, 27], [40351, 27], [40651, 27], [40951, 27], [41251, 27], [41551, 27], [41851, 27], [42151, 27], [42451, 27], [42751, 27], [43051, 27], [43351, 27], [43651, 27], [43951, 26], [44251, 26], [44551, 26], [44851, 26], [45151, 26], [45451, 25]], "point": [164, 126]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.44|+00.53|-00.62"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 151, 90, 182], "mask": [[45000, 84], [45300, 84], [45600, 85], [45900, 85], [46200, 85], [46500, 85], [46800, 86], [47100, 86], [47400, 86], [47700, 86], [48000, 87], [48301, 86], [48601, 86], [48902, 85], [49203, 85], [49503, 85], [49804, 84], [50104, 84], [50405, 84], [50706, 83], [51006, 83], [51307, 82], [51607, 83], [51908, 82], [52209, 81], [52509, 82], [52810, 81], [53110, 81], [53411, 80], [53712, 79], [54012, 79], [54313, 78]], "point": [45, 165]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "TissueBox|-02.80|+00.70|-00.59", "placeStationary": true, "receptacleObjectId": "Drawer|-01.44|+00.53|-00.62"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 151, 88, 248], "mask": [[45001, 81], [45300, 83], [45600, 83], [45900, 83], [46200, 83], [46500, 84], [46800, 84], [47100, 84], [47400, 84], [47700, 85], [48000, 85], [48300, 85], [48600, 85], [48900, 86], [49200, 86], [49500, 86], [49800, 86], [50100, 87], [50400, 87], [50700, 87], [51000, 87], [51300, 88], [51600, 17], [51636, 52], [51900, 15], [51936, 52], [52200, 14], [52237, 52], [52500, 13], [52537, 52], [52800, 13], [52838, 50], [53100, 13], [53138, 50], [53400, 12], [53439, 49], [53700, 12], [53739, 49], [54000, 12], [54040, 47], [54300, 12], [54340, 47], [54600, 13], [54640, 47], [54900, 12], [54940, 47], [55200, 12], [55239, 48], [55500, 11], [55539, 47], [55800, 11], [55838, 48], [56100, 10], [56138, 48], [56400, 10], [56438, 48], [56700, 10], [56737, 49], [57000, 9], [57037, 48], [57300, 9], [57336, 49], [57600, 8], [57636, 49], [57900, 8], [57936, 49], [58200, 7], [58235, 49], [58500, 7], [58535, 49], [58800, 6], [58835, 49], [59100, 6], [59134, 50], [59400, 5], [59434, 50], [59700, 5], [59733, 50], [60000, 4], [60033, 50], [60300, 4], [60333, 50], [60600, 5], [60632, 51], [60900, 6], [60932, 51], [61200, 7], [61232, 50], [61500, 7], [61531, 51], [61800, 8], [61831, 51], [62100, 9], [62130, 52], [62400, 9], [62430, 51], [62700, 10], [62730, 51], [63000, 81], [63300, 80], [63600, 79], [63900, 79], [64200, 79], [64500, 79], [64800, 79], [65100, 79], [65400, 78], [65700, 78], [66000, 78], [66300, 78], [66600, 78], [66900, 78], [67200, 78], [67500, 77], [67800, 77], [68100, 77], [68400, 77], [68700, 77], [69000, 77], [69300, 76], [69600, 76], [69900, 76], [70200, 76], [70500, 76], [70800, 75], [71100, 75], [71400, 76], [71700, 76], [72000, 75], [72300, 75], [72600, 75], [72900, 75], [73200, 75], [73500, 75], [73800, 75], [74100, 74]], "point": [44, 198]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.44|+00.53|-00.62"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 151, 88, 248], "mask": [[45001, 81], [45300, 83], [45600, 83], [45900, 83], [46200, 83], [46500, 84], [46800, 84], [47100, 84], [47400, 84], [47700, 85], [48000, 85], [48300, 85], [48600, 85], [48900, 86], [49200, 86], [49500, 86], [49800, 86], [50100, 87], [50400, 87], [50700, 87], [51000, 87], [51300, 88], [51600, 17], [51636, 9], [51663, 25], [51900, 15], [51936, 8], [51964, 24], [52200, 14], [52237, 6], [52264, 25], [52500, 13], [52537, 5], [52564, 25], [52800, 13], [52838, 5], [52865, 23], [53100, 13], [53138, 5], [53165, 23], [53400, 12], [53439, 3], [53466, 22], [53700, 12], [53739, 3], [53766, 22], [54000, 12], [54040, 2], [54066, 21], [54300, 12], [54340, 2], [54367, 20], [54600, 13], [54640, 1], [54666, 21], [54900, 12], [54940, 1], [54966, 21], [55200, 12], [55239, 1], [55266, 21], [55500, 11], [55539, 1], [55565, 21], [55800, 11], [55838, 2], [55865, 21], [56100, 10], [56138, 1], [56165, 21], [56400, 10], [56438, 1], [56465, 21], [56700, 10], [56737, 2], [56764, 22], [57000, 9], [57037, 1], [57064, 21], [57300, 9], [57336, 2], [57364, 21], [57600, 8], [57636, 1], [57663, 22], [57900, 8], [57936, 1], [57963, 22], [58200, 7], [58235, 2], [58263, 21], [58500, 7], [58535, 1], [58563, 21], [58800, 6], [58835, 1], [58862, 22], [59100, 6], [59134, 2], [59162, 22], [59400, 5], [59434, 1], [59462, 22], [59700, 5], [59733, 2], [59761, 22], [60000, 4], [60033, 1], [60061, 22], [60300, 4], [60333, 2], [60361, 22], [60600, 5], [60632, 3], [60661, 22], [60900, 6], [60932, 4], [60960, 23], [61200, 7], [61232, 4], [61260, 22], [61500, 7], [61531, 6], [61560, 22], [61800, 8], [61831, 6], [61859, 23], [62100, 9], [62130, 8], [62159, 23], [62400, 9], [62430, 8], [62459, 22], [62700, 10], [62730, 9], [62759, 22], [63000, 81], [63300, 81], [63600, 81], [63900, 80], [64200, 80], [64500, 80], [64800, 80], [65100, 79], [65400, 79], [65700, 79], [66000, 79], [66300, 79], [66600, 78], [66900, 78], [67200, 78], [67500, 78], [67800, 78], [68100, 77], [68400, 77], [68700, 77], [69000, 77], [69300, 76], [69600, 76], [69900, 76], [70200, 76], [70500, 76], [70800, 75], [71100, 75], [71400, 76], [71700, 76], [72000, 77], [72300, 77], [72600, 78], [72900, 78], [73200, 78], [73500, 78], [73800, 78], [74100, 78]], "point": [36, 194]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan427", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -2.0, "y": 0.902041554, "z": 1.5}, "object_poses": [{"objectName": "Cloth_3b9d62d5", "position": {"x": -2.69483042, "y": 0.176077783, "z": -0.635338247}, "rotation": {"x": 0.0, "y": 180.000046, "z": 0.0}}, {"objectName": "TissueBox_0568c766", "position": {"x": -1.665032, "y": 0.704016268, "z": -0.6421268}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "SprayBottle_13477bc4", "position": {"x": -0.732001066, "y": 0.9513991, "z": -0.722208142}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SprayBottle_13477bc4", "position": {"x": -0.607000947, "y": 0.9514352, "z": -0.748999357}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBar_1b2cf64e", "position": {"x": -2.57564688, "y": 0.7033865, "z": -0.812742531}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "SoapBar_1b2cf64e", "position": {"x": -0.9195008, "y": 0.954476833, "z": -0.7490002}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "DishSponge_b55ab5e2", "position": {"x": -3.60287166, "y": 0.0406776667, "z": 0.595}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_f315e63f", "position": {"x": -1.89268577, "y": 0.7060998, "z": -0.698998749}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "SoapBottle_f315e63f", "position": {"x": -3.15679216, "y": 0.6150794, "z": 1.09410977}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_a528e9d7", "position": {"x": -2.576, "y": 1.683, "z": 2.2447}, "rotation": {"x": 0.0, "y": -6.83018834e-06, "z": 0.0}}, {"objectName": "Towel_08b02c7c", "position": {"x": -3.66, "y": 1.567, "z": -0.459400028}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_014ddfaf", "position": {"x": -0.919500649, "y": 0.954092145, "z": -0.802583456}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "HandTowel_a528e9d7", "position": {"x": -1.275, "y": 1.683, "z": 2.2444}, "rotation": {"x": 0.0, "y": -6.83018834e-06, "z": 0.0}}, {"objectName": "SoapBar_1b2cf64e", "position": {"x": -3.11647344, "y": 0.6123661, "z": 0.4109658}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_d548da56", "position": {"x": -1.38050878, "y": 0.695359349, "z": -0.751498342}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plunger_e84c4ed6", "position": {"x": -1.08817315, "y": 0.0005790219, "z": -0.5260009}, "rotation": {"x": -0.00118385791, "y": 0.000438582065, "z": 0.0007821216}}, {"objectName": "SprayBottle_13477bc4", "position": {"x": -1.665032, "y": 0.7003167, "z": -0.7558711}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "SoapBottle_f315e63f", "position": {"x": -3.21532631, "y": 0.6150794, "z": 1.60999656}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "TissueBox_0568c766", "position": {"x": -2.80330038, "y": 0.704016268, "z": -0.585253537}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "ToiletPaper_6b21fc4b", "position": {"x": -2.80330062, "y": 0.7003167, "z": -0.8127423}, "rotation": {"x": 0.0, "y": 90.0000458, "z": 0.0}}, {"objectName": "PaperTowelRoll_0da11a15", "position": {"x": -0.5445009, "y": 1.06032741, "z": -0.7489992}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ScrubBrush_80c1e216", "position": {"x": -1.041, "y": 0.0010420084, "z": -0.707}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_3b9d62d5", "position": {"x": -3.94826078, "y": 0.0380398631, "z": 0.965374947}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b55ab5e2", "position": {"x": -3.51652431, "y": 0.0406776667, "z": 1.15056241}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 400383282, "scene_num": 427}, "task_id": "trial_T20190909_060953_266728", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "AE861G0AY5RGT_3NVC2EB65TQQIF9SB8OWE39INOBY3L", "high_descs": ["Turn left and walk to the bathroom counter.", "Pick up the box of Kleenex on the left end of the counter.", "Go to the end of the counter.", "Open the top left drawer and place the kleenex and drawer", "Turn right and walk to the far right end of the counter.", "Pick up the box of Kleenex on the far right end of the counter.", "Turn left and walk to the far left end of the counter.", "Open the top left drawer and place the box of Kleenex in drawer next to the other box of Kleenex."], "task_desc": "Place two boxes of Kleenex in a drawer on the counter.", "votes": [1, 1]}, {"assignment_id": "A1OWHPMKE7YAGL_3QFUFYSY91650KB16W1RZNU8VDCF4L", "high_descs": ["Turn around, go forward, turn to the right and go to the counter.", "Pick up the box of tissues that is on the counter.", "Turn around, go forward, turn around and face the counter.", "Put the box of tissues in the top left drawer beneath the counter.", "Turn right and go to the tub, hang a left and go to the counter.", "Pick up the box of tissues that is on the counter. ", "Turn around, go forward, hang a right, go forward, turn to face the counter on the left.", "Put the box of tissues in the top left drawer beneath the counter, to the right of other box of tissues."], "task_desc": "Put two boxes of tissues in a drawer. ", "votes": [1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3WEV0KO0OPJR4LZG5R2J9Q7TXG3DSJ", "high_descs": ["turn left and walk over to the left side of the bathroom cabinets up ahead", "grab a tissue box off of the bathroom cabinet", "turn around to back up a bit and face the bathroom cabinets again", "place the tissue box inside of the left cabinet", "move to the right a bit to face the right side of the bathroom cabinet", "grab the tissue box off of the top of the cabinet there", "move to the left a bit to face the left side of the bathroom cabinet", "place the tissue box inside the same cabinet on the left side of the cabinet"], "task_desc": "place two tissues boxes inside of the bathroom cabinet", "votes": [1, 1]}]}}