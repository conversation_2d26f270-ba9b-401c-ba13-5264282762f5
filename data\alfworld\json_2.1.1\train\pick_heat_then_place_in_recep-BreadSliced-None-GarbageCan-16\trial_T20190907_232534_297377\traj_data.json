{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000160.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000162.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000163.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000165.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000166.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000187.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000188.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000189.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000190.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000191.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000192.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000193.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000194.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000195.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000196.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000197.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000198.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000199.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000200.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000201.png", "low_idx": 34}, {"high_idx": 8, "image_name": "000000202.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000203.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000204.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000205.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000206.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000207.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000208.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000209.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000210.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000211.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000212.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000213.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000214.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000215.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000216.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000217.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000218.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000219.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000220.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000221.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000222.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000223.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000224.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000225.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000226.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000227.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000230.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000231.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000232.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000233.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000234.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000235.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000236.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000237.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000238.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000239.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000240.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000241.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000242.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000243.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000244.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000245.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000246.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000247.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000248.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000249.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000250.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000251.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000252.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000253.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000254.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000255.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000256.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000257.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000258.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000259.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000260.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000261.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000262.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000268.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000269.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000270.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000271.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000272.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000273.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000274.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000275.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000276.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000277.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000278.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000279.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000280.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000281.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000282.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000283.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000284.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000285.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000286.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000287.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000288.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000289.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000290.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000291.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000292.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000293.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000294.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000295.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000296.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000297.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000298.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000299.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000300.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000301.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000302.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000303.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000304.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000305.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000306.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000307.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000308.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000309.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000310.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000311.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000312.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000313.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000314.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000315.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000316.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000317.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000318.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000319.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000320.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000321.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000322.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000323.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000324.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000325.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000326.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000327.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000328.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000329.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000330.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000331.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000332.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000333.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000334.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000335.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000336.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000337.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000338.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000339.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000340.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000341.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000342.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000343.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000344.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000345.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000346.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000347.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000348.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000349.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000350.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000351.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000352.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000353.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000354.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000355.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000356.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000357.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000358.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000359.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000360.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000361.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000362.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000363.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000364.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000365.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000366.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000367.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000368.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000369.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000370.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000371.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000372.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000373.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000374.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000375.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000376.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000377.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000378.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000379.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000380.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000381.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000382.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000383.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000384.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000385.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000386.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000387.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000388.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000389.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000390.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000391.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000392.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000393.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000394.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000395.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000396.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000397.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000398.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000399.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000400.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000401.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000402.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000403.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000404.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000405.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000406.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000407.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000408.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000409.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000410.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000411.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000412.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000413.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000414.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000415.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000416.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000417.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000418.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000419.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000420.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000421.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000422.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000423.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000424.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000425.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000426.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000427.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000428.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000429.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000430.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000431.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000432.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000433.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000434.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000435.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000436.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000437.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000438.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000439.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000440.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000441.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000442.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000443.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000444.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000445.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000446.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000447.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000448.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000449.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000450.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000451.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000452.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000453.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000454.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000455.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000456.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000457.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000458.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000459.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000460.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000461.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000462.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000463.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000464.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000465.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000466.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000467.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000468.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000469.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000470.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000471.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000472.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000473.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000474.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000475.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000476.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000477.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000478.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000479.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000480.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000481.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000482.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000483.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000484.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000485.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000486.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000487.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000488.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000489.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000490.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000491.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000492.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000493.png", "low_idx": 90}, {"high_idx": 11, "image_name": "000000494.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000495.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000496.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000497.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000498.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000499.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000500.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000501.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000502.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000503.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000504.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000505.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000506.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000507.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000508.png", "low_idx": 91}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|4|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [-2.857600212, -2.857600212, 4.41588496, 4.41588496, 3.3676232, 3.3676232]], "coordinateReceptacleObjectId": ["SinkBasin", [-2.8576, -2.8576, 5.3932, 5.3932, 3.224, 3.224]], "forceVisible": true, "objectId": "Knife|-00.71|+00.84|+01.10"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|9|0|1|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [10.96544, 10.96544, 0.23827748, 0.23827748, 4.3403306, 4.3403306]], "forceVisible": true, "objectId": "Bread|+02.74|+01.09|+00.06"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|7|0|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "drawer"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [-2.857600212, -2.857600212, 4.41588496, 4.41588496, 3.3676232, 3.3676232]], "coordinateReceptacleObjectId": ["Drawer", [10.77314, 10.77314, -1.0718684, -1.0718684, 3.3335844, 3.3335844]], "forceVisible": true, "objectId": "Knife|-00.71|+00.84|+01.10", "receptacleObjectId": "Drawer|+02.69|+00.83|-00.27"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|9|0|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [10.96544, 10.96544, 0.23827748, 0.23827748, 4.3403306, 4.3403306]], "coordinateReceptacleObjectId": ["CounterTop", [11.252, 11.252, 2.7, 2.7, 3.972, 3.972]], "forceVisible": true, "objectId": "Bread|+02.74|+01.09|+00.06|BreadSliced_4"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|8|-4|2|0"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.758, 7.758, -7.3052, -7.3052, 6.982419, 6.982419]], "forceVisible": true, "objectId": "Microwave|+01.94|+01.75|-01.83"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-4|13|2|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "garbagecan"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [10.96544, 10.96544, 0.23827748, 0.23827748, 4.3403306, 4.3403306]], "coordinateReceptacleObjectId": ["GarbageCan", [-3.805050612, -3.805050612, 11.30460928, 11.30460928, -0.0039073228, -0.0039073228]], "forceVisible": true, "objectId": "Bread|+02.74|+01.09|+00.06|BreadSliced_4", "receptacleObjectId": "GarbageCan|-00.95|00.00|+02.83"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|-00.71|+00.84|+01.10"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [163, 137, 174, 183], "mask": [[40967, 3], [41266, 5], [41566, 6], [41865, 7], [42165, 6], [42465, 6], [42765, 5], [43065, 5], [43365, 5], [43665, 5], [43965, 5], [44265, 5], [44565, 5], [44865, 5], [45165, 5], [45465, 5], [45765, 5], [46065, 4], [46365, 4], [46665, 4], [46965, 3], [47265, 3], [47565, 4], [47865, 5], [48164, 7], [48464, 11], [48764, 11], [49064, 11], [49364, 11], [49664, 11], [49964, 11], [50264, 11], [50564, 11], [50863, 12], [51163, 12], [51463, 11], [51763, 11], [52063, 11], [52363, 11], [52663, 11], [52963, 11], [53263, 11], [53563, 11], [53863, 11], [54165, 8], [54466, 7], [54769, 4]], "point": [168, 159]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|+02.74|+01.09|+00.06"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [66, 121, 185, 187], "mask": [[36089, 75], [36386, 84], [36683, 91], [36980, 96], [37279, 98], [37578, 101], [37877, 103], [38176, 105], [38475, 106], [38774, 108], [39073, 109], [39372, 111], [39672, 111], [39971, 113], [40271, 113], [40570, 114], [40870, 115], [41170, 115], [41470, 115], [41769, 116], [42069, 116], [42369, 116], [42669, 116], [42969, 117], [43268, 118], [43568, 118], [43868, 118], [44168, 118], [44468, 118], [44767, 119], [45067, 119], [45367, 119], [45667, 119], [45967, 119], [46267, 119], [46566, 120], [46866, 119], [47166, 119], [47466, 119], [47766, 119], [48066, 118], [48367, 117], [48667, 117], [48967, 116], [49267, 116], [49568, 115], [49868, 115], [50169, 114], [50470, 113], [50769, 114], [51069, 114], [51369, 114], [51669, 114], [51969, 114], [52269, 114], [52570, 113], [52870, 113], [53170, 113], [53471, 112], [53771, 111], [54071, 111], [54372, 110], [54672, 110], [54973, 108], [55274, 106], [55575, 102], [55876, 92]], "point": [125, 153]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+02.69|+00.83|-00.27"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [165, 133, 280, 167], "mask": [[39766, 115], [40066, 114], [40366, 114], [40666, 113], [40966, 113], [41266, 112], [41566, 112], [41866, 112], [42166, 111], [42466, 111], [42766, 110], [43066, 110], [43366, 109], [43666, 109], [43966, 108], [44266, 108], [44566, 107], [44865, 108], [45165, 107], [45465, 107], [45765, 106], [46065, 106], [46365, 105], [46665, 105], [46965, 104], [47265, 104], [47565, 104], [47865, 103], [48165, 103], [48465, 102], [48765, 102], [49065, 101], [49365, 101], [49665, 100], [49965, 99]], "point": [222, 149]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|-00.71|+00.84|+01.10", "placeStationary": true, "receptacleObjectId": "Drawer|+02.69|+00.83|-00.27"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [167, 136, 299, 278], "mask": [[40670, 3], [40773, 3], [40969, 108], [41269, 108], [41569, 109], [41869, 109], [42169, 110], [42468, 111], [42768, 112], [43068, 112], [43368, 113], [43668, 113], [43968, 114], [44268, 114], [44568, 115], [44868, 115], [45168, 116], [45468, 117], [45768, 117], [46068, 118], [46368, 118], [46668, 119], [46967, 120], [47268, 120], [47568, 120], [47868, 121], [48168, 121], [48468, 122], [48768, 122], [49068, 123], [49368, 123], [49668, 124], [49968, 124], [50268, 125], [50568, 125], [50868, 126], [51168, 126], [51468, 127], [51768, 127], [52069, 127], [52369, 127], [52669, 128], [52969, 128], [53269, 129], [53569, 129], [53869, 130], [54169, 130], [54469, 131], [54769, 131], [55069, 131], [55369, 131], [55669, 131], [55969, 131], [56269, 131], [56570, 130], [56870, 130], [57170, 130], [57470, 130], [57770, 130], [58070, 130], [58370, 130], [58670, 130], [58970, 130], [59270, 130], [59570, 130], [59870, 130], [60170, 130], [60470, 130], [60770, 130], [61071, 129], [61371, 129], [61671, 129], [61971, 129], [62271, 129], [62571, 129], [62871, 129], [63171, 129], [63471, 129], [63771, 129], [64071, 129], [64371, 129], [64671, 129], [64971, 129], [65271, 129], [65572, 128], [65872, 128], [66172, 128], [66472, 128], [66772, 128], [67072, 128], [67372, 128], [67672, 128], [67972, 128], [68272, 128], [68572, 128], [68872, 128], [69172, 128], [69472, 128], [69772, 128], [70072, 128], [70373, 127], [70673, 127], [70973, 127], [71273, 127], [71573, 127], [71873, 127], [72173, 127], [72473, 127], [72773, 127], [73073, 127], [73373, 127], [73673, 127], [73973, 127], [74273, 127], [74573, 127], [74874, 126], [75174, 126], [75474, 126], [75773, 127], [76073, 24], [76132, 68], [76437, 63], [76739, 61], [77039, 61], [77339, 61], [77639, 61], [77938, 62], [78237, 63], [78535, 65], [78772, 51], [78831, 69], [79072, 128], [79372, 128], [79672, 128], [79971, 129], [80271, 129], [80571, 129], [80871, 129], [81171, 129], [81471, 129], [81770, 130], [82070, 130], [82370, 130], [82670, 130], [82970, 130], [83271, 129]], "point": [233, 206]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+02.69|+00.83|-00.27"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [167, 136, 299, 278], "mask": [[40670, 3], [40773, 3], [40969, 108], [41269, 108], [41569, 109], [41869, 109], [42169, 110], [42468, 111], [42768, 112], [43068, 112], [43368, 113], [43668, 113], [43968, 114], [44268, 114], [44568, 115], [44868, 115], [45168, 116], [45468, 117], [45768, 117], [46068, 118], [46368, 118], [46668, 119], [46967, 15], [46984, 103], [47268, 14], [47284, 104], [47568, 14], [47585, 103], [47868, 14], [47885, 104], [48168, 14], [48185, 104], [48468, 14], [48485, 105], [48768, 14], [48785, 105], [49068, 14], [49085, 106], [49368, 14], [49385, 106], [49668, 14], [49686, 106], [49968, 14], [49986, 106], [50268, 15], [50286, 107], [50568, 15], [50586, 107], [50868, 15], [50886, 108], [51168, 15], [51186, 108], [51468, 15], [51486, 109], [51768, 15], [51786, 109], [52069, 14], [52086, 110], [52369, 14], [52387, 109], [52669, 15], [52687, 110], [52969, 15], [52987, 110], [53269, 15], [53287, 111], [53569, 15], [53587, 111], [53869, 15], [53887, 112], [54169, 15], [54187, 112], [54469, 16], [54487, 113], [54769, 16], [54787, 113], [55069, 17], [55087, 113], [55369, 17], [55387, 113], [55669, 17], [55687, 113], [55969, 17], [55987, 113], [56269, 17], [56287, 113], [56570, 16], [56587, 113], [56870, 16], [56888, 112], [57170, 16], [57188, 112], [57470, 15], [57488, 112], [57770, 15], [57788, 112], [58070, 16], [58088, 112], [58370, 16], [58388, 112], [58670, 16], [58688, 112], [58970, 16], [58988, 112], [59270, 16], [59289, 111], [59570, 16], [59589, 111], [59870, 16], [59889, 111], [60170, 16], [60189, 111], [60470, 17], [60489, 111], [60770, 17], [60789, 111], [61071, 16], [61089, 111], [61371, 16], [61389, 111], [61671, 16], [61690, 110], [61971, 16], [61990, 110], [62271, 16], [62290, 110], [62571, 16], [62590, 110], [62871, 17], [62890, 110], [63171, 17], [63190, 110], [63471, 17], [63490, 110], [63771, 17], [63790, 110], [64071, 17], [64091, 109], [64371, 17], [64391, 109], [64671, 17], [64691, 109], [64971, 17], [64991, 109], [65271, 18], [65291, 109], [65572, 17], [65591, 109], [65872, 17], [65891, 109], [66172, 17], [66191, 109], [66472, 17], [66491, 109], [66772, 17], [66792, 108], [67072, 17], [67092, 108], [67372, 18], [67392, 108], [67672, 18], [67692, 108], [67972, 18], [67992, 108], [68272, 18], [68292, 108], [68572, 18], [68592, 108], [68872, 18], [68892, 108], [69172, 18], [69192, 108], [69472, 18], [69492, 108], [69772, 19], [69792, 108], [70072, 19], [70092, 108], [70373, 18], [70392, 108], [70673, 18], [70692, 108], [70973, 18], [70992, 108], [71273, 18], [71292, 108], [71573, 127], [71873, 127], [72173, 127], [72473, 127], [72773, 127], [73073, 127], [73373, 127], [73673, 127], [73973, 127], [74273, 127], [74573, 127], [74874, 126], [75174, 126], [75474, 126], [75773, 127], [76073, 127], [76373, 127], [76673, 127], [76973, 127], [77273, 127], [77573, 127], [77872, 128], [78172, 128], [78472, 128], [78772, 128], [79072, 128], [79372, 128], [79672, 128], [79971, 129], [80271, 129], [80571, 129], [80871, 129], [81171, 129], [81471, 129], [81770, 130], [82070, 130], [82370, 130], [82670, 130], [82970, 130], [83271, 129]], "point": [233, 206]}}, "high_idx": 5}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+02.74|+01.09|+00.06|BreadSliced_4"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [153, 122, 161, 188], "mask": [[36453, 8], [36753, 8], [37053, 8], [37353, 8], [37653, 8], [37953, 8], [38253, 8], [38553, 8], [38853, 8], [39153, 8], [39453, 8], [39753, 9], [40053, 9], [40353, 9], [40653, 9], [40953, 9], [41253, 9], [41553, 9], [41853, 9], [42153, 9], [42453, 9], [42753, 9], [43053, 9], [43353, 9], [43653, 9], [43953, 9], [44253, 9], [44553, 9], [44853, 9], [45153, 9], [45453, 9], [45753, 9], [46053, 9], [46353, 9], [46653, 9], [46953, 9], [47253, 9], [47553, 9], [47853, 9], [48153, 9], [48453, 9], [48753, 9], [49053, 9], [49353, 9], [49653, 9], [49953, 9], [50253, 9], [50553, 9], [50853, 9], [51153, 9], [51453, 9], [51753, 9], [52053, 9], [52353, 9], [52653, 9], [52953, 9], [53253, 9], [53553, 9], [53853, 9], [54153, 9], [54453, 9], [54753, 8], [55053, 8], [55353, 8], [55653, 8], [55953, 8], [56253, 8]], "point": [157, 154]}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.94|+01.75|-01.83"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [48, 1, 296, 88], "mask": [[48, 248], [348, 248], [648, 248], [948, 248], [1248, 248], [1548, 248], [1848, 248], [2148, 248], [2448, 248], [2748, 248], [3048, 248], [3348, 248], [3648, 248], [3948, 248], [4248, 248], [4548, 248], [4848, 248], [5148, 248], [5448, 248], [5748, 248], [6048, 248], [6348, 248], [6648, 248], [6948, 248], [7248, 248], [7548, 248], [7848, 248], [8148, 248], [8448, 248], [8748, 248], [9048, 248], [9348, 248], [9648, 248], [9948, 248], [10248, 248], [10548, 248], [10848, 248], [11148, 248], [11448, 248], [11748, 248], [12048, 248], [12348, 248], [12648, 248], [12948, 248], [13248, 248], [13548, 248], [13848, 248], [14148, 248], [14448, 248], [14748, 248], [15048, 248], [15348, 248], [15648, 248], [15948, 248], [16248, 248], [16548, 248], [16848, 248], [17148, 248], [17448, 248], [17748, 248], [18048, 248], [18348, 248], [18648, 248], [18948, 248], [19248, 248], [19548, 248], [19848, 248], [20148, 248], [20448, 248], [20748, 248], [21048, 248], [21348, 248], [21648, 248], [21948, 248], [22248, 248], [22548, 249], [22848, 249], [23148, 249], [23448, 249], [23749, 246], [24051, 242], [24352, 239], [24653, 236], [24955, 232], [25256, 229], [25558, 225], [25859, 222], [26161, 218]], "point": [172, 43]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+02.74|+01.09|+00.06|BreadSliced_4", "placeStationary": true, "receptacleObjectId": "Microwave|+01.94|+01.75|-01.83"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 296, 88], "mask": [[0, 296], [300, 296], [600, 296], [900, 296], [1200, 296], [1500, 296], [1800, 296], [2100, 296], [2400, 296], [2700, 296], [3000, 296], [3300, 296], [3600, 296], [3900, 296], [4200, 296], [4500, 296], [4800, 296], [5100, 296], [5400, 296], [5700, 296], [6000, 296], [6300, 296], [6600, 296], [6900, 296], [7200, 296], [7500, 296], [7800, 296], [8100, 296], [8400, 296], [8700, 296], [9000, 296], [9300, 296], [9600, 296], [9900, 296], [10201, 295], [10502, 294], [10803, 293], [11105, 291], [11406, 290], [11707, 289], [12009, 287], [12310, 286], [12611, 285], [12912, 284], [13214, 282], [13515, 281], [13816, 280], [14118, 278], [14419, 277], [14720, 276], [15021, 275], [15323, 273], [15624, 272], [15925, 271], [16227, 269], [16528, 268], [16829, 267], [17131, 265], [17432, 264], [17733, 263], [18034, 262], [18336, 260], [18637, 259], [18938, 258], [19240, 256], [19541, 255], [19842, 254], [20143, 253], [20445, 251], [20746, 250], [21047, 249], [21348, 248], [21648, 248], [21948, 248], [22248, 248], [22548, 249], [22848, 249], [23148, 249], [23448, 249], [23749, 246], [24051, 242], [24352, 239], [24653, 236], [24955, 232], [25256, 229], [25558, 225], [25859, 222], [26161, 218]], "point": [148, 43]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.94|+01.75|-01.83"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 296, 88], "mask": [[0, 127], [168, 128], [300, 127], [469, 127], [600, 127], [769, 127], [900, 127], [1069, 127], [1200, 127], [1370, 126], [1500, 127], [1670, 126], [1800, 127], [1970, 126], [2100, 127], [2270, 126], [2400, 127], [2571, 125], [2700, 127], [2871, 125], [3000, 127], [3171, 125], [3300, 127], [3471, 125], [3600, 127], [3771, 125], [3900, 127], [4071, 125], [4200, 127], [4371, 125], [4500, 127], [4671, 125], [4800, 127], [4971, 125], [5100, 127], [5272, 124], [5400, 127], [5572, 124], [5700, 127], [5872, 124], [6000, 127], [6172, 124], [6300, 127], [6472, 124], [6600, 127], [6772, 124], [6900, 127], [7072, 124], [7200, 127], [7372, 124], [7500, 127], [7672, 124], [7800, 127], [7972, 124], [8100, 127], [8272, 124], [8400, 127], [8572, 124], [8700, 127], [8872, 124], [9000, 127], [9172, 124], [9300, 127], [9472, 124], [9600, 127], [9772, 124], [9900, 127], [10072, 124], [10201, 126], [10372, 124], [10502, 125], [10671, 125], [10803, 124], [10971, 125], [11105, 122], [11271, 125], [11406, 121], [11571, 125], [11707, 120], [11870, 126], [12009, 118], [12170, 126], [12310, 117], [12470, 126], [12611, 116], [12770, 126], [12912, 115], [13069, 127], [13214, 113], [13369, 127], [13515, 112], [13669, 127], [13816, 112], [13968, 128], [14118, 110], [14268, 128], [14419, 109], [14568, 128], [14720, 108], [14867, 129], [15021, 107], [15167, 129], [15323, 106], [15466, 130], [15624, 105], [15765, 131], [15925, 104], [16045, 12], [16064, 132], [16227, 106], [16334, 162], [16528, 268], [16829, 267], [17131, 265], [17432, 264], [17733, 263], [18034, 262], [18336, 260], [18637, 259], [18938, 258], [19240, 256], [19541, 255], [19842, 254], [20143, 253], [20445, 251], [20746, 250], [21047, 249], [21348, 248], [21648, 248], [21948, 248], [22248, 248], [22548, 249], [22848, 249], [23148, 249], [23448, 249], [23749, 246], [24051, 242], [24352, 239], [24653, 236], [24955, 232], [25256, 229], [25558, 225], [25859, 222], [26161, 218]], "point": [148, 53]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.758, 7.758, -7.3052, -7.3052, 6.982419, 6.982419]], "forceVisible": true, "objectId": "Microwave|+01.94|+01.75|-01.83"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [48, 1, 296, 88], "mask": [[48, 248], [348, 248], [648, 248], [948, 248], [1248, 248], [1548, 248], [1848, 248], [2148, 248], [2448, 248], [2748, 248], [3048, 248], [3348, 248], [3648, 248], [3948, 248], [4248, 248], [4548, 248], [4848, 248], [5148, 248], [5448, 248], [5748, 248], [6048, 248], [6348, 248], [6648, 248], [6948, 248], [7248, 248], [7548, 248], [7848, 248], [8148, 248], [8448, 248], [8748, 248], [9048, 248], [9348, 248], [9648, 248], [9948, 248], [10248, 248], [10548, 248], [10848, 248], [11148, 248], [11448, 248], [11748, 248], [12048, 248], [12348, 248], [12648, 248], [12948, 248], [13248, 248], [13548, 248], [13848, 248], [14148, 248], [14448, 248], [14748, 248], [15048, 248], [15348, 248], [15648, 248], [15948, 248], [16248, 248], [16548, 248], [16848, 248], [17148, 248], [17448, 248], [17748, 248], [18048, 248], [18348, 248], [18648, 248], [18948, 248], [19248, 248], [19548, 248], [19848, 248], [20148, 248], [20448, 248], [20748, 248], [21048, 248], [21348, 248], [21648, 248], [21948, 248], [22248, 248], [22548, 249], [22848, 249], [23148, 249], [23448, 249], [23749, 246], [24051, 242], [24352, 239], [24653, 236], [24955, 232], [25256, 229], [25558, 225], [25859, 222], [26161, 218]], "point": [172, 43]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.758, 7.758, -7.3052, -7.3052, 6.982419, 6.982419]], "forceVisible": true, "objectId": "Microwave|+01.94|+01.75|-01.83"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [48, 1, 296, 88], "mask": [[48, 248], [348, 248], [648, 248], [948, 248], [1248, 248], [1548, 248], [1848, 248], [2148, 248], [2448, 248], [2748, 248], [3048, 248], [3348, 248], [3648, 248], [3948, 248], [4248, 248], [4548, 248], [4848, 248], [5148, 248], [5448, 248], [5748, 248], [6048, 248], [6348, 248], [6648, 248], [6948, 248], [7248, 248], [7548, 248], [7848, 248], [8148, 248], [8448, 248], [8748, 248], [9048, 248], [9348, 248], [9648, 248], [9948, 248], [10248, 248], [10548, 248], [10848, 248], [11148, 248], [11448, 248], [11748, 248], [12048, 248], [12348, 248], [12648, 248], [12948, 248], [13248, 248], [13548, 248], [13848, 248], [14148, 248], [14448, 248], [14748, 248], [15048, 248], [15348, 248], [15648, 248], [15948, 248], [16248, 248], [16548, 248], [16848, 248], [17148, 248], [17448, 248], [17748, 248], [18048, 248], [18348, 248], [18648, 248], [18948, 248], [19248, 248], [19548, 248], [19848, 248], [20148, 248], [20448, 248], [20748, 248], [21048, 248], [21348, 248], [21648, 248], [21948, 248], [22248, 248], [22548, 249], [22848, 249], [23148, 249], [23448, 249], [23749, 246], [24051, 242], [24352, 239], [24653, 236], [24955, 232], [25256, 229], [25558, 225], [25859, 222], [26161, 218]], "point": [172, 43]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.94|+01.75|-01.83"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [48, 1, 296, 88], "mask": [[48, 248], [348, 248], [648, 248], [948, 248], [1248, 248], [1548, 248], [1848, 248], [2148, 248], [2448, 248], [2748, 248], [3048, 248], [3348, 248], [3648, 248], [3948, 248], [4248, 248], [4548, 248], [4848, 248], [5148, 248], [5448, 248], [5748, 248], [6048, 248], [6348, 248], [6648, 248], [6948, 248], [7248, 248], [7548, 248], [7848, 248], [8148, 248], [8448, 248], [8748, 248], [9048, 248], [9348, 248], [9648, 248], [9948, 248], [10248, 248], [10548, 248], [10848, 248], [11148, 248], [11448, 248], [11748, 248], [12048, 248], [12348, 248], [12648, 248], [12948, 248], [13248, 248], [13548, 248], [13848, 248], [14148, 248], [14448, 248], [14748, 248], [15048, 248], [15348, 248], [15648, 248], [15948, 248], [16248, 248], [16548, 248], [16848, 248], [17148, 248], [17448, 248], [17748, 248], [18048, 248], [18348, 248], [18648, 248], [18948, 248], [19248, 248], [19548, 248], [19848, 248], [20148, 248], [20448, 248], [20748, 248], [21048, 248], [21348, 248], [21648, 248], [21948, 248], [22248, 248], [22548, 249], [22848, 249], [23148, 249], [23448, 249], [23749, 246], [24051, 242], [24352, 239], [24653, 236], [24955, 232], [25256, 229], [25558, 225], [25859, 222], [26161, 218]], "point": [172, 43]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+02.74|+01.09|+00.06|BreadSliced_4"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [127, 1, 171, 55], "mask": [[127, 41], [427, 42], [727, 42], [1027, 42], [1327, 43], [1627, 43], [1927, 43], [2227, 43], [2527, 44], [2827, 44], [3127, 44], [3427, 44], [3727, 44], [4027, 44], [4327, 44], [4627, 44], [4927, 44], [5227, 45], [5527, 45], [5827, 45], [6127, 45], [6427, 45], [6727, 45], [7027, 45], [7327, 45], [7627, 45], [7927, 45], [8227, 45], [8527, 45], [8827, 45], [9127, 45], [9427, 45], [9727, 45], [10027, 45], [10327, 45], [10627, 44], [10927, 44], [11227, 44], [11527, 44], [11827, 43], [12127, 43], [12427, 43], [12727, 43], [13027, 42], [13327, 42], [13627, 42], [13928, 40], [14228, 40], [14528, 40], [14828, 39], [15128, 39], [15429, 37], [15729, 36], [16029, 16], [16057, 7], [16333, 1]], "point": [149, 27]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.94|+01.75|-01.83"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 296, 88], "mask": [[0, 296], [300, 296], [600, 296], [900, 296], [1200, 296], [1500, 296], [1800, 296], [2100, 296], [2400, 296], [2700, 296], [3000, 296], [3300, 296], [3600, 296], [3900, 296], [4200, 296], [4500, 296], [4800, 296], [5100, 296], [5400, 296], [5700, 296], [6000, 296], [6300, 296], [6600, 296], [6900, 296], [7200, 296], [7500, 296], [7800, 296], [8100, 296], [8400, 296], [8700, 296], [9000, 296], [9300, 296], [9600, 296], [9900, 296], [10201, 295], [10502, 294], [10803, 293], [11105, 291], [11406, 290], [11707, 289], [12009, 287], [12310, 286], [12611, 285], [12912, 284], [13214, 282], [13515, 281], [13816, 280], [14118, 278], [14419, 277], [14720, 276], [15021, 275], [15323, 273], [15624, 272], [15925, 271], [16227, 269], [16528, 268], [16829, 267], [17131, 265], [17432, 264], [17733, 263], [18034, 262], [18336, 260], [18637, 259], [18938, 258], [19240, 256], [19541, 255], [19842, 254], [20143, 253], [20445, 251], [20746, 250], [21047, 249], [21348, 248], [21648, 248], [21948, 248], [22248, 248], [22548, 249], [22848, 249], [23148, 249], [23448, 249], [23749, 246], [24051, 242], [24352, 239], [24653, 236], [24955, 232], [25256, 229], [25558, 225], [25859, 222], [26161, 218]], "point": [148, 43]}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+02.74|+01.09|+00.06|BreadSliced_4", "placeStationary": true, "receptacleObjectId": "GarbageCan|-00.95|00.00|+02.83"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [85, 144, 190, 228], "mask": [[43000, 78], [43296, 86], [43594, 90], [43893, 92], [44192, 93], [44492, 94], [44791, 96], [45091, 96], [45390, 98], [45690, 98], [45990, 98], [46290, 98], [46590, 98], [46890, 98], [47190, 98], [47489, 99], [47789, 100], [48089, 100], [48389, 100], [48689, 100], [48989, 100], [49289, 100], [49588, 101], [49888, 101], [50188, 101], [50488, 101], [50788, 101], [51088, 102], [51388, 102], [51687, 103], [51987, 103], [52287, 103], [52587, 103], [52887, 103], [53187, 103], [53487, 103], [53786, 104], [54086, 104], [54386, 105], [54686, 105], [54986, 105], [55286, 105], [55586, 105], [55885, 106], [56185, 106], [56485, 106], [56785, 106], [57085, 106], [57385, 106], [57685, 106], [57985, 106], [58285, 106], [58585, 106], [58885, 106], [59185, 106], [59485, 106], [59785, 106], [60085, 106], [60385, 106], [60685, 106], [60985, 105], [61286, 104], [61586, 104], [61886, 104], [62186, 103], [62486, 103], [62787, 102], [63087, 101], [63387, 101], [63687, 101], [63988, 99], [64289, 48], [64355, 32], [64590, 40], [64661, 25], [64890, 35], [64967, 18], [65191, 30], [65278, 6], [65492, 26], [65793, 22], [66094, 19], [66395, 16], [66695, 14], [66996, 11], [67297, 8], [67598, 6], [67899, 4], [68201, 1]], "point": [137, 185]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan16", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 0.25, "y": 0.900999665, "z": 0.5}, "object_poses": [{"objectName": "Pan_c7f7c9bb", "position": {"x": 1.836, "y": 1.0276562, "z": -1.84}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Ladle_8232b75a", "position": {"x": 2.70621943, "y": 1.066041, "z": -1.151727}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_98253f2a", "position": {"x": 1.92600012, "y": 0.7645124, "z": 3.821562}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "WineBottle_ca02bbc4", "position": {"x": -0.6295028, "y": 0.04893136, "z": 2.46851683}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_ca02bbc4", "position": {"x": 1.92600012, "y": 0.7653991, "z": 3.7473433}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "Spoon_5dd10cb9", "position": {"x": 2.7627275, "y": 0.8023311, "z": -0.3137451}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_8af67485", "position": {"x": 2.626, "y": 0.7650188, "z": 4.118438}, "rotation": {"x": 0.0, "y": -4.34670546e-05, "z": 0.0}}, {"objectName": "Fork_8af67485", "position": {"x": 2.71075034, "y": 0.7650188, "z": 4.22975063}, "rotation": {"x": 0.0, "y": 224.999969, "z": 0.0}}, {"objectName": "Egg_6cf51d94", "position": {"x": 1.29036856, "y": 1.07088161, "z": -1.66760087}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_7fa88b29", "position": {"x": 2.70621943, "y": 1.0254631, "z": -0.541442335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1895c9f9", "position": {"x": 2.21616364, "y": 0.764959335, "z": 3.945086}, "rotation": {"x": 0.0, "y": 224.999969, "z": 0.0}}, {"objectName": "SoapBottle_a47378fd", "position": {"x": 1.409931, "y": 1.02554524, "z": -1.98779488}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_2bc5be8e", "position": {"x": 2.59943748, "y": 0.8226274, "z": 4.495}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_2bc5be8e", "position": {"x": 2.451, "y": 0.822627544, "z": 4.044219}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "Apple_dc079739", "position": {"x": 2.95947552, "y": 1.07722592, "z": -1.45686913}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_c7f7c9bb", "position": {"x": 2.621801, "y": 1.02648294, "z": -0.8465846}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_105a13ed", "position": {"x": 2.55596113, "y": 0.765187, "z": 3.86503887}, "rotation": {"x": 0.0, "y": 224.999969, "z": 0.0}}, {"objectName": "Knife_2539b087", "position": {"x": -0.714400053, "y": 0.8419058, "z": 1.10397124}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SoapBottle_a47378fd", "position": {"x": 2.55198979, "y": 0.7631422, "z": 4.24039268}, "rotation": {"x": 0.0, "y": 329.9999, "z": 0.0}}, {"objectName": "Egg_6cf51d94", "position": {"x": 1.5294919, "y": 1.07088161, "z": -1.88106322}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Fork_8af67485", "position": {"x": 2.97599983, "y": 0.7650188, "z": 4.11843824}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "Potato_a189bfca", "position": {"x": -0.5775822, "y": 0.8585599, "z": 1.33710933}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Ladle_8232b75a", "position": {"x": 1.29036927, "y": 1.066041, "z": -1.8810637}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Tomato_6be5e93d", "position": {"x": 2.008894, "y": 0.8181622, "z": 3.632855}, "rotation": {"x": 0.0, "y": 224.999969, "z": 0.0}}, {"objectName": "Pot_267fd2ad", "position": {"x": 2.116, "y": 1.0276562, "z": -1.84}, "rotation": {"x": 0.0, "y": 270.0001, "z": 0.0}}, {"objectName": "WineBottle_ca02bbc4", "position": {"x": 2.69894743, "y": 0.7653991, "z": 3.68898153}, "rotation": {"x": 0.0, "y": 329.9999, "z": 0.0}}, {"objectName": "Plate_1895c9f9", "position": {"x": 2.875057, "y": 1.02736247, "z": -0.236300111}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_87bb090b", "position": {"x": -0.6696348, "y": 1.0424999, "z": 2.43427372}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_758d0bf4", "position": {"x": 2.919334, "y": 0.8687557, "z": 3.60413647}, "rotation": {"x": 0.0, "y": 329.9999, "z": 0.0}}, {"objectName": "Lettuce_2bc5be8e", "position": {"x": -0.922705233, "y": 1.50027251, "z": -0.6279499}, "rotation": {"x": 0.0, "y": 89.99984, "z": 0.0}}, {"objectName": "Apple_dc079739", "position": {"x": -0.947403669, "y": 0.83656776, "z": -0.07307497}, "rotation": {"x": 0.0, "y": 89.99984, "z": 0.0}}, {"objectName": "SaltShaker_c2d3cb7a", "position": {"x": 1.40993047, "y": 1.02317965, "z": -1.88106346}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_26395c85", "position": {"x": 2.331603, "y": 0.7607765, "z": 4.32523727}, "rotation": {"x": 0.0, "y": 329.9999, "z": 0.0}}, {"objectName": "ButterKnife_98253f2a", "position": {"x": -0.7144, "y": 0.816598237, "z": 1.4148221}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_7275394e", "position": {"x": 2.74136, "y": 1.08508265, "z": 0.05956937}, "rotation": {"x": 0.0198850557, "y": 7.16642462e-05, "z": -2.4453353e-05}}, {"objectName": "Cup_7fa88b29", "position": {"x": -0.851217866, "y": 0.8151458, "z": 1.181684}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_00fca5a2", "position": {"x": 2.719964, "y": 1.02643776, "z": 1.59717107}, "rotation": {"x": 0.0, "y": 136.999985, "z": 0.0}}, {"objectName": "Bowl_b3ba1f80", "position": {"x": 2.169365, "y": 2.27759838, "z": -1.87190175}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_5dd10cb9", "position": {"x": -0.8512178, "y": 0.8176854, "z": 1.492535}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_a04e993b", "position": {"x": 2.976, "y": 0.759470344, "z": 3.89578176}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}], "object_toggles": [], "random_seed": 1535181309, "scene_num": 16}, "task_id": "trial_T20190907_232534_297377", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AO33H4GL9KZX9_31LVTDXBLARQGT1X7BBL17Q0QYIRLA", "high_descs": ["Turn around and go to the sink. ", "Pick up the knife with wooden handle.", "Turn around to go to the counter on the opposite side.", "Slice a half of the bread on the counter.", "Look for a drawer for the knife. ", "Place the knife in the drawer. ", "Go back to the bread on the counter.", "Pick up one slice of the bread.", "Turn around to go the to microwave above the stove. ", "Heat the slice of bread in the microwave", "Turn around and go to the trash can on the left on the opposite side.", "Place the bread in the trash can."], "task_desc": "Throw away a heated slice of bread.", "votes": [1, 1, 1]}, {"assignment_id": "A1OWHPMKE7YAGL_37Z929RLGCPQ0L9FV9DHD85D6F2STL", "high_descs": ["Turn to the left, go forward, turn to the left, go forward to the sink.", "Pick up the yellow handled knife that is in the sink. ", "Turn to the left, go forward to the fridge, turn to the left, go forward to the counter where the bread is. ", "Use the knife to cut the bread into slices. ", "Turn around, go forward, turn around.", "Open the drawer that is on the right underneath the bread and put the knife inside, close the drawer. ", "Go forward to the counter. ", "Pick up a slice of bread from the counter. ", "Turn around, go forward and hang a left, go forward to the microwave that is above the stove. ", "Put the bread inside the microwave, turn it on to cook, remove the cooked bread from the microwave and close the door. ", "Back up, turn to the right, go forward, turn to the right, go forward then hang a left and go to the garbage can on the left. ", "Put the bread in the garbage can. "], "task_desc": "Put cooked bread in a garbage can. ", "votes": [1, 1, 1]}, {"assignment_id": "AKW57KYG90X61_3E47SOBEYTDKKQ484BODKWZM9UIICG", "high_descs": ["turn left to the sink", "pick up the knife", "turn around to the table", "slice the bread on the table", "move to the drawer", "place the knife in the drawer", "turn left to the table", "pick up a slice", "head to the oven behind", "place the bread to heat and take it out", "turn around to the bin ", "trash the bread slice"], "task_desc": "trash a heated bread slice", "votes": [1, 0, 1]}]}}