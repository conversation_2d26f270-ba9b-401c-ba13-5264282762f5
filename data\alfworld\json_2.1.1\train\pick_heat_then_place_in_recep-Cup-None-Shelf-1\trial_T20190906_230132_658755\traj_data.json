{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000113.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000114.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000205.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000206.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000207.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000208.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000209.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000210.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000265.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000266.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000267.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000268.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000269.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000270.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000271.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000272.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000273.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000274.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000275.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000276.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000277.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000278.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000279.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000280.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000340.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000341.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000342.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000343.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000344.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000345.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000346.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000347.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000348.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000349.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000350.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000351.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000352.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000353.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000354.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000355.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000356.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000357.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000358.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000359.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000360.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000361.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000362.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000363.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000364.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000365.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000366.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000367.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000368.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000369.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000370.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000371.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000372.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000373.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000374.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000375.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000376.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000377.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000378.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000379.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000380.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000381.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000382.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000383.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000384.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000385.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000386.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000387.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000388.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000389.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000390.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000391.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000392.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000393.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000394.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000395.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000396.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000397.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000398.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000399.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000400.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000401.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000402.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000403.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000404.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000405.png", "low_idx": 61}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-7|2|0"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-4.05455304, -4.05455304, -10.27497484, -10.27497484, 6.61672736, 6.61672736]], "coordinateReceptacleObjectId": ["Cabinet", [-2.9112, -2.9112, -9.84, -9.84, 8.06, 8.06]], "forceVisible": true, "objectId": "Cup|-01.01|+01.65|-02.57"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-7|2|0"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.948, -0.948, -10.1004, -10.1004, 6.756, 6.756]], "forceVisible": true, "objectId": "Microwave|-00.24|+01.69|-02.53"}}, {"discrete_action": {"action": "GotoLocation", "args": ["shelf"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|7|-5|2|30"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "shelf"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-4.05455304, -4.05455304, -10.27497484, -10.27497484, 6.61672736, 6.61672736]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [7.216, 7.216, -10.2544, -10.2544, 3.4799996, 3.4799996]], "forceVisible": true, "objectId": "Cup|-01.01|+01.65|-02.57", "receptacleObjectId": "<PERSON><PERSON>|+01.80|+00.87|-02.56"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.73|+02.02|-02.46"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [45, 1, 222, 127], "mask": [[45, 178], [345, 178], [645, 178], [945, 178], [1245, 178], [1545, 178], [1845, 178], [2145, 178], [2445, 178], [2745, 178], [3045, 178], [3345, 178], [3645, 178], [3945, 178], [4245, 178], [4545, 178], [4845, 178], [5145, 178], [5445, 178], [5745, 178], [6045, 178], [6345, 178], [6645, 178], [6945, 178], [7245, 178], [7545, 178], [7845, 178], [8145, 178], [8445, 178], [8745, 178], [9045, 178], [9345, 178], [9645, 178], [9945, 178], [10245, 178], [10545, 178], [10845, 178], [11145, 178], [11445, 178], [11745, 178], [12045, 178], [12345, 178], [12645, 178], [12945, 178], [13245, 178], [13545, 178], [13845, 178], [14145, 178], [14445, 178], [14745, 178], [15045, 178], [15345, 178], [15645, 178], [15945, 178], [16245, 178], [16545, 178], [16845, 178], [17145, 178], [17445, 178], [17745, 178], [18045, 178], [18345, 178], [18645, 178], [18945, 178], [19245, 178], [19545, 178], [19845, 178], [20145, 178], [20445, 178], [20745, 178], [21045, 178], [21345, 178], [21645, 178], [21945, 178], [22245, 178], [22545, 178], [22845, 178], [23145, 178], [23445, 178], [23745, 178], [24045, 178], [24345, 178], [24645, 178], [24945, 178], [25245, 178], [25545, 178], [25845, 178], [26145, 178], [26445, 178], [26745, 178], [27045, 178], [27345, 178], [27645, 178], [27945, 178], [28245, 178], [28545, 178], [28845, 178], [29145, 178], [29445, 178], [29745, 178], [30045, 178], [30345, 178], [30645, 178], [30945, 178], [31245, 178], [31545, 178], [31845, 178], [32145, 178], [32445, 178], [32745, 178], [33045, 178], [33345, 178], [33645, 178], [33945, 178], [34245, 178], [34545, 178], [34845, 178], [35145, 178], [35445, 178], [35745, 178], [36045, 178], [36345, 178], [36645, 178], [36945, 178], [37245, 178], [37545, 178], [37848, 173]], "point": [133, 63]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.01|+01.65|-02.57"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [141, 78, 167, 125], "mask": [[23247, 15], [23544, 21], [23843, 23], [24142, 25], [24442, 25], [24742, 25], [25042, 25], [25341, 26], [25641, 26], [25941, 26], [26241, 26], [26541, 27], [26841, 27], [27141, 27], [27441, 27], [27741, 27], [28041, 27], [28341, 27], [28641, 27], [28941, 27], [29241, 27], [29541, 27], [29841, 27], [30141, 27], [30441, 27], [30741, 27], [31041, 27], [31341, 27], [31641, 27], [31941, 27], [32241, 27], [32541, 27], [32841, 27], [33141, 27], [33441, 26], [33741, 26], [34041, 26], [34341, 26], [34641, 26], [34941, 26], [35242, 25], [35542, 25], [35842, 25], [36142, 25], [36442, 24], [36743, 23], [37043, 22], [37344, 6], [37351, 6], [37359, 6]], "point": [154, 100]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.73|+02.02|-02.46"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 50, 127], "mask": [[0, 51], [300, 51], [600, 51], [900, 51], [1200, 51], [1500, 51], [1800, 51], [2100, 51], [2400, 51], [2700, 51], [3000, 51], [3300, 51], [3600, 51], [3900, 51], [4200, 51], [4500, 51], [4800, 51], [5100, 51], [5400, 51], [5700, 51], [6000, 51], [6300, 51], [6600, 51], [6900, 51], [7200, 51], [7500, 51], [7800, 51], [8100, 51], [8400, 51], [8700, 51], [9000, 51], [9300, 51], [9600, 51], [9900, 51], [10200, 51], [10500, 51], [10800, 51], [11100, 51], [11400, 51], [11700, 51], [12000, 51], [12300, 51], [12600, 51], [12900, 51], [13200, 51], [13500, 51], [13800, 51], [14100, 51], [14400, 51], [14700, 51], [15000, 51], [15300, 51], [15600, 51], [15900, 51], [16200, 51], [16500, 51], [16800, 51], [17100, 51], [17400, 51], [17700, 51], [18000, 51], [18300, 51], [18600, 51], [18900, 51], [19200, 51], [19500, 51], [19800, 51], [20100, 51], [20400, 51], [20700, 51], [21000, 51], [21300, 51], [21600, 51], [21900, 51], [22200, 51], [22500, 51], [22800, 51], [23100, 51], [23400, 51], [23700, 51], [24000, 51], [24300, 51], [24600, 51], [24900, 51], [25200, 51], [25500, 51], [25800, 51], [26100, 51], [26400, 51], [26700, 51], [27000, 51], [27300, 51], [27600, 51], [27900, 51], [28200, 51], [28500, 51], [28800, 51], [29100, 51], [29400, 51], [29700, 51], [30000, 51], [30300, 51], [30600, 51], [30900, 51], [31200, 51], [31500, 51], [31800, 51], [32100, 51], [32400, 51], [32700, 51], [33000, 51], [33300, 51], [33600, 51], [33900, 51], [34200, 51], [34500, 51], [34800, 51], [35100, 51], [35404, 47], [35708, 43], [36013, 38], [36317, 34], [36622, 29], [36927, 24], [37231, 20], [37536, 14], [37841, 9]], "point": [25, 63]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.01|+01.65|-02.57", "placeStationary": true, "receptacleObjectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 85], [5186, 81], [5400, 79], [5494, 73], [5700, 77], [5797, 70], [6000, 76], [6099, 68], [6300, 75], [6400, 67], [6600, 75], [6701, 66], [6900, 75], [7002, 65], [7200, 75], [7303, 64], [7500, 75], [7603, 64], [7800, 74], [7903, 64], [8100, 74], [8203, 64], [8400, 74], [8503, 64], [8700, 74], [8804, 63], [9000, 74], [9104, 63], [9300, 74], [9404, 63], [9600, 74], [9704, 63], [9900, 74], [10004, 63], [10200, 74], [10304, 63], [10500, 74], [10604, 63], [10800, 74], [10904, 63], [11100, 74], [11204, 63], [11400, 74], [11504, 63], [11700, 74], [11804, 63], [12000, 74], [12104, 63], [12300, 74], [12404, 63], [12600, 74], [12704, 63], [12900, 74], [13004, 63], [13200, 74], [13304, 63], [13500, 74], [13604, 63], [13800, 74], [13904, 63], [14100, 74], [14204, 63], [14400, 74], [14504, 63], [14700, 74], [14804, 63], [15000, 74], [15104, 63], [15300, 74], [15404, 63], [15600, 74], [15704, 63], [15900, 74], [16004, 63], [16200, 74], [16304, 63], [16500, 74], [16604, 63], [16800, 74], [16904, 63], [17100, 74], [17204, 63], [17400, 74], [17504, 62], [17700, 74], [17804, 62], [18000, 74], [18104, 62], [18300, 74], [18404, 62], [18600, 74], [18704, 62], [18900, 74], [19004, 62], [19200, 74], [19304, 62], [19500, 74], [19604, 62], [19800, 75], [19904, 62], [20100, 75], [20203, 63], [20400, 75], [20503, 63], [20700, 75], [20803, 63], [21000, 75], [21103, 63], [21300, 76], [21403, 63], [21600, 76], [21703, 63], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [73, 50]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.948, -0.948, -10.1004, -10.1004, 6.756, 6.756]], "forceVisible": true, "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.948, -0.948, -10.1004, -10.1004, 6.756, 6.756]], "forceVisible": true, "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.01|+01.65|-02.57"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [74, 18, 103, 73], "mask": [[5185, 1], [5479, 15], [5777, 20], [6076, 23], [6375, 25], [6675, 26], [6975, 27], [7275, 28], [7575, 28], [7874, 29], [8174, 29], [8474, 29], [8774, 30], [9074, 30], [9374, 30], [9674, 30], [9974, 30], [10274, 30], [10574, 30], [10874, 30], [11174, 30], [11474, 30], [11774, 30], [12074, 30], [12374, 30], [12674, 30], [12974, 30], [13274, 30], [13574, 30], [13874, 30], [14174, 30], [14474, 30], [14774, 30], [15074, 30], [15374, 30], [15674, 30], [15974, 30], [16274, 30], [16574, 30], [16874, 30], [17174, 30], [17474, 30], [17774, 30], [18074, 30], [18374, 30], [18674, 30], [18974, 30], [19274, 30], [19574, 30], [19875, 29], [20175, 28], [20475, 28], [20775, 28], [21075, 28], [21376, 27], [21676, 27]], "point": [88, 44]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.01|+01.65|-02.57", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|+01.80|+00.87|-02.56"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [73, 173, 212, 206], "mask": [[51717, 2], [51778, 22], [52018, 2], [52078, 24], [52319, 2], [52378, 25], [52620, 2], [52678, 27], [52882, 1], [52978, 28], [53182, 1], [53278, 28], [53482, 2], [53578, 28], [53781, 3], [53878, 29], [54081, 3], [54178, 29], [54380, 5], [54478, 29], [54680, 5], [54779, 29], [54980, 5], [55015, 3], [55079, 29], [55279, 7], [55315, 5], [55379, 29], [55579, 7], [55615, 6], [55679, 30], [55878, 9], [55914, 7], [55979, 30], [56178, 9], [56214, 7], [56279, 30], [56478, 10], [56514, 7], [56579, 31], [56777, 12], [56813, 8], [56879, 31], [57077, 12], [57113, 8], [57179, 31], [57377, 13], [57412, 9], [57479, 31], [57676, 15], [57711, 10], [57779, 32], [57976, 16], [58011, 10], [58079, 32], [58275, 18], [58310, 11], [58379, 32], [58575, 19], [58609, 12], [58679, 33], [58875, 20], [58908, 13], [58979, 33], [59174, 23], [59206, 15], [59279, 33], [59474, 26], [59503, 18], [59579, 34], [59773, 48], [59879, 34], [60073, 48], [60179, 34], [60374, 47], [60479, 34], [60675, 46], [60779, 33], [60975, 46], [61079, 32], [61276, 45], [61379, 31], [61679, 30]], "point": [120, 188]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan1", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -0.25, "y": 0.9009995, "z": 1.25}, "object_poses": [{"objectName": "Potato_dc7b7f7e", "position": {"x": -0.197295129, "y": 1.14208543, "z": 0.7573217}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_dc7b7f7e", "position": {"x": -0.9956643, "y": 0.941885352, "z": -2.48345661}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": 1.78926146, "y": 0.877827168, "z": -2.53643179}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": 2.10056639, "y": 0.5500548, "z": -2.66644931}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Tomato_811f3b02", "position": {"x": -0.473685682, "y": 1.15066838, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": -1.69473386, "y": 0.47902, "z": -0.196888119}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_b714915a", "position": {"x": -0.3354904, "y": 1.10790622, "z": -0.00100004673}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e166a32f", "position": {"x": -0.197295129, "y": 1.19122255, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e166a32f", "position": {"x": -0.197295129, "y": 1.19122255, "z": -0.253773928}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cd066502", "position": {"x": -1.69341934, "y": 0.912529349, "z": -2.47583175}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": -1.81492758, "y": 0.124897718, "z": 0.248873025}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": -1.85282, "y": 0.784756, "z": -1.41488659}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": 0.7790776, "y": 0.3302362, "z": -2.28662539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": -0.059099853, "y": 1.11164212, "z": -0.00100004673}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7ed44c70", "position": {"x": -0.0361, "y": 0.950499952, "z": -2.3722}, "rotation": {"x": 0.0, "y": 270.000061, "z": 0.0}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": -1.90772438, "y": 0.0487296022, "z": 2.12468362}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": 0.506796241, "y": 0.141359568, "z": -2.485253}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": -1.96227539, "y": 0.0475771464, "z": 2.029}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": 0.8929328, "y": 0.141800582, "z": -2.38279819}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9960ee75", "position": {"x": -2.03700638, "y": 0.7951334, "z": 1.00317216}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Book_f5814e4b", "position": {"x": 0.155, "y": 1.1, "z": 0.617}, "rotation": {"x": 0.0, "y": 315.826447, "z": 0.0}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": 2.096321, "y": 0.8815479, "z": -2.563602}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Knife_c9d70d0f", "position": {"x": -1.93770242, "y": 0.936749637, "z": -0.8849294}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_35d530e8", "position": {"x": -1.61199057, "y": 0.9838652, "z": -0.513000965}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_89238ee6", "position": {"x": -1.96337986, "y": 0.7703047, "z": -1.67392874}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Potato_dc7b7f7e", "position": {"x": -2.15801048, "y": 1.33652139, "z": 1.09256208}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": -0.3354904, "y": 1.11258078, "z": -0.253773928}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7ed44c70", "position": {"x": -0.0361, "y": 0.950499952, "z": -2.5762}, "rotation": {"x": 0.0, "y": 270.000061, "z": 0.0}}, {"objectName": "Pan_e8d2711b", "position": {"x": 0.355485976, "y": 1.11120951, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ba789e3c", "position": {"x": -2.03855252, "y": 0.537431538, "z": 0.9204594}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_811f3b02", "position": {"x": -1.96227539, "y": 0.08708125, "z": 1.93331647}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Vase_d1ae33eb", "position": {"x": 2.042472, "y": 0.5407073, "z": -2.49500537}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_30762d8a", "position": {"x": -2.08159924, "y": 1.53813827, "z": 1.09256268}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": 1.06418574, "y": 0.1392324, "z": -2.28615522}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_1a4cbefa", "position": {"x": 1.78946733, "y": 0.5518556, "z": -2.51112652}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": -0.473685682, "y": 1.1125288, "z": -0.5065479}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_9e34d9fc", "position": {"x": -0.4652, "y": 0.950499952, "z": -2.372}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": 0.453817844, "y": 0.9256999, "z": -2.56081653}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_2c38299b", "position": {"x": -0.3354904, "y": 1.2158854, "z": 0.5045478}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_77fad61b", "position": {"x": -1.01363826, "y": 1.65418184, "z": -2.56874371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_5ffb3206", "position": {"x": 0.2172907, "y": 1.109299, "z": -0.253773928}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9960ee75", "position": {"x": -2.12153745, "y": 0.5371333, "z": 1.16859972}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_b714915a", "position": {"x": -1.77484643, "y": 0.9077062, "z": -0.513000965}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_816f44ce", "position": {"x": 1.99396789, "y": 0.8771374, "z": -2.563601}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Lettuce_e166a32f", "position": {"x": 1.09816635, "y": 0.99032253, "z": -2.56081653}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": 0.2172907, "y": 1.11164212, "z": -0.5065479}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": -2.12153745, "y": 0.5910905, "z": 1.33402669}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": -1.90809989, "y": 0.769320369, "z": -1.32853913}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_cd066502", "position": {"x": 0.2172907, "y": 1.11272943, "z": -0.0009999275}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": -1.7313, "y": 0.956599951, "z": -0.1689994}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}], "object_toggles": [], "random_seed": 921254672, "scene_num": 1}, "task_id": "trial_T20190906_230132_658755", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A35P2RX8HKZM96_38JBBYETQRR59HP7FDS4GJP6O894EY", "high_descs": ["Turn right, walk to the fridge and turn left and walk to the counter.", "Take a glass from the upper cabinet.", "Turn left and walk to the microwave on your right.", "Put the glass in the microwave, turn it on, and remove it when it's done.", "Turn left and walk to the brown shelf.", "Place the glass on the shelf."], "task_desc": "Microwave a glass,  place it on the shelf.", "votes": [1, 1, 1, 1, 1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3QHK8ZVMIPZ30MK8BEPV1SLW7YPBLY", "high_descs": ["Go to the cabinet to the right of the microwave.", "Take the glass from the cabinet.", "Bring the glass to the microwave.", "Heat the glass in the microwave. ", "Go to the shelf to your left.", "Place the glass on the top shelf."], "task_desc": "Place a heated glass on a shelf.", "votes": [1, 1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3ERMJ6L4D1904SZ3Y9EUW3EPO2Q7MQ", "high_descs": ["Turn right then turn left and straight, to the counter to the right of the stove.", "Take the glass from the cabinet above the counter.", "Turn left then turn right at the stove, then look up at the microwave.", "Heat the glass in the microwave.", "Turn around, then turn right, then go right again to face the shelves to the left of the counter.", "Put the glass on the top shelf, toward the right."], "task_desc": "Put a warm glass on the shelf.", "votes": [1, 1, 1]}, {"assignment_id": "A2CT57W84KXX25_3ZGVPD4G6WYN1XVTQ17P912GTG1ZT4", "high_descs": ["Turn right, take a step, turn left, walk to the counter. ", "Take a glass out of the cupboard above the potato. ", "Walk in front of the stove. ", "Put the glass in the microwave, heat it for a few seconds, take the glass out of the microwave. ", "Turn to the left, walk to the wall, turn to the brown shelves. ", "Place the glass to the right of all of the objects on the first shelf."], "task_desc": "Put the heated glass on the shelf. ", "votes": [1, 0, 1]}]}}