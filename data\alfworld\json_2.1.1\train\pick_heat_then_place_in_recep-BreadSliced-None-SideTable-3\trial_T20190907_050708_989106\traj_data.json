{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000079.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000080.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000081.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000082.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000083.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000084.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000085.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 5, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000140.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000142.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000144.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000145.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000146.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000148.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000149.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000150.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000151.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000153.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000154.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000155.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000156.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000157.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000158.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000159.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000161.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000162.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000166.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000197.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000198.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000199.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000200.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000201.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000202.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000203.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000204.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000221.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000222.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000289.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000290.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000291.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000330.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000331.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000332.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000333.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000335.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000336.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000337.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000338.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000339.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000340.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000341.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000342.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000343.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000344.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000345.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000346.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000347.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000348.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000349.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000350.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000351.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000352.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000353.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000354.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000355.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000356.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000357.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000358.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000359.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000360.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000361.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000362.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000363.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000364.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000365.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000366.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000367.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000368.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000369.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000370.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000371.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000372.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000373.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000374.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000375.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000376.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000377.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000378.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000379.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000380.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000381.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000382.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000383.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000384.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000385.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000386.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000387.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000388.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000389.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000390.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000391.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000392.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000393.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000394.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000395.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000396.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000397.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000398.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000399.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000400.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000401.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000402.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000403.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000404.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000405.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000406.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000407.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000408.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000409.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000410.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000411.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000412.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000413.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000414.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000415.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000416.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000417.png", "low_idx": 61}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-9|3|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [-7.16126252, -7.16126252, -8.80539704, -8.80539704, 5.41080188, 5.41080188]], "coordinateReceptacleObjectId": ["CounterTop", [-7.236, -7.236, 4.728, 4.728, 5.434, 5.434]], "forceVisible": true, "objectId": "Knife|-01.79|+01.35|-02.20"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-8.19928264, -8.19928264, -10.88489912, -10.88489912, 5.589581, 5.589581]], "forceVisible": true, "objectId": "Bread|-02.05|+01.40|-02.72"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 3, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-8|0|15"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "countertop"]}, "high_idx": 4, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [-7.16126252, -7.16126252, -8.80539704, -8.80539704, 5.41080188, 5.41080188]], "coordinateReceptacleObjectId": ["CounterTop", [-7.236, -7.236, 4.728, 4.728, 5.434, 5.434]], "forceVisible": true, "objectId": "Knife|-01.79|+01.35|-02.20", "receptacleObjectId": "CounterTop|-01.81|+01.36|+01.18"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 5, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-9|3|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 6, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-8.19928264, -8.19928264, -10.88489912, -10.88489912, 5.589581, 5.589581]], "coordinateReceptacleObjectId": ["CounterTop", [-7.236, -7.236, 4.728, 4.728, 5.434, 5.434]], "forceVisible": true, "objectId": "Bread|-02.05|+01.40|-02.72|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 7, "planner_action": {"action": "GotoLocation", "location": "loc|1|3|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 8, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 9, "planner_action": {"action": "GotoLocation", "location": "loc|1|4|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "sidetable"]}, "high_idx": 10, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-8.19928264, -8.19928264, -10.88489912, -10.88489912, 5.589581, 5.589581]], "coordinateReceptacleObjectId": ["SideTable", [4.088, 4.088, 3.4844, 3.4844, 4.9732, 4.9732]], "forceVisible": true, "objectId": "Bread|-02.05|+01.40|-02.72|BreadSliced_1", "receptacleObjectId": "SideTable|+01.02|+01.24|+00.87"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 11, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|-01.79|+01.35|-02.20"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [162, 126, 167, 178], "mask": [[37662, 1], [37962, 1], [38262, 1], [38562, 1], [38862, 1], [39162, 1], [39462, 1], [39762, 1], [40062, 1], [40362, 1], [41563, 1], [41863, 1], [42163, 1], [42463, 1], [42763, 1], [43063, 1], [43363, 1], [43663, 1], [43963, 1], [44263, 1], [44563, 1], [44863, 1], [45163, 2], [45463, 2], [45763, 3], [46063, 3], [46363, 3], [46663, 3], [46963, 3], [47263, 3], [47563, 3], [47863, 3], [48163, 4], [48463, 4], [48763, 4], [49063, 4], [49363, 4], [49663, 4], [49963, 4], [50263, 4], [50563, 4], [50863, 4], [51163, 4], [51464, 4], [51764, 4], [52064, 4], [52364, 4], [52664, 3], [52965, 2], [53265, 2]], "point": [164, 151]}}, "high_idx": 1}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-02.05|+01.40|-02.72"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [0, 95, 48, 130], "mask": [[28222, 25], [28515, 34], [28809, 39], [29104, 43], [29401, 44], [29700, 44], [30000, 43], [30300, 42], [30600, 41], [30900, 40], [31200, 39], [31500, 38], [31800, 37], [32100, 36], [32400, 36], [32700, 36], [33000, 36], [33300, 36], [33600, 36], [33900, 36], [34200, 37], [34500, 37], [34800, 37], [35100, 37], [35400, 37], [35700, 37], [36000, 37], [36300, 38], [36600, 38], [36900, 38], [37200, 38], [37500, 38], [37800, 38], [38100, 38], [38400, 34], [38702, 8]], "point": [24, 111]}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|-01.79|+01.35|-02.20", "placeStationary": true, "receptacleObjectId": "CounterTop|-01.81|+01.36|+01.18"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 111, 129, 300], "mask": [[33106, 4], [33125, 5], [33425, 4], [33725, 4], [34006, 7], [34017, 2], [34025, 3], [34304, 15], [34325, 2], [34579, 8], [34603, 24], [34866, 30], [34902, 24], [35158, 38], [35202, 23], [35457, 68], [35757, 68], [36056, 69], [36346, 1], [36356, 69], [36644, 81], [36941, 84], [37242, 83], [37543, 82], [37834, 2], [37843, 82], [38187, 38], [38486, 38], [38785, 39], [39084, 40], [39382, 42], [39681, 42], [39980, 43], [40278, 44], [40577, 44], [40876, 44], [41174, 45], [41473, 45], [41772, 45], [42071, 45], [42369, 45], [42668, 45], [42967, 44], [43265, 45], [43564, 45], [43863, 45], [44161, 45], [44460, 45], [44759, 45], [45068, 36], [45375, 28], [45679, 23], [45981, 21], [46282, 19], [46582, 19], [46882, 18], [47181, 19], [47480, 19], [47779, 20], [48078, 20], [48377, 20], [48677, 20], [48976, 20], [49275, 20], [49574, 20], [49873, 20], [50172, 20], [50471, 20], [50770, 20], [51069, 20], [51369, 19], [51668, 19], [51967, 19], [52266, 19], [52565, 19], [52864, 19], [53163, 19], [53462, 20], [53761, 20], [54060, 20], [54359, 21], [54659, 20], [54958, 21], [55257, 21], [55556, 22], [55855, 23], [56154, 23], [56453, 24], [56752, 25], [57051, 25], [57351, 25], [57650, 25], [57949, 26], [58248, 27], [58547, 28], [58846, 28], [59145, 29], [59444, 30], [59743, 31], [60043, 30], [60342, 31], [60641, 32], [60940, 33], [61239, 34], [61538, 34], [61837, 35], [62136, 36], [62435, 37], [62734, 37], [63034, 37], [63333, 38], [63632, 38], [63931, 39], [64230, 40], [64529, 40], [64828, 41], [65127, 42], [65426, 42], [65726, 42], [66025, 43], [66324, 43], [66623, 44], [66922, 44], [67221, 45], [67520, 46], [67819, 46], [68118, 47], [68417, 48], [68717, 47], [69016, 48], [69315, 49], [69614, 49], [69913, 50], [70212, 50], [70511, 51], [70810, 52], [71109, 52], [71409, 52], [71708, 52], [72007, 53], [72305, 54], [72604, 55], [72903, 56], [73202, 56], [73501, 57], [73800, 57], [74100, 57], [74400, 57], [74700, 56], [75000, 56], [75300, 55], [75600, 55], [75900, 54], [76200, 54], [76500, 43], [76800, 32], [77100, 26], [77400, 21], [77700, 18], [78000, 15], [78300, 16], [78600, 20], [78900, 25], [79200, 30], [79500, 38], [79800, 47], [80100, 48], [80400, 48], [80700, 48], [81000, 47], [81300, 47], [81600, 46], [81900, 46], [82200, 45], [82500, 45], [82800, 45], [83100, 44], [83400, 44], [83700, 43], [84000, 43], [84300, 42], [84600, 42], [84900, 42], [85200, 41], [85500, 41], [85800, 40], [86100, 40], [86400, 39], [86700, 39], [87000, 38], [87300, 38], [87600, 38], [87900, 37], [88200, 37], [88500, 36], [88800, 36], [89100, 35], [89400, 35], [89700, 35]], "point": [64, 204]}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 5}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 5}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 5}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-02.05|+01.40|-02.72|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [17, 97, 28, 131], "mask": [[28826, 3], [29123, 5], [29423, 4], [29721, 4], [30019, 5], [30319, 5], [30618, 5], [30918, 5], [31218, 5], [31517, 6], [31817, 6], [32117, 6], [32417, 6], [32717, 6], [33017, 6], [33317, 6], [33618, 5], [33918, 5], [34218, 6], [34519, 5], [34819, 6], [35120, 5], [35420, 5], [35720, 6], [36020, 6], [36321, 5], [36621, 5], [36921, 5], [37221, 6], [37521, 6], [37822, 5], [38122, 5], [38422, 5], [38722, 6], [39023, 5]], "point": [22, 113]}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 7}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 8}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-02.05|+01.40|-02.72|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 72, 216, 245], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54174, 31], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 23], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1], [73200, 1]], "point": [108, 157]}}, "high_idx": 8}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 245], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 109], [35827, 87], [36000, 107], [36129, 85], [36300, 106], [36430, 83], [36600, 105], [36732, 81], [36900, 104], [37033, 80], [37200, 103], [37333, 80], [37500, 103], [37634, 79], [37800, 102], [37935, 78], [38100, 102], [38235, 78], [38400, 101], [38535, 78], [38700, 101], [38835, 77], [39000, 101], [39135, 77], [39300, 101], [39435, 77], [39600, 101], [39735, 77], [39900, 102], [40035, 77], [40200, 102], [40335, 77], [40500, 103], [40634, 78], [40800, 103], [40934, 78], [41100, 103], [41234, 77], [41400, 103], [41534, 77], [41700, 104], [41835, 76], [42000, 104], [42135, 76], [42300, 104], [42435, 76], [42600, 104], [42735, 76], [42900, 104], [43035, 76], [43200, 104], [43335, 75], [43500, 103], [43635, 75], [43800, 103], [43935, 75], [44100, 103], [44236, 74], [44400, 103], [44536, 74], [44700, 103], [44836, 74], [45000, 104], [45135, 75], [45300, 104], [45435, 75], [45600, 105], [45734, 75], [45900, 115], [46023, 86], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54174, 31], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 23], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1], [73200, 1]], "point": [108, 157]}}, "high_idx": 8}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 8}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 8}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-02.05|+01.40|-02.72|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [101, 120, 135, 154], "mask": [[35809, 18], [36107, 22], [36406, 24], [36705, 27], [37004, 29], [37303, 30], [37603, 31], [37902, 33], [38202, 33], [38501, 34], [38801, 34], [39101, 34], [39401, 34], [39701, 34], [40002, 33], [40302, 33], [40603, 31], [40903, 31], [41203, 31], [41503, 31], [41804, 31], [42104, 31], [42404, 31], [42704, 31], [43004, 31], [43304, 31], [43603, 32], [43903, 32], [44203, 33], [44503, 33], [44803, 33], [45104, 31], [45404, 31], [45705, 29], [46015, 8]], "point": [118, 136]}}, "high_idx": 8}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 245], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54174, 31], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 23], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1], [73200, 1]], "point": [108, 157]}}, "high_idx": 8}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 9}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 9}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 9}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-02.05|+01.40|-02.72|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "SideTable|+01.02|+01.24|+00.87"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [40, 72, 299, 188], "mask": [[21383, 18], [21683, 18], [21982, 19], [22282, 5], [22582, 5], [22881, 6], [23181, 5], [23481, 5], [23780, 6], [24080, 5], [24380, 5], [24679, 6], [24979, 5], [25278, 6], [25578, 6], [25878, 5], [26177, 6], [26477, 6], [26777, 5], [26802, 1], [27076, 6], [27102, 1], [27376, 6], [27401, 2], [27675, 6], [27701, 3], [27975, 6], [28001, 3], [28183, 2], [28275, 6], [28301, 3], [28483, 2], [28574, 6], [28601, 3], [28783, 3], [28874, 6], [28901, 3], [29084, 3], [29174, 6], [29202, 2], [29385, 2], [29473, 6], [29503, 2], [29685, 3], [29773, 6], [29804, 1], [29986, 3], [30073, 6], [30104, 1], [30287, 2], [30372, 6], [30587, 3], [30672, 6], [30888, 3], [30971, 7], [31189, 2], [31271, 6], [31489, 3], [31571, 6], [31790, 3], [31870, 7], [32091, 2], [32170, 6], [32391, 3], [32470, 6], [32692, 3], [32769, 7], [32993, 2], [33069, 6], [33293, 3], [33368, 7], [33594, 3], [33668, 7], [33895, 2], [33968, 6], [34195, 3], [34267, 7], [34317, 20], [34431, 20], [34496, 3], [34567, 7], [34613, 29], [34727, 29], [34796, 3], [34867, 6], [34909, 36], [35024, 36], [35097, 3], [35166, 7], [35206, 42], [35321, 42], [35398, 2], [35466, 7], [35503, 47], [35619, 47], [35698, 2], [35766, 6], [35801, 51], [35918, 50], [35999, 1], [36065, 7], [36100, 54], [36216, 54], [36365, 7], [36398, 57], [36515, 57], [36664, 7], [36696, 61], [36814, 61], [36964, 7], [36994, 64], [37112, 65], [37264, 7], [37293, 66], [37412, 66], [37563, 7], [37592, 68], [37711, 68], [37863, 7], [37891, 70], [38011, 70], [38163, 7], [38190, 72], [38310, 72], [38462, 7], [38489, 74], [38610, 73], [38762, 7], [38788, 76], [38909, 76], [39061, 8], [39087, 77], [39209, 77], [39361, 7], [39386, 79], [39508, 79], [39661, 7], [39686, 79], [39808, 80], [39960, 8], [39985, 80], [40108, 81], [40260, 7], [40285, 81], [40408, 81], [40560, 7], [40584, 38], [40628, 38], [40708, 38], [40752, 38], [40859, 8], [40884, 36], [40930, 36], [41008, 36], [41054, 37], [41159, 7], [41183, 36], [41231, 36], [41308, 36], [41356, 36], [41459, 7], [41483, 35], [41532, 35], [41608, 36], [41657, 36], [41758, 8], [41782, 36], [41832, 35], [41908, 36], [41958, 35], [42058, 7], [42082, 36], [42132, 35], [42209, 35], [42258, 36], [42357, 8], [42382, 36], [42431, 36], [42509, 36], [42558, 36], [42657, 8], [42682, 37], [42730, 37], [42810, 37], [42858, 36], [42957, 7], [42982, 39], [43028, 39], [43110, 39], [43156, 39], [43256, 8], [43282, 84], [43411, 84], [43556, 8], [43582, 84], [43711, 84], [43856, 7], [43882, 84], [44011, 85], [44155, 8], [44182, 84], [44312, 84], [44455, 8], [44482, 84], [44612, 84], [44754, 8], [44782, 83], [44913, 83], [45054, 8], [45083, 82], [45214, 82], [45354, 8], [45383, 81], [45515, 81], [45653, 8], [45684, 79], [45816, 80], [45953, 8], [45984, 79], [46117, 79], [46253, 8], [46285, 77], [46418, 77], [46552, 8], [46585, 76], [46719, 76], [46852, 8], [46886, 75], [47020, 75], [47152, 8], [47186, 74], [47321, 74], [47451, 8], [47487, 72], [47622, 72], [47751, 8], [47788, 70], [47924, 69], [48050, 9], [48089, 67], [48225, 68], [48350, 8], [48390, 65], [48527, 65], [48650, 8], [48691, 63], [48829, 62], [48949, 9], [48993, 59], [49130, 60], [49249, 8], [49294, 57], [49432, 57], [49549, 8], [49595, 54], [49734, 54], [49848, 9], [49897, 50], [50037, 49], [50148, 8], [50200, 44], [50340, 44], [50447, 9], [50502, 40], [50642, 40], [50747, 9], [50805, 34], [50945, 35], [51047, 9], [51107, 29], [51248, 30], [51346, 9], [51415, 13], [51557, 13], [51646, 53], [51946, 103], [52245, 155], [52545, 206], [52844, 256], [53144, 256], [53444, 256], [53743, 257], [54043, 257], [54343, 257], [54642, 258], [54942, 258], [55242, 258], [55541, 259], [55841, 259], [56140, 260]], "point": [164, 131]}}, "high_idx": 10}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan3", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -0.75, "y": 1.12401652, "z": -0.25}, "object_poses": [{"objectName": "Potato_e8912d85", "position": {"x": 0.277946, "y": 1.36275363, "z": -2.78936434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": 0.824245334, "y": 0.344535828, "z": -1.85446763}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": 1.05303526, "y": 0.344535828, "z": -1.85446763}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.74942863, "y": 0.3447429, "z": 0.110898912}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -2.04982066, "y": 1.32392883, "z": -2.981163}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -1.81848335, "y": 0.344855428, "z": 1.52389443}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -0.6309, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.60347974, "y": 0.423119783, "z": -0.3644116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.16594541, "y": 1.32354856, "z": -2.76001024}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": 1.02584076, "y": 1.32412946, "z": -2.05370474}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -1.38003659, "y": 1.32412946, "z": -2.9817524}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.5189867, "y": 0.437571168, "z": -0.561244547}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": 0.6365833, "y": 1.3294332, "z": -2.05370474}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -2.04982138, "y": 1.36382449, "z": 2.069365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -1.79031563, "y": 1.323614, "z": 1.74455106}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -1.53080976, "y": 1.323614, "z": 2.069365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.79031563, "y": 1.35270047, "z": -2.20134926}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.88644969, "y": 0.344005942, "z": 1.11066759}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.68100739, "y": 0.280376852, "z": 2.193}, "rotation": {"x": 0.0, "y": 6.83018925e-06, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": -1.6980927, "y": 1.08497071, "z": -0.4510209}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -2.17957425, "y": 1.32130814, "z": 1.094923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": -1.602987, "y": 0.338814139, "z": -1.9633193}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": -1.74942863, "y": 0.338814139, "z": 0.6130607}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": -1.60347974, "y": 0.415021241, "z": -1.348922}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": -1.38003659, "y": 1.39739525, "z": -3.31436586}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": -2.04982066, "y": 1.39739525, "z": -2.72122478}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": 1.02584076, "y": 1.39739525, "z": -2.42411566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": 0.896088362, "y": 1.39739525, "z": -2.05370474}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.2394, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -2.04982066, "y": 1.35270047, "z": -1.68147337}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": 1.0170449, "y": 1.67342818, "z": 1.54570937}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": 0.9777746, "y": 1.79697371, "z": 2.3483057}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.40105689, "y": 1.32319188, "z": 1.74455106}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": 0.813877642, "y": 0.7413571, "z": 1.05461586}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": -1.85165894, "y": 0.341211677, "z": -1.76394641}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -0.749, "y": 1.3324, "z": -2.973}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -2.04982138, "y": 1.36382449, "z": 0.445294976}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_9d168802", "position": {"x": -1.88114953, "y": 0.327762932, "z": -2.03532434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": -1.38003659, "y": 1.31800008, "z": -2.76001024}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": 0.900508642, "y": 0.3459003, "z": -2.06108117}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.89176786, "y": 1.40042925, "z": -1.07341027}, "rotation": {"x": 1.04642177, "y": 359.6988, "z": 355.7077}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.82631838, "y": 1.07898974, "z": -0.4966268}, "rotation": {"x": 0.0, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "PaperTowelRoll_ddd73f57", "position": {"x": -1.66056263, "y": 1.42728543, "z": 1.4197371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 0.9578094, "y": 1.98004115, "z": 1.78890634}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -1.74942863, "y": 0.344428062, "z": 0.311763644}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": -1.77246583, "y": 0.415021241, "z": -1.02086711}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": 0.766335964, "y": 1.32256436, "z": -2.42411566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": 0.766335964, "y": 1.32412946, "z": -2.05370474}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": 0.9385045, "y": 1.80610383, "z": 2.11270857}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": -1.75051689, "y": 0.33881402, "z": 1.317281}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1804851122, "scene_num": 3}, "task_id": "trial_T20190907_050708_989106", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1OWHPMKE7YAGL_3FIJLY1B6XLJ4OBM884Y1ZL1S6OPFZ", "high_descs": ["Go forward towards the stove and then turn right toward the counter with the toaster. ", "Pick up the big knife with the yellow handle off the counter. ", "Use the knife to cut the bread to the left of the toaster into slices. ", "Turn to the right and move towards the sink. ", "Stand the knife up by sticking the tip of the blade into the counter in front of the sink. ", "Turn around and go back to the bread on the counter. ", "Pick up a slice of the bread. ", "Turn around and head towards the other side of the room, then go right to the microwave.", "Open the microwave and place the bread inside, close the microwave and turn it on to cook the bread, remove the bread from the microwave and close the door. ", "Move over to the left side of the counter in front of the microwave. ", "Place the cooked bread on the counter. "], "task_desc": "Place a slice of cooked bread on the counter. ", "votes": [1, 1, 1]}, {"assignment_id": "A17TKHT8FEVH0R_3NGI5ARFTWMSE9HE19PDCXH7RVO1PR", "high_descs": ["Move forward and stop at the counter on your right", "Grab the large knife that is on the counter", "Slice the loaf of bread that is on the left side of the counter", "Turn to your right", "Place the knife in front of the sink with the pointed edge facing down", "Turn to your left and face the counter", "Grab a slice of bread", "Turn around and head to the microwave", "Cook the bread inside the microwave and grab the bread", "Move backwards slightly while in front of the microwave", "Place the bread on the left side of the black table"], "task_desc": "Placing cooked bread on a table", "votes": [1, 1, 1]}, {"assignment_id": "A23HZ18KTCK2DA_39ZSFO5CABDC1V5E8KUDQ30ST2JUJ5", "high_descs": ["Go to the toaster that is on the counter.", "Pick up the knife that is on the counter to the right of the toaster.", "With the knife in hand slice the bread that is to the left of the toaster", "Go to the the sink with the knife in hand.", "By the left side of the sink stick the knife in the counter in front of sink.", "Turn around and go back to the toaster.", "Pick up a slice of bread that is to the left of the toaster.", "With the slice of bread in hand go to the microwave.", "Open the microwave and put the slice of bread in.  Close the door and start the microwave.  Take out the bread and close the door.", "Turn just a little so you are near the same counter the microwave is on but closer to the left side.", "Place the bread on the front left of the counter in front of the microwave standing up on it's end."], "task_desc": "Slice bread to warm it up.", "votes": [1, 1]}, {"assignment_id": "A6SR4BU227GUH_3R9WASFE22XDISDSVH20SXNF2CRFZK", "high_descs": ["go straight towards the oven, then turn right and face the counter top near the toaster", "pick up the knife with the yellow handle on the counter top", "slice the bread behind the toaster on the counter top", "turn right towards the sink", "place the knife in front of the sink vertically and standing up", "go back to the counter top with the toaster", "pick up a slice of toast", "turn right until you reach the wall, then turn right again and face the microwave", "open the microwave door and place the toast in the microwave for several seconds, then remove from the microwave", "face the black table on which the microwave sits", "place the toast standing up in front of the microwave"], "task_desc": "Slice toast and heat it up in the microwave", "votes": [1, 1]}, {"assignment_id": "A38Z99XF4NDNH0_3ZSY5X72N0SYME4UK3RDTOUULW7ORU", "high_descs": ["Move to the countertop to the right of the oven", "Pick up the knife with the yellow handle next to the toaster", "Cut the bread on top of the counter", "Carry the knife to the counter top where the sink is", "Place the knife on top of the counter in front of the sink", "Move to your left by the counter top with the toaster", "Pick up a slice of bread to the left of the toaster", "Carry the bread to the microwave to the right of the fridge", "Place the bread in the microwave turn it on then remove the bread", "Move to the left side of the table top where the microwave is", "Place the bread on the left corner in front of the microwave"], "task_desc": "Cut a slice of bread to heat in the microwave", "votes": [1, 1]}, {"assignment_id": "A2OK2IR4FI636Y_3LJ7UR74RKU93N2K8CVN66JF3ZLN4G", "high_descs": ["Move toward the oven, then turn right toward the toaster.", "Pick up the knife to the right of the toaster.", "Slice the bread to the left of the toaster.", "Turn right to face the wall.", "Place the knife on the counter.", "Turn back to the toaster.", "Pick up a slice of bread.", "Cross the room, then turn right to the microwave.", "Place the bread in the microwave, cook it, then open the microwave and remove the bread.", "Turn toward the counter in front of the microwave.", "Place the bread on the counter."], "task_desc": "Cook a slice of bread in the microwave.", "votes": [0, 1, 1]}]}}