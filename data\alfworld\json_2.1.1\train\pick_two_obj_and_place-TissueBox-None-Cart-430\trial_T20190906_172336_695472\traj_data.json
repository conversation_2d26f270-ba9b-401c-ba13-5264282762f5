{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000205.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000206.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000207.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000208.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000299.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000314.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000315.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000316.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000317.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000318.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000319.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000320.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000321.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000322.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000323.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000324.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000325.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000326.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000327.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000328.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000329.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000330.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000331.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000332.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000333.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000334.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000335.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000336.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000337.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000338.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000339.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000340.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000341.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000342.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000343.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000344.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000345.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000346.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000347.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000348.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000349.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000350.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000351.png", "low_idx": 66}, {"high_idx": 6, "image_name": "000000352.png", "low_idx": 66}, {"high_idx": 6, "image_name": "000000353.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000354.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000355.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000356.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000357.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000358.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000359.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000360.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000361.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000362.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000363.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000364.png", "low_idx": 68}, {"high_idx": 6, "image_name": "000000365.png", "low_idx": 68}, {"high_idx": 6, "image_name": "000000366.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000367.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000368.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000369.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000370.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000371.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000372.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000373.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000374.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000375.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000376.png", "low_idx": 74}, {"high_idx": 6, "image_name": "000000377.png", "low_idx": 74}, {"high_idx": 6, "image_name": "000000378.png", "low_idx": 75}, {"high_idx": 6, "image_name": "000000379.png", "low_idx": 75}, {"high_idx": 6, "image_name": "000000380.png", "low_idx": 75}, {"high_idx": 6, "image_name": "000000381.png", "low_idx": 75}, {"high_idx": 6, "image_name": "000000382.png", "low_idx": 75}, {"high_idx": 6, "image_name": "000000383.png", "low_idx": 75}, {"high_idx": 6, "image_name": "000000384.png", "low_idx": 75}, {"high_idx": 6, "image_name": "000000385.png", "low_idx": 75}, {"high_idx": 6, "image_name": "000000386.png", "low_idx": 75}, {"high_idx": 6, "image_name": "000000387.png", "low_idx": 75}, {"high_idx": 6, "image_name": "000000388.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000389.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000390.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000391.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000392.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000393.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000394.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000395.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000396.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000397.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000398.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000399.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000400.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000401.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000402.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000403.png", "low_idx": 76}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "TissueBox", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|-6|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["tissuebox"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["TissueBox", [-0.833818972, -0.833818972, -7.22128344, -7.22128344, 3.853782176, 3.853782176]], "coordinateReceptacleObjectId": ["CounterTop", [0.7864308, 0.7864308, -8.044, -8.044, 1.724, 1.724]], "forceVisible": true, "objectId": "TissueBox|-00.21|+00.96|-01.81"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cart"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-9|6|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["tissuebox", "cart"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["TissueBox", [-0.833818972, -0.833818972, -7.22128344, -7.22128344, 3.853782176, 3.853782176]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-11.66, -11.66, 5.924, 5.924, 0.048, 0.048]], "forceVisible": true, "objectId": "TissueBox|-00.21|+00.96|-01.81", "receptacleObjectId": "Cart|-02.92|+00.01|+01.48"}}, {"discrete_action": {"action": "GotoLocation", "args": ["toilet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|0|5|0|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["tissuebox"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["TissueBox", [0.0420148076, 0.0420148076, 9.1090994, 9.1090994, 3.864450932, 3.864450932]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-0.256, -0.256, 7.356, 7.356, 0.048159182, 0.048159182]], "forceVisible": true, "objectId": "TissueBox|+00.01|+00.97|+02.28"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cart"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-9|6|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["tissuebox", "cart"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["TissueBox", [0.0420148076, 0.0420148076, 9.1090994, 9.1090994, 3.864450932, 3.864450932]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-11.66, -11.66, 5.924, 5.924, 0.048, 0.048]], "forceVisible": true, "objectId": "TissueBox|+00.01|+00.97|+02.28", "receptacleObjectId": "Cart|-02.92|+00.01|+01.48"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "TissueBox|-00.21|+00.96|-01.81"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [193, 138, 278, 185], "mask": [[41297, 73], [41597, 73], [41896, 75], [42196, 75], [42496, 75], [42795, 76], [43095, 77], [43395, 77], [43695, 77], [43994, 79], [44294, 79], [44594, 79], [44893, 80], [45193, 81], [45493, 81], [45793, 81], [46093, 81], [46393, 82], [46693, 82], [46993, 82], [47293, 83], [47593, 83], [47893, 83], [48193, 83], [48493, 84], [48794, 83], [49094, 83], [49394, 83], [49694, 84], [49994, 84], [50294, 84], [50594, 85], [50894, 85], [51194, 85], [51494, 85], [51794, 84], [52095, 82], [52395, 81], [52695, 80], [52995, 79], [53295, 78], [53595, 77], [53895, 76], [54195, 75], [54495, 74], [54795, 73], [55096, 71], [55396, 70]], "point": [235, 160]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "TissueBox|-00.21|+00.96|-01.81", "placeStationary": true, "receptacleObjectId": "Cart|-02.92|+00.01|+01.48"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 283, 211], "mask": [[0, 7], [48, 9], [227, 9], [269, 15], [300, 7], [348, 9], [527, 9], [569, 15], [600, 6], [649, 8], [827, 9], [869, 15], [900, 6], [949, 9], [1127, 8], [1169, 15], [1200, 6], [1249, 9], [1426, 9], [1469, 15], [1500, 6], [1550, 8], [1726, 9], [1770, 14], [1800, 6], [1850, 9], [2026, 8], [2070, 14], [2100, 6], [2150, 9], [2326, 8], [2370, 14], [2400, 6], [2451, 8], [2625, 9], [2670, 14], [2700, 6], [2751, 9], [2925, 9], [2970, 14], [3000, 6], [3051, 9], [3225, 8], [3270, 13], [3300, 6], [3352, 8], [3525, 8], [3570, 13], [3600, 6], [3652, 9], [3824, 9], [3870, 13], [3900, 6], [3952, 9], [4124, 8], [4170, 13], [4200, 6], [4253, 8], [4424, 8], [4469, 14], [4500, 7], [4553, 9], [4724, 8], [4769, 14], [4800, 7], [4853, 9], [5023, 8], [5069, 14], [5100, 7], [5154, 8], [5323, 8], [5369, 14], [5400, 7], [5454, 8], [5623, 8], [5669, 14], [5700, 7], [5754, 9], [5922, 9], [5969, 14], [6000, 7], [6055, 8], [6222, 8], [6269, 14], [6300, 7], [6355, 8], [6522, 8], [6569, 13], [6600, 7], [6655, 175], [6869, 13], [6900, 7], [6956, 173], [7169, 13], [7200, 8], [7256, 173], [7469, 13], [7500, 8], [7556, 173], [7768, 14], [7800, 8], [7855, 174], [8068, 14], [8100, 8], [8155, 10], [8320, 10], [8368, 14], [8400, 8], [8455, 11], [8620, 10], [8668, 14], [8700, 8], [8755, 11], [8920, 10], [8968, 14], [9000, 8], [9054, 12], [9220, 10], [9268, 14], [9300, 8], [9354, 13], [9519, 12], [9568, 14], [9600, 8], [9654, 13], [9819, 12], [9868, 14], [9900, 9], [9953, 14], [10119, 12], [10168, 13], [10200, 9], [10253, 14], [10419, 12], [10468, 13], [10500, 9], [10553, 6], [10560, 8], [10718, 14], [10768, 13], [10800, 9], [10853, 6], [10860, 8], [11018, 14], [11067, 14], [11100, 9], [11152, 7], [11161, 7], [11318, 7], [11326, 6], [11367, 14], [11400, 9], [11452, 7], [11461, 8], [11618, 7], [11626, 6], [11667, 14], [11700, 9], [11752, 6], [11761, 8], [11917, 8], [11926, 7], [11967, 14], [12000, 9], [12051, 7], [12062, 7], [12217, 8], [12226, 7], [12267, 14], [12300, 9], [12351, 7], [12362, 8], [12517, 7], [12527, 6], [12567, 13], [12600, 9], [12651, 7], [12662, 8], [12817, 7], [12827, 6], [12867, 13], [12900, 10], [12951, 6], [12963, 7], [13116, 8], [13127, 6], [13167, 13], [13200, 10], [13250, 7], [13263, 8], [13416, 7], [13427, 7], [13467, 13], [13500, 10], [13550, 7], [13563, 8], [13716, 7], [13727, 7], [13767, 13], [13800, 10], [13850, 7], [13864, 7], [14016, 7], [14028, 6], [14067, 13], [14100, 10], [14149, 7], [14164, 7], [14315, 8], [14328, 6], [14366, 14], [14400, 10], [14449, 7], [14464, 8], [14615, 7], [14628, 7], [14666, 14], [14700, 11], [14749, 7], [14765, 75], [14844, 78], [14928, 7], [14966, 13], [15000, 11], [15049, 6], [15065, 75], [15144, 78], [15229, 6], [15266, 13], [15300, 11], [15348, 7], [15365, 75], [15444, 77], [15529, 6], [15566, 13], [15600, 11], [15648, 7], [15666, 74], [15744, 77], [15829, 7], [15866, 13], [15900, 11], [15948, 7], [15966, 74], [16044, 77], [16129, 7], [16165, 14], [16200, 11], [16247, 7], [16266, 8], [16413, 8], [16430, 6], [16465, 14], [16500, 12], [16547, 7], [16565, 9], [16713, 8], [16730, 6], [16765, 14], [16800, 12], [16847, 7], [16865, 9], [17013, 8], [17030, 7], [17065, 14], [17100, 12], [17147, 7], [17165, 10], [17313, 8], [17330, 7], [17365, 13], [17400, 12], [17446, 7], [17465, 10], [17612, 10], [17630, 7], [17665, 13], [17700, 12], [17746, 7], [17765, 75], [17845, 77], [17931, 6], [17965, 13], [18000, 12], [18046, 7], [18064, 76], [18145, 77], [18231, 7], [18264, 14], [18300, 13], [18345, 7], [18364, 76], [18445, 77], [18531, 7], [18564, 14], [18600, 13], [18645, 7], [18664, 76], [18745, 77], [18831, 7], [18864, 14], [18900, 13], [18945, 7], [18964, 76], [19045, 78], [19132, 6], [19164, 14], [19200, 13], [19244, 8], [19263, 6], [19270, 7], [19411, 12], [19432, 7], [19464, 14], [19500, 13], [19544, 7], [19563, 6], [19570, 7], [19711, 12], [19732, 7], [19764, 13], [19800, 13], [19844, 7], [19863, 6], [19870, 7], [20010, 13], [20032, 7], [20063, 14], [20100, 14], [20144, 7], [20163, 6], [20171, 7], [20310, 7], [20318, 5], [20332, 7], [20363, 14], [20400, 14], [20443, 8], [20462, 6], [20471, 7], [20610, 7], [20618, 6], [20633, 7], [20663, 14], [20700, 14], [20743, 7], [20762, 6], [20771, 7], [20910, 6], [20918, 6], [20933, 7], [20963, 14], [21000, 14], [21043, 7], [21062, 6], [21072, 7], [21209, 7], [21218, 6], [21233, 7], [21263, 14], [21300, 14], [21342, 8], [21362, 50], [21431, 8], [21447, 77], [21533, 7], [21563, 14], [21600, 15], [21642, 8], [21661, 51], [21732, 6], [21747, 78], [21834, 7], [21863, 14], [21900, 15], [21942, 7], [21961, 51], [22032, 7], [22047, 78], [22134, 7], [22162, 15], [22200, 15], [22242, 7], [22261, 51], [22332, 7], [22347, 78], [22434, 7], [22462, 15], [22500, 15], [22541, 8], [22561, 51], [22632, 6], [22648, 77], [22734, 7], [22762, 15], [22800, 15], [22841, 7], [22860, 7], [22874, 7], [23008, 6], [23019, 6], [23034, 8], [23062, 14], [23100, 15], [23141, 7], [23160, 7], [23174, 7], [23307, 7], [23319, 6], [23335, 7], [23362, 14], [23400, 16], [23440, 8], [23460, 6], [23474, 7], [23607, 7], [23620, 6], [23635, 7], [23662, 14], [23700, 16], [23740, 8], [23760, 6], [23775, 6], [23907, 6], [23920, 6], [23935, 7], [23961, 15], [24001, 15], [24040, 7], [24060, 6], [24075, 7], [24207, 6], [24220, 6], [24235, 7], [24261, 15], [24301, 15], [24340, 7], [24359, 7], [24376, 6], [24506, 7], [24520, 6], [24536, 7], [24561, 15], [24601, 15], [24639, 8], [24659, 52], [24731, 95], [24836, 7], [24861, 15], [24902, 14], [24939, 8], [24959, 52], [25031, 96], [25136, 7], [25161, 14], [25202, 15], [25239, 7], [25259, 52], [25331, 96], [25436, 7], [25460, 15], [25502, 15], [25538, 8], [25558, 53], [25631, 96], [25736, 8], [25760, 15], [25803, 15], [25838, 8], [25858, 53], [25931, 96], [26037, 7], [26060, 14], [26103, 16], [26138, 7], [26158, 53], [26231, 96], [26337, 7], [26359, 15], [26403, 16], [26438, 7], [26458, 6], [26478, 6], [26605, 6], [26621, 7], [26637, 7], [26659, 15], [26704, 16], [26737, 8], [26757, 7], [26778, 7], [26904, 6], [26922, 6], [26937, 8], [26958, 15], [27004, 17], [27037, 8], [27057, 7], [27079, 6], [27204, 6], [27222, 6], [27238, 7], [27258, 15], [27305, 16], [27337, 7], [27357, 6], [27379, 6], [27504, 6], [27522, 6], [27538, 7], [27557, 15], [27606, 16], [27636, 8], [27657, 6], [27679, 6], [27804, 6], [27822, 6], [27838, 7], [27857, 15], [27906, 16], [27936, 8], [27956, 7], [27980, 6], [28103, 6], [28122, 7], [28138, 8], [28156, 15], [28207, 16], [28236, 8], [28256, 7], [28280, 6], [28403, 6], [28423, 6], [28438, 8], [28456, 14], [28508, 16], [28536, 7], [28556, 54], [28631, 98], [28739, 7], [28755, 15], [28809, 15], [28835, 8], [28856, 54], [28931, 98], [29039, 7], [29055, 14], [29109, 16], [29135, 8], [29156, 54], [29231, 98], [29339, 8], [29354, 15], [29410, 15], [29435, 8], [29455, 55], [29531, 99], [29639, 8], [29654, 14], [29711, 15], [29734, 8], [29755, 55], [29831, 99], [29940, 7], [29953, 15], [30011, 16], [30034, 8], [30055, 6], [30082, 6], [30202, 5], [30224, 6], [30240, 7], [30253, 14], [30312, 15], [30334, 8], [30355, 6], [30382, 6], [30501, 6], [30524, 6], [30540, 8], [30552, 14], [30613, 15], [30633, 8], [30654, 7], [30683, 6], [30801, 6], [30824, 6], [30840, 8], [30852, 14], [30913, 16], [30933, 8], [30954, 7], [30983, 6], [31101, 5], [31124, 7], [31140, 8], [31151, 14], [31214, 15], [31233, 8], [31254, 7], [31283, 6], [31401, 5], [31424, 7], [31441, 7], [31451, 14], [31515, 15], [31533, 8], [31554, 6], [31584, 6], [31700, 6], [31725, 6], [31741, 8], [31750, 14], [31815, 15], [31832, 8], [31853, 7], [31884, 6], [32000, 6], [32025, 6], [32041, 8], [32050, 13], [32116, 15], [32132, 8], [32153, 179], [32341, 22], [32417, 23], [32453, 179], [32642, 20], [32718, 22], [32753, 179], [32942, 20], [33018, 21], [33052, 180], [33242, 19], [33319, 20], [33352, 180], [33542, 18], [33620, 19], [33652, 7], [33686, 118], [33826, 6], [33843, 17], [33920, 18], [33952, 7], [33986, 118], [34126, 7], [34143, 16], [34221, 17], [34251, 7], [34286, 6], [34398, 6], [34426, 7], [34443, 16], [34522, 16], [34551, 7], [34586, 7], [34698, 6], [34726, 7], [34743, 15], [34822, 16], [34851, 7], [34886, 7], [34997, 7], [35027, 6], [35043, 15], [35123, 14], [35151, 7], [35186, 7], [35297, 7], [35327, 6], [35344, 13], [35424, 14], [35451, 6], [35486, 8], [35597, 7], [35627, 7], [35643, 13], [35725, 231], [36025, 230], [36326, 229], [36627, 227], [36927, 226], [37228, 225], [37529, 223], [37829, 14], [37849, 7], [37884, 4], [37891, 5], [37995, 5], [38001, 4], [38028, 7], [38039, 13], [38130, 13], [38148, 7], [38184, 4], [38191, 5], [38295, 5], [38301, 4], [38329, 6], [38339, 12], [38431, 13], [38448, 7], [38484, 4], [38492, 5], [38594, 5], [38601, 5], [38629, 7], [38638, 13], [38731, 14], [38748, 7], [38784, 4], [38792, 5], [38894, 5], [38901, 5], [38929, 7], [38938, 12], [39032, 13], [39048, 7], [39084, 4], [39092, 5], [39194, 5], [39202, 4], [39229, 7], [39237, 12], [39333, 13], [39347, 8], [39384, 4], [39393, 5], [39494, 4], [39502, 4], [39529, 7], [39537, 12], [39634, 12], [39647, 7], [39683, 5], [39693, 5], [39793, 5], [39802, 4], [39830, 18], [39934, 214], [40235, 212], [40536, 210], [40836, 210], [41137, 208], [41438, 15], [41482, 5], [41494, 6], [41592, 5], [41602, 5], [41631, 14], [41738, 15], [41782, 5], [41794, 6], [41891, 6], [41903, 4], [41931, 13], [42039, 14], [42082, 4], [42094, 6], [42191, 7], [42203, 4], [42231, 12], [42340, 203], [42641, 201], [42941, 201], [43242, 199], [43543, 198], [43843, 197], [44144, 195], [44445, 11], [44481, 4], [44492, 15], [44585, 13], [44604, 4], [44628, 11], [44745, 12], [44781, 4], [44792, 15], [44885, 13], [44904, 4], [44927, 11], [45046, 12], [45081, 4], [45092, 15], [45185, 14], [45204, 4], [45227, 11], [45347, 11], [45380, 5], [45392, 37], [45457, 42], [45504, 4], [45526, 11], [45647, 12], [45680, 5], [45692, 36], [45758, 41], [45804, 5], [45826, 10], [45948, 11], [45980, 5], [45992, 36], [46065, 34], [46104, 5], [46125, 11], [46249, 11], [46280, 4], [46292, 3], [46296, 10], [46386, 13], [46404, 5], [46425, 10], [46550, 11], [46580, 4], [46592, 3], [46598, 6], [46688, 6], [46695, 4], [46705, 4], [46724, 11], [46850, 11], [46880, 4], [46891, 4], [46995, 4], [47005, 4], [47024, 10], [47151, 11], [47180, 4], [47191, 4], [47296, 3], [47305, 4], [47323, 11], [47452, 11], [47479, 5], [47491, 4], [47596, 3], [47605, 4], [47623, 10], [47752, 11], [47779, 5], [47791, 4], [47896, 4], [47905, 5], [47922, 10], [48053, 11], [48079, 5], [48091, 41], [48166, 34], [48205, 5], [48222, 10], [48354, 10], [48379, 4], [48391, 42], [48467, 33], [48505, 5], [48521, 10], [48654, 11], [48679, 4], [48691, 43], [48767, 33], [48805, 5], [48821, 10], [48955, 11], [48979, 4], [48991, 3], [49096, 4], [49105, 5], [49120, 10], [49256, 10], [49278, 5], [49290, 4], [49396, 4], [49406, 4], [49420, 9], [49557, 10], [49578, 5], [49590, 4], [49696, 4], [49706, 4], [49719, 10], [49857, 11], [49878, 5], [49890, 4], [49996, 4], [50006, 4], [50019, 9], [50158, 10], [50178, 5], [50190, 4], [50297, 3], [50306, 5], [50318, 10], [50459, 10], [50478, 4], [50490, 4], [50597, 4], [50606, 5], [50618, 9], [50759, 10], [50778, 4], [50790, 55], [50869, 32], [50906, 5], [50917, 9], [51060, 10], [51078, 4], [51090, 57], [51168, 33], [51206, 5], [51217, 9], [51361, 10], [51377, 5], [51390, 59], [51468, 33], [51506, 5], [51516, 9], [51661, 10], [51677, 5], [51689, 4], [51797, 4], [51807, 4], [51816, 9], [51962, 10], [51977, 5], [51989, 4], [52097, 4], [52107, 4], [52115, 9], [52263, 9], [52277, 5], [52289, 4], [52397, 4], [52407, 4], [52415, 9], [52563, 10], [52577, 4], [52589, 4], [52697, 4], [52707, 5], [52714, 9], [52864, 10], [52877, 4], [52889, 4], [52997, 4], [53007, 5], [53014, 8], [53165, 9], [53176, 5], [53189, 4], [53298, 3], [53307, 5], [53313, 9], [53466, 9], [53476, 5], [53489, 113], [53607, 14], [53766, 15], [53789, 113], [53907, 14], [54067, 14], [54088, 114], [54208, 12], [54368, 13], [54388, 4], [54498, 4], [54508, 11], [54668, 12], [54688, 4], [54798, 4], [54808, 11], [54969, 11], [54988, 4], [55098, 4], [55108, 10], [55270, 10], [55288, 4], [55398, 4], [55408, 10], [55570, 10], [55588, 4], [55698, 4], [55708, 9], [55871, 9], [55888, 4], [55998, 4], [56008, 8], [56172, 144], [56473, 142], [56773, 142], [57074, 140], [57375, 139], [57675, 9], [57687, 4], [57799, 4], [57805, 8], [57976, 8], [57987, 4], [58099, 4], [58105, 7], [58277, 8], [58287, 4], [58399, 4], [58404, 8], [58577, 8], [58586, 5], [58699, 4], [58704, 7], [58878, 12], [58999, 12], [59179, 131], [59479, 130], [59780, 129], [60081, 9], [60200, 8], [60382, 8], [60500, 8], [60682, 8], [60800, 7], [60983, 124], [61284, 122], [61584, 121], [61885, 120], [62186, 12], [62293, 11], [62486, 13], [62592, 12], [62787, 13], [62891, 12], [63087, 13], [63191, 13]], "point": [141, 107]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "TissueBox|+00.01|+00.97|+02.28"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [141, 125, 163, 166], "mask": [[37349, 2], [37648, 4], [37947, 6], [38246, 7], [38547, 7], [38848, 6], [39149, 5], [39442, 20], [39742, 21], [40042, 21], [40342, 21], [40642, 21], [40942, 21], [41242, 21], [41542, 21], [41841, 22], [42141, 22], [42441, 22], [42741, 22], [43041, 23], [43341, 23], [43641, 23], [43941, 23], [44241, 23], [44541, 23], [44841, 23], [45141, 23], [45441, 23], [45741, 23], [46041, 23], [46341, 23], [46641, 23], [46941, 23], [47241, 23], [47541, 23], [47841, 23], [48141, 23], [48441, 23], [48741, 23], [49041, 23], [49341, 23], [49641, 23]], "point": [152, 144]}}, "high_idx": 5}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "TissueBox|+00.01|+00.97|+02.28", "placeStationary": true, "receptacleObjectId": "Cart|-02.92|+00.01|+01.48"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 283, 206], "mask": [[0, 7], [48, 9], [227, 9], [269, 15], [300, 7], [348, 9], [527, 9], [569, 15], [600, 6], [649, 8], [827, 9], [869, 15], [900, 6], [949, 9], [1127, 8], [1169, 15], [1200, 6], [1249, 9], [1426, 9], [1469, 15], [1500, 6], [1550, 8], [1726, 9], [1770, 14], [1800, 6], [1850, 9], [2026, 8], [2070, 14], [2100, 6], [2150, 9], [2326, 8], [2370, 14], [2400, 6], [2451, 8], [2625, 9], [2670, 14], [2700, 6], [2751, 9], [2925, 9], [2970, 14], [3000, 6], [3051, 9], [3225, 8], [3270, 13], [3300, 6], [3352, 8], [3525, 8], [3570, 13], [3600, 6], [3652, 9], [3824, 9], [3870, 13], [3900, 6], [3952, 9], [4124, 8], [4170, 13], [4200, 6], [4253, 8], [4424, 8], [4469, 14], [4500, 7], [4553, 9], [4724, 8], [4769, 14], [4800, 7], [4853, 9], [5023, 8], [5069, 14], [5100, 7], [5154, 8], [5323, 8], [5369, 14], [5400, 7], [5454, 8], [5623, 8], [5669, 14], [5700, 7], [5754, 9], [5922, 9], [5969, 14], [6000, 7], [6055, 8], [6222, 8], [6269, 14], [6300, 7], [6355, 8], [6522, 8], [6569, 13], [6600, 7], [6655, 175], [6869, 13], [6900, 7], [6956, 173], [7169, 13], [7200, 8], [7256, 173], [7469, 13], [7500, 8], [7556, 173], [7768, 14], [7800, 8], [7855, 174], [8068, 14], [8100, 8], [8155, 10], [8320, 10], [8368, 14], [8400, 8], [8455, 11], [8620, 10], [8668, 14], [8700, 8], [8755, 11], [8920, 10], [8968, 14], [9000, 8], [9054, 12], [9220, 10], [9268, 14], [9300, 8], [9354, 13], [9519, 12], [9568, 14], [9600, 8], [9654, 13], [9819, 12], [9868, 14], [9900, 9], [9953, 14], [10119, 12], [10168, 13], [10200, 9], [10253, 14], [10419, 12], [10468, 13], [10500, 9], [10553, 6], [10560, 8], [10718, 14], [10768, 13], [10800, 9], [10853, 6], [10860, 8], [11018, 14], [11067, 14], [11100, 9], [11152, 7], [11161, 7], [11318, 7], [11326, 6], [11367, 14], [11400, 9], [11452, 7], [11461, 8], [11618, 7], [11626, 6], [11667, 14], [11700, 9], [11752, 6], [11761, 8], [11917, 8], [11926, 7], [11967, 14], [12000, 9], [12051, 7], [12062, 7], [12217, 8], [12226, 7], [12267, 14], [12300, 9], [12351, 7], [12362, 8], [12517, 7], [12527, 6], [12567, 13], [12600, 9], [12651, 7], [12662, 8], [12817, 7], [12827, 6], [12867, 13], [12900, 10], [12951, 6], [12963, 7], [13116, 8], [13127, 6], [13167, 13], [13200, 10], [13250, 7], [13263, 8], [13416, 7], [13427, 7], [13467, 13], [13500, 10], [13550, 7], [13563, 8], [13716, 7], [13727, 7], [13767, 13], [13800, 10], [13850, 7], [13864, 7], [14016, 7], [14028, 6], [14067, 13], [14100, 10], [14149, 7], [14164, 7], [14315, 8], [14328, 6], [14366, 14], [14400, 10], [14449, 7], [14464, 8], [14615, 7], [14628, 7], [14666, 14], [14700, 11], [14749, 7], [14765, 75], [14844, 78], [14928, 7], [14966, 13], [15000, 11], [15049, 6], [15065, 75], [15144, 78], [15229, 6], [15266, 13], [15300, 11], [15348, 7], [15365, 75], [15444, 77], [15529, 6], [15566, 13], [15600, 11], [15648, 7], [15666, 74], [15744, 77], [15829, 7], [15866, 13], [15900, 11], [15948, 7], [15966, 74], [16044, 77], [16129, 7], [16165, 14], [16200, 11], [16247, 7], [16266, 8], [16413, 8], [16430, 6], [16465, 14], [16500, 12], [16547, 7], [16565, 9], [16713, 8], [16730, 6], [16765, 14], [16800, 12], [16847, 7], [16865, 9], [17013, 8], [17030, 7], [17065, 14], [17100, 12], [17147, 7], [17165, 10], [17313, 8], [17330, 7], [17365, 13], [17400, 12], [17446, 7], [17465, 10], [17612, 10], [17630, 7], [17665, 13], [17700, 12], [17746, 7], [17765, 75], [17845, 77], [17931, 6], [17965, 13], [18000, 12], [18046, 7], [18064, 76], [18145, 77], [18231, 7], [18264, 14], [18300, 13], [18345, 7], [18364, 76], [18445, 77], [18531, 7], [18564, 14], [18600, 13], [18645, 7], [18664, 76], [18745, 77], [18831, 7], [18864, 14], [18900, 13], [18945, 7], [18964, 76], [19045, 78], [19132, 6], [19164, 14], [19200, 13], [19244, 8], [19263, 6], [19270, 7], [19411, 12], [19432, 7], [19464, 14], [19500, 13], [19544, 7], [19563, 6], [19570, 7], [19711, 12], [19732, 7], [19764, 13], [19800, 13], [19844, 7], [19863, 6], [19870, 7], [20010, 13], [20032, 7], [20063, 14], [20100, 14], [20144, 7], [20163, 6], [20171, 7], [20310, 7], [20318, 5], [20332, 7], [20363, 14], [20400, 14], [20443, 8], [20462, 6], [20471, 7], [20610, 7], [20618, 6], [20633, 7], [20663, 14], [20700, 14], [20743, 7], [20762, 6], [20771, 7], [20910, 6], [20918, 6], [20933, 7], [20963, 14], [21000, 14], [21043, 7], [21062, 6], [21072, 7], [21209, 7], [21218, 6], [21233, 7], [21263, 14], [21300, 14], [21342, 8], [21362, 50], [21431, 8], [21447, 4], [21485, 39], [21533, 7], [21563, 14], [21600, 15], [21642, 8], [21661, 51], [21732, 6], [21747, 4], [21785, 40], [21834, 7], [21863, 14], [21900, 15], [21942, 7], [21961, 51], [22032, 7], [22047, 4], [22085, 40], [22134, 7], [22162, 15], [22200, 15], [22242, 7], [22261, 51], [22332, 7], [22347, 4], [22386, 39], [22434, 7], [22462, 15], [22500, 15], [22541, 8], [22561, 51], [22632, 6], [22648, 3], [22686, 39], [22734, 7], [22762, 15], [22800, 15], [22841, 7], [22860, 7], [22874, 7], [23008, 6], [23019, 6], [23034, 8], [23062, 14], [23100, 15], [23141, 7], [23160, 7], [23174, 7], [23307, 7], [23319, 6], [23335, 7], [23362, 14], [23400, 16], [23440, 8], [23460, 6], [23474, 7], [23607, 7], [23620, 6], [23635, 7], [23662, 14], [23700, 16], [23740, 8], [23760, 6], [23775, 6], [23907, 6], [23920, 6], [23935, 7], [23961, 15], [24001, 15], [24040, 7], [24060, 6], [24075, 7], [24207, 6], [24220, 6], [24235, 7], [24261, 15], [24301, 15], [24340, 7], [24359, 7], [24376, 6], [24506, 7], [24520, 6], [24536, 7], [24561, 15], [24601, 15], [24639, 8], [24659, 52], [24731, 20], [24786, 40], [24836, 7], [24861, 15], [24902, 14], [24939, 8], [24959, 52], [25031, 20], [25086, 41], [25136, 7], [25161, 14], [25202, 15], [25239, 7], [25259, 52], [25331, 20], [25386, 41], [25436, 7], [25460, 15], [25502, 15], [25538, 8], [25558, 53], [25631, 20], [25687, 40], [25736, 8], [25760, 15], [25803, 15], [25838, 8], [25858, 53], [25931, 20], [25987, 40], [26037, 7], [26060, 14], [26103, 16], [26138, 7], [26158, 53], [26231, 20], [26287, 40], [26337, 7], [26359, 15], [26403, 16], [26438, 7], [26458, 6], [26478, 6], [26605, 6], [26621, 7], [26637, 7], [26659, 15], [26704, 16], [26737, 8], [26757, 7], [26778, 7], [26904, 6], [26922, 6], [26937, 8], [26958, 15], [27004, 17], [27037, 8], [27057, 7], [27079, 6], [27204, 6], [27222, 6], [27238, 7], [27258, 15], [27305, 16], [27337, 7], [27357, 6], [27379, 6], [27504, 6], [27522, 6], [27538, 7], [27557, 15], [27606, 16], [27636, 8], [27657, 6], [27679, 6], [27804, 6], [27822, 6], [27838, 7], [27857, 15], [27906, 16], [27936, 8], [27956, 7], [27980, 6], [28103, 6], [28122, 7], [28138, 8], [28156, 15], [28207, 16], [28236, 8], [28256, 7], [28280, 6], [28403, 6], [28423, 6], [28438, 8], [28456, 14], [28508, 16], [28536, 7], [28556, 54], [28631, 20], [28685, 44], [28739, 7], [28755, 15], [28809, 15], [28835, 8], [28856, 54], [28931, 20], [28985, 44], [29039, 7], [29055, 14], [29109, 16], [29135, 8], [29156, 54], [29231, 20], [29285, 44], [29339, 8], [29354, 15], [29410, 15], [29435, 8], [29455, 55], [29531, 20], [29585, 45], [29639, 8], [29654, 14], [29711, 15], [29734, 8], [29755, 55], [29831, 20], [29885, 45], [29940, 7], [29953, 15], [30011, 16], [30034, 8], [30055, 6], [30082, 6], [30202, 5], [30224, 6], [30240, 7], [30253, 14], [30312, 15], [30334, 8], [30355, 6], [30382, 6], [30501, 6], [30524, 6], [30540, 8], [30552, 14], [30613, 15], [30633, 8], [30654, 7], [30683, 6], [30801, 6], [30824, 6], [30840, 8], [30852, 14], [30913, 16], [30933, 8], [30954, 7], [30983, 6], [31101, 5], [31124, 7], [31140, 8], [31151, 14], [31214, 15], [31233, 8], [31254, 7], [31283, 6], [31401, 5], [31424, 7], [31441, 7], [31451, 14], [31515, 15], [31533, 8], [31554, 6], [31584, 6], [31700, 6], [31725, 6], [31741, 8], [31750, 14], [31815, 15], [31832, 8], [31853, 7], [31884, 6], [32000, 6], [32025, 6], [32041, 8], [32050, 13], [32116, 15], [32132, 8], [32153, 98], [32283, 49], [32341, 22], [32417, 23], [32453, 98], [32583, 49], [32642, 20], [32718, 22], [32753, 98], [32883, 49], [32942, 20], [33018, 21], [33052, 99], [33183, 49], [33242, 19], [33319, 20], [33352, 99], [33483, 49], [33542, 18], [33620, 19], [33652, 7], [33686, 65], [33782, 22], [33826, 6], [33843, 17], [33920, 18], [33952, 7], [33986, 65], [34082, 22], [34126, 7], [34143, 16], [34221, 17], [34251, 7], [34286, 6], [34398, 6], [34426, 7], [34443, 16], [34522, 16], [34551, 7], [34586, 7], [34698, 6], [34726, 7], [34743, 15], [34822, 16], [34851, 7], [34886, 7], [34997, 7], [35027, 6], [35043, 15], [35123, 14], [35151, 7], [35186, 7], [35297, 7], [35327, 6], [35344, 13], [35424, 14], [35451, 6], [35486, 8], [35597, 7], [35627, 7], [35643, 13], [35725, 231], [36025, 230], [36326, 229], [36627, 227], [36927, 226], [37228, 225], [37529, 223], [37829, 14], [37849, 7], [37884, 4], [37891, 5], [37995, 5], [38001, 4], [38028, 7], [38039, 13], [38130, 13], [38148, 7], [38184, 4], [38191, 5], [38295, 5], [38301, 4], [38329, 6], [38339, 12], [38431, 13], [38448, 7], [38484, 4], [38492, 5], [38594, 5], [38601, 5], [38629, 7], [38638, 13], [38731, 14], [38748, 7], [38784, 4], [38792, 5], [38894, 5], [38901, 5], [38929, 7], [38938, 12], [39032, 13], [39048, 7], [39084, 4], [39092, 5], [39194, 5], [39202, 4], [39229, 7], [39237, 12], [39333, 13], [39347, 8], [39384, 4], [39393, 5], [39494, 4], [39502, 4], [39529, 7], [39537, 12], [39634, 12], [39647, 7], [39683, 5], [39693, 5], [39793, 5], [39802, 4], [39830, 18], [39934, 214], [40235, 212], [40536, 210], [40836, 210], [41137, 208], [41438, 15], [41482, 5], [41494, 6], [41592, 5], [41602, 5], [41631, 14], [41738, 15], [41782, 5], [41794, 6], [41891, 6], [41903, 4], [41931, 13], [42039, 14], [42082, 4], [42094, 6], [42191, 7], [42203, 4], [42231, 12], [42340, 203], [42641, 201], [42941, 201], [43242, 199], [43543, 198], [43843, 197], [44144, 195], [44445, 11], [44481, 4], [44492, 15], [44585, 13], [44604, 4], [44628, 11], [44745, 12], [44781, 4], [44792, 15], [44885, 13], [44904, 4], [44927, 11], [45046, 12], [45081, 4], [45092, 15], [45185, 14], [45204, 4], [45227, 11], [45347, 11], [45380, 5], [45392, 37], [45457, 42], [45504, 4], [45526, 11], [45647, 12], [45680, 5], [45692, 36], [45758, 41], [45804, 5], [45826, 10], [45948, 11], [45980, 5], [45992, 36], [46065, 34], [46104, 5], [46125, 11], [46249, 11], [46280, 4], [46292, 3], [46296, 10], [46386, 13], [46404, 5], [46425, 10], [46550, 11], [46580, 4], [46592, 3], [46598, 6], [46688, 6], [46695, 4], [46705, 4], [46724, 11], [46850, 11], [46880, 4], [46891, 4], [46995, 4], [47005, 4], [47024, 10], [47151, 11], [47180, 4], [47191, 4], [47296, 3], [47305, 4], [47323, 11], [47452, 11], [47479, 5], [47491, 4], [47596, 3], [47605, 4], [47623, 10], [47752, 11], [47779, 5], [47791, 4], [47896, 4], [47905, 5], [47922, 10], [48053, 11], [48079, 5], [48091, 41], [48166, 34], [48205, 5], [48222, 10], [48354, 10], [48379, 4], [48391, 42], [48467, 33], [48505, 5], [48521, 10], [48654, 11], [48679, 4], [48691, 43], [48767, 33], [48805, 5], [48821, 10], [48955, 11], [48979, 4], [48991, 3], [49096, 4], [49105, 5], [49120, 10], [49256, 10], [49278, 5], [49290, 4], [49396, 4], [49406, 4], [49420, 9], [49557, 10], [49578, 5], [49590, 4], [49696, 4], [49706, 4], [49719, 10], [49857, 11], [49878, 5], [49890, 4], [49996, 4], [50006, 4], [50019, 9], [50158, 10], [50178, 5], [50190, 4], [50297, 3], [50306, 5], [50318, 10], [50459, 10], [50478, 4], [50490, 4], [50597, 4], [50606, 5], [50618, 9], [50759, 10], [50778, 4], [50790, 51], [50869, 32], [50906, 5], [50917, 9], [51060, 10], [51078, 4], [51090, 49], [51168, 33], [51206, 5], [51217, 9], [51361, 10], [51377, 5], [51390, 47], [51468, 33], [51506, 5], [51516, 9], [51661, 10], [51677, 5], [51689, 4], [51797, 4], [51807, 4], [51816, 9], [51962, 10], [51977, 5], [51989, 4], [52097, 4], [52107, 4], [52115, 9], [52263, 9], [52277, 5], [52289, 4], [52397, 4], [52407, 4], [52415, 9], [52563, 10], [52577, 4], [52589, 4], [52697, 4], [52707, 5], [52714, 9], [52864, 10], [52877, 4], [52889, 4], [52997, 4], [53007, 5], [53014, 8], [53165, 9], [53176, 5], [53189, 4], [53298, 3], [53307, 5], [53313, 9], [53466, 9], [53476, 5], [53489, 31], [53583, 19], [53607, 14], [53766, 15], [53789, 29], [53888, 14], [53907, 14], [54067, 14], [54088, 29], [54188, 14], [54208, 12], [54368, 13], [54388, 4], [54498, 4], [54508, 11], [54668, 12], [54688, 4], [54798, 4], [54808, 11], [54969, 11], [54988, 4], [55098, 4], [55108, 10], [55270, 10], [55288, 4], [55398, 4], [55408, 10], [55570, 10], [55588, 4], [55698, 4], [55708, 9], [55871, 9], [55888, 4], [55998, 4], [56008, 8], [56172, 40], [56287, 29], [56473, 40], [56586, 29], [56773, 40], [56886, 29], [57074, 39], [57186, 28], [57375, 38], [57486, 28], [57675, 9], [57687, 4], [57799, 4], [57805, 8], [57976, 8], [57987, 4], [58099, 4], [58105, 7], [58277, 8], [58287, 4], [58399, 4], [58404, 8], [58577, 8], [58586, 5], [58699, 4], [58704, 7], [58878, 10], [59179, 9], [59479, 8], [59780, 7], [60081, 6], [60382, 4], [60682, 4], [60983, 3], [61284, 1], [61584, 1]], "point": [141, 99]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan430", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -0.25, "y": 0.9130393, "z": 0.75}, "object_poses": [{"objectName": "Cloth_72b782f7", "position": {"x": -2.97771478, "y": 0.24505876, "z": 1.47356427}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cloth_72b782f7", "position": {"x": -1.74622178, "y": 0.07583475, "z": -1.22212744}, "rotation": {"x": 9.923122e-15, "y": 45.00003, "z": 9.92313e-15}}, {"objectName": "TissueBox_7f6b7a12", "position": {"x": 0.9302288, "y": 0.624048, "z": -2.02095914}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "TissueBox_7f6b7a12", "position": {"x": 0.0105037019, "y": 0.966112733, "z": 2.27727485}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "TissueBox_7f6b7a12", "position": {"x": -0.208454743, "y": 0.963445544, "z": -1.80532086}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_2ebf1d6c", "position": {"x": -0.0487191677, "y": 0.962390542, "z": -2.181234}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_2ebf1d6c", "position": {"x": -2.97771454, "y": 0.873777032, "z": 1.47356427}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBar_25110512", "position": {"x": 0.111016423, "y": 0.962815762, "z": -1.99327743}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_2f71aee2", "position": {"x": -2.86465931, "y": 0.243132338, "z": 1.55157173}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_91046b03", "position": {"x": 1.99250269, "y": 0.624912739, "z": -2.46304083}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_91046b03", "position": {"x": 0.4304876, "y": 0.9643103, "z": -1.80532086}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_67d42c38", "position": {"x": 1.35513842, "y": 0.6344514, "z": -2.09463954}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_2ebf1d6c", "position": {"x": 0.270752, "y": 0.962390542, "z": -2.27521229}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_31149277", "position": {"x": -0.885, "y": 1.617, "z": -2.5037}, "rotation": {"x": 0.0, "y": 179.999969, "z": 0.0}}, {"objectName": "Towel_24c29b1a", "position": {"x": 0.465890378, "y": 1.21, "z": -1.095}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBottle_67d42c38", "position": {"x": 0.9302288, "y": 0.6344514, "z": -2.242}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBar_25110512", "position": {"x": 1.5675931, "y": 0.6234182, "z": -2.46304083}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_cc5027dd", "position": {"x": -0.527925968, "y": 0.959745944, "z": -2.08725572}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plunger_65d4d49a", "position": {"x": 0.5063196, "y": 0.0115768015, "z": 2.268863}, "rotation": {"x": -0.00118246267, "y": 0.0004405065, "z": 0.000780726841}}, {"objectName": "SprayBottle_2f71aee2", "position": {"x": 0.270752, "y": 0.959746, "z": -1.99327743}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "TissueBox_7f6b7a12", "position": {"x": 0.4304876, "y": 0.963445544, "z": -1.99327743}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_b4152fb5", "position": {"x": 1.1426841, "y": 0.620348454, "z": -2.020959}, "rotation": {"x": 8.142229e-13, "y": -3.417002e-26, "z": -4.80899738e-12}}, {"objectName": "ScrubBrush_f6ab61ab", "position": {"x": 0.342862785, "y": 0.0120397955, "z": 2.072015}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_72b782f7", "position": {"x": 1.78004789, "y": 0.6222749, "z": -2.242}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_91046b03", "position": {"x": -2.921187, "y": 0.8756968, "z": 1.39555693}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Footstool_4fb1de73", "position": {"x": -0.325, "y": 0.297, "z": -1.98}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2874738460, "scene_num": 430}, "task_id": "trial_T20190906_172336_695472", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A1GVTH5YS3WOK0_33PPUNGG3BMALII1MC2U9M1Y57BZRM", "high_descs": ["Turn left and walk towards the dresser with the mirror.", "Pick up the tissue box on the dresser.", "Turn around, walk to the toilet, then turn left towards the metal rack.", "Place the tissue box to the right of the candle on the rack.", "Turn around, walk to the glass door, and turn left to face the toilet.", "Pick up the tissue box on the toilet basin.", "Turn left and walk to the metal rack.", "Place the tissue box to the left of the green sponge on the rack."], "task_desc": "Place two tissue boxes on the metal rack.", "votes": [1, 1]}, {"assignment_id": "A36DK84J5YJ942_3D4CH1LGEDA4Q1RA23EJXULYOV4G9K", "high_descs": ["move to the table to the right of the shower", "pick up a box of tissue from the counter", "move to the rack to the left of the sink", "put the tissues on the rack", "move to the toilet to the left of the shower", "pick up a box of tissue from the toilet", "move to the rack to the left of the sink", "put the tissues on the rack"], "task_desc": "Put two boxes of tissues on the rack to the left of the sink.", "votes": [1, 1]}, {"assignment_id": "A3B58QWMTBXVE0_3GM6G9ZBKQEN2QWABCHV1Z67EOKMTZ", "high_descs": ["Turn left and walk to the counter.", "Pick up a box of tissues from the counter.", "Turn around and walk to the toilet, turn left and walk to the rack.", "Put the box of tissues on the rack.", "Turn around and walk to the toilet.", "Pick up a box of tissues from the toilet.", "Turn around and walk back to the rack.", "Put the second box of tissues on the rack."], "task_desc": "Put two boxes of tissues on a rack.", "votes": [1, 1]}, {"assignment_id": "A1ELPYAFO7MANS_326O153BMLFI9D808OTXJNJBZO9DET", "high_descs": ["Turn left and walk to the counter.", "Pick up the tissue box on the right.", "Turn around and walk ahead, then turn left and go to the corner near the cart.", "Put the tissue box to the right of the candle on the top shelf.", "Turn around and walk to the toilet.", "Pick up the tissue box.", "Turn around and go back to the cart in the corner of the room.", "Put the tissue box to the left of the sponge on the top shelf."], "task_desc": "Put two tissue boxes on a cart.", "votes": [1, 1]}, {"assignment_id": "A2VXEPC4KG1CWU_3H7Z272LXAO54GLIUJ7WWCQKM4VPLO", "high_descs": ["Turn left and walk to the counter top against the wall", "Pick up the tissue box closest to you", "Turn right and walk toward the toilet then turn left and head forward to the metal rack", "Place the tissue box on the top rack", "Turn around and walk toward the toilet, then face the toilet", "Pick up the tissue box on top of the toilet", "Turn left and head to the metal rack again", "Place the tissue box on the top rack to the left of the other tissue box"], "task_desc": "Place tissue boxes on the top metal rack next to the sink", "votes": [1, 1]}, {"assignment_id": "A320QA9HJFUOZO_3SBEHTYCWQKXNT5D0362HNR499GIYH", "high_descs": ["Turn left and walk over to the vanity by the mirror", "Pick up the box of tissues from the vanity", "Walk across the bathroom to the metal shelf by the door", "Put the tissue box down on the shelf right of the sponge", "Turn right and walk over to the toilet", "Pick up the tissue box from the toilet", "Turn left and walk back to the metal shelf", "Put the tissue box down on the shelf left of the sponge"], "task_desc": "Put both tissue boxes on the metal shelf", "votes": [1, 1]}]}}