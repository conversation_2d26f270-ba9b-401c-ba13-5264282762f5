{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 34}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|5|5|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [8.293432, 8.293432, 5.9363222, 5.9363222, 3.87349, 3.87349]], "coordinateReceptacleObjectId": ["CounterTop", [0.008, 0.008, -6.052, -6.052, 3.788, 3.788]], "forceVisible": true, "objectId": "Egg|+02.07|+00.97|+01.48"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|3|2|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [8.293432, 8.293432, 5.9363222, 5.9363222, 3.87349, 3.87349]], "coordinateReceptacleObjectId": ["CounterTop", [-0.06, -0.06, 2.024, 2.024, 3.8444, 3.8444]], "forceVisible": true, "objectId": "Egg|+02.07|+00.97|+01.48", "receptacleObjectId": "CounterTop|-00.02|+00.96|+00.51"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+02.07|+00.97|+01.48"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [79, 101, 101, 127], "mask": [[30085, 8], [30384, 11], [30683, 13], [30982, 15], [31281, 17], [31581, 18], [31880, 19], [32180, 20], [32480, 20], [32779, 22], [33079, 22], [33379, 22], [33679, 23], [33979, 23], [34279, 23], [34579, 23], [34880, 22], [35180, 22], [35480, 21], [35781, 20], [36081, 20], [36382, 18], [36683, 16], [36984, 14], [37285, 12], [37587, 9], [37890, 3]], "point": [90, 113]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+02.07|+00.97|+01.48", "placeStationary": true, "receptacleObjectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 120], [15470, 85], [15638, 118], [15772, 82], [15937, 118], [16074, 80], [16237, 117], [16375, 79], [16536, 118], [16675, 78], [16835, 119], [16975, 78], [17134, 120], [17275, 78], [17434, 60], [17505, 49], [17575, 78], [17733, 58], [17807, 47], [17875, 77], [18032, 57], [18109, 45], [18175, 77], [18332, 56], [18409, 45], [18475, 77], [18631, 56], [18709, 45], [18775, 76], [18930, 57], [19010, 44], [19075, 76], [19230, 56], [19313, 41], [19374, 77], [19529, 58], [19614, 40], [19674, 76], [19828, 59], [19910, 2], [19915, 39], [19974, 76], [20128, 59], [20210, 3], [20215, 39], [20274, 76], [20427, 60], [20510, 3], [20515, 39], [20574, 76], [20726, 62], [20810, 3], [20815, 39], [20874, 75], [21025, 63], [21110, 3], [21115, 39], [21174, 75], [21325, 63], [21410, 3], [21415, 39], [21474, 75], [21624, 64], [21710, 3], [21715, 39], [21773, 75], [21923, 66], [22010, 3], [22015, 39], [22073, 75], [22223, 66], [22310, 3], [22315, 39], [22373, 75], [22522, 67], [22610, 3], [22615, 39], [22673, 75], [22821, 68], [22910, 3], [22914, 40], [22973, 74], [23121, 68], [23211, 1], [23214, 40], [23273, 74], [23420, 70], [23511, 1], [23514, 40], [23573, 74], [23719, 71], [23813, 42], [23872, 74], [24019, 71], [24113, 43], [24172, 74], [24318, 72], [24412, 45], [24470, 76], [24617, 74], [24711, 49], [24768, 77], [24916, 75], [25011, 134], [25216, 75], [25311, 134], [25515, 76], [25611, 134], [25814, 78], [25911, 133], [26114, 78], [26210, 134], [26413, 80], [26509, 135], [26712, 82], [26808, 135], [27012, 83], [27106, 137], [27311, 232], [27610, 233], [27910, 232], [28209, 233], [28508, 234], [28807, 234], [29107, 234], [29406, 235], [29705, 236], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 120], [15470, 85], [15638, 118], [15772, 82], [15937, 118], [16074, 80], [16237, 117], [16375, 79], [16536, 118], [16675, 78], [16835, 119], [16975, 78], [17134, 120], [17275, 78], [17434, 60], [17505, 49], [17575, 78], [17733, 58], [17807, 47], [17875, 77], [18032, 57], [18109, 45], [18175, 77], [18332, 56], [18409, 45], [18475, 77], [18631, 56], [18709, 45], [18775, 76], [18930, 57], [19010, 44], [19075, 76], [19230, 56], [19313, 41], [19374, 77], [19529, 58], [19614, 35], [19674, 76], [19828, 59], [19910, 2], [19915, 32], [19974, 76], [20128, 59], [20210, 3], [20215, 30], [20274, 76], [20427, 60], [20510, 3], [20515, 30], [20574, 76], [20726, 62], [20810, 3], [20815, 29], [20874, 75], [21025, 63], [21110, 3], [21115, 28], [21174, 75], [21325, 63], [21410, 3], [21415, 28], [21474, 75], [21624, 64], [21710, 3], [21715, 27], [21773, 75], [21923, 66], [22010, 3], [22015, 27], [22073, 75], [22223, 66], [22310, 3], [22315, 26], [22373, 75], [22522, 67], [22610, 3], [22615, 26], [22673, 75], [22821, 68], [22910, 3], [22914, 27], [22973, 74], [23121, 68], [23211, 1], [23214, 27], [23273, 74], [23420, 70], [23511, 1], [23514, 27], [23573, 74], [23719, 71], [23813, 28], [23872, 74], [24019, 71], [24113, 27], [24172, 74], [24318, 72], [24412, 28], [24470, 76], [24617, 74], [24711, 30], [24768, 77], [24916, 75], [25011, 30], [25063, 82], [25216, 75], [25311, 30], [25363, 82], [25515, 76], [25611, 30], [25662, 83], [25814, 78], [25911, 31], [25962, 82], [26114, 78], [26210, 32], [26261, 83], [26413, 80], [26509, 34], [26561, 83], [26712, 82], [26808, 35], [26860, 83], [27012, 83], [27106, 38], [27159, 84], [27311, 134], [27458, 85], [27610, 137], [27757, 86], [27910, 139], [28054, 88], [28209, 233], [28508, 234], [28807, 234], [29107, 234], [29406, 235], [29705, 236], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+02.07|+00.97|+01.48"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [140, 66, 162, 94], "mask": [[19649, 6], [19947, 10], [20245, 13], [20545, 14], [20844, 16], [21143, 17], [21443, 18], [21742, 19], [22042, 20], [22341, 21], [22641, 21], [22941, 22], [23241, 22], [23541, 22], [23841, 22], [24140, 23], [24440, 23], [24741, 22], [25041, 22], [25341, 22], [25641, 21], [25942, 20], [26242, 19], [26543, 18], [26843, 17], [27144, 15], [27445, 13], [27747, 10], [28049, 5]], "point": [151, 79]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 120], [15470, 85], [15638, 118], [15772, 82], [15937, 118], [16074, 80], [16237, 117], [16375, 79], [16536, 118], [16675, 78], [16835, 119], [16975, 78], [17134, 120], [17275, 78], [17434, 60], [17505, 49], [17575, 78], [17733, 58], [17807, 47], [17875, 77], [18032, 57], [18109, 45], [18175, 77], [18332, 56], [18409, 45], [18475, 77], [18631, 56], [18709, 45], [18775, 76], [18930, 57], [19010, 44], [19075, 76], [19230, 56], [19313, 41], [19374, 77], [19529, 58], [19614, 40], [19674, 76], [19828, 59], [19910, 2], [19915, 39], [19974, 76], [20128, 59], [20210, 3], [20215, 39], [20274, 76], [20427, 60], [20510, 3], [20515, 39], [20574, 76], [20726, 62], [20810, 3], [20815, 39], [20874, 75], [21025, 63], [21110, 3], [21115, 39], [21174, 75], [21325, 63], [21410, 3], [21415, 39], [21474, 75], [21624, 64], [21710, 3], [21715, 39], [21773, 75], [21923, 66], [22010, 3], [22015, 39], [22073, 75], [22223, 66], [22310, 3], [22315, 39], [22373, 75], [22522, 67], [22610, 3], [22615, 39], [22673, 75], [22821, 68], [22910, 3], [22914, 40], [22973, 74], [23121, 68], [23211, 1], [23214, 40], [23273, 74], [23420, 70], [23511, 1], [23514, 40], [23573, 74], [23719, 71], [23813, 42], [23872, 74], [24019, 71], [24113, 43], [24172, 74], [24318, 72], [24412, 45], [24470, 76], [24617, 74], [24711, 49], [24768, 77], [24916, 75], [25011, 134], [25216, 75], [25311, 134], [25515, 76], [25611, 134], [25814, 78], [25911, 133], [26114, 78], [26210, 134], [26413, 80], [26509, 135], [26712, 82], [26808, 135], [27012, 83], [27106, 137], [27311, 232], [27610, 233], [27910, 232], [28209, 233], [28508, 234], [28807, 234], [29107, 234], [29406, 235], [29705, 236], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+02.07|+00.97|+01.48", "placeStationary": true, "receptacleObjectId": "CounterTop|-00.02|+00.96|+00.51"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 78, 299, 238], "mask": [[23115, 165], [23363, 37], [23416, 163], [23663, 37], [23716, 163], [23942, 1], [23963, 37], [24016, 163], [24241, 2], [24263, 37], [24316, 162], [24540, 3], [24562, 38], [24616, 162], [24840, 3], [24862, 38], [24915, 163], [25140, 3], [25161, 39], [25215, 163], [25440, 4], [25461, 39], [25514, 164], [25741, 3], [25760, 40], [25814, 163], [26042, 3], [26059, 41], [26112, 165], [26342, 4], [26358, 42], [26411, 166], [26642, 5], [26656, 44], [26710, 167], [26941, 8], [26954, 46], [27008, 169], [27241, 60], [27303, 146], [27454, 23], [27541, 207], [27755, 23], [27840, 29], [27874, 174], [28055, 23], [28140, 28], [28176, 172], [28355, 23], [28440, 28], [28476, 172], [28655, 23], [28740, 28], [28776, 172], [28955, 24], [29040, 28], [29075, 172], [29256, 23], [29340, 26], [29375, 172], [29556, 24], [29640, 25], [29674, 173], [29856, 24], [29940, 25], [29975, 171], [30157, 24], [30241, 23], [30275, 30], [30315, 131], [30457, 25], [30541, 23], [30575, 28], [30621, 48], [30681, 65], [30757, 26], [30842, 21], [30875, 5], [30882, 18], [30925, 41], [30987, 59], [31057, 27], [31142, 21], [31174, 5], [31182, 18], [31228, 37], [31291, 55], [31357, 28], [31443, 19], [31474, 5], [31483, 17], [31531, 32], [31595, 51], [31657, 29], [31743, 19], [31774, 4], [31783, 17], [31833, 29], [31899, 47], [31957, 30], [32043, 18], [32073, 5], [32084, 16], [32136, 24], [32201, 45], [32257, 32], [32344, 17], [32373, 4], [32384, 16], [32439, 21], [32503, 43], [32557, 34], [32644, 16], [32673, 4], [32685, 15], [32742, 17], [32805, 40], [32857, 36], [32944, 16], [32973, 4], [32985, 15], [33044, 14], [33107, 38], [33158, 38], [33244, 15], [33272, 5], [33285, 15], [33345, 13], [33409, 36], [33458, 43], [33515, 1], [33543, 16], [33572, 5], [33586, 14], [33647, 10], [33711, 34], [33758, 58], [33843, 15], [33872, 5], [33886, 14], [33949, 8], [34013, 32], [34058, 57], [34143, 15], [34172, 5], [34186, 14], [34250, 7], [34315, 30], [34358, 57], [34443, 15], [34471, 6], [34486, 14], [34552, 5], [34616, 29], [34658, 56], [34743, 14], [34771, 6], [34787, 13], [34853, 4], [34917, 29], [34957, 56], [35042, 15], [35071, 6], [35087, 13], [35155, 2], [35218, 28], [35257, 55], [35342, 17], [35370, 7], [35387, 13], [35518, 30], [35555, 56], [35641, 19], [35670, 7], [35687, 13], [35818, 92], [35940, 22], [35968, 9], [35987, 13], [36118, 91], [36240, 37], [36288, 12], [36418, 90], [36538, 39], [36588, 12], [36718, 90], [36838, 39], [36888, 12], [37018, 89], [37139, 38], [37188, 12], [37317, 90], [37439, 38], [37489, 11], [37616, 90], [37739, 38], [37789, 11], [37915, 91], [38039, 38], [38089, 11], [38156, 2], [38213, 92], [38339, 38], [38389, 11], [38455, 3], [38512, 93], [38639, 38], [38689, 11], [38753, 5], [38810, 94], [38939, 38], [38990, 10], [39051, 8], [39108, 96], [39240, 37], [39290, 10], [39349, 11], [39406, 98], [39540, 39], [39590, 10], [39646, 14], [39704, 100], [39840, 43], [39890, 10], [39944, 17], [40002, 102], [40140, 45], [40190, 12], [40241, 21], [40300, 104], [40440, 45], [40491, 13], [40539, 25], [40598, 106], [40740, 45], [40791, 15], [40835, 31], [40895, 109], [41040, 45], [41091, 20], [41129, 39], [41190, 114], [41340, 45], [41391, 80], [41482, 122], [41640, 45], [41691, 213], [41940, 45], [41992, 212], [42240, 45], [42292, 212], [42540, 45], [42592, 212], [42840, 45], [42892, 212], [43140, 46], [43192, 212], [43439, 47], [43493, 212], [43739, 47], [43793, 94], [43889, 116], [44039, 48], [44093, 7], [44101, 84], [44190, 115], [44339, 48], [44393, 7], [44402, 83], [44490, 116], [44639, 48], [44694, 6], [44702, 82], [44790, 116], [44938, 49], [44994, 6], [45003, 81], [45090, 117], [45238, 48], [45294, 6], [45303, 81], [45389, 118], [45537, 48], [45594, 6], [45604, 80], [45689, 119], [45836, 49], [45895, 5], [45904, 79], [45989, 119], [46136, 50], [46195, 5], [46204, 79], [46288, 121], [46435, 52], [46495, 5], [46505, 78], [46588, 122], [46734, 55], [46795, 5], [46805, 78], [46888, 122], [47033, 58], [47093, 7], [47106, 77], [47187, 124], [47333, 67], [47406, 76], [47487, 125], [47632, 68], [47707, 75], [47786, 128], [47931, 69], [48007, 75], [48086, 130], [48229, 71], [48307, 75], [48386, 133], [48523, 77], [48608, 74], [48685, 67], [48753, 66], [48824, 76], [48908, 74], [48985, 61], [49058, 62], [49124, 76], [49209, 72], [49285, 58], [49361, 59], [49424, 76], [49509, 72], [49584, 57], [49663, 58], [49724, 76], [49809, 72], [49884, 55], [49965, 56], [50024, 76], [50110, 71], [50183, 55], [50266, 56], [50325, 75], [50410, 71], [50483, 54], [50567, 55], [50625, 75], [50710, 70], [50783, 53], [50868, 55], [50925, 75], [51010, 70], [51082, 54], [51169, 54], [51225, 75], [51310, 70], [51382, 53], [51470, 53], [51526, 74], [51611, 69], [51682, 52], [51770, 54], [51826, 74], [51911, 68], [51981, 53], [52070, 54], [52127, 73], [52211, 68], [52281, 53], [52371, 53], [52427, 73], [52511, 68], [52581, 53], [52671, 54], [52727, 73], [52811, 67], [52881, 52], [52971, 53], [53028, 72], [53111, 67], [53181, 52], [53271, 53], [53328, 72], [53410, 68], [53481, 53], [53571, 53], [53629, 71], [53710, 68], [53781, 53], [53871, 52], [53930, 70], [54009, 68], [54081, 53], [54171, 51], [54231, 69], [54308, 69], [54381, 53], [54471, 50], [54532, 68], [54607, 69], [54682, 52], [54770, 51], [54833, 67], [54905, 70], [54982, 53], [55070, 51], [55133, 67], [55204, 70], [55283, 52], [55370, 51], [55434, 66], [55502, 72], [55583, 52], [55670, 51], [55734, 139], [55883, 52], [55969, 52], [56035, 138], [56183, 52], [56269, 53], [56335, 137], [56483, 53], [56569, 53], [56635, 137], [56774, 1], [56777, 1], [56780, 1], [56782, 54], [56869, 54], [56935, 137], [57073, 2], [57076, 2], [57079, 1], [57082, 54], [57168, 55], [57235, 137], [57373, 1], [57376, 1], [57379, 1], [57381, 55], [57468, 56], [57535, 136], [57673, 1], [57676, 1], [57678, 2], [57681, 56], [57768, 58], [57834, 137], [57972, 2], [57975, 2], [57978, 1], [57981, 56], [58068, 59], [58134, 137], [58272, 1], [58275, 1], [58277, 2], [58280, 57], [58367, 63], [58432, 138], [58572, 1], [58574, 2], [58577, 1], [58580, 57], [58667, 203], [58871, 2], [58874, 1], [58877, 1], [58879, 58], [58967, 203], [59171, 1], [59173, 2], [59176, 2], [59179, 59], [59267, 202], [59470, 2], [59473, 2], [59476, 1], [59478, 60], [59566, 203], [59770, 4], [59775, 2], [59778, 60], [59866, 272], [60166, 273], [60466, 273], [60765, 274], [61065, 274], [61365, 275], [61665, 275], [61964, 276], [62264, 277], [62563, 279], [62862, 281], [63161, 283], [63460, 285], [63759, 293], [64053, 4192], [68255, 287], [68558, 281], [68861, 276], [69163, 273], [69464, 270], [69766, 267], [70067, 265], [70368, 263], [70669, 261], [70970, 259], [71271, 129]], "point": [149, 157]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan2", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 1.25, "y": 0.9009992, "z": 1.0}, "object_poses": [{"objectName": "Potato_5e728867", "position": {"x": 1.746321, "y": 0.9352317, "z": 0.103804827}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ec6bb035", "position": {"x": 1.71057916, "y": 0.73976326, "z": 0.543363}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ec6bb035", "position": {"x": -1.55163574, "y": 0.9558624, "z": -0.9641914}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_233c7df0", "position": {"x": 1.802076, "y": 0.91254133, "z": -1.61003125}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_e1c57c26", "position": {"x": 1.66173053, "y": 0.458954871, "z": 0.736144662}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_4f8c70c0", "position": {"x": 1.802076, "y": 0.9115421, "z": -1.31893754}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": 1.9753, "y": 0.938299954, "z": 0.6015}, "rotation": {"x": 0.0, "y": 180.000214, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": -0.320658833, "y": 0.982472539, "z": 0.9702008}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": 2.073358, "y": 0.9683725, "z": 1.48408055}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d852c2bd", "position": {"x": -0.249916419, "y": 0.11356014, "z": -1.347}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": 1.66067827, "y": 0.7173935, "z": 0.04876554}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": 0.000411391258, "y": 0.9475927, "z": 0.9702008}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": 0.1074348, "y": 0.9291344, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": -1.15179658, "y": 0.117000341, "z": -1.5507803}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_448791c6", "position": {"x": -1.796, "y": 0.0974895656, "z": 1.32999992}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "Bowl_253fa632", "position": {"x": -1.841917, "y": 1.66042042, "z": -1.28275013}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_586649ce", "position": {"x": 1.19580007, "y": 0.10542345, "z": -1.41618121}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_586649ce", "position": {"x": 1.55991328, "y": 0.907806158, "z": 0.182397366}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_14add4df", "position": {"x": -0.106612019, "y": 0.9219062, "z": 0.9702008}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": 0.000411391258, "y": 1.00230384, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": 0.000411391258, "y": 1.00230384, "z": 0.0417993069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": 1.95639992, "y": 1.00142312, "z": -0.5472686}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": 2.02046251, "y": 1.00142312, "z": -0.808987558}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": -0.106612019, "y": 0.9291344, "z": -0.190301061}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_4e364eea", "position": {"x": 0.321481615, "y": 0.923730552, "z": 0.506000042}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": -0.21363543, "y": 0.929685533, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_c80d57de", "position": {"x": 0.214458212, "y": 0.926148534, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ec6bb035", "position": {"x": 0.170128584, "y": 0.8049335, "z": -1.4355}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_233c7df0", "position": {"x": 1.09552133, "y": 1.68067992, "z": -1.65244007}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": -1.75051844, "y": 1.73395526, "z": 0.201504618}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_d852c2bd", "position": {"x": -0.638020456, "y": 0.9102082, "z": -1.61003125}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_6d10ab8d", "position": {"x": -1.28641355, "y": 0.9, "z": -1.34873831}, "rotation": {"x": 0.0, "y": 79.76979, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": -1.75051868, "y": 1.545918, "z": -0.175190732}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_de267f63", "position": {"x": 1.1179688, "y": 0.9264999, "z": -1.70706248}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": -0.00256602466, "y": 0.813929, "z": -1.54605973}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": 0.214458212, "y": 0.9475927, "z": 1.20230114}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_586649ce", "position": {"x": -0.106612019, "y": 0.9219062, "z": 0.506000042}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_14add4df", "position": {"x": -0.6997603, "y": 0.6678068, "z": -1.3015}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_6cb12d41", "position": {"x": 1.81349766, "y": 0.9094269, "z": 1.33350134}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_4f8c70c0", "position": {"x": -1.55163574, "y": 0.911542058, "z": -1.11337709}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": 0.256177843, "y": 0.9874709, "z": -0.07600148}, "rotation": {"x": -0.00406789174, "y": 330.886841, "z": 359.980927}}, {"objectName": "Tomato_448791c6", "position": {"x": -0.320658833, "y": 0.98076725, "z": -0.190301061}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5e728867", "position": {"x": 0.256475866, "y": 0.7844443, "z": -1.38022017}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_e1c57c26", "position": {"x": -1.48323619, "y": 1.67920291, "z": -1.61474991}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_bfeb74cb", "position": {"x": 0.214458212, "y": 0.9267294, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": 1.3554858, "y": 0.9554, "z": -1.36239815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_253fa632", "position": {"x": -0.175260633, "y": 0.7590201, "z": -1.38022017}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2080565419, "scene_num": 2}, "task_id": "trial_T20190908_122832_412609", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1HKHM4NVAO98H_32EYX73OY301K8APCXS3TG6LX6DUR4", "high_descs": ["move to the left a bit and face the kitchen counter again", "grab the egg off of the kitchen counter", "move to the right side of the kitchen counter space where the microwave is", "place the egg inside the microwave, turn it on, then take it back out", "turn around and walk to the right side of the kitchen island counter", "place the egg down on the counter behind the clear cup and in front of the salt shaker"], "task_desc": "place a microwaved egg down on the kitchen island countertop", "votes": [1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_39JEC7537XSXRLA6ZQCD079IBBYCVI", "high_descs": ["Turn left, move forward, then turn right to face the counter.", "Pick up the egg on the counter.", "Turn around, step forward, then turn left to go toward the coffee maker, then turn left to face the microwave.", "Open the microwave put the egg inside and cook it, then take the egg out.", "Turn left, move forward, then turn left to face the kitchen island.", "Put the egg on the kitchen island, in front of the salt."], "task_desc": "Put a cooked egg on the kitchen island.", "votes": [1, 1]}, {"assignment_id": "ARB8SJAWXUWTD_3L4PIM1GQW7I1HUUMYPSW1CYTT4RYD", "high_descs": ["Go to the counter left of the stove", "Pick up the egg on the counter", "Go to the microwave", "Heat the egg in the microwave", "Go to the center table", "Place the egg on the center table"], "task_desc": "Place a heated egg on a table", "votes": [1, 1]}]}}