{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000340.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000341.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000342.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000343.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000344.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000345.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000346.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000347.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000348.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000349.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000350.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000351.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000352.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000353.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000354.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000355.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000356.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000357.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000358.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000359.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000360.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000361.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000362.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000363.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000364.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000365.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000366.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000367.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000368.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000369.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000370.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000371.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000372.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000373.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000374.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000375.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000376.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000377.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000378.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000379.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000380.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000381.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000382.png", "low_idx": 52}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-1|9|3|15"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-4.00457476, -4.00457476, 8.866354, 8.866354, 5.99874304, 5.99874304]], "coordinateReceptacleObjectId": ["Cabinet", [-3.6468, -3.6468, 8.444304, 8.444304, 7.24115516, 7.24115516]], "forceVisible": true, "objectId": "Cup|-01.00|+01.50|+02.22"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|1|6|1|-15"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-4.00457476, -4.00457476, 8.866354, 8.866354, 5.99874304, 5.99874304]], "coordinateReceptacleObjectId": ["Cabinet", [6.00313236, 6.00313236, 8.32905484, 8.32905484, 8.45383264, 8.45383264]], "forceVisible": true, "objectId": "Cup|-01.00|+01.50|+02.22", "receptacleObjectId": "Cabinet|+01.50|+02.11|+02.08"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.91|+01.81|+02.11"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [89, 1, 215, 119], "mask": [[89, 127], [389, 127], [689, 127], [989, 127], [1289, 127], [1589, 127], [1889, 127], [2189, 127], [2489, 127], [2789, 127], [3089, 126], [3389, 126], [3690, 125], [3990, 125], [4290, 125], [4590, 125], [4890, 125], [5190, 125], [5490, 125], [5790, 125], [6090, 125], [6390, 125], [6690, 125], [6990, 125], [7290, 125], [7590, 125], [7890, 125], [8190, 124], [8490, 124], [8790, 124], [9090, 124], [9391, 123], [9691, 123], [9991, 123], [10291, 123], [10591, 123], [10891, 123], [11191, 123], [11491, 123], [11791, 123], [12091, 123], [12391, 123], [12691, 123], [12991, 123], [13291, 122], [13591, 122], [13891, 122], [14191, 122], [14491, 122], [14792, 121], [15092, 121], [15392, 121], [15692, 121], [15992, 121], [16292, 121], [16592, 121], [16892, 121], [17192, 121], [17492, 121], [17792, 121], [18092, 121], [18392, 120], [18692, 120], [18992, 120], [19292, 120], [19592, 120], [19892, 120], [20193, 119], [20493, 119], [20793, 119], [21093, 119], [21393, 119], [21693, 119], [21993, 119], [22293, 119], [22593, 119], [22893, 119], [23193, 119], [23493, 118], [23793, 118], [24093, 118], [24393, 118], [24693, 118], [24993, 118], [25293, 118], [25593, 118], [25894, 117], [26194, 117], [26494, 117], [26794, 117], [27094, 117], [27394, 117], [27694, 117], [27994, 117], [28294, 117], [28594, 116], [28894, 116], [29194, 116], [29494, 116], [29794, 116], [30094, 116], [30394, 116], [30694, 116], [30994, 116], [31295, 115], [31595, 115], [31895, 115], [32195, 115], [32495, 115], [32795, 115], [33095, 115], [33395, 115], [33695, 114], [33995, 114], [34295, 114], [34595, 114], [34895, 114], [35195, 114], [35496, 112]], "point": [152, 59]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.00|+01.50|+02.22"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [118, 42, 156, 109], "mask": [[12431, 11], [12720, 34], [13018, 39], [13318, 39], [13618, 38], [13918, 38], [14218, 38], [14518, 38], [14819, 37], [15119, 37], [15419, 37], [15719, 37], [16019, 37], [16319, 37], [16619, 36], [16919, 36], [17220, 35], [17520, 35], [17820, 35], [18120, 35], [18420, 35], [18720, 35], [19020, 35], [19320, 35], [19621, 33], [19921, 33], [20221, 33], [20521, 33], [20821, 33], [21121, 33], [21421, 33], [21722, 32], [22022, 32], [22322, 32], [22622, 31], [22922, 31], [23222, 31], [23522, 31], [23822, 31], [24123, 30], [24423, 30], [24723, 30], [25023, 30], [25323, 30], [25623, 29], [25923, 29], [26223, 29], [26524, 28], [26824, 28], [27124, 28], [27424, 28], [27724, 28], [28024, 28], [28324, 28], [28624, 27], [28925, 26], [29225, 26], [29525, 26], [29825, 26], [30125, 26], [30425, 26], [30725, 26], [31026, 25], [31326, 25], [31626, 24], [31926, 24], [32226, 24], [32527, 22]], "point": [137, 74]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.91|+01.81|+02.11"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [51, 1, 199, 146], "mask": [[51, 42], [98, 102], [351, 42], [399, 101], [651, 42], [699, 101], [951, 42], [999, 101], [1251, 42], [1299, 101], [1551, 42], [1599, 101], [1851, 42], [1899, 101], [2151, 42], [2199, 101], [2451, 42], [2499, 101], [2751, 42], [2799, 101], [3051, 42], [3099, 101], [3352, 41], [3399, 101], [3652, 41], [3699, 101], [3952, 41], [3999, 101], [4252, 41], [4299, 101], [4552, 41], [4599, 101], [4852, 41], [4899, 101], [5152, 41], [5199, 101], [5452, 42], [5499, 101], [5752, 42], [5799, 101], [6052, 42], [6099, 101], [6352, 42], [6399, 101], [6652, 42], [6700, 100], [6953, 41], [7000, 99], [7253, 41], [7300, 99], [7553, 41], [7600, 99], [7853, 41], [7900, 99], [8153, 41], [8200, 99], [8453, 41], [8500, 99], [8753, 41], [8800, 99], [9053, 41], [9100, 99], [9353, 41], [9400, 99], [9653, 41], [9700, 99], [9953, 41], [10000, 99], [10254, 40], [10300, 99], [10554, 40], [10600, 99], [10854, 40], [10900, 99], [11154, 40], [11200, 99], [11454, 41], [11500, 99], [11754, 41], [11800, 99], [12054, 41], [12100, 99], [12354, 41], [12400, 99], [12654, 41], [12700, 99], [12954, 41], [13000, 99], [13254, 41], [13301, 98], [13555, 40], [13601, 97], [13855, 40], [13901, 97], [14155, 40], [14201, 97], [14455, 40], [14501, 97], [14755, 40], [14801, 97], [15055, 40], [15101, 97], [15355, 40], [15401, 97], [15655, 40], [15701, 97], [15955, 40], [16001, 97], [16255, 40], [16301, 97], [16555, 40], [16601, 97], [16856, 39], [16901, 97], [17156, 40], [17201, 97], [17456, 40], [17501, 97], [17756, 40], [17801, 97], [18056, 40], [18101, 97], [18356, 40], [18401, 97], [18656, 40], [18701, 97], [18956, 40], [19001, 97], [19256, 40], [19301, 97], [19556, 40], [19602, 96], [19856, 40], [19902, 96], [20156, 40], [20202, 95], [20457, 39], [20502, 95], [20757, 39], [20802, 95], [21057, 39], [21102, 95], [21357, 39], [21402, 95], [21657, 39], [21702, 95], [21957, 39], [22002, 95], [22257, 39], [22302, 95], [22557, 39], [22602, 95], [22857, 39], [22902, 95], [23157, 40], [23202, 95], [23457, 40], [23502, 95], [23758, 39], [23802, 95], [24058, 39], [24102, 95], [24358, 39], [24402, 95], [24658, 39], [24702, 95], [24958, 39], [25002, 95], [25258, 39], [25302, 95], [25558, 39], [25602, 95], [25858, 39], [25902, 95], [26158, 39], [26203, 94], [26458, 39], [26503, 94], [26758, 39], [26803, 94], [27059, 38], [27103, 93], [27359, 38], [27403, 93], [27659, 38], [27703, 93], [27959, 38], [28003, 93], [28259, 38], [28303, 93], [28559, 38], [28603, 93], [28859, 39], [28903, 93], [29159, 39], [29203, 93], [29459, 39], [29503, 93], [29759, 39], [29803, 93], [30059, 39], [30103, 93], [30360, 38], [30403, 93], [30660, 38], [30703, 93], [30960, 38], [31003, 93], [31260, 38], [31303, 93], [31560, 38], [31603, 93], [31860, 38], [31903, 93], [32160, 38], [32203, 93], [32460, 38], [32504, 92], [32760, 38], [32804, 92], [33060, 38], [33104, 92], [33360, 38], [33404, 92], [33660, 38], [33704, 91], [33961, 37], [34261, 37], [34561, 37], [34861, 38], [35161, 37], [35461, 37], [35761, 36], [36061, 35], [36361, 34], [36661, 34], [36961, 33], [37262, 31], [37562, 30], [37862, 30], [38162, 29], [38462, 28], [38762, 27], [39062, 27], [39362, 26], [39662, 25], [39962, 24], [40262, 24], [40563, 22], [40863, 21], [41163, 20], [41463, 20], [41763, 19], [42063, 18], [42363, 17], [42663, 17], [42963, 16], [43267, 11], [43571, 5]], "point": [125, 72]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.00|+01.50|+02.22", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 75], [127, 134], [300, 75], [427, 134], [600, 75], [727, 134], [900, 75], [1027, 133], [1200, 76], [1327, 133], [1500, 76], [1627, 133], [1800, 76], [1927, 133], [2100, 76], [2227, 133], [2400, 76], [2527, 133], [2700, 76], [2826, 134], [3000, 77], [3126, 134], [3300, 77], [3426, 134], [3600, 77], [3726, 134], [3900, 77], [4026, 133], [4200, 77], [4326, 133], [4500, 77], [4626, 133], [4800, 78], [4926, 133], [5100, 78], [5226, 133], [5400, 78], [5526, 133], [5700, 78], [5826, 133], [6000, 78], [6126, 133], [6300, 79], [6425, 134], [6600, 79], [6725, 134], [6900, 79], [7025, 133], [7200, 79], [7325, 133], [7500, 79], [7625, 133], [7800, 79], [7925, 133], [8100, 80], [8225, 133], [8400, 80], [8525, 133], [8700, 80], [8825, 133], [9000, 80], [9125, 133], [9300, 80], [9425, 133], [9600, 80], [9725, 133], [9900, 81], [10024, 133], [10200, 81], [10324, 133], [10500, 81], [10624, 133], [10800, 81], [10924, 133], [11100, 81], [11224, 133], [11400, 81], [11524, 133], [11700, 82], [11824, 133], [12000, 82], [12124, 133], [12300, 82], [12424, 133], [12600, 82], [12724, 133], [12900, 82], [13024, 132], [13200, 82], [13324, 132], [13500, 83], [13623, 133], [13800, 83], [13923, 133], [14100, 83], [14223, 133], [14400, 83], [14523, 133], [14700, 83], [14823, 133], [15000, 83], [15123, 133], [15300, 84], [15423, 133], [15600, 84], [15723, 133], [15900, 84], [16023, 132], [16200, 84], [16323, 132], [16500, 84], [16623, 132], [16800, 84], [16923, 132], [17100, 85], [17222, 133], [17400, 85], [17522, 133], [17700, 85], [17822, 133], [18000, 85], [18122, 133], [18300, 85], [18422, 133], [18600, 86], [18722, 133], [18900, 86], [19022, 132], [19200, 86], [19322, 132], [19500, 86], [19622, 132], [19800, 86], [19922, 132], [20100, 86], [20222, 132], [20400, 87], [20522, 132], [20700, 87], [20821, 133], [21000, 87], [21121, 133], [21300, 92], [21419, 135], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.00|+01.50|+02.22"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [75, 1, 126, 72], "mask": [[75, 52], [375, 52], [675, 52], [975, 52], [1276, 51], [1576, 51], [1876, 51], [2176, 51], [2476, 51], [2776, 50], [3077, 49], [3377, 49], [3677, 49], [3977, 49], [4277, 49], [4577, 49], [4878, 48], [5178, 48], [5478, 48], [5778, 48], [6078, 48], [6379, 46], [6679, 46], [6979, 46], [7279, 46], [7579, 46], [7879, 46], [8180, 45], [8480, 45], [8780, 45], [9080, 45], [9380, 45], [9680, 45], [9981, 43], [10281, 43], [10581, 43], [10881, 43], [11181, 43], [11481, 43], [11782, 42], [12082, 42], [12382, 42], [12682, 42], [12982, 42], [13282, 42], [13583, 40], [13883, 40], [14183, 40], [14483, 40], [14783, 40], [15083, 40], [15384, 39], [15684, 39], [15984, 39], [16284, 39], [16584, 39], [16884, 39], [17185, 37], [17485, 37], [17785, 37], [18085, 37], [18385, 37], [18686, 36], [18986, 36], [19286, 36], [19586, 36], [19886, 36], [20186, 36], [20487, 35], [20787, 34], [21087, 34], [21392, 27]], "point": [100, 35]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+01.50|+02.11|+02.08"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [33, 81, 119, 147], "mask": [[24041, 79], [24341, 79], [24641, 79], [24941, 79], [25240, 80], [25540, 80], [25840, 80], [26140, 80], [26440, 80], [26740, 80], [27040, 80], [27340, 80], [27639, 81], [27939, 81], [28239, 81], [28539, 81], [28839, 81], [29139, 81], [29439, 81], [29739, 81], [30038, 81], [30338, 81], [30638, 81], [30938, 81], [31238, 81], [31538, 81], [31838, 81], [32138, 81], [32438, 81], [32737, 82], [33037, 82], [33337, 82], [33637, 82], [33937, 82], [34237, 82], [34537, 82], [34837, 82], [35136, 83], [35436, 83], [35736, 83], [36036, 83], [36336, 83], [36636, 83], [36936, 83], [37236, 83], [37535, 84], [37835, 84], [38135, 84], [38435, 84], [38735, 84], [39035, 83], [39335, 83], [39635, 83], [39934, 84], [40234, 84], [40534, 84], [40834, 84], [41134, 84], [41434, 84], [41734, 84], [42034, 84], [42334, 84], [42633, 84], [42933, 81], [43233, 78], [43533, 75], [43834, 73]], "point": [76, 113]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.00|+01.50|+02.22", "placeStationary": true, "receptacleObjectId": "Cabinet|+01.50|+02.11|+02.08"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 27, 114, 146], "mask": [[7800, 1], [8100, 1], [8400, 2], [8700, 3], [9000, 4], [9300, 5], [9600, 5], [9900, 6], [10200, 7], [10500, 8], [10800, 8], [11100, 9], [11400, 10], [11700, 11], [12000, 12], [12300, 12], [12600, 13], [12900, 14], [13200, 15], [13500, 15], [13800, 16], [14100, 17], [14400, 18], [14700, 19], [15000, 19], [15300, 20], [15600, 21], [15900, 22], [16200, 22], [16500, 23], [16800, 24], [17100, 25], [17400, 25], [17700, 26], [18000, 27], [18300, 28], [18600, 29], [18900, 29], [19200, 30], [19500, 31], [19800, 32], [20100, 32], [20400, 33], [20700, 34], [21000, 35], [21300, 36], [21600, 36], [21900, 37], [22200, 38], [22500, 39], [22800, 39], [23100, 40], [23400, 41], [23700, 42], [24000, 42], [24300, 43], [24600, 42], [24900, 42], [24945, 70], [25200, 42], [25245, 70], [25500, 42], [25545, 70], [25800, 42], [25845, 70], [26100, 42], [26145, 70], [26400, 42], [26444, 71], [26700, 42], [26744, 71], [27000, 42], [27044, 71], [27300, 41], [27344, 71], [27600, 41], [27644, 71], [27900, 41], [27944, 71], [28200, 41], [28244, 71], [28500, 41], [28544, 71], [28800, 41], [28843, 72], [29100, 41], [29143, 72], [29400, 41], [29443, 72], [29700, 40], [29743, 71], [30000, 40], [30043, 71], [30300, 40], [30343, 71], [30600, 40], [30643, 71], [30900, 40], [30943, 71], [31200, 40], [31243, 71], [31500, 40], [31542, 72], [31800, 40], [31843, 71], [32100, 40], [32400, 39], [32700, 39], [33000, 39], [33042, 72], [33300, 39], [33342, 72], [33600, 39], [33642, 72], [33900, 39], [33941, 73], [34200, 39], [34241, 73], [34500, 39], [34541, 73], [34800, 38], [34841, 73], [35100, 38], [35141, 73], [35400, 38], [35441, 73], [35700, 38], [35741, 73], [36000, 38], [36041, 73], [36300, 38], [36341, 73], [36600, 38], [36640, 74], [36900, 38], [36940, 74], [37200, 37], [37240, 74], [37500, 37], [37540, 73], [37800, 37], [37840, 73], [38101, 36], [38140, 73], [38403, 34], [38440, 73], [38704, 33], [38740, 73], [39006, 31], [39040, 73], [39308, 29], [39339, 74], [39609, 28], [39639, 74], [39911, 25], [39939, 74], [40213, 23], [40239, 74], [40514, 22], [40539, 74], [40816, 20], [40839, 74], [41117, 19], [41139, 74], [41419, 17], [41439, 74], [41721, 15], [41738, 75], [42022, 14], [42038, 75], [42324, 11], [42338, 75], [42625, 10], [42638, 75], [42927, 8], [42938, 75], [43229, 6], [43238, 73], [43530, 5]], "point": [57, 85]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+01.50|+02.11|+02.08"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 27, 114, 146], "mask": [[7800, 1], [8100, 1], [8400, 2], [8700, 3], [9000, 4], [9300, 5], [9600, 5], [9900, 6], [10200, 7], [10500, 8], [10800, 8], [11100, 9], [11400, 10], [11700, 11], [12000, 12], [12300, 12], [12600, 13], [12900, 14], [13200, 15], [13500, 15], [13800, 16], [14100, 17], [14400, 18], [14700, 19], [15000, 19], [15300, 20], [15600, 21], [15900, 22], [16200, 22], [16500, 23], [16800, 24], [17100, 25], [17400, 25], [17700, 26], [18000, 27], [18300, 28], [18600, 29], [18900, 29], [19200, 30], [19500, 31], [19800, 32], [20100, 32], [20400, 33], [20700, 34], [21000, 35], [21300, 36], [21600, 36], [21900, 37], [22200, 38], [22500, 39], [22800, 39], [23100, 40], [23400, 41], [23700, 42], [24000, 42], [24300, 43], [24600, 42], [24900, 42], [24945, 70], [25200, 42], [25245, 70], [25500, 42], [25545, 70], [25800, 42], [25845, 70], [26100, 42], [26145, 70], [26400, 42], [26444, 71], [26700, 42], [26744, 71], [27000, 42], [27044, 71], [27300, 41], [27344, 71], [27600, 41], [27644, 71], [27900, 41], [27944, 71], [28200, 41], [28244, 71], [28500, 41], [28544, 71], [28800, 41], [28843, 72], [29100, 41], [29143, 72], [29400, 41], [29443, 72], [29700, 40], [29743, 71], [30000, 40], [30043, 71], [30300, 40], [30343, 71], [30600, 40], [30643, 71], [30900, 40], [30943, 71], [31200, 40], [31243, 71], [31500, 40], [31542, 72], [31800, 40], [31843, 71], [32100, 40], [32400, 39], [32700, 39], [33000, 39], [33042, 72], [33300, 39], [33342, 72], [33600, 39], [33642, 72], [33900, 39], [33941, 73], [34200, 39], [34241, 52], [34301, 13], [34500, 39], [34541, 48], [34606, 8], [34800, 38], [34841, 47], [34907, 7], [35100, 38], [35141, 47], [35208, 6], [35400, 38], [35441, 47], [35508, 6], [35700, 38], [35741, 47], [35808, 6], [36000, 38], [36041, 47], [36108, 6], [36300, 38], [36341, 48], [36408, 6], [36600, 38], [36640, 49], [36708, 6], [36900, 38], [36940, 49], [37008, 6], [37200, 37], [37240, 49], [37308, 6], [37500, 37], [37540, 49], [37607, 6], [37800, 37], [37840, 49], [37907, 6], [38101, 36], [38140, 49], [38207, 6], [38403, 34], [38440, 49], [38507, 6], [38704, 33], [38740, 49], [38807, 6], [39006, 31], [39040, 49], [39107, 6], [39308, 29], [39339, 50], [39406, 7], [39609, 28], [39639, 50], [39706, 7], [39911, 25], [39939, 50], [40006, 7], [40213, 23], [40239, 50], [40306, 7], [40514, 22], [40539, 50], [40606, 7], [40816, 20], [40839, 50], [40906, 7], [41117, 19], [41139, 50], [41205, 8], [41419, 17], [41439, 50], [41505, 8], [41721, 15], [41738, 52], [41805, 8], [42022, 14], [42038, 52], [42105, 8], [42324, 11], [42338, 52], [42405, 8], [42625, 10], [42638, 52], [42705, 8], [42927, 8], [42938, 52], [43004, 9], [43229, 6], [43238, 52], [43304, 9], [43530, 5]], "point": [57, 85]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan12", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 1.0, "y": 0.9009999, "z": 2.75}, "object_poses": [{"objectName": "Potato_22312ae0", "position": {"x": -0.9648372, "y": 0.8477766, "z": 0.348953933}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": 1.51607227, "y": 0.937464237, "z": 2.11864662}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -0.7937801, "y": 0.9379421, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -1.12628, "y": 0.9379421, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.8657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.50362825, "y": 0.9798608, "z": 1.58957624}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.42390835, "y": 0.105368823, "z": 2.84730816}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -1.00114369, "y": 1.49968576, "z": 2.2165885}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": 1.38253057, "y": 0.105463147, "z": -0.0303022265}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -0.804073453, "y": 0.0866650939, "z": 2.52877021}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.876905, "y": 0.9598927, "z": 1.0763762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -1.01830924, "y": 1.56053746, "z": 0.118999816}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.51607227, "y": 0.9372058, "z": 1.00726593}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": 1.62400174, "y": 1.944939, "z": 1.363442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": -0.946484864, "y": 0.19658269, "z": -0.0445037037}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": 1.31033671, "y": 0.7431432, "z": 1.05253744}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": 1.66628313, "y": 1.500433, "z": 1.0115906}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.10601223, "y": 1.94781125, "z": 1.45824826}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.044755, "y": 0.9342062, "z": 2.159162}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -0.988836765, "y": 0.8101194, "z": 0.118999906}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": -1.01830876, "y": 1.36844981, "z": -0.03430283}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": -1.12628, "y": 1.02830088, "z": 1.0763762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.46265638, "y": 1.07142246, "z": 0.274840862}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.9230004, "y": 0.990678251, "z": 2.26549959}, "rotation": {"x": -3.14625759e-05, "y": 302.470337, "z": 4.54345864e-05}}, {"objectName": "Fork_d40bfead", "position": {"x": 1.275738, "y": 0.747115731, "z": 0.780662537}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -0.976327658, "y": 0.6270374, "z": 0.272302568}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": 1.45662689, "y": 0.9410784, "z": 1.71495044}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.4704}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": -0.712255, "y": 0.9528999, "z": 2.462146}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.8965961, "y": 0.7685599, "z": 0.779062569}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": 1.75026774, "y": 1.497415, "z": 2.30767179}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": 1.45694256, "y": 0.119435936, "z": 2.97771883}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": 1.28540039, "y": 0.9342062, "z": 2.4056766}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -0.8769051, "y": 0.9379421, "z": 0.921}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": -1.065349, "y": 1.5319767, "z": -0.110954285}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": 1.66985226, "y": 0.9423697, "z": 0.8063456}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": -1.06915367, "y": 1.95106936, "z": 1.31762326}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.597631, "y": 0.9798608, "z": 1.7985332}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -1.0813, "y": 1.499687, "z": 2.58348751}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pan_9f7abbea", "position": {"x": 1.3781, "y": 1.73753989, "z": 0.3929}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": 1.55062962, "y": 0.9458269, "z": 1.75674188}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": 1.38810134, "y": 0.8384428, "z": 1.31293225}, "rotation": {"x": 1.40334191e-14, "y": 180.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3527807837, "scene_num": 12}, "task_id": "trial_T20190907_210928_389193", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3PPLDHC3CG0YN_39GXDJN2OWVNUTYVM67RXZTXRTJV8M", "high_descs": ["Walk to the counter to the right of the stove. ", "Open the cabinet above the loaf of bread, remove the vase, and close the door. ", "Turn around and walk to the sink. ", "Put the vase in the microwave above the sink, heat up the vase, take the vase out of the microwave. ", "Look up at the cabinet above the microwave. ", "Open the cabinet on the left side, put the vase on the bottom shelf, close the door. "], "task_desc": "To heat up a vase and put it in the cabinet. ", "votes": [1, 1, 1]}, {"assignment_id": "AJQGWGESKQT4Y_39KFRKBFIQC7JFGGYYJ71KNROGAOYE", "high_descs": ["Go to the left and forward to face the counter to the right of the stove.", "Pick the vase up from the upper cabinet on the left.", "Turn around and face the microwave above the sink.", "Put the vase in the microwave and shut the door and then open the door and take the vase out again.", "Back up and face the cabinet on the left above the microwave.", "Put the vase in the cabinet and shut the door."], "task_desc": "Put a heated vase in the cabinet.", "votes": [1, 0, 1]}, {"assignment_id": "A1DMXEJGJY02E1_3SPJ033424KTI4PJILR2TO65PLYYJ1", "high_descs": ["Walk forward and stand to the left of the stove.", "Open the upper cabinet to the left of the stove and take out a glass.", "Turn around and go to the microwave.", "Microwave the cup and pick it up again.", "Look up at the cabinet above the microwave.", "Put the cup in the cabinet."], "task_desc": "Put a hot cup in the cabinet above the microwave.", "votes": [0, 1, 1]}]}}