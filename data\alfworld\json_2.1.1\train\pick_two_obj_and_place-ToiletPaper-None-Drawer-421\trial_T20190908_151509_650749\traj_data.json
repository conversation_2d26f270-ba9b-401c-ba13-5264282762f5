{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 2}, {"high_idx": 2, "image_name": "000000037.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000038.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000039.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000040.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000041.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000042.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000043.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000044.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000045.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000046.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000110.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000111.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 17}, {"high_idx": 5, "image_name": "000000138.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000139.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000140.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000141.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000142.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000143.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000144.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000145.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000146.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000147.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000148.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000149.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000150.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000151.png", "low_idx": 18}, {"high_idx": 5, "image_name": "000000152.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000153.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000154.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000155.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000156.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000157.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000158.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000159.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000160.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000203.png", "low_idx": 26}, {"high_idx": 7, "image_name": "000000204.png", "low_idx": 26}, {"high_idx": 7, "image_name": "000000205.png", "low_idx": 26}, {"high_idx": 7, "image_name": "000000206.png", "low_idx": 26}, {"high_idx": 7, "image_name": "000000207.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000208.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000209.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000210.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000211.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000212.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000213.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000214.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000215.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000216.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000217.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000218.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000219.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000221.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000222.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 28}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "ToiletPaper", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["toilet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|13|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-0.64689982, -0.64689982, 15.385992, 15.385992, 3.7993076, 3.7993076]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-2.4, -2.4, 14.684, 14.684, -0.000692814588, -0.000692814588]], "forceVisible": true, "objectId": "ToiletPaper|-00.16|+00.95|+03.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|11|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-0.64689982, -0.64689982, 15.385992, 15.385992, 3.7993076, 3.7993076]], "coordinateReceptacleObjectId": ["Drawer", [-1.2763008, -1.2763008, 12.05185508, 12.05185508, 2.4631992, 2.4631992]], "forceVisible": true, "objectId": "ToiletPaper|-00.16|+00.95|+03.85", "receptacleObjectId": "Drawer|-00.32|+00.62|+03.01"}}, {"discrete_action": {"action": "GotoLocation", "args": ["toiletpaperhanger"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|14|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-4.6264, -4.6264, 15.70399952, 15.70399952, 3.588, 3.588]], "coordinateReceptacleObjectId": ["ToiletPaperHanger", [-4.804, -4.804, 16.0, 16.0, 3.588, 3.588]], "forceVisible": true, "objectId": "ToiletPaper|-01.16|+00.90|+03.93"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-5|11|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-4.6264, -4.6264, 15.70399952, 15.70399952, 3.588, 3.588]], "coordinateReceptacleObjectId": ["Drawer", [-1.2763008, -1.2763008, 12.05185508, 12.05185508, 2.4631992, 2.4631992]], "forceVisible": true, "objectId": "ToiletPaper|-01.16|+00.90|+03.93", "receptacleObjectId": "Drawer|-00.32|+00.62|+03.01"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-00.16|+00.95|+03.85"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [0, 82, 13, 119], "mask": [[24300, 1], [24600, 2], [24900, 3], [25200, 3], [25500, 4], [25800, 4], [26100, 5], [26400, 5], [26700, 5], [27000, 6], [27300, 6], [27600, 7], [27900, 7], [28200, 8], [28500, 8], [28800, 9], [29100, 9], [29400, 10], [29700, 10], [30000, 10], [30300, 11], [30600, 11], [30900, 12], [31200, 12], [31500, 13], [31800, 13], [32100, 14], [32400, 14], [32700, 13], [33000, 9], [33300, 9], [33600, 8], [33900, 8], [34200, 7], [34500, 6], [34800, 6], [35100, 5], [35400, 3]], "point": [6, 99]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-00.32|+00.62|+03.01"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [24, 171, 147, 210], "mask": [[51025, 123], [51324, 124], [51625, 123], [51925, 123], [52226, 122], [52526, 122], [52827, 121], [53127, 121], [53428, 120], [53728, 120], [54029, 119], [54329, 116], [54630, 110], [54930, 104], [55231, 98], [55531, 97], [55832, 94], [56133, 92], [56433, 91], [56734, 89], [57034, 88], [57335, 86], [57635, 84], [57936, 82], [58236, 81], [58537, 79], [58837, 78], [59138, 76], [59438, 74], [59739, 72], [60039, 71], [60340, 70], [60640, 69], [60941, 68], [61241, 68], [61542, 67], [61843, 66], [62143, 65], [62444, 64], [62744, 64]], "point": [85, 189]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-00.16|+00.95|+03.85", "placeStationary": true, "receptacleObjectId": "Drawer|-00.32|+00.62|+03.01"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 171, 143, 286], "mask": [[51032, 112], [51331, 113], [51631, 113], [51931, 113], [52230, 114], [52530, 114], [52829, 115], [53129, 115], [53429, 115], [53728, 116], [54028, 116], [54327, 117], [54627, 113], [54926, 108], [55226, 103], [55526, 102], [55825, 101], [56125, 100], [56424, 100], [56724, 99], [57023, 99], [57323, 98], [57623, 96], [57922, 96], [58222, 95], [58521, 95], [58821, 94], [59121, 93], [59420, 92], [59720, 91], [60019, 91], [60319, 91], [60618, 91], [60918, 91], [61218, 91], [61517, 92], [61817, 92], [62116, 92], [62416, 92], [62715, 93], [63015, 93], [63315, 92], [63614, 93], [63914, 93], [64213, 94], [64513, 93], [64812, 94], [65112, 94], [65412, 94], [65711, 94], [66011, 94], [66310, 95], [66610, 95], [66910, 94], [67209, 95], [67509, 95], [67808, 96], [68108, 97], [68407, 98], [68707, 98], [69007, 98], [69306, 100], [69606, 100], [69905, 101], [70205, 101], [70504, 103], [70804, 103], [71104, 103], [71403, 104], [71703, 105], [72002, 106], [72302, 106], [72602, 106], [72901, 108], [73201, 108], [73500, 109], [73800, 109], [74100, 110], [74400, 110], [74700, 110], [75000, 110], [75300, 111], [75600, 111], [75900, 111], [76200, 111], [76500, 112], [76800, 112], [77100, 112], [77400, 112], [77700, 113], [78000, 113], [78300, 113], [78600, 113], [78900, 114], [79200, 114], [79500, 114], [79800, 114], [80100, 115], [80400, 115], [80701, 115], [81002, 114], [81303, 114], [81604, 113], [81905, 113], [82206, 112], [82507, 112], [82808, 111], [83109, 111], [83410, 110], [83711, 110], [84012, 109], [84313, 108], [84615, 107], [84916, 106], [85217, 106], [85518, 105]], "point": [71, 227]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-00.32|+00.62|+03.01"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 171, 147, 286], "mask": [[51032, 34], [51078, 66], [51331, 32], [51379, 65], [51631, 31], [51680, 64], [51931, 30], [51981, 63], [52230, 31], [52281, 63], [52530, 30], [52581, 63], [52829, 30], [52882, 62], [53129, 30], [53182, 62], [53429, 30], [53482, 62], [53728, 31], [53782, 62], [54028, 31], [54083, 61], [54327, 32], [54383, 61], [54627, 32], [54683, 61], [54926, 34], [54984, 60], [55226, 34], [55284, 60], [55526, 34], [55584, 60], [55825, 36], [55885, 59], [56125, 36], [56185, 59], [56424, 38], [56485, 59], [56724, 38], [56785, 59], [57023, 39], [57085, 59], [57323, 40], [57385, 59], [57623, 40], [57685, 59], [57922, 42], [57985, 59], [58222, 42], [58284, 60], [58521, 43], [58583, 61], [58821, 44], [58883, 61], [59121, 45], [59182, 62], [59420, 47], [59480, 64], [59720, 48], [59778, 66], [60019, 53], [60076, 68], [60319, 125], [60618, 126], [60918, 126], [61218, 126], [61517, 127], [61817, 127], [62116, 128], [62416, 128], [62715, 129], [63015, 129], [63315, 129], [63614, 130], [63914, 130], [64213, 131], [64513, 131], [64812, 132], [65112, 132], [65412, 132], [65711, 133], [66011, 133], [66310, 134], [66610, 134], [66910, 134], [67209, 135], [67509, 135], [67808, 136], [68108, 136], [68407, 137], [68707, 137], [69007, 137], [69306, 137], [69606, 137], [69905, 138], [70205, 138], [70504, 139], [70804, 139], [71104, 139], [71403, 140], [71703, 140], [72002, 141], [72302, 141], [72602, 141], [72901, 142], [73201, 142], [73500, 143], [73800, 143], [74100, 143], [74400, 143], [74700, 147], [75000, 147], [75300, 147], [75600, 147], [75900, 147], [76200, 147], [76500, 147], [76800, 147], [77100, 147], [77400, 147], [77700, 147], [78000, 147], [78300, 147], [78600, 147], [78900, 147], [79200, 147], [79500, 147], [79800, 147], [80100, 147], [80400, 147], [80701, 146], [81002, 146], [81303, 145], [81604, 144], [81905, 143], [82206, 142], [82507, 141], [82808, 140], [83109, 139], [83410, 138], [83711, 137], [84012, 136], [84313, 135], [84615, 133], [84916, 132], [85217, 131], [85518, 130]], "point": [73, 227]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-01.16|+00.90|+03.93"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [178, 209, 198, 212], "mask": [[62579, 20], [62878, 21], [63178, 21], [63478, 21]], "point": [188, 209]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-00.32|+00.62|+03.01"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [24, 171, 147, 210], "mask": [[51025, 123], [51324, 124], [51625, 123], [51925, 123], [52226, 122], [52526, 122], [52827, 121], [53127, 121], [53428, 120], [53728, 120], [54029, 119], [54329, 119], [54630, 118], [54930, 118], [55231, 117], [55531, 117], [55832, 116], [56133, 115], [56433, 115], [56734, 114], [57034, 114], [57335, 113], [57635, 113], [57936, 112], [58236, 112], [58537, 111], [58837, 111], [59138, 110], [59438, 110], [59739, 109], [60039, 109], [60340, 108], [60640, 108], [60941, 107], [61241, 107], [61542, 106], [61843, 105], [62143, 105], [62444, 104], [62744, 104]], "point": [85, 189]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-01.16|+00.90|+03.93", "placeStationary": true, "receptacleObjectId": "Drawer|-00.32|+00.62|+03.01"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 171, 147, 286], "mask": [[51032, 34], [51078, 66], [51331, 32], [51379, 65], [51631, 31], [51680, 64], [51931, 30], [51981, 63], [52230, 31], [52281, 63], [52530, 30], [52581, 63], [52829, 30], [52882, 62], [53129, 30], [53182, 62], [53429, 30], [53482, 62], [53728, 31], [53782, 62], [54028, 31], [54083, 61], [54327, 32], [54383, 61], [54627, 32], [54683, 61], [54926, 34], [54984, 60], [55226, 34], [55284, 60], [55526, 34], [55584, 60], [55825, 36], [55885, 59], [56125, 36], [56185, 59], [56424, 38], [56485, 59], [56724, 38], [56785, 59], [57023, 39], [57085, 59], [57323, 40], [57385, 59], [57623, 40], [57685, 59], [57922, 42], [57985, 59], [58222, 42], [58284, 60], [58521, 43], [58583, 61], [58821, 44], [58883, 61], [59121, 45], [59182, 62], [59420, 47], [59480, 64], [59720, 48], [59778, 66], [60019, 53], [60076, 68], [60319, 125], [60618, 126], [60918, 126], [61218, 126], [61517, 127], [61817, 127], [62116, 128], [62416, 128], [62715, 129], [63015, 129], [63315, 129], [63614, 130], [63914, 130], [64213, 131], [64513, 131], [64812, 132], [65112, 132], [65412, 132], [65711, 133], [66011, 133], [66310, 134], [66610, 134], [66910, 134], [67209, 135], [67509, 135], [67808, 136], [68108, 136], [68407, 137], [68707, 135], [69007, 134], [69306, 134], [69606, 133], [69905, 133], [70205, 132], [70504, 132], [70804, 131], [71104, 131], [71403, 132], [71703, 132], [72002, 133], [72302, 132], [72602, 132], [72901, 133], [73201, 133], [73500, 134], [73800, 134], [74100, 134], [74400, 134], [74700, 134], [74845, 2], [75000, 135], [75144, 3], [75300, 135], [75443, 4], [75600, 135], [75742, 5], [75900, 136], [76041, 6], [76200, 136], [76341, 6], [76500, 136], [76640, 7], [76800, 136], [76942, 5], [77100, 137], [77244, 3], [77400, 137], [77700, 137], [78000, 138], [78300, 138], [78600, 138], [78900, 139], [79200, 139], [79500, 140], [79800, 141], [80100, 141], [80400, 142], [80701, 142], [81002, 143], [81303, 144], [81604, 144], [81905, 143], [82206, 142], [82507, 141], [82808, 140], [83109, 139], [83410, 138], [83711, 137], [84012, 136], [84313, 135], [84615, 133], [84916, 132], [85217, 131], [85518, 130]], "point": [73, 227]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-00.32|+00.62|+03.01"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 171, 147, 286], "mask": [[51032, 34], [51078, 66], [51331, 32], [51379, 65], [51631, 31], [51680, 64], [51931, 30], [51981, 63], [52230, 31], [52281, 11], [52296, 48], [52530, 30], [52581, 10], [52597, 47], [52829, 30], [52882, 8], [52897, 47], [53129, 30], [53182, 8], [53198, 46], [53429, 30], [53482, 8], [53498, 46], [53728, 31], [53782, 8], [53798, 46], [54028, 31], [54083, 8], [54098, 46], [54327, 32], [54383, 8], [54398, 46], [54627, 32], [54683, 8], [54699, 45], [54926, 34], [54984, 7], [54999, 45], [55226, 34], [55284, 8], [55299, 45], [55526, 34], [55584, 8], [55599, 45], [55825, 36], [55885, 7], [55900, 44], [56125, 36], [56185, 7], [56200, 44], [56424, 38], [56485, 8], [56500, 44], [56724, 38], [56785, 8], [56800, 44], [57023, 39], [57085, 8], [57100, 44], [57323, 40], [57385, 8], [57401, 43], [57623, 40], [57685, 9], [57701, 43], [57922, 42], [57985, 9], [58000, 44], [58222, 42], [58284, 11], [58299, 45], [58521, 43], [58583, 61], [58821, 44], [58883, 61], [59121, 45], [59182, 62], [59420, 47], [59480, 64], [59720, 48], [59778, 66], [60019, 53], [60076, 68], [60319, 125], [60618, 126], [60918, 126], [61218, 126], [61517, 127], [61817, 127], [62116, 128], [62416, 128], [62715, 129], [63015, 129], [63315, 129], [63614, 130], [63914, 130], [64213, 131], [64513, 131], [64812, 132], [65112, 132], [65412, 132], [65711, 133], [66011, 133], [66310, 134], [66610, 134], [66910, 134], [67209, 135], [67509, 135], [67808, 136], [68108, 136], [68407, 137], [68707, 137], [69007, 137], [69306, 137], [69606, 137], [69905, 138], [70205, 138], [70504, 139], [70804, 139], [71104, 139], [71403, 140], [71703, 140], [72002, 141], [72302, 141], [72602, 141], [72901, 142], [73201, 142], [73500, 143], [73800, 143], [74100, 143], [74400, 143], [74700, 147], [75000, 147], [75300, 147], [75600, 147], [75900, 147], [76200, 147], [76500, 147], [76800, 147], [77100, 147], [77400, 147], [77700, 147], [78000, 147], [78300, 147], [78600, 147], [78900, 147], [79200, 147], [79500, 147], [79800, 147], [80100, 147], [80400, 147], [80701, 146], [81002, 146], [81303, 145], [81604, 144], [81905, 143], [82206, 142], [82507, 141], [82808, 140], [83109, 139], [83410, 138], [83711, 137], [84012, 136], [84313, 135], [84615, 133], [84916, 132], [85217, 131], [85518, 130]], "point": [73, 227]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan421", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.0, "y": 0.9008256, "z": 3.25}, "object_poses": [{"objectName": "SoapBar_0fd13d7a", "position": {"x": -0.470215231, "y": 0.206573546, "z": 2.49546385}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBar_0fd13d7a", "position": {"x": -0.13493228, "y": 0.9532904, "z": 3.59649658}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_1814f8bc", "position": {"x": -0.215308219, "y": 0.952834368, "z": 3.596498}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_1814f8bc", "position": {"x": -0.4820853, "y": 0.812007546, "z": 2.35045624}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_862832c1", "position": {"x": -0.2465005, "y": 0.206633866, "z": 2.95740056}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_862832c1", "position": {"x": -0.358357877, "y": 0.5137005, "z": 1.40800071}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_dab0d4d7", "position": {"x": -2.146, "y": 0.0531792976, "z": 3.843}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ToiletPaper_dab0d4d7", "position": {"x": -0.417906046, "y": 0.809, "z": 3.230978}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_f6c37fb2", "position": {"x": -0.03691358, "y": 1.658, "z": 3.44300032}, "rotation": {"x": 0.0, "y": 90.0004, "z": 0.0}}, {"objectName": "SoapBar_0fd13d7a", "position": {"x": -0.417906046, "y": 0.8120697, "z": 3.17273784}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_335b3797", "position": {"x": -0.161724955, "y": 0.9498269, "z": 3.846498}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plunger_a5ac7169", "position": {"x": -0.139841557, "y": -0.0006361902, "z": 3.369947}, "rotation": {"x": -0.00118185522, "y": 0.0004360497, "z": 0.000781859}}, {"objectName": "Candle_1814f8bc", "position": {"x": -0.134933412, "y": 0.952834368, "z": 3.53399777}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_5484beaa", "position": {"x": -0.14460443, "y": 0.8046577, "z": 1.32575345}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_dab0d4d7", "position": {"x": -1.1566, "y": 0.897, "z": 3.92599988}, "rotation": {"x": 0.0, "y": 180.000046, "z": 90.0}}, {"objectName": "SprayBottle_55833eba", "position": {"x": -0.3537268, "y": 0.8134041, "z": 1.28866208}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Towel_e5bc324c", "position": {"x": -1.106, "y": 1.151, "z": 1.28995311}, "rotation": {"x": 0.0, "y": 1.70754731e-06, "z": 0.0}}, {"objectName": "Cloth_c4a4c4ad", "position": {"x": -0.2465005, "y": 0.5110778, "z": 3.01296377}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ScrubBrush_274c0193", "position": {"x": -0.346180737, "y": -0.000173203647, "z": 3.34195137}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_862832c1", "position": {"x": -0.215307146, "y": 0.954732358, "z": 3.72149682}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2952784775, "scene_num": 421}, "task_id": "trial_T20190908_151509_650749", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3F7G1FSFWQPLE_3KAKFY4PGXT4SFUN7Q2TSFFUXXTI3Z", "high_descs": ["Turn right to face the toilet.", "Pick up the toilet paper roll off of the toilet tank.", "Walk over to the leftmost sink basin on your right.", "Open the drawer that is all the way to the left in front of you and put the toilet paper roll inside in the back left corner, then close the drawer.", "Turn left and walk over to the wall, then look down at the ground.", "Pick up the toilet paper roll off of the ground.", "Walk back over to the leftmost sink basin.", "Open the drawer that is all the way to the left in front of you and put the toilet paper roll inside to the right of the roll that's already in there then close the drawer."], "task_desc": "Move two toilet paper rolls into a drawer.", "votes": [1, 1]}, {"assignment_id": "A1NWXL4QO30M8U_3PEIJLRY6WKY98L6GWDW0MD6J4CXW7", "high_descs": ["Turn right to face the toilet. ", "Pick up the roll of toilet paper on the left side of the toilet. ", "Turn around and take one step, then turn left and take one step, then turn left and turn to the sink.", "Open the drawer on the left beneath the sink and put the toilet paper in the back left side of the drawer.", "Turn to the left and walk to the wall.", "Pick up the empty roll on the floor near the toilet. ", "Turn around and take one step, then turn left to face the sink. ", "Open the drawer on the left below the sink and put the empty roll inside to the right of the roll of toilet paper. "], "task_desc": "Put away the toilet paper rolls. ", "votes": [1, 1]}, {"assignment_id": "AHBWX3WYMAB0E_3X31TUMD70DA8DOE5PLNW2SPGIU1LR", "high_descs": ["Turn to your right and face the toilet.", "Pick up the roll of toilet paper from the back of the toilet.", "Move slightly to your right to the front of the bathroom vanity.", "Put the roll of toilet paper in the top left drawer of the vanity.", "Turn left and move to the toilet paper holder.", "Pick up the empty toilet paper roll from the floor.", "Turn right and move back to the front of the vanity.", "Put the empty toilet paper roll in the top left drawer of the vanity."], "task_desc": "Move the full and empty toilet paper roll to the top left drawer of the vanity.", "votes": [1, 1]}]}}