{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000108.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000111.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000219.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000221.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000222.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 34}, {"high_idx": 8, "image_name": "000000234.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000235.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000236.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000237.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000238.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000239.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000240.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000241.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000242.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000243.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000244.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000245.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000246.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000247.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000248.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000249.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000250.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000251.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000252.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000253.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000254.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000255.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000256.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000257.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000258.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000259.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000260.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000261.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000262.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000268.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000269.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000270.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000271.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000272.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000273.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000274.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000275.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000276.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000277.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000278.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000279.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000280.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000281.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000282.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000283.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000284.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000285.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000286.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000287.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000288.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000289.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000290.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000291.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000292.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000293.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000294.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000295.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000315.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000316.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000317.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000318.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000319.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000320.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000321.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000322.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000323.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000324.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000325.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000326.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000327.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000328.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000329.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000330.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000331.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000332.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000333.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000334.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000335.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000336.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000337.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000338.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000339.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000340.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000341.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000342.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000343.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000344.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000345.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000346.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000347.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000348.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000349.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000350.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000351.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000352.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000353.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000354.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000355.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000356.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000357.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000358.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000359.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000360.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000361.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000362.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000363.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000364.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000365.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000366.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000367.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000368.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000369.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000370.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000371.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000372.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000373.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000374.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000375.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000376.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000377.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000378.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000379.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000380.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000381.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000382.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000383.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000384.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000385.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000386.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000387.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000388.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000389.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000390.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000391.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000392.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000393.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000394.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000395.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000396.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000397.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000398.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000399.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000400.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000401.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000402.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000403.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000404.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000405.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000406.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000407.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000408.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000409.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000410.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000411.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000412.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000413.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000414.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000415.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000416.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000417.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000418.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000419.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000420.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000421.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000422.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000423.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000424.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000425.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000426.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000427.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000428.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000429.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000430.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000431.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000432.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000433.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000434.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000435.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000436.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000437.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000438.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000439.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000440.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000441.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000442.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000443.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000444.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000445.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000446.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000447.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000448.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000449.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000450.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000451.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000452.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000453.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000454.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000455.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000456.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000457.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000458.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000459.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000460.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000461.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000462.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000463.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000464.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000465.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000466.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000467.png", "low_idx": 79}, {"high_idx": 11, "image_name": "000000468.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000469.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000470.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000471.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000472.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000473.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000474.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000475.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000476.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000477.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000478.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000479.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000480.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000481.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000482.png", "low_idx": 80}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-16|-4|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [-16.009128, -16.009128, -0.992664, -0.992664, 2.864473344, 2.864473344]], "coordinateReceptacleObjectId": ["SideTable", [-16.284, -16.284, -1.236, -1.236, 2.904, 2.904]], "forceVisible": true, "objectId": "Knife|-04.00|+00.72|-00.25"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-13|-4|0|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-13.45781328, -13.45781328, -1.163003684, -1.163003684, 3.9042096, 3.9042096]], "forceVisible": true, "objectId": "Bread|-03.36|+00.98|-00.29"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-12|-5|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [-16.009128, -16.009128, -0.992664, -0.992664, 2.864473344, 2.864473344]], "coordinateReceptacleObjectId": ["DiningTable", [-12.132, -12.132, -1.764, -1.764, 0.0, 0.0]], "forceVisible": true, "objectId": "Knife|-04.00|+00.72|-00.25", "receptacleObjectId": "DiningTable|-03.03|+00.00|-00.44"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-13|-4|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-13.45781328, -13.45781328, -1.163003684, -1.163003684, 3.9042096, 3.9042096]], "coordinateReceptacleObjectId": ["DiningTable", [-12.132, -12.132, -1.764, -1.764, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-03.36|+00.98|-00.29|BreadSliced_4"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-8|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-15|-5|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "sidetable"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-13.45781328, -13.45781328, -1.163003684, -1.163003684, 3.9042096, 3.9042096]], "coordinateReceptacleObjectId": ["SideTable", [-16.284, -16.284, -1.236, -1.236, 2.904, 2.904]], "forceVisible": true, "objectId": "Bread|-03.36|+00.98|-00.29|BreadSliced_4", "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 12, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|-04.00|+00.72|-00.25"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [103, 165, 176, 174], "mask": [[49318, 7], [49604, 65], [49903, 74], [50204, 73], [50506, 71], [50809, 68], [51113, 36], [51170, 6], [51418, 31], [51471, 3], [51725, 23], [52035, 13]], "point": [139, 168]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-03.36|+00.98|-00.29"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [63, 111, 164, 158], "mask": [[33072, 5], [33370, 94], [33669, 95], [33969, 95], [34268, 96], [34568, 96], [34867, 97], [35167, 97], [35467, 97], [35766, 98], [36066, 98], [36365, 99], [36665, 99], [36965, 99], [37264, 100], [37564, 100], [37864, 100], [38164, 100], [38463, 101], [38763, 101], [39063, 101], [39363, 102], [39663, 102], [39964, 100], [40264, 100], [40564, 100], [40864, 100], [41164, 100], [41465, 99], [41765, 99], [42066, 98], [42366, 98], [42666, 98], [42967, 97], [43267, 97], [43567, 97], [43868, 96], [44168, 96], [44468, 96], [44769, 95], [45069, 95], [45369, 95], [45669, 95], [45970, 94], [46270, 94], [46571, 93], [46871, 92], [47172, 90]], "point": [113, 133]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|-04.00|+00.72|-00.25", "placeStationary": true, "receptacleObjectId": "DiningTable|-03.03|+00.00|-00.44"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 189], "mask": [[0, 5], [119, 185], [419, 185], [719, 185], [1020, 183], [1320, 183], [1620, 182], [1920, 182], [2220, 182], [2520, 182], [2820, 182], [3120, 182], [3420, 182], [3721, 181], [4021, 181], [4321, 182], [4621, 182], [4921, 182], [5221, 183], [5521, 184], [5821, 184], [6121, 185], [6421, 185], [6721, 186], [7021, 186], [7321, 187], [7621, 187], [7921, 188], [8220, 189], [8520, 88], [8609, 1], [8611, 99], [8820, 88], [8909, 1], [8911, 99], [9120, 85], [9206, 2], [9209, 1], [9211, 100], [9420, 85], [9506, 2], [9509, 1], [9511, 101], [9720, 85], [9806, 1], [9808, 1], [9811, 101], [10020, 85], [10106, 1], [10108, 1], [10110, 1], [10112, 101], [10320, 85], [10406, 1], [10408, 1], [10410, 1], [10412, 101], [10620, 85], [10706, 1], [10708, 1], [10710, 1], [10712, 102], [10919, 86], [11006, 1], [11008, 1], [11010, 1], [11012, 102], [11219, 86], [11306, 1], [11308, 1], [11310, 1], [11312, 103], [11485, 1], [11518, 87], [11612, 103], [11776, 1], [11785, 1], [11818, 87], [11913, 103], [12059, 1], [12067, 1], [12076, 1], [12085, 1], [12093, 1], [12118, 87], [12213, 104], [12358, 2], [12367, 2], [12376, 2], [12385, 2], [12393, 1], [12418, 88], [12512, 110], [12658, 3], [12667, 2], [12676, 2], [12684, 3], [12693, 2], [12718, 90], [12811, 186], [13018, 91], [13111, 186], [13318, 91], [13411, 186], [13618, 92], [13711, 187], [13918, 92], [14012, 186], [14218, 92], [14312, 186], [14518, 92], [14612, 186], [14818, 92], [14912, 187], [15118, 92], [15212, 187], [15418, 92], [15512, 187], [15717, 93], [15813, 297], [16113, 297], [16413, 297], [16713, 24], [16741, 269], [17014, 21], [17100, 210], [17314, 20], [17400, 210], [17614, 20], [17700, 210], [17914, 19], [18000, 211], [18215, 18], [18300, 211], [18515, 17], [18600, 211], [18815, 17], [18900, 211], [19115, 17], [19200, 211], [19415, 17], [19500, 211], [19716, 15], [19800, 211], [20016, 15], [20100, 211], [20316, 15], [20400, 211], [20616, 14], [20700, 211], [20916, 14], [21000, 212], [21216, 14], [21300, 213], [21516, 13], [21600, 229], [21900, 229], [22200, 228], [22500, 228], [22800, 228], [23100, 227], [23400, 227], [23700, 227], [24000, 226], [24300, 226], [24600, 226], [24900, 226], [25200, 226], [25500, 227], [25800, 227], [26100, 227], [26400, 227], [26700, 227], [27000, 227], [27300, 228], [27600, 228], [27900, 228], [28200, 228], [28500, 228], [28800, 229], [29100, 229], [29400, 229], [29700, 229], [30000, 229], [30300, 229], [30600, 230], [30900, 230], [31200, 230], [31500, 230], [31800, 230], [32100, 231], [32400, 231], [32700, 232], [33000, 4200], [37213, 10], [37273, 6], [37427, 6], [37486, 11], [37514, 10], [37574, 3], [37728, 3], [37785, 11], [37814, 11], [38085, 10], [38115, 10], [38384, 10], [38416, 10], [38683, 11], [38717, 10], [38982, 11], [39017, 11], [39282, 10], [39318, 10], [39581, 10], [39619, 10], [39880, 10], [39920, 10], [40179, 10], [40221, 9], [40478, 11], [40521, 10], [40778, 10], [40822, 10], [41077, 10], [41123, 10], [41376, 10], [41424, 9], [41675, 10], [41724, 10], [41975, 9], [42025, 10], [42274, 9], [42326, 10], [42573, 10], [42627, 9], [42872, 10], [42928, 9], [43171, 10], [43228, 10], [43471, 9], [43529, 9], [43770, 9], [43830, 9], [44069, 9], [44131, 9], [44368, 10], [44432, 9], [44668, 9], [44732, 9], [44967, 9], [45033, 9], [45266, 9], [45334, 9], [45565, 9], [45635, 8], [45865, 8], [45935, 9], [46164, 9], [46236, 9], [46463, 9], [46537, 9], [46762, 9], [46838, 8], [47061, 9], [47139, 8], [47361, 8], [47439, 9], [47660, 8], [47740, 9], [47959, 9], [48041, 8], [48258, 9], [48342, 8], [48558, 8], [48642, 9], [48857, 8], [48943, 8], [49156, 8], [49244, 8], [49455, 8], [49545, 8], [49754, 9], [49846, 8], [50054, 8], [50146, 8], [50353, 8], [50447, 8], [50652, 8], [50748, 8], [50951, 8], [51049, 7], [51251, 7], [51350, 7], [51550, 8], [51650, 8], [51849, 8], [51951, 8], [52148, 8], [52252, 7], [52447, 8], [52553, 7], [52747, 7], [52853, 8], [53046, 7], [53154, 8], [53345, 8], [53455, 7], [53644, 8], [53756, 7], [53944, 7], [54057, 7], [54243, 7], [54357, 7], [54542, 7], [54658, 8], [54841, 7], [54959, 7], [55140, 8], [55259, 8], [55439, 8], [55560, 8], [55738, 8], [55861, 7], [56038, 8], [56161, 7], [56338, 7], [56464, 1], [56641, 1]], "point": [149, 94]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-03.36|+00.98|-00.29|BreadSliced_4"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [148, 112, 156, 158], "mask": [[33449, 7], [33748, 8], [34048, 8], [34348, 9], [34648, 9], [34948, 9], [35248, 9], [35548, 9], [35848, 9], [36148, 9], [36448, 9], [36748, 9], [37048, 9], [37348, 9], [37648, 9], [37948, 9], [38248, 9], [38548, 9], [38848, 9], [39148, 9], [39448, 9], [39748, 9], [40048, 9], [40348, 9], [40648, 9], [40948, 9], [41248, 9], [41548, 9], [41848, 9], [42148, 9], [42448, 9], [42748, 9], [43048, 9], [43348, 9], [43648, 9], [43948, 9], [44248, 9], [44548, 9], [44848, 9], [45148, 9], [45448, 9], [45748, 9], [46048, 9], [46348, 9], [46648, 9], [46949, 7], [47249, 7]], "point": [152, 134]}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-03.36|+00.98|-00.29|BreadSliced_4", "placeStationary": true, "receptacleObjectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 32743], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 62]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 6442], [6448, 286], [6757, 274], [7061, 267], [7364, 262], [7666, 259], [7967, 257], [8269, 254], [8570, 252], [8871, 250], [9171, 250], [9472, 248], [9772, 248], [10072, 248], [10373, 247], [10673, 246], [10973, 246], [11273, 246], [11573, 246], [11873, 246], [12173, 246], [12473, 246], [12773, 246], [13073, 246], [13373, 246], [13673, 247], [13973, 247], [14273, 246], [14573, 246], [14873, 246], [15173, 246], [15473, 246], [15774, 245], [16074, 245], [16374, 245], [16674, 245], [16974, 245], [17274, 245], [17574, 245], [17874, 245], [18174, 245], [18474, 245], [18774, 245], [19073, 246], [19373, 246], [19673, 246], [19973, 246], [20273, 246], [20573, 246], [20873, 246], [21173, 246], [21473, 246], [21773, 246], [22073, 247], [22373, 247], [22673, 247], [22973, 248], [23272, 249], [23572, 249], [23872, 251], [24171, 8572], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 81]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-03.36|+00.98|-00.29|BreadSliced_4"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [119, 22, 173, 81], "mask": [[6442, 6], [6734, 23], [7031, 30], [7328, 36], [7626, 40], [7925, 42], [8224, 45], [8523, 47], [8822, 49], [9121, 50], [9421, 51], [9720, 52], [10020, 52], [10320, 53], [10620, 53], [10919, 54], [11219, 54], [11519, 54], [11819, 54], [12119, 54], [12419, 54], [12719, 54], [13019, 54], [13319, 54], [13619, 54], [13920, 53], [14220, 53], [14519, 54], [14819, 54], [15119, 54], [15419, 54], [15719, 55], [16019, 55], [16319, 55], [16619, 55], [16919, 55], [17219, 55], [17519, 55], [17819, 55], [18119, 55], [18419, 55], [18719, 55], [19019, 54], [19319, 54], [19619, 54], [19919, 54], [20219, 54], [20519, 54], [20819, 54], [21119, 54], [21419, 54], [21719, 54], [22019, 54], [22320, 53], [22620, 53], [22920, 53], [23221, 51], [23521, 51], [23821, 51], [24123, 48]], "point": [146, 50]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 32743], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 62]}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-03.36|+00.98|-00.29|BreadSliced_4", "placeStationary": true, "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [21, 115, 140, 251], "mask": [[34251, 87], [34551, 18], [34576, 62], [34851, 12], [34877, 61], [35150, 13], [35177, 20], [35208, 30], [35450, 13], [35477, 8], [35509, 29], [35749, 13], [35788, 9], [35808, 30], [36049, 13], [36083, 15], [36106, 32], [36349, 13], [36376, 24], [36403, 35], [36648, 13], [36676, 62], [36948, 13], [36976, 62], [37247, 13], [37276, 62], [37547, 13], [37575, 63], [37846, 14], [37875, 63], [38146, 13], [38175, 63], [38446, 13], [38474, 64], [38745, 14], [38774, 64], [39045, 13], [39074, 64], [39344, 14], [39373, 65], [39644, 14], [39673, 65], [39943, 15], [39973, 65], [40243, 15], [40272, 66], [40542, 17], [40564, 74], [40842, 96], [41142, 97], [41441, 98], [41741, 98], [42041, 98], [42341, 98], [42641, 98], [42942, 97], [43242, 97], [43543, 96], [43843, 96], [44143, 96], [44444, 95], [44744, 95], [45045, 94], [45345, 94], [45684, 55], [46007, 32], [46310, 29], [46547, 2], [46611, 28], [46847, 6], [46884, 6], [46910, 29], [47147, 13], [47183, 21], [47209, 30], [47448, 20], [47482, 23], [47506, 33], [47748, 33], [47782, 57], [48049, 91], [48349, 91], [48649, 91], [48950, 90], [49250, 90], [49550, 90], [49849, 91], [50149, 91], [50449, 91], [50748, 92], [51048, 92], [51348, 92], [51626, 1], [51647, 93], [51926, 2], [51947, 93], [52225, 3], [52246, 94], [52525, 4], [52546, 94], [52824, 116], [53124, 116], [53424, 116], [53723, 117], [54022, 118], [54322, 118], [54621, 119], [54922, 118], [55223, 118], [55523, 11], [55546, 95], [55824, 11], [55846, 95], [56124, 11], [56145, 96], [56425, 11], [56445, 96], [56725, 11], [56745, 92], [57026, 11], [57044, 93], [57326, 11], [57344, 93], [57627, 11], [57644, 93], [57928, 10], [57943, 94], [58228, 11], [58243, 94], [58529, 10], [58542, 95], [58829, 11], [58842, 95], [59130, 10], [59142, 95], [59430, 107], [59731, 106], [60032, 105], [60332, 105], [60633, 104], [60933, 104], [61234, 103], [61534, 102], [61835, 101], [62136, 9], [62158, 78], [62436, 10], [62457, 79], [62737, 9], [62757, 79], [63037, 10], [63057, 79], [63338, 9], [63356, 80], [63638, 10], [63656, 80], [63939, 10], [63956, 75], [64240, 9], [64256, 71], [64540, 10], [64555, 68], [64841, 9], [64855, 64], [65141, 10], [65155, 62], [65442, 9], [65454, 62], [65742, 10], [65754, 60], [66043, 9], [66054, 59], [66343, 10], [66354, 57], [66644, 65], [66945, 64], [67245, 63], [67546, 62], [67846, 61], [68147, 60], [68447, 60], [68748, 58], [69049, 8], [69349, 9], [69650, 8], [69950, 9], [70251, 8], [70551, 9], [70852, 8], [71153, 8], [71453, 8], [71754, 8], [72054, 8], [72355, 8], [72655, 8], [72956, 8], [73256, 9], [73557, 8], [73858, 8], [74158, 8], [74459, 7], [74759, 7], [75060, 5]], "point": [80, 182]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan28", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.75, "y": 0.900998235, "z": -0.5}, "object_poses": [{"objectName": "Potato_5e3edff7", "position": {"x": -0.9900841, "y": 0.9742758, "z": -3.67670417}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -3.12119365, "y": 0.79206425, "z": -3.286844}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -4.182487, "y": 0.690064251, "z": -0.187332019}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -0.3156285, "y": 0.9418486, "z": -1.4505}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -4.06235027, "y": 0.6924257, "z": -0.126497984}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -0.440981239, "y": 0.766915262, "z": -2.632294}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -0.07872227, "y": 0.9562999, "z": -1.4505}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -0.54936254, "y": 0.0811921954, "z": -2.84986377}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -0.252262622, "y": 1.94312871, "z": -0.71324563}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -3.36197615, "y": 0.7742202, "z": -3.711399}, "rotation": {"x": 0.0, "y": 334.125916, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -3.935215, "y": 0.7742202, "z": -2.95915747}, "rotation": {"x": 0.0, "y": 334.125916, "z": 0.0}}, {"objectName": "Knife_1b546504", "position": {"x": -3.631666, "y": 0.7857067, "z": -3.15535545}, "rotation": {"x": 0.0, "y": 334.125916, "z": 0.0}}, {"objectName": "Knife_1b546504", "position": {"x": -4.002282, "y": 0.716118336, "z": -0.248166}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -3.16354656, "y": 0.892809868, "z": -0.376520932}, "rotation": {"x": 0.0, "y": 90.00033, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -0.7690936, "y": 0.07881671, "z": -3.321147}, "rotation": {"x": 0.0, "y": 225.0, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -0.202733248, "y": 1.93725908, "z": -2.338461}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -2.56082582, "y": 0.9760524, "z": -0.6338309}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.55309939, "y": 0.8510728, "z": -3.50686383}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "SaltShaker_6f13b4f9", "position": {"x": -0.43649286, "y": 0.7435723, "z": -1.33847606}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -0.126795739, "y": 1.9352572, "z": -2.17961454}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -0.636511, "y": 0.075627625, "z": -3.18856454}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -4.24255562, "y": 0.689485431, "z": -0.491501868}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_c7418f98", "position": {"x": -0.2221, "y": 0.961999953, "z": -1.8457}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -2.76173186, "y": 0.8951693, "z": -0.3765232}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -0.397310972, "y": 0.9430031, "z": -2.67028737}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -0.760500252, "y": 0.864232957, "z": -3.45282269}, "rotation": {"x": 9.923132e-15, "y": 315.000031, "z": -9.92312e-15}}, {"objectName": "Pan_d1250561", "position": {"x": -0.217, "y": 0.958, "z": -2.215}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -0.4273748, "y": 0.09629309, "z": -1.48973584}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -3.36445332, "y": 0.9760524, "z": -0.290750921}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -3.67723751, "y": 0.775088251, "z": -3.55653381}, "rotation": {"x": 0.0, "y": 334.125916, "z": 0.0}}, {"objectName": "Knife_1b546504", "position": {"x": -4.06235027, "y": 0.716118336, "z": -0.430667937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_6f13b4f9", "position": {"x": -0.669536233, "y": 0.0769338, "z": -3.332316}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -0.244763374, "y": 0.938606143, "z": -2.72939873}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -3.77244115, "y": 0.760130465, "z": -3.75784922}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -2.13991928, "y": 1.00371194, "z": -3.439128}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -3.23029256, "y": 0.7596526, "z": -3.06190515}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -3.28484178, "y": 0.79206425, "z": -2.949436}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -0.207706392, "y": 1.52977717, "z": -0.9949342}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -0.5836203, "y": 0.748395562, "z": -1.48847449}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -0.3213089, "y": 1.57969487, "z": -0.646000147}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -0.252379239, "y": 1.28159928, "z": -0.646000266}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -0.3591778, "y": 1.48701847, "z": -0.9949338}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 1288874860, "scene_num": 28}, "task_id": "trial_T20190907_180304_503402", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3MXBLYX2CDGTP_323Q6SJS8LXRR7DYPVWTSQJA6KTHFB", "high_descs": ["Turn right and walk to the small brown table with two knives on it.", "Pick up one knife from the table.", "Go to the white table on your right.", "Slice the loaf of bread on the back of the table with the knife.", "Turn to the other loaf of bread on the right front side of the white table.", "Place the knife under the loaf of bread.", "Turn to your left and face the sliced loaf of bread.", "Pick up one slice of bread from the table.", "Turn around then turn left and walk to the stove.", "Put the slice of bread in the microwave above the stove and cook it.", "Take the slice of bread out of the microwave, turn around and walk back to the small brown table next to the white table.", "Put the slice of bread on the brown table next to the knife."], "task_desc": "Slice a loaf of bread with a knife, cook it in a microwave put it the small brown table.", "votes": [1, 1]}, {"assignment_id": "A1OWHPMKE7YAGL_3EG49X351XTSU4K2T6BB16UR1D9X6L", "high_descs": ["Move forward, go to the right, go to the wall, then turn right to face the small black table. ", "Pick up the yellow handled knife that is closer to the back wall.", "Turn to the right, go forward, then turn to the left at the loaf of bread that is to the left of the fork.", "Cut the loaf of bread into slices with the knife. ", "Turn around, move forward, turn to the left, move forward, turn to the left at the other loaf of bread. ", "Put the knife on the table underneath the loaf of bread. ", "Move forward, turn to the left, go forward to the loaf of bread that has been cut. ", "Pick up a slice of bread from the table. ", "Turn around, move forward toward the table, take a left and go forward to the microwave. ", "Put the bread in the microwave and turn in on to cook, remove the bread from the microwave when it is done cooking and close the door. ", "Back up, turn around, go forward to the door, take a right and go to the small black table. ", "Put the bread down on the table in front of the yellow handled knife."], "task_desc": "Put cooked bread on a table. ", "votes": [1, 1]}, {"assignment_id": "A1AKL5YH9NLD2V_3RXPCZQMQSSM1WOFYQZQIO3FQCEG1V", "high_descs": ["Turn right and walk to the black table by the door.", "Pick up the yellow knife next to the sponge on the table.", "Turn right and walk to the bread next to the lotion on the table.", "Cut up the loaf of bread next to the lotion.", "Walk to the middle of the white table.", "Place the knife under the rightmost loaf of bread.", "Walk to the cut up loaf of bread on the table.", "Pick up a piece of bread from the table.", "Turn right and walk to the microwave above the stove.", "Microwave the piece of bread then pick up the piece of bread from the microwave and close the microwave.", "Turn around and walk to the black next to the door.", "Place the piece of bread on the black table."], "task_desc": "Place a microwaved piece of bread onto the black table by the door.", "votes": [1, 1]}]}}