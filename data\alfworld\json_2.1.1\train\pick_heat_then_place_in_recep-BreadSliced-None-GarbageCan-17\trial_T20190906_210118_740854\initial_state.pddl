
(define (problem plan_trial_T20190906_210118_740854)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON>haker - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Apple_bar__minus_00_dot_14_bar__plus_00_dot_82_bar__minus_00_dot_61 - object
        Bowl_bar__plus_01_dot_29_bar__plus_00_dot_91_bar__plus_00_dot_71 - object
        Bread_bar__minus_01_dot_01_bar__plus_00_dot_81_bar__plus_01_dot_90 - object
        Bread_bar__minus_01_dot_26_bar__plus_01_dot_70_bar__plus_01_dot_09 - object
        ButterKnife_bar__plus_01_dot_12_bar__plus_00_dot_89_bar__plus_02_dot_37 - object
        ButterKnife_bar__plus_01_dot_13_bar__plus_00_dot_91_bar__plus_00_dot_14 - object
        ButterKnife_bar__minus_01_dot_14_bar__plus_00_dot_91_bar__minus_00_dot_82 - object
        CellPhone_bar__plus_01_dot_13_bar__plus_00_dot_91_bar__minus_00_dot_43 - object
        CellPhone_bar__plus_01_dot_28_bar__plus_00_dot_88_bar__plus_02_dot_69 - object
        CellPhone_bar__minus_01_dot_48_bar__plus_00_dot_75_bar__plus_02_dot_83 - object
        Chair_bar__plus_00_dot_98_bar__plus_00_dot_54_bar__plus_02_dot_52 - object
        Cup_bar__minus_01_dot_36_bar__plus_01_dot_65_bar__plus_00_dot_18 - object
        DishSponge_bar__plus_01_dot_12_bar__plus_00_dot_89_bar__plus_02_dot_80 - object
        Egg_bar__plus_01_dot_52_bar__plus_00_dot_95_bar__plus_00_dot_42 - object
        Egg_bar__minus_00_dot_08_bar__plus_00_dot_79_bar__minus_00_dot_49 - object
        Egg_bar__minus_01_dot_44_bar__plus_01_dot_67_bar__plus_00_dot_88 - object
        Faucet_bar__minus_00_dot_03_bar__plus_00_dot_93_bar__minus_00_dot_81 - object
        Fork_bar__plus_01_dot_28_bar__plus_00_dot_89_bar__plus_02_dot_26 - object
        Fork_bar__plus_01_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_14 - object
        Glassbottle_bar__plus_01_dot_09_bar__plus_00_dot_12_bar__plus_01_dot_16 - object
        Glassbottle_bar__minus_01_dot_11_bar__plus_00_dot_75_bar__plus_02_dot_46 - object
        Kettle_bar__minus_01_dot_36_bar__plus_00_dot_74_bar__plus_03_dot_02 - object
        Knife_bar__plus_01_dot_20_bar__plus_00_dot_91_bar__plus_02_dot_69 - object
        Knife_bar__plus_01_dot_45_bar__plus_00_dot_91_bar__plus_02_dot_69 - object
        Knife_bar__minus_01_dot_20_bar__plus_00_dot_77_bar__plus_01_dot_90 - object
        Lettuce_bar__minus_01_dot_29_bar__plus_00_dot_82_bar__plus_02_dot_64 - object
        Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_82_bar__plus_00_dot_88 - object
        LightSwitch_bar__plus_00_dot_12_bar__plus_01_dot_35_bar__plus_03_dot_80 - object
        Mug_bar__plus_01_dot_03_bar__plus_00_dot_88_bar__plus_02_dot_80 - object
        Pan_bar__minus_01_dot_33_bar__plus_00_dot_90_bar__minus_00_dot_70 - object
        PepperShaker_bar__minus_01_dot_48_bar__plus_00_dot_75_bar__plus_02_dot_64 - object
        Plate_bar__minus_01_dot_33_bar__plus_01_dot_66_bar__minus_00_dot_13 - object
        Potato_bar__plus_00_dot_95_bar__plus_00_dot_93_bar__plus_02_dot_26 - object
        Potato_bar__plus_01_dot_45_bar__plus_00_dot_93_bar__plus_02_dot_15 - object
        Potato_bar__minus_01_dot_26_bar__plus_01_dot_33_bar__plus_00_dot_99 - object
        Pot_bar__plus_00_dot_50_bar__plus_00_dot_94_bar__minus_00_dot_74 - object
        Pot_bar__plus_00_dot_80_bar__plus_00_dot_94_bar__minus_00_dot_74 - object
        SaltShaker_bar__plus_01_dot_44_bar__plus_00_dot_91_bar__minus_00_dot_43 - object
        Sink_bar__minus_00_dot_01_bar__plus_00_dot_91_bar__minus_00_dot_62 - object
        SoapBottle_bar__plus_00_dot_95_bar__plus_00_dot_89_bar__plus_02_dot_15 - object
        SoapBottle_bar__plus_01_dot_22_bar__plus_00_dot_12_bar__plus_00_dot_73 - object
        Spatula_bar__plus_01_dot_37_bar__plus_00_dot_93_bar__minus_00_dot_14 - object
        Spatula_bar__minus_00_dot_97_bar__plus_00_dot_93_bar__minus_00_dot_43 - object
        Spatula_bar__minus_01_dot_20_bar__plus_00_dot_76_bar__plus_02_dot_64 - object
        Spoon_bar__plus_00_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_65 - object
        Spoon_bar__plus_01_dot_16_bar__plus_00_dot_78_bar__plus_00_dot_33 - object
        Spoon_bar__plus_01_dot_37_bar__plus_00_dot_89_bar__plus_02_dot_48 - object
        SprayBottle_bar__plus_01_dot_28_bar__plus_00_dot_89_bar__plus_02_dot_37 - object
        SprayBottle_bar__minus_01_dot_01_bar__plus_00_dot_75_bar__plus_02_dot_46 - object
        StoveKnob_bar__plus_00_dot_43_bar__plus_01_dot_09_bar__minus_00_dot_91 - object
        StoveKnob_bar__plus_00_dot_55_bar__plus_01_dot_09_bar__minus_00_dot_91 - object
        StoveKnob_bar__plus_00_dot_78_bar__plus_01_dot_09_bar__minus_00_dot_91 - object
        StoveKnob_bar__plus_00_dot_89_bar__plus_01_dot_09_bar__minus_00_dot_91 - object
        Tomato_bar__plus_01_dot_13_bar__plus_00_dot_96_bar__minus_00_dot_14 - object
        Tomato_bar__minus_01_dot_01_bar__plus_00_dot_80_bar__plus_02_dot_64 - object
        Tomato_bar__minus_01_dot_29_bar__plus_00_dot_96_bar__minus_00_dot_48 - object
        Window_bar__plus_01_dot_60_bar__plus_01_dot_65_bar__plus_02_dot_49 - object
        Window_bar__minus_00_dot_01_bar__plus_01_dot_64_bar__minus_00_dot_97 - object
        WineBottle_bar__minus_01_dot_51_bar__plus_00_dot_91_bar__minus_00_dot_65 - object
        Cabinet_bar__plus_00_dot_34_bar__plus_00_dot_40_bar__minus_00_dot_35 - receptacle
        Cabinet_bar__plus_00_dot_59_bar__plus_02_dot_31_bar__minus_00_dot_65 - receptacle
        Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__plus_00_dot_53 - receptacle
        Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__plus_00_dot_58 - receptacle
        Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__plus_01_dot_23 - receptacle
        Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__minus_00_dot_18 - receptacle
        Cabinet_bar__plus_01_dot_19_bar__plus_02_dot_06_bar__plus_00_dot_38 - receptacle
        Cabinet_bar__plus_01_dot_19_bar__plus_02_dot_06_bar__minus_00_dot_64 - receptacle
        Cabinet_bar__plus_01_dot_19_bar__plus_02_dot_31_bar__plus_00_dot_43 - receptacle
        Cabinet_bar__plus_01_dot_19_bar__plus_02_dot_31_bar__plus_01_dot_18 - receptacle
        Cabinet_bar__minus_00_dot_37_bar__plus_00_dot_40_bar__minus_00_dot_35 - receptacle
        Cabinet_bar__minus_00_dot_57_bar__plus_02_dot_06_bar__minus_00_dot_65 - receptacle
        Cabinet_bar__minus_00_dot_99_bar__plus_00_dot_40_bar__plus_00_dot_37 - receptacle
        Cabinet_bar__minus_00_dot_99_bar__plus_00_dot_40_bar__minus_00_dot_34 - receptacle
        Cabinet_bar__minus_01_dot_10_bar__plus_02_dot_06_bar__minus_00_dot_65 - receptacle
        Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_06_bar__plus_00_dot_38 - receptacle
        Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_06_bar__minus_00_dot_28 - receptacle
        Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_06_bar__minus_00_dot_33 - receptacle
        Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_31_bar__plus_00_dot_43 - receptacle
        Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_31_bar__plus_01_dot_38 - receptacle
        CoffeeMachine_bar__plus_01_dot_31_bar__plus_00_dot_90_bar__minus_00_dot_74 - receptacle
        CounterTop_bar__plus_01_dot_28_bar__plus_00_dot_95_bar__plus_00_dot_07 - receptacle
        CounterTop_bar__minus_01_dot_00_bar__plus_00_dot_95_bar__minus_00_dot_67 - receptacle
        DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48 - receptacle
        DiningTable_bar__minus_01_dot_21_bar__plus_00_dot_78_bar__plus_02_dot_46 - receptacle
        Drawer_bar__plus_01_dot_12_bar__plus_00_dot_79_bar__plus_00_dot_37 - receptacle
        Drawer_bar__plus_01_dot_12_bar__plus_00_dot_79_bar__plus_00_dot_73 - receptacle
        Drawer_bar__plus_01_dot_12_bar__plus_00_dot_79_bar__plus_01_dot_08 - receptacle
        Drawer_bar__plus_01_dot_12_bar__plus_00_dot_79_bar__minus_00_dot_02 - receptacle
        Drawer_bar__minus_01_dot_12_bar__plus_00_dot_79_bar__plus_00_dot_21 - receptacle
        Drawer_bar__minus_01_dot_12_bar__plus_00_dot_79_bar__minus_00_dot_17 - receptacle
        Fridge_bar__minus_01_dot_30_bar__plus_00_dot_01_bar__plus_00_dot_99 - receptacle
        GarbageCan_bar__plus_01_dot_30_bar__plus_00_dot_01_bar__plus_01_dot_47 - receptacle
        Microwave_bar__minus_01_dot_34_bar__plus_00_dot_90_bar__plus_00_dot_05 - receptacle
        Sink_bar__minus_00_dot_01_bar__plus_00_dot_91_bar__minus_00_dot_62_bar_SinkBasin - receptacle
        StoveBurner_bar__plus_00_dot_50_bar__plus_00_dot_93_bar__minus_00_dot_47 - receptacle
        StoveBurner_bar__plus_00_dot_50_bar__plus_00_dot_93_bar__minus_00_dot_74 - receptacle
        StoveBurner_bar__plus_00_dot_80_bar__plus_00_dot_93_bar__minus_00_dot_47 - receptacle
        StoveBurner_bar__plus_00_dot_80_bar__plus_00_dot_93_bar__minus_00_dot_74 - receptacle
        Toaster_bar__plus_01_dot_36_bar__plus_00_dot_90_bar__plus_01_dot_05 - receptacle
        loc_bar_3_bar_5_bar_1_bar__minus_30 - location
        loc_bar_0_bar_10_bar_3_bar_30 - location
        loc_bar_2_bar_0_bar_2_bar_45 - location
        loc_bar_3_bar_1_bar_1_bar_60 - location
        loc_bar_2_bar_10_bar_1_bar_0 - location
        loc_bar_2_bar_10_bar_1_bar_60 - location
        loc_bar_2_bar_1_bar_1_bar_45 - location
        loc_bar__minus_1_bar_2_bar_3_bar__minus_30 - location
        loc_bar_3_bar_2_bar_1_bar__minus_30 - location
        loc_bar_3_bar_2_bar_1_bar_60 - location
        loc_bar__minus_1_bar_2_bar_2_bar_60 - location
        loc_bar_3_bar_5_bar_1_bar_60 - location
        loc_bar_2_bar_10_bar_1_bar_45 - location
        loc_bar__minus_2_bar_0_bar_3_bar_60 - location
        loc_bar_2_bar_0_bar_2_bar__minus_30 - location
        loc_bar_0_bar_3_bar_3_bar_45 - location
        loc_bar__minus_2_bar_0_bar_3_bar__minus_30 - location
        loc_bar_0_bar_0_bar_2_bar_45 - location
        loc_bar__minus_2_bar_0_bar_2_bar_45 - location
        loc_bar__minus_1_bar_4_bar_3_bar_60 - location
        loc_bar_0_bar_13_bar_0_bar_30 - location
        loc_bar__minus_2_bar_0_bar_2_bar__minus_30 - location
        loc_bar_1_bar_0_bar_2_bar_60 - location
        loc_bar_2_bar_0_bar_2_bar_60 - location
        loc_bar_2_bar_0_bar_1_bar__minus_30 - location
        loc_bar__minus_1_bar_5_bar_3_bar__minus_30 - location
        loc_bar_1_bar_2_bar_1_bar_60 - location
        loc_bar_2_bar_0_bar_2_bar_30 - location
        loc_bar_3_bar_5_bar_1_bar_45 - location
        loc_bar_2_bar_0_bar_1_bar_60 - location
        loc_bar__minus_1_bar_1_bar_3_bar__minus_30 - location
        loc_bar_0_bar_2_bar_1_bar_45 - location
        loc_bar_1_bar_6_bar_1_bar_60 - location
        loc_bar__minus_2_bar_0_bar_3_bar_45 - location
        loc_bar_1_bar_2_bar_2_bar_60 - location
        loc_bar_0_bar_0_bar_2_bar_0 - location
        loc_bar_0_bar_6_bar_1_bar_45 - location
        loc_bar_2_bar_0_bar_1_bar_45 - location
        loc_bar_3_bar_13_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType Fridge_bar__minus_01_dot_30_bar__plus_00_dot_01_bar__plus_00_dot_99 FridgeType)
        (receptacleType CounterTop_bar__plus_01_dot_28_bar__plus_00_dot_95_bar__plus_00_dot_07 CounterTopType)
        (receptacleType Sink_bar__minus_00_dot_01_bar__plus_00_dot_91_bar__minus_00_dot_62_bar_SinkBasin SinkBasinType)
        (receptacleType Drawer_bar__plus_01_dot_12_bar__plus_00_dot_79_bar__plus_00_dot_73 DrawerType)
        (receptacleType StoveBurner_bar__plus_00_dot_80_bar__plus_00_dot_93_bar__minus_00_dot_74 StoveBurnerType)
        (receptacleType Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__plus_00_dot_58 CabinetType)
        (receptacleType Drawer_bar__plus_01_dot_12_bar__plus_00_dot_79_bar__minus_00_dot_02 DrawerType)
        (receptacleType Cabinet_bar__minus_00_dot_99_bar__plus_00_dot_40_bar__plus_00_dot_37 CabinetType)
        (receptacleType Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__minus_00_dot_18 CabinetType)
        (receptacleType Cabinet_bar__plus_01_dot_19_bar__plus_02_dot_06_bar__minus_00_dot_64 CabinetType)
        (receptacleType Drawer_bar__minus_01_dot_12_bar__plus_00_dot_79_bar__minus_00_dot_17 DrawerType)
        (receptacleType GarbageCan_bar__plus_01_dot_30_bar__plus_00_dot_01_bar__plus_01_dot_47 GarbageCanType)
        (receptacleType Microwave_bar__minus_01_dot_34_bar__plus_00_dot_90_bar__plus_00_dot_05 MicrowaveType)
        (receptacleType Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_06_bar__minus_00_dot_33 CabinetType)
        (receptacleType Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__plus_01_dot_23 CabinetType)
        (receptacleType DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48 DiningTableType)
        (receptacleType Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_06_bar__minus_00_dot_28 CabinetType)
        (receptacleType Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_31_bar__plus_01_dot_38 CabinetType)
        (receptacleType CounterTop_bar__minus_01_dot_00_bar__plus_00_dot_95_bar__minus_00_dot_67 CounterTopType)
        (receptacleType CoffeeMachine_bar__plus_01_dot_31_bar__plus_00_dot_90_bar__minus_00_dot_74 CoffeeMachineType)
        (receptacleType Cabinet_bar__plus_00_dot_34_bar__plus_00_dot_40_bar__minus_00_dot_35 CabinetType)
        (receptacleType Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__plus_00_dot_53 CabinetType)
        (receptacleType StoveBurner_bar__plus_00_dot_80_bar__plus_00_dot_93_bar__minus_00_dot_47 StoveBurnerType)
        (receptacleType Toaster_bar__plus_01_dot_36_bar__plus_00_dot_90_bar__plus_01_dot_05 ToasterType)
        (receptacleType Cabinet_bar__minus_00_dot_99_bar__plus_00_dot_40_bar__minus_00_dot_34 CabinetType)
        (receptacleType Cabinet_bar__minus_01_dot_10_bar__plus_02_dot_06_bar__minus_00_dot_65 CabinetType)
        (receptacleType Drawer_bar__plus_01_dot_12_bar__plus_00_dot_79_bar__plus_00_dot_37 DrawerType)
        (receptacleType Drawer_bar__plus_01_dot_12_bar__plus_00_dot_79_bar__plus_01_dot_08 DrawerType)
        (receptacleType Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_06_bar__plus_00_dot_38 CabinetType)
        (receptacleType Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_31_bar__plus_00_dot_43 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_37_bar__plus_00_dot_40_bar__minus_00_dot_35 CabinetType)
        (receptacleType Cabinet_bar__plus_00_dot_59_bar__plus_02_dot_31_bar__minus_00_dot_65 CabinetType)
        (receptacleType Drawer_bar__minus_01_dot_12_bar__plus_00_dot_79_bar__plus_00_dot_21 DrawerType)
        (receptacleType Cabinet_bar__plus_01_dot_19_bar__plus_02_dot_31_bar__plus_01_dot_18 CabinetType)
        (receptacleType StoveBurner_bar__plus_00_dot_50_bar__plus_00_dot_93_bar__minus_00_dot_74 StoveBurnerType)
        (receptacleType StoveBurner_bar__plus_00_dot_50_bar__plus_00_dot_93_bar__minus_00_dot_47 StoveBurnerType)
        (receptacleType Cabinet_bar__minus_00_dot_57_bar__plus_02_dot_06_bar__minus_00_dot_65 CabinetType)
        (receptacleType DiningTable_bar__minus_01_dot_21_bar__plus_00_dot_78_bar__plus_02_dot_46 DiningTableType)
        (receptacleType Cabinet_bar__plus_01_dot_19_bar__plus_02_dot_31_bar__plus_00_dot_43 CabinetType)
        (receptacleType Cabinet_bar__plus_01_dot_19_bar__plus_02_dot_06_bar__plus_00_dot_38 CabinetType)
        (objectType ButterKnife_bar__minus_01_dot_14_bar__plus_00_dot_91_bar__minus_00_dot_82 ButterKnifeType)
        (objectType Spatula_bar__plus_01_dot_37_bar__plus_00_dot_93_bar__minus_00_dot_14 SpatulaType)
        (objectType Spoon_bar__plus_01_dot_37_bar__plus_00_dot_89_bar__plus_02_dot_48 SpoonType)
        (objectType CellPhone_bar__plus_01_dot_13_bar__plus_00_dot_91_bar__minus_00_dot_43 CellPhoneType)
        (objectType ButterKnife_bar__plus_01_dot_13_bar__plus_00_dot_91_bar__plus_00_dot_14 ButterKnifeType)
        (objectType Pan_bar__minus_01_dot_33_bar__plus_00_dot_90_bar__minus_00_dot_70 PanType)
        (objectType SoapBottle_bar__plus_00_dot_95_bar__plus_00_dot_89_bar__plus_02_dot_15 SoapBottleType)
        (objectType ButterKnife_bar__plus_01_dot_12_bar__plus_00_dot_89_bar__plus_02_dot_37 ButterKnifeType)
        (objectType Egg_bar__minus_00_dot_08_bar__plus_00_dot_79_bar__minus_00_dot_49 EggType)
        (objectType Glassbottle_bar__plus_01_dot_09_bar__plus_00_dot_12_bar__plus_01_dot_16 GlassbottleType)
        (objectType PepperShaker_bar__minus_01_dot_48_bar__plus_00_dot_75_bar__plus_02_dot_64 PepperShakerType)
        (objectType Pot_bar__plus_00_dot_50_bar__plus_00_dot_94_bar__minus_00_dot_74 PotType)
        (objectType Knife_bar__plus_01_dot_20_bar__plus_00_dot_91_bar__plus_02_dot_69 KnifeType)
        (objectType SprayBottle_bar__minus_01_dot_01_bar__plus_00_dot_75_bar__plus_02_dot_46 SprayBottleType)
        (objectType Pot_bar__plus_00_dot_80_bar__plus_00_dot_94_bar__minus_00_dot_74 PotType)
        (objectType LightSwitch_bar__plus_00_dot_12_bar__plus_01_dot_35_bar__plus_03_dot_80 LightSwitchType)
        (objectType Cup_bar__minus_01_dot_36_bar__plus_01_dot_65_bar__plus_00_dot_18 CupType)
        (objectType Sink_bar__minus_00_dot_01_bar__plus_00_dot_91_bar__minus_00_dot_62 SinkType)
        (objectType SoapBottle_bar__plus_01_dot_22_bar__plus_00_dot_12_bar__plus_00_dot_73 SoapBottleType)
        (objectType SaltShaker_bar__plus_01_dot_44_bar__plus_00_dot_91_bar__minus_00_dot_43 SaltShakerType)
        (objectType Egg_bar__plus_01_dot_52_bar__plus_00_dot_95_bar__plus_00_dot_42 EggType)
        (objectType StoveKnob_bar__plus_00_dot_43_bar__plus_01_dot_09_bar__minus_00_dot_91 StoveKnobType)
        (objectType Window_bar__plus_01_dot_60_bar__plus_01_dot_65_bar__plus_02_dot_49 WindowType)
        (objectType Glassbottle_bar__minus_01_dot_11_bar__plus_00_dot_75_bar__plus_02_dot_46 GlassbottleType)
        (objectType Chair_bar__plus_00_dot_98_bar__plus_00_dot_54_bar__plus_02_dot_52 ChairType)
        (objectType Potato_bar__plus_00_dot_95_bar__plus_00_dot_93_bar__plus_02_dot_26 PotatoType)
        (objectType Spoon_bar__plus_00_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_65 SpoonType)
        (objectType Tomato_bar__plus_01_dot_13_bar__plus_00_dot_96_bar__minus_00_dot_14 TomatoType)
        (objectType Lettuce_bar__minus_01_dot_29_bar__plus_00_dot_82_bar__plus_02_dot_64 LettuceType)
        (objectType Bread_bar__minus_01_dot_26_bar__plus_01_dot_70_bar__plus_01_dot_09 BreadType)
        (objectType Kettle_bar__minus_01_dot_36_bar__plus_00_dot_74_bar__plus_03_dot_02 KettleType)
        (objectType Fork_bar__plus_01_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_14 ForkType)
        (objectType StoveKnob_bar__plus_00_dot_55_bar__plus_01_dot_09_bar__minus_00_dot_91 StoveKnobType)
        (objectType Bowl_bar__plus_01_dot_29_bar__plus_00_dot_91_bar__plus_00_dot_71 BowlType)
        (objectType Spoon_bar__plus_01_dot_16_bar__plus_00_dot_78_bar__plus_00_dot_33 SpoonType)
        (objectType Potato_bar__minus_01_dot_26_bar__plus_01_dot_33_bar__plus_00_dot_99 PotatoType)
        (objectType Mug_bar__plus_01_dot_03_bar__plus_00_dot_88_bar__plus_02_dot_80 MugType)
        (objectType Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_82_bar__plus_00_dot_88 LettuceType)
        (objectType Tomato_bar__minus_01_dot_29_bar__plus_00_dot_96_bar__minus_00_dot_48 TomatoType)
        (objectType StoveKnob_bar__plus_00_dot_89_bar__plus_01_dot_09_bar__minus_00_dot_91 StoveKnobType)
        (objectType Window_bar__minus_00_dot_01_bar__plus_01_dot_64_bar__minus_00_dot_97 WindowType)
        (objectType Knife_bar__minus_01_dot_20_bar__plus_00_dot_77_bar__plus_01_dot_90 KnifeType)
        (objectType WineBottle_bar__minus_01_dot_51_bar__plus_00_dot_91_bar__minus_00_dot_65 WineBottleType)
        (objectType StoveKnob_bar__plus_00_dot_78_bar__plus_01_dot_09_bar__minus_00_dot_91 StoveKnobType)
        (objectType Apple_bar__minus_00_dot_14_bar__plus_00_dot_82_bar__minus_00_dot_61 AppleType)
        (objectType DishSponge_bar__plus_01_dot_12_bar__plus_00_dot_89_bar__plus_02_dot_80 DishSpongeType)
        (objectType Plate_bar__minus_01_dot_33_bar__plus_01_dot_66_bar__minus_00_dot_13 PlateType)
        (objectType CellPhone_bar__plus_01_dot_28_bar__plus_00_dot_88_bar__plus_02_dot_69 CellPhoneType)
        (objectType SprayBottle_bar__plus_01_dot_28_bar__plus_00_dot_89_bar__plus_02_dot_37 SprayBottleType)
        (objectType Tomato_bar__minus_01_dot_01_bar__plus_00_dot_80_bar__plus_02_dot_64 TomatoType)
        (objectType CellPhone_bar__minus_01_dot_48_bar__plus_00_dot_75_bar__plus_02_dot_83 CellPhoneType)
        (objectType Potato_bar__plus_01_dot_45_bar__plus_00_dot_93_bar__plus_02_dot_15 PotatoType)
        (objectType Spatula_bar__minus_00_dot_97_bar__plus_00_dot_93_bar__minus_00_dot_43 SpatulaType)
        (objectType Egg_bar__minus_01_dot_44_bar__plus_01_dot_67_bar__plus_00_dot_88 EggType)
        (objectType Fork_bar__plus_01_dot_28_bar__plus_00_dot_89_bar__plus_02_dot_26 ForkType)
        (objectType Spatula_bar__minus_01_dot_20_bar__plus_00_dot_76_bar__plus_02_dot_64 SpatulaType)
        (objectType Bread_bar__minus_01_dot_01_bar__plus_00_dot_81_bar__plus_01_dot_90 BreadType)
        (objectType Knife_bar__plus_01_dot_45_bar__plus_00_dot_91_bar__plus_02_dot_69 KnifeType)
        (canContain FridgeType BreadType)
        (canContain FridgeType BowlType)
        (canContain FridgeType PotType)
        (canContain FridgeType WineBottleType)
        (canContain FridgeType MugType)
        (canContain FridgeType EggType)
        (canContain FridgeType GlassbottleType)
        (canContain FridgeType LettuceType)
        (canContain FridgeType PotatoType)
        (canContain FridgeType CupType)
        (canContain FridgeType PlateType)
        (canContain FridgeType TomatoType)
        (canContain FridgeType AppleType)
        (canContain FridgeType PanType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType SprayBottleType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType KettleType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType WineBottleType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType GlassbottleType)
        (canContain CounterTopType CellPhoneType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain SinkBasinType DishSpongeType)
        (canContain SinkBasinType BowlType)
        (canContain SinkBasinType KettleType)
        (canContain SinkBasinType PotType)
        (canContain SinkBasinType MugType)
        (canContain SinkBasinType EggType)
        (canContain SinkBasinType ForkType)
        (canContain SinkBasinType GlassbottleType)
        (canContain SinkBasinType SpoonType)
        (canContain SinkBasinType LettuceType)
        (canContain SinkBasinType PotatoType)
        (canContain SinkBasinType ButterKnifeType)
        (canContain SinkBasinType CupType)
        (canContain SinkBasinType PlateType)
        (canContain SinkBasinType TomatoType)
        (canContain SinkBasinType KnifeType)
        (canContain SinkBasinType AppleType)
        (canContain SinkBasinType PanType)
        (canContain SinkBasinType SpatulaType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType SprayBottleType)
        (canContain DrawerType ForkType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain StoveBurnerType KettleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType SprayBottleType)
        (canContain DrawerType ForkType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType SprayBottleType)
        (canContain DrawerType ForkType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain GarbageCanType BreadType)
        (canContain GarbageCanType DishSpongeType)
        (canContain GarbageCanType SprayBottleType)
        (canContain GarbageCanType WineBottleType)
        (canContain GarbageCanType EggType)
        (canContain GarbageCanType SoapBottleType)
        (canContain GarbageCanType LettuceType)
        (canContain GarbageCanType PotatoType)
        (canContain GarbageCanType TomatoType)
        (canContain GarbageCanType AppleType)
        (canContain MicrowaveType MugType)
        (canContain MicrowaveType PotatoType)
        (canContain MicrowaveType EggType)
        (canContain MicrowaveType AppleType)
        (canContain MicrowaveType TomatoType)
        (canContain MicrowaveType BreadType)
        (canContain MicrowaveType CupType)
        (canContain MicrowaveType GlassbottleType)
        (canContain MicrowaveType PlateType)
        (canContain MicrowaveType BowlType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DiningTableType SaltShakerType)
        (canContain DiningTableType BreadType)
        (canContain DiningTableType DishSpongeType)
        (canContain DiningTableType SprayBottleType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType KettleType)
        (canContain DiningTableType PotType)
        (canContain DiningTableType WineBottleType)
        (canContain DiningTableType MugType)
        (canContain DiningTableType EggType)
        (canContain DiningTableType ForkType)
        (canContain DiningTableType GlassbottleType)
        (canContain DiningTableType CellPhoneType)
        (canContain DiningTableType SpoonType)
        (canContain DiningTableType SoapBottleType)
        (canContain DiningTableType LettuceType)
        (canContain DiningTableType PotatoType)
        (canContain DiningTableType ButterKnifeType)
        (canContain DiningTableType CupType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType PepperShakerType)
        (canContain DiningTableType TomatoType)
        (canContain DiningTableType KnifeType)
        (canContain DiningTableType AppleType)
        (canContain DiningTableType PanType)
        (canContain DiningTableType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType SprayBottleType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType KettleType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType WineBottleType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType GlassbottleType)
        (canContain CounterTopType CellPhoneType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain CoffeeMachineType MugType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType KettleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType SprayBottleType)
        (canContain DrawerType ForkType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType SprayBottleType)
        (canContain DrawerType ForkType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType SprayBottleType)
        (canContain DrawerType ForkType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType KettleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain StoveBurnerType KettleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DiningTableType SaltShakerType)
        (canContain DiningTableType BreadType)
        (canContain DiningTableType DishSpongeType)
        (canContain DiningTableType SprayBottleType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType KettleType)
        (canContain DiningTableType PotType)
        (canContain DiningTableType WineBottleType)
        (canContain DiningTableType MugType)
        (canContain DiningTableType EggType)
        (canContain DiningTableType ForkType)
        (canContain DiningTableType GlassbottleType)
        (canContain DiningTableType CellPhoneType)
        (canContain DiningTableType SpoonType)
        (canContain DiningTableType SoapBottleType)
        (canContain DiningTableType LettuceType)
        (canContain DiningTableType PotatoType)
        (canContain DiningTableType ButterKnifeType)
        (canContain DiningTableType CupType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType PepperShakerType)
        (canContain DiningTableType TomatoType)
        (canContain DiningTableType KnifeType)
        (canContain DiningTableType AppleType)
        (canContain DiningTableType PanType)
        (canContain DiningTableType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType SprayBottleType)
        (canContain CabinetType BowlType)
        (canContain CabinetType KettleType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType GlassbottleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (pickupable ButterKnife_bar__minus_01_dot_14_bar__plus_00_dot_91_bar__minus_00_dot_82)
        (pickupable Spatula_bar__plus_01_dot_37_bar__plus_00_dot_93_bar__minus_00_dot_14)
        (pickupable Spoon_bar__plus_01_dot_37_bar__plus_00_dot_89_bar__plus_02_dot_48)
        (pickupable CellPhone_bar__plus_01_dot_13_bar__plus_00_dot_91_bar__minus_00_dot_43)
        (pickupable ButterKnife_bar__plus_01_dot_13_bar__plus_00_dot_91_bar__plus_00_dot_14)
        (pickupable Pan_bar__minus_01_dot_33_bar__plus_00_dot_90_bar__minus_00_dot_70)
        (pickupable SoapBottle_bar__plus_00_dot_95_bar__plus_00_dot_89_bar__plus_02_dot_15)
        (pickupable ButterKnife_bar__plus_01_dot_12_bar__plus_00_dot_89_bar__plus_02_dot_37)
        (pickupable Egg_bar__minus_00_dot_08_bar__plus_00_dot_79_bar__minus_00_dot_49)
        (pickupable Glassbottle_bar__plus_01_dot_09_bar__plus_00_dot_12_bar__plus_01_dot_16)
        (pickupable PepperShaker_bar__minus_01_dot_48_bar__plus_00_dot_75_bar__plus_02_dot_64)
        (pickupable Pot_bar__plus_00_dot_50_bar__plus_00_dot_94_bar__minus_00_dot_74)
        (pickupable Knife_bar__plus_01_dot_20_bar__plus_00_dot_91_bar__plus_02_dot_69)
        (pickupable SprayBottle_bar__minus_01_dot_01_bar__plus_00_dot_75_bar__plus_02_dot_46)
        (pickupable Pot_bar__plus_00_dot_80_bar__plus_00_dot_94_bar__minus_00_dot_74)
        (pickupable Cup_bar__minus_01_dot_36_bar__plus_01_dot_65_bar__plus_00_dot_18)
        (pickupable SoapBottle_bar__plus_01_dot_22_bar__plus_00_dot_12_bar__plus_00_dot_73)
        (pickupable SaltShaker_bar__plus_01_dot_44_bar__plus_00_dot_91_bar__minus_00_dot_43)
        (pickupable Egg_bar__plus_01_dot_52_bar__plus_00_dot_95_bar__plus_00_dot_42)
        (pickupable Glassbottle_bar__minus_01_dot_11_bar__plus_00_dot_75_bar__plus_02_dot_46)
        (pickupable Potato_bar__plus_00_dot_95_bar__plus_00_dot_93_bar__plus_02_dot_26)
        (pickupable Spoon_bar__plus_00_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_65)
        (pickupable Tomato_bar__plus_01_dot_13_bar__plus_00_dot_96_bar__minus_00_dot_14)
        (pickupable Lettuce_bar__minus_01_dot_29_bar__plus_00_dot_82_bar__plus_02_dot_64)
        (pickupable Bread_bar__minus_01_dot_26_bar__plus_01_dot_70_bar__plus_01_dot_09)
        (pickupable Kettle_bar__minus_01_dot_36_bar__plus_00_dot_74_bar__plus_03_dot_02)
        (pickupable Fork_bar__plus_01_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_14)
        (pickupable Bowl_bar__plus_01_dot_29_bar__plus_00_dot_91_bar__plus_00_dot_71)
        (pickupable Spoon_bar__plus_01_dot_16_bar__plus_00_dot_78_bar__plus_00_dot_33)
        (pickupable Potato_bar__minus_01_dot_26_bar__plus_01_dot_33_bar__plus_00_dot_99)
        (pickupable Mug_bar__plus_01_dot_03_bar__plus_00_dot_88_bar__plus_02_dot_80)
        (pickupable Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_82_bar__plus_00_dot_88)
        (pickupable Tomato_bar__minus_01_dot_29_bar__plus_00_dot_96_bar__minus_00_dot_48)
        (pickupable Knife_bar__minus_01_dot_20_bar__plus_00_dot_77_bar__plus_01_dot_90)
        (pickupable WineBottle_bar__minus_01_dot_51_bar__plus_00_dot_91_bar__minus_00_dot_65)
        (pickupable Apple_bar__minus_00_dot_14_bar__plus_00_dot_82_bar__minus_00_dot_61)
        (pickupable DishSponge_bar__plus_01_dot_12_bar__plus_00_dot_89_bar__plus_02_dot_80)
        (pickupable Plate_bar__minus_01_dot_33_bar__plus_01_dot_66_bar__minus_00_dot_13)
        (pickupable CellPhone_bar__plus_01_dot_28_bar__plus_00_dot_88_bar__plus_02_dot_69)
        (pickupable SprayBottle_bar__plus_01_dot_28_bar__plus_00_dot_89_bar__plus_02_dot_37)
        (pickupable Tomato_bar__minus_01_dot_01_bar__plus_00_dot_80_bar__plus_02_dot_64)
        (pickupable CellPhone_bar__minus_01_dot_48_bar__plus_00_dot_75_bar__plus_02_dot_83)
        (pickupable Potato_bar__plus_01_dot_45_bar__plus_00_dot_93_bar__plus_02_dot_15)
        (pickupable Spatula_bar__minus_00_dot_97_bar__plus_00_dot_93_bar__minus_00_dot_43)
        (pickupable Egg_bar__minus_01_dot_44_bar__plus_01_dot_67_bar__plus_00_dot_88)
        (pickupable Fork_bar__plus_01_dot_28_bar__plus_00_dot_89_bar__plus_02_dot_26)
        (pickupable Spatula_bar__minus_01_dot_20_bar__plus_00_dot_76_bar__plus_02_dot_64)
        (pickupable Bread_bar__minus_01_dot_01_bar__plus_00_dot_81_bar__plus_01_dot_90)
        (pickupable Knife_bar__plus_01_dot_45_bar__plus_00_dot_91_bar__plus_02_dot_69)
        (isReceptacleObject Pan_bar__minus_01_dot_33_bar__plus_00_dot_90_bar__minus_00_dot_70)
        (isReceptacleObject Pot_bar__plus_00_dot_50_bar__plus_00_dot_94_bar__minus_00_dot_74)
        (isReceptacleObject Pot_bar__plus_00_dot_80_bar__plus_00_dot_94_bar__minus_00_dot_74)
        (isReceptacleObject Cup_bar__minus_01_dot_36_bar__plus_01_dot_65_bar__plus_00_dot_18)
        (isReceptacleObject Bowl_bar__plus_01_dot_29_bar__plus_00_dot_91_bar__plus_00_dot_71)
        (isReceptacleObject Mug_bar__plus_01_dot_03_bar__plus_00_dot_88_bar__plus_02_dot_80)
        (isReceptacleObject Plate_bar__minus_01_dot_33_bar__plus_01_dot_66_bar__minus_00_dot_13)
        (openable Fridge_bar__minus_01_dot_30_bar__plus_00_dot_01_bar__plus_00_dot_99)
        (openable Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__plus_00_dot_58)
        (openable Cabinet_bar__minus_00_dot_99_bar__plus_00_dot_40_bar__plus_00_dot_37)
        (openable Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__minus_00_dot_18)
        (openable Microwave_bar__minus_01_dot_34_bar__plus_00_dot_90_bar__plus_00_dot_05)
        (openable Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__plus_01_dot_23)
        (openable Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_06_bar__minus_00_dot_28)
        (openable Cabinet_bar__plus_00_dot_34_bar__plus_00_dot_40_bar__minus_00_dot_35)
        (openable Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_06_bar__plus_00_dot_38)
        (openable Cabinet_bar__minus_00_dot_37_bar__plus_00_dot_40_bar__minus_00_dot_35)
        (openable Cabinet_bar__plus_00_dot_59_bar__plus_02_dot_31_bar__minus_00_dot_65)
        
        (atLocation agent1 loc_bar_3_bar_13_bar_2_bar_30)
        
        (cleanable ButterKnife_bar__minus_01_dot_14_bar__plus_00_dot_91_bar__minus_00_dot_82)
        (cleanable Spatula_bar__plus_01_dot_37_bar__plus_00_dot_93_bar__minus_00_dot_14)
        (cleanable Spoon_bar__plus_01_dot_37_bar__plus_00_dot_89_bar__plus_02_dot_48)
        (cleanable ButterKnife_bar__plus_01_dot_13_bar__plus_00_dot_91_bar__plus_00_dot_14)
        (cleanable Pan_bar__minus_01_dot_33_bar__plus_00_dot_90_bar__minus_00_dot_70)
        (cleanable ButterKnife_bar__plus_01_dot_12_bar__plus_00_dot_89_bar__plus_02_dot_37)
        (cleanable Egg_bar__minus_00_dot_08_bar__plus_00_dot_79_bar__minus_00_dot_49)
        (cleanable Pot_bar__plus_00_dot_50_bar__plus_00_dot_94_bar__minus_00_dot_74)
        (cleanable Knife_bar__plus_01_dot_20_bar__plus_00_dot_91_bar__plus_02_dot_69)
        (cleanable Pot_bar__plus_00_dot_80_bar__plus_00_dot_94_bar__minus_00_dot_74)
        (cleanable Cup_bar__minus_01_dot_36_bar__plus_01_dot_65_bar__plus_00_dot_18)
        (cleanable Egg_bar__plus_01_dot_52_bar__plus_00_dot_95_bar__plus_00_dot_42)
        (cleanable Potato_bar__plus_00_dot_95_bar__plus_00_dot_93_bar__plus_02_dot_26)
        (cleanable Spoon_bar__plus_00_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_65)
        (cleanable Tomato_bar__plus_01_dot_13_bar__plus_00_dot_96_bar__minus_00_dot_14)
        (cleanable Lettuce_bar__minus_01_dot_29_bar__plus_00_dot_82_bar__plus_02_dot_64)
        (cleanable Kettle_bar__minus_01_dot_36_bar__plus_00_dot_74_bar__plus_03_dot_02)
        (cleanable Fork_bar__plus_01_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_14)
        (cleanable Bowl_bar__plus_01_dot_29_bar__plus_00_dot_91_bar__plus_00_dot_71)
        (cleanable Spoon_bar__plus_01_dot_16_bar__plus_00_dot_78_bar__plus_00_dot_33)
        (cleanable Potato_bar__minus_01_dot_26_bar__plus_01_dot_33_bar__plus_00_dot_99)
        (cleanable Mug_bar__plus_01_dot_03_bar__plus_00_dot_88_bar__plus_02_dot_80)
        (cleanable Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_82_bar__plus_00_dot_88)
        (cleanable Tomato_bar__minus_01_dot_29_bar__plus_00_dot_96_bar__minus_00_dot_48)
        (cleanable Knife_bar__minus_01_dot_20_bar__plus_00_dot_77_bar__plus_01_dot_90)
        (cleanable Apple_bar__minus_00_dot_14_bar__plus_00_dot_82_bar__minus_00_dot_61)
        (cleanable DishSponge_bar__plus_01_dot_12_bar__plus_00_dot_89_bar__plus_02_dot_80)
        (cleanable Plate_bar__minus_01_dot_33_bar__plus_01_dot_66_bar__minus_00_dot_13)
        (cleanable Tomato_bar__minus_01_dot_01_bar__plus_00_dot_80_bar__plus_02_dot_64)
        (cleanable Potato_bar__plus_01_dot_45_bar__plus_00_dot_93_bar__plus_02_dot_15)
        (cleanable Spatula_bar__minus_00_dot_97_bar__plus_00_dot_93_bar__minus_00_dot_43)
        (cleanable Egg_bar__minus_01_dot_44_bar__plus_01_dot_67_bar__plus_00_dot_88)
        (cleanable Fork_bar__plus_01_dot_28_bar__plus_00_dot_89_bar__plus_02_dot_26)
        (cleanable Spatula_bar__minus_01_dot_20_bar__plus_00_dot_76_bar__plus_02_dot_64)
        (cleanable Knife_bar__plus_01_dot_45_bar__plus_00_dot_91_bar__plus_02_dot_69)
        
        (heatable Egg_bar__minus_00_dot_08_bar__plus_00_dot_79_bar__minus_00_dot_49)
        (heatable Cup_bar__minus_01_dot_36_bar__plus_01_dot_65_bar__plus_00_dot_18)
        (heatable Egg_bar__plus_01_dot_52_bar__plus_00_dot_95_bar__plus_00_dot_42)
        (heatable Potato_bar__plus_00_dot_95_bar__plus_00_dot_93_bar__plus_02_dot_26)
        (heatable Tomato_bar__plus_01_dot_13_bar__plus_00_dot_96_bar__minus_00_dot_14)
        (heatable Bread_bar__minus_01_dot_26_bar__plus_01_dot_70_bar__plus_01_dot_09)
        (heatable Potato_bar__minus_01_dot_26_bar__plus_01_dot_33_bar__plus_00_dot_99)
        (heatable Mug_bar__plus_01_dot_03_bar__plus_00_dot_88_bar__plus_02_dot_80)
        (heatable Tomato_bar__minus_01_dot_29_bar__plus_00_dot_96_bar__minus_00_dot_48)
        (heatable Apple_bar__minus_00_dot_14_bar__plus_00_dot_82_bar__minus_00_dot_61)
        (heatable Plate_bar__minus_01_dot_33_bar__plus_01_dot_66_bar__minus_00_dot_13)
        (heatable Tomato_bar__minus_01_dot_01_bar__plus_00_dot_80_bar__plus_02_dot_64)
        (heatable Potato_bar__plus_01_dot_45_bar__plus_00_dot_93_bar__plus_02_dot_15)
        (heatable Egg_bar__minus_01_dot_44_bar__plus_01_dot_67_bar__plus_00_dot_88)
        (heatable Bread_bar__minus_01_dot_01_bar__plus_00_dot_81_bar__plus_01_dot_90)
        (coolable Pan_bar__minus_01_dot_33_bar__plus_00_dot_90_bar__minus_00_dot_70)
        (coolable Egg_bar__minus_00_dot_08_bar__plus_00_dot_79_bar__minus_00_dot_49)
        (coolable Pot_bar__plus_00_dot_50_bar__plus_00_dot_94_bar__minus_00_dot_74)
        (coolable Pot_bar__plus_00_dot_80_bar__plus_00_dot_94_bar__minus_00_dot_74)
        (coolable Cup_bar__minus_01_dot_36_bar__plus_01_dot_65_bar__plus_00_dot_18)
        (coolable Egg_bar__plus_01_dot_52_bar__plus_00_dot_95_bar__plus_00_dot_42)
        (coolable Potato_bar__plus_00_dot_95_bar__plus_00_dot_93_bar__plus_02_dot_26)
        (coolable Tomato_bar__plus_01_dot_13_bar__plus_00_dot_96_bar__minus_00_dot_14)
        (coolable Lettuce_bar__minus_01_dot_29_bar__plus_00_dot_82_bar__plus_02_dot_64)
        (coolable Bread_bar__minus_01_dot_26_bar__plus_01_dot_70_bar__plus_01_dot_09)
        (coolable Bowl_bar__plus_01_dot_29_bar__plus_00_dot_91_bar__plus_00_dot_71)
        (coolable Potato_bar__minus_01_dot_26_bar__plus_01_dot_33_bar__plus_00_dot_99)
        (coolable Mug_bar__plus_01_dot_03_bar__plus_00_dot_88_bar__plus_02_dot_80)
        (coolable Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_82_bar__plus_00_dot_88)
        (coolable Tomato_bar__minus_01_dot_29_bar__plus_00_dot_96_bar__minus_00_dot_48)
        (coolable WineBottle_bar__minus_01_dot_51_bar__plus_00_dot_91_bar__minus_00_dot_65)
        (coolable Apple_bar__minus_00_dot_14_bar__plus_00_dot_82_bar__minus_00_dot_61)
        (coolable Plate_bar__minus_01_dot_33_bar__plus_01_dot_66_bar__minus_00_dot_13)
        (coolable Tomato_bar__minus_01_dot_01_bar__plus_00_dot_80_bar__plus_02_dot_64)
        (coolable Potato_bar__plus_01_dot_45_bar__plus_00_dot_93_bar__plus_02_dot_15)
        (coolable Egg_bar__minus_01_dot_44_bar__plus_01_dot_67_bar__plus_00_dot_88)
        (coolable Bread_bar__minus_01_dot_01_bar__plus_00_dot_81_bar__plus_01_dot_90)
        
        (isCool Bread_bar__minus_01_dot_26_bar__plus_01_dot_70_bar__plus_01_dot_09)
        
        
        
        (sliceable Egg_bar__minus_00_dot_08_bar__plus_00_dot_79_bar__minus_00_dot_49)
        (sliceable Egg_bar__plus_01_dot_52_bar__plus_00_dot_95_bar__plus_00_dot_42)
        (sliceable Potato_bar__plus_00_dot_95_bar__plus_00_dot_93_bar__plus_02_dot_26)
        (sliceable Tomato_bar__plus_01_dot_13_bar__plus_00_dot_96_bar__minus_00_dot_14)
        (sliceable Lettuce_bar__minus_01_dot_29_bar__plus_00_dot_82_bar__plus_02_dot_64)
        (sliceable Bread_bar__minus_01_dot_26_bar__plus_01_dot_70_bar__plus_01_dot_09)
        (sliceable Potato_bar__minus_01_dot_26_bar__plus_01_dot_33_bar__plus_00_dot_99)
        (sliceable Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_82_bar__plus_00_dot_88)
        (sliceable Tomato_bar__minus_01_dot_29_bar__plus_00_dot_96_bar__minus_00_dot_48)
        (sliceable Apple_bar__minus_00_dot_14_bar__plus_00_dot_82_bar__minus_00_dot_61)
        (sliceable Tomato_bar__minus_01_dot_01_bar__plus_00_dot_80_bar__plus_02_dot_64)
        (sliceable Potato_bar__plus_01_dot_45_bar__plus_00_dot_93_bar__plus_02_dot_15)
        (sliceable Egg_bar__minus_01_dot_44_bar__plus_01_dot_67_bar__plus_00_dot_88)
        (sliceable Bread_bar__minus_01_dot_01_bar__plus_00_dot_81_bar__plus_01_dot_90)
        
        (inReceptacle Plate_bar__minus_01_dot_33_bar__plus_01_dot_66_bar__minus_00_dot_13 Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_06_bar__minus_00_dot_28)
        (inReceptacle Cup_bar__minus_01_dot_36_bar__plus_01_dot_65_bar__plus_00_dot_18 Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_06_bar__plus_00_dot_38)
        (inReceptacle SoapBottle_bar__plus_01_dot_22_bar__plus_00_dot_12_bar__plus_00_dot_73 Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__plus_00_dot_58)
        (inReceptacle Glassbottle_bar__plus_01_dot_09_bar__plus_00_dot_12_bar__plus_01_dot_16 Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__plus_01_dot_23)
        (inReceptacle Spatula_bar__plus_01_dot_37_bar__plus_00_dot_93_bar__minus_00_dot_14 CounterTop_bar__plus_01_dot_28_bar__plus_00_dot_95_bar__plus_00_dot_07)
        (inReceptacle Fork_bar__plus_01_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_14 CounterTop_bar__plus_01_dot_28_bar__plus_00_dot_95_bar__plus_00_dot_07)
        (inReceptacle CellPhone_bar__plus_01_dot_13_bar__plus_00_dot_91_bar__minus_00_dot_43 CounterTop_bar__plus_01_dot_28_bar__plus_00_dot_95_bar__plus_00_dot_07)
        (inReceptacle Bowl_bar__plus_01_dot_29_bar__plus_00_dot_91_bar__plus_00_dot_71 CounterTop_bar__plus_01_dot_28_bar__plus_00_dot_95_bar__plus_00_dot_07)
        (inReceptacle SaltShaker_bar__plus_01_dot_44_bar__plus_00_dot_91_bar__minus_00_dot_43 CounterTop_bar__plus_01_dot_28_bar__plus_00_dot_95_bar__plus_00_dot_07)
        (inReceptacle ButterKnife_bar__plus_01_dot_13_bar__plus_00_dot_91_bar__plus_00_dot_14 CounterTop_bar__plus_01_dot_28_bar__plus_00_dot_95_bar__plus_00_dot_07)
        (inReceptacle Egg_bar__plus_01_dot_52_bar__plus_00_dot_95_bar__plus_00_dot_42 CounterTop_bar__plus_01_dot_28_bar__plus_00_dot_95_bar__plus_00_dot_07)
        (inReceptacle Tomato_bar__plus_01_dot_13_bar__plus_00_dot_96_bar__minus_00_dot_14 CounterTop_bar__plus_01_dot_28_bar__plus_00_dot_95_bar__plus_00_dot_07)
        (inReceptacle Knife_bar__plus_01_dot_20_bar__plus_00_dot_91_bar__plus_02_dot_69 DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48)
        (inReceptacle DishSponge_bar__plus_01_dot_12_bar__plus_00_dot_89_bar__plus_02_dot_80 DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48)
        (inReceptacle Spoon_bar__plus_01_dot_37_bar__plus_00_dot_89_bar__plus_02_dot_48 DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48)
        (inReceptacle CellPhone_bar__plus_01_dot_28_bar__plus_00_dot_88_bar__plus_02_dot_69 DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48)
        (inReceptacle SprayBottle_bar__plus_01_dot_28_bar__plus_00_dot_89_bar__plus_02_dot_37 DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48)
        (inReceptacle SoapBottle_bar__plus_00_dot_95_bar__plus_00_dot_89_bar__plus_02_dot_15 DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48)
        (inReceptacle Potato_bar__plus_01_dot_45_bar__plus_00_dot_93_bar__plus_02_dot_15 DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48)
        (inReceptacle Mug_bar__plus_01_dot_03_bar__plus_00_dot_88_bar__plus_02_dot_80 DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48)
        (inReceptacle Fork_bar__plus_01_dot_28_bar__plus_00_dot_89_bar__plus_02_dot_26 DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48)
        (inReceptacle ButterKnife_bar__plus_01_dot_12_bar__plus_00_dot_89_bar__plus_02_dot_37 DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48)
        (inReceptacle Potato_bar__plus_00_dot_95_bar__plus_00_dot_93_bar__plus_02_dot_26 DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48)
        (inReceptacle Knife_bar__plus_01_dot_45_bar__plus_00_dot_91_bar__plus_02_dot_69 DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48)
        (inReceptacle ButterKnife_bar__minus_01_dot_14_bar__plus_00_dot_91_bar__minus_00_dot_82 CounterTop_bar__minus_01_dot_00_bar__plus_00_dot_95_bar__minus_00_dot_67)
        (inReceptacle Pan_bar__minus_01_dot_33_bar__plus_00_dot_90_bar__minus_00_dot_70 CounterTop_bar__minus_01_dot_00_bar__plus_00_dot_95_bar__minus_00_dot_67)
        (inReceptacle Spatula_bar__minus_00_dot_97_bar__plus_00_dot_93_bar__minus_00_dot_43 CounterTop_bar__minus_01_dot_00_bar__plus_00_dot_95_bar__minus_00_dot_67)
        (inReceptacle Tomato_bar__minus_01_dot_29_bar__plus_00_dot_96_bar__minus_00_dot_48 CounterTop_bar__minus_01_dot_00_bar__plus_00_dot_95_bar__minus_00_dot_67)
        (inReceptacle WineBottle_bar__minus_01_dot_51_bar__plus_00_dot_91_bar__minus_00_dot_65 CounterTop_bar__minus_01_dot_00_bar__plus_00_dot_95_bar__minus_00_dot_67)
        (inReceptacle Pot_bar__plus_00_dot_50_bar__plus_00_dot_94_bar__minus_00_dot_74 StoveBurner_bar__plus_00_dot_50_bar__plus_00_dot_93_bar__minus_00_dot_74)
        (inReceptacle Pot_bar__plus_00_dot_80_bar__plus_00_dot_94_bar__minus_00_dot_74 StoveBurner_bar__plus_00_dot_80_bar__plus_00_dot_93_bar__minus_00_dot_74)
        (inReceptacle Spoon_bar__plus_01_dot_16_bar__plus_00_dot_78_bar__plus_00_dot_33 Drawer_bar__plus_01_dot_12_bar__plus_00_dot_79_bar__plus_00_dot_37)
        (inReceptacle Lettuce_bar__minus_01_dot_29_bar__plus_00_dot_82_bar__plus_02_dot_64 DiningTable_bar__minus_01_dot_21_bar__plus_00_dot_78_bar__plus_02_dot_46)
        (inReceptacle Kettle_bar__minus_01_dot_36_bar__plus_00_dot_74_bar__plus_03_dot_02 DiningTable_bar__minus_01_dot_21_bar__plus_00_dot_78_bar__plus_02_dot_46)
        (inReceptacle SprayBottle_bar__minus_01_dot_01_bar__plus_00_dot_75_bar__plus_02_dot_46 DiningTable_bar__minus_01_dot_21_bar__plus_00_dot_78_bar__plus_02_dot_46)
        (inReceptacle Tomato_bar__minus_01_dot_01_bar__plus_00_dot_80_bar__plus_02_dot_64 DiningTable_bar__minus_01_dot_21_bar__plus_00_dot_78_bar__plus_02_dot_46)
        (inReceptacle CellPhone_bar__minus_01_dot_48_bar__plus_00_dot_75_bar__plus_02_dot_83 DiningTable_bar__minus_01_dot_21_bar__plus_00_dot_78_bar__plus_02_dot_46)
        (inReceptacle Glassbottle_bar__minus_01_dot_11_bar__plus_00_dot_75_bar__plus_02_dot_46 DiningTable_bar__minus_01_dot_21_bar__plus_00_dot_78_bar__plus_02_dot_46)
        (inReceptacle PepperShaker_bar__minus_01_dot_48_bar__plus_00_dot_75_bar__plus_02_dot_64 DiningTable_bar__minus_01_dot_21_bar__plus_00_dot_78_bar__plus_02_dot_46)
        (inReceptacle Knife_bar__minus_01_dot_20_bar__plus_00_dot_77_bar__plus_01_dot_90 DiningTable_bar__minus_01_dot_21_bar__plus_00_dot_78_bar__plus_02_dot_46)
        (inReceptacle Spatula_bar__minus_01_dot_20_bar__plus_00_dot_76_bar__plus_02_dot_64 DiningTable_bar__minus_01_dot_21_bar__plus_00_dot_78_bar__plus_02_dot_46)
        (inReceptacle Bread_bar__minus_01_dot_01_bar__plus_00_dot_81_bar__plus_01_dot_90 DiningTable_bar__minus_01_dot_21_bar__plus_00_dot_78_bar__plus_02_dot_46)
        (inReceptacle Bread_bar__minus_01_dot_26_bar__plus_01_dot_70_bar__plus_01_dot_09 Fridge_bar__minus_01_dot_30_bar__plus_00_dot_01_bar__plus_00_dot_99)
        (inReceptacle Egg_bar__minus_01_dot_44_bar__plus_01_dot_67_bar__plus_00_dot_88 Fridge_bar__minus_01_dot_30_bar__plus_00_dot_01_bar__plus_00_dot_99)
        (inReceptacle Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_82_bar__plus_00_dot_88 Fridge_bar__minus_01_dot_30_bar__plus_00_dot_01_bar__plus_00_dot_99)
        (inReceptacle Potato_bar__minus_01_dot_26_bar__plus_01_dot_33_bar__plus_00_dot_99 Fridge_bar__minus_01_dot_30_bar__plus_00_dot_01_bar__plus_00_dot_99)
        (inReceptacle Egg_bar__minus_00_dot_08_bar__plus_00_dot_79_bar__minus_00_dot_49 Sink_bar__minus_00_dot_01_bar__plus_00_dot_91_bar__minus_00_dot_62_bar_SinkBasin)
        (inReceptacle Spoon_bar__plus_00_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_65 Sink_bar__minus_00_dot_01_bar__plus_00_dot_91_bar__minus_00_dot_62_bar_SinkBasin)
        (inReceptacle Apple_bar__minus_00_dot_14_bar__plus_00_dot_82_bar__minus_00_dot_61 Sink_bar__minus_00_dot_01_bar__plus_00_dot_91_bar__minus_00_dot_62_bar_SinkBasin)
        
        
        (receptacleAtLocation Cabinet_bar__plus_00_dot_34_bar__plus_00_dot_40_bar__minus_00_dot_35 loc_bar__minus_1_bar_2_bar_2_bar_60)
        (receptacleAtLocation Cabinet_bar__plus_00_dot_59_bar__plus_02_dot_31_bar__minus_00_dot_65 loc_bar_2_bar_0_bar_2_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__plus_00_dot_53 loc_bar_3_bar_2_bar_1_bar_60)
        (receptacleAtLocation Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__plus_00_dot_58 loc_bar_0_bar_2_bar_1_bar_45)
        (receptacleAtLocation Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__plus_01_dot_23 loc_bar_0_bar_6_bar_1_bar_45)
        (receptacleAtLocation Cabinet_bar__plus_00_dot_99_bar__plus_00_dot_40_bar__minus_00_dot_18 loc_bar_1_bar_2_bar_1_bar_60)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_19_bar__plus_02_dot_06_bar__plus_00_dot_38 loc_bar_3_bar_2_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_19_bar__plus_02_dot_06_bar__minus_00_dot_64 loc_bar_2_bar_0_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_19_bar__plus_02_dot_31_bar__plus_00_dot_43 loc_bar_3_bar_2_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_19_bar__plus_02_dot_31_bar__plus_01_dot_18 loc_bar_3_bar_5_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_37_bar__plus_00_dot_40_bar__minus_00_dot_35 loc_bar_1_bar_2_bar_2_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_57_bar__plus_02_dot_06_bar__minus_00_dot_65 loc_bar__minus_2_bar_0_bar_2_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_99_bar__plus_00_dot_40_bar__plus_00_dot_37 loc_bar_0_bar_3_bar_3_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_99_bar__plus_00_dot_40_bar__minus_00_dot_34 loc_bar__minus_2_bar_0_bar_3_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_10_bar__plus_02_dot_06_bar__minus_00_dot_65 loc_bar__minus_2_bar_0_bar_2_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_06_bar__plus_00_dot_38 loc_bar__minus_2_bar_0_bar_3_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_06_bar__minus_00_dot_28 loc_bar__minus_1_bar_1_bar_3_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_06_bar__minus_00_dot_33 loc_bar__minus_2_bar_0_bar_3_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_31_bar__plus_00_dot_43 loc_bar__minus_1_bar_2_bar_3_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_19_bar__plus_02_dot_31_bar__plus_01_dot_38 loc_bar__minus_1_bar_5_bar_3_bar__minus_30)
        (receptacleAtLocation CoffeeMachine_bar__plus_01_dot_31_bar__plus_00_dot_90_bar__minus_00_dot_74 loc_bar_2_bar_0_bar_1_bar_45)
        (receptacleAtLocation CounterTop_bar__plus_01_dot_28_bar__plus_00_dot_95_bar__plus_00_dot_07 loc_bar_2_bar_1_bar_1_bar_45)
        (receptacleAtLocation CounterTop_bar__minus_01_dot_00_bar__plus_00_dot_95_bar__minus_00_dot_67 loc_bar__minus_2_bar_0_bar_2_bar_45)
        (receptacleAtLocation DiningTable_bar__plus_01_dot_19_bar__plus_00_dot_92_bar__plus_02_dot_48 loc_bar_2_bar_10_bar_1_bar_45)
        (receptacleAtLocation DiningTable_bar__minus_01_dot_21_bar__plus_00_dot_78_bar__plus_02_dot_46 loc_bar_0_bar_10_bar_3_bar_30)
        (receptacleAtLocation Drawer_bar__plus_01_dot_12_bar__plus_00_dot_79_bar__plus_00_dot_37 loc_bar_3_bar_1_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_01_dot_12_bar__plus_00_dot_79_bar__plus_00_dot_73 loc_bar_3_bar_2_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_01_dot_12_bar__plus_00_dot_79_bar__plus_01_dot_08 loc_bar_3_bar_5_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_01_dot_12_bar__plus_00_dot_79_bar__minus_00_dot_02 loc_bar_2_bar_0_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_12_bar__plus_00_dot_79_bar__plus_00_dot_21 loc_bar__minus_2_bar_0_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_12_bar__plus_00_dot_79_bar__minus_00_dot_17 loc_bar__minus_2_bar_0_bar_3_bar_60)
        (receptacleAtLocation Fridge_bar__minus_01_dot_30_bar__plus_00_dot_01_bar__plus_00_dot_99 loc_bar__minus_1_bar_4_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_01_dot_30_bar__plus_00_dot_01_bar__plus_01_dot_47 loc_bar_1_bar_6_bar_1_bar_60)
        (receptacleAtLocation Microwave_bar__minus_01_dot_34_bar__plus_00_dot_90_bar__plus_00_dot_05 loc_bar__minus_2_bar_0_bar_3_bar_45)
        (receptacleAtLocation Sink_bar__minus_00_dot_01_bar__plus_00_dot_91_bar__minus_00_dot_62_bar_SinkBasin loc_bar_1_bar_0_bar_2_bar_60)
        (receptacleAtLocation StoveBurner_bar__plus_00_dot_50_bar__plus_00_dot_93_bar__minus_00_dot_47 loc_bar_2_bar_0_bar_2_bar_60)
        (receptacleAtLocation StoveBurner_bar__plus_00_dot_50_bar__plus_00_dot_93_bar__minus_00_dot_74 loc_bar_2_bar_0_bar_2_bar_45)
        (receptacleAtLocation StoveBurner_bar__plus_00_dot_80_bar__plus_00_dot_93_bar__minus_00_dot_47 loc_bar_2_bar_0_bar_2_bar_60)
        (receptacleAtLocation StoveBurner_bar__plus_00_dot_80_bar__plus_00_dot_93_bar__minus_00_dot_74 loc_bar_2_bar_0_bar_2_bar_45)
        (receptacleAtLocation Toaster_bar__plus_01_dot_36_bar__plus_00_dot_90_bar__plus_01_dot_05 loc_bar_3_bar_5_bar_1_bar_45)
        (objectAtLocation SprayBottle_bar__minus_01_dot_01_bar__plus_00_dot_75_bar__plus_02_dot_46 loc_bar_0_bar_10_bar_3_bar_30)
        (objectAtLocation Spoon_bar__plus_00_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_65 loc_bar_1_bar_0_bar_2_bar_60)
        (objectAtLocation ButterKnife_bar__minus_01_dot_14_bar__plus_00_dot_91_bar__minus_00_dot_82 loc_bar__minus_2_bar_0_bar_2_bar_45)
        (objectAtLocation Potato_bar__plus_00_dot_95_bar__plus_00_dot_93_bar__plus_02_dot_26 loc_bar_2_bar_10_bar_1_bar_45)
        (objectAtLocation Egg_bar__minus_01_dot_44_bar__plus_01_dot_67_bar__plus_00_dot_88 loc_bar__minus_1_bar_4_bar_3_bar_60)
        (objectAtLocation Knife_bar__plus_01_dot_45_bar__plus_00_dot_91_bar__plus_02_dot_69 loc_bar_2_bar_10_bar_1_bar_45)
        (objectAtLocation Spatula_bar__minus_01_dot_20_bar__plus_00_dot_76_bar__plus_02_dot_64 loc_bar_0_bar_10_bar_3_bar_30)
        (objectAtLocation SoapBottle_bar__plus_00_dot_95_bar__plus_00_dot_89_bar__plus_02_dot_15 loc_bar_2_bar_10_bar_1_bar_45)
        (objectAtLocation Pot_bar__plus_00_dot_80_bar__plus_00_dot_94_bar__minus_00_dot_74 loc_bar_2_bar_0_bar_2_bar_45)
        (objectAtLocation Lettuce_bar__minus_01_dot_32_bar__plus_00_dot_82_bar__plus_00_dot_88 loc_bar__minus_1_bar_4_bar_3_bar_60)
        (objectAtLocation CellPhone_bar__plus_01_dot_28_bar__plus_00_dot_88_bar__plus_02_dot_69 loc_bar_2_bar_10_bar_1_bar_45)
        (objectAtLocation Tomato_bar__plus_01_dot_13_bar__plus_00_dot_96_bar__minus_00_dot_14 loc_bar_2_bar_1_bar_1_bar_45)
        (objectAtLocation Fork_bar__plus_01_dot_28_bar__plus_00_dot_89_bar__plus_02_dot_26 loc_bar_2_bar_10_bar_1_bar_45)
        (objectAtLocation Glassbottle_bar__minus_01_dot_11_bar__plus_00_dot_75_bar__plus_02_dot_46 loc_bar_0_bar_10_bar_3_bar_30)
        (objectAtLocation Bread_bar__minus_01_dot_01_bar__plus_00_dot_81_bar__plus_01_dot_90 loc_bar_0_bar_10_bar_3_bar_30)
        (objectAtLocation Tomato_bar__minus_01_dot_29_bar__plus_00_dot_96_bar__minus_00_dot_48 loc_bar__minus_2_bar_0_bar_2_bar_45)
        (objectAtLocation Knife_bar__minus_01_dot_20_bar__plus_00_dot_77_bar__plus_01_dot_90 loc_bar_0_bar_10_bar_3_bar_30)
        (objectAtLocation Egg_bar__minus_00_dot_08_bar__plus_00_dot_79_bar__minus_00_dot_49 loc_bar_1_bar_0_bar_2_bar_60)
        (objectAtLocation Spatula_bar__minus_00_dot_97_bar__plus_00_dot_93_bar__minus_00_dot_43 loc_bar__minus_2_bar_0_bar_2_bar_45)
        (objectAtLocation Spoon_bar__plus_01_dot_37_bar__plus_00_dot_89_bar__plus_02_dot_48 loc_bar_2_bar_10_bar_1_bar_45)
        (objectAtLocation ButterKnife_bar__plus_01_dot_12_bar__plus_00_dot_89_bar__plus_02_dot_37 loc_bar_2_bar_10_bar_1_bar_45)
        (objectAtLocation CellPhone_bar__plus_01_dot_13_bar__plus_00_dot_91_bar__minus_00_dot_43 loc_bar_2_bar_1_bar_1_bar_45)
        (objectAtLocation Potato_bar__plus_01_dot_45_bar__plus_00_dot_93_bar__plus_02_dot_15 loc_bar_2_bar_10_bar_1_bar_45)
        (objectAtLocation Chair_bar__plus_00_dot_98_bar__plus_00_dot_54_bar__plus_02_dot_52 loc_bar_2_bar_10_bar_1_bar_60)
        (objectAtLocation Sink_bar__minus_00_dot_01_bar__plus_00_dot_91_bar__minus_00_dot_62 loc_bar_0_bar_0_bar_2_bar_45)
        (objectAtLocation Bread_bar__minus_01_dot_26_bar__plus_01_dot_70_bar__plus_01_dot_09 loc_bar__minus_1_bar_4_bar_3_bar_60)
        (objectAtLocation StoveKnob_bar__plus_00_dot_55_bar__plus_01_dot_09_bar__minus_00_dot_91 loc_bar_2_bar_0_bar_2_bar_30)
        (objectAtLocation StoveKnob_bar__plus_00_dot_89_bar__plus_01_dot_09_bar__minus_00_dot_91 loc_bar_2_bar_0_bar_2_bar_30)
        (objectAtLocation StoveKnob_bar__plus_00_dot_43_bar__plus_01_dot_09_bar__minus_00_dot_91 loc_bar_2_bar_0_bar_2_bar_30)
        (objectAtLocation StoveKnob_bar__plus_00_dot_78_bar__plus_01_dot_09_bar__minus_00_dot_91 loc_bar_2_bar_0_bar_2_bar_30)
        (objectAtLocation Glassbottle_bar__plus_01_dot_09_bar__plus_00_dot_12_bar__plus_01_dot_16 loc_bar_0_bar_6_bar_1_bar_45)
        (objectAtLocation Fork_bar__plus_01_dot_37_bar__plus_00_dot_91_bar__plus_00_dot_14 loc_bar_2_bar_1_bar_1_bar_45)
        (objectAtLocation Kettle_bar__minus_01_dot_36_bar__plus_00_dot_74_bar__plus_03_dot_02 loc_bar_0_bar_10_bar_3_bar_30)
        (objectAtLocation Tomato_bar__minus_01_dot_01_bar__plus_00_dot_80_bar__plus_02_dot_64 loc_bar_0_bar_10_bar_3_bar_30)
        (objectAtLocation Plate_bar__minus_01_dot_33_bar__plus_01_dot_66_bar__minus_00_dot_13 loc_bar__minus_1_bar_1_bar_3_bar__minus_30)
        (objectAtLocation CellPhone_bar__minus_01_dot_48_bar__plus_00_dot_75_bar__plus_02_dot_83 loc_bar_0_bar_10_bar_3_bar_30)
        (objectAtLocation Lettuce_bar__minus_01_dot_29_bar__plus_00_dot_82_bar__plus_02_dot_64 loc_bar_0_bar_10_bar_3_bar_30)
        (objectAtLocation Pot_bar__plus_00_dot_50_bar__plus_00_dot_94_bar__minus_00_dot_74 loc_bar_2_bar_0_bar_2_bar_45)
        (objectAtLocation SoapBottle_bar__plus_01_dot_22_bar__plus_00_dot_12_bar__plus_00_dot_73 loc_bar_0_bar_2_bar_1_bar_45)
        (objectAtLocation Window_bar__minus_00_dot_01_bar__plus_01_dot_64_bar__minus_00_dot_97 loc_bar_0_bar_0_bar_2_bar_0)
        (objectAtLocation Window_bar__plus_01_dot_60_bar__plus_01_dot_65_bar__plus_02_dot_49 loc_bar_2_bar_10_bar_1_bar_0)
        (objectAtLocation WineBottle_bar__minus_01_dot_51_bar__plus_00_dot_91_bar__minus_00_dot_65 loc_bar__minus_2_bar_0_bar_2_bar_45)
        (objectAtLocation Spatula_bar__plus_01_dot_37_bar__plus_00_dot_93_bar__minus_00_dot_14 loc_bar_2_bar_1_bar_1_bar_45)
        (objectAtLocation Bowl_bar__plus_01_dot_29_bar__plus_00_dot_91_bar__plus_00_dot_71 loc_bar_2_bar_1_bar_1_bar_45)
        (objectAtLocation Apple_bar__minus_00_dot_14_bar__plus_00_dot_82_bar__minus_00_dot_61 loc_bar_1_bar_0_bar_2_bar_60)
        (objectAtLocation LightSwitch_bar__plus_00_dot_12_bar__plus_01_dot_35_bar__plus_03_dot_80 loc_bar_0_bar_13_bar_0_bar_30)
        (objectAtLocation Cup_bar__minus_01_dot_36_bar__plus_01_dot_65_bar__plus_00_dot_18 loc_bar__minus_2_bar_0_bar_3_bar__minus_30)
        (objectAtLocation Knife_bar__plus_01_dot_20_bar__plus_00_dot_91_bar__plus_02_dot_69 loc_bar_2_bar_10_bar_1_bar_45)
        (objectAtLocation Egg_bar__plus_01_dot_52_bar__plus_00_dot_95_bar__plus_00_dot_42 loc_bar_2_bar_1_bar_1_bar_45)
        (objectAtLocation SaltShaker_bar__plus_01_dot_44_bar__plus_00_dot_91_bar__minus_00_dot_43 loc_bar_2_bar_1_bar_1_bar_45)
        (objectAtLocation PepperShaker_bar__minus_01_dot_48_bar__plus_00_dot_75_bar__plus_02_dot_64 loc_bar_0_bar_10_bar_3_bar_30)
        (objectAtLocation Potato_bar__minus_01_dot_26_bar__plus_01_dot_33_bar__plus_00_dot_99 loc_bar__minus_1_bar_4_bar_3_bar_60)
        (objectAtLocation ButterKnife_bar__plus_01_dot_13_bar__plus_00_dot_91_bar__plus_00_dot_14 loc_bar_2_bar_1_bar_1_bar_45)
        (objectAtLocation DishSponge_bar__plus_01_dot_12_bar__plus_00_dot_89_bar__plus_02_dot_80 loc_bar_2_bar_10_bar_1_bar_45)
        (objectAtLocation Pan_bar__minus_01_dot_33_bar__plus_00_dot_90_bar__minus_00_dot_70 loc_bar__minus_2_bar_0_bar_2_bar_45)
        (objectAtLocation Spoon_bar__plus_01_dot_16_bar__plus_00_dot_78_bar__plus_00_dot_33 loc_bar_3_bar_1_bar_1_bar_60)
        (objectAtLocation SprayBottle_bar__plus_01_dot_28_bar__plus_00_dot_89_bar__plus_02_dot_37 loc_bar_2_bar_10_bar_1_bar_45)
        (objectAtLocation Mug_bar__plus_01_dot_03_bar__plus_00_dot_88_bar__plus_02_dot_80 loc_bar_2_bar_10_bar_1_bar_45)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o - object)
                                (and
                                    (sliceable ?o)
                                    (isSliced ?o)
                                    (heatable ?o)
                                    (objectType ?o BreadType)
                                    (receptacleType ?r GarbageCanType)
                                    (isHot ?o)
                                    (inReceptacle ?o ?r)
                                )
                            )
                        )
                    )
                )
            )
            