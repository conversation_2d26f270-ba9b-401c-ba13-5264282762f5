
(define (problem plan_trial_T20190908_121048_491026)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__plus_01_dot_73_bar__plus_00_dot_68_bar__plus_02_dot_26 - object
        AlarmClock_bar__minus_01_dot_67_bar__plus_01_dot_64_bar__minus_02_dot_07 - object
        AlarmClock_bar__minus_01_dot_80_bar__plus_00_dot_97_bar__minus_00_dot_34 - object
        Blinds_bar__minus_01_dot_06_bar__plus_02_dot_36_bar__minus_03_dot_17 - object
        Book_bar__plus_01_dot_50_bar__plus_00_dot_77_bar__minus_00_dot_70 - object
        Book_bar__minus_01_dot_66_bar__plus_00_dot_80_bar__plus_00_dot_26 - object
        Boots_bar__minus_01_dot_84_bar__plus_00_dot_00_bar__minus_01_dot_08 - object
        Bowl_bar__minus_01_dot_68_bar__plus_01_dot_14_bar__minus_02_dot_30 - object
        CD_bar__plus_01_dot_41_bar__plus_00_dot_37_bar__plus_02_dot_18 - object
        CD_bar__plus_02_dot_55_bar__plus_00_dot_37_bar__minus_01_dot_59 - object
        CD_bar__minus_01_dot_76_bar__plus_01_dot_64_bar__minus_02_dot_42 - object
        CellPhone_bar__plus_02_dot_14_bar__plus_00_dot_44_bar__plus_02_dot_10 - object
        CellPhone_bar__minus_01_dot_61_bar__plus_00_dot_80_bar__minus_00_dot_02 - object
        CreditCard_bar__plus_02_dot_21_bar__plus_00_dot_68_bar__minus_01_dot_67 - object
        HousePlant_bar__minus_01_dot_79_bar__plus_00_dot_97_bar__plus_00_dot_53 - object
        KeyChain_bar__plus_01_dot_65_bar__plus_00_dot_68_bar__plus_02_dot_13 - object
        Lamp_bar__plus_01_dot_40_bar__plus_00_dot_67_bar__plus_02_dot_21 - object
        Lamp_bar__plus_02_dot_60_bar__plus_00_dot_67_bar__minus_01_dot_71 - object
        Laptop_bar__plus_01_dot_50_bar__plus_00_dot_77_bar__minus_00_dot_11 - object
        LightSwitch_bar__minus_00_dot_16_bar__plus_01_dot_09_bar__plus_02_dot_45 - object
        Mirror_bar__minus_01_dot_95_bar__plus_01_dot_74_bar__minus_00_dot_21 - object
        Painting_bar__plus_00_dot_38_bar__plus_01_dot_98_bar__minus_01_dot_90 - object
        Painting_bar__plus_00_dot_88_bar__plus_01_dot_98_bar__minus_01_dot_90 - object
        Painting_bar__plus_01_dot_37_bar__plus_01_dot_98_bar__minus_01_dot_90 - object
        Pencil_bar__minus_01_dot_50_bar__plus_00_dot_42_bar__plus_00_dot_26 - object
        Pen_bar__minus_01_dot_66_bar__plus_00_dot_98_bar__plus_00_dot_46 - object
        Pen_bar__minus_01_dot_67_bar__plus_01_dot_15_bar__minus_02_dot_07 - object
        Pen_bar__minus_01_dot_67_bar__plus_01_dot_64_bar__minus_01_dot_96 - object
        Pillow_bar__plus_00_dot_78_bar__plus_00_dot_84_bar__minus_00_dot_99 - object
        Pillow_bar__plus_01_dot_41_bar__plus_00_dot_79_bar__minus_01_dot_51 - object
        Statue_bar__plus_02_dot_44_bar__plus_00_dot_69_bar__minus_01_dot_72 - object
        Vase_bar__minus_01_dot_65_bar__plus_01_dot_63_bar__minus_02_dot_32 - object
        Vase_bar__minus_01_dot_67_bar__plus_01_dot_15_bar__minus_01_dot_73 - object
        Window_bar__minus_01_dot_10_bar__plus_01_dot_57_bar__minus_03_dot_23 - object
        ArmChair_bar__plus_02_dot_41_bar__plus_00_dot_01_bar__plus_02_dot_00 - receptacle
        Bed_bar__plus_01_dot_02_bar__plus_00_dot_06_bar__minus_00_dot_73 - receptacle
        Drawer_bar__plus_01_dot_58_bar__plus_00_dot_17_bar__plus_02_dot_14 - receptacle
        Drawer_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__plus_02_dot_14 - receptacle
        Drawer_bar__plus_02_dot_44_bar__plus_00_dot_17_bar__minus_01_dot_60 - receptacle
        Drawer_bar__plus_02_dot_44_bar__plus_00_dot_46_bar__minus_01_dot_60 - receptacle
        Drawer_bar__minus_01_dot_59_bar__plus_00_dot_10_bar__plus_00_dot_26 - receptacle
        Drawer_bar__minus_01_dot_59_bar__plus_00_dot_10_bar__minus_00_dot_54 - receptacle
        Drawer_bar__minus_01_dot_59_bar__plus_00_dot_28_bar__plus_00_dot_26 - receptacle
        Drawer_bar__minus_01_dot_59_bar__plus_00_dot_28_bar__minus_00_dot_54 - receptacle
        Drawer_bar__minus_01_dot_59_bar__plus_00_dot_48_bar__plus_00_dot_26 - receptacle
        Drawer_bar__minus_01_dot_59_bar__plus_00_dot_48_bar__minus_00_dot_54 - receptacle
        Drawer_bar__minus_01_dot_59_bar__plus_00_dot_67_bar__plus_00_dot_26 - receptacle
        Drawer_bar__minus_01_dot_59_bar__plus_00_dot_67_bar__minus_00_dot_54 - receptacle
        Drawer_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__plus_00_dot_26 - receptacle
        Drawer_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__minus_00_dot_54 - receptacle
        Drawer_bar__minus_01_dot_72_bar__plus_00_dot_30_bar__minus_02_dot_07 - receptacle
        Drawer_bar__minus_01_dot_72_bar__plus_00_dot_80_bar__minus_02_dot_07 - receptacle
        Dresser_bar__minus_01_dot_75_bar__plus_00_dot_00_bar__minus_00_dot_15 - receptacle
        Dresser_bar__minus_01_dot_77_bar__minus_00_dot_01_bar__minus_02_dot_08 - receptacle
        GarbageCan_bar__minus_01_dot_80_bar__plus_00_dot_00_bar__plus_00_dot_86 - receptacle
        Shelf_bar__minus_01_dot_70_bar__plus_01_dot_14_bar__minus_02_dot_06 - receptacle
        Shelf_bar__minus_01_dot_70_bar__plus_01_dot_63_bar__minus_02_dot_06 - receptacle
        SideTable_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__plus_02_dot_14 - receptacle
        SideTable_bar__plus_02_dot_44_bar__plus_00_dot_46_bar__minus_01_dot_60 - receptacle
        loc_bar__minus_1_bar_2_bar_3_bar_45 - location
        loc_bar__minus_4_bar__minus_2_bar_3_bar_60 - location
        loc_bar__minus_2_bar_1_bar_3_bar_30 - location
        loc_bar__minus_2_bar_3_bar_2_bar_45 - location
        loc_bar__minus_4_bar__minus_8_bar_3_bar_0 - location
        loc_bar__minus_4_bar__minus_4_bar_3_bar_60 - location
        loc_bar__minus_1_bar__minus_1_bar_3_bar_45 - location
        loc_bar_0_bar_3_bar_3_bar_45 - location
        loc_bar_9_bar_3_bar_0_bar_60 - location
        loc_bar_2_bar_5_bar_1_bar_60 - location
        loc_bar__minus_3_bar_2_bar_2_bar_45 - location
        loc_bar__minus_4_bar__minus_8_bar_3_bar_30 - location
        loc_bar__minus_4_bar__minus_10_bar_2_bar__minus_30 - location
        loc_bar_9_bar__minus_2_bar_2_bar_60 - location
        loc_bar_9_bar__minus_3_bar_2_bar_60 - location
        loc_bar__minus_4_bar__minus_8_bar_3_bar_60 - location
        loc_bar__minus_2_bar__minus_2_bar_3_bar_30 - location
        loc_bar__minus_1_bar_7_bar_0_bar_45 - location
        loc_bar__minus_3_bar__minus_8_bar_1_bar__minus_15 - location
        loc_bar__minus_4_bar__minus_8_bar_3_bar_45 - location
        loc_bar_8_bar_5_bar_0_bar_60 - location
        loc_bar__minus_1_bar_0_bar_3_bar_45 - location
        loc_bar__minus_3_bar__minus_3_bar_0_bar_45 - location
        loc_bar__minus_4_bar__minus_10_bar_3_bar_0 - location
        loc_bar__minus_5_bar_4_bar_3_bar_60 - location
        loc_bar__minus_4_bar__minus_10_bar_2_bar_0 - location
        loc_bar_10_bar__minus_4_bar_2_bar_60 - location
        loc_bar_2_bar_7_bar_1_bar_45 - location
        loc_bar_4_bar_3_bar_2_bar_45 - location
        loc_bar_9_bar__minus_5_bar_3_bar__minus_15 - location
        loc_bar_7_bar_4_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType Drawer_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__minus_00_dot_54 DrawerType)
        (receptacleType Drawer_bar__minus_01_dot_59_bar__plus_00_dot_48_bar__minus_00_dot_54 DrawerType)
        (receptacleType GarbageCan_bar__minus_01_dot_80_bar__plus_00_dot_00_bar__plus_00_dot_86 GarbageCanType)
        (receptacleType Drawer_bar__minus_01_dot_59_bar__plus_00_dot_67_bar__plus_00_dot_26 DrawerType)
        (receptacleType Dresser_bar__minus_01_dot_77_bar__minus_00_dot_01_bar__minus_02_dot_08 DresserType)
        (receptacleType Drawer_bar__plus_01_dot_58_bar__plus_00_dot_17_bar__plus_02_dot_14 DrawerType)
        (receptacleType Drawer_bar__minus_01_dot_59_bar__plus_00_dot_67_bar__minus_00_dot_54 DrawerType)
        (receptacleType SideTable_bar__plus_02_dot_44_bar__plus_00_dot_46_bar__minus_01_dot_60 SideTableType)
        (receptacleType Drawer_bar__plus_02_dot_44_bar__plus_00_dot_17_bar__minus_01_dot_60 DrawerType)
        (receptacleType Drawer_bar__plus_02_dot_44_bar__plus_00_dot_46_bar__minus_01_dot_60 DrawerType)
        (receptacleType Drawer_bar__minus_01_dot_72_bar__plus_00_dot_30_bar__minus_02_dot_07 DrawerType)
        (receptacleType Shelf_bar__minus_01_dot_70_bar__plus_01_dot_63_bar__minus_02_dot_06 ShelfType)
        (receptacleType Drawer_bar__minus_01_dot_59_bar__plus_00_dot_28_bar__plus_00_dot_26 DrawerType)
        (receptacleType Drawer_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__plus_00_dot_26 DrawerType)
        (receptacleType Bed_bar__plus_01_dot_02_bar__plus_00_dot_06_bar__minus_00_dot_73 BedType)
        (receptacleType ArmChair_bar__plus_02_dot_41_bar__plus_00_dot_01_bar__plus_02_dot_00 ArmChairType)
        (receptacleType SideTable_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__plus_02_dot_14 SideTableType)
        (receptacleType Drawer_bar__minus_01_dot_59_bar__plus_00_dot_48_bar__plus_00_dot_26 DrawerType)
        (receptacleType Drawer_bar__minus_01_dot_59_bar__plus_00_dot_10_bar__minus_00_dot_54 DrawerType)
        (receptacleType Drawer_bar__minus_01_dot_72_bar__plus_00_dot_80_bar__minus_02_dot_07 DrawerType)
        (receptacleType Shelf_bar__minus_01_dot_70_bar__plus_01_dot_14_bar__minus_02_dot_06 ShelfType)
        (receptacleType Drawer_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__plus_02_dot_14 DrawerType)
        (receptacleType Drawer_bar__minus_01_dot_59_bar__plus_00_dot_10_bar__plus_00_dot_26 DrawerType)
        (receptacleType Drawer_bar__minus_01_dot_59_bar__plus_00_dot_28_bar__minus_00_dot_54 DrawerType)
        (receptacleType Dresser_bar__minus_01_dot_75_bar__plus_00_dot_00_bar__minus_00_dot_15 DresserType)
        (objectType Painting_bar__plus_01_dot_37_bar__plus_01_dot_98_bar__minus_01_dot_90 PaintingType)
        (objectType Book_bar__plus_01_dot_50_bar__plus_00_dot_77_bar__minus_00_dot_70 BookType)
        (objectType HousePlant_bar__minus_01_dot_79_bar__plus_00_dot_97_bar__plus_00_dot_53 HousePlantType)
        (objectType Window_bar__minus_01_dot_10_bar__plus_01_dot_57_bar__minus_03_dot_23 WindowType)
        (objectType Statue_bar__plus_02_dot_44_bar__plus_00_dot_69_bar__minus_01_dot_72 StatueType)
        (objectType CellPhone_bar__minus_01_dot_61_bar__plus_00_dot_80_bar__minus_00_dot_02 CellPhoneType)
        (objectType LightSwitch_bar__minus_00_dot_16_bar__plus_01_dot_09_bar__plus_02_dot_45 LightSwitchType)
        (objectType CD_bar__plus_01_dot_41_bar__plus_00_dot_37_bar__plus_02_dot_18 CDType)
        (objectType CD_bar__plus_02_dot_55_bar__plus_00_dot_37_bar__minus_01_dot_59 CDType)
        (objectType Book_bar__minus_01_dot_66_bar__plus_00_dot_80_bar__plus_00_dot_26 BookType)
        (objectType KeyChain_bar__plus_01_dot_65_bar__plus_00_dot_68_bar__plus_02_dot_13 KeyChainType)
        (objectType CreditCard_bar__plus_02_dot_21_bar__plus_00_dot_68_bar__minus_01_dot_67 CreditCardType)
        (objectType CD_bar__minus_01_dot_76_bar__plus_01_dot_64_bar__minus_02_dot_42 CDType)
        (objectType Pen_bar__minus_01_dot_67_bar__plus_01_dot_64_bar__minus_01_dot_96 PenType)
        (objectType AlarmClock_bar__plus_01_dot_73_bar__plus_00_dot_68_bar__plus_02_dot_26 AlarmClockType)
        (objectType Pen_bar__minus_01_dot_67_bar__plus_01_dot_15_bar__minus_02_dot_07 PenType)
        (objectType Vase_bar__minus_01_dot_67_bar__plus_01_dot_15_bar__minus_01_dot_73 VaseType)
        (objectType AlarmClock_bar__minus_01_dot_80_bar__plus_00_dot_97_bar__minus_00_dot_34 AlarmClockType)
        (objectType Pillow_bar__plus_01_dot_41_bar__plus_00_dot_79_bar__minus_01_dot_51 PillowType)
        (objectType CellPhone_bar__plus_02_dot_14_bar__plus_00_dot_44_bar__plus_02_dot_10 CellPhoneType)
        (objectType Painting_bar__plus_00_dot_38_bar__plus_01_dot_98_bar__minus_01_dot_90 PaintingType)
        (objectType Blinds_bar__minus_01_dot_06_bar__plus_02_dot_36_bar__minus_03_dot_17 BlindsType)
        (objectType Pillow_bar__plus_00_dot_78_bar__plus_00_dot_84_bar__minus_00_dot_99 PillowType)
        (objectType Boots_bar__minus_01_dot_84_bar__plus_00_dot_00_bar__minus_01_dot_08 BootsType)
        (objectType Vase_bar__minus_01_dot_65_bar__plus_01_dot_63_bar__minus_02_dot_32 VaseType)
        (objectType Painting_bar__plus_00_dot_88_bar__plus_01_dot_98_bar__minus_01_dot_90 PaintingType)
        (objectType Bowl_bar__minus_01_dot_68_bar__plus_01_dot_14_bar__minus_02_dot_30 BowlType)
        (objectType Mirror_bar__minus_01_dot_95_bar__plus_01_dot_74_bar__minus_00_dot_21 MirrorType)
        (objectType Pen_bar__minus_01_dot_66_bar__plus_00_dot_98_bar__plus_00_dot_46 PenType)
        (objectType Pencil_bar__minus_01_dot_50_bar__plus_00_dot_42_bar__plus_00_dot_26 PencilType)
        (objectType AlarmClock_bar__minus_01_dot_67_bar__plus_01_dot_64_bar__minus_02_dot_07 AlarmClockType)
        (objectType Laptop_bar__plus_01_dot_50_bar__plus_00_dot_77_bar__minus_00_dot_11 LaptopType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DresserType PenType)
        (canContain DresserType BookType)
        (canContain DresserType BowlType)
        (canContain DresserType VaseType)
        (canContain DresserType CDType)
        (canContain DresserType CellPhoneType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType LaptopType)
        (canContain DresserType PencilType)
        (canContain DresserType StatueType)
        (canContain DresserType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType BowlType)
        (canContain SideTableType VaseType)
        (canContain SideTableType CDType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType StatueType)
        (canContain SideTableType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType StatueType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType CellPhoneType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType BowlType)
        (canContain SideTableType VaseType)
        (canContain SideTableType CDType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType StatueType)
        (canContain SideTableType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType StatueType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DresserType PenType)
        (canContain DresserType BookType)
        (canContain DresserType BowlType)
        (canContain DresserType VaseType)
        (canContain DresserType CDType)
        (canContain DresserType CellPhoneType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType LaptopType)
        (canContain DresserType PencilType)
        (canContain DresserType StatueType)
        (canContain DresserType AlarmClockType)
        (pickupable Book_bar__plus_01_dot_50_bar__plus_00_dot_77_bar__minus_00_dot_70)
        (pickupable Statue_bar__plus_02_dot_44_bar__plus_00_dot_69_bar__minus_01_dot_72)
        (pickupable CellPhone_bar__minus_01_dot_61_bar__plus_00_dot_80_bar__minus_00_dot_02)
        (pickupable CD_bar__plus_01_dot_41_bar__plus_00_dot_37_bar__plus_02_dot_18)
        (pickupable CD_bar__plus_02_dot_55_bar__plus_00_dot_37_bar__minus_01_dot_59)
        (pickupable Book_bar__minus_01_dot_66_bar__plus_00_dot_80_bar__plus_00_dot_26)
        (pickupable KeyChain_bar__plus_01_dot_65_bar__plus_00_dot_68_bar__plus_02_dot_13)
        (pickupable CreditCard_bar__plus_02_dot_21_bar__plus_00_dot_68_bar__minus_01_dot_67)
        (pickupable CD_bar__minus_01_dot_76_bar__plus_01_dot_64_bar__minus_02_dot_42)
        (pickupable Pen_bar__minus_01_dot_67_bar__plus_01_dot_64_bar__minus_01_dot_96)
        (pickupable AlarmClock_bar__plus_01_dot_73_bar__plus_00_dot_68_bar__plus_02_dot_26)
        (pickupable Pen_bar__minus_01_dot_67_bar__plus_01_dot_15_bar__minus_02_dot_07)
        (pickupable Vase_bar__minus_01_dot_67_bar__plus_01_dot_15_bar__minus_01_dot_73)
        (pickupable AlarmClock_bar__minus_01_dot_80_bar__plus_00_dot_97_bar__minus_00_dot_34)
        (pickupable Pillow_bar__plus_01_dot_41_bar__plus_00_dot_79_bar__minus_01_dot_51)
        (pickupable CellPhone_bar__plus_02_dot_14_bar__plus_00_dot_44_bar__plus_02_dot_10)
        (pickupable Pillow_bar__plus_00_dot_78_bar__plus_00_dot_84_bar__minus_00_dot_99)
        (pickupable Boots_bar__minus_01_dot_84_bar__plus_00_dot_00_bar__minus_01_dot_08)
        (pickupable Vase_bar__minus_01_dot_65_bar__plus_01_dot_63_bar__minus_02_dot_32)
        (pickupable Bowl_bar__minus_01_dot_68_bar__plus_01_dot_14_bar__minus_02_dot_30)
        (pickupable Pen_bar__minus_01_dot_66_bar__plus_00_dot_98_bar__plus_00_dot_46)
        (pickupable Pencil_bar__minus_01_dot_50_bar__plus_00_dot_42_bar__plus_00_dot_26)
        (pickupable AlarmClock_bar__minus_01_dot_67_bar__plus_01_dot_64_bar__minus_02_dot_07)
        (pickupable Laptop_bar__plus_01_dot_50_bar__plus_00_dot_77_bar__minus_00_dot_11)
        (isReceptacleObject Bowl_bar__minus_01_dot_68_bar__plus_01_dot_14_bar__minus_02_dot_30)
        (openable Drawer_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__minus_00_dot_54)
        (openable Drawer_bar__minus_01_dot_59_bar__plus_00_dot_48_bar__minus_00_dot_54)
        (openable Drawer_bar__minus_01_dot_59_bar__plus_00_dot_67_bar__plus_00_dot_26)
        (openable Drawer_bar__plus_01_dot_58_bar__plus_00_dot_17_bar__plus_02_dot_14)
        (openable Drawer_bar__minus_01_dot_59_bar__plus_00_dot_67_bar__minus_00_dot_54)
        (openable Drawer_bar__plus_02_dot_44_bar__plus_00_dot_17_bar__minus_01_dot_60)
        (openable Drawer_bar__plus_02_dot_44_bar__plus_00_dot_46_bar__minus_01_dot_60)
        (openable Drawer_bar__minus_01_dot_59_bar__plus_00_dot_28_bar__plus_00_dot_26)
        (openable Drawer_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__plus_00_dot_26)
        (openable Drawer_bar__minus_01_dot_59_bar__plus_00_dot_48_bar__plus_00_dot_26)
        (openable Drawer_bar__minus_01_dot_59_bar__plus_00_dot_10_bar__minus_00_dot_54)
        (openable Drawer_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__plus_02_dot_14)
        (openable Drawer_bar__minus_01_dot_59_bar__plus_00_dot_10_bar__plus_00_dot_26)
        (openable Drawer_bar__minus_01_dot_59_bar__plus_00_dot_28_bar__minus_00_dot_54)
        
        (atLocation agent1 loc_bar_7_bar_4_bar_2_bar_30)
        
        (cleanable Bowl_bar__minus_01_dot_68_bar__plus_01_dot_14_bar__minus_02_dot_30)
        
        
        (coolable Bowl_bar__minus_01_dot_68_bar__plus_01_dot_14_bar__minus_02_dot_30)
        
        
        
        
        
        
        
        (inReceptacle KeyChain_bar__plus_01_dot_65_bar__plus_00_dot_68_bar__plus_02_dot_13 SideTable_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__plus_02_dot_14)
        (inReceptacle AlarmClock_bar__plus_01_dot_73_bar__plus_00_dot_68_bar__plus_02_dot_26 SideTable_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__plus_02_dot_14)
        (inReceptacle Pencil_bar__minus_01_dot_50_bar__plus_00_dot_42_bar__plus_00_dot_26 Drawer_bar__minus_01_dot_59_bar__plus_00_dot_48_bar__plus_00_dot_26)
        (inReceptacle Statue_bar__plus_02_dot_44_bar__plus_00_dot_69_bar__minus_01_dot_72 SideTable_bar__plus_02_dot_44_bar__plus_00_dot_46_bar__minus_01_dot_60)
        (inReceptacle CreditCard_bar__plus_02_dot_21_bar__plus_00_dot_68_bar__minus_01_dot_67 SideTable_bar__plus_02_dot_44_bar__plus_00_dot_46_bar__minus_01_dot_60)
        (inReceptacle CellPhone_bar__plus_02_dot_14_bar__plus_00_dot_44_bar__plus_02_dot_10 ArmChair_bar__plus_02_dot_41_bar__plus_00_dot_01_bar__plus_02_dot_00)
        (inReceptacle CD_bar__plus_02_dot_55_bar__plus_00_dot_37_bar__minus_01_dot_59 Drawer_bar__plus_02_dot_44_bar__plus_00_dot_46_bar__minus_01_dot_60)
        (inReceptacle Mirror_bar__minus_01_dot_95_bar__plus_01_dot_74_bar__minus_00_dot_21 Dresser_bar__minus_01_dot_75_bar__plus_00_dot_00_bar__minus_00_dot_15)
        (inReceptacle AlarmClock_bar__minus_01_dot_80_bar__plus_00_dot_97_bar__minus_00_dot_34 Dresser_bar__minus_01_dot_75_bar__plus_00_dot_00_bar__minus_00_dot_15)
        (inReceptacle Pen_bar__minus_01_dot_66_bar__plus_00_dot_98_bar__plus_00_dot_46 Dresser_bar__minus_01_dot_75_bar__plus_00_dot_00_bar__minus_00_dot_15)
        (inReceptacle HousePlant_bar__minus_01_dot_79_bar__plus_00_dot_97_bar__plus_00_dot_53 Dresser_bar__minus_01_dot_75_bar__plus_00_dot_00_bar__minus_00_dot_15)
        (inReceptacle CellPhone_bar__minus_01_dot_61_bar__plus_00_dot_80_bar__minus_00_dot_02 Drawer_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__plus_00_dot_26)
        (inReceptacle Book_bar__minus_01_dot_66_bar__plus_00_dot_80_bar__plus_00_dot_26 Drawer_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__plus_00_dot_26)
        (inReceptacle CD_bar__plus_01_dot_41_bar__plus_00_dot_37_bar__plus_02_dot_18 Drawer_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__plus_02_dot_14)
        (inReceptacle Laptop_bar__plus_01_dot_50_bar__plus_00_dot_77_bar__minus_00_dot_11 Bed_bar__plus_01_dot_02_bar__plus_00_dot_06_bar__minus_00_dot_73)
        (inReceptacle Pillow_bar__plus_00_dot_78_bar__plus_00_dot_84_bar__minus_00_dot_99 Bed_bar__plus_01_dot_02_bar__plus_00_dot_06_bar__minus_00_dot_73)
        (inReceptacle Pillow_bar__plus_01_dot_41_bar__plus_00_dot_79_bar__minus_01_dot_51 Bed_bar__plus_01_dot_02_bar__plus_00_dot_06_bar__minus_00_dot_73)
        (inReceptacle Book_bar__plus_01_dot_50_bar__plus_00_dot_77_bar__minus_00_dot_70 Bed_bar__plus_01_dot_02_bar__plus_00_dot_06_bar__minus_00_dot_73)
        (inReceptacle Vase_bar__minus_01_dot_67_bar__plus_01_dot_15_bar__minus_01_dot_73 Shelf_bar__minus_01_dot_70_bar__plus_01_dot_14_bar__minus_02_dot_06)
        (inReceptacle Pen_bar__minus_01_dot_67_bar__plus_01_dot_15_bar__minus_02_dot_07 Shelf_bar__minus_01_dot_70_bar__plus_01_dot_14_bar__minus_02_dot_06)
        (inReceptacle Bowl_bar__minus_01_dot_68_bar__plus_01_dot_14_bar__minus_02_dot_30 Shelf_bar__minus_01_dot_70_bar__plus_01_dot_14_bar__minus_02_dot_06)
        (inReceptacle Vase_bar__minus_01_dot_65_bar__plus_01_dot_63_bar__minus_02_dot_32 Shelf_bar__minus_01_dot_70_bar__plus_01_dot_63_bar__minus_02_dot_06)
        (inReceptacle Pen_bar__minus_01_dot_67_bar__plus_01_dot_64_bar__minus_01_dot_96 Shelf_bar__minus_01_dot_70_bar__plus_01_dot_63_bar__minus_02_dot_06)
        (inReceptacle AlarmClock_bar__minus_01_dot_67_bar__plus_01_dot_64_bar__minus_02_dot_07 Shelf_bar__minus_01_dot_70_bar__plus_01_dot_63_bar__minus_02_dot_06)
        
        
        (receptacleAtLocation ArmChair_bar__plus_02_dot_41_bar__plus_00_dot_01_bar__plus_02_dot_00 loc_bar_9_bar_3_bar_0_bar_60)
        (receptacleAtLocation Bed_bar__plus_01_dot_02_bar__plus_00_dot_06_bar__minus_00_dot_73 loc_bar_4_bar_3_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__plus_01_dot_58_bar__plus_00_dot_17_bar__plus_02_dot_14 loc_bar_2_bar_5_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__plus_02_dot_14 loc_bar_8_bar_5_bar_0_bar_60)
        (receptacleAtLocation Drawer_bar__plus_02_dot_44_bar__plus_00_dot_17_bar__minus_01_dot_60 loc_bar_9_bar__minus_2_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__plus_02_dot_44_bar__plus_00_dot_46_bar__minus_01_dot_60 loc_bar_9_bar__minus_3_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_59_bar__plus_00_dot_10_bar__plus_00_dot_26 loc_bar_0_bar_3_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_59_bar__plus_00_dot_10_bar__minus_00_dot_54 loc_bar__minus_2_bar_3_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_59_bar__plus_00_dot_28_bar__plus_00_dot_26 loc_bar__minus_1_bar__minus_1_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_59_bar__plus_00_dot_28_bar__minus_00_dot_54 loc_bar__minus_1_bar_0_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_59_bar__plus_00_dot_48_bar__plus_00_dot_26 loc_bar__minus_1_bar_2_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_59_bar__plus_00_dot_48_bar__minus_00_dot_54 loc_bar__minus_1_bar__minus_1_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_59_bar__plus_00_dot_67_bar__plus_00_dot_26 loc_bar__minus_3_bar__minus_3_bar_0_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_59_bar__plus_00_dot_67_bar__minus_00_dot_54 loc_bar__minus_3_bar_2_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__plus_00_dot_26 loc_bar__minus_2_bar_1_bar_3_bar_30)
        (receptacleAtLocation Drawer_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__minus_00_dot_54 loc_bar__minus_2_bar__minus_2_bar_3_bar_30)
        (receptacleAtLocation Drawer_bar__minus_01_dot_72_bar__plus_00_dot_30_bar__minus_02_dot_07 loc_bar__minus_4_bar__minus_8_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_72_bar__plus_00_dot_80_bar__minus_02_dot_07 loc_bar__minus_4_bar__minus_8_bar_3_bar_45)
        (receptacleAtLocation Dresser_bar__minus_01_dot_75_bar__plus_00_dot_00_bar__minus_00_dot_15 loc_bar__minus_4_bar__minus_2_bar_3_bar_60)
        (receptacleAtLocation Dresser_bar__minus_01_dot_77_bar__minus_00_dot_01_bar__minus_02_dot_08 loc_bar__minus_4_bar__minus_8_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_01_dot_80_bar__plus_00_dot_00_bar__plus_00_dot_86 loc_bar__minus_5_bar_4_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__minus_01_dot_70_bar__plus_01_dot_14_bar__minus_02_dot_06 loc_bar__minus_4_bar__minus_8_bar_3_bar_30)
        (receptacleAtLocation Shelf_bar__minus_01_dot_70_bar__plus_01_dot_63_bar__minus_02_dot_06 loc_bar__minus_4_bar__minus_8_bar_3_bar_0)
        (receptacleAtLocation SideTable_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__plus_02_dot_14 loc_bar_2_bar_7_bar_1_bar_45)
        (receptacleAtLocation SideTable_bar__plus_02_dot_44_bar__plus_00_dot_46_bar__minus_01_dot_60 loc_bar_10_bar__minus_4_bar_2_bar_60)
        (objectAtLocation CD_bar__plus_01_dot_41_bar__plus_00_dot_37_bar__plus_02_dot_18 loc_bar_8_bar_5_bar_0_bar_60)
        (objectAtLocation CellPhone_bar__minus_01_dot_61_bar__plus_00_dot_80_bar__minus_00_dot_02 loc_bar__minus_2_bar_1_bar_3_bar_30)
        (objectAtLocation Pen_bar__minus_01_dot_67_bar__plus_01_dot_64_bar__minus_01_dot_96 loc_bar__minus_4_bar__minus_8_bar_3_bar_0)
        (objectAtLocation AlarmClock_bar__plus_01_dot_73_bar__plus_00_dot_68_bar__plus_02_dot_26 loc_bar_2_bar_7_bar_1_bar_45)
        (objectAtLocation Book_bar__plus_01_dot_50_bar__plus_00_dot_77_bar__minus_00_dot_70 loc_bar_4_bar_3_bar_2_bar_45)
        (objectAtLocation AlarmClock_bar__minus_01_dot_67_bar__plus_01_dot_64_bar__minus_02_dot_07 loc_bar__minus_4_bar__minus_8_bar_3_bar_0)
        (objectAtLocation Pen_bar__minus_01_dot_66_bar__plus_00_dot_98_bar__plus_00_dot_46 loc_bar__minus_4_bar__minus_2_bar_3_bar_60)
        (objectAtLocation CD_bar__plus_02_dot_55_bar__plus_00_dot_37_bar__minus_01_dot_59 loc_bar_9_bar__minus_3_bar_2_bar_60)
        (objectAtLocation Book_bar__minus_01_dot_66_bar__plus_00_dot_80_bar__plus_00_dot_26 loc_bar__minus_2_bar_1_bar_3_bar_30)
        (objectAtLocation KeyChain_bar__plus_01_dot_65_bar__plus_00_dot_68_bar__plus_02_dot_13 loc_bar_2_bar_7_bar_1_bar_45)
        (objectAtLocation AlarmClock_bar__minus_01_dot_80_bar__plus_00_dot_97_bar__minus_00_dot_34 loc_bar__minus_4_bar__minus_2_bar_3_bar_60)
        (objectAtLocation Pillow_bar__plus_01_dot_41_bar__plus_00_dot_79_bar__minus_01_dot_51 loc_bar_4_bar_3_bar_2_bar_45)
        (objectAtLocation Pillow_bar__plus_00_dot_78_bar__plus_00_dot_84_bar__minus_00_dot_99 loc_bar_4_bar_3_bar_2_bar_45)
        (objectAtLocation Vase_bar__minus_01_dot_67_bar__plus_01_dot_15_bar__minus_01_dot_73 loc_bar__minus_4_bar__minus_8_bar_3_bar_30)
        (objectAtLocation HousePlant_bar__minus_01_dot_79_bar__plus_00_dot_97_bar__plus_00_dot_53 loc_bar__minus_4_bar__minus_2_bar_3_bar_60)
        (objectAtLocation Laptop_bar__plus_01_dot_50_bar__plus_00_dot_77_bar__minus_00_dot_11 loc_bar_4_bar_3_bar_2_bar_45)
        (objectAtLocation Vase_bar__minus_01_dot_65_bar__plus_01_dot_63_bar__minus_02_dot_32 loc_bar__minus_4_bar__minus_8_bar_3_bar_0)
        (objectAtLocation Pencil_bar__minus_01_dot_50_bar__plus_00_dot_42_bar__plus_00_dot_26 loc_bar__minus_1_bar_2_bar_3_bar_45)
        (objectAtLocation CreditCard_bar__plus_02_dot_21_bar__plus_00_dot_68_bar__minus_01_dot_67 loc_bar_10_bar__minus_4_bar_2_bar_60)
        (objectAtLocation Boots_bar__minus_01_dot_84_bar__plus_00_dot_00_bar__minus_01_dot_08 loc_bar__minus_4_bar__minus_4_bar_3_bar_60)
        (objectAtLocation Painting_bar__plus_00_dot_38_bar__plus_01_dot_98_bar__minus_01_dot_90 loc_bar__minus_3_bar__minus_8_bar_1_bar__minus_15)
        (objectAtLocation Painting_bar__plus_00_dot_88_bar__plus_01_dot_98_bar__minus_01_dot_90 loc_bar__minus_3_bar__minus_8_bar_1_bar__minus_15)
        (objectAtLocation Painting_bar__plus_01_dot_37_bar__plus_01_dot_98_bar__minus_01_dot_90 loc_bar_9_bar__minus_5_bar_3_bar__minus_15)
        (objectAtLocation Pen_bar__minus_01_dot_67_bar__plus_01_dot_15_bar__minus_02_dot_07 loc_bar__minus_4_bar__minus_8_bar_3_bar_30)
        (objectAtLocation LightSwitch_bar__minus_00_dot_16_bar__plus_01_dot_09_bar__plus_02_dot_45 loc_bar__minus_1_bar_7_bar_0_bar_45)
        (objectAtLocation Mirror_bar__minus_01_dot_95_bar__plus_01_dot_74_bar__minus_00_dot_21 loc_bar__minus_4_bar__minus_2_bar_3_bar_60)
        (objectAtLocation CellPhone_bar__plus_02_dot_14_bar__plus_00_dot_44_bar__plus_02_dot_10 loc_bar_9_bar_3_bar_0_bar_60)
        (objectAtLocation CD_bar__minus_01_dot_76_bar__plus_01_dot_64_bar__minus_02_dot_42 loc_bar__minus_4_bar__minus_10_bar_3_bar_0)
        (objectAtLocation Window_bar__minus_01_dot_10_bar__plus_01_dot_57_bar__minus_03_dot_23 loc_bar__minus_4_bar__minus_10_bar_2_bar_0)
        (objectAtLocation Bowl_bar__minus_01_dot_68_bar__plus_01_dot_14_bar__minus_02_dot_30 loc_bar__minus_4_bar__minus_8_bar_3_bar_30)
        (objectAtLocation Statue_bar__plus_02_dot_44_bar__plus_00_dot_69_bar__minus_01_dot_72 loc_bar_10_bar__minus_4_bar_2_bar_60)
        (objectAtLocation Blinds_bar__minus_01_dot_06_bar__plus_02_dot_36_bar__minus_03_dot_17 loc_bar__minus_4_bar__minus_10_bar_2_bar__minus_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PenType)
                                    (receptacleType ?r SideTableType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PenType)
                                            (receptacleType ?r SideTableType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            