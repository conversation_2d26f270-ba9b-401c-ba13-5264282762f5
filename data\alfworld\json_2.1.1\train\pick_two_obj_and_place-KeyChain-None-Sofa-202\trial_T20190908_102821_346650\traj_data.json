{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 48}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON><PERSON><PERSON><PERSON>", "parent_target": "So<PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["keychain"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|12|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["keychain"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON><PERSON><PERSON><PERSON>", [-4.20163296, -4.20163296, 15.91544344, 15.91544344, 2.831342936, 2.831342936]], "forceVisible": true, "objectId": "KeyChain|-01.05|+00.71|+03.98"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sofa"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|7|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["keychain", "sofa"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON><PERSON><PERSON><PERSON>", [-4.20163296, -4.20163296, 15.91544344, 15.91544344, 2.831342936, 2.831342936]], "coordinateReceptacleObjectId": ["So<PERSON>", [-4.644, -4.644, 1.808, 1.808, 0.0001871585844, 0.0001871585844]], "forceVisible": true, "objectId": "KeyChain|-01.05|+00.71|+03.98", "receptacleObjectId": "Sofa|-01.16|+00.00|+00.45"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|4|4|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["keychain"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON><PERSON><PERSON><PERSON>", [4.02666332, 4.02666332, 2.0245916, 2.0245916, 0.963343024, 0.963343024]], "coordinateReceptacleObjectId": ["SideTable", [2.508, 2.508, 1.36, 1.36, 0.008, 0.008]], "forceVisible": true, "objectId": "KeyChain|+01.01|+00.24|+00.51"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sofa"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-5|7|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["keychain", "sofa"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON><PERSON><PERSON><PERSON>", [4.02666332, 4.02666332, 2.0245916, 2.0245916, 0.963343024, 0.963343024]], "coordinateReceptacleObjectId": ["So<PERSON>", [-4.644, -4.644, 1.808, 1.808, 0.0001871585844, 0.0001871585844]], "forceVisible": true, "objectId": "KeyChain|+01.01|+00.24|+00.51", "receptacleObjectId": "Sofa|-01.16|+00.00|+00.45"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "KeyChain|-01.05|+00.71|+03.98"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [122, 130, 145, 139], "mask": [[38822, 1], [38837, 5], [39122, 3], [39136, 7], [39424, 5], [39436, 7], [39725, 4], [39737, 5], [39744, 1], [40026, 3], [40034, 2], [40037, 2], [40045, 1], [40329, 1], [40333, 1], [40335, 4], [40345, 1], [40631, 4], [40636, 3], [40645, 1], [40930, 5], [40939, 7], [41226, 8], [41526, 1]], "point": [134, 133]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "KeyChain|-01.05|+00.71|+03.98", "placeStationary": true, "receptacleObjectId": "Sofa|-01.16|+00.00|+00.45"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 4, 299, 229], "mask": [[918, 66], [1075, 65], [1207, 90], [1361, 90], [1500, 111], [1648, 114], [1800, 117], [1942, 128], [2100, 118], [2240, 132], [2400, 120], [2539, 134], [2700, 121], [2838, 137], [3000, 122], [3137, 140], [3300, 124], [3435, 143], [3600, 125], [3734, 146], [3900, 126], [4033, 149], [4200, 128], [4332, 151], [4500, 129], [4630, 155], [4800, 129], [4930, 156], [5100, 129], [5230, 157], [5400, 129], [5530, 157], [5700, 129], [5830, 157], [6000, 129], [6130, 157], [6300, 129], [6430, 158], [6600, 288], [6900, 288], [7200, 288], [7500, 288], [7800, 288], [8100, 288], [8400, 288], [8700, 288], [9000, 288], [9300, 288], [9600, 288], [9900, 288], [10200, 288], [10500, 288], [10800, 288], [11100, 288], [11400, 288], [11700, 289], [12000, 289], [12300, 289], [12600, 289], [12900, 289], [13200, 289], [13500, 288], [13800, 299], [14100, 8400], [22568, 232], [22871, 229], [23173, 227], [23474, 226], [23776, 224], [24077, 223], [24377, 223], [24677, 223], [24978, 222], [25278, 222], [25578, 222], [25879, 221], [26179, 221], [26479, 221], [26779, 221], [27079, 221], [27380, 220], [27679, 221], [27979, 221], [28279, 221], [28579, 221], [28879, 221], [29179, 221], [29479, 221], [29780, 220], [30080, 220], [30380, 220], [30679, 221], [30979, 221], [31279, 221], [31579, 221], [31879, 221], [32179, 221], [32479, 221], [32779, 221], [33079, 221], [33379, 221], [33679, 221], [33979, 221], [34279, 82], [34369, 131], [34579, 82], [34670, 130], [34879, 82], [34970, 130], [35179, 82], [35270, 130], [35479, 82], [35570, 130], [35779, 82], [35870, 130], [36078, 83], [36170, 130], [36378, 84], [36471, 129], [36678, 84], [36771, 129], [36977, 85], [37071, 129], [37277, 85], [37371, 129], [37576, 86], [37671, 129], [37876, 86], [37971, 129], [38175, 87], [38271, 129], [38473, 89], [38572, 128], [38772, 90], [38872, 128], [39070, 92], [39172, 128], [39352, 110], [39472, 290], [39772, 290], [40072, 290], [40372, 290], [40672, 290], [40972, 290], [41272, 290], [41572, 290], [41872, 290], [42172, 290], [42472, 290], [42772, 291], [43072, 291], [43372, 291], [43672, 291], [43973, 290], [44273, 290], [44573, 290], [44873, 290], [45173, 290], [45473, 290], [45773, 290], [46073, 290], [46373, 290], [46673, 290], [46973, 290], [47273, 293], [47573, 20656], [68233, 167], [68695, 5]], "point": [149, 115]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "KeyChain|+01.01|+00.24|+00.51"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [146, 200, 166, 203], "mask": [[59847, 5], [59859, 5], [60146, 6], [60162, 3], [60447, 5], [60463, 4], [60747, 5]], "point": [159, 199]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "KeyChain|+01.01|+00.24|+00.51", "placeStationary": true, "receptacleObjectId": "Sofa|-01.16|+00.00|+00.45"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 4, 299, 229], "mask": [[918, 66], [1075, 65], [1207, 90], [1361, 90], [1500, 111], [1648, 114], [1800, 117], [1942, 128], [2100, 118], [2240, 132], [2400, 120], [2539, 134], [2700, 121], [2838, 137], [3000, 122], [3137, 140], [3300, 124], [3435, 143], [3600, 125], [3734, 146], [3900, 126], [4033, 149], [4200, 128], [4332, 151], [4500, 129], [4630, 155], [4800, 129], [4930, 156], [5100, 129], [5230, 157], [5400, 129], [5530, 157], [5700, 129], [5830, 157], [6000, 129], [6130, 157], [6300, 129], [6430, 158], [6600, 288], [6900, 288], [7200, 288], [7500, 288], [7800, 288], [8100, 288], [8400, 288], [8700, 288], [9000, 288], [9300, 288], [9600, 288], [9900, 288], [10200, 288], [10500, 288], [10800, 288], [11100, 288], [11400, 288], [11700, 289], [12000, 289], [12300, 289], [12600, 289], [12900, 289], [13200, 289], [13500, 288], [13800, 299], [14100, 8400], [22568, 232], [22871, 229], [23173, 227], [23474, 226], [23776, 224], [24077, 223], [24377, 223], [24677, 223], [24978, 222], [25278, 222], [25578, 222], [25879, 221], [26179, 221], [26479, 221], [26779, 221], [27079, 221], [27380, 220], [27679, 221], [27979, 221], [28279, 221], [28579, 221], [28879, 221], [29179, 221], [29479, 221], [29780, 220], [30080, 220], [30380, 220], [30679, 221], [30979, 221], [31279, 221], [31579, 221], [31879, 221], [32179, 221], [32479, 221], [32779, 221], [33079, 221], [33379, 221], [33679, 221], [33979, 221], [34279, 82], [34369, 131], [34579, 82], [34670, 130], [34879, 82], [34970, 130], [35179, 82], [35270, 130], [35479, 82], [35570, 130], [35779, 82], [35870, 130], [36078, 83], [36170, 130], [36378, 84], [36471, 129], [36678, 84], [36771, 129], [36977, 85], [37071, 129], [37277, 85], [37371, 129], [37576, 86], [37671, 129], [37876, 86], [37971, 129], [38175, 87], [38271, 129], [38473, 89], [38572, 128], [38772, 90], [38872, 128], [39070, 92], [39172, 128], [39352, 110], [39472, 290], [39772, 290], [40072, 290], [40372, 290], [40672, 290], [40972, 290], [41272, 290], [41572, 290], [41872, 290], [42172, 290], [42472, 290], [42772, 291], [43072, 291], [43372, 291], [43672, 291], [43973, 290], [44273, 290], [44573, 290], [44873, 290], [45173, 290], [45473, 290], [45773, 290], [46073, 290], [46373, 290], [46673, 290], [46973, 290], [47273, 293], [47573, 901], [48482, 281], [48764, 4], [48772, 1], [48778, 285], [49064, 6], [49073, 2], [49077, 286], [49364, 6], [49374, 1], [49376, 2], [49379, 290], [49672, 297], [49971, 9], [49984, 283], [50273, 7], [50284, 283], [50573, 8], [50582, 1], [50586, 281], [50873, 12], [50888, 280], [51172, 17057], [68233, 15], [68250, 150], [68695, 5]], "point": [149, 115]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan202", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.0, "y": 0.9010459, "z": 3.0}, "object_poses": [{"objectName": "Watch_4c46810f", "position": {"x": -1.05040824, "y": 0.707272649, "z": 4.08872128}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_2197d4a8", "position": {"x": -1.05040824, "y": 0.707835734, "z": 3.97886086}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a4a44225", "position": {"x": 1.00666583, "y": 0.71784085, "z": 0.5022677}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_e89cdb06", "position": {"x": -1.35185313, "y": 0.54741025, "z": 0.6506119}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_e89cdb06", "position": {"x": -3.20019054, "y": 0.564928055, "z": 0.47514832}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_76ac64dc", "position": {"x": -1.49920559, "y": 0.708587646, "z": 3.759139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Book_5cd72af5", "position": {"x": -2.82461929, "y": 0.565264165, "z": 0.47514832}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Box_08dfd057", "position": {"x": 0.332, "y": 0.892, "z": 0.488}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_76ac64dc", "position": {"x": -1.4613781, "y": 0.44600004, "z": 2.28078413}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_2197d4a8", "position": {"x": 1.00666583, "y": 0.240835756, "z": 0.5061479}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pillow_617abc68", "position": {"x": -0.506, "y": 0.664, "z": 0.431}, "rotation": {"x": 36.2882767, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_a36dc426", "position": {"x": -1.79840386, "y": 0.7128878, "z": 3.6492784}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_e89cdb06", "position": {"x": -1.35185313, "y": 0.54741025, "z": 0.471167117}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a4a44225", "position": {"x": -0.351181924, "y": 0.54604274, "z": 0.830056548}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Watch_4c46810f", "position": {"x": 1.00666583, "y": 0.718117535, "z": 0.583401561}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2605656404, "scene_num": 202}, "task_id": "trial_T20190908_102821_346650", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "AHBWX3WYMAB0E_33CKWXB73MBXI6KACF34CNJBIDO11M", "high_descs": ["Turn around and face the TV stand.", "Pick up the set of keys from the top of the TV stand.", "Turn around and move across the room to the love seat.", "Put the keys on the left cushion of the love seat in front of the closest remote control.", "Turn left and head to the side table on the left side of the love seat.", "Pick up the set of keys from the top of the table.", "Turn right and head back to the front of the love seat.", "Put down the second set of keys to the left of the first set."], "task_desc": "Move two sets of keys to the left cushion of the loveseat.", "votes": [1, 1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3Z7EFSHGNC5PVAF1Z5DVOS63RMBXCB", "high_descs": ["Turn around to face the wooden table.", "Pick up the set of keys off of the right side of the table.", "Turn around and walk over to the blue couch.", "Put the set of keys on the front left edge of the rightmost couch cushion.", "Turn left and walk over to the wall, then turn right and walk up to the wooden table.", "Pick up the set of keys off of the shelf below the table.", "Turn around and walk forward, then hang a left and walk over to the blue couch.", "Put a set of keys on the middle front edge of the blue couch."], "task_desc": "Move two sets of keys to the blue couch.", "votes": [1, 1, 1]}, {"assignment_id": "ARB8SJAWXUWTD_3TESA3PJ341RAKSOYN50G7VHEXCMMH", "high_descs": ["Go to the TV table", "Pick up the keys", "Go to the sofa", "Put the keys down on the sofa", "Go to the table on the left", "Pick up the keys", "Go to the sofa", "Put the keys down on the sofa"], "task_desc": "Move two sets of keys to a sofa", "votes": [1, 0, 1]}]}}