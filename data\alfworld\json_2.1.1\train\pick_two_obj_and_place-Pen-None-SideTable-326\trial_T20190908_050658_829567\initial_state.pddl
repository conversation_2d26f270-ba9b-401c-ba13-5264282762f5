
(define (problem plan_trial_T20190908_050658_829567)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON>haker - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__minus_02_dot_89_bar__plus_01_dot_41_bar__minus_01_dot_50 - object
        BaseballBat_bar__minus_02_dot_98_bar__plus_00_dot_02_bar__minus_02_dot_70 - object
        BasketBall_bar__minus_02_dot_72_bar__plus_00_dot_12_bar__minus_01_dot_93 - object
        Blinds_bar__plus_02_dot_29_bar__plus_02_dot_07_bar__minus_03_dot_18 - object
        Blinds_bar__minus_01_dot_00_bar__plus_02_dot_07_bar__minus_03_dot_18 - object
        Book_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_00_dot_60 - object
        Book_bar__minus_02_dot_92_bar__plus_00_dot_60_bar__minus_00_dot_04 - object
        Bowl_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_70 - object
        CD_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_01_dot_40 - object
        CD_bar__minus_01_dot_66_bar__plus_00_dot_61_bar__plus_00_dot_34 - object
        CellPhone_bar__plus_00_dot_92_bar__plus_01_dot_14_bar__minus_01_dot_49 - object
        CellPhone_bar__plus_02_dot_96_bar__plus_00_dot_79_bar__minus_01_dot_97 - object
        CellPhone_bar__minus_01_dot_09_bar__plus_00_dot_62_bar__plus_00_dot_25 - object
        Chair_bar__plus_02_dot_73_bar__plus_00_dot_00_bar__minus_01_dot_21 - object
        Chair_bar__plus_02_dot_85_bar__plus_00_dot_00_bar__plus_00_dot_16 - object
        Cloth_bar__plus_00_dot_71_bar_00_dot_00_bar__minus_02_dot_75 - object
        Cloth_bar__plus_00_dot_91_bar__plus_00_dot_03_bar__minus_02_dot_70 - object
        Cloth_bar__plus_00_dot_97_bar_00_dot_00_bar__minus_02_dot_40 - object
        CreditCard_bar__plus_02_dot_99_bar__plus_00_dot_54_bar__minus_00_dot_38 - object
        KeyChain_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_01_dot_97 - object
        KeyChain_bar__minus_01_dot_42_bar__plus_00_dot_08_bar__plus_00_dot_35 - object
        KeyChain_bar__minus_01_dot_57_bar__plus_00_dot_34_bar__plus_00_dot_39 - object
        Lamp_bar__plus_03_dot_24_bar__plus_00_dot_79_bar__minus_00_dot_65 - object
        Laptop_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_01_dot_44 - object
        LightSwitch_bar__minus_00_dot_14_bar__plus_01_dot_33_bar__plus_00_dot_60 - object
        Mirror_bar__plus_00_dot_45_bar__plus_01_dot_49_bar__plus_00_dot_62 - object
        Mug_bar__minus_01_dot_52_bar__plus_00_dot_61_bar__plus_00_dot_38 - object
        Pencil_bar__plus_03_dot_11_bar__plus_00_dot_55_bar__minus_00_dot_55 - object
        Pen_bar__plus_00_dot_86_bar__plus_01_dot_42_bar__minus_02_dot_59 - object
        Pen_bar__plus_03_dot_02_bar__plus_00_dot_80_bar__minus_01_dot_12 - object
        Pen_bar__plus_03_dot_09_bar__plus_00_dot_80_bar__minus_00_dot_84 - object
        Pillow_bar__minus_00_dot_94_bar__plus_00_dot_69_bar__minus_00_dot_60 - object
        Pillow_bar__minus_02_dot_47_bar__plus_00_dot_66_bar__plus_00_dot_11 - object
        Poster_bar__plus_03_dot_40_bar__plus_01_dot_70_bar__minus_00_dot_79 - object
        Poster_bar__plus_03_dot_40_bar__plus_01_dot_86_bar__minus_01_dot_98 - object
        TeddyBear_bar__minus_02_dot_65_bar__plus_00_dot_62_bar__minus_00_dot_40 - object
        TennisRacket_bar__minus_03_dot_02_bar__plus_00_dot_30_bar__minus_02_dot_20 - object
        Watch_bar__minus_01_dot_59_bar__plus_00_dot_61_bar__plus_00_dot_54 - object
        Window_bar__plus_02_dot_28_bar__plus_00_dot_93_bar__minus_03_dot_18 - object
        Window_bar__minus_01_dot_02_bar__plus_00_dot_93_bar__minus_03_dot_19 - object
        Bed_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__minus_00_dot_55 - receptacle
        Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55 - receptacle
        DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42 - receptacle
        Drawer_bar__minus_01_dot_52_bar__plus_00_dot_15_bar__plus_00_dot_36 - receptacle
        Drawer_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36 - receptacle
        GarbageCan_bar__plus_01_dot_69_bar_00_dot_00_bar__minus_02_dot_79 - receptacle
        Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_01_dot_62 - receptacle
        Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_02_dot_16 - receptacle
        Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_02_dot_73 - receptacle
        Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_62 - receptacle
        Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_02_dot_16 - receptacle
        Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_02_dot_73 - receptacle
        Shelf_bar__plus_03_dot_04_bar__plus_00_dot_53_bar__minus_00_dot_56 - receptacle
        Shelf_bar__plus_03_dot_04_bar__plus_00_dot_53_bar__minus_02_dot_26 - receptacle
        Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_01_dot_62 - receptacle
        Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_02_dot_19 - receptacle
        Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_02_dot_74 - receptacle
        Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_62 - receptacle
        Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_19 - receptacle
        Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_74 - receptacle
        SideTable_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36 - receptacle
        loc_bar_1_bar__minus_10_bar_1_bar_15 - location
        loc_bar__minus_9_bar__minus_8_bar_3_bar_15 - location
        loc_bar__minus_9_bar__minus_10_bar_3_bar_30 - location
        loc_bar__minus_9_bar__minus_10_bar_0_bar_45 - location
        loc_bar_3_bar__minus_3_bar_3_bar_45 - location
        loc_bar_1_bar_0_bar_0_bar_30 - location
        loc_bar_9_bar__minus_11_bar_2_bar_60 - location
        loc_bar_9_bar__minus_11_bar_2_bar__minus_30 - location
        loc_bar_1_bar__minus_10_bar_1_bar_30 - location
        loc_bar_2_bar_0_bar_0_bar_15 - location
        loc_bar_9_bar__minus_1_bar_1_bar_60 - location
        loc_bar_1_bar__minus_6_bar_1_bar_30 - location
        loc_bar_1_bar__minus_9_bar_1_bar_15 - location
        loc_bar__minus_4_bar__minus_11_bar_2_bar__minus_30 - location
        loc_bar_9_bar__minus_8_bar_1_bar_60 - location
        loc_bar_10_bar__minus_2_bar_1_bar_0 - location
        loc_bar__minus_6_bar__minus_3_bar_0_bar_60 - location
        loc_bar_10_bar__minus_7_bar_0_bar_60 - location
        loc_bar__minus_6_bar__minus_2_bar_0_bar_60 - location
        loc_bar__minus_6_bar__minus_1_bar_0_bar_60 - location
        loc_bar__minus_9_bar__minus_10_bar_3_bar_60 - location
        loc_bar__minus_8_bar__minus_8_bar_3_bar_30 - location
        loc_bar_9_bar__minus_11_bar_3_bar_60 - location
        loc_bar__minus_9_bar__minus_8_bar_3_bar_60 - location
        loc_bar__minus_9_bar__minus_9_bar_3_bar_60 - location
        loc_bar_1_bar__minus_10_bar_1_bar_60 - location
        loc_bar__minus_9_bar__minus_10_bar_3_bar_15 - location
        loc_bar__minus_9_bar__minus_9_bar_3_bar_30 - location
        loc_bar_1_bar__minus_6_bar_1_bar_15 - location
        loc_bar_10_bar__minus_8_bar_1_bar__minus_15 - location
        loc_bar_1_bar__minus_9_bar_1_bar_30 - location
        loc_bar_8_bar__minus_5_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_11_bar_2_bar_60 - location
        loc_bar__minus_9_bar__minus_9_bar_3_bar_15 - location
        loc_bar_9_bar_0_bar_1_bar_60 - location
        loc_bar__minus_6_bar__minus_11_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_01_dot_62 ShelfType)
        (receptacleType Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55 BedType)
        (receptacleType Drawer_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36 DrawerType)
        (receptacleType Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_01_dot_62 ShelfType)
        (receptacleType Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_02_dot_19 ShelfType)
        (receptacleType SideTable_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36 SideTableType)
        (receptacleType Shelf_bar__plus_03_dot_04_bar__plus_00_dot_53_bar__minus_02_dot_26 ShelfType)
        (receptacleType GarbageCan_bar__plus_01_dot_69_bar_00_dot_00_bar__minus_02_dot_79 GarbageCanType)
        (receptacleType Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_62 ShelfType)
        (receptacleType Drawer_bar__minus_01_dot_52_bar__plus_00_dot_15_bar__plus_00_dot_36 DrawerType)
        (receptacleType Bed_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__minus_00_dot_55 BedType)
        (receptacleType Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_02_dot_74 ShelfType)
        (receptacleType Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_02_dot_73 ShelfType)
        (receptacleType Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_19 ShelfType)
        (receptacleType Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_62 ShelfType)
        (receptacleType Shelf_bar__plus_03_dot_04_bar__plus_00_dot_53_bar__minus_00_dot_56 ShelfType)
        (receptacleType Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_74 ShelfType)
        (receptacleType Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_02_dot_16 ShelfType)
        (receptacleType DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42 DiningTableType)
        (receptacleType Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_02_dot_73 ShelfType)
        (receptacleType Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_02_dot_16 ShelfType)
        (objectType Bowl_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_70 BowlType)
        (objectType Chair_bar__plus_02_dot_73_bar__plus_00_dot_00_bar__minus_01_dot_21 ChairType)
        (objectType CD_bar__minus_01_dot_66_bar__plus_00_dot_61_bar__plus_00_dot_34 CDType)
        (objectType Cloth_bar__plus_00_dot_71_bar_00_dot_00_bar__minus_02_dot_75 ClothType)
        (objectType BasketBall_bar__minus_02_dot_72_bar__plus_00_dot_12_bar__minus_01_dot_93 BasketBallType)
        (objectType TennisRacket_bar__minus_03_dot_02_bar__plus_00_dot_30_bar__minus_02_dot_20 TennisRacketType)
        (objectType CellPhone_bar__plus_02_dot_96_bar__plus_00_dot_79_bar__minus_01_dot_97 CellPhoneType)
        (objectType KeyChain_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_01_dot_97 KeyChainType)
        (objectType Pen_bar__plus_03_dot_09_bar__plus_00_dot_80_bar__minus_00_dot_84 PenType)
        (objectType Chair_bar__plus_02_dot_85_bar__plus_00_dot_00_bar__plus_00_dot_16 ChairType)
        (objectType Cloth_bar__plus_00_dot_91_bar__plus_00_dot_03_bar__minus_02_dot_70 ClothType)
        (objectType Watch_bar__minus_01_dot_59_bar__plus_00_dot_61_bar__plus_00_dot_54 WatchType)
        (objectType Pen_bar__plus_00_dot_86_bar__plus_01_dot_42_bar__minus_02_dot_59 PenType)
        (objectType Book_bar__minus_02_dot_92_bar__plus_00_dot_60_bar__minus_00_dot_04 BookType)
        (objectType BaseballBat_bar__minus_02_dot_98_bar__plus_00_dot_02_bar__minus_02_dot_70 BaseballBatType)
        (objectType Poster_bar__plus_03_dot_40_bar__plus_01_dot_70_bar__minus_00_dot_79 PosterType)
        (objectType CellPhone_bar__plus_00_dot_92_bar__plus_01_dot_14_bar__minus_01_dot_49 CellPhoneType)
        (objectType Mirror_bar__plus_00_dot_45_bar__plus_01_dot_49_bar__plus_00_dot_62 MirrorType)
        (objectType CreditCard_bar__plus_02_dot_99_bar__plus_00_dot_54_bar__minus_00_dot_38 CreditCardType)
        (objectType Pillow_bar__minus_02_dot_47_bar__plus_00_dot_66_bar__plus_00_dot_11 PillowType)
        (objectType CD_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_01_dot_40 CDType)
        (objectType Window_bar__plus_02_dot_28_bar__plus_00_dot_93_bar__minus_03_dot_18 WindowType)
        (objectType Laptop_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_01_dot_44 LaptopType)
        (objectType Mug_bar__minus_01_dot_52_bar__plus_00_dot_61_bar__plus_00_dot_38 MugType)
        (objectType CellPhone_bar__minus_01_dot_09_bar__plus_00_dot_62_bar__plus_00_dot_25 CellPhoneType)
        (objectType KeyChain_bar__minus_01_dot_42_bar__plus_00_dot_08_bar__plus_00_dot_35 KeyChainType)
        (objectType Poster_bar__plus_03_dot_40_bar__plus_01_dot_86_bar__minus_01_dot_98 PosterType)
        (objectType TeddyBear_bar__minus_02_dot_65_bar__plus_00_dot_62_bar__minus_00_dot_40 TeddyBearType)
        (objectType AlarmClock_bar__minus_02_dot_89_bar__plus_01_dot_41_bar__minus_01_dot_50 AlarmClockType)
        (objectType Pencil_bar__plus_03_dot_11_bar__plus_00_dot_55_bar__minus_00_dot_55 PencilType)
        (objectType Cloth_bar__plus_00_dot_97_bar_00_dot_00_bar__minus_02_dot_40 ClothType)
        (objectType Pillow_bar__minus_00_dot_94_bar__plus_00_dot_69_bar__minus_00_dot_60 PillowType)
        (objectType Blinds_bar__plus_02_dot_29_bar__plus_02_dot_07_bar__minus_03_dot_18 BlindsType)
        (objectType Blinds_bar__minus_01_dot_00_bar__plus_02_dot_07_bar__minus_03_dot_18 BlindsType)
        (objectType Book_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_00_dot_60 BookType)
        (objectType LightSwitch_bar__minus_00_dot_14_bar__plus_01_dot_33_bar__plus_00_dot_60 LightSwitchType)
        (objectType Window_bar__minus_01_dot_02_bar__plus_00_dot_93_bar__minus_03_dot_19 WindowType)
        (objectType Pen_bar__plus_03_dot_02_bar__plus_00_dot_80_bar__minus_01_dot_12 PenType)
        (objectType KeyChain_bar__minus_01_dot_57_bar__plus_00_dot_34_bar__plus_00_dot_39 KeyChainType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain BedType BasketBallType)
        (canContain BedType BaseballBatType)
        (canContain BedType TennisRacketType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType WatchType)
        (canContain SideTableType BowlType)
        (canContain SideTableType CDType)
        (canContain SideTableType MugType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType BasketBallType)
        (canContain SideTableType BaseballBatType)
        (canContain SideTableType TennisRacketType)
        (canContain SideTableType ClothType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType ClothType)
        (canContain GarbageCanType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain BedType BasketBallType)
        (canContain BedType BaseballBatType)
        (canContain BedType TennisRacketType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DiningTableType PenType)
        (canContain DiningTableType BookType)
        (canContain DiningTableType WatchType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType CDType)
        (canContain DiningTableType MugType)
        (canContain DiningTableType CellPhoneType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType BasketBallType)
        (canContain DiningTableType BaseballBatType)
        (canContain DiningTableType TennisRacketType)
        (canContain DiningTableType ClothType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType PencilType)
        (canContain DiningTableType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (pickupable Bowl_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_70)
        (pickupable CD_bar__minus_01_dot_66_bar__plus_00_dot_61_bar__plus_00_dot_34)
        (pickupable Cloth_bar__plus_00_dot_71_bar_00_dot_00_bar__minus_02_dot_75)
        (pickupable BasketBall_bar__minus_02_dot_72_bar__plus_00_dot_12_bar__minus_01_dot_93)
        (pickupable TennisRacket_bar__minus_03_dot_02_bar__plus_00_dot_30_bar__minus_02_dot_20)
        (pickupable CellPhone_bar__plus_02_dot_96_bar__plus_00_dot_79_bar__minus_01_dot_97)
        (pickupable KeyChain_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_01_dot_97)
        (pickupable Pen_bar__plus_03_dot_09_bar__plus_00_dot_80_bar__minus_00_dot_84)
        (pickupable Cloth_bar__plus_00_dot_91_bar__plus_00_dot_03_bar__minus_02_dot_70)
        (pickupable Watch_bar__minus_01_dot_59_bar__plus_00_dot_61_bar__plus_00_dot_54)
        (pickupable Pen_bar__plus_00_dot_86_bar__plus_01_dot_42_bar__minus_02_dot_59)
        (pickupable Book_bar__minus_02_dot_92_bar__plus_00_dot_60_bar__minus_00_dot_04)
        (pickupable BaseballBat_bar__minus_02_dot_98_bar__plus_00_dot_02_bar__minus_02_dot_70)
        (pickupable CellPhone_bar__plus_00_dot_92_bar__plus_01_dot_14_bar__minus_01_dot_49)
        (pickupable CreditCard_bar__plus_02_dot_99_bar__plus_00_dot_54_bar__minus_00_dot_38)
        (pickupable Pillow_bar__minus_02_dot_47_bar__plus_00_dot_66_bar__plus_00_dot_11)
        (pickupable CD_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_01_dot_40)
        (pickupable Laptop_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_01_dot_44)
        (pickupable Mug_bar__minus_01_dot_52_bar__plus_00_dot_61_bar__plus_00_dot_38)
        (pickupable CellPhone_bar__minus_01_dot_09_bar__plus_00_dot_62_bar__plus_00_dot_25)
        (pickupable KeyChain_bar__minus_01_dot_42_bar__plus_00_dot_08_bar__plus_00_dot_35)
        (pickupable TeddyBear_bar__minus_02_dot_65_bar__plus_00_dot_62_bar__minus_00_dot_40)
        (pickupable AlarmClock_bar__minus_02_dot_89_bar__plus_01_dot_41_bar__minus_01_dot_50)
        (pickupable Pencil_bar__plus_03_dot_11_bar__plus_00_dot_55_bar__minus_00_dot_55)
        (pickupable Cloth_bar__plus_00_dot_97_bar_00_dot_00_bar__minus_02_dot_40)
        (pickupable Pillow_bar__minus_00_dot_94_bar__plus_00_dot_69_bar__minus_00_dot_60)
        (pickupable Book_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_00_dot_60)
        (pickupable Pen_bar__plus_03_dot_02_bar__plus_00_dot_80_bar__minus_01_dot_12)
        (pickupable KeyChain_bar__minus_01_dot_57_bar__plus_00_dot_34_bar__plus_00_dot_39)
        (isReceptacleObject Bowl_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_70)
        (isReceptacleObject Mug_bar__minus_01_dot_52_bar__plus_00_dot_61_bar__plus_00_dot_38)
        (openable Drawer_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36)
        (openable Drawer_bar__minus_01_dot_52_bar__plus_00_dot_15_bar__plus_00_dot_36)
        
        (atLocation agent1 loc_bar__minus_6_bar__minus_11_bar_3_bar_30)
        
        (cleanable Bowl_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_70)
        (cleanable Cloth_bar__plus_00_dot_71_bar_00_dot_00_bar__minus_02_dot_75)
        (cleanable Cloth_bar__plus_00_dot_91_bar__plus_00_dot_03_bar__minus_02_dot_70)
        (cleanable Mug_bar__minus_01_dot_52_bar__plus_00_dot_61_bar__plus_00_dot_38)
        (cleanable Cloth_bar__plus_00_dot_97_bar_00_dot_00_bar__minus_02_dot_40)
        
        (heatable Mug_bar__minus_01_dot_52_bar__plus_00_dot_61_bar__plus_00_dot_38)
        (coolable Bowl_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_70)
        (coolable Mug_bar__minus_01_dot_52_bar__plus_00_dot_61_bar__plus_00_dot_38)
        
        
        
        
        
        
        
        (inReceptacle Pen_bar__plus_03_dot_09_bar__plus_00_dot_80_bar__minus_00_dot_84 DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42)
        (inReceptacle KeyChain_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_01_dot_97 DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42)
        (inReceptacle CD_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_01_dot_40 DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42)
        (inReceptacle Pen_bar__plus_03_dot_02_bar__plus_00_dot_80_bar__minus_01_dot_12 DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42)
        (inReceptacle CellPhone_bar__plus_02_dot_96_bar__plus_00_dot_79_bar__minus_01_dot_97 DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42)
        (inReceptacle TeddyBear_bar__minus_02_dot_65_bar__plus_00_dot_62_bar__minus_00_dot_40 Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55)
        (inReceptacle Pillow_bar__minus_02_dot_47_bar__plus_00_dot_66_bar__plus_00_dot_11 Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55)
        (inReceptacle Book_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_00_dot_60 Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55)
        (inReceptacle Laptop_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_01_dot_44 Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55)
        (inReceptacle Book_bar__minus_02_dot_92_bar__plus_00_dot_60_bar__minus_00_dot_04 Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55)
        (inReceptacle KeyChain_bar__minus_01_dot_42_bar__plus_00_dot_08_bar__plus_00_dot_35 Drawer_bar__minus_01_dot_52_bar__plus_00_dot_15_bar__plus_00_dot_36)
        (inReceptacle Pillow_bar__minus_00_dot_94_bar__plus_00_dot_69_bar__minus_00_dot_60 Bed_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__minus_00_dot_55)
        (inReceptacle CellPhone_bar__minus_01_dot_09_bar__plus_00_dot_62_bar__plus_00_dot_25 Bed_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__minus_00_dot_55)
        (inReceptacle Mug_bar__minus_01_dot_52_bar__plus_00_dot_61_bar__plus_00_dot_38 SideTable_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36)
        (inReceptacle CD_bar__minus_01_dot_66_bar__plus_00_dot_61_bar__plus_00_dot_34 SideTable_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36)
        (inReceptacle Watch_bar__minus_01_dot_59_bar__plus_00_dot_61_bar__plus_00_dot_54 SideTable_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36)
        (inReceptacle KeyChain_bar__minus_01_dot_57_bar__plus_00_dot_34_bar__plus_00_dot_39 Drawer_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36)
        (inReceptacle Bowl_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_70 Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_62)
        (inReceptacle AlarmClock_bar__minus_02_dot_89_bar__plus_01_dot_41_bar__minus_01_dot_50 Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_62)
        (inReceptacle CellPhone_bar__plus_00_dot_92_bar__plus_01_dot_14_bar__minus_01_dot_49 Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_01_dot_62)
        (inReceptacle Pen_bar__plus_00_dot_86_bar__plus_01_dot_42_bar__minus_02_dot_59 Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_02_dot_73)
        (inReceptacle CreditCard_bar__plus_02_dot_99_bar__plus_00_dot_54_bar__minus_00_dot_38 Shelf_bar__plus_03_dot_04_bar__plus_00_dot_53_bar__minus_00_dot_56)
        (inReceptacle Pencil_bar__plus_03_dot_11_bar__plus_00_dot_55_bar__minus_00_dot_55 Shelf_bar__plus_03_dot_04_bar__plus_00_dot_53_bar__minus_00_dot_56)
        
        
        (receptacleAtLocation Bed_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__minus_00_dot_55 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (receptacleAtLocation Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55 loc_bar__minus_9_bar__minus_10_bar_0_bar_45)
        (receptacleAtLocation DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42 loc_bar_8_bar__minus_5_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_52_bar__plus_00_dot_15_bar__plus_00_dot_36 loc_bar__minus_6_bar__minus_3_bar_0_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36 loc_bar__minus_6_bar__minus_2_bar_0_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_01_dot_69_bar_00_dot_00_bar__minus_02_dot_79 loc_bar_9_bar__minus_11_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_01_dot_62 loc_bar_1_bar__minus_6_bar_1_bar_30)
        (receptacleAtLocation Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_02_dot_16 loc_bar_1_bar__minus_9_bar_1_bar_30)
        (receptacleAtLocation Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_02_dot_73 loc_bar_1_bar__minus_10_bar_1_bar_30)
        (receptacleAtLocation Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_62 loc_bar_1_bar__minus_6_bar_1_bar_15)
        (receptacleAtLocation Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_02_dot_16 loc_bar_1_bar__minus_9_bar_1_bar_15)
        (receptacleAtLocation Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_02_dot_73 loc_bar_1_bar__minus_10_bar_1_bar_15)
        (receptacleAtLocation Shelf_bar__plus_03_dot_04_bar__plus_00_dot_53_bar__minus_00_dot_56 loc_bar_9_bar__minus_1_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_03_dot_04_bar__plus_00_dot_53_bar__minus_02_dot_26 loc_bar_9_bar__minus_8_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_01_dot_62 loc_bar__minus_8_bar__minus_8_bar_3_bar_30)
        (receptacleAtLocation Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_02_dot_19 loc_bar__minus_9_bar__minus_9_bar_3_bar_30)
        (receptacleAtLocation Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_02_dot_74 loc_bar__minus_9_bar__minus_10_bar_3_bar_30)
        (receptacleAtLocation Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_62 loc_bar__minus_9_bar__minus_8_bar_3_bar_15)
        (receptacleAtLocation Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_19 loc_bar__minus_9_bar__minus_9_bar_3_bar_15)
        (receptacleAtLocation Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_74 loc_bar__minus_9_bar__minus_10_bar_3_bar_15)
        (receptacleAtLocation SideTable_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36 loc_bar__minus_6_bar__minus_1_bar_0_bar_60)
        (objectAtLocation CD_bar__minus_01_dot_66_bar__plus_00_dot_61_bar__plus_00_dot_34 loc_bar__minus_6_bar__minus_1_bar_0_bar_60)
        (objectAtLocation CellPhone_bar__plus_00_dot_92_bar__plus_01_dot_14_bar__minus_01_dot_49 loc_bar_1_bar__minus_6_bar_1_bar_30)
        (objectAtLocation Pen_bar__plus_00_dot_86_bar__plus_01_dot_42_bar__minus_02_dot_59 loc_bar_1_bar__minus_10_bar_1_bar_15)
        (objectAtLocation KeyChain_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_01_dot_97 loc_bar_8_bar__minus_5_bar_1_bar_60)
        (objectAtLocation Book_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_00_dot_60 loc_bar__minus_9_bar__minus_10_bar_0_bar_45)
        (objectAtLocation KeyChain_bar__minus_01_dot_42_bar__plus_00_dot_08_bar__plus_00_dot_35 loc_bar__minus_6_bar__minus_3_bar_0_bar_60)
        (objectAtLocation Pen_bar__plus_03_dot_09_bar__plus_00_dot_80_bar__minus_00_dot_84 loc_bar_8_bar__minus_5_bar_1_bar_60)
        (objectAtLocation CellPhone_bar__minus_01_dot_09_bar__plus_00_dot_62_bar__plus_00_dot_25 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation Chair_bar__plus_02_dot_73_bar__plus_00_dot_00_bar__minus_01_dot_21 loc_bar_10_bar__minus_7_bar_0_bar_60)
        (objectAtLocation Chair_bar__plus_02_dot_85_bar__plus_00_dot_00_bar__plus_00_dot_16 loc_bar_9_bar_0_bar_1_bar_60)
        (objectAtLocation Book_bar__minus_02_dot_92_bar__plus_00_dot_60_bar__minus_00_dot_04 loc_bar__minus_9_bar__minus_10_bar_0_bar_45)
        (objectAtLocation Laptop_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_01_dot_44 loc_bar__minus_9_bar__minus_10_bar_0_bar_45)
        (objectAtLocation KeyChain_bar__minus_01_dot_57_bar__plus_00_dot_34_bar__plus_00_dot_39 loc_bar__minus_6_bar__minus_2_bar_0_bar_60)
        (objectAtLocation Cloth_bar__plus_00_dot_97_bar_00_dot_00_bar__minus_02_dot_40 loc_bar_1_bar__minus_10_bar_1_bar_60)
        (objectAtLocation BaseballBat_bar__minus_02_dot_98_bar__plus_00_dot_02_bar__minus_02_dot_70 loc_bar__minus_9_bar__minus_10_bar_3_bar_60)
        (objectAtLocation BasketBall_bar__minus_02_dot_72_bar__plus_00_dot_12_bar__minus_01_dot_93 loc_bar__minus_9_bar__minus_8_bar_3_bar_60)
        (objectAtLocation Pillow_bar__minus_02_dot_47_bar__plus_00_dot_66_bar__plus_00_dot_11 loc_bar__minus_9_bar__minus_10_bar_0_bar_45)
        (objectAtLocation Pillow_bar__minus_00_dot_94_bar__plus_00_dot_69_bar__minus_00_dot_60 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation TennisRacket_bar__minus_03_dot_02_bar__plus_00_dot_30_bar__minus_02_dot_20 loc_bar__minus_9_bar__minus_9_bar_3_bar_60)
        (objectAtLocation Pencil_bar__plus_03_dot_11_bar__plus_00_dot_55_bar__minus_00_dot_55 loc_bar_9_bar__minus_1_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__plus_02_dot_99_bar__plus_00_dot_54_bar__minus_00_dot_38 loc_bar_9_bar__minus_1_bar_1_bar_60)
        (objectAtLocation Cloth_bar__plus_00_dot_91_bar__plus_00_dot_03_bar__minus_02_dot_70 loc_bar_1_bar__minus_10_bar_1_bar_60)
        (objectAtLocation TeddyBear_bar__minus_02_dot_65_bar__plus_00_dot_62_bar__minus_00_dot_40 loc_bar__minus_9_bar__minus_10_bar_0_bar_45)
        (objectAtLocation LightSwitch_bar__minus_00_dot_14_bar__plus_01_dot_33_bar__plus_00_dot_60 loc_bar_1_bar_0_bar_0_bar_30)
        (objectAtLocation Poster_bar__plus_03_dot_40_bar__plus_01_dot_86_bar__minus_01_dot_98 loc_bar_10_bar__minus_8_bar_1_bar__minus_15)
        (objectAtLocation Poster_bar__plus_03_dot_40_bar__plus_01_dot_70_bar__minus_00_dot_79 loc_bar_10_bar__minus_2_bar_1_bar_0)
        (objectAtLocation Pen_bar__plus_03_dot_02_bar__plus_00_dot_80_bar__minus_01_dot_12 loc_bar_8_bar__minus_5_bar_1_bar_60)
        (objectAtLocation Cloth_bar__plus_00_dot_71_bar_00_dot_00_bar__minus_02_dot_75 loc_bar_1_bar__minus_10_bar_1_bar_60)
        (objectAtLocation AlarmClock_bar__minus_02_dot_89_bar__plus_01_dot_41_bar__minus_01_dot_50 loc_bar__minus_9_bar__minus_8_bar_3_bar_15)
        (objectAtLocation Watch_bar__minus_01_dot_59_bar__plus_00_dot_61_bar__plus_00_dot_54 loc_bar__minus_6_bar__minus_1_bar_0_bar_60)
        (objectAtLocation Mirror_bar__plus_00_dot_45_bar__plus_01_dot_49_bar__plus_00_dot_62 loc_bar_2_bar_0_bar_0_bar_15)
        (objectAtLocation CellPhone_bar__plus_02_dot_96_bar__plus_00_dot_79_bar__minus_01_dot_97 loc_bar_8_bar__minus_5_bar_1_bar_60)
        (objectAtLocation CD_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_01_dot_40 loc_bar_8_bar__minus_5_bar_1_bar_60)
        (objectAtLocation Window_bar__plus_02_dot_28_bar__plus_00_dot_93_bar__minus_03_dot_18 loc_bar_9_bar__minus_11_bar_2_bar_60)
        (objectAtLocation Window_bar__minus_01_dot_02_bar__plus_00_dot_93_bar__minus_03_dot_19 loc_bar__minus_4_bar__minus_11_bar_2_bar_60)
        (objectAtLocation Mug_bar__minus_01_dot_52_bar__plus_00_dot_61_bar__plus_00_dot_38 loc_bar__minus_6_bar__minus_1_bar_0_bar_60)
        (objectAtLocation Blinds_bar__minus_01_dot_00_bar__plus_02_dot_07_bar__minus_03_dot_18 loc_bar__minus_4_bar__minus_11_bar_2_bar__minus_30)
        (objectAtLocation Blinds_bar__plus_02_dot_29_bar__plus_02_dot_07_bar__minus_03_dot_18 loc_bar_9_bar__minus_11_bar_2_bar__minus_30)
        (objectAtLocation Bowl_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_70 loc_bar__minus_9_bar__minus_8_bar_3_bar_15)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PenType)
                                    (receptacleType ?r SideTableType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PenType)
                                            (receptacleType ?r SideTableType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            