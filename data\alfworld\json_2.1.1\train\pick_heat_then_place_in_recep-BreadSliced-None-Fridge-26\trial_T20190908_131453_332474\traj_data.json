{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000289.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000290.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000291.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000330.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000331.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000332.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000333.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000335.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000336.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000337.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000338.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000339.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000340.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000341.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000342.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000343.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000344.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000345.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000346.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000347.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000348.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000349.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000350.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000351.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000352.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000353.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000354.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000355.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000356.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000357.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000358.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000359.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000360.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000361.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000362.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000363.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000364.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000365.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000366.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000367.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000368.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000369.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000370.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000371.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000372.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000373.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000374.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000375.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000376.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000377.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000378.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000379.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000380.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000381.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000382.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000383.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000384.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000385.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000386.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000387.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000388.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000389.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000390.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000391.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000392.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000393.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000394.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000395.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000396.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000397.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000398.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000399.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000400.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000401.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000402.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000403.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000404.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000405.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000406.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000407.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000408.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000409.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000410.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000411.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000412.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000413.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000414.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000415.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000416.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000417.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000418.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000419.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000420.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000421.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000422.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000423.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000424.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000425.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000426.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000427.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000428.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000429.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000430.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000431.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000432.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000433.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000434.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000435.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000436.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000437.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000438.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000439.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000440.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000441.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000442.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000443.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000444.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000445.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000446.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000447.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000448.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000449.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000450.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000451.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000452.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000453.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000454.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000455.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000456.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000457.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000458.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000459.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000460.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000461.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000462.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000463.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000464.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000465.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000466.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000467.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000468.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000469.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000470.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000471.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000472.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000473.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000474.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000475.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000476.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000477.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000478.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000479.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000480.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000481.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000482.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000483.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000484.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000485.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000486.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000487.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000488.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000489.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000490.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000491.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000492.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000493.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000494.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000495.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000496.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000497.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000498.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000499.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000500.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000501.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000502.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000503.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000504.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000505.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000506.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000507.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000508.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000509.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000510.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000511.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000512.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000513.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000514.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000515.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000516.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000517.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000518.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000519.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000520.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000521.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000522.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000523.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000524.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000525.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000526.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000527.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000528.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000529.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000530.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000531.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000532.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000533.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000534.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000535.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000536.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000537.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000538.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000539.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000540.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000541.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000542.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000543.png", "low_idx": 83}, {"high_idx": 11, "image_name": "000000544.png", "low_idx": 83}, {"high_idx": 11, "image_name": "000000545.png", "low_idx": 83}, {"high_idx": 11, "image_name": "000000546.png", "low_idx": 83}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|14|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [-2.2882316, -2.2882316, 14.141448, 14.141448, 3.602651356, 3.602651356]], "coordinateReceptacleObjectId": ["DiningTable", [-1.86, -1.86, 12.012, 12.012, 0.024, 0.024]], "forceVisible": true, "objectId": "ButterKnife|-00.57|+00.90|+03.54"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|10|1|60"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-2.288243056, -2.288243056, 10.12330244, 10.12330244, 3.8313868, 3.8313868]], "forceVisible": true, "objectId": "Bread|-00.57|+00.96|+02.53"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|11|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [-2.2882316, -2.2882316, 14.141448, 14.141448, 3.602651356, 3.602651356]], "coordinateReceptacleObjectId": ["DiningTable", [-1.86, -1.86, 12.012, 12.012, 0.024, 0.024]], "forceVisible": true, "objectId": "ButterKnife|-00.57|+00.90|+03.54", "receptacleObjectId": "DiningTable|-00.47|+00.01|+03.00"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-4|10|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-2.288243056, -2.288243056, 10.12330244, 10.12330244, 3.8313868, 3.8313868]], "coordinateReceptacleObjectId": ["DiningTable", [-1.86, -1.86, 12.012, 12.012, 0.024, 0.024]], "forceVisible": true, "objectId": "Bread|-00.57|+00.96|+02.53|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-3|5|2|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-1.436, -1.436, 0.896, 0.896, 3.6564356, 3.6564356]], "forceVisible": true, "objectId": "Microwave|-00.36|+00.91|+00.22"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-8|18|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "fridge"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-2.288243056, -2.288243056, 10.12330244, 10.12330244, 3.8313868, 3.8313868]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-11.208, -11.208, 17.708, 17.708, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-00.57|+00.96|+02.53|BreadSliced_1", "receptacleObjectId": "<PERSON><PERSON>|-02.80|+00.00|+04.43"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|-00.57|+00.90|+03.54"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [134, 107, 144, 179], "mask": [[31938, 4], [32238, 4], [32538, 5], [32838, 5], [33138, 5], [33438, 5], [33738, 6], [34038, 6], [34338, 6], [34638, 6], [34938, 7], [35237, 8], [35537, 8], [35837, 8], [36137, 8], [36437, 8], [36737, 7], [37037, 7], [37337, 7], [37637, 7], [37937, 7], [38237, 7], [38537, 7], [38837, 7], [39137, 6], [39437, 6], [39737, 6], [40037, 6], [40337, 5], [40637, 5], [40937, 5], [41237, 5], [41537, 5], [41837, 4], [42137, 4], [42437, 4], [42737, 4], [43037, 4], [43337, 4], [43636, 5], [43936, 5], [44236, 5], [44536, 5], [44836, 4], [45136, 4], [45436, 4], [45736, 5], [46036, 5], [46335, 6], [46635, 6], [46935, 6], [47235, 6], [47535, 6], [47835, 6], [48135, 6], [48434, 7], [48734, 7], [49034, 7], [49334, 7], [49634, 7], [49934, 7], [50234, 7], [50534, 7], [50834, 8], [51134, 7], [51434, 7], [51734, 7], [52034, 7], [52334, 7], [52634, 7], [52934, 7], [53235, 5], [53535, 4]], "point": [139, 142]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-00.57|+00.96|+02.53"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [88, 107, 191, 172], "mask": [[31931, 14], [32225, 27], [32521, 35], [32817, 42], [33113, 49], [33410, 54], [33707, 60], [34005, 65], [34303, 68], [34601, 72], [34901, 74], [35200, 77], [35500, 78], [35796, 83], [36095, 86], [36394, 88], [36694, 89], [36993, 91], [37292, 93], [37592, 94], [37891, 96], [38191, 96], [38490, 98], [38790, 99], [39089, 100], [39389, 101], [39689, 101], [39989, 102], [40289, 102], [40588, 104], [40888, 104], [41188, 104], [41488, 104], [41788, 104], [42088, 104], [42388, 104], [42688, 104], [42988, 104], [43288, 103], [43588, 103], [43889, 102], [44189, 102], [44490, 100], [44790, 100], [45091, 98], [45392, 97], [45693, 95], [45994, 93], [46294, 92], [46595, 90], [46896, 89], [47197, 87], [47498, 85], [47799, 83], [48101, 79], [48402, 77], [48704, 74], [49006, 71], [49309, 66], [49611, 62], [49913, 57], [50216, 52], [50520, 45], [50824, 37], [51127, 30], [51436, 16]], "point": [139, 138]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|-00.57|+00.90|+03.54", "placeStationary": true, "receptacleObjectId": "DiningTable|-00.47|+00.01|+03.00"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 3, 299, 192], "mask": [[600, 105], [713, 171], [900, 102], [1016, 126], [1145, 40], [1200, 101], [1317, 124], [1446, 39], [1500, 99], [1618, 124], [1746, 40], [1800, 99], [1918, 124], [2046, 40], [2101, 97], [2219, 123], [2347, 40], [2401, 97], [2519, 124], [2647, 40], [2700, 98], [2818, 125], [2947, 41], [3000, 98], [3118, 126], [3247, 41], [3300, 99], [3417, 127], [3548, 40], [3600, 100], [3715, 130], [3848, 41], [3900, 102], [4013, 132], [4148, 41], [4200, 106], [4309, 137], [4448, 42], [4500, 247], [4749, 41], [4800, 247], [5049, 42], [5100, 248], [5349, 42], [5400, 248], [5650, 42], [5700, 249], [5953, 39], [6000, 249], [6253, 39], [6300, 249], [6554, 39], [6600, 249], [6854, 39], [6900, 249], [7154, 40], [7200, 67], [7325, 124], [7455, 39], [7500, 64], [7627, 121], [7755, 40], [7800, 62], [7930, 117], [8055, 40], [8100, 60], [8231, 116], [8356, 40], [8400, 59], [8532, 115], [8656, 40], [8700, 57], [8833, 114], [8957, 39], [9000, 56], [9134, 114], [9249, 1], [9251, 1], [9257, 40], [9300, 56], [9434, 115], [9550, 1], [9552, 1], [9557, 40], [9600, 55], [9735, 115], [9851, 1], [9853, 1], [9858, 40], [9900, 54], [10035, 116], [10152, 1], [10158, 40], [10200, 54], [10335, 120], [10458, 41], [10500, 53], [10635, 120], [10758, 41], [10800, 53], [10935, 121], [11058, 95], [11235, 121], [11358, 95], [11535, 122], [11659, 93], [11835, 122], [11959, 93], [12135, 122], [12259, 93], [12435, 122], [12560, 91], [12735, 123], [12860, 91], [13035, 123], [13160, 91], [13335, 123], [13460, 91], [13635, 123], [13761, 89], [13935, 48], [13987, 72], [14061, 89], [14235, 37], [14288, 71], [14361, 89], [14535, 30], [14588, 71], [14662, 87], [14835, 24], [14888, 71], [14962, 87], [15134, 14], [15188, 72], [15262, 87], [15434, 4], [15488, 72], [15562, 87], [15734, 3], [15788, 14], [15819, 1], [15821, 39], [15863, 85], [16034, 3], [16088, 8], [16125, 35], [16163, 85], [16387, 6], [16431, 29], [16464, 84], [16680, 9], [16734, 23], [16768, 79], [16971, 14], [17039, 4], [17044, 11], [17070, 77], [17264, 17], [17341, 1], [17347, 8], [17371, 76], [17558, 21], [17649, 6], [17672, 75], [17837, 1], [17852, 25], [17951, 5], [17973, 73], [18134, 4], [18146, 29], [18253, 3], [18273, 73], [18434, 40], [18554, 3], [18574, 72], [18734, 39], [18855, 2], [18874, 71], [19034, 38], [19156, 1], [19175, 70], [19334, 37], [19457, 1], [19475, 70], [19634, 37], [19757, 1], [19776, 69], [19934, 36], [20058, 1], [20076, 68], [20234, 36], [20360, 1], [20375, 69], [20534, 35], [20661, 83], [20833, 36], [20962, 82], [21133, 35], [21263, 81], [21433, 35], [21563, 81], [21732, 36], [21864, 80], [22032, 36], [22164, 81], [22332, 35], [22465, 80], [22631, 36], [22765, 81], [22931, 36], [23066, 81], [23231, 36], [23366, 82], [23531, 36], [23666, 83], [23830, 37], [23966, 84], [24130, 37], [24266, 85], [24430, 38], [24567, 85], [24729, 39], [24867, 86], [25029, 40], [25167, 87], [25329, 40], [25467, 88], [25628, 42], [25766, 90], [25928, 42], [26066, 91], [26227, 44], [26365, 93], [26526, 46], [26665, 95], [26825, 48], [26964, 97], [27124, 51], [27263, 100], [27423, 53], [27563, 102], [27722, 55], [27862, 105], [28020, 59], [28161, 109], [28318, 63], [28457, 1], [28460, 115], [28614, 69], [28755, 230], [29051, 1], [29054, 233], [29351, 240], [29650, 244], [29948, 250], [30242, 1], [30245, 257], [30540, 267], [30826, 1], [30834, 7866], [38935, 10], [39234, 10], [39534, 10], [39833, 10], [40133, 10], [40432, 10], [40732, 9], [41031, 10], [41331, 9], [41630, 10], [41930, 9], [42229, 10], [42529, 9], [42828, 10], [43128, 9], [43427, 9], [43727, 9], [44026, 9], [44326, 9], [44625, 9], [44925, 9], [45224, 9], [45524, 9], [45823, 9], [46123, 8], [46422, 9], [46722, 8], [47021, 9], [47321, 8], [47620, 9], [47700, 1], [47920, 8], [48000, 2], [48219, 9], [48300, 3], [48519, 8], [48600, 4], [48818, 8], [48900, 5], [49118, 8], [49200, 6], [49417, 8], [49500, 7], [49717, 8], [49800, 8], [50016, 8], [50101, 9], [50316, 8], [50402, 9], [50615, 8], [50703, 9], [50915, 8], [51004, 9], [51214, 8], [51305, 9], [51514, 7], [51606, 9], [51813, 8], [51907, 9], [52113, 7], [52209, 8], [52412, 8], [52510, 8], [52712, 7], [52811, 8], [53011, 8], [53112, 8], [53311, 7], [53413, 8], [53610, 8], [53714, 8], [53910, 7], [54015, 8], [54209, 7], [54316, 8], [54509, 7], [54618, 7], [54808, 7], [54919, 8], [55108, 7], [55220, 8], [55407, 7], [55521, 8], [55707, 7], [55822, 8], [56006, 7], [56123, 8], [56305, 8], [56424, 8], [56605, 8], [56725, 8], [56904, 8], [57026, 7], [57205, 6], [57327, 4], [57506, 4]], "point": [149, 96]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.57|+00.96|+02.53|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [140, 107, 150, 172], "mask": [[31940, 8], [32240, 9], [32540, 9], [32840, 9], [33140, 9], [33440, 9], [33740, 9], [34040, 9], [34340, 9], [34640, 9], [34940, 9], [35240, 10], [35540, 10], [35840, 10], [36140, 10], [36440, 10], [36740, 10], [37040, 10], [37340, 10], [37640, 10], [37940, 10], [38240, 10], [38540, 10], [38840, 10], [39140, 10], [39440, 10], [39740, 10], [40040, 10], [40340, 10], [40640, 10], [40940, 10], [41240, 10], [41540, 10], [41840, 10], [42140, 10], [42440, 10], [42740, 11], [43040, 11], [43340, 11], [43640, 11], [43940, 11], [44241, 10], [44541, 10], [44841, 10], [45141, 10], [45441, 10], [45741, 10], [46041, 10], [46341, 10], [46641, 10], [46941, 10], [47241, 10], [47541, 10], [47841, 10], [48141, 10], [48441, 10], [48741, 10], [49041, 10], [49341, 10], [49641, 10], [49941, 10], [50241, 10], [50541, 10], [50841, 10], [51141, 10], [51441, 8]], "point": [145, 138]}}, "high_idx": 7}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.57|+00.96|+02.53|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 47], [39961, 72], [40200, 44], [40263, 70], [40500, 42], [40565, 68], [40800, 41], [40866, 67], [41100, 40], [41168, 65], [41400, 40], [41471, 62], [41700, 34], [41735, 2], [41777, 56], [42000, 32], [42078, 55], [42300, 30], [42379, 54], [42600, 29], [42680, 53], [42900, 28], [42981, 52], [43200, 28], [43281, 52], [43500, 27], [43582, 51], [43800, 27], [43882, 51], [44100, 27], [44182, 51], [44400, 26], [44482, 51], [44700, 26], [44782, 51], [45000, 27], [45081, 51], [45300, 27], [45380, 52], [45600, 29], [45678, 54], [45900, 31], [45972, 60], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-1.436, -1.436, 0.896, 0.896, 3.6564356, 3.6564356]], "forceVisible": true, "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-1.436, -1.436, 0.896, 0.896, 3.6564356, 3.6564356]], "forceVisible": true, "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.57|+00.96|+02.53|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [26, 134, 81, 154], "mask": [[39947, 14], [40244, 19], [40542, 23], [40841, 25], [41140, 28], [41440, 31], [41734, 1], [41737, 40], [42032, 46], [42330, 49], [42629, 51], [42928, 53], [43228, 53], [43527, 55], [43827, 55], [44127, 55], [44426, 56], [44726, 56], [45027, 54], [45327, 53], [45629, 49], [45931, 41]], "point": [53, 143]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.80|+00.00|+04.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 220], "mask": [[0, 26399], [26400, 299], [26700, 298], [27000, 298], [27300, 297], [27600, 297], [27900, 296], [28200, 295], [28500, 295], [28800, 293], [29100, 293], [29400, 292], [29700, 292], [30000, 291], [30300, 290], [30600, 290], [30900, 286], [31200, 284], [31500, 283], [31800, 282], [32100, 282], [32400, 281], [32700, 280], [33000, 280], [33300, 279], [33600, 278], [33900, 278], [34200, 277], [34500, 276], [34800, 276], [35100, 275], [35400, 274], [35700, 273], [36000, 273], [36300, 272], [36600, 271], [36900, 271], [37200, 270], [37500, 269], [37800, 269], [38100, 268], [38400, 267], [38700, 267], [39000, 266], [39300, 265], [39600, 265], [39900, 264], [40200, 263], [40500, 263], [40800, 262], [41100, 261], [41400, 260], [41700, 260], [42001, 258], [42302, 256], [42603, 255], [42904, 253], [43204, 252], [43505, 251], [43806, 249], [44107, 247], [44408, 246], [44709, 244], [45010, 242], [45311, 241], [45612, 239], [45913, 237], [46214, 236], [46515, 234], [46816, 232], [47117, 230], [47418, 229], [47719, 227], [48019, 226], [48320, 225], [48621, 223], [48922, 221], [49223, 220], [49524, 218], [49825, 216], [50126, 215], [50427, 213], [50728, 211], [51029, 210], [51330, 208], [51631, 206], [51932, 205], [52233, 203], [52534, 201], [52834, 200], [53135, 199], [53436, 197], [53737, 195], [54038, 194], [54339, 192], [54640, 190], [54941, 189], [55242, 187], [55543, 185], [55844, 184], [56145, 182], [56446, 180], [56747, 179], [57048, 177], [57349, 175], [57649, 175], [57950, 173], [58251, 171], [58552, 169], [58853, 168], [59154, 166], [59455, 164], [59756, 163], [60057, 161], [60358, 159], [60659, 158], [60960, 156], [61261, 154], [61562, 153], [61862, 153], [62163, 152], [62463, 151], [62763, 151], [63063, 150], [63364, 149], [63666, 145], [63970, 136], [64274, 128], [64579, 118], [64885, 106], [65192, 92], [65501, 73], [65812, 52]], "point": [149, 109]}}, "high_idx": 11}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.57|+00.96|+02.53|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.80|+00.00|+04.43"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 14699], [14700, 298], [15000, 298], [15300, 297], [15600, 297], [15900, 296], [16200, 295], [16500, 295], [16800, 294], [17100, 294], [17400, 293], [17700, 292], [18000, 292], [18300, 291], [18600, 291], [18900, 290], [19200, 289], [19500, 289], [19800, 288], [20100, 288], [20400, 287], [20700, 286], [21000, 286], [21300, 285], [21600, 285], [21900, 284], [22200, 284], [22500, 283], [22800, 282], [23100, 282], [23400, 281], [23700, 281], [24000, 280], [24300, 279], [24600, 279], [24900, 278], [25200, 278], [25500, 277], [25800, 276], [26100, 276], [26400, 275], [26700, 275], [27000, 274], [27300, 273], [27600, 273], [27900, 272], [28200, 79], [28293, 179], [28500, 80], [28593, 178], [28800, 82], [28892, 179], [29100, 85], [29191, 179], [29400, 269], [29700, 269], [30000, 268], [30300, 268], [30600, 267], [30900, 266], [31200, 266], [31500, 265], [31800, 265], [32100, 264], [32400, 263], [32700, 263], [33000, 262], [33300, 262], [33600, 261], [33900, 260], [34200, 260], [34500, 259], [34800, 259], [35100, 258], [35400, 257], [35701, 256], [36002, 254], [36300, 1], [36303, 253], [36600, 2], [36604, 251], [36900, 3], [36905, 250], [37200, 3], [37205, 249], [37500, 4], [37506, 247], [37800, 5], [37807, 246], [38100, 6], [38108, 244], [38400, 7], [38409, 243], [38700, 8], [38709, 242], [39000, 8], [39010, 240], [39300, 9], [39311, 239], [39600, 10], [39612, 237], [39900, 11], [39913, 236], [40200, 12], [40214, 234], [40500, 13], [40514, 111], [40651, 19], [40689, 58], [40800, 13], [40815, 108], [40953, 17], [40989, 58], [41100, 14], [41116, 106], [41254, 15], [41290, 56], [41400, 15], [41417, 104], [41555, 14], [41591, 55], [41700, 16], [41718, 102], [41856, 12], [41891, 54], [42000, 17], [42019, 100], [42156, 12], [42191, 53], [42300, 18], [42319, 100], [42457, 11], [42491, 53], [42600, 18], [42620, 98], [42757, 11], [42791, 52], [42900, 19], [42921, 96], [43058, 10], [43091, 52], [43200, 20], [43222, 95], [43358, 10], [43391, 51], [43500, 21], [43523, 93], [43659, 9], [43691, 50], [43800, 22], [43823, 93], [43959, 9], [43990, 51], [44100, 23], [44124, 92], [44259, 10], [44290, 50], [44400, 23], [44425, 91], [44559, 10], [44590, 50], [44700, 24], [44726, 90], [44859, 10], [44889, 50], [45000, 25], [45027, 88], [45159, 10], [45188, 51], [45300, 26], [45328, 88], [45459, 11], [45488, 50], [45600, 27], [45628, 88], [45759, 11], [45787, 50], [45900, 28], [45929, 87], [46059, 11], [46086, 51], [46200, 28], [46230, 86], [46359, 10], [46385, 51], [46500, 29], [46531, 85], [46658, 11], [46684, 52], [46800, 30], [46832, 84], [46958, 11], [46985, 50], [47100, 31], [47133, 84], [47257, 12], [47285, 49], [47400, 32], [47433, 84], [47557, 12], [47585, 49], [47700, 33], [47734, 84], [47856, 13], [47885, 48], [48000, 33], [48035, 83], [48156, 14], [48184, 49], [48300, 34], [48336, 83], [48455, 16], [48483, 49], [48600, 35], [48637, 83], [48754, 18], [48782, 49], [48900, 36], [48937, 84], [49053, 21], [49081, 50], [49200, 37], [49238, 84], [49352, 78], [49500, 38], [49539, 84], [49650, 80], [49800, 38], [49840, 85], [49949, 80], [50100, 39], [50141, 86], [50246, 82], [50400, 40], [50442, 88], [50544, 84], [50700, 41], [50742, 94], [50838, 89], [51000, 42], [51043, 184], [51300, 43], [51344, 182], [51600, 43], [51645, 181], [51900, 44], [51946, 179], [52200, 45], [52247, 177], [52500, 46], [52547, 177], [52800, 47], [52848, 175], [53100, 48], [53149, 174], [53400, 48], [53450, 172], [53700, 49], [53751, 170], [54000, 50], [54051, 170], [54300, 51], [54352, 168], [54600, 52], [54653, 167], [54900, 53], [54954, 165], [55200, 53], [55255, 163], [55500, 54], [55556, 162], [55800, 55], [55856, 161], [56100, 56], [56157, 160], [56400, 57], [56458, 158], [56700, 58], [56759, 156], [57000, 58], [57060, 155], [57300, 59], [57361, 153], [57600, 60], [57661, 153], [57900, 61], [57962, 151], [58200, 62], [58263, 149], [58500, 212], [58800, 211], [59100, 211], [59400, 211], [59700, 211], [60000, 212], [60300, 212], [60600, 212], [60900, 212], [61200, 212], [61500, 212], [61800, 212], [62100, 212], [62400, 213], [62700, 213], [63000, 212], [63300, 63], [63364, 148], [63600, 62], [63666, 145], [63900, 62], [63970, 136], [64200, 62], [64274, 128], [64500, 62], [64579, 118], [64800, 62], [64885, 106], [65100, 61], [65192, 92], [65400, 61], [65501, 73], [65700, 61], [65812, 52], [66000, 61], [66300, 61], [66600, 61], [66900, 60], [67200, 60], [67500, 60], [67800, 60], [68100, 60], [68400, 60], [68700, 59], [69000, 59], [69300, 59], [69600, 59], [69900, 59], [70200, 58], [70500, 58], [70800, 58], [71100, 58], [71400, 58], [71700, 58], [72000, 57], [72300, 57], [72600, 57], [72900, 57], [73200, 57], [73500, 57], [73800, 56], [74100, 56], [74400, 56], [74700, 56], [75000, 56], [75300, 55], [75600, 55], [75900, 55], [76200, 55], [76500, 55], [76800, 55], [77100, 54], [77400, 54], [77700, 54], [78000, 54], [78300, 54], [78600, 53], [78900, 53], [79200, 53], [79500, 53], [79800, 53], [80100, 53], [80400, 52], [80700, 52], [81000, 52], [81300, 52], [81600, 52], [81900, 52], [82200, 51], [82500, 51], [82800, 51], [83100, 51], [83400, 51], [83700, 50], [84000, 50], [84300, 50], [84600, 50], [84900, 50], [85200, 50], [85500, 49], [85800, 49], [86100, 49], [86400, 49], [86700, 49], [87000, 49], [87300, 48], [87600, 48], [87900, 48], [88200, 48], [88500, 48], [88800, 47], [89100, 47], [89400, 47], [89700, 47]], "point": [159, 149]}}, "high_idx": 11}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.80|+00.00|+04.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 111], [187, 225], [483, 234], [773, 255], [1029, 13670], [14700, 298], [15000, 298], [15300, 297], [15600, 297], [15900, 296], [16200, 295], [16500, 295], [16800, 294], [17100, 294], [17400, 293], [17700, 292], [18000, 292], [18300, 291], [18600, 291], [18900, 290], [19200, 289], [19500, 289], [19800, 288], [20100, 288], [20400, 287], [20700, 286], [21000, 286], [21300, 285], [21600, 285], [21900, 284], [22200, 284], [22500, 283], [22800, 282], [23100, 282], [23400, 281], [23700, 281], [24000, 280], [24300, 279], [24600, 279], [24900, 278], [25200, 278], [25500, 277], [25800, 276], [26100, 276], [26400, 275], [26700, 275], [27000, 274], [27300, 273], [27600, 273], [27900, 272], [28200, 79], [28293, 179], [28500, 80], [28593, 178], [28800, 82], [28892, 179], [29100, 85], [29191, 179], [29400, 269], [29700, 269], [30000, 268], [30300, 268], [30600, 267], [30900, 266], [31200, 266], [31500, 265], [31800, 265], [32100, 264], [32400, 263], [32700, 263], [33000, 262], [33300, 262], [33600, 261], [33900, 260], [34200, 260], [34500, 259], [34800, 259], [35100, 258], [35400, 257], [35701, 256], [36002, 254], [36300, 1], [36303, 253], [36600, 2], [36604, 251], [36900, 3], [36905, 250], [37200, 3], [37205, 249], [37500, 4], [37506, 247], [37800, 5], [37807, 246], [38100, 6], [38108, 244], [38400, 7], [38409, 243], [38700, 8], [38709, 242], [39000, 8], [39010, 240], [39300, 9], [39311, 239], [39600, 10], [39612, 237], [39900, 11], [39913, 236], [40200, 12], [40214, 234], [40500, 13], [40514, 111], [40651, 19], [40689, 58], [40800, 13], [40815, 108], [40953, 17], [40989, 58], [41100, 14], [41116, 106], [41254, 15], [41290, 56], [41400, 15], [41417, 104], [41555, 14], [41591, 55], [41700, 16], [41718, 102], [41856, 12], [41891, 54], [42000, 17], [42019, 100], [42156, 12], [42191, 53], [42300, 18], [42319, 100], [42457, 11], [42491, 53], [42600, 18], [42620, 98], [42757, 11], [42791, 52], [42900, 19], [42921, 96], [43058, 10], [43091, 52], [43200, 20], [43222, 95], [43358, 10], [43391, 51], [43500, 21], [43523, 93], [43659, 9], [43691, 50], [43800, 22], [43823, 93], [43959, 9], [43990, 51], [44100, 23], [44124, 92], [44259, 10], [44290, 50], [44400, 23], [44425, 91], [44559, 10], [44590, 50], [44700, 24], [44726, 90], [44859, 10], [44889, 50], [45000, 25], [45027, 88], [45159, 10], [45188, 51], [45300, 26], [45328, 88], [45459, 11], [45488, 50], [45600, 27], [45628, 88], [45759, 11], [45787, 50], [45900, 28], [45929, 87], [46059, 11], [46086, 51], [46200, 28], [46230, 86], [46359, 10], [46385, 51], [46500, 29], [46531, 85], [46658, 11], [46684, 52], [46800, 30], [46832, 84], [46958, 11], [46985, 50], [47100, 31], [47133, 84], [47257, 12], [47285, 49], [47400, 32], [47433, 84], [47557, 12], [47585, 49], [47700, 33], [47734, 84], [47856, 13], [47885, 48], [48000, 33], [48035, 83], [48156, 14], [48184, 49], [48300, 34], [48336, 83], [48455, 16], [48483, 49], [48600, 35], [48637, 83], [48754, 18], [48782, 49], [48900, 36], [48937, 84], [49053, 21], [49081, 50], [49200, 37], [49238, 84], [49352, 78], [49500, 38], [49539, 84], [49650, 80], [49800, 38], [49840, 85], [49949, 80], [50100, 39], [50141, 86], [50246, 82], [50400, 40], [50442, 88], [50544, 84], [50700, 41], [50742, 94], [50838, 89], [51000, 42], [51043, 184], [51300, 43], [51344, 182], [51600, 43], [51645, 181], [51900, 44], [51946, 179], [52200, 45], [52247, 177], [52500, 46], [52547, 177], [52800, 47], [52848, 175], [53100, 48], [53149, 174], [53400, 48], [53450, 172], [53700, 49], [53751, 170], [54000, 50], [54051, 170], [54300, 51], [54352, 168], [54600, 52], [54653, 167], [54900, 53], [54954, 165], [55200, 53], [55255, 163], [55500, 54], [55556, 162], [55800, 55], [55856, 161], [56100, 56], [56157, 160], [56400, 57], [56458, 158], [56700, 58], [56759, 156], [57000, 58], [57060, 155], [57300, 59], [57361, 153], [57600, 60], [57661, 153], [57900, 61], [57962, 151], [58200, 62], [58263, 149], [58500, 212], [58800, 211], [59100, 211], [59400, 211], [59700, 211], [60000, 212], [60300, 212], [60600, 212], [60900, 212], [61200, 212], [61500, 212], [61800, 212], [62100, 212], [62400, 213], [62700, 213], [63000, 212], [63300, 63], [63364, 148], [63600, 62], [63666, 145], [63900, 62], [63970, 136], [64200, 62], [64274, 128], [64500, 62], [64579, 118], [64800, 62], [64885, 106], [65100, 61], [65192, 92], [65400, 61], [65501, 73], [65700, 61], [65812, 52], [66000, 61], [66300, 61], [66600, 61], [66900, 60], [67200, 60], [67500, 60], [67800, 60], [68100, 60], [68400, 60], [68700, 59], [69000, 59], [69300, 59], [69600, 59], [69900, 59], [70200, 58], [70500, 58], [70800, 58], [71100, 58], [71400, 58], [71700, 58], [72000, 57], [72300, 57], [72600, 57], [72900, 57], [73200, 57], [73500, 57], [73800, 56], [74100, 56], [74400, 56], [74700, 56], [75000, 56], [75300, 55], [75600, 55], [75900, 55], [76200, 55], [76500, 55], [76800, 55], [77100, 54], [77400, 54], [77700, 54], [78000, 54], [78300, 54], [78600, 53], [78900, 53], [79200, 53], [79500, 53], [79800, 53], [80100, 53], [80400, 52], [80700, 52], [81000, 52], [81300, 52], [81600, 52], [81900, 52], [82200, 51], [82500, 51], [82800, 51], [83100, 51], [83400, 51], [83700, 50], [84000, 50], [84300, 50], [84600, 50], [84900, 50], [85200, 50], [85500, 49], [85800, 49], [86100, 49], [86400, 49], [86700, 49], [87000, 49], [87300, 48], [87600, 48], [87900, 48], [88200, 48], [88500, 48], [88800, 47], [89100, 47], [89400, 47], [89700, 47]], "point": [159, 149]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan26", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.0, "y": 0.901591063, "z": 1.5}, "object_poses": [{"objectName": "ButterKnife_19505874", "position": {"x": -2.628046, "y": 0.9355421, "z": 3.673572}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_19505874", "position": {"x": -2.03741145, "y": 0.928248465, "z": 0.348999977}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_5216d872", "position": {"x": -0.314754725, "y": 0.9156206, "z": 2.32991719}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Spoon_11e3f5dc", "position": {"x": -1.353308, "y": 0.798715353, "z": 0.281820238}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Pot_c7fbe144", "position": {"x": -0.572059631, "y": 0.9002281, "z": 2.93264}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Cup_c473c604", "position": {"x": -2.69151688, "y": 0.298772573, "z": 4.64971161}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_d1c1b4b5", "position": {"x": -2.73827887, "y": 0.303408921, "z": 4.427}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_57182a42", "position": {"x": -0.139269322, "y": 1.77323067, "z": 0.160195857}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9504375a", "position": {"x": -2.847339, "y": 1.18295324, "z": 4.3527627}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_c5422882", "position": {"x": -0.433944255, "y": 0.658710659, "z": 0.245823622}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c5422882", "position": {"x": -1.87820625, "y": 0.658710659, "z": 0.5739484}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_a7306c85", "position": {"x": -1.00791872, "y": 0.7908945, "z": 0.3371}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_5deff24a", "position": {"x": -2.58349657, "y": 0.95696336, "z": 2.30972886}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Egg_05581a9a", "position": {"x": -2.70696, "y": 0.9688633, "z": 3.20964146}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d1c1b4b5", "position": {"x": -2.19144225, "y": 1.77788591, "z": 0.1601962}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_80a3685e", "position": {"x": -0.2289859, "y": 0.9011693, "z": 2.329917}, "rotation": {"x": 0.0, "y": 180.000153, "z": 0.0}}, {"objectName": "Bread_4b633365", "position": {"x": -0.572060764, "y": 0.9578467, "z": 2.53082561}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Apple_5deff24a", "position": {"x": -2.847339, "y": 0.688914239, "z": 4.204288}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_8490bc5f", "position": {"x": -0.657827258, "y": 0.980303168, "z": 3.334455}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Spatula_5216d872", "position": {"x": -2.99566031, "y": 0.9156206, "z": 1.91336834}, "rotation": {"x": 0.0, "y": 90.00016, "z": 0.0}}, {"objectName": "Knife_6c866e4b", "position": {"x": -0.4878505, "y": 0.6880228, "z": 0.464573443}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_bbca86d2", "position": {"x": -2.78957844, "y": 0.9268664, "z": 2.177609}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Tomato_d8a5b3b1", "position": {"x": -2.78957915, "y": 0.9644336, "z": 1.91336787}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Cup_c473c604", "position": {"x": -0.143215269, "y": 0.9003033, "z": 2.93263865}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "SaltShaker_38b3b13a", "position": {"x": -2.78957725, "y": 0.896926939, "z": 2.57397079}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "PepperShaker_c5422882", "position": {"x": -0.802897036, "y": 1.76987314, "z": 0.2970712}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_19505874", "position": {"x": -0.5720579, "y": 0.900662839, "z": 3.535362}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "SoapBottle_57182a42", "position": {"x": -2.785874, "y": 0.9351635, "z": 3.30242777}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pan_b3245c00", "position": {"x": -2.58349633, "y": 0.9002302, "z": 2.44184947}, "rotation": {"x": 0.0, "y": 180.000153, "z": 0.0}}, {"objectName": "DishSponge_50b7389e", "position": {"x": -2.480455, "y": 0.900185, "z": 2.70609045}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Pot_c7fbe144", "position": {"x": -0.5359, "y": 0.928928852, "z": 4.2742}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_11e3f5dc", "position": {"x": -2.03741145, "y": 0.9293358, "z": 0.433551341}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_a7306c85", "position": {"x": -0.921571434, "y": 0.7908945, "z": 0.39237985}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Bowl_9504375a", "position": {"x": -1.35898626, "y": 1.7701875, "z": 0.2058212}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 149759926, "scene_num": 26}, "task_id": "trial_T20190908_131453_332474", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1K5ILJMG439M5_354P56DE9NUBNCY6EEZX9YXXBWT7SV", "high_descs": ["Turn around and move to face the white table where the black toaster is.", "Pick up the knife from the table.", "Bring the knife with you and go to the right side of the table to where the bread is.", "Use the knife to slice the bread.", "Move to the left with the knife and face the pot on the table.", "Put the knife in the pot.", "Go back to the right where the bread was sliced.", "Grab a slice of bread from the table.", "Bring the bread over to the microwave on the left of the sink.", "Put the bread in the microwave, cook it, then take it out again.", "Bring the bread over to the white fridge.", "Put the bread in the fridge and shut the door."], "task_desc": "Put warm bread in the fridge.", "votes": [1, 1]}, {"assignment_id": "ARB8SJAWXUWTD_39LNWE0K4XNOR976MAYUFO5UA9QIUI", "high_descs": ["Go to the toaster", "Pick up the knife in front of the toaster", "Go to the bread", "Cut the bread", "Turn around and face the table", "Put the knife in the pot", "Go to the bread", "Pick up a slice of bread", "Go to the microwave", "Heat the bread", "Go to the fridge", "Put the bread in the fridge"], "task_desc": "Put a heated slice of bread in the fridge", "votes": [1, 1]}, {"assignment_id": "A1OWHPMKE7YAGL_3F6KKYWMNESUO87BXVLXAWXNAB2DN2", "high_descs": ["Turn around, go across the room to the table on the right where the toaster is. ", "Pick up the butter knife that is on the table in front of the toaster.", "Turn to the right, go to the end of the table where the bread is.", "Cut the bread into slices.", "Turn around, move to the right, turn around and back up to face the pot that is on the table.", "Put the knife into the pot that is on the table.", "Turn to the right, go back to where the bread is on the table.", "Pick up a slice of the bread.", "Turn to the right, go to the microwave that is across the room to the left of the sink.", "Put the bread inside the microwave and turn it on, then remove the cooked bread from the microwave.", "Turn around, go to the wall, hang a left and go to the fridge.", "Put the cooked bread inside the fridge."], "task_desc": "Put cooked bread inside the fridge.", "votes": [1, 1]}]}}