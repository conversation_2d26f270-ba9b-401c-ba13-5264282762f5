{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 43}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-1|4|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-4.50512, -4.50512, 3.9947524, 3.9947524, 3.8922532, 3.8922532]], "coordinateReceptacleObjectId": ["CounterTop", [-3.888, -3.888, 3.684, 3.684, 3.8936, 3.8936]], "forceVisible": true, "objectId": "Egg|-01.13|+00.97|+01.00"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|3|9|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-4.50512, -4.50512, 3.9947524, 3.9947524, 3.8922532, 3.8922532]], "coordinateReceptacleObjectId": ["CounterTop", [6.02, 6.02, 9.24, 9.24, 3.8936, 3.8936]], "forceVisible": true, "objectId": "Egg|-01.13|+00.97|+01.00", "receptacleObjectId": "CounterTop|+01.51|+00.97|+02.31"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.13|+00.97|+01.00"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [143, 96, 155, 113], "mask": [[28648, 3], [28946, 7], [29246, 8], [29545, 9], [29844, 11], [30144, 11], [30444, 12], [30743, 13], [31043, 13], [31343, 13], [31643, 13], [31943, 13], [32244, 12], [32544, 11], [32845, 10], [33145, 9], [33446, 7], [33748, 3]], "point": [149, 103]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.13|+00.97|+01.00", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 78], [129, 132], [300, 79], [429, 132], [600, 79], [729, 132], [900, 79], [1029, 131], [1200, 79], [1328, 132], [1500, 79], [1628, 132], [1800, 79], [1928, 132], [2100, 80], [2228, 132], [2400, 80], [2528, 132], [2700, 80], [2828, 132], [3000, 80], [3128, 132], [3300, 80], [3428, 132], [3600, 80], [3728, 132], [3900, 81], [4028, 131], [4200, 81], [4328, 131], [4500, 81], [4628, 131], [4800, 81], [4927, 132], [5100, 81], [5227, 132], [5400, 81], [5527, 132], [5700, 82], [5827, 132], [6000, 82], [6127, 132], [6300, 82], [6427, 132], [6600, 82], [6727, 132], [6900, 82], [7027, 131], [7200, 82], [7327, 131], [7500, 83], [7627, 131], [7800, 83], [7927, 131], [8100, 83], [8227, 131], [8400, 83], [8526, 132], [8700, 83], [8826, 132], [9000, 83], [9126, 132], [9300, 84], [9426, 132], [9600, 84], [9726, 132], [9900, 84], [10026, 131], [10200, 84], [10326, 131], [10500, 84], [10626, 131], [10800, 84], [10926, 131], [11100, 85], [11226, 131], [11400, 85], [11526, 131], [11700, 85], [11826, 131], [12000, 85], [12125, 132], [12300, 85], [12425, 132], [12600, 85], [12725, 132], [12900, 86], [13025, 131], [13200, 86], [13325, 131], [13500, 86], [13625, 131], [13800, 86], [13925, 131], [14100, 86], [14225, 131], [14400, 86], [14525, 131], [14700, 87], [14825, 131], [15000, 87], [15125, 131], [15300, 87], [15425, 131], [15600, 87], [15724, 132], [15900, 87], [16024, 131], [16200, 87], [16324, 131], [16500, 88], [16624, 131], [16800, 88], [16924, 131], [17100, 88], [17224, 131], [17400, 88], [17524, 131], [17700, 88], [17824, 131], [18000, 88], [18124, 131], [18300, 89], [18424, 131], [18600, 89], [18724, 131], [18900, 89], [19023, 131], [19200, 89], [19323, 131], [19500, 89], [19623, 131], [19800, 89], [19923, 131], [20100, 90], [20223, 131], [20400, 90], [20523, 131], [20700, 90], [20823, 131], [21000, 90], [21123, 131], [21300, 91], [21423, 131], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 78], [129, 132], [300, 79], [429, 132], [600, 79], [729, 132], [900, 79], [1029, 131], [1200, 79], [1328, 132], [1500, 79], [1628, 132], [1800, 79], [1928, 132], [2100, 80], [2228, 132], [2400, 80], [2528, 132], [2700, 80], [2828, 132], [3000, 80], [3128, 132], [3300, 80], [3428, 132], [3600, 80], [3728, 132], [3900, 81], [4028, 131], [4200, 81], [4328, 131], [4500, 81], [4628, 131], [4800, 81], [4927, 132], [5100, 81], [5227, 132], [5400, 81], [5527, 132], [5700, 82], [5827, 132], [6000, 82], [6127, 132], [6300, 82], [6427, 132], [6600, 82], [6727, 132], [6900, 82], [7027, 131], [7200, 82], [7327, 131], [7500, 83], [7627, 131], [7800, 83], [7927, 131], [8100, 83], [8227, 131], [8400, 83], [8526, 132], [8700, 83], [8826, 132], [9000, 17], [9022, 61], [9126, 132], [9300, 15], [9324, 60], [9426, 132], [9600, 14], [9626, 58], [9726, 132], [9900, 13], [9927, 57], [10026, 131], [10200, 12], [10228, 56], [10326, 131], [10500, 11], [10529, 55], [10626, 131], [10800, 11], [10830, 54], [10926, 131], [11100, 10], [11130, 55], [11226, 131], [11400, 10], [11431, 54], [11526, 131], [11700, 9], [11732, 53], [11826, 131], [12000, 9], [12032, 53], [12125, 132], [12300, 9], [12333, 52], [12425, 132], [12600, 9], [12633, 52], [12725, 132], [12900, 8], [12934, 52], [13025, 131], [13200, 8], [13234, 52], [13325, 131], [13500, 8], [13535, 51], [13625, 131], [13800, 8], [13835, 51], [13925, 131], [14100, 8], [14135, 51], [14225, 131], [14400, 8], [14436, 50], [14525, 131], [14700, 8], [14736, 51], [14825, 131], [15000, 8], [15036, 51], [15125, 131], [15300, 8], [15336, 51], [15425, 131], [15600, 8], [15636, 51], [15724, 132], [15900, 8], [15936, 51], [16024, 131], [16200, 9], [16237, 50], [16324, 131], [16500, 9], [16537, 51], [16624, 131], [16800, 9], [16837, 51], [16924, 131], [17100, 9], [17137, 51], [17224, 131], [17400, 10], [17437, 51], [17524, 131], [17700, 10], [17737, 51], [17824, 131], [18000, 10], [18036, 52], [18124, 131], [18300, 11], [18336, 53], [18424, 131], [18600, 12], [18636, 53], [18724, 131], [18900, 12], [18935, 54], [19023, 131], [19200, 13], [19235, 54], [19323, 131], [19500, 14], [19534, 55], [19623, 131], [19800, 15], [19834, 55], [19923, 131], [20100, 16], [20133, 57], [20223, 131], [20400, 17], [20432, 58], [20523, 131], [20700, 18], [20731, 59], [20823, 131], [21000, 19], [21030, 60], [21123, 131], [21300, 22], [21328, 63], [21423, 131], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.13|+00.97|+01.00"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [8, 31, 36, 72], "mask": [[9017, 5], [9315, 9], [9614, 12], [9913, 14], [10212, 16], [10511, 18], [10811, 19], [11110, 20], [11410, 21], [11709, 23], [12009, 23], [12309, 24], [12609, 24], [12908, 26], [13208, 26], [13508, 27], [13808, 27], [14108, 27], [14408, 28], [14708, 28], [15008, 28], [15308, 28], [15608, 28], [15908, 28], [16209, 28], [16509, 28], [16809, 28], [17109, 28], [17410, 27], [17710, 27], [18010, 26], [18311, 25], [18612, 24], [18912, 23], [19213, 22], [19514, 20], [19815, 19], [20116, 17], [20417, 15], [20718, 13], [21019, 11], [21322, 6]], "point": [22, 50]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 78], [129, 132], [300, 79], [429, 132], [600, 79], [729, 132], [900, 79], [1029, 131], [1200, 79], [1328, 132], [1500, 79], [1628, 132], [1800, 79], [1928, 132], [2100, 80], [2228, 132], [2400, 80], [2528, 132], [2700, 80], [2828, 132], [3000, 80], [3128, 132], [3300, 80], [3428, 132], [3600, 80], [3728, 132], [3900, 81], [4028, 131], [4200, 81], [4328, 131], [4500, 81], [4628, 131], [4800, 81], [4927, 132], [5100, 81], [5227, 132], [5400, 81], [5527, 132], [5700, 82], [5827, 132], [6000, 82], [6127, 132], [6300, 82], [6427, 132], [6600, 82], [6727, 132], [6900, 82], [7027, 131], [7200, 82], [7327, 131], [7500, 83], [7627, 131], [7800, 83], [7927, 131], [8100, 83], [8227, 131], [8400, 83], [8526, 132], [8700, 83], [8826, 132], [9000, 83], [9126, 132], [9300, 84], [9426, 132], [9600, 84], [9726, 132], [9900, 84], [10026, 131], [10200, 84], [10326, 131], [10500, 84], [10626, 131], [10800, 84], [10926, 131], [11100, 85], [11226, 131], [11400, 85], [11526, 131], [11700, 85], [11826, 131], [12000, 85], [12125, 132], [12300, 85], [12425, 132], [12600, 85], [12725, 132], [12900, 86], [13025, 131], [13200, 86], [13325, 131], [13500, 86], [13625, 131], [13800, 86], [13925, 131], [14100, 86], [14225, 131], [14400, 86], [14525, 131], [14700, 87], [14825, 131], [15000, 87], [15125, 131], [15300, 87], [15425, 131], [15600, 87], [15724, 132], [15900, 87], [16024, 131], [16200, 87], [16324, 131], [16500, 88], [16624, 131], [16800, 88], [16924, 131], [17100, 88], [17224, 131], [17400, 88], [17524, 131], [17700, 88], [17824, 131], [18000, 88], [18124, 131], [18300, 89], [18424, 131], [18600, 89], [18724, 131], [18900, 89], [19023, 131], [19200, 89], [19323, 131], [19500, 89], [19623, 131], [19800, 89], [19923, 131], [20100, 90], [20223, 131], [20400, 90], [20523, 131], [20700, 90], [20823, 131], [21000, 90], [21123, 131], [21300, 91], [21423, 131], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.13|+00.97|+01.00", "placeStationary": true, "receptacleObjectId": "CounterTop|+01.51|+00.97|+02.31"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [7, 85, 299, 193], "mask": [[25354, 146], [25654, 146], [25954, 146], [26254, 146], [26554, 146], [26854, 146], [27154, 146], [27454, 146], [27754, 146], [28054, 146], [28354, 146], [28654, 146], [28954, 146], [29254, 146], [29554, 146], [29854, 146], [30154, 146], [30454, 146], [30754, 146], [31054, 146], [31354, 146], [31654, 146], [31954, 146], [32254, 89], [32554, 86], [32854, 84], [33154, 83], [33454, 82], [33754, 81], [34054, 81], [34354, 80], [34654, 80], [34954, 80], [35254, 80], [35554, 80], [35854, 80], [36154, 80], [36340, 2], [36454, 81], [36640, 2], [36754, 81], [36939, 4], [37054, 81], [37239, 5], [37354, 82], [37538, 8], [37654, 82], [37838, 9], [37954, 82], [38137, 12], [38254, 83], [38437, 14], [38554, 83], [38736, 17], [38854, 84], [39036, 19], [39155, 83], [39336, 21], [39455, 83], [39635, 24], [39755, 84], [39935, 26], [40055, 84], [40234, 30], [40355, 84], [40534, 32], [40655, 85], [40833, 36], [40955, 85], [41133, 38], [41255, 85], [41432, 42], [41499, 9], [41555, 86], [41732, 45], [41798, 23], [41854, 87], [42031, 52], [42098, 52], [42154, 88], [42331, 58], [42398, 52], [42454, 88], [42630, 66], [42698, 52], [42754, 88], [42930, 120], [43054, 89], [43229, 121], [43354, 89], [43529, 122], [43654, 89], [43828, 216], [44128, 216], [44427, 217], [44727, 218], [45026, 219], [45326, 220], [45626, 220], [45925, 221], [46225, 222], [46524, 223], [46824, 223], [47123, 225], [47423, 225], [47722, 227], [48022, 227], [48321, 228], [48621, 229], [48920, 230], [49220, 231], [49519, 233], [49819, 234], [50118, 235], [50418, 236], [50717, 238], [51017, 239], [51317, 240], [51616, 243], [51916, 244], [52215, 246], [52515, 248], [52814, 251], [53114, 252], [53413, 256], [53713, 258], [54012, 263], [54312, 267], [54611, 289], [54911, 289], [55210, 290], [55510, 290], [55809, 291], [56109, 291], [56408, 292], [56708, 292], [57008, 292], [57307, 293], [57607, 293]], "point": [154, 139]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan12", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 0.75, "y": 0.9009999, "z": -0.5}, "object_poses": [{"objectName": "Spatula_733b0828", "position": {"x": 1.275738, "y": 0.7618369, "z": 0.9166}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": -1.01497185, "y": 0.6009827, "z": 0.348953784}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.597631, "y": 0.9798608, "z": 1.7985332}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": -0.8771876, "y": 1.53448272, "z": -0.03430243}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.61988139, "y": 1.59505081, "z": 1.59899759}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -0.988837242, "y": 0.8136976, "z": 0.272302568}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -0.804073453, "y": 0.0866650939, "z": 2.31314516}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": 1.42390835, "y": 0.1314235, "z": 2.91251349}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.209405, "y": 0.9342062, "z": 0.765623748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -0.868187, "y": 0.08057672, "z": 0.8450084}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": -0.937683463, "y": 0.6562203, "z": 0.195651367}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.34043908, "y": 1.74606276, "z": 0.270527035}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.9230004, "y": 0.990678251, "z": 2.26549959}, "rotation": {"x": -3.14625759e-05, "y": 302.470337, "z": 4.54345864e-05}}, {"objectName": "Fork_d40bfead", "position": {"x": 1.44873142, "y": 0.747115731, "z": 0.780662537}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -0.8771881, "y": 1.56053746, "z": 0.119000219}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": -1.03728545, "y": 1.94630635, "z": 1.92461956}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.8657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": -0.7236026, "y": 0.7618369, "z": 1.05093741}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": 1.31033671, "y": 0.7685599, "z": 2.5278933}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": 1.30961347, "y": 0.0972995758, "z": 0.373630881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": -1.211005, "y": 0.9871304, "z": 2.234908}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.043155, "y": 0.9342062, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -0.793779969, "y": 0.9379421, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": -0.7106551, "y": 0.9705572, "z": 0.8433119}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": 1.38017225, "y": 1.75122666, "z": 0.0663864}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": -1.11600673, "y": 1.82829392, "z": -0.08169362}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": -1.12628, "y": 0.9730633, "z": 0.9986881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.45544767, "y": 0.0995715857, "z": 0.171664327}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_9f7abbea", "position": {"x": -1.109, "y": 0.9307289, "z": 1.4704}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": 1.42227757, "y": 0.8445722, "z": 1.36970234}, "rotation": {"x": 1.40334191e-14, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": 1.5864712, "y": 1.49912679, "z": 0.918661}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2647625095, "scene_num": 12}, "task_id": "trial_T20190908_215643_953236", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1HKHM4NVAO98H_3PXX5PX6L0PUPSF2AVPMT0PN8L1BAN", "high_descs": ["turn right and walk over to the kitchen counter to the left of the stove on the left", "grab the egg off of the kitchen counter there", "turn around and walk over to the kitchen sink", "place the egg inside of the microwave above the kitchen sink, cook it, then take it back out", "move to the left a bit to face the kitchen counter space to the left of the kitchen sink there", "place the egg down on top of the kitchen counter space in front of the coffee maker"], "task_desc": "place a microwaved egg down on the kitchen counter space", "votes": [1, 1]}, {"assignment_id": "A2871R3LEPWMMK_3Q5ZZ9ZEVR6EHO5ECNEVUNJ0815582", "high_descs": ["Turn right and walk to the counter on the left to the left of the stove.", "Pick up the egg in between the salt shakers near the back of the counter.", "Turn around and walk to the sink.", "Put the egg inside the microwave above the sink, heat it, remove it and close the door.", "Turn around and walk to the counter on the right with the coffee maker.", "Place the heated egg on the counter in front of the coffee maker."], "task_desc": "Place a heated egg on a counter.", "votes": [1, 1]}, {"assignment_id": "A2TUUIV61CR0C7_3U84XHCDIF4B512178VY2FAQ7E54ZP", "high_descs": ["Turn around, walk forward to the counter to the right of the fridge and face it.", "Pick up the egg on the counter to the left of the stove.", "Turn around and walk to the front of the sink.", "Heat the egg in the microwave.", "Turn left and walk to the end of the counter.", "Place the egg on the counter."], "task_desc": "Heat the egg in the microwave and return it to the end of the counter.", "votes": [1, 1]}]}}