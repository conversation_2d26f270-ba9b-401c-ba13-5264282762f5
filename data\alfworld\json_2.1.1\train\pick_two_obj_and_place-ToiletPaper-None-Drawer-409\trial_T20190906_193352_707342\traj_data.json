{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000197.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 37}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "ToiletPaper", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["toiletpaperhanger"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-1|8|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [1.235199688, 1.235199688, 7.64920044, 7.64920044, 2.4624, 2.4624]], "coordinateReceptacleObjectId": ["ToiletPaperHanger", [1.5076, 1.5076, 8.038, 8.038, 2.5956, 2.5956]], "forceVisible": true, "objectId": "ToiletPaper|+00.31|+00.62|+01.91"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|10|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [1.235199688, 1.235199688, 7.64920044, 7.64920044, 2.4624, 2.4624]], "coordinateReceptacleObjectId": ["Drawer", [1.773921372, 1.773921372, 8.7748, 8.7748, 2.498000144, 2.498000144]], "forceVisible": true, "objectId": "ToiletPaper|+00.31|+00.62|+01.91", "receptacleObjectId": "Drawer|+00.44|+00.62|+02.19"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|9|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [1.558960916, 1.558960916, 9.0844822, 9.0844822, 3.2661692, 3.2661692]], "coordinateReceptacleObjectId": ["CounterTop", [1.8339208, 1.8339208, 11.04, 11.04, 3.1583996, 3.1583996]], "forceVisible": true, "objectId": "ToiletPaper|+00.39|+00.82|+02.27"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-2|10|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [1.558960916, 1.558960916, 9.0844822, 9.0844822, 3.2661692, 3.2661692]], "coordinateReceptacleObjectId": ["Drawer", [1.773921372, 1.773921372, 8.7748, 8.7748, 2.498000144, 2.498000144]], "forceVisible": true, "objectId": "ToiletPaper|+00.39|+00.82|+02.27", "receptacleObjectId": "Drawer|+00.44|+00.62|+02.19"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|+00.31|+00.62|+01.91"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [161, 179, 166, 198], "mask": [[53561, 5], [53861, 5], [54161, 5], [54461, 5], [54761, 5], [55061, 5], [55361, 5], [55661, 5], [55961, 5], [56261, 5], [56561, 6], [56861, 6], [57161, 6], [57461, 6], [57761, 6], [58061, 6], [58361, 6], [58661, 6], [58961, 6], [59262, 4]], "point": [163, 187]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+00.44|+00.62|+02.19"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [175, 170, 269, 210], "mask": [[50879, 90], [51179, 91], [51479, 90], [51779, 90], [52079, 89], [52379, 89], [52678, 89], [52978, 89], [53278, 88], [53578, 88], [53878, 87], [54178, 87], [54478, 86], [54778, 86], [55077, 86], [55377, 86], [55677, 85], [55977, 85], [56277, 84], [56577, 84], [56877, 83], [57177, 83], [57476, 83], [57776, 83], [58076, 82], [58376, 82], [58676, 81], [58976, 81], [59276, 80], [59576, 80], [59875, 80], [60175, 80], [60475, 79], [60775, 79], [61075, 78], [61375, 78], [61675, 77], [61975, 77], [62275, 76], [62575, 76], [62875, 75]], "point": [222, 189]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|+00.31|+00.62|+01.91", "placeStationary": true, "receptacleObjectId": "Drawer|+00.44|+00.62|+02.19"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [177, 170, 299, 300], "mask": [[50880, 84], [51180, 84], [51480, 84], [51780, 37], [51823, 42], [52079, 36], [52129, 36], [52379, 34], [52431, 35], [52679, 32], [52733, 33], [52979, 31], [53034, 33], [53279, 31], [53336, 31], [53579, 30], [53637, 30], [53879, 30], [53938, 30], [54179, 30], [54239, 29], [54478, 30], [54539, 30], [54778, 30], [54840, 29], [55078, 29], [55140, 29], [55378, 29], [55441, 29], [55678, 29], [55742, 28], [55978, 29], [56042, 29], [56278, 28], [56342, 29], [56578, 28], [56641, 30], [56877, 29], [56941, 31], [57177, 29], [57241, 31], [57477, 28], [57541, 32], [57777, 28], [57840, 33], [58077, 28], [58140, 33], [58377, 28], [58440, 34], [58677, 27], [58739, 35], [58977, 27], [59039, 36], [59277, 27], [59339, 36], [59577, 27], [59638, 37], [59878, 25], [59938, 38], [60178, 25], [60237, 39], [60478, 25], [60537, 40], [60778, 25], [60837, 40], [61078, 24], [61136, 42], [61378, 24], [61436, 42], [61678, 24], [61735, 43], [61978, 25], [62035, 44], [62278, 25], [62334, 45], [62578, 26], [62634, 46], [62878, 26], [62934, 46], [63179, 25], [63233, 47], [63479, 26], [63533, 48], [63779, 26], [63832, 49], [64079, 27], [64132, 50], [64379, 29], [64432, 50], [64679, 30], [64731, 51], [64979, 32], [65029, 54], [65279, 33], [65327, 56], [65579, 34], [65626, 58], [65879, 43], [65924, 60], [66179, 105], [66479, 106], [66780, 105], [67080, 106], [67380, 106], [67680, 106], [67980, 107], [68280, 107], [68580, 108], [68880, 108], [69180, 108], [69480, 109], [69780, 109], [70081, 109], [70381, 109], [70681, 110], [70981, 110], [71281, 110], [71581, 111], [71881, 111], [72181, 112], [72481, 112], [72781, 112], [73081, 113], [73382, 112], [73682, 113], [73982, 113], [74282, 113], [74582, 114], [74882, 114], [75182, 115], [75482, 115], [75782, 115], [76082, 116], [76382, 116], [76683, 116], [76983, 116], [77283, 116], [77583, 117], [77883, 117], [78183, 117], [78483, 117], [78783, 117], [79083, 117], [79383, 117], [79683, 117], [79983, 117], [80284, 116], [80584, 116], [80884, 116], [81184, 116], [81484, 116], [81784, 116], [82084, 116], [82384, 116], [82684, 116], [82984, 116], [83284, 116], [83585, 115], [83885, 115], [84185, 115], [84485, 115], [84785, 115], [85085, 115], [85385, 115], [85685, 113], [85984, 113], [86284, 112], [86584, 111], [86883, 111], [87183, 109], [87483, 108], [87783, 107], [88082, 107], [88382, 106], [88682, 105], [88982, 103], [89282, 102], [89582, 101], [89882, 100]], "point": [238, 234]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+00.44|+00.62|+02.19"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [177, 170, 299, 300], "mask": [[50880, 84], [51180, 84], [51480, 84], [51780, 37], [51823, 42], [52079, 36], [52129, 36], [52379, 34], [52431, 35], [52679, 32], [52733, 33], [52979, 31], [53034, 33], [53279, 31], [53336, 31], [53579, 30], [53637, 30], [53879, 30], [53938, 30], [54179, 30], [54239, 29], [54478, 30], [54539, 30], [54778, 30], [54840, 29], [55078, 20], [55101, 6], [55140, 29], [55378, 18], [55402, 5], [55441, 29], [55678, 18], [55703, 4], [55742, 28], [55978, 18], [56004, 3], [56042, 29], [56278, 17], [56303, 3], [56342, 29], [56578, 17], [56603, 3], [56641, 30], [56877, 18], [56903, 3], [56941, 31], [57177, 18], [57203, 3], [57241, 31], [57477, 18], [57502, 3], [57541, 32], [57777, 17], [57802, 3], [57840, 33], [58077, 17], [58102, 3], [58140, 33], [58377, 17], [58402, 3], [58440, 34], [58677, 17], [58701, 3], [58739, 35], [58977, 17], [59001, 3], [59039, 36], [59277, 16], [59301, 3], [59339, 36], [59577, 16], [59601, 3], [59638, 37], [59878, 15], [59901, 2], [59938, 38], [60178, 15], [60200, 3], [60237, 39], [60478, 15], [60500, 3], [60537, 40], [60778, 15], [60800, 3], [60837, 40], [61078, 16], [61099, 3], [61136, 42], [61378, 24], [61436, 42], [61678, 24], [61735, 43], [61978, 25], [62035, 44], [62278, 25], [62334, 45], [62578, 26], [62634, 46], [62878, 26], [62934, 46], [63179, 25], [63233, 47], [63479, 26], [63533, 48], [63779, 26], [63832, 49], [64079, 27], [64132, 50], [64379, 29], [64432, 50], [64679, 30], [64731, 51], [64979, 32], [65029, 54], [65279, 33], [65327, 56], [65579, 34], [65626, 58], [65879, 43], [65924, 60], [66179, 105], [66479, 106], [66780, 105], [67080, 106], [67380, 106], [67680, 106], [67980, 107], [68280, 107], [68580, 108], [68880, 108], [69180, 108], [69480, 109], [69780, 109], [70081, 109], [70381, 109], [70681, 110], [70981, 110], [71281, 110], [71581, 111], [71881, 111], [72181, 112], [72481, 112], [72781, 112], [73081, 113], [73382, 112], [73682, 113], [73982, 113], [74282, 113], [74582, 114], [74882, 114], [75182, 115], [75482, 115], [75782, 115], [76082, 116], [76382, 116], [76683, 116], [76983, 116], [77283, 116], [77583, 117], [77883, 117], [78183, 117], [78483, 117], [78783, 117], [79083, 117], [79383, 117], [79683, 117], [79983, 117], [80284, 116], [80584, 116], [80884, 116], [81184, 116], [81484, 116], [81784, 116], [82084, 116], [82384, 116], [82684, 116], [82984, 116], [83284, 116], [83585, 115], [83885, 115], [84185, 115], [84485, 115], [84785, 115], [85085, 115], [85385, 115], [85685, 113], [85984, 113], [86284, 112], [86584, 111], [86883, 111], [87183, 109], [87483, 108], [87783, 107], [88082, 107], [88382, 106], [88682, 105], [88982, 103], [89282, 102], [89582, 101], [89882, 100]], "point": [238, 234]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|+00.39|+00.82|+02.27"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [128, 71, 158, 114], "mask": [[21138, 12], [21436, 16], [21734, 20], [22032, 23], [22331, 26], [22631, 26], [22930, 28], [23229, 29], [23529, 29], [23828, 31], [24129, 30], [24429, 30], [24729, 30], [25029, 30], [25329, 30], [25629, 30], [25929, 30], [26229, 30], [26529, 30], [26829, 30], [27129, 30], [27430, 29], [27730, 29], [28030, 29], [28330, 29], [28630, 29], [28930, 29], [29230, 28], [29530, 28], [29830, 28], [30130, 28], [30431, 27], [30731, 27], [31031, 27], [31331, 27], [31631, 27], [31932, 25], [32232, 25], [32532, 24], [32833, 22], [33134, 21], [33436, 17], [33737, 14], [34039, 10]], "point": [143, 91]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+00.44|+00.62|+02.19"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [175, 170, 269, 210], "mask": [[50879, 90], [51179, 91], [51479, 90], [51779, 90], [52079, 89], [52379, 89], [52678, 89], [52978, 89], [53278, 88], [53578, 88], [53878, 87], [54178, 87], [54478, 86], [54778, 86], [55077, 86], [55377, 86], [55677, 85], [55977, 85], [56277, 84], [56577, 84], [56877, 83], [57177, 83], [57476, 83], [57776, 83], [58076, 82], [58376, 82], [58676, 81], [58976, 81], [59276, 80], [59576, 80], [59875, 80], [60175, 80], [60475, 79], [60775, 79], [61075, 78], [61375, 78], [61675, 77], [61976, 76], [62279, 72], [62581, 70], [62881, 69]], "point": [222, 189]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|+00.39|+00.82|+02.27", "placeStationary": true, "receptacleObjectId": "Drawer|+00.44|+00.62|+02.19"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [177, 170, 299, 300], "mask": [[50880, 84], [51180, 84], [51480, 84], [51780, 85], [52079, 86], [52379, 87], [52679, 87], [52979, 88], [53279, 88], [53579, 88], [53879, 89], [54179, 89], [54478, 91], [54778, 91], [55078, 20], [55101, 68], [55378, 18], [55402, 68], [55678, 18], [55703, 67], [55978, 18], [56004, 67], [56278, 17], [56303, 68], [56578, 17], [56603, 68], [56877, 18], [56903, 69], [57177, 18], [57203, 69], [57477, 18], [57502, 71], [57777, 17], [57802, 71], [58077, 17], [58102, 71], [58377, 17], [58402, 72], [58677, 17], [58701, 73], [58977, 17], [59001, 74], [59277, 16], [59301, 74], [59577, 16], [59601, 74], [59878, 15], [59901, 75], [60178, 15], [60200, 76], [60478, 15], [60500, 77], [60778, 15], [60800, 77], [61078, 16], [61099, 79], [61378, 100], [61678, 100], [61978, 101], [62279, 100], [62581, 99], [62881, 99], [63182, 98], [63483, 98], [63783, 98], [64084, 98], [64385, 97], [64685, 97], [64986, 97], [65287, 96], [65587, 97], [65888, 96], [66188, 96], [66489, 96], [66790, 95], [67090, 96], [67391, 95], [67692, 94], [67992, 95], [68293, 94], [68594, 94], [68894, 94], [69195, 93], [69496, 93], [69796, 93], [70097, 93], [70397, 93], [70697, 94], [70997, 94], [71297, 94], [71597, 95], [71897, 95], [72197, 96], [72497, 96], [72797, 96], [73097, 97], [73397, 97], [73697, 98], [73997, 98], [74297, 98], [74597, 99], [74897, 99], [75197, 100], [75497, 100], [75797, 100], [76097, 101], [76397, 101], [76697, 102], [76997, 102], [77297, 102], [77597, 103], [77897, 103], [78197, 103], [78497, 103], [78797, 103], [79097, 103], [79397, 103], [79697, 103], [79996, 104], [80295, 105], [80594, 106], [80894, 106], [81193, 107], [81492, 108], [81791, 109], [82091, 109], [82390, 110], [82689, 111], [82989, 111], [83288, 112], [83587, 113], [83886, 114], [84186, 114], [84485, 115], [84785, 115], [85085, 115], [85385, 115], [85685, 113], [85984, 113], [86284, 112], [86584, 111], [86883, 111], [87183, 109], [87483, 108], [87783, 107], [88082, 107], [88382, 106], [88682, 105], [88982, 103], [89282, 102], [89582, 101], [89882, 100]], "point": [238, 234]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+00.44|+00.62|+02.19"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [177, 170, 299, 300], "mask": [[50880, 84], [51180, 84], [51480, 84], [51780, 85], [52079, 86], [52379, 87], [52679, 87], [52979, 88], [53279, 88], [53579, 32], [53620, 47], [53879, 30], [53922, 46], [54179, 29], [54224, 44], [54478, 29], [54526, 43], [54778, 29], [54826, 43], [55078, 20], [55101, 6], [55127, 42], [55378, 18], [55402, 4], [55428, 42], [55678, 18], [55703, 3], [55728, 42], [55978, 18], [56004, 2], [56029, 42], [56278, 17], [56303, 2], [56329, 42], [56578, 17], [56603, 2], [56629, 42], [56877, 18], [56903, 2], [56929, 43], [57177, 18], [57203, 2], [57228, 44], [57477, 18], [57502, 2], [57528, 45], [57777, 17], [57802, 2], [57828, 45], [58077, 17], [58102, 2], [58127, 46], [58377, 17], [58402, 2], [58427, 47], [58677, 17], [58701, 2], [58727, 47], [58977, 17], [59001, 2], [59026, 49], [59277, 16], [59301, 2], [59326, 49], [59577, 16], [59601, 2], [59626, 49], [59878, 15], [59901, 1], [59925, 51], [60178, 15], [60200, 2], [60225, 51], [60478, 15], [60500, 2], [60525, 52], [60778, 15], [60800, 3], [60824, 53], [61078, 16], [61099, 4], [61124, 54], [61378, 26], [61423, 55], [61678, 26], [61723, 55], [61978, 27], [62023, 56], [62278, 29], [62322, 57], [62578, 30], [62621, 59], [62878, 32], [62919, 61], [63179, 101], [63479, 102], [63779, 102], [64079, 103], [64379, 103], [64679, 103], [64979, 104], [65279, 104], [65579, 105], [65879, 105], [66179, 105], [66479, 106], [66780, 105], [67080, 106], [67380, 106], [67680, 106], [67980, 107], [68280, 107], [68580, 108], [68880, 108], [69180, 108], [69480, 109], [69780, 109], [70081, 109], [70381, 109], [70681, 110], [70981, 110], [71281, 110], [71581, 111], [71881, 111], [72181, 112], [72481, 112], [72781, 112], [73081, 113], [73382, 112], [73682, 113], [73982, 113], [74282, 113], [74582, 114], [74882, 114], [75182, 115], [75482, 115], [75782, 115], [76082, 116], [76382, 116], [76683, 116], [76983, 116], [77283, 116], [77583, 117], [77883, 117], [78183, 117], [78483, 117], [78783, 117], [79083, 117], [79383, 117], [79683, 117], [79983, 117], [80284, 116], [80584, 116], [80884, 116], [81184, 116], [81484, 116], [81784, 116], [82084, 116], [82384, 116], [82684, 116], [82984, 116], [83284, 116], [83585, 115], [83885, 115], [84185, 115], [84485, 115], [84785, 115], [85085, 115], [85385, 115], [85685, 113], [85984, 113], [86284, 112], [86584, 111], [86883, 111], [87183, 109], [87483, 108], [87783, 107], [88082, 107], [88382, 106], [88682, 105], [88982, 103], [89282, 102], [89582, 101], [89882, 100]], "point": [238, 234]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan409", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -0.75, "y": 0.9011651, "z": 3.25}, "object_poses": [{"objectName": "SoapBar_abaf7d3b", "position": {"x": 0.481954217, "y": 0.676389337, "z": 2.8550396}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SprayBottle_d5e13b2f", "position": {"x": 0.6246331, "y": 1.04626751, "z": 1.33629847}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBottle_bfb43855", "position": {"x": 0.594000041, "y": 1.05246258, "z": 1.46744227}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBottle_bfb43855", "position": {"x": 0.323645264, "y": 0.827141643, "z": 3.23942661}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_3326814a", "position": {"x": 0.634889, "y": 1.512, "z": 1.8920002}, "rotation": {"x": 0.0, "y": 90.00026, "z": 0.0}}, {"objectName": "Towel_9ca2fbae", "position": {"x": 0.609628439, "y": 1.376, "z": 0.583}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBar_abaf7d3b", "position": {"x": 0.35669145, "y": 0.676389337, "z": 2.8550396}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ToiletPaper_5db3475e", "position": {"x": 0.389740229, "y": 0.8165423, "z": 2.27112055}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_321c92a6", "position": {"x": 0.308799922, "y": 0.6156, "z": 1.91230011}, "rotation": {"x": 0.0, "y": 180.000015, "z": 90.0}}, {"objectName": "Candle_0bc9cce6", "position": {"x": 0.5026288, "y": 0.2130247, "z": 2.1558795}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ScrubBrush_b98b86ba", "position": {"x": 0.5530571, "y": 0.000166088343, "z": 1.725937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_0a396f26", "position": {"x": 0.558668137, "y": 0.215942591, "z": 2.979755}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plunger_17d994b3", "position": {"x": 0.5940484, "y": -0.0002968982, "z": 1.89854741}, "rotation": {"x": -0.00118541578, "y": 0.0004420988, "z": 0.0007844724}}, {"objectName": "SprayBottle_d5e13b2f", "position": {"x": 0.6552663, "y": 1.04626751, "z": 1.27072644}, "rotation": {"x": 0.0, "y": 180.000153, "z": 0.0}}], "object_toggles": [], "random_seed": 2426952819, "scene_num": 409}, "task_id": "trial_T20190906_193352_707342", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A1HKHM4NVAO98H_308Q0PEVBBUIMSX5SREMI6YE2XVI9Q", "high_descs": ["walk over to the right side of the bathroom sink counter", "grab a yellow tube off of the floor to the left of the toilet", "bring the tube over to the front of the sink", "put the tube inside the top right drawer of the sink cabinets", "walk up to the kitchen sink cabinet on the right", "grab the toilet paper off of the counter", "turn around to step back a bit from the counter", "open the same drawer and put the toilet paper in there too"], "task_desc": "put the tube and the toilet paper roll in the kitchen cabinet drawer", "votes": [1, 1]}, {"assignment_id": "A1GVTH5YS3WOK0_3KAKFY4PGXJW7Q8PBIDCRTVS25LI39", "high_descs": ["Walk towards the toilet and turn left to face the toilet.", "Pick up the toilet paper cardboard roll in front of the plunger on the floor.", "Turn left and head towards the sink on your right.", "Open the top drawer on the right side of the sink and place the cardboard roll to the left of the toilet paper and close the drawer.", "Look at the top counter.", "Pick up the toilet paper on the counter.", "Look at the sink.", "Open the top drawer on the right side of the sink and place the cardboard roll to the left of the toilet paper and close the drawer."], "task_desc": "Place the toilet paper and cardboard roll in the drawer below the sink.", "votes": [1, 1]}, {"assignment_id": "A3HL2LL0LEPZT8_3R0T90IZ1VT6OZYDENOAZKM6T90GCF", "high_descs": ["Go straight, then turn left to face the counter to the right of the sink and the toilet.", "Take the cardboard roll from the floor next to the plunger.", "Go left to the counter to the right of the sink.", "Open the most right drawer under the counter, put the cardboard roll in the upper left corner of the drawer.", "Stay at the counter.", "Take the toilet paper roll from the counter.", "Stay at the counter.", "Open the most right drawer under the counter, put the toilet paper roll to the right of the cardboard roll."], "task_desc": "Put toilet paper rolls in a drawer.", "votes": [1, 1]}]}}