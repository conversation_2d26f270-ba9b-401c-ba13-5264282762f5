{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000317.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000318.png", "low_idx": 60}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "DiningTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|3|13|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [5.61547136, 5.61547136, 13.29782296, 13.29782296, 4.1708908, 4.1708908]], "coordinateReceptacleObjectId": ["CounterTop", [5.564, 5.564, 3.588, 3.588, 4.1128, 4.1128]], "forceVisible": true, "objectId": "Egg|+01.40|+01.04|+03.32"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|6|2|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|3|26|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [5.61547136, 5.61547136, 13.29782296, 13.29782296, 4.1708908, 4.1708908]], "coordinateReceptacleObjectId": ["DiningTable", [-0.624711872, -0.624711872, 25.896156, 25.896156, 2.673336984, 2.673336984]], "forceVisible": true, "objectId": "Egg|+01.40|+01.04|+03.32", "receptacleObjectId": "DiningTable|-00.16|+00.67|+06.47"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.40|+01.04|+03.32"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [115, 111, 138, 140], "mask": [[33124, 4], [33422, 9], [33721, 11], [34020, 14], [34319, 15], [34618, 17], [34918, 18], [35217, 19], [35517, 20], [35816, 21], [36116, 22], [36416, 22], [36716, 22], [37015, 24], [37315, 24], [37615, 24], [37915, 24], [38216, 23], [38516, 23], [38816, 23], [39116, 22], [39417, 21], [39717, 21], [40017, 20], [40318, 19], [40619, 17], [40920, 15], [41221, 13], [41522, 10], [41825, 5]], "point": [126, 124]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.40|+01.04|+03.32", "placeStationary": true, "receptacleObjectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 61], [26562, 98], [26788, 60], [26864, 96], [27087, 59], [27166, 94], [27387, 59], [27466, 94], [27686, 59], [27767, 92], [27985, 60], [28067, 92], [28285, 60], [28367, 92], [28584, 61], [28667, 92], [28883, 61], [28968, 91], [29183, 61], [29268, 90], [29482, 62], [29568, 90], [29781, 63], [29867, 91], [30081, 63], [30167, 91], [30380, 65], [30467, 90], [30679, 66], [30767, 90], [30978, 67], [31067, 90], [31278, 67], [31366, 91], [31577, 69], [31666, 91], [31876, 70], [31966, 90], [32176, 70], [32266, 90], [32475, 72], [32565, 91], [32775, 72], [32865, 91], [33075, 73], [33164, 91], [33375, 73], [33464, 91], [33675, 74], [33763, 92], [33975, 75], [34062, 93], [34275, 78], [34360, 95], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 61], [26562, 98], [26788, 60], [26864, 96], [27087, 59], [27166, 94], [27387, 59], [27466, 94], [27686, 59], [27767, 20], [27792, 67], [27985, 60], [28067, 18], [28093, 66], [28285, 60], [28367, 17], [28394, 65], [28584, 61], [28667, 17], [28695, 64], [28883, 61], [28968, 15], [28995, 64], [29183, 61], [29268, 14], [29296, 62], [29482, 62], [29568, 14], [29596, 62], [29781, 63], [29867, 14], [29896, 62], [30081, 63], [30167, 14], [30196, 62], [30380, 65], [30467, 14], [30497, 60], [30679, 66], [30767, 14], [30797, 60], [30978, 67], [31067, 14], [31097, 60], [31278, 67], [31366, 15], [31397, 60], [31577, 69], [31666, 15], [31696, 61], [31876, 70], [31966, 15], [31996, 60], [32176, 70], [32266, 15], [32296, 60], [32475, 72], [32565, 16], [32596, 60], [32775, 72], [32865, 17], [32895, 61], [33075, 73], [33164, 18], [33194, 61], [33375, 73], [33464, 19], [33494, 61], [33675, 74], [33763, 21], [33793, 62], [33975, 75], [34062, 23], [34091, 64], [34275, 78], [34360, 95], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.40|+01.04|+03.32"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [181, 93, 196, 114], "mask": [[27787, 5], [28085, 8], [28384, 10], [28684, 11], [28983, 12], [29282, 14], [29582, 14], [29881, 15], [30181, 15], [30481, 16], [30781, 16], [31081, 16], [31381, 16], [31681, 15], [31981, 15], [32281, 15], [32581, 15], [32882, 13], [33182, 12], [33483, 11], [33784, 9], [34085, 6]], "point": [188, 102]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 61], [26562, 98], [26788, 60], [26864, 96], [27087, 59], [27166, 94], [27387, 59], [27466, 94], [27686, 59], [27767, 92], [27985, 60], [28067, 92], [28285, 60], [28367, 92], [28584, 61], [28667, 92], [28883, 61], [28968, 91], [29183, 61], [29268, 90], [29482, 62], [29568, 90], [29781, 63], [29867, 91], [30081, 63], [30167, 91], [30380, 65], [30467, 90], [30679, 66], [30767, 90], [30978, 67], [31067, 90], [31278, 67], [31366, 91], [31577, 69], [31666, 91], [31876, 70], [31966, 90], [32176, 70], [32266, 90], [32475, 72], [32565, 91], [32775, 72], [32865, 91], [33075, 73], [33164, 91], [33375, 73], [33464, 91], [33675, 74], [33763, 92], [33975, 75], [34062, 93], [34275, 78], [34360, 95], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.40|+01.04|+03.32", "placeStationary": true, "receptacleObjectId": "DiningTable|-00.16|+00.67|+06.47"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 71, 299, 300], "mask": [[21141, 3], [21148, 12], [21420, 1], [21424, 1], [21441, 2], [21448, 17], [21468, 3], [21714, 7], [21725, 1], [21727, 1], [21740, 3], [21747, 17], [21767, 9], [22008, 14], [22040, 3], [22047, 15], [22065, 10], [22303, 20], [22326, 1], [22328, 1], [22340, 2], [22346, 14], [22364, 10], [22387, 2], [22597, 5], [22605, 18], [22640, 2], [22646, 13], [22662, 12], [22687, 6], [22894, 10], [22908, 16], [22929, 1], [22940, 2], [22946, 12], [22961, 12], [22987, 10], [23191, 15], [23210, 15], [23229, 1], [23240, 2], [23245, 11], [23259, 15], [23288, 14], [23487, 21], [23511, 14], [23530, 1], [23540, 1], [23545, 10], [23558, 16], [23587, 19], [23784, 25], [23813, 13], [23830, 1], [23839, 2], [23844, 9], [23856, 18], [23887, 21], [24081, 30], [24116, 11], [24139, 2], [24144, 8], [24155, 20], [24186, 25], [24378, 34], [24419, 8], [24431, 1], [24439, 1], [24444, 7], [24454, 21], [24485, 29], [24675, 40], [24721, 7], [24731, 1], [24739, 1], [24743, 7], [24752, 23], [24785, 31], [24972, 46], [25023, 5], [25039, 1], [25043, 6], [25051, 23], [25085, 34], [25269, 52], [25325, 4], [25339, 1], [25343, 5], [25350, 24], [25385, 36], [25567, 55], [25627, 2], [25643, 4], [25656, 18], [25685, 38], [25865, 56], [25929, 1], [25942, 3], [25948, 5], [25957, 17], [25985, 38], [26163, 57], [26224, 2], [26242, 1], [26247, 8], [26257, 17], [26285, 38], [26461, 58], [26523, 3], [26528, 2], [26544, 1], [26547, 27], [26585, 37], [26759, 58], [26822, 5], [26829, 2], [26843, 1], [26846, 27], [26885, 37], [27057, 71], [27130, 1], [27142, 2], [27145, 28], [27185, 36], [27233, 2], [27355, 74], [27430, 2], [27441, 2], [27444, 29], [27485, 36], [27533, 3], [27653, 77], [27731, 1], [27741, 2], [27744, 29], [27785, 36], [27833, 5], [27951, 79], [28041, 1], [28043, 30], [28085, 35], [28133, 7], [28249, 74], [28352, 21], [28385, 35], [28433, 8], [28547, 75], [28653, 20], [28685, 8], [28711, 9], [28733, 10], [28845, 77], [28953, 19], [28985, 7], [29014, 5], [29033, 12], [29144, 78], [29253, 19], [29285, 6], [29318, 1], [29333, 13], [29443, 79], [29553, 19], [29585, 4], [29632, 16], [29741, 81], [29853, 19], [29885, 4], [29932, 18], [30040, 81], [30153, 18], [30185, 4], [30232, 19], [30338, 83], [30453, 18], [30485, 3], [30532, 21], [30637, 42], [30685, 36], [30753, 18], [30785, 3], [30832, 23], [30936, 42], [30987, 34], [31053, 18], [31085, 3], [31132, 24], [31234, 43], [31288, 33], [31353, 18], [31385, 2], [31433, 24], [31533, 44], [31589, 32], [31669, 3], [31684, 3], [31734, 24], [31832, 44], [31890, 30], [31983, 4], [32035, 24], [32130, 46], [32190, 29], [32284, 3], [32336, 24], [32429, 47], [32491, 29], [32582, 5], [32637, 24], [32728, 48], [32791, 29], [32878, 9], [32937, 25], [33026, 31], [33058, 18], [33091, 29], [33170, 16], [33238, 26], [33325, 33], [33363, 13], [33392, 28], [33460, 26], [33538, 27], [33623, 35], [33667, 9], [33692, 28], [33753, 33], [33838, 28], [33922, 36], [33969, 7], [33992, 28], [34053, 33], [34138, 29], [34222, 35], [34270, 6], [34292, 29], [34353, 33], [34438, 30], [34521, 35], [34571, 5], [34591, 30], [34653, 33], [34738, 31], [34820, 35], [34871, 6], [34891, 30], [34953, 33], [35038, 32], [35119, 36], [35171, 7], [35191, 30], [35253, 34], [35337, 34], [35418, 37], [35472, 6], [35490, 31], [35553, 34], [35637, 35], [35717, 38], [35772, 7], [35789, 32], [35852, 35], [35936, 37], [36016, 39], [36072, 9], [36088, 34], [36152, 35], [36234, 41], [36315, 40], [36372, 11], [36386, 36], [36452, 35], [36533, 43], [36614, 42], [36673, 49], [36752, 36], [36831, 45], [36914, 42], [36973, 49], [37052, 36], [37128, 49], [37213, 43], [37273, 49], [37352, 36], [37427, 51], [37512, 45], [37573, 50], [37652, 37], [37725, 53], [37811, 46], [37874, 49], [37951, 38], [38022, 57], [38110, 47], [38174, 49], [38251, 39], [38319, 61], [38409, 49], [38474, 22], [38498, 25], [38551, 41], [38614, 66], [38708, 50], [38775, 17], [38803, 21], [38851, 42], [38908, 73], [39007, 51], [39075, 14], [39106, 18], [39151, 46], [39201, 81], [39307, 52], [39375, 13], [39408, 16], [39451, 131], [39606, 53], [39675, 11], [39709, 16], [39750, 133], [39905, 54], [39976, 9], [40010, 15], [40050, 134], [40204, 56], [40276, 8], [40311, 14], [40350, 134], [40503, 57], [40576, 7], [40612, 13], [40650, 135], [40803, 57], [40876, 6], [40913, 13], [40950, 23], [40978, 54], [41061, 25], [41102, 59], [41177, 4], [41214, 12], [41249, 20], [41281, 51], [41362, 24], [41402, 59], [41477, 2], [41514, 13], [41549, 18], [41584, 26], [41612, 19], [41663, 24], [41701, 60], [41777, 1], [41815, 12], [41849, 17], [41885, 21], [41915, 16], [41963, 25], [42001, 61], [42116, 11], [42149, 16], [42186, 19], [42218, 13], [42264, 24], [42300, 62], [42416, 12], [42448, 16], [42487, 17], [42521, 10], [42564, 25], [42600, 62], [42717, 11], [42748, 16], [42788, 15], [42822, 9], [42865, 25], [42900, 63], [43017, 47], [43088, 14], [43124, 8], [43165, 25], [43200, 63], [43318, 41], [43388, 13], [43425, 7], [43466, 25], [43500, 63], [43618, 40], [43688, 14], [43726, 6], [43766, 26], [43800, 64], [43919, 39], [43988, 14], [44027, 6], [44067, 25], [44100, 64], [44219, 38], [44288, 14], [44329, 4], [44366, 26], [44400, 33], [44442, 22], [44519, 38], [44560, 3], [44588, 14], [44630, 3], [44666, 27], [44700, 31], [44744, 21], [44820, 37], [44859, 4], [44888, 15], [44931, 3], [44965, 28], [45000, 30], [45045, 20], [45120, 37], [45159, 4], [45188, 15], [45232, 2], [45265, 28], [45300, 29], [45346, 19], [45420, 37], [45459, 4], [45488, 16], [45532, 61], [45600, 28], [45647, 18], [45720, 37], [45760, 3], [45787, 18], [45833, 61], [45900, 27], [45947, 19], [46020, 38], [46060, 3], [46087, 19], [46133, 61], [46200, 27], [46247, 18], [46320, 38], [46361, 2], [46387, 21], [46433, 61], [46500, 26], [46547, 18], [46620, 38], [46687, 22], [46732, 63], [46800, 26], [46847, 17], [46920, 39], [46987, 24], [47032, 63], [47100, 26], [47147, 16], [47219, 41], [47287, 26], [47331, 64], [47400, 26], [47447, 16], [47519, 43], [47587, 28], [47630, 65], [47700, 26], [47747, 15], [47819, 44], [47886, 31], [47929, 67], [48000, 27], [48048, 13], [48119, 44], [48186, 34], [48227, 69], [48300, 27], [48348, 13], [48418, 45], [48486, 110], [48600, 28], [48648, 12], [48716, 47], [48786, 111], [48900, 30], [48949, 11], [49015, 48], [49086, 111], [49200, 31], [49249, 11], [49313, 51], [49385, 112], [49500, 32], [49550, 9], [49612, 52], [49685, 49], [49737, 60], [49800, 32], [49851, 8], [49910, 55], [49984, 44], [50041, 57], [50100, 33], [50152, 7], [50209, 57], [50283, 44], [50344, 54], [50400, 33], [50452, 7], [50508, 60], [50582, 44], [50646, 52], [50700, 34], [50753, 6], [50806, 64], [50880, 44], [50947, 52], [51000, 34], [51054, 6], [51105, 119], [51248, 51], [51300, 34], [51354, 6], [51402, 121], [51549, 50], [51600, 34], [51654, 7], [51699, 123], [51850, 49], [51900, 35], [51955, 7], [51995, 127], [52150, 85], [52256, 6], [52293, 128], [52451, 84], [52556, 7], [52590, 131], [52751, 84], [52857, 7], [52887, 134], [53051, 85], [53157, 8], [53182, 138], [53351, 85], [53458, 9], [53476, 144], [53651, 86], [53758, 162], [53952, 85], [54058, 162], [54252, 85], [54359, 161], [54552, 85], [54659, 161], [54852, 47], [54900, 38], [54959, 161], [55151, 48], [55200, 38], [55259, 95], [55364, 56], [55451, 48], [55500, 39], [55559, 92], [55666, 54], [55751, 48], [55800, 39], [55858, 87], [55967, 53], [56050, 49], [56100, 40], [56157, 58], [56267, 53], [56350, 49], [56400, 40], [56457, 57], [56542, 8], [56567, 53], [56650, 49], [56700, 41], [56755, 59], [56835, 16], [56866, 54], [56949, 50], [57000, 41], [57054, 60], [57129, 23], [57165, 56], [57249, 50], [57300, 43], [57352, 64], [57420, 33], [57463, 58], [57548, 51], [57600, 156], [57760, 61], [57847, 51], [57900, 222], [58147, 51], [58200, 222], [58446, 52], [58500, 223], [58745, 53], [58800, 224], [59044, 54], [59100, 225], [59342, 56], [59400, 226], [59641, 57], [59700, 228], [59938, 60], [60000, 229], [60236, 62], [60300, 298], [60600, 298], [60900, 297], [61200, 297], [61500, 297], [61800, 297], [62100, 297], [62400, 297], [62700, 297], [63000, 296], [63300, 295], [63600, 295], [63900, 294], [64200, 294], [64500, 293], [64800, 293], [65100, 292], [65400, 292], [65700, 291], [66000, 291], [66300, 290], [66600, 290], [66900, 289], [67200, 289], [67500, 288], [67800, 288], [68100, 145], [68255, 132], [68400, 142], [68558, 128], [68700, 140], [68860, 126], [69000, 139], [69162, 123], [69300, 137], [69463, 122], [69600, 136], [69764, 120], [69900, 135], [70065, 119], [70201, 133], [70366, 117], [70502, 131], [70667, 116], [70803, 129], [70968, 114], [71104, 128], [71268, 114], [71405, 126], [71569, 112], [71705, 125], [71870, 111], [72006, 124], [72170, 110], [72307, 122], [72471, 109], [72608, 121], [72771, 108], [72909, 119], [73072, 107], [73209, 118], [73372, 106], [73510, 117], [73673, 104], [73811, 116], [73973, 103], [74112, 114], [74274, 101], [74413, 113], [74574, 100], [74713, 113], [74874, 99], [75014, 111], [75175, 97], [75315, 110], [75475, 95], [75616, 109], [75775, 94], [75917, 108], [76075, 93], [76217, 107], [76375, 92], [76518, 106], [76676, 90], [76819, 105], [76976, 89], [77120, 104], [77276, 87], [77421, 103], [77576, 86], [77722, 102], [77876, 85], [78022, 102], [78176, 84], [78323, 101], [78476, 83], [78624, 100], [78776, 82], [78925, 99], [79076, 81], [79226, 98], [79376, 79], [79527, 97], [79676, 78], [79829, 95], [79976, 77], [80130, 94], [80275, 77], [80432, 93], [80575, 76], [80734, 91], [80875, 75], [81035, 90], [81175, 73], [81337, 88], [81475, 72], [81639, 87], [81774, 72], [81940, 86], [82074, 71], [82242, 84], [82374, 70], [82544, 83], [82673, 70], [82846, 81], [82973, 69], [83147, 81], [83272, 68], [83449, 79], [83572, 66], [83751, 78], [83871, 64], [84052, 77], [84171, 62], [84354, 76], [84470, 60], [84656, 74], [84770, 58], [84958, 73], [85069, 57], [85259, 73], [85368, 55], [85561, 72], [85667, 54], [85863, 71], [85966, 52], [86164, 71], [86265, 51], [86466, 71], [86564, 49], [86768, 70], [86862, 49], [87070, 70], [87160, 48], [87372, 70], [87458, 48], [87677, 68], [87754, 49], [87981, 120], [88286, 112], [88591, 105], [88895, 98], [89200, 91], [89505, 83], [89809, 67]], "point": [149, 184]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan18", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -0.25, "y": 0.900999844, "z": 3.0}, "object_poses": [{"objectName": "Pan_08687688", "position": {"x": 1.317894, "y": 0.988599956, "z": 4.2942}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": -0.0284192562, "y": 0.7949763, "z": 6.78657436}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 0.8712312, "y": 0.7719865, "z": 0.619083345}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": -0.07436885, "y": 1.57915688, "z": 0.2071247}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": 1.58258653, "y": 0.9927422, "z": 0.4407184}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": 1.079631, "y": 1.58063388, "z": 0.207324728}, "rotation": {"x": 0.0, "y": 0.0, "z": -1.70754731e-06}}, {"objectName": "Spoon_cefc2847", "position": {"x": 0.150742471, "y": 0.7612178, "z": 6.479039}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": -0.6172804, "y": 0.7863806, "z": 0.6202002}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": -0.413737565, "y": 0.771929264, "z": 0.6202002}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": 1.40386784, "y": 1.0427227, "z": 3.32445574}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 1.49008977, "y": 0.141221583, "z": 3.43957567}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": -0.346677959, "y": 0.789788663, "z": 6.478116}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 1.13578975, "y": 0.9927622, "z": 4.7165}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": -0.3991838, "y": 0.7550884, "z": 6.89582157}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 0.8978806, "y": 1.57459259, "z": 0.383118451}, "rotation": {"x": 0.0, "y": 0.0, "z": -1.70754731e-06}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 1.40386784, "y": 0.9877, "z": 3.13762426}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 1.22514915, "y": 0.987700045, "z": 0.4407184}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": -0.221677959, "y": 0.848502, "z": 6.787113}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 0.221146822, "y": 1.17167974, "z": 0.310429215}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 0.169216156, "y": 0.8346215, "z": 6.807433}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Bowl_9f9e1c57", "position": {"x": 1.53359437, "y": 2.20614338, "z": 3.84069443}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b7525b79", "position": {"x": 1.09999228, "y": 1.0487318, "z": 0.434997439}, "rotation": {"x": -0.0006395338, "y": -0.0002419045, "z": 0.003708425}}, {"objectName": "Egg_8021024b", "position": {"x": -0.288631022, "y": 0.810111, "z": 6.1728096}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": 1.3873, "y": 0.9203327, "z": 2.13703656}, "rotation": {"x": -1.51660667e-21, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Fork_8ae143f8", "position": {"x": -0.5356747, "y": 0.9932486, "z": 0.212008715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 2.10496449, "y": 0.155250221, "z": 6.52859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": 1.562, "y": 0.988599956, "z": 3.848}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 1.32257462, "y": 0.141221583, "z": 2.646204}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": -0.422519267, "y": 0.7550884, "z": 6.6539917}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": 0.05866337, "y": 0.84850204, "z": 6.261198}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 0.380409181, "y": 1.57965469, "z": 0.295221567}, "rotation": {"x": 0.0, "y": 0.0, "z": -1.70754731e-06}}, {"objectName": "Spatula_925e7ba3", "position": {"x": -0.0794049352, "y": 0.7869452, "z": 0.618839443}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_bd2376ff", "position": {"x": -0.0341779739, "y": 0.8643737, "z": 6.1691184}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_73d89a5b", "position": {"x": 1.32, "y": 0.988599956, "z": 3.848}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": 1.44258, "y": 0.9255203, "z": 2.085737}, "rotation": {"x": 2.27491e-21, "y": 89.99999, "z": 1.40334183e-14}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": 1.238102, "y": 0.7716638, "z": 4.717268}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 0.123716265, "y": 0.7550884, "z": 6.10775661}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Pan_08687688", "position": {"x": -0.3842585, "y": 0.9908586, "z": 0.395208716}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": 2.09459472, "y": 1.56507456, "z": 6.001}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": -0.09667797, "y": 0.7596526, "z": 6.941612}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_8e8a26e1", "position": {"x": 1.657605, "y": 0.136771977, "z": 1.79032278}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": 1.2923851, "y": 0.772951365, "z": 3.36045027}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": -0.0037561655, "y": 0.7550884, "z": 6.604039}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2298380149, "scene_num": 18}, "task_id": "trial_T20190908_051238_005636", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AISNLDPD2DFEG_3VELCLL3GN0GNWSKN6T80333PBUF13", "high_descs": ["Turn left, go forward a bit, turn right and go to the counter", "Pick up the egg on the counter", "Turn around, go forward a bit, turn left, go to the microwave", "Put the egg in the microwave, start the microwave, wait, pick up the egg from the microwave", "Turn left, go forward a bit, turn left, go forward, turn left and go to the table", "Put the egg on the table"], "task_desc": "Cook an egg in the microwave", "votes": [1, 1, 1]}, {"assignment_id": "AM2KK02JXXW48_3WETL7AQWWPKV2DA4YUI2KRNY1M35S", "high_descs": ["Go forward and to the counter left of the sink. ", "Pick up the egg on the counter. ", "Turn right, bring the egg to the microwave on the counter. ", "Heat the egg in the microwave. ", "Take the heated egg, turn around, go to the right side of the round black table. ", "Put the egg on the table in front of the spoon."], "task_desc": "Put a heated egg on the round table.", "votes": [1, 1, 1]}, {"assignment_id": "AD0NVUGLDYDYN_3EFVCAY5L6QE3YY15HT8UBI3WOGJ8T", "high_descs": ["walk to the counter at front", "grab the egg on the counter", "turn right, walk to the microwave", "put the egg in the microwave, heat the egg for a while, take the egg out", "turn around, walk to the table on the left", "put the egg on the table"], "task_desc": "cook the egg in the microwave, put the egg on the table", "votes": [1, 1, 0]}]}}