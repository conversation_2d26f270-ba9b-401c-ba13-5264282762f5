{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000204.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000206.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000207.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 58}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Tomato", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-13|-6|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["tomato"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Tomato", [-13.16944884, -13.16944884, -1.733999728, -1.733999728, 3.0534148, 3.0534148]], "coordinateReceptacleObjectId": ["DiningTable", [-11.476, -11.476, -2.024, -2.024, 2.708, 2.708]], "forceVisible": true, "objectId": "Tomato|-03.29|+00.76|-00.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-5|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tomato", "countertop"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Tomato", [-13.16944884, -13.16944884, -1.733999728, -1.733999728, 3.0534148, 3.0534148]], "coordinateReceptacleObjectId": ["CounterTop", [-1.076, -1.076, -4.3592, -4.3592, 3.806, 3.806]], "forceVisible": true, "objectId": "Tomato|-03.29|+00.76|-00.43", "receptacleObjectId": "CounterTop|-00.27|+00.95|-01.09"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-13|-6|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["tomato"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Tomato", [-13.0232544, -13.0232544, -2.8369464, -2.8369464, 3.0534148, 3.0534148]], "coordinateReceptacleObjectId": ["DiningTable", [-11.476, -11.476, -2.024, -2.024, 2.708, 2.708]], "forceVisible": true, "objectId": "Tomato|-03.26|+00.76|-00.71"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-5|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tomato", "countertop"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Tomato", [-13.0232544, -13.0232544, -2.8369464, -2.8369464, 3.0534148, 3.0534148]], "coordinateReceptacleObjectId": ["CounterTop", [-1.076, -1.076, -4.3592, -4.3592, 3.806, 3.806]], "forceVisible": true, "objectId": "Tomato|-03.26|+00.76|-00.71", "receptacleObjectId": "CounterTop|-00.27|+00.95|-01.09"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Tomato|-03.29|+00.76|-00.43"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [132, 106, 155, 128], "mask": [[31641, 4], [31939, 9], [32237, 13], [32536, 15], [32835, 18], [33134, 20], [33433, 21], [33733, 22], [34033, 23], [34332, 24], [34632, 24], [34932, 24], [35232, 24], [35533, 23], [35833, 23], [36133, 23], [36433, 22], [36734, 21], [37035, 19], [37335, 19], [37637, 16], [37938, 14], [38240, 9]], "point": [143, 116]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Tomato|-03.29|+00.76|-00.43", "placeStationary": true, "receptacleObjectId": "CounterTop|-00.27|+00.95|-01.09"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 92, 299, 210], "mask": [[27447, 69], [27519, 35], [27747, 69], [27819, 9], [27831, 23], [28047, 69], [28119, 10], [28132, 23], [28347, 70], [28420, 9], [28433, 22], [28647, 70], [28720, 10], [28734, 22], [28947, 70], [29021, 9], [29035, 22], [29247, 71], [29321, 9], [29335, 22], [29547, 71], [29621, 10], [29636, 22], [29847, 72], [29922, 9], [29936, 22], [30147, 73], [30221, 10], [30237, 22], [30447, 73], [30522, 10], [30537, 22], [30747, 73], [30822, 10], [30837, 23], [31047, 73], [31122, 11], [31138, 22], [31347, 74], [31423, 10], [31438, 23], [31647, 74], [31723, 10], [31738, 23], [31947, 74], [32023, 11], [32038, 24], [32247, 74], [32324, 10], [32338, 24], [32547, 75], [32624, 11], [32638, 25], [32847, 75], [32924, 11], [32938, 25], [33147, 17], [33175, 47], [33224, 11], [33239, 25], [33447, 14], [33477, 45], [33525, 11], [33539, 25], [33747, 11], [33777, 46], [33825, 11], [33839, 26], [34047, 11], [34077, 46], [34125, 11], [34140, 25], [34347, 11], [34363, 4], [34377, 46], [34426, 11], [34440, 26], [34647, 22], [34677, 46], [34727, 10], [34740, 26], [34947, 22], [34974, 2], [34977, 43], [35031, 6], [35041, 26], [35247, 19], [35275, 43], [35333, 4], [35342, 25], [35547, 18], [35576, 42], [35634, 4], [35642, 26], [35847, 18], [35877, 42], [35934, 4], [35943, 25], [36147, 18], [36177, 42], [36235, 3], [36243, 26], [36447, 18], [36477, 43], [36535, 3], [36544, 25], [36747, 18], [36777, 43], [36836, 3], [36844, 26], [37047, 12], [37080, 40], [37136, 3], [37145, 25], [37347, 12], [37381, 40], [37437, 2], [37445, 26], [37647, 12], [37681, 41], [37737, 3], [37746, 25], [37947, 12], [37981, 59], [38046, 26], [38247, 12], [38281, 60], [38346, 27], [38535, 24], [38581, 61], [38646, 27], [38835, 24], [38881, 93], [39134, 25], [39182, 92], [39434, 25], [39482, 93], [39734, 25], [39782, 93], [40034, 25], [40082, 94], [40334, 25], [40382, 94], [40633, 26], [40682, 95], [40800, 159], [40982, 95], [41100, 159], [41282, 96], [41400, 159], [41582, 96], [41700, 159], [41882, 97], [42000, 159], [42182, 97], [42300, 159], [42482, 98], [42600, 159], [42782, 98], [42900, 159], [43082, 99], [43200, 159], [43382, 99], [43500, 159], [43681, 36], [43719, 63], [43800, 159], [43981, 35], [44021, 61], [44100, 158], [44281, 34], [44322, 61], [44400, 158], [44581, 33], [44623, 60], [44700, 158], [44881, 33], [44924, 60], [45000, 158], [45181, 33], [45224, 60], [45300, 158], [45481, 33], [45525, 60], [45600, 158], [45781, 33], [45825, 60], [45900, 158], [46081, 33], [46125, 61], [46200, 158], [46380, 34], [46426, 60], [46500, 158], [46680, 34], [46726, 61], [46800, 158], [46980, 35], [47026, 61], [47100, 158], [47280, 37], [47325, 63], [47400, 158], [47580, 39], [47624, 65], [47700, 158], [47880, 40], [47923, 66], [48000, 158], [48180, 41], [48224, 66], [48300, 158], [48480, 41], [48524, 66], [48600, 158], [48779, 43], [48824, 67], [48900, 158], [49079, 43], [49124, 67], [49200, 158], [49379, 43], [49424, 68], [49500, 158], [49679, 43], [49724, 68], [49800, 158], [49979, 43], [50025, 68], [50100, 158], [50279, 44], [50325, 68], [50400, 159], [50579, 44], [50625, 69], [50700, 159], [50879, 44], [50926, 68], [51000, 159], [51179, 44], [51226, 69], [51300, 159], [51478, 45], [51526, 69], [51600, 159], [51778, 45], [51827, 69], [51900, 159], [52078, 45], [52127, 69], [52200, 159], [52378, 45], [52427, 70], [52500, 159], [52678, 45], [52728, 69], [52800, 223], [53028, 70], [53100, 223], [53328, 70], [53400, 223], [53629, 70], [53700, 224], [53929, 70], [54000, 224], [54229, 295], [54530, 294], [54830, 294], [55130, 294], [55430, 294], [55731, 294], [56031, 294], [56331, 294], [56632, 293], [56932, 293], [57232, 294], [57532, 294], [57832, 295], [58132, 296], [58432, 297], [58730, 4270]], "point": [149, 150]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Tomato|-03.26|+00.76|-00.71"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [134, 143, 162, 168], "mask": [[42744, 10], [43042, 14], [43340, 18], [43639, 20], [43938, 22], [44237, 23], [44537, 24], [44836, 26], [45136, 26], [45436, 26], [45735, 28], [46035, 28], [46335, 28], [46634, 29], [46935, 27], [47235, 27], [47535, 27], [47835, 26], [48136, 25], [48436, 24], [48737, 22], [49038, 20], [49339, 18], [49640, 16], [49942, 12], [50244, 8]], "point": [148, 154]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Tomato|-03.26|+00.76|-00.71", "placeStationary": true, "receptacleObjectId": "CounterTop|-00.27|+00.95|-01.09"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 92, 299, 210], "mask": [[27447, 69], [27519, 35], [27747, 69], [27819, 9], [27831, 23], [28047, 69], [28119, 10], [28132, 23], [28347, 70], [28420, 9], [28433, 22], [28647, 70], [28720, 10], [28734, 22], [28947, 70], [29021, 9], [29035, 22], [29247, 71], [29321, 9], [29335, 22], [29547, 71], [29621, 10], [29636, 22], [29847, 72], [29922, 9], [29936, 22], [30147, 73], [30221, 10], [30237, 22], [30447, 73], [30522, 10], [30537, 22], [30747, 73], [30822, 10], [30837, 23], [31047, 73], [31122, 11], [31138, 22], [31347, 74], [31423, 10], [31438, 23], [31647, 74], [31723, 10], [31738, 23], [31947, 74], [32023, 11], [32038, 24], [32247, 74], [32324, 10], [32338, 24], [32547, 75], [32624, 11], [32638, 25], [32847, 75], [32924, 11], [32938, 25], [33147, 17], [33175, 47], [33224, 11], [33239, 25], [33447, 14], [33477, 45], [33525, 11], [33539, 25], [33747, 11], [33777, 46], [33825, 11], [33839, 26], [34047, 11], [34077, 46], [34125, 11], [34140, 25], [34347, 11], [34363, 4], [34377, 46], [34426, 11], [34440, 26], [34647, 22], [34677, 46], [34727, 10], [34740, 26], [34947, 22], [34974, 2], [34977, 43], [35031, 6], [35041, 26], [35247, 19], [35275, 43], [35333, 4], [35342, 25], [35547, 18], [35576, 42], [35634, 4], [35642, 26], [35847, 18], [35877, 42], [35934, 4], [35943, 25], [36147, 18], [36177, 42], [36235, 3], [36243, 26], [36447, 18], [36477, 43], [36535, 3], [36544, 25], [36747, 18], [36777, 43], [36836, 3], [36844, 26], [37047, 12], [37080, 40], [37136, 3], [37145, 25], [37347, 12], [37381, 40], [37437, 2], [37445, 26], [37647, 12], [37681, 41], [37737, 3], [37746, 25], [37947, 12], [37981, 59], [38046, 26], [38247, 12], [38281, 60], [38346, 27], [38535, 24], [38581, 61], [38646, 27], [38835, 24], [38881, 93], [39134, 25], [39182, 92], [39434, 25], [39482, 93], [39734, 25], [39782, 93], [40034, 25], [40082, 94], [40334, 25], [40382, 94], [40633, 26], [40682, 95], [40800, 159], [40982, 95], [41100, 159], [41282, 96], [41400, 159], [41582, 96], [41700, 159], [41882, 97], [42000, 159], [42182, 97], [42300, 159], [42482, 98], [42600, 159], [42782, 98], [42900, 159], [43082, 99], [43200, 159], [43382, 99], [43500, 159], [43681, 36], [43719, 63], [43800, 159], [43981, 35], [44021, 61], [44100, 158], [44281, 34], [44322, 61], [44400, 158], [44581, 33], [44623, 60], [44700, 158], [44881, 33], [44924, 60], [45000, 158], [45181, 33], [45224, 60], [45300, 158], [45481, 33], [45525, 60], [45600, 158], [45781, 33], [45825, 60], [45900, 158], [46081, 33], [46125, 61], [46200, 158], [46380, 34], [46426, 60], [46500, 158], [46680, 34], [46726, 61], [46800, 158], [46980, 35], [47026, 61], [47100, 158], [47280, 37], [47325, 42], [47370, 18], [47400, 158], [47580, 39], [47624, 38], [47676, 13], [47700, 158], [47880, 40], [47923, 37], [47979, 10], [48000, 158], [48180, 41], [48224, 34], [48282, 8], [48300, 158], [48480, 41], [48524, 33], [48583, 7], [48600, 158], [48779, 43], [48824, 31], [48885, 6], [48900, 158], [49079, 43], [49124, 30], [49186, 5], [49200, 158], [49379, 43], [49424, 29], [49488, 4], [49500, 158], [49679, 43], [49724, 28], [49789, 3], [49800, 158], [49979, 43], [50025, 26], [50090, 3], [50100, 158], [50279, 44], [50325, 26], [50391, 2], [50400, 159], [50579, 44], [50625, 25], [50691, 3], [50700, 159], [50879, 44], [50926, 24], [50992, 2], [51000, 159], [51179, 44], [51226, 24], [51292, 3], [51300, 159], [51478, 45], [51526, 23], [51593, 2], [51600, 159], [51778, 45], [51827, 22], [51893, 3], [51900, 159], [52078, 45], [52127, 22], [52193, 3], [52200, 159], [52378, 45], [52427, 22], [52493, 4], [52500, 159], [52678, 45], [52728, 21], [52794, 3], [52800, 223], [53028, 21], [53094, 4], [53100, 223], [53328, 21], [53394, 4], [53400, 223], [53629, 21], [53694, 5], [53700, 224], [53929, 21], [53994, 5], [54000, 224], [54229, 21], [54294, 230], [54530, 21], [54594, 230], [54830, 21], [54893, 231], [55130, 22], [55193, 231], [55430, 22], [55493, 231], [55731, 22], [55792, 233], [56031, 22], [56092, 233], [56331, 23], [56391, 234], [56632, 23], [56690, 235], [56932, 24], [56989, 236], [57232, 25], [57288, 238], [57532, 27], [57587, 239], [57832, 28], [57885, 242], [58132, 30], [58183, 245], [58432, 33], [58479, 250], [58730, 4270]], "point": [149, 150]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan19", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -2.0, "y": 0.9016907, "z": -3.25}, "object_poses": [{"objectName": "Pan_4966d0cc", "position": {"x": -0.3767, "y": 0.9759, "z": -2.4513}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_4966d0cc", "position": {"x": -2.27053785, "y": 0.912074, "z": -3.95224237}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Potato_2b74ee3a", "position": {"x": -0.253700018, "y": 0.123593524, "z": 0.0126149431}, "rotation": {"x": 0.0, "y": 269.999969, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -2.79729939, "y": 0.713334441, "z": -0.829666436}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -0.09526098, "y": 0.9160421, "z": -1.61635566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -2.72361231, "y": 0.7319673, "z": -0.4335}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -0.5006437, "y": 0.7613414, "z": -0.837175131}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -0.41940093, "y": 0.9171294, "z": -1.49366212}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -3.16700172, "y": 0.9148294, "z": -3.804}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_f34461c5", "position": {"x": -3.29236221, "y": 0.7633537, "z": -0.433499932}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -0.312580645, "y": 1.29672146, "z": -3.85819554}, "rotation": {"x": 0.0, "y": 315.0, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -0.41940093, "y": 0.9155963, "z": -1.30962193}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_697b561f", "position": {"x": -3.18270254, "y": 1.3965019, "z": -2.06418538}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_697b561f", "position": {"x": -0.0763015747, "y": 0.911298, "z": -3.012186}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -3.082638, "y": 0.7429032, "z": -2.91282749}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -0.338365972, "y": 0.9123061, "z": -0.524823248}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_908150ce", "position": {"x": -2.77741146, "y": 0.8015844, "z": -0.5843884}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -1.06620538, "y": 0.07511723, "z": -3.6739974}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -3.23445082, "y": 0.636239946, "z": -1.94104671}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -3.0079875, "y": 0.759607255, "z": -0.6835}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_fd5c5c80", "position": {"x": -2.836, "y": 0.9039041, "z": -3.745}, "rotation": {"x": 0.0, "y": 92.62838, "z": 0.0}}, {"objectName": "Egg_b0ce4484", "position": {"x": -1.56911922, "y": 0.8266152, "z": -3.83288431}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_410beacd", "position": {"x": -2.65256286, "y": 0.7140956, "z": -0.105986416}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Bread_77e17ce3", "position": {"x": -0.327004015, "y": 0.9481207, "z": -3.68102622}, "rotation": {"x": 0.004293074, "y": 298.22287, "z": 0.0431186967}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -3.29236221, "y": 0.714318752, "z": -0.495999932}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_4966d0cc", "position": {"x": -0.1653, "y": 0.9759, "z": -2.0309}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Potato_2b74ee3a", "position": {"x": -0.0963167846, "y": 1.05657792, "z": -0.8022115}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_908150ce", "position": {"x": -1.32405484, "y": 0.988086462, "z": -3.75989842}, "rotation": {"x": 1.58991528, "y": 253.806763, "z": 357.401855}}, {"objectName": "Tomato_f34461c5", "position": {"x": -3.2558136, "y": 0.7633537, "z": -0.7092366}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -3.01428747, "y": 0.929043651, "z": -2.501158}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -0.496454358, "y": 1.29672146, "z": -3.76271033}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -0.01422599, "y": 0.9309999, "z": -1.55500889}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -0.3825171, "y": 0.742647648, "z": -2.91359067}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_46bffc5f", "position": {"x": -3.30916429, "y": 0.94613713, "z": -3.22723937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -0.4128108, "y": 0.910006046, "z": -2.8113637}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -2.2414968, "y": 0.90390414, "z": -3.59148455}, "rotation": {"x": 0.0, "y": 14.9999714, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -0.37460953, "y": 0.07864249, "z": -0.605684459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -1.77635264, "y": 0.790120363, "z": -3.83288383}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -2.62912345, "y": 0.9148294, "z": -3.804}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -2.93075848, "y": 0.7624276, "z": -0.358240336}, "rotation": {"x": -0.00214235066, "y": 134.999557, "z": -0.00130094273}}, {"objectName": "Bowl_697b561f", "position": {"x": -2.608369, "y": 0.714565456, "z": -0.841820061}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}], "object_toggles": [], "random_seed": 2166448054, "scene_num": 19}, "task_id": "trial_T20190907_144603_396680", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A1MKCTVKE7J0ZP_358UUM7WR2KR18CV3HABI28ZVZ47RE", "high_descs": ["Turn left, go straight, then turn left past the fridge, then turn right to face the table.", "Pick up the tomato, nearest to the coffee maker.", "Turn right and then go toward the microwave in front of you.", "Put the tomato on the right front corner of the counter.", "Turn around and return to the table.", "Pick up the tomato, on the table and near the fork.", "Turn right and return to the counter with the microwave. ", "Put the tomato on the counter, behind the other tomato."], "task_desc": "Put two tomatoes on the counter.", "votes": [1, 1]}, {"assignment_id": "A2A028LRDJB7ZB_3K3R2QNK8EKNVJS5M83019MPXOP9UP", "high_descs": ["Turn to your left cross the room then turn left stay beside the fridge", "Pick up the furthest tomato on table beside the coffee maker", "Turn to your right walk towards the counter beside the stove", "Place the tomato beside the spoon on the counter", "Turn around to your right then head to the table", "Pick up the tomato that is at the edge of the table", "Pick up the tomato that is at the edge of the table", "Put the tomato right next to the other tomato"], "task_desc": "Put the two tomato on the counter ", "votes": [1, 1]}, {"assignment_id": "A2BLQ1GVEHJR8T_3KOPY89HMBJS21SVT0YC3UASGFOJ3R", "high_descs": ["Turn to your left and cross the room to the round table in the corner.", "Pick up the tomato that is closest to the coffee maker.", "Turn right and cross the room to the microwave. ", "Put the tomato of the right front corner of the counter the microwave is sitting on, just to the left of the stove.", "Go back to the round corner table.", "Pick up the remaining tomato.", "Carry the tomato to the counter where the other tomato is.", "Place the tomato on the counter, behind the first tomato."], "task_desc": "Put two tomatoes on a counter to the left of the stove.", "votes": [1, 1]}, {"assignment_id": "A320QA9HJFUOZO_3B837J3LDRDDK6WNRK8MDT4VILURSE", "high_descs": ["Turn left and walk over to the round wooden table", "\nPick up the tomato nearest the coffee maker from the table", "Turn right and walk across to the kitchen counter by the microwave", "Put the tomato down on the counter just left of the oven", "Turn around and walk back to the round table", "Pick up the other tomato from the round table", "Turn right and walk back to the counter by the microwave", "Put the tomato down on the counter just behind the other tomato"], "task_desc": "Put the tomatoes on the kitchen counter", "votes": [1, 1]}, {"assignment_id": "A3PPLDHC3CG0YN_3G0WWMR1UY1GSSPI692099KKZZEQNS", "high_descs": ["Turn left and walk to the table, face the table where the three tomatoes are. ", "Pick up the tomato to the left of the coffee maker. ", "Move to the counter with the microwave on it. ", "Place the tomato on the counter to the right of the spoon. ", "Turn around and walk back to the table, face the table where the two tomatoes are. ", "Pick up the tomato that's on the left edge of the table. ", "Turn right and walk back to the counter with the microwave on it. ", "Put the tomato down on the counter behind the first tomato. "], "task_desc": "To move two tomatoes to the counter with the microwave on it. ", "votes": [1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3UOUJI6MTGVDWFA3PO9EHC874YPUXG", "high_descs": ["Turn left, move forward, turn left, then turn right to face the table.", "Pick up the tomato on the table, nearest to the coffee maker.", "Turn right and go to the counter ahead, near the microwave.", "Put the tomato on the counter, to the right of the spoon.", "Turn around, go straight, then turn right to return to the table.", "Pick up a tomato on the table, to the left of the apple.", "Turn right, and return to the counter in front of you.", "Put the tomato on the counter, behind the other tomato."], "task_desc": "Move two tomatoes to the counter.", "votes": [1, 1]}]}}