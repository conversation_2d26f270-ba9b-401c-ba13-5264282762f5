{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000108.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000298.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000306.png", "low_idx": 50}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "RemoteControl", "parent_target": "ArmChair", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["remotecontrol"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|0|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["remotecontrol"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["RemoteControl", [6.40295124, 6.40295124, 0.36553872, 0.36553872, 3.0388292, 3.0388292]], "forceVisible": true, "objectId": "RemoteControl|+01.60|+00.76|+00.09"}}, {"discrete_action": {"action": "GotoLocation", "args": ["armchair"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-3|4|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["remotecontrol", "armchair"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["RemoteControl", [6.40295124, 6.40295124, 0.36553872, 0.36553872, 3.0388292, 3.0388292]], "coordinateReceptacleObjectId": ["ArmChair", [-2.532, -2.532, 8.776, 8.776, 0.0, 0.0]], "forceVisible": true, "objectId": "RemoteControl|+01.60|+00.76|+00.09", "receptacleObjectId": "ArmChair|-00.63|+00.00|+02.19"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sofa"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-3|4|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["remotecontrol"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["RemoteControl", [-5.08361624, -5.08361624, 4.20447064, 4.20447064, 1.3189456, 1.3189456]], "coordinateReceptacleObjectId": ["So<PERSON>", [-5.78, -5.78, 1.68, 1.68, -0.112, -0.112]], "forceVisible": true, "objectId": "RemoteControl|-01.27|+00.33|+01.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["armchair"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-3|4|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["remotecontrol", "armchair"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["RemoteControl", [-5.08361624, -5.08361624, 4.20447064, 4.20447064, 1.3189456, 1.3189456]], "coordinateReceptacleObjectId": ["ArmChair", [-2.532, -2.532, 8.776, 8.776, 0.0, 0.0]], "forceVisible": true, "objectId": "RemoteControl|-01.27|+00.33|+01.05", "receptacleObjectId": "ArmChair|-00.63|+00.00|+02.19"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "RemoteControl|+01.60|+00.76|+00.09"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [116, 100, 134, 145], "mask": [[29823, 9], [30122, 11], [30421, 12], [30721, 12], [31021, 13], [31321, 13], [31621, 13], [31920, 14], [32220, 14], [32520, 14], [32820, 14], [33120, 14], [33419, 15], [33719, 15], [34019, 15], [34319, 16], [34619, 16], [34918, 17], [35218, 17], [35518, 17], [35818, 17], [36118, 17], [36418, 17], [36718, 17], [37018, 17], [37318, 17], [37617, 18], [37917, 18], [38217, 18], [38517, 18], [38817, 18], [39117, 18], [39417, 18], [39717, 18], [40017, 18], [40317, 17], [40617, 17], [40917, 17], [41217, 17], [41516, 18], [41816, 18], [42116, 18], [42416, 18], [42716, 18], [43016, 18], [43317, 17]], "point": [125, 121]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "RemoteControl|+01.60|+00.76|+00.09", "placeStationary": true, "receptacleObjectId": "ArmChair|-00.63|+00.00|+02.19"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [93, 1, 257, 155], "mask": [[107, 125], [406, 127], [706, 127], [1006, 127], [1306, 128], [1606, 128], [1906, 128], [2206, 128], [2505, 130], [2805, 130], [3105, 130], [3405, 131], [3705, 131], [4005, 131], [4305, 131], [4604, 133], [4904, 133], [5204, 133], [5504, 133], [5804, 134], [6104, 134], [6404, 134], [6703, 136], [7003, 136], [7303, 136], [7603, 136], [7903, 137], [8203, 137], [8502, 138], [8802, 138], [9102, 124], [9227, 14], [9402, 123], [9527, 14], [9702, 123], [9827, 14], [10002, 123], [10127, 15], [10302, 123], [10427, 15], [10601, 123], [10728, 14], [10901, 123], [11028, 14], [11201, 123], [11328, 15], [11501, 122], [11628, 15], [11801, 122], [11929, 14], [12101, 122], [12229, 14], [12401, 122], [12529, 15], [12700, 122], [12829, 15], [13000, 122], [13130, 14], [13300, 122], [13430, 15], [13600, 121], [13730, 15], [13900, 121], [14030, 15], [14200, 12], [14213, 108], [14330, 15], [14500, 12], [14513, 108], [14631, 15], [14799, 13], [14813, 107], [14931, 15], [15099, 13], [15113, 107], [15231, 15], [15399, 13], [15413, 107], [15531, 15], [15699, 13], [15713, 107], [15832, 15], [15999, 12], [16014, 105], [16132, 15], [16299, 12], [16314, 105], [16432, 15], [16599, 12], [16614, 105], [16732, 16], [16898, 13], [16914, 104], [17033, 15], [17198, 13], [17214, 104], [17333, 15], [17498, 13], [17514, 104], [17633, 15], [17798, 13], [17814, 104], [17933, 16], [18098, 13], [18115, 102], [18233, 16], [18398, 13], [18415, 102], [18534, 15], [18698, 12], [18715, 102], [18834, 16], [18997, 13], [19015, 101], [19134, 16], [19297, 13], [19315, 101], [19434, 16], [19597, 13], [19615, 101], [19735, 15], [19897, 13], [19915, 101], [20035, 16], [20197, 13], [20216, 99], [20335, 16], [20497, 13], [20516, 99], [20635, 16], [20797, 13], [20816, 99], [20936, 15], [21096, 14], [21116, 99], [21236, 16], [21396, 13], [21415, 101], [21536, 16], [21696, 13], [21715, 101], [21836, 16], [21996, 13], [22015, 101], [22136, 17], [22296, 13], [22315, 101], [22437, 16], [22596, 13], [22615, 101], [22737, 16], [22896, 13], [22915, 101], [23037, 16], [23195, 14], [23215, 102], [23337, 17], [23495, 14], [23515, 102], [23638, 16], [23795, 14], [23815, 102], [23938, 16], [24095, 13], [24115, 102], [24238, 16], [24395, 13], [24415, 102], [24538, 17], [24695, 13], [24714, 104], [24839, 16], [24995, 13], [25014, 104], [25139, 16], [25294, 14], [25314, 104], [25439, 17], [25594, 14], [25614, 63], [25681, 37], [25739, 17], [25894, 14], [25914, 62], [25981, 37], [26039, 17], [26194, 14], [26214, 51], [26268, 9], [26281, 37], [26340, 16], [26494, 14], [26514, 53], [26571, 6], [26580, 1], [26582, 1], [26584, 35], [26640, 17], [26794, 14], [26814, 54], [26871, 7], [26879, 40], [26940, 17], [27094, 13], [27114, 57], [27172, 4], [27177, 1], [27179, 40], [27240, 17], [27393, 14], [27414, 58], [27475, 3], [27479, 40], [27540, 17], [27693, 15], [27714, 59], [27776, 43], [27840, 18], [27993, 15], [28013, 58], [28076, 43], [28140, 18], [28293, 15], [28313, 56], [28371, 49], [28439, 18], [28594, 14], [28613, 107], [28739, 18], [28894, 14], [28913, 107], [29039, 17], [29194, 14], [29213, 107], [29338, 18], [29495, 14], [29513, 107], [29638, 17], [29795, 14], [29813, 108], [29937, 18], [30095, 14], [30113, 108], [30237, 17], [30395, 14], [30413, 108], [30536, 18], [30696, 14], [30713, 108], [30836, 17], [30996, 14], [31012, 109], [31135, 18], [31296, 14], [31312, 109], [31435, 17], [31596, 14], [31612, 110], [31735, 17], [31897, 13], [31912, 110], [32034, 17], [32197, 14], [32212, 110], [32334, 17], [32497, 14], [32512, 110], [32633, 17], [32798, 13], [32812, 110], [32933, 17], [33098, 13], [33112, 110], [33232, 17], [33398, 13], [33412, 111], [33532, 17], [33698, 125], [33832, 16], [33999, 124], [34131, 16], [34299, 124], [34431, 16], [34599, 124], [34730, 16], [34899, 63], [34976, 48], [35030, 16], [35200, 62], [35276, 48], [35329, 16], [35500, 62], [35576, 48], [35629, 16], [35800, 62], [35876, 48], [35929, 15], [36101, 61], [36176, 48], [36228, 16], [36401, 61], [36476, 48], [36528, 15], [36701, 61], [36776, 49], [36827, 16], [37001, 124], [37127, 15], [37302, 123], [37426, 16], [37602, 123], [37726, 15], [37902, 139], [38203, 137], [38503, 137], [38803, 136], [39103, 136], [39404, 134], [39704, 134], [40004, 133], [40304, 132], [40605, 131], [40905, 130], [41205, 130], [41506, 128], [41806, 128], [42106, 127], [42406, 127], [42707, 125], [43007, 125], [43307, 124], [43607, 124], [43908, 122], [44208, 122], [44508, 121], [44809, 120], [45109, 119], [45409, 119], [45709, 118], [46010, 117], [46310, 116]], "point": [175, 77]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "RemoteControl|-01.27|+00.33|+01.05"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [153, 167, 166, 204], "mask": [[49955, 9], [50255, 9], [50555, 9], [50855, 9], [51155, 9], [51455, 10], [51754, 11], [52054, 11], [52354, 11], [52654, 11], [52954, 11], [53254, 11], [53554, 12], [53854, 12], [54154, 12], [54454, 12], [54753, 13], [55053, 13], [55353, 13], [55653, 13], [55953, 13], [56253, 13], [56553, 13], [56853, 13], [57153, 13], [57453, 13], [57753, 13], [58053, 13], [58353, 13], [58653, 14], [58953, 14], [59253, 14], [59553, 14], [59853, 14], [60154, 13], [60454, 13], [60754, 13], [61054, 13]], "point": [159, 184]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "RemoteControl|-01.27|+00.33|+01.05", "placeStationary": true, "receptacleObjectId": "ArmChair|-00.63|+00.00|+02.19"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [93, 1, 257, 155], "mask": [[107, 125], [406, 127], [706, 127], [1006, 127], [1306, 128], [1606, 128], [1906, 128], [2206, 128], [2505, 130], [2805, 130], [3105, 130], [3405, 131], [3705, 131], [4005, 131], [4305, 131], [4604, 133], [4904, 133], [5204, 133], [5504, 133], [5804, 134], [6104, 134], [6404, 134], [6703, 136], [7003, 136], [7303, 136], [7603, 136], [7903, 137], [8203, 137], [8502, 138], [8802, 138], [9102, 124], [9227, 14], [9402, 123], [9527, 14], [9702, 123], [9827, 14], [10002, 123], [10127, 15], [10302, 123], [10427, 15], [10601, 123], [10728, 14], [10901, 123], [11028, 14], [11201, 123], [11328, 15], [11501, 122], [11628, 15], [11801, 122], [11929, 14], [12101, 122], [12229, 14], [12401, 122], [12529, 15], [12700, 122], [12829, 15], [13000, 122], [13130, 14], [13300, 122], [13430, 15], [13600, 121], [13730, 15], [13900, 121], [14030, 15], [14200, 12], [14213, 108], [14330, 15], [14500, 12], [14513, 108], [14631, 15], [14799, 13], [14813, 107], [14931, 15], [15099, 13], [15113, 107], [15231, 15], [15399, 13], [15413, 107], [15531, 15], [15699, 13], [15713, 107], [15832, 15], [15999, 12], [16014, 105], [16132, 15], [16299, 12], [16314, 105], [16432, 15], [16599, 12], [16614, 105], [16732, 16], [16898, 13], [16914, 104], [17033, 15], [17198, 13], [17214, 104], [17333, 15], [17498, 13], [17514, 104], [17633, 15], [17798, 13], [17814, 104], [17933, 16], [18098, 13], [18115, 102], [18233, 16], [18398, 13], [18415, 102], [18534, 15], [18698, 12], [18715, 102], [18834, 16], [18997, 13], [19015, 101], [19134, 16], [19297, 13], [19315, 101], [19434, 16], [19597, 13], [19615, 101], [19735, 15], [19897, 13], [19915, 101], [20035, 16], [20197, 13], [20216, 99], [20335, 16], [20497, 13], [20516, 99], [20635, 16], [20797, 13], [20816, 99], [20936, 15], [21096, 14], [21116, 99], [21236, 16], [21396, 13], [21415, 101], [21536, 16], [21696, 13], [21715, 101], [21836, 16], [21996, 13], [22015, 101], [22136, 17], [22296, 13], [22315, 101], [22437, 16], [22596, 13], [22615, 101], [22737, 16], [22896, 13], [22915, 101], [23037, 16], [23195, 14], [23215, 102], [23337, 17], [23495, 14], [23515, 102], [23638, 16], [23795, 14], [23815, 102], [23938, 16], [24095, 13], [24115, 102], [24238, 16], [24395, 13], [24415, 102], [24538, 17], [24695, 13], [24714, 104], [24839, 16], [24995, 13], [25014, 104], [25139, 16], [25294, 14], [25314, 104], [25439, 17], [25594, 14], [25614, 63], [25681, 37], [25739, 17], [25894, 14], [25914, 62], [25981, 37], [26039, 17], [26194, 14], [26214, 51], [26268, 9], [26281, 37], [26340, 16], [26494, 14], [26514, 53], [26571, 6], [26580, 1], [26582, 1], [26584, 35], [26640, 17], [26794, 14], [26814, 54], [26871, 7], [26879, 40], [26940, 17], [27094, 13], [27114, 57], [27172, 4], [27177, 1], [27179, 40], [27240, 17], [27393, 14], [27414, 58], [27475, 3], [27479, 40], [27540, 17], [27693, 15], [27714, 59], [27776, 43], [27840, 18], [27993, 15], [28013, 58], [28076, 43], [28140, 18], [28293, 15], [28313, 56], [28371, 49], [28439, 18], [28594, 14], [28613, 107], [28739, 18], [28894, 14], [28913, 107], [29039, 17], [29194, 14], [29213, 107], [29338, 18], [29495, 14], [29513, 107], [29638, 17], [29795, 14], [29813, 108], [29937, 18], [30095, 14], [30113, 108], [30237, 17], [30395, 14], [30413, 108], [30536, 18], [30696, 14], [30713, 108], [30836, 17], [30996, 14], [31012, 14], [31036, 85], [31135, 18], [31296, 14], [31312, 14], [31337, 84], [31435, 17], [31596, 14], [31612, 14], [31637, 85], [31735, 17], [31897, 13], [31912, 14], [31937, 85], [32034, 17], [32197, 14], [32212, 14], [32237, 85], [32334, 17], [32497, 14], [32512, 14], [32536, 86], [32633, 17], [32798, 13], [32812, 14], [32836, 86], [32933, 17], [33098, 13], [33112, 14], [33136, 86], [33232, 17], [33398, 13], [33412, 14], [33436, 87], [33532, 17], [33698, 28], [33736, 87], [33832, 16], [33999, 26], [34036, 87], [34131, 16], [34299, 26], [34336, 87], [34431, 16], [34599, 26], [34636, 87], [34730, 16], [34899, 26], [34936, 26], [34976, 48], [35030, 16], [35200, 25], [35236, 26], [35276, 48], [35329, 16], [35500, 25], [35536, 26], [35576, 48], [35629, 16], [35800, 25], [35836, 26], [35876, 48], [35929, 15], [36101, 24], [36136, 26], [36176, 48], [36228, 16], [36401, 24], [36436, 26], [36476, 48], [36528, 15], [36701, 24], [36736, 26], [36776, 49], [36827, 16], [37001, 25], [37035, 90], [37127, 15], [37302, 24], [37335, 90], [37426, 16], [37602, 24], [37635, 90], [37726, 15], [37902, 24], [37935, 106], [38203, 23], [38235, 105], [38503, 23], [38535, 105], [38803, 23], [38834, 105], [39103, 23], [39134, 105], [39404, 22], [39434, 104], [39704, 134], [40004, 133], [40304, 132], [40605, 131], [40905, 130], [41205, 130], [41506, 128], [41806, 128], [42106, 127], [42406, 127], [42707, 125], [43007, 125], [43307, 124], [43607, 124], [43908, 122], [44208, 122], [44508, 121], [44809, 120], [45109, 119], [45409, 119], [45709, 118], [46010, 117], [46310, 116]], "point": [175, 77]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan207", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 0.5, "y": 0.9009992, "z": 3.25}, "object_poses": [{"objectName": "Watch_6b796512", "position": {"x": -0.301128268, "y": 0.47745198, "z": 0.618746}, "rotation": {"x": 0.0, "y": 89.9998245, "z": 0.0}}, {"objectName": "Statue_7ce9b434", "position": {"x": -0.0518449247, "y": 0.4833651, "z": -0.08663845}, "rotation": {"x": 0.0, "y": 89.9998245, "z": 0.0}}, {"objectName": "Statue_7ce9b434", "position": {"x": 1.52567053, "y": 0.767064, "z": 0.3916782}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "KeyChain_2d83981f", "position": {"x": -0.5551634, "y": 0.291835755, "z": 2.20027065}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_8cb9b9d4", "position": {"x": -0.633, "y": 0.290996, "z": 1.95654154}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_8cb9b9d4", "position": {"x": 1.75087607, "y": 0.7600796, "z": 0.691971}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "RemoteControl_5dced9ac", "position": {"x": 1.67580569, "y": 0.7597073, "z": -0.0587622523}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "RemoteControl_5dced9ac", "position": {"x": 1.60073781, "y": 0.7597073, "z": 0.09138468}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Box_d6526770", "position": {"x": -0.134939715, "y": 0.6569344, "z": 0.266053855}, "rotation": {"x": 0.0, "y": 89.9998245, "z": 0.0}}, {"objectName": "Laptop_e596732c", "position": {"x": -1.27090251, "y": 0.331190884, "z": 0.6437116}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "CreditCard_8cb9b9d4", "position": {"x": 1.60151029, "y": 0.179175138, "z": 0.176152185}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "KeyChain_2d83981f", "position": {"x": 1.75087345, "y": 0.757835746, "z": -0.208909154}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Vase_625d2f5f", "position": {"x": 1.73348939, "y": 0.184338629, "z": 0.241720751}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Vase_31bce8de", "position": {"x": -1.80041933, "y": 1.616, "z": -1.78580987}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pillow_e6d56e59", "position": {"x": -1.235, "y": 0.461, "z": -0.025}, "rotation": {"x": 0.0, "y": 75.00003, "z": 0.0}}, {"objectName": "RemoteControl_5dced9ac", "position": {"x": -1.27090406, "y": 0.3297364, "z": 1.05111766}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "Watch_6b796512", "position": {"x": -1.76253116, "y": 0.489272654, "z": 1.62416875}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_77e7f5e9", "position": {"x": -1.79980278, "y": 1.31599987, "z": -1.47010064}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_7ce9b434", "position": {"x": -1.50315619, "y": 0.499063939, "z": 1.94838738}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1717950418, "scene_num": 207}, "task_id": "trial_T20190908_130008_528143", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3SFPSMFRSRTB3_3CFJTT4SXWHMTYVA925J5WRA2YJ7IV", "high_descs": ["Turn right, proceed to table stand on left.  Turn left to face tv stand.", "Pick up left remote from tv stand.", "Turn around, proceed to couch.  Turn left and proceed to black chair.", "Put remote on chair to the left of the credit card and keys.", "Turn left to face the couch.", "Pick up remote from the couch, to the right of the laptop.", "Turn right to face chair.", "Put remote on chair to the right of the credit card."], "task_desc": "Move two remotes to a chair.", "votes": [1, 1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3FIJLY1B6XVRPDXK4GTF2L53M0UPFQ", "high_descs": ["Turn left, go around the chair to go to the entertainment center with the TV on it.", "Pick up the leftmost remote on the entertainment center.", "Turn right, go forward, turn right and go between the couch and coffee table until you reach the chair.", "Put the remote on the chair, to the left of the credit card.", "Turn left and look down at the couch.", "Pick up the remote on the couch.", "Turn right and look down at the chair.", "Put the remote on the chair, to the right of the credit card."], "task_desc": "Move two remotes to the chair.", "votes": [0, 1, 1]}, {"assignment_id": "ARB8SJAWXUWTD_3YMU66OBIQZ5QWVRWNRSBX3O1ORGHI", "high_descs": ["Go to the TV table", "Pick up a remote", "Go to the left couch", "Put the remote down on the couch", "Go to the sofa", "Pick up a remote", "Go to the left couch", "Put the remote down on the couch"], "task_desc": "Move two remotes to a couch", "votes": [1, 0, 1]}]}}