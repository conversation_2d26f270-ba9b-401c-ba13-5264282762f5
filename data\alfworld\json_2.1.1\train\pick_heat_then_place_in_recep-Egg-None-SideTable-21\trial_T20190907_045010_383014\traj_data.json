{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 63}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-2|1|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-1.8145628, -1.8145628, 3.0326848, 3.0326848, 3.748904, 3.748904]], "coordinateReceptacleObjectId": ["DiningTable", [-2.096, -2.096, 3.804, 3.804, 0.024, 0.024]], "forceVisible": true, "objectId": "Egg|-00.45|+00.94|+00.76"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-8|-11|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|1|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "sidetable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-1.8145628, -1.8145628, 3.0326848, 3.0326848, 3.748904, 3.748904]], "coordinateReceptacleObjectId": ["SideTable", [3.0924, 3.0924, 1.7924, 1.7924, 2.8004, 2.8004]], "forceVisible": true, "objectId": "Egg|-00.45|+00.94|+00.76", "receptacleObjectId": "SideTable|+00.77|+00.70|+00.45"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.45|+00.94|+00.76"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [156, 96, 174, 117], "mask": [[28662, 7], [28961, 10], [29259, 13], [29559, 14], [29858, 15], [30157, 17], [30457, 17], [30757, 17], [31056, 19], [31356, 19], [31656, 19], [31956, 19], [32256, 19], [32556, 19], [32856, 18], [33156, 18], [33457, 17], [33757, 16], [34058, 14], [34359, 13], [34660, 10], [34961, 8]], "point": [165, 105]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.45|+00.94|+00.76", "placeStationary": true, "receptacleObjectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 188], [31250, 188], [31550, 188], [31849, 189], [32149, 189], [32448, 190], [32748, 191], [33047, 192], [33347, 192], [33646, 193], [33946, 192], [34245, 193], [34545, 193], [34844, 194], [35144, 194], [35443, 194], [35743, 194], [36043, 194], [36342, 195], [36642, 194], [36941, 195], [37241, 195], [37540, 195], [37840, 195], [38139, 196], [38439, 195], [38738, 196], [39038, 196], [39337, 197], [39637, 196], [39936, 197], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 188], [31250, 188], [31550, 188], [31849, 189], [32149, 189], [32448, 190], [32748, 191], [33047, 192], [33347, 192], [33646, 193], [33946, 192], [34245, 193], [34545, 193], [34844, 106], [34951, 87], [35144, 103], [35253, 85], [35443, 103], [35555, 82], [35743, 103], [35855, 82], [36043, 102], [36156, 81], [36342, 103], [36456, 81], [36642, 102], [36757, 79], [36941, 103], [37057, 79], [37241, 103], [37357, 79], [37540, 104], [37657, 78], [37840, 104], [37957, 78], [38139, 105], [38257, 78], [38439, 105], [38557, 77], [38738, 107], [38856, 78], [39038, 107], [39156, 78], [39337, 109], [39455, 79], [39637, 110], [39754, 79], [39936, 113], [40052, 81], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.45|+00.94|+00.76"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [144, 117, 156, 134], "mask": [[34950, 1], [35247, 6], [35546, 9], [35846, 9], [36145, 11], [36445, 11], [36744, 13], [37044, 13], [37344, 13], [37644, 13], [37944, 13], [38244, 13], [38544, 13], [38845, 11], [39145, 11], [39446, 9], [39747, 7], [40049, 3]], "point": [150, 124]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 188], [31250, 188], [31550, 188], [31849, 189], [32149, 189], [32448, 190], [32748, 191], [33047, 192], [33347, 192], [33646, 193], [33946, 192], [34245, 193], [34545, 193], [34844, 194], [35144, 194], [35443, 194], [35743, 194], [36043, 194], [36342, 195], [36642, 194], [36941, 195], [37241, 195], [37540, 195], [37840, 195], [38139, 196], [38439, 195], [38738, 196], [39038, 196], [39337, 197], [39637, 196], [39936, 197], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.45|+00.94|+00.76", "placeStationary": true, "receptacleObjectId": "SideTable|+00.77|+00.70|+00.45"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [53, 105, 160, 237], "mask": [[31275, 25], [31347, 11], [31574, 25], [31647, 11], [31874, 25], [31947, 12], [32174, 25], [32247, 12], [32473, 26], [32547, 12], [32773, 26], [32847, 12], [33073, 25], [33147, 12], [33372, 26], [33446, 13], [33672, 26], [33746, 13], [33971, 27], [34046, 13], [34271, 27], [34346, 13], [34571, 28], [34645, 14], [34870, 29], [34945, 14], [35170, 29], [35245, 14], [35470, 29], [35544, 15], [35769, 30], [35844, 15], [36069, 32], [36143, 16], [36369, 10], [36381, 22], [36441, 18], [36668, 11], [36681, 25], [36739, 20], [36968, 10], [36981, 26], [37038, 21], [37268, 10], [37281, 27], [37337, 22], [37567, 11], [37581, 29], [37637, 22], [37867, 10], [37882, 29], [37936, 23], [38167, 10], [38181, 31], [38235, 24], [38466, 11], [38481, 32], [38534, 25], [38766, 10], [38781, 36], [38830, 29], [39066, 10], [39080, 79], [39365, 11], [39380, 80], [39665, 10], [39680, 80], [39964, 11], [39979, 81], [40264, 11], [40278, 82], [40564, 11], [40578, 82], [40863, 11], [40877, 83], [41163, 11], [41177, 83], [41463, 1], [41471, 3], [41476, 58], [41542, 18], [41772, 1], [41776, 52], [41842, 18], [42075, 52], [42142, 18], [42375, 51], [42442, 18], [42675, 7], [42713, 12], [42742, 18], [42975, 6], [43003, 3], [43042, 18], [43276, 5], [43286, 38], [43342, 18], [43576, 49], [43641, 19], [43876, 50], [43940, 20], [44177, 49], [44233, 27], [44477, 83], [44776, 84], [45076, 84], [45376, 84], [45658, 1], [45675, 85], [45957, 3], [45975, 85], [46257, 4], [46274, 86], [46557, 5], [46573, 87], [46856, 7], [46872, 89], [47156, 10], [47171, 90], [47456, 105], [47755, 106], [48055, 106], [48355, 106], [48654, 107], [48954, 107], [49254, 107], [49553, 107], [49854, 106], [50154, 106], [50454, 12], [50472, 88], [50755, 10], [50772, 88], [51055, 9], [51072, 88], [51356, 8], [51371, 89], [51656, 8], [51671, 89], [51956, 8], [51971, 89], [52257, 8], [52271, 89], [52557, 8], [52570, 90], [52858, 7], [52870, 90], [53158, 8], [53170, 90], [53458, 8], [53469, 91], [53759, 8], [53769, 91], [54059, 8], [54069, 91], [54360, 7], [54369, 91], [54660, 100], [54960, 100], [55261, 99], [55561, 99], [55861, 99], [56162, 97], [56462, 97], [56763, 96], [57063, 96], [57363, 8], [57380, 51], [57437, 2], [57448, 11], [57664, 7], [57680, 51], [57737, 2], [57748, 11], [57964, 8], [57980, 51], [58037, 1], [58048, 11], [58265, 7], [58280, 51], [58337, 1], [58349, 10], [58565, 7], [58580, 51], [58637, 1], [58648, 11], [58865, 8], [58879, 52], [58936, 2], [58948, 11], [59166, 7], [59179, 52], [59236, 2], [59248, 11], [59466, 7], [59479, 52], [59536, 3], [59548, 11], [59767, 7], [59779, 54], [59834, 7], [59842, 1], [59848, 11], [60067, 7], [60078, 81], [60367, 8], [60378, 81], [60668, 7], [60678, 81], [60968, 7], [60978, 81], [61269, 7], [61277, 82], [61569, 7], [61577, 82], [61869, 7], [61877, 82], [62170, 89], [62470, 89], [62771, 88], [63071, 88], [63371, 87], [63672, 86], [63972, 86], [64273, 85], [64573, 7], [64654, 4], [64873, 7], [64954, 4], [65174, 6], [65254, 4], [65474, 7], [65554, 4], [65775, 6], [65854, 4], [66075, 6], [66154, 4], [66375, 7], [66454, 4], [66676, 6], [66754, 4], [66976, 6], [67054, 4], [67277, 6], [67354, 4], [67577, 6], [67654, 4], [67877, 7], [67954, 4], [68178, 6], [68254, 4], [68478, 6], [68554, 4], [68779, 6], [68854, 4], [69079, 6], [69154, 4], [69379, 6], [69454, 4], [69680, 4], [69754, 4], [69980, 4], [70054, 4], [70281, 3], [70354, 3], [70584, 1], [70883, 2]], "point": [106, 170]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan21", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -0.75, "y": 0.869696259, "z": -0.75}, "object_poses": [{"objectName": "DishSponge_11e4138d", "position": {"x": -0.8554554, "y": 0.900185, "z": 0.7581735}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "DishSponge_11e4138d", "position": {"x": 0.729649961, "y": 0.8831642, "z": -0.10800001}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_0ad5c562", "position": {"x": 0.574625134, "y": 0.7605971, "z": -0.188901335}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_8142ce7b", "position": {"x": 0.6616132, "y": 0.6803962, "z": 0.508934}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_8142ce7b", "position": {"x": 0.8662456, "y": 0.7755549, "z": -0.252836943}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_93a81026", "position": {"x": -1.97052181, "y": 0.702918768, "z": -0.983721733}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_93a81026", "position": {"x": -0.0518234968, "y": 0.901750147, "z": 1.18701339}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Fork_34f12e87", "position": {"x": -0.855452836, "y": 0.9011693, "z": 1.18701792}, "rotation": {"x": 0.0, "y": 180.00032, "z": 0.0}}, {"objectName": "Fork_34f12e87", "position": {"x": -1.88688087, "y": 0.7023379, "z": -1.09179544}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_cde1951c", "position": {"x": -2.097, "y": 0.9306, "z": 1.049}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_c79ff911", "position": {"x": -1.63595831, "y": 0.7383946, "z": -1.19986916}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": -1.26100016, "y": 0.100637436, "z": -3.57439852}, "rotation": {"x": 0.0, "y": 269.999878, "z": 0.0}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": 0.8244622, "y": 0.5143165, "z": -1.9580003}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bowl_737b77b7", "position": {"x": 0.841818154, "y": 0.6638305, "z": 0.387266}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_737b77b7", "position": {"x": -0.05182445, "y": 0.899054945, "z": 1.01547563}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "PepperShaker_87875872", "position": {"x": 0.842995167, "y": 0.01202476, "z": -0.8333649}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_87875872", "position": {"x": 0.877196848, "y": 0.8799062, "z": 0.09879146}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_886fcaf3", "position": {"x": -1.5567826, "y": 0.692880869, "z": -3.27692056}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_0ad3089c", "position": {"x": 0.7994509, "y": 0.859162, "z": -0.735619843}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Apple_093f2b24", "position": {"x": 0.601545, "y": 0.7188863, "z": 0.6306019}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_cde1951c", "position": {"x": -1.552, "y": 0.9306, "z": 0.785}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_2b33f66e", "position": {"x": -2.02399969, "y": 0.69248, "z": -1.44700074}, "rotation": {"x": -3.713544e-06, "y": -0.0002253917, "z": 1.925994e-05}}, {"objectName": "Fork_34f12e87", "position": {"x": 0.671831965, "y": 0.760416448, "z": 0.00290554762}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_d7803c74", "position": {"x": 0.7323308, "y": 0.0149986744, "z": -0.250064164}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_093f2b24", "position": {"x": 0.8034234, "y": 0.93709, "z": 0.02986098}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_63693e0a", "position": {"x": 0.7401087, "y": -0.007886887, "z": -0.596487045}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_c79ff911", "position": {"x": -0.4536407, "y": 0.937226, "z": 0.7581712}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Spatula_8142ce7b", "position": {"x": -1.53555322, "y": 0.7115746, "z": -3.78265572}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_027e0a30", "position": {"x": 0.6729982, "y": 0.805609345, "z": -0.845712662}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Cup_cb0de209", "position": {"x": -1.71959925, "y": 0.700419068, "z": -0.875647962}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_886fcaf3", "position": {"x": 0.812662363, "y": 1.46013677, "z": 0.5966235}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_87875872", "position": {"x": 0.787663, "y": 0.01202476, "z": 0.07674818}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_0ad5c562", "position": {"x": 0.721681535, "y": 0.6646421, "z": 0.630601943}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_11e4138d", "position": {"x": -1.71959925, "y": 0.7013536, "z": -1.52409041}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_42314d78", "position": {"x": -0.873921752, "y": 0.931037068, "z": 0.978991}, "rotation": {"x": 0.02980692, "y": 24.2316418, "z": 359.895}}, {"objectName": "Lettuce_0ad3089c", "position": {"x": 0.7856681, "y": 1.24533343, "z": -2.035625}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": -0.25273174, "y": 0.949009538, "z": 1.0154767}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Spoon_93a81026", "position": {"x": 0.7690388, "y": 0.7616844, "z": 0.00290554762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_33d4fba0", "position": {"x": 0.7407, "y": 0.7546, "z": -0.1881}, "rotation": {"x": 270.0, "y": 315.0, "z": 0.0}}, {"objectName": "Mug_288ff1e3", "position": {"x": 0.67299825, "y": 0.7675562, "z": -0.515433967}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Bowl_737b77b7", "position": {"x": -1.05636024, "y": 0.899054945, "z": 1.18701911}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}], "object_toggles": [], "random_seed": 1259761766, "scene_num": 21}, "task_id": "trial_T20190907_045010_383014", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AM2KK02JXXW48_3R6P78PK7NSNAG0O09J0OUMAIFBGTE", "high_descs": ["Turn around and head to the table with the toaster.", "Pick up the egg on the table. ", "Turn around and head to the microwave with the egg. ", "Heat the egg in the microwave. ", "Take the egg, turn around and head over to the small black table.", "Put the egg on the small black table."], "task_desc": "Put a heated egg on the small black table. ", "votes": [1, 1]}, {"assignment_id": "A2A4UAFZ5LW71K_3S06PH7KSULJPCEOW0YIC30ID78D12", "high_descs": ["turn around, walk forward to white table with toaster on it", "pick up egg that is on the table", "turn around, walk across to the white table with microwave on it", "open microwave, place egg in microwave, close microwave, turn microwave on, remove egg from microwave", "turn around, walk across towards white table with toaster on it, turn right, face small black table", "place egg on small black table"], "task_desc": "put cooked egg on table", "votes": [1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3Y9N9SS8L1SFZFS3J4C10WFFBSF3DF", "high_descs": ["Turn left and take a half step then turn left and walk to the toaster.", "Pick up the egg that's in front of the toaster.", "Turn around and walk to the microwave you see in the far corner and stand facing it.", "Cook the egg in the microwave and get it back out and close the door.", "Walk back over to the toaster and turn right to look at the small black table to the left of the sink.", "Put the egg on the edge of the small black table under the middle of the spatula."], "task_desc": "Put a cooked egg on the small black table.", "votes": [1, 1]}]}}