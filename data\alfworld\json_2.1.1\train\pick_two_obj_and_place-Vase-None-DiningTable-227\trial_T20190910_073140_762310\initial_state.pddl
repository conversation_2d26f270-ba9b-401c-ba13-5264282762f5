
(define (problem plan_trial_T20190910_073140_762310)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__minus_05_dot_69_bar__plus_00_dot_82_bar__plus_03_dot_27 - object
        Box_bar__minus_06_dot_06_bar__plus_00_dot_82_bar__plus_01_dot_51 - object
        Chair_bar__minus_05_dot_38_bar__plus_00_dot_02_bar__plus_02_dot_70 - object
        Chair_bar__minus_05_dot_39_bar__plus_00_dot_02_bar__plus_03_dot_37 - object
        Chair_bar__minus_05_dot_41_bar__plus_00_dot_02_bar__plus_01_dot_78 - object
        Chair_bar__minus_06_dot_21_bar__plus_00_dot_02_bar__plus_02_dot_52 - object
        Chair_bar__minus_06_dot_25_bar__plus_00_dot_02_bar__plus_03_dot_43 - object
        Chair_bar__minus_06_dot_30_bar__plus_00_dot_02_bar__plus_01_dot_67 - object
        CreditCard_bar__minus_01_dot_00_bar__plus_00_dot_27_bar__plus_02_dot_32 - object
        CreditCard_bar__minus_03_dot_13_bar__plus_00_dot_28_bar__plus_03_dot_38 - object
        FloorLamp_bar__minus_00_dot_37_bar__plus_00_dot_03_bar__plus_04_dot_99 - object
        HousePlant_bar__minus_01_dot_11_bar__plus_00_dot_31_bar__plus_02_dot_74 - object
        KeyChain_bar__minus_04_dot_78_bar__plus_01_dot_58_bar__plus_05_dot_21 - object
        Laptop_bar__minus_05_dot_69_bar__plus_00_dot_64_bar__plus_01_dot_51 - object
        LightSwitch_bar__plus_00_dot_00_bar__plus_01_dot_19_bar__plus_04_dot_41 - object
        Newspaper_bar__minus_05_dot_94_bar__plus_00_dot_64_bar__plus_03_dot_27 - object
        Newspaper_bar__minus_06_dot_06_bar__plus_00_dot_64_bar__plus_02_dot_21 - object
        Painting_bar__minus_00_dot_97_bar__plus_01_dot_40_bar__plus_05_dot_27 - object
        Painting_bar__minus_05_dot_77_bar__plus_01_dot_40_bar__minus_00_dot_03 - object
        Pillow_bar__minus_01_dot_52_bar__plus_00_dot_44_bar__plus_03_dot_32 - object
        Plate_bar__minus_02_dot_67_bar__plus_00_dot_31_bar__plus_02_dot_00 - object
        Plate_bar__minus_05_dot_69_bar__plus_00_dot_65_bar__plus_03_dot_62 - object
        RemoteControl_bar__minus_02_dot_72_bar__plus_00_dot_28_bar__plus_03_dot_38 - object
        RemoteControl_bar__minus_05_dot_45_bar__plus_00_dot_64_bar__plus_03_dot_27 - object
        Statue_bar__minus_00_dot_81_bar__plus_01_dot_57_bar__plus_00_dot_21 - object
        Statue_bar__minus_02_dot_44_bar__plus_00_dot_31_bar__plus_02_dot_00 - object
        Statue_bar__minus_03_dot_17_bar__plus_01_dot_11_bar__plus_05_dot_19 - object
        Statue_bar__minus_03_dot_85_bar__plus_00_dot_93_bar__plus_05_dot_17 - object
        Statue_bar__minus_05_dot_03_bar__plus_01_dot_57_bar__plus_05_dot_21 - object
        Statue_bar__minus_05_dot_87_bar__plus_00_dot_63_bar__plus_02_dot_79 - object
        Statue_bar__minus_06_dot_59_bar__plus_01_dot_02_bar__plus_05_dot_17 - object
        Television_bar__minus_03_dot_50_bar__plus_01_dot_51_bar__plus_00_dot_00 - object
        Vase_bar__minus_00_dot_76_bar__plus_00_dot_94_bar__plus_00_dot_20 - object
        Vase_bar__minus_01_dot_35_bar__plus_01_dot_26_bar__plus_00_dot_23 - object
        Vase_bar__minus_01_dot_70_bar__plus_00_dot_93_bar__plus_00_dot_18 - object
        Vase_bar__minus_01_dot_84_bar__plus_01_dot_29_bar__plus_00_dot_19 - object
        Vase_bar__minus_03_dot_18_bar__plus_00_dot_18_bar__plus_01_dot_72 - object
        Vase_bar__minus_04_dot_96_bar__plus_00_dot_94_bar__plus_05_dot_20 - object
        Window_bar__plus_00_dot_00_bar__plus_01_dot_25_bar__plus_01_dot_37 - object
        Window_bar__plus_00_dot_00_bar__plus_01_dot_25_bar__plus_02_dot_47 - object
        Window_bar__plus_00_dot_00_bar__plus_01_dot_25_bar__plus_03_dot_56 - object
        ArmChair_bar__minus_01_dot_01_bar__plus_00_dot_02_bar__plus_02_dot_08 - receptacle
        ArmChair_bar__minus_01_dot_52_bar__plus_00_dot_02_bar__plus_03_dot_31 - receptacle
        Cabinet_bar__minus_01_dot_53_bar__plus_01_dot_87_bar__plus_00_dot_33 - receptacle
        Cabinet_bar__minus_01_dot_98_bar__plus_01_dot_17_bar__plus_05_dot_03 - receptacle
        Cabinet_bar__minus_02_dot_02_bar__plus_01_dot_71_bar__plus_00_dot_32 - receptacle
        Cabinet_bar__minus_02_dot_47_bar__plus_01_dot_33_bar__plus_05_dot_03 - receptacle
        Cabinet_bar__minus_03_dot_70_bar__plus_01_dot_71_bar__plus_05_dot_03 - receptacle
        Cabinet_bar__minus_04_dot_18_bar__plus_01_dot_87_bar__plus_05_dot_03 - receptacle
        Cabinet_bar__minus_05_dot_37_bar__plus_01_dot_17_bar__plus_05_dot_03 - receptacle
        Cabinet_bar__minus_05_dot_85_bar__plus_01_dot_33_bar__plus_05_dot_03 - receptacle
        CoffeeTable_bar__minus_02_dot_67_bar__plus_00_dot_02_bar__plus_02_dot_00 - receptacle
        CoffeeTable_bar__minus_03_dot_00_bar__plus_00_dot_02_bar__plus_01_dot_73 - receptacle
        DiningTable_bar__minus_05_dot_82_bar__plus_00_dot_00_bar__plus_02_dot_56 - receptacle
        Drawer_bar__minus_01_dot_09_bar__plus_00_dot_23_bar__plus_00_dot_46 - receptacle
        Drawer_bar__minus_02_dot_68_bar__plus_00_dot_23_bar__plus_00_dot_46 - receptacle
        Drawer_bar__minus_02_dot_94_bar__plus_00_dot_51_bar__plus_05_dot_00 - receptacle
        Drawer_bar__minus_02_dot_94_bar__plus_00_dot_82_bar__plus_05_dot_00 - receptacle
        Drawer_bar__minus_04_dot_26_bar__plus_00_dot_23_bar__plus_00_dot_46 - receptacle
        Drawer_bar__minus_06_dot_33_bar__plus_00_dot_51_bar__plus_05_dot_00 - receptacle
        Drawer_bar__minus_06_dot_33_bar__plus_00_dot_82_bar__plus_05_dot_00 - receptacle
        GarbageCan_bar__minus_06_dot_86_bar__plus_00_dot_03_bar__plus_00_dot_22 - receptacle
        Shelf_bar__minus_00_dot_81_bar__plus_01_dot_69_bar__plus_00_dot_19 - receptacle
        Shelf_bar__minus_01_dot_04_bar__plus_01_dot_00_bar__plus_00_dot_18 - receptacle
        Shelf_bar__minus_01_dot_04_bar__plus_01_dot_32_bar__plus_00_dot_18 - receptacle
        Shelf_bar__minus_01_dot_78_bar__plus_00_dot_99_bar__plus_00_dot_18 - receptacle
        Shelf_bar__minus_02_dot_22_bar__plus_00_dot_45_bar__plus_05_dot_17 - receptacle
        Shelf_bar__minus_02_dot_67_bar__plus_00_dot_61_bar__plus_00_dot_27 - receptacle
        Shelf_bar__minus_03_dot_19_bar__plus_01_dot_15_bar__plus_05_dot_17 - receptacle
        Shelf_bar__minus_03_dot_94_bar__plus_00_dot_99_bar__plus_05_dot_17 - receptacle
        Shelf_bar__minus_04_dot_67_bar__plus_01_dot_00_bar__plus_05_dot_17 - receptacle
        Shelf_bar__minus_04_dot_67_bar__plus_01_dot_32_bar__plus_05_dot_17 - receptacle
        Shelf_bar__minus_04_dot_90_bar__plus_01_dot_69_bar__plus_05_dot_17 - receptacle
        Shelf_bar__minus_05_dot_61_bar__plus_00_dot_45_bar__plus_05_dot_17 - receptacle
        Shelf_bar__minus_06_dot_57_bar__plus_01_dot_15_bar__plus_05_dot_17 - receptacle
        Sofa_bar__minus_03_dot_33_bar__plus_00_dot_02_bar__plus_03_dot_63 - receptacle
        loc_bar__minus_25_bar_17_bar_2_bar_60 - location
        loc_bar__minus_9_bar_3_bar_2_bar_45 - location
        loc_bar__minus_19_bar_13_bar_3_bar_60 - location
        loc_bar__minus_16_bar_18_bar_0_bar_0 - location
        loc_bar__minus_12_bar_3_bar_0_bar_60 - location
        loc_bar__minus_5_bar_5_bar_3_bar_45 - location
        loc_bar__minus_16_bar_18_bar_0_bar_45 - location
        loc_bar__minus_7_bar_17_bar_0_bar_60 - location
        loc_bar__minus_19_bar_18_bar_0_bar_30 - location
        loc_bar__minus_2_bar_3_bar_2_bar_45 - location
        loc_bar__minus_8_bar_9_bar_1_bar_60 - location
        loc_bar__minus_23_bar_18_bar_0_bar_45 - location
        loc_bar__minus_26_bar_18_bar_0_bar_45 - location
        loc_bar__minus_24_bar_3_bar_3_bar_60 - location
        loc_bar__minus_3_bar_3_bar_2_bar_30 - location
        loc_bar__minus_19_bar_18_bar_0_bar_15 - location
        loc_bar__minus_12_bar_17_bar_0_bar_60 - location
        loc_bar__minus_23_bar_2_bar_2_bar_30 - location
        loc_bar__minus_19_bar_11_bar_3_bar_60 - location
        loc_bar__minus_2_bar_11_bar_1_bar_45 - location
        loc_bar__minus_26_bar_17_bar_0_bar_60 - location
        loc_bar__minus_13_bar_18_bar_0_bar_45 - location
        loc_bar__minus_7_bar_4_bar_2_bar_0 - location
        loc_bar__minus_11_bar_18_bar_0_bar_30 - location
        loc_bar__minus_25_bar_18_bar_0_bar_30 - location
        loc_bar__minus_11_bar_5_bar_3_bar_45 - location
        loc_bar__minus_14_bar_3_bar_2_bar_15 - location
        loc_bar__minus_19_bar_7_bar_3_bar_60 - location
        loc_bar__minus_26_bar_4_bar_0_bar_60 - location
        loc_bar__minus_24_bar_17_bar_0_bar_60 - location
        loc_bar__minus_2_bar_5_bar_1_bar_45 - location
        loc_bar__minus_20_bar_18_bar_0_bar_0 - location
        loc_bar__minus_4_bar_19_bar_0_bar_30 - location
        loc_bar__minus_2_bar_14_bar_1_bar_45 - location
        loc_bar__minus_2_bar_11_bar_3_bar_60 - location
        loc_bar__minus_3_bar_19_bar_1_bar_60 - location
        loc_bar__minus_13_bar_10_bar_0_bar_60 - location
        loc_bar__minus_25_bar_17_bar_0_bar_45 - location
        loc_bar__minus_5_bar_5_bar_3_bar_30 - location
        loc_bar__minus_19_bar_18_bar_0_bar_45 - location
        loc_bar__minus_5_bar_4_bar_2_bar__minus_15 - location
        loc_bar__minus_2_bar_17_bar_1_bar_45 - location
        loc_bar__minus_7_bar_9_bar_0_bar_60 - location
        loc_bar__minus_12_bar_17_bar_0_bar_45 - location
        loc_bar__minus_19_bar_10_bar_3_bar_45 - location
        loc_bar__minus_18_bar_18_bar_0_bar__minus_15 - location
        loc_bar__minus_7_bar_5_bar_2_bar_60 - location
        loc_bar__minus_3_bar_3_bar_2_bar_0 - location
        loc_bar__minus_9_bar_18_bar_0_bar_45 - location
        loc_bar__minus_17_bar_4_bar_1_bar_60 - location
        loc_bar__minus_6_bar_19_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType CoffeeTable_bar__minus_03_dot_00_bar__plus_00_dot_02_bar__plus_01_dot_73 CoffeeTableType)
        (receptacleType Shelf_bar__minus_02_dot_22_bar__plus_00_dot_45_bar__plus_05_dot_17 ShelfType)
        (receptacleType ArmChair_bar__minus_01_dot_01_bar__plus_00_dot_02_bar__plus_02_dot_08 ArmChairType)
        (receptacleType Shelf_bar__minus_01_dot_78_bar__plus_00_dot_99_bar__plus_00_dot_18 ShelfType)
        (receptacleType Cabinet_bar__minus_03_dot_70_bar__plus_01_dot_71_bar__plus_05_dot_03 CabinetType)
        (receptacleType Shelf_bar__minus_04_dot_67_bar__plus_01_dot_32_bar__plus_05_dot_17 ShelfType)
        (receptacleType Shelf_bar__minus_04_dot_67_bar__plus_01_dot_00_bar__plus_05_dot_17 ShelfType)
        (receptacleType Shelf_bar__minus_02_dot_67_bar__plus_00_dot_61_bar__plus_00_dot_27 ShelfType)
        (receptacleType DiningTable_bar__minus_05_dot_82_bar__plus_00_dot_00_bar__plus_02_dot_56 DiningTableType)
        (receptacleType Drawer_bar__minus_04_dot_26_bar__plus_00_dot_23_bar__plus_00_dot_46 DrawerType)
        (receptacleType Cabinet_bar__minus_05_dot_37_bar__plus_01_dot_17_bar__plus_05_dot_03 CabinetType)
        (receptacleType Shelf_bar__minus_01_dot_04_bar__plus_01_dot_00_bar__plus_00_dot_18 ShelfType)
        (receptacleType Drawer_bar__minus_06_dot_33_bar__plus_00_dot_51_bar__plus_05_dot_00 DrawerType)
        (receptacleType Shelf_bar__minus_06_dot_57_bar__plus_01_dot_15_bar__plus_05_dot_17 ShelfType)
        (receptacleType Shelf_bar__minus_05_dot_61_bar__plus_00_dot_45_bar__plus_05_dot_17 ShelfType)
        (receptacleType Drawer_bar__minus_02_dot_94_bar__plus_00_dot_51_bar__plus_05_dot_00 DrawerType)
        (receptacleType Cabinet_bar__minus_02_dot_02_bar__plus_01_dot_71_bar__plus_00_dot_32 CabinetType)
        (receptacleType Shelf_bar__minus_01_dot_04_bar__plus_01_dot_32_bar__plus_00_dot_18 ShelfType)
        (receptacleType Cabinet_bar__minus_01_dot_53_bar__plus_01_dot_87_bar__plus_00_dot_33 CabinetType)
        (receptacleType Drawer_bar__minus_06_dot_33_bar__plus_00_dot_82_bar__plus_05_dot_00 DrawerType)
        (receptacleType Shelf_bar__minus_00_dot_81_bar__plus_01_dot_69_bar__plus_00_dot_19 ShelfType)
        (receptacleType Shelf_bar__minus_03_dot_94_bar__plus_00_dot_99_bar__plus_05_dot_17 ShelfType)
        (receptacleType Cabinet_bar__minus_05_dot_85_bar__plus_01_dot_33_bar__plus_05_dot_03 CabinetType)
        (receptacleType Shelf_bar__minus_04_dot_90_bar__plus_01_dot_69_bar__plus_05_dot_17 ShelfType)
        (receptacleType Cabinet_bar__minus_04_dot_18_bar__plus_01_dot_87_bar__plus_05_dot_03 CabinetType)
        (receptacleType Cabinet_bar__minus_02_dot_47_bar__plus_01_dot_33_bar__plus_05_dot_03 CabinetType)
        (receptacleType GarbageCan_bar__minus_06_dot_86_bar__plus_00_dot_03_bar__plus_00_dot_22 GarbageCanType)
        (receptacleType ArmChair_bar__minus_01_dot_52_bar__plus_00_dot_02_bar__plus_03_dot_31 ArmChairType)
        (receptacleType Drawer_bar__minus_02_dot_68_bar__plus_00_dot_23_bar__plus_00_dot_46 DrawerType)
        (receptacleType Cabinet_bar__minus_01_dot_98_bar__plus_01_dot_17_bar__plus_05_dot_03 CabinetType)
        (receptacleType CoffeeTable_bar__minus_02_dot_67_bar__plus_00_dot_02_bar__plus_02_dot_00 CoffeeTableType)
        (receptacleType Shelf_bar__minus_03_dot_19_bar__plus_01_dot_15_bar__plus_05_dot_17 ShelfType)
        (receptacleType Drawer_bar__minus_02_dot_94_bar__plus_00_dot_82_bar__plus_05_dot_00 DrawerType)
        (receptacleType Sofa_bar__minus_03_dot_33_bar__plus_00_dot_02_bar__plus_03_dot_63 SofaType)
        (receptacleType Drawer_bar__minus_01_dot_09_bar__plus_00_dot_23_bar__plus_00_dot_46 DrawerType)
        (objectType Pillow_bar__minus_01_dot_52_bar__plus_00_dot_44_bar__plus_03_dot_32 PillowType)
        (objectType Chair_bar__minus_05_dot_41_bar__plus_00_dot_02_bar__plus_01_dot_78 ChairType)
        (objectType Vase_bar__minus_01_dot_84_bar__plus_01_dot_29_bar__plus_00_dot_19 VaseType)
        (objectType Vase_bar__minus_04_dot_96_bar__plus_00_dot_94_bar__plus_05_dot_20 VaseType)
        (objectType Statue_bar__minus_05_dot_03_bar__plus_01_dot_57_bar__plus_05_dot_21 StatueType)
        (objectType Statue_bar__minus_03_dot_85_bar__plus_00_dot_93_bar__plus_05_dot_17 StatueType)
        (objectType KeyChain_bar__minus_04_dot_78_bar__plus_01_dot_58_bar__plus_05_dot_21 KeyChainType)
        (objectType Newspaper_bar__minus_05_dot_94_bar__plus_00_dot_64_bar__plus_03_dot_27 NewspaperType)
        (objectType Chair_bar__minus_06_dot_25_bar__plus_00_dot_02_bar__plus_03_dot_43 ChairType)
        (objectType Window_bar__plus_00_dot_00_bar__plus_01_dot_25_bar__plus_01_dot_37 WindowType)
        (objectType Chair_bar__minus_06_dot_30_bar__plus_00_dot_02_bar__plus_01_dot_67 ChairType)
        (objectType Vase_bar__minus_01_dot_35_bar__plus_01_dot_26_bar__plus_00_dot_23 VaseType)
        (objectType Window_bar__plus_00_dot_00_bar__plus_01_dot_25_bar__plus_02_dot_47 WindowType)
        (objectType Box_bar__minus_05_dot_69_bar__plus_00_dot_82_bar__plus_03_dot_27 BoxType)
        (objectType Vase_bar__minus_03_dot_18_bar__plus_00_dot_18_bar__plus_01_dot_72 VaseType)
        (objectType Painting_bar__minus_00_dot_97_bar__plus_01_dot_40_bar__plus_05_dot_27 PaintingType)
        (objectType Plate_bar__minus_05_dot_69_bar__plus_00_dot_65_bar__plus_03_dot_62 PlateType)
        (objectType CreditCard_bar__minus_01_dot_00_bar__plus_00_dot_27_bar__plus_02_dot_32 CreditCardType)
        (objectType FloorLamp_bar__minus_00_dot_37_bar__plus_00_dot_03_bar__plus_04_dot_99 FloorLampType)
        (objectType RemoteControl_bar__minus_02_dot_72_bar__plus_00_dot_28_bar__plus_03_dot_38 RemoteControlType)
        (objectType HousePlant_bar__minus_01_dot_11_bar__plus_00_dot_31_bar__plus_02_dot_74 HousePlantType)
        (objectType Vase_bar__minus_01_dot_70_bar__plus_00_dot_93_bar__plus_00_dot_18 VaseType)
        (objectType Chair_bar__minus_05_dot_38_bar__plus_00_dot_02_bar__plus_02_dot_70 ChairType)
        (objectType Chair_bar__minus_06_dot_21_bar__plus_00_dot_02_bar__plus_02_dot_52 ChairType)
        (objectType Statue_bar__minus_00_dot_81_bar__plus_01_dot_57_bar__plus_00_dot_21 StatueType)
        (objectType LightSwitch_bar__plus_00_dot_00_bar__plus_01_dot_19_bar__plus_04_dot_41 LightSwitchType)
        (objectType Box_bar__minus_06_dot_06_bar__plus_00_dot_82_bar__plus_01_dot_51 BoxType)
        (objectType RemoteControl_bar__minus_05_dot_45_bar__plus_00_dot_64_bar__plus_03_dot_27 RemoteControlType)
        (objectType Chair_bar__minus_05_dot_39_bar__plus_00_dot_02_bar__plus_03_dot_37 ChairType)
        (objectType Painting_bar__minus_05_dot_77_bar__plus_01_dot_40_bar__minus_00_dot_03 PaintingType)
        (objectType Statue_bar__minus_05_dot_87_bar__plus_00_dot_63_bar__plus_02_dot_79 StatueType)
        (objectType Television_bar__minus_03_dot_50_bar__plus_01_dot_51_bar__plus_00_dot_00 TelevisionType)
        (objectType Statue_bar__minus_03_dot_17_bar__plus_01_dot_11_bar__plus_05_dot_19 StatueType)
        (objectType Laptop_bar__minus_05_dot_69_bar__plus_00_dot_64_bar__plus_01_dot_51 LaptopType)
        (objectType Statue_bar__minus_02_dot_44_bar__plus_00_dot_31_bar__plus_02_dot_00 StatueType)
        (objectType Plate_bar__minus_02_dot_67_bar__plus_00_dot_31_bar__plus_02_dot_00 PlateType)
        (objectType Newspaper_bar__minus_06_dot_06_bar__plus_00_dot_64_bar__plus_02_dot_21 NewspaperType)
        (objectType CreditCard_bar__minus_03_dot_13_bar__plus_00_dot_28_bar__plus_03_dot_38 CreditCardType)
        (objectType Window_bar__plus_00_dot_00_bar__plus_01_dot_25_bar__plus_03_dot_56 WindowType)
        (objectType Statue_bar__minus_06_dot_59_bar__plus_01_dot_02_bar__plus_05_dot_17 StatueType)
        (objectType Vase_bar__minus_00_dot_76_bar__plus_00_dot_94_bar__plus_00_dot_20 VaseType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType VaseType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType PlateType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain CabinetType NewspaperType)
        (canContain CabinetType VaseType)
        (canContain CabinetType BoxType)
        (canContain CabinetType PlateType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain DiningTableType NewspaperType)
        (canContain DiningTableType VaseType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType StatueType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain CabinetType NewspaperType)
        (canContain CabinetType VaseType)
        (canContain CabinetType BoxType)
        (canContain CabinetType PlateType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain CabinetType NewspaperType)
        (canContain CabinetType VaseType)
        (canContain CabinetType BoxType)
        (canContain CabinetType PlateType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain CabinetType NewspaperType)
        (canContain CabinetType VaseType)
        (canContain CabinetType BoxType)
        (canContain CabinetType PlateType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain CabinetType NewspaperType)
        (canContain CabinetType VaseType)
        (canContain CabinetType BoxType)
        (canContain CabinetType PlateType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain CabinetType NewspaperType)
        (canContain CabinetType VaseType)
        (canContain CabinetType BoxType)
        (canContain CabinetType PlateType)
        (canContain CabinetType NewspaperType)
        (canContain CabinetType VaseType)
        (canContain CabinetType BoxType)
        (canContain CabinetType PlateType)
        (canContain GarbageCanType NewspaperType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain CabinetType NewspaperType)
        (canContain CabinetType VaseType)
        (canContain CabinetType BoxType)
        (canContain CabinetType PlateType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType VaseType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType PlateType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (pickupable Pillow_bar__minus_01_dot_52_bar__plus_00_dot_44_bar__plus_03_dot_32)
        (pickupable Vase_bar__minus_01_dot_84_bar__plus_01_dot_29_bar__plus_00_dot_19)
        (pickupable Vase_bar__minus_04_dot_96_bar__plus_00_dot_94_bar__plus_05_dot_20)
        (pickupable Statue_bar__minus_05_dot_03_bar__plus_01_dot_57_bar__plus_05_dot_21)
        (pickupable Statue_bar__minus_03_dot_85_bar__plus_00_dot_93_bar__plus_05_dot_17)
        (pickupable KeyChain_bar__minus_04_dot_78_bar__plus_01_dot_58_bar__plus_05_dot_21)
        (pickupable Newspaper_bar__minus_05_dot_94_bar__plus_00_dot_64_bar__plus_03_dot_27)
        (pickupable Vase_bar__minus_01_dot_35_bar__plus_01_dot_26_bar__plus_00_dot_23)
        (pickupable Box_bar__minus_05_dot_69_bar__plus_00_dot_82_bar__plus_03_dot_27)
        (pickupable Vase_bar__minus_03_dot_18_bar__plus_00_dot_18_bar__plus_01_dot_72)
        (pickupable Plate_bar__minus_05_dot_69_bar__plus_00_dot_65_bar__plus_03_dot_62)
        (pickupable CreditCard_bar__minus_01_dot_00_bar__plus_00_dot_27_bar__plus_02_dot_32)
        (pickupable RemoteControl_bar__minus_02_dot_72_bar__plus_00_dot_28_bar__plus_03_dot_38)
        (pickupable Vase_bar__minus_01_dot_70_bar__plus_00_dot_93_bar__plus_00_dot_18)
        (pickupable Statue_bar__minus_00_dot_81_bar__plus_01_dot_57_bar__plus_00_dot_21)
        (pickupable Box_bar__minus_06_dot_06_bar__plus_00_dot_82_bar__plus_01_dot_51)
        (pickupable RemoteControl_bar__minus_05_dot_45_bar__plus_00_dot_64_bar__plus_03_dot_27)
        (pickupable Statue_bar__minus_05_dot_87_bar__plus_00_dot_63_bar__plus_02_dot_79)
        (pickupable Statue_bar__minus_03_dot_17_bar__plus_01_dot_11_bar__plus_05_dot_19)
        (pickupable Laptop_bar__minus_05_dot_69_bar__plus_00_dot_64_bar__plus_01_dot_51)
        (pickupable Statue_bar__minus_02_dot_44_bar__plus_00_dot_31_bar__plus_02_dot_00)
        (pickupable Plate_bar__minus_02_dot_67_bar__plus_00_dot_31_bar__plus_02_dot_00)
        (pickupable Newspaper_bar__minus_06_dot_06_bar__plus_00_dot_64_bar__plus_02_dot_21)
        (pickupable CreditCard_bar__minus_03_dot_13_bar__plus_00_dot_28_bar__plus_03_dot_38)
        (pickupable Statue_bar__minus_06_dot_59_bar__plus_01_dot_02_bar__plus_05_dot_17)
        (pickupable Vase_bar__minus_00_dot_76_bar__plus_00_dot_94_bar__plus_00_dot_20)
        (isReceptacleObject Box_bar__minus_05_dot_69_bar__plus_00_dot_82_bar__plus_03_dot_27)
        (isReceptacleObject Plate_bar__minus_05_dot_69_bar__plus_00_dot_65_bar__plus_03_dot_62)
        (isReceptacleObject Box_bar__minus_06_dot_06_bar__plus_00_dot_82_bar__plus_01_dot_51)
        (isReceptacleObject Plate_bar__minus_02_dot_67_bar__plus_00_dot_31_bar__plus_02_dot_00)
        (openable Cabinet_bar__minus_03_dot_70_bar__plus_01_dot_71_bar__plus_05_dot_03)
        (openable Drawer_bar__minus_04_dot_26_bar__plus_00_dot_23_bar__plus_00_dot_46)
        (openable Cabinet_bar__minus_05_dot_37_bar__plus_01_dot_17_bar__plus_05_dot_03)
        (openable Drawer_bar__minus_06_dot_33_bar__plus_00_dot_51_bar__plus_05_dot_00)
        (openable Drawer_bar__minus_02_dot_94_bar__plus_00_dot_51_bar__plus_05_dot_00)
        (openable Cabinet_bar__minus_02_dot_02_bar__plus_01_dot_71_bar__plus_00_dot_32)
        (openable Cabinet_bar__minus_01_dot_53_bar__plus_01_dot_87_bar__plus_00_dot_33)
        (openable Drawer_bar__minus_06_dot_33_bar__plus_00_dot_82_bar__plus_05_dot_00)
        (openable Cabinet_bar__minus_05_dot_85_bar__plus_01_dot_33_bar__plus_05_dot_03)
        (openable Cabinet_bar__minus_04_dot_18_bar__plus_01_dot_87_bar__plus_05_dot_03)
        (openable Cabinet_bar__minus_02_dot_47_bar__plus_01_dot_33_bar__plus_05_dot_03)
        (openable Drawer_bar__minus_02_dot_68_bar__plus_00_dot_23_bar__plus_00_dot_46)
        (openable Cabinet_bar__minus_01_dot_98_bar__plus_01_dot_17_bar__plus_05_dot_03)
        (openable Drawer_bar__minus_02_dot_94_bar__plus_00_dot_82_bar__plus_05_dot_00)
        (openable Drawer_bar__minus_01_dot_09_bar__plus_00_dot_23_bar__plus_00_dot_46)
        
        (atLocation agent1 loc_bar__minus_6_bar_19_bar_3_bar_30)
        
        (cleanable Plate_bar__minus_05_dot_69_bar__plus_00_dot_65_bar__plus_03_dot_62)
        (cleanable Plate_bar__minus_02_dot_67_bar__plus_00_dot_31_bar__plus_02_dot_00)
        
        (heatable Plate_bar__minus_05_dot_69_bar__plus_00_dot_65_bar__plus_03_dot_62)
        (heatable Plate_bar__minus_02_dot_67_bar__plus_00_dot_31_bar__plus_02_dot_00)
        (coolable Plate_bar__minus_05_dot_69_bar__plus_00_dot_65_bar__plus_03_dot_62)
        (coolable Plate_bar__minus_02_dot_67_bar__plus_00_dot_31_bar__plus_02_dot_00)
        
        
        (toggleable FloorLamp_bar__minus_00_dot_37_bar__plus_00_dot_03_bar__plus_04_dot_99)
        
        
        
        
        (inReceptacleObject Newspaper_bar__minus_05_dot_94_bar__plus_00_dot_64_bar__plus_03_dot_27 Box_bar__minus_05_dot_69_bar__plus_00_dot_82_bar__plus_03_dot_27)
        (inReceptacle Vase_bar__minus_01_dot_70_bar__plus_00_dot_93_bar__plus_00_dot_18 Shelf_bar__minus_01_dot_78_bar__plus_00_dot_99_bar__plus_00_dot_18)
        (inReceptacle Statue_bar__minus_03_dot_85_bar__plus_00_dot_93_bar__plus_05_dot_17 Shelf_bar__minus_03_dot_94_bar__plus_00_dot_99_bar__plus_05_dot_17)
        (inReceptacle Box_bar__minus_06_dot_06_bar__plus_00_dot_82_bar__plus_01_dot_51 DiningTable_bar__minus_05_dot_82_bar__plus_00_dot_00_bar__plus_02_dot_56)
        (inReceptacle RemoteControl_bar__minus_05_dot_45_bar__plus_00_dot_64_bar__plus_03_dot_27 DiningTable_bar__minus_05_dot_82_bar__plus_00_dot_00_bar__plus_02_dot_56)
        (inReceptacle Laptop_bar__minus_05_dot_69_bar__plus_00_dot_64_bar__plus_01_dot_51 DiningTable_bar__minus_05_dot_82_bar__plus_00_dot_00_bar__plus_02_dot_56)
        (inReceptacle Box_bar__minus_05_dot_69_bar__plus_00_dot_82_bar__plus_03_dot_27 DiningTable_bar__minus_05_dot_82_bar__plus_00_dot_00_bar__plus_02_dot_56)
        (inReceptacle Plate_bar__minus_05_dot_69_bar__plus_00_dot_65_bar__plus_03_dot_62 DiningTable_bar__minus_05_dot_82_bar__plus_00_dot_00_bar__plus_02_dot_56)
        (inReceptacle Newspaper_bar__minus_06_dot_06_bar__plus_00_dot_64_bar__plus_02_dot_21 DiningTable_bar__minus_05_dot_82_bar__plus_00_dot_00_bar__plus_02_dot_56)
        (inReceptacle Statue_bar__minus_05_dot_87_bar__plus_00_dot_63_bar__plus_02_dot_79 DiningTable_bar__minus_05_dot_82_bar__plus_00_dot_00_bar__plus_02_dot_56)
        (inReceptacle Newspaper_bar__minus_05_dot_94_bar__plus_00_dot_64_bar__plus_03_dot_27 DiningTable_bar__minus_05_dot_82_bar__plus_00_dot_00_bar__plus_02_dot_56)
        (inReceptacle Statue_bar__minus_06_dot_59_bar__plus_01_dot_02_bar__plus_05_dot_17 Shelf_bar__minus_06_dot_57_bar__plus_01_dot_15_bar__plus_05_dot_17)
        (inReceptacle RemoteControl_bar__minus_02_dot_72_bar__plus_00_dot_28_bar__plus_03_dot_38 Sofa_bar__minus_03_dot_33_bar__plus_00_dot_02_bar__plus_03_dot_63)
        (inReceptacle CreditCard_bar__minus_03_dot_13_bar__plus_00_dot_28_bar__plus_03_dot_38 Sofa_bar__minus_03_dot_33_bar__plus_00_dot_02_bar__plus_03_dot_63)
        (inReceptacle Vase_bar__minus_01_dot_84_bar__plus_01_dot_29_bar__plus_00_dot_19 Cabinet_bar__minus_02_dot_02_bar__plus_01_dot_71_bar__plus_00_dot_32)
        (inReceptacle Vase_bar__minus_04_dot_96_bar__plus_00_dot_94_bar__plus_05_dot_20 Shelf_bar__minus_04_dot_67_bar__plus_01_dot_00_bar__plus_05_dot_17)
        (inReceptacle Vase_bar__minus_00_dot_76_bar__plus_00_dot_94_bar__plus_00_dot_20 Shelf_bar__minus_01_dot_04_bar__plus_01_dot_00_bar__plus_00_dot_18)
        (inReceptacle Statue_bar__minus_00_dot_81_bar__plus_01_dot_57_bar__plus_00_dot_21 Shelf_bar__minus_00_dot_81_bar__plus_01_dot_69_bar__plus_00_dot_19)
        (inReceptacle Pillow_bar__minus_01_dot_52_bar__plus_00_dot_44_bar__plus_03_dot_32 ArmChair_bar__minus_01_dot_52_bar__plus_00_dot_02_bar__plus_03_dot_31)
        (inReceptacle CreditCard_bar__minus_01_dot_00_bar__plus_00_dot_27_bar__plus_02_dot_32 ArmChair_bar__minus_01_dot_01_bar__plus_00_dot_02_bar__plus_02_dot_08)
        (inReceptacle Statue_bar__minus_02_dot_44_bar__plus_00_dot_31_bar__plus_02_dot_00 CoffeeTable_bar__minus_02_dot_67_bar__plus_00_dot_02_bar__plus_02_dot_00)
        (inReceptacle Plate_bar__minus_02_dot_67_bar__plus_00_dot_31_bar__plus_02_dot_00 CoffeeTable_bar__minus_02_dot_67_bar__plus_00_dot_02_bar__plus_02_dot_00)
        (inReceptacle Vase_bar__minus_01_dot_35_bar__plus_01_dot_26_bar__plus_00_dot_23 Shelf_bar__minus_01_dot_04_bar__plus_01_dot_32_bar__plus_00_dot_18)
        (inReceptacle Statue_bar__minus_05_dot_03_bar__plus_01_dot_57_bar__plus_05_dot_21 Shelf_bar__minus_04_dot_90_bar__plus_01_dot_69_bar__plus_05_dot_17)
        (inReceptacle Statue_bar__minus_03_dot_17_bar__plus_01_dot_11_bar__plus_05_dot_19 Shelf_bar__minus_03_dot_19_bar__plus_01_dot_15_bar__plus_05_dot_17)
        (inReceptacle Vase_bar__minus_03_dot_18_bar__plus_00_dot_18_bar__plus_01_dot_72 CoffeeTable_bar__minus_03_dot_00_bar__plus_00_dot_02_bar__plus_01_dot_73)
        
        
        (receptacleAtLocation ArmChair_bar__minus_01_dot_01_bar__plus_00_dot_02_bar__plus_02_dot_08 loc_bar__minus_8_bar_9_bar_1_bar_60)
        (receptacleAtLocation ArmChair_bar__minus_01_dot_52_bar__plus_00_dot_02_bar__plus_03_dot_31 loc_bar__minus_7_bar_9_bar_0_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_53_bar__plus_01_dot_87_bar__plus_00_dot_33 loc_bar__minus_5_bar_4_bar_2_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_98_bar__plus_01_dot_17_bar__plus_05_dot_03 loc_bar__minus_9_bar_18_bar_0_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_02_bar__plus_01_dot_71_bar__plus_00_dot_32 loc_bar__minus_7_bar_4_bar_2_bar_0)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_47_bar__plus_01_dot_33_bar__plus_05_dot_03 loc_bar__minus_11_bar_18_bar_0_bar_30)
        (receptacleAtLocation Cabinet_bar__minus_03_dot_70_bar__plus_01_dot_71_bar__plus_05_dot_03 loc_bar__minus_16_bar_18_bar_0_bar_0)
        (receptacleAtLocation Cabinet_bar__minus_04_dot_18_bar__plus_01_dot_87_bar__plus_05_dot_03 loc_bar__minus_18_bar_18_bar_0_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_05_dot_37_bar__plus_01_dot_17_bar__plus_05_dot_03 loc_bar__minus_23_bar_18_bar_0_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_05_dot_85_bar__plus_01_dot_33_bar__plus_05_dot_03 loc_bar__minus_25_bar_18_bar_0_bar_30)
        (receptacleAtLocation CoffeeTable_bar__minus_02_dot_67_bar__plus_00_dot_02_bar__plus_02_dot_00 loc_bar__minus_12_bar_3_bar_0_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_03_dot_00_bar__plus_00_dot_02_bar__plus_01_dot_73 loc_bar__minus_17_bar_4_bar_1_bar_60)
        (receptacleAtLocation DiningTable_bar__minus_05_dot_82_bar__plus_00_dot_00_bar__plus_02_dot_56 loc_bar__minus_19_bar_13_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_09_bar__plus_00_dot_23_bar__plus_00_dot_46 loc_bar__minus_7_bar_5_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_02_dot_68_bar__plus_00_dot_23_bar__plus_00_dot_46 loc_bar__minus_5_bar_5_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_94_bar__plus_00_dot_51_bar__plus_05_dot_00 loc_bar__minus_12_bar_17_bar_0_bar_60)
        (receptacleAtLocation Drawer_bar__minus_02_dot_94_bar__plus_00_dot_82_bar__plus_05_dot_00 loc_bar__minus_12_bar_17_bar_0_bar_45)
        (receptacleAtLocation Drawer_bar__minus_04_dot_26_bar__plus_00_dot_23_bar__plus_00_dot_46 loc_bar__minus_11_bar_5_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_06_dot_33_bar__plus_00_dot_51_bar__plus_05_dot_00 loc_bar__minus_26_bar_17_bar_0_bar_60)
        (receptacleAtLocation Drawer_bar__minus_06_dot_33_bar__plus_00_dot_82_bar__plus_05_dot_00 loc_bar__minus_25_bar_17_bar_0_bar_45)
        (receptacleAtLocation GarbageCan_bar__minus_06_dot_86_bar__plus_00_dot_03_bar__plus_00_dot_22 loc_bar__minus_24_bar_3_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__minus_00_dot_81_bar__plus_01_dot_69_bar__plus_00_dot_19 loc_bar__minus_3_bar_3_bar_2_bar_0)
        (receptacleAtLocation Shelf_bar__minus_01_dot_04_bar__plus_01_dot_00_bar__plus_00_dot_18 loc_bar__minus_2_bar_3_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__minus_01_dot_04_bar__plus_01_dot_32_bar__plus_00_dot_18 loc_bar__minus_3_bar_3_bar_2_bar_30)
        (receptacleAtLocation Shelf_bar__minus_01_dot_78_bar__plus_00_dot_99_bar__plus_00_dot_18 loc_bar__minus_9_bar_3_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__minus_02_dot_22_bar__plus_00_dot_45_bar__plus_05_dot_17 loc_bar__minus_7_bar_17_bar_0_bar_60)
        (receptacleAtLocation Shelf_bar__minus_02_dot_67_bar__plus_00_dot_61_bar__plus_00_dot_27 loc_bar__minus_5_bar_5_bar_3_bar_30)
        (receptacleAtLocation Shelf_bar__minus_03_dot_19_bar__plus_01_dot_15_bar__plus_05_dot_17 loc_bar__minus_13_bar_18_bar_0_bar_45)
        (receptacleAtLocation Shelf_bar__minus_03_dot_94_bar__plus_00_dot_99_bar__plus_05_dot_17 loc_bar__minus_16_bar_18_bar_0_bar_45)
        (receptacleAtLocation Shelf_bar__minus_04_dot_67_bar__plus_01_dot_00_bar__plus_05_dot_17 loc_bar__minus_19_bar_18_bar_0_bar_45)
        (receptacleAtLocation Shelf_bar__minus_04_dot_67_bar__plus_01_dot_32_bar__plus_05_dot_17 loc_bar__minus_19_bar_18_bar_0_bar_30)
        (receptacleAtLocation Shelf_bar__minus_04_dot_90_bar__plus_01_dot_69_bar__plus_05_dot_17 loc_bar__minus_20_bar_18_bar_0_bar_0)
        (receptacleAtLocation Shelf_bar__minus_05_dot_61_bar__plus_00_dot_45_bar__plus_05_dot_17 loc_bar__minus_24_bar_17_bar_0_bar_60)
        (receptacleAtLocation Shelf_bar__minus_06_dot_57_bar__plus_01_dot_15_bar__plus_05_dot_17 loc_bar__minus_26_bar_18_bar_0_bar_45)
        (receptacleAtLocation Sofa_bar__minus_03_dot_33_bar__plus_00_dot_02_bar__plus_03_dot_63 loc_bar__minus_13_bar_10_bar_0_bar_60)
        (objectAtLocation Plate_bar__minus_05_dot_69_bar__plus_00_dot_65_bar__plus_03_dot_62 loc_bar__minus_19_bar_13_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__minus_05_dot_45_bar__plus_00_dot_64_bar__plus_03_dot_27 loc_bar__minus_19_bar_13_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_00_bar__plus_00_dot_27_bar__plus_02_dot_32 loc_bar__minus_8_bar_9_bar_1_bar_60)
        (objectAtLocation Newspaper_bar__minus_05_dot_94_bar__plus_00_dot_64_bar__plus_03_dot_27 loc_bar__minus_19_bar_13_bar_3_bar_60)
        (objectAtLocation Box_bar__minus_05_dot_69_bar__plus_00_dot_82_bar__plus_03_dot_27 loc_bar__minus_19_bar_13_bar_3_bar_60)
        (objectAtLocation Chair_bar__minus_05_dot_41_bar__plus_00_dot_02_bar__plus_01_dot_78 loc_bar__minus_19_bar_7_bar_3_bar_60)
        (objectAtLocation Chair_bar__minus_05_dot_39_bar__plus_00_dot_02_bar__plus_03_dot_37 loc_bar__minus_19_bar_13_bar_3_bar_60)
        (objectAtLocation Box_bar__minus_06_dot_06_bar__plus_00_dot_82_bar__plus_01_dot_51 loc_bar__minus_19_bar_13_bar_3_bar_60)
        (objectAtLocation Chair_bar__minus_06_dot_21_bar__plus_00_dot_02_bar__plus_02_dot_52 loc_bar__minus_19_bar_10_bar_3_bar_45)
        (objectAtLocation Chair_bar__minus_05_dot_38_bar__plus_00_dot_02_bar__plus_02_dot_70 loc_bar__minus_19_bar_11_bar_3_bar_60)
        (objectAtLocation Chair_bar__minus_06_dot_25_bar__plus_00_dot_02_bar__plus_03_dot_43 loc_bar__minus_25_bar_17_bar_2_bar_60)
        (objectAtLocation Chair_bar__minus_06_dot_30_bar__plus_00_dot_02_bar__plus_01_dot_67 loc_bar__minus_26_bar_4_bar_0_bar_60)
        (objectAtLocation Statue_bar__minus_06_dot_59_bar__plus_01_dot_02_bar__plus_05_dot_17 loc_bar__minus_26_bar_18_bar_0_bar_45)
        (objectAtLocation KeyChain_bar__minus_04_dot_78_bar__plus_01_dot_58_bar__plus_05_dot_21 loc_bar__minus_19_bar_18_bar_0_bar_15)
        (objectAtLocation Vase_bar__minus_01_dot_70_bar__plus_00_dot_93_bar__plus_00_dot_18 loc_bar__minus_9_bar_3_bar_2_bar_45)
        (objectAtLocation FloorLamp_bar__minus_00_dot_37_bar__plus_00_dot_03_bar__plus_04_dot_99 loc_bar__minus_3_bar_19_bar_1_bar_60)
        (objectAtLocation Television_bar__minus_03_dot_50_bar__plus_01_dot_51_bar__plus_00_dot_00 loc_bar__minus_14_bar_3_bar_2_bar_15)
        (objectAtLocation Vase_bar__minus_00_dot_76_bar__plus_00_dot_94_bar__plus_00_dot_20 loc_bar__minus_2_bar_3_bar_2_bar_45)
        (objectAtLocation Vase_bar__minus_01_dot_84_bar__plus_01_dot_29_bar__plus_00_dot_19 loc_bar__minus_7_bar_4_bar_2_bar_0)
        (objectAtLocation Newspaper_bar__minus_06_dot_06_bar__plus_00_dot_64_bar__plus_02_dot_21 loc_bar__minus_19_bar_13_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__minus_03_dot_13_bar__plus_00_dot_28_bar__plus_03_dot_38 loc_bar__minus_13_bar_10_bar_0_bar_60)
        (objectAtLocation RemoteControl_bar__minus_02_dot_72_bar__plus_00_dot_28_bar__plus_03_dot_38 loc_bar__minus_13_bar_10_bar_0_bar_60)
        (objectAtLocation Laptop_bar__minus_05_dot_69_bar__plus_00_dot_64_bar__plus_01_dot_51 loc_bar__minus_19_bar_13_bar_3_bar_60)
        (objectAtLocation Pillow_bar__minus_01_dot_52_bar__plus_00_dot_44_bar__plus_03_dot_32 loc_bar__minus_7_bar_9_bar_0_bar_60)
        (objectAtLocation LightSwitch_bar__plus_00_dot_00_bar__plus_01_dot_19_bar__plus_04_dot_41 loc_bar__minus_2_bar_17_bar_1_bar_45)
        (objectAtLocation Vase_bar__minus_03_dot_18_bar__plus_00_dot_18_bar__plus_01_dot_72 loc_bar__minus_17_bar_4_bar_1_bar_60)
        (objectAtLocation Statue_bar__minus_02_dot_44_bar__plus_00_dot_31_bar__plus_02_dot_00 loc_bar__minus_12_bar_3_bar_0_bar_60)
        (objectAtLocation HousePlant_bar__minus_01_dot_11_bar__plus_00_dot_31_bar__plus_02_dot_74 loc_bar__minus_2_bar_11_bar_3_bar_60)
        (objectAtLocation Vase_bar__minus_01_dot_35_bar__plus_01_dot_26_bar__plus_00_dot_23 loc_bar__minus_3_bar_3_bar_2_bar_30)
        (objectAtLocation Plate_bar__minus_02_dot_67_bar__plus_00_dot_31_bar__plus_02_dot_00 loc_bar__minus_12_bar_3_bar_0_bar_60)
        (objectAtLocation Vase_bar__minus_04_dot_96_bar__plus_00_dot_94_bar__plus_05_dot_20 loc_bar__minus_19_bar_18_bar_0_bar_45)
        (objectAtLocation Painting_bar__minus_05_dot_77_bar__plus_01_dot_40_bar__minus_00_dot_03 loc_bar__minus_23_bar_2_bar_2_bar_30)
        (objectAtLocation Window_bar__plus_00_dot_00_bar__plus_01_dot_25_bar__plus_02_dot_47 loc_bar__minus_2_bar_11_bar_1_bar_45)
        (objectAtLocation Window_bar__plus_00_dot_00_bar__plus_01_dot_25_bar__plus_03_dot_56 loc_bar__minus_2_bar_14_bar_1_bar_45)
        (objectAtLocation Window_bar__plus_00_dot_00_bar__plus_01_dot_25_bar__plus_01_dot_37 loc_bar__minus_2_bar_5_bar_1_bar_45)
        (objectAtLocation Painting_bar__minus_00_dot_97_bar__plus_01_dot_40_bar__plus_05_dot_27 loc_bar__minus_4_bar_19_bar_0_bar_30)
        (objectAtLocation Statue_bar__minus_05_dot_03_bar__plus_01_dot_57_bar__plus_05_dot_21 loc_bar__minus_20_bar_18_bar_0_bar_0)
        (objectAtLocation Statue_bar__minus_00_dot_81_bar__plus_01_dot_57_bar__plus_00_dot_21 loc_bar__minus_3_bar_3_bar_2_bar_0)
        (objectAtLocation Statue_bar__minus_03_dot_85_bar__plus_00_dot_93_bar__plus_05_dot_17 loc_bar__minus_16_bar_18_bar_0_bar_45)
        (objectAtLocation Statue_bar__minus_03_dot_17_bar__plus_01_dot_11_bar__plus_05_dot_19 loc_bar__minus_13_bar_18_bar_0_bar_45)
        (objectAtLocation Statue_bar__minus_05_dot_87_bar__plus_00_dot_63_bar__plus_02_dot_79 loc_bar__minus_19_bar_13_bar_3_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 VaseType)
                                    (receptacleType ?r DiningTableType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 VaseType)
                                            (receptacleType ?r DiningTableType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            