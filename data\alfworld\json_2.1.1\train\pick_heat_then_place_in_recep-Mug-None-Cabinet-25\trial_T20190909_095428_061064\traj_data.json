{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 34}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|5|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [-2.58799982, -2.58799982, 1.4696, 1.4696, 3.5528428, 3.5528428]], "coordinateReceptacleObjectId": ["CounterTop", [-6.232, -6.232, 1.112, 1.112, 3.4364, 3.4364]], "forceVisible": true, "objectId": "Mug|-00.65|+00.89|+00.37"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-8|5|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-11.18, -11.18, 5.396, 5.396, 3.2488436, 3.2488436]], "forceVisible": true, "objectId": "Microwave|-02.80|+00.81|+01.35"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-8|5|3|-15"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [-2.58799982, -2.58799982, 1.4696, 1.4696, 3.5528428, 3.5528428]], "coordinateReceptacleObjectId": ["Cabinet", [-11.1271496, -11.1271496, 3.301936628, 3.301936628, 7.19391156, 7.19391156]], "forceVisible": true, "objectId": "Mug|-00.65|+00.89|+00.37", "receptacleObjectId": "Cabinet|-02.78|+01.80|+00.83"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-00.65|+00.89|+00.37"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [49, 91, 81, 125], "mask": [[27064, 1], [27358, 13], [27655, 17], [27953, 20], [28252, 22], [28550, 24], [28850, 25], [29149, 29], [29449, 31], [29749, 32], [30049, 32], [30349, 27], [30378, 4], [30650, 26], [30679, 3], [30950, 26], [30979, 3], [31250, 27], [31280, 2], [31551, 26], [31580, 2], [31851, 26], [31880, 2], [32151, 26], [32180, 2], [32452, 26], [32480, 2], [32752, 26], [32779, 3], [33052, 29], [33353, 28], [33653, 27], [33953, 26], [34253, 26], [34554, 25], [34854, 25], [35154, 25], [35455, 23], [35755, 23], [36055, 22], [36356, 20], [36657, 17], [36959, 13], [37261, 8]], "point": [65, 107]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-00.65|+00.89|+00.37", "placeStationary": true, "receptacleObjectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [2, 34, 295, 294], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25870, 224], [26170, 223], [26469, 224], [26768, 224], [27068, 224], [27367, 224], [27666, 225], [27965, 226], [28265, 225], [28564, 226], [28864, 225], [29163, 226], [29463, 226], [29763, 226], [30062, 227], [30362, 227], [30661, 227], [30961, 227], [31260, 228], [31560, 228], [31860, 228], [32159, 98], [32261, 127], [32459, 96], [32562, 126], [32758, 97], [32863, 125], [33058, 96], [33163, 124], [33358, 96], [33464, 123], [33657, 96], [33764, 122], [33957, 96], [34064, 122], [34256, 97], [34364, 121], [34556, 97], [34664, 5], [34677, 108], [34855, 99], [34964, 3], [34979, 106], [35155, 99], [35263, 3], [35280, 104], [35455, 100], [35562, 3], [35582, 102], [35754, 102], [35861, 3], [35882, 101], [36054, 110], [36183, 100], [36353, 111], [36483, 99], [36653, 110], [36784, 98], [36953, 110], [37084, 97], [37252, 111], [37385, 96], [37552, 111], [37685, 95], [37851, 112], [37985, 95], [38151, 112], [38284, 95], [38450, 113], [38584, 95], [38750, 114], [38884, 95], [39050, 114], [39184, 94], [39349, 116], [39483, 95], [39649, 117], [39782, 95], [39948, 119], [40081, 96], [40248, 120], [40380, 96], [40548, 122], [40678, 98], [40847, 125], [40976, 99], [41147, 228], [41446, 228], [41746, 228], [42045, 229], [42345, 228], [42645, 228], [42944, 228], [43244, 228], [43543, 228], [43843, 228], [44143, 227], [44442, 228], [44742, 227], [45041, 228], [45341, 227], [45640, 228], [45940, 228], [46240, 227], [46539, 228], [46839, 227], [47138, 228], [47438, 227], [47738, 227], [48037, 227], [48337, 227], [48636, 227], [48936, 227], [49235, 228], [49535, 227], [49835, 227], [50134, 227], [50434, 224], [50733, 224], [51033, 224], [51333, 223], [51632, 64], [51697, 159], [51932, 64], [51997, 158], [52231, 65], [52531, 65], [52830, 66], [53130, 66], [53430, 65], [53729, 66], [54029, 66], [54328, 67], [54628, 67], [54927, 67], [55227, 67], [55527, 67], [55826, 68], [56126, 68], [56425, 68], [56725, 68], [57025, 68], [57324, 69], [57624, 69], [57923, 70], [58223, 69], [58522, 70], [58822, 70], [59122, 70], [59421, 71], [59721, 70], [60020, 71], [60320, 71], [60620, 71], [60919, 72], [61219, 71], [61518, 72], [61818, 72], [62117, 73], [62417, 73], [62717, 72], [63016, 73], [63316, 73], [63615, 74], [63915, 74], [64215, 74], [64514, 74], [64814, 74], [65113, 75], [65413, 75], [65712, 76], [66012, 75], [66312, 75], [66611, 76], [66911, 76], [67210, 77], [67510, 76], [67810, 76], [68109, 77], [68409, 77], [68708, 78], [69008, 77], [69307, 78], [69607, 78], [69907, 78], [70206, 79], [70506, 79], [70805, 79], [71105, 79], [71405, 79], [71704, 80], [72004, 80], [72303, 80], [72603, 80], [72902, 81], [73202, 81], [73503, 80], [73804, 78], [74105, 77], [74405, 77], [74706, 76], [75007, 75], [75308, 74], [75609, 72], [75910, 71], [76211, 70], [76512, 69], [76813, 68], [77113, 67], [77414, 66], [77715, 65], [78016, 64], [78317, 63], [78618, 61], [78919, 60], [79220, 59], [79521, 58], [79822, 57], [80122, 56], [80423, 55], [80724, 54], [81025, 53], [81326, 52], [81627, 51], [81928, 49], [82229, 48], [82530, 47], [82831, 46], [83131, 46], [83432, 44], [83733, 43], [84034, 42], [84335, 41], [84636, 40], [84937, 38], [85238, 37], [85539, 36], [85839, 36], [86140, 38], [86441, 39], [86742, 39], [87043, 31], [87076, 5], [87345, 29], [87377, 2], [87649, 25], [87953, 20]], "point": [148, 163]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [2, 34, 295, 294], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25870, 224], [26170, 223], [26469, 224], [26768, 224], [27068, 224], [27367, 224], [27666, 225], [27965, 226], [28265, 225], [28564, 226], [28864, 225], [29163, 226], [29463, 226], [29763, 226], [30062, 227], [30362, 227], [30661, 227], [30961, 227], [31260, 68], [31333, 155], [31560, 63], [31638, 150], [31860, 61], [31940, 148], [32159, 60], [32241, 16], [32261, 127], [32459, 59], [32542, 13], [32562, 126], [32758, 59], [32843, 12], [32863, 125], [33058, 58], [33144, 10], [33163, 124], [33358, 58], [33444, 10], [33464, 123], [33657, 59], [33748, 5], [33764, 122], [33957, 59], [34049, 4], [34064, 122], [34256, 60], [34350, 3], [34364, 121], [34556, 60], [34651, 2], [34664, 5], [34677, 108], [34855, 61], [34944, 3], [34951, 3], [34964, 3], [34979, 106], [35155, 61], [35244, 4], [35251, 3], [35263, 3], [35280, 104], [35455, 61], [35544, 5], [35551, 4], [35562, 3], [35582, 102], [35754, 62], [35844, 5], [35851, 5], [35861, 3], [35882, 101], [36054, 63], [36144, 5], [36151, 13], [36183, 100], [36353, 64], [36444, 4], [36451, 13], [36483, 99], [36653, 64], [36744, 4], [36751, 12], [36784, 98], [36953, 64], [37044, 4], [37050, 13], [37084, 97], [37252, 65], [37344, 3], [37350, 13], [37385, 96], [37552, 65], [37644, 2], [37650, 13], [37685, 95], [37851, 66], [37949, 14], [37985, 95], [38151, 66], [38248, 15], [38284, 95], [38450, 67], [38548, 15], [38584, 95], [38750, 68], [38846, 18], [38884, 95], [39050, 68], [39144, 20], [39184, 94], [39349, 69], [39444, 21], [39483, 95], [39649, 69], [39744, 22], [39782, 95], [39948, 70], [40044, 23], [40081, 96], [40248, 70], [40344, 24], [40380, 96], [40548, 70], [40644, 26], [40678, 98], [40847, 72], [40944, 28], [40976, 99], [41147, 72], [41243, 132], [41446, 74], [41542, 132], [41746, 75], [41841, 133], [42045, 77], [42140, 134], [42345, 79], [42438, 135], [42645, 82], [42735, 138], [42944, 228], [43244, 228], [43543, 228], [43843, 228], [44143, 227], [44442, 228], [44742, 227], [45041, 228], [45341, 227], [45640, 228], [45940, 228], [46240, 227], [46539, 228], [46839, 227], [47138, 228], [47438, 227], [47738, 227], [48037, 227], [48337, 227], [48636, 227], [48936, 227], [49235, 228], [49535, 227], [49835, 227], [50134, 227], [50434, 224], [50733, 224], [51033, 224], [51333, 223], [51632, 64], [51697, 159], [51932, 64], [51997, 158], [52231, 65], [52531, 65], [52830, 66], [53130, 66], [53430, 65], [53729, 66], [54029, 66], [54328, 67], [54628, 67], [54927, 67], [55227, 67], [55527, 67], [55826, 68], [56126, 68], [56425, 68], [56725, 68], [57025, 68], [57324, 69], [57624, 69], [57923, 70], [58223, 69], [58522, 70], [58822, 70], [59122, 70], [59421, 71], [59721, 70], [60020, 71], [60320, 71], [60620, 71], [60919, 72], [61219, 71], [61518, 72], [61818, 72], [62117, 73], [62417, 73], [62717, 72], [63016, 73], [63316, 73], [63615, 74], [63915, 74], [64215, 74], [64514, 74], [64814, 74], [65113, 75], [65413, 75], [65712, 76], [66012, 75], [66312, 75], [66611, 76], [66911, 76], [67210, 77], [67510, 76], [67810, 76], [68109, 77], [68409, 77], [68708, 78], [69008, 77], [69307, 78], [69607, 78], [69907, 78], [70206, 79], [70506, 79], [70805, 79], [71105, 79], [71405, 79], [71704, 80], [72004, 80], [72303, 80], [72603, 80], [72902, 81], [73202, 81], [73503, 80], [73804, 78], [74105, 77], [74405, 77], [74706, 76], [75007, 75], [75308, 74], [75609, 72], [75910, 71], [76211, 70], [76512, 69], [76813, 68], [77113, 67], [77414, 66], [77715, 65], [78016, 64], [78317, 63], [78618, 61], [78919, 60], [79220, 59], [79521, 58], [79822, 57], [80122, 56], [80423, 55], [80724, 54], [81025, 53], [81326, 52], [81627, 51], [81928, 49], [82229, 48], [82530, 47], [82831, 46], [83131, 46], [83432, 44], [83733, 43], [84034, 42], [84335, 41], [84636, 40], [84937, 38], [85238, 37], [85539, 36], [85839, 36], [86140, 38], [86441, 39], [86742, 39], [87043, 31], [87076, 5], [87345, 29], [87377, 2], [87649, 25], [87953, 20]], "point": [148, 163]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-11.18, -11.18, 5.396, 5.396, 3.2488436, 3.2488436]], "forceVisible": true, "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-11.18, -11.18, 5.396, 5.396, 3.2488436, 3.2488436]], "forceVisible": true, "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-00.65|+00.89|+00.37"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [116, 105, 150, 143], "mask": [[31328, 5], [31623, 15], [31921, 19], [32219, 22], [32518, 24], [32817, 26], [33116, 28], [33416, 28], [33716, 32], [34016, 33], [34316, 34], [34616, 35], [34916, 28], [34947, 4], [35216, 28], [35248, 3], [35516, 28], [35549, 2], [35816, 28], [35849, 2], [36117, 27], [36149, 2], [36417, 27], [36448, 3], [36717, 27], [36748, 3], [37017, 27], [37048, 2], [37317, 27], [37347, 3], [37617, 27], [37646, 4], [37917, 32], [38217, 31], [38517, 31], [38818, 28], [39118, 26], [39418, 26], [39718, 26], [40018, 26], [40318, 26], [40618, 26], [40919, 25], [41219, 24], [41520, 22], [41821, 20], [42122, 18], [42424, 14], [42727, 8]], "point": [133, 123]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [2, 34, 295, 294], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25870, 224], [26170, 223], [26469, 224], [26768, 224], [27068, 224], [27367, 224], [27666, 225], [27965, 226], [28265, 225], [28564, 226], [28864, 225], [29163, 226], [29463, 226], [29763, 226], [30062, 227], [30362, 227], [30661, 227], [30961, 227], [31260, 228], [31560, 228], [31860, 228], [32159, 98], [32261, 127], [32459, 96], [32562, 126], [32758, 97], [32863, 125], [33058, 96], [33163, 124], [33358, 96], [33464, 123], [33657, 96], [33764, 122], [33957, 96], [34064, 122], [34256, 97], [34364, 121], [34556, 97], [34664, 5], [34677, 108], [34855, 99], [34964, 3], [34979, 106], [35155, 99], [35263, 3], [35280, 104], [35455, 100], [35562, 3], [35582, 102], [35754, 102], [35861, 3], [35882, 101], [36054, 110], [36183, 100], [36353, 111], [36483, 99], [36653, 110], [36784, 98], [36953, 110], [37084, 97], [37252, 111], [37385, 96], [37552, 111], [37685, 95], [37851, 112], [37985, 95], [38151, 112], [38284, 95], [38450, 113], [38584, 95], [38750, 114], [38884, 95], [39050, 114], [39184, 94], [39349, 116], [39483, 95], [39649, 117], [39782, 95], [39948, 119], [40081, 96], [40248, 120], [40380, 96], [40548, 122], [40678, 98], [40847, 125], [40976, 99], [41147, 228], [41446, 228], [41746, 228], [42045, 229], [42345, 228], [42645, 228], [42944, 228], [43244, 228], [43543, 228], [43843, 228], [44143, 227], [44442, 228], [44742, 227], [45041, 228], [45341, 227], [45640, 228], [45940, 228], [46240, 227], [46539, 228], [46839, 227], [47138, 228], [47438, 227], [47738, 227], [48037, 227], [48337, 227], [48636, 227], [48936, 227], [49235, 228], [49535, 227], [49835, 227], [50134, 227], [50434, 224], [50733, 224], [51033, 224], [51333, 223], [51632, 64], [51697, 159], [51932, 64], [51997, 158], [52231, 65], [52531, 65], [52830, 66], [53130, 66], [53430, 65], [53729, 66], [54029, 66], [54328, 67], [54628, 67], [54927, 67], [55227, 67], [55527, 67], [55826, 68], [56126, 68], [56425, 68], [56725, 68], [57025, 68], [57324, 69], [57624, 69], [57923, 70], [58223, 69], [58522, 70], [58822, 70], [59122, 70], [59421, 71], [59721, 70], [60020, 71], [60320, 71], [60620, 71], [60919, 72], [61219, 71], [61518, 72], [61818, 72], [62117, 73], [62417, 73], [62717, 72], [63016, 73], [63316, 73], [63615, 74], [63915, 74], [64215, 74], [64514, 74], [64814, 74], [65113, 75], [65413, 75], [65712, 76], [66012, 75], [66312, 75], [66611, 76], [66911, 76], [67210, 77], [67510, 76], [67810, 76], [68109, 77], [68409, 77], [68708, 78], [69008, 77], [69307, 78], [69607, 78], [69907, 78], [70206, 79], [70506, 79], [70805, 79], [71105, 79], [71405, 79], [71704, 80], [72004, 80], [72303, 80], [72603, 80], [72902, 81], [73202, 81], [73503, 80], [73804, 78], [74105, 77], [74405, 77], [74706, 76], [75007, 75], [75308, 74], [75609, 72], [75910, 71], [76211, 70], [76512, 69], [76813, 68], [77113, 67], [77414, 66], [77715, 65], [78016, 64], [78317, 63], [78618, 61], [78919, 60], [79220, 59], [79521, 58], [79822, 57], [80122, 56], [80423, 55], [80724, 54], [81025, 53], [81326, 52], [81627, 51], [81928, 49], [82229, 48], [82530, 47], [82831, 46], [83131, 46], [83432, 44], [83733, 43], [84034, 42], [84335, 41], [84636, 40], [84937, 38], [85238, 37], [85539, 36], [85839, 36], [86140, 38], [86441, 39], [86742, 39], [87043, 31], [87076, 5], [87345, 29], [87377, 2], [87649, 25], [87953, 20]], "point": [148, 163]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-02.78|+01.80|+00.83"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 42, 299, 279], "mask": [[12329, 103], [12433, 146], [12628, 251], [12928, 251], [13228, 251], [13528, 252], [13828, 252], [14128, 252], [14428, 252], [14727, 253], [15027, 253], [15327, 253], [15627, 254], [15927, 254], [16227, 254], [16527, 254], [16826, 255], [17126, 255], [17426, 256], [17726, 256], [18026, 256], [18326, 256], [18626, 256], [18925, 257], [19225, 257], [19525, 258], [19825, 258], [20125, 258], [20425, 258], [20725, 258], [21024, 259], [21324, 259], [21624, 260], [21924, 260], [22224, 260], [22524, 260], [22824, 260], [23123, 261], [23423, 262], [23723, 262], [24023, 262], [24323, 262], [24623, 262], [24923, 262], [25222, 263], [25522, 264], [25822, 264], [26122, 264], [26422, 264], [26722, 264], [27022, 264], [27321, 265], [27621, 266], [27921, 266], [28221, 266], [28521, 266], [28821, 266], [29121, 266], [29420, 268], [29720, 268], [30020, 268], [30320, 268], [30620, 268], [30920, 268], [31220, 268], [31519, 270], [31819, 270], [32119, 270], [32419, 270], [32719, 270], [33019, 270], [33319, 270], [33618, 272], [33918, 272], [34218, 272], [34518, 272], [34818, 272], [35118, 272], [35418, 273], [35717, 274], [36017, 274], [36317, 274], [36617, 274], [36917, 274], [37217, 274], [37517, 275], [37816, 276], [38116, 276], [38416, 276], [38716, 276], [39016, 276], [39316, 276], [39616, 277], [39916, 277], [40215, 278], [40515, 278], [40815, 278], [41115, 278], [41415, 279], [41715, 279], [42015, 279], [42314, 280], [42614, 280], [42914, 280], [43214, 280], [43514, 281], [43814, 281], [44114, 281], [44413, 282], [44713, 282], [45013, 282], [45313, 282], [45613, 283], [45913, 283], [46213, 283], [46512, 284], [46812, 284], [47112, 284], [47412, 285], [47712, 285], [48012, 285], [48312, 285], [48611, 286], [48911, 286], [49211, 286], [49511, 287], [49811, 287], [50111, 287], [50411, 287], [50710, 288], [51010, 288], [51310, 288], [51610, 289], [51910, 289], [52210, 289], [52510, 289], [52809, 290], [53109, 290], [53409, 290], [53709, 291], [54009, 291], [54309, 291], [54609, 291], [54908, 292], [55208, 292], [55508, 292], [55808, 292], [56108, 292], [56408, 292], [56708, 141], [56851, 149], [57007, 133], [57160, 140], [57307, 130], [57463, 137], [57607, 127], [57766, 134], [57907, 124], [58069, 131], [58207, 122], [58371, 129], [58507, 121], [58672, 128], [58807, 119], [58974, 126], [59106, 118], [59276, 124], [59406, 117], [59577, 123], [59706, 116], [59878, 122], [60006, 115], [60179, 121], [60306, 114], [60480, 120], [60606, 113], [60781, 119], [60906, 112], [61082, 118], [61205, 112], [61383, 117], [61505, 111], [61684, 116], [61805, 110], [61985, 115], [62105, 109], [62286, 114], [62405, 109], [62586, 114], [62705, 108], [62887, 113], [63005, 107], [63188, 112], [63305, 107], [63488, 112], [63604, 107], [63789, 111], [63904, 107], [64089, 111], [64204, 106], [64390, 110], [64504, 106], [64690, 110], [64804, 106], [64990, 110], [65104, 105], [65291, 109], [65404, 105], [65591, 109], [65703, 95], [65891, 109], [66003, 93], [66191, 109], [66303, 91], [66492, 108], [66603, 90], [66792, 108], [66903, 89], [67092, 108], [67203, 89], [67392, 108], [67503, 88], [67692, 108], [67802, 89], [67992, 108], [68102, 89], [68292, 108], [68402, 88], [68592, 108], [68702, 88], [68892, 108], [69002, 88], [69192, 108], [69302, 87], [69492, 108], [69602, 87], [69792, 108], [69901, 88], [70092, 108], [70201, 88], [70392, 108], [70501, 88], [70600, 8], [70692, 108], [70801, 88], [70901, 7], [70992, 108], [71101, 88], [71202, 7], [71291, 109], [71401, 89], [71503, 6], [71591, 109], [71701, 89], [71805, 4], [71891, 199], [72107, 2], [72191, 199], [72409, 1], [72490, 201], [72790, 201], [73090, 202], [73390, 203], [73689, 204], [73989, 205], [74289, 206], [74589, 207], [74888, 209], [75188, 210], [75488, 211], [75788, 212], [76087, 215], [76387, 217], [76687, 220], [76987, 223], [77287, 227], [77586, 228], [77886, 228], [78186, 228], [78486, 229], [78785, 230], [79085, 230], [79385, 230], [79685, 231], [79984, 232], [80284, 232], [80584, 233], [80883, 234], [81183, 234], [81483, 234], [81783, 235], [82082, 236], [82382, 236], [82682, 237], [82981, 238], [83281, 239], [83580, 120]], "point": [149, 159]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-00.65|+00.89|+00.37", "placeStationary": true, "receptacleObjectId": "Cabinet|-02.78|+01.80|+00.83"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 275], "mask": [[0, 2], [300, 3], [600, 4], [900, 4], [1200, 5], [1500, 6], [1800, 7], [2100, 7], [2400, 8], [2700, 9], [3000, 9], [3300, 10], [3599, 12], [3898, 13], [4197, 15], [4497, 16], [4796, 17], [5095, 19], [5395, 20], [5694, 21], [5993, 23], [6292, 25], [6592, 25], [6891, 27], [7190, 29], [7490, 29], [7789, 31], [8088, 33], [8387, 34], [8687, 35], [8986, 37], [9285, 38], [9585, 39], [9884, 41], [10183, 42], [10482, 44], [10782, 45], [11081, 46], [11380, 48], [11680, 49], [11979, 50], [12278, 52], [12577, 53], [12877, 53], [12933, 243], [13177, 53], [13233, 242], [13478, 52], [13533, 242], [13778, 52], [13833, 241], [14078, 52], [14133, 240], [14378, 51], [14432, 241], [14678, 51], [14732, 240], [14978, 51], [15032, 239], [15278, 51], [15332, 238], [15579, 50], [15632, 238], [15879, 50], [15932, 237], [16179, 50], [16232, 236], [16479, 49], [16532, 236], [16779, 49], [16831, 236], [17079, 49], [17131, 235], [17379, 49], [17431, 234], [17680, 48], [17731, 234], [17980, 48], [18031, 233], [18280, 48], [18331, 232], [18580, 47], [18631, 232], [18880, 47], [18930, 232], [19180, 47], [19230, 231], [19481, 46], [19530, 230], [19781, 46], [19830, 230], [20081, 46], [20130, 229], [20381, 46], [20430, 228], [20681, 45], [20730, 227], [20981, 45], [21029, 228], [21281, 45], [21329, 227], [21582, 44], [21629, 227], [21882, 44], [21929, 227], [22182, 44], [22229, 227], [22482, 44], [22529, 227], [22782, 43], [22829, 227], [23082, 43], [23382, 43], [23683, 42], [23983, 42], [24283, 42], [24583, 42], [24628, 254], [24883, 42], [24928, 253], [25183, 41], [25228, 252], [25483, 41], [25527, 252], [25784, 40], [25827, 251], [26084, 40], [26127, 250], [26384, 40], [26427, 249], [26684, 40], [26727, 248], [26984, 40], [27027, 247], [27284, 39], [27327, 246], [27585, 38], [27627, 245], [27885, 38], [27926, 245], [28185, 38], [28226, 244], [28485, 38], [28526, 243], [28785, 38], [28826, 242], [29085, 38], [29126, 241], [29385, 37], [29426, 240], [29686, 36], [29726, 239], [29986, 36], [30025, 239], [30286, 36], [30325, 238], [30586, 36], [30625, 237], [30886, 36], [30925, 236], [31186, 36], [31225, 235], [31486, 35], [31525, 235], [31787, 34], [31825, 235], [32087, 34], [32124, 236], [32387, 34], [32424, 236], [32687, 34], [32724, 236], [32987, 34], [33024, 236], [33287, 34], [33324, 236], [33587, 33], [33624, 236], [33888, 32], [33924, 237], [34188, 32], [34224, 237], [34488, 32], [34523, 238], [34788, 32], [34823, 238], [35088, 32], [35123, 238], [35388, 32], [35423, 238], [35688, 32], [35723, 238], [35989, 30], [36023, 238], [36289, 30], [36323, 239], [36589, 30], [36622, 240], [36889, 30], [36922, 240], [37189, 30], [37222, 240], [37489, 30], [37522, 240], [37790, 29], [37822, 240], [38090, 28], [38122, 240], [38390, 28], [38422, 240], [38690, 28], [38721, 241], [38990, 28], [39021, 242], [39290, 28], [39321, 242], [39590, 28], [39621, 242], [39891, 27], [39921, 242], [40191, 26], [40221, 242], [40491, 26], [40521, 242], [40791, 26], [40821, 242], [41091, 26], [41120, 243], [41391, 26], [41420, 244], [41691, 26], [41720, 244], [41992, 25], [42020, 244], [42292, 24], [42320, 244], [42592, 24], [42620, 244], [42892, 24], [42920, 244], [43192, 24], [43219, 245], [43492, 24], [43519, 245], [43792, 24], [43819, 245], [44093, 23], [44119, 246], [44393, 22], [44419, 246], [44693, 22], [44719, 246], [44993, 22], [45019, 246], [45293, 22], [45318, 247], [45593, 22], [45618, 247], [45894, 21], [45918, 247], [46194, 21], [46218, 247], [46494, 21], [46518, 248], [46794, 20], [46818, 248], [47094, 20], [47118, 248], [47394, 20], [47418, 248], [47694, 20], [47717, 249], [47995, 19], [48017, 249], [48295, 19], [48317, 249], [48595, 19], [48617, 249], [48895, 18], [48917, 249], [49195, 18], [49217, 250], [49495, 18], [49517, 250], [49795, 18], [49816, 251], [50096, 17], [50116, 251], [50396, 17], [50416, 251], [50696, 17], [50716, 251], [50996, 16], [51016, 251], [51296, 16], [51316, 251], [51596, 16], [51616, 252], [51896, 16], [51916, 252], [52197, 15], [52215, 253], [52497, 15], [52515, 253], [52797, 15], [52815, 253], [53097, 14], [53115, 253], [53397, 14], [53415, 253], [53697, 14], [53715, 253], [53997, 14], [54015, 254], [54298, 13], [54314, 255], [54598, 13], [54614, 255], [54898, 13], [54914, 255], [55198, 12], [55214, 255], [55498, 12], [55514, 255], [55798, 12], [55814, 255], [56099, 11], [56114, 255], [56399, 11], [56413, 256], [56699, 11], [56713, 136], [56851, 119], [56999, 11], [57013, 127], [57160, 110], [57299, 11], [57313, 124], [57463, 107], [57599, 10], [57613, 121], [57766, 104], [57899, 10], [57913, 118], [58069, 101], [58200, 9], [58213, 116], [58371, 99], [58500, 9], [58513, 115], [58672, 98], [58800, 9], [58812, 114], [58974, 96], [59100, 9], [59112, 112], [59276, 95], [59400, 9], [59412, 111], [59577, 94], [59700, 8], [59712, 110], [59878, 93], [60000, 8], [60012, 109], [60179, 92], [60300, 8], [60312, 108], [60480, 91], [60600, 8], [60612, 107], [60781, 90], [60900, 8], [60911, 107], [61082, 89], [61200, 8], [61211, 106], [61383, 88], [61500, 8], [61511, 105], [61684, 87], [61800, 7], [61811, 104], [61985, 87], [62100, 7], [62111, 103], [62286, 86], [62400, 7], [62411, 103], [62586, 86], [62700, 7], [62711, 102], [62887, 85], [63000, 7], [63010, 102], [63188, 84], [63300, 7], [63310, 102], [63488, 84], [63600, 7], [63610, 101], [63789, 83], [63900, 6], [63910, 101], [64089, 83], [64200, 6], [64210, 100], [64390, 83], [64500, 6], [64510, 100], [64690, 83], [64800, 6], [64810, 100], [64990, 83], [65100, 6], [65110, 99], [65291, 82], [65400, 6], [65409, 100], [65591, 82], [65700, 6], [65709, 89], [65891, 82], [66000, 5], [66009, 87], [66191, 82], [66300, 5], [66309, 85], [66492, 81], [66600, 5], [66609, 84], [66792, 81], [66900, 5], [66909, 83], [67092, 82], [67200, 5], [67209, 83], [67392, 82], [67500, 5], [67508, 83], [67692, 82], [67800, 5], [67808, 83], [67992, 82], [68100, 5], [68108, 83], [68292, 82], [68400, 4], [68408, 82], [68592, 82], [68700, 4], [68708, 82], [68892, 82], [69000, 4], [69008, 82], [69192, 82], [69300, 4], [69308, 81], [69492, 83], [69600, 4], [69607, 82], [69792, 83], [69900, 4], [69907, 82], [70092, 83], [70200, 4], [70207, 82], [70392, 83], [70500, 3], [70507, 82], [70600, 8], [70692, 83], [70800, 3], [70807, 82], [70901, 7], [70992, 83], [71100, 3], [71107, 82], [71202, 7], [71291, 84], [71400, 3], [71407, 83], [71503, 6], [71591, 84], [71700, 3], [71707, 83], [71805, 4], [71891, 84], [72000, 3], [72006, 84], [72107, 2], [72191, 85], [72300, 3], [72306, 84], [72409, 1], [72490, 86], [72600, 2], [72606, 85], [72790, 86], [72900, 2], [72906, 85], [73090, 86], [73200, 2], [73206, 86], [73390, 86], [73500, 2], [73506, 87], [73689, 87], [73800, 2], [73806, 87], [73989, 87], [74100, 2], [74105, 89], [74289, 87], [74400, 2], [74405, 90], [74589, 88], [74700, 1], [74705, 91], [74888, 89], [75000, 1], [75005, 92], [75188, 89], [75300, 1], [75305, 93], [75488, 89], [75600, 1], [75605, 94], [75788, 89], [75900, 1], [75905, 95], [76087, 90], [76200, 1], [76205, 97], [76387, 90], [76500, 1], [76504, 100], [76687, 90], [76804, 103], [76987, 91], [77104, 106], [77287, 91], [77404, 110], [77586, 92], [77704, 110], [77886, 92], [78004, 110], [78186, 92], [78304, 110], [78486, 92], [78603, 112], [78785, 93], [78903, 112], [79085, 93], [79203, 112], [79385, 95], [79503, 112], [79685, 97], [79803, 113], [79984, 101], [80103, 113], [80284, 104], [80403, 113], [80584, 107], [80702, 115], [80883, 111], [81002, 115], [81183, 114], [81302, 115], [81483, 117], [81602, 115], [81783, 117], [81902, 116], [82082, 118], [82202, 116], [82382, 118]], "point": [149, 137]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-02.78|+01.80|+00.83"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 275], "mask": [[0, 2], [300, 3], [600, 4], [900, 4], [1200, 5], [1500, 6], [1800, 7], [2100, 7], [2400, 8], [2700, 9], [3000, 9], [3300, 10], [3599, 12], [3898, 13], [4197, 15], [4497, 16], [4796, 17], [5095, 19], [5395, 20], [5694, 21], [5993, 23], [6292, 25], [6592, 25], [6891, 27], [7190, 29], [7490, 29], [7789, 31], [8088, 33], [8387, 34], [8687, 35], [8986, 37], [9285, 38], [9585, 39], [9884, 41], [10183, 42], [10482, 44], [10782, 45], [11081, 46], [11380, 48], [11680, 49], [11979, 50], [12278, 52], [12577, 53], [12877, 53], [12933, 243], [13177, 53], [13233, 242], [13478, 52], [13533, 242], [13778, 52], [13833, 241], [14078, 52], [14133, 240], [14378, 51], [14432, 241], [14678, 51], [14732, 240], [14978, 51], [15032, 239], [15278, 51], [15332, 238], [15579, 50], [15632, 238], [15879, 50], [15932, 237], [16179, 50], [16232, 236], [16479, 49], [16532, 236], [16779, 49], [16831, 236], [17079, 49], [17131, 235], [17379, 49], [17431, 234], [17680, 48], [17731, 234], [17980, 48], [18031, 233], [18280, 48], [18331, 232], [18580, 47], [18631, 232], [18880, 47], [18930, 232], [19180, 47], [19230, 231], [19481, 46], [19530, 230], [19781, 46], [19830, 230], [20081, 46], [20130, 229], [20381, 46], [20430, 228], [20681, 45], [20730, 227], [20981, 45], [21029, 228], [21281, 45], [21329, 227], [21582, 44], [21629, 227], [21882, 44], [21929, 227], [22182, 44], [22229, 227], [22482, 44], [22529, 227], [22782, 43], [22829, 227], [23082, 43], [23382, 43], [23683, 42], [23983, 42], [24283, 42], [24583, 42], [24628, 254], [24883, 42], [24928, 253], [25183, 41], [25228, 252], [25483, 41], [25527, 252], [25784, 40], [25827, 251], [26084, 40], [26127, 250], [26384, 40], [26427, 249], [26684, 40], [26727, 248], [26984, 40], [27027, 247], [27284, 39], [27327, 246], [27585, 38], [27627, 245], [27885, 38], [27926, 245], [28185, 38], [28226, 244], [28485, 38], [28526, 243], [28785, 38], [28826, 242], [29085, 38], [29126, 241], [29385, 37], [29426, 240], [29686, 36], [29726, 239], [29986, 36], [30025, 239], [30286, 36], [30325, 238], [30586, 36], [30625, 237], [30886, 36], [30925, 236], [31186, 36], [31225, 235], [31486, 35], [31525, 235], [31787, 34], [31825, 235], [32087, 34], [32124, 236], [32387, 34], [32424, 236], [32687, 34], [32724, 236], [32987, 34], [33024, 236], [33287, 34], [33324, 236], [33587, 33], [33624, 236], [33888, 32], [33924, 237], [34188, 32], [34224, 237], [34488, 32], [34523, 238], [34788, 32], [34823, 238], [35088, 32], [35123, 238], [35388, 32], [35423, 238], [35688, 32], [35723, 238], [35989, 30], [36023, 238], [36289, 30], [36323, 239], [36589, 30], [36622, 240], [36889, 30], [36922, 240], [37189, 30], [37222, 240], [37489, 30], [37522, 240], [37790, 29], [37822, 240], [38090, 28], [38122, 240], [38390, 28], [38422, 240], [38690, 28], [38721, 241], [38990, 28], [39021, 242], [39290, 28], [39321, 242], [39590, 28], [39621, 242], [39891, 27], [39921, 242], [40191, 26], [40221, 242], [40491, 26], [40521, 242], [40791, 26], [40821, 242], [41091, 26], [41120, 243], [41391, 26], [41420, 244], [41691, 26], [41720, 244], [41992, 25], [42020, 244], [42292, 24], [42320, 244], [42592, 24], [42620, 244], [42892, 24], [42920, 244], [43192, 24], [43219, 245], [43492, 24], [43519, 245], [43792, 24], [43819, 245], [44093, 23], [44119, 246], [44393, 22], [44419, 246], [44693, 22], [44719, 246], [44993, 22], [45019, 246], [45293, 22], [45318, 247], [45593, 22], [45618, 247], [45894, 21], [45918, 247], [46194, 21], [46218, 247], [46494, 21], [46518, 248], [46794, 20], [46818, 248], [47094, 20], [47118, 248], [47394, 20], [47418, 248], [47694, 20], [47717, 249], [47995, 19], [48017, 249], [48295, 19], [48317, 249], [48595, 19], [48617, 249], [48895, 18], [48917, 249], [49195, 18], [49217, 250], [49495, 18], [49517, 250], [49795, 18], [49816, 251], [50096, 17], [50116, 251], [50396, 17], [50416, 251], [50696, 17], [50716, 251], [50996, 16], [51016, 251], [51296, 16], [51316, 251], [51596, 16], [51616, 252], [51896, 16], [51916, 252], [52197, 15], [52215, 253], [52497, 15], [52515, 253], [52797, 15], [52815, 253], [53097, 14], [53115, 253], [53397, 14], [53415, 253], [53697, 14], [53715, 253], [53997, 14], [54015, 254], [54298, 13], [54314, 255], [54598, 13], [54614, 255], [54898, 13], [54914, 255], [55198, 12], [55214, 255], [55498, 12], [55514, 255], [55798, 12], [55814, 255], [56099, 11], [56114, 255], [56399, 11], [56413, 256], [56699, 11], [56713, 257], [56999, 11], [57013, 257], [57299, 11], [57313, 257], [57599, 10], [57613, 257], [57899, 10], [57913, 257], [58200, 9], [58213, 257], [58500, 9], [58513, 257], [58800, 9], [58812, 258], [59100, 9], [59112, 259], [59400, 9], [59412, 259], [59700, 8], [59712, 259], [60000, 8], [60012, 259], [60300, 8], [60312, 259], [60600, 8], [60612, 259], [60900, 8], [60911, 260], [61200, 8], [61211, 260], [61500, 8], [61511, 260], [61800, 7], [61811, 261], [62100, 7], [62111, 261], [62400, 7], [62411, 261], [62700, 7], [62711, 261], [63000, 7], [63010, 262], [63300, 7], [63310, 262], [63600, 7], [63610, 262], [63900, 6], [63910, 262], [64200, 6], [64210, 263], [64500, 6], [64510, 263], [64800, 6], [64810, 263], [65100, 6], [65110, 263], [65400, 6], [65409, 264], [65700, 6], [65709, 264], [66000, 5], [66009, 264], [66300, 5], [66309, 264], [66600, 5], [66609, 264], [66900, 5], [66909, 265], [67200, 5], [67209, 265], [67500, 5], [67508, 266], [67800, 5], [67808, 266], [68100, 5], [68108, 266], [68400, 4], [68408, 266], [68700, 4], [68708, 266], [69000, 4], [69008, 266], [69300, 4], [69308, 267], [69600, 4], [69607, 268], [69900, 4], [69907, 168], [70100, 75], [70200, 4], [70207, 166], [70405, 70], [70500, 3], [70507, 166], [70705, 70], [70800, 3], [70807, 166], [71005, 70], [71100, 3], [71107, 166], [71305, 70], [71400, 3], [71407, 166], [71605, 70], [71700, 3], [71707, 166], [71905, 70], [72000, 3], [72006, 167], [72205, 71], [72300, 3], [72306, 167], [72505, 71], [72600, 2], [72606, 167], [72806, 70], [72900, 2], [72906, 167], [73106, 70], [73200, 2], [73206, 167], [73406, 70], [73500, 2], [73506, 167], [73706, 70], [73800, 2], [73806, 167], [74006, 70], [74100, 2], [74105, 168], [74306, 70], [74400, 2], [74405, 168], [74606, 71], [74700, 1], [74705, 168], [74906, 71], [75000, 1], [75005, 168], [75206, 71], [75300, 1], [75305, 168], [75506, 71], [75600, 1], [75605, 168], [75806, 71], [75900, 1], [75905, 168], [76106, 71], [76200, 1], [76205, 168], [76406, 71], [76500, 1], [76504, 169], [76706, 71], [76804, 170], [77006, 72], [77104, 170], [77306, 72], [77404, 170], [77606, 72], [77704, 170], [77906, 72], [78004, 170], [78206, 72], [78304, 170], [78507, 71], [78603, 171], [78807, 71], [78903, 171], [79107, 71], [79203, 171], [79407, 73], [79503, 171], [79707, 75], [79803, 171], [80007, 78], [80103, 171], [80307, 81], [80403, 171], [80607, 84], [80702, 173], [80907, 87], [81002, 174], [81206, 91], [81302, 178], [81504, 96], [81602, 298], [81902, 298], [82202, 298]], "point": [149, 137]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan25", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.75, "y": 0.9009992, "z": 2.75}, "object_poses": [{"objectName": "Potato_a3c14b3a", "position": {"x": -2.74435782, "y": 0.96022445, "z": 1.33778489}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "Potato_a3c14b3a", "position": {"x": -2.837207, "y": 1.48440087, "z": 2.04581}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Ladle_3943cbe8", "position": {"x": -1.46534276, "y": 0.169670582, "z": 0.401696056}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_2c8fe53a", "position": {"x": -2.56398416, "y": 0.6933651, "z": 1.47479427}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_77ce7117", "position": {"x": -2.73584533, "y": 0.8385998, "z": 0.871343434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_61785f6c", "position": {"x": -2.72453046, "y": 1.43537343, "z": 0.288195252}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Tomato_2ee69558", "position": {"x": -2.671585, "y": 1.19324148, "z": 2.22170043}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_4c742e05", "position": {"x": -2.00053287, "y": 0.121914148, "z": 0.401696056}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_69e12ad1", "position": {"x": -2.60810471, "y": 0.818599939, "z": 0.0556365252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_407d7c3b", "position": {"x": -2.375244, "y": 0.818599939, "z": 0.4262423}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_407d7c3b", "position": {"x": -1.15182948, "y": 0.8185999, "z": 0.352121234}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_db4f73f8", "position": {"x": -2.782, "y": 1.49174559, "z": 2.2217}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -2.78200078, "y": 0.897816, "z": 2.48553681}, "rotation": {"x": 0.0, "y": 269.999817, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -2.89241433, "y": 1.21668339, "z": 1.95786464}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -0.362648815, "y": 0.129467249, "z": 2.53909016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_2c8fe53a", "position": {"x": -1.21490157, "y": 0.813989341, "z": 0.498001337}, "rotation": {"x": 0.0002906469, "y": -0.000448465726, "z": 359.7127}}, {"objectName": "Ladle_3943cbe8", "position": {"x": -2.60917473, "y": 0.165891767, "z": 1.51088572}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_e0764688", "position": {"x": -0.2442, "y": 0.8297, "z": 2.0755}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "SoapBottle_3a4a4d0d", "position": {"x": -0.1476208, "y": 0.821380854, "z": 0.5003637}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_ccf3345d", "position": {"x": -0.531748533, "y": 0.8226794, "z": 2.09645438}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_2ee69558", "position": {"x": -0.262604654, "y": 0.106025338, "z": 2.67462254}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_a3c14b3a", "position": {"x": -2.837207, "y": 0.8636336, "z": 2.2217}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_69e12ad1", "position": {"x": -2.957396, "y": 0.818599939, "z": 0.4262423}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_29dab58d", "position": {"x": -2.726793, "y": 1.14841223, "z": 2.39759016}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_77ce7117", "position": {"x": -0.341, "y": 0.696, "z": 1.194}, "rotation": {"x": 0.0, "y": 45.0000267, "z": 0.0}}, {"objectName": "Knife_298dd93a", "position": {"x": -1.87690437, "y": 0.849218249, "z": 0.528825939}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_68224885", "position": {"x": -2.84400558, "y": 0.94571954, "z": 1.28474009}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "Cup_61785f6c", "position": {"x": -2.8372066, "y": 0.8251886, "z": 2.04580879}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ButterKnife_d1baac51", "position": {"x": -2.4939487, "y": 0.812210739, "z": 0.476511866}, "rotation": {"x": 0.0, "y": 50.7348976, "z": 0.0}}, {"objectName": "Lettuce_b2f1312c", "position": {"x": -2.724535, "y": 0.9075473, "z": 0.20387885}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_407d7c3b", "position": {"x": -0.8170933, "y": 0.8185999, "z": 0.0556362867}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_eb848da8", "position": {"x": -2.50179482, "y": 1.84862339, "z": 2.030786}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_db4f73f8", "position": {"x": -0.979999542, "y": 0.8554756, "z": 0.24999997}, "rotation": {"x": -7.853774e-06, "y": 29.9999084, "z": -3.53421e-06}}, {"objectName": "Spoon_df0734fa", "position": {"x": -1.18964624, "y": 0.6994931, "z": 0.37735492}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_57da2f86", "position": {"x": -0.646999955, "y": 0.8882107, "z": 0.3674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_4c742e05", "position": {"x": -0.397978365, "y": 0.115337931, "z": 0.9141403}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3793845451, "scene_num": 25}, "task_id": "trial_T20190909_095428_061064", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2YUCJ28XANFOX_3ATPCQ38JB1Q208DKN361BNOT5FAYJ", "high_descs": ["turn around and cross the kitchen to the coffee maker", "pick up the mug in the coffee maker and turn around", "cross the kitchen to the microwave", "open the microwave and place the mug inside, close the door and turn on the microwave and then remove", "lift the mug above the microwave and open the cabinet ", "place the mug in the cabinet and close the door"], "task_desc": "warm up the mug and put it in the cabinet ", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3D4CH1LGEDKCBQD8YB30YG50ISVG92", "high_descs": ["Walk over to where the coffee machine is on the counter behind you.", "Pick up the coffee mug out of the coffee machine.", "Turn right and walk over to the microwave.", "Open the microwave and put the mug inside then close the door and turn on the microwave, after a couple seconds open the microwave back up and take the now heated mug out, then close the microwave.", "Look up at the upper cabinets above the microwave.", "Open the upper cabinet door that is in front of you and put the heated mug inside, then close the door."], "task_desc": "Place a heated mug in an upper cabinet.", "votes": [1, 1]}, {"assignment_id": "AKW57KYG90X61_3X31TUMD70DA8DOE5PLNW2SPGX51LW", "high_descs": ["move around to the coffee maker", "pick up a mug from the coffee maker", "turn right and and to the microwave oven", "place the mug to heat in the oven and take it out", "move up the oven to the cupboard", "place the cup in the cupboard"], "task_desc": "place a heated mug in the cupboard", "votes": [1, 1]}]}}