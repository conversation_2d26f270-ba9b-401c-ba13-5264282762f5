{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 39}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "ToiletPaper", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-9|10|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-9.488728, -9.488728, 7.02794744, 7.02794744, 3.292, 3.292]], "coordinateReceptacleObjectId": ["CounterTop", [-8.712, -8.712, 7.56, 7.56, 3.128, 3.128]], "forceVisible": true, "objectId": "ToiletPaper|-02.37|+00.82|+01.76"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-9|13|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-9.488728, -9.488728, 7.02794744, 7.02794744, 3.292, 3.292]], "coordinateReceptacleObjectId": ["Drawer", [-8.6796, -8.6796, 7.562752, 7.562752, 2.4252, 2.4252]], "forceVisible": true, "objectId": "ToiletPaper|-02.37|+00.82|+01.76", "receptacleObjectId": "Drawer|-02.17|+00.61|+01.89"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-10|10|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-9.84831716, -9.84831716, 6.60409832, 6.60409832, 3.292, 3.292]], "coordinateReceptacleObjectId": ["CounterTop", [-8.712, -8.712, 7.56, 7.56, 3.128, 3.128]], "forceVisible": true, "objectId": "ToiletPaper|-02.46|+00.82|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-9|13|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-9.84831716, -9.84831716, 6.60409832, 6.60409832, 3.292, 3.292]], "coordinateReceptacleObjectId": ["Drawer", [-8.6796, -8.6796, 7.562752, 7.562752, 2.4252, 2.4252]], "forceVisible": true, "objectId": "ToiletPaper|-02.46|+00.82|+01.65", "receptacleObjectId": "Drawer|-02.17|+00.61|+01.89"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-02.37|+00.82|+01.76"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [176, 130, 186, 157], "mask": [[38879, 6], [39178, 8], [39478, 9], [39778, 9], [40078, 9], [40378, 9], [40677, 9], [40977, 9], [41277, 9], [41577, 9], [41877, 9], [42177, 9], [42477, 9], [42777, 9], [43077, 8], [43377, 8], [43676, 9], [43976, 9], [44276, 9], [44576, 9], [44876, 9], [45176, 8], [45476, 8], [45776, 8], [46076, 8], [46376, 8], [46677, 6], [46978, 4]], "point": [181, 142]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-02.17|+00.61|+01.89"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [58, 118, 212, 146], "mask": [[35158, 155], [35458, 155], [35758, 155], [36059, 153], [36359, 153], [36659, 153], [36960, 152], [37260, 151], [37560, 151], [37861, 150], [38161, 150], [38461, 150], [38761, 149], [39062, 148], [39362, 148], [39662, 148], [39963, 147], [40263, 146], [40563, 146], [40864, 145], [41164, 145], [41464, 144], [41765, 143], [42065, 143], [42365, 143], [42666, 142], [42966, 141], [43266, 141], [43567, 140]], "point": [135, 131]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-02.37|+00.82|+01.76", "placeStationary": true, "receptacleObjectId": "Drawer|-02.17|+00.61|+01.89"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [31, 118, 230, 206], "mask": [[35161, 149], [35460, 150], [35760, 150], [36059, 151], [36359, 152], [36659, 152], [36958, 153], [37258, 153], [37558, 154], [37857, 155], [38157, 155], [38456, 156], [38756, 157], [39056, 157], [39355, 158], [39655, 159], [39954, 160], [40254, 160], [40554, 160], [40853, 162], [41153, 162], [41452, 163], [41752, 163], [42052, 164], [42351, 165], [42651, 165], [42950, 166], [43250, 167], [43550, 167], [43849, 168], [44149, 168], [44448, 170], [44748, 170], [45048, 170], [45347, 171], [45647, 172], [45947, 172], [46246, 173], [46546, 174], [46845, 175], [47145, 175], [47445, 175], [47744, 177], [48044, 177], [48343, 178], [48643, 178], [48943, 179], [49242, 180], [49542, 180], [49841, 181], [50141, 182], [50441, 182], [50740, 183], [51040, 183], [51332, 198], [51631, 200], [51931, 200], [52232, 199], [52532, 198], [52833, 197], [53133, 197], [53434, 195], [53734, 195], [54035, 194], [54335, 193], [54636, 192], [54936, 192], [55237, 190], [55537, 190], [55838, 189], [56138, 188], [56439, 187], [56739, 187], [57040, 185], [57340, 185], [57641, 184], [57941, 183], [58242, 182], [58542, 182], [58843, 180], [59143, 180], [59444, 179], [59744, 178], [60045, 177], [60345, 177], [60646, 104], [60752, 69], [60946, 99], [61055, 66], [61247, 95], [61358, 63], [61547, 94], [61660, 60]], "point": [130, 161]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-02.17|+00.61|+01.89"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [31, 118, 230, 206], "mask": [[35161, 149], [35460, 150], [35760, 150], [36059, 151], [36359, 152], [36659, 152], [36958, 153], [37258, 153], [37558, 154], [37857, 155], [38157, 155], [38456, 156], [38756, 157], [39056, 157], [39355, 158], [39655, 159], [39954, 160], [40254, 160], [40554, 160], [40853, 162], [41153, 162], [41452, 163], [41752, 163], [42052, 164], [42351, 165], [42651, 165], [42950, 166], [43250, 167], [43550, 167], [43849, 168], [44149, 168], [44448, 170], [44748, 170], [45048, 170], [45347, 171], [45647, 172], [45947, 172], [46246, 173], [46546, 174], [46845, 175], [47145, 175], [47445, 175], [47744, 28], [47777, 144], [48044, 27], [48077, 144], [48343, 27], [48378, 143], [48643, 27], [48678, 143], [48943, 28], [48978, 144], [49242, 29], [49279, 143], [49542, 29], [49579, 143], [49841, 31], [49879, 143], [50141, 31], [50179, 144], [50441, 31], [50480, 143], [50740, 32], [50780, 143], [51040, 183], [51332, 198], [51631, 200], [51931, 200], [52232, 199], [52532, 198], [52833, 197], [53133, 197], [53434, 195], [53734, 195], [54035, 194], [54335, 193], [54636, 192], [54936, 192], [55237, 190], [55537, 190], [55838, 189], [56138, 188], [56439, 187], [56739, 187], [57040, 185], [57340, 185], [57641, 184], [57941, 183], [58242, 182], [58542, 182], [58843, 180], [59143, 180], [59444, 179], [59744, 178], [60045, 177], [60345, 177], [60646, 175], [60946, 175], [61247, 174], [61547, 173]], "point": [130, 161]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-02.46|+00.82|+01.65"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [128, 106, 152, 142], "mask": [[31634, 12], [31933, 16], [32231, 19], [32530, 21], [32829, 23], [33129, 24], [33428, 25], [33728, 25], [34028, 25], [34328, 25], [34628, 25], [34928, 25], [35228, 25], [35528, 25], [35828, 25], [36128, 25], [36428, 25], [36728, 25], [37029, 24], [37329, 24], [37629, 24], [37929, 24], [38229, 24], [38529, 24], [38829, 24], [39129, 24], [39429, 24], [39729, 24], [40029, 24], [40329, 24], [40629, 24], [40930, 22], [41231, 21], [41532, 19], [41832, 17], [42134, 14], [42438, 6]], "point": [140, 123]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-02.17|+00.61|+01.89"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [58, 118, 212, 146], "mask": [[35158, 155], [35458, 155], [35758, 155], [36059, 153], [36359, 153], [36659, 153], [36960, 152], [37260, 151], [37560, 151], [37861, 150], [38161, 150], [38461, 150], [38761, 149], [39062, 148], [39362, 148], [39662, 148], [39963, 147], [40263, 146], [40563, 146], [40864, 145], [41164, 145], [41464, 144], [41765, 143], [42065, 143], [42365, 143], [42666, 142], [42966, 141], [43266, 141], [43567, 140]], "point": [135, 131]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-02.46|+00.82|+01.65", "placeStationary": true, "receptacleObjectId": "Drawer|-02.17|+00.61|+01.89"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [31, 118, 230, 206], "mask": [[35161, 149], [35460, 150], [35760, 150], [36059, 151], [36359, 152], [36659, 152], [36958, 153], [37258, 153], [37558, 154], [37857, 155], [38157, 155], [38456, 156], [38756, 157], [39056, 157], [39355, 158], [39655, 159], [39954, 160], [40254, 160], [40554, 160], [40853, 162], [41153, 162], [41452, 163], [41752, 163], [42052, 164], [42351, 165], [42651, 165], [42950, 166], [43250, 167], [43550, 167], [43849, 168], [44149, 168], [44448, 170], [44748, 170], [45048, 170], [45347, 171], [45647, 172], [45947, 172], [46246, 173], [46546, 174], [46845, 175], [47145, 175], [47445, 175], [47744, 28], [47777, 144], [48044, 27], [48077, 144], [48343, 27], [48378, 143], [48643, 27], [48678, 143], [48943, 28], [48978, 144], [49242, 29], [49279, 143], [49542, 29], [49579, 143], [49841, 31], [49879, 143], [50141, 31], [50179, 144], [50441, 31], [50480, 143], [50740, 32], [50780, 143], [51040, 183], [51332, 198], [51631, 200], [51931, 200], [52232, 199], [52532, 198], [52833, 197], [53133, 197], [53434, 195], [53734, 195], [54035, 116], [54155, 74], [54335, 110], [54458, 70], [54636, 104], [54761, 67], [54936, 98], [55064, 64], [55237, 92], [55367, 60], [55537, 91], [55670, 57], [55838, 88], [55973, 54], [56138, 87], [56276, 50], [56439, 85], [56578, 48], [56739, 84], [56879, 47], [57040, 82], [57180, 45], [57340, 81], [57480, 45], [57641, 78], [57781, 44], [57941, 77], [58082, 42], [58242, 75], [58383, 41], [58542, 74], [58684, 40], [58843, 72], [58985, 38], [59143, 71], [59285, 38], [59444, 68], [59586, 37], [59744, 67], [59887, 35], [60045, 65], [60188, 34], [60345, 65], [60489, 33], [60646, 63], [60790, 31], [60946, 63], [61090, 31], [61247, 62], [61391, 30], [61547, 62], [61692, 28]], "point": [130, 161]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-02.17|+00.61|+01.89"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [31, 118, 230, 206], "mask": [[35161, 149], [35460, 150], [35760, 150], [36059, 151], [36359, 152], [36659, 152], [36958, 153], [37258, 153], [37558, 154], [37857, 155], [38157, 155], [38456, 156], [38756, 157], [39056, 157], [39355, 158], [39655, 159], [39954, 160], [40254, 160], [40554, 160], [40853, 162], [41153, 162], [41452, 163], [41752, 163], [42052, 23], [42084, 132], [42351, 22], [42386, 130], [42651, 20], [42687, 129], [42950, 20], [42988, 128], [43250, 19], [43288, 129], [43550, 19], [43589, 128], [43849, 19], [43889, 128], [44149, 19], [44189, 128], [44448, 20], [44489, 129], [44748, 20], [44789, 129], [45048, 20], [45090, 128], [45347, 22], [45390, 128], [45647, 22], [45690, 129], [45947, 22], [45990, 129], [46246, 24], [46291, 128], [46546, 24], [46591, 129], [46845, 25], [46891, 129], [47145, 26], [47191, 129], [47445, 26], [47492, 128], [47744, 27], [47792, 129], [48044, 27], [48092, 129], [48343, 27], [48392, 129], [48643, 27], [48691, 130], [48943, 28], [48991, 131], [49242, 29], [49291, 131], [49542, 29], [49590, 132], [49841, 31], [49889, 133], [50141, 31], [50187, 136], [50441, 31], [50486, 137], [50740, 32], [50780, 143], [51040, 183], [51332, 198], [51631, 200], [51931, 200], [52232, 199], [52532, 198], [52833, 197], [53133, 197], [53434, 195], [53734, 195], [54035, 194], [54335, 193], [54636, 192], [54936, 192], [55237, 190], [55537, 190], [55838, 189], [56138, 188], [56439, 187], [56739, 187], [57040, 185], [57340, 185], [57641, 184], [57941, 183], [58242, 182], [58542, 182], [58843, 180], [59143, 180], [59444, 179], [59744, 178], [60045, 177], [60345, 177], [60646, 175], [60946, 175], [61247, 174], [61547, 173]], "point": [130, 161]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan410", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -3.0, "y": 0.900394559, "z": 3.75}, "object_poses": [{"objectName": "Cloth_b248458c", "position": {"x": -1.27390885, "y": 0.887613654, "z": 1.744392}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_b248458c", "position": {"x": -2.168731, "y": 0.2741993, "z": 1.83225811}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBar_20ca2d0e", "position": {"x": -3.31208587, "y": 0.537457, "z": 1.83225811}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_fe7b7393", "position": {"x": -1.01336944, "y": 0.8353581, "z": 1.54506254}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_185a3fef", "position": {"x": -2.10248947, "y": 0.8274041, "z": 1.75698686}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_185a3fef", "position": {"x": -1.04324019, "y": 0.120159544, "z": 3.64031482}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Candle_fe7b7393", "position": {"x": -3.4519043, "y": 0.8353581, "z": 1.54506254}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_3ac7a1fe", "position": {"x": -0.923299968, "y": 1.59, "z": 2.018}, "rotation": {"x": 0.0, "y": 89.99992, "z": 0.0}}, {"objectName": "Plunger_2aa32165", "position": {"x": -1.06452632, "y": -0.0006047785, "z": 4.838613}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBar_20ca2d0e", "position": {"x": -1.07926679, "y": 1.04416239, "z": 4.46044254}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "ToiletPaper_9fe07971", "position": {"x": -2.46207929, "y": 0.823, "z": 1.65102458}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_b248458c", "position": {"x": -4.30962324, "y": 0.2093386, "z": 2.0745995}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ToiletPaper_5eb36cc2", "position": {"x": -2.372182, "y": 0.823, "z": 1.75698686}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Towel_a7a469d6", "position": {"x": -4.7391, "y": 1.52200007, "z": 3.359}, "rotation": {"x": 0.0, "y": 90.00005, "z": 0.0}}, {"objectName": "ScrubBrush_57fbbbc9", "position": {"x": -1.06544423, "y": -0.0006047785, "z": 4.05289841}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_e9cac663", "position": {"x": -2.19238687, "y": 0.8283692, "z": 1.43910027}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_185a3fef", "position": {"x": -3.432941, "y": 0.8274041, "z": 1.6510247}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1024204341, "scene_num": 410}, "task_id": "trial_T20190908_111427_862927", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "AISNLDPD2DFEG_354P56DE9NUBNCY6EEZX9YXXBYB7SH", "high_descs": ["Turn right, go forward, turn right and go to the counter", "Pick up the empty toilet paper on the counter", "Turn around, go forward a bit, turn around", "Open the drawer under the counter, put the empty toilet paper in the drawer, close the drawer", "Turn right, go forward a bit, turn left, go to the counter", "Pick up the toilet paper on the counter", "Turn around, go forward a bit, turn around", "Open the drawer under the counter, put the toilet paper in the drawer, close the drawer"], "task_desc": "Put toilet papers into drawer", "votes": [1, 1]}, {"assignment_id": "AHBWX3WYMAB0E_3I2PTA7R3WLN5BQD54Z1I3NIZVFQK8", "high_descs": ["Turn around and move towards the bathroom vanity.", "Pick up the empty toilet paper roll from the counter.", "Move a few inches to the left towards the middle of the vanity.", "Put the empty roll in the top middle drawer in the front left corner.", "Move a few inches towards the right sink.", "Pick up the full roll of toilet paper.", "Move left towards the center of the vanity.", "Put the full roll of toilet paper in the top middle drawer behind the empty roll."], "task_desc": "Put a full and an empty roll of toilet paper in the upper middle drawer of the bathroom vanity.", "votes": [1, 1]}, {"assignment_id": "A3HL2LL0LEPZT8_3P4RDNWND8XFDTWCABUG8MWJTT7JI2", "high_descs": ["Turn right, go forward, turn left at the sinks, go forward to the counter between the two sinks.", "Take the cardboard roll from the counter.", "Turn around, go forward a step, turn around to face the counter.", "Put the cardboard roll in the drawer below the counter on the left outside corner.", "Turn right, go forward a step, turn left, go forward to the toilet paper on the counter.", "Take the toilet paper from the counter.", "Go back to facing the drawer below the counter with the cardboard roll in it.", "Put the toilet paper in the drawer below the counter behind the cardboard roll."], "task_desc": "Put toilet papers rolls in a drawer.", "votes": [1, 1]}]}}