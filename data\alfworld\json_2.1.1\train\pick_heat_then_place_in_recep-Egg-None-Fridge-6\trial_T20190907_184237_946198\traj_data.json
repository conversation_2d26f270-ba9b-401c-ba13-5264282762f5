{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000326.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000327.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000328.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000329.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000330.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000331.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000332.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000333.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000334.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000335.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000336.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000337.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000338.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000339.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000340.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000341.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000342.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000343.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000344.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000345.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000346.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000347.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000348.png", "low_idx": 62}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-7|-2|3|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-9.75827028, -9.75827028, -2.0310724, -2.0310724, 5.32416536, 5.32416536]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-9.90399932, -9.90399932, -3.132, -3.132, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|-02.44|+01.33|-00.51"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|-3|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-9.75827028, -9.75827028, -2.0310724, -2.0310724, 5.32416536, 5.32416536]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-9.90399932, -9.90399932, -3.132, -3.132, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|-02.44|+01.33|-00.51", "receptacleObjectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 17999], [18000, 299], [18300, 299], [18600, 299], [18900, 298], [19200, 298], [19500, 298], [19800, 298], [20100, 297], [20400, 297], [20700, 297], [21000, 296], [21300, 296], [21600, 296], [21900, 296], [22200, 295], [22500, 295], [22800, 295], [23100, 294], [23400, 294], [23700, 294], [24000, 294], [24300, 293], [24600, 293], [24900, 293], [25200, 293], [25500, 292], [25800, 292], [26100, 292], [26400, 291], [26700, 291], [27000, 291], [27300, 291], [27600, 290], [27900, 290], [28200, 290], [28500, 290], [28800, 289], [29100, 289], [29400, 289], [29700, 288], [30000, 288], [30300, 288], [30600, 288], [30900, 287], [31200, 287], [31500, 287], [31800, 286], [32100, 286], [32400, 286], [32700, 286], [33000, 285], [33300, 285], [33600, 285], [33900, 285], [34200, 284], [34500, 284], [34800, 284], [35100, 283], [35400, 283], [35700, 283], [36000, 283], [36300, 282], [36600, 282], [36900, 282], [37200, 281], [37500, 281], [37800, 281], [38100, 281], [38400, 280], [38700, 280], [39000, 280], [39300, 280], [39600, 279], [39900, 279], [40200, 279], [40500, 278], [40800, 278], [41100, 278], [41400, 278], [41700, 277], [42000, 277], [42300, 277], [42600, 277], [42900, 276], [43200, 276], [43500, 276], [43800, 275], [44100, 275], [44400, 275], [44700, 275], [45000, 274], [45300, 274], [45600, 274], [45900, 273], [46200, 273], [46500, 273], [46800, 273], [47100, 272], [47400, 272], [47700, 272], [48000, 272], [48300, 271], [48600, 271], [48900, 271], [49200, 270], [49500, 270], [49800, 270], [50100, 270], [50400, 269], [50700, 269], [51000, 269], [51300, 268], [51600, 268], [51900, 268], [52200, 268], [52500, 267], [52800, 267], [53100, 267], [53400, 267], [53700, 266], [54000, 266], [54300, 266], [54600, 265], [54900, 265], [55200, 265], [55500, 265], [55800, 264], [56100, 264], [56400, 264], [56700, 264], [57000, 263], [57300, 263], [57600, 263], [57900, 262], [58200, 262], [58500, 262], [58800, 262], [59100, 261], [59400, 261], [59700, 261], [60000, 260], [60300, 260], [60600, 260], [60900, 260], [61200, 259], [61500, 259], [61800, 259], [62100, 259], [62400, 258], [62700, 258], [63000, 258], [63300, 257], [63600, 257], [63900, 257], [64200, 257], [64500, 256], [64800, 256], [65100, 256], [65400, 256], [65700, 255], [66000, 255], [66300, 255], [66600, 254], [66900, 254], [67200, 254], [67500, 254], [67800, 253], [68100, 253], [68400, 253], [68700, 252], [69000, 252], [69300, 252], [69600, 252], [69900, 251], [70200, 251], [70500, 251], [70800, 251], [71100, 250], [71400, 250], [71700, 250], [72000, 249], [72300, 249], [72600, 249], [72900, 249], [73200, 248], [73500, 248], [73800, 248], [74100, 247], [74400, 247], [74700, 247], [75000, 247], [75300, 246], [75600, 246], [75900, 246], [76200, 246], [76500, 245], [76800, 245], [77100, 245], [77400, 244], [77700, 244], [78000, 244], [78300, 244], [78600, 243], [78900, 243], [79200, 243], [79500, 243], [79800, 242], [80100, 242], [80400, 242], [80700, 242], [81000, 242], [81300, 242], [81600, 242], [81900, 243], [82200, 243], [82500, 243], [82800, 243], [83100, 243], [83400, 244], [83700, 243], [84000, 243], [84300, 243], [84600, 243], [84900, 242], [85200, 242], [85500, 242], [85800, 241], [86100, 241], [86400, 241], [86700, 240], [87000, 240], [87300, 240], [87600, 239], [87900, 239], [88200, 239], [88500, 238], [88800, 238], [89100, 238], [89400, 237], [89700, 237]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-02.44|+01.33|-00.51"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [137, 90, 156, 114], "mask": [[26845, 4], [27143, 8], [27442, 10], [27741, 12], [28040, 14], [28340, 15], [28639, 16], [28939, 17], [29238, 18], [29538, 18], [29838, 19], [30138, 19], [30438, 19], [30737, 20], [31037, 20], [31338, 19], [31638, 19], [31938, 18], [32238, 18], [32539, 17], [32839, 16], [33140, 14], [33441, 12], [33742, 10], [34044, 7]], "point": [146, 101]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 285, 300], "mask": [[0, 285], [300, 285], [600, 285], [900, 286], [1200, 286], [1500, 286], [1800, 285], [2100, 285], [2400, 285], [2700, 285], [3000, 284], [3300, 284], [3600, 284], [3900, 284], [4200, 284], [4500, 283], [4800, 283], [5100, 283], [5400, 283], [5700, 282], [6000, 282], [6300, 282], [6600, 282], [6900, 282], [7200, 281], [7500, 281], [7800, 281], [8100, 281], [8400, 280], [8700, 280], [9000, 280], [9300, 280], [9600, 279], [9900, 279], [10200, 279], [10500, 279], [10800, 279], [11100, 278], [11400, 278], [11700, 278], [12000, 278], [12300, 277], [12600, 277], [12900, 277], [13200, 277], [13500, 276], [13800, 276], [14100, 276], [14400, 276], [14700, 276], [15000, 275], [15300, 275], [15600, 275], [15900, 275], [16200, 274], [16500, 274], [16800, 274], [17100, 274], [17400, 274], [17700, 273], [18000, 273], [18300, 273], [18600, 273], [18900, 272], [19200, 37], [19238, 234], [19500, 37], [19555, 217], [19800, 33], [19857, 215], [20100, 30], [20159, 212], [20400, 28], [20460, 211], [20700, 26], [20761, 210], [21000, 24], [21062, 209], [21300, 23], [21362, 209], [21600, 23], [21663, 207], [21900, 22], [21964, 206], [22200, 22], [22264, 206], [22500, 21], [22565, 205], [22800, 21], [22865, 204], [23100, 20], [23166, 203], [23400, 20], [23466, 203], [23700, 20], [23766, 203], [24000, 20], [24066, 203], [24300, 20], [24366, 202], [24600, 20], [24666, 202], [24900, 20], [24966, 202], [25200, 20], [25266, 202], [25500, 20], [25566, 201], [25800, 20], [25867, 200], [26100, 20], [26167, 200], [26400, 21], [26467, 200], [26700, 22], [26767, 199], [27000, 22], [27067, 199], [27300, 23], [27366, 200], [27600, 24], [27666, 200], [27900, 24], [27966, 200], [28200, 25], [28266, 199], [28500, 25], [28566, 199], [28800, 26], [28866, 199], [29100, 27], [29166, 199], [29400, 27], [29466, 198], [29700, 28], [29766, 198], [30000, 29], [30065, 199], [30300, 30], [30365, 199], [30600, 31], [30665, 198], [30900, 32], [30964, 199], [31200, 33], [31263, 200], [31500, 35], [31563, 200], [31800, 36], [31862, 201], [32100, 37], [32162, 200], [32400, 38], [32460, 202], [32700, 41], [32758, 204], [33000, 43], [33056, 206], [33300, 46], [33351, 210], [33600, 261], [33900, 261], [34200, 261], [34500, 261], [34800, 260], [35100, 260], [35400, 260], [35700, 260], [36000, 259], [36300, 259], [36600, 259], [36900, 259], [37200, 258], [37500, 258], [37800, 258], [38100, 258], [38400, 258], [38700, 257], [39000, 257], [39300, 257], [39600, 257], [39900, 256], [40200, 256], [40500, 256], [40800, 256], [41100, 255], [41400, 255], [41700, 255], [42000, 255], [42300, 255], [42600, 254], [42900, 254], [43200, 254], [43500, 254], [43800, 253], [44100, 253], [44400, 253], [44700, 253], [45000, 253], [45300, 252], [45600, 252], [45900, 252], [46200, 252], [46500, 251], [46800, 251], [47100, 251], [47400, 251], [47700, 250], [48000, 250], [48300, 250], [48600, 250], [48900, 250], [49200, 249], [49500, 249], [49800, 249], [50100, 249], [50400, 248], [50700, 248], [51000, 248], [51300, 248], [51600, 248], [51900, 247], [52200, 247], [52500, 247], [52800, 247], [53100, 246], [53400, 246], [53700, 246], [54000, 246], [54300, 245], [54600, 245], [54900, 245], [55200, 245], [55500, 245], [55800, 244], [56100, 244], [56400, 244], [56700, 244], [57000, 243], [57300, 243], [57600, 243], [57900, 243], [58200, 242], [58500, 242], [58800, 242], [59100, 242], [59400, 242], [59700, 241], [60000, 241], [60300, 241], [60600, 241], [60900, 240], [61200, 240], [61500, 240], [61800, 240], [62100, 240], [62400, 239], [62700, 239], [63000, 239], [63300, 239], [63600, 238], [63900, 238], [64200, 238], [64500, 238], [64800, 237], [65100, 237], [65400, 237], [65700, 237], [66000, 237], [66300, 236], [66600, 236], [66900, 236], [67200, 236], [67500, 235], [67800, 235], [68100, 235], [68400, 235], [68700, 235], [69000, 234], [69300, 234], [69600, 234], [69900, 234], [70200, 233], [70500, 233], [70800, 233], [71100, 233], [71400, 145], [71555, 77], [71700, 143], [71857, 75], [72000, 141], [72159, 73], [72300, 140], [72460, 72], [72600, 139], [72761, 71], [72900, 138], [73062, 69], [73200, 137], [73363, 68], [73500, 137], [73664, 67], [73800, 136], [73964, 67], [74100, 135], [74265, 65], [74400, 134], [74566, 64], [74700, 134], [74866, 64], [75000, 133], [75167, 63], [75300, 133], [75467, 62], [75600, 133], [75767, 62], [75900, 132], [76068, 61], [76200, 132], [76368, 61], [76500, 132], [76668, 61], [76800, 132], [76968, 60], [77100, 132], [77268, 60], [77400, 132], [77568, 60], [77700, 132], [77868, 60], [78000, 132], [78168, 59], [78300, 132], [78468, 59], [78600, 132], [78768, 59], [78900, 132], [79068, 59], [79200, 132], [79368, 59], [79500, 132], [79668, 58], [79800, 132], [79968, 58], [80100, 132], [80268, 58], [80400, 133], [80567, 59], [80700, 133], [80867, 58], [81000, 133], [81167, 58], [81300, 134], [81466, 59], [81600, 134], [81766, 59], [81900, 135], [82065, 59], [82200, 136], [82364, 60], [82500, 136], [82664, 60], [82800, 137], [82963, 61], [83100, 31], [83136, 102], [83262, 62], [83400, 139], [83561, 62], [83700, 141], [83859, 64], [84000, 143], [84157, 66], [84300, 145], [84455, 68], [84600, 222], [84900, 222], [85200, 222], [85500, 222], [85800, 222], [86100, 221], [86400, 221], [86700, 221], [87000, 221], [87300, 220], [87600, 220], [87900, 220], [88200, 220], [88500, 219], [88800, 219], [89100, 219], [89400, 219], [89700, 219]], "point": [142, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-02.44|+01.33|-00.51", "placeStationary": true, "receptacleObjectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 235], [23400, 234], [23700, 234], [24000, 234], [24300, 234], [24600, 233], [24900, 233], [25200, 233], [25500, 233], [25800, 232], [26100, 232], [26400, 232], [26700, 232], [27000, 231], [27300, 231], [27600, 231], [27900, 231], [28200, 230], [28500, 230], [28800, 230], [29100, 230], [29400, 229], [29700, 229], [30000, 229], [30300, 229], [30600, 228], [30900, 228], [31200, 228], [31500, 228], [31800, 227], [32100, 227], [32400, 227], [32700, 227], [33000, 226], [33300, 226], [33600, 226], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 235], [23400, 234], [23700, 234], [24000, 234], [24300, 234], [24600, 233], [24900, 233], [25200, 233], [25500, 233], [25800, 232], [26100, 232], [26400, 232], [26700, 232], [27000, 231], [27300, 231], [27600, 231], [27900, 231], [28200, 230], [28500, 230], [28800, 230], [29100, 230], [29400, 229], [29700, 157], [29861, 68], [30000, 156], [30163, 66], [30300, 154], [30464, 65], [30600, 154], [30765, 63], [30900, 153], [31065, 63], [31200, 153], [31366, 62], [31500, 152], [31666, 62], [31800, 152], [31967, 60], [32100, 152], [32267, 60], [32400, 152], [32567, 60], [32700, 152], [32867, 60], [33000, 152], [33167, 59], [33300, 152], [33466, 60], [33600, 152], [33766, 60], [33900, 153], [34066, 59], [34200, 153], [34365, 60], [34500, 154], [34664, 61], [34800, 156], [34963, 62], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-02.44|+01.33|-00.51"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [152, 100, 166, 117], "mask": [[29857, 4], [30156, 7], [30454, 10], [30754, 11], [31053, 12], [31353, 13], [31652, 14], [31952, 15], [32252, 15], [32552, 15], [32852, 15], [33152, 15], [33452, 14], [33752, 14], [34053, 13], [34353, 12], [34654, 10], [34956, 7]], "point": [159, 107]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 235], [23400, 234], [23700, 234], [24000, 234], [24300, 234], [24600, 233], [24900, 233], [25200, 233], [25500, 233], [25800, 232], [26100, 232], [26400, 232], [26700, 232], [27000, 231], [27300, 231], [27600, 231], [27900, 231], [28200, 230], [28500, 230], [28800, 230], [29100, 230], [29400, 229], [29700, 229], [30000, 229], [30300, 229], [30600, 228], [30900, 228], [31200, 228], [31500, 228], [31800, 227], [32100, 227], [32400, 227], [32700, 227], [33000, 226], [33300, 226], [33600, 226], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 243], "mask": [[0, 48599], [48600, 298], [48900, 299], [49200, 299], [49500, 299], [49800, 299], [50100, 299], [50400, 299], [50700, 298], [51000, 297], [51300, 296], [51600, 295], [51901, 293], [52202, 290], [52503, 288], [52805, 285], [53106, 282], [53407, 280], [53708, 278], [54009, 275], [54311, 272], [54612, 270], [54913, 267], [55214, 265], [55515, 262], [55816, 260], [56118, 257], [56419, 254], [56720, 252], [57021, 249], [57322, 247], [57624, 244], [57925, 241], [58226, 239], [58527, 236], [58828, 234], [59129, 232], [59431, 229], [59732, 226], [60033, 224], [60334, 222], [60635, 220], [60936, 218], [61238, 215], [61539, 213], [61840, 211], [62141, 209], [62442, 207], [62744, 204], [63045, 202], [63346, 200], [63647, 197], [63948, 195], [64249, 193], [64551, 190], [64852, 188], [65153, 186], [65454, 184], [65755, 182], [66056, 180], [66358, 177], [66658, 176], [66958, 175], [67258, 174], [67557, 175], [67857, 175], [68157, 174], [68457, 174], [68758, 172], [69059, 170], [69360, 168], [69662, 165], [69966, 157], [70269, 150], [70573, 143], [70876, 136], [71180, 128], [71486, 116], [71792, 56], [71852, 44], [72100, 44], [72156, 33], [72408, 34], [72458, 22], [72718, 22], [72760, 10]], "point": [149, 121]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-02.44|+01.33|-00.51", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 38999], [39000, 298], [39300, 297], [39600, 100], [39703, 193], [39900, 295], [40200, 295], [40500, 294], [40800, 293], [41100, 292], [41400, 291], [41700, 290], [42000, 289], [42300, 289], [42600, 288], [42900, 287], [43200, 286], [43500, 285], [43800, 284], [44100, 283], [44400, 282], [44700, 282], [45000, 281], [45300, 280], [45600, 279], [45900, 278], [46200, 277], [46500, 276], [46800, 275], [47100, 275], [47400, 274], [47700, 273], [48000, 272], [48300, 271], [48600, 270], [48900, 269], [49200, 268], [49500, 268], [49800, 267], [50100, 266], [50400, 265], [50700, 264], [51000, 263], [51300, 262], [51600, 261], [51900, 261], [52200, 260], [52500, 259], [52800, 258], [53100, 257], [53400, 256], [53700, 255], [54000, 254], [54300, 254], [54600, 253], [54900, 252], [55200, 251], [55500, 250], [55800, 249], [56100, 248], [56400, 247], [56700, 247], [57000, 246], [57300, 245], [57600, 244], [57900, 243], [58200, 242], [58500, 241], [58800, 240], [59100, 240], [59400, 239], [59700, 238], [60000, 237], [60300, 236], [60600, 235], [60900, 234], [61200, 233], [61500, 233], [61800, 232], [62100, 231], [62400, 230], [62700, 229], [63000, 229], [63300, 229], [63600, 229], [63900, 229], [64200, 229], [64500, 229], [64800, 229], [65100, 230], [65400, 230], [65700, 230], [66000, 230], [66300, 230], [66600, 230], [66900, 231], [67200, 231], [67500, 231], [67800, 231], [68100, 231], [68400, 231], [68700, 230], [69000, 58], [69059, 170], [69300, 57], [69360, 168], [69600, 57], [69662, 165], [69900, 57], [69966, 157], [70200, 57], [70269, 150], [70500, 57], [70573, 143], [70800, 56], [70876, 136], [71100, 56], [71180, 128], [71400, 56], [71486, 116], [71700, 56], [71792, 56], [71852, 44], [72000, 56], [72100, 44], [72156, 33], [72300, 56], [72408, 34], [72458, 22], [72600, 55], [72718, 22], [72760, 10], [72900, 55], [73200, 55], [73500, 55], [73800, 55], [74100, 54], [74400, 54], [74700, 54], [75000, 54], [75300, 54], [75600, 54], [75900, 53], [76200, 53], [76500, 53], [76800, 53], [77100, 53], [77400, 52], [77700, 52], [78000, 52], [78300, 52], [78600, 52], [78900, 51], [79200, 51], [79500, 51], [79800, 51], [80100, 51], [80400, 51], [80700, 50], [81000, 50], [81300, 50], [81600, 50], [81900, 50], [82200, 49], [82500, 49], [82800, 49], [83100, 49], [83400, 49], [83700, 49], [84000, 48], [84300, 48], [84600, 48], [84900, 48], [85200, 48], [85500, 47], [85800, 47], [86100, 47], [86400, 47], [86700, 47], [87000, 47], [87300, 46], [87600, 46], [87900, 46], [88200, 46], [88500, 46], [88800, 45], [89100, 45], [89400, 45], [89700, 45]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 8533], [8536, 294], [8838, 291], [9140, 288], [9441, 286], [9741, 286], [10042, 284], [10343, 283], [10643, 282], [10944, 281], [11244, 281], [11544, 281], [11845, 280], [12145, 280], [12445, 280], [12745, 280], [13045, 280], [13345, 280], [13645, 280], [13944, 282], [14244, 282], [14544, 283], [14843, 285], [15142, 286], [15441, 289], [15740, 291], [16039, 22960], [39000, 298], [39300, 297], [39600, 100], [39703, 193], [39900, 295], [40200, 295], [40500, 294], [40800, 293], [41100, 292], [41400, 291], [41700, 290], [42000, 289], [42300, 289], [42600, 288], [42900, 287], [43200, 286], [43500, 285], [43800, 284], [44100, 283], [44400, 282], [44700, 282], [45000, 281], [45300, 280], [45600, 279], [45900, 278], [46200, 277], [46500, 276], [46800, 275], [47100, 275], [47400, 274], [47700, 273], [48000, 272], [48300, 271], [48600, 270], [48900, 269], [49200, 268], [49500, 268], [49800, 267], [50100, 266], [50400, 265], [50700, 264], [51000, 263], [51300, 262], [51600, 261], [51900, 261], [52200, 260], [52500, 259], [52800, 258], [53100, 257], [53400, 256], [53700, 255], [54000, 254], [54300, 254], [54600, 253], [54900, 252], [55200, 251], [55500, 250], [55800, 249], [56100, 248], [56400, 247], [56700, 247], [57000, 246], [57300, 245], [57600, 244], [57900, 243], [58200, 242], [58500, 241], [58800, 240], [59100, 240], [59400, 239], [59700, 238], [60000, 237], [60300, 236], [60600, 235], [60900, 234], [61200, 233], [61500, 233], [61800, 232], [62100, 231], [62400, 230], [62700, 229], [63000, 229], [63300, 229], [63600, 229], [63900, 229], [64200, 229], [64500, 229], [64800, 229], [65100, 230], [65400, 230], [65700, 230], [66000, 230], [66300, 230], [66600, 230], [66900, 231], [67200, 231], [67500, 231], [67800, 231], [68100, 231], [68400, 231], [68700, 230], [69000, 58], [69059, 170], [69300, 57], [69360, 168], [69600, 57], [69662, 165], [69900, 57], [69966, 157], [70200, 57], [70269, 150], [70500, 57], [70573, 143], [70800, 56], [70876, 136], [71100, 56], [71180, 128], [71400, 56], [71486, 116], [71700, 56], [71792, 104], [72000, 56], [72100, 89], [72300, 56], [72408, 72], [72600, 55], [72718, 52], [72900, 55], [73200, 55], [73500, 55], [73800, 55], [74100, 54], [74400, 54], [74700, 54], [75000, 54], [75300, 54], [75600, 54], [75900, 53], [76200, 53], [76500, 53], [76800, 53], [77100, 53], [77400, 52], [77700, 52], [78000, 52], [78300, 52], [78600, 52], [78900, 51], [79200, 51], [79500, 51], [79800, 51], [80100, 51], [80400, 51], [80700, 50], [81000, 50], [81300, 50], [81600, 50], [81900, 50], [82200, 49], [82500, 49], [82800, 49], [83100, 49], [83400, 49], [83700, 49], [84000, 48], [84300, 48], [84600, 48], [84900, 48], [85200, 48], [85500, 47], [85800, 47], [86100, 47], [86400, 47], [86700, 47], [87000, 47], [87300, 46], [87600, 46], [87900, 46], [88200, 46], [88500, 46], [88800, 45], [89100, 45], [89400, 45], [89700, 45]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan6", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.5, "y": 0.9009992, "z": 0.5}, "object_poses": [{"objectName": "Potato_b563bb4b", "position": {"x": 0.8441905, "y": 0.94723314, "z": -1.90684259}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_0884e440", "position": {"x": 1.6916, "y": 0.9295, "z": 0.0642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": -0.471179664, "y": 0.911448538, "z": 1.094}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": 1.76969028, "y": 0.9122294, "z": 0.4652969}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": -0.7309452, "y": 0.9120294, "z": 0.870661139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": 1.113426, "y": 0.7945592, "z": -1.30836427}, "rotation": {"x": 1.40334208e-14, "y": 225.000031, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": -2.43956757, "y": 1.33104134, "z": -0.5077681}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": 1.36464584, "y": 0.9450056, "z": -1.0882268}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": -2.379117, "y": 0.799445331, "z": 0.8572111}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.62175035, "y": 0.9080944, "z": 1.53077412}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.3105998, "y": 0.111672342, "z": -0.07298669}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": 1.7355001, "y": 1.6587497, "z": -0.6949473}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": -2.560535, "y": 1.53300011, "z": 0.8464532}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": 1.85536671, "y": 0.906100035, "z": 0.435648441}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -2.37255049, "y": 0.994053841, "z": 1.53077412}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_209e2ea9", "position": {"x": -2.37255049, "y": 0.996719658, "z": 0.6312814}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_db7f1139", "position": {"x": 1.42698455, "y": 0.906100035, "z": 0.4652969}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": -0.211414054, "y": 0.9725113, "z": 1.094}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": 1.68793631, "y": 0.246842712, "z": 0.6514689}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_adb25946", "position": {"x": 0.0, "y": 0.9, "z": 0.948}, "rotation": {"x": 0.0, "y": 29.9999466, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": -2.34871125, "y": 0.7987526, "z": 0.486988872}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": -2.485795, "y": 1.36202419, "z": -0.7909757}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.466795, "y": 0.5919727, "z": -0.9623109}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": -0.6010624, "y": 0.911448538, "z": 0.870661139}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bread_209e2ea9", "position": {"x": -2.20641732, "y": 0.996719658, "z": 1.830605}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_7b0d90af", "position": {"x": -2.37255049, "y": 0.913369656, "z": 1.2309432}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": 1.22146761, "y": 0.949258566, "z": -1.16139281}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "Kettle_0884e440", "position": {"x": 1.4123, "y": 0.9295, "z": -0.3359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_b563bb4b", "position": {"x": 1.4235425, "y": 0.9549633, "z": -1.30938172}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": -2.524844, "y": 1.53261185, "z": 2.62279987}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -0.471179664, "y": 0.994053841, "z": 0.870661139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": -2.704817, "y": 0.9087127, "z": 0.6312814}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": 0.588964939, "y": 1.66571486, "z": -1.802901}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": 0.0483515263, "y": 0.925899863, "z": 0.6473222}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_40552007", "position": {"x": 1.68401384, "y": 1.01538539, "z": 0.406000018}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": 1.29147983, "y": 0.9209855, "z": -1.23140574}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": 1.85536671, "y": 0.9061, "z": 0.3467031}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": 1.59924793, "y": 0.910464168, "z": -0.642795563}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_bc458cba", "position": {"x": -2.3114, "y": 0.105, "z": 0.895}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": -2.31830549, "y": 0.7745324, "z": 0.8093223}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_9b863229", "position": {"x": -2.62175035, "y": 0.9532293, "z": 1.2309432}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_db7f1139", "position": {"x": 0.0483515263, "y": 0.9059, "z": 0.423983335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3794400062, "scene_num": 6}, "task_id": "trial_T20190907_184237_946198", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1WJU1IQ3UTRC6_3N1FSUEFL8H0HBB7JIHKA17LP23D4M", "high_descs": ["Turn left and go to the fridge.", "Open the fridge, take out an egg and close the fridge.", "Turn right and go to the microwave on the counter.", "Open the microwave and heat the egg, then take the egg out of the microwave.", "Turn left and head to the fridge.", "Open the fridge and put the egg back in."], "task_desc": "Heat up an egg from the fridge and put it back in the fridge.", "votes": [1, 1, 1, 1]}, {"assignment_id": "AD0NVUGLDYDYN_3URFVVM168Z21AAIQ6XZEXOICXOUZC", "high_descs": ["turn around, walk to the refrigerator", "open the refrigerator, take the egg out of it, close the refrigerator", "turn right,  walk to the microwave in the corner", "open the microwave, heat the egg with microwave, take the egg out", "turn left, walk to the refrigerator on the right", "open the refrigerator, put the egg in, close the refrigerator"], "task_desc": "cook the egg from refrigerator, put it back in the refrigerator", "votes": [1, 1, 1, 1]}, {"assignment_id": "A1ZE52NWZPN85P_3TYCR1GOTF0ZIKNOHS5YNASZZCXZL1", "high_descs": ["Turn around and then turn to your right to face the fridge", "Open the freezer and pick up an egg from in it", "Turn to your right and walk to the microwave and turn to face it on your left", "Put the egg in the microwave and cook it for a few seconds, then take it back out", "Go left and then turn to your right to face the fridge again", "Open the door and place the egg back in the freezer"], "task_desc": "Take an egg from the freezer, cook it, and put it back", "votes": [1, 1]}]}}