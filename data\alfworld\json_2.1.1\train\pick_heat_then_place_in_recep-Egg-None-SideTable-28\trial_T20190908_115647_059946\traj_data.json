{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000317.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000318.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000319.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000320.png", "low_idx": 52}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-10|-4|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-10.24330236, -10.24330236, -2.192247868, -2.192247868, 3.7427504, 3.7427504]], "coordinateReceptacleObjectId": ["DiningTable", [-12.132, -12.132, -1.764, -1.764, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|-02.56|+00.94|-00.55"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-8|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-15|-5|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "sidetable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-10.24330236, -10.24330236, -2.192247868, -2.192247868, 3.7427504, 3.7427504]], "coordinateReceptacleObjectId": ["SideTable", [-16.284, -16.284, -1.236, -1.236, 2.904, 2.904]], "forceVisible": true, "objectId": "Egg|-02.56|+00.94|-00.55", "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-02.56|+00.94|-00.55"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [119, 116, 140, 140], "mask": [[34627, 5], [34925, 9], [35223, 13], [35522, 15], [35822, 16], [36121, 17], [36420, 19], [36720, 20], [37019, 21], [37319, 21], [37619, 22], [37919, 22], [38219, 22], [38519, 22], [38819, 22], [39119, 22], [39420, 20], [39720, 20], [40020, 20], [40321, 18], [40622, 17], [40922, 16], [41223, 14], [41525, 10], [41827, 6]], "point": [129, 127]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-02.56|+00.94|-00.55", "placeStationary": true, "receptacleObjectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 11811], [11812, 299], [12112, 299], [12412, 289], [12721, 279], [13022, 44], [13070, 5], [13077, 222], [13323, 39], [13371, 1], [13379, 219], [13624, 35], [13679, 219], [13925, 31], [13980, 217], [14225, 28], [14272, 5], [14281, 216], [14526, 24], [14572, 5], [14581, 215], [14826, 21], [14872, 6], [14881, 215], [15127, 18], [15172, 6], [15182, 214], [15427, 16], [15473, 6], [15482, 213], [15727, 14], [15773, 6], [15782, 213], [16027, 12], [16073, 6], [16082, 213], [16328, 10], [16374, 5], [16382, 213], [16628, 9], [16674, 5], [16683, 212], [16928, 10], [16974, 6], [16983, 212], [17228, 10], [17274, 6], [17283, 212], [17528, 10], [17575, 5], [17583, 212], [17828, 11], [17875, 5], [17883, 212], [18128, 11], [18175, 5], [18183, 212], [18428, 11], [18476, 4], [18483, 212], [18728, 12], [18776, 3], [18783, 212], [19028, 12], [19076, 3], [19082, 214], [19328, 12], [19376, 3], [19382, 214], [19627, 14], [19677, 1], [19682, 215], [19927, 14], [19977, 1], [19981, 216], [20227, 14], [20280, 217], [20526, 16], [20580, 218], [20826, 16], [20878, 220], [21126, 16], [21178, 221], [21425, 18], [21478, 222], [21725, 18], [21779, 221], [22024, 19], [22079, 222], [22324, 20], [22379, 222], [22623, 21], [22680, 222], [22923, 21], [22980, 223], [23222, 23], [23280, 224], [23522, 23], [23580, 225], [23821, 24], [23879, 227], [24120, 25], [24176, 241], [24419, 27], [24474, 272], [24771, 275], [25068, 7675], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [140, 65]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 11811], [11812, 299], [12112, 299], [12412, 289], [12721, 279], [13022, 44], [13070, 5], [13077, 222], [13323, 39], [13371, 1], [13379, 219], [13624, 35], [13679, 219], [13928, 28], [13980, 217], [14230, 23], [14272, 5], [14281, 216], [14532, 18], [14572, 5], [14581, 215], [14833, 14], [14872, 6], [14881, 215], [15134, 11], [15172, 6], [15182, 214], [15435, 8], [15473, 6], [15482, 213], [15735, 6], [15773, 6], [15782, 213], [16036, 3], [16073, 6], [16082, 213], [16336, 2], [16374, 5], [16382, 213], [16674, 5], [16683, 212], [16937, 1], [16974, 6], [16983, 212], [17274, 6], [17283, 212], [17575, 5], [17583, 212], [17838, 1], [17875, 5], [17883, 212], [18138, 1], [18175, 5], [18183, 212], [18476, 4], [18483, 212], [18739, 1], [18776, 3], [18783, 212], [19039, 1], [19076, 3], [19082, 214], [19339, 1], [19376, 3], [19382, 214], [19639, 2], [19677, 1], [19682, 215], [19939, 2], [19977, 1], [19981, 216], [20239, 2], [20280, 217], [20539, 3], [20580, 218], [20839, 3], [20878, 220], [21139, 3], [21178, 221], [21438, 5], [21478, 222], [21738, 5], [21779, 221], [22038, 5], [22079, 222], [22337, 7], [22379, 222], [22637, 7], [22680, 222], [22936, 8], [22980, 223], [23235, 10], [23280, 224], [23535, 10], [23580, 225], [23834, 11], [23879, 227], [24120, 2], [24132, 13], [24176, 241], [24419, 5], [24430, 16], [24474, 272], [24771, 275], [25068, 7675], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [140, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-02.56|+00.94|-00.55"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [114, 47, 138, 82], "mask": [[13924, 4], [14222, 8], [14521, 11], [14820, 13], [15119, 15], [15418, 17], [15718, 17], [16017, 19], [16317, 19], [16616, 21], [16916, 21], [17216, 22], [17515, 23], [17815, 23], [18115, 23], [18415, 24], [18714, 25], [19014, 25], [19314, 25], [19614, 25], [19914, 25], [20214, 25], [20515, 24], [20815, 24], [21115, 24], [21415, 23], [21716, 22], [22016, 22], [22317, 20], [22617, 20], [22918, 18], [23219, 16], [23519, 16], [23821, 13], [24122, 10], [24424, 6]], "point": [126, 63]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 11811], [11812, 299], [12112, 299], [12412, 289], [12721, 279], [13022, 44], [13070, 5], [13077, 222], [13323, 39], [13371, 1], [13379, 219], [13624, 35], [13679, 219], [13925, 31], [13980, 217], [14225, 28], [14272, 5], [14281, 216], [14526, 24], [14572, 5], [14581, 215], [14826, 21], [14872, 6], [14881, 215], [15127, 18], [15172, 6], [15182, 214], [15427, 16], [15473, 6], [15482, 213], [15727, 14], [15773, 6], [15782, 213], [16027, 12], [16073, 6], [16082, 213], [16328, 10], [16374, 5], [16382, 213], [16628, 9], [16674, 5], [16683, 212], [16928, 10], [16974, 6], [16983, 212], [17228, 10], [17274, 6], [17283, 212], [17528, 10], [17575, 5], [17583, 212], [17828, 11], [17875, 5], [17883, 212], [18128, 11], [18175, 5], [18183, 212], [18428, 11], [18476, 4], [18483, 212], [18728, 12], [18776, 3], [18783, 212], [19028, 12], [19076, 3], [19082, 214], [19328, 12], [19376, 3], [19382, 214], [19627, 14], [19677, 1], [19682, 215], [19927, 14], [19977, 1], [19981, 216], [20227, 14], [20280, 217], [20526, 16], [20580, 218], [20826, 16], [20878, 220], [21126, 16], [21178, 221], [21425, 18], [21478, 222], [21725, 18], [21779, 221], [22024, 19], [22079, 222], [22324, 20], [22379, 222], [22623, 21], [22680, 222], [22923, 21], [22980, 223], [23222, 23], [23280, 224], [23522, 23], [23580, 225], [23821, 24], [23879, 227], [24120, 25], [24176, 241], [24419, 27], [24474, 272], [24771, 275], [25068, 7675], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [140, 65]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-02.56|+00.94|-00.55", "placeStationary": true, "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [21, 115, 140, 251], "mask": [[34251, 49], [34325, 13], [34551, 49], [34625, 13], [34851, 49], [34925, 13], [35150, 50], [35225, 13], [35450, 50], [35525, 13], [35749, 51], [35826, 12], [36049, 51], [36126, 12], [36349, 26], [36376, 24], [36425, 13], [36648, 23], [36676, 24], [36725, 13], [36948, 22], [36978, 23], [37025, 13], [37247, 19], [37281, 20], [37324, 14], [37547, 18], [37582, 20], [37624, 14], [37846, 18], [37883, 20], [37923, 15], [38146, 17], [38184, 19], [38222, 16], [38446, 16], [38484, 20], [38521, 17], [38745, 17], [38785, 21], [38820, 18], [39045, 16], [39085, 23], [39117, 21], [39344, 17], [39386, 52], [39644, 17], [39686, 52], [39943, 18], [39986, 52], [40243, 18], [40286, 52], [40542, 19], [40586, 52], [40842, 19], [40886, 52], [41142, 19], [41186, 53], [41441, 21], [41486, 53], [41741, 8], [41757, 6], [41785, 54], [42040, 6], [42059, 4], [42085, 38], [42125, 14], [42340, 5], [42360, 4], [42384, 38], [42425, 14], [42639, 5], [42662, 3], [42683, 39], [42726, 13], [42939, 4], [42963, 3], [42981, 40], [43025, 14], [43239, 4], [43263, 5], [43279, 42], [43325, 14], [43538, 4], [43564, 8], [43576, 45], [43625, 14], [43838, 4], [43865, 56], [43925, 14], [44137, 4], [44165, 56], [44225, 14], [44437, 4], [44466, 38], [44514, 7], [44524, 15], [44736, 5], [44766, 30], [44815, 6], [44824, 15], [45036, 5], [45066, 9], [45099, 2], [45115, 6], [45124, 15], [45335, 6], [45366, 7], [45391, 11], [45414, 7], [45424, 15], [45635, 7], [45665, 8], [45685, 18], [45713, 8], [45724, 15], [45935, 7], [45965, 40], [46010, 11], [46023, 16], [46234, 8], [46265, 56], [46323, 16], [46534, 9], [46565, 56], [46623, 16], [46833, 11], [46864, 57], [46923, 16], [47133, 11], [47164, 57], [47222, 17], [47432, 13], [47463, 58], [47522, 17], [47732, 14], [47763, 58], [47822, 17], [48031, 16], [48062, 59], [48122, 18], [48331, 17], [48361, 60], [48422, 18], [48631, 19], [48661, 59], [48722, 18], [48930, 22], [48959, 61], [49023, 17], [49230, 89], [49323, 17], [49529, 90], [49624, 16], [49829, 89], [49924, 16], [50128, 90], [50224, 16], [50428, 89], [50524, 16], [50728, 89], [50818, 1], [50820, 1], [50822, 1], [50824, 16], [51027, 90], [51118, 1], [51120, 1], [51122, 1], [51124, 16], [51327, 90], [51418, 1], [51420, 1], [51422, 1], [51424, 16], [51626, 91], [51718, 1], [51720, 1], [51722, 18], [51926, 91], [52018, 1], [52020, 2], [52023, 17], [52225, 92], [52318, 2], [52321, 1], [52323, 17], [52525, 95], [52621, 19], [52824, 116], [53124, 116], [53424, 116], [53723, 117], [54022, 118], [54322, 118], [54621, 119], [54922, 118], [55223, 118], [55523, 11], [55546, 95], [55824, 11], [55846, 95], [56124, 11], [56145, 96], [56425, 11], [56445, 96], [56725, 11], [56745, 92], [57026, 11], [57044, 93], [57326, 11], [57344, 93], [57627, 11], [57644, 93], [57928, 10], [57943, 94], [58228, 11], [58243, 94], [58529, 10], [58542, 95], [58829, 11], [58842, 95], [59130, 10], [59142, 95], [59430, 107], [59731, 106], [60032, 105], [60332, 105], [60633, 104], [60933, 104], [61234, 103], [61534, 102], [61835, 101], [62136, 9], [62158, 78], [62436, 10], [62457, 79], [62737, 9], [62757, 79], [63037, 10], [63057, 79], [63338, 9], [63356, 80], [63638, 10], [63656, 80], [63939, 10], [63956, 80], [64240, 9], [64256, 80], [64540, 10], [64555, 81], [64841, 9], [64855, 82], [65141, 10], [65155, 82], [65442, 9], [65454, 83], [65742, 10], [65754, 83], [66043, 9], [66054, 83], [66343, 10], [66354, 83], [66644, 93], [66945, 92], [67245, 92], [67546, 91], [67846, 91], [68147, 90], [68447, 90], [68748, 89], [69049, 8], [69131, 7], [69349, 9], [69431, 7], [69650, 8], [69731, 7], [69950, 9], [70032, 6], [70251, 8], [70332, 6], [70551, 9], [70632, 5], [70852, 8], [70932, 4], [71153, 8], [71232, 3], [71453, 8], [71532, 3], [71754, 8], [71832, 2], [72054, 8], [72132, 1], [72355, 8], [72432, 1], [72655, 8], [72956, 8], [73256, 9], [73557, 8], [73858, 8], [74158, 8], [74459, 7], [74759, 7], [75060, 5]], "point": [80, 182]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan28", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -3.25, "y": 0.900998235, "z": -1.25}, "object_poses": [{"objectName": "Pan_d1250561", "position": {"x": -0.3349222, "y": 1.28464341, "z": -0.5296891}, "rotation": {"x": 0.0, "y": 0.000169038554, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -0.6245041, "y": 0.8454427, "z": -3.47318172}, "rotation": {"x": 9.923132e-15, "y": 315.000031, "z": -9.92312e-15}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -0.501232266, "y": 1.503309, "z": -3.675436}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -2.13991928, "y": 0.9423421, "z": -3.75178385}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_c7418f98", "position": {"x": -3.32981253, "y": 0.761066, "z": -3.421749}, "rotation": {"x": 0.0, "y": 109.125885, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -2.96264029, "y": 0.8951693, "z": -0.5480598}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -0.509394348, "y": 0.7530448, "z": -2.84604383}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -1.47139764, "y": 0.9434294, "z": -3.75178385}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -3.94221377, "y": 0.6912032, "z": -0.187332019}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -0.321308225, "y": 1.48941362, "z": -0.413377553}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -3.92534733, "y": 0.774220169, "z": -3.39901733}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -3.52776623, "y": 0.774220169, "z": -2.86154866}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -0.283441752, "y": 1.53947651, "z": -0.8786227}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -0.236659765, "y": 0.9376062, "z": -1.31903028}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -3.83149123, "y": 0.7550884, "z": -3.73737121}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -0.167794466, "y": 1.64071226, "z": -1.88466358}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -0.212670773, "y": 1.50082648, "z": -3.14060116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_c7418f98", "position": {"x": -0.4374, "y": 0.961999953, "z": -1.8457}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -3.8821454, "y": 0.6918449, "z": -0.4306679}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -0.157691017, "y": 0.9420032, "z": -1.5381465}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -4.122419, "y": 0.742062449, "z": -0.308999926}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_d1250561", "position": {"x": -3.36445379, "y": 0.89266485, "z": -0.3765198}, "rotation": {"x": 0.0, "y": 0.000327849062, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -2.560824, "y": 0.9087526, "z": -0.2907554}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -2.984, "y": 0.956136167, "z": -0.311999947}, "rotation": {"x": -8.878277e-07, "y": 50.59269, "z": -9.574079e-06}}, {"objectName": "Spatula_1d69618b", "position": {"x": -0.643234, "y": 0.8284667, "z": -3.570089}, "rotation": {"x": 9.92312057e-15, "y": 45.0000343, "z": 9.923131e-15}}, {"objectName": "Knife_1b546504", "position": {"x": -3.36445284, "y": 0.9202391, "z": -0.204982013}, "rotation": {"x": 0.0, "y": 0.000327849062, "z": 0.0}}, {"objectName": "SaltShaker_6f13b4f9", "position": {"x": -0.126796126, "y": 1.93525827, "z": -2.00375628}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -1.24855721, "y": 0.9386062, "z": -3.75178385}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -0.394597232, "y": 0.9413421, "z": -1.31903028}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -4.182487, "y": 0.7527082, "z": -0.4306679}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -0.473566, "y": 0.940864265, "z": -1.40667677}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -0.397045672, "y": 1.52068627, "z": -0.994933665}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -2.56082559, "y": 0.9356876, "z": -0.548061967}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -4.002282, "y": 0.6916294, "z": -0.369833976}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.755323, "y": 0.851072848, "z": -3.04361916}, "rotation": {"x": 0.0, "y": 334.125916, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -0.201238275, "y": 1.57045233, "z": -2.04372525}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -0.376193166, "y": 1.28490734, "z": -0.297067046}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 3856107046, "scene_num": 28}, "task_id": "trial_T20190908_115647_059946", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2871R3LEPWMMK_3JMSRU9HQLLCOJUKVHI8QV9LWOVEVZ", "high_descs": ["Turn around and walk to the white table ahead.", "Pick up the egg on the table in front of the gray plate.", "Turn around and walk to the stove on the left.", "Put the egg inside the microwave in between the tomato and the mug, heat it for a few seconds, remove it and close the door.", "Turn around and hang a right at the door to reach the small black table.", "Put the heated egg on the right corner of the table to the left of the fork."], "task_desc": "Place a heated egg on a table.", "votes": [1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3PJUZCGDJ97XIB7QFDRC63O2USS98R", "high_descs": ["Turn around and go to the white table behind you.", "Pick up the egg on the white table, next to the plate.", "Turn around, go forward and then turn left to go toward the stove, then look up the microwave.", "Open the microwave, put the egg inside and cook it, then take it out.", "Turn around, go straight to the door, then turn right to go to the black table.", "Put the egg on the black table."], "task_desc": "Put a cooked egg on the black table.", "votes": [1, 1]}, {"assignment_id": "A24RGLS3BRZ60J_3YT88D1N0BPVYAKB7TN55VU7P09K31", "high_descs": ["Turn around and face the white table.", "Pick up the egg from in front of the plate on the white table.", "Carry the egg and turn around, then left to find the stove. ", "Open the microwave above the stove and place the egg inside. Shut the door, then open it again. Take the egg out and shut the door.", "Carry the egg and turn around and then find the small black table to the right.", "Place the egg on the top right corner of the table."], "task_desc": "Heat an egg and place it on the table.", "votes": [1, 1]}]}}