{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 46}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [5.55240536, 5.55240536, 5.59234904, 5.59234904, 3.368084, 3.368084]], "coordinateReceptacleObjectId": ["SinkBasin", [5.84550096, 5.84550096, 5.47880888, 5.47880888, 3.3322824, 3.3322824]], "forceVisible": true, "objectId": "Cup|+01.39|+00.84|+01.40"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|0|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [5.55240536, 5.55240536, 5.59234904, 5.59234904, 3.368084, 3.368084]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-4.004, -4.004, 0.476, 0.476, 0.0, 0.0]], "forceVisible": true, "objectId": "Cup|+01.39|+00.84|+01.40", "receptacleObjectId": "<PERSON><PERSON>|-01.00|+00.00|+00.12"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+01.39|+00.84|+01.40"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [169, 114, 208, 170], "mask": [[34084, 7], [34382, 11], [34680, 15], [34977, 21], [35275, 25], [35573, 29], [35871, 34], [36170, 36], [36470, 37], [36769, 38], [37069, 38], [37369, 38], [37669, 39], [37969, 39], [38269, 39], [38569, 39], [38869, 39], [39170, 38], [39470, 38], [39770, 38], [40070, 39], [40370, 39], [40670, 39], [40970, 39], [41270, 39], [41570, 38], [41870, 38], [42170, 37], [42470, 37], [42770, 36], [43070, 35], [43370, 35], [43670, 34], [43970, 34], [44270, 33], [44571, 31], [44871, 31], [45171, 30], [45471, 29], [45771, 29], [46071, 28], [46371, 28], [46671, 27], [46971, 26], [47271, 26], [47571, 25], [47871, 25], [48171, 24], [48471, 23], [48771, 23], [49072, 21], [49372, 20], [49673, 19], [49975, 15], [50277, 11], [50579, 7], [50881, 3]], "point": [188, 141]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+01.39|+00.84|+01.40", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 75], [127, 134], [300, 75], [427, 134], [600, 75], [727, 134], [900, 75], [1027, 133], [1200, 76], [1327, 133], [1500, 76], [1627, 133], [1800, 76], [1927, 133], [2100, 76], [2227, 133], [2400, 76], [2527, 133], [2700, 76], [2826, 134], [3000, 77], [3126, 134], [3300, 77], [3426, 134], [3600, 77], [3726, 134], [3900, 77], [4026, 133], [4200, 77], [4326, 133], [4500, 77], [4626, 133], [4800, 78], [4926, 133], [5100, 78], [5226, 133], [5400, 78], [5526, 133], [5700, 78], [5826, 133], [6000, 78], [6126, 133], [6300, 79], [6425, 134], [6600, 79], [6725, 134], [6900, 79], [7025, 133], [7200, 79], [7325, 133], [7500, 79], [7625, 133], [7800, 79], [7925, 133], [8100, 80], [8225, 133], [8400, 80], [8525, 133], [8700, 80], [8825, 133], [9000, 80], [9125, 133], [9300, 80], [9425, 133], [9600, 80], [9725, 133], [9900, 81], [10024, 133], [10200, 81], [10324, 133], [10500, 81], [10624, 133], [10800, 81], [10924, 133], [11100, 81], [11224, 133], [11400, 81], [11524, 133], [11700, 82], [11824, 133], [12000, 82], [12124, 133], [12300, 82], [12424, 133], [12600, 82], [12724, 133], [12900, 82], [13024, 132], [13200, 82], [13324, 132], [13500, 83], [13623, 133], [13800, 83], [13923, 133], [14100, 83], [14223, 133], [14400, 83], [14523, 133], [14700, 83], [14823, 133], [15000, 83], [15123, 133], [15300, 84], [15423, 133], [15600, 84], [15723, 133], [15900, 84], [16023, 132], [16200, 84], [16323, 132], [16500, 84], [16623, 132], [16800, 84], [16923, 132], [17100, 85], [17222, 133], [17400, 85], [17522, 133], [17700, 85], [17822, 133], [18000, 85], [18122, 133], [18300, 85], [18422, 133], [18600, 86], [18722, 133], [18900, 86], [19022, 132], [19200, 86], [19322, 132], [19500, 86], [19622, 132], [19800, 86], [19922, 132], [20100, 86], [20222, 132], [20400, 87], [20522, 132], [20700, 87], [20821, 133], [21000, 87], [21121, 133], [21300, 92], [21419, 135], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+01.39|+00.84|+01.40"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [75, 1, 126, 72], "mask": [[75, 52], [375, 52], [675, 52], [975, 52], [1276, 51], [1576, 51], [1876, 51], [2176, 51], [2476, 51], [2776, 50], [3077, 49], [3377, 49], [3677, 49], [3977, 49], [4277, 49], [4577, 49], [4878, 48], [5178, 48], [5478, 48], [5778, 48], [6078, 48], [6379, 46], [6679, 46], [6979, 46], [7279, 46], [7579, 46], [7879, 46], [8180, 45], [8480, 45], [8780, 45], [9080, 45], [9380, 45], [9680, 45], [9981, 43], [10281, 43], [10581, 43], [10881, 43], [11181, 43], [11481, 43], [11782, 42], [12082, 42], [12382, 42], [12682, 42], [12982, 42], [13282, 42], [13583, 40], [13883, 40], [14183, 40], [14483, 40], [14783, 40], [15083, 40], [15384, 39], [15684, 39], [15984, 39], [16284, 39], [16584, 39], [16884, 39], [17185, 37], [17485, 37], [17785, 37], [18085, 37], [18385, 37], [18686, 36], [18986, 36], [19286, 36], [19586, 36], [19886, 36], [20186, 36], [20487, 35], [20787, 34], [21087, 34], [21392, 27]], "point": [100, 35]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-01.00|+00.00|+00.12"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 224], "mask": [[0, 19200], [19201, 299], [19501, 299], [19802, 298], [20102, 298], [20403, 297], [20704, 296], [21004, 296], [21305, 295], [21606, 294], [21906, 294], [22207, 293], [22508, 292], [22808, 292], [23109, 291], [23409, 291], [23710, 290], [24011, 289], [24311, 289], [24612, 288], [24913, 287], [25213, 287], [25514, 286], [25815, 285], [26116, 284], [26416, 284], [26716, 284], [27017, 283], [27318, 282], [27618, 282], [27919, 281], [28219, 281], [28520, 280], [28821, 279], [29121, 279], [29422, 278], [29723, 277], [30023, 277], [30324, 276], [30625, 275], [30925, 275], [31226, 274], [31526, 274], [31827, 273], [32128, 272], [32428, 272], [32729, 271], [33030, 270], [33330, 270], [33631, 269], [33932, 268], [34232, 268], [34533, 267], [34833, 267], [35134, 266], [35435, 265], [35735, 265], [36036, 264], [36337, 263], [36637, 263], [36938, 262], [37239, 261], [37539, 261], [37840, 260], [38140, 260], [38441, 259], [38742, 258], [39042, 258], [39343, 257], [39644, 256], [39944, 256], [40245, 255], [40545, 255], [40846, 254], [41147, 253], [41447, 83], [41570, 130], [41748, 78], [41874, 126], [42049, 74], [42177, 123], [42349, 71], [42480, 120], [42650, 67], [42783, 117], [42951, 63], [43086, 114], [43251, 60], [43389, 111], [43552, 56], [43692, 108], [43852, 55], [43993, 107], [44153, 54], [44293, 107], [44454, 52], [44594, 106], [44754, 52], [44894, 106], [45055, 52], [45193, 107], [45356, 51], [45493, 107], [45656, 51], [45793, 107], [45957, 50], [46093, 107], [46258, 49], [46393, 107], [46558, 49], [46693, 107], [46859, 49], [46992, 108], [47159, 49], [47292, 108], [47460, 48], [47592, 108], [47761, 47], [47892, 108], [48061, 47], [48192, 108], [48362, 46], [48492, 108], [48663, 46], [48791, 109], [48963, 46], [49091, 109], [49264, 45], [49391, 109], [49565, 44], [49691, 109], [49865, 44], [49991, 109], [50166, 43], [50291, 109], [50466, 44], [50590, 110], [50767, 43], [50890, 110], [51068, 42], [51190, 110], [51368, 42], [51490, 110], [51669, 41], [51790, 110], [51970, 40], [52090, 109], [52270, 41], [52389, 109], [52571, 40], [52689, 108], [52872, 39], [52989, 107], [53172, 39], [53289, 105], [53473, 38], [53589, 104], [53773, 38], [53889, 103], [54074, 38], [54188, 103], [54375, 37], [54488, 102], [54675, 37], [54788, 101], [54976, 36], [55088, 99], [55277, 35], [55388, 98], [55577, 35], [55688, 97], [55878, 35], [55987, 97], [56179, 34], [56287, 96], [56479, 34], [56587, 94], [56780, 33], [56887, 93], [57080, 33], [57187, 92], [57381, 32], [57487, 91], [57682, 32], [57786, 91], [57982, 32], [58086, 90], [58283, 31], [58386, 88], [58584, 30], [58686, 87], [58884, 30], [58986, 86], [59185, 29], [59286, 85], [59486, 29], [59585, 85], [59786, 29], [59885, 83], [60087, 28], [60185, 82], [60387, 28], [60485, 81], [60688, 27], [60785, 80], [60989, 26], [61085, 79], [61289, 26], [61385, 78], [61590, 26], [61684, 77], [61891, 25], [61984, 76], [62191, 25], [62284, 75], [62492, 24], [62584, 74], [62793, 23], [62884, 73], [63093, 23], [63184, 71], [63394, 23], [63483, 71], [63694, 23], [63783, 70], [63995, 22], [64083, 69], [64296, 21], [64383, 68], [64596, 21], [64683, 67], [64897, 20], [64983, 65], [65198, 20], [65282, 65], [65498, 20], [65582, 64], [65799, 19], [65882, 63], [66099, 19], [66182, 62], [66400, 18], [66482, 60], [66701, 17], [66782, 59], [67002, 17], [67081, 59]], "point": [149, 111]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+01.39|+00.84|+01.40", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-01.00|+00.00|+00.12"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 2100], [2101, 299], [2401, 299], [2702, 298], [3002, 298], [3303, 297], [3603, 297], [3904, 296], [4205, 295], [4505, 295], [4806, 294], [5106, 294], [5407, 293], [5707, 293], [6008, 292], [6308, 292], [6609, 291], [6909, 291], [7210, 290], [7510, 290], [7811, 289], [8111, 289], [8412, 288], [8712, 288], [9013, 287], [9313, 287], [9614, 286], [9914, 286], [10215, 285], [10515, 285], [10816, 284], [11116, 284], [11417, 283], [11717, 283], [12018, 282], [12318, 282], [12619, 281], [12919, 281], [13220, 280], [13520, 280], [13821, 279], [14121, 279], [14422, 278], [14722, 278], [15023, 277], [15323, 277], [15624, 276], [15925, 275], [16226, 274], [16526, 274], [16826, 274], [17126, 274], [17427, 273], [17727, 273], [18028, 272], [18328, 272], [18629, 271], [18929, 271], [19230, 270], [19531, 269], [19831, 269], [20132, 268], [20432, 268], [20733, 267], [21033, 267], [21334, 266], [21634, 266], [21935, 265], [22235, 265], [22536, 264], [22836, 264], [23137, 263], [23437, 263], [23738, 262], [24038, 262], [24339, 261], [24639, 261], [24940, 260], [25240, 260], [25541, 259], [25841, 259], [26142, 258], [26442, 258], [26743, 257], [27043, 257], [27344, 256], [27644, 256], [27945, 255], [28245, 147], [28409, 91], [28546, 147], [28707, 93], [28846, 149], [29006, 94], [29147, 151], [29303, 97], [29447, 253], [29748, 252], [30048, 252], [30349, 251], [30649, 251], [30950, 250], [31250, 250], [31551, 249], [31851, 249], [32152, 248], [32452, 248], [32753, 247], [33053, 113], [33185, 115], [33354, 112], [33485, 115], [33654, 113], [33784, 116], [33955, 112], [34084, 116], [34255, 112], [34384, 116], [34556, 111], [34683, 117], [34856, 111], [34983, 117], [35157, 110], [35282, 118], [35458, 109], [35582, 118], [35758, 110], [35881, 119], [36059, 110], [36181, 119], [36359, 111], [36480, 41], [36529, 71], [36660, 110], [36779, 41], [36832, 68], [36960, 159], [37133, 67], [37261, 157], [37434, 66], [37561, 157], [37735, 65], [37862, 156], [38035, 65], [38162, 156], [38336, 64], [38463, 155], [38637, 63], [38763, 154], [38937, 63], [39064, 153], [39237, 63], [39364, 153], [39538, 62], [39665, 152], [39838, 62], [39965, 152], [40138, 62], [40266, 151], [40438, 62], [40566, 151], [40738, 62], [40867, 151], [41039, 61], [41167, 151], [41339, 61], [41468, 62], [41570, 49], [41639, 61], [41768, 58], [41874, 45], [41939, 61], [42069, 54], [42177, 43], [42239, 61], [42369, 51], [42480, 40], [42539, 61], [42670, 47], [42783, 38], [42839, 61], [42970, 44], [43086, 36], [43138, 62], [43271, 40], [43389, 34], [43436, 64], [43571, 37], [43692, 33], [43735, 65], [43872, 35], [43993, 107], [44172, 35], [44293, 107], [44473, 33], [44594, 106], [44773, 33], [44894, 106], [45074, 33], [45193, 107], [45374, 33], [45493, 107], [45675, 32], [45793, 107], [45975, 32], [46093, 107], [46276, 31], [46393, 107], [46576, 31], [46693, 107], [46877, 31], [46992, 108], [47177, 31], [47292, 108], [47478, 30], [47592, 108], [47778, 30], [47892, 108], [48079, 29], [48192, 108], [48379, 29], [48492, 108], [48680, 29], [48791, 109], [48980, 29], [49091, 109], [49281, 28], [49391, 109], [49581, 28], [49691, 109], [49882, 27], [49991, 109], [50182, 27], [50291, 109], [50483, 27], [50590, 110], [50783, 27], [50890, 110], [51084, 26], [51190, 110], [51385, 25], [51490, 110], [51685, 25], [51790, 110], [51986, 24], [52090, 110], [52286, 25], [52389, 111], [52587, 24], [52689, 111], [52887, 24], [52989, 111], [53188, 23], [53289, 111], [53488, 23], [53589, 111], [53789, 22], [53889, 111], [54089, 23], [54188, 112], [54390, 22], [54488, 112], [54690, 22], [54788, 112], [54991, 21], [55088, 71], [55160, 40], [55291, 21], [55388, 112], [55592, 20], [55688, 112], [55892, 21], [55987, 113], [56193, 20], [56287, 113], [56493, 20], [56587, 113], [56794, 19], [56887, 113], [57094, 19], [57187, 113], [57395, 18], [57487, 113], [57695, 19], [57786, 114], [57996, 18], [58086, 114], [58296, 18], [58386, 114], [58597, 17], [58686, 114], [58897, 17], [58986, 114], [59198, 16], [59286, 114], [59498, 17], [59585, 115], [59799, 16], [59885, 115], [60099, 16], [60185, 115], [60400, 15], [60485, 115], [60700, 15], [60785, 115], [61001, 14], [61085, 115], [61301, 14], [61385, 115], [61602, 14], [61684, 116], [61903, 13], [61984, 53], [62038, 62], [62209, 7], [62304, 26], [62337, 63], [62637, 63], [62938, 62], [63238, 62], [63538, 62], [63838, 62], [64138, 62], [64438, 62], [64739, 61], [65039, 61], [65339, 61], [65639, 61], [65939, 61], [66239, 61], [66540, 60], [66840, 60], [67140, 60], [67440, 60], [67740, 60], [68040, 60], [68341, 59], [68641, 59], [68941, 59], [69241, 59], [69541, 59], [69842, 58], [70142, 58], [70442, 58], [70742, 58], [71042, 58], [71342, 58], [71643, 57], [71943, 57], [72243, 57], [72543, 57], [72843, 57], [73143, 57], [73444, 56], [73744, 56], [74044, 56], [74344, 56], [74644, 56], [74944, 56], [75245, 55], [75545, 55], [75845, 55], [76145, 55], [76445, 55], [76745, 55], [77046, 54], [77346, 54], [77646, 54], [77946, 54], [78246, 54], [78546, 54], [78847, 53], [79147, 53], [79447, 53], [79747, 53], [80047, 53], [80348, 52], [80648, 52], [80948, 52], [81248, 52], [81548, 52], [81848, 52], [82149, 51], [82449, 51], [82749, 51], [83049, 51], [83349, 51], [83649, 51], [83950, 50], [84250, 50], [84550, 50], [84850, 50], [85150, 50], [85450, 50], [85751, 49], [86051, 49], [86351, 49], [86651, 49], [86951, 49], [87251, 49], [87552, 48], [87852, 48], [88152, 48], [88452, 48], [88752, 48], [89053, 47], [89353, 47], [89653, 47], [89953, 47]], "point": [149, 137]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-01.00|+00.00|+00.12"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 2100], [2101, 299], [2401, 299], [2702, 298], [3002, 298], [3303, 297], [3603, 297], [3904, 296], [4205, 295], [4505, 295], [4806, 294], [5106, 294], [5407, 293], [5707, 293], [6008, 292], [6308, 292], [6609, 291], [6909, 291], [7210, 290], [7510, 290], [7811, 289], [8111, 289], [8412, 288], [8712, 288], [9013, 287], [9313, 287], [9614, 286], [9914, 286], [10215, 285], [10515, 285], [10816, 284], [11116, 284], [11417, 283], [11717, 283], [12018, 282], [12318, 282], [12619, 281], [12919, 281], [13220, 280], [13520, 280], [13821, 279], [14121, 279], [14422, 278], [14722, 278], [15023, 277], [15323, 277], [15624, 276], [15925, 275], [16226, 274], [16526, 274], [16826, 274], [17126, 274], [17427, 273], [17727, 273], [18028, 272], [18328, 272], [18629, 271], [18929, 271], [19230, 270], [19531, 269], [19831, 269], [20132, 268], [20432, 268], [20733, 267], [21033, 267], [21334, 266], [21634, 266], [21935, 265], [22235, 265], [22536, 264], [22836, 264], [23137, 263], [23437, 263], [23738, 262], [24038, 262], [24339, 261], [24639, 261], [24940, 260], [25240, 260], [25541, 259], [25841, 259], [26142, 258], [26442, 258], [26743, 257], [27043, 257], [27344, 256], [27644, 256], [27945, 255], [28245, 147], [28409, 91], [28546, 147], [28707, 93], [28846, 149], [29006, 94], [29147, 151], [29303, 97], [29447, 253], [29748, 252], [30048, 252], [30349, 251], [30649, 251], [30950, 250], [31250, 250], [31551, 249], [31851, 249], [32152, 248], [32452, 248], [32753, 247], [33053, 59], [33134, 32], [33185, 115], [33354, 57], [33435, 31], [33485, 115], [33654, 56], [33735, 32], [33784, 116], [33955, 55], [34036, 31], [34084, 116], [34255, 54], [34336, 31], [34384, 116], [34556, 53], [34636, 31], [34683, 117], [34856, 53], [34936, 31], [34983, 117], [35157, 52], [35236, 31], [35282, 118], [35458, 52], [35536, 31], [35582, 118], [35758, 52], [35836, 32], [35881, 119], [36059, 52], [36136, 33], [36181, 119], [36359, 52], [36436, 34], [36480, 41], [36529, 71], [36660, 51], [36736, 34], [36779, 41], [36832, 68], [36960, 52], [37036, 83], [37133, 67], [37261, 51], [37335, 83], [37434, 66], [37561, 52], [37635, 83], [37735, 65], [37862, 51], [37935, 83], [38035, 65], [38162, 51], [38235, 83], [38336, 64], [38463, 51], [38535, 83], [38637, 63], [38763, 51], [38835, 82], [38937, 63], [39064, 51], [39135, 82], [39237, 63], [39364, 51], [39435, 82], [39538, 62], [39665, 51], [39735, 82], [39838, 62], [39965, 51], [40034, 83], [40138, 62], [40266, 50], [40334, 83], [40438, 62], [40566, 51], [40634, 83], [40738, 62], [40867, 50], [40934, 84], [41039, 61], [41167, 51], [41234, 84], [41339, 61], [41468, 50], [41534, 85], [41639, 61], [41768, 51], [41833, 86], [41939, 61], [42069, 50], [42132, 88], [42239, 61], [42369, 51], [42432, 88], [42539, 61], [42670, 50], [42731, 90], [42839, 61], [42970, 52], [43030, 92], [43138, 62], [43271, 152], [43436, 64], [43571, 154], [43735, 65], [43872, 228], [44172, 228], [44473, 227], [44773, 227], [45074, 226], [45374, 226], [45675, 225], [45975, 225], [46276, 224], [46576, 224], [46877, 223], [47177, 223], [47478, 222], [47778, 222], [48079, 221], [48379, 221], [48680, 220], [48980, 220], [49281, 219], [49581, 219], [49882, 218], [50182, 218], [50483, 217], [50783, 217], [51084, 216], [51385, 215], [51685, 215], [51986, 214], [52286, 214], [52587, 213], [52887, 213], [53188, 212], [53488, 212], [53789, 211], [54089, 211], [54390, 210], [54690, 210], [54991, 168], [55160, 40], [55291, 209], [55592, 208], [55892, 208], [56193, 207], [56493, 207], [56794, 206], [57094, 206], [57395, 205], [57695, 205], [57996, 204], [58296, 204], [58597, 203], [58897, 203], [59198, 202], [59498, 202], [59799, 201], [60099, 201], [60400, 200], [60700, 200], [61001, 199], [61301, 199], [61602, 198], [61903, 134], [62038, 62], [62209, 25], [62304, 26], [62337, 63], [62637, 63], [62938, 62], [63238, 62], [63538, 62], [63838, 62], [64138, 62], [64438, 62], [64739, 61], [65039, 61], [65339, 61], [65639, 61], [65939, 61], [66239, 61], [66540, 60], [66840, 60], [67140, 60], [67440, 60], [67740, 60], [68040, 60], [68341, 59], [68641, 59], [68941, 59], [69241, 59], [69541, 59], [69842, 58], [70142, 58], [70442, 58], [70742, 58], [71042, 58], [71342, 58], [71643, 57], [71943, 57], [72243, 57], [72543, 57], [72843, 57], [73143, 57], [73444, 56], [73744, 56], [74044, 56], [74344, 56], [74644, 56], [74944, 56], [75245, 55], [75545, 55], [75845, 55], [76145, 55], [76445, 55], [76745, 55], [77046, 54], [77346, 54], [77646, 54], [77946, 54], [78246, 54], [78546, 54], [78847, 53], [79147, 53], [79447, 53], [79747, 53], [80047, 53], [80348, 52], [80648, 52], [80948, 52], [81248, 52], [81548, 52], [81848, 52], [82149, 51], [82449, 51], [82749, 51], [83049, 51], [83349, 51], [83649, 51], [83950, 50], [84250, 50], [84550, 50], [84850, 50], [85150, 50], [85450, 50], [85751, 49], [86051, 49], [86351, 49], [86651, 49], [86951, 49], [87251, 49], [87552, 48], [87852, 48], [88152, 48], [88452, 48], [88752, 48], [89053, 47], [89353, 47], [89653, 47], [89953, 47]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan12", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": 1.0, "y": 0.9009999, "z": -1.25}, "object_poses": [{"objectName": "Potato_22312ae0", "position": {"x": -1.211005, "y": 0.9705572, "z": 2.310654}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": 1.59296286, "y": 0.9705572, "z": 2.11864662}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -0.689003944, "y": 0.7468791, "z": 0.847031236}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": 1.36229229, "y": 0.937942, "z": 1.00726593}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_d40bfead", "position": {"x": -0.689003944, "y": 0.747385561, "z": 2.31313133}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.96003, "y": 0.9390294, "z": 0.6879356}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": -1.06534982, "y": 1.35438263, "z": 0.19565101}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": -0.96003, "y": 0.9730633, "z": 0.9986881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.38810134, "y": 0.842021, "z": 1.39808726}, "rotation": {"x": 1.40334191e-14, "y": 180.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -0.791773558, "y": 0.08874029, "z": 0.7746959}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": 1.48333013, "y": 0.7688297, "z": 0.780662537}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -1.01830924, "y": 1.56053746, "z": 0.118999816}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -0.8771876, "y": 1.56053746, "z": -0.03430243}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.5864712, "y": 1.50343263, "z": 1.0115906}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -0.7106551, "y": 0.9342062, "z": 0.9986881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -0.876905, "y": 0.9342062, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.00063038, "y": 1.82503581, "z": 0.0106455684}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": 1.66985226, "y": 0.9342061, "z": 0.739372134}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": -1.12628, "y": 1.02830076, "z": 0.8433119}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": 1.45669448, "y": 1.01416636, "z": 1.68157053}, "rotation": {"x": 3.86250019, "y": 59.981308, "z": 359.927338}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.523011, "y": 0.0695113838, "z": 2.945116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.9230002, "y": 0.990676045, "z": 2.26549983}, "rotation": {"x": 3.20195068e-05, "y": 302.4703, "z": 7.218442e-06}}, {"objectName": "Fork_d40bfead", "position": {"x": -1.04315484, "y": 0.938448548, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -0.9648368, "y": 0.876337349, "z": 0.195651308}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": 1.41898918, "y": 0.09737432, "z": 0.0706810355}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.4704}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": 1.275738, "y": 0.7618369, "z": 0.9166}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": 1.59296227, "y": 0.959892631, "z": 1.00726593}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -0.961227536, "y": 1.9462316, "z": 1.92461956}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": -0.712255, "y": 0.9871304, "z": 2.3864}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.16145623, "y": 1.49741375, "z": 2.246276}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -1.12628, "y": 0.9379421, "z": 0.8433119}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": -0.899039865, "y": 0.598476648, "z": 0.3489541}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": 1.39171886, "y": 0.0867086649, "z": 2.532645}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": -0.7106551, "y": 0.937464237, "z": 0.921}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.35784, "y": 0.105368823, "z": 2.945116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -1.01497114, "y": 0.564397633, "z": 0.118999816}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pan_9f7abbea", "position": {"x": 1.3781, "y": 1.73753989, "z": 0.3929}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.827398658, "y": 0.7476966, "z": 0.7790625}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": 1.43918228, "y": 0.932899952, "z": 0.87331903}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 451327067, "scene_num": 12}, "task_id": "trial_T20190908_095945_179137", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2JBNDG0U9IA6I_37U1UTWH9YD3MB3C9URYXOOW0E78RM", "high_descs": ["Turn around and walk straight then turn right and face the kitchen sink", "Pick up the glass cup from the kitchen sink", "While holding the cup slowly lift up and face the microwave above", "Open the microwave to place the cup inside and warm the cup, then remove the cup ", "Turn right and walk straight slightly then turn right and walk to the refrigerator", "Open the refrigerator and place the glass cup inside then close the door"], "task_desc": "Place a heated glass cup in the refrigerator", "votes": [1, 1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3L2IS5HSFD9G5AIXJQBMKW1BW84NUN", "high_descs": ["turn around and walk to the kitchen sink on the right", "grab the clear cup out of the right side kitchen sink", "look up at the microwave above the kitchen sink", "place the cup inside of the microwave, cook it, then take it back out", "look back down at the kitchen sink, then turn right and head to the fridge on the right at the end of the room on the right", "place the cup inside of the fridge there"], "task_desc": "place a microwaved cup inside of the fridge", "votes": [1, 1, 1]}, {"assignment_id": "A1ELPYAFO7MANS_3WYGZ5XF3Z6ZIR5QX8O6D32JHG3KSO", "high_descs": ["Turn around and walk to the sink on the right.", "Pick up the cup in the sink.", "Look up.", "Heat the cup in the microwave.", "Turn around and veer left to the fridge.", "Place the cup in the fridge, on the same shelf but opposite side to the avocado."], "task_desc": "Place a heated cup in a fridge.", "votes": [1, 1, 0]}]}}