{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000041.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000042.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000043.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000044.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000045.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 3, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000078.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000079.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000080.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000081.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000082.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000083.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000084.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 4, "image_name": "000000086.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000089.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000090.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000091.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000092.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000093.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000094.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000095.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000096.png", "low_idx": 10}, {"high_idx": 4, "image_name": "000000097.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000098.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000099.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000100.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000101.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000102.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000103.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000104.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000105.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000106.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000107.png", "low_idx": 11}, {"high_idx": 4, "image_name": "000000108.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000109.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000110.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000111.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 13}, {"high_idx": 5, "image_name": "000000121.png", "low_idx": 14}, {"high_idx": 5, "image_name": "000000122.png", "low_idx": 14}, {"high_idx": 5, "image_name": "000000123.png", "low_idx": 14}, {"high_idx": 5, "image_name": "000000124.png", "low_idx": 14}, {"high_idx": 5, "image_name": "000000125.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000126.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000127.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000128.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000129.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000130.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000131.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000132.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000133.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000134.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000135.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000136.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000137.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000138.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000139.png", "low_idx": 15}, {"high_idx": 5, "image_name": "000000140.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000141.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000142.png", "low_idx": 16}, {"high_idx": 5, "image_name": "000000143.png", "low_idx": 16}, {"high_idx": 6, "image_name": "000000144.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000145.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000146.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000147.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000148.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000149.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000150.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000151.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000152.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000153.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000154.png", "low_idx": 17}, {"high_idx": 6, "image_name": "000000155.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000156.png", "low_idx": 18}, {"high_idx": 6, "image_name": "000000157.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000158.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000159.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000160.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 19}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 20}, {"high_idx": 7, "image_name": "000000179.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000180.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000181.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000182.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000183.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000184.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000185.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000186.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000187.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000188.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000189.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000190.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000191.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000192.png", "low_idx": 21}, {"high_idx": 7, "image_name": "000000193.png", "low_idx": 21}, {"high_idx": 8, "image_name": "000000194.png", "low_idx": 22}, {"high_idx": 8, "image_name": "000000195.png", "low_idx": 22}, {"high_idx": 8, "image_name": "000000196.png", "low_idx": 22}, {"high_idx": 8, "image_name": "000000197.png", "low_idx": 22}, {"high_idx": 8, "image_name": "000000198.png", "low_idx": 22}, {"high_idx": 8, "image_name": "000000199.png", "low_idx": 22}, {"high_idx": 8, "image_name": "000000200.png", "low_idx": 22}, {"high_idx": 8, "image_name": "000000201.png", "low_idx": 22}, {"high_idx": 8, "image_name": "000000202.png", "low_idx": 22}, {"high_idx": 8, "image_name": "000000203.png", "low_idx": 22}, {"high_idx": 8, "image_name": "000000204.png", "low_idx": 22}, {"high_idx": 8, "image_name": "000000205.png", "low_idx": 23}, {"high_idx": 8, "image_name": "000000206.png", "low_idx": 23}, {"high_idx": 8, "image_name": "000000207.png", "low_idx": 23}, {"high_idx": 8, "image_name": "000000208.png", "low_idx": 23}, {"high_idx": 8, "image_name": "000000209.png", "low_idx": 23}, {"high_idx": 8, "image_name": "000000210.png", "low_idx": 23}, {"high_idx": 8, "image_name": "000000211.png", "low_idx": 23}, {"high_idx": 8, "image_name": "000000212.png", "low_idx": 23}, {"high_idx": 8, "image_name": "000000213.png", "low_idx": 23}, {"high_idx": 8, "image_name": "000000214.png", "low_idx": 23}, {"high_idx": 8, "image_name": "000000215.png", "low_idx": 23}, {"high_idx": 8, "image_name": "000000216.png", "low_idx": 24}, {"high_idx": 8, "image_name": "000000217.png", "low_idx": 24}, {"high_idx": 8, "image_name": "000000218.png", "low_idx": 25}, {"high_idx": 8, "image_name": "000000219.png", "low_idx": 25}, {"high_idx": 8, "image_name": "000000220.png", "low_idx": 26}, {"high_idx": 8, "image_name": "000000221.png", "low_idx": 26}, {"high_idx": 8, "image_name": "000000222.png", "low_idx": 27}, {"high_idx": 8, "image_name": "000000223.png", "low_idx": 27}, {"high_idx": 8, "image_name": "000000224.png", "low_idx": 28}, {"high_idx": 8, "image_name": "000000225.png", "low_idx": 28}, {"high_idx": 8, "image_name": "000000226.png", "low_idx": 28}, {"high_idx": 8, "image_name": "000000227.png", "low_idx": 28}, {"high_idx": 8, "image_name": "000000228.png", "low_idx": 28}, {"high_idx": 8, "image_name": "000000229.png", "low_idx": 28}, {"high_idx": 8, "image_name": "000000230.png", "low_idx": 28}, {"high_idx": 8, "image_name": "000000231.png", "low_idx": 28}, {"high_idx": 8, "image_name": "000000232.png", "low_idx": 28}, {"high_idx": 8, "image_name": "000000233.png", "low_idx": 28}, {"high_idx": 8, "image_name": "000000234.png", "low_idx": 28}, {"high_idx": 8, "image_name": "000000235.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000236.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000237.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000238.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000239.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000240.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000241.png", "low_idx": 32}, {"high_idx": 8, "image_name": "000000242.png", "low_idx": 32}, {"high_idx": 8, "image_name": "000000243.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000244.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000245.png", "low_idx": 34}, {"high_idx": 8, "image_name": "000000246.png", "low_idx": 34}, {"high_idx": 8, "image_name": "000000247.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000248.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000249.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000250.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000251.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000252.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000253.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000254.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000255.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000256.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000257.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000258.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000259.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000260.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000261.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000262.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000268.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000269.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000270.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000271.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000272.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000273.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000274.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000275.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000276.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000277.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000278.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000279.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000280.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000281.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000282.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000283.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000284.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000285.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000286.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000287.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000288.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000289.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000290.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000291.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000292.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000293.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000294.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000295.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000296.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000297.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000298.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000299.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000300.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000301.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000302.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000303.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000304.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000305.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000306.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000307.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000308.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000309.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000310.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000311.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000312.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000313.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000314.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000315.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000316.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000317.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000318.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000319.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000320.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000321.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000322.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000323.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000324.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000325.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000326.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000327.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000328.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000329.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000330.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000331.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000332.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000333.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000334.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000335.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000336.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000337.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000338.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000339.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000340.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000341.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000342.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000343.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000344.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000345.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000346.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000347.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000348.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000349.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000350.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000351.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000352.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000353.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000354.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000355.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000356.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000357.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000358.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000359.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000360.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000361.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000362.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000363.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000364.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000365.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000366.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000367.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000368.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000369.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000370.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000371.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000372.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000373.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000374.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000375.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000376.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000377.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000378.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000379.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000380.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000381.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000382.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000383.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000384.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000385.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000386.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000387.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000388.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000389.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000390.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000391.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000392.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000393.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000394.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000395.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000396.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000397.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000398.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000399.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000400.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000401.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000402.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000403.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000404.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000405.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000406.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000407.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000408.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000409.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000410.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000411.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000412.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000413.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000414.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000415.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000416.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000417.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000418.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000419.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000420.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000421.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000422.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000423.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000424.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000425.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000426.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000427.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000428.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000429.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000430.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000431.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000432.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000433.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000434.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000435.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000436.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000437.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000438.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000439.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000440.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000441.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000442.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000443.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000444.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000445.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000446.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000447.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000448.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000449.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000450.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000451.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000452.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000453.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000454.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000455.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000456.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000457.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000458.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000459.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000460.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000461.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000462.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000463.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000464.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000465.png", "low_idx": 85}, {"high_idx": 11, "image_name": "000000466.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000467.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000468.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000469.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000470.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000471.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000472.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000473.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000474.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000475.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000476.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000477.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000478.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000479.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000480.png", "low_idx": 86}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|8|3|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [4.91945552, 4.91945552, 2.990052224, 2.990052224, 3.6440084, 3.6440084]], "coordinateReceptacleObjectId": ["CounterTop", [4.844, 4.844, 1.856, 1.856, 3.7488, 3.7488]], "forceVisible": true, "objectId": "ButterKnife|+01.23|+00.91|+00.75"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|8|2|3|60"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [6.13680268, 6.13680268, 1.947330236, 1.947330236, 3.8915676, 3.8915676]], "forceVisible": true, "objectId": "Bread|+01.53|+00.97|+00.49"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|8|3|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "drawer"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [4.91945552, 4.91945552, 2.990052224, 2.990052224, 3.6440084, 3.6440084]], "coordinateReceptacleObjectId": ["Drawer", [12.084, 12.084, 2.782, 2.782, 3.0768, 3.0768]], "forceVisible": true, "objectId": "ButterKnife|+01.23|+00.91|+00.75", "receptacleObjectId": "Drawer|+03.02|+00.77|+00.70"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|8|2|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [6.13680268, 6.13680268, 1.947330236, 1.947330236, 3.8915676, 3.8915676]], "coordinateReceptacleObjectId": ["CounterTop", [4.844, 4.844, 1.856, 1.856, 3.7488, 3.7488]], "forceVisible": true, "objectId": "Bread|+01.53|+00.97|+00.49|BreadSliced_7"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-1|5|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|11|5|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "garbagecan"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [6.13680268, 6.13680268, 1.947330236, 1.947330236, 3.8915676, 3.8915676]], "coordinateReceptacleObjectId": ["GarbageCan", [12.728, 12.728, 6.4, 6.4, 0.1178724468, 0.1178724468]], "forceVisible": true, "objectId": "Bread|+01.53|+00.97|+00.49|BreadSliced_7", "receptacleObjectId": "GarbageCan|+03.18|+00.03|+01.60"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|+01.23|+00.91|+00.75"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [147, 116, 151, 131], "mask": [[34649, 1], [34947, 4], [35247, 5], [35547, 5], [35847, 5], [36147, 5], [36447, 5], [36747, 5], [37047, 5], [37347, 5], [37647, 5], [37947, 4], [38248, 3], [38548, 3], [38848, 3], [39148, 3]], "point": [149, 122]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|+01.53|+00.97|+00.49"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [96, 66, 187, 180], "mask": [[19640, 12], [19937, 20], [20236, 26], [20535, 31], [20835, 34], [21134, 38], [21434, 41], [21733, 44], [22033, 46], [22332, 50], [22632, 51], [22931, 53], [23230, 55], [23530, 56], [23829, 58], [24129, 59], [24428, 60], [24728, 60], [25027, 61], [25327, 61], [25626, 62], [25926, 62], [26225, 62], [26525, 62], [26824, 63], [27123, 63], [27423, 63], [27722, 64], [28022, 63], [28322, 63], [28621, 64], [28921, 63], [29220, 64], [29519, 65], [29819, 65], [30118, 65], [30417, 66], [30717, 65], [31016, 66], [31316, 66], [31615, 66], [31915, 66], [32214, 67], [32514, 67], [32813, 67], [33113, 67], [33412, 68], [33712, 67], [34011, 68], [34311, 67], [34610, 68], [34910, 68], [35209, 68], [35509, 68], [35808, 69], [36107, 69], [36407, 69], [36706, 70], [37006, 69], [37305, 70], [37605, 70], [37904, 70], [38204, 70], [38503, 71], [38803, 70], [39102, 71], [39402, 71], [39701, 71], [40001, 71], [40300, 71], [40599, 72], [40899, 72], [41198, 72], [41498, 72], [41797, 72], [42097, 72], [42396, 73], [42696, 72], [42996, 72], [43296, 72], [43596, 71], [43896, 71], [44197, 70], [44498, 68], [44799, 67], [45100, 65], [45400, 65], [45701, 64], [46001, 63], [46302, 62], [46602, 61], [46903, 60], [47203, 60], [47504, 58], [47804, 58], [48104, 58], [48405, 56], [48705, 56], [49006, 55], [49307, 53], [49608, 52], [49911, 49], [50214, 45], [50517, 42], [50820, 38], [51124, 34], [51427, 30], [51730, 27], [52033, 24], [52336, 20], [52639, 17], [52942, 13], [53245, 10], [53548, 6], [53853, 1]], "point": [141, 122]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+03.02|+00.77|+00.70"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [98, 140, 226, 161], "mask": [[41798, 129], [42098, 128], [42398, 128], [42699, 127], [42999, 126], [43299, 126], [43599, 126], [43899, 126], [44200, 124], [44500, 124], [44800, 124], [45100, 123], [45400, 123], [45701, 122], [46001, 122], [46301, 121], [46601, 121], [46901, 121], [47202, 119], [47502, 119], [47802, 119], [48102, 119]], "point": [162, 149]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|+01.23|+00.91|+00.75", "placeStationary": true, "receptacleObjectId": "Drawer|+03.02|+00.77|+00.70"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [85, 140, 244, 216], "mask": [[41798, 129], [42098, 130], [42398, 130], [42698, 130], [42997, 131], [43297, 132], [43597, 132], [43897, 132], [44196, 134], [44496, 57], [44561, 69], [44796, 53], [44864, 66], [45096, 51], [45164, 67], [45396, 50], [45465, 66], [45695, 50], [45765, 66], [45995, 25], [46064, 68], [46295, 15], [46333, 12], [46364, 68], [46595, 11], [46609, 1], [46616, 30], [46663, 69], [46895, 10], [46911, 36], [46962, 70], [47194, 54], [47260, 73], [47494, 57], [47557, 76], [47794, 139], [48094, 140], [48394, 140], [48693, 141], [48993, 142], [49293, 142], [49593, 142], [49893, 143], [50192, 144], [50492, 144], [50792, 144], [51092, 145], [51391, 146], [51691, 146], [51991, 147], [52291, 147], [52591, 147], [52890, 149], [53190, 149], [53490, 149], [53790, 150], [54090, 150], [54389, 151], [54689, 152], [54989, 152], [55289, 152], [55589, 152], [55888, 154], [56188, 154], [56486, 159], [56786, 159], [57085, 160], [57386, 159], [57686, 159], [57986, 158], [58286, 158], [58587, 156], [58887, 156], [59187, 155], [59488, 154], [59788, 154], [60088, 153], [60389, 152], [60689, 151], [60989, 151], [61289, 150], [61590, 149], [61890, 149], [62190, 148], [62491, 147], [62791, 146], [63091, 146], [63392, 144], [63692, 144], [63992, 143], [64292, 143], [64593, 142]], "point": [164, 177]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+03.02|+00.77|+00.70"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [85, 140, 244, 216], "mask": [[41798, 129], [42098, 130], [42398, 130], [42698, 130], [42997, 131], [43297, 132], [43597, 132], [43897, 132], [44196, 134], [44496, 57], [44562, 68], [44796, 53], [44864, 66], [45096, 51], [45165, 66], [45396, 50], [45465, 66], [45695, 50], [45764, 67], [45995, 23], [46064, 38], [46105, 27], [46295, 15], [46325, 21], [46363, 39], [46406, 26], [46595, 10], [46609, 1], [46615, 31], [46662, 40], [46706, 26], [46895, 10], [46909, 38], [46961, 41], [47007, 25], [47194, 55], [47259, 43], [47307, 26], [47494, 108], [47607, 26], [47794, 109], [47907, 26], [48094, 109], [48207, 27], [48394, 109], [48508, 26], [48693, 110], [48808, 26], [48993, 111], [49108, 27], [49293, 111], [49408, 27], [49593, 111], [49708, 27], [49893, 112], [50008, 28], [50192, 113], [50308, 28], [50492, 113], [50608, 28], [50792, 113], [50908, 28], [51092, 114], [51208, 29], [51391, 115], [51509, 28], [51691, 115], [51809, 28], [51991, 115], [52109, 29], [52291, 115], [52409, 29], [52591, 115], [52709, 29], [52890, 116], [53010, 29], [53190, 116], [53310, 29], [53490, 116], [53610, 29], [53790, 116], [53910, 30], [54090, 116], [54210, 30], [54389, 117], [54511, 29], [54689, 117], [54811, 30], [54989, 117], [55111, 30], [55289, 117], [55411, 30], [55589, 117], [55711, 30], [55888, 118], [56012, 30], [56188, 154], [56486, 159], [56786, 159], [57085, 160], [57386, 159], [57686, 159], [57986, 158], [58286, 158], [58587, 156], [58887, 156], [59187, 155], [59488, 154], [59788, 154], [60088, 153], [60389, 152], [60689, 151], [60989, 151], [61289, 150], [61590, 149], [61890, 149], [62190, 148], [62491, 147], [62791, 146], [63091, 146], [63392, 144], [63692, 144], [63992, 143], [64292, 143], [64593, 142]], "point": [164, 177]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+01.53|+00.97|+00.49|BreadSliced_7"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [84, 157, 151, 201], "mask": [[46893, 2], [47188, 13], [47487, 19], [47786, 24], [48086, 28], [48385, 33], [48685, 36], [48984, 39], [49284, 42], [49584, 44], [49884, 47], [50184, 50], [50484, 51], [50785, 52], [51086, 53], [51387, 54], [51688, 55], [51989, 56], [52290, 56], [52591, 57], [52891, 58], [53192, 58], [53493, 58], [53793, 59], [54094, 58], [54394, 58], [54695, 57], [54996, 56], [55296, 55], [55597, 54], [55899, 52], [56202, 48], [56505, 45], [56808, 41], [57111, 38], [57414, 35], [57717, 32], [58020, 29], [58323, 26], [58626, 22], [58928, 20], [59231, 17], [59534, 13], [59837, 10], [60140, 6]], "point": [117, 178]}}, "high_idx": 7}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+01.53|+00.97|+00.49|BreadSliced_7", "placeStationary": true, "receptacleObjectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 51], [25964, 136], [26190, 51], [26264, 136], [26489, 52], [26564, 136], [26789, 52], [26863, 137], [27089, 52], [27163, 137], [27388, 53], [27463, 43], [27517, 83], [27688, 53], [27763, 41], [27821, 79], [27988, 54], [28063, 38], [28123, 77], [28287, 55], [28363, 37], [28424, 76], [28587, 55], [28663, 35], [28725, 75], [28887, 55], [28963, 34], [29026, 74], [29187, 55], [29263, 34], [29327, 73], [29486, 56], [29563, 33], [29628, 72], [29786, 56], [29863, 32], [29929, 71], [30086, 56], [30163, 32], [30229, 71], [30385, 57], [30462, 32], [30529, 71], [30685, 57], [30762, 32], [30830, 70], [30985, 57], [31062, 32], [31130, 70], [31284, 58], [31362, 31], [31430, 70], [31584, 58], [31662, 31], [31730, 70], [31884, 58], [31962, 31], [32030, 70], [32183, 59], [32262, 31], [32330, 70], [32483, 60], [32562, 31], [32630, 70], [32783, 60], [32861, 33], [32930, 70], [33083, 62], [33160, 34], [33230, 70], [33382, 64], [33458, 36], [33530, 70], [33682, 68], [33755, 39], [33829, 71], [33982, 112], [34129, 71], [34281, 114], [34428, 72], [34581, 114], [34727, 73], [34881, 115], [35027, 73], [35180, 116], [35326, 74], [35480, 117], [35626, 74], [35780, 118], [35925, 75], [36080, 119], [36223, 77], [36379, 121], [36522, 78], [36679, 122], [36821, 79], [36979, 124], [37119, 81], [37278, 128], [37417, 83], [37578, 134], [37714, 86], [37878, 222], [38177, 223], [38477, 223], [38777, 223], [39076, 224], [39376, 224], [39676, 224], [39976, 224], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 60], [62453, 59], [62752, 60], [63052, 60], [63352, 60], [63651, 61], [63951, 61], [64251, 61], [64550, 62], [64841, 71], [65141, 70], [65440, 71], [65740, 71], [66040, 71], [66339, 72], [66639, 72], [66939, 72], [67238, 73], [67538, 72], [67838, 72], [68138, 72], [68438, 72], [68738, 72], [69037, 73], [69337, 73], [69637, 73], [69938, 72], [70238, 71], [70538, 71], [70838, 71], [71138, 71], [71438, 71], [71738, 71], [72038, 71], [72338, 71], [72639, 67], [72939, 58], [73239, 53], [73539, 48], [73839, 47], [74139, 47], [74440, 46], [74740, 46], [75040, 45], [75341, 44], [75641, 44], [75941, 44], [76241, 44], [76542, 43], [76842, 43], [77142, 43], [77443, 42], [77743, 42], [78044, 41], [78344, 41], [78644, 40], [78945, 39], [79245, 39], [79546, 38], [79847, 38], [80148, 41], [80448, 48], [80749, 51], [81050, 53], [81351, 53], [81653, 52], [81954, 51], [82255, 50], [82556, 49], [82858, 46], [83159, 45], [83460, 44], [83762, 42], [84064, 40], [84365, 39], [84667, 37], [84969, 35], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 51], [25964, 136], [26190, 51], [26264, 136], [26489, 52], [26564, 136], [26789, 52], [26863, 137], [27089, 52], [27163, 137], [27388, 53], [27463, 43], [27517, 83], [27688, 53], [27763, 41], [27821, 79], [27988, 54], [28063, 38], [28123, 77], [28287, 51], [28368, 32], [28424, 76], [28587, 46], [28672, 26], [28725, 75], [28887, 44], [28974, 23], [29026, 74], [29187, 43], [29276, 21], [29327, 73], [29486, 43], [29577, 19], [29628, 72], [29786, 41], [29878, 17], [29929, 71], [30086, 40], [30179, 16], [30229, 71], [30385, 41], [30479, 15], [30529, 71], [30685, 41], [30779, 15], [30830, 70], [30985, 41], [31079, 15], [31130, 70], [31284, 42], [31379, 14], [31430, 70], [31584, 42], [31679, 14], [31730, 70], [31884, 42], [31980, 13], [32030, 70], [32183, 43], [32280, 13], [32330, 70], [32483, 43], [32580, 13], [32630, 70], [32783, 43], [32880, 14], [32930, 70], [33083, 43], [33179, 15], [33230, 70], [33382, 45], [33479, 15], [33530, 70], [33682, 45], [33778, 16], [33829, 71], [33982, 45], [34078, 16], [34129, 71], [34281, 47], [34378, 17], [34428, 72], [34581, 47], [34677, 18], [34727, 73], [34881, 47], [34977, 19], [35027, 73], [35180, 48], [35277, 19], [35326, 74], [35480, 48], [35577, 20], [35626, 74], [35780, 48], [35877, 21], [35925, 75], [36080, 49], [36177, 22], [36223, 77], [36379, 50], [36477, 23], [36522, 78], [36679, 50], [36776, 25], [36821, 79], [36979, 50], [37076, 27], [37119, 81], [37278, 51], [37376, 30], [37417, 83], [37578, 51], [37676, 36], [37714, 86], [37878, 51], [37976, 124], [38177, 52], [38276, 124], [38477, 53], [38576, 124], [38777, 53], [38876, 124], [39076, 54], [39175, 125], [39376, 54], [39475, 125], [39676, 54], [39775, 125], [39976, 56], [40074, 126], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 60], [62453, 59], [62752, 60], [63052, 60], [63352, 60], [63651, 61], [63951, 61], [64251, 61], [64550, 62], [64841, 71], [65141, 70], [65440, 71], [65740, 71], [66040, 71], [66339, 72], [66639, 72], [66939, 72], [67238, 73], [67538, 72], [67838, 72], [68138, 72], [68438, 72], [68738, 72], [69037, 73], [69337, 73], [69637, 73], [69938, 72], [70238, 71], [70538, 71], [70838, 71], [71138, 71], [71438, 71], [71738, 71], [72038, 71], [72338, 71], [72639, 69], [72939, 69], [73239, 69], [73539, 69], [73839, 69], [74139, 69], [74440, 68], [74740, 68], [75040, 68], [75341, 66], [75641, 66], [75941, 66], [76241, 66], [76542, 65], [76842, 65], [77142, 65], [77443, 64], [77743, 63], [78044, 62], [78344, 62], [78644, 62], [78945, 61], [79245, 61], [79546, 60], [79847, 59], [80148, 57], [80448, 57], [80749, 56], [81050, 55], [81351, 54], [81653, 52], [81954, 51], [82255, 50], [82556, 49], [82858, 46], [83159, 45], [83460, 44], [83762, 42], [84064, 40], [84365, 39], [84667, 37], [84969, 35], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+01.53|+00.97|+00.49|BreadSliced_7"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [126, 94, 179, 134], "mask": [[28044, 18], [28338, 30], [28633, 39], [28931, 43], [29230, 46], [29529, 48], [29827, 51], [30126, 53], [30426, 53], [30726, 53], [31026, 53], [31326, 53], [31626, 53], [31926, 54], [32226, 54], [32526, 54], [32826, 54], [33126, 53], [33427, 52], [33727, 51], [34027, 51], [34328, 50], [34628, 49], [34928, 49], [35228, 49], [35528, 49], [35828, 49], [36129, 48], [36429, 48], [36729, 47], [37029, 47], [37329, 47], [37629, 47], [37929, 47], [38229, 47], [38530, 46], [38830, 46], [39130, 45], [39430, 45], [39730, 45], [40032, 42]], "point": [152, 113]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 51], [25964, 136], [26190, 51], [26264, 136], [26489, 52], [26564, 136], [26789, 52], [26863, 137], [27089, 52], [27163, 137], [27388, 53], [27463, 43], [27517, 83], [27688, 53], [27763, 41], [27821, 79], [27988, 54], [28063, 38], [28123, 77], [28287, 55], [28363, 37], [28424, 76], [28587, 55], [28663, 35], [28725, 75], [28887, 55], [28963, 34], [29026, 74], [29187, 55], [29263, 34], [29327, 73], [29486, 56], [29563, 33], [29628, 72], [29786, 56], [29863, 32], [29929, 71], [30086, 56], [30163, 32], [30229, 71], [30385, 57], [30462, 32], [30529, 71], [30685, 57], [30762, 32], [30830, 70], [30985, 57], [31062, 32], [31130, 70], [31284, 58], [31362, 31], [31430, 70], [31584, 58], [31662, 31], [31730, 70], [31884, 58], [31962, 31], [32030, 70], [32183, 59], [32262, 31], [32330, 70], [32483, 60], [32562, 31], [32630, 70], [32783, 60], [32861, 33], [32930, 70], [33083, 62], [33160, 34], [33230, 70], [33382, 64], [33458, 36], [33530, 70], [33682, 68], [33755, 39], [33829, 71], [33982, 112], [34129, 71], [34281, 114], [34428, 72], [34581, 114], [34727, 73], [34881, 115], [35027, 73], [35180, 116], [35326, 74], [35480, 117], [35626, 74], [35780, 118], [35925, 75], [36080, 119], [36223, 77], [36379, 121], [36522, 78], [36679, 122], [36821, 79], [36979, 124], [37119, 81], [37278, 128], [37417, 83], [37578, 134], [37714, 86], [37878, 222], [38177, 223], [38477, 223], [38777, 223], [39076, 224], [39376, 224], [39676, 224], [39976, 224], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 60], [62453, 59], [62752, 60], [63052, 60], [63352, 60], [63651, 61], [63951, 61], [64251, 61], [64550, 62], [64841, 71], [65141, 70], [65440, 71], [65740, 71], [66040, 71], [66339, 72], [66639, 72], [66939, 72], [67238, 73], [67538, 72], [67838, 72], [68138, 72], [68438, 72], [68738, 70], [69037, 65], [69337, 62], [69637, 60], [69938, 57], [70238, 55], [70538, 53], [70838, 51], [71138, 50], [71438, 50], [71738, 49], [72038, 49], [72338, 49], [72639, 48], [72939, 48], [73239, 47], [73539, 47], [73839, 47], [74139, 47], [74440, 46], [74740, 46], [75040, 46], [75341, 45], [75641, 44], [75941, 44], [76241, 44], [76542, 43], [76842, 43], [77142, 43], [77443, 42], [77743, 42], [78044, 42], [78344, 43], [78644, 44], [78945, 45], [79245, 46], [79546, 46], [79847, 46], [80148, 46], [80448, 46], [80749, 46], [81050, 46], [81351, 46], [81653, 44], [81954, 44], [82255, 43], [82556, 43], [82858, 41], [83159, 41], [83460, 40], [83762, 39], [84064, 37], [84365, 37], [84667, 35], [84969, 34], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 9}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+01.53|+00.97|+00.49|BreadSliced_7", "placeStationary": true, "receptacleObjectId": "GarbageCan|+03.18|+00.03|+01.60"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [4, 124, 127, 252], "mask": [[36955, 5], [37253, 10], [37552, 15], [37850, 20], [38149, 24], [38447, 30], [38746, 34], [39044, 39], [39343, 43], [39641, 49], [39940, 53], [40239, 57], [40537, 62], [40836, 67], [41134, 72], [41433, 76], [41731, 81], [42030, 84], [42328, 86], [42627, 87], [42925, 90], [43224, 91], [43523, 92], [43821, 94], [44120, 96], [44418, 98], [44717, 99], [45015, 101], [45314, 102], [45612, 105], [45911, 106], [46210, 107], [46509, 108], [46809, 109], [47108, 110], [47408, 110], [47708, 110], [48008, 111], [48308, 111], [48607, 112], [48907, 112], [49207, 112], [49507, 113], [49806, 114], [50106, 114], [50406, 114], [50706, 115], [51005, 116], [51305, 116], [51605, 116], [51905, 117], [52205, 117], [52504, 118], [52804, 118], [53104, 119], [53404, 119], [53704, 119], [54004, 119], [54304, 119], [54604, 120], [54904, 120], [55204, 120], [55504, 120], [55804, 121], [56104, 121], [56404, 121], [56704, 121], [57004, 122], [57304, 122], [57604, 122], [57904, 122], [58204, 123], [58504, 123], [58805, 122], [59105, 122], [59405, 122], [59705, 122], [60005, 122], [60305, 121], [60605, 120], [60905, 119], [61205, 118], [61505, 118], [61805, 119], [62105, 119], [62405, 119], [62705, 119], [63005, 119], [63305, 120], [63605, 120], [63906, 119], [64206, 119], [64506, 120], [64806, 120], [65106, 120], [65406, 120], [65706, 120], [66006, 121], [66306, 121], [66606, 121], [66906, 121], [67207, 121], [67508, 120], [67810, 117], [68112, 109], [68415, 99], [68718, 90], [69022, 80], [69325, 74], [69628, 69], [69931, 64], [70235, 58], [70538, 53], [70841, 48], [71144, 44], [71448, 40], [71751, 36], [72052, 35], [72354, 33], [72656, 31], [72957, 30], [73259, 27], [73562, 24], [73865, 21], [74169, 17], [74472, 14], [74776, 10], [75079, 7], [75382, 4]], "point": [65, 187]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan30", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 2.5, "y": 0.9304675, "z": 0.75}, "object_poses": [{"objectName": "Potato_ed324e47", "position": {"x": 0.8517525, "y": 1.1119374, "z": -1.53184986}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ff5a3b19", "position": {"x": 1.303802, "y": 1.71182191, "z": -1.68962383}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Ladle_ff5a3b19", "position": {"x": -0.789968, "y": 0.93724, "z": -0.494032025}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_3cec4766", "position": {"x": 0.304221183, "y": 1.25931478, "z": -1.74189985}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_449c396f", "position": {"x": 3.02804923, "y": 0.176759958, "z": -0.404700667}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_64c860f2", "position": {"x": 3.15292, "y": 0.762357533, "z": 0.09872391}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_64c860f2", "position": {"x": -0.950113654, "y": 0.532988966, "z": -0.273891777}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_0fe289e3", "position": {"x": 3.11145973, "y": 0.748487055, "z": 0.03222303}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_12a59a81", "position": {"x": 0.550151, "y": 0.91150856, "z": 0.259786785}, "rotation": {"x": 0.0, "y": 36.8105049, "z": 0.0}}, {"objectName": "Mug_ba5c7501", "position": {"x": -0.7759715, "y": 0.8953, "z": -0.8543895}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Apple_1e1bef89", "position": {"x": -0.09009417, "y": 0.9741304, "z": -1.44025278}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Lettuce_99ab2e9c", "position": {"x": -0.86270237, "y": 0.965891, "z": -0.047219038}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_99ab2e9c", "position": {"x": 1.09886408, "y": 0.976551, "z": 0.8892696}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_c39806f6", "position": {"x": -1.08090544, "y": 0.896606147, "z": 0.623000562}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_c39806f6", "position": {"x": -0.7864864, "y": 0.70288074, "z": 0.401712358}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6d2fd9f8", "position": {"x": 0.01749932, "y": 0.896606147, "z": -1.23981452}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "PepperShaker_6d2fd9f8", "position": {"x": 3.113, "y": 1.46086979, "z": -1.6488}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_46f611f6", "position": {"x": 0.8088945, "y": 1.67199469, "z": -1.55020607}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_5121dd32", "position": {"x": 2.97611427, "y": 1.47366667, "z": -1.697775}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_5121dd32", "position": {"x": 3.27973366, "y": 1.27347, "z": -0.790915}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c285ee2", "position": {"x": 3.355766, "y": 0.953335047, "z": -0.6986661}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bf5eb4b5", "position": {"x": 0.7810867, "y": 0.923881, "z": 0.4525168}, "rotation": {"x": 0.0, "y": 36.8105049, "z": 0.0}}, {"objectName": "Glassbottle_20771abe", "position": {"x": 3.08485031, "y": 0.176437557, "z": -0.7863487}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_5121dd32", "position": {"x": 0.05900544, "y": 0.909402966, "z": -1.3839978}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Potato_ed324e47", "position": {"x": 3.15291977, "y": 0.7858177, "z": -0.100778744}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_12a59a81", "position": {"x": -0.9354367, "y": 0.900848567, "z": 0.846407056}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ff5a3b19", "position": {"x": 3.111114, "y": 0.7495246, "z": 0.829003453}, "rotation": {"x": 0.0, "y": 270.000061, "z": 0.0}}, {"objectName": "Pot_7a7d333f", "position": {"x": -0.4203, "y": 0.902399957, "z": -0.9203}, "rotation": {"x": 0.0, "y": 134.999832, "z": 0.0}}, {"objectName": "Kettle_449c396f", "position": {"x": -0.4198, "y": 0.902399957, "z": -1.2699}, "rotation": {"x": 0.0, "y": 315.000153, "z": 0.0}}, {"objectName": "Spatula_64c860f2", "position": {"x": -0.9910205, "y": 0.146091834, "z": -0.186395854}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_3e58a77d", "position": {"x": 3.35452032, "y": 0.9264183, "z": 0.232798547}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_99ab2e9c", "position": {"x": 1.36086369, "y": 0.973691, "z": 0.747513056}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_be22aed5", "position": {"x": -0.597458, "y": 0.8981324, "z": -0.7300181}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "SaltShaker_c39806f6", "position": {"x": -0.8273932, "y": 0.70288074, "z": 0.926687658}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c285ee2", "position": {"x": 0.0223874375, "y": 0.952835, "z": -1.8032918}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6d2fd9f8", "position": {"x": -0.2322971, "y": 1.25991642, "z": -1.55225611}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "ButterKnife_db774b98", "position": {"x": 1.22986388, "y": 0.9110021, "z": 0.747513056}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_1e1bef89", "position": {"x": -1.01872838, "y": 1.07148921, "z": 1.47094}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pan_000ae861", "position": {"x": 0.341, "y": 0.8731569, "z": -1.649}, "rotation": {"x": 0.0, "y": 167.065247, "z": 0.0}}, {"objectName": "Bread_1e8ab5c8", "position": {"x": 1.53420067, "y": 0.9728919, "z": 0.486832559}, "rotation": {"x": 7.962103e-06, "y": 289.2875, "z": 7.692746e-06}}, {"objectName": "CellPhone_bf099a5c", "position": {"x": -0.950113654, "y": 0.704174757, "z": 0.926687658}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_3cec4766", "position": {"x": -0.9641653, "y": 0.8998642, "z": -0.6677512}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Tomato_bfd17fb4", "position": {"x": 3.12221074, "y": 0.9377893, "z": -0.8978308}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0fe289e3", "position": {"x": 3.11146, "y": 0.748487055, "z": 0.165224791}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_46f611f6", "position": {"x": 1.09886408, "y": 0.9052154, "z": 0.6057565}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ba5c7501", "position": {"x": -1.11901474, "y": 0.9926588, "z": 1.25879979}, "rotation": {"x": 0.0, "y": 179.999847, "z": 0.0}}], "object_toggles": [], "random_seed": 2482796124, "scene_num": 30}, "task_id": "trial_T20190907_044445_578004", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AT3C00TKZK13L_33UKMF931C1RFLMUK9I7ND1Z8SGTTZ", "high_descs": ["Walk towards the counter", "Grab knife from the counter", "Slice bread on the counter", "Turn right and walk towards the counter in front", "Open counter drawer and put knife inside ", "Turn around and walk to the counter in front", "Grab a slice of bread", "Walk towards the microwave and open it", "Put slice of bread inside the microwave ", "Cook slice of bread and take it out", "Walk towards the trash can", "Put the slice of bread in the trash"], "task_desc": "Slice bread, cook a slice, put it in the trash", "votes": [1, 1, 1]}, {"assignment_id": "A3C81THYYSBGVD_31HQ4X3T3VR2H81AM0BQRHVWCN8SLG", "high_descs": ["Turn left and face the counter in front of you.", "Take the knife from the counter in front of you.", "Turn left and face the bread on the counter to your right.", "Slice the bread in front of you on the counter.", "Turn around and face the drawer under the coffee maker.", "Open the drawer under the coffee maker, place the knife inside and close the drawer.", "Turn around and face the sliced bread on the counter.", "Take a slice of bread from the counter.", "Turn left, then right, and right again and face the microwave on your left.", "Heat up the slice of bread in the microwave, take it out and close the microwave.", "Turn left, then left again and face the blue recycling bin on your right.", "Place the slice of bread inside of the recycling bin in front of you."], "task_desc": "Place a hot slice of bread in the recycling bin.", "votes": [1, 1]}, {"assignment_id": "AISNLDPD2DFEG_34S9DKFK766P1UG4XJJUMVNMY3WNYJ", "high_descs": ["Turn left, go to the table", "Take the knife on the table", "Turn left, go forward a bit, turn right to face the table", "Slice the bread on the table", "Turn around to face the counter", "Open the top drawer, put the knife in the drawer, close the drawer", "Turn around to face the table", "Pick up a slice of bread", "Turn left, go forward a bit, turn right, go forward, turn right go forward, turn left to face the microwave", "Open the microwave, put the bread in it, close the microwave, start the microwave, open the microwave, get the bread, close the microwave", "Turn left, go forward, turn left, go forward, turn left, go forward, turn right to face the trash", "Put the bread in the trash"], "task_desc": "Slice the bread, cook a slice, put it in the trash", "votes": [1, 1]}, {"assignment_id": "A3PPLDHC3CG0YN_3VAR3R6G1SIS49PYLULD27ZPVYNO8A", "high_descs": ["Turn left and face the counter with the loaf of bread. ", "Pick up the knife next to the bowl. ", "Move in front of the loaf of bread. ", "Slice half of the loaf of bread. ", "Turn around to face the coffee maker. ", "Open the drawer below the coffee maker, place the knife in the drawer on the right side, close the drawer. ", "Turn around to face the loaf of bread. ", "Pick up a slice of bread. ", "Walk around the island to the microwave on the left. ", "Open the microwave door, place the bread in the microwave next to the apple, close the door, heat the bread, take the bread out of the microwave, and close the door. ", "Turn left and walk around the island to the blue recycling bin on the right. ", "Place the heated slice of bread in the recycle bin. "], "task_desc": "Heat a piece of bread for the recycle bin. ", "votes": [1, 1]}, {"assignment_id": "AKW57KYG90X61_34QN5IT0T2871SXFJ9J2C637X3U807", "high_descs": ["turn around towards the table", "pick up the knife on the table", "turn left towards the bread", "slice the bread on the table", "turn around towards the drawer", "drop the knife inside the drawer", "turn around towards the sliced bread", "pick up a slice from the bread", "move forward and locate the microwave", "place the bread inside the microwave to warm and take it out", "turn around and head for the bin", "trash the bread in the bin"], "task_desc": "trash a hot bread slice", "votes": [1, 0, 1]}, {"assignment_id": "A3HL2LL0LEPZT8_39ASUFLU60OW7JDHZOU9DUM4IEEXET", "high_descs": ["Turn left, go to the kitchen island counter.", "Take the knife from the counter.", "Go to the left and face the bread on the counter.", "Cut the bread into slices.", "Turn around, go in front of the coffee machine on the counter.", "Put the knife in the right side of the drawer underneath the coffee machine counter.", "Turn around, go to the bread that was sliced.", "Take a slice of bread from the counter.", "Turn left, go past the island then turn right, go forward to the oven, turn right, go forward to the wall, turn left, go to the microwave on the counter.", "Heat the bread in the microwave, take the bread from the microwave.", "Turn left, go forward to the oven, turn left, go forward, turn left after passing the island's end, go forward to the wall, turn right, go to the blue bin.", "Put the bread in the blue bin."], "task_desc": "Put a hot slice of bread in a bin.", "votes": [0, 1, 1]}]}}