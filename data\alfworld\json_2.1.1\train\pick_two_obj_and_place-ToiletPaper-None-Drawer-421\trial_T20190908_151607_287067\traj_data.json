{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 36}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "ToiletPaper", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|9|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-1.671624184, -1.671624184, 9.40182496, 9.40182496, 3.236, 3.236]], "coordinateReceptacleObjectId": ["CounterTop", [-1.4, -1.4, 5.814656, 5.814656, 0.02, 0.02]], "forceVisible": true, "objectId": "ToiletPaper|-00.42|+00.81|+02.35"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|9|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-1.671624184, -1.671624184, 9.40182496, 9.40182496, 3.236, 3.236]], "coordinateReceptacleObjectId": ["Drawer", [-1.2763008, -1.2763008, 7.91576768, 7.91576768, 2.4632, 2.4632]], "forceVisible": true, "objectId": "ToiletPaper|-00.42|+00.81|+02.35", "receptacleObjectId": "Drawer|-00.32|+00.62|+01.98"}}, {"discrete_action": {"action": "GotoLocation", "args": ["toiletpaperhanger"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|14|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-4.6264, -4.6264, 15.70399952, 15.70399952, 3.588, 3.588]], "coordinateReceptacleObjectId": ["ToiletPaperHanger", [-4.804, -4.804, 16.0, 16.0, 3.588, 3.588]], "forceVisible": true, "objectId": "ToiletPaper|-01.16|+00.90|+03.93"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-5|9|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-4.6264, -4.6264, 15.70399952, 15.70399952, 3.588, 3.588]], "coordinateReceptacleObjectId": ["Drawer", [-1.2763008, -1.2763008, 7.91576768, 7.91576768, 2.4632, 2.4632]], "forceVisible": true, "objectId": "ToiletPaper|-01.16|+00.90|+03.93", "receptacleObjectId": "Drawer|-00.32|+00.62|+01.98"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-00.42|+00.81|+02.35"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [114, 92, 126, 121], "mask": [[27417, 5], [27715, 8], [28015, 9], [28314, 10], [28614, 10], [28914, 10], [29214, 11], [29514, 11], [29815, 10], [30115, 10], [30415, 10], [30715, 10], [31015, 10], [31316, 10], [31616, 10], [31916, 10], [32216, 10], [32516, 10], [32817, 9], [33117, 9], [33417, 10], [33717, 10], [34017, 10], [34317, 10], [34618, 9], [34918, 9], [35218, 9], [35518, 9], [35818, 8], [36120, 5]], "point": [120, 105]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-00.32|+00.62|+01.98"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [154, 171, 277, 210], "mask": [[51154, 123], [51454, 124], [51754, 123], [52054, 123], [52354, 122], [52654, 122], [52954, 121], [53254, 121], [53554, 120], [53854, 120], [54154, 119], [54454, 119], [54754, 118], [55054, 117], [55354, 117], [55654, 116], [55954, 116], [56254, 115], [56554, 115], [56854, 114], [57154, 114], [57454, 113], [57754, 113], [58054, 112], [58354, 112], [58654, 111], [58954, 111], [59254, 110], [59554, 109], [59854, 109], [60154, 108], [60454, 108], [60754, 107], [61054, 107], [61354, 106], [61654, 106], [61954, 105], [62254, 105], [62554, 104], [62854, 104]], "point": [215, 189]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-00.42|+00.81|+02.35", "placeStationary": true, "receptacleObjectId": "Drawer|-00.32|+00.62|+01.98"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [154, 171, 299, 286], "mask": [[51158, 112], [51458, 112], [51758, 113], [52058, 113], [52357, 115], [52657, 115], [52957, 116], [53257, 116], [53557, 116], [53857, 117], [54157, 117], [54457, 118], [54757, 118], [55057, 119], [55357, 119], [55657, 119], [55957, 120], [56257, 120], [56557, 121], [56857, 121], [57157, 122], [57457, 122], [57757, 122], [58057, 123], [58357, 123], [58657, 124], [58957, 124], [59257, 125], [59558, 124], [59858, 124], [60158, 125], [60458, 125], [60758, 126], [61058, 126], [61358, 127], [61658, 127], [61958, 127], [62258, 128], [62558, 128], [62858, 129], [63158, 129], [63458, 130], [63758, 130], [64058, 130], [64358, 131], [64658, 131], [64958, 132], [65258, 132], [65558, 133], [65858, 133], [66158, 133], [66458, 134], [66758, 134], [67058, 135], [67358, 135], [67658, 136], [67958, 136], [68258, 136], [68558, 137], [68858, 137], [69159, 137], [69461, 135], [69762, 135], [70062, 135], [70363, 134], [70663, 135], [70964, 134], [71265, 134], [71565, 134], [71866, 134], [72166, 134], [72466, 134], [72766, 134], [73066, 134], [73366, 134], [73666, 134], [73966, 134], [74266, 134], [74566, 134], [74866, 134], [75155, 1], [75165, 135], [75455, 2], [75465, 135], [75755, 3], [75765, 135], [76055, 3], [76065, 135], [76355, 4], [76364, 136], [76655, 4], [76664, 136], [76955, 3], [76964, 136], [77255, 2], [77263, 137], [77563, 137], [77863, 137], [78162, 138], [78462, 138], [78762, 138], [79061, 139], [79361, 139], [79660, 140], [79960, 140], [80259, 141], [80558, 142], [80857, 143], [81156, 144], [81455, 144], [81755, 143], [82055, 142], [82355, 141], [82655, 140], [82954, 140], [83254, 139], [83554, 138], [83854, 137], [84154, 136], [84454, 135], [84754, 134], [85054, 133], [85354, 131], [85654, 130]], "point": [226, 227]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-00.32|+00.62|+01.98"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [154, 171, 299, 286], "mask": [[51158, 112], [51458, 112], [51758, 113], [52058, 113], [52357, 25], [52386, 86], [52657, 24], [52687, 85], [52957, 24], [52988, 85], [53257, 23], [53288, 85], [53557, 23], [53588, 85], [53857, 23], [53888, 86], [54157, 23], [54188, 86], [54457, 23], [54487, 88], [54757, 23], [54787, 88], [55057, 23], [55087, 89], [55357, 23], [55387, 89], [55657, 22], [55687, 89], [55957, 22], [55987, 90], [56257, 22], [56286, 91], [56557, 22], [56586, 92], [56857, 22], [56886, 92], [57157, 22], [57186, 93], [57457, 22], [57486, 93], [57757, 22], [57786, 93], [58057, 22], [58085, 95], [58357, 23], [58384, 96], [58657, 124], [58957, 124], [59257, 125], [59558, 124], [59858, 124], [60158, 125], [60458, 125], [60758, 126], [61058, 126], [61358, 127], [61658, 127], [61958, 127], [62258, 128], [62558, 128], [62858, 129], [63158, 129], [63458, 130], [63758, 130], [64058, 130], [64358, 131], [64658, 131], [64958, 132], [65258, 132], [65558, 133], [65858, 133], [66158, 133], [66458, 134], [66758, 134], [67058, 135], [67358, 135], [67658, 136], [67958, 136], [68258, 136], [68558, 137], [68858, 137], [69158, 138], [69458, 138], [69758, 139], [70058, 139], [70358, 139], [70658, 140], [70958, 140], [71258, 141], [71558, 141], [71859, 141], [72159, 141], [72459, 141], [72759, 141], [73059, 141], [73359, 141], [73659, 141], [73959, 141], [74259, 141], [74559, 141], [74855, 145], [75155, 145], [75455, 145], [75755, 145], [76055, 145], [76355, 145], [76655, 145], [76955, 145], [77255, 145], [77555, 145], [77855, 145], [78155, 145], [78455, 145], [78755, 145], [79055, 145], [79355, 145], [79655, 145], [79955, 145], [80255, 145], [80555, 145], [80855, 145], [81155, 145], [81455, 144], [81755, 143], [82055, 142], [82355, 141], [82655, 140], [82954, 140], [83254, 139], [83554, 138], [83854, 137], [84154, 136], [84454, 135], [84754, 134], [85054, 133], [85354, 131], [85654, 130]], "point": [226, 227]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-01.16|+00.90|+03.93"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [179, 125, 223, 157], "mask": [[37381, 39], [37680, 42], [37980, 42], [38280, 42], [38580, 43], [38880, 43], [39180, 43], [39479, 44], [39779, 45], [40079, 45], [40379, 45], [40679, 45], [40979, 45], [41279, 45], [41581, 43], [41881, 43], [42181, 43], [42481, 43], [42781, 43], [43081, 43], [43379, 45], [43679, 45], [43979, 45], [44279, 44], [44579, 44], [44879, 44], [45179, 44], [45480, 43], [45780, 42], [46080, 42], [46380, 42], [46680, 41], [46981, 39]], "point": [201, 140]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-00.32|+00.62|+01.98"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [154, 171, 277, 210], "mask": [[51154, 123], [51454, 124], [51754, 123], [52054, 123], [52354, 122], [52654, 122], [52954, 121], [53254, 121], [53554, 120], [53854, 120], [54154, 119], [54454, 119], [54754, 118], [55054, 117], [55354, 117], [55654, 116], [55954, 116], [56254, 115], [56554, 115], [56854, 114], [57154, 114], [57454, 113], [57754, 113], [58054, 112], [58354, 112], [58654, 111], [58954, 111], [59255, 109], [59557, 106], [59859, 104], [60162, 100], [60464, 98], [60767, 94], [61069, 92], [61372, 88], [61674, 86], [61976, 83], [62279, 80], [62581, 77], [62881, 77]], "point": [215, 189]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-01.16|+00.90|+03.93", "placeStationary": true, "receptacleObjectId": "Drawer|-00.32|+00.62|+01.98"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [155, 171, 299, 286], "mask": [[51158, 112], [51458, 112], [51758, 113], [52058, 113], [52357, 25], [52386, 86], [52657, 24], [52687, 85], [52957, 24], [52988, 85], [53257, 23], [53288, 85], [53557, 23], [53588, 85], [53857, 23], [53888, 86], [54157, 23], [54188, 86], [54457, 23], [54487, 88], [54757, 23], [54787, 88], [55057, 23], [55087, 89], [55357, 23], [55387, 89], [55657, 22], [55687, 89], [55957, 22], [55987, 90], [56257, 22], [56286, 91], [56557, 22], [56586, 92], [56857, 22], [56886, 92], [57157, 22], [57186, 93], [57457, 22], [57486, 93], [57757, 22], [57786, 93], [58057, 22], [58085, 95], [58357, 23], [58384, 96], [58657, 124], [58957, 124], [59257, 125], [59558, 124], [59859, 123], [60162, 121], [60464, 119], [60767, 117], [61069, 115], [61372, 113], [61674, 111], [61976, 109], [62279, 107], [62581, 105], [62881, 106], [63182, 105], [63483, 105], [63783, 105], [64084, 104], [64385, 104], [64685, 104], [64986, 104], [65287, 103], [65587, 104], [65888, 103], [66188, 103], [66489, 103], [66790, 102], [67090, 103], [67391, 102], [67692, 102], [67992, 102], [68293, 101], [68594, 101], [68894, 101], [69195, 101], [69496, 100], [69796, 101], [70097, 100], [70397, 100], [70697, 101], [70997, 101], [71297, 102], [71597, 102], [71897, 103], [72197, 103], [72497, 103], [72797, 103], [73097, 103], [73397, 103], [73697, 103], [73997, 103], [74297, 103], [74597, 103], [74897, 103], [75155, 1], [75197, 103], [75455, 2], [75497, 103], [75755, 3], [75797, 103], [76055, 3], [76097, 103], [76355, 4], [76397, 103], [76655, 4], [76697, 103], [76955, 3], [76997, 103], [77255, 2], [77297, 103], [77597, 103], [77897, 103], [78197, 103], [78497, 103], [78797, 103], [79097, 103], [79397, 103], [79697, 103], [79996, 104], [80295, 105], [80594, 106], [80894, 106], [81193, 107], [81492, 107], [81791, 107], [82091, 106], [82390, 106], [82689, 106], [82989, 105], [83288, 105], [83587, 105], [83886, 105], [84186, 104], [84485, 104], [84784, 104], [85083, 104], [85383, 102], [85682, 102]], "point": [227, 227]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-00.32|+00.62|+01.98"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [154, 171, 299, 286], "mask": [[51158, 43], [51213, 57], [51458, 41], [51515, 55], [51758, 40], [51816, 55], [52058, 40], [52117, 54], [52357, 25], [52386, 12], [52418, 54], [52657, 24], [52687, 10], [52718, 54], [52957, 24], [52988, 9], [53019, 54], [53257, 23], [53288, 9], [53319, 54], [53557, 23], [53588, 8], [53619, 54], [53857, 23], [53888, 8], [53919, 55], [54157, 23], [54188, 8], [54219, 55], [54457, 23], [54487, 9], [54518, 57], [54757, 23], [54787, 9], [54818, 57], [55057, 23], [55087, 8], [55118, 58], [55357, 23], [55387, 8], [55417, 59], [55657, 22], [55687, 8], [55717, 59], [55957, 22], [55987, 8], [56017, 60], [56257, 22], [56286, 9], [56317, 60], [56557, 22], [56586, 8], [56616, 62], [56857, 22], [56886, 8], [56916, 62], [57157, 22], [57186, 8], [57216, 63], [57457, 22], [57486, 8], [57515, 64], [57757, 22], [57786, 8], [57815, 64], [58057, 22], [58085, 10], [58115, 65], [58357, 23], [58384, 11], [58414, 66], [58657, 39], [58714, 67], [58957, 39], [59014, 67], [59257, 40], [59314, 68], [59558, 41], [59612, 70], [59858, 42], [59911, 71], [60158, 47], [60209, 74], [60458, 125], [60758, 126], [61058, 126], [61358, 127], [61658, 127], [61958, 127], [62258, 128], [62558, 128], [62858, 129], [63158, 129], [63458, 130], [63758, 130], [64058, 130], [64358, 131], [64658, 131], [64958, 132], [65258, 132], [65558, 133], [65858, 133], [66158, 133], [66458, 134], [66758, 134], [67058, 135], [67358, 135], [67658, 136], [67958, 136], [68258, 136], [68558, 137], [68858, 137], [69158, 138], [69458, 138], [69758, 139], [70058, 139], [70358, 139], [70658, 140], [70958, 140], [71258, 141], [71558, 141], [71859, 141], [72159, 141], [72459, 141], [72759, 141], [73059, 141], [73359, 141], [73659, 141], [73959, 141], [74259, 141], [74559, 141], [74855, 145], [75155, 145], [75455, 145], [75755, 145], [76055, 145], [76355, 145], [76655, 145], [76955, 145], [77255, 145], [77555, 145], [77855, 145], [78155, 145], [78455, 145], [78755, 145], [79055, 145], [79355, 145], [79655, 145], [79955, 145], [80255, 145], [80555, 145], [80855, 145], [81155, 145], [81455, 144], [81755, 143], [82055, 142], [82355, 141], [82655, 140], [82954, 140], [83254, 139], [83554, 138], [83854, 137], [84154, 136], [84454, 135], [84754, 134], [85054, 133], [85354, 131], [85654, 130]], "point": [226, 227]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan421", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.5, "y": 0.9008256, "z": 2.0}, "object_poses": [{"objectName": "SoapBar_0fd13d7a", "position": {"x": -0.331843555, "y": 0.8284542, "z": 2.656924}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_f6c37fb2", "position": {"x": -0.03691358, "y": 1.658, "z": 3.44300032}, "rotation": {"x": 0.0, "y": 90.0004, "z": 0.0}}, {"objectName": "SoapBar_0fd13d7a", "position": {"x": -0.299715579, "y": 0.8284542, "z": 1.82074}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_335b3797", "position": {"x": -1.1566, "y": 0.897, "z": 3.92599988}, "rotation": {"x": 0.0, "y": 180.000046, "z": 90.0}}, {"objectName": "Plunger_a5ac7169", "position": {"x": -0.139841557, "y": -0.0006361902, "z": 3.369947}, "rotation": {"x": -0.00118185522, "y": 0.0004360497, "z": 0.000781859}}, {"objectName": "Candle_1814f8bc", "position": {"x": -0.470215231, "y": 0.510781348, "z": 2.84627438}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_5484beaa", "position": {"x": -0.13493225, "y": 0.9545255, "z": 3.78399658}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_dab0d4d7", "position": {"x": -0.417906046, "y": 0.809, "z": 2.35045624}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_55833eba", "position": {"x": -2.146, "y": 0.0575833656, "z": 3.81893826}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Towel_e5bc324c", "position": {"x": -1.106, "y": 1.151, "z": 1.28995311}, "rotation": {"x": 0.0, "y": 1.70754731e-06, "z": 0.0}}, {"objectName": "Cloth_c4a4c4ad", "position": {"x": -0.358357877, "y": 0.205373645, "z": 1.92337871}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ScrubBrush_274c0193", "position": {"x": -0.346180737, "y": -0.000173203647, "z": 3.34195137}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_862832c1", "position": {"x": -0.188516587, "y": 0.954391062, "z": 3.658998}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3425199427, "scene_num": 421}, "task_id": "trial_T20190908_151607_287067", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A98E8M4QLI9RS_324G5B4FB6ZBM3EU5CGDZSREG7J70N", "high_descs": ["turn around and move to the counter between the two sinks", "pick up the empty toilet paper rolls from the counter", "move back slightly from the counter where the two sinks are to face the drawer on the right", "open the drawer to the right, put the empty toilet paper roll in to the drawer and close the drawer", "turn to the left and move forward to the toilet which will be on the right", "remove the toilet paper roll from the toilet paper holder attached to the wall", "turn around and move forward to the counter with the sinks on the left, stop at the counter between the two sinks and turn to face the counter", "open the drawer on the right and put the toilet paper roll in to the drawer to the right of the empty toilet paper roll"], "task_desc": "put a roll of toilet paper along with an empty toilet paper roll in to the drawer", "votes": [1, 1]}, {"assignment_id": "A2KAGFQU28JY43_3EQHHY4HQVJTA3BWVLQJM00YWHTG5Y", "high_descs": ["Turn around and go to the counter space between the two sinks, across from you. ", "Pick up the empty toilet paper roll, on the counter top, between the two sinks. ", "Step back from the sink and to the right so that you are in front of the right drawers under the counter. ", "Place the empty toilet paper roll in the top, right drawer, under the counter. ", "Turn to your left and go to the toilet paper holder on the wall across from you. ", "Remove the toilet paper from the toilet paper holder on the wall. ", "Turn around and go to the drawers closest to the right sink, on your left.", "Place the roll of toilet paper in the top drawer, under the counter, to the right of the empty toilet paper roll. "], "task_desc": "Put two rolls of toilet paper in the bathroom drawer. ", "votes": [1, 1]}, {"assignment_id": "A24RGLS3BRZ60J_3ZR9AIQJUE0E3GWB3EI6WG74P4F04G", "high_descs": ["Turn around and face the sinks.", "Pick up the empty roll from the center of the two sink.s", "Carry the roll and take a step to the right.", "Open the top right drawer and place the roll inside.", "Turn to the left to face the toilet paper on the wall.", "Take the toilet paper off of the wall holder.", "Carry the toilet paper and turn around. Face the sinks on the left.", "Open the top right drawer and place the toilet paper inside. Shut the drawer."], "task_desc": "Move an empty roll and roll of toilet paper to the drawer.", "votes": [1, 1]}]}}