
(define (problem plan_trial_T20190908_140940_290533)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Book_bar__plus_00_dot_12_bar__plus_00_dot_90_bar__plus_01_dot_07 - object
        Box_bar__minus_00_dot_22_bar__plus_00_dot_73_bar__minus_02_dot_14 - object
        CellPhone_bar__plus_03_dot_10_bar__plus_00_dot_08_bar__plus_01_dot_45 - object
        CellPhone_bar__minus_00_dot_30_bar__plus_00_dot_91_bar__plus_00_dot_97 - object
        Chair_bar__plus_01_dot_05_bar__plus_00_dot_00_bar__minus_02_dot_31 - object
        Chair_bar__plus_01_dot_63_bar__plus_00_dot_02_bar__plus_02_dot_21 - object
        Chair_bar__plus_02_dot_04_bar__plus_00_dot_00_bar__minus_02_dot_35 - object
        Chair_bar__plus_03_dot_14_bar__plus_00_dot_02_bar__minus_01_dot_63 - object
        CreditCard_bar__minus_00_dot_01_bar__plus_00_dot_54_bar__minus_02_dot_09 - object
        CreditCard_bar__minus_00_dot_02_bar__plus_00_dot_91_bar__plus_02_dot_19 - object
        DeskLamp_bar__plus_03_dot_14_bar__plus_00_dot_59_bar__plus_01_dot_47 - object
        FloorLamp_bar__plus_03_dot_10_bar__plus_00_dot_02_bar__minus_02_dot_34 - object
        HousePlant_bar__plus_00_dot_49_bar__plus_00_dot_87_bar__minus_01_dot_58 - object
        KeyChain_bar__plus_03_dot_08_bar__plus_00_dot_59_bar__plus_01_dot_20 - object
        Laptop_bar__plus_00_dot_07_bar__plus_00_dot_91_bar__plus_02_dot_43 - object
        LightSwitch_bar__minus_00_dot_93_bar__plus_01_dot_32_bar__plus_02_dot_85 - object
        Newspaper_bar__minus_02_dot_81_bar__plus_00_dot_57_bar__plus_02_dot_01 - object
        Painting_bar__plus_03_dot_37_bar__plus_01_dot_79_bar__minus_00_dot_95 - object
        Painting_bar__minus_01_dot_56_bar__plus_01_dot_41_bar__minus_02_dot_32 - object
        Painting_bar__minus_03_dot_49_bar__plus_01_dot_39_bar__minus_00_dot_51 - object
        Pillow_bar__minus_02_dot_89_bar__plus_00_dot_62_bar__plus_00_dot_82 - object
        RemoteControl_bar__plus_03_dot_16_bar__plus_00_dot_59_bar__plus_00_dot_50 - object
        RemoteControl_bar__minus_02_dot_96_bar__plus_00_dot_56_bar__plus_01_dot_81 - object
        Statue_bar__minus_00_dot_20_bar__plus_00_dot_91_bar__plus_01_dot_22 - object
        Statue_bar__minus_03_dot_26_bar__plus_00_dot_60_bar__minus_00_dot_49 - object
        Television_bar__plus_00_dot_07_bar__plus_01_dot_39_bar__plus_01_dot_70 - object
        Vase_bar__minus_01_dot_05_bar__plus_01_dot_20_bar__minus_02_dot_15 - object
        Vase_bar__minus_02_dot_17_bar__plus_01_dot_20_bar__minus_02_dot_18 - object
        WateringCan_bar__plus_02_dot_89_bar__plus_00_dot_01_bar__minus_01_dot_92 - object
        Window_bar__plus_01_dot_13_bar__plus_01_dot_22_bar__minus_02_dot_74 - object
        Window_bar__plus_02_dot_01_bar__plus_01_dot_22_bar__minus_02_dot_74 - object
        Window_bar__plus_02_dot_90_bar__plus_01_dot_22_bar__minus_02_dot_74 - object
        Window_bar__minus_00_dot_19_bar__plus_01_dot_16_bar__minus_02_dot_56 - object
        Window_bar__minus_02_dot_97_bar__plus_01_dot_16_bar__minus_02_dot_56 - object
        Window_bar__minus_03_dot_37_bar__plus_01_dot_18_bar__minus_01_dot_54 - object
        ArmChair_bar__minus_02_dot_86_bar__plus_00_dot_02_bar__minus_02_dot_04 - receptacle
        Cabinet_bar__plus_00_dot_33_bar__plus_00_dot_51_bar__minus_02_dot_02 - receptacle
        Drawer_bar__plus_02_dot_99_bar__plus_00_dot_09_bar__plus_00_dot_37 - receptacle
        Drawer_bar__plus_02_dot_99_bar__plus_00_dot_09_bar__plus_01_dot_40 - receptacle
        Drawer_bar__plus_02_dot_99_bar__plus_00_dot_09_bar__minus_00_dot_57 - receptacle
        Drawer_bar__plus_02_dot_99_bar__plus_00_dot_34_bar__plus_00_dot_37 - receptacle
        Drawer_bar__plus_02_dot_99_bar__plus_00_dot_34_bar__plus_01_dot_40 - receptacle
        Drawer_bar__plus_02_dot_99_bar__plus_00_dot_34_bar__minus_00_dot_57 - receptacle
        Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_00_dot_99 - receptacle
        Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_01_dot_47 - receptacle
        Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_01_dot_94 - receptacle
        Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_02_dot_42 - receptacle
        Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_00_dot_99 - receptacle
        Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_01_dot_47 - receptacle
        Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_01_dot_94 - receptacle
        Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_02_dot_42 - receptacle
        Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_00_dot_99 - receptacle
        Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_01_dot_47 - receptacle
        Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_01_dot_94 - receptacle
        Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_02_dot_42 - receptacle
        Drawer_bar__minus_00_dot_17_bar__plus_00_dot_45_bar__minus_02_dot_04 - receptacle
        Drawer_bar__minus_03_dot_13_bar__plus_00_dot_09_bar__minus_00_dot_49 - receptacle
        Drawer_bar__minus_03_dot_13_bar__plus_00_dot_34_bar__minus_00_dot_49 - receptacle
        Dresser_bar__minus_00_dot_02_bar__plus_00_dot_01_bar__plus_01_dot_72 - receptacle
        GarbageCan_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__plus_00_dot_89 - receptacle
        SideTable_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__plus_00_dot_37 - receptacle
        SideTable_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__plus_01_dot_40 - receptacle
        SideTable_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__minus_00_dot_57 - receptacle
        SideTable_bar__minus_00_dot_17_bar__plus_00_dot_01_bar__minus_02_dot_24 - receptacle
        SideTable_bar__minus_03_dot_30_bar__plus_00_dot_02_bar__minus_00_dot_49 - receptacle
        Sofa_bar__minus_02_dot_96_bar__plus_00_dot_08_bar__plus_01_dot_39 - receptacle
        loc_bar_9_bar_6_bar_2_bar_60 - location
        loc_bar_10_bar__minus_7_bar_1_bar_60 - location
        loc_bar_10_bar_5_bar_2_bar_60 - location
        loc_bar_10_bar__minus_9_bar_3_bar_60 - location
        loc_bar__minus_1_bar__minus_7_bar_2_bar_30 - location
        loc_bar_11_bar_4_bar_1_bar_60 - location
        loc_bar__minus_12_bar__minus_5_bar_3_bar_60 - location
        loc_bar__minus_5_bar_2_bar_1_bar_60 - location
        loc_bar__minus_4_bar_5_bar_1_bar_45 - location
        loc_bar_4_bar__minus_6_bar_3_bar_60 - location
        loc_bar__minus_4_bar_9_bar_0_bar_30 - location
        loc_bar_10_bar__minus_8_bar_1_bar_60 - location
        loc_bar__minus_11_bar__minus_2_bar_3_bar_15 - location
        loc_bar__minus_10_bar_0_bar_3_bar_60 - location
        loc_bar_7_bar_7_bar_0_bar_60 - location
        loc_bar_11_bar_5_bar_1_bar_60 - location
        loc_bar_10_bar__minus_9_bar_1_bar_60 - location
        loc_bar__minus_5_bar_6_bar_1_bar_60 - location
        loc_bar__minus_5_bar_8_bar_1_bar_45 - location
        loc_bar__minus_2_bar__minus_7_bar_3_bar_45 - location
        loc_bar_11_bar__minus_4_bar_1_bar__minus_15 - location
        loc_bar_10_bar_2_bar_2_bar_45 - location
        loc_bar_10_bar__minus_7_bar_0_bar_60 - location
        loc_bar__minus_4_bar_7_bar_1_bar_45 - location
        loc_bar__minus_7_bar_6_bar_3_bar_60 - location
        loc_bar__minus_5_bar_8_bar_1_bar_60 - location
        loc_bar__minus_4_bar_8_bar_1_bar_45 - location
        loc_bar__minus_5_bar_4_bar_1_bar_45 - location
        loc_bar__minus_12_bar__minus_5_bar_2_bar_15 - location
        loc_bar__minus_10_bar__minus_6_bar_2_bar_30 - location
        loc_bar__minus_3_bar__minus_5_bar_2_bar_60 - location
        loc_bar_10_bar__minus_9_bar_3_bar_45 - location
        loc_bar_0_bar__minus_7_bar_1_bar_60 - location
        loc_bar_11_bar_2_bar_0_bar_60 - location
        loc_bar_10_bar__minus_9_bar_2_bar_45 - location
        loc_bar_11_bar__minus_3_bar_0_bar_60 - location
        loc_bar_4_bar__minus_7_bar_2_bar_60 - location
        loc_bar__minus_4_bar_9_bar_1_bar_45 - location
        loc_bar__minus_5_bar_6_bar_1_bar_45 - location
        loc_bar_9_bar_2_bar_2_bar_60 - location
        loc_bar_6_bar__minus_8_bar_2_bar_30 - location
        loc_bar__minus_10_bar__minus_6_bar_0_bar_60 - location
        loc_bar_0_bar__minus_4_bar_2_bar_45 - location
        loc_bar__minus_6_bar__minus_5_bar_2_bar_15 - location
        loc_bar__minus_5_bar_2_bar_1_bar_45 - location
        loc_bar__minus_3_bar_8_bar_1_bar_60 - location
        loc_bar__minus_9_bar__minus_3_bar_2_bar_45 - location
        loc_bar__minus_5_bar_1_bar_1_bar_30 - location
        )
    

(:init


        (receptacleType SideTable_bar__minus_00_dot_17_bar__plus_00_dot_01_bar__minus_02_dot_24 SideTableType)
        (receptacleType Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_01_dot_94 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_02_dot_42 DrawerType)
        (receptacleType ArmChair_bar__minus_02_dot_86_bar__plus_00_dot_02_bar__minus_02_dot_04 ArmChairType)
        (receptacleType Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_02_dot_42 DrawerType)
        (receptacleType GarbageCan_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__plus_00_dot_89 GarbageCanType)
        (receptacleType Sofa_bar__minus_02_dot_96_bar__plus_00_dot_08_bar__plus_01_dot_39 SofaType)
        (receptacleType Drawer_bar__minus_03_dot_13_bar__plus_00_dot_34_bar__minus_00_dot_49 DrawerType)
        (receptacleType Drawer_bar__plus_02_dot_99_bar__plus_00_dot_09_bar__plus_01_dot_40 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_01_dot_47 DrawerType)
        (receptacleType Dresser_bar__minus_00_dot_02_bar__plus_00_dot_01_bar__plus_01_dot_72 DresserType)
        (receptacleType Drawer_bar__plus_02_dot_99_bar__plus_00_dot_09_bar__minus_00_dot_57 DrawerType)
        (receptacleType SideTable_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__plus_01_dot_40 SideTableType)
        (receptacleType Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_00_dot_99 DrawerType)
        (receptacleType Drawer_bar__minus_03_dot_13_bar__plus_00_dot_09_bar__minus_00_dot_49 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_01_dot_94 DrawerType)
        (receptacleType Cabinet_bar__plus_00_dot_33_bar__plus_00_dot_51_bar__minus_02_dot_02 CabinetType)
        (receptacleType Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_00_dot_99 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_17_bar__plus_00_dot_45_bar__minus_02_dot_04 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_01_dot_47 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_01_dot_94 DrawerType)
        (receptacleType Drawer_bar__plus_02_dot_99_bar__plus_00_dot_09_bar__plus_00_dot_37 DrawerType)
        (receptacleType SideTable_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__plus_00_dot_37 SideTableType)
        (receptacleType Drawer_bar__plus_02_dot_99_bar__plus_00_dot_34_bar__plus_00_dot_37 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_01_dot_47 DrawerType)
        (receptacleType SideTable_bar__minus_03_dot_30_bar__plus_00_dot_02_bar__minus_00_dot_49 SideTableType)
        (receptacleType Drawer_bar__plus_02_dot_99_bar__plus_00_dot_34_bar__minus_00_dot_57 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_02_dot_42 DrawerType)
        (receptacleType SideTable_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__minus_00_dot_57 SideTableType)
        (receptacleType Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_00_dot_99 DrawerType)
        (receptacleType Drawer_bar__plus_02_dot_99_bar__plus_00_dot_34_bar__plus_01_dot_40 DrawerType)
        (objectType CellPhone_bar__minus_00_dot_30_bar__plus_00_dot_91_bar__plus_00_dot_97 CellPhoneType)
        (objectType HousePlant_bar__plus_00_dot_49_bar__plus_00_dot_87_bar__minus_01_dot_58 HousePlantType)
        (objectType Chair_bar__plus_03_dot_14_bar__plus_00_dot_02_bar__minus_01_dot_63 ChairType)
        (objectType RemoteControl_bar__plus_03_dot_16_bar__plus_00_dot_59_bar__plus_00_dot_50 RemoteControlType)
        (objectType FloorLamp_bar__plus_03_dot_10_bar__plus_00_dot_02_bar__minus_02_dot_34 FloorLampType)
        (objectType WateringCan_bar__plus_02_dot_89_bar__plus_00_dot_01_bar__minus_01_dot_92 WateringCanType)
        (objectType DeskLamp_bar__plus_03_dot_14_bar__plus_00_dot_59_bar__plus_01_dot_47 DeskLampType)
        (objectType Window_bar__plus_01_dot_13_bar__plus_01_dot_22_bar__minus_02_dot_74 WindowType)
        (objectType Chair_bar__plus_01_dot_05_bar__plus_00_dot_00_bar__minus_02_dot_31 ChairType)
        (objectType Newspaper_bar__minus_02_dot_81_bar__plus_00_dot_57_bar__plus_02_dot_01 NewspaperType)
        (objectType Statue_bar__minus_03_dot_26_bar__plus_00_dot_60_bar__minus_00_dot_49 StatueType)
        (objectType LightSwitch_bar__minus_00_dot_93_bar__plus_01_dot_32_bar__plus_02_dot_85 LightSwitchType)
        (objectType Statue_bar__minus_00_dot_20_bar__plus_00_dot_91_bar__plus_01_dot_22 StatueType)
        (objectType Vase_bar__minus_01_dot_05_bar__plus_01_dot_20_bar__minus_02_dot_15 VaseType)
        (objectType Window_bar__minus_03_dot_37_bar__plus_01_dot_18_bar__minus_01_dot_54 WindowType)
        (objectType Window_bar__minus_02_dot_97_bar__plus_01_dot_16_bar__minus_02_dot_56 WindowType)
        (objectType CreditCard_bar__minus_00_dot_02_bar__plus_00_dot_91_bar__plus_02_dot_19 CreditCardType)
        (objectType Chair_bar__plus_01_dot_63_bar__plus_00_dot_02_bar__plus_02_dot_21 ChairType)
        (objectType KeyChain_bar__plus_03_dot_08_bar__plus_00_dot_59_bar__plus_01_dot_20 KeyChainType)
        (objectType Window_bar__plus_02_dot_01_bar__plus_01_dot_22_bar__minus_02_dot_74 WindowType)
        (objectType Book_bar__plus_00_dot_12_bar__plus_00_dot_90_bar__plus_01_dot_07 BookType)
        (objectType Painting_bar__minus_03_dot_49_bar__plus_01_dot_39_bar__minus_00_dot_51 PaintingType)
        (objectType CellPhone_bar__plus_03_dot_10_bar__plus_00_dot_08_bar__plus_01_dot_45 CellPhoneType)
        (objectType RemoteControl_bar__minus_02_dot_96_bar__plus_00_dot_56_bar__plus_01_dot_81 RemoteControlType)
        (objectType Chair_bar__plus_02_dot_04_bar__plus_00_dot_00_bar__minus_02_dot_35 ChairType)
        (objectType Window_bar__plus_02_dot_90_bar__plus_01_dot_22_bar__minus_02_dot_74 WindowType)
        (objectType Television_bar__plus_00_dot_07_bar__plus_01_dot_39_bar__plus_01_dot_70 TelevisionType)
        (objectType Painting_bar__plus_03_dot_37_bar__plus_01_dot_79_bar__minus_00_dot_95 PaintingType)
        (objectType Box_bar__minus_00_dot_22_bar__plus_00_dot_73_bar__minus_02_dot_14 BoxType)
        (objectType CreditCard_bar__minus_00_dot_01_bar__plus_00_dot_54_bar__minus_02_dot_09 CreditCardType)
        (objectType Window_bar__minus_00_dot_19_bar__plus_01_dot_16_bar__minus_02_dot_56 WindowType)
        (objectType Vase_bar__minus_02_dot_17_bar__plus_01_dot_20_bar__minus_02_dot_18 VaseType)
        (objectType Pillow_bar__minus_02_dot_89_bar__plus_00_dot_62_bar__plus_00_dot_82 PillowType)
        (objectType Painting_bar__minus_01_dot_56_bar__plus_01_dot_41_bar__minus_02_dot_32 PaintingType)
        (objectType Laptop_bar__plus_00_dot_07_bar__plus_00_dot_91_bar__plus_02_dot_43 LaptopType)
        (canContain SideTableType BookType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType CellPhoneType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain GarbageCanType NewspaperType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType BookType)
        (canContain SofaType CellPhoneType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DresserType BookType)
        (canContain DresserType NewspaperType)
        (canContain DresserType VaseType)
        (canContain DresserType BoxType)
        (canContain DresserType CellPhoneType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType LaptopType)
        (canContain DresserType RemoteControlType)
        (canContain DresserType StatueType)
        (canContain DresserType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain SideTableType BookType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain CabinetType BookType)
        (canContain CabinetType NewspaperType)
        (canContain CabinetType VaseType)
        (canContain CabinetType BoxType)
        (canContain CabinetType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain SideTableType BookType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain SideTableType BookType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain SideTableType BookType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType BookType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (pickupable CellPhone_bar__minus_00_dot_30_bar__plus_00_dot_91_bar__plus_00_dot_97)
        (pickupable RemoteControl_bar__plus_03_dot_16_bar__plus_00_dot_59_bar__plus_00_dot_50)
        (pickupable WateringCan_bar__plus_02_dot_89_bar__plus_00_dot_01_bar__minus_01_dot_92)
        (pickupable Newspaper_bar__minus_02_dot_81_bar__plus_00_dot_57_bar__plus_02_dot_01)
        (pickupable Statue_bar__minus_03_dot_26_bar__plus_00_dot_60_bar__minus_00_dot_49)
        (pickupable Statue_bar__minus_00_dot_20_bar__plus_00_dot_91_bar__plus_01_dot_22)
        (pickupable Vase_bar__minus_01_dot_05_bar__plus_01_dot_20_bar__minus_02_dot_15)
        (pickupable CreditCard_bar__minus_00_dot_02_bar__plus_00_dot_91_bar__plus_02_dot_19)
        (pickupable KeyChain_bar__plus_03_dot_08_bar__plus_00_dot_59_bar__plus_01_dot_20)
        (pickupable Book_bar__plus_00_dot_12_bar__plus_00_dot_90_bar__plus_01_dot_07)
        (pickupable CellPhone_bar__plus_03_dot_10_bar__plus_00_dot_08_bar__plus_01_dot_45)
        (pickupable RemoteControl_bar__minus_02_dot_96_bar__plus_00_dot_56_bar__plus_01_dot_81)
        (pickupable Box_bar__minus_00_dot_22_bar__plus_00_dot_73_bar__minus_02_dot_14)
        (pickupable CreditCard_bar__minus_00_dot_01_bar__plus_00_dot_54_bar__minus_02_dot_09)
        (pickupable Vase_bar__minus_02_dot_17_bar__plus_01_dot_20_bar__minus_02_dot_18)
        (pickupable Pillow_bar__minus_02_dot_89_bar__plus_00_dot_62_bar__plus_00_dot_82)
        (pickupable Laptop_bar__plus_00_dot_07_bar__plus_00_dot_91_bar__plus_02_dot_43)
        (isReceptacleObject Box_bar__minus_00_dot_22_bar__plus_00_dot_73_bar__minus_02_dot_14)
        (openable Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_01_dot_94)
        (openable Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_02_dot_42)
        (openable Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_02_dot_42)
        (openable Drawer_bar__minus_03_dot_13_bar__plus_00_dot_34_bar__minus_00_dot_49)
        (openable Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_01_dot_47)
        (openable Drawer_bar__plus_02_dot_99_bar__plus_00_dot_09_bar__minus_00_dot_57)
        (openable Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_00_dot_99)
        (openable Drawer_bar__minus_03_dot_13_bar__plus_00_dot_09_bar__minus_00_dot_49)
        (openable Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_01_dot_94)
        (openable Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_00_dot_99)
        (openable Drawer_bar__minus_00_dot_17_bar__plus_00_dot_45_bar__minus_02_dot_04)
        (openable Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_01_dot_47)
        (openable Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_01_dot_94)
        (openable Drawer_bar__plus_02_dot_99_bar__plus_00_dot_09_bar__plus_00_dot_37)
        (openable Drawer_bar__plus_02_dot_99_bar__plus_00_dot_34_bar__plus_00_dot_37)
        (openable Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_01_dot_47)
        (openable Drawer_bar__plus_02_dot_99_bar__plus_00_dot_34_bar__minus_00_dot_57)
        (openable Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_02_dot_42)
        (openable Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_00_dot_99)
        
        (atLocation agent1 loc_bar__minus_5_bar_1_bar_1_bar_30)
        
        
        
        
        
        
        
        (toggleable FloorLamp_bar__plus_03_dot_10_bar__plus_00_dot_02_bar__minus_02_dot_34)
        (toggleable DeskLamp_bar__plus_03_dot_14_bar__plus_00_dot_59_bar__plus_01_dot_47)
        
        
        
        
        (inReceptacle KeyChain_bar__plus_03_dot_08_bar__plus_00_dot_59_bar__plus_01_dot_20 SideTable_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__plus_01_dot_40)
        (inReceptacle DeskLamp_bar__plus_03_dot_14_bar__plus_00_dot_59_bar__plus_01_dot_47 SideTable_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__plus_01_dot_40)
        (inReceptacle Newspaper_bar__minus_02_dot_81_bar__plus_00_dot_57_bar__plus_02_dot_01 Sofa_bar__minus_02_dot_96_bar__plus_00_dot_08_bar__plus_01_dot_39)
        (inReceptacle RemoteControl_bar__minus_02_dot_96_bar__plus_00_dot_56_bar__plus_01_dot_81 Sofa_bar__minus_02_dot_96_bar__plus_00_dot_08_bar__plus_01_dot_39)
        (inReceptacle Pillow_bar__minus_02_dot_89_bar__plus_00_dot_62_bar__plus_00_dot_82 Sofa_bar__minus_02_dot_96_bar__plus_00_dot_08_bar__plus_01_dot_39)
        (inReceptacle CellPhone_bar__minus_00_dot_30_bar__plus_00_dot_91_bar__plus_00_dot_97 Dresser_bar__minus_00_dot_02_bar__plus_00_dot_01_bar__plus_01_dot_72)
        (inReceptacle CreditCard_bar__minus_00_dot_02_bar__plus_00_dot_91_bar__plus_02_dot_19 Dresser_bar__minus_00_dot_02_bar__plus_00_dot_01_bar__plus_01_dot_72)
        (inReceptacle Television_bar__plus_00_dot_07_bar__plus_01_dot_39_bar__plus_01_dot_70 Dresser_bar__minus_00_dot_02_bar__plus_00_dot_01_bar__plus_01_dot_72)
        (inReceptacle Statue_bar__minus_00_dot_20_bar__plus_00_dot_91_bar__plus_01_dot_22 Dresser_bar__minus_00_dot_02_bar__plus_00_dot_01_bar__plus_01_dot_72)
        (inReceptacle Book_bar__plus_00_dot_12_bar__plus_00_dot_90_bar__plus_01_dot_07 Dresser_bar__minus_00_dot_02_bar__plus_00_dot_01_bar__plus_01_dot_72)
        (inReceptacle Laptop_bar__plus_00_dot_07_bar__plus_00_dot_91_bar__plus_02_dot_43 Dresser_bar__minus_00_dot_02_bar__plus_00_dot_01_bar__plus_01_dot_72)
        (inReceptacle CellPhone_bar__plus_03_dot_10_bar__plus_00_dot_08_bar__plus_01_dot_45 Drawer_bar__plus_02_dot_99_bar__plus_00_dot_09_bar__plus_01_dot_40)
        (inReceptacle RemoteControl_bar__plus_03_dot_16_bar__plus_00_dot_59_bar__plus_00_dot_50 SideTable_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__plus_00_dot_37)
        (inReceptacle Statue_bar__minus_03_dot_26_bar__plus_00_dot_60_bar__minus_00_dot_49 SideTable_bar__minus_03_dot_30_bar__plus_00_dot_02_bar__minus_00_dot_49)
        (inReceptacle CreditCard_bar__minus_00_dot_01_bar__plus_00_dot_54_bar__minus_02_dot_09 SideTable_bar__minus_00_dot_17_bar__plus_00_dot_01_bar__minus_02_dot_24)
        (inReceptacle Box_bar__minus_00_dot_22_bar__plus_00_dot_73_bar__minus_02_dot_14 SideTable_bar__minus_00_dot_17_bar__plus_00_dot_01_bar__minus_02_dot_24)
        
        
        (receptacleAtLocation ArmChair_bar__minus_02_dot_86_bar__plus_00_dot_02_bar__minus_02_dot_04 loc_bar__minus_9_bar__minus_3_bar_2_bar_45)
        (receptacleAtLocation Cabinet_bar__plus_00_dot_33_bar__plus_00_dot_51_bar__minus_02_dot_02 loc_bar_0_bar__minus_7_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_02_dot_99_bar__plus_00_dot_09_bar__plus_00_dot_37 loc_bar_9_bar_6_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__plus_02_dot_99_bar__plus_00_dot_09_bar__plus_01_dot_40 loc_bar_11_bar_5_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_02_dot_99_bar__plus_00_dot_09_bar__minus_00_dot_57 loc_bar_9_bar_2_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__plus_02_dot_99_bar__plus_00_dot_34_bar__plus_00_dot_37 loc_bar_10_bar_5_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__plus_02_dot_99_bar__plus_00_dot_34_bar__plus_01_dot_40 loc_bar_11_bar_5_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_02_dot_99_bar__plus_00_dot_34_bar__minus_00_dot_57 loc_bar_10_bar_2_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_00_dot_99 loc_bar__minus_5_bar_2_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_01_dot_47 loc_bar__minus_5_bar_8_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_01_dot_94 loc_bar__minus_5_bar_6_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_16_bar__plus_00_dot_22_bar__plus_02_dot_42 loc_bar__minus_5_bar_8_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_00_dot_99 loc_bar__minus_5_bar_2_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_01_dot_47 loc_bar__minus_5_bar_4_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_01_dot_94 loc_bar__minus_5_bar_6_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_16_bar__plus_00_dot_47_bar__plus_02_dot_42 loc_bar__minus_5_bar_8_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_00_dot_99 loc_bar__minus_4_bar_5_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_01_dot_47 loc_bar__minus_4_bar_7_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_01_dot_94 loc_bar__minus_4_bar_9_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_16_bar__plus_00_dot_72_bar__plus_02_dot_42 loc_bar__minus_4_bar_8_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_17_bar__plus_00_dot_45_bar__minus_02_dot_04 loc_bar_0_bar__minus_4_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__minus_03_dot_13_bar__plus_00_dot_09_bar__minus_00_dot_49 loc_bar__minus_10_bar__minus_6_bar_0_bar_60)
        (receptacleAtLocation Drawer_bar__minus_03_dot_13_bar__plus_00_dot_34_bar__minus_00_dot_49 loc_bar__minus_10_bar__minus_6_bar_0_bar_60)
        (receptacleAtLocation Dresser_bar__minus_00_dot_02_bar__plus_00_dot_01_bar__plus_01_dot_72 loc_bar__minus_3_bar_8_bar_1_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__plus_00_dot_89 loc_bar_11_bar_4_bar_1_bar_60)
        (receptacleAtLocation SideTable_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__plus_00_dot_37 loc_bar_11_bar__minus_3_bar_0_bar_60)
        (receptacleAtLocation SideTable_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__plus_01_dot_40 loc_bar_11_bar_2_bar_0_bar_60)
        (receptacleAtLocation SideTable_bar__plus_03_dot_16_bar__plus_00_dot_02_bar__minus_00_dot_57 loc_bar_10_bar__minus_7_bar_0_bar_60)
        (receptacleAtLocation SideTable_bar__minus_00_dot_17_bar__plus_00_dot_01_bar__minus_02_dot_24 loc_bar__minus_3_bar__minus_5_bar_2_bar_60)
        (receptacleAtLocation SideTable_bar__minus_03_dot_30_bar__plus_00_dot_02_bar__minus_00_dot_49 loc_bar__minus_10_bar_0_bar_3_bar_60)
        (receptacleAtLocation Sofa_bar__minus_02_dot_96_bar__plus_00_dot_08_bar__plus_01_dot_39 loc_bar__minus_7_bar_6_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__minus_02_dot_96_bar__plus_00_dot_56_bar__plus_01_dot_81 loc_bar__minus_7_bar_6_bar_3_bar_60)
        (objectAtLocation CellPhone_bar__plus_03_dot_10_bar__plus_00_dot_08_bar__plus_01_dot_45 loc_bar_11_bar_5_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__minus_00_dot_01_bar__plus_00_dot_54_bar__minus_02_dot_09 loc_bar__minus_3_bar__minus_5_bar_2_bar_60)
        (objectAtLocation Statue_bar__minus_03_dot_26_bar__plus_00_dot_60_bar__minus_00_dot_49 loc_bar__minus_10_bar_0_bar_3_bar_60)
        (objectAtLocation Book_bar__plus_00_dot_12_bar__plus_00_dot_90_bar__plus_01_dot_07 loc_bar__minus_3_bar_8_bar_1_bar_60)
        (objectAtLocation Chair_bar__plus_01_dot_63_bar__plus_00_dot_02_bar__plus_02_dot_21 loc_bar_7_bar_7_bar_0_bar_60)
        (objectAtLocation Box_bar__minus_00_dot_22_bar__plus_00_dot_73_bar__minus_02_dot_14 loc_bar__minus_3_bar__minus_5_bar_2_bar_60)
        (objectAtLocation Chair_bar__plus_01_dot_05_bar__plus_00_dot_00_bar__minus_02_dot_31 loc_bar_4_bar__minus_7_bar_2_bar_60)
        (objectAtLocation Chair_bar__plus_03_dot_14_bar__plus_00_dot_02_bar__minus_01_dot_63 loc_bar_10_bar__minus_7_bar_1_bar_60)
        (objectAtLocation Chair_bar__plus_02_dot_04_bar__plus_00_dot_00_bar__minus_02_dot_35 loc_bar_10_bar__minus_9_bar_3_bar_60)
        (objectAtLocation Statue_bar__minus_00_dot_20_bar__plus_00_dot_91_bar__plus_01_dot_22 loc_bar__minus_3_bar_8_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__minus_00_dot_02_bar__plus_00_dot_91_bar__plus_02_dot_19 loc_bar__minus_3_bar_8_bar_1_bar_60)
        (objectAtLocation Laptop_bar__plus_00_dot_07_bar__plus_00_dot_91_bar__plus_02_dot_43 loc_bar__minus_3_bar_8_bar_1_bar_60)
        (objectAtLocation DeskLamp_bar__plus_03_dot_14_bar__plus_00_dot_59_bar__plus_01_dot_47 loc_bar_11_bar_2_bar_0_bar_60)
        (objectAtLocation LightSwitch_bar__minus_00_dot_93_bar__plus_01_dot_32_bar__plus_02_dot_85 loc_bar__minus_4_bar_9_bar_0_bar_30)
        (objectAtLocation WateringCan_bar__plus_02_dot_89_bar__plus_00_dot_01_bar__minus_01_dot_92 loc_bar_10_bar__minus_8_bar_1_bar_60)
        (objectAtLocation Newspaper_bar__minus_02_dot_81_bar__plus_00_dot_57_bar__plus_02_dot_01 loc_bar__minus_7_bar_6_bar_3_bar_60)
        (objectAtLocation Vase_bar__minus_02_dot_17_bar__plus_01_dot_20_bar__minus_02_dot_18 loc_bar__minus_10_bar__minus_6_bar_2_bar_30)
        (objectAtLocation CellPhone_bar__minus_00_dot_30_bar__plus_00_dot_91_bar__plus_00_dot_97 loc_bar__minus_3_bar_8_bar_1_bar_60)
        (objectAtLocation RemoteControl_bar__plus_03_dot_16_bar__plus_00_dot_59_bar__plus_00_dot_50 loc_bar_11_bar__minus_3_bar_0_bar_60)
        (objectAtLocation Vase_bar__minus_01_dot_05_bar__plus_01_dot_20_bar__minus_02_dot_15 loc_bar__minus_2_bar__minus_7_bar_3_bar_45)
        (objectAtLocation HousePlant_bar__plus_00_dot_49_bar__plus_00_dot_87_bar__minus_01_dot_58 loc_bar_4_bar__minus_6_bar_3_bar_60)
        (objectAtLocation Television_bar__plus_00_dot_07_bar__plus_01_dot_39_bar__plus_01_dot_70 loc_bar__minus_3_bar_8_bar_1_bar_60)
        (objectAtLocation Pillow_bar__minus_02_dot_89_bar__plus_00_dot_62_bar__plus_00_dot_82 loc_bar__minus_7_bar_6_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__plus_03_dot_08_bar__plus_00_dot_59_bar__plus_01_dot_20 loc_bar_11_bar_2_bar_0_bar_60)
        (objectAtLocation Painting_bar__minus_01_dot_56_bar__plus_01_dot_41_bar__minus_02_dot_32 loc_bar__minus_6_bar__minus_5_bar_2_bar_15)
        (objectAtLocation Painting_bar__plus_03_dot_37_bar__plus_01_dot_79_bar__minus_00_dot_95 loc_bar_11_bar__minus_4_bar_1_bar__minus_15)
        (objectAtLocation Painting_bar__minus_03_dot_49_bar__plus_01_dot_39_bar__minus_00_dot_51 loc_bar__minus_11_bar__minus_2_bar_3_bar_15)
        (objectAtLocation FloorLamp_bar__plus_03_dot_10_bar__plus_00_dot_02_bar__minus_02_dot_34 loc_bar_10_bar__minus_9_bar_1_bar_60)
        (objectAtLocation Window_bar__plus_01_dot_13_bar__plus_01_dot_22_bar__minus_02_dot_74 loc_bar_6_bar__minus_8_bar_2_bar_30)
        (objectAtLocation Window_bar__minus_03_dot_37_bar__plus_01_dot_18_bar__minus_01_dot_54 loc_bar__minus_12_bar__minus_5_bar_3_bar_60)
        (objectAtLocation Window_bar__plus_02_dot_90_bar__plus_01_dot_22_bar__minus_02_dot_74 loc_bar_10_bar__minus_9_bar_2_bar_45)
        (objectAtLocation Window_bar__plus_02_dot_01_bar__plus_01_dot_22_bar__minus_02_dot_74 loc_bar_10_bar__minus_9_bar_3_bar_45)
        (objectAtLocation Window_bar__minus_00_dot_19_bar__plus_01_dot_16_bar__minus_02_dot_56 loc_bar__minus_1_bar__minus_7_bar_2_bar_30)
        (objectAtLocation Window_bar__minus_02_dot_97_bar__plus_01_dot_16_bar__minus_02_dot_56 loc_bar__minus_12_bar__minus_5_bar_2_bar_15)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 RemoteControlType)
                                    (receptacleType ?r ArmChairType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 RemoteControlType)
                                            (receptacleType ?r ArmChairType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            