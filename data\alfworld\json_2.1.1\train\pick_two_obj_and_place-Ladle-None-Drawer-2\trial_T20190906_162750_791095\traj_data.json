{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000299.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000306.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000307.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000308.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000309.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000310.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000311.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000312.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000313.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000314.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000315.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000316.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000317.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000318.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000319.png", "low_idx": 52}, {"high_idx": 7, "image_name": "000000320.png", "low_idx": 52}, {"high_idx": 7, "image_name": "000000321.png", "low_idx": 52}, {"high_idx": 7, "image_name": "000000322.png", "low_idx": 52}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Ladle", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["ladle"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Ladle", [-7.259196, -7.259196, -4.25459432, -4.25459432, 3.8234496, 3.8234496]], "coordinateReceptacleObjectId": ["CounterTop", [0.008, 0.008, -6.052, -6.052, 3.788, 3.788]], "forceVisible": true, "objectId": "Ladle|-01.81|+00.96|-01.06"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|-1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["ladle", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Ladle", [-7.259196, -7.259196, -4.25459432, -4.25459432, 3.8234496, 3.8234496]], "coordinateReceptacleObjectId": ["Drawer", [6.0, 6.0, 2.4204, 2.4204, 2.52, 2.52]], "forceVisible": true, "objectId": "Ladle|-01.81|+00.96|-01.06", "receptacleObjectId": "Drawer|+01.50|+00.63|+00.61"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|2|0"}}, {"discrete_action": {"action": "PickupObject", "args": ["ladle"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Ladle", [4.8744, 4.8744, -6.45899964, -6.45899964, 6.896004, 6.896004]], "coordinateReceptacleObjectId": ["Cabinet", [6.822, 6.822, -5.797, -5.797, 8.08, 8.08]], "forceVisible": true, "objectId": "Ladle|+01.22|+01.72|-01.61"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|4|-1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["ladle", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Ladle", [4.8744, 4.8744, -6.45899964, -6.45899964, 6.896004, 6.896004]], "coordinateReceptacleObjectId": ["Drawer", [6.0, 6.0, 2.4204, 2.4204, 2.52, 2.52]], "forceVisible": true, "objectId": "Ladle|+01.22|+01.72|-01.61", "receptacleObjectId": "Drawer|+01.50|+00.63|+00.61"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Ladle|-01.81|+00.96|-01.06"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [63, 98, 84, 149], "mask": [[29172, 8], [29469, 13], [29768, 15], [30066, 18], [30366, 19], [30665, 20], [30964, 21], [31264, 21], [31564, 21], [31864, 21], [32164, 21], [32465, 20], [32765, 19], [33066, 18], [33367, 16], [33668, 14], [33969, 12], [34271, 8], [34571, 3], [34871, 3], [35171, 3], [35470, 3], [35770, 3], [36070, 3], [36370, 3], [36669, 3], [36969, 3], [37268, 4], [37568, 4], [37868, 4], [38168, 3], [38467, 4], [38767, 4], [39067, 4], [39366, 4], [39666, 4], [39966, 4], [40266, 4], [40565, 5], [40865, 4], [41165, 4], [41465, 4], [41765, 4], [42064, 5], [42364, 4], [42664, 4], [42964, 1], [42967, 1], [43264, 1], [43267, 1], [43563, 2], [43566, 2], [43863, 4], [44163, 4], [44463, 4]], "point": [72, 121]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+01.50|+00.63|+00.61"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [236, 94, 288, 213], "mask": [[28143, 1], [28443, 1], [28743, 2], [29042, 3], [29342, 3], [29642, 4], [29942, 4], [30241, 6], [30541, 6], [30841, 7], [31140, 8], [31440, 9], [31740, 9], [32039, 11], [32339, 11], [32639, 12], [32939, 12], [33238, 13], [33538, 14], [33838, 14], [34137, 16], [34437, 16], [34737, 17], [35037, 17], [35336, 19], [35636, 19], [35936, 20], [36236, 20], [36537, 20], [36837, 20], [37138, 19], [37438, 20], [37738, 20], [38039, 20], [38339, 20], [38639, 21], [38940, 20], [39240, 21], [39541, 20], [39841, 21], [40141, 21], [40442, 21], [40742, 21], [41042, 21], [41343, 21], [41643, 21], [41944, 21], [42244, 21], [42544, 22], [42845, 21], [43145, 22], [43445, 22], [43746, 22], [44046, 22], [44347, 21], [44647, 22], [44947, 22], [45248, 22], [45548, 22], [45848, 23], [46149, 22], [46449, 23], [46750, 22], [47050, 23], [47350, 23], [47651, 23], [47951, 23], [48251, 23], [48552, 23], [48852, 23], [49153, 23], [49453, 23], [49753, 24], [50054, 23], [50354, 24], [50654, 24], [50955, 24], [51255, 24], [51556, 24], [51856, 24], [52156, 24], [52457, 24], [52757, 24], [53057, 25], [53358, 24], [53658, 25], [53959, 24], [54259, 25], [54559, 25], [54860, 25], [55160, 25], [55460, 26], [55761, 25], [56061, 25], [56362, 25], [56662, 25], [56962, 26], [57263, 25], [57563, 26], [57864, 25], [58164, 25], [58464, 24], [58765, 22], [59065, 22], [59365, 21], [59666, 20], [59966, 19], [60267, 17], [60567, 17], [60867, 16], [61168, 14], [61468, 14], [61768, 13], [62069, 11], [62369, 11], [62670, 9], [62970, 8], [63270, 7], [63571, 5], [63871, 5]], "point": [262, 152]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Ladle|-01.81|+00.96|-01.06", "placeStationary": true, "receptacleObjectId": "Drawer|+01.50|+00.63|+00.61"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [163, 94, 288, 213], "mask": [[28066, 3], [28365, 5], [28665, 80], [28965, 80], [29265, 80], [29565, 81], [29865, 81], [30165, 82], [30465, 82], [30765, 83], [31065, 83], [31365, 84], [31664, 85], [31963, 87], [32263, 87], [32563, 88], [32863, 88], [33163, 88], [33464, 88], [33764, 88], [34064, 89], [34364, 89], [34664, 90], [34964, 90], [35264, 91], [35564, 91], [35864, 92], [36164, 92], [36464, 93], [36764, 93], [37064, 93], [37364, 94], [37664, 94], [37965, 94], [38265, 94], [38565, 95], [38865, 95], [39165, 96], [39465, 96], [39765, 97], [40065, 97], [40365, 98], [40665, 98], [40965, 98], [41265, 99], [41565, 99], [41865, 100], [42165, 100], [42466, 100], [42766, 100], [43066, 101], [43366, 101], [43666, 102], [43966, 102], [44266, 102], [44566, 103], [44866, 103], [45166, 104], [45466, 104], [45766, 105], [46066, 105], [46366, 106], [46666, 106], [46967, 106], [47267, 106], [47567, 107], [47867, 107], [48167, 107], [48467, 108], [48767, 108], [49067, 109], [49367, 109], [49667, 110], [49967, 110], [50267, 111], [50567, 111], [50867, 112], [51167, 112], [51468, 112], [51768, 112], [52068, 112], [52368, 113], [52668, 113], [52968, 114], [53268, 114], [53568, 115], [53868, 115], [54168, 116], [54468, 116], [54768, 117], [55068, 117], [55368, 118], [55668, 118], [55969, 117], [56269, 118], [56569, 118], [56869, 119], [57169, 119], [57469, 120], [57769, 120], [58069, 120], [58369, 119], [58669, 118], [58969, 118], [59269, 117], [59569, 117], [59869, 116], [60169, 115], [60469, 115], [60770, 113], [61070, 112], [61370, 112], [61670, 111], [61970, 110], [62270, 110], [62570, 109], [62870, 6], [63170, 5], [63470, 5], [63771, 4]], "point": [225, 152]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+01.50|+00.63|+00.61"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [163, 94, 288, 213], "mask": [[28066, 3], [28365, 5], [28665, 80], [28965, 80], [29265, 80], [29565, 81], [29865, 81], [30165, 82], [30465, 82], [30765, 83], [31065, 83], [31365, 84], [31664, 85], [31963, 87], [32263, 87], [32563, 88], [32863, 88], [33163, 88], [33464, 88], [33764, 88], [34064, 89], [34364, 89], [34664, 90], [34964, 32], [35006, 48], [35264, 31], [35308, 47], [35564, 30], [35609, 18], [35645, 10], [35864, 29], [35910, 9], [35942, 1], [35946, 10], [36164, 29], [36226, 30], [36464, 29], [36516, 41], [36764, 29], [36811, 46], [37064, 30], [37110, 47], [37364, 30], [37410, 48], [37664, 31], [37709, 49], [37965, 30], [38008, 51], [38265, 31], [38307, 52], [38565, 33], [38606, 54], [38865, 95], [39165, 96], [39465, 96], [39765, 97], [40065, 97], [40365, 98], [40665, 98], [40965, 98], [41265, 99], [41565, 99], [41865, 100], [42165, 100], [42466, 100], [42766, 100], [43066, 101], [43366, 101], [43666, 102], [43966, 102], [44266, 102], [44566, 103], [44866, 103], [45166, 104], [45466, 104], [45766, 105], [46066, 105], [46366, 106], [46666, 106], [46967, 106], [47267, 106], [47567, 107], [47867, 107], [48167, 107], [48467, 108], [48767, 108], [49067, 109], [49367, 109], [49667, 110], [49967, 110], [50267, 111], [50567, 111], [50867, 112], [51167, 112], [51468, 112], [51768, 112], [52068, 112], [52368, 113], [52668, 113], [52968, 114], [53268, 114], [53568, 115], [53868, 115], [54168, 116], [54468, 116], [54768, 117], [55068, 117], [55368, 118], [55668, 118], [55969, 117], [56269, 118], [56569, 118], [56869, 119], [57169, 119], [57469, 120], [57769, 120], [58069, 120], [58369, 119], [58669, 118], [58969, 118], [59269, 117], [59569, 117], [59869, 116], [60169, 115], [60469, 115], [60770, 113], [61070, 112], [61370, 112], [61670, 111], [61970, 110], [62270, 110], [62570, 109], [62870, 6], [63170, 5], [63470, 5], [63771, 4]], "point": [225, 152]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+01.71|+02.02|-01.45"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 253, 125], "mask": [[0, 253], [300, 253], [600, 253], [900, 253], [1200, 253], [1500, 253], [1800, 253], [2100, 253], [2400, 253], [2700, 253], [3000, 253], [3300, 253], [3600, 253], [3900, 253], [4200, 253], [4500, 253], [4800, 253], [5100, 253], [5400, 253], [5700, 253], [6000, 253], [6300, 253], [6600, 253], [6900, 253], [7200, 253], [7500, 253], [7800, 253], [8100, 253], [8400, 253], [8700, 253], [9000, 253], [9300, 253], [9600, 253], [9900, 253], [10200, 253], [10500, 253], [10800, 253], [11100, 253], [11400, 253], [11700, 253], [12000, 253], [12300, 253], [12600, 253], [12900, 253], [13200, 253], [13500, 253], [13800, 253], [14100, 253], [14400, 253], [14700, 253], [15000, 253], [15300, 253], [15600, 253], [15900, 253], [16200, 253], [16500, 253], [16800, 253], [17100, 253], [17400, 253], [17700, 253], [18000, 253], [18300, 253], [18600, 253], [18900, 253], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 253], [20700, 253], [21000, 253], [21300, 253], [21600, 253], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 253], [25500, 253], [25800, 253], [26100, 253], [26400, 253], [26700, 253], [27000, 254], [27300, 254], [27600, 254], [27900, 254], [28200, 254], [28500, 254], [28800, 254], [29100, 254], [29400, 254], [29700, 254], [30000, 254], [30300, 254], [30600, 254], [30900, 254], [31200, 254], [31500, 254], [31800, 254], [32100, 254], [32400, 254], [32700, 254], [33000, 254], [33300, 254], [33600, 254], [33900, 254], [34200, 254], [34500, 254], [34800, 254], [35100, 254], [35400, 254], [35700, 254], [36000, 254], [36300, 254], [36600, 254], [36900, 254], [37200, 252]], "point": [126, 62]}}, "high_idx": 5}, {"api_action": {"action": "PickupObject", "objectId": "Ladle|+01.22|+01.72|-01.61"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [67, 106, 121, 116], "mask": [[31614, 8], [31910, 12], [32205, 17], [32502, 20], [32799, 23], [33097, 25], [33396, 26], [33694, 27], [33984, 37], [34274, 15], [34297, 24], [34567, 13], [34597, 23]], "point": [94, 112]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+01.71|+02.02|-01.45"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 124], "mask": [[0, 247], [248, 299], [548, 299], [849, 298], [1149, 298], [1449, 298], [1749, 298], [2049, 298], [2349, 298], [2649, 298], [2949, 298], [3249, 298], [3549, 298], [3849, 298], [4149, 298], [4449, 298], [4749, 298], [5049, 298], [5349, 298], [5649, 298], [5949, 298], [6249, 298], [6549, 298], [6849, 298], [7149, 298], [7449, 298], [7749, 298], [8049, 298], [8349, 298], [8649, 298], [8949, 298], [9249, 298], [9549, 298], [9849, 298], [10149, 298], [10449, 298], [10749, 298], [11049, 298], [11349, 298], [11649, 298], [11949, 298], [12249, 298], [12549, 298], [12849, 298], [13149, 298], [13449, 298], [13749, 298], [14049, 298], [14349, 298], [14649, 298], [14949, 298], [15249, 298], [15549, 298], [15849, 298], [16149, 298], [16449, 298], [16749, 298], [17049, 298], [17349, 298], [17649, 298], [17949, 298], [18249, 298], [18549, 298], [18849, 298], [19149, 298], [19449, 298], [19748, 299], [20048, 299], [20348, 299], [20648, 299], [20949, 298], [21249, 298], [21549, 298], [21849, 298], [22149, 298], [22449, 298], [22749, 298], [23049, 298], [23349, 298], [23649, 298], [23949, 298], [24249, 298], [24549, 298], [24849, 98], [24955, 192], [25149, 93], [25262, 185], [25449, 92], [25566, 181], [25749, 92], [25868, 179], [26049, 92], [26170, 177], [26349, 92], [26470, 177], [26649, 92], [26770, 177], [26949, 92], [27070, 177], [27249, 93], [27369, 178], [27548, 94], [27669, 178], [27848, 94], [27969, 178], [28148, 94], [28269, 178], [28448, 94], [28569, 178], [28748, 94], [28869, 178], [29048, 94], [29169, 178], [29348, 94], [29469, 178], [29648, 94], [29769, 178], [29948, 94], [30069, 178], [30248, 95], [30368, 179], [30548, 95], [30668, 179], [30848, 95], [30968, 179], [31148, 95], [31268, 179], [31448, 95], [31568, 179], [31748, 95], [31868, 179], [32048, 95], [32168, 179], [32348, 95], [32468, 179], [32648, 95], [32768, 179], [32948, 95], [33068, 179], [33248, 96], [33367, 180], [33548, 96], [33667, 180], [33848, 96], [33967, 180], [34148, 96], [34267, 180], [34448, 48], [34500, 44], [34567, 180], [34748, 44], [35048, 40], [35348, 36], [35648, 32], [35948, 28], [36248, 24], [36548, 20], [36848, 15], [37148, 11]], "point": [149, 61]}}, "high_idx": 5}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+01.50|+00.63|+00.61"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [236, 94, 288, 213], "mask": [[28143, 1], [28443, 1], [28743, 2], [29042, 3], [29342, 3], [29642, 4], [29942, 4], [30241, 6], [30541, 6], [30841, 7], [31140, 8], [31440, 9], [31740, 9], [32039, 11], [32339, 11], [32639, 12], [32939, 12], [33238, 13], [33538, 14], [33838, 14], [34137, 16], [34437, 16], [34737, 17], [35037, 17], [35336, 19], [35636, 19], [35936, 20], [36236, 20], [36537, 20], [36837, 20], [37138, 19], [37438, 20], [37738, 20], [38039, 20], [38339, 20], [38639, 21], [38940, 20], [39240, 21], [39541, 20], [39841, 21], [40141, 21], [40442, 21], [40742, 21], [41042, 21], [41343, 21], [41643, 21], [41944, 21], [42244, 21], [42544, 22], [42845, 21], [43145, 22], [43445, 22], [43746, 22], [44046, 22], [44347, 21], [44647, 22], [44947, 22], [45248, 22], [45548, 22], [45848, 23], [46149, 22], [46449, 23], [46750, 22], [47050, 23], [47350, 23], [47651, 23], [47951, 23], [48251, 23], [48552, 23], [48852, 23], [49153, 23], [49453, 23], [49753, 24], [50054, 23], [50354, 24], [50654, 24], [50955, 24], [51255, 24], [51556, 24], [51856, 24], [52156, 24], [52457, 24], [52757, 24], [53057, 25], [53358, 24], [53658, 25], [53959, 24], [54259, 25], [54559, 25], [54860, 25], [55160, 25], [55460, 26], [55761, 25], [56061, 25], [56362, 25], [56662, 25], [56962, 26], [57263, 25], [57563, 26], [57864, 25], [58164, 25], [58464, 24], [58765, 22], [59065, 22], [59365, 21], [59666, 20], [59966, 19], [60267, 17], [60567, 17], [60867, 16], [61168, 14], [61468, 14], [61768, 13], [62069, 11], [62369, 11], [62670, 9], [62970, 8], [63270, 7], [63571, 5], [63871, 5]], "point": [262, 152]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Ladle|+01.22|+01.72|-01.61", "placeStationary": true, "receptacleObjectId": "Drawer|+01.50|+00.63|+00.61"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [163, 94, 288, 213], "mask": [[28066, 3], [28365, 5], [28665, 80], [28965, 80], [29265, 80], [29565, 81], [29865, 81], [30165, 82], [30465, 82], [30765, 83], [31065, 83], [31365, 84], [31664, 85], [31963, 87], [32263, 87], [32563, 88], [32863, 88], [33163, 88], [33464, 88], [33764, 88], [34064, 89], [34364, 89], [34664, 90], [34964, 32], [35006, 48], [35264, 31], [35308, 47], [35564, 30], [35609, 18], [35645, 10], [35864, 29], [35910, 9], [35942, 1], [35946, 10], [36164, 29], [36226, 30], [36464, 29], [36516, 41], [36764, 29], [36811, 46], [37064, 30], [37110, 47], [37364, 30], [37410, 48], [37664, 31], [37709, 49], [37965, 30], [38008, 51], [38265, 31], [38307, 52], [38565, 33], [38606, 54], [38865, 95], [39165, 96], [39465, 96], [39765, 97], [40065, 97], [40365, 98], [40665, 98], [40965, 98], [41265, 99], [41565, 99], [41865, 100], [42165, 100], [42466, 100], [42766, 100], [43066, 101], [43366, 101], [43666, 102], [43966, 102], [44266, 102], [44566, 103], [44866, 103], [45166, 104], [45466, 104], [45766, 105], [46066, 105], [46366, 106], [46666, 106], [46967, 106], [47267, 106], [47567, 107], [47867, 107], [48167, 107], [48467, 108], [48767, 108], [49067, 109], [49367, 109], [49667, 110], [49967, 110], [50267, 111], [50567, 111], [50867, 112], [51167, 112], [51468, 112], [51768, 112], [52068, 112], [52368, 113], [52668, 113], [52968, 114], [53268, 114], [53568, 115], [53868, 115], [54168, 116], [54468, 116], [54768, 117], [55068, 117], [55368, 118], [55668, 118], [55969, 117], [56269, 118], [56569, 118], [56869, 119], [57169, 119], [57469, 120], [57769, 120], [58069, 120], [58369, 119], [58669, 118], [58969, 118], [59269, 117], [59569, 117], [59869, 116], [60169, 115], [60469, 115], [60770, 113], [61070, 112], [61370, 112], [61670, 111], [61970, 110], [62270, 110], [62570, 109], [62870, 6], [63170, 5], [63470, 5], [63771, 4]], "point": [225, 152]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+01.50|+00.63|+00.61"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [163, 94, 288, 213], "mask": [[28066, 3], [28365, 5], [28665, 80], [28965, 80], [29265, 80], [29565, 81], [29865, 81], [30165, 82], [30465, 82], [30765, 83], [31065, 83], [31365, 84], [31664, 85], [31963, 87], [32263, 87], [32563, 88], [32863, 88], [33163, 88], [33464, 88], [33764, 88], [34064, 89], [34364, 89], [34664, 90], [34964, 32], [35006, 48], [35264, 31], [35308, 47], [35564, 30], [35609, 18], [35645, 10], [35864, 29], [35910, 9], [35942, 1], [35946, 10], [36164, 29], [36226, 30], [36464, 29], [36516, 41], [36764, 29], [36811, 46], [37064, 30], [37110, 47], [37364, 30], [37410, 48], [37664, 31], [37709, 49], [37965, 30], [38008, 51], [38265, 31], [38307, 52], [38565, 33], [38606, 54], [38865, 95], [39165, 96], [39465, 96], [39765, 35], [39809, 53], [40065, 34], [40111, 51], [40365, 32], [40413, 50], [40665, 32], [40714, 17], [40752, 11], [40965, 32], [41014, 8], [41049, 1], [41053, 10], [41265, 32], [41333, 31], [41565, 32], [41621, 43], [41865, 32], [41915, 50], [42165, 32], [42215, 50], [42466, 31], [42514, 52], [42766, 32], [42814, 52], [43066, 32], [43113, 54], [43366, 33], [43412, 55], [43666, 35], [43711, 57], [43966, 37], [44009, 59], [44266, 102], [44566, 103], [44866, 103], [45166, 104], [45466, 104], [45766, 105], [46066, 105], [46366, 106], [46666, 106], [46967, 106], [47267, 106], [47567, 107], [47867, 107], [48167, 107], [48467, 108], [48767, 108], [49067, 109], [49367, 109], [49667, 110], [49967, 110], [50267, 111], [50567, 111], [50867, 112], [51167, 112], [51468, 112], [51768, 112], [52068, 112], [52368, 113], [52668, 113], [52968, 114], [53268, 114], [53568, 115], [53868, 115], [54168, 116], [54468, 116], [54768, 117], [55068, 117], [55368, 118], [55668, 118], [55969, 117], [56269, 118], [56569, 118], [56869, 119], [57169, 119], [57469, 120], [57769, 120], [58069, 120], [58369, 119], [58669, 118], [58969, 118], [59269, 117], [59569, 117], [59869, 116], [60169, 115], [60469, 115], [60770, 113], [61070, 112], [61370, 112], [61670, 111], [61970, 110], [62270, 110], [62570, 109], [62870, 6], [63170, 5], [63470, 5], [63771, 4]], "point": [225, 152]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan2", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 1.25, "y": 0.9009992, "z": 0.0}, "object_poses": [{"objectName": "Potato_5e728867", "position": {"x": -1.68249583, "y": 0.8630446, "z": 0.2015051}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_5e728867", "position": {"x": 1.89233732, "y": 1.03015482, "z": -0.5996125}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Ladle_ec6bb035", "position": {"x": 1.2186, "y": 1.724001, "z": -1.61474991}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_e1c57c26", "position": {"x": 0.361856759, "y": 0.112351418, "z": -1.40862167}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_4f8c70c0", "position": {"x": -0.106612019, "y": 0.9256421, "z": 0.0417993069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_4f8c70c0", "position": {"x": -1.66821682, "y": 0.911542058, "z": -1.41596878}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_c80d57de", "position": {"x": 1.55919147, "y": 0.347422, "z": 0.7361447}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": 1.802076, "y": 0.9155856, "z": -1.61003125}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": -0.00256602466, "y": 0.8175189, "z": -1.54605973}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_4e364eea", "position": {"x": 1.34167874, "y": 1.67776918, "z": -1.69013023}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d852c2bd", "position": {"x": 0.1074348, "y": 0.92430824, "z": 1.20230114}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d852c2bd", "position": {"x": -1.80318069, "y": 1.46792245, "z": -0.175190732}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": 1.76426947, "y": 0.6018661, "z": 0.6677724}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": 0.214458212, "y": 0.9475927, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": -0.744763732, "y": 1.68317306, "z": -1.61474991}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": -0.320658833, "y": 0.9291344, "z": 0.506000042}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_448791c6", "position": {"x": 0.170128584, "y": 0.8157383, "z": -1.4355}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_253fa632", "position": {"x": -0.137838274, "y": 0.113292515, "z": -1.4095}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_253fa632", "position": {"x": -0.361994565, "y": 0.113292515, "z": -1.347}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_14add4df", "position": {"x": 1.90011775, "y": 0.907806158, "z": 1.25821185}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": 0.000411391258, "y": 1.00617969, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": -1.46391475, "y": 0.9920796, "z": -0.9641914}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": 1.3554858, "y": 0.9554, "z": -1.36239815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": -1.796, "y": 0.09461791, "z": 1.37116122}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": -0.175260633, "y": 0.813862145, "z": -1.38022017}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": 2.00143766, "y": 1.66291428, "z": -1.20452523}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_4e364eea", "position": {"x": -0.106612019, "y": 0.923730552, "z": 0.506000042}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": 1.9753, "y": 0.938299954, "z": 0.6015}, "rotation": {"x": 0.0, "y": 180.000214, "z": 0.0}}, {"objectName": "Fork_c80d57de", "position": {"x": -0.106612019, "y": 0.926148534, "z": -0.190301061}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ec6bb035", "position": {"x": -1.814799, "y": 0.9558624, "z": -1.06364858}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_233c7df0", "position": {"x": 0.321481615, "y": 0.9266413, "z": 0.506000042}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": 1.98843122, "y": 1.06329572, "z": -0.5472686}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_d852c2bd", "position": {"x": 0.1074348, "y": 0.92430824, "z": 0.0417993069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_6d10ab8d", "position": {"x": -1.28641355, "y": 0.9, "z": -1.34873831}, "rotation": {"x": 0.0, "y": 79.76979, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": -1.80318046, "y": 1.545918, "z": 0.20150499}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_de267f63", "position": {"x": 1.71057916, "y": 0.7104007, "z": -0.145488292}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": -1.721387, "y": 0.6164668, "z": -0.175190285}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": 1.76926708, "y": 0.784203947, "z": 1.36581}, "rotation": {"x": 0.0, "y": 0.0, "z": -7.062251e-30}}, {"objectName": "SaltShaker_586649ce", "position": {"x": 0.000411391258, "y": 0.9219062, "z": 0.506000042}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_14add4df", "position": {"x": 0.138286144, "y": 0.109093368, "z": -1.22013044}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_6cb12d41", "position": {"x": 0.1074348, "y": 0.923526943, "z": 0.9702008}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_4f8c70c0", "position": {"x": 1.71057916, "y": 0.6954429, "z": 0.6728655}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": -0.21363543, "y": 1.00617969, "z": 0.9702008}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_448791c6", "position": {"x": 1.1179688, "y": 0.9666673, "z": -1.70706248}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5e728867", "position": {"x": -0.261607945, "y": 0.784452736, "z": -1.49077988}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_e1c57c26", "position": {"x": 1.66173053, "y": 0.2369377, "z": -0.157344744}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_bfeb74cb", "position": {"x": -1.874256, "y": 0.912629366, "z": -1.41596878}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": -0.08891332, "y": 0.755646348, "z": -1.54605973}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_253fa632", "position": {"x": -1.8419168, "y": 1.66042042, "z": -0.9806492}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3877535537, "scene_num": 2}, "task_id": "trial_T20190906_162750_791095", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A320QA9HJFUOZO_3L6L49WXW3E5DYWE4CZ1CPJS9VY54E", "high_descs": ["Turn around and walk through the kitchen to the counter just left of the fridge", "Pick up the gold spoon from the counter", "Walk back through the kitchen to the stove", "Open the top drawer under the stove and place the spoon inside", "Turn around and go to the cupboard above the coffee maker", "Take the spoon from the cabinet above the coffee maker", "Turn around and walk back to the drawer below the stove", "Place the spoon in the drawer with the other spoon"], "task_desc": "Put two gold spoons in the drawer below the stove", "votes": [1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A3HL2LL0LEPZT8_3W92K5RLWXY47BFTO80OLFJ5M70V55", "high_descs": ["Turn around, walk towards the coffee machine, make a right, walk towards the fridge.", "One the counter to the right of the fridge get the gold spoon with blue handle", "Turn around, walk towards the microwave, make a left walk towards the stove top.", "Put the spoon in the drawer below the stove top.", "Go to the right towards the microwave, look at the cabinet above.", "Get the gold spoon with blue handle from the cabinet.", "Turn around and go near the stove top.", "Put the spoon in the drawer below the stove top."], "task_desc": "Put all the gold spoons with blue handles in the drawer under the stove top.", "votes": [1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A2NGMLBFZ3YQP5_3FE2ERCCZ0PDA1OF7BNOOTUFK9AOPJ", "high_descs": ["Turn left, turn left again, walk past the dishwasher, turn right, walk to the counter to the left of the refrigerator.", "Pick up ladle from the counter.", "Turn right, turn right again, cross room, turn left, walk to drawers to left of the stove.", "Put the ladle in the top drawer.", "Turn left, walk to counter where coffee maker is.", "Open cabinet above counter, take the ladle from cabinet.", "Turn left, turn left again, walk to the stove, turn right to face stove.", "Put the ladle in the top drawer."], "task_desc": "Put two ladles in the top drawer next to stove.", "votes": [1, 1, 1]}, {"assignment_id": "A1O3TWBUDONVLO_3S0TNUHWKWZ10CY9VL7XRDNQRWI8DN", "high_descs": ["Turn around, walk forward, turn right and walk forward, then turn left to face the counter.", "Pick up the ladle on the back of the counter.", "Turn around and cross the room, then turn left and face the drawer below the stove.", "Place the ladle in the drawer and close it.", "Turn around and walk forward to face the cabinet above the coffee maker.", "Take the ladle out of the cabinet above the coffee maker.", "Turn around and step forward to face the drawer below the stove.", "Place the ladle next to the other ladle in the drawer."], "task_desc": "To put two ladles into a drawer below the stove.", "votes": [1, 1]}, {"assignment_id": "A2A4UAFZ5LW71K_32Z9ZLUT1O1LXKQROYFJFKSOX58HOO", "high_descs": ["turn around and walk towards the refrigerator behind you. Stand facing the counter to the left of the fridge", "pick up the spoon that is on the counter", "turn around and walk towards the microwave behind you, turn left", "put the spoon in the drawer under the stove", "turn around and go to the left, walk towards the coffee pot", "take a spoon from the cabinet above the coffee pot", "turn around and walk back towards the stove", "put the spoon in the drawer under the stove"], "task_desc": "put two spoons in a drawer", "votes": [1, 1, 0, 0, 1]}]}}