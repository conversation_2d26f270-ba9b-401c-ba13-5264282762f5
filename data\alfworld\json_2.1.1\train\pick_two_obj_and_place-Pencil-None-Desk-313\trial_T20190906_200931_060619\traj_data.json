{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000158.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000159.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000160.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000161.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000162.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000163.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000165.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000166.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 24}, {"high_idx": 7, "image_name": "000000206.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000207.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000208.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000209.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000210.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000211.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000212.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000213.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000214.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000215.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000216.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000217.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000218.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000219.png", "low_idx": 25}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 25}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Pencil", "parent_target": "Desk", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|-2|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["pencil"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pencil", [6.4018774, 6.4018774, -2.495370148, -2.495370148, 2.8483016, 2.8483016]], "coordinateReceptacleObjectId": ["SideTable", [6.2496, 6.2496, -1.86, -1.86, -0.0496, -0.0496]], "forceVisible": true, "objectId": "Pencil|+01.60|+00.71|-00.62"}}, {"discrete_action": {"action": "GotoLocation", "args": ["desk"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|-2|2|60"}}, {"discrete_action": {"action": "PutObject", "args": ["pencil", "desk"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pencil", [6.4018774, 6.4018774, -2.495370148, -2.495370148, 2.8483016, 2.8483016]], "coordinateReceptacleObjectId": ["Desk", [2.532, 2.532, -6.52, -6.52, -0.148, -0.148]], "forceVisible": true, "objectId": "Pencil|+01.60|+00.71|-00.62", "receptacleObjectId": "Desk|+00.63|-00.04|-01.63"}}, {"discrete_action": {"action": "GotoLocation", "args": ["shelf"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|4|-2|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["pencil"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pencil", [5.92218732, 5.92218732, -1.858393908, -1.858393908, 1.968051792, 1.968051792]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [6.1672, 6.1672, -1.8988, -1.8988, 1.9228, 1.9228]], "forceVisible": true, "objectId": "Pencil|+01.48|+00.49|-00.46"}}, {"discrete_action": {"action": "GotoLocation", "args": ["desk"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|4|-2|2|60"}}, {"discrete_action": {"action": "PutObject", "args": ["pencil", "desk"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pencil", [5.92218732, 5.92218732, -1.858393908, -1.858393908, 1.968051792, 1.968051792]], "coordinateReceptacleObjectId": ["Desk", [2.532, 2.532, -6.52, -6.52, -0.148, -0.148]], "forceVisible": true, "objectId": "Pencil|+01.48|+00.49|-00.46", "receptacleObjectId": "Desk|+00.63|-00.04|-01.63"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Pencil|+01.60|+00.71|-00.62"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [178, 112, 182, 148], "mask": [[33479, 1], [33779, 1], [34078, 2], [34379, 1], [34679, 1], [34979, 2], [35279, 2], [35579, 2], [35879, 2], [36179, 2], [36479, 2], [36779, 2], [37079, 2], [37379, 2], [37679, 2], [37979, 2], [38279, 2], [38579, 2], [38880, 2], [39180, 2], [39480, 2], [39780, 2], [40080, 2], [40380, 2], [40680, 2], [40980, 2], [41280, 2], [41580, 2], [41880, 2], [42180, 2], [42480, 2], [42780, 2], [43081, 2], [43381, 2], [43681, 2], [43981, 2], [44281, 2]], "point": [180, 129]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pencil|+01.60|+00.71|-00.62", "placeStationary": true, "receptacleObjectId": "Desk|+00.63|-00.04|-01.63"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 14, 299, 170], "mask": [[3922, 66], [3991, 209], [4221, 67], [4291, 209], [4521, 68], [4591, 209], [4820, 69], [4892, 208], [5120, 69], [5192, 208], [5420, 69], [5492, 208], [5719, 70], [5792, 208], [6019, 71], [6092, 208], [6318, 70], [6403, 197], [6618, 67], [6705, 195], [6918, 65], [7007, 193], [7217, 64], [7309, 191], [7517, 63], [7610, 190], [7816, 62], [7910, 190], [8116, 61], [8211, 189], [8415, 62], [8512, 188], [8715, 61], [8812, 188], [9015, 60], [9112, 188], [9314, 61], [9412, 188], [9614, 61], [9712, 188], [9913, 62], [10012, 39], [10055, 145], [10213, 62], [10312, 36], [10357, 143], [10513, 62], [10611, 35], [10660, 140], [10812, 63], [10911, 33], [10961, 139], [11112, 63], [11211, 32], [11262, 138], [11411, 65], [11510, 33], [11563, 137], [11711, 66], [11808, 34], [11863, 137], [12011, 66], [12107, 35], [12164, 136], [12310, 68], [12406, 36], [12464, 136], [12610, 70], [12704, 38], [12764, 136], [12909, 73], [13002, 40], [13064, 136], [13209, 75], [13299, 43], [13364, 136], [13509, 82], [13592, 50], [13663, 137], [13808, 134], [13963, 137], [14108, 134], [14263, 137], [14407, 31], [14446, 96], [14563, 137], [14707, 28], [14749, 93], [14863, 137], [15006, 27], [15050, 92], [15163, 137], [15306, 26], [15351, 91], [15463, 137], [15606, 25], [15651, 91], [15763, 137], [15905, 25], [15952, 90], [16063, 137], [16205, 24], [16252, 90], [16363, 137], [16504, 22], [16553, 89], [16663, 137], [16804, 21], [16853, 89], [16963, 137], [17104, 21], [17153, 89], [17263, 137], [17403, 21], [17454, 88], [17563, 137], [17703, 21], [17727, 2], [17754, 88], [17863, 33], [17911, 89], [18002, 23], [18028, 1], [18055, 88], [18162, 34], [18212, 88], [18302, 23], [18329, 1], [18355, 88], [18462, 34], [18512, 88], [18602, 24], [18629, 1], [18655, 89], [18761, 35], [18812, 88], [18901, 25], [18930, 1], [18956, 89], [19060, 36], [19113, 87], [19201, 26], [19256, 91], [19359, 37], [19413, 115], [19557, 92], [19656, 40], [19713, 116], [19857, 139], [20013, 117], [20157, 139], [20313, 118], [20458, 86], [20562, 35], [20613, 120], [20758, 86], [20862, 35], [20914, 120], [21058, 86], [21162, 35], [21214, 121], [21359, 85], [21462, 35], [21514, 121], [21659, 85], [21762, 35], [21814, 122], [21958, 86], [22062, 35], [22114, 122], [22258, 86], [22362, 35], [22414, 123], [22557, 87], [22662, 35], [22715, 122], [22856, 142], [23015, 123], [23155, 143], [23315, 124], [23454, 144], [23615, 125], [23752, 146], [23915, 128], [24049, 149], [24215, 283], [24516, 282], [24816, 282], [25116, 283], [25415, 766], [26219, 259], [26520, 6285], [32902, 38], [33000, 105], [33202, 37], [33301, 104], [33502, 37], [33602, 103], [33802, 36], [33902, 102], [34102, 36], [34203, 101], [34403, 34], [34504, 100], [34703, 34], [34805, 99], [35003, 33], [35105, 99], [35303, 33], [35406, 98], [35603, 32], [35707, 97], [35903, 32], [36008, 96], [36203, 31], [36309, 95], [36503, 31], [36609, 94], [36804, 29], [36910, 93], [37104, 29], [37211, 92], [37404, 28], [37512, 91], [37704, 28], [37813, 90], [38004, 28], [38113, 90], [38304, 27], [38414, 89], [38604, 27], [38715, 88], [38905, 25], [39016, 87], [39205, 25], [39317, 85], [39505, 24], [39617, 85], [39806, 23], [39918, 84], [40106, 22], [40219, 83], [40406, 22], [40520, 82], [40706, 21], [40821, 81], [41011, 16], [41121, 81], [41316, 10], [41422, 80], [41618, 8], [41723, 79], [41920, 5], [42024, 77], [42222, 3], [42324, 77], [42523, 1], [42625, 76], [42823, 1], [42926, 75], [43227, 74], [43528, 73], [43828, 73], [44129, 72], [44430, 71], [44731, 69], [45032, 68], [45332, 68], [45633, 67], [45934, 66], [46235, 65], [46536, 64], [46836, 64], [47137, 63], [47438, 61], [47739, 60], [48040, 59], [48340, 59], [48641, 58], [48942, 57], [49243, 56], [49543, 56], [49843, 56], [50143, 55], [50376, 1], [50444, 54], [50675, 3], [50744, 54], [50918, 82]], "point": [149, 91]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Pencil|+01.48|+00.49|-00.46"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [124, 179, 161, 179], "mask": [[53524, 38]], "point": [142, 178]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pencil|+01.48|+00.49|-00.46", "placeStationary": true, "receptacleObjectId": "Desk|+00.63|-00.04|-01.63"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 14, 299, 170], "mask": [[3922, 66], [3991, 209], [4221, 67], [4291, 209], [4521, 68], [4591, 209], [4820, 69], [4892, 208], [5120, 69], [5192, 208], [5420, 69], [5492, 208], [5719, 70], [5792, 208], [6019, 71], [6092, 208], [6318, 70], [6403, 197], [6618, 67], [6705, 195], [6918, 65], [7007, 193], [7217, 64], [7309, 191], [7517, 63], [7610, 190], [7816, 62], [7910, 190], [8116, 61], [8211, 189], [8415, 62], [8512, 188], [8715, 61], [8812, 188], [9015, 60], [9112, 188], [9314, 61], [9412, 188], [9614, 61], [9712, 188], [9913, 62], [10012, 39], [10055, 145], [10213, 62], [10312, 36], [10357, 143], [10513, 62], [10611, 35], [10660, 140], [10812, 63], [10911, 33], [10961, 139], [11112, 63], [11211, 32], [11262, 138], [11411, 65], [11510, 33], [11563, 137], [11711, 66], [11808, 34], [11863, 137], [12011, 66], [12107, 35], [12164, 136], [12310, 68], [12406, 36], [12464, 136], [12610, 70], [12704, 38], [12764, 136], [12909, 73], [13002, 40], [13064, 136], [13209, 75], [13299, 43], [13364, 136], [13509, 82], [13592, 50], [13663, 137], [13808, 134], [13963, 137], [14108, 134], [14263, 137], [14407, 31], [14446, 96], [14563, 137], [14707, 28], [14749, 93], [14863, 137], [15006, 27], [15050, 92], [15163, 137], [15306, 26], [15351, 91], [15463, 137], [15606, 25], [15651, 91], [15763, 137], [15905, 25], [15952, 90], [16063, 137], [16205, 24], [16252, 90], [16363, 137], [16504, 22], [16553, 89], [16663, 137], [16804, 21], [16853, 89], [16963, 137], [17104, 21], [17153, 89], [17263, 137], [17403, 21], [17454, 88], [17563, 137], [17703, 21], [17727, 2], [17754, 88], [17863, 33], [17911, 89], [18002, 23], [18028, 1], [18055, 88], [18162, 34], [18212, 88], [18302, 23], [18329, 1], [18355, 88], [18462, 34], [18512, 88], [18602, 24], [18629, 1], [18655, 89], [18761, 35], [18812, 88], [18901, 25], [18930, 1], [18956, 89], [19060, 36], [19113, 87], [19201, 26], [19256, 91], [19359, 37], [19413, 115], [19557, 92], [19656, 40], [19713, 116], [19857, 139], [20013, 117], [20157, 139], [20313, 118], [20458, 86], [20562, 35], [20613, 120], [20758, 86], [20862, 35], [20914, 120], [21058, 86], [21162, 35], [21214, 121], [21359, 85], [21462, 35], [21514, 121], [21659, 85], [21762, 35], [21814, 122], [21958, 86], [22062, 35], [22114, 122], [22258, 86], [22362, 35], [22414, 123], [22557, 87], [22662, 35], [22715, 122], [22856, 142], [23015, 123], [23155, 143], [23315, 124], [23454, 144], [23615, 125], [23752, 146], [23915, 128], [24049, 149], [24215, 283], [24516, 282], [24816, 282], [25116, 283], [25415, 518], [25972, 209], [26219, 14], [26274, 204], [26520, 6285], [32902, 38], [33000, 105], [33202, 37], [33301, 104], [33502, 37], [33602, 103], [33802, 36], [33902, 102], [34102, 36], [34203, 101], [34403, 34], [34504, 100], [34703, 34], [34805, 99], [35003, 33], [35105, 99], [35303, 33], [35406, 98], [35603, 32], [35707, 97], [35903, 32], [36008, 96], [36203, 31], [36309, 95], [36503, 31], [36609, 94], [36804, 29], [36910, 93], [37104, 29], [37211, 92], [37404, 28], [37512, 91], [37704, 28], [37813, 90], [38004, 28], [38113, 90], [38304, 27], [38414, 89], [38604, 27], [38715, 88], [38905, 25], [39016, 87], [39205, 25], [39317, 85], [39505, 24], [39617, 85], [39806, 23], [39918, 84], [40106, 22], [40219, 83], [40406, 22], [40520, 82], [40706, 21], [40821, 81], [41011, 16], [41121, 81], [41316, 10], [41422, 80], [41618, 8], [41723, 79], [41920, 5], [42024, 77], [42222, 3], [42324, 77], [42523, 1], [42625, 76], [42823, 1], [42926, 75], [43227, 74], [43528, 73], [43828, 73], [44129, 72], [44430, 71], [44731, 69], [45032, 68], [45332, 68], [45633, 67], [45934, 66], [46235, 65], [46536, 64], [46836, 64], [47137, 63], [47438, 61], [47739, 60], [48040, 59], [48340, 59], [48641, 58], [48942, 57], [49243, 56], [49543, 56], [49843, 56], [50143, 55], [50376, 1], [50444, 54], [50675, 3], [50744, 54], [50918, 82]], "point": [149, 91]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan313", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -0.25, "y": 0.9009992, "z": 0.5}, "object_poses": [{"objectName": "Mug_6594cbaa", "position": {"x": 1.47999644, "y": 0.6995, "z": -1.42398977}, "rotation": {"x": 0.0, "y": 179.999939, "z": 0.0}}, {"objectName": "CD_45b3def1", "position": {"x": 1.63853884, "y": 0.70800674, "z": -0.5444213}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_ba9fc135", "position": {"x": 0.741232, "y": 0.704804957, "z": -1.42399073}, "rotation": {"x": 0.0, "y": 179.999939, "z": 0.0}}, {"objectName": "Pen_7d241fed", "position": {"x": 1.5624, "y": 0.714052856, "z": -0.30615747}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pencil_cbb1b9aa", "position": {"x": 1.48054683, "y": 0.492012948, "z": -0.464598477}, "rotation": {"x": 0.0, "y": 269.999969, "z": 0.0}}, {"objectName": "Pencil_cbb1b9aa", "position": {"x": 0.479780942, "y": 0.0855162, "z": -1.58570766}, "rotation": {"x": 0.0, "y": 179.999939, "z": 0.0}}, {"objectName": "Pencil_cbb1b9aa", "position": {"x": -0.0280000567, "y": 0.501216233, "z": -1.58570814}, "rotation": {"x": 0.0, "y": 179.999939, "z": 0.0}}, {"objectName": "CreditCard_8168a6e4", "position": {"x": 1.5724268, "y": 0.488333553, "z": -0.604899168}, "rotation": {"x": 0.0, "y": 269.999969, "z": 0.0}}, {"objectName": "Book_af63f8ac", "position": {"x": 0.387418538, "y": 0.497273326, "z": -1.53239584}, "rotation": {"x": 0.0, "y": 179.999939, "z": 0.0}}, {"objectName": "KeyChain_ce99f974", "position": {"x": -0.0279999971, "y": 0.08267662, "z": -1.63902}, "rotation": {"x": 0.0, "y": 89.99994, "z": 0.0}}, {"objectName": "KeyChain_ce99f974", "position": {"x": 0.110543489, "y": 0.49765718, "z": -1.5857079}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "Box_bc1de45f", "position": {"x": 1.537, "y": 1.652, "z": 0.692}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Book_af63f8ac", "position": {"x": 0.387418419, "y": 0.290309638, "z": -1.42577219}, "rotation": {"x": 0.0, "y": 179.999939, "z": 0.0}}, {"objectName": "AlarmClock_aaa51558", "position": {"x": 1.48626113, "y": 0.7091989, "z": -0.2267362}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_ba9fc135", "position": {"x": -0.120362639, "y": 0.0844264254, "z": -1.372461}, "rotation": {"x": 0.0, "y": 179.999939, "z": 0.0}}, {"objectName": "TissueBox_8d9a2301", "position": {"x": 1.46969056, "y": 1.41350913, "z": 0.3935154}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}, {"objectName": "TeddyBear_52590c6e", "position": {"x": -2.204, "y": 0.5001, "z": 0.181}, "rotation": {"x": 355.000061, "y": 297.467682, "z": 9.162092}}, {"objectName": "CreditCard_8168a6e4", "position": {"x": 0.9874868, "y": 0.7039794, "z": -1.42399025}, "rotation": {"x": 0.0, "y": 179.999939, "z": 0.0}}, {"objectName": "KeyChain_ce99f974", "position": {"x": 1.63853884, "y": 0.7092358, "z": -0.385578752}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "BaseballBat_433a0e8d", "position": {"x": -2.202, "y": 0.04, "z": -1.343}, "rotation": {"x": 5.122642e-06, "y": 44.99992, "z": 89.9998245}}, {"objectName": "TennisRacket_7b4a37c4", "position": {"x": -2.254, "y": 0.324, "z": -1.806}, "rotation": {"x": 15.0002995, "y": 180.0, "z": 180.0}}, {"objectName": "Pillow_75bd6d3d", "position": {"x": -1.923996, "y": 0.5989486, "z": -0.5704446}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_44137064", "position": {"x": 0.037954092, "y": 0.6949834, "z": -1.45929337}, "rotation": {"x": 0.0, "y": 348.828857, "z": 0.0}}, {"objectName": "CD_45b3def1", "position": {"x": -0.576363862, "y": 0.0464673378, "z": 0.7732324}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_7d241fed", "position": {"x": 1.23374152, "y": 0.707152843, "z": -1.326397}, "rotation": {"x": 0.0, "y": 179.999939, "z": 0.0}}, {"objectName": "Pencil_cbb1b9aa", "position": {"x": 1.60046935, "y": 0.7120754, "z": -0.623842537}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_6594cbaa", "position": {"x": 0.9874869, "y": 0.6995, "z": -1.5215832}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}], "object_toggles": [], "random_seed": 3243106175, "scene_num": 313}, "task_id": "trial_T20190906_200931_060619", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3JZLYQ606HJJR_3OS4RQUCRCWH0UACP3MX6FGCA4QBFK", "high_descs": ["Move to the small shelving unit by the large bookcase on the left.", "Pick up the silver pencil on the top shelf. ", "Turn right and move across the room to the desk. ", "Put the pencil on the desk to the right of the brown pencil.", "Turn around and return to the small shelving unit by the large bookcase. ", "Pick up the other silver pencil that is on top of the shelf. ", "Turn right and move to the desk in front of you.", "Put the silver pencil on top of the other silver pencil. "], "task_desc": "Put the pencils where they belong.", "votes": [1, 1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3Q5C1WP23PIOLZZVHUGZERCQZYP51G", "high_descs": ["Turn right and walk to the desk then turn left and walk to the black table with a cd on it.", "Pick up the pencil that's sitting to the right of the cd.", "Turn right and face the desk in front of you.", "Put the pencil to the right of the pencil by the edge of the desk so they are both lined up.", "Turn left to face that table again.", "Pick up the pencil to the left of the bottom shelf on the table.", "Turn right to face the other table again.", "Put the pencil on the right pencil so it makes a T with the point on the bottom pencil."], "task_desc": "Put two pencils on the desk.", "votes": [1, 1, 1]}, {"assignment_id": "A3A0VPWPASCO9J_3DQQ64TANJ2LFAXHYXEY5N71MCQWPL", "high_descs": ["Walk across the room to the bookshelf.", "Pick up the pencil next to the disk.", "Turn to the desk on the right.", "Put the pencil down in front of the red credit card.", "Turn around to the bookshelf on the left.", "Pick up the pencil on the bottom shelf.", "Turn to the desk on the right.", "Set the pencil down on top of the red credit card."], "task_desc": "Move two pencils from the bookshelf to the desk.", "votes": [1, 0, 1]}]}}