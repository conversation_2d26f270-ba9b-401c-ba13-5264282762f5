{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000296.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 34}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|4|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [-4.377064, -4.377064, 1.790638924, 1.790638924, 3.163578, 3.163578]], "coordinateReceptacleObjectId": ["SinkBasin", [-4.4268, -4.4268, 1.3484, 1.3484, 3.141353368, 3.141353368]], "forceVisible": true, "objectId": "Mug|-01.09|+00.79|+00.45"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-3|5|2|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-1.436, -1.436, 0.896, 0.896, 3.6564356, 3.6564356]], "forceVisible": true, "objectId": "Microwave|-00.36|+00.91|+00.22"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-3|4|2|-30"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [-4.377064, -4.377064, 1.790638924, 1.790638924, 3.163578, 3.163578]], "coordinateReceptacleObjectId": ["Cabinet", [-2.0759564, -2.0759564, 1.569164516, 1.569164516, 8.186748, 8.186748]], "forceVisible": true, "objectId": "Mug|-01.09|+00.79|+00.45", "receptacleObjectId": "Cabinet|-00.52|+02.05|+00.39"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-01.09|+00.79|+00.45"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [165, 100, 196, 135], "mask": [[29875, 5], [30171, 13], [30469, 17], [30768, 19], [31067, 21], [31366, 23], [31666, 24], [31965, 26], [32265, 26], [32565, 26], [32865, 29], [33165, 31], [33465, 31], [33765, 26], [33794, 3], [34065, 26], [34093, 3], [34365, 25], [34393, 3], [34665, 25], [34692, 3], [34965, 25], [34991, 4], [35265, 24], [35290, 4], [35565, 24], [35590, 3], [35865, 27], [36165, 26], [36465, 26], [36765, 24], [37065, 23], [37365, 22], [37665, 22], [37965, 22], [38265, 22], [38565, 22], [38865, 21], [39166, 20], [39466, 19], [39767, 18], [40068, 16], [40370, 12]], "point": [180, 116]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-01.09|+00.79|+00.45", "placeStationary": true, "receptacleObjectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 66], [36972, 60], [37200, 62], [37276, 56], [37500, 59], [37577, 55], [37800, 57], [37878, 54], [38100, 56], [38178, 54], [38400, 56], [38480, 52], [38700, 56], [38782, 50], [39000, 56], [39078, 2], [39082, 51], [39300, 56], [39378, 3], [39383, 50], [39600, 56], [39678, 3], [39683, 50], [39900, 57], [39978, 3], [39983, 50], [40200, 57], [40278, 2], [40282, 51], [40500, 57], [40578, 2], [40582, 51], [40800, 58], [40878, 2], [40882, 51], [41100, 58], [41179, 1], [41182, 51], [41400, 58], [41481, 52], [41700, 58], [41781, 52], [42000, 59], [42081, 52], [42300, 59], [42381, 52], [42600, 59], [42680, 53], [42900, 59], [42979, 54], [43200, 59], [43279, 54], [43500, 60], [43580, 53], [43800, 60], [43880, 53], [44100, 60], [44180, 53], [44400, 60], [44480, 53], [44700, 61], [44779, 54], [45000, 61], [45078, 54], [45300, 61], [45377, 55], [45600, 63], [45675, 57], [45900, 65], [45971, 61], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [65, 123]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-1.436, -1.436, 0.896, 0.896, 3.6564356, 3.6564356]], "forceVisible": true, "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-1.436, -1.436, 0.896, 0.896, 3.6564356, 3.6564356]], "forceVisible": true, "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-01.09|+00.79|+00.45"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [56, 124, 82, 154], "mask": [[36966, 6], [37262, 14], [37559, 18], [37857, 21], [38156, 22], [38456, 24], [38756, 26], [39056, 22], [39080, 2], [39356, 22], [39381, 2], [39656, 22], [39681, 2], [39957, 21], [39981, 2], [40257, 21], [40280, 2], [40557, 21], [40580, 2], [40858, 20], [40880, 2], [41158, 21], [41180, 2], [41458, 23], [41758, 23], [42059, 22], [42359, 22], [42659, 21], [42959, 20], [43259, 20], [43560, 20], [43860, 20], [44160, 20], [44460, 20], [44761, 18], [45061, 17], [45361, 16], [45663, 12], [45965, 6]], "point": [69, 138]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.52|+02.05|+00.39"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [52, 48, 246, 212], "mask": [[14184, 132], [14483, 133], [14783, 133], [15083, 133], [15383, 134], [15683, 134], [15982, 135], [16282, 135], [16582, 135], [16882, 136], [17182, 136], [17481, 137], [17781, 137], [18081, 137], [18381, 138], [18681, 138], [18980, 139], [19280, 139], [19580, 139], [19880, 139], [20180, 140], [20479, 141], [20779, 141], [21079, 141], [21379, 141], [21679, 142], [21979, 142], [22278, 143], [22578, 143], [22878, 143], [23178, 144], [23478, 144], [23777, 145], [24077, 145], [24377, 145], [24677, 146], [24977, 146], [25276, 147], [25576, 147], [25876, 147], [26176, 147], [26476, 148], [26775, 149], [27075, 149], [27375, 149], [27675, 149], [27975, 150], [28275, 150], [28574, 151], [28874, 151], [29174, 151], [29474, 152], [29774, 152], [30073, 153], [30373, 153], [30673, 153], [30973, 154], [31273, 154], [31572, 155], [31872, 155], [32172, 155], [32472, 155], [32772, 156], [33071, 157], [33371, 157], [33671, 157], [33971, 157], [34271, 158], [34571, 158], [34870, 159], [35170, 159], [35470, 159], [35770, 160], [36070, 160], [36369, 161], [36669, 161], [36969, 161], [37269, 161], [37569, 162], [37868, 163], [38168, 163], [38468, 163], [38768, 163], [39068, 164], [39367, 165], [39667, 165], [39967, 165], [40267, 165], [40567, 166], [40866, 167], [41166, 167], [41466, 167], [41766, 167], [42066, 168], [42366, 168], [42665, 169], [42965, 169], [43265, 169], [43565, 169], [43865, 170], [44164, 171], [44464, 171], [44764, 171], [45064, 171], [45364, 172], [45663, 173], [45963, 173], [46263, 173], [46563, 173], [46863, 174], [47162, 175], [47462, 175], [47762, 175], [48062, 175], [48362, 176], [48662, 176], [48961, 177], [49261, 177], [49561, 177], [49861, 177], [50161, 178], [50460, 179], [50760, 179], [51060, 179], [51360, 179], [51660, 180], [51959, 181], [52259, 181], [52559, 181], [52859, 181], [53159, 182], [53458, 183], [53758, 183], [54058, 183], [54358, 183], [54658, 183], [54957, 185], [55257, 185], [55557, 83], [55660, 82], [55857, 79], [55964, 78], [56157, 76], [56267, 75], [56457, 74], [56569, 74], [56756, 73], [56871, 72], [57056, 71], [57173, 70], [57356, 69], [57475, 68], [57656, 68], [57776, 67], [57956, 67], [58077, 67], [58255, 67], [58378, 66], [58555, 66], [58679, 65], [58855, 65], [58980, 64], [59155, 64], [59281, 63], [59455, 63], [59582, 63], [59754, 63], [59883, 62], [60054, 63], [60183, 62], [60354, 63], [60483, 62], [60654, 62], [60784, 61], [60954, 62], [61084, 61], [61253, 62], [61385, 61], [61553, 62], [61685, 61], [61853, 59], [61985, 61], [62153, 54], [62285, 61], [62453, 53], [62585, 61], [62753, 51], [62885, 62], [63052, 51], [63185, 62], [63352, 50], [63485, 62]], "point": [149, 129]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-01.09|+00.79|+00.45", "placeStationary": true, "receptacleObjectId": "Cabinet|-00.52|+02.05|+00.39"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 239, 212], "mask": [[0, 72], [300, 73], [600, 73], [900, 73], [1200, 73], [1500, 74], [1800, 74], [2100, 74], [2400, 75], [2700, 75], [3000, 75], [3300, 75], [3600, 76], [3900, 76], [4200, 76], [4500, 76], [4800, 77], [5100, 77], [5400, 77], [5700, 77], [6000, 78], [6300, 78], [6600, 78], [6900, 78], [7200, 79], [7500, 79], [7800, 79], [8100, 79], [8400, 80], [8700, 80], [9000, 80], [9300, 80], [9600, 81], [9900, 81], [10200, 81], [10500, 82], [10800, 82], [11100, 82], [11400, 82], [11700, 83], [12000, 83], [12300, 83], [12600, 83], [12900, 84], [13200, 84], [13500, 84], [13800, 84], [14100, 85], [14400, 85], [14700, 85], [15000, 85], [15300, 84], [15600, 84], [15900, 84], [16200, 84], [16500, 84], [16586, 127], [16800, 83], [16886, 127], [17100, 83], [17185, 128], [17400, 83], [17485, 129], [17700, 83], [17785, 129], [18000, 83], [18085, 129], [18300, 82], [18385, 129], [18600, 82], [18685, 129], [18900, 82], [18984, 130], [19200, 82], [19284, 131], [19500, 82], [19584, 131], [19800, 82], [19884, 131], [20100, 81], [20184, 131], [20400, 81], [20484, 131], [20700, 81], [20783, 133], [21000, 81], [21083, 133], [21300, 81], [21383, 133], [21600, 80], [21683, 133], [21900, 80], [21983, 133], [22200, 80], [22282, 134], [22500, 80], [22582, 135], [22800, 80], [22882, 135], [23100, 79], [23182, 135], [23400, 79], [23482, 135], [23700, 79], [23782, 135], [24000, 79], [24081, 137], [24300, 79], [24381, 137], [24600, 79], [24681, 137], [24900, 78], [24981, 137], [25200, 78], [25281, 137], [25500, 78], [25580, 138], [25800, 78], [25880, 139], [26100, 78], [26180, 139], [26400, 77], [26480, 139], [26700, 77], [26780, 139], [27000, 77], [27080, 139], [27300, 77], [27379, 140], [27600, 77], [27679, 141], [27900, 77], [27979, 141], [28200, 76], [28279, 141], [28500, 76], [28579, 141], [28800, 76], [28878, 142], [29100, 76], [29178, 143], [29400, 76], [29478, 143], [29700, 75], [29778, 143], [30000, 75], [30078, 143], [30300, 75], [30378, 143], [30600, 75], [30677, 144], [30900, 75], [30977, 145], [31200, 74], [31277, 145], [31500, 74], [31577, 145], [31800, 74], [31877, 145], [32100, 74], [32176, 146], [32400, 74], [32476, 146], [32700, 74], [32776, 147], [33000, 73], [33076, 147], [33300, 73], [33376, 147], [33600, 73], [33676, 147], [33900, 73], [33975, 148], [34200, 73], [34275, 149], [34500, 72], [34575, 149], [34800, 72], [34875, 149], [35100, 72], [35175, 149], [35400, 72], [35474, 150], [35700, 72], [35774, 150], [36000, 71], [36074, 151], [36300, 71], [36374, 151], [36600, 71], [36674, 151], [36900, 71], [36974, 151], [37200, 71], [37273, 152], [37500, 71], [37573, 152], [37800, 70], [37873, 153], [38100, 70], [38173, 153], [38400, 70], [38473, 153], [38700, 70], [38772, 154], [39000, 70], [39072, 154], [39300, 69], [39372, 155], [39600, 69], [39672, 155], [39900, 69], [39972, 155], [40200, 69], [40272, 155], [40500, 69], [40571, 156], [40800, 68], [40871, 156], [41100, 68], [41171, 157], [41400, 68], [41471, 157], [41700, 68], [41771, 157], [42000, 68], [42070, 158], [42300, 68], [42370, 158], [42600, 67], [42670, 159], [42900, 67], [42970, 159], [43200, 67], [43270, 159], [43500, 67], [43570, 159], [43800, 67], [43869, 160], [44100, 66], [44169, 160], [44400, 66], [44469, 161], [44700, 66], [44769, 161], [45000, 66], [45069, 161], [45300, 66], [45369, 161], [45600, 65], [45669, 161], [45900, 65], [46200, 65], [46500, 65], [46800, 65], [47100, 65], [47400, 64], [47700, 64], [48000, 64], [48300, 64], [48600, 64], [48900, 63], [49200, 63], [49500, 63], [49800, 63], [50032, 1], [50101, 62], [50166, 1], [50332, 1], [50402, 60], [50465, 2], [50631, 2], [50703, 59], [50765, 3], [50931, 2], [51004, 58], [51065, 4], [51230, 3], [51305, 57], [51365, 4], [51529, 5], [51607, 55], [51665, 5], [51829, 5], [51908, 54], [51965, 6], [52128, 6], [52209, 52], [52264, 7], [52427, 7], [52510, 51], [52564, 8], [52727, 7], [52811, 50], [52864, 8], [53026, 9], [53112, 49], [53164, 9], [53326, 9], [53413, 48], [53464, 10], [53625, 10], [53714, 46], [53763, 11], [53924, 11], [54016, 44], [54063, 12], [54224, 11], [54317, 43], [54363, 13], [54523, 12], [54618, 42], [54663, 13], [54823, 13], [54919, 41], [54963, 14], [55122, 14], [55220, 40], [55263, 14], [55421, 15], [55521, 38], [55562, 16], [55721, 15], [55822, 37], [55862, 17], [56020, 16], [56123, 36], [56162, 17], [56319, 17], [56425, 34], [56462, 18], [56619, 18], [56726, 33], [56762, 19], [56918, 19], [57027, 31], [57061, 20], [57218, 19], [57328, 30], [57361, 21], [57517, 20], [57629, 29], [57661, 21], [57816, 21], [57930, 28], [57961, 22], [58116, 22], [58231, 27], [58261, 23], [58415, 23], [58532, 25], [58561, 23], [58715, 23], [58834, 23], [58860, 25], [59014, 24], [59135, 22], [59160, 26], [59313, 25], [59436, 21], [59460, 26], [59613, 25], [59737, 20], [59760, 27], [59912, 27], [60038, 19], [60060, 28], [60211, 28], [60339, 17], [60359, 29], [60511, 28], [60640, 16], [60659, 30], [60810, 29], [60941, 15], [60959, 30], [61110, 29], [61243, 13], [61259, 31], [61409, 31], [61544, 12], [61559, 32], [61708, 32], [61845, 10], [61859, 32], [62008, 31], [62146, 9], [62160, 32], [62307, 31], [62447, 8], [62461, 32], [62606, 31], [62748, 7], [63049, 6], [63350, 4]], "point": [119, 105]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.52|+02.05|+00.39"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 239, 212], "mask": [[0, 72], [300, 73], [600, 73], [900, 73], [1200, 73], [1500, 74], [1800, 74], [2100, 74], [2400, 75], [2700, 75], [3000, 75], [3300, 75], [3600, 76], [3900, 76], [4200, 76], [4500, 76], [4800, 77], [5100, 77], [5400, 77], [5700, 77], [6000, 78], [6300, 78], [6600, 78], [6900, 78], [7200, 79], [7500, 79], [7800, 79], [8100, 79], [8400, 80], [8700, 80], [9000, 80], [9300, 80], [9600, 81], [9900, 81], [10200, 81], [10500, 82], [10800, 82], [11100, 82], [11400, 82], [11700, 83], [12000, 83], [12300, 83], [12600, 83], [12900, 84], [13200, 84], [13500, 84], [13800, 84], [14100, 85], [14400, 85], [14700, 85], [15000, 85], [15300, 84], [15600, 84], [15900, 84], [16200, 84], [16500, 84], [16586, 127], [16800, 83], [16886, 127], [17100, 83], [17185, 128], [17400, 83], [17485, 129], [17700, 83], [17785, 129], [18000, 83], [18085, 129], [18300, 82], [18385, 129], [18600, 82], [18685, 129], [18900, 82], [18984, 130], [19200, 82], [19284, 131], [19500, 82], [19584, 131], [19800, 82], [19884, 131], [20100, 81], [20184, 131], [20400, 81], [20484, 131], [20700, 81], [20783, 133], [21000, 81], [21083, 133], [21300, 81], [21383, 133], [21600, 80], [21683, 133], [21900, 80], [21983, 133], [22200, 80], [22282, 134], [22500, 80], [22582, 135], [22800, 80], [22882, 135], [23100, 79], [23182, 135], [23400, 79], [23482, 135], [23700, 79], [23782, 135], [24000, 79], [24081, 137], [24300, 79], [24381, 137], [24600, 79], [24681, 137], [24900, 78], [24981, 137], [25200, 78], [25281, 137], [25500, 78], [25580, 138], [25800, 78], [25880, 139], [26100, 78], [26180, 139], [26400, 77], [26480, 139], [26700, 77], [26780, 139], [27000, 77], [27080, 139], [27300, 77], [27379, 140], [27600, 77], [27679, 141], [27900, 77], [27979, 141], [28200, 76], [28279, 141], [28500, 76], [28579, 141], [28800, 76], [28878, 142], [29100, 76], [29178, 143], [29400, 76], [29478, 143], [29700, 75], [29778, 143], [30000, 75], [30078, 143], [30300, 75], [30378, 143], [30600, 75], [30677, 144], [30900, 75], [30977, 145], [31200, 74], [31277, 145], [31500, 74], [31577, 145], [31800, 74], [31877, 145], [32100, 74], [32176, 146], [32400, 74], [32476, 146], [32700, 74], [32776, 147], [33000, 73], [33076, 147], [33300, 73], [33376, 147], [33600, 73], [33676, 147], [33900, 73], [33975, 148], [34200, 73], [34275, 149], [34500, 72], [34575, 149], [34800, 72], [34875, 149], [35100, 72], [35175, 149], [35400, 72], [35474, 150], [35700, 72], [35774, 150], [36000, 71], [36074, 151], [36300, 71], [36374, 151], [36600, 71], [36674, 151], [36900, 71], [36974, 151], [37200, 71], [37273, 152], [37500, 71], [37573, 152], [37800, 70], [37873, 153], [38100, 70], [38173, 153], [38400, 70], [38473, 153], [38700, 70], [38772, 154], [39000, 70], [39072, 154], [39300, 69], [39372, 155], [39600, 69], [39672, 155], [39900, 69], [39972, 155], [40200, 69], [40272, 155], [40500, 69], [40571, 156], [40800, 68], [40871, 156], [41100, 68], [41171, 157], [41400, 68], [41471, 157], [41700, 68], [41771, 157], [42000, 68], [42070, 158], [42300, 68], [42370, 158], [42600, 67], [42670, 159], [42900, 67], [42970, 159], [43200, 67], [43270, 159], [43500, 67], [43570, 159], [43800, 67], [43869, 160], [44100, 66], [44169, 160], [44400, 66], [44469, 161], [44700, 66], [44769, 161], [45000, 66], [45069, 161], [45300, 66], [45369, 161], [45600, 65], [45669, 161], [45900, 65], [46200, 65], [46500, 65], [46800, 65], [47100, 65], [47400, 64], [47700, 64], [48000, 64], [48300, 64], [48600, 64], [48900, 63], [49200, 63], [49500, 63], [49800, 63], [50032, 1], [50101, 62], [50166, 1], [50332, 1], [50402, 60], [50465, 2], [50631, 2], [50703, 59], [50765, 3], [50931, 2], [51004, 58], [51065, 4], [51230, 3], [51305, 57], [51365, 4], [51529, 5], [51607, 55], [51665, 5], [51829, 5], [51908, 54], [51965, 6], [52128, 6], [52209, 52], [52264, 7], [52427, 7], [52510, 51], [52564, 8], [52727, 7], [52811, 50], [52864, 8], [53026, 9], [53112, 49], [53164, 9], [53326, 9], [53413, 48], [53464, 10], [53625, 10], [53714, 46], [53763, 11], [53924, 11], [54016, 44], [54063, 12], [54224, 11], [54317, 43], [54363, 13], [54523, 12], [54618, 42], [54663, 13], [54823, 13], [54919, 41], [54963, 14], [55122, 14], [55220, 40], [55263, 14], [55421, 15], [55521, 38], [55562, 16], [55721, 15], [55822, 37], [55862, 17], [56020, 16], [56123, 36], [56162, 17], [56319, 17], [56425, 34], [56462, 18], [56619, 18], [56726, 33], [56762, 19], [56918, 19], [57027, 31], [57061, 20], [57218, 19], [57328, 30], [57361, 21], [57517, 20], [57629, 29], [57661, 21], [57816, 21], [57930, 28], [57961, 22], [58116, 22], [58231, 27], [58261, 23], [58415, 23], [58532, 25], [58561, 23], [58715, 23], [58834, 23], [58860, 25], [59014, 24], [59135, 22], [59160, 26], [59313, 25], [59436, 21], [59460, 26], [59613, 25], [59737, 20], [59760, 27], [59912, 27], [60038, 19], [60060, 28], [60211, 28], [60339, 17], [60359, 29], [60511, 28], [60640, 16], [60659, 30], [60810, 29], [60941, 15], [60959, 30], [61110, 29], [61243, 13], [61259, 31], [61409, 31], [61544, 12], [61559, 32], [61708, 32], [61845, 10], [61859, 32], [62008, 31], [62146, 9], [62160, 32], [62307, 31], [62447, 8], [62461, 32], [62606, 31], [62748, 7], [63049, 6], [63350, 4]], "point": [119, 105]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan26", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.0, "y": 0.901591063, "z": 2.0}, "object_poses": [{"objectName": "Potato_bbca86d2", "position": {"x": -2.83180285, "y": 0.325335622, "z": 4.64971161}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_bbca86d2", "position": {"x": -1.26696062, "y": 0.822140157, "z": 0.281820148}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_11e3f5dc", "position": {"x": -2.37760639, "y": 0.663533866, "z": 0.300511062}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_11e3f5dc", "position": {"x": -2.03741145, "y": 0.9293358, "z": 0.5181027}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_c473c604", "position": {"x": -2.88892174, "y": 1.18601537, "z": 4.3527627}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_c473c604", "position": {"x": -2.72259068, "y": 0.632254243, "z": 4.57547474}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_d1c1b4b5", "position": {"x": -1.69137478, "y": 1.77788591, "z": 0.1601962}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_57182a42", "position": {"x": -2.480455, "y": 0.900284231, "z": 2.70609045}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "SoapBottle_57182a42", "position": {"x": -0.572060764, "y": 0.900284231, "z": 2.53082561}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Bowl_9504375a", "position": {"x": -2.13632059, "y": 1.7701875, "z": 0.2058212}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_38b3b13a", "position": {"x": -2.89261961, "y": 0.896926939, "z": 2.04548883}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "SaltShaker_38b3b13a", "position": {"x": -2.58349586, "y": 0.896926939, "z": 2.57397}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "PepperShaker_c5422882", "position": {"x": -2.892618, "y": 0.896926939, "z": 2.573971}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "PepperShaker_c5422882", "position": {"x": -0.4878505, "y": 0.658710659, "z": 0.409886}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_8490bc5f", "position": {"x": -0.6578267, "y": 0.980303168, "z": 3.53536224}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Lettuce_8490bc5f", "position": {"x": -2.58349657, "y": 0.980303168, "z": 2.30972886}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Apple_5deff24a", "position": {"x": -2.805756, "y": 0.688914239, "z": 4.204288}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_5deff24a", "position": {"x": -2.89261866, "y": 0.95696336, "z": 2.30972981}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Egg_05581a9a", "position": {"x": -0.835224152, "y": 0.8292578, "z": 0.281820148}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d1c1b4b5", "position": {"x": -0.447004467, "y": 0.8900369, "z": 2.92799973}, "rotation": {"x": 359.9629, "y": 0.0013508189, "z": 359.772766}}, {"objectName": "Fork_80a3685e", "position": {"x": -2.628046, "y": 0.936048567, "z": 3.39521384}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_4b633365", "position": {"x": -0.314754128, "y": 0.9578467, "z": 2.53082466}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Apple_5deff24a", "position": {"x": -1.00791872, "y": 0.8522371, "z": 0.3371}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_8490bc5f", "position": {"x": -0.657827854, "y": 0.980303168, "z": 3.13354754}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Spatula_5216d872", "position": {"x": -2.37741518, "y": 0.9156206, "z": 2.441849}, "rotation": {"x": 0.0, "y": 90.00016, "z": 0.0}}, {"objectName": "Knife_6c866e4b", "position": {"x": -1.71648753, "y": 0.6880228, "z": 0.409886}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_bbca86d2", "position": {"x": -2.628046, "y": 0.9617457, "z": 3.673572}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_d8a5b3b1", "position": {"x": -2.847339, "y": 1.25014567, "z": 4.649712}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_c473c604", "position": {"x": -2.789577, "y": 0.9003033, "z": 2.7060914}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "SaltShaker_38b3b13a", "position": {"x": -2.48045564, "y": 0.896926939, "z": 2.44184923}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "PepperShaker_c5422882", "position": {"x": -0.326131761, "y": 0.658710659, "z": 0.245823622}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_19505874", "position": {"x": -0.380038, "y": 0.662446558, "z": 0.355198532}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_57182a42", "position": {"x": -2.86669946, "y": 0.0538429059, "z": 0.351642847}, "rotation": {"x": 0.0, "y": 26.1012325, "z": 0.0}}, {"objectName": "Pan_b3245c00", "position": {"x": -2.68653822, "y": 0.9002302, "z": 2.04548788}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}, {"objectName": "DishSponge_50b7389e", "position": {"x": -2.99565959, "y": 0.900185, "z": 2.17760944}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Pot_c7fbe144", "position": {"x": -0.5359, "y": 0.928928852, "z": 4.2742}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_11e3f5dc", "position": {"x": -0.2183193, "y": 0.663533866, "z": 0.409886}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_a7306c85", "position": {"x": -1.094266, "y": 0.7908945, "z": 0.447659731}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9504375a", "position": {"x": -2.35680723, "y": 1.7701875, "z": 0.2514462}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1769490370, "scene_num": 26}, "task_id": "trial_T20190909_023042_670199", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1RLO9LNUJIW5S_3NLZY2D53SGYPH8VQ4B2X30XOXIQLD", "high_descs": ["Turn around and walk to the sink.", "Pick up the mug that's in front of you in the sink.", "Take a small step to your left then a small step backwards so you can use the microwave.", "Warm the mug in the microwave then take it back out and close the door.", "Look up at the cupboards above the microwave.", "Open the cabinet that's over the right side of the microwave and put the mug up there before closing the door."], "task_desc": "Put a warm mug in the cupboard.", "votes": [1, 1, 1]}, {"assignment_id": "A2TUUIV61CR0C7_3Z7ISHFUH3MCO2P3JBLBJRQV9018Z0", "high_descs": ["Turn completely around and walk to the sink. ", "Pick up the black mug in the sink. ", "Turn left, walk slightly forward, turn right and face the microwave. ", "Open the microwave, put the mug inside and close the door.  Turn on the microwave.  When it turns off open the microwave door, take out the mug, and close the microwave door. ", "Look up at the upper cabinet above the right side of the microwave. ", "Open the cabinet door, put the mug inside, and close the cabinet door. "], "task_desc": "Warm up the mug in the sink and put it away in the cabinet. ", "votes": [1, 1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3KJYX6QCMC2KZZGC9ITZNBMD10PVJ7", "high_descs": ["Turn around and go to the sink.", "Pick up the mug from the sink.", "Go to your left, to the counter with the microwave on it.", "Open the microwave, put the mug inside, heat it up, and take it out.", "Look up to the cabinet above the counter.", "Open the cabinet and put the mug inside, then close it."], "task_desc": "Put a warm mug in the cabinet.", "votes": [0, 1, 1]}]}}