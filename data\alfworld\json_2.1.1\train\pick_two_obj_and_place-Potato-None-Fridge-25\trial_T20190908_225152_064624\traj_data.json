{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 38}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Potato", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-8|4|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["potato"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Potato", [-8.09272956, -8.09272956, 1.3550632, 1.3550632, 2.9450992, 2.9450992]], "coordinateReceptacleObjectId": ["SinkBasin", [-6.9823656, -6.9823656, 1.183695672, 1.183695672, 2.748186588, 2.748186588]], "forceVisible": true, "objectId": "Potato|-02.02|+00.74|+00.34"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-8|9|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["potato", "fridge"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Potato", [-8.09272956, -8.09272956, 1.3550632, 1.3550632, 2.9450992, 2.9450992]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-11.128, -11.128, 8.8868, 8.8868, 0.0, 0.0]], "forceVisible": true, "objectId": "Potato|-02.02|+00.74|+00.34", "receptacleObjectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-8|4|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["potato"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Potato", [-8.17709256, -8.17709256, 0.1845963, 0.1845963, 3.4495324, 3.4495324]], "coordinateReceptacleObjectId": ["CounterTop", [-6.232, -6.232, 1.112, 1.112, 3.4364, 3.4364]], "forceVisible": true, "objectId": "Potato|-02.04|+00.86|+00.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-8|9|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["potato", "fridge"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Potato", [-8.17709256, -8.17709256, 0.1845963, 0.1845963, 3.4495324, 3.4495324]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-11.128, -11.128, 8.8868, 8.8868, 0.0, 0.0]], "forceVisible": true, "objectId": "Potato|-02.04|+00.86|+00.05", "receptacleObjectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Potato|-02.02|+00.74|+00.34"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [146, 103, 165, 123], "mask": [[30754, 2], [31051, 8], [31349, 12], [31648, 14], [31948, 15], [32247, 17], [32547, 17], [32846, 19], [33146, 19], [33446, 19], [33746, 20], [34046, 20], [34346, 19], [34646, 19], [34947, 18], [35247, 17], [35548, 16], [35849, 14], [36150, 12], [36451, 9], [36753, 5]], "point": [155, 112]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 226], "mask": [[0, 27899], [27900, 298], [28200, 298], [28500, 297], [28800, 296], [29100, 296], [29400, 295], [29700, 294], [30000, 293], [30300, 293], [30600, 292], [30900, 291], [31200, 291], [31500, 290], [31800, 289], [32100, 288], [32400, 288], [32700, 287], [33000, 286], [33300, 285], [33600, 285], [33900, 284], [34200, 283], [34500, 283], [34800, 282], [35100, 281], [35400, 280], [35700, 280], [36001, 278], [36301, 277], [36602, 276], [36903, 274], [37204, 272], [37505, 270], [37806, 269], [38106, 268], [38407, 266], [38708, 265], [39009, 263], [39310, 261], [39611, 259], [39911, 259], [40212, 257], [40513, 255], [40814, 254], [41115, 252], [41416, 250], [41716, 249], [42017, 248], [42318, 246], [42619, 244], [42920, 242], [43221, 241], [43521, 240], [43822, 238], [44123, 237], [44424, 235], [44725, 233], [45026, 231], [45326, 231], [45627, 229], [45928, 227], [46229, 226], [46530, 224], [46831, 222], [47131, 221], [47432, 220], [47733, 218], [48034, 216], [48335, 215], [48636, 213], [48936, 212], [49237, 210], [49538, 209], [49839, 207], [50140, 205], [50441, 204], [50741, 203], [51042, 201], [51343, 199], [51644, 198], [51945, 196], [52246, 194], [52546, 193], [52847, 192], [53148, 190], [53449, 188], [53750, 187], [54051, 185], [54351, 184], [54652, 182], [54953, 181], [55254, 179], [55555, 177], [55856, 176], [56156, 175], [56457, 173], [56758, 171], [57059, 170], [57360, 168], [57661, 166], [57961, 166], [58262, 164], [58563, 162], [58864, 160], [59165, 159], [59465, 158], [59766, 156], [60067, 155], [60368, 153], [60669, 151], [60970, 149], [61270, 149], [61571, 147], [61872, 145], [62173, 143], [62474, 142], [62775, 140], [63075, 139], [63376, 138], [63677, 136], [63978, 134], [64279, 132], [64580, 131], [64880, 130], [65182, 127], [65487, 117], [65792, 107], [66097, 97], [66402, 86], [66707, 76], [67013, 64], [67319, 52], [67628, 33]], "point": [149, 112]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Potato|-02.02|+00.74|+00.34", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 174], [222, 251], [522, 251], [822, 251], [1122, 251], [1422, 251], [1722, 251], [2022, 251], [2322, 251], [2622, 251], [2921, 252], [3221, 252], [3521, 253], [3820, 254], [4120, 255], [4419, 256], [4719, 257], [5018, 258], [5317, 260], [5617, 261], [5916, 263], [6215, 265], [6514, 267], [6813, 270], [7111, 273], [7409, 277], [7707, 281], [8004, 289], [8298, 7980], [16315, 263], [16615, 264], [16915, 264], [17214, 265], [17514, 71], [17595, 104], [17700, 80], [17814, 68], [17898, 100], [18000, 81], [18113, 68], [18200, 98], [18300, 81], [18413, 67], [18501, 96], [18600, 82], [18712, 67], [18802, 95], [18900, 83], [19012, 66], [19103, 93], [19200, 84], [19311, 66], [19404, 91], [19500, 86], [19609, 68], [19704, 91], [19800, 87], [19908, 69], [20005, 89], [20100, 89], [20206, 71], [20309, 84], [20400, 90], [20505, 72], [20610, 83], [20700, 91], [20803, 74], [20910, 82], [21000, 92], [21100, 77], [21210, 82], [21300, 94], [21398, 79], [21504, 4], [21511, 80], [21600, 176], [21804, 4], [21811, 79], [21900, 176], [22104, 3], [22110, 80], [22200, 176], [22404, 3], [22410, 79], [22500, 176], [22703, 3], [22710, 79], [22800, 176], [23003, 3], [23009, 79], [23100, 176], [23303, 2], [23309, 78], [23400, 176], [23603, 1], [23608, 79], [23700, 176], [23908, 78], [24000, 176], [24207, 78], [24300, 175], [24506, 79], [24600, 175], [24805, 79], [24900, 175], [25103, 81], [25200, 175], [25401, 82], [25500, 175], [25701, 81], [25800, 175], [26001, 81], [26100, 175], [26301, 80], [26400, 175], [26600, 80], [26700, 175], [26900, 80], [27000, 176], [27200, 79], [27300, 177], [27499, 80], [27600, 178], [27798, 80], [27900, 179], [28098, 79], [28200, 180], [28396, 81], [28500, 182], [28694, 82], [28800, 185], [28991, 85], [29100, 275], [29400, 274], [29700, 274], [30000, 273], [30300, 272], [30600, 272], [30900, 271], [31200, 271], [31500, 270], [31800, 269], [32100, 269], [32400, 268], [32700, 268], [33000, 267], [33300, 266], [33600, 266], [33900, 265], [34200, 264], [34500, 264], [34800, 263], [35100, 263], [35400, 262], [35700, 261], [36000, 261], [36300, 260], [36600, 260], [36900, 259], [37200, 258], [37500, 258], [37800, 257], [38100, 256], [38400, 256], [38700, 255], [39000, 255], [39300, 254], [39600, 253], [39900, 253], [40200, 252], [40500, 252], [40800, 251], [41100, 250], [41400, 250], [41700, 249], [42000, 248], [42300, 248], [42600, 247], [42900, 247], [43200, 246], [43500, 245], [43800, 245], [44100, 244], [44400, 243], [44700, 243], [45000, 242], [45300, 242], [45600, 241], [45900, 240], [46200, 240], [46500, 239], [46800, 239], [47100, 238], [47400, 237], [47700, 237], [48000, 236], [48300, 235], [48600, 235], [48900, 234], [49200, 234], [49500, 233], [49800, 232], [50100, 232], [50400, 231], [50700, 231], [51000, 230], [51300, 229], [51600, 229], [51900, 228], [52200, 227], [52500, 227], [52800, 226], [53100, 226], [53400, 225], [53700, 224], [54000, 224], [54300, 223], [54600, 223], [54900, 222], [55200, 221], [55500, 221], [55800, 220], [56100, 219], [56400, 219], [56700, 218], [57000, 218], [57300, 217], [57600, 216], [57900, 216], [58200, 215], [58500, 214], [58800, 214], [59100, 213], [59400, 213], [59700, 212], [60000, 211], [60300, 211], [60600, 210], [60900, 210], [61200, 209], [61500, 206], [61800, 87], [62100, 87], [62400, 87], [62700, 87], [63000, 86], [63300, 86], [63600, 86], [63900, 86], [64200, 86], [64500, 86], [64800, 86], [65100, 86], [65400, 85], [65700, 85], [66000, 85], [66300, 85], [66600, 85], [66900, 85], [67200, 85], [67500, 85], [67800, 84], [68100, 84], [68400, 84], [68700, 84], [69000, 84], [69300, 84], [69600, 84], [69900, 84], [70200, 83], [70500, 83], [70800, 83], [71100, 83], [71400, 83], [71700, 83], [72000, 83], [72300, 83], [72600, 82], [72900, 82], [73200, 82], [73500, 82], [73800, 82], [74100, 82], [74400, 82], [74700, 82], [75000, 81], [75300, 81], [75600, 81], [75900, 81], [76200, 81], [76500, 81], [76800, 81], [77100, 81], [77400, 80], [77700, 80], [78000, 80], [78300, 80], [78600, 80], [78900, 80], [79200, 80], [79500, 80], [79800, 80], [80100, 79], [80400, 79], [80700, 79], [81000, 79], [81300, 79], [81600, 79], [81900, 79], [82200, 79], [82500, 78], [82800, 78], [83100, 78], [83400, 78], [83700, 78], [84000, 78], [84300, 78], [84600, 78], [84900, 77], [85200, 77], [85500, 77], [85800, 77], [86100, 77], [86400, 77], [86700, 77], [87000, 77], [87300, 76], [87600, 76], [87900, 76], [88200, 76], [88500, 76], [88800, 76], [89100, 76], [89400, 76], [89700, 75]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 174], [222, 251], [522, 251], [822, 251], [1122, 251], [1422, 251], [1722, 251], [2022, 251], [2322, 251], [2622, 251], [2921, 252], [3221, 252], [3521, 253], [3820, 254], [4120, 255], [4419, 256], [4719, 257], [5018, 258], [5317, 260], [5617, 261], [5916, 263], [6215, 265], [6514, 267], [6813, 270], [7111, 273], [7409, 277], [7707, 281], [8004, 289], [8298, 7980], [16315, 263], [16615, 264], [16915, 264], [17214, 265], [17514, 71], [17595, 104], [17700, 80], [17814, 68], [17898, 100], [18000, 81], [18113, 68], [18200, 98], [18300, 81], [18413, 67], [18501, 96], [18600, 82], [18712, 67], [18802, 95], [18900, 83], [19012, 66], [19103, 93], [19200, 84], [19311, 66], [19404, 91], [19500, 86], [19609, 68], [19704, 91], [19800, 87], [19908, 69], [20005, 89], [20100, 89], [20206, 71], [20309, 84], [20400, 90], [20505, 72], [20610, 83], [20700, 91], [20803, 74], [20910, 82], [21000, 92], [21100, 77], [21210, 82], [21300, 94], [21398, 79], [21504, 4], [21511, 80], [21600, 176], [21804, 4], [21811, 79], [21900, 176], [22104, 3], [22110, 80], [22200, 176], [22404, 3], [22410, 79], [22500, 176], [22703, 3], [22710, 79], [22800, 176], [23003, 3], [23009, 79], [23100, 176], [23303, 2], [23309, 78], [23400, 176], [23603, 1], [23608, 79], [23700, 176], [23908, 78], [24000, 176], [24207, 78], [24300, 175], [24506, 79], [24600, 175], [24805, 79], [24900, 175], [25103, 81], [25200, 175], [25401, 82], [25500, 175], [25701, 81], [25800, 175], [26001, 81], [26100, 175], [26301, 80], [26400, 175], [26600, 80], [26700, 137], [26845, 30], [26900, 80], [27000, 135], [27147, 29], [27200, 79], [27300, 134], [27449, 28], [27499, 80], [27600, 133], [27750, 28], [27798, 80], [27900, 132], [28051, 28], [28098, 79], [28200, 132], [28351, 29], [28396, 81], [28500, 132], [28652, 30], [28694, 82], [28800, 131], [28953, 32], [28991, 85], [29100, 131], [29253, 122], [29400, 131], [29553, 121], [29700, 131], [29853, 121], [30000, 131], [30153, 120], [30300, 131], [30453, 119], [30600, 131], [30753, 119], [30900, 131], [31053, 118], [31200, 132], [31353, 118], [31500, 132], [31652, 118], [31800, 133], [31952, 117], [32100, 134], [32251, 118], [32400, 134], [32550, 118], [32700, 136], [32849, 119], [33000, 137], [33147, 120], [33300, 139], [33445, 121], [33600, 266], [33900, 265], [34200, 264], [34500, 264], [34800, 263], [35100, 263], [35400, 262], [35700, 261], [36000, 261], [36300, 260], [36600, 260], [36900, 259], [37200, 258], [37500, 258], [37800, 257], [38100, 256], [38400, 256], [38700, 255], [39000, 255], [39300, 254], [39600, 253], [39900, 253], [40200, 252], [40500, 252], [40800, 251], [41100, 250], [41400, 250], [41700, 249], [42000, 248], [42300, 248], [42600, 247], [42900, 247], [43200, 246], [43500, 245], [43800, 245], [44100, 244], [44400, 243], [44700, 243], [45000, 242], [45300, 242], [45600, 241], [45900, 240], [46200, 240], [46500, 239], [46800, 239], [47100, 238], [47400, 237], [47700, 237], [48000, 236], [48300, 235], [48600, 235], [48900, 234], [49200, 234], [49500, 233], [49800, 232], [50100, 232], [50400, 231], [50700, 231], [51000, 230], [51300, 229], [51600, 229], [51900, 228], [52200, 227], [52500, 227], [52800, 226], [53100, 226], [53400, 225], [53700, 224], [54000, 224], [54300, 223], [54600, 223], [54900, 222], [55200, 221], [55500, 221], [55800, 220], [56100, 219], [56400, 219], [56700, 218], [57000, 218], [57300, 217], [57600, 216], [57900, 216], [58200, 215], [58500, 214], [58800, 214], [59100, 213], [59400, 213], [59700, 212], [60000, 211], [60300, 211], [60600, 210], [60900, 210], [61200, 209], [61500, 206], [61800, 87], [62100, 87], [62400, 87], [62700, 87], [63000, 86], [63300, 86], [63600, 86], [63900, 86], [64200, 86], [64500, 86], [64800, 86], [65100, 86], [65400, 85], [65700, 85], [66000, 85], [66300, 85], [66600, 85], [66900, 85], [67200, 85], [67500, 85], [67800, 84], [68100, 84], [68400, 84], [68700, 84], [69000, 84], [69300, 84], [69600, 84], [69900, 84], [70200, 83], [70500, 83], [70800, 83], [71100, 83], [71400, 83], [71700, 83], [72000, 83], [72300, 83], [72600, 82], [72900, 82], [73200, 82], [73500, 82], [73800, 82], [74100, 82], [74400, 82], [74700, 82], [75000, 81], [75300, 81], [75600, 81], [75900, 81], [76200, 81], [76500, 81], [76800, 81], [77100, 81], [77400, 80], [77700, 80], [78000, 80], [78300, 80], [78600, 80], [78900, 80], [79200, 80], [79500, 80], [79800, 80], [80100, 79], [80400, 79], [80700, 79], [81000, 79], [81300, 79], [81600, 79], [81900, 79], [82200, 79], [82500, 78], [82800, 78], [83100, 78], [83400, 78], [83700, 78], [84000, 78], [84300, 78], [84600, 78], [84900, 77], [85200, 77], [85500, 77], [85800, 77], [86100, 77], [86400, 77], [86700, 77], [87000, 77], [87300, 76], [87600, 76], [87900, 76], [88200, 76], [88500, 76], [88800, 76], [89100, 76], [89400, 76], [89700, 75]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Potato|-02.04|+00.86|+00.05"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [151, 105, 168, 122], "mask": [[31356, 6], [31654, 10], [31953, 13], [32252, 15], [32552, 15], [32852, 16], [33151, 17], [33451, 17], [33751, 18], [34051, 18], [34351, 17], [34652, 16], [34952, 16], [35252, 15], [35553, 13], [35854, 11], [36156, 8], [36458, 4]], "point": [159, 112]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 226], "mask": [[0, 27899], [27900, 298], [28200, 298], [28500, 297], [28800, 296], [29100, 296], [29400, 295], [29700, 294], [30000, 293], [30300, 293], [30600, 292], [30900, 291], [31200, 291], [31500, 290], [31800, 289], [32100, 288], [32400, 288], [32700, 287], [33000, 286], [33300, 285], [33600, 285], [33900, 284], [34200, 283], [34500, 283], [34800, 282], [35100, 281], [35400, 280], [35700, 280], [36001, 278], [36301, 277], [36602, 276], [36903, 274], [37204, 272], [37505, 270], [37806, 269], [38106, 268], [38407, 266], [38708, 265], [39009, 263], [39310, 261], [39611, 259], [39911, 259], [40212, 257], [40513, 255], [40814, 254], [41115, 252], [41416, 250], [41716, 249], [42017, 248], [42318, 246], [42619, 244], [42920, 242], [43221, 241], [43521, 240], [43822, 238], [44123, 237], [44424, 235], [44725, 233], [45026, 231], [45326, 231], [45627, 229], [45928, 227], [46229, 226], [46530, 224], [46831, 222], [47131, 221], [47432, 220], [47733, 218], [48034, 216], [48335, 215], [48636, 213], [48936, 212], [49237, 210], [49538, 209], [49839, 207], [50140, 205], [50441, 204], [50741, 203], [51042, 201], [51343, 199], [51644, 198], [51945, 196], [52246, 194], [52546, 193], [52847, 192], [53148, 190], [53449, 188], [53750, 187], [54051, 185], [54351, 184], [54652, 182], [54953, 181], [55254, 179], [55555, 177], [55856, 176], [56156, 175], [56457, 173], [56758, 171], [57059, 170], [57360, 168], [57661, 166], [57961, 166], [58262, 164], [58563, 162], [58864, 160], [59165, 159], [59465, 158], [59766, 156], [60067, 155], [60368, 153], [60669, 151], [60970, 149], [61270, 149], [61571, 147], [61872, 145], [62173, 143], [62474, 142], [62775, 140], [63075, 139], [63376, 138], [63677, 136], [63978, 134], [64279, 132], [64580, 131], [64880, 130], [65182, 127], [65487, 117], [65792, 107], [66097, 97], [66402, 86], [66707, 76], [67013, 64], [67319, 52], [67628, 33]], "point": [149, 112]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Potato|-02.04|+00.86|+00.05", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 174], [222, 251], [522, 251], [822, 251], [1122, 251], [1422, 251], [1722, 251], [2022, 251], [2322, 251], [2622, 251], [2921, 252], [3221, 252], [3521, 253], [3820, 254], [4120, 255], [4419, 256], [4719, 257], [5018, 258], [5317, 260], [5617, 261], [5916, 263], [6215, 265], [6514, 267], [6813, 270], [7111, 273], [7409, 277], [7707, 281], [8004, 289], [8298, 7980], [16315, 263], [16615, 264], [16915, 264], [17214, 265], [17514, 71], [17595, 104], [17700, 80], [17814, 68], [17898, 100], [18000, 81], [18113, 68], [18200, 98], [18300, 81], [18413, 67], [18501, 96], [18600, 82], [18712, 67], [18802, 95], [18900, 83], [19012, 66], [19103, 93], [19200, 84], [19311, 66], [19404, 91], [19500, 86], [19609, 68], [19704, 91], [19800, 87], [19908, 69], [20005, 89], [20100, 89], [20206, 71], [20309, 84], [20400, 90], [20505, 72], [20610, 83], [20700, 91], [20803, 74], [20910, 82], [21000, 92], [21100, 77], [21210, 82], [21300, 94], [21398, 79], [21504, 4], [21511, 80], [21600, 176], [21804, 4], [21811, 79], [21900, 176], [22104, 3], [22110, 80], [22200, 176], [22404, 3], [22410, 79], [22500, 176], [22703, 3], [22710, 79], [22800, 176], [23003, 3], [23009, 79], [23100, 176], [23303, 2], [23309, 78], [23400, 176], [23603, 1], [23608, 79], [23700, 176], [23908, 78], [24000, 176], [24207, 78], [24300, 175], [24506, 79], [24600, 175], [24805, 79], [24900, 175], [25103, 81], [25200, 175], [25401, 82], [25500, 175], [25701, 81], [25800, 175], [26001, 81], [26100, 175], [26301, 80], [26400, 175], [26600, 80], [26700, 137], [26845, 30], [26900, 80], [27000, 135], [27147, 29], [27200, 79], [27300, 134], [27449, 28], [27499, 80], [27600, 133], [27750, 28], [27798, 80], [27900, 132], [28051, 28], [28098, 79], [28200, 132], [28351, 29], [28396, 81], [28500, 132], [28652, 30], [28694, 82], [28800, 131], [28953, 32], [28991, 85], [29100, 131], [29253, 122], [29400, 131], [29553, 121], [29700, 131], [29853, 121], [30000, 131], [30153, 120], [30300, 131], [30453, 119], [30600, 131], [30753, 119], [30900, 131], [31053, 118], [31200, 132], [31353, 118], [31500, 132], [31652, 118], [31800, 133], [31952, 117], [32100, 134], [32251, 118], [32400, 134], [32550, 118], [32700, 136], [32849, 119], [33000, 137], [33147, 120], [33300, 139], [33445, 121], [33600, 266], [33900, 265], [34200, 264], [34500, 264], [34800, 263], [35100, 263], [35400, 262], [35700, 261], [36000, 261], [36300, 260], [36600, 260], [36900, 259], [37200, 258], [37500, 258], [37800, 257], [38100, 256], [38400, 256], [38700, 255], [39000, 255], [39300, 254], [39600, 253], [39900, 253], [40200, 252], [40500, 252], [40800, 251], [41100, 250], [41400, 250], [41700, 249], [42000, 248], [42300, 248], [42600, 247], [42900, 247], [43200, 246], [43500, 245], [43800, 245], [44100, 244], [44400, 243], [44700, 243], [45000, 242], [45300, 242], [45600, 241], [45900, 240], [46200, 240], [46500, 239], [46800, 239], [47100, 238], [47400, 237], [47700, 237], [48000, 236], [48300, 235], [48600, 235], [48900, 234], [49200, 234], [49500, 233], [49800, 232], [50100, 232], [50400, 231], [50700, 231], [51000, 230], [51300, 229], [51600, 229], [51900, 228], [52200, 227], [52500, 227], [52800, 226], [53100, 226], [53400, 225], [53700, 224], [54000, 224], [54300, 223], [54600, 223], [54900, 222], [55200, 221], [55500, 221], [55800, 220], [56100, 219], [56400, 219], [56700, 218], [57000, 218], [57300, 217], [57600, 216], [57900, 216], [58200, 215], [58500, 214], [58800, 214], [59100, 213], [59400, 213], [59700, 212], [60000, 211], [60300, 211], [60600, 210], [60900, 210], [61200, 209], [61500, 206], [61800, 87], [62100, 87], [62400, 87], [62700, 87], [63000, 86], [63300, 86], [63600, 86], [63900, 86], [64200, 86], [64500, 86], [64800, 86], [65100, 86], [65400, 85], [65700, 85], [66000, 85], [66300, 85], [66600, 85], [66900, 85], [67200, 85], [67500, 85], [67800, 84], [68100, 84], [68400, 84], [68700, 84], [69000, 84], [69300, 84], [69600, 84], [69900, 84], [70200, 83], [70500, 83], [70800, 83], [71100, 83], [71400, 83], [71700, 83], [72000, 83], [72300, 83], [72600, 82], [72900, 82], [73200, 82], [73500, 82], [73800, 82], [74100, 82], [74400, 82], [74700, 82], [75000, 81], [75300, 81], [75600, 81], [75900, 81], [76200, 81], [76500, 81], [76800, 81], [77100, 81], [77400, 80], [77700, 80], [78000, 80], [78300, 80], [78600, 80], [78900, 80], [79200, 80], [79500, 80], [79800, 80], [80100, 79], [80400, 79], [80700, 79], [81000, 79], [81300, 79], [81600, 79], [81900, 79], [82200, 79], [82500, 78], [82800, 78], [83100, 78], [83400, 78], [83700, 78], [84000, 78], [84300, 78], [84600, 78], [84900, 77], [85200, 77], [85500, 77], [85800, 77], [86100, 77], [86400, 77], [86700, 77], [87000, 77], [87300, 76], [87600, 76], [87900, 76], [88200, 76], [88500, 76], [88800, 76], [89100, 76], [89400, 76], [89700, 75]], "point": [149, 149]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.78|+00.00|+02.22"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 174], [222, 251], [522, 251], [822, 251], [1122, 251], [1422, 251], [1722, 251], [2022, 251], [2322, 251], [2622, 251], [2921, 252], [3221, 252], [3521, 253], [3820, 254], [4120, 255], [4419, 256], [4719, 257], [5018, 258], [5317, 260], [5617, 261], [5916, 263], [6215, 265], [6514, 267], [6813, 270], [7111, 273], [7409, 277], [7707, 281], [8004, 289], [8298, 7980], [16315, 263], [16615, 264], [16915, 264], [17214, 265], [17514, 71], [17595, 104], [17700, 80], [17814, 68], [17898, 100], [18000, 81], [18113, 68], [18200, 98], [18300, 81], [18413, 67], [18501, 96], [18600, 82], [18712, 67], [18802, 95], [18900, 83], [19012, 66], [19103, 93], [19200, 84], [19311, 66], [19404, 91], [19500, 86], [19609, 68], [19704, 91], [19800, 87], [19908, 69], [20005, 89], [20100, 89], [20206, 71], [20309, 84], [20400, 90], [20505, 72], [20610, 83], [20700, 91], [20803, 74], [20910, 82], [21000, 92], [21100, 77], [21210, 82], [21300, 94], [21398, 79], [21504, 4], [21511, 80], [21600, 176], [21804, 4], [21811, 79], [21900, 176], [22104, 3], [22110, 80], [22200, 176], [22404, 3], [22410, 79], [22500, 176], [22703, 3], [22710, 79], [22800, 176], [23003, 3], [23009, 79], [23100, 176], [23303, 2], [23309, 78], [23400, 176], [23603, 1], [23608, 79], [23700, 176], [23908, 78], [24000, 176], [24207, 78], [24300, 175], [24506, 79], [24600, 175], [24805, 79], [24900, 175], [25103, 81], [25200, 175], [25401, 82], [25500, 175], [25701, 81], [25800, 175], [26001, 81], [26100, 175], [26301, 80], [26400, 175], [26600, 80], [26700, 137], [26845, 17], [26870, 5], [26900, 80], [27000, 135], [27147, 13], [27172, 4], [27200, 79], [27300, 134], [27449, 10], [27473, 4], [27499, 80], [27600, 133], [27750, 8], [27775, 3], [27798, 80], [27900, 132], [28051, 6], [28076, 3], [28098, 79], [28200, 132], [28351, 6], [28376, 4], [28396, 81], [28500, 132], [28652, 4], [28677, 5], [28694, 82], [28800, 131], [28953, 3], [28977, 8], [28991, 85], [29100, 131], [29253, 3], [29278, 97], [29400, 131], [29553, 3], [29578, 96], [29700, 131], [29853, 2], [29878, 96], [30000, 131], [30153, 2], [30178, 95], [30300, 131], [30453, 3], [30478, 94], [30600, 131], [30753, 3], [30778, 94], [30900, 131], [31053, 3], [31078, 93], [31200, 132], [31353, 3], [31377, 94], [31500, 132], [31652, 5], [31677, 93], [31800, 133], [31952, 5], [31976, 93], [32100, 134], [32251, 7], [32275, 94], [32400, 134], [32550, 9], [32575, 93], [32700, 136], [32849, 11], [32873, 95], [33000, 137], [33147, 14], [33172, 95], [33300, 139], [33445, 18], [33470, 96], [33600, 266], [33900, 265], [34200, 264], [34500, 264], [34800, 263], [35100, 263], [35400, 262], [35700, 261], [36000, 261], [36300, 260], [36600, 260], [36900, 259], [37200, 258], [37500, 258], [37800, 257], [38100, 256], [38400, 256], [38700, 255], [39000, 255], [39300, 254], [39600, 253], [39900, 253], [40200, 252], [40500, 252], [40800, 251], [41100, 250], [41400, 250], [41700, 249], [42000, 248], [42300, 248], [42600, 247], [42900, 247], [43200, 246], [43500, 245], [43800, 245], [44100, 244], [44400, 243], [44700, 243], [45000, 242], [45300, 242], [45600, 241], [45900, 240], [46200, 240], [46500, 239], [46800, 239], [47100, 238], [47400, 237], [47700, 237], [48000, 236], [48300, 235], [48600, 235], [48900, 234], [49200, 234], [49500, 233], [49800, 232], [50100, 232], [50400, 231], [50700, 231], [51000, 230], [51300, 229], [51600, 229], [51900, 228], [52200, 227], [52500, 227], [52800, 226], [53100, 226], [53400, 225], [53700, 224], [54000, 224], [54300, 223], [54600, 223], [54900, 222], [55200, 221], [55500, 221], [55800, 220], [56100, 219], [56400, 219], [56700, 218], [57000, 218], [57300, 217], [57600, 216], [57900, 216], [58200, 215], [58500, 214], [58800, 214], [59100, 213], [59400, 213], [59700, 212], [60000, 211], [60300, 211], [60600, 210], [60900, 210], [61200, 209], [61500, 206], [61800, 87], [62100, 87], [62400, 87], [62700, 87], [63000, 86], [63300, 86], [63600, 86], [63900, 86], [64200, 86], [64500, 86], [64800, 86], [65100, 86], [65400, 85], [65700, 85], [66000, 85], [66300, 85], [66600, 85], [66900, 85], [67200, 85], [67500, 85], [67800, 84], [68100, 84], [68400, 84], [68700, 84], [69000, 84], [69300, 84], [69600, 84], [69900, 84], [70200, 83], [70500, 83], [70800, 83], [71100, 83], [71400, 83], [71700, 83], [72000, 83], [72300, 83], [72600, 82], [72900, 82], [73200, 82], [73500, 82], [73800, 82], [74100, 82], [74400, 82], [74700, 82], [75000, 81], [75300, 81], [75600, 81], [75900, 81], [76200, 81], [76500, 81], [76800, 81], [77100, 81], [77400, 80], [77700, 80], [78000, 80], [78300, 80], [78600, 80], [78900, 80], [79200, 80], [79500, 80], [79800, 80], [80100, 79], [80400, 79], [80700, 79], [81000, 79], [81300, 79], [81600, 79], [81900, 79], [82200, 79], [82500, 78], [82800, 78], [83100, 78], [83400, 78], [83700, 78], [84000, 78], [84300, 78], [84600, 78], [84900, 77], [85200, 77], [85500, 77], [85800, 77], [86100, 77], [86400, 77], [86700, 77], [87000, 77], [87300, 76], [87600, 76], [87900, 76], [88200, 76], [88500, 76], [88800, 76], [89100, 76], [89400, 76], [89700, 75]], "point": [149, 149]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan25", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -2.0, "y": 0.9009992, "z": 1.5}, "object_poses": [{"objectName": "Pan_e0764688", "position": {"x": -0.2442, "y": 0.8297, "z": 2.0755}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Potato_a3c14b3a", "position": {"x": -0.47795558, "y": 0.8623831, "z": 0.6589823}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_a3c14b3a", "position": {"x": -2.04427314, "y": 0.8623831, "z": 0.046149075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_77ce7117", "position": {"x": -2.84096551, "y": 0.838599861, "z": 0.20387885}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_68224885", "position": {"x": -1.41260135, "y": 0.7219329, "z": 0.210238934}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_61785f6c", "position": {"x": -0.1476208, "y": 0.823938131, "z": 0.426242471}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_61785f6c", "position": {"x": -2.50179482, "y": 1.8493973, "z": 1.92695618}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_29dab58d", "position": {"x": -1.46534276, "y": 0.130002677, "z": 0.440719754}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_298dd93a", "position": {"x": -2.375244, "y": 0.849218369, "z": 0.20387885}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_4c742e05", "position": {"x": -2.726792, "y": 1.44222367, "z": 2.04581022}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_4c742e05", "position": {"x": -2.78200054, "y": 1.44222367, "z": 2.39759}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_69e12ad1", "position": {"x": -1.20742941, "y": 0.8185999, "z": 0.046149075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_69e12ad1", "position": {"x": -2.957396, "y": 0.818599939, "z": 0.1297577}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_407d7c3b", "position": {"x": -2.504354, "y": 0.8185999, "z": 0.707708836}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_407d7c3b", "position": {"x": -2.85834, "y": 1.430771, "z": 0.6745089}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b2f1312c", "position": {"x": -2.8372066, "y": 0.9087977, "z": 2.04580879}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -0.314988971, "y": 0.896565557, "z": 0.5003637}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -0.362648815, "y": 0.129467249, "z": 2.64073944}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -2.78199935, "y": 1.21668339, "z": 1.957865}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Fork_2c8fe53a", "position": {"x": -0.400792, "y": 0.8241485, "z": 1.32607329}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_3943cbe8", "position": {"x": -2.49167442, "y": 0.8679624, "z": 0.4262423}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_e0764688", "position": {"x": -0.2442, "y": 0.8297, "z": 1.684}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_3a4a4d0d", "position": {"x": -2.397328, "y": 1.43380368, "z": 0.114731081}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_ccf3345d", "position": {"x": -0.531748533, "y": 0.8226794, "z": 2.09645438}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_2ee69558", "position": {"x": -2.66945338, "y": 1.18149, "z": 2.37316322}, "rotation": {"x": 0.0025834213, "y": 359.9513, "z": 0.438487262}}, {"objectName": "Potato_a3c14b3a", "position": {"x": -2.02318239, "y": 0.7362748, "z": 0.3387658}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_69e12ad1", "position": {"x": -2.05799365, "y": 0.1203081, "z": 0.362672359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_29dab58d", "position": {"x": -2.569851, "y": 0.126223862, "z": 1.51088572}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_77ce7117", "position": {"x": -0.169301391, "y": 0.8385998, "z": 0.9925278}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_298dd93a", "position": {"x": -1.37479818, "y": 0.8492183, "z": 0.5594009}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_68224885", "position": {"x": -0.2292566, "y": 0.08077991, "z": 2.53909016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_61785f6c", "position": {"x": -2.83951759, "y": 1.43537343, "z": 0.238338888}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "ButterKnife_d1baac51", "position": {"x": -2.4939487, "y": 0.812210739, "z": 0.476511866}, "rotation": {"x": 0.0, "y": 50.7348976, "z": 0.0}}, {"objectName": "Lettuce_b2f1312c", "position": {"x": -1.8365, "y": 0.7415, "z": 0.238453716}, "rotation": {"x": 0.0, "y": 55.91092, "z": 0.0}}, {"objectName": "PepperShaker_407d7c3b", "position": {"x": -1.15182948, "y": 0.8185999, "z": 0.426242471}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_eb848da8", "position": {"x": -1.10540831, "y": 0.6980382, "z": 0.444575071}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_db4f73f8", "position": {"x": -0.979999542, "y": 0.8554753, "z": 0.249999955}, "rotation": {"x": -5.270255e-06, "y": 29.9999123, "z": -4.99874841e-06}}, {"objectName": "Spoon_df0734fa", "position": {"x": -2.813009, "y": 0.8247293, "z": 0.871343434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_57da2f86", "position": {"x": -2.72679353, "y": 0.8198504, "z": 2.39759135}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_4c742e05", "position": {"x": -2.81078959, "y": 0.918047249, "z": 1.28474021}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}], "object_toggles": [], "random_seed": 2935392558, "scene_num": 25}, "task_id": "trial_T20190908_225152_064624", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3T9K2RY06UO14_34HJIJKLP8NUWHXR6B6VH9XUJ2Y4VM", "high_descs": ["Turn and go to the sink.", "Pick up a potato in the sink. ", "Turn and go to the refrigerator. ", "Put the potato in the refrigerator.", "Turn and go to the sink.", "Pick up a potato by the sink. ", "Turn and go to the refrigerator. ", "Put the potato in the refrigerator."], "task_desc": "Move two potatoes to the refrigerator. ", "votes": [1, 1, 1]}, {"assignment_id": "A12HZGOZQD5YK7_37FMASSAYFI9VAY0MJHZAYD6LUOBIB", "high_descs": ["Turn right and go to the sink.", "Pick up the potato in the sink.", "Turn around and go to the fridge.", "Put the potato on the top shelf in the fridge.", "Turn left and go back to the sink.", "Pick up the potato in front of the glass.", "Turn around and head back to the fridge.", "Put the potato in the fridge to the right of the other potato."], "task_desc": "Put two potatoes in the fridge.", "votes": [1, 1, 1]}, {"assignment_id": "ANBWJZYU2A68T_3KMS4QQVK5HQEM04ROO764J4SVNFKO", "high_descs": ["Turn to your right and go the sink", "Pick up the dark fruit from the right sink with a cabbage", "Turn around and go to the fridge", "Open the fridge and put the fruit on the tray with a cup, close the fridge", "Go back to the sink", "Pick up the second fruit from the counter beyond the sink", "Go back to the fridge", "Open the fridge, put the fruit by the side of the first fruit on the same tray, and close the fridge"], "task_desc": "Put two dark fruits in the fridge", "votes": [0, 1, 1]}]}}