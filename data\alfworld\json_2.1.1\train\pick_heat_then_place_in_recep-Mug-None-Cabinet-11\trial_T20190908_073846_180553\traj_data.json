{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 35}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-8|-1|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [-7.7158146, -7.7158146, 0.660800576, 0.660800576, 3.788900136, 3.788900136]], "coordinateReceptacleObjectId": ["DiningTable", [-8.812, -8.812, 1.792, 1.792, 3.96, 3.96]], "forceVisible": true, "objectId": "Mug|-01.93|+00.95|+00.17"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-4|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-2|2|60"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [-7.7158146, -7.7158146, 0.660800576, 0.660800576, 3.788900136, 3.788900136]], "coordinateReceptacleObjectId": ["Cabinet", [-1.586, -1.586, -5.40400028, -5.40400028, 1.610000132, 1.610000132]], "forceVisible": true, "objectId": "Mug|-01.93|+00.95|+00.17", "receptacleObjectId": "Cabinet|-00.40|+00.40|-01.35"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-01.93|+00.95|+00.17"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [160, 99, 194, 147], "mask": [[29575, 3], [29874, 4], [30174, 5], [30471, 12], [30768, 17], [31067, 20], [31365, 24], [31664, 26], [31963, 28], [32262, 30], [32562, 31], [32861, 32], [33161, 33], [33461, 33], [33761, 33], [34060, 35], [34360, 35], [34660, 35], [34961, 34], [35261, 34], [35561, 33], [35861, 33], [36161, 32], [36461, 32], [36761, 32], [37061, 31], [37361, 31], [37661, 31], [37961, 30], [38261, 30], [38561, 30], [38861, 29], [39161, 29], [39461, 29], [39761, 29], [40061, 28], [40361, 28], [40661, 28], [40961, 28], [41261, 27], [41561, 27], [41862, 26], [42162, 25], [42463, 24], [42764, 22], [43065, 20], [43366, 18], [43668, 14], [43971, 8]], "point": [177, 122]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-01.93|+00.95|+00.17", "placeStationary": true, "receptacleObjectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 271], [28517, 271], [28816, 271], [29115, 272], [29415, 273], [29714, 274], [30013, 275], [30313, 275], [30612, 276], [30911, 277], [31211, 277], [31510, 278], [31809, 279], [32109, 279], [32408, 280], [32708, 280], [33007, 280], [33306, 281], [33606, 281], [33905, 281], [34204, 282], [34504, 282], [34803, 282], [35102, 283], [35402, 282], [35701, 283], [36000, 283], [36300, 283], [36600, 282], [36900, 282], [37200, 281], [37500, 281], [37800, 280], [38100, 280], [38400, 280], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 121], [28347, 141], [28517, 118], [28650, 138], [28816, 116], [28952, 135], [29115, 116], [29254, 133], [29415, 115], [29555, 133], [29714, 115], [29856, 132], [30013, 115], [30156, 132], [30313, 115], [30456, 132], [30612, 116], [30757, 131], [30911, 117], [31061, 127], [31211, 117], [31362, 126], [31510, 118], [31656, 3], [31663, 125], [31809, 119], [31956, 4], [31963, 125], [32109, 120], [32256, 4], [32263, 125], [32408, 121], [32556, 4], [32563, 125], [32708, 121], [32856, 3], [32862, 126], [33007, 122], [33156, 3], [33162, 125], [33306, 123], [33456, 3], [33461, 126], [33606, 123], [33755, 3], [33761, 126], [33905, 125], [34055, 3], [34061, 125], [34204, 126], [34355, 2], [34360, 126], [34504, 126], [34655, 2], [34660, 126], [34803, 127], [34955, 1], [34959, 126], [35102, 128], [35259, 126], [35402, 128], [35558, 126], [35701, 129], [35857, 127], [36000, 130], [36157, 126], [36300, 130], [36456, 127], [36600, 130], [36755, 127], [36900, 131], [37055, 127], [37200, 131], [37355, 126], [37500, 131], [37655, 126], [37800, 131], [37955, 125], [38100, 131], [38255, 125], [38400, 131], [38555, 125], [38700, 131], [38854, 125], [39000, 132], [39154, 125], [39300, 133], [39453, 125], [39600, 134], [39752, 126], [39900, 135], [40050, 127], [40200, 137], [40348, 129], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-01.93|+00.95|+00.17"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [128, 95, 162, 135], "mask": [[28338, 9], [28635, 15], [28932, 20], [29231, 23], [29530, 25], [29829, 27], [30128, 28], [30428, 28], [30728, 29], [31028, 33], [31328, 34], [31628, 28], [31659, 4], [31928, 28], [31960, 3], [32229, 27], [32260, 3], [32529, 27], [32560, 3], [32829, 27], [32859, 3], [33129, 27], [33159, 3], [33429, 27], [33459, 2], [33729, 26], [33758, 3], [34030, 25], [34058, 3], [34330, 25], [34357, 3], [34630, 25], [34657, 3], [34930, 25], [34956, 3], [35230, 29], [35530, 28], [35830, 27], [36130, 27], [36430, 26], [36730, 25], [37031, 24], [37331, 24], [37631, 24], [37931, 24], [38231, 24], [38531, 24], [38831, 23], [39132, 22], [39433, 20], [39734, 18], [40035, 15], [40337, 11]], "point": [145, 114]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 271], [28517, 271], [28816, 271], [29115, 272], [29415, 273], [29714, 274], [30013, 275], [30313, 275], [30612, 276], [30911, 277], [31211, 277], [31510, 278], [31809, 279], [32109, 279], [32408, 280], [32708, 280], [33007, 280], [33306, 281], [33606, 281], [33905, 281], [34204, 282], [34504, 282], [34803, 282], [35102, 283], [35402, 282], [35701, 283], [36000, 283], [36300, 283], [36600, 282], [36900, 282], [37200, 281], [37500, 281], [37800, 280], [38100, 280], [38400, 280], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.40|+00.40|-01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [17, 83, 116, 151], "mask": [[24617, 85], [24917, 85], [25217, 86], [25517, 86], [25817, 86], [26118, 85], [26419, 84], [26719, 85], [27020, 84], [27321, 83], [27621, 83], [27922, 82], [28222, 83], [28523, 82], [28824, 81], [29124, 81], [29425, 81], [29726, 80], [30026, 80], [30327, 79], [30627, 79], [30928, 79], [31229, 78], [31529, 78], [31830, 77], [32131, 77], [32431, 77], [32732, 76], [33032, 76], [33333, 75], [33634, 75], [33934, 75], [34235, 74], [34535, 74], [34836, 74], [35137, 73], [35437, 73], [35738, 72], [36039, 71], [36339, 72], [36640, 71], [36940, 71], [37241, 70], [37542, 70], [37842, 70], [38143, 69], [38444, 68], [38744, 68], [39045, 68], [39345, 68], [39646, 67], [39947, 66], [40247, 67], [40548, 66], [40849, 65], [41149, 65], [41450, 64], [41750, 65], [42051, 64], [42352, 63], [42652, 63], [42953, 63], [43253, 63], [43554, 62], [43855, 61], [44155, 61], [44456, 61], [44757, 60], [45057, 60]], "point": [66, 116]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-01.93|+00.95|+00.17", "placeStationary": true, "receptacleObjectId": "Cabinet|-00.40|+00.40|-01.35"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [18, 83, 117, 211], "mask": [[24618, 84], [24918, 85], [25219, 85], [25520, 84], [25820, 84], [26121, 83], [26421, 83], [26722, 83], [27023, 82], [27323, 82], [27624, 81], [27925, 80], [28225, 81], [28526, 80], [28826, 80], [29127, 49], [29181, 25], [29428, 45], [29484, 23], [29728, 44], [29785, 22], [30029, 42], [30085, 22], [30329, 41], [30386, 21], [30630, 39], [30686, 21], [30931, 38], [30986, 22], [31231, 38], [31287, 21], [31532, 37], [31587, 21], [31832, 38], [31887, 21], [32133, 37], [32188, 20], [32434, 36], [32488, 21], [32734, 37], [32788, 21], [33035, 36], [33089, 20], [33335, 37], [33389, 20], [33636, 36], [33689, 21], [33937, 35], [33990, 20], [34237, 36], [34290, 20], [34538, 35], [34590, 20], [34838, 36], [34891, 19], [35139, 35], [35191, 20], [35440, 34], [35491, 20], [35740, 35], [35792, 19], [36041, 34], [36092, 19], [36342, 34], [36391, 20], [36642, 34], [36691, 21], [36943, 34], [36990, 22], [37243, 34], [37289, 23], [37544, 34], [37588, 24], [37845, 36], [37885, 28], [38145, 68], [38446, 67], [38746, 67], [39047, 66], [39348, 66], [39648, 66], [39949, 65], [40249, 65], [40550, 65], [40851, 64], [41151, 64], [41452, 63], [41752, 63], [42053, 63], [42354, 62], [42654, 62], [42955, 61], [43255, 61], [43556, 61], [43857, 60], [44157, 60], [44458, 59], [44791, 27], [45091, 27], [45391, 26], [45690, 27], [45990, 27], [46290, 27], [46590, 27], [46890, 27], [47190, 27], [47490, 27], [47789, 28], [48089, 28], [48389, 28], [48689, 28], [48989, 28], [49289, 28], [49590, 26], [49890, 26], [50191, 25], [50491, 25], [50792, 24], [51092, 24], [51392, 24], [51693, 23], [51993, 23], [52294, 22], [52594, 22], [52895, 21], [53195, 21], [53496, 20], [53796, 19], [54096, 19], [54397, 18], [54697, 18], [54998, 17], [55298, 17], [55599, 16], [55899, 16], [56200, 15], [56500, 15], [56800, 15], [57101, 14], [57401, 14], [57702, 13], [58002, 12], [58303, 11], [58603, 11], [58904, 10], [59204, 10], [59505, 9], [59805, 9], [60105, 9], [60406, 8], [60706, 8], [61007, 7], [61307, 7], [61608, 6], [61908, 6], [62209, 4], [62509, 4], [62809, 4], [63110, 3]], "point": [67, 146]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.40|+00.40|-01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [18, 83, 117, 211], "mask": [[24618, 84], [24918, 85], [25219, 85], [25520, 84], [25820, 84], [26121, 83], [26421, 83], [26722, 83], [27023, 82], [27323, 82], [27624, 81], [27925, 80], [28225, 81], [28526, 80], [28826, 80], [29127, 49], [29181, 25], [29428, 45], [29484, 23], [29728, 44], [29785, 22], [30029, 42], [30085, 22], [30329, 41], [30386, 21], [30630, 39], [30686, 21], [30931, 38], [30986, 22], [31231, 38], [31287, 21], [31532, 37], [31587, 21], [31832, 38], [31887, 21], [32133, 37], [32188, 20], [32434, 36], [32488, 21], [32734, 37], [32788, 21], [33035, 36], [33089, 20], [33335, 37], [33389, 20], [33636, 36], [33689, 21], [33937, 35], [33990, 20], [34237, 36], [34290, 20], [34538, 35], [34590, 20], [34838, 36], [34891, 19], [35139, 35], [35191, 20], [35440, 34], [35491, 20], [35740, 35], [35792, 19], [36041, 34], [36092, 19], [36342, 34], [36391, 20], [36642, 34], [36691, 21], [36943, 34], [36990, 22], [37243, 34], [37289, 23], [37544, 34], [37590, 22], [37845, 35], [37891, 22], [38145, 34], [38191, 22], [38446, 33], [38492, 21], [38746, 32], [38792, 21], [39047, 31], [39093, 20], [39348, 30], [39393, 21], [39648, 30], [39693, 21], [39949, 30], [39993, 21], [40249, 30], [40293, 21], [40550, 30], [40593, 22], [40851, 29], [40893, 22], [41151, 30], [41192, 23], [41452, 29], [41492, 23], [41752, 30], [41792, 23], [42053, 29], [42092, 24], [42354, 29], [42392, 24], [42654, 30], [42692, 24], [42955, 31], [42990, 26], [43255, 61], [43556, 61], [43857, 60], [44157, 60], [44458, 59], [44791, 27], [45091, 27], [45391, 26], [45690, 27], [45990, 27], [46290, 27], [46590, 27], [46890, 27], [47190, 27], [47490, 27], [47789, 28], [48089, 28], [48389, 28], [48689, 28], [48989, 28], [49289, 28], [49590, 26], [49890, 26], [50191, 25], [50491, 25], [50792, 24], [51092, 24], [51392, 24], [51693, 23], [51993, 23], [52294, 22], [52594, 22], [52895, 21], [53195, 21], [53496, 20], [53796, 19], [54096, 19], [54397, 18], [54697, 18], [54998, 17], [55298, 17], [55599, 16], [55899, 16], [56200, 15], [56500, 15], [56800, 15], [57101, 14], [57401, 14], [57702, 13], [58002, 12], [58303, 11], [58603, 11], [58904, 10], [59204, 10], [59505, 9], [59805, 9], [60105, 9], [60406, 8], [60706, 8], [61007, 7], [61307, 7], [61608, 6], [61908, 6], [62209, 4], [62509, 4], [62809, 4], [63110, 3]], "point": [67, 146]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan11", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.25, "y": 0.9009992, "z": -0.25}, "object_poses": [{"objectName": "Pan_4b842b85", "position": {"x": 1.0071, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Potato_483d2614", "position": {"x": 0.009415984, "y": 0.942136168, "z": -1.5719192}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_483d2614", "position": {"x": -0.296794474, "y": 0.100041717, "z": 0.3960294}, "rotation": {"x": 0.0, "y": 89.99995, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": -0.494962871, "y": 0.7735841, "z": -1.5058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": -1.03559017, "y": 0.7731357, "z": -1.5054}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_d4975865", "position": {"x": 1.84613431, "y": 0.9123421, "z": 0.292338252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": -1.29282951, "y": 0.7747009, "z": -1.47242057}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": 1.7913661, "y": 0.7743738, "z": 0.339037955}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": -2.47704625, "y": 0.9672249, "z": 0.345098346}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": 1.412741, "y": 0.927299857, "z": -1.40615737}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": -2.3974998, "y": 0.815964937, "z": -1.84918261}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": -2.31, "y": 0.5464649, "z": -1.63487637}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": -0.53883034, "y": 0.109321654, "z": -1.58258522}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": -2.47704625, "y": 0.9506611, "z": 0.165200233}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": -0.09671791, "y": 0.111744404, "z": -1.50870013}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": 0.27113992, "y": 0.109544933, "z": -1.50970006}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_b9b64351", "position": {"x": -2.06597686, "y": 0.977574646, "z": 0.3450983}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_b9b64351", "position": {"x": -1.79193056, "y": 0.977574646, "z": 0.435047358}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": -0.394602656, "y": 0.1192514, "z": 0.5259999}, "rotation": {"x": 0.0, "y": 89.99995, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": 1.84613431, "y": 0.961345851, "z": 0.62386173}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": 1.66300225, "y": 0.9117321, "z": 0.62386173}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": 0.5628937, "y": 0.109618187, "z": 0.270902365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": -1.92895377, "y": 0.9485312, "z": 0.704894543}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": -2.61406946, "y": 0.9485312, "z": 0.524996459}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": 1.72404635, "y": 0.9951729, "z": 0.3752191}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_b9b64351", "position": {"x": 1.412741, "y": 0.9376496, "z": -1.6548}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": 0.9603606, "y": 0.920098543, "z": -1.64917481}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": 1.60859013, "y": 1.32816672, "z": -1.77289879}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Kettle_7922e01e", "position": {"x": 1.361, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": 0.701403737, "y": 0.9179861, "z": -1.44982815}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": 1.9071784, "y": 0.961345851, "z": 0.4581}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": 1.9071784, "y": 0.910996437, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": -2.203, "y": 0.9530839, "z": 0.255149245}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_da74902f", "position": {"x": -2.383, "y": 0.94, "z": 0.583}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": 0.6496123, "y": 0.934549868, "z": -1.78207254}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_54f2d061", "position": {"x": -0.175111651, "y": 1.01658535, "z": -1.73768091}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": -1.79193056, "y": 0.9873883, "z": 0.2551492}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": -0.3596393, "y": 1.00254059, "z": -1.6548}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_42ce5824", "position": {"x": 0.167712927, "y": 0.9086062, "z": 0.5409809}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": -2.203, "y": 0.9485312, "z": 0.5249964}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_41d4caea", "position": {"x": 0.432402283, "y": 0.9794513, "z": 0.292338252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_d4975865", "position": {"x": -0.3596393, "y": 0.9123421, "z": -1.40615737}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_483d2614", "position": {"x": 1.535548, "y": 0.942136168, "z": -1.90344274}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_4b842b85", "position": {"x": -0.5690172, "y": 1.32834435, "z": -1.809647}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": 0.564747, "y": 0.9118642, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": -2.61406946, "y": 0.9533544, "z": 0.435047448}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": -1.92895365, "y": 0.947225034, "z": 0.165200144}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1630511995, "scene_num": 11}, "task_id": "trial_T20190908_073846_180553", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1SX8IVV82M0LW_317HQ483IA93BU1ZT1XR07TY83WIN8", "high_descs": ["Walk to the table behind you.", "Grab the coffee mug from the table.", "Walk to the microwave that's behind you.", "Place the mug in the microwave and heat it, then grab the mug out of the microwave.", "Go to the middle cabinet below the microwave.", "Place the heated mug in the cabinet."], "task_desc": "Warm a mug and place it in a cabinet.", "votes": [1, 1, 1]}, {"assignment_id": "A2AXP68AJJ3B94_336KAV9KYT9TC8RBB0LAU7KONSI2YO", "high_descs": ["Turn around and move to the table", "Grab the mug from the table", "Turn around and move towards the microwave on the right", "Heat the mug in the microwave", "Remove the mug and move to the cubbard beneath the microwave", "Place the mug in the cubbard"], "task_desc": "Heat a mug and place it in the cubbard", "votes": [1, 1, 1]}, {"assignment_id": "AD0NVUGLDYDYN_31LM9EDVOO9Z65L5BJIRC88YLJEJNG", "high_descs": ["turn right, walk to the white table on the right", "grab the cup on the table", "turn around to the microwave", "open the microwave, heat the cup in it for a while, take the cup out", "turn around, turn around to the cabinet", "open the right cabinet door, put the cup in the cabinet"], "task_desc": "heat the cup in the microwave, put the cup in the cabinet", "votes": [1, 0, 1]}]}}