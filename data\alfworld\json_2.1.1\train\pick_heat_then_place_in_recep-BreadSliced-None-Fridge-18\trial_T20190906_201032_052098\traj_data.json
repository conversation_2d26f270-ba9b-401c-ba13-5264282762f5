{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000096.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000097.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000098.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000099.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000100.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000101.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000102.png", "low_idx": 28}, {"high_idx": 0, "image_name": "000000103.png", "low_idx": 29}, {"high_idx": 0, "image_name": "000000104.png", "low_idx": 29}, {"high_idx": 0, "image_name": "000000105.png", "low_idx": 30}, {"high_idx": 0, "image_name": "000000106.png", "low_idx": 30}, {"high_idx": 0, "image_name": "000000107.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000108.png", "low_idx": 31}, {"high_idx": 0, "image_name": "000000109.png", "low_idx": 32}, {"high_idx": 0, "image_name": "000000110.png", "low_idx": 32}, {"high_idx": 0, "image_name": "000000111.png", "low_idx": 33}, {"high_idx": 0, "image_name": "000000112.png", "low_idx": 33}, {"high_idx": 0, "image_name": "000000113.png", "low_idx": 34}, {"high_idx": 0, "image_name": "000000114.png", "low_idx": 34}, {"high_idx": 0, "image_name": "000000115.png", "low_idx": 35}, {"high_idx": 0, "image_name": "000000116.png", "low_idx": 35}, {"high_idx": 0, "image_name": "000000117.png", "low_idx": 36}, {"high_idx": 0, "image_name": "000000118.png", "low_idx": 36}, {"high_idx": 0, "image_name": "000000119.png", "low_idx": 37}, {"high_idx": 0, "image_name": "000000120.png", "low_idx": 37}, {"high_idx": 1, "image_name": "000000121.png", "low_idx": 38}, {"high_idx": 1, "image_name": "000000122.png", "low_idx": 38}, {"high_idx": 1, "image_name": "000000123.png", "low_idx": 38}, {"high_idx": 1, "image_name": "000000124.png", "low_idx": 38}, {"high_idx": 1, "image_name": "000000125.png", "low_idx": 38}, {"high_idx": 1, "image_name": "000000126.png", "low_idx": 38}, {"high_idx": 1, "image_name": "000000127.png", "low_idx": 38}, {"high_idx": 1, "image_name": "000000128.png", "low_idx": 38}, {"high_idx": 1, "image_name": "000000129.png", "low_idx": 38}, {"high_idx": 1, "image_name": "000000130.png", "low_idx": 38}, {"high_idx": 1, "image_name": "000000131.png", "low_idx": 38}, {"high_idx": 1, "image_name": "000000132.png", "low_idx": 38}, {"high_idx": 1, "image_name": "000000133.png", "low_idx": 38}, {"high_idx": 1, "image_name": "000000134.png", "low_idx": 38}, {"high_idx": 1, "image_name": "000000135.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 51}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 51}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 52}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 52}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 53}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 53}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 54}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 54}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 55}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 55}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 56}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 56}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 57}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 57}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 58}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 58}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 59}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 59}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 60}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 60}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 61}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 61}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 61}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 61}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 61}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 61}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 61}, {"high_idx": 2, "image_name": "000000205.png", "low_idx": 61}, {"high_idx": 2, "image_name": "000000206.png", "low_idx": 61}, {"high_idx": 2, "image_name": "000000207.png", "low_idx": 61}, {"high_idx": 2, "image_name": "000000208.png", "low_idx": 61}, {"high_idx": 2, "image_name": "000000209.png", "low_idx": 62}, {"high_idx": 2, "image_name": "000000210.png", "low_idx": 62}, {"high_idx": 2, "image_name": "000000211.png", "low_idx": 63}, {"high_idx": 2, "image_name": "000000212.png", "low_idx": 63}, {"high_idx": 2, "image_name": "000000213.png", "low_idx": 63}, {"high_idx": 2, "image_name": "000000214.png", "low_idx": 63}, {"high_idx": 2, "image_name": "000000215.png", "low_idx": 63}, {"high_idx": 2, "image_name": "000000216.png", "low_idx": 63}, {"high_idx": 2, "image_name": "000000217.png", "low_idx": 63}, {"high_idx": 2, "image_name": "000000218.png", "low_idx": 63}, {"high_idx": 2, "image_name": "000000219.png", "low_idx": 63}, {"high_idx": 2, "image_name": "000000220.png", "low_idx": 63}, {"high_idx": 2, "image_name": "000000221.png", "low_idx": 63}, {"high_idx": 2, "image_name": "000000222.png", "low_idx": 64}, {"high_idx": 2, "image_name": "000000223.png", "low_idx": 64}, {"high_idx": 2, "image_name": "000000224.png", "low_idx": 65}, {"high_idx": 2, "image_name": "000000225.png", "low_idx": 65}, {"high_idx": 2, "image_name": "000000226.png", "low_idx": 66}, {"high_idx": 2, "image_name": "000000227.png", "low_idx": 66}, {"high_idx": 2, "image_name": "000000228.png", "low_idx": 67}, {"high_idx": 2, "image_name": "000000229.png", "low_idx": 67}, {"high_idx": 2, "image_name": "000000230.png", "low_idx": 67}, {"high_idx": 2, "image_name": "000000231.png", "low_idx": 67}, {"high_idx": 2, "image_name": "000000232.png", "low_idx": 67}, {"high_idx": 2, "image_name": "000000233.png", "low_idx": 67}, {"high_idx": 2, "image_name": "000000234.png", "low_idx": 67}, {"high_idx": 2, "image_name": "000000235.png", "low_idx": 67}, {"high_idx": 2, "image_name": "000000236.png", "low_idx": 67}, {"high_idx": 2, "image_name": "000000237.png", "low_idx": 67}, {"high_idx": 2, "image_name": "000000238.png", "low_idx": 67}, {"high_idx": 2, "image_name": "000000239.png", "low_idx": 68}, {"high_idx": 2, "image_name": "000000240.png", "low_idx": 68}, {"high_idx": 2, "image_name": "000000241.png", "low_idx": 69}, {"high_idx": 2, "image_name": "000000242.png", "low_idx": 69}, {"high_idx": 2, "image_name": "000000243.png", "low_idx": 69}, {"high_idx": 2, "image_name": "000000244.png", "low_idx": 69}, {"high_idx": 2, "image_name": "000000245.png", "low_idx": 69}, {"high_idx": 2, "image_name": "000000246.png", "low_idx": 69}, {"high_idx": 2, "image_name": "000000247.png", "low_idx": 69}, {"high_idx": 2, "image_name": "000000248.png", "low_idx": 69}, {"high_idx": 2, "image_name": "000000249.png", "low_idx": 69}, {"high_idx": 2, "image_name": "000000250.png", "low_idx": 69}, {"high_idx": 2, "image_name": "000000251.png", "low_idx": 69}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 70}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 70}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 70}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 70}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 70}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 70}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 70}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 70}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 70}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 70}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 71}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 71}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 72}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 72}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 72}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 72}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 72}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 72}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 72}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 72}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 72}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 72}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 72}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 72}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 72}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 72}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000306.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000307.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000308.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000309.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000310.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000311.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000312.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000313.png", "low_idx": 74}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 75}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 75}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 75}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 75}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 75}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 75}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 75}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 75}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 75}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 75}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 75}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 76}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 76}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 76}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 76}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 76}, {"high_idx": 8, "image_name": "000000330.png", "low_idx": 76}, {"high_idx": 8, "image_name": "000000331.png", "low_idx": 76}, {"high_idx": 8, "image_name": "000000332.png", "low_idx": 76}, {"high_idx": 8, "image_name": "000000333.png", "low_idx": 76}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 76}, {"high_idx": 8, "image_name": "000000335.png", "low_idx": 76}, {"high_idx": 8, "image_name": "000000336.png", "low_idx": 77}, {"high_idx": 8, "image_name": "000000337.png", "low_idx": 77}, {"high_idx": 8, "image_name": "000000338.png", "low_idx": 77}, {"high_idx": 8, "image_name": "000000339.png", "low_idx": 77}, {"high_idx": 8, "image_name": "000000340.png", "low_idx": 77}, {"high_idx": 8, "image_name": "000000341.png", "low_idx": 77}, {"high_idx": 8, "image_name": "000000342.png", "low_idx": 77}, {"high_idx": 8, "image_name": "000000343.png", "low_idx": 77}, {"high_idx": 8, "image_name": "000000344.png", "low_idx": 77}, {"high_idx": 8, "image_name": "000000345.png", "low_idx": 77}, {"high_idx": 8, "image_name": "000000346.png", "low_idx": 77}, {"high_idx": 8, "image_name": "000000347.png", "low_idx": 78}, {"high_idx": 8, "image_name": "000000348.png", "low_idx": 78}, {"high_idx": 8, "image_name": "000000349.png", "low_idx": 79}, {"high_idx": 8, "image_name": "000000350.png", "low_idx": 79}, {"high_idx": 8, "image_name": "000000351.png", "low_idx": 79}, {"high_idx": 8, "image_name": "000000352.png", "low_idx": 79}, {"high_idx": 8, "image_name": "000000353.png", "low_idx": 79}, {"high_idx": 8, "image_name": "000000354.png", "low_idx": 79}, {"high_idx": 8, "image_name": "000000355.png", "low_idx": 79}, {"high_idx": 8, "image_name": "000000356.png", "low_idx": 79}, {"high_idx": 8, "image_name": "000000357.png", "low_idx": 79}, {"high_idx": 8, "image_name": "000000358.png", "low_idx": 79}, {"high_idx": 8, "image_name": "000000359.png", "low_idx": 79}, {"high_idx": 8, "image_name": "000000360.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000361.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000362.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000363.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000364.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000365.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000366.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000367.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000368.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000369.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000370.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000371.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000372.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000373.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000374.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000375.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000376.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000377.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000378.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000379.png", "low_idx": 85}, {"high_idx": 8, "image_name": "000000380.png", "low_idx": 85}, {"high_idx": 8, "image_name": "000000381.png", "low_idx": 86}, {"high_idx": 8, "image_name": "000000382.png", "low_idx": 86}, {"high_idx": 8, "image_name": "000000383.png", "low_idx": 87}, {"high_idx": 8, "image_name": "000000384.png", "low_idx": 87}, {"high_idx": 8, "image_name": "000000385.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000386.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000387.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000388.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000389.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000390.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000391.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000392.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000393.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000394.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000395.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000396.png", "low_idx": 89}, {"high_idx": 8, "image_name": "000000397.png", "low_idx": 89}, {"high_idx": 8, "image_name": "000000398.png", "low_idx": 90}, {"high_idx": 8, "image_name": "000000399.png", "low_idx": 90}, {"high_idx": 8, "image_name": "000000400.png", "low_idx": 91}, {"high_idx": 8, "image_name": "000000401.png", "low_idx": 91}, {"high_idx": 8, "image_name": "000000402.png", "low_idx": 92}, {"high_idx": 8, "image_name": "000000403.png", "low_idx": 92}, {"high_idx": 8, "image_name": "000000404.png", "low_idx": 93}, {"high_idx": 8, "image_name": "000000405.png", "low_idx": 93}, {"high_idx": 8, "image_name": "000000406.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000407.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000408.png", "low_idx": 95}, {"high_idx": 8, "image_name": "000000409.png", "low_idx": 95}, {"high_idx": 8, "image_name": "000000410.png", "low_idx": 96}, {"high_idx": 8, "image_name": "000000411.png", "low_idx": 96}, {"high_idx": 8, "image_name": "000000412.png", "low_idx": 97}, {"high_idx": 8, "image_name": "000000413.png", "low_idx": 97}, {"high_idx": 8, "image_name": "000000414.png", "low_idx": 98}, {"high_idx": 8, "image_name": "000000415.png", "low_idx": 98}, {"high_idx": 8, "image_name": "000000416.png", "low_idx": 99}, {"high_idx": 8, "image_name": "000000417.png", "low_idx": 99}, {"high_idx": 8, "image_name": "000000418.png", "low_idx": 100}, {"high_idx": 8, "image_name": "000000419.png", "low_idx": 100}, {"high_idx": 8, "image_name": "000000420.png", "low_idx": 101}, {"high_idx": 8, "image_name": "000000421.png", "low_idx": 101}, {"high_idx": 8, "image_name": "000000422.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000423.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000424.png", "low_idx": 103}, {"high_idx": 8, "image_name": "000000425.png", "low_idx": 103}, {"high_idx": 8, "image_name": "000000426.png", "low_idx": 104}, {"high_idx": 8, "image_name": "000000427.png", "low_idx": 104}, {"high_idx": 8, "image_name": "000000428.png", "low_idx": 105}, {"high_idx": 8, "image_name": "000000429.png", "low_idx": 105}, {"high_idx": 8, "image_name": "000000430.png", "low_idx": 105}, {"high_idx": 8, "image_name": "000000431.png", "low_idx": 105}, {"high_idx": 8, "image_name": "000000432.png", "low_idx": 105}, {"high_idx": 8, "image_name": "000000433.png", "low_idx": 105}, {"high_idx": 8, "image_name": "000000434.png", "low_idx": 105}, {"high_idx": 8, "image_name": "000000435.png", "low_idx": 105}, {"high_idx": 8, "image_name": "000000436.png", "low_idx": 105}, {"high_idx": 8, "image_name": "000000437.png", "low_idx": 105}, {"high_idx": 8, "image_name": "000000438.png", "low_idx": 105}, {"high_idx": 9, "image_name": "000000439.png", "low_idx": 106}, {"high_idx": 9, "image_name": "000000440.png", "low_idx": 106}, {"high_idx": 9, "image_name": "000000441.png", "low_idx": 106}, {"high_idx": 9, "image_name": "000000442.png", "low_idx": 106}, {"high_idx": 9, "image_name": "000000443.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000444.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000445.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000446.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000447.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000448.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000449.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000450.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000451.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000452.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000453.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000454.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000455.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000456.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000457.png", "low_idx": 107}, {"high_idx": 9, "image_name": "000000458.png", "low_idx": 108}, {"high_idx": 9, "image_name": "000000459.png", "low_idx": 108}, {"high_idx": 9, "image_name": "000000460.png", "low_idx": 108}, {"high_idx": 9, "image_name": "000000461.png", "low_idx": 108}, {"high_idx": 9, "image_name": "000000462.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000463.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000464.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000465.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000466.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000467.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000468.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000469.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000470.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000471.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000472.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000473.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000474.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000475.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000476.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000477.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000478.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000479.png", "low_idx": 109}, {"high_idx": 9, "image_name": "000000480.png", "low_idx": 110}, {"high_idx": 9, "image_name": "000000481.png", "low_idx": 110}, {"high_idx": 9, "image_name": "000000482.png", "low_idx": 110}, {"high_idx": 9, "image_name": "000000483.png", "low_idx": 110}, {"high_idx": 9, "image_name": "000000484.png", "low_idx": 110}, {"high_idx": 9, "image_name": "000000485.png", "low_idx": 110}, {"high_idx": 9, "image_name": "000000486.png", "low_idx": 111}, {"high_idx": 9, "image_name": "000000487.png", "low_idx": 111}, {"high_idx": 9, "image_name": "000000488.png", "low_idx": 111}, {"high_idx": 9, "image_name": "000000489.png", "low_idx": 111}, {"high_idx": 9, "image_name": "000000490.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000491.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000492.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000493.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000494.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000495.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000496.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000497.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000498.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000499.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000500.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000501.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000502.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000503.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000504.png", "low_idx": 112}, {"high_idx": 9, "image_name": "000000505.png", "low_idx": 113}, {"high_idx": 9, "image_name": "000000506.png", "low_idx": 113}, {"high_idx": 9, "image_name": "000000507.png", "low_idx": 113}, {"high_idx": 9, "image_name": "000000508.png", "low_idx": 113}, {"high_idx": 10, "image_name": "000000509.png", "low_idx": 114}, {"high_idx": 10, "image_name": "000000510.png", "low_idx": 114}, {"high_idx": 10, "image_name": "000000511.png", "low_idx": 114}, {"high_idx": 10, "image_name": "000000512.png", "low_idx": 114}, {"high_idx": 10, "image_name": "000000513.png", "low_idx": 114}, {"high_idx": 10, "image_name": "000000514.png", "low_idx": 114}, {"high_idx": 10, "image_name": "000000515.png", "low_idx": 114}, {"high_idx": 10, "image_name": "000000516.png", "low_idx": 114}, {"high_idx": 10, "image_name": "000000517.png", "low_idx": 114}, {"high_idx": 10, "image_name": "000000518.png", "low_idx": 114}, {"high_idx": 10, "image_name": "000000519.png", "low_idx": 114}, {"high_idx": 10, "image_name": "000000520.png", "low_idx": 115}, {"high_idx": 10, "image_name": "000000521.png", "low_idx": 115}, {"high_idx": 10, "image_name": "000000522.png", "low_idx": 115}, {"high_idx": 10, "image_name": "000000523.png", "low_idx": 115}, {"high_idx": 10, "image_name": "000000524.png", "low_idx": 115}, {"high_idx": 10, "image_name": "000000525.png", "low_idx": 115}, {"high_idx": 10, "image_name": "000000526.png", "low_idx": 115}, {"high_idx": 10, "image_name": "000000527.png", "low_idx": 115}, {"high_idx": 10, "image_name": "000000528.png", "low_idx": 115}, {"high_idx": 10, "image_name": "000000529.png", "low_idx": 115}, {"high_idx": 10, "image_name": "000000530.png", "low_idx": 115}, {"high_idx": 10, "image_name": "000000531.png", "low_idx": 116}, {"high_idx": 10, "image_name": "000000532.png", "low_idx": 116}, {"high_idx": 10, "image_name": "000000533.png", "low_idx": 116}, {"high_idx": 10, "image_name": "000000534.png", "low_idx": 116}, {"high_idx": 10, "image_name": "000000535.png", "low_idx": 116}, {"high_idx": 10, "image_name": "000000536.png", "low_idx": 116}, {"high_idx": 10, "image_name": "000000537.png", "low_idx": 116}, {"high_idx": 10, "image_name": "000000538.png", "low_idx": 116}, {"high_idx": 10, "image_name": "000000539.png", "low_idx": 116}, {"high_idx": 10, "image_name": "000000540.png", "low_idx": 116}, {"high_idx": 10, "image_name": "000000541.png", "low_idx": 116}, {"high_idx": 10, "image_name": "000000542.png", "low_idx": 117}, {"high_idx": 10, "image_name": "000000543.png", "low_idx": 117}, {"high_idx": 10, "image_name": "000000544.png", "low_idx": 118}, {"high_idx": 10, "image_name": "000000545.png", "low_idx": 118}, {"high_idx": 10, "image_name": "000000546.png", "low_idx": 119}, {"high_idx": 10, "image_name": "000000547.png", "low_idx": 119}, {"high_idx": 10, "image_name": "000000548.png", "low_idx": 120}, {"high_idx": 10, "image_name": "000000549.png", "low_idx": 120}, {"high_idx": 10, "image_name": "000000550.png", "low_idx": 121}, {"high_idx": 10, "image_name": "000000551.png", "low_idx": 121}, {"high_idx": 10, "image_name": "000000552.png", "low_idx": 122}, {"high_idx": 10, "image_name": "000000553.png", "low_idx": 122}, {"high_idx": 10, "image_name": "000000554.png", "low_idx": 123}, {"high_idx": 10, "image_name": "000000555.png", "low_idx": 123}, {"high_idx": 10, "image_name": "000000556.png", "low_idx": 124}, {"high_idx": 10, "image_name": "000000557.png", "low_idx": 124}, {"high_idx": 10, "image_name": "000000558.png", "low_idx": 125}, {"high_idx": 10, "image_name": "000000559.png", "low_idx": 125}, {"high_idx": 10, "image_name": "000000560.png", "low_idx": 126}, {"high_idx": 10, "image_name": "000000561.png", "low_idx": 126}, {"high_idx": 10, "image_name": "000000562.png", "low_idx": 127}, {"high_idx": 10, "image_name": "000000563.png", "low_idx": 127}, {"high_idx": 10, "image_name": "000000564.png", "low_idx": 128}, {"high_idx": 10, "image_name": "000000565.png", "low_idx": 128}, {"high_idx": 10, "image_name": "000000566.png", "low_idx": 129}, {"high_idx": 10, "image_name": "000000567.png", "low_idx": 129}, {"high_idx": 10, "image_name": "000000568.png", "low_idx": 130}, {"high_idx": 10, "image_name": "000000569.png", "low_idx": 130}, {"high_idx": 10, "image_name": "000000570.png", "low_idx": 131}, {"high_idx": 10, "image_name": "000000571.png", "low_idx": 131}, {"high_idx": 10, "image_name": "000000572.png", "low_idx": 132}, {"high_idx": 10, "image_name": "000000573.png", "low_idx": 132}, {"high_idx": 10, "image_name": "000000574.png", "low_idx": 133}, {"high_idx": 10, "image_name": "000000575.png", "low_idx": 133}, {"high_idx": 10, "image_name": "000000576.png", "low_idx": 133}, {"high_idx": 10, "image_name": "000000577.png", "low_idx": 133}, {"high_idx": 10, "image_name": "000000578.png", "low_idx": 133}, {"high_idx": 10, "image_name": "000000579.png", "low_idx": 133}, {"high_idx": 10, "image_name": "000000580.png", "low_idx": 133}, {"high_idx": 10, "image_name": "000000581.png", "low_idx": 133}, {"high_idx": 10, "image_name": "000000582.png", "low_idx": 133}, {"high_idx": 10, "image_name": "000000583.png", "low_idx": 133}, {"high_idx": 10, "image_name": "000000584.png", "low_idx": 133}, {"high_idx": 10, "image_name": "000000585.png", "low_idx": 134}, {"high_idx": 10, "image_name": "000000586.png", "low_idx": 134}, {"high_idx": 10, "image_name": "000000587.png", "low_idx": 135}, {"high_idx": 10, "image_name": "000000588.png", "low_idx": 135}, {"high_idx": 10, "image_name": "000000589.png", "low_idx": 136}, {"high_idx": 10, "image_name": "000000590.png", "low_idx": 136}, {"high_idx": 10, "image_name": "000000591.png", "low_idx": 137}, {"high_idx": 10, "image_name": "000000592.png", "low_idx": 137}, {"high_idx": 10, "image_name": "000000593.png", "low_idx": 137}, {"high_idx": 10, "image_name": "000000594.png", "low_idx": 137}, {"high_idx": 10, "image_name": "000000595.png", "low_idx": 137}, {"high_idx": 10, "image_name": "000000596.png", "low_idx": 137}, {"high_idx": 10, "image_name": "000000597.png", "low_idx": 137}, {"high_idx": 10, "image_name": "000000598.png", "low_idx": 137}, {"high_idx": 10, "image_name": "000000599.png", "low_idx": 137}, {"high_idx": 10, "image_name": "000000600.png", "low_idx": 137}, {"high_idx": 10, "image_name": "000000601.png", "low_idx": 137}, {"high_idx": 11, "image_name": "000000602.png", "low_idx": 138}, {"high_idx": 11, "image_name": "000000603.png", "low_idx": 138}, {"high_idx": 11, "image_name": "000000604.png", "low_idx": 138}, {"high_idx": 11, "image_name": "000000605.png", "low_idx": 138}, {"high_idx": 11, "image_name": "000000606.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000607.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000608.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000609.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000610.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000611.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000612.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000613.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000614.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000615.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000616.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000617.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000618.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000619.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000620.png", "low_idx": 139}, {"high_idx": 11, "image_name": "000000621.png", "low_idx": 140}, {"high_idx": 11, "image_name": "000000622.png", "low_idx": 140}, {"high_idx": 11, "image_name": "000000623.png", "low_idx": 140}, {"high_idx": 11, "image_name": "000000624.png", "low_idx": 140}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|2|4|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [2.4822248, 2.4822248, 1.214432956, 1.214432956, 3.9709684, 3.9709684]], "coordinateReceptacleObjectId": ["CounterTop", [5.564, 5.564, 3.588, 3.588, 4.1128, 4.1128]], "forceVisible": true, "objectId": "ButterKnife|+00.62|+00.99|+00.30"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|3|26|3|60"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [0.588207244, 0.588207244, 25.39834596, 25.39834596, 3.31879258, 3.31879258]], "forceVisible": true, "objectId": "Bread|+00.15|+00.83|+06.35"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|3|26|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [2.4822248, 2.4822248, 1.214432956, 1.214432956, 3.9709684, 3.9709684]], "coordinateReceptacleObjectId": ["DiningTable", [-0.624711872, -0.624711872, 25.896156, 25.896156, 2.673336984, 2.673336984]], "forceVisible": true, "objectId": "ButterKnife|+00.62|+00.99|+00.30", "receptacleObjectId": "DiningTable|-00.16|+00.67|+06.47"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|3|26|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [0.588207244, 0.588207244, 25.39834596, 25.39834596, 3.31879258, 3.31879258]], "coordinateReceptacleObjectId": ["DiningTable", [-0.624711872, -0.624711872, 25.896156, 25.896156, 2.673336984, 2.673336984]], "forceVisible": true, "objectId": "Bread|+00.15|+00.83|+06.35|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|1|6|2|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|4|22|1|30"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "fridge"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [0.588207244, 0.588207244, 25.39834596, 25.39834596, 3.31879258, 3.31879258]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [8.14, 8.14, 22.504, 22.504, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|+00.15|+00.83|+06.35|BreadSliced_1", "receptacleObjectId": "<PERSON><PERSON>|+02.04|+00.00|+05.63"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|+00.62|+00.99|+00.30"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [76, 124, 153, 129], "mask": [[36990, 10], [37284, 27], [37339, 12], [37578, 75], [37876, 78], [38176, 77], [38538, 12]], "point": [114, 125]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|+00.15|+00.83|+06.35"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [66, 76, 148, 153], "mask": [[22627, 2], [22921, 12], [23218, 17], [23516, 21], [23814, 25], [24113, 27], [24411, 30], [24709, 33], [25007, 36], [25306, 38], [25604, 40], [25903, 42], [26202, 43], [26500, 46], [26799, 47], [27098, 48], [27396, 51], [27695, 52], [27994, 53], [28292, 55], [28590, 57], [28889, 58], [29187, 60], [29486, 61], [29784, 64], [30083, 65], [30382, 67], [30680, 69], [30979, 70], [31278, 71], [31576, 73], [31875, 74], [32174, 75], [32473, 75], [32772, 76], [33071, 76], [33370, 76], [33670, 75], [33969, 75], [34268, 75], [34568, 74], [34867, 74], [35167, 73], [35467, 72], [35766, 72], [36066, 70], [36366, 69], [36666, 68], [36966, 67], [37267, 65], [37567, 63], [37867, 62], [38168, 60], [38468, 58], [38768, 57], [39069, 54], [39369, 53], [39670, 51], [39971, 49], [40271, 48], [40572, 45], [40873, 43], [41173, 42], [41474, 40], [41775, 37], [42076, 35], [42377, 33], [42678, 31], [42979, 28], [43280, 26], [43581, 24], [43882, 21], [44183, 19], [44484, 16], [44785, 14], [45087, 11], [45388, 8], [45690, 5]], "point": [107, 113]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|+00.62|+00.99|+00.30", "placeStationary": true, "receptacleObjectId": "DiningTable|-00.16|+00.67|+06.47"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 71, 299, 300], "mask": [[21141, 3], [21148, 12], [21420, 1], [21424, 1], [21441, 2], [21448, 15], [21714, 7], [21725, 1], [21727, 1], [21740, 3], [21747, 16], [21773, 4], [22008, 14], [22040, 3], [22047, 15], [22074, 1], [22303, 20], [22326, 1], [22328, 1], [22340, 2], [22346, 14], [22597, 5], [22605, 18], [22640, 2], [22646, 13], [22894, 10], [22908, 16], [22929, 1], [22940, 2], [22946, 12], [23191, 15], [23210, 15], [23229, 1], [23240, 2], [23245, 11], [23259, 2], [23301, 1], [23487, 21], [23511, 14], [23530, 1], [23540, 1], [23545, 10], [23558, 3], [23601, 5], [23784, 25], [23813, 13], [23830, 1], [23839, 2], [23844, 9], [23856, 5], [23902, 6], [24081, 30], [24116, 11], [24139, 2], [24144, 8], [24155, 7], [24202, 9], [24378, 34], [24419, 8], [24431, 1], [24439, 1], [24444, 7], [24454, 8], [24502, 12], [24675, 40], [24721, 7], [24731, 1], [24739, 1], [24743, 7], [24752, 10], [24803, 13], [24972, 46], [25023, 5], [25039, 1], [25043, 6], [25051, 12], [25103, 16], [25269, 52], [25325, 4], [25339, 1], [25343, 5], [25350, 14], [25403, 18], [25567, 55], [25627, 2], [25643, 4], [25656, 9], [25703, 21], [25865, 56], [25929, 1], [25942, 3], [25948, 5], [25957, 7], [26003, 23], [26163, 57], [26224, 2], [26242, 1], [26247, 8], [26257, 7], [26303, 26], [26461, 58], [26523, 3], [26528, 2], [26544, 1], [26547, 17], [26603, 28], [26759, 58], [26822, 5], [26829, 2], [26843, 1], [26846, 17], [26903, 30], [27057, 71], [27130, 1], [27142, 2], [27145, 17], [27202, 33], [27355, 74], [27430, 2], [27441, 2], [27444, 18], [27502, 34], [27653, 77], [27731, 1], [27741, 2], [27744, 18], [27801, 37], [27951, 79], [28041, 1], [28043, 18], [28101, 39], [28249, 74], [28352, 9], [28400, 41], [28547, 75], [28653, 8], [28699, 10], [28720, 23], [28845, 77], [28953, 8], [28997, 7], [29022, 23], [29144, 78], [29253, 8], [29295, 6], [29324, 22], [29443, 79], [29553, 9], [29594, 5], [29625, 23], [29741, 81], [29853, 9], [29892, 4], [29908, 12], [29925, 25], [30040, 76], [30153, 10], [30191, 4], [30204, 18], [30226, 25], [30338, 75], [30453, 10], [30489, 4], [30501, 22], [30526, 27], [30637, 75], [30753, 12], [30784, 8], [30799, 24], [30827, 28], [30936, 75], [31053, 14], [31080, 11], [31097, 12], [31113, 10], [31127, 29], [31234, 76], [31353, 39], [31395, 9], [31417, 6], [31427, 30], [31533, 77], [31653, 48], [31720, 3], [31727, 4], [31733, 25], [31832, 77], [31953, 46], [32021, 1], [32027, 2], [32034, 25], [32130, 79], [32253, 45], [32326, 2], [32334, 26], [32429, 80], [32553, 43], [32635, 26], [32728, 81], [32853, 42], [32935, 27], [33026, 82], [33153, 41], [33236, 28], [33325, 83], [33453, 40], [33534, 31], [33623, 85], [33753, 40], [33833, 33], [33922, 87], [34053, 39], [34131, 36], [34222, 87], [34353, 38], [34429, 39], [34521, 88], [34653, 38], [34730, 39], [34820, 89], [34953, 37], [35030, 40], [35119, 91], [35253, 37], [35330, 41], [35418, 92], [35553, 37], [35630, 42], [35717, 49], [35769, 42], [35852, 38], [35931, 42], [36016, 46], [36072, 40], [36152, 37], [36231, 44], [36315, 45], [36373, 40], [36452, 37], [36531, 45], [36614, 45], [36674, 42], [36721, 1], [36752, 37], [36831, 45], [36914, 44], [36975, 47], [37052, 36], [37131, 46], [37213, 45], [37275, 47], [37352, 36], [37431, 47], [37512, 45], [37576, 47], [37652, 36], [37731, 47], [37811, 46], [37875, 48], [37951, 37], [38031, 48], [38110, 47], [38175, 48], [38251, 38], [38331, 49], [38409, 48], [38475, 48], [38551, 38], [38631, 49], [38708, 49], [38775, 49], [38851, 38], [38930, 51], [39007, 51], [39075, 49], [39151, 39], [39230, 52], [39307, 52], [39375, 49], [39451, 40], [39530, 52], [39606, 54], [39675, 50], [39750, 42], [39830, 53], [39905, 56], [39976, 49], [40050, 42], [40130, 54], [40204, 57], [40276, 49], [40350, 44], [40429, 55], [40503, 59], [40576, 49], [40650, 45], [40728, 57], [40803, 59], [40877, 49], [40950, 46], [41027, 59], [41102, 60], [41177, 49], [41249, 49], [41325, 61], [41402, 61], [41478, 49], [41549, 51], [41624, 63], [41701, 62], [41778, 49], [41849, 54], [41921, 67], [42001, 62], [42079, 48], [42149, 59], [42217, 71], [42300, 63], [42379, 49], [42448, 141], [42600, 63], [42680, 48], [42748, 142], [42900, 63], [42980, 47], [43044, 146], [43200, 63], [43280, 45], [43346, 145], [43500, 63], [43580, 42], [43647, 145], [43800, 64], [43881, 39], [43949, 143], [44100, 64], [44182, 37], [44250, 142], [44400, 64], [44482, 35], [44551, 142], [44700, 65], [44782, 33], [44851, 142], [45000, 65], [45082, 32], [45152, 141], [45300, 65], [45383, 28], [45452, 141], [45600, 65], [45683, 27], [45753, 141], [45900, 65], [45983, 25], [46053, 141], [46200, 65], [46283, 24], [46354, 140], [46500, 66], [46583, 22], [46654, 141], [46800, 66], [46883, 21], [46954, 141], [47100, 66], [47182, 20], [47255, 140], [47400, 67], [47481, 20], [47555, 140], [47700, 67], [47780, 20], [47855, 141], [48000, 68], [48079, 19], [48155, 141], [48300, 71], [48375, 22], [48454, 69], [48524, 72], [48600, 95], [48755, 65], [48827, 70], [48900, 93], [49055, 63], [49130, 67], [49200, 91], [49356, 59], [49433, 64], [49500, 90], [49656, 57], [49736, 61], [49800, 88], [49956, 55], [50038, 60], [50100, 87], [50256, 53], [50341, 57], [50400, 85], [50556, 52], [50643, 55], [50700, 84], [50856, 50], [50945, 54], [51000, 82], [51156, 49], [51247, 52], [51300, 81], [51456, 2], [51465, 38], [51549, 50], [51600, 80], [51755, 1], [51766, 36], [51851, 48], [51900, 78], [52067, 34], [52152, 125], [52368, 32], [52454, 122], [52669, 30], [52755, 120], [52952, 1], [52970, 29], [53056, 117], [53251, 1], [53270, 28], [53358, 114], [53549, 3], [53571, 26], [53659, 112], [53848, 3], [53871, 26], [53960, 110], [54147, 4], [54171, 25], [54261, 109], [54445, 6], [54471, 24], [54562, 107], [54745, 6], [54771, 24], [54863, 36], [54900, 68], [55043, 8], [55071, 23], [55164, 35], [55200, 68], [55342, 9], [55371, 23], [55466, 33], [55500, 67], [55640, 11], [55671, 22], [55767, 32], [55800, 67], [55940, 11], [55971, 22], [56068, 31], [56100, 66], [56238, 14], [56274, 18], [56369, 30], [56400, 66], [56537, 14], [56575, 17], [56670, 29], [56700, 66], [56836, 14], [56877, 15], [56971, 28], [57000, 66], [57135, 14], [57178, 13], [57272, 27], [57300, 66], [57434, 14], [57479, 12], [57573, 26], [57600, 66], [57732, 15], [57780, 11], [57873, 25], [57900, 66], [58031, 15], [58081, 11], [58172, 26], [58200, 66], [58330, 16], [58381, 12], [58472, 26], [58500, 66], [58628, 17], [58682, 11], [58772, 26], [58800, 67], [58927, 18], [58982, 12], [59072, 26], [59100, 67], [59226, 19], [59283, 12], [59372, 26], [59400, 67], [59524, 21], [59583, 13], [59671, 27], [59700, 68], [59823, 21], [59883, 14], [59971, 27], [60000, 68], [60120, 1], [60122, 22], [60183, 14], [60271, 27], [60300, 69], [60420, 24], [60483, 15], [60571, 27], [60600, 69], [60719, 25], [60783, 16], [60870, 28], [60900, 70], [61018, 26], [61083, 17], [61170, 27], [61200, 71], [61317, 28], [61383, 18], [61470, 27], [61500, 71], [61615, 30], [61683, 19], [61769, 28], [61800, 72], [61914, 31], [61983, 20], [62069, 28], [62100, 72], [62213, 33], [62282, 22], [62369, 28], [62400, 73], [62512, 34], [62582, 23], [62668, 29], [62700, 74], [62810, 36], [62882, 24], [62968, 29], [63000, 75], [63109, 37], [63181, 26], [63267, 29], [63300, 76], [63408, 39], [63480, 28], [63567, 28], [63600, 76], [63707, 40], [63780, 29], [63866, 29], [63900, 77], [64005, 42], [64080, 30], [64165, 29], [64200, 78], [64304, 44], [64379, 33], [64464, 30], [64500, 79], [64603, 45], [64679, 34], [64763, 30], [64800, 80], [64901, 47], [64978, 37], [65062, 31], [65100, 81], [65200, 48], [65278, 38], [65362, 30], [65400, 82], [65498, 51], [65577, 41], [65660, 32], [65700, 84], [65797, 52], [65877, 43], [65959, 32], [66000, 85], [66096, 53], [66177, 45], [66258, 33], [66300, 86], [66394, 56], [66476, 48], [66556, 34], [66600, 88], [66693, 57], [66776, 50], [66855, 35], [66900, 150], [67075, 53], [67153, 36], [67200, 150], [67375, 55], [67451, 38], [67500, 151], [67674, 58], [67750, 38], [67800, 151], [67974, 60], [68048, 40], [68100, 152], [68274, 63], [68346, 41], [68400, 152], [68573, 66], [68644, 42], [68700, 153], [68872, 114], [69000, 153], [69172, 113], [69300, 153], [69471, 114], [69600, 154], [69770, 114], [69900, 155], [70070, 114], [70201, 156], [70368, 115], [70502, 157], [70666, 117], [70803, 279], [71104, 278], [71405, 276], [71705, 276], [72006, 274], [72307, 273], [72608, 271], [72909, 270], [73209, 269], [73510, 267], [73811, 265], [74112, 263], [74413, 261], [74713, 260], [75014, 258], [75315, 59], [75383, 187], [75616, 50], [75702, 167], [75917, 46], [76011, 157], [76217, 44], [76324, 143], [76518, 41], [76649, 2], [76704, 62], [76819, 40], [77041, 24], [77120, 39], [77342, 21], [77421, 37], [77643, 19], [77722, 36], [77944, 17], [78022, 36], [78244, 16], [78323, 35], [78544, 15], [78624, 34], [78844, 14], [78925, 34], [79020, 18], [79143, 14], [79226, 35], [79307, 39], [79441, 14], [79527, 36], [79597, 57], [79738, 16], [79829, 131], [80034, 19], [80130, 135], [80331, 21], [80432, 138], [80627, 24], [80734, 141], [80923, 27], [81035, 147], [81218, 30], [81337, 157], [81514, 33], [81639, 207], [81940, 205], [82242, 202], [82544, 199], [82846, 196], [83147, 193], [83449, 189], [83751, 184], [84052, 181], [84354, 176], [84656, 172], [84958, 168], [85259, 164], [85561, 160], [85863, 155], [86164, 152], [86466, 147], [86768, 143], [87070, 138], [87372, 134], [87677, 126], [87981, 120], [88286, 112], [88591, 105], [88895, 98], [89200, 91], [89505, 83], [89809, 67]], "point": [149, 184]}}, "high_idx": 5}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+00.15|+00.83|+06.35|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [95, 90, 125, 132], "mask": [[26800, 5], [27098, 9], [27397, 12], [27695, 15], [27998, 13], [28300, 12], [28601, 12], [28902, 12], [29204, 10], [29505, 10], [29806, 10], [30107, 10], [30408, 10], [30709, 9], [31010, 9], [31311, 9], [31611, 9], [31912, 9], [32213, 8], [32513, 9], [32814, 8], [33115, 8], [33415, 8], [33716, 8], [34016, 8], [34317, 7], [34617, 8], [34918, 7], [35218, 7], [35519, 6], [35819, 6], [36119, 6], [36419, 6], [36719, 6], [37019, 6], [37320, 6], [37620, 6], [37920, 6], [38220, 6], [38520, 6], [38821, 4], [39121, 3], [39421, 2]], "point": [113, 108]}}, "high_idx": 7}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+00.15|+00.83|+06.35|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 90], [25392, 69], [25591, 89], [25695, 66], [25890, 88], [25997, 64], [26190, 88], [26298, 62], [26489, 88], [26598, 62], [26788, 89], [26898, 62], [27087, 90], [27199, 61], [27387, 89], [27499, 61], [27686, 90], [27799, 60], [27985, 91], [28099, 60], [28285, 91], [28399, 60], [28584, 92], [28698, 61], [28883, 93], [28998, 61], [29183, 93], [29298, 60], [29482, 95], [29598, 60], [29781, 96], [29897, 61], [30081, 96], [30197, 61], [30380, 97], [30497, 60], [30679, 98], [30796, 61], [30978, 100], [31096, 61], [31278, 100], [31396, 61], [31577, 102], [31695, 62], [31876, 103], [31995, 61], [32176, 104], [32294, 62], [32475, 106], [32593, 63], [32775, 109], [32891, 65], [33075, 180], [33375, 180], [33675, 180], [33975, 180], [34275, 180], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 50], [25047, 114], [25292, 47], [25351, 31], [25392, 69], [25591, 46], [25653, 27], [25695, 66], [25890, 46], [25955, 23], [25997, 64], [26190, 44], [26256, 22], [26298, 62], [26489, 44], [26557, 20], [26598, 62], [26788, 44], [26857, 20], [26898, 62], [27087, 45], [27158, 19], [27199, 61], [27387, 44], [27459, 17], [27499, 61], [27686, 45], [27759, 17], [27799, 60], [27985, 45], [28060, 16], [28099, 60], [28285, 45], [28360, 16], [28399, 60], [28584, 46], [28660, 16], [28698, 61], [28883, 47], [28960, 16], [28998, 61], [29183, 47], [29260, 16], [29298, 60], [29482, 47], [29560, 17], [29598, 60], [29781, 49], [29860, 17], [29897, 61], [30081, 49], [30160, 17], [30197, 61], [30380, 50], [30460, 17], [30497, 60], [30679, 51], [30760, 17], [30796, 61], [30978, 52], [31060, 18], [31096, 61], [31278, 52], [31360, 18], [31396, 61], [31577, 53], [31660, 19], [31695, 62], [31876, 54], [31960, 19], [31995, 61], [32176, 54], [32260, 20], [32294, 62], [32475, 55], [32560, 21], [32593, 63], [32775, 55], [32860, 24], [32891, 65], [33075, 55], [33160, 95], [33375, 55], [33460, 95], [33675, 55], [33760, 95], [33975, 55], [34060, 95], [34275, 55], [34359, 96], [34576, 54], [34659, 95], [34876, 56], [34958, 96], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+00.15|+00.83|+06.35|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [129, 84, 159, 117], "mask": [[25042, 5], [25339, 12], [25637, 16], [25936, 19], [26234, 22], [26533, 24], [26832, 25], [27132, 26], [27431, 28], [27731, 28], [28030, 30], [28330, 30], [28630, 30], [28930, 30], [29230, 30], [29529, 31], [29830, 30], [30130, 30], [30430, 30], [30730, 30], [31030, 30], [31330, 30], [31630, 30], [31930, 30], [32230, 30], [32530, 30], [32830, 30], [33130, 30], [33430, 30], [33730, 30], [34030, 30], [34330, 29], [34630, 29], [34932, 26]], "point": [144, 99]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 90], [25392, 69], [25591, 89], [25695, 66], [25890, 88], [25997, 64], [26190, 88], [26298, 62], [26489, 88], [26598, 62], [26788, 89], [26898, 62], [27087, 90], [27199, 61], [27387, 89], [27499, 61], [27686, 90], [27799, 60], [27985, 91], [28099, 60], [28285, 91], [28399, 60], [28584, 92], [28698, 61], [28883, 93], [28998, 61], [29183, 93], [29298, 60], [29482, 95], [29598, 60], [29781, 96], [29897, 61], [30081, 96], [30197, 61], [30380, 97], [30497, 60], [30679, 98], [30796, 61], [30978, 100], [31096, 61], [31278, 100], [31396, 61], [31577, 102], [31695, 62], [31876, 103], [31995, 61], [32176, 104], [32294, 62], [32475, 106], [32593, 63], [32775, 109], [32891, 65], [33075, 180], [33375, 180], [33675, 180], [33975, 180], [34275, 180], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+02.04|+00.00|+05.63"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 45599], [45600, 299], [45900, 299], [46200, 298], [46500, 298], [46800, 298], [47100, 297], [47400, 297], [47700, 297], [48000, 296], [48300, 296], [48600, 296], [48900, 295], [49200, 295], [49500, 295], [49800, 294], [50100, 294], [50400, 294], [50700, 293], [51000, 293], [51300, 293], [51600, 292], [51900, 292], [52200, 292], [52500, 291], [52800, 291], [53100, 291], [53400, 290], [53700, 290], [54000, 290], [54300, 289], [54600, 289], [54900, 289], [55200, 288], [55500, 288], [55800, 288], [56100, 286], [56400, 286], [56700, 286], [57000, 285], [57300, 285], [57600, 285], [57900, 284], [58200, 284], [58500, 284], [58800, 284], [59100, 284], [59400, 285], [59700, 286], [60000, 286], [60300, 285], [60600, 285], [60900, 285], [61200, 284], [61500, 284], [61800, 284], [62100, 283], [62400, 283], [62700, 281], [63000, 280], [63300, 280], [63600, 280], [63900, 279], [64200, 279], [64500, 279], [64800, 278], [65100, 137], [65261, 117], [65400, 128], [65566, 112], [65700, 126], [65871, 106], [66000, 124], [66174, 103], [66300, 122], [66476, 101], [66600, 120], [66778, 98], [66900, 118], [67080, 96], [67200, 117], [67382, 94], [67500, 116], [67683, 92], [67800, 114], [67985, 90], [68100, 113], [68286, 89], [68400, 112], [68587, 87], [68700, 111], [68889, 85], [69000, 110], [69190, 84], [69300, 109], [69491, 82], [69600, 108], [69792, 81], [69900, 107], [70092, 81], [70200, 107], [70393, 79], [70500, 106], [70694, 78], [70800, 105], [70995, 77], [71100, 105], [71295, 76], [71400, 104], [71596, 75], [71700, 104], [71896, 75], [72000, 103], [72197, 73], [72300, 103], [72497, 73], [72600, 103], [72797, 73], [72900, 103], [73097, 72], [73200, 102], [73397, 72], [73500, 102], [73698, 71], [73800, 102], [73998, 70], [74100, 102], [74298, 70], [74400, 101], [74598, 70], [74700, 101], [74898, 69], [75000, 101], [75199, 68], [75300, 101], [75499, 68], [75600, 101], [75799, 67], [75900, 100], [76099, 67], [76200, 100], [76400, 66], [76500, 100], [76700, 65], [76800, 100], [77000, 65], [77100, 100], [77300, 65], [77400, 100], [77601, 63], [77700, 100], [77901, 63], [78000, 100], [78201, 63], [78300, 100], [78501, 62], [78600, 100], [78800, 63], [78900, 101], [79100, 63], [79200, 101], [79400, 62], [79500, 101], [79699, 63], [79800, 102], [79999, 63], [80100, 102], [80298, 63], [80400, 102], [80598, 63], [80700, 103], [80898, 63], [81000, 103], [81198, 62], [81300, 103], [81498, 62], [81600, 103], [81798, 62], [81900, 103], [82098, 61], [82200, 103], [82397, 62], [82500, 103], [82697, 62], [82800, 103], [82997, 61], [83100, 103], [83297, 61], [83400, 103], [83597, 61], [83700, 104], [83897, 60], [84000, 104], [84197, 60], [84300, 104], [84497, 60], [84600, 104], [84797, 59], [84900, 104], [85097, 59], [85200, 104], [85396, 60], [85500, 104], [85696, 59], [85800, 105], [85996, 59], [86100, 105], [86296, 59], [86400, 105], [86596, 58], [86700, 105], [86895, 59], [87000, 105], [87195, 59], [87300, 106], [87495, 58], [87600, 106], [87795, 58], [87900, 106], [88094, 59], [88200, 107], [88394, 58], [88500, 107], [88693, 59], [88800, 110], [88990, 62], [89100, 119], [89287, 64], [89400, 251], [89700, 251]], "point": [149, 149]}}, "high_idx": 11}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+00.15|+00.83|+06.35|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|+02.04|+00.00|+05.63"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[38, 7], [110, 23], [166, 4], [195, 105], [338, 8], [410, 23], [465, 5], [495, 105], [638, 8], [710, 23], [765, 5], [795, 105], [937, 9], [1010, 23], [1065, 4], [1096, 104], [1237, 10], [1309, 25], [1365, 4], [1396, 104], [1536, 11], [1609, 25], [1665, 4], [1696, 104], [1836, 12], [1909, 25], [1965, 4], [1996, 104], [2135, 13], [2209, 25], [2265, 4], [2296, 104], [2435, 14], [2508, 27], [2564, 5], [2596, 104], [2734, 15], [2808, 27], [2864, 4], [2896, 104], [3033, 17], [3108, 27], [3164, 4], [3195, 105], [3332, 19], [3407, 28], [3464, 5], [3495, 106], [3630, 21], [3707, 29], [3763, 6], [3795, 109], [3929, 23], [4007, 29], [4063, 6], [4095, 111], [4227, 26], [4306, 30], [4363, 6], [4394, 115], [4523, 30], [4606, 31], [4663, 6], [4694, 160], [4905, 32], [4962, 7], [4993, 162], [5205, 33], [5262, 8], [5293, 163], [5504, 34], [5562, 8], [5592, 164], [5803, 35], [5861, 10], [5892, 169], [6092, 47], [6161, 11], [6191, 248], [6460, 13], [6490, 250], [6760, 15], [6789, 252], [7059, 19], [7087, 266], [7357, 50228], [57586, 299], [57887, 297], [58187, 297], [58488, 296], [58789, 295], [59089, 295], [59390, 295], [59691, 295], [59992, 294], [60292, 293], [60593, 292], [60894, 291], [61194, 290], [61495, 289], [61796, 288], [62097, 286], [62397, 286], [62698, 283], [62999, 281], [63299, 281], [63600, 280], [63900, 279], [64200, 279], [64500, 279], [64800, 278], [65100, 137], [65261, 117], [65400, 128], [65566, 112], [65700, 126], [65871, 106], [66000, 124], [66174, 103], [66300, 122], [66476, 101], [66600, 120], [66778, 98], [66900, 118], [67080, 96], [67200, 117], [67382, 94], [67500, 116], [67683, 92], [67800, 114], [67985, 90], [68100, 113], [68286, 89], [68400, 112], [68587, 87], [68700, 111], [68889, 85], [69000, 110], [69190, 84], [69300, 109], [69491, 82], [69600, 108], [69792, 81], [69900, 107], [70092, 81], [70200, 107], [70393, 79], [70500, 106], [70694, 78], [70800, 105], [70995, 77], [71100, 105], [71295, 76], [71400, 104], [71596, 75], [71700, 104], [71896, 75], [72000, 103], [72197, 73], [72300, 103], [72497, 73], [72600, 103], [72797, 73], [72900, 103], [73097, 72], [73200, 102], [73397, 72], [73500, 102], [73698, 71], [73800, 102], [73998, 70], [74100, 102], [74298, 70], [74400, 101], [74598, 70], [74700, 101], [74898, 69], [75000, 101], [75199, 68], [75300, 101], [75499, 68], [75600, 101], [75799, 67], [75900, 100], [76099, 67], [76200, 100], [76400, 66], [76500, 100], [76700, 65], [76800, 100], [77000, 65], [77100, 100], [77300, 65], [77400, 100], [77601, 63], [77700, 100], [77901, 63], [78000, 100], [78201, 63], [78300, 100], [78501, 62], [78600, 100], [78800, 63], [78900, 101], [79100, 63], [79200, 101], [79400, 62], [79500, 101], [79699, 63], [79800, 102], [79999, 63], [80100, 102], [80298, 63], [80400, 102], [80598, 63], [80700, 103], [80898, 63], [81000, 103], [81198, 62], [81300, 103], [81498, 62], [81600, 103], [81798, 62], [81900, 103], [82098, 61], [82200, 103], [82397, 62], [82500, 103], [82697, 62], [82800, 103], [82997, 61], [83100, 103], [83297, 61], [83400, 103], [83597, 61], [83700, 104], [83897, 60], [84000, 104], [84197, 60], [84300, 104], [84497, 60], [84600, 104], [84797, 59], [84900, 104], [85097, 59], [85200, 104], [85396, 60], [85500, 104], [85696, 59], [85800, 105], [85996, 59], [86100, 105], [86296, 59], [86400, 105], [86596, 58], [86700, 105], [86895, 59], [87000, 105], [87195, 59], [87300, 106], [87495, 58], [87600, 106], [87795, 58], [87900, 106], [88094, 59], [88200, 107], [88394, 58], [88500, 107], [88693, 59], [88800, 110], [88990, 62], [89100, 119], [89287, 64], [89400, 251], [89700, 251]], "point": [149, 149]}}, "high_idx": 11}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+02.04|+00.00|+05.63"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[38, 7], [110, 17], [195, 105], [338, 8], [410, 17], [495, 105], [638, 8], [710, 17], [795, 105], [937, 9], [1010, 17], [1096, 104], [1237, 10], [1309, 18], [1396, 104], [1536, 11], [1609, 18], [1696, 104], [1836, 12], [1909, 18], [1996, 104], [2135, 13], [2209, 18], [2296, 104], [2435, 14], [2508, 19], [2596, 104], [2734, 15], [2808, 19], [2896, 104], [3033, 17], [3108, 19], [3195, 105], [3332, 19], [3407, 20], [3495, 106], [3630, 21], [3707, 20], [3795, 109], [3929, 23], [4007, 20], [4095, 111], [4227, 26], [4306, 21], [4394, 115], [4523, 30], [4606, 21], [4694, 160], [4905, 23], [4993, 162], [5205, 23], [5293, 163], [5504, 24], [5592, 164], [5803, 25], [5892, 169], [6092, 36], [6171, 1], [6191, 238], [6470, 3], [6490, 241], [6765, 10], [6789, 252], [7059, 19], [7087, 266], [7357, 50228], [57586, 299], [57887, 297], [58187, 297], [58488, 296], [58789, 295], [59089, 295], [59390, 295], [59691, 295], [59992, 294], [60292, 293], [60593, 292], [60894, 291], [61194, 290], [61495, 289], [61796, 288], [62097, 286], [62397, 286], [62698, 283], [62999, 281], [63299, 281], [63600, 280], [63900, 279], [64200, 279], [64500, 279], [64800, 278], [65100, 278], [65400, 278], [65700, 277], [66000, 277], [66300, 277], [66600, 276], [66900, 276], [67200, 276], [67500, 275], [67800, 275], [68100, 275], [68400, 274], [68700, 274], [69000, 274], [69300, 273], [69600, 273], [69900, 273], [70200, 272], [70500, 272], [70800, 272], [71100, 271], [71400, 271], [71700, 271], [72000, 270], [72300, 270], [72600, 270], [72900, 269], [73200, 269], [73500, 269], [73800, 268], [74100, 268], [74400, 268], [74700, 267], [75000, 267], [75300, 267], [75600, 266], [75900, 266], [76200, 266], [76500, 265], [76800, 265], [77100, 265], [77400, 264], [77700, 264], [78000, 264], [78300, 263], [78600, 263], [78900, 263], [79200, 262], [79500, 262], [79800, 262], [80100, 261], [80400, 261], [80700, 261], [81000, 260], [81300, 260], [81600, 260], [81900, 259], [82200, 259], [82500, 259], [82800, 258], [83100, 258], [83400, 258], [83700, 257], [84000, 257], [84300, 257], [84600, 256], [84900, 256], [85200, 256], [85500, 255], [85800, 255], [86100, 255], [86400, 254], [86700, 254], [87000, 254], [87300, 253], [87600, 253], [87900, 253], [88200, 252], [88500, 252], [88800, 252], [89100, 251], [89400, 251], [89700, 251]], "point": [149, 149]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan18", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.5, "y": 0.900999844, "z": 7.25}, "object_poses": [{"objectName": "Potato_5b11696a", "position": {"x": -0.31275335, "y": 0.7949763, "z": 6.479039}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 0.674817264, "y": 0.143296719, "z": 0.6552941}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": -0.6113827, "y": 0.9927421, "z": 0.6700087}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": 0.6205562, "y": 0.9927421, "z": 0.303608239}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_73d89a5b", "position": {"x": 1.559894, "y": 0.988599956, "z": 4.2942}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": 1.40406525, "y": 0.7728348, "z": 3.36045027}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": -0.6217506, "y": 0.810111, "z": 6.604039}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": 0.150742471, "y": 0.810111, "z": 6.541539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_8e8a26e1", "position": {"x": 0.3052411, "y": 0.756232858, "z": 6.541539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 0.169216156, "y": 0.7606825, "z": 6.807433}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 0.124454886, "y": 0.144737661, "z": 0.277464271}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": 2.13723, "y": 1.56507456, "z": 5.376}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": 0.8476807, "y": 1.04216564, "z": 0.6700103}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 1.14319348, "y": 0.136815369, "z": 4.72489929}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": -0.422519267, "y": 0.84850204, "z": 6.6539917}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 2.13723, "y": 1.59014213, "z": 5.501}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 0.06465548, "y": 1.17167974, "z": 0.245674372}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9f9e1c57", "position": {"x": 2.05195951, "y": 1.51464689, "z": 5.751}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_b7525b79", "position": {"x": 0.147051811, "y": 0.829698145, "z": 6.34958649}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": 1.33202, "y": 0.940655, "z": 1.98313773}, "rotation": {"x": 2.27491e-21, "y": 89.99999, "z": 1.40334183e-14}}, {"objectName": "Knife_94d26365", "position": {"x": 0.260726243, "y": 0.8013998, "z": 0.5631321}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": 1.40386784, "y": 0.9932486, "z": 3.51128745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 2.00932431, "y": 1.59014213, "z": 5.501}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": -0.221677959, "y": 0.7611296, "z": 6.787113}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 0.8154362, "y": 0.144326568, "z": 0.418435961}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": -0.0341779739, "y": 0.7550883, "z": 6.1691184}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": 2.05195951, "y": 1.60402262, "z": 6.001}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 1.13579154, "y": 0.992762148, "z": 3.23104}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": 1.27674, "y": 0.906034, "z": 2.52536845}, "rotation": {"x": -1.40334183e-14, "y": 180.0, "z": 2.25533157e-21}}, {"objectName": "PaperTowelRoll_bd2376ff", "position": {"x": 1.49322724, "y": 1.09698546, "z": 0.213347912}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_73d89a5b", "position": {"x": 1.32, "y": 0.988599956, "z": 3.848}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": 1.13578975, "y": 1.027588, "z": 4.67067957}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": 0.6205562, "y": 0.9927421, "z": 0.6700103}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": -0.308550477, "y": 0.9877, "z": 0.486808717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_08687688", "position": {"x": 1.3131808, "y": 0.9855794, "z": 4.28467369}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": -0.2924135, "y": 0.80955404, "z": 6.34710932}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 1.48978925, "y": 2.20666957, "z": 3.84069443}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_8e8a26e1", "position": {"x": 0.440992653, "y": 1.57573712, "z": 0.251273155}, "rotation": {"x": 0.0, "y": 0.0, "z": -1.70754731e-06}}, {"objectName": "Spoon_cefc2847", "position": {"x": 1.33202, "y": 0.89201, "z": 2.239636}, "rotation": {"x": -1.40334191e-14, "y": 180.0, "z": 1.71913032e-21}}, {"objectName": "Mug_6d84c70f", "position": {"x": 0.167965025, "y": 1.57459259, "z": 0.338969976}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 910813349, "scene_num": 18}, "task_id": "trial_T20190906_201032_052098", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "ACSS93E03ZUGX_3ERMJ6L4D1904SZ3Y9EUW3EPOQIM79", "high_descs": ["Turn around and walk all the way to the end of kitchen facing the counter.", "Pick up the knife from off of the counter.", "Turn around and walk straight to the kitchen table.", "Slice the bread with the knife on the kitchen table.", "Continue to stand in front of the table.", "Place the knife on top of the table.", "Continue to stand in front of the table.", "Pick up the slice of bread from the table.", "Turn around and walk towards the microwave on the counter at the other side of the kitchen.", "Open the microwave, place the bread inside of it, close the microwave, wait a second, open the microwave, pick up the slice of bread and close the microwave.", "Turn around and walk across the kitchen then turn right to face the refrigerator.", "Open the refrigerator, place the bread inside and close the refrigerator."], "task_desc": "Place the microwaved bread inside of the refrigerator.", "votes": [1, 1]}, {"assignment_id": "A2M7VEYAS1405L_3RU7GD8VPRAS8TG8ES4X8Y4GG9NPSC", "high_descs": ["turn around and cross the room to the counter next to the microwave", "take the knife on the back of the counter", "cross the room to the trashcan next to the refrigerator, turn left, and face the round grey table", "slice the bread with the knife", "look up across the table", "put the knife on the grey plate", "look down across the table", "take a slice of bread", "cross the room to the microwave", "put the bread in the microwave, to the left of the apple in the microwave, close the door, and run the microwave for 3 seconds. Then, remove the slice of bread from the microwave and close the microwave door.", "cross the room to the refrigerator", "open the refrigerator, place the bread on the shelf next to the apple, and close the refrigerator"], "task_desc": "Place a slice of microwaved bread in the refrigerator.", "votes": [1, 1]}, {"assignment_id": "A3HL2LL0LEPZT8_3XIQGXAUMFPBY4Y7CGG37KUNVUF7XR", "high_descs": ["Turn around, go past the round table, turn left, go to the fridge, turn right, go to the counter to the left of the microwave.", "Take the knife that is closest to the microwave.", "Turn around, go in between the round table and the fridge, face the round table.", "Slice the bread on the table.", "Stay at the table.", "Put the knife to the right of the cup on the table.", "Stay at the table.", "Take a piece of bread from the table.", "Turn left, go to the microwave on the other side of the room.", "Heat the bread in the microwave, take the bread from the microwave.", "Turn around, go to the black table, turn right to go to the fridge.", "Put the bread in the fridge."], "task_desc": "Put a heated slice of bread in a fridge.", "votes": [1, 1]}]}}