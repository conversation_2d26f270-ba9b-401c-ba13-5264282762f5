{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000266.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 33}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-6|4|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [-5.631528, -5.631528, 1.762879016, 1.762879016, 0.481232764, 0.481232764]], "coordinateReceptacleObjectId": ["Cabinet", [-5.14542484, -5.14542484, 2.258996, 2.258996, 1.540134548, 1.540134548]], "forceVisible": true, "objectId": "Mug|-01.41|+00.12|+00.44"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-8|5|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-11.18, -11.18, 5.396, 5.396, 3.2488436, 3.2488436]], "forceVisible": true, "objectId": "Microwave|-02.80|+00.81|+01.35"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-8|5|3|-15"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [-5.631528, -5.631528, 1.762879016, 1.762879016, 0.481232764, 0.481232764]], "coordinateReceptacleObjectId": ["Cabinet", [-11.1271496, -11.1271496, 3.301936628, 3.301936628, 7.19391156, 7.19391156]], "forceVisible": true, "objectId": "Mug|-01.41|+00.12|+00.44", "receptacleObjectId": "Cabinet|-02.78|+01.80|+00.83"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-01.29|+00.39|+00.56"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 172, 215, 213], "mask": [[51395, 121], [51695, 121], [51996, 119], [52296, 119], [52597, 117], [52897, 117], [53198, 115], [53498, 115], [53798, 114], [54099, 113], [54399, 112], [54700, 111], [55000, 110], [55301, 109], [55601, 108], [55901, 108], [56202, 106], [56502, 106], [56803, 104], [57103, 103], [57404, 102], [57704, 101], [58004, 101], [58305, 99], [58605, 99], [58906, 97], [59206, 97], [59507, 95], [59807, 95], [60107, 94], [60408, 93], [60708, 92], [61009, 91], [61309, 90], [61610, 89], [61910, 88], [62210, 88], [62511, 86], [62811, 86], [63112, 84], [63412, 84], [63713, 82]], "point": [155, 191]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-01.41|+00.12|+00.44"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [125, 178, 146, 199], "mask": [[53230, 8], [53528, 11], [53827, 13], [54126, 15], [54426, 16], [54725, 17], [55025, 17], [55043, 1], [55325, 21], [55625, 22], [55925, 22], [56225, 18], [56244, 3], [56525, 22], [56825, 21], [57126, 19], [57426, 17], [57726, 17], [58026, 16], [58327, 15], [58627, 14], [58928, 13], [59229, 10], [59531, 7]], "point": [135, 187]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-01.29|+00.39|+00.56"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [78, 172, 212, 300], "mask": [[51393, 3], [51398, 90], [51512, 1], [51693, 4], [51699, 89], [51993, 4], [51999, 89], [52111, 1], [52293, 5], [52300, 88], [52593, 5], [52600, 88], [52710, 1], [52893, 6], [52900, 88], [53192, 7], [53201, 87], [53309, 1], [53492, 7], [53501, 87], [53792, 8], [53802, 87], [53908, 1], [54092, 8], [54102, 87], [54392, 9], [54402, 87], [54507, 1], [54692, 9], [54703, 86], [54992, 9], [55003, 86], [55292, 10], [55304, 85], [55591, 11], [55604, 85], [55891, 12], [55904, 85], [56191, 12], [56205, 84], [56491, 13], [56505, 84], [56791, 13], [56806, 83], [57091, 13], [57106, 83], [57391, 14], [57406, 84], [57691, 14], [57707, 83], [57991, 15], [58007, 83], [58290, 16], [58308, 82], [58590, 16], [58608, 82], [58890, 17], [58908, 82], [59190, 17], [59209, 81], [59490, 18], [59509, 81], [59790, 18], [59810, 80], [60090, 19], [60110, 80], [60390, 19], [60410, 80], [60689, 20], [60711, 79], [60989, 21], [61011, 79], [61289, 21], [61312, 31], [61357, 34], [61589, 22], [61612, 26], [61662, 29], [61889, 22], [61912, 24], [61964, 27], [61995, 1], [62189, 22], [62213, 20], [62267, 24], [62489, 23], [62513, 18], [62569, 22], [62594, 1], [62789, 23], [62814, 15], [62871, 20], [63088, 25], [63114, 13], [63173, 21], [63388, 25], [63688, 26], [63988, 26], [64288, 26], [64588, 26], [64888, 26], [65188, 26], [65488, 25], [65787, 26], [66087, 26], [66387, 26], [66687, 26], [66987, 26], [67287, 26], [67587, 25], [67887, 25], [68186, 25], [68486, 25], [68786, 24], [69086, 24], [69386, 24], [69686, 23], [69986, 23], [70286, 22], [70585, 23], [70885, 22], [71185, 22], [71485, 22], [71785, 8], [72085, 6], [72385, 5], [72685, 4], [72984, 5], [73284, 4], [73584, 4], [73884, 3], [74184, 3], [74484, 3], [74784, 3], [75084, 3], [75384, 3], [75683, 4], [75983, 4], [76283, 4], [76583, 4], [76883, 4], [77183, 5], [77483, 5], [77783, 6], [78082, 9], [78382, 11], [78682, 15], [78982, 20], [79282, 25], [79582, 26], [79882, 26], [80182, 26], [80481, 28], [80781, 28], [81081, 28], [81381, 29], [81681, 29], [81981, 29], [82281, 29], [82581, 28], [82881, 28], [83180, 29], [83480, 29], [83780, 29], [84080, 29], [84380, 29], [84680, 29], [84980, 29], [85280, 29], [85579, 30], [85879, 30], [86179, 30], [86479, 30], [86779, 29], [87079, 29], [87379, 29], [87679, 29], [87978, 30], [88278, 30], [88578, 30], [88878, 30], [89178, 30], [89478, 30], [89778, 30]], "point": [135, 206]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-01.41|+00.12|+00.44", "placeStationary": true, "receptacleObjectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [2, 34, 295, 294], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25870, 224], [26170, 223], [26469, 224], [26768, 224], [27068, 224], [27367, 224], [27666, 225], [27965, 226], [28265, 225], [28564, 226], [28864, 225], [29163, 226], [29463, 226], [29763, 226], [30062, 227], [30362, 227], [30661, 227], [30961, 227], [31260, 228], [31560, 228], [31860, 228], [32159, 229], [32459, 229], [32758, 230], [33058, 229], [33358, 229], [33657, 229], [33957, 229], [34256, 229], [34556, 229], [34855, 230], [35155, 229], [35455, 229], [35754, 229], [36054, 229], [36353, 229], [36653, 229], [36953, 228], [37252, 229], [37552, 228], [37851, 229], [38151, 228], [38450, 229], [38750, 229], [39050, 228], [39349, 229], [39649, 228], [39948, 229], [40248, 228], [40548, 228], [40847, 228], [41147, 228], [41446, 228], [41746, 228], [42045, 229], [42345, 228], [42645, 228], [42944, 228], [43244, 228], [43543, 228], [43843, 228], [44143, 227], [44442, 228], [44742, 227], [45041, 228], [45341, 227], [45640, 228], [45940, 228], [46240, 227], [46539, 228], [46839, 227], [47138, 228], [47438, 227], [47738, 227], [48037, 227], [48337, 227], [48636, 227], [48936, 227], [49235, 228], [49535, 227], [49835, 227], [50134, 227], [50434, 224], [50733, 224], [51033, 224], [51333, 223], [51632, 64], [51697, 159], [51932, 64], [51997, 158], [52231, 65], [52531, 65], [52830, 66], [53130, 66], [53430, 65], [53729, 66], [54029, 66], [54328, 67], [54628, 67], [54927, 67], [55227, 67], [55527, 67], [55826, 68], [56126, 68], [56425, 68], [56725, 68], [57025, 68], [57324, 69], [57624, 69], [57923, 70], [58223, 69], [58522, 70], [58822, 70], [59122, 70], [59421, 71], [59721, 70], [60020, 71], [60320, 71], [60620, 71], [60919, 72], [61219, 71], [61518, 72], [61818, 72], [62117, 73], [62417, 73], [62717, 72], [63016, 73], [63316, 73], [63615, 74], [63915, 74], [64215, 74], [64514, 74], [64814, 74], [65113, 75], [65413, 75], [65712, 76], [66012, 75], [66312, 75], [66611, 76], [66911, 76], [67210, 77], [67510, 76], [67810, 76], [68109, 77], [68409, 77], [68708, 78], [69008, 77], [69307, 78], [69607, 78], [69907, 78], [70206, 79], [70506, 79], [70805, 79], [71105, 79], [71405, 79], [71704, 80], [72004, 80], [72303, 80], [72603, 80], [72902, 81], [73202, 81], [73503, 80], [73804, 78], [74105, 77], [74405, 77], [74706, 76], [75007, 75], [75308, 74], [75609, 72], [75910, 71], [76211, 70], [76512, 69], [76813, 68], [77113, 67], [77414, 66], [77715, 65], [78016, 64], [78317, 63], [78618, 61], [78919, 60], [79220, 59], [79521, 58], [79822, 57], [80122, 56], [80423, 55], [80724, 54], [81025, 53], [81326, 52], [81627, 51], [81928, 49], [82229, 48], [82530, 47], [82831, 46], [83131, 46], [83432, 44], [83733, 43], [84034, 42], [84335, 41], [84636, 40], [84937, 38], [85238, 37], [85539, 36], [85839, 36], [86140, 38], [86441, 39], [86742, 39], [87043, 31], [87076, 5], [87345, 29], [87377, 2], [87649, 25], [87953, 20]], "point": [148, 163]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [2, 34, 295, 294], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25870, 224], [26170, 223], [26469, 224], [26768, 224], [27068, 224], [27367, 224], [27666, 225], [27965, 226], [28265, 225], [28564, 226], [28864, 225], [29163, 226], [29463, 226], [29763, 226], [30062, 227], [30362, 227], [30661, 227], [30961, 227], [31260, 83], [31347, 141], [31560, 78], [31652, 136], [31860, 76], [31954, 134], [32159, 75], [32256, 132], [32459, 74], [32557, 131], [32758, 74], [32858, 130], [33058, 73], [33158, 129], [33358, 73], [33459, 128], [33657, 74], [33763, 123], [33957, 74], [34064, 122], [34256, 75], [34365, 120], [34556, 75], [34665, 120], [34855, 76], [34959, 3], [34966, 119], [35155, 76], [35259, 4], [35266, 118], [35455, 76], [35559, 4], [35566, 118], [35754, 77], [35859, 4], [35866, 117], [36054, 77], [36159, 4], [36166, 117], [36353, 78], [36459, 4], [36465, 117], [36653, 78], [36759, 3], [36765, 117], [36953, 78], [37059, 3], [37065, 116], [37252, 79], [37359, 2], [37365, 116], [37552, 80], [37659, 1], [37664, 116], [37851, 81], [37964, 116], [38151, 81], [38263, 116], [38450, 82], [38562, 117], [38750, 82], [38860, 119], [39050, 82], [39158, 120], [39349, 83], [39458, 120], [39649, 83], [39758, 119], [39948, 84], [40058, 119], [40248, 84], [40358, 118], [40548, 84], [40658, 118], [40847, 86], [40958, 117], [41147, 86], [41257, 118], [41446, 88], [41556, 118], [41746, 89], [41855, 119], [42045, 91], [42154, 120], [42345, 93], [42452, 121], [42645, 96], [42749, 124], [42944, 228], [43244, 228], [43543, 228], [43843, 228], [44143, 227], [44442, 228], [44742, 227], [45041, 228], [45341, 227], [45640, 228], [45940, 228], [46240, 227], [46539, 228], [46839, 227], [47138, 228], [47438, 227], [47738, 227], [48037, 227], [48337, 227], [48636, 227], [48936, 227], [49235, 228], [49535, 227], [49835, 227], [50134, 227], [50434, 224], [50733, 224], [51033, 224], [51333, 223], [51632, 64], [51697, 159], [51932, 64], [51997, 158], [52231, 65], [52531, 65], [52830, 66], [53130, 66], [53430, 65], [53729, 66], [54029, 66], [54328, 67], [54628, 67], [54927, 67], [55227, 67], [55527, 67], [55826, 68], [56126, 68], [56425, 68], [56725, 68], [57025, 68], [57324, 69], [57624, 69], [57923, 70], [58223, 69], [58522, 70], [58822, 70], [59122, 70], [59421, 71], [59721, 70], [60020, 71], [60320, 71], [60620, 71], [60919, 72], [61219, 71], [61518, 72], [61818, 72], [62117, 73], [62417, 73], [62717, 72], [63016, 73], [63316, 73], [63615, 74], [63915, 74], [64215, 74], [64514, 74], [64814, 74], [65113, 75], [65413, 75], [65712, 76], [66012, 75], [66312, 75], [66611, 76], [66911, 76], [67210, 77], [67510, 76], [67810, 76], [68109, 77], [68409, 77], [68708, 78], [69008, 77], [69307, 78], [69607, 78], [69907, 78], [70206, 79], [70506, 79], [70805, 79], [71105, 79], [71405, 79], [71704, 80], [72004, 80], [72303, 80], [72603, 80], [72902, 81], [73202, 81], [73503, 80], [73804, 78], [74105, 77], [74405, 77], [74706, 76], [75007, 75], [75308, 74], [75609, 72], [75910, 71], [76211, 70], [76512, 69], [76813, 68], [77113, 67], [77414, 66], [77715, 65], [78016, 64], [78317, 63], [78618, 61], [78919, 60], [79220, 59], [79521, 58], [79822, 57], [80122, 56], [80423, 55], [80724, 54], [81025, 53], [81326, 52], [81627, 51], [81928, 49], [82229, 48], [82530, 47], [82831, 46], [83131, 46], [83432, 44], [83733, 43], [84034, 42], [84335, 41], [84636, 40], [84937, 38], [85238, 37], [85539, 36], [85839, 36], [86140, 38], [86441, 39], [86742, 39], [87043, 31], [87076, 5], [87345, 29], [87377, 2], [87649, 25], [87953, 20]], "point": [148, 163]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-11.18, -11.18, 5.396, 5.396, 3.2488436, 3.2488436]], "forceVisible": true, "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-11.18, -11.18, 5.396, 5.396, 3.2488436, 3.2488436]], "forceVisible": true, "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-01.41|+00.12|+00.44"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [131, 105, 165, 143], "mask": [[31343, 4], [31638, 14], [31936, 18], [32234, 22], [32533, 24], [32832, 26], [33131, 27], [33431, 28], [33731, 32], [34031, 33], [34331, 34], [34631, 34], [34931, 28], [34962, 4], [35231, 28], [35263, 3], [35531, 28], [35563, 3], [35831, 28], [35863, 3], [36131, 28], [36163, 3], [36431, 28], [36463, 2], [36731, 28], [36762, 3], [37031, 28], [37062, 3], [37331, 28], [37361, 4], [37632, 27], [37660, 4], [37932, 32], [38232, 31], [38532, 30], [38832, 28], [39132, 26], [39432, 26], [39732, 26], [40032, 26], [40332, 26], [40632, 26], [40933, 25], [41233, 24], [41534, 22], [41835, 20], [42136, 18], [42438, 14], [42741, 8]], "point": [148, 123]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [2, 34, 295, 294], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25870, 224], [26170, 223], [26469, 224], [26768, 224], [27068, 224], [27367, 224], [27666, 225], [27965, 226], [28265, 225], [28564, 226], [28864, 225], [29163, 226], [29463, 226], [29763, 226], [30062, 227], [30362, 227], [30661, 227], [30961, 227], [31260, 228], [31560, 228], [31860, 228], [32159, 229], [32459, 229], [32758, 230], [33058, 229], [33358, 229], [33657, 229], [33957, 229], [34256, 229], [34556, 229], [34855, 230], [35155, 229], [35455, 229], [35754, 229], [36054, 229], [36353, 229], [36653, 229], [36953, 228], [37252, 229], [37552, 228], [37851, 229], [38151, 228], [38450, 229], [38750, 229], [39050, 228], [39349, 229], [39649, 228], [39948, 229], [40248, 228], [40548, 228], [40847, 228], [41147, 228], [41446, 228], [41746, 228], [42045, 229], [42345, 228], [42645, 228], [42944, 228], [43244, 228], [43543, 228], [43843, 228], [44143, 227], [44442, 228], [44742, 227], [45041, 228], [45341, 227], [45640, 228], [45940, 228], [46240, 227], [46539, 228], [46839, 227], [47138, 228], [47438, 227], [47738, 227], [48037, 227], [48337, 227], [48636, 227], [48936, 227], [49235, 228], [49535, 227], [49835, 227], [50134, 227], [50434, 224], [50733, 224], [51033, 224], [51333, 223], [51632, 64], [51697, 159], [51932, 64], [51997, 158], [52231, 65], [52531, 65], [52830, 66], [53130, 66], [53430, 65], [53729, 66], [54029, 66], [54328, 67], [54628, 67], [54927, 67], [55227, 67], [55527, 67], [55826, 68], [56126, 68], [56425, 68], [56725, 68], [57025, 68], [57324, 69], [57624, 69], [57923, 70], [58223, 69], [58522, 70], [58822, 70], [59122, 70], [59421, 71], [59721, 70], [60020, 71], [60320, 71], [60620, 71], [60919, 72], [61219, 71], [61518, 72], [61818, 72], [62117, 73], [62417, 73], [62717, 72], [63016, 73], [63316, 73], [63615, 74], [63915, 74], [64215, 74], [64514, 74], [64814, 74], [65113, 75], [65413, 75], [65712, 76], [66012, 75], [66312, 75], [66611, 76], [66911, 76], [67210, 77], [67510, 76], [67810, 76], [68109, 77], [68409, 77], [68708, 78], [69008, 77], [69307, 78], [69607, 78], [69907, 78], [70206, 79], [70506, 79], [70805, 79], [71105, 79], [71405, 79], [71704, 80], [72004, 80], [72303, 80], [72603, 80], [72902, 81], [73202, 81], [73503, 80], [73804, 78], [74105, 77], [74405, 77], [74706, 76], [75007, 75], [75308, 74], [75609, 72], [75910, 71], [76211, 70], [76512, 69], [76813, 68], [77113, 67], [77414, 66], [77715, 65], [78016, 64], [78317, 63], [78618, 61], [78919, 60], [79220, 59], [79521, 58], [79822, 57], [80122, 56], [80423, 55], [80724, 54], [81025, 53], [81326, 52], [81627, 51], [81928, 49], [82229, 48], [82530, 47], [82831, 46], [83131, 46], [83432, 44], [83733, 43], [84034, 42], [84335, 41], [84636, 40], [84937, 38], [85238, 37], [85539, 36], [85839, 36], [86140, 38], [86441, 39], [86742, 39], [87043, 31], [87076, 5], [87345, 29], [87377, 2], [87649, 25], [87953, 20]], "point": [148, 163]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-02.78|+01.80|+00.83"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 42, 299, 279], "mask": [[12329, 103], [12433, 146], [12628, 251], [12928, 251], [13228, 251], [13528, 252], [13828, 252], [14128, 252], [14428, 252], [14727, 253], [15027, 253], [15327, 253], [15627, 254], [15927, 254], [16227, 254], [16527, 254], [16826, 255], [17126, 255], [17426, 256], [17726, 256], [18026, 256], [18326, 256], [18626, 256], [18925, 257], [19225, 257], [19525, 258], [19825, 258], [20125, 258], [20425, 258], [20725, 258], [21024, 259], [21324, 259], [21624, 260], [21924, 260], [22224, 260], [22524, 260], [22824, 260], [23123, 261], [23423, 262], [23723, 262], [24023, 262], [24323, 262], [24623, 262], [24923, 262], [25222, 263], [25522, 264], [25822, 264], [26122, 264], [26422, 264], [26722, 264], [27022, 264], [27321, 265], [27621, 266], [27921, 266], [28221, 266], [28521, 266], [28821, 266], [29121, 266], [29420, 268], [29720, 268], [30020, 268], [30320, 268], [30620, 268], [30920, 268], [31220, 268], [31519, 270], [31819, 270], [32119, 270], [32419, 270], [32719, 270], [33019, 270], [33319, 270], [33618, 272], [33918, 272], [34218, 272], [34518, 272], [34818, 272], [35118, 272], [35418, 273], [35717, 274], [36017, 274], [36317, 274], [36617, 274], [36917, 274], [37217, 274], [37517, 275], [37816, 276], [38116, 276], [38416, 276], [38716, 276], [39016, 276], [39316, 276], [39616, 277], [39916, 277], [40215, 278], [40515, 278], [40815, 278], [41115, 278], [41415, 279], [41715, 279], [42015, 279], [42314, 280], [42614, 280], [42914, 280], [43214, 280], [43514, 281], [43814, 281], [44114, 281], [44413, 282], [44713, 282], [45013, 282], [45313, 282], [45613, 283], [45913, 283], [46213, 283], [46512, 284], [46812, 284], [47112, 284], [47412, 285], [47712, 285], [48012, 285], [48312, 285], [48611, 286], [48911, 286], [49211, 286], [49511, 287], [49811, 287], [50111, 287], [50411, 287], [50710, 288], [51010, 288], [51310, 288], [51610, 289], [51910, 289], [52210, 289], [52510, 289], [52809, 290], [53109, 290], [53409, 290], [53709, 291], [54009, 291], [54309, 291], [54609, 291], [54908, 292], [55208, 292], [55508, 292], [55808, 292], [56108, 292], [56408, 292], [56708, 141], [56851, 149], [57007, 133], [57160, 140], [57307, 130], [57463, 137], [57607, 127], [57766, 134], [57907, 124], [58069, 131], [58207, 122], [58371, 129], [58507, 121], [58672, 128], [58807, 119], [58974, 126], [59106, 118], [59276, 124], [59406, 117], [59577, 123], [59706, 116], [59878, 122], [60006, 115], [60179, 121], [60306, 114], [60480, 120], [60606, 113], [60781, 119], [60906, 112], [61082, 118], [61205, 112], [61383, 117], [61505, 111], [61684, 116], [61805, 110], [61985, 115], [62105, 109], [62286, 114], [62405, 109], [62586, 114], [62705, 108], [62887, 113], [63005, 107], [63188, 112], [63305, 107], [63488, 112], [63604, 107], [63789, 111], [63904, 107], [64089, 111], [64204, 106], [64390, 110], [64504, 106], [64690, 110], [64804, 106], [64990, 110], [65104, 105], [65291, 109], [65404, 105], [65591, 109], [65703, 95], [65891, 109], [66003, 93], [66191, 109], [66303, 91], [66492, 108], [66603, 90], [66792, 108], [66903, 89], [67092, 108], [67203, 89], [67392, 108], [67503, 88], [67692, 108], [67802, 89], [67992, 108], [68102, 89], [68292, 108], [68402, 88], [68592, 108], [68702, 88], [68892, 108], [69002, 88], [69192, 108], [69302, 87], [69492, 108], [69602, 87], [69792, 108], [69901, 88], [70092, 108], [70201, 88], [70392, 108], [70501, 88], [70600, 8], [70692, 108], [70801, 88], [70901, 7], [70992, 108], [71101, 88], [71202, 7], [71291, 109], [71401, 89], [71503, 6], [71591, 109], [71701, 89], [71805, 4], [71891, 199], [72107, 2], [72191, 199], [72409, 1], [72490, 201], [72790, 201], [73090, 202], [73390, 203], [73689, 204], [73989, 205], [74289, 206], [74589, 207], [74888, 209], [75188, 210], [75488, 211], [75788, 212], [76087, 215], [76387, 217], [76687, 220], [76987, 223], [77287, 227], [77586, 228], [77886, 228], [78186, 228], [78486, 229], [78785, 230], [79085, 230], [79385, 230], [79685, 231], [79984, 232], [80284, 232], [80584, 233], [80883, 234], [81183, 234], [81483, 234], [81783, 235], [82082, 236], [82382, 236], [82682, 237], [82981, 238], [83281, 239], [83580, 120]], "point": [149, 159]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-01.41|+00.12|+00.44", "placeStationary": true, "receptacleObjectId": "Cabinet|-02.78|+01.80|+00.83"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 275], "mask": [[0, 2], [300, 3], [600, 4], [900, 4], [1200, 5], [1500, 6], [1800, 7], [2100, 7], [2400, 8], [2700, 9], [3000, 9], [3300, 10], [3599, 12], [3898, 13], [4197, 15], [4497, 16], [4796, 17], [5095, 19], [5395, 20], [5694, 21], [5993, 23], [6292, 25], [6592, 25], [6891, 27], [7190, 29], [7490, 29], [7789, 31], [8088, 33], [8387, 34], [8687, 35], [8986, 37], [9285, 38], [9585, 39], [9884, 41], [10183, 42], [10482, 44], [10782, 45], [11081, 46], [11380, 48], [11680, 49], [11979, 50], [12278, 52], [12577, 53], [12877, 53], [12933, 243], [13177, 53], [13233, 242], [13478, 52], [13533, 242], [13778, 52], [13833, 241], [14078, 52], [14133, 240], [14378, 51], [14432, 241], [14678, 51], [14732, 240], [14978, 51], [15032, 239], [15278, 51], [15332, 238], [15579, 50], [15632, 238], [15879, 50], [15932, 237], [16179, 50], [16232, 236], [16479, 49], [16532, 236], [16779, 49], [16831, 236], [17079, 49], [17131, 235], [17379, 49], [17431, 234], [17680, 48], [17731, 234], [17980, 48], [18031, 233], [18280, 48], [18331, 232], [18580, 47], [18631, 232], [18880, 47], [18930, 232], [19180, 47], [19230, 231], [19481, 46], [19530, 230], [19781, 46], [19830, 230], [20081, 46], [20130, 229], [20381, 46], [20430, 228], [20681, 45], [20730, 227], [20981, 45], [21029, 228], [21281, 45], [21329, 227], [21582, 44], [21629, 227], [21882, 44], [21929, 227], [22182, 44], [22229, 227], [22482, 44], [22529, 227], [22782, 43], [22829, 227], [23082, 43], [23382, 43], [23683, 42], [23983, 42], [24283, 42], [24583, 42], [24628, 254], [24883, 42], [24928, 253], [25183, 41], [25228, 252], [25483, 41], [25527, 252], [25784, 40], [25827, 251], [26084, 40], [26127, 250], [26384, 40], [26427, 249], [26684, 40], [26727, 248], [26984, 40], [27027, 247], [27284, 39], [27327, 246], [27585, 38], [27627, 245], [27885, 38], [27926, 245], [28185, 38], [28226, 244], [28485, 38], [28526, 243], [28785, 38], [28826, 242], [29085, 38], [29126, 241], [29385, 37], [29426, 240], [29686, 36], [29726, 239], [29986, 36], [30025, 239], [30286, 36], [30325, 238], [30586, 36], [30625, 237], [30886, 36], [30925, 236], [31186, 36], [31225, 235], [31486, 35], [31525, 235], [31787, 34], [31825, 235], [32087, 34], [32124, 236], [32387, 34], [32424, 236], [32687, 34], [32724, 236], [32987, 34], [33024, 236], [33287, 34], [33324, 236], [33587, 33], [33624, 236], [33888, 32], [33924, 237], [34188, 32], [34224, 237], [34488, 32], [34523, 238], [34788, 32], [34823, 238], [35088, 32], [35123, 238], [35388, 32], [35423, 238], [35688, 32], [35723, 238], [35989, 30], [36023, 238], [36289, 30], [36323, 239], [36589, 30], [36622, 240], [36889, 30], [36922, 240], [37189, 30], [37222, 240], [37489, 30], [37522, 240], [37790, 29], [37822, 240], [38090, 28], [38122, 240], [38390, 28], [38422, 240], [38690, 28], [38721, 241], [38990, 28], [39021, 242], [39290, 28], [39321, 242], [39590, 28], [39621, 242], [39891, 27], [39921, 242], [40191, 26], [40221, 242], [40491, 26], [40521, 242], [40791, 26], [40821, 242], [41091, 26], [41120, 243], [41391, 26], [41420, 244], [41691, 26], [41720, 244], [41992, 25], [42020, 244], [42292, 24], [42320, 244], [42592, 24], [42620, 244], [42892, 24], [42920, 244], [43192, 24], [43219, 245], [43492, 24], [43519, 245], [43792, 24], [43819, 245], [44093, 23], [44119, 246], [44393, 22], [44419, 246], [44693, 22], [44719, 246], [44993, 22], [45019, 246], [45293, 22], [45318, 247], [45593, 22], [45618, 247], [45894, 21], [45918, 247], [46194, 21], [46218, 247], [46494, 21], [46518, 248], [46794, 20], [46818, 248], [47094, 20], [47118, 248], [47394, 20], [47418, 248], [47694, 20], [47717, 249], [47995, 19], [48017, 249], [48295, 19], [48317, 249], [48595, 19], [48617, 249], [48895, 18], [48917, 249], [49195, 18], [49217, 250], [49495, 18], [49517, 250], [49795, 18], [49816, 251], [50096, 17], [50116, 251], [50396, 17], [50416, 251], [50696, 17], [50716, 251], [50996, 16], [51016, 251], [51296, 16], [51316, 251], [51596, 16], [51616, 252], [51896, 16], [51916, 252], [52197, 15], [52215, 253], [52497, 15], [52515, 253], [52797, 15], [52815, 253], [53097, 14], [53115, 253], [53397, 14], [53415, 253], [53697, 14], [53715, 253], [53997, 14], [54015, 254], [54298, 13], [54314, 255], [54598, 13], [54614, 255], [54898, 13], [54914, 255], [55198, 12], [55214, 255], [55498, 12], [55514, 255], [55798, 12], [55814, 255], [56099, 11], [56114, 255], [56399, 11], [56413, 256], [56699, 11], [56713, 136], [56851, 119], [56999, 11], [57013, 127], [57160, 110], [57299, 11], [57313, 124], [57463, 107], [57599, 10], [57613, 121], [57766, 104], [57899, 10], [57913, 118], [58069, 101], [58200, 9], [58213, 116], [58371, 99], [58500, 9], [58513, 115], [58672, 98], [58800, 9], [58812, 114], [58974, 96], [59100, 9], [59112, 112], [59276, 95], [59400, 9], [59412, 111], [59577, 94], [59700, 8], [59712, 110], [59878, 93], [60000, 8], [60012, 109], [60179, 92], [60300, 8], [60312, 108], [60480, 91], [60600, 8], [60612, 107], [60781, 90], [60900, 8], [60911, 107], [61082, 89], [61200, 8], [61211, 106], [61383, 88], [61500, 8], [61511, 105], [61684, 87], [61800, 7], [61811, 104], [61985, 87], [62100, 7], [62111, 103], [62286, 86], [62400, 7], [62411, 103], [62586, 86], [62700, 7], [62711, 102], [62887, 85], [63000, 7], [63010, 102], [63188, 84], [63300, 7], [63310, 102], [63488, 84], [63600, 7], [63610, 101], [63789, 83], [63900, 6], [63910, 101], [64089, 83], [64200, 6], [64210, 100], [64390, 83], [64500, 6], [64510, 100], [64690, 83], [64800, 6], [64810, 100], [64990, 83], [65100, 6], [65110, 99], [65291, 82], [65400, 6], [65409, 100], [65591, 82], [65700, 6], [65709, 89], [65891, 82], [66000, 5], [66009, 87], [66191, 82], [66300, 5], [66309, 85], [66492, 81], [66600, 5], [66609, 84], [66792, 81], [66900, 5], [66909, 83], [67092, 82], [67200, 5], [67209, 83], [67392, 82], [67500, 5], [67508, 83], [67692, 82], [67800, 5], [67808, 83], [67992, 82], [68100, 5], [68108, 83], [68292, 82], [68400, 4], [68408, 82], [68592, 82], [68700, 4], [68708, 82], [68892, 82], [69000, 4], [69008, 82], [69192, 82], [69300, 4], [69308, 81], [69492, 83], [69600, 4], [69607, 82], [69792, 83], [69900, 4], [69907, 82], [70092, 83], [70200, 4], [70207, 82], [70392, 83], [70500, 3], [70507, 82], [70600, 8], [70692, 83], [70800, 3], [70807, 82], [70901, 7], [70992, 83], [71100, 3], [71107, 82], [71202, 7], [71291, 84], [71400, 3], [71407, 83], [71503, 6], [71591, 84], [71700, 3], [71707, 83], [71805, 4], [71891, 84], [72000, 3], [72006, 84], [72107, 2], [72191, 85], [72300, 3], [72306, 84], [72409, 1], [72490, 86], [72600, 2], [72606, 85], [72790, 86], [72900, 2], [72906, 85], [73090, 86], [73200, 2], [73206, 86], [73390, 86], [73500, 2], [73506, 87], [73689, 87], [73800, 2], [73806, 87], [73989, 87], [74100, 2], [74105, 89], [74289, 87], [74400, 2], [74405, 90], [74589, 88], [74700, 1], [74705, 91], [74888, 89], [75000, 1], [75005, 92], [75188, 89], [75300, 1], [75305, 93], [75488, 89], [75600, 1], [75605, 94], [75788, 89], [75900, 1], [75905, 95], [76087, 90], [76200, 1], [76205, 97], [76387, 90], [76500, 1], [76504, 100], [76687, 90], [76804, 103], [76987, 91], [77104, 106], [77287, 91], [77404, 110], [77586, 92], [77704, 110], [77886, 92], [78004, 110], [78186, 92], [78304, 110], [78486, 92], [78603, 112], [78785, 93], [78903, 112], [79085, 93], [79203, 112], [79385, 95], [79503, 112], [79685, 97], [79803, 113], [79984, 101], [80103, 113], [80284, 104], [80403, 113], [80584, 107], [80702, 115], [80883, 111], [81002, 115], [81183, 114], [81302, 115], [81483, 117], [81602, 115], [81783, 117], [81902, 116], [82082, 118], [82202, 116], [82382, 118]], "point": [149, 137]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-02.78|+01.80|+00.83"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 275], "mask": [[0, 2], [300, 3], [600, 4], [900, 4], [1200, 5], [1500, 6], [1800, 7], [2100, 7], [2400, 8], [2700, 9], [3000, 9], [3300, 10], [3599, 12], [3898, 13], [4197, 15], [4497, 16], [4796, 17], [5095, 19], [5395, 20], [5694, 21], [5993, 23], [6292, 25], [6592, 25], [6891, 27], [7190, 29], [7490, 29], [7789, 31], [8088, 33], [8387, 34], [8687, 35], [8986, 37], [9285, 38], [9585, 39], [9884, 41], [10183, 42], [10482, 44], [10782, 45], [11081, 46], [11380, 48], [11680, 49], [11979, 50], [12278, 52], [12577, 53], [12877, 53], [12933, 243], [13177, 53], [13233, 242], [13478, 52], [13533, 242], [13778, 52], [13833, 241], [14078, 52], [14133, 240], [14378, 51], [14432, 241], [14678, 51], [14732, 240], [14978, 51], [15032, 239], [15278, 51], [15332, 238], [15579, 50], [15632, 238], [15879, 50], [15932, 237], [16179, 50], [16232, 236], [16479, 49], [16532, 236], [16779, 49], [16831, 236], [17079, 49], [17131, 235], [17379, 49], [17431, 234], [17680, 48], [17731, 234], [17980, 48], [18031, 233], [18280, 48], [18331, 232], [18580, 47], [18631, 232], [18880, 47], [18930, 232], [19180, 47], [19230, 231], [19481, 46], [19530, 230], [19781, 46], [19830, 230], [20081, 46], [20130, 229], [20381, 46], [20430, 228], [20681, 45], [20730, 227], [20981, 45], [21029, 228], [21281, 45], [21329, 227], [21582, 44], [21629, 227], [21882, 44], [21929, 227], [22182, 44], [22229, 227], [22482, 44], [22529, 227], [22782, 43], [22829, 227], [23082, 43], [23382, 43], [23683, 42], [23983, 42], [24283, 42], [24583, 42], [24628, 254], [24883, 42], [24928, 253], [25183, 41], [25228, 252], [25483, 41], [25527, 252], [25784, 40], [25827, 251], [26084, 40], [26127, 250], [26384, 40], [26427, 249], [26684, 40], [26727, 248], [26984, 40], [27027, 247], [27284, 39], [27327, 246], [27585, 38], [27627, 245], [27885, 38], [27926, 245], [28185, 38], [28226, 244], [28485, 38], [28526, 243], [28785, 38], [28826, 242], [29085, 38], [29126, 241], [29385, 37], [29426, 240], [29686, 36], [29726, 239], [29986, 36], [30025, 239], [30286, 36], [30325, 238], [30586, 36], [30625, 237], [30886, 36], [30925, 236], [31186, 36], [31225, 235], [31486, 35], [31525, 235], [31787, 34], [31825, 235], [32087, 34], [32124, 236], [32387, 34], [32424, 236], [32687, 34], [32724, 236], [32987, 34], [33024, 236], [33287, 34], [33324, 236], [33587, 33], [33624, 236], [33888, 32], [33924, 237], [34188, 32], [34224, 237], [34488, 32], [34523, 238], [34788, 32], [34823, 238], [35088, 32], [35123, 238], [35388, 32], [35423, 238], [35688, 32], [35723, 238], [35989, 30], [36023, 238], [36289, 30], [36323, 239], [36589, 30], [36622, 240], [36889, 30], [36922, 240], [37189, 30], [37222, 240], [37489, 30], [37522, 240], [37790, 29], [37822, 240], [38090, 28], [38122, 240], [38390, 28], [38422, 240], [38690, 28], [38721, 241], [38990, 28], [39021, 242], [39290, 28], [39321, 242], [39590, 28], [39621, 242], [39891, 27], [39921, 242], [40191, 26], [40221, 242], [40491, 26], [40521, 242], [40791, 26], [40821, 242], [41091, 26], [41120, 243], [41391, 26], [41420, 244], [41691, 26], [41720, 244], [41992, 25], [42020, 244], [42292, 24], [42320, 244], [42592, 24], [42620, 244], [42892, 24], [42920, 244], [43192, 24], [43219, 245], [43492, 24], [43519, 245], [43792, 24], [43819, 245], [44093, 23], [44119, 246], [44393, 22], [44419, 246], [44693, 22], [44719, 246], [44993, 22], [45019, 246], [45293, 22], [45318, 247], [45593, 22], [45618, 247], [45894, 21], [45918, 247], [46194, 21], [46218, 247], [46494, 21], [46518, 248], [46794, 20], [46818, 248], [47094, 20], [47118, 248], [47394, 20], [47418, 248], [47694, 20], [47717, 249], [47995, 19], [48017, 249], [48295, 19], [48317, 249], [48595, 19], [48617, 249], [48895, 18], [48917, 249], [49195, 18], [49217, 250], [49495, 18], [49517, 250], [49795, 18], [49816, 251], [50096, 17], [50116, 251], [50396, 17], [50416, 251], [50696, 17], [50716, 251], [50996, 16], [51016, 251], [51296, 16], [51316, 251], [51596, 16], [51616, 252], [51896, 16], [51916, 252], [52197, 15], [52215, 253], [52497, 15], [52515, 253], [52797, 15], [52815, 253], [53097, 14], [53115, 253], [53397, 14], [53415, 253], [53697, 14], [53715, 253], [53997, 14], [54015, 254], [54298, 13], [54314, 255], [54598, 13], [54614, 255], [54898, 13], [54914, 255], [55198, 12], [55214, 255], [55498, 12], [55514, 255], [55798, 12], [55814, 255], [56099, 11], [56114, 255], [56399, 11], [56413, 256], [56699, 11], [56713, 257], [56999, 11], [57013, 257], [57299, 11], [57313, 257], [57599, 10], [57613, 257], [57899, 10], [57913, 257], [58200, 9], [58213, 257], [58500, 9], [58513, 257], [58800, 9], [58812, 258], [59100, 9], [59112, 259], [59400, 9], [59412, 259], [59700, 8], [59712, 259], [60000, 8], [60012, 259], [60300, 8], [60312, 259], [60600, 8], [60612, 259], [60900, 8], [60911, 260], [61200, 8], [61211, 260], [61500, 8], [61511, 260], [61800, 7], [61811, 261], [62100, 7], [62111, 261], [62400, 7], [62411, 261], [62700, 7], [62711, 261], [63000, 7], [63010, 262], [63300, 7], [63310, 262], [63600, 7], [63610, 262], [63900, 6], [63910, 262], [64200, 6], [64210, 263], [64500, 6], [64510, 263], [64800, 6], [64810, 263], [65100, 6], [65110, 263], [65400, 6], [65409, 264], [65700, 6], [65709, 264], [66000, 5], [66009, 264], [66300, 5], [66309, 264], [66600, 5], [66609, 264], [66900, 5], [66909, 265], [67200, 5], [67209, 265], [67500, 5], [67508, 266], [67800, 5], [67808, 266], [68100, 5], [68108, 266], [68400, 4], [68408, 266], [68700, 4], [68708, 266], [69000, 4], [69008, 266], [69300, 4], [69308, 267], [69600, 4], [69607, 268], [69900, 4], [69907, 168], [70100, 75], [70200, 4], [70207, 166], [70405, 70], [70500, 3], [70507, 166], [70705, 70], [70800, 3], [70807, 166], [71005, 70], [71100, 3], [71107, 166], [71305, 70], [71400, 3], [71407, 166], [71605, 70], [71700, 3], [71707, 166], [71905, 70], [72000, 3], [72006, 167], [72205, 71], [72300, 3], [72306, 167], [72505, 71], [72600, 2], [72606, 167], [72806, 70], [72900, 2], [72906, 167], [73106, 70], [73200, 2], [73206, 167], [73406, 70], [73500, 2], [73506, 167], [73706, 70], [73800, 2], [73806, 167], [74006, 70], [74100, 2], [74105, 168], [74306, 70], [74400, 2], [74405, 168], [74606, 71], [74700, 1], [74705, 168], [74906, 71], [75000, 1], [75005, 168], [75206, 71], [75300, 1], [75305, 168], [75506, 71], [75600, 1], [75605, 168], [75806, 71], [75900, 1], [75905, 168], [76106, 71], [76200, 1], [76205, 168], [76406, 71], [76500, 1], [76504, 169], [76706, 71], [76804, 170], [77006, 72], [77104, 170], [77306, 72], [77404, 170], [77606, 72], [77704, 170], [77906, 72], [78004, 170], [78206, 72], [78304, 170], [78507, 71], [78603, 171], [78807, 71], [78903, 171], [79107, 71], [79203, 171], [79407, 73], [79503, 171], [79707, 75], [79803, 171], [80007, 78], [80103, 171], [80307, 81], [80403, 171], [80607, 84], [80702, 173], [80907, 87], [81002, 174], [81206, 91], [81302, 178], [81504, 96], [81602, 298], [81902, 298], [82202, 298]], "point": [149, 137]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan25", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.75, "y": 0.9009992, "z": 1.75}, "object_poses": [{"objectName": "Potato_a3c14b3a", "position": {"x": -2.67158651, "y": 0.8636336, "z": 2.39759159}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Potato_a3c14b3a", "position": {"x": -2.02318239, "y": 0.7362748, "z": 0.253082037}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_eb848da8", "position": {"x": -2.68782187, "y": 0.121093571, "z": 1.589964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_eb848da8", "position": {"x": -2.53052735, "y": 0.121093571, "z": 1.55042481}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_df0734fa", "position": {"x": -2.5815177, "y": 0.8247293, "z": 0.7895261}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_df0734fa", "position": {"x": -1.16156685, "y": 0.6996034, "z": 0.37735492}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_77ce7117", "position": {"x": -0.314988971, "y": 0.8385998, "z": 0.426242471}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_68224885", "position": {"x": -2.78199935, "y": 1.167996, "z": 1.957865}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_61785f6c", "position": {"x": -2.65940022, "y": 1.43537343, "z": 0.2230649}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Knife_298dd93a", "position": {"x": -0.246464968, "y": 0.8492183, "z": 1.1037097}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_298dd93a", "position": {"x": -1.542167, "y": 0.8492183, "z": 0.528825939}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_2ee69558", "position": {"x": -2.83720827, "y": 1.19324148, "z": 2.485535}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_3a4a4d0d", "position": {"x": -0.362648815, "y": 0.05428255, "z": 2.53909016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_4c742e05", "position": {"x": -2.78199983, "y": 1.14032376, "z": 2.133755}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -2.504354, "y": 0.896565557, "z": 0.7895261}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -1.538, "y": 0.7484, "z": 0.186}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_2c8fe53a", "position": {"x": -2.211642, "y": 0.8241485, "z": 0.528825939}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_3943cbe8", "position": {"x": -0.3711, "y": 0.7386, "z": 0.8791}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "Pan_e0764688", "position": {"x": -0.391666919, "y": 0.812210858, "z": 1.1083349}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_3a4a4d0d", "position": {"x": -2.49167442, "y": 0.8213809, "z": 0.0556365252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_ccf3345d", "position": {"x": -0.5186, "y": 0.8297, "z": 1.684}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_2ee69558", "position": {"x": -2.813009, "y": 0.873123646, "z": 0.953160763}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_a3c14b3a", "position": {"x": -1.15182948, "y": 0.8623831, "z": 0.352121234}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_69e12ad1", "position": {"x": -2.6484983, "y": 0.116529286, "z": 1.4713465}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_29dab58d", "position": {"x": -2.11545444, "y": 0.130002677, "z": 0.362672359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_77ce7117", "position": {"x": -0.341, "y": 0.696, "z": 1.194}, "rotation": {"x": 0.0, "y": 45.0000267, "z": 0.0}}, {"objectName": "Knife_298dd93a", "position": {"x": -2.662812, "y": 0.8191597, "z": 0.557993948}, "rotation": {"x": 89.9515457, "y": 300.034515, "z": 0.0}}, {"objectName": "Egg_68224885", "position": {"x": -1.41260135, "y": 0.7218885, "z": 0.338766426}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_61785f6c", "position": {"x": -2.6715858, "y": 1.44595587, "z": 2.48553538}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ButterKnife_d1baac51", "position": {"x": -2.04427314, "y": 0.823642, "z": 0.549209237}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b2f1312c", "position": {"x": -1.8365, "y": 0.7415, "z": 0.238453716}, "rotation": {"x": 0.0, "y": 55.91092, "z": 0.0}}, {"objectName": "PepperShaker_407d7c3b", "position": {"x": -2.84096551, "y": 0.818599939, "z": 0.4262423}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_eb848da8", "position": {"x": -2.504354, "y": 0.823164165, "z": 0.625891566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_db4f73f8", "position": {"x": -0.979999542, "y": 0.8554753, "z": 0.249999955}, "rotation": {"x": -5.270255e-06, "y": 29.9999123, "z": -4.99874841e-06}}, {"objectName": "Spoon_df0734fa", "position": {"x": -2.04427314, "y": 0.8247293, "z": 0.528825939}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_57da2f86", "position": {"x": -1.407882, "y": 0.120308191, "z": 0.440719754}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_4c742e05", "position": {"x": -2.782, "y": 0.8214564, "z": 2.2217}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 62798778, "scene_num": 25}, "task_id": "trial_T20190909_095516_427126", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A20FCMWP43CVIU_3GD6L00D3VOUEV6Z51M2WF7EG1AM1W", "high_descs": ["walk to face sink cabinets", "remove mug from cabinet under sink", "walk to face microwave", "heat and remove mug from microwave", "look up to face cabinets above microwave", "put mug inside cabinet"], "task_desc": "put mug inside kitchen cabinet", "votes": [1, 1, 1]}, {"assignment_id": "A1T643M1P572AA_36AHBNMV1U3O07BP4XU8RCVUE8GDYS", "high_descs": ["turn right and face towards the sink", "reach underneath the sink in left cabinet and take the mug out", "turn around and move forward and turn left and face the microwave", "Place the mug inside the microwave and warm it up and then take it out and shut the door", "look up above the microwave and see the cabinet", "place the mug above the microwave inside the cupboard on the right side"], "task_desc": "take the mug from underneath the sink, warm it up in the microwave, place the mug into the cupboard.", "votes": [1, 0, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3R6P78PK7N2VV5MMWH8HPG6CCZAGTE", "high_descs": ["Turn left and walk to the sink then take a small step to your left.", "Open the cabinet under the left sink and grab the mug that's down there and close the door.", "Turn right and walk to the microwave.", "Warm the mug in the microwave then take it back out and close the door.", "Look up at the cabinets.", "Open the cabinets and put the mug in there then close the door."], "task_desc": "Put a warm mug in the cabinet.", "votes": [0, 1, 1]}]}}