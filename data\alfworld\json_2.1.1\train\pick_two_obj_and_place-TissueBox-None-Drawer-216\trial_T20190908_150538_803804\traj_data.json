{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 67}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 67}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000296.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 68}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000299.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000300.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000301.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000302.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000303.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000304.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000305.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000306.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000307.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000308.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000309.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000310.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000311.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000312.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000313.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000314.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000315.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000316.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000317.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000318.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000319.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000320.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000321.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000322.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000323.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000324.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000325.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000326.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000327.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000328.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000329.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000330.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000331.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000332.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000333.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000334.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000335.png", "low_idx": 74}, {"high_idx": 6, "image_name": "000000336.png", "low_idx": 74}, {"high_idx": 6, "image_name": "000000337.png", "low_idx": 75}, {"high_idx": 6, "image_name": "000000338.png", "low_idx": 75}, {"high_idx": 6, "image_name": "000000339.png", "low_idx": 76}, {"high_idx": 6, "image_name": "000000340.png", "low_idx": 76}, {"high_idx": 6, "image_name": "000000341.png", "low_idx": 77}, {"high_idx": 6, "image_name": "000000342.png", "low_idx": 77}, {"high_idx": 6, "image_name": "000000343.png", "low_idx": 78}, {"high_idx": 6, "image_name": "000000344.png", "low_idx": 78}, {"high_idx": 6, "image_name": "000000345.png", "low_idx": 79}, {"high_idx": 6, "image_name": "000000346.png", "low_idx": 79}, {"high_idx": 6, "image_name": "000000347.png", "low_idx": 79}, {"high_idx": 6, "image_name": "000000348.png", "low_idx": 79}, {"high_idx": 6, "image_name": "000000349.png", "low_idx": 79}, {"high_idx": 6, "image_name": "000000350.png", "low_idx": 79}, {"high_idx": 6, "image_name": "000000351.png", "low_idx": 79}, {"high_idx": 6, "image_name": "000000352.png", "low_idx": 79}, {"high_idx": 6, "image_name": "000000353.png", "low_idx": 79}, {"high_idx": 6, "image_name": "000000354.png", "low_idx": 79}, {"high_idx": 6, "image_name": "000000355.png", "low_idx": 79}, {"high_idx": 6, "image_name": "000000356.png", "low_idx": 80}, {"high_idx": 6, "image_name": "000000357.png", "low_idx": 80}, {"high_idx": 6, "image_name": "000000358.png", "low_idx": 81}, {"high_idx": 6, "image_name": "000000359.png", "low_idx": 81}, {"high_idx": 6, "image_name": "000000360.png", "low_idx": 81}, {"high_idx": 6, "image_name": "000000361.png", "low_idx": 81}, {"high_idx": 6, "image_name": "000000362.png", "low_idx": 81}, {"high_idx": 6, "image_name": "000000363.png", "low_idx": 81}, {"high_idx": 6, "image_name": "000000364.png", "low_idx": 81}, {"high_idx": 6, "image_name": "000000365.png", "low_idx": 81}, {"high_idx": 6, "image_name": "000000366.png", "low_idx": 81}, {"high_idx": 6, "image_name": "000000367.png", "low_idx": 81}, {"high_idx": 6, "image_name": "000000368.png", "low_idx": 81}, {"high_idx": 6, "image_name": "000000369.png", "low_idx": 82}, {"high_idx": 6, "image_name": "000000370.png", "low_idx": 82}, {"high_idx": 6, "image_name": "000000371.png", "low_idx": 83}, {"high_idx": 6, "image_name": "000000372.png", "low_idx": 83}, {"high_idx": 6, "image_name": "000000373.png", "low_idx": 84}, {"high_idx": 6, "image_name": "000000374.png", "low_idx": 84}, {"high_idx": 6, "image_name": "000000375.png", "low_idx": 85}, {"high_idx": 6, "image_name": "000000376.png", "low_idx": 85}, {"high_idx": 6, "image_name": "000000377.png", "low_idx": 86}, {"high_idx": 6, "image_name": "000000378.png", "low_idx": 86}, {"high_idx": 6, "image_name": "000000379.png", "low_idx": 87}, {"high_idx": 6, "image_name": "000000380.png", "low_idx": 87}, {"high_idx": 6, "image_name": "000000381.png", "low_idx": 88}, {"high_idx": 6, "image_name": "000000382.png", "low_idx": 88}, {"high_idx": 6, "image_name": "000000383.png", "low_idx": 88}, {"high_idx": 6, "image_name": "000000384.png", "low_idx": 88}, {"high_idx": 6, "image_name": "000000385.png", "low_idx": 88}, {"high_idx": 6, "image_name": "000000386.png", "low_idx": 88}, {"high_idx": 6, "image_name": "000000387.png", "low_idx": 88}, {"high_idx": 6, "image_name": "000000388.png", "low_idx": 88}, {"high_idx": 6, "image_name": "000000389.png", "low_idx": 88}, {"high_idx": 6, "image_name": "000000390.png", "low_idx": 88}, {"high_idx": 6, "image_name": "000000391.png", "low_idx": 88}, {"high_idx": 6, "image_name": "000000392.png", "low_idx": 89}, {"high_idx": 6, "image_name": "000000393.png", "low_idx": 89}, {"high_idx": 6, "image_name": "000000394.png", "low_idx": 90}, {"high_idx": 6, "image_name": "000000395.png", "low_idx": 90}, {"high_idx": 6, "image_name": "000000396.png", "low_idx": 91}, {"high_idx": 6, "image_name": "000000397.png", "low_idx": 91}, {"high_idx": 7, "image_name": "000000398.png", "low_idx": 92}, {"high_idx": 7, "image_name": "000000399.png", "low_idx": 92}, {"high_idx": 7, "image_name": "000000400.png", "low_idx": 92}, {"high_idx": 7, "image_name": "000000401.png", "low_idx": 92}, {"high_idx": 7, "image_name": "000000402.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000403.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000404.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000405.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000406.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000407.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000408.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000409.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000410.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000411.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000412.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000413.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000414.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000415.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000416.png", "low_idx": 93}, {"high_idx": 7, "image_name": "000000417.png", "low_idx": 94}, {"high_idx": 7, "image_name": "000000418.png", "low_idx": 94}, {"high_idx": 7, "image_name": "000000419.png", "low_idx": 94}, {"high_idx": 7, "image_name": "000000420.png", "low_idx": 94}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "TissueBox", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|6|-3|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["tissuebox"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["TissueBox", [5.96853732, 5.96853732, -5.956, -5.956, 2.630798, 2.630798]], "coordinateReceptacleObjectId": ["SideTable", [6.328, 6.328, -5.956, -5.956, -0.004, -0.004]], "forceVisible": true, "objectId": "TissueBox|+01.49|+00.66|-01.49"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tissuebox", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["TissueBox", [5.96853732, 5.96853732, -5.956, -5.956, 2.630798, 2.630798]], "coordinateReceptacleObjectId": ["Drawer", [-4.092, -4.092, 4.13040064, 4.13040064, 2.931000232, 2.931000232]], "forceVisible": true, "objectId": "TissueBox|+01.49|+00.66|-01.49", "receptacleObjectId": "Drawer|-01.02|+00.73|+01.03"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-17|-2|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["tissuebox"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["TissueBox", [-16.66105844, -16.66105844, 1.0280025, 1.0280025, 4.26689624, 4.26689624]], "coordinateReceptacleObjectId": ["DiningTable", [-15.764, -15.764, 1.028, 1.028, 0.0, 0.0]], "forceVisible": true, "objectId": "TissueBox|-04.17|+01.07|+00.26"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-5|1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tissuebox", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["TissueBox", [-16.66105844, -16.66105844, 1.0280025, 1.0280025, 4.26689624, 4.26689624]], "coordinateReceptacleObjectId": ["Drawer", [-4.092, -4.092, 4.13040064, 4.13040064, 2.931000232, 2.931000232]], "forceVisible": true, "objectId": "TissueBox|-04.17|+01.07|+00.26", "receptacleObjectId": "Drawer|-01.02|+00.73|+01.03"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "TissueBox|+01.49|+00.66|-01.49"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [130, 88, 173, 119], "mask": [[26251, 2], [26550, 4], [26849, 6], [26859, 2], [27131, 41], [27431, 42], [27731, 42], [28031, 42], [28331, 42], [28631, 42], [28930, 43], [29230, 43], [29530, 43], [29830, 43], [30130, 43], [30430, 43], [30730, 44], [31030, 44], [31330, 44], [31630, 44], [31930, 44], [32230, 44], [32530, 44], [32830, 44], [33130, 44], [33430, 43], [33730, 43], [34031, 42], [34331, 42], [34631, 42], [34931, 42], [35231, 42], [35531, 42]], "point": [151, 102]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.02|+00.73|+01.03"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [108, 136, 299, 171], "mask": [[40608, 192], [40908, 192], [41208, 192], [41508, 191], [41808, 191], [42108, 190], [42409, 188], [42709, 188], [43009, 187], [43309, 187], [43609, 186], [43909, 186], [44209, 185], [44510, 184], [44810, 183], [45110, 182], [45410, 182], [45710, 181], [46010, 181], [46311, 179], [46611, 179], [46911, 178], [47211, 178], [47511, 177], [47811, 177], [48111, 176], [48412, 174], [48712, 174], [49012, 173], [49312, 173], [49612, 172], [49912, 172], [50213, 170], [50513, 170], [50813, 169], [51113, 168]], "point": [203, 152]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "TissueBox|+01.49|+00.66|-01.49", "placeStationary": true, "receptacleObjectId": "Drawer|-01.02|+00.73|+01.03"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [99, 136, 299, 218], "mask": [[40608, 192], [40907, 193], [41207, 193], [41507, 193], [41807, 193], [42107, 193], [42407, 193], [42706, 194], [43006, 194], [43306, 194], [43606, 194], [43906, 194], [44206, 194], [44505, 195], [44805, 195], [45105, 195], [45405, 195], [45705, 195], [46004, 196], [46304, 196], [46604, 196], [46904, 196], [47204, 196], [47504, 196], [47803, 197], [48103, 197], [48403, 197], [48703, 197], [49003, 197], [49303, 197], [49602, 198], [49902, 198], [50202, 198], [50502, 198], [50802, 198], [51102, 198], [51401, 199], [51701, 199], [52001, 199], [52301, 199], [52601, 199], [52901, 199], [53200, 200], [53500, 200], [53800, 200], [54100, 200], [54400, 200], [54699, 201], [54999, 201], [55300, 200], [55600, 200], [55900, 200], [56200, 200], [56500, 200], [56801, 199], [57101, 199], [57401, 199], [57701, 199], [58002, 198], [58302, 198], [58602, 198], [58902, 198], [59202, 198], [59503, 197], [59803, 197], [60103, 197], [60403, 197], [60704, 196], [61004, 196], [61304, 196], [61604, 196], [61904, 196], [62205, 195], [62505, 195], [62805, 195], [63105, 195], [63520, 80], [63821, 79], [64121, 79], [64421, 79], [64721, 79], [65021, 79], [65321, 79]], "point": [199, 176]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.02|+00.73|+01.03"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [99, 136, 299, 218], "mask": [[40608, 144], [40771, 29], [40907, 145], [41071, 29], [41207, 145], [41372, 28], [41507, 144], [41672, 28], [41807, 144], [41973, 27], [42107, 143], [42273, 27], [42407, 143], [42574, 26], [42706, 144], [42874, 26], [43006, 143], [43175, 25], [43306, 143], [43475, 25], [43606, 135], [43743, 6], [43776, 24], [43906, 134], [44044, 4], [44076, 24], [44206, 134], [44345, 3], [44377, 23], [44505, 134], [44646, 1], [44677, 23], [44805, 133], [44978, 22], [45105, 132], [45278, 22], [45405, 131], [45578, 22], [45705, 129], [45878, 22], [46004, 130], [46177, 23], [46304, 129], [46477, 23], [46604, 128], [46776, 24], [46904, 128], [47076, 24], [47204, 128], [47375, 25], [47504, 130], [47675, 25], [47803, 131], [47974, 26], [48103, 130], [48274, 26], [48403, 130], [48573, 27], [48703, 130], [48873, 27], [49003, 133], [49172, 28], [49303, 136], [49472, 28], [49602, 141], [49771, 29], [49902, 141], [50071, 29], [50202, 141], [50370, 30], [50502, 142], [50670, 30], [50802, 142], [50969, 31], [51102, 142], [51269, 31], [51401, 144], [51568, 32], [51701, 144], [51868, 32], [52001, 144], [52167, 33], [52301, 145], [52467, 33], [52601, 145], [52766, 34], [52901, 145], [53066, 34], [53200, 147], [53365, 35], [53500, 147], [53665, 35], [53800, 200], [54100, 200], [54400, 200], [54699, 201], [54999, 201], [55300, 200], [55600, 200], [55900, 200], [56200, 200], [56500, 200], [56801, 199], [57101, 199], [57401, 199], [57701, 199], [58002, 198], [58302, 198], [58602, 198], [58902, 198], [59202, 198], [59503, 197], [59803, 197], [60103, 197], [60403, 197], [60704, 196], [61004, 196], [61304, 196], [61604, 196], [61904, 196], [62205, 195], [62505, 195], [62805, 195], [63105, 195], [63406, 194], [63706, 194], [64006, 194], [64306, 194], [64606, 194], [64907, 193], [65207, 193]], "point": [199, 176]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "TissueBox|-04.17|+01.07|+00.26"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [160, 55, 194, 115], "mask": [[16374, 1], [16673, 3], [16971, 6], [17270, 7], [17567, 11], [17867, 12], [18168, 11], [18469, 10], [18769, 10], [19070, 10], [19371, 9], [19660, 28], [19960, 29], [20260, 29], [20560, 29], [20860, 29], [21160, 30], [21460, 30], [21760, 30], [22060, 30], [22360, 30], [22660, 31], [22960, 31], [23260, 31], [23560, 31], [23860, 31], [24160, 32], [24460, 32], [24760, 32], [25060, 32], [25360, 33], [25660, 33], [25960, 33], [26260, 33], [26560, 33], [26860, 34], [27160, 34], [27460, 34], [27760, 34], [28060, 35], [28360, 35], [28660, 34], [28960, 34], [29260, 34], [29560, 34], [29860, 34], [30161, 33], [30461, 33], [30761, 32], [31061, 32], [31361, 32], [31661, 32], [31961, 32], [32261, 32], [32561, 32], [32861, 31], [33161, 31], [33461, 31], [33761, 31], [34061, 31], [34361, 31]], "point": [177, 84]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.02|+00.73|+01.03"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [108, 136, 299, 171], "mask": [[40608, 192], [40908, 192], [41208, 192], [41508, 191], [41808, 191], [42108, 190], [42409, 188], [42709, 188], [43009, 187], [43309, 187], [43609, 186], [43909, 186], [44209, 185], [44510, 184], [44810, 183], [45110, 182], [45410, 182], [45710, 181], [46010, 181], [46311, 179], [46611, 179], [46911, 178], [47211, 178], [47511, 177], [47811, 177], [48111, 176], [48412, 174], [48712, 174], [49012, 173], [49312, 173], [49612, 172], [49912, 172], [50213, 170], [50513, 170], [50813, 169], [51113, 168]], "point": [203, 152]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "TissueBox|-04.17|+01.07|+00.26", "placeStationary": true, "receptacleObjectId": "Drawer|-01.02|+00.73|+01.03"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [99, 136, 299, 218], "mask": [[40608, 144], [40771, 29], [40907, 145], [41071, 29], [41207, 145], [41372, 28], [41507, 144], [41672, 28], [41807, 144], [41973, 27], [42107, 143], [42273, 27], [42407, 143], [42574, 26], [42706, 144], [42874, 26], [43006, 143], [43175, 25], [43306, 143], [43475, 25], [43606, 135], [43743, 6], [43776, 24], [43906, 134], [44044, 4], [44076, 24], [44206, 134], [44345, 3], [44377, 23], [44505, 134], [44646, 1], [44677, 23], [44805, 133], [44978, 22], [45105, 132], [45278, 22], [45405, 131], [45578, 22], [45705, 129], [45878, 22], [46004, 130], [46177, 23], [46304, 129], [46477, 23], [46604, 128], [46776, 24], [46904, 128], [47076, 24], [47204, 128], [47375, 25], [47504, 130], [47675, 25], [47803, 131], [47974, 26], [48103, 130], [48274, 26], [48403, 130], [48573, 27], [48703, 130], [48873, 27], [49003, 133], [49172, 28], [49303, 136], [49472, 28], [49602, 141], [49771, 29], [49902, 141], [50071, 29], [50202, 141], [50370, 30], [50502, 142], [50670, 30], [50802, 142], [50969, 31], [51102, 142], [51269, 31], [51401, 144], [51568, 32], [51701, 144], [51868, 32], [52001, 144], [52167, 33], [52301, 145], [52467, 33], [52601, 145], [52766, 34], [52901, 145], [53066, 34], [53200, 147], [53365, 35], [53500, 147], [53665, 35], [53800, 200], [54100, 200], [54400, 200], [54699, 201], [54999, 201], [55300, 200], [55600, 200], [55900, 200], [56200, 200], [56500, 200], [56801, 58], [56867, 133], [57101, 53], [57170, 130], [57401, 36], [57473, 127], [57701, 34], [57776, 124], [58002, 30], [58078, 12], [58091, 109], [58302, 28], [58380, 7], [58391, 109], [58602, 27], [58684, 2], [58691, 109], [58902, 25], [58991, 109], [59202, 24], [59291, 109], [59503, 21], [59590, 110], [59803, 20], [59890, 110], [60103, 18], [60190, 110], [60516, 84], [60816, 84], [61117, 83], [61417, 83], [61717, 83], [62017, 83], [62318, 82], [62618, 82], [62918, 82], [63218, 82], [63518, 82], [63819, 81], [64119, 81], [64419, 81], [64719, 81], [65019, 81], [65320, 80]], "point": [199, 176]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.02|+00.73|+01.03"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [99, 136, 299, 218], "mask": [[40608, 144], [40771, 29], [40907, 145], [41071, 29], [41207, 145], [41372, 28], [41507, 144], [41672, 28], [41807, 144], [41973, 27], [42107, 143], [42273, 27], [42407, 143], [42574, 26], [42706, 144], [42874, 26], [43006, 143], [43175, 25], [43306, 143], [43475, 25], [43606, 135], [43743, 6], [43776, 24], [43906, 134], [44044, 4], [44076, 24], [44206, 134], [44345, 3], [44377, 23], [44505, 134], [44646, 1], [44677, 23], [44805, 133], [44978, 22], [45105, 132], [45278, 22], [45405, 131], [45578, 22], [45705, 129], [45878, 22], [46004, 130], [46180, 20], [46304, 129], [46480, 20], [46604, 128], [46781, 19], [46904, 128], [47081, 19], [47204, 128], [47382, 18], [47504, 130], [47682, 18], [47803, 131], [47983, 17], [48103, 130], [48283, 17], [48403, 130], [48584, 16], [48703, 130], [48884, 16], [49003, 133], [49185, 15], [49303, 136], [49485, 15], [49602, 141], [49786, 14], [49902, 141], [50086, 14], [50202, 141], [50387, 13], [50502, 141], [50687, 13], [50802, 140], [50988, 12], [51102, 139], [51288, 12], [51401, 139], [51588, 12], [51701, 138], [51888, 12], [52001, 137], [52187, 13], [52301, 137], [52487, 13], [52601, 137], [52786, 14], [52901, 138], [53086, 14], [53200, 139], [53385, 15], [53500, 139], [53684, 16], [53800, 139], [53984, 16], [54100, 139], [54283, 17], [54400, 200], [54699, 201], [54999, 201], [55300, 200], [55600, 200], [55900, 200], [56200, 200], [56500, 200], [56801, 199], [57101, 199], [57401, 199], [57701, 199], [58002, 198], [58302, 198], [58602, 198], [58902, 198], [59202, 198], [59503, 197], [59803, 197], [60103, 197], [60403, 197], [60704, 196], [61004, 196], [61304, 196], [61604, 196], [61904, 196], [62205, 195], [62505, 195], [62805, 195], [63105, 195], [63406, 194], [63706, 194], [64006, 194], [64306, 194], [64606, 194], [64907, 193], [65207, 193]], "point": [199, 176]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan216", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -2.5, "y": 0.9009992, "z": 0.0}, "object_poses": [{"objectName": "TissueBox_50e690f5", "position": {"x": 1.49213433, "y": 0.6576995, "z": -1.489}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Newspaper_932fe9f5", "position": {"x": 0.09891027, "y": 0.8964504, "z": 1.18729341}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_5b8bcfa9", "position": {"x": -0.169747531, "y": 0.4831681, "z": -1.06747341}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a56cd598", "position": {"x": 0.05700004, "y": 0.551459849, "z": -1.31966329}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Box_6fa08eb0", "position": {"x": -3.78054142, "y": 1.305, "z": 0.457275361}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_462d92e0", "position": {"x": 1.11789083, "y": 0.659330845, "z": 1.26747978}, "rotation": {"x": 0.0, "y": -1.70754731e-06, "z": 0.0}}, {"objectName": "TissueBox_50e690f5", "position": {"x": -4.16526461, "y": 1.06672406, "z": 0.257000625}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Newspaper_932fe9f5", "position": {"x": -1.04760683, "y": 0.898240447, "z": 1.140554}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_5b568144", "position": {"x": 0.7474587, "y": 0.4578337, "z": 1.17132747}, "rotation": {"x": 0.0, "y": -1.70754731e-06, "z": 0.0}}, {"objectName": "Vase_1d8d3594", "position": {"x": -1.22670615, "y": 0.137048855, "z": 1.16571152}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_b9ed9b88", "position": {"x": -4.00511837, "y": 1.07091224, "z": -0.060388267}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_5b8bcfa9", "position": {"x": 1.40774417, "y": 0.6573635, "z": -1.56433856}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pillow_22286f86", "position": {"x": -1.82799935, "y": 0.585, "z": -1.25104988}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a56cd598", "position": {"x": -0.169747591, "y": 0.551768959, "z": -1.2356}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_fbe5dbee", "position": {"x": 0.434110135, "y": 0.541742444, "z": -1.1877985}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_6ea54dcc", "position": {"x": -0.5166645, "y": 0.114890076, "z": 1.20104086}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_d6a7b63c", "position": {"x": 1.82969475, "y": 0.7575242, "z": -1.41366136}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1787680082, "scene_num": 216}, "task_id": "trial_T20190908_150538_803804", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A1O3TWBUDONVLO_3IJXV6UZ10AWBHFPIG3PADIA3CWRI1", "high_descs": ["Turn right and walk across the room the turn right and walk forward to face the end table.", "Pick up the tissue box on the end table.", "Turn around and walk forward to face the left end of the dresser.", "Place the tissue box in the left side of the dresser.", "Turn left and walk around to face the left side of the kitchen table.", "Pick up the tissue box on the kitchen table.", "Turn right and walk forward and then turn left to face the dresser.", "Place the tissue box in the drawer on the left side of the dresser."], "task_desc": "To put two boxes of tissue into the drawer on the left side of the dresser.", "votes": [1, 1, 1]}, {"assignment_id": "A3PPLDHC3CG0YN_3RRCEFRB7P3FN3ZL1UFVGX3NG7EB4D", "high_descs": ["Turn right, walk to the end table to the left of the couch on the right. ", "Pick up the tissue box in front of the remote. ", "Turn around and walk to the shelves on the right. ", "Open the top drawer on the left, place the tissues inside, and close the drawer. ", "Turn left, turn left at the table, turn right, turn right again to face the table. ", "Pick up the tissue box to the left of the box. ", "Turn around and walk back to the shelves on the left. ", "Open the drawer on the left, place the tissues in front of the first tissue box, and close the drawer. "], "task_desc": "To move two boxes of tissues to a drawer. ", "votes": [1, 1, 1]}, {"assignment_id": "A2A028LRDJB7ZB_3FIJLY1B6XVRPDXK4GTF2L53M3BFP3", "high_descs": ["Turn to your right then head straight , turn right on the side table on the couch", "Pick up the box of tissue on the side table", "Turn left and head the drawer on your right", "Open the drawer on your left put the tissue box inside the drawer,close the drawer", "Turn left then head to the table", "Pick up the tissue box on the table", "Turn right walk back to the drawer", "Open the drawer put in the tissue box in the drawer then close it "], "task_desc": "Put the two tissue box in the drawer", "votes": [1, 0, 1]}]}}