{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 46}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-6|-2|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-5.6748538, -5.6748538, -5.4422388, -5.4422388, 3.401718616, 3.401718616]], "coordinateReceptacleObjectId": ["SinkBasin", [-5.3792, -5.3792, -5.0, -5.0, 3.356, 3.356]], "forceVisible": true, "objectId": "Cup|-01.42|+00.85|-01.36"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|-2|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-8|4|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-5.6748538, -5.6748538, -5.4422388, -5.4422388, 3.401718616, 3.401718616]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-11.424, -11.424, 3.408, 3.408, 0.0, 0.0]], "forceVisible": true, "objectId": "Cup|-01.42|+00.85|-01.36", "receptacleObjectId": "<PERSON><PERSON>|-02.86|+00.00|+00.85"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.42|+00.85|-01.36"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [118, 91, 141, 135], "mask": [[27129, 2], [27424, 11], [27721, 16], [28020, 19], [28319, 20], [28619, 21], [28918, 22], [29218, 22], [29518, 22], [29818, 23], [30118, 23], [30418, 23], [30718, 22], [31019, 21], [31319, 21], [31619, 21], [31919, 21], [32220, 19], [32520, 19], [32820, 19], [33121, 17], [33422, 16], [33723, 14], [34024, 12], [34325, 10], [34627, 6], [34929, 2], [35229, 2], [35530, 2], [35830, 2], [36130, 2], [36427, 9], [36725, 13], [37023, 17], [37322, 19], [37622, 19], [37921, 20], [38221, 21], [38521, 20], [38821, 20], [39121, 20], [39422, 18], [39723, 16], [40025, 11], [40329, 4]], "point": [129, 112]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.42|+00.85|-01.36", "placeStationary": true, "receptacleObjectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 238], [20108, 239], [20407, 240], [20707, 239], [21006, 240], [21305, 241], [21604, 241], [21903, 242], [22203, 242], [22502, 243], [22801, 243], [23100, 244], [23400, 244], [23700, 243], [24000, 243], [24300, 243], [24600, 117], [24722, 121], [24900, 113], [25025, 117], [25200, 111], [25326, 116], [25500, 110], [25627, 115], [25800, 110], [25928, 113], [26100, 109], [26229, 112], [26400, 108], [26530, 111], [26700, 108], [26830, 111], [27000, 108], [27130, 110], [27300, 108], [27431, 109], [27600, 108], [27731, 109], [27900, 107], [28031, 109], [28200, 108], [28331, 108], [28500, 108], [28631, 108], [28800, 108], [28930, 109], [29100, 108], [29230, 108], [29400, 109], [29530, 108], [29700, 109], [29829, 109], [30000, 110], [30129, 109], [30300, 110], [30428, 109], [30600, 111], [30728, 109], [30900, 112], [31027, 110], [31200, 113], [31326, 110], [31500, 114], [31625, 111], [31800, 116], [31923, 113], [32100, 236], [32400, 235], [32700, 235], [33000, 235], [33300, 234], [33600, 234], [33900, 234], [34200, 234], [34500, 233], [34800, 233], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 133], [19963, 84], [20108, 133], [20264, 83], [20407, 133], [20565, 82], [20707, 133], [20865, 81], [21006, 133], [21165, 81], [21305, 134], [21466, 80], [21604, 135], [21766, 79], [21903, 136], [22066, 79], [22203, 136], [22366, 79], [22502, 137], [22666, 79], [22801, 138], [22966, 78], [23100, 139], [23266, 78], [23400, 139], [23565, 79], [23700, 140], [23865, 78], [24000, 140], [24165, 78], [24300, 140], [24465, 78], [24600, 117], [24722, 18], [24764, 79], [24900, 113], [25025, 16], [25064, 78], [25200, 111], [25326, 15], [25364, 78], [25500, 110], [25627, 14], [25663, 79], [25800, 110], [25928, 14], [25963, 78], [26100, 109], [26229, 13], [26262, 79], [26400, 108], [26530, 13], [26562, 79], [26700, 108], [26830, 13], [26861, 80], [27000, 108], [27130, 14], [27160, 80], [27300, 108], [27431, 14], [27459, 81], [27600, 108], [27731, 16], [27758, 82], [27900, 107], [28031, 18], [28056, 84], [28200, 108], [28331, 20], [28353, 86], [28500, 108], [28631, 20], [28653, 86], [28800, 108], [28930, 21], [28953, 86], [29100, 108], [29230, 21], [29253, 85], [29400, 109], [29530, 21], [29553, 85], [29700, 109], [29829, 22], [29853, 85], [30000, 110], [30129, 22], [30153, 85], [30300, 110], [30428, 23], [30453, 84], [30600, 111], [30728, 21], [30756, 81], [30900, 112], [31027, 18], [31059, 78], [31200, 113], [31326, 17], [31361, 75], [31500, 114], [31625, 17], [31662, 74], [31800, 116], [31923, 18], [31963, 73], [32100, 141], [32264, 72], [32400, 140], [32564, 71], [32700, 140], [32865, 70], [33000, 140], [33164, 71], [33300, 140], [33464, 70], [33600, 141], [33764, 70], [33900, 142], [34063, 71], [34200, 143], [34362, 72], [34500, 145], [34660, 73], [34800, 147], [34957, 76], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.42|+00.85|-01.36"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [139, 67, 165, 117], "mask": [[19942, 21], [20241, 23], [20540, 25], [20840, 25], [21139, 26], [21439, 27], [21739, 27], [22039, 27], [22339, 27], [22639, 27], [22939, 27], [23239, 27], [23539, 26], [23840, 25], [24140, 25], [24440, 25], [24740, 24], [25041, 23], [25341, 23], [25641, 22], [25942, 21], [26242, 20], [26543, 19], [26843, 18], [27144, 16], [27445, 14], [27747, 11], [28049, 7], [28351, 2], [28651, 2], [28951, 2], [29251, 2], [29551, 2], [29851, 2], [30151, 2], [30451, 2], [30749, 7], [31045, 14], [31343, 18], [31642, 20], [31941, 22], [32241, 23], [32540, 24], [32840, 25], [33140, 24], [33440, 24], [33741, 23], [34042, 21], [34343, 19], [34645, 15], [34947, 10]], "point": [152, 91]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 238], [20108, 239], [20407, 240], [20707, 239], [21006, 240], [21305, 241], [21604, 241], [21903, 242], [22203, 242], [22502, 243], [22801, 243], [23100, 244], [23400, 244], [23700, 243], [24000, 243], [24300, 243], [24600, 117], [24722, 121], [24900, 113], [25025, 117], [25200, 111], [25326, 116], [25500, 110], [25627, 115], [25800, 110], [25928, 113], [26100, 109], [26229, 112], [26400, 108], [26530, 111], [26700, 108], [26830, 111], [27000, 108], [27130, 110], [27300, 108], [27431, 109], [27600, 108], [27731, 109], [27900, 107], [28031, 109], [28200, 108], [28331, 108], [28500, 108], [28631, 108], [28800, 108], [28930, 109], [29100, 108], [29230, 108], [29400, 109], [29530, 108], [29700, 109], [29829, 109], [30000, 110], [30129, 109], [30300, 110], [30428, 109], [30600, 111], [30728, 109], [30900, 112], [31027, 110], [31200, 113], [31326, 110], [31500, 114], [31625, 111], [31800, 116], [31923, 113], [32100, 236], [32400, 235], [32700, 235], [33000, 235], [33300, 234], [33600, 234], [33900, 234], [34200, 234], [34500, 233], [34800, 233], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.86|+00.00|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 218], "mask": [[0, 2099], [2100, 299], [2400, 298], [2700, 296], [3000, 297], [3300, 297], [3600, 296], [3900, 296], [4200, 295], [4500, 295], [4800, 294], [5100, 294], [5400, 293], [5700, 293], [6000, 292], [6300, 292], [6600, 291], [6900, 291], [7200, 290], [7500, 290], [7800, 289], [8100, 288], [8400, 288], [8700, 287], [9000, 287], [9300, 286], [9600, 286], [9900, 285], [10200, 285], [10500, 284], [10800, 284], [11100, 283], [11400, 283], [11700, 282], [12000, 282], [12300, 281], [12600, 281], [12900, 280], [13200, 280], [13500, 279], [13800, 279], [14100, 278], [14400, 278], [14700, 277], [15000, 277], [15300, 276], [15600, 276], [15900, 275], [16200, 275], [16500, 274], [16800, 274], [17100, 273], [17400, 273], [17700, 272], [18000, 272], [18300, 271], [18600, 271], [18900, 270], [19200, 270], [19500, 269], [19800, 269], [20100, 268], [20400, 268], [20700, 267], [21000, 267], [21300, 266], [21600, 266], [21900, 265], [22200, 265], [22500, 264], [22800, 264], [23100, 263], [23400, 263], [23700, 262], [24000, 262], [24300, 261], [24600, 261], [24900, 260], [25200, 260], [25500, 259], [25800, 259], [26100, 258], [26400, 257], [26700, 257], [27000, 256], [27300, 256], [27600, 255], [27900, 255], [28200, 254], [28500, 254], [28800, 253], [29100, 253], [29400, 252], [29700, 252], [30000, 251], [30300, 251], [30600, 250], [30900, 250], [31200, 249], [31500, 249], [31800, 248], [32100, 248], [32400, 247], [32700, 247], [33000, 246], [33300, 246], [33600, 245], [33900, 245], [34200, 244], [34500, 244], [34800, 243], [35100, 243], [35400, 242], [35700, 242], [36000, 241], [36300, 241], [36600, 240], [36900, 240], [37200, 239], [37500, 239], [37800, 238], [38100, 238], [38400, 237], [38700, 237], [39000, 236], [39300, 236], [39600, 235], [39900, 235], [40200, 234], [40500, 234], [40800, 233], [41100, 233], [41400, 232], [41700, 232], [42000, 231], [42300, 231], [42600, 230], [42900, 230], [43200, 229], [43500, 229], [43800, 228], [44100, 228], [44400, 227], [44700, 226], [45000, 226], [45300, 225], [45600, 225], [45900, 224], [46200, 224], [46500, 223], [46800, 223], [47100, 222], [47400, 222], [47700, 221], [48000, 221], [48300, 220], [48600, 220], [48900, 219], [49200, 219], [49500, 148], [49652, 66], [49800, 142], [49958, 60], [50100, 136], [50264, 53], [50401, 133], [50566, 51], [50702, 130], [50868, 48], [51003, 126], [51171, 45], [51305, 122], [51473, 42], [51606, 119], [51775, 40], [51907, 116], [52077, 37], [52208, 114], [52378, 36], [52509, 112], [52679, 34], [52810, 110], [52980, 33], [53111, 108], [53281, 31], [53413, 106], [53581, 31], [53714, 104], [53882, 29], [54015, 102], [54183, 28], [54316, 100], [54484, 26], [54617, 98], [54785, 25], [54918, 96], [55086, 23], [55219, 94], [55387, 22], [55520, 93], [55687, 21], [55822, 91], [55987, 21], [56123, 89], [56288, 19], [56424, 88], [56588, 19], [56725, 87], [56888, 18], [57026, 85], [57189, 17], [57327, 84], [57489, 16], [57628, 82], [57790, 15], [57929, 81], [58090, 14], [58231, 79], [58390, 14], [58532, 77], [58691, 12], [58833, 76], [58991, 12], [59136, 73], [59291, 11], [59437, 72], [59591, 11], [59738, 71], [59891, 10], [60039, 70], [60191, 10], [60340, 69], [60491, 9], [60641, 68], [60791, 9], [60942, 67], [61091, 8], [61244, 65], [61391, 8], [61545, 64], [61691, 7], [61846, 63], [61991, 7], [62147, 62], [62291, 6], [62448, 61], [62591, 6], [62749, 60], [62891, 5], [63050, 59], [63191, 4], [63351, 58], [63491, 4], [63652, 57], [63791, 3], [63954, 55], [64091, 3], [64255, 55], [64390, 3], [64556, 54], [64690, 3], [64857, 53], [64990, 2], [65158, 52], [65290, 2]], "point": [149, 108]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.42|+00.85|-01.36", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.86|+00.00|+00.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 12650], [12784, 3], [12788, 162], [13084, 3], [13088, 8343], [21434, 295], [21736, 292], [22037, 290], [22338, 289], [22638, 288], [22939, 287], [23239, 287], [23539, 287], [23839, 287], [24140, 286], [24439, 287], [24739, 288], [25039, 288], [25339, 289], [25638, 291], [25937, 293], [26236, 10364], [36601, 299], [36902, 298], [37202, 298], [37503, 297], [37804, 296], [38105, 295], [38406, 294], [38707, 72], [38811, 32], [38862, 2], [38866, 134], [39007, 72], [39111, 32], [39166, 134], [39308, 71], [39410, 33], [39465, 135], [39609, 70], [39710, 33], [39764, 136], [39910, 69], [40009, 34], [40062, 138], [40211, 69], [40309, 34], [40361, 139], [40512, 68], [40608, 36], [40661, 139], [40813, 69], [40906, 38], [40961, 139], [41113, 71], [41204, 40], [41261, 139], [41414, 131], [41560, 140], [41715, 130], [41859, 141], [42016, 131], [42158, 142], [42317, 131], [42457, 143], [42618, 282], [42918, 282], [43219, 281], [43520, 280], [43821, 279], [44122, 278], [44423, 277], [44723, 277], [45024, 276], [45325, 275], [45626, 274], [45927, 273], [46228, 272], [46529, 271], [46829, 271], [47130, 270], [47431, 269], [47732, 268], [48033, 267], [48334, 266], [48634, 266], [48935, 265], [49236, 264], [49537, 111], [49652, 148], [49838, 104], [49958, 142], [50139, 97], [50264, 136], [50439, 95], [50566, 134], [50740, 92], [50868, 132], [51041, 88], [51171, 129], [51342, 85], [51473, 127], [51643, 82], [51775, 125], [51944, 79], [52077, 123], [52244, 78], [52378, 122], [52545, 76], [52679, 121], [52846, 74], [52980, 120], [53147, 72], [53281, 119], [53448, 71], [53581, 119], [53749, 69], [53882, 118], [54050, 67], [54183, 117], [54350, 66], [54484, 116], [54651, 64], [54785, 115], [54952, 62], [55086, 114], [55253, 60], [55387, 113], [55554, 59], [55687, 113], [55855, 58], [55987, 113], [56155, 57], [56288, 112], [56456, 56], [56588, 112], [56757, 55], [56888, 112], [57058, 53], [57189, 111], [57359, 52], [57489, 111], [57660, 50], [57790, 110], [57960, 50], [58090, 110], [58262, 48], [58390, 110], [58566, 43], [58691, 109], [58991, 109], [59291, 109], [59591, 109], [59891, 109], [60191, 109], [60491, 109], [60791, 109], [61091, 109], [61391, 109], [61691, 109], [61991, 109], [62291, 109], [62591, 109], [62891, 109], [63191, 109], [63491, 109], [63791, 109], [64091, 109], [64391, 109], [64692, 108], [64992, 108], [65292, 108], [65592, 108], [65892, 108], [66192, 108], [66492, 108], [66792, 108], [67092, 108], [67392, 108], [67692, 108], [67992, 108], [68292, 108], [68593, 107], [68893, 107], [69193, 107], [69493, 107], [69793, 107], [70093, 107], [70393, 107], [70693, 107], [70993, 107], [71293, 107], [71593, 107], [71893, 107], [72194, 106], [72494, 106], [72794, 106], [73094, 106], [73394, 106], [73694, 106], [73994, 106], [74294, 106], [74594, 106], [74894, 106], [75194, 106], [75494, 106], [75794, 106], [76095, 105], [76395, 105], [76695, 105], [76995, 105], [77295, 105], [77595, 105], [77895, 105], [78195, 105], [78495, 105], [78795, 105], [79095, 105], [79395, 105], [79696, 104], [79996, 104], [80296, 104], [80596, 104], [80896, 104], [81196, 104], [81496, 104], [81796, 104], [82096, 104], [82396, 104], [82696, 104], [82996, 104], [83297, 103], [83597, 103], [83897, 103], [84197, 103], [84497, 103], [84797, 103], [85097, 103], [85397, 103], [85697, 103], [85997, 103], [86297, 103], [86597, 103], [86897, 103], [87198, 102], [87498, 102], [87798, 102], [88098, 102], [88398, 102], [88698, 102], [88998, 102], [89298, 102], [89598, 102], [89898, 102]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.86|+00.00|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 12650], [12784, 3], [12788, 162], [13084, 3], [13088, 6863], [19958, 290], [20261, 284], [20563, 281], [20864, 279], [21165, 266], [21434, 8], [21466, 263], [21736, 6], [21766, 262], [22037, 5], [22067, 260], [22338, 3], [22367, 260], [22638, 3], [22667, 259], [22939, 2], [22968, 258], [23239, 2], [23268, 258], [23539, 2], [23568, 258], [23839, 1], [23868, 258], [24140, 1], [24168, 258], [24439, 2], [24467, 259], [24739, 2], [24767, 260], [25039, 2], [25067, 260], [25339, 2], [25367, 261], [25638, 4], [25667, 262], [25937, 5], [25966, 264], [26236, 6], [26266, 276], [26566, 277], [26865, 278], [27165, 279], [27464, 281], [27763, 283], [28062, 285], [28361, 287], [28660, 289], [28959, 294], [29255, 294], [29558, 289], [29860, 285], [30162, 282], [30463, 280], [30764, 278], [31065, 277], [31365, 277], [31665, 277], [31966, 275], [32266, 276], [32566, 276], [32865, 277], [33165, 278], [33465, 279], [33764, 280], [34063, 282], [34362, 285], [34660, 290], [34958, 1642], [36601, 299], [36902, 298], [37202, 298], [37503, 297], [37804, 296], [38105, 295], [38406, 294], [38707, 72], [38811, 32], [38862, 2], [38866, 134], [39007, 72], [39111, 32], [39166, 134], [39308, 71], [39410, 33], [39465, 135], [39609, 70], [39710, 33], [39764, 136], [39910, 69], [40009, 34], [40062, 138], [40211, 69], [40309, 34], [40361, 139], [40512, 68], [40608, 36], [40661, 139], [40813, 69], [40906, 38], [40961, 139], [41113, 71], [41204, 40], [41261, 139], [41414, 131], [41560, 140], [41715, 130], [41859, 141], [42016, 131], [42158, 142], [42317, 131], [42457, 143], [42618, 282], [42918, 282], [43219, 281], [43520, 280], [43821, 279], [44122, 278], [44423, 277], [44723, 277], [45024, 276], [45325, 275], [45626, 274], [45927, 273], [46228, 272], [46529, 271], [46829, 271], [47130, 270], [47431, 269], [47732, 268], [48033, 267], [48334, 266], [48634, 266], [48935, 265], [49236, 264], [49537, 263], [49838, 262], [50139, 261], [50439, 261], [50740, 260], [51041, 259], [51342, 258], [51643, 257], [51944, 256], [52244, 256], [52545, 255], [52846, 254], [53147, 253], [53448, 252], [53749, 251], [54050, 250], [54350, 250], [54651, 249], [54952, 248], [55253, 247], [55554, 246], [55855, 245], [56155, 245], [56456, 244], [56757, 243], [57058, 242], [57359, 241], [57660, 240], [57960, 240], [58262, 238], [58566, 120], [58690, 110], [58990, 110], [59290, 110], [59590, 110], [59890, 110], [60190, 110], [60490, 110], [60790, 110], [61091, 109], [61391, 109], [61691, 109], [61991, 109], [62291, 109], [62591, 109], [62891, 109], [63191, 109], [63491, 109], [63791, 109], [64091, 109], [64391, 109], [64692, 108], [64992, 108], [65292, 108], [65592, 108], [65892, 108], [66192, 108], [66492, 108], [66792, 108], [67092, 108], [67392, 108], [67692, 108], [67992, 108], [68292, 108], [68593, 107], [68893, 107], [69193, 107], [69493, 107], [69793, 107], [70093, 107], [70393, 107], [70693, 107], [70993, 107], [71293, 107], [71593, 107], [71893, 107], [72194, 106], [72494, 106], [72794, 106], [73094, 106], [73394, 106], [73694, 106], [73994, 106], [74294, 106], [74594, 106], [74894, 106], [75194, 106], [75494, 106], [75794, 106], [76095, 105], [76395, 105], [76695, 105], [76995, 105], [77295, 105], [77595, 105], [77895, 105], [78195, 105], [78495, 105], [78795, 105], [79095, 105], [79395, 105], [79696, 104], [79996, 104], [80296, 104], [80596, 104], [80896, 104], [81196, 104], [81496, 104], [81796, 104], [82096, 104], [82396, 104], [82696, 104], [82996, 104], [83297, 103], [83597, 103], [83897, 103], [84197, 103], [84497, 103], [84797, 103], [85097, 103], [85397, 103], [85697, 103], [85997, 103], [86297, 103], [86597, 103], [86897, 103], [87198, 102], [87498, 102], [87798, 102], [88098, 102], [88398, 102], [88698, 102], [88998, 102], [89298, 102], [89598, 102], [89898, 102]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan22", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -2.0, "y": 0.9009992, "z": 0.0}, "object_poses": [{"objectName": "CreditCard_35b71b93", "position": {"x": 0.3594984, "y": 0.7431961, "z": -1.13832915}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_35b71b93", "position": {"x": 0.09704453, "y": 0.744511, "z": -1.05214345}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_dc49bd4c", "position": {"x": -1.33236611, "y": 0.8495982, "z": -1.25}, "rotation": {"x": 1.40334183e-14, "y": 3.208973e-43, "z": 2.89269767e-27}}, {"objectName": "Spoon_4b9198b3", "position": {"x": -1.15967155, "y": 0.850685537, "z": -1.25}, "rotation": {"x": -1.40334183e-14, "y": 180.0, "z": 1.44634883e-27}}, {"objectName": "Fork_b9d165af", "position": {"x": -0.5563109, "y": 0.911848545, "z": -1.199}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spatula_3b50a8bf", "position": {"x": -2.62819934, "y": 0.7615887, "z": 0.226726711}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_3b50a8bf", "position": {"x": -1.0733242, "y": 0.864556, "z": -1.19472015}, "rotation": {"x": 0.0, "y": 270.0, "z": -1.40334183e-14}}, {"objectName": "Cup_46773746", "position": {"x": -1.591408, "y": 0.8504164, "z": -1.19472015}, "rotation": {"x": 1.40334183e-14, "y": 3.208973e-43, "z": 2.89269767e-27}}, {"objectName": "Plate_ad96aa4f", "position": {"x": 0.4135064, "y": 1.113564, "z": 0.9988123}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Egg_e02c5e35", "position": {"x": -2.7403214, "y": 0.854985058, "z": 0.9327627}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Egg_e02c5e35", "position": {"x": -2.438055, "y": 0.9464633, "z": 0.359614462}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_b7a8ccec", "position": {"x": -2.810885, "y": 1.79409635, "z": -0.376690328}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_b7a8ccec", "position": {"x": -0.0496140122, "y": 1.66150928, "z": -1.36000526}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e2bf19ed", "position": {"x": -2.8607583, "y": 1.7524637, "z": 0.690474868}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_e2bf19ed", "position": {"x": 0.548222065, "y": 1.19090188, "z": 1.47979164}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "SaltShaker_4f98d325", "position": {"x": -2.81177831, "y": 1.66157842, "z": -1.05923724}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f88244ed", "position": {"x": -2.84395027, "y": 0.906299949, "z": -1.11555123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f88244ed", "position": {"x": -2.68010283, "y": 1.46886182, "z": 0.932762861}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_a99f93a6", "position": {"x": 0.398949146, "y": 1.05720615, "z": -1.31486058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a99f93a6", "position": {"x": -2.75755763, "y": 0.089363575, "z": 1.46684813}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_304c8d90", "position": {"x": -2.59, "y": 0.9, "z": -1.147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a99f93a6", "position": {"x": -1.50506079, "y": 0.9035056, "z": -1.08416045}, "rotation": {"x": 1.40334183e-14, "y": 3.208973e-43, "z": 2.89269767e-27}}, {"objectName": "Fork_b9d165af", "position": {"x": -2.08521843, "y": 0.9118485, "z": -1.11555123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_1cf3b23c", "position": {"x": -2.81148577, "y": 2.044735, "z": 1.370662}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_35b71b93", "position": {"x": 0.156094015, "y": 1.1086961, "z": 1.715143}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Bread_81a93477", "position": {"x": 0.842876, "y": 0.965819955, "z": -1.11555123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ad96aa4f", "position": {"x": -2.8607583, "y": 0.380319834, "z": 0.690474033}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_f346af22", "position": {"x": -0.748, "y": 0.8999999, "z": -1.291}, "rotation": {"x": 0.0, "y": 315.000031, "z": 0.0}}, {"objectName": "Spatula_3b50a8bf", "position": {"x": -2.60430479, "y": 0.92629987, "z": 0.275109649}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Lettuce_e2bf19ed", "position": {"x": -2.68010283, "y": 1.7524637, "z": 1.01352537}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_4f98d325", "position": {"x": -1.45504689, "y": 0.1135959, "z": -1.06876659}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9ffe4e75", "position": {"x": 0.203787982, "y": 1.15205479, "z": 1.05698335}, "rotation": {"x": 359.98233, "y": 0.00772271631, "z": 0.2332306}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": -2.830618, "y": 0.9, "z": -0.112962961}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_dc49bd4c", "position": {"x": -0.158721983, "y": 1.11174214, "z": 1.79627371}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Pot_86f597c8", "position": {"x": -2.823, "y": 0.9287, "z": -0.3964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "DishSponge_9171dd8d", "position": {"x": -1.221, "y": 0.8416841, "z": -1.1264}, "rotation": {"x": 0.0, "y": 41.1644974, "z": 0.0}}, {"objectName": "Egg_e02c5e35", "position": {"x": -0.662034154, "y": 0.9464633, "z": -1.03210282}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_4b9198b3", "position": {"x": 0.8175242, "y": 1.11282945, "z": 1.05656481}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Knife_1fa5d324", "position": {"x": 0.0605277, "y": 1.1069684, "z": 1.58733857}, "rotation": {"x": 270.0, "y": 209.997864, "z": 0.0}}, {"objectName": "Cup_46773746", "position": {"x": -1.41871345, "y": 0.850429654, "z": -1.3605597}, "rotation": {"x": 1.40334183e-14, "y": 3.208973e-43, "z": 2.89269767e-27}}, {"objectName": "Mug_f88244ed", "position": {"x": -2.80054045, "y": 0.373455942, "z": 1.01352572}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_b7a8ccec", "position": {"x": -2.68010163, "y": 1.47302485, "z": 0.690475464}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 4082026549, "scene_num": 22}, "task_id": "trial_T20190908_072702_772430", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3R19ZA45J8915_3F6HPJW4JGHPN3WBWZ3M9AZ8CMI2WT", "high_descs": ["Turn around and walk up to the kitchen sink. ", "Pick up the glass that is to the left of the other glass in the sink. ", "Turn to the left, walk a few steps, then turn right to face the microwave.", "Heat the cup in the microwave before taking it back out. ", "Turn around, walk a few steps, then turn left and head towards the fridge. ", "Place the cup in the fridge, to the right of the egg, and close the door."], "task_desc": "Place a heated cup inside the refrigerator.", "votes": [1, 1]}, {"assignment_id": "A3A0VPWPASCO9J_3M81GAB8A30ERJ0L3JXRIEKQIPTQBQ", "high_descs": ["Turn and go to the sink", "Pick up the cup near the center of the sink.", "Turn and take the cup to the microwave.", "Heat the cup in the microwave.", "Take the hot cup to the fridge.", "Put the hot cup in the fridge."], "task_desc": "Put a hot cup in the fridge.", "votes": [1, 1]}, {"assignment_id": "AKW57KYG90X61_3RRCEFRB7PT72EDN5MQEFBJLMYOB48", "high_descs": ["turn around and head to the sink", "pick up the cup", "turn left and head to the oven", "heat the cup in the oven", "turn around and head to the fridge", "place the cup in the fridge"], "task_desc": "place a hot cup in the fridge", "votes": [1, 1]}]}}