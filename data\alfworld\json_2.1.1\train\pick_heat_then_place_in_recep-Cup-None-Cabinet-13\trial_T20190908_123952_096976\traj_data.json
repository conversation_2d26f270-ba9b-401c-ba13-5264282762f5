{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000298.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000299.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000317.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000318.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000319.png", "low_idx": 54}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-15|14|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-17.912208, -17.912208, 14.19520092, 14.19520092, 3.689437868, 3.689437868]], "coordinateReceptacleObjectId": ["CounterTop", [-17.04, -17.04, 19.7744, 19.7744, 3.836, 3.836]], "forceVisible": true, "objectId": "Cup|-04.48|+00.92|+03.55"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-11|10|2|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-12|10|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-17.912208, -17.912208, 14.19520092, 14.19520092, 3.689437868, 3.689437868]], "coordinateReceptacleObjectId": ["Cabinet", [-15.84405708, -15.84405708, 8.47927284, 8.47927284, 1.555381416, 1.555381416]], "forceVisible": true, "objectId": "Cup|-04.48|+00.92|+03.55", "receptacleObjectId": "Cabinet|-03.96|+00.39|+02.12"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-04.48|+00.92|+03.55"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [146, 103, 181, 143], "mask": [[30757, 13], [31054, 19], [31352, 24], [31650, 27], [31949, 29], [32248, 32], [32547, 33], [32847, 34], [33147, 34], [33446, 36], [33746, 36], [34046, 36], [34347, 35], [34647, 35], [34947, 34], [35247, 34], [35547, 34], [35847, 34], [36148, 32], [36448, 32], [36748, 32], [37048, 31], [37349, 30], [37649, 30], [37950, 28], [38251, 26], [38551, 26], [38851, 25], [39151, 25], [39451, 25], [39752, 23], [40052, 23], [40352, 23], [40652, 22], [40952, 22], [41252, 22], [41553, 21], [41854, 19], [42155, 17], [42456, 14], [42758, 10]], "point": [163, 122]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-04.48|+00.92|+03.55", "placeStationary": true, "receptacleObjectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 66], [17490, 173], [17700, 66], [17795, 40], [17860, 103], [18000, 66], [18096, 38], [18161, 102], [18300, 66], [18399, 35], [18461, 102], [18600, 66], [18700, 34], [18761, 102], [18900, 66], [18995, 2], [19001, 33], [19061, 102], [19200, 66], [19295, 3], [19301, 33], [19361, 102], [19500, 66], [19595, 3], [19601, 33], [19661, 101], [19800, 67], [19895, 4], [19901, 33], [19961, 101], [20100, 67], [20195, 4], [20201, 33], [20260, 102], [20400, 67], [20495, 4], [20501, 33], [20560, 102], [20700, 67], [20795, 4], [20801, 33], [20860, 102], [21000, 67], [21095, 4], [21101, 33], [21160, 102], [21300, 67], [21395, 4], [21401, 33], [21460, 102], [21600, 67], [21695, 4], [21701, 33], [21760, 102], [21900, 67], [21995, 4], [22001, 33], [22060, 102], [22200, 68], [22295, 3], [22301, 33], [22360, 101], [22500, 68], [22595, 3], [22601, 33], [22660, 101], [22800, 68], [22895, 3], [22900, 35], [22960, 101], [23100, 68], [23195, 3], [23200, 35], [23260, 101], [23400, 68], [23495, 2], [23500, 35], [23560, 101], [23700, 68], [23795, 2], [23799, 36], [23860, 101], [24000, 68], [24095, 2], [24099, 36], [24160, 101], [24300, 68], [24395, 1], [24399, 36], [24460, 101], [24600, 69], [24698, 37], [24760, 101], [24900, 69], [24998, 37], [25060, 101], [25200, 69], [25297, 38], [25360, 100], [25500, 69], [25596, 39], [25659, 101], [25800, 69], [25895, 40], [25959, 101], [26100, 69], [26195, 40], [26259, 101], [26400, 69], [26495, 40], [26559, 101], [26700, 69], [26795, 40], [26859, 101], [27000, 70], [27095, 40], [27159, 101], [27300, 70], [27395, 40], [27459, 101], [27600, 70], [27695, 45], [27755, 105], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [133, 65]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 105], [14206, 158], [14400, 89], [14524, 140], [14700, 86], [14829, 135], [15000, 86], [15131, 133], [15300, 86], [15431, 133], [15600, 87], [15731, 133], [15900, 87], [16031, 133], [16200, 87], [16331, 133], [16500, 87], [16631, 133], [16800, 87], [16931, 132], [17100, 87], [17230, 133], [17400, 66], [17530, 133], [17700, 66], [17830, 5], [17860, 103], [18000, 66], [18130, 4], [18161, 102], [18300, 66], [18430, 4], [18461, 102], [18600, 66], [18730, 4], [18761, 102], [18900, 66], [19030, 4], [19061, 102], [19200, 66], [19330, 4], [19361, 102], [19500, 66], [19630, 4], [19661, 101], [19800, 67], [19929, 5], [19961, 101], [20100, 67], [20228, 6], [20260, 102], [20400, 67], [20528, 6], [20560, 102], [20700, 67], [20827, 7], [20860, 102], [21000, 67], [21127, 7], [21160, 102], [21300, 67], [21427, 7], [21460, 102], [21600, 67], [21726, 8], [21760, 102], [21900, 67], [22026, 8], [22060, 102], [22200, 68], [22326, 8], [22360, 101], [22500, 68], [22626, 8], [22660, 101], [22800, 68], [22926, 9], [22960, 101], [23100, 68], [23226, 9], [23260, 101], [23400, 68], [23526, 9], [23560, 101], [23700, 68], [23825, 10], [23860, 101], [24000, 68], [24125, 10], [24160, 101], [24300, 68], [24425, 10], [24460, 101], [24600, 69], [24725, 10], [24760, 101], [24900, 69], [25025, 10], [25060, 101], [25200, 69], [25325, 10], [25360, 100], [25500, 69], [25625, 10], [25659, 101], [25800, 69], [25925, 10], [25959, 101], [26100, 69], [26225, 10], [26259, 101], [26400, 69], [26525, 10], [26559, 101], [26700, 69], [26825, 10], [26859, 101], [27000, 70], [27095, 1], [27125, 10], [27159, 101], [27300, 70], [27395, 10], [27412, 23], [27459, 101], [27600, 70], [27695, 45], [27755, 105], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [133, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-04.48|+00.92|+03.55"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [86, 48, 130, 92], "mask": [[14205, 1], [14489, 35], [14786, 43], [15086, 45], [15386, 45], [15687, 44], [15987, 44], [16287, 44], [16587, 44], [16887, 44], [17187, 43], [17488, 42], [17788, 42], [18088, 42], [18388, 42], [18688, 42], [18989, 41], [19289, 41], [19589, 41], [19890, 39], [20190, 38], [20491, 37], [20791, 36], [21092, 35], [21392, 35], [21692, 34], [21992, 34], [22293, 33], [22593, 33], [22893, 33], [23193, 33], [23493, 33], [23794, 31], [24094, 31], [24394, 31], [24694, 31], [24995, 30], [25295, 30], [25595, 30], [25895, 30], [26195, 30], [26495, 30], [26795, 30], [27096, 29], [27405, 7]], "point": [108, 69]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 66], [17490, 173], [17700, 66], [17795, 40], [17860, 103], [18000, 66], [18096, 38], [18161, 102], [18300, 66], [18399, 35], [18461, 102], [18600, 66], [18700, 34], [18761, 102], [18900, 66], [18995, 2], [19001, 33], [19061, 102], [19200, 66], [19295, 3], [19301, 33], [19361, 102], [19500, 66], [19595, 3], [19601, 33], [19661, 101], [19800, 67], [19895, 4], [19901, 33], [19961, 101], [20100, 67], [20195, 4], [20201, 33], [20260, 102], [20400, 67], [20495, 4], [20501, 33], [20560, 102], [20700, 67], [20795, 4], [20801, 33], [20860, 102], [21000, 67], [21095, 4], [21101, 33], [21160, 102], [21300, 67], [21395, 4], [21401, 33], [21460, 102], [21600, 67], [21695, 4], [21701, 33], [21760, 102], [21900, 67], [21995, 4], [22001, 33], [22060, 102], [22200, 68], [22295, 3], [22301, 33], [22360, 101], [22500, 68], [22595, 3], [22601, 33], [22660, 101], [22800, 68], [22895, 3], [22900, 35], [22960, 101], [23100, 68], [23195, 3], [23200, 35], [23260, 101], [23400, 68], [23495, 2], [23500, 35], [23560, 101], [23700, 68], [23795, 2], [23799, 36], [23860, 101], [24000, 68], [24095, 2], [24099, 36], [24160, 101], [24300, 68], [24395, 1], [24399, 36], [24460, 101], [24600, 69], [24698, 37], [24760, 101], [24900, 69], [24998, 37], [25060, 101], [25200, 69], [25297, 38], [25360, 100], [25500, 69], [25596, 39], [25659, 101], [25800, 69], [25895, 40], [25959, 101], [26100, 69], [26195, 40], [26259, 101], [26400, 69], [26495, 40], [26559, 101], [26700, 69], [26795, 40], [26859, 101], [27000, 70], [27095, 40], [27159, 101], [27300, 70], [27395, 40], [27459, 101], [27600, 70], [27695, 45], [27755, 105], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [133, 65]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-03.96|+00.39|+02.12"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [69, 65, 143, 140], "mask": [[19269, 73], [19569, 73], [19870, 72], [20170, 72], [20470, 72], [20771, 71], [21071, 71], [21371, 71], [21672, 70], [21972, 70], [22272, 70], [22573, 69], [22873, 69], [23174, 68], [23474, 68], [23774, 68], [24075, 67], [24375, 67], [24675, 68], [24976, 67], [25276, 67], [25576, 67], [25877, 66], [26177, 66], [26477, 66], [26778, 65], [27078, 65], [27378, 65], [27679, 64], [27979, 64], [28279, 64], [28580, 63], [28880, 63], [29180, 63], [29481, 62], [29781, 62], [30081, 62], [30382, 61], [30682, 61], [30982, 61], [31283, 60], [31583, 60], [31883, 60], [32184, 59], [32484, 59], [32785, 58], [33085, 58], [33385, 59], [33686, 58], [33986, 58], [34286, 58], [34587, 57], [34887, 57], [35187, 57], [35488, 56], [35788, 56], [36088, 56], [36389, 55], [36689, 55], [36989, 55], [37290, 54], [37590, 54], [37890, 54], [38191, 53], [38491, 53], [38791, 53], [39092, 52], [39392, 52], [39692, 52], [39993, 51], [40293, 51], [40593, 51], [40894, 50], [41194, 50], [41494, 50], [41795, 49]], "point": [106, 101]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-04.48|+00.92|+03.55", "placeStationary": true, "receptacleObjectId": "Cabinet|-03.96|+00.39|+02.12"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [68, 66, 141, 184], "mask": [[19568, 3], [19572, 67], [19868, 3], [19872, 67], [20168, 71], [20468, 4], [20473, 66], [20768, 4], [20773, 66], [21068, 71], [21368, 5], [21374, 65], [21668, 5], [21674, 65], [21968, 71], [22268, 6], [22275, 65], [22568, 6], [22575, 65], [22868, 72], [23168, 7], [23176, 64], [23468, 7], [23476, 64], [23768, 72], [24068, 8], [24077, 63], [24368, 8], [24377, 63], [24668, 72], [24968, 9], [24978, 62], [25268, 9], [25278, 62], [25569, 71], [25869, 9], [25879, 61], [26169, 9], [26179, 61], [26469, 71], [26769, 10], [26780, 60], [27069, 10], [27080, 60], [27369, 71], [27669, 11], [27681, 59], [27969, 11], [27981, 59], [28269, 71], [28569, 12], [28582, 59], [28869, 12], [28882, 59], [29169, 72], [29469, 13], [29483, 58], [29769, 13], [29783, 58], [30069, 72], [30369, 14], [30384, 57], [30669, 14], [30684, 57], [30969, 72], [31269, 15], [31285, 56], [31569, 72], [31869, 72], [32169, 16], [32186, 55], [32469, 72], [32769, 72], [33069, 17], [33087, 54], [33369, 72], [33669, 72], [33969, 18], [33988, 53], [34269, 72], [34569, 72], [34869, 19], [34889, 52], [35169, 73], [35469, 73], [35769, 20], [35790, 52], [36069, 73], [36369, 73], [36669, 21], [36691, 51], [36970, 72], [37270, 72], [37571, 20], [37592, 50], [37871, 71], [38172, 70], [38472, 20], [38493, 49], [38773, 69], [39073, 69], [39374, 19], [39394, 48], [39674, 68], [39974, 21], [39996, 46], [40275, 19], [40296, 46], [40575, 67], [40876, 66], [41176, 19], [41477, 19], [41777, 19], [42078, 18], [42378, 18], [42679, 17], [42979, 17], [43279, 17], [43580, 16], [43880, 16], [44181, 16], [44481, 16], [44782, 15], [45082, 15], [45383, 14], [45683, 14], [45984, 13], [46284, 13], [46584, 13], [46885, 12], [47185, 12], [47486, 11], [47786, 12], [48087, 11], [48387, 11], [48688, 10], [48988, 10], [49289, 9], [49589, 9], [49889, 9], [50190, 8], [50490, 8], [50791, 7], [51091, 7], [51392, 7], [51692, 7], [51993, 6], [52293, 6], [52594, 5], [52894, 5], [53194, 5], [53495, 4], [53795, 4], [54096, 3], [54396, 3], [54697, 3], [54997, 3]], "point": [104, 124]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-03.96|+00.39|+02.12"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [68, 66, 141, 184], "mask": [[19568, 3], [19572, 67], [19868, 3], [19872, 67], [20168, 71], [20468, 4], [20473, 66], [20768, 4], [20773, 66], [21068, 71], [21368, 5], [21374, 65], [21668, 5], [21674, 65], [21968, 71], [22268, 6], [22275, 65], [22568, 6], [22575, 65], [22868, 72], [23168, 7], [23176, 64], [23468, 7], [23476, 64], [23768, 72], [24068, 8], [24077, 63], [24368, 8], [24377, 63], [24668, 72], [24968, 9], [24978, 62], [25268, 9], [25278, 62], [25569, 71], [25869, 9], [25879, 61], [26169, 9], [26179, 61], [26469, 71], [26769, 10], [26780, 60], [27069, 10], [27080, 60], [27369, 71], [27669, 11], [27681, 59], [27969, 11], [27981, 59], [28269, 71], [28569, 12], [28582, 59], [28869, 12], [28882, 59], [29169, 72], [29469, 13], [29483, 58], [29769, 13], [29783, 58], [30069, 72], [30369, 14], [30384, 57], [30669, 14], [30684, 57], [30969, 72], [31269, 15], [31285, 56], [31569, 72], [31869, 72], [32169, 16], [32186, 55], [32469, 72], [32769, 72], [33069, 17], [33087, 40], [33134, 7], [33369, 56], [33436, 5], [33669, 54], [33737, 4], [33969, 18], [33988, 34], [34038, 3], [34269, 53], [34339, 2], [34569, 52], [34639, 2], [34869, 19], [34889, 32], [34939, 2], [35169, 52], [35239, 3], [35469, 52], [35539, 3], [35769, 20], [35790, 31], [35839, 3], [36069, 52], [36139, 3], [36369, 53], [36439, 3], [36669, 21], [36691, 31], [36738, 4], [36970, 53], [37038, 4], [37270, 54], [37337, 5], [37571, 20], [37592, 32], [37637, 5], [37871, 54], [37937, 5], [38172, 53], [38237, 5], [38472, 20], [38493, 32], [38537, 5], [38773, 53], [38836, 6], [39073, 54], [39135, 7], [39374, 19], [39394, 36], [39432, 10], [39674, 68], [39974, 21], [39996, 46], [40275, 19], [40296, 46], [40575, 67], [40876, 66], [41176, 19], [41477, 19], [41777, 19], [42078, 18], [42378, 18], [42679, 17], [42979, 17], [43279, 17], [43580, 16], [43880, 16], [44181, 16], [44481, 16], [44782, 15], [45082, 15], [45383, 14], [45683, 14], [45984, 13], [46284, 13], [46584, 13], [46885, 12], [47185, 12], [47486, 11], [47786, 12], [48087, 11], [48387, 11], [48688, 10], [48988, 10], [49289, 9], [49589, 9], [49889, 9], [50190, 8], [50490, 8], [50791, 7], [51091, 7], [51392, 7], [51692, 7], [51993, 6], [52293, 6], [52594, 5], [52894, 5], [53194, 5], [53495, 4], [53795, 4], [54096, 3], [54396, 3], [54697, 3], [54997, 3]], "point": [104, 124]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan13", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -0.75, "y": 0.8995012, "z": 1.5}, "object_poses": [{"objectName": "Pan_94f6c891", "position": {"x": -2.92400026, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -1.86012983, "y": 0.92379117, "z": 2.02650762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -3.613587, "y": 0.95696795, "z": 5.182719}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -4.24220753, "y": 0.9230642, "z": 2.30013466}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -3.92572761, "y": 1.45841229, "z": 5.475017}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -0.334957659, "y": 0.926028669, "z": 5.932334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -1.69388, "y": 0.924428642, "z": 1.8}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -1.69388, "y": 0.924048543, "z": 1.72449732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -0.411640465, "y": 0.9400999, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -3.74827051, "y": 0.104547471, "z": 6.45731449}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -3.74827051, "y": 0.104547471, "z": 6.29268551}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_53fee662", "position": {"x": -0.301119328, "y": 1.5816828, "z": 3.22872138}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Plate_53fee662", "position": {"x": -4.19639969, "y": 0.791783035, "z": 3.22241282}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Knife_52085e6b", "position": {"x": -3.73307371, "y": 0.9532002, "z": 5.259397}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -1.777005, "y": 0.9532003, "z": 1.9510051}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -0.334957659, "y": 0.982143, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -0.181592077, "y": 1.00465465, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -0.301117539, "y": 1.41551137, "z": 3.85049963}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -0.4098, "y": 0.9556, "z": 6.55700064}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -2.53047323, "y": 1.54121816, "z": 1.63371325}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -4.478052, "y": 0.95933944, "z": 3.66537166}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -1.86012983, "y": 0.976970434, "z": 1.87550259}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -3.34089136, "y": 0.9532003, "z": 1.87549877}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.259896457, "y": 1.36942482, "z": 3.47924948}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -3.61358714, "y": 0.9240485, "z": 5.33607531}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -0.342341423, "y": 1.68280423, "z": 3.79428244}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Plate_53fee662", "position": {"x": -0.334957659, "y": 0.92592597, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -4.191, "y": 1.0039, "z": 2.538}, "rotation": {"x": 0.0, "y": 278.173157, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -0.4018539, "y": 1.15011132, "z": 3.85050058}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -4.102533, "y": 0.9805429, "z": 2.30013466}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -4.18449736, "y": 0.9244287, "z": 4.90006828}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -1.94325483, "y": 0.938499868, "z": 2.02650762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_aa4cec24", "position": {"x": -0.565006, "y": 1.02938533, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -4.478052, "y": 0.922359467, "z": 3.54880023}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -0.532724559, "y": 0.746293068, "z": 6.72287941}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_94f6c891", "position": {"x": -2.45436978, "y": 0.9335794, "z": 2.0230875}, "rotation": {"x": 0.0, "y": 240.000244, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -2.469, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -4.12835073, "y": 0.0767763257, "z": 3.37432456}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -0.258274853, "y": 0.924821734, "z": 5.932334}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -4.2056303, "y": 0.07426733, "z": 4.890051}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -0.258274853, "y": 0.9251421, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -3.41765475, "y": 0.74652493, "z": 1.9275552}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -4.24944735, "y": 0.756804943, "z": 2.41306114}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -2.74077916, "y": 1.54121816, "z": 1.60112977}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1700973083, "scene_num": 13}, "task_id": "trial_T20190908_123952_096976", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1NWXL4QO30M8U_32ZKVD547IEU57GH1GAC6SBH2733B5", "high_descs": ["Turn left and walk to the fridge, then turn left and take one step, then turn left and walk across the room.", "Pick up the black cup that is on the upper right corner of the sink. ", "Turn around and take two steps, then turn right and walk to the stove then look up.", "Open the microwave and put the cup inside, close the door and turn the microwave on for three seconds, then take the cup. ", "Look down, turn to the right, then look down again.", "Put the cup in the cabinet on the left."], "task_desc": "Heat the black cup.", "votes": [1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3KMS4QQVK5HQEM04ROO764J4SQYKFU", "high_descs": ["Turn left and walk to the fridge then take a step to your left and walk one more step forward then turn left and walk to the sink.", "Pick up the black cup that's near the egg to the right of the sink..", "Turn left and take two steps to your left then walk to the microwave that's in front of you.", "Warm the cup in the microwave then take it back out and close the door.", "Turn right and take a tiny step forward.", "Put the cup in the cupboard under the counter all the way to the left of the cupboards opposing you."], "task_desc": "Put a warm mug in the cupboard.", "votes": [1, 1]}, {"assignment_id": "A2A028LRDJB7ZB_3MTMREQS4Y9MDVDD5MLIGE28IHRWAM", "high_descs": ["Turn left walk straight then head to the sink", "Pick up the cup on sink counter beside the faucet", "Turn left then head to the microwave ", "Open the microwave then put in the cup, get it then close the microwave", "Turn left then walk forward", "Open the cabinet and put in the cup then close the cabinet"], "task_desc": "Put the heated cup in the cabinet", "votes": [1, 1]}]}}