{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 32}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|1|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [5.807683, 5.807683, 2.075891732, 2.075891732, 3.78305292, 3.78305292]], "coordinateReceptacleObjectId": ["CounterTop", [3.956, 3.956, 4.068, 4.068, 3.7844, 3.7844]], "forceVisible": true, "objectId": "Egg|+01.45|+00.95|+00.52"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-3|2|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [5.807683, 5.807683, 2.075891732, 2.075891732, 3.78305292, 3.78305292]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-2.136, -2.136, -6.216, -6.216, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|+01.45|+00.95|+00.52", "receptacleObjectId": "<PERSON><PERSON>|-00.53|+00.00|-01.55"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.45|+00.95|+00.52"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [50, 116, 69, 136], "mask": [[34556, 4], [34854, 9], [35153, 12], [35452, 14], [35751, 16], [36051, 17], [36351, 17], [36650, 19], [36950, 19], [37250, 20], [37550, 20], [37850, 20], [38151, 19], [38451, 19], [38751, 19], [39052, 18], [39353, 16], [39653, 16], [39954, 14], [40256, 11], [40557, 9]], "point": [59, 125]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.45|+00.95|+00.52", "placeStationary": true, "receptacleObjectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 125], [23901, 99], [24050, 126], [24201, 99], [24349, 127], [24501, 99], [24649, 127], [24800, 100], [24948, 129], [25100, 100], [25248, 128], [25400, 100], [25547, 129], [25700, 100], [25847, 129], [25999, 101], [26146, 130], [26299, 101], [26446, 130], [26599, 101], [26745, 131], [26899, 101], [27045, 92], [27150, 26], [27199, 101], [27344, 91], [27453, 23], [27499, 101], [27644, 89], [27754, 22], [27798, 102], [27943, 89], [28056, 20], [28098, 102], [28243, 88], [28356, 20], [28398, 102], [28542, 88], [28657, 19], [28698, 102], [28842, 88], [28957, 19], [28998, 102], [29141, 89], [29257, 19], [29297, 103], [29441, 89], [29561, 15], [29597, 102], [29740, 90], [29863, 13], [29897, 102], [30039, 91], [30157, 3], [30163, 13], [30197, 101], [30339, 91], [30457, 4], [30464, 12], [30497, 101], [30638, 92], [30757, 4], [30763, 13], [30796, 101], [30938, 93], [31057, 3], [31063, 14], [31096, 101], [31237, 94], [31356, 4], [31363, 16], [31395, 101], [31537, 94], [31656, 4], [31662, 18], [31693, 103], [31836, 95], [31956, 3], [31962, 21], [31991, 104], [32136, 95], [32256, 3], [32262, 133], [32435, 96], [32556, 2], [32561, 133], [32735, 96], [32856, 2], [32861, 133], [33034, 97], [33156, 1], [33160, 133], [33334, 97], [33456, 1], [33460, 133], [33633, 99], [33759, 133], [33933, 99], [34059, 133], [34232, 100], [34358, 134], [34532, 100], [34657, 134], [34831, 101], [34956, 135], [35131, 101], [35256, 134], [35430, 102], [35556, 134], [35730, 102], [35856, 133], [36029, 103], [36156, 133], [36328, 104], [36456, 132], [36628, 104], [36755, 133], [36927, 106], [37055, 132], [37227, 106], [37355, 132], [37526, 108], [37654, 132], [37826, 109], [37953, 133], [38125, 111], [38252, 133], [38425, 113], [38550, 135], [38724, 118], [38845, 139], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 125], [23901, 99], [24050, 126], [24201, 99], [24349, 127], [24501, 99], [24649, 127], [24800, 100], [24948, 129], [25100, 100], [25248, 128], [25400, 100], [25547, 129], [25700, 100], [25847, 129], [25999, 101], [26146, 130], [26299, 101], [26446, 130], [26599, 101], [26745, 131], [26899, 101], [27045, 92], [27150, 26], [27199, 101], [27344, 91], [27452, 24], [27499, 101], [27644, 89], [27754, 22], [27798, 102], [27943, 89], [28055, 21], [28098, 102], [28243, 88], [28356, 20], [28398, 102], [28542, 88], [28657, 19], [28698, 102], [28842, 88], [28957, 19], [28998, 102], [29141, 89], [29257, 19], [29297, 103], [29441, 89], [29561, 15], [29597, 102], [29740, 90], [29863, 13], [29897, 102], [30039, 91], [30157, 3], [30163, 13], [30197, 101], [30339, 91], [30457, 4], [30464, 12], [30497, 101], [30638, 92], [30757, 4], [30763, 13], [30796, 101], [30938, 93], [31057, 3], [31063, 14], [31096, 101], [31237, 94], [31356, 4], [31363, 16], [31395, 101], [31537, 94], [31656, 4], [31662, 18], [31693, 103], [31836, 95], [31956, 3], [31962, 21], [31991, 104], [32136, 95], [32256, 3], [32262, 12], [32279, 116], [32435, 96], [32556, 2], [32561, 11], [32581, 113], [32735, 96], [32856, 2], [32861, 10], [32882, 112], [33034, 97], [33156, 1], [33160, 10], [33182, 111], [33334, 97], [33456, 1], [33460, 10], [33483, 110], [33633, 99], [33759, 10], [33783, 109], [33933, 99], [34059, 10], [34084, 108], [34232, 100], [34358, 10], [34384, 108], [34532, 100], [34657, 11], [34684, 107], [34831, 101], [34956, 12], [34984, 107], [35131, 101], [35256, 12], [35284, 106], [35430, 102], [35556, 12], [35584, 106], [35730, 102], [35856, 12], [35884, 105], [36029, 103], [36156, 12], [36184, 105], [36328, 104], [36456, 12], [36484, 104], [36628, 104], [36755, 13], [36784, 104], [36927, 106], [37055, 14], [37083, 104], [37227, 106], [37355, 14], [37383, 104], [37526, 108], [37654, 16], [37682, 104], [37826, 109], [37953, 18], [37981, 105], [38125, 111], [38252, 20], [38279, 106], [38425, 113], [38550, 135], [38724, 118], [38845, 139], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.45|+00.95|+00.52"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [168, 108, 183, 128], "mask": [[32274, 5], [32572, 9], [32871, 11], [33170, 12], [33470, 13], [33769, 14], [34069, 15], [34368, 16], [34668, 16], [34968, 16], [35268, 16], [35568, 16], [35868, 16], [36168, 16], [36468, 16], [36768, 16], [37069, 14], [37369, 14], [37670, 12], [37971, 10], [38272, 7]], "point": [175, 117]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 125], [23901, 99], [24050, 126], [24201, 99], [24349, 127], [24501, 99], [24649, 127], [24800, 100], [24948, 129], [25100, 100], [25248, 128], [25400, 100], [25547, 129], [25700, 100], [25847, 129], [25999, 101], [26146, 130], [26299, 101], [26446, 130], [26599, 101], [26745, 131], [26899, 101], [27045, 92], [27150, 26], [27199, 101], [27344, 91], [27453, 23], [27499, 101], [27644, 89], [27754, 22], [27798, 102], [27943, 89], [28056, 20], [28098, 102], [28243, 88], [28356, 20], [28398, 102], [28542, 88], [28657, 19], [28698, 102], [28842, 88], [28957, 19], [28998, 102], [29141, 89], [29257, 19], [29297, 103], [29441, 89], [29561, 15], [29597, 102], [29740, 90], [29863, 13], [29897, 102], [30039, 91], [30157, 3], [30163, 13], [30197, 101], [30339, 91], [30457, 4], [30464, 12], [30497, 101], [30638, 92], [30757, 4], [30763, 13], [30796, 101], [30938, 93], [31057, 4], [31063, 14], [31096, 101], [31237, 94], [31356, 4], [31363, 16], [31395, 101], [31537, 94], [31656, 4], [31662, 18], [31693, 103], [31836, 95], [31956, 3], [31962, 21], [31991, 104], [32136, 95], [32256, 3], [32262, 133], [32435, 96], [32556, 2], [32561, 133], [32735, 96], [32856, 2], [32861, 133], [33034, 97], [33156, 1], [33160, 133], [33334, 97], [33456, 1], [33460, 133], [33633, 99], [33759, 133], [33933, 99], [34059, 133], [34232, 100], [34358, 134], [34532, 100], [34657, 134], [34831, 101], [34956, 135], [35131, 101], [35256, 134], [35430, 102], [35556, 134], [35730, 102], [35856, 133], [36029, 103], [36156, 133], [36328, 104], [36456, 132], [36628, 104], [36755, 133], [36927, 106], [37055, 132], [37227, 106], [37355, 132], [37526, 108], [37654, 132], [37826, 109], [37953, 133], [38125, 111], [38252, 133], [38425, 113], [38550, 135], [38724, 119], [38845, 139], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.53|+00.00|-01.55"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 225], "mask": [[0, 41700], [41701, 299], [42002, 298], [42302, 298], [42603, 297], [42904, 296], [43205, 295], [43506, 294], [43807, 293], [44108, 292], [44409, 291], [44710, 290], [45011, 289], [45312, 288], [45613, 287], [45914, 286], [46215, 285], [46515, 285], [46816, 284], [47117, 283], [47418, 282], [47719, 281], [48020, 280], [48321, 279], [48622, 278], [48923, 276], [49224, 274], [49525, 272], [49826, 270], [50127, 268], [50428, 266], [50729, 264], [51029, 263], [51330, 260], [51631, 258], [51932, 256], [52233, 254], [52534, 252], [52835, 250], [53136, 248], [53437, 246], [53738, 244], [54039, 242], [54340, 240], [54641, 237], [54942, 235], [55242, 234], [55543, 232], [55844, 230], [56145, 228], [56446, 226], [56747, 224], [57048, 222], [57349, 220], [57650, 218], [57951, 215], [58252, 213], [58553, 211], [58854, 209], [59155, 207], [59456, 205], [59756, 204], [60057, 202], [60358, 200], [60659, 198], [60960, 195], [61261, 193], [61562, 191], [61863, 189], [62164, 187], [62465, 185], [62766, 183], [63067, 181], [63368, 179], [63669, 177], [63969, 176], [64270, 173], [64571, 171], [64872, 169], [65173, 167], [65474, 165], [65775, 163], [66076, 161], [66377, 159], [66678, 157], [66979, 155], [67284, 143]], "point": [149, 112]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.45|+00.95|+00.52", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-00.53|+00.00|-01.55"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 212], [238, 275], [538, 275], [837, 278], [1136, 280], [1435, 283], [1733, 288], [2031, 60898], [62930, 299], [63230, 299], [63530, 299], [63830, 300], [64131, 299], [64431, 299], [64731, 299], [65031, 299], [65331, 299], [65631, 600], [66232, 298], [66532, 296], [66832, 152], [66985, 142], [67132, 295], [67432, 152], [67732, 152], [68033, 150], [68333, 150], [68633, 150], [68933, 150], [69233, 150], [69533, 150], [69833, 150], [70134, 149], [70434, 148], [70734, 148], [71034, 148], [71334, 148], [71634, 148], [71935, 147], [72235, 147], [72535, 147], [72835, 146], [73135, 146], [73435, 146], [73736, 145], [74036, 145], [74336, 145], [74636, 145], [74936, 145], [75236, 144], [75536, 144], [75837, 143], [76137, 143], [76437, 143], [76737, 143], [77037, 143], [77337, 143], [77638, 141], [77938, 141], [78238, 141], [78538, 141], [78838, 141], [79138, 141], [79438, 141], [79739, 140], [80039, 139], [80339, 139], [80639, 139], [80939, 139], [81239, 139], [81540, 138], [81840, 138], [82140, 138], [82440, 137], [82740, 137], [83040, 137], [83341, 136], [83641, 136], [83941, 136], [84241, 136], [84541, 136], [84841, 135], [85141, 135], [85442, 134], [85742, 133], [86042, 130], [86342, 130], [86642, 130], [86942, 129], [87243, 128], [87543, 128], [87843, 128], [88143, 128], [88443, 128], [88743, 57], [88809, 61], [89044, 56], [89149, 20], [89344, 56], [89644, 56], [89944, 56]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.53|+00.00|-01.55"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 212], [238, 275], [538, 275], [837, 278], [1136, 280], [1435, 283], [1733, 288], [2031, 4387], [6420, 295], [6722, 292], [7024, 289], [7325, 288], [7626, 286], [7926, 286], [8227, 284], [8527, 284], [8828, 283], [9128, 283], [9429, 281], [9729, 281], [10029, 281], [10329, 281], [10630, 280], [10930, 281], [11230, 281], [11530, 281], [11829, 282], [12129, 283], [12429, 283], [12729, 284], [13028, 286], [13328, 286], [13627, 289], [13926, 291], [14224, 48705], [62930, 299], [63230, 299], [63530, 299], [63830, 300], [64131, 299], [64431, 299], [64731, 299], [65031, 299], [65331, 299], [65631, 600], [66232, 298], [66532, 296], [66832, 152], [66985, 142], [67132, 295], [67432, 152], [67732, 152], [68033, 150], [68333, 150], [68633, 150], [68933, 150], [69233, 150], [69533, 150], [69833, 150], [70134, 149], [70434, 148], [70734, 148], [71034, 148], [71334, 148], [71634, 148], [71935, 147], [72235, 147], [72535, 147], [72835, 146], [73135, 146], [73435, 146], [73736, 145], [74036, 145], [74336, 145], [74636, 145], [74936, 145], [75236, 144], [75536, 144], [75837, 143], [76137, 143], [76437, 143], [76737, 143], [77037, 143], [77337, 143], [77638, 141], [77938, 141], [78238, 141], [78538, 141], [78838, 141], [79138, 141], [79438, 141], [79739, 140], [80039, 139], [80339, 139], [80639, 139], [80939, 139], [81239, 139], [81540, 138], [81840, 138], [82140, 138], [82440, 137], [82740, 137], [83040, 137], [83341, 136], [83641, 136], [83941, 136], [84241, 136], [84541, 136], [84841, 135], [85141, 135], [85442, 134], [85742, 133], [86042, 130], [86342, 130], [86642, 130], [86942, 129], [87243, 128], [87543, 128], [87843, 128], [88143, 128], [88443, 128], [88743, 57], [88809, 61], [89044, 56], [89149, 20], [89344, 56], [89644, 56], [89944, 56]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan14", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 0.75, "y": 0.9009992, "z": 0.25}, "object_poses": [{"objectName": "Pan_7013969f", "position": {"x": 1.5433, "y": 0.9098, "z": -0.3203}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ButterKnife_38537d23", "position": {"x": 0.7327081, "y": 0.9106421, "z": 1.00428116}, "rotation": {"x": 0.0, "y": 269.999817, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 1.77508092, "y": 2.13560438, "z": 1.19849527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 1.23939538, "y": 0.7527544, "z": -1.65478253}, "rotation": {"x": 0.035023734, "y": -0.000220710528, "z": 0.124343365}}, {"objectName": "Fork_192d20c7", "position": {"x": 0.98900044, "y": 0.911148548, "z": 0.9159616}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spoon_89592edf", "position": {"x": 1.75787544, "y": 0.9117294, "z": 1.00428414}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Knife_06a240e2", "position": {"x": 1.8659904, "y": 0.943637133, "z": -1.63160944}, "rotation": {"x": 0.0, "y": -0.000160509444, "z": 0.0}}, {"objectName": "Tomato_817a54e2", "position": {"x": 0.220125377, "y": 0.974455357, "z": 0.739318848}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_e61d6e1d", "position": {"x": 1.79610693, "y": 1.01113224, "z": -1.7172941}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_f9995e03", "position": {"x": 1.83586109, "y": 1.01045275, "z": -0.888098955}, "rotation": {"x": 0.0, "y": 0.0001673396, "z": 0.0}}, {"objectName": "Mug_f9995e03", "position": {"x": 1.7063539, "y": 1.01045275, "z": -0.729025662}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_5b80d804", "position": {"x": 1.70924687, "y": 0.127512544, "z": 1.77969086}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_5b80d804", "position": {"x": 1.83075309, "y": 0.127512544, "z": 1.58830917}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_e8bf9352", "position": {"x": 1.58645463, "y": 0.9128847, "z": -1.37455654}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_fd471042", "position": {"x": 0.7327074, "y": 0.9087641, "z": 1.26924193}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_5ecc05ec", "position": {"x": -0.7415557, "y": 1.08980477, "z": -1.59446669}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_85efe221", "position": {"x": 1.45192075, "y": 0.94576323, "z": 0.518972933}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Fork_192d20c7", "position": {"x": 1.86599016, "y": 0.911748469, "z": -1.5459249}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Ladle_4b0a72cf", "position": {"x": 1.20112586, "y": 0.926698267, "z": 0.7435304}, "rotation": {"x": 3.34713626, "y": 0.08807726, "z": 346.984436}}, {"objectName": "Apple_5b80d804", "position": {"x": 1.86598945, "y": 0.968506932, "z": -1.288871}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 1.68454254, "y": 0.905599952, "z": 0.467114925}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pan_7013969f", "position": {"x": 1.807, "y": 0.9098, "z": -0.3203}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Tomato_817a54e2", "position": {"x": 0.553935, "y": 0.975055337, "z": -1.26375878}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 1.696, "y": 1.0807, "z": 0.0741453841}, "rotation": {"x": 0.0, "y": 0.0, "z": 49.76936}}, {"objectName": "Potato_6d0d5c3c", "position": {"x": 0.1596567, "y": 0.951402247, "z": -1.26375985}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Knife_06a240e2", "position": {"x": 1.14033246, "y": 0.7612278, "z": -1.57659507}, "rotation": {"x": 275.5523, "y": 12.5306168, "z": 346.393066}}, {"objectName": "ButterKnife_38537d23", "position": {"x": 1.75787544, "y": 0.9106421, "z": 1.00428414}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bread_8f8a19bf", "position": {"x": 1.615, "y": 0.9770397, "z": 0.761}, "rotation": {"x": -5.25803e-06, "y": 50.5256, "z": 2.524555e-05}}, {"objectName": "Lettuce_e61d6e1d", "position": {"x": 0.85669595, "y": 0.989343941, "z": 1.12062061}, "rotation": {"x": 0.194773674, "y": 270.009369, "z": 1.529223}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 1.76208282, "y": 0.905599952, "z": 0.5708326}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_bb5e3511", "position": {"x": 1.5345701, "y": 0.9084982, "z": 0.05875536}, "rotation": {"x": 0.0, "y": 195.000244, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 0.962753654, "y": 0.753088832, "z": -1.6595732}, "rotation": {"x": 359.387238, "y": 0.4193204, "z": 359.145721}}, {"objectName": "Spoon_89592edf", "position": {"x": 1.501584, "y": 0.9117294, "z": 0.9159631}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_7266264e", "position": {"x": 0.193208635, "y": 0.900000036, "z": 1.17073774}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f9995e03", "position": {"x": 1.84200585, "y": 2.13229823, "z": 0.8429922}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 877639515, "scene_num": 14}, "task_id": "trial_T20190909_091415_283977", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1ELPYAFO7MANS_36W0OB37HZ55HDQWMFFHW4JUCV2ZH5", "high_descs": ["Turn right and take a step to the counter.", "Pick up the egg on the counter.", "Take two steps right.", "Heat the egg in the microwave.", "Turn right and walk to the fridge.", "Place the egg in the top left drawer of the fridge."], "task_desc": "Place a heated egg in a fridge.", "votes": [1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3L4PIM1GQW7I1HUUMYPSW1CYTAHRYO", "high_descs": ["Turn right to face the counter.", "Pick up the egg from the counter.", "Turn right move forward, then turn left to face the microwave.", "Open the microwave, put the egg inside to heat it up, then take it out.", "Turn around and move forward, then turn left to face the fridge.", "Open the fridge, put the egg inside, then close it."], "task_desc": "Put a warm egg in the fridge.", "votes": [1, 1]}, {"assignment_id": "A2A4UAFZ5LW71K_3ZAK8W07I75DKEQQIK9O9K5YOWKU08", "high_descs": ["turn right, walk forward to counter", "pick up the egg that is on the counter", "turn right, take a few steps, turn left to face microwave", "open microwave, put egg in, close it, turn it on, take egg out when it turns off", "turn around, walk to fridge, turn left", "put egg in the fridge"], "task_desc": "put a cooked egg in the fridge", "votes": [1, 1]}]}}