{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000199.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000200.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000202.png", "low_idx": 29}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|0|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-0.3297932, -0.3297932, -2.765095472, -2.765095472, 3.0312164, 3.0312164]], "coordinateReceptacleObjectId": ["SinkBasin", [-0.1172, -0.1172, -2.45, -2.45, 2.992, 2.992]], "forceVisible": true, "objectId": "Cup|-00.08|+00.76|-00.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-0.3297932, -0.3297932, -2.765095472, -2.765095472, 3.0312164, 3.0312164]], "coordinateReceptacleObjectId": ["CounterTop", [-4.0076, -4.0076, -2.676, -2.676, 3.7904, 3.7904]], "forceVisible": true, "objectId": "Cup|-00.08|+00.76|-00.69", "receptacleObjectId": "CounterTop|-01.00|+00.95|-00.67"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.08|+00.76|-00.69"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [156, 69, 188, 111], "mask": [[20571, 2], [20866, 13], [21163, 18], [21461, 22], [21760, 25], [22059, 27], [22358, 29], [22657, 30], [22957, 31], [23256, 33], [23556, 33], [23856, 33], [24156, 33], [24456, 33], [24756, 33], [25056, 33], [25356, 33], [25656, 33], [25956, 33], [26256, 32], [26556, 32], [26856, 32], [27157, 31], [27458, 29], [27758, 29], [28058, 28], [28359, 26], [28659, 25], [28959, 25], [29260, 23], [29560, 23], [29860, 22], [30161, 21], [30461, 20], [30761, 19], [31061, 19], [31362, 17], [31662, 17], [31962, 16], [32263, 15], [32564, 13], [32865, 11], [33167, 7]], "point": [172, 89]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.08|+00.76|-00.69", "placeStationary": true, "receptacleObjectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 95], [24139, 137], [24337, 94], [24440, 136], [24637, 93], [24741, 134], [24936, 94], [25042, 133], [25236, 94], [25342, 132], [25535, 94], [25642, 132], [25834, 95], [25942, 132], [26134, 95], [26243, 130], [26433, 96], [26542, 131], [26733, 96], [26842, 130], [27032, 98], [27142, 130], [27331, 99], [27442, 130], [27631, 100], [27741, 130], [27930, 102], [28040, 5], [28051, 120], [28230, 103], [28338, 5], [28353, 118], [28529, 112], [28654, 116], [28829, 111], [28955, 115], [29128, 112], [29255, 114], [29427, 112], [29555, 114], [29727, 111], [29855, 114], [30026, 112], [30155, 113], [30326, 112], [30455, 113], [30625, 113], [30754, 114], [30924, 114], [31054, 113], [31224, 114], [31353, 114], [31523, 116], [31652, 114], [31823, 116], [31951, 115], [32122, 119], [32250, 116], [32421, 122], [32547, 118], [32721, 244], [33020, 244], [33320, 244], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 122], [19989, 92], [20146, 120], [20291, 90], [20445, 119], [20593, 88], [20744, 119], [20895, 85], [21044, 118], [21196, 84], [21343, 119], [21497, 82], [21643, 118], [21797, 82], [21942, 119], [22098, 81], [22241, 120], [22398, 80], [22541, 120], [22698, 80], [22840, 121], [22997, 80], [23140, 121], [23297, 80], [23439, 122], [23597, 80], [23739, 122], [23897, 79], [24038, 95], [24139, 22], [24197, 79], [24337, 94], [24440, 21], [24497, 79], [24637, 93], [24741, 20], [24797, 78], [24936, 94], [25042, 20], [25097, 78], [25236, 94], [25342, 20], [25396, 78], [25535, 94], [25642, 21], [25696, 78], [25834, 95], [25942, 21], [25995, 79], [26134, 95], [26243, 20], [26294, 79], [26433, 96], [26542, 21], [26594, 79], [26733, 96], [26842, 22], [26893, 79], [27032, 98], [27142, 22], [27193, 79], [27331, 99], [27442, 22], [27492, 80], [27631, 100], [27741, 23], [27792, 79], [27930, 102], [28040, 5], [28051, 13], [28092, 79], [28230, 103], [28338, 5], [28353, 12], [28391, 80], [28529, 112], [28654, 11], [28691, 79], [28829, 111], [28955, 10], [28990, 80], [29128, 112], [29255, 10], [29290, 79], [29427, 112], [29555, 11], [29590, 79], [29727, 111], [29855, 11], [29889, 80], [30026, 112], [30155, 11], [30189, 79], [30326, 112], [30455, 11], [30488, 80], [30625, 113], [30754, 12], [30788, 80], [30924, 114], [31054, 13], [31087, 80], [31224, 114], [31353, 14], [31387, 80], [31523, 116], [31652, 15], [31687, 79], [31823, 116], [31951, 16], [31986, 80], [32122, 119], [32250, 18], [32286, 80], [32421, 122], [32547, 21], [32585, 80], [32721, 148], [32885, 80], [33020, 150], [33184, 80], [33320, 152], [33482, 82], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.08|+00.76|-00.69"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [161, 67, 197, 112], "mask": [[19968, 21], [20266, 25], [20564, 29], [20863, 32], [21162, 34], [21462, 35], [21761, 36], [22061, 37], [22361, 37], [22661, 37], [22961, 36], [23261, 36], [23561, 36], [23861, 36], [24161, 36], [24461, 36], [24761, 36], [25062, 35], [25362, 34], [25663, 33], [25963, 32], [26263, 31], [26563, 31], [26864, 29], [27164, 29], [27464, 28], [27764, 28], [28064, 28], [28365, 26], [28665, 26], [28965, 25], [29265, 25], [29566, 24], [29866, 23], [30166, 23], [30466, 22], [30766, 22], [31067, 20], [31367, 20], [31667, 20], [31967, 19], [32268, 18], [32568, 17], [32869, 16], [33170, 14], [33472, 10]], "point": [179, 88]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 95], [24139, 137], [24337, 94], [24440, 136], [24637, 93], [24741, 134], [24936, 94], [25042, 133], [25236, 94], [25342, 132], [25535, 94], [25642, 132], [25834, 95], [25942, 132], [26134, 95], [26243, 130], [26433, 96], [26542, 131], [26733, 96], [26842, 130], [27032, 98], [27142, 130], [27331, 99], [27442, 130], [27631, 100], [27741, 130], [27930, 102], [28040, 5], [28051, 120], [28230, 103], [28338, 5], [28353, 118], [28529, 112], [28654, 116], [28829, 111], [28955, 115], [29128, 112], [29255, 114], [29427, 112], [29555, 114], [29727, 111], [29855, 114], [30026, 112], [30155, 113], [30326, 112], [30455, 113], [30625, 113], [30754, 114], [30924, 114], [31054, 113], [31224, 114], [31353, 114], [31523, 116], [31652, 114], [31823, 116], [31951, 115], [32122, 119], [32250, 116], [32421, 122], [32547, 118], [32721, 244], [33020, 244], [33320, 244], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.08|+00.76|-00.69", "placeStationary": true, "receptacleObjectId": "CounterTop|-01.00|+00.95|-00.67"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 103, 299, 239], "mask": [[30603, 26], [30638, 2], [30645, 163], [30875, 22], [30903, 26], [30939, 2], [30946, 162], [31177, 19], [31204, 25], [31246, 161], [31478, 18], [31505, 24], [31547, 159], [31780, 15], [31805, 25], [31847, 159], [32081, 14], [32100, 30], [32147, 158], [32382, 13], [32400, 31], [32447, 158], [32683, 11], [32700, 31], [32747, 158], [32984, 10], [33000, 31], [33042, 2], [33046, 159], [33286, 8], [33300, 32], [33342, 162], [33586, 7], [33600, 32], [33643, 162], [33887, 6], [33900, 33], [33943, 151], [34103, 2], [34191, 2], [34200, 33], [34244, 147], [34500, 33], [34545, 144], [34800, 33], [34845, 143], [35100, 33], [35144, 143], [35400, 33], [35443, 144], [35595, 4], [35700, 35], [35741, 145], [35893, 6], [36000, 186], [36192, 7], [36300, 186], [36491, 8], [36600, 186], [36792, 7], [36892, 1], [37008, 78], [37092, 8], [37309, 78], [37393, 7], [37493, 1], [37609, 78], [37695, 5], [37794, 1], [37909, 79], [37997, 3], [38094, 3], [38209, 79], [38509, 80], [38808, 83], [39108, 85], [39408, 88], [39708, 94], [39896, 4], [40008, 94], [40194, 6], [40308, 94], [40490, 10], [40607, 95], [40789, 11], [40907, 95], [41089, 11], [41207, 95], [41388, 12], [41507, 95], [41686, 14], [41807, 95], [41985, 15], [42107, 95], [42285, 15], [42406, 96], [42584, 16], [42706, 96], [42884, 16], [43006, 35], [43045, 57], [43183, 17], [43306, 34], [43346, 56], [43483, 17], [43606, 33], [43647, 55], [43782, 18], [43905, 34], [43948, 55], [44082, 18], [44205, 34], [44248, 55], [44381, 19], [44505, 34], [44545, 58], [44681, 19], [44805, 34], [44845, 59], [44980, 20], [45105, 34], [45145, 59], [45280, 20], [45405, 33], [45445, 60], [45579, 21], [45704, 34], [45745, 60], [45879, 21], [46004, 34], [46045, 61], [46178, 22], [46304, 34], [46345, 62], [46478, 22], [46604, 34], [46645, 63], [46777, 23], [46904, 34], [46944, 64], [47077, 23], [47204, 33], [47244, 65], [47376, 24], [47503, 34], [47544, 66], [47675, 25], [47803, 34], [47844, 67], [47975, 25], [48103, 34], [48143, 69], [48274, 26], [48403, 34], [48443, 70], [48573, 27], [48703, 34], [48743, 72], [48873, 27], [49003, 34], [49043, 73], [49172, 28], [49302, 34], [49342, 75], [49471, 29], [49602, 34], [49642, 77], [49769, 31], [49902, 34], [49942, 79], [50068, 32], [50202, 34], [50242, 3], [50255, 68], [50367, 33], [50502, 34], [50563, 62], [50665, 35], [50801, 32], [50867, 60], [50963, 37], [51101, 29], [51170, 60], [51260, 40], [51401, 26], [51473, 61], [51557, 43], [51701, 23], [51776, 63], [51852, 48], [52001, 22], [52077, 123], [52301, 20], [52379, 121], [52600, 19], [52681, 119], [52900, 17], [52983, 117], [53200, 15], [53285, 115], [53500, 14], [53586, 114], [53800, 13], [53887, 113], [54100, 12], [54188, 112], [54399, 12], [54489, 111], [54699, 11], [54790, 110], [54999, 10], [55091, 109], [55299, 8], [55393, 107], [55599, 7], [55694, 106], [55899, 6], [55995, 105], [56198, 7], [56295, 105], [56498, 6], [56596, 104], [56798, 5], [56897, 103], [57098, 5], [57197, 103], [57398, 4], [57498, 102], [57697, 4], [57799, 101], [57997, 4], [58099, 101], [58297, 3], [58400, 100], [58597, 2], [58701, 99], [58897, 2], [59001, 99], [59197, 1], [59302, 98], [59496, 2], [59602, 13], [59621, 79], [59796, 2], [59902, 3], [59923, 77], [60096, 1], [60223, 77], [60396, 1], [60523, 77], [60696, 1], [60823, 77], [61124, 76], [61295, 1], [61424, 76], [61595, 1], [61724, 76], [62024, 76], [62325, 75], [62625, 75], [62925, 75], [63225, 75], [63525, 75], [63826, 74], [64126, 74], [64426, 74], [64593, 1], [64725, 75], [64893, 1], [65018, 82], [65192, 2], [65306, 94], [65492, 2], [65606, 94], [65790, 4], [65906, 188], [66206, 188], [66506, 188], [66806, 188], [67106, 189], [67405, 190], [67705, 190], [68005, 191], [68304, 192], [68604, 192], [68904, 192], [69204, 193], [69503, 194], [69803, 194], [70103, 194], [70403, 195], [70702, 196], [71002, 196], [71302, 196], [71602, 98]], "point": [149, 166]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan17", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -0.25, "y": 0.908999562, "z": 2.5}, "object_poses": [{"objectName": "SprayBottle_4f8d0eee", "position": {"x": 1.36534023, "y": 0.9109041, "z": -0.144580841}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": 1.128434, "y": 0.911064267, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": 1.325258, "y": 1.65534413, "z": -0.393620074}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": -1.10519874, "y": 0.7489421, "z": 2.08785558}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": -1.10519874, "y": 0.7489421, "z": 2.273428}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": -0.170651615, "y": 0.117355466, "z": -0.6216246}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": -1.12367022, "y": 0.9128167, "z": -0.828023434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": 1.20740271, "y": 0.91254133, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 1.444309, "y": 0.9126294, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": -1.29051554, "y": 0.750755668, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": -0.503703535, "y": 0.9270999, "z": -0.430464923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": 1.52327764, "y": 0.9264999, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.5029, "y": 0.9393, "z": -0.4699}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": -1.10519874, "y": 0.749391854, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -1.296285, "y": 1.04961908, "z": -0.0576033629}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": -1.14979541, "y": 0.75040555, "z": 1.09268785}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": 1.21878171, "y": 0.116305709, "z": -0.05235876}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": -0.503703535, "y": 0.9340927, "z": -0.5894883}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": 1.36722, "y": 0.9082927, "z": 2.36918736}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -1.37803471, "y": 0.800886452, "z": 0.8833122}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -1.149796, "y": 1.47098637, "z": 1.30206287}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": 1.28637147, "y": 0.9128286, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": 1.30640435, "y": 0.113363981, "z": 0.07148978}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -1.32097554, "y": 1.63195741, "z": 1.18811417}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": 0.0417298935, "y": 0.7535562, "z": -0.533726156}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": 1.47041214, "y": 0.146455675, "z": 1.44079888}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": 1.06902707, "y": 0.935938537, "z": 2.68390536}, "rotation": {"x": -3.829391e-09, "y": 7.10744234e-06, "z": -4.051029e-06}}, {"objectName": "Glassbottle_6018293c", "position": {"x": 1.20097, "y": 0.8870167, "z": 2.36918736}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": 1.03472006, "y": 0.886848569, "z": 2.261375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": 0.802, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.9998245, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -1.28665, "y": 0.961828768, "z": -0.477608353}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": -1.29051554, "y": 0.750511765, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": 1.450345, "y": 0.8840117, "z": 2.261375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": 1.238, "y": 0.9697, "z": -0.16}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": -0.813686848, "y": 0.9125919, "z": -0.669}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": 1.117845, "y": 0.8876286, "z": 2.261375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": 0.08650948, "y": 0.11756748, "z": -0.5338761}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": 1.284095, "y": 0.9012999, "z": 2.36918736}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": -1.01254034, "y": 0.7473406, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -1.32097435, "y": 1.69716144, "z": 0.7816504}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": -0.0824483, "y": 0.7578041, "z": -0.691273868}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": 1.28637147, "y": 0.9334927, "z": 0.4229527}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -1.361721, "y": 1.04961908, "z": -0.05760355}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": 1.0494653, "y": 0.9078062, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": -1.10519874, "y": 0.7452062, "z": 2.83014464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": -0.91988194, "y": 0.7938309, "z": 2.83014464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": -1.29051554, "y": 0.7489421, "z": 2.83014464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": 1.09901655, "y": 0.7755628, "z": 1.05059385}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_a0065d6c", "position": {"x": -1.332, "y": 0.9, "z": -0.699}, "rotation": {"x": 0.0, "y": 345.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 1.20740271, "y": 0.9126294, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": 0.146277547, "y": 0.91150403, "z": -0.359971046}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": 1.450345, "y": 0.881300032, "z": 2.477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1761237081, "scene_num": 17}, "task_id": "trial_T20190908_130923_081236", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A24RGLS3BRZ60J_3GA6AFUKORF4WKJ318K8S3EFXIM3HV", "high_descs": ["Move forward to go straight to the sink.", "Pick up the white cup from the back of the sink.", "Hold the cup and turn to the right to find the microwave.", "Open the microwave and place the cup inside. Shut the door. When the microwave is finished, open the door and take the cup out. Shut the door.", "Hold the cup and face to the left.", "Place the cup on the counter to the right side, in front of the pan."], "task_desc": "Heat a cup and place it on the counter.", "votes": [1, 1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3YHH42UU5E6A1O3W96HNIX9FBO6L0M", "high_descs": ["Turn left and take a step forward, then turn right and walk up to the sink.", "Pick up the white cup out of the sink.", "Turn right and walk over to the microwave.", "Put the white cup in the microwave and turn it on for a couple seconds, after that take it out and close the microwave.", "Turn left to face the counter.", "Put the heated white cup on the counter to the right of the spatula."], "task_desc": "Put a heated white cup on the counter.", "votes": [1, 1, 1]}, {"assignment_id": "A3SFPSMFRSRTB3_33PPUNGG3BWI674ZIKRBA8L0ZOQZRW", "high_descs": ["Proceed ahead to sink.", "Pick up cup from center of sink.", "Turn right, proceed to microwave.", "Open microwave, put cup in microwave to right of eggs.  Close microwave.  Wait three seconds, open microwave, remove cup, close door.", "Turn left to face counter.", "Place cup on counter to the right of the spatula."], "task_desc": "Pick up cup from sink, warm in microwave, put on counter.", "votes": [1, 0, 1]}]}}