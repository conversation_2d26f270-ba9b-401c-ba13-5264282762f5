{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 36}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "ToiletPaper", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["toilet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-11|9|0|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-12.0699854, -12.0699854, 12.88027096, 12.88027096, 3.8056512, 3.8056512]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-11.368, -11.368, 11.02, 11.02, 0.003943652, 0.003943652]], "forceVisible": true, "objectId": "ToiletPaper|-03.02|+00.95|+03.22"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-9|7|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-12.0699854, -12.0699854, 12.88027096, 12.88027096, 3.8056512, 3.8056512]], "coordinateReceptacleObjectId": ["Drawer", [-11.37996484, -11.37996484, 1.1200004, 1.1200004, 1.276000144, 1.276000144]], "forceVisible": true, "objectId": "ToiletPaper|-03.02|+00.95|+03.22", "receptacleObjectId": "Drawer|-02.84|+00.32|+00.28"}}, {"discrete_action": {"action": "GotoLocation", "args": ["toiletpaperhanger"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-9|11|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-9.1472, -9.1472, 12.886, 12.886, 3.3, 3.3]], "coordinateReceptacleObjectId": ["ToiletPaperHanger", [-9.4, -9.4, 13.2, 13.2, 3.3, 3.3]], "forceVisible": true, "objectId": "ToiletPaper|-02.29|+00.83|+03.22"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-9|7|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-9.1472, -9.1472, 12.886, 12.886, 3.3, 3.3]], "coordinateReceptacleObjectId": ["Drawer", [-11.37996484, -11.37996484, 1.1200004, 1.1200004, 1.276000144, 1.276000144]], "forceVisible": true, "objectId": "ToiletPaper|-02.29|+00.83|+03.22", "receptacleObjectId": "Drawer|-02.84|+00.32|+00.28"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-03.02|+00.95|+03.22"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [74, 134, 100, 169], "mask": [[39986, 4], [40281, 14], [40578, 19], [40877, 21], [41175, 23], [41474, 24], [41774, 25], [42074, 25], [42374, 25], [42674, 25], [42974, 25], [43275, 24], [43575, 24], [43875, 24], [44175, 24], [44475, 25], [44775, 25], [45076, 24], [45376, 24], [45676, 24], [45976, 24], [46276, 24], [46576, 24], [46877, 23], [47177, 24], [47477, 24], [47777, 24], [48077, 24], [48377, 24], [48678, 23], [48978, 23], [49278, 22], [49578, 20], [49879, 18], [50181, 14], [50483, 8]], "point": [87, 150]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-02.84|+00.32|+00.28"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [192, 139, 289, 169], "mask": [[41597, 93], [41897, 93], [42197, 92], [42496, 93], [42796, 92], [43096, 92], [43396, 91], [43696, 91], [43996, 90], [44295, 91], [44595, 90], [44895, 90], [45195, 89], [45495, 89], [45794, 89], [46094, 88], [46394, 88], [46694, 87], [46994, 87], [47294, 86], [47593, 87], [47893, 86], [48193, 86], [48493, 85], [48793, 85], [49093, 84], [49392, 85], [49692, 84], [49992, 84], [50292, 83], [50592, 83]], "point": [240, 153]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-03.02|+00.95|+03.22", "placeStationary": true, "receptacleObjectId": "Drawer|-02.84|+00.32|+00.28"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [195, 139, 299, 227], "mask": [[41600, 85], [41900, 85], [42199, 87], [42499, 88], [42799, 88], [43099, 89], [43399, 89], [43699, 90], [43998, 91], [44298, 92], [44598, 92], [44898, 93], [45198, 93], [45497, 95], [45797, 95], [46097, 96], [46397, 97], [46697, 97], [46997, 98], [47296, 99], [47596, 100], [47896, 100], [48196, 101], [48496, 101], [48795, 103], [49095, 103], [49395, 104], [49696, 104], [49996, 104], [50296, 104], [50596, 104], [50896, 104], [51196, 104], [51497, 103], [51797, 103], [52097, 103], [52397, 103], [52697, 103], [52997, 103], [53298, 102], [53598, 102], [53898, 102], [54198, 102], [54498, 102], [54798, 102], [55099, 101], [55399, 101], [55699, 101], [55999, 101], [56299, 101], [56599, 101], [56899, 101], [57200, 100], [57500, 100], [57800, 100], [58100, 100], [58400, 100], [58700, 100], [59001, 99], [59301, 99], [59601, 99], [59901, 99], [60201, 99], [60501, 99], [60802, 98], [61102, 98], [61402, 98], [61702, 98], [62002, 98], [62302, 98], [62603, 97], [62903, 97], [63203, 97], [63503, 97], [63803, 97], [64103, 97], [64403, 97], [64703, 97], [65003, 97], [65302, 98], [65602, 98], [65902, 98], [66202, 98], [66501, 99], [66801, 99], [67101, 99], [67401, 99], [67701, 99], [68001, 99]], "point": [247, 182]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-02.84|+00.32|+00.28"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [195, 139, 299, 227], "mask": [[41600, 85], [41900, 85], [42199, 87], [42499, 88], [42799, 88], [43099, 89], [43399, 89], [43699, 90], [43998, 91], [44298, 92], [44598, 92], [44898, 93], [45198, 93], [45497, 95], [45797, 95], [46097, 96], [46397, 97], [46697, 97], [46997, 98], [47296, 99], [47596, 100], [47896, 100], [48196, 101], [48496, 101], [48795, 103], [49095, 103], [49395, 104], [49696, 104], [49996, 104], [50296, 104], [50596, 104], [50896, 104], [51196, 104], [51497, 103], [51797, 103], [52097, 103], [52397, 103], [52697, 103], [52997, 103], [53298, 102], [53598, 102], [53898, 102], [54198, 102], [54498, 102], [54798, 102], [55099, 101], [55399, 101], [55699, 101], [55999, 15], [56019, 81], [56299, 14], [56322, 78], [56599, 14], [56623, 77], [56899, 14], [56924, 76], [57200, 14], [57225, 75], [57500, 14], [57526, 74], [57800, 14], [57826, 74], [58100, 14], [58126, 74], [58400, 14], [58427, 73], [58700, 100], [59001, 99], [59301, 99], [59601, 99], [59901, 99], [60201, 99], [60501, 99], [60802, 98], [61102, 98], [61402, 98], [61702, 98], [62002, 98], [62302, 98], [62603, 97], [62903, 97], [63203, 97], [63503, 97], [63803, 97], [64103, 97], [64403, 97], [64703, 97], [65003, 97], [65302, 98], [65602, 98], [65902, 98], [66202, 98], [66501, 99], [66801, 99], [67101, 99], [67401, 99], [67701, 99], [68001, 99]], "point": [247, 182]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-02.29|+00.83|+03.22"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [101, 126, 138, 155], "mask": [[37603, 36], [37903, 36], [38203, 36], [38502, 37], [38802, 37], [39102, 37], [39402, 37], [39702, 37], [40002, 37], [40301, 38], [40601, 38], [40901, 38], [41201, 38], [41501, 38], [41801, 38], [42101, 38], [42401, 38], [42701, 38], [43001, 38], [43301, 38], [43601, 38], [43901, 38], [44201, 38], [44502, 37], [44802, 37], [45102, 37], [45402, 37], [45702, 37], [46003, 36], [46303, 36]], "point": [119, 139]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-02.84|+00.32|+00.28"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [192, 139, 289, 169], "mask": [[41597, 93], [41897, 93], [42197, 92], [42496, 93], [42796, 92], [43096, 92], [43396, 91], [43696, 91], [43996, 90], [44295, 91], [44595, 90], [44895, 90], [45195, 89], [45495, 89], [45794, 89], [46094, 88], [46394, 88], [46694, 87], [46994, 87], [47294, 86], [47593, 87], [47893, 86], [48193, 86], [48493, 85], [48793, 85], [49093, 84], [49392, 85], [49692, 84], [49992, 84], [50292, 83], [50592, 83]], "point": [240, 153]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-02.29|+00.83|+03.22", "placeStationary": true, "receptacleObjectId": "Drawer|-02.84|+00.32|+00.28"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [195, 139, 299, 227], "mask": [[41600, 85], [41900, 85], [42199, 87], [42499, 88], [42799, 88], [43099, 89], [43399, 89], [43699, 90], [43998, 91], [44298, 92], [44598, 92], [44898, 93], [45198, 93], [45497, 95], [45797, 95], [46097, 96], [46397, 97], [46697, 97], [46997, 98], [47296, 99], [47596, 100], [47896, 100], [48196, 101], [48496, 101], [48795, 103], [49095, 103], [49395, 104], [49696, 104], [49996, 104], [50296, 104], [50596, 104], [50896, 104], [51196, 104], [51497, 103], [51797, 103], [52097, 103], [52397, 103], [52697, 103], [52997, 103], [53298, 102], [53598, 102], [53898, 102], [54198, 102], [54498, 102], [54798, 102], [55099, 101], [55399, 101], [55699, 101], [55999, 15], [56019, 81], [56299, 14], [56322, 78], [56599, 14], [56623, 77], [56899, 14], [56924, 76], [57200, 14], [57225, 75], [57500, 14], [57526, 74], [57800, 14], [57826, 74], [58100, 14], [58126, 74], [58400, 14], [58427, 73], [58700, 100], [59001, 99], [59301, 99], [59601, 99], [59901, 99], [60201, 99], [60501, 99], [60802, 98], [61102, 98], [61402, 98], [61702, 98], [62002, 98], [62302, 98], [62603, 97], [62903, 97], [63203, 97], [63503, 97], [63803, 97], [64103, 97], [64403, 97], [64703, 97], [65003, 97], [65302, 98], [65602, 98], [65902, 98], [66202, 98], [66501, 99], [66801, 99], [67101, 99], [67401, 99], [67701, 99], [68001, 99]], "point": [247, 182]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-02.84|+00.32|+00.28"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [195, 139, 299, 227], "mask": [[41600, 85], [41900, 85], [42199, 87], [42499, 88], [42799, 88], [43099, 89], [43399, 89], [43699, 90], [43998, 91], [44298, 92], [44598, 92], [44898, 93], [45198, 93], [45497, 95], [45797, 95], [46097, 96], [46397, 97], [46697, 97], [46997, 98], [47296, 99], [47596, 100], [47896, 100], [48196, 101], [48496, 101], [48795, 103], [49095, 103], [49395, 104], [49696, 104], [49996, 104], [50296, 104], [50596, 104], [50896, 104], [51196, 104], [51497, 103], [51797, 13], [51817, 83], [52097, 13], [52119, 81], [52397, 13], [52420, 80], [52697, 13], [52721, 79], [52997, 14], [53022, 78], [53298, 13], [53323, 77], [53598, 13], [53623, 77], [53898, 13], [53923, 77], [54198, 13], [54223, 77], [54498, 14], [54523, 77], [54798, 14], [54822, 78], [55099, 13], [55122, 78], [55399, 13], [55422, 78], [55699, 14], [55721, 79], [55999, 14], [56021, 79], [56299, 14], [56322, 78], [56599, 14], [56623, 77], [56899, 14], [56924, 76], [57200, 14], [57225, 75], [57500, 14], [57526, 74], [57800, 14], [57826, 74], [58100, 14], [58126, 74], [58400, 14], [58427, 73], [58700, 100], [59001, 99], [59301, 99], [59601, 99], [59901, 99], [60201, 99], [60501, 99], [60802, 98], [61102, 98], [61402, 98], [61702, 98], [62002, 98], [62302, 98], [62603, 97], [62903, 97], [63203, 97], [63503, 97], [63803, 97], [64103, 97], [64403, 97], [64703, 97], [65003, 97], [65302, 98], [65602, 98], [65902, 98], [66202, 98], [66501, 99], [66801, 99], [67101, 99], [67401, 99], [67701, 99], [68001, 99]], "point": [247, 182]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan423", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.75, "y": 0.9019853, "z": 2.25}, "object_poses": [{"objectName": "Cloth_3812449a", "position": {"x": -2.03704929, "y": 0.8119264, "z": 0.155}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_3812449a", "position": {"x": -2.96204925, "y": 0.8119264, "z": 0.342499971}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_ce702ff7", "position": {"x": -0.3484639, "y": 0.8139013, "z": 2.48055172}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_ce702ff7", "position": {"x": -1.81649792, "y": 0.01922578, "z": 0.137652248}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_01c27901", "position": {"x": -0.2555344, "y": 0.219504416, "z": 2.9402473}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_10d8a609", "position": {"x": -0.0343, "y": 1.53700006, "z": 2.30900025}, "rotation": {"x": 0.0, "y": 90.0003, "z": 0.0}}, {"objectName": "ToiletPaper_2c74e5b0", "position": {"x": -2.2868, "y": 0.825, "z": 3.2215}, "rotation": {"x": 1.628444e-12, "y": 180.000046, "z": 270.0}}, {"objectName": "HandTowel_10d8a609", "position": {"x": -3.1727, "y": 1.487, "z": 0.340999782}, "rotation": {"x": 0.0, "y": 270.000244, "z": 0.0}}, {"objectName": "Towel_1c670900", "position": {"x": -1.513, "y": 1.496, "z": 0.109}, "rotation": {"x": 0.0, "y": 1.70754709e-06, "z": 0.0}}, {"objectName": "Cloth_3812449a", "position": {"x": -3.65348864, "y": 0.190482587, "z": 1.27327275}, "rotation": {"x": 1.40334191e-14, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBar_57e826c8", "position": {"x": -3.12214971, "y": 0.8130697, "z": 0.155}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_2c74e5b0", "position": {"x": -3.01749635, "y": 0.9514128, "z": 3.22006774}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Plunger_a91f9bf0", "position": {"x": -2.53099918, "y": 0.000522919, "z": 3.026999}, "rotation": {"x": -0.00118147617, "y": 0.000440507982, "z": 0.0007804241}}, {"objectName": "Candle_f9e80138", "position": {"x": -0.412727833, "y": 0.812504768, "z": 2.35051155}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_01c27901", "position": {"x": -2.76749659, "y": 0.954541743, "z": 3.193275}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "ToiletPaper_0148fbc8", "position": {"x": -2.11709952, "y": 0.81, "z": 0.342499971}, "rotation": {"x": 1.6284477e-12, "y": 7.84645738e-26, "z": 5.521441e-12}}, {"objectName": "SprayBottle_ce702ff7", "position": {"x": -2.89249659, "y": 0.955390036, "z": 3.19327545}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "ScrubBrush_a6463e50", "position": {"x": -2.33284664, "y": 0.000985913, "z": 3.163393}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2280225214, "scene_num": 423}, "task_id": "trial_T20190907_111333_143099", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A1HKHM4NVAO98H_317HQ483IA93BU1ZT1XR07TY8I9INF", "high_descs": ["turn right and walk over to the toilet at the end of the room", "grab the toilet paper off the top of the toilet there", "turn around and walk over to the right side of the bathroom sink cabinet", "place the toilet paper in the bottom right side of the cabinet", "turn around and walk back over to the toilet", "grab the toilet paper off of the toilet paper holder on the wall to the right of the toilet", "turn around and walk over to the bathroom sink cabinet", "place the toilet paper in the bottom right side of the cabinet"], "task_desc": "place the toilet paper in the bottom right side of the cabinet", "votes": [0, 1, 1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "AM2KK02JXXW48_37C0GNLMHIKEWYFL26AZ7ZRZLNA6DP", "high_descs": ["Turn right, go to the toilet, turn right to face the toilet. ", "Pick up the toilet paper on the toilet tank lid. ", "Turn around, go to the sink. ", "Open the drawer on the the right below the sink and put the toilet paper in there. ", "Turn around and go to the toilet paper holder.", "Take the toilet paper from the holder. ", "Turn around and go to the sink. ", "Open the lower drawer on the right and put the toilet paper in there next to the other toilet paper. "], "task_desc": "Put two toilet papers in the right drawer under the sink. ", "votes": [1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A1GVTH5YS3WOK0_3FTOP5WARI5WL9TYADGT1WCLDZLJ05", "high_descs": ["Turn right and walk towards the toilet.", "Pick up the toilet paper on the toilet.", "Turn around and walk towards the vanity.", "Open the right drawer below the sink and place the toilet paper in it.", "Turn around and walk towards the toilet paper holder on the wall.", "Take the toilet paper from the toilet paper holder.", "Turn around and walk towards the vanity.", "Open the right drawer below the sink and place the toilet paper in it."], "task_desc": "Place the toilet paper rolls in the drawer below the sink.", "votes": [1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A1O3TWBUDONVLO_3OVR4I9USS0U6KFCO4N4A0EK8GZQ49", "high_descs": ["Turn right and walk forward and turn to the toilet on the left.", "Pick up the toilet paper on the back of the toilet.", "Turn around and walk across the room to face the sink.", "Place the toilet paper in the bottom right drawer.", "Turn around and approach the toilet paper on the wall.", "Remove the toilet paper on the roll on the wall.", "Turn around and walk across the room to face the sink.", "Place the toilet paper in the bottom right drawer."], "task_desc": "TO put two rolls of toilet paper in the bottom right drawer under the sink.", "votes": [1, 1]}, {"assignment_id": "A2CT57W84KXX25_3KIBXJ1WD8BCZARZUJIGSC8GQZLOKZ", "high_descs": ["Turn right, walk to the tub, turn right to the toilet. ", "Pick up the toilet paper from the toilet tank. ", "Turn around, walk to the counter on the other side of the room. ", "Put the toilet paper in the bottom right drawer. ", "Turn around, walk to the wall. ", "Take the toilet paper off of the holder. ", "Turn around, walk back to the counter.", "Put the toilet paper in the bottom drawer next to the first roll of toilet paper. "], "task_desc": "Put two rolls of toilet paper in the drawer. ", "votes": [1, 1]}]}}