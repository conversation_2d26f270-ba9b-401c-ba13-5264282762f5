{"pddl_domain": ";; Specification in PDDL of the Alfred domain\n;; Intended to be used with Fast Downward which supports PDDL 2.2 level 1 plus the :action-costs requirement from PDDL 3.1.\n\n(define (domain alfred)\n (:requirements\n    :adl\n    :action-costs\n    :typing\n )\n (:types\n  agent\n  location\n  receptacle\n  object\n  rtype\n  otype\n  )\n\n\n (:predicates\n    (atLocation ?a - agent ?l - location)                     ; true if the agent is at the location\n    (receptacleAtLocation ?r - receptacle ?l - location)      ; true if the receptacle is at the location (constant)\n    (objectAtLocation ?o - object ?l - location)              ; true if the object is at the location\n    (openable ?r - receptacle)                                ; true if a receptacle is openable\n    (opened ?r - receptacle)                                  ; true if a receptacle is opened\n    (inReceptacle ?o - object ?r - receptacle)                ; object ?o is in receptacle ?r\n    (isReceptacleObject ?o - object)                          ; true if the object can have things put inside it\n    (inReceptacleObject ?innerObject - object ?outerObject - object)                ; object ?innerObject is inside object ?outerObject\n    (isReceptacleObjectFull ?o - object)                      ; true if the receptacle object contains something\n    (wasInReceptacle ?o - object ?r - receptacle)             ; object ?o was or is in receptacle ?r now or some time in the past\n    (checked ?r - receptacle)                                 ; whether the receptacle has been looked inside/visited\n    (examined ?l - location)                                  ; TODO\n    (receptacleType ?r - receptacle ?t - rtype)               ; the type of receptacle (Cabinet vs Cabinet|01|2...)\n    (canContain ?rt - rtype ?ot - otype)                      ; true if receptacle can hold object\n    (objectType ?o - object ?t - otype)                       ; the type of object (Apple vs Apple|01|2...)\n    (holds ?a - agent ?o - object)                            ; object ?o is held by agent ?a\n    (holdsAny ?a - agent)                                     ; agent ?a holds an object\n    (holdsAnyReceptacleObject ?a - agent)                        ; agent ?a holds a receptacle object\n    (full ?r - receptacle)                                    ; true if the receptacle has no remaining space\n    (isClean ?o - object)                                     ; true if the object has been clean in sink\n    (cleanable ?o - object)                                   ; true if the object can be placed in a sink\n    (isHot ?o - object)                                       ; true if the object has been heated up\n    (heatable ?o - object)                                    ; true if the object can be heated up in a microwave\n    (isCool ?o - object)                                      ; true if the object has been cooled\n    (coolable ?o - object)                                    ; true if the object can be cooled in the fridge\n    (pickupable ?o - object)                                   ; true if the object can be picked up\n    (moveable ?o - object)                                      ; true if the object can be moved\n    (toggleable ?o - object)                                  ; true if the object can be turned on/off\n    (isOn ?o - object)                                        ; true if the object is on\n    (isToggled ?o - object)                                   ; true if the object has been toggled\n    (sliceable ?o - object)                                   ; true if the object can be sliced\n    (isSliced ?o - object)                                    ; true if the object is sliced\n )\n\n  (:functions\n    (distance ?from ?to)\n    (total-cost) - number\n   )\n\n;; All actions are specified such that the final arguments are the ones used\n;; for performing actions in Unity.\n\n\n(:action look\n    :parameters (?a - agent ?l - location)\n    :precondition\n        (and\n            (atLocation ?a ?l)\n        )\n    :effect\n        (and\n            (checked ?l)\n        )\n)\n\n(:action inventory\n    :parameters (?a - agent)\n    :precondition\n        ()\n    :effect\n        (and\n            (checked ?a)\n        )\n)\n\n(:action examineReceptacle\n    :parameters (?a - agent ?r - receptacle)\n    :precondition\n        (and\n            (exists (?l - location)\n                (and\n                    (atLocation ?a ?l)\n                    (receptacleAtLocation ?r ?l)\n                )\n            )\n        )\n    :effect\n        (and\n            (checked ?r)\n        )\n)\n\n(:action examineObject\n    :parameters (?a - agent ?o - object)\n    :precondition\n        (or\n            ;(exists (?l - location)\n            ;    (and\n            ;        (atLocation ?a ?l)\n            ;        (objectAtLocation ?o ?l)\n            ;    )\n            ;)\n            (exists (?l - location, ?r - receptacle)\n                (and\n                    (atLocation ?a ?l)\n                    (receptacleAtLocation ?r ?l)\n                    ; (objectAtLocation ?o ?l)\n                    (inReceptacle ?o ?r)\n                    (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n              )\n            )\n            (holds ?a ?o)\n        )\n    :effect\n        (and\n            (checked ?o)\n        )\n)\n\n;; agent goes to receptacle\n (:action GotoLocation\n    :parameters (?a - agent ?lStart - location ?lEnd - location ?r - receptacle)\n    :precondition (and\n                    (atLocation ?a ?lStart)\n                    (receptacleAtLocation ?r ?lEnd)\n                    ;(exists (?r - receptacle) (receptacleAtLocation ?r ?lEnd))\n                  )\n    :effect (and\n                (not (atLocation ?a ?lStart))\n                (atLocation ?a ?lEnd)\n                ; (forall (?r - receptacle)\n                ;     (when (and (receptacleAtLocation ?r ?lEnd)\n                ;                (or (not (openable ?r)) (opened ?r)))\n                ;         (checked ?r)\n                ;     )\n                ; )\n                ; (increase (total-cost) (distance ?lStart ?lEnd))\n                (increase (total-cost) 1)\n            )\n )\n\n;; agent opens receptacle\n (:action OpenObject\n    :parameters (?a - agent ?l - location ?r - receptacle)\n    :precondition (and\n            (openable ?r)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (not (opened ?r))\n            )\n    :effect (and\n                (opened ?r)\n                (checked ?r)\n                (increase (total-cost) 1)\n            )\n )\n;; agent closes receptacle\n (:action CloseObject\n    :parameters (?a - agent ?l - location ?r - receptacle)\n    :precondition (and\n            (openable ?r)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (opened ?r)\n            )\n    :effect (and\n                (not (opened ?r))\n                (increase (total-cost) 1)\n            )\n\n )\n\n ;; agent picks up object from a receptacle\n (:action PickupObject\n    :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n    :precondition\n        (and\n            (pickupable ?o)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            ; (objectAtLocation ?o ?l)\n            (inReceptacle ?o ?r)\n            (not (holdsAny ?a))  ; agent's hands are empty.\n            ;(not (holdsAnyReceptacleObject ?a))\n            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n            ;(not (isReceptacleObject ?o))\n        )\n    :effect\n        (and\n            (not (inReceptacle ?o ?r))\n            (holds ?a ?o)\n            (holdsAny ?a)\n            (not (objectAtLocation ?o ?l))\n            ;(not (full ?r))\n            (increase (total-cost) 1)\n        )\n )\n\n\n; ;; agent picks up object from a receptacle\n; (:action PickupObjectFromReceptacleObject\n;    :parameters (?a - agent ?l - location ?o - object ?outerR - object ?r - receptacle)\n;    :precondition\n;        (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (inReceptacle ?o ?r)\n;            (pickupable ?o)\n;            (not (holdsAny ?a))  ; agent's hands are empty.\n;            (not (holdsAnyReceptacleObject ?a))\n;            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n;            (not (isReceptacleObject ?o))\n;            (isReceptacleObject ?outerR)\n;            (inReceptacleObject ?o ?outerR)\n;        )\n;    :effect\n;        (and\n;            (not (inReceptacle ?o ?r))\n;            (holds ?a ?o)\n;            (holdsAny ?a)\n;            (not (objectAtLocation ?o ?l))\n;            (not (full ?r))\n;            (increase (total-cost) 1)\n;\n;            (not (inReceptacleObject ?o ?outerR))\n;            (not (isReceptacleObjectFull ?outerR))\n;        )\n; )\n;\n; ;; agent picks up object from a receptacle\n; (:action PickupEmptyReceptacleObject\n;    :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n;    :precondition\n;        (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            ; (objectAtLocation ?o ?l)\n;            (inReceptacle ?o ?r)\n;            (pickupable ?o)\n;            (not (holdsAny ?a))  ; agent's hands are empty.\n;            (not (holdsAnyReceptacleObject ?a))\n;            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n;            (isReceptacleObject ?o)\n;            (not (isReceptacleObjectFull ?o))\n;        )\n;    :effect\n;        (and\n;            (not (inReceptacle ?o ?r))\n;            (holds ?a ?o)\n;            (holdsAny ?a)\n;            (not (objectAtLocation ?o ?l))\n;            (not (full ?r))\n;            (increase (total-cost) 1)\n;            (holdsAnyReceptacleObject ?a)\n;        )\n; )\n;\n; ;; agent picks up object from a receptacle\n; (:action PickupFullReceptacleObject\n;    :parameters (?a - agent ?l - location ?o - object ?outerR - object ?r - receptacle)\n;    :precondition\n;        (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (inReceptacle ?outerR ?r)\n;            (pickupable ?outerR)\n;            (not (holdsAny ?a))  ; agent's hands are empty.\n;            (not (holdsAnyReceptacleObject ?a))\n;            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n;            (not (isReceptacleObject ?o))\n;            (isReceptacleObject ?outerR)\n;            (inReceptacleObject ?o ?outerR)\n;        )\n;    :effect\n;        (and\n;            (not (inReceptacle ?o ?r))\n;            (not (inReceptacle ?outerR ?r))\n;            (holds ?a ?outerR)\n;            (holdsAny ?a)\n;            (not (objectAtLocation ?o ?l))\n;            (not (objectAtLocation ?outerR ?l))\n;            (not (full ?r))\n;            (increase (total-cost) 1)\n;            (holdsAnyReceptacleObject ?a)\n;        )\n; )\n\n\n;; agent puts down an object\n (:action PutObject\n    :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n    :precondition (and\n            (holds ?a ?o)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (or (not (openable ?r)) (opened ?r))    ; receptacle is opened if it is openable\n            ;(not (full ?r))\n            (objectType ?o ?ot)\n            (receptacleType ?r ?rt)\n            (canContain ?rt ?ot)\n            ;(not (holdsAnyReceptacleObject ?a))\n            )\n    :effect (and\n                (inReceptacle ?o ?r)\n                (objectAtLocation ?o ?l)\n                ;(full ?r)\n                (not (holds ?a ?o))\n                (not (holdsAny ?a))\n                (increase (total-cost) 1)\n            )\n )\n\n;;; agent puts down an object\n; (:action PutObjectInReceptacleObject\n;    :parameters (?a - agent ?l - location ?ot - otype ?o - object ?outerO - object ?outerR - receptacle)\n;    :precondition (and\n;            (atLocation ?a ?l)\n;            (objectAtLocation ?outerO ?l)\n;            (isReceptacleObject ?outerO)\n;            (not (isReceptacleObject ?o))\n;            (objectType ?o ?ot)\n;            (holds ?a ?o)\n;            (not (holdsAnyReceptacleObject ?a))\n;            (inReceptacle ?outerO ?outerR)\n;            (not (isReceptacleObjectFull ?outerO))\n;            )\n;    :effect (and\n;                (inReceptacleObject ?o ?outerO)\n;                (inReceptacle ?o ?outerR)\n;                (not (holds ?a ?o))\n;                (not (holdsAny ?a))\n;                (objectAtLocation ?o ?l)\n;                (isReceptacleObjectFull ?outerO)\n;                (increase (total-cost) 1)\n;            )\n; )\n;\n;;; agent puts down an object\n; (:action PutEmptyReceptacleObjectinReceptacle\n;    :parameters (?a - agent ?l - location ?ot - otype ?o - object ?r - receptacle)\n;    :precondition (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (or (not (openable ?r)) (opened ?r))    ; receptacle is opened if it is openable\n;            (not (full ?r))\n;            (objectType ?o ?ot)\n;            (holds ?a ?o)\n;            (holdsAnyReceptacleObject ?a)\n;            (isReceptacleObject ?o)\n;            (not (isReceptacleObjectFull ?o))\n;            )\n;    :effect (and\n;                (inReceptacle ?o ?r)\n;                (objectAtLocation ?o ?l)\n;                (full ?r)\n;                (not (holds ?a ?o))\n;                (not (holdsAny ?a))\n;                (not (holdsAnyReceptacleObject ?a))\n;                (increase (total-cost) 1)\n;            )\n; )\n;\n;;; agent puts down a receptacle object in a receptacle\n; (:action PutFullReceptacleObjectInReceptacle\n;    :parameters (?a - agent ?l - location ?ot - otype ?innerO - object ?outerO - object ?r - receptacle) ; ?rt - rtype)\n;    :precondition (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (objectType ?outerO ?ot)\n;            (holds ?a ?outerO)\n;            (holdsAnyReceptacleObject ?a)\n;            (isReceptacleObject ?outerO)\n;            (isReceptacleObjectFull ?outerO)\n;            (inReceptacleObject ?innerO ?outerO)\n;            )\n;    :effect (and\n;                (not (holdsAny ?a))\n;                (not (holdsAnyReceptacleObject ?a))\n;                (objectAtLocation ?outerO ?l)\n;                (objectAtLocation ?innerO ?l)\n;                (inReceptacle ?outerO ?r)\n;                (inReceptacle ?innerO ?r)\n;                (not (holds ?a ?outerO))\n;                (increase (total-cost) 1)\n;            )\n; )\n\n;; agent cleans some object\n (:action CleanObject\n    :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n    :precondition (and\n            (cleanable ?o)\n            (or\n                (receptacleType ?r SinkType)\n                (receptacleType ?r SinkBasinType)\n            )\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (holds ?a ?o)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isClean ?o)\n            )\n )\n\n\n;; agent heats-up some object\n (:action HeatObject\n    :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n    :precondition (and\n            (heatable ?o)\n            (or\n                (receptacleType ?r MicrowaveType)\n            )\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (holds ?a ?o)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isHot ?o)\n                (not (isCool ?o))\n            )\n )\n\n;; agent cools some object\n (:action CoolObject\n    :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n    :precondition (and\n            (coolable ?o)\n            (or\n                (receptacleType ?r FridgeType)\n            )\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (holds ?a ?o)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isCool ?o)\n                (not (isHot ?o))\n            )\n )\n\n\n;; agent toggle object\n (:action ToggleObject\n    :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n    :precondition (and\n            (toggleable ?o)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (inReceptacle ?o ?r)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (when (isOn ?o)\n                    (not (isOn ?o)))\n                (when (not (isOn ?o))\n                    (isOn ?o))\n                (isToggled ?o)\n            )\n )\n\n\n;; agent slices some object with a knife\n (:action SliceObject\n    :parameters (?a - agent ?l - location ?co - object ?ko - object)\n    :precondition\n            (and\n                (sliceable ?co)\n                (or\n                    (objectType ?ko KnifeType)\n                    (objectType ?ko ButterKnifeType)\n                )\n                (atLocation ?a ?l)\n                (objectAtLocation ?co ?l)\n                (holds ?a ?ko)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isSliced ?co)\n            )\n )\n\n\n(:action help\n    :parameters (?a - agent)\n    :precondition\n        ()\n    :effect\n        (and\n            (checked ?a)\n        )\n)\n)\n", "grammar": "grammar :: \"\"\"\n    {\n        \"intro\": [\n            {\n                \"rhs\": \"-= Welcome to TextWorld, ALFRED! =-\\n\\n#look.feedback#\\n\\n#task#\"\n            }\n        ],\n\n        \"notImplemented\": [\n            {\n                \"rhs\": \"TODO\"\n            }\n        ],\n\n        \"task\": [\n            {\n                \"rhs\": \"Your task is to: put two laptop in desk.\"\n            }\n        ],\n\n        \"GotoLocation.feedback\": [\n            {\n                \"rhs\": \"You arrive at {r.name}. #examineReceptacle.feedback#\"\n            }\n        ],\n\n        \"OpenObject.feedback\": [\n            {\n                \"rhs\": \"You open the {r.name}. #examineReceptacle.feedback#\"\n            }\n        ],\n\n        \"CloseObject.feedback\": [\n            {\n                \"rhs\": \"You close the {r.name}.\"\n            }\n        ],\n\n        \"PickupObject.feedback\": [\n            {\n                \"rhs\": \"You pick up the {o.name} from the {r.name}.\"\n            }\n        ],\n\n        \"PickupObjectFromReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PickupObjectFromReceptacleObject: You pick up the {o.name}.\"\n            }\n        ],\n\n        \"PickupEmptyReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PickupEmptyReceptacleObject: You pick up the {o.name}.\"\n            }\n        ],\n\n        \"PickupFullReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PickupFullReceptacleObject: You pick up the {outerr.name}.\"\n            }\n        ],\n\n        \"PutObject.feedback\": [\n            {\n                \"rhs\": \"You move the {o.name} to the {r.name}.\"\n            }\n        ],\n\n        \"PutObjectInReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PutObjectInReceptacleObject: You put the {o.name} in the {outero.name}.\"\n            }\n        ],\n\n        \"PutEmptyReceptacleObjectinReceptacle.feedback\": [\n            {\n                \"rhs\": \"PutEmptyReceptacleObjectinReceptacle: You put the {o.name} in the {r.name}.\"\n            }\n        ],\n\n        \"PutFullReceptacleObjectInReceptacle.feedback\": [\n            {\n                \"rhs\": \"PutFullReceptacleObjectInReceptacle: You put the {outero.name} in the {r.name}.\"\n            }\n        ],\n\n        \"inventory.feedback\": [\n            {\n                \"condition\": \"holdsany(a:agent)\",\n                \"rhs\": \"You are carrying: [{o.indefinite + ' ' + o.name | holds(a:agent, o:object)}].\"\n            },\n            {\n                \"rhs\": \"You are not carrying anything.\"\n            }\n        ],\n\n        \"examineReceptacle.feedback\": [\n            {\n                \"condition\": \"openable(r:receptacle) & opened(r:receptacle)\",\n                \"rhs\": \"The {r.name} is open. In it, you see [{o.indefinite + ' ' + o.name | inreceptacle(o:object, r:receptacle)}].\"\n            },\n            {\n                \"condition\": \"openable(r:receptacle)\",\n                \"rhs\": \"The {r.name} is closed.\"\n            },\n            {\n                \"rhs\": \"On the {r.name}, you see [{o.indefinite + ' ' + o.name | inreceptacle(o:object, r:receptacle)}].\"\n            }\n        ],\n\n        \"examineObject.feedback\": [\n            {\n                \"condition\": \"isreceptacleobject(o:object)\",\n                \"rhs\": \"This is a normal {o.name}. In it, you see [{o2.indefinite + ' ' + o2.name | inreceptacleobject(o2:object, o:object)}].\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & ishot(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a hot and clean sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & iscool(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a cool and clean sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a clean sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"ishot(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a hot sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"iscool(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a cool sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & ishot(o:object)\",\n                \"rhs\": \"This is a hot and clean {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & iscool(o:object)\",\n                \"rhs\": \"This is a cool and clean {o.name}.\"\n            },\n            {\n                \"condition\": \"ishot(o:object)\",\n                \"rhs\": \"This is a hot {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object)\",\n                \"rhs\": \"This is a clean {o.name}.\"\n            },\n            {\n                \"condition\": \"iscool(o:object)\",\n                \"rhs\": \"This is a cold {o.name}.\"\n            },\n            {\n                \"condition\": \"toggleable(o:object) & istoggled(o:object)\",\n                \"rhs\": \"This {o.name} is on.\"\n            },\n            {\n                \"condition\": \"toggleable(o:object) & not_istoggled(o:object)\",\n                \"rhs\": \"This {o.name} is off.\"\n            },\n            {\n                \"condition\": \"sliceable(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a sliced {o.name}.\"\n            },\n            {\n                \"rhs\": \"There's nothing special about {o.name}.\"\n            }\n        ]\n    }\n\"\"\";\n\naction GotoLocation {\n    template :: \"go to [{r.name | receptacleatlocation(r:receptacle, lend:location)}]\";\n    feedback :: \"#GotoLocation.feedback#\";\n}\n\naction OpenObject {\n    template :: \"open {r}\";\n    feedback :: \"#OpenObject.feedback#\";\n}\n\naction CloseObject {\n    template :: \"close {r}\";\n    feedback :: \"#CloseObject.feedback#\";\n}\n\naction PickupObject {\n    template :: \"take {o} from {r}\";\n    feedback :: \"#PickupObject.feedback#\";\n}\n\naction PickupObjectFromReceptacleObject {\n    template :: \"take {o} from {r}\";\n    feedback :: \"#PickupObjectFromReceptacleObject.feedback#\";\n}\n\naction PickupEmptyReceptacleObject {\n    template :: \"take {o} from {r}\";\n    feedback :: \"#PickupEmptyReceptacleObject.feedback#\";\n}\n\naction PickupFullReceptacleObject {\n    template :: \"take {outerr} from {r}\";\n    feedback :: \"#PickupFullReceptacleObject.feedback#\";\n}\n\naction PutObject {\n    template :: \"move {o} to {r}\";\n    feedback :: \"#PutObject.feedback#\";\n}\n\naction PutObjectInReceptacleObject {\n    template :: \"put {o} into {outero}\";\n    feedback :: \"#PutObjectInReceptacleObject.feedback#\";\n}\n\naction PutEmptyReceptacleObjectinReceptacle {\n    template :: \"move {o} to {r}\";\n    feedback :: \"#PutEmptyReceptacleObjectinReceptacle.feedback#\";\n}\n\naction PutFullReceptacleObjectInReceptacle {\n    template :: \"put {outero} in {r}\";\n    feedback :: \"#PutFullReceptacleObjectInReceptacle.feedback#\";\n}\n\naction inventory {\n    template :: \"inventory\";\n    feedback :: \"#inventory.feedback#\";\n}\n\naction examineReceptacle {\n    template :: \"examine {r}\";\n    feedback :: \"#examineReceptacle.feedback#\";\n}\n\naction examineObject {\n    template :: \"examine {o}\";\n    feedback :: \"#examineObject.feedback#\";\n}\n\naction ToggleObject {\n    template :: \"use {o}\";\n    feedback :: \"#toggleObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"toggleObject.feedback\": [\n                {\n                    \"condition\": \"toggleable(o:object) & istoggled(o:object)\",\n                    \"rhs\": \"You turn on the {o.name}.\"\n                },\n                {\n                    \"condition\": \"toggleable(o:object)\",\n                    \"rhs\": \"You turn off the {o.name}.\"\n                },\n                {\n                    \"rhs\": \"You don't see any switch on the {o.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction HeatObject {\n    template :: \"heat {o} with {r}\";\n    feedback :: \"#heatObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"heatObject.feedback\": [\n                {\n                    \"rhs\": \"You heat the {o.name} using the {r.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction CleanObject {\n    template :: \"clean {o} with {r}\";\n    feedback :: \"#cleanObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"cleanObject.feedback\": [\n                {\n                    \"rhs\": \"You clean the {o.name} using the {r.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction CoolObject {\n    template :: \"cool {o} with {r}\";\n    feedback :: \"#coolObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"coolObject.feedback\": [\n                {\n                    \"rhs\": \"You cool the {o.name} using the {r.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction SliceObject {\n    template :: \"slice {co} with {ko}\";\n    feedback :: \"#sliceObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"sliceObject.feedback\": [\n                {\n                    \"rhs\": \"You sliced the {co.name} with the {ko.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction look {\n    template :: \"look\";\n    feedback :: \"#look.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"look.feedback\": [\n                {\n                   \"condition\": \"atlocation(a:agent, l:location) & receptacleatlocation(r:receptacle, l:location)\",\n                   \"rhs\": \"#look-variations#. Next to it, you see #list_objects_on_the_floor#.\"\n                },\n                {\n                    \"rhs\": \"You are in the middle of a room. Looking quickly around you, you see #list_appliances#.\"\n                }\n            ],\n\n            \"look-variations\": [\n                {\n                    \"rhs\": \"You are facing the [{r.name | atlocation(a:agent, l:location) & receptacleatlocation(r:receptacle, l:location)}]\"\n                }\n            ],\n\n            \"list_objects_on_the_floor\": [\n                {\n                    \"condition\": \"atlocation(a:agent, l:location) & objectatlocation(o:object, l:location) & receptacleatlocation(r:receptacle, l:location) & not_inreceptacle(o:object, r:receptacle)\",\n                    \"rhs\": \"[{#overview(o)# | atlocation(a:agent, l:location) & objectatlocation(o:object, l:location) & receptacleatlocation(r:receptacle, l:location) & not_inreceptacle(o:object, r:receptacle)}]\"\n                },\n                {\n                    \"rhs\": \"nothing\"\n                }\n            ],\n\n            \"list_appliances\": [\n                {\n                    \"condition\": \"receptacleatlocation(r:receptacle, l:location)\",\n                    \"rhs\": \"[{#overview(r)# | receptacleatlocation(r:receptacle, l:location)}]\"\n                },\n                {\n                    \"rhs\": \"nothing\"\n                }\n            ],\n\n            \"overview(o, r)\": [\n                {\n                    \"rhs\": \"{o.indefinite} {o.name} (in/on the {r.name})}\"\n                }\n            ],\n\n            \"overview(o)\": [\n                {\n                    \"rhs\": \"{o.indefinite} {o.name}\"\n                }\n            ],\n            \"overview(r)\": [\n                {\n                    \"rhs\": \"{r.indefinite} {r.name or r.id}\"\n                }\n            ],\n            \"overview_with_state(r)\": [\n                {\n                    \"rhs\": \"{r.indefinite} {r.name or r.id}#overview_state(r)#\"\n                }\n            ],\n            \"overview_state(r)\": [\n                {\n                    \"condition\": \"openable(r:receptacle) & opened(r:receptacle)\",\n                    \"rhs\": \" (it is open)\"\n                },\n                {\n                    \"condition\": \"openable(r:receptacle)\",\n                    \"rhs\": \" (it is closed)\"\n                },\n                {\n                    \"rhs\": \"\"\n                }\n            ],\n\n            \"list_empty\": [\n                {\n                    \"rhs\": \"nothing\"\n                }\n            ],\n            \"list_separator\": [\n                {\n                    \"rhs\": \", \"\n                }\n            ],\n            \"list_last_separator\": [\n                {\n                    \"rhs\": \", and \"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction help {\n    template :: \"help\";\n    feedback :: \"\nAvailable commands:\n  look:                             look around your current location\n  inventory:                        check your current inventory\n  go to (receptacle):               move to a receptacle\n  open (receptacle):                open a receptacle\n  close (receptacle):               close a receptacle\n  take (object) from (receptacle):  take an object from a receptacle\n  move (object) to (receptacle):  place an object in or on a receptacle\n  examine (something):              examine a receptacle or an object\n  use (object):                     use an object\n  heat (object) with (receptacle):  heat an object using a receptacle\n  clean (object) with (receptacle): clean an object using a receptacle\n  cool (object) with (receptacle):  cool an object using a receptacle\n  slice (object) with (object):     slice an object using a sharp object\n\";\n}\n", "pddl_problem": "\n(define (problem plan_trial_T20190907_151137_619046)\n(:domain alfred)\n(:objects\nagent1 - agent\n<PERSON><PERSON><PERSON><PERSON> - object\n        HousePlant - object\n        Candle - object\n        SprayBottle - object\n        Bowl - object\n        Window - object\n        CD - object\n        Egg - object\n        Glassbottle - object\n        Sink - object\n        Pillow - object\n        Spoon - object\n        SoapBottle - object\n        TeddyBear - object\n        ButterKnife - object\n        Cup - object\n        Plate - object\n        RemoteControl - object\n        Tomato - object\n        Statue - object\n        HandTowel - object\n        Knife - object\n        StoveKnob - object\n        LightSwitch - object\n        Pen - object\n        Painting - object\n        DishSponge - object\n        Vase - object\n        Mug - object\n        ToiletPaper - object\n        Box - object\n        Mirror - object\n        Ladle - object\n        Fork - object\n        Blinds - object\n        Footstool - object\n        KeyChain - object\n        Cloth - object\n        Laptop - object\n        TissueBox - object\n        PepperShaker - object\n        FloorLamp - object\n        WateringCan - object\n        Apple - object\n        Pan - object\n        PaperTowel - object\n        PaperTowelRoll - object\n        Newspaper - object\n        Television - object\n        Chair - object\n        CellPhone - object\n        CreditCard - object\n        Lettuce - object\n        BasketBall - object\n        Potato - object\n        Curtains - object\n        Boots - object\n        Pencil - object\n        AlarmClock - object\n        ToiletPaperRoll - object\n        Spatula - object\n        Book - object\n        Bread - object\n        SoapBar - object\n        Watch - object\n        DeskLamp - object\n        Kettle - object\n        Pot - object\n        ScrubBrush - object\n        WineBottle - object\n        ShowerDoor - object\n        Bathtub - object\n        LaundryHamperLid - object\n        ShowerGlass - object\n        Poster - object\n        TennisRacket - object\n        BaseballBat - object\n        Towel - object\n        Plunger - object\nSaltShakerType - otype\n        HousePlantType - otype\n        CandleType - otype\n        SprayBottleType - otype\n        BowlType - otype\n        WindowType - otype\n        CDType - otype\n        EggType - otype\n        GlassbottleType - otype\n        SinkType - otype\n        PillowType - otype\n        SpoonType - otype\n        SoapBottleType - otype\n        TeddyBearType - otype\n        ButterKnifeType - otype\n        CupType - otype\n        PlateType - otype\n        RemoteControlType - otype\n        TomatoType - otype\n        StatueType - otype\n        HandTowelType - otype\n        KnifeType - otype\n        StoveKnobType - otype\n        LightSwitchType - otype\n        PenType - otype\n        PaintingType - otype\n        DishSpongeType - otype\n        VaseType - otype\n        MugType - otype\n        ToiletPaperType - otype\n        BoxType - otype\n        MirrorType - otype\n        LadleType - otype\n        ForkType - otype\n        BlindsType - otype\n        FootstoolType - otype\n        KeyChainType - otype\n        ClothType - otype\n        LaptopType - otype\n        TissueBoxType - otype\n        PepperShakerType - otype\n        FloorLampType - otype\n        WateringCanType - otype\n        AppleType - otype\n        PanType - otype\n        PaperTowelType - otype\n        PaperTowelRollType - otype\n        NewspaperType - otype\n        TelevisionType - otype\n        ChairType - otype\n        CellPhoneType - otype\n        CreditCardType - otype\n        LettuceType - otype\n        BasketBallType - otype\n        PotatoType - otype\n        CurtainsType - otype\n        BootsType - otype\n        PencilType - otype\n        AlarmClockType - otype\n        ToiletPaperRollType - otype\n        SpatulaType - otype\n        BookType - otype\n        BreadType - otype\n        SoapBarType - otype\n        WatchType - otype\n        DeskLampType - otype\n        KettleType - otype\n        PotType - otype\n        ScrubBrushType - otype\n        WineBottleType - otype\n        ShowerDoorType - otype\n        BathtubType - otype\n        LaundryHamperLidType - otype\n        ShowerGlassType - otype\n        PosterType - otype\n        TennisRacketType - otype\n        BaseballBatType - otype\n        TowelType - otype\n        PlungerType - otype\nSafeType - rtype\n        DrawerType - rtype\n        CoffeeMachineType - rtype\n        HandTowelHolderType - rtype\n        SinkBasinType - rtype\n        DresserType - rtype\n        LaundryHamperType - rtype\n        TVStandType - rtype\n        BathtubBasinType - rtype\n        CabinetType - rtype\n        FridgeType - rtype\n        DeskType - rtype\n        ToiletType - rtype\n        CartType - rtype\n        SideTableType - rtype\n        SofaType - rtype\n        CoffeeTableType - rtype\n        DiningTableType - rtype\n        CounterTopType - rtype\n        GarbageCanType - rtype\n        ArmChairType - rtype\n        ShelfType - rtype\n        MicrowaveType - rtype\n        ToasterType - rtype\n        BedType - rtype\n        PaintingHangerType - rtype\n        TowelHolderType - rtype\n        ToiletPaperHangerType - rtype\n        StoveBurnerType - rtype\n        OttomanType - rtype\n\n\n        AlarmClock_bar__plus_01_dot_55_bar__plus_00_dot_55_bar__plus_02_dot_32 - object\n        Blinds_bar__minus_00_dot_48_bar__plus_02_dot_79_bar__minus_03_dot_25 - object\n        Blinds_bar__minus_01_dot_96_bar__plus_02_dot_79_bar__minus_03_dot_25 - object\n        Blinds_bar__minus_03_dot_80_bar__plus_02_dot_51_bar__plus_00_dot_60 - object\n        Blinds_bar__minus_03_dot_80_bar__plus_02_dot_51_bar__minus_00_dot_65 - object\n        Book_bar__minus_00_dot_54_bar__plus_00_dot_08_bar__minus_02_dot_93 - object\n        Book_bar__minus_02_dot_63_bar__plus_00_dot_08_bar__minus_02_dot_90 - object\n        CD_bar__minus_02_dot_87_bar__plus_00_dot_08_bar__minus_03_dot_06 - object\n        CellPhone_bar__minus_00_dot_37_bar__plus_00_dot_09_bar__minus_02_dot_83 - object\n        Chair_bar__minus_02_dot_07_bar__plus_00_dot_04_bar__minus_02_dot_69 - object\n        CreditCard_bar__minus_03_dot_26_bar__plus_00_dot_19_bar__plus_03_dot_51 - object\n        DeskLamp_bar__plus_01_dot_87_bar__plus_00_dot_55_bar__plus_02_dot_42 - object\n        HousePlant_bar__minus_00_dot_20_bar__plus_01_dot_10_bar__minus_03_dot_08 - object\n        KeyChain_bar__plus_01_dot_61_bar__plus_00_dot_55_bar__plus_02_dot_44 - object\n        Laptop_bar__plus_00_dot_10_bar__plus_00_dot_83_bar__plus_00_dot_98 - object\n        Laptop_bar__plus_00_dot_67_bar__plus_00_dot_83_bar__plus_00_dot_98 - object\n        LaundryHamperLid_bar__plus_01_dot_83_bar__plus_00_dot_79_bar__plus_03_dot_69 - object\n        LightSwitch_bar__minus_00_dot_16_bar__plus_01_dot_28_bar__plus_04_dot_00 - object\n        Mirror_bar__minus_01_dot_60_bar__plus_01_dot_45_bar__plus_04_dot_00 - object\n        Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91 - object\n        Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74 - object\n        Pencil_bar__minus_00_dot_19_bar__plus_00_dot_09_bar__minus_02_dot_93 - object\n        Pencil_bar__minus_00_dot_28_bar__plus_00_dot_59_bar__minus_02_dot_83 - object\n        Pencil_bar__minus_03_dot_40_bar__plus_00_dot_38_bar__minus_02_dot_84 - object\n        Pen_bar__minus_01_dot_07_bar__plus_00_dot_59_bar__minus_02_dot_88 - object\n        Pillow_bar__plus_01_dot_24_bar__plus_00_dot_94_bar__plus_00_dot_98 - object\n        Pillow_bar__plus_01_dot_82_bar__plus_00_dot_87_bar__plus_01_dot_15 - object\n        RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_56_bar__plus_02_dot_39 - object\n        RemoteControl_bar__minus_01_dot_01_bar__plus_00_dot_19_bar__plus_03_dot_48 - object\n        RemoteControl_bar__minus_03_dot_43_bar__plus_00_dot_19_bar__plus_03_dot_31 - object\n        TeddyBear_bar__plus_01_dot_87_bar__plus_00_dot_04_bar__minus_00_dot_56 - object\n        Television_bar__minus_03_dot_88_bar__plus_01_dot_77_bar__plus_02_dot_12 - object\n        Window_bar__minus_00_dot_47_bar__plus_02_dot_15_bar__minus_03_dot_30 - object\n        Window_bar__minus_01_dot_95_bar__plus_02_dot_15_bar__minus_03_dot_30 - object\n        Window_bar__minus_03_dot_89_bar__plus_01_dot_75_bar__plus_00_dot_58 - object\n        Window_bar__minus_03_dot_89_bar__plus_01_dot_75_bar__minus_00_dot_68 - object\n        ArmChair_bar__minus_00_dot_94_bar__plus_00_dot_00_bar__plus_03_dot_61 - receptacle\n        ArmChair_bar__minus_02_dot_17_bar__plus_00_dot_00_bar__plus_03_dot_63 - receptacle\n        ArmChair_bar__minus_03_dot_37_bar__plus_00_dot_00_bar__plus_03_dot_38 - receptacle\n        Bed_bar__plus_00_dot_99_bar__plus_00_dot_03_bar__plus_00_dot_67 - receptacle\n        Cabinet_bar__plus_02_dot_06_bar__plus_01_dot_50_bar__minus_02_dot_64 - receptacle\n        Cabinet_bar__minus_03_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64 - receptacle\n        Desk_bar__minus_01_dot_27_bar__plus_01_dot_17_bar__minus_02_dot_99 - receptacle\n        Drawer_bar__minus_00_dot_37_bar__plus_00_dot_29_bar__minus_02_dot_85 - receptacle\n        Drawer_bar__minus_00_dot_37_bar__plus_00_dot_79_bar__minus_02_dot_85 - receptacle\n        Drawer_bar__minus_01_dot_16_bar__plus_00_dot_29_bar__minus_02_dot_85 - receptacle\n        Drawer_bar__minus_01_dot_16_bar__plus_00_dot_79_bar__minus_02_dot_85 - receptacle\n        GarbageCan_bar__minus_03_dot_34_bar__plus_00_dot_04_bar__minus_02_dot_80 - receptacle\n        LaundryHamper_bar__plus_01_dot_83_bar__plus_00_dot_04_bar__plus_03_dot_69 - receptacle\n        Safe_bar__plus_01_dot_72_bar__plus_00_dot_05_bar__minus_03_dot_05 - receptacle\n        SideTable_bar__plus_01_dot_78_bar__plus_00_dot_02_bar__plus_02_dot_38 - receptacle\n        loc_bar__minus_13_bar__minus_9_bar_2_bar_60 - location\n        loc_bar__minus_11_bar__minus_8_bar_2_bar_15 - location\n        loc_bar__minus_3_bar_10_bar_0_bar_60 - location\n        loc_bar__minus_13_bar_2_bar_3_bar__minus_30 - location\n        loc_bar__minus_2_bar__minus_9_bar_2_bar__minus_30 - location\n        loc_bar__minus_13_bar_11_bar_0_bar_60 - location\n        loc_bar__minus_13_bar__minus_3_bar_3_bar__minus_30 - location\n        loc_bar__minus_2_bar_3_bar_1_bar_45 - location\n        loc_bar__minus_5_bar__minus_9_bar_1_bar_45 - location\n        loc_bar_7_bar_12_bar_0_bar_60 - location\n        loc_bar__minus_8_bar_10_bar_0_bar_60 - location\n        loc_bar_4_bar__minus_8_bar_1_bar_15 - location\n        loc_bar_6_bar_13_bar_2_bar_60 - location\n        loc_bar__minus_13_bar_2_bar_3_bar_0 - location\n        loc_bar__minus_9_bar_12_bar_3_bar_60 - location\n        loc_bar__minus_7_bar__minus_9_bar_2_bar_60 - location\n        loc_bar__minus_13_bar_8_bar_3_bar_0 - location\n        loc_bar__minus_5_bar__minus_9_bar_2_bar_60 - location\n        loc_bar__minus_6_bar_13_bar_0_bar_15 - location\n        loc_bar__minus_1_bar_14_bar_0_bar_45 - location\n        loc_bar__minus_4_bar__minus_9_bar_2_bar_30 - location\n        loc_bar_7_bar_12_bar_0_bar_45 - location\n        loc_bar__minus_4_bar_12_bar_0_bar_60 - location\n        loc_bar__minus_7_bar__minus_9_bar_2_bar__minus_30 - location\n        loc_bar__minus_13_bar__minus_3_bar_3_bar_0 - location\n        loc_bar_7_bar__minus_8_bar_2_bar_60 - location\n        loc_bar_5_bar__minus_8_bar_2_bar_60 - location\n        loc_bar_7_bar__minus_4_bar_0_bar_60 - location\n        loc_bar__minus_6_bar__minus_9_bar_1_bar_45 - location\n        loc_bar__minus_11_bar__minus_8_bar_2_bar_60 - location\n        loc_bar__minus_5_bar_11_bar_3_bar_30 - location\n        )\n    \n\n(:init\n\n\n        (receptacleType LaundryHamper_bar__plus_01_dot_83_bar__plus_00_dot_04_bar__plus_03_dot_69 LaundryHamperType)\n        (receptacleType Drawer_bar__minus_00_dot_37_bar__plus_00_dot_29_bar__minus_02_dot_85 DrawerType)\n        (receptacleType Safe_bar__plus_01_dot_72_bar__plus_00_dot_05_bar__minus_03_dot_05 SafeType)\n        (receptacleType Bed_bar__plus_00_dot_99_bar__plus_00_dot_03_bar__plus_00_dot_67 BedType)\n        (receptacleType Desk_bar__minus_01_dot_27_bar__plus_01_dot_17_bar__minus_02_dot_99 DeskType)\n        (receptacleType Drawer_bar__minus_01_dot_16_bar__plus_00_dot_79_bar__minus_02_dot_85 DrawerType)\n        (receptacleType Drawer_bar__minus_01_dot_16_bar__plus_00_dot_29_bar__minus_02_dot_85 DrawerType)\n        (receptacleType GarbageCan_bar__minus_03_dot_34_bar__plus_00_dot_04_bar__minus_02_dot_80 GarbageCanType)\n        (receptacleType Drawer_bar__minus_00_dot_37_bar__plus_00_dot_79_bar__minus_02_dot_85 DrawerType)\n        (receptacleType ArmChair_bar__minus_03_dot_37_bar__plus_00_dot_00_bar__plus_03_dot_38 ArmChairType)\n        (receptacleType ArmChair_bar__minus_02_dot_17_bar__plus_00_dot_00_bar__plus_03_dot_63 ArmChairType)\n        (receptacleType SideTable_bar__plus_01_dot_78_bar__plus_00_dot_02_bar__plus_02_dot_38 SideTableType)\n        (receptacleType ArmChair_bar__minus_00_dot_94_bar__plus_00_dot_00_bar__plus_03_dot_61 ArmChairType)\n        (receptacleType Cabinet_bar__plus_02_dot_06_bar__plus_01_dot_50_bar__minus_02_dot_64 CabinetType)\n        (receptacleType Cabinet_bar__minus_03_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64 CabinetType)\n        (objectType LightSwitch_bar__minus_00_dot_16_bar__plus_01_dot_28_bar__plus_04_dot_00 LightSwitchType)\n        (objectType Window_bar__minus_01_dot_95_bar__plus_02_dot_15_bar__minus_03_dot_30 WindowType)\n        (objectType Pillow_bar__plus_01_dot_82_bar__plus_00_dot_87_bar__plus_01_dot_15 PillowType)\n        (objectType Window_bar__minus_00_dot_47_bar__plus_02_dot_15_bar__minus_03_dot_30 WindowType)\n        (objectType Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91 MugType)\n        (objectType Chair_bar__minus_02_dot_07_bar__plus_00_dot_04_bar__minus_02_dot_69 ChairType)\n        (objectType TeddyBear_bar__plus_01_dot_87_bar__plus_00_dot_04_bar__minus_00_dot_56 TeddyBearType)\n        (objectType Pen_bar__minus_01_dot_07_bar__plus_00_dot_59_bar__minus_02_dot_88 PenType)\n        (objectType CD_bar__minus_02_dot_87_bar__plus_00_dot_08_bar__minus_03_dot_06 CDType)\n        (objectType Pencil_bar__minus_03_dot_40_bar__plus_00_dot_38_bar__minus_02_dot_84 PencilType)\n        (objectType Pencil_bar__minus_00_dot_19_bar__plus_00_dot_09_bar__minus_02_dot_93 PencilType)\n        (objectType Television_bar__minus_03_dot_88_bar__plus_01_dot_77_bar__plus_02_dot_12 TelevisionType)\n        (objectType Laptop_bar__plus_00_dot_67_bar__plus_00_dot_83_bar__plus_00_dot_98 LaptopType)\n        (objectType Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74 MugType)\n        (objectType AlarmClock_bar__plus_01_dot_55_bar__plus_00_dot_55_bar__plus_02_dot_32 AlarmClockType)\n        (objectType Blinds_bar__minus_03_dot_80_bar__plus_02_dot_51_bar__minus_00_dot_65 BlindsType)\n        (objectType Pencil_bar__minus_00_dot_28_bar__plus_00_dot_59_bar__minus_02_dot_83 PencilType)\n        (objectType Book_bar__minus_02_dot_63_bar__plus_00_dot_08_bar__minus_02_dot_90 BookType)\n        (objectType CreditCard_bar__minus_03_dot_26_bar__plus_00_dot_19_bar__plus_03_dot_51 CreditCardType)\n        (objectType Blinds_bar__minus_03_dot_80_bar__plus_02_dot_51_bar__plus_00_dot_60 BlindsType)\n        (objectType Mirror_bar__minus_01_dot_60_bar__plus_01_dot_45_bar__plus_04_dot_00 MirrorType)\n        (objectType Laptop_bar__plus_00_dot_10_bar__plus_00_dot_83_bar__plus_00_dot_98 LaptopType)\n        (objectType KeyChain_bar__plus_01_dot_61_bar__plus_00_dot_55_bar__plus_02_dot_44 KeyChainType)\n        (objectType Window_bar__minus_03_dot_89_bar__plus_01_dot_75_bar__plus_00_dot_58 WindowType)\n        (objectType RemoteControl_bar__minus_03_dot_43_bar__plus_00_dot_19_bar__plus_03_dot_31 RemoteControlType)\n        (objectType RemoteControl_bar__minus_01_dot_01_bar__plus_00_dot_19_bar__plus_03_dot_48 RemoteControlType)\n        (objectType RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_56_bar__plus_02_dot_39 RemoteControlType)\n        (objectType Book_bar__minus_00_dot_54_bar__plus_00_dot_08_bar__minus_02_dot_93 BookType)\n        (objectType HousePlant_bar__minus_00_dot_20_bar__plus_01_dot_10_bar__minus_03_dot_08 HousePlantType)\n        (objectType DeskLamp_bar__plus_01_dot_87_bar__plus_00_dot_55_bar__plus_02_dot_42 DeskLampType)\n        (objectType Pillow_bar__plus_01_dot_24_bar__plus_00_dot_94_bar__plus_00_dot_98 PillowType)\n        (objectType LaundryHamperLid_bar__plus_01_dot_83_bar__plus_00_dot_79_bar__plus_03_dot_69 LaundryHamperLidType)\n        (objectType Window_bar__minus_03_dot_89_bar__plus_01_dot_75_bar__minus_00_dot_68 WindowType)\n        (objectType Blinds_bar__minus_01_dot_96_bar__plus_02_dot_79_bar__minus_03_dot_25 BlindsType)\n        (objectType Blinds_bar__minus_00_dot_48_bar__plus_02_dot_79_bar__minus_03_dot_25 BlindsType)\n        (objectType CellPhone_bar__minus_00_dot_37_bar__plus_00_dot_09_bar__minus_02_dot_83 CellPhoneType)\n        (canContain DrawerType PenType)\n        (canContain DrawerType BookType)\n        (canContain DrawerType CDType)\n        (canContain DrawerType CellPhoneType)\n        (canContain DrawerType KeyChainType)\n        (canContain DrawerType CreditCardType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType RemoteControlType)\n        (canContain SafeType CellPhoneType)\n        (canContain SafeType KeyChainType)\n        (canContain SafeType CreditCardType)\n        (canContain SafeType CDType)\n        (canContain BedType LaptopType)\n        (canContain BedType BookType)\n        (canContain BedType CellPhoneType)\n        (canContain BedType PillowType)\n        (canContain DeskType PenType)\n        (canContain DeskType BookType)\n        (canContain DeskType CDType)\n        (canContain DeskType MugType)\n        (canContain DeskType CellPhoneType)\n        (canContain DeskType KeyChainType)\n        (canContain DeskType CreditCardType)\n        (canContain DeskType LaptopType)\n        (canContain DeskType PencilType)\n        (canContain DeskType RemoteControlType)\n        (canContain DeskType AlarmClockType)\n        (canContain DrawerType PenType)\n        (canContain DrawerType BookType)\n        (canContain DrawerType CDType)\n        (canContain DrawerType CellPhoneType)\n        (canContain DrawerType KeyChainType)\n        (canContain DrawerType CreditCardType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType RemoteControlType)\n        (canContain DrawerType PenType)\n        (canContain DrawerType BookType)\n        (canContain DrawerType CDType)\n        (canContain DrawerType CellPhoneType)\n        (canContain DrawerType KeyChainType)\n        (canContain DrawerType CreditCardType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType RemoteControlType)\n        (canContain GarbageCanType PenType)\n        (canContain GarbageCanType CDType)\n        (canContain GarbageCanType PencilType)\n        (canContain DrawerType PenType)\n        (canContain DrawerType BookType)\n        (canContain DrawerType CDType)\n        (canContain DrawerType CellPhoneType)\n        (canContain DrawerType KeyChainType)\n        (canContain DrawerType CreditCardType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType RemoteControlType)\n        (canContain ArmChairType LaptopType)\n        (canContain ArmChairType BookType)\n        (canContain ArmChairType CellPhoneType)\n        (canContain ArmChairType PillowType)\n        (canContain ArmChairType RemoteControlType)\n        (canContain ArmChairType KeyChainType)\n        (canContain ArmChairType CreditCardType)\n        (canContain ArmChairType LaptopType)\n        (canContain ArmChairType BookType)\n        (canContain ArmChairType CellPhoneType)\n        (canContain ArmChairType PillowType)\n        (canContain ArmChairType RemoteControlType)\n        (canContain ArmChairType KeyChainType)\n        (canContain ArmChairType CreditCardType)\n        (canContain SideTableType PenType)\n        (canContain SideTableType BookType)\n        (canContain SideTableType CDType)\n        (canContain SideTableType MugType)\n        (canContain SideTableType CellPhoneType)\n        (canContain SideTableType KeyChainType)\n        (canContain SideTableType CreditCardType)\n        (canContain SideTableType LaptopType)\n        (canContain SideTableType PencilType)\n        (canContain SideTableType RemoteControlType)\n        (canContain SideTableType AlarmClockType)\n        (canContain ArmChairType LaptopType)\n        (canContain ArmChairType BookType)\n        (canContain ArmChairType CellPhoneType)\n        (canContain ArmChairType PillowType)\n        (canContain ArmChairType RemoteControlType)\n        (canContain ArmChairType KeyChainType)\n        (canContain ArmChairType CreditCardType)\n        (canContain CabinetType BookType)\n        (canContain CabinetType CDType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType BookType)\n        (canContain CabinetType CDType)\n        (canContain CabinetType MugType)\n        (pickupable Pillow_bar__plus_01_dot_82_bar__plus_00_dot_87_bar__plus_01_dot_15)\n        (pickupable Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91)\n        (pickupable TeddyBear_bar__plus_01_dot_87_bar__plus_00_dot_04_bar__minus_00_dot_56)\n        (pickupable Pen_bar__minus_01_dot_07_bar__plus_00_dot_59_bar__minus_02_dot_88)\n        (pickupable CD_bar__minus_02_dot_87_bar__plus_00_dot_08_bar__minus_03_dot_06)\n        (pickupable Pencil_bar__minus_03_dot_40_bar__plus_00_dot_38_bar__minus_02_dot_84)\n        (pickupable Pencil_bar__minus_00_dot_19_bar__plus_00_dot_09_bar__minus_02_dot_93)\n        (pickupable Laptop_bar__plus_00_dot_67_bar__plus_00_dot_83_bar__plus_00_dot_98)\n        (pickupable Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74)\n        (pickupable AlarmClock_bar__plus_01_dot_55_bar__plus_00_dot_55_bar__plus_02_dot_32)\n        (pickupable Pencil_bar__minus_00_dot_28_bar__plus_00_dot_59_bar__minus_02_dot_83)\n        (pickupable Book_bar__minus_02_dot_63_bar__plus_00_dot_08_bar__minus_02_dot_90)\n        (pickupable CreditCard_bar__minus_03_dot_26_bar__plus_00_dot_19_bar__plus_03_dot_51)\n        (pickupable Laptop_bar__plus_00_dot_10_bar__plus_00_dot_83_bar__plus_00_dot_98)\n        (pickupable KeyChain_bar__plus_01_dot_61_bar__plus_00_dot_55_bar__plus_02_dot_44)\n        (pickupable RemoteControl_bar__minus_03_dot_43_bar__plus_00_dot_19_bar__plus_03_dot_31)\n        (pickupable RemoteControl_bar__minus_01_dot_01_bar__plus_00_dot_19_bar__plus_03_dot_48)\n        (pickupable RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_56_bar__plus_02_dot_39)\n        (pickupable Book_bar__minus_00_dot_54_bar__plus_00_dot_08_bar__minus_02_dot_93)\n        (pickupable Pillow_bar__plus_01_dot_24_bar__plus_00_dot_94_bar__plus_00_dot_98)\n        (pickupable CellPhone_bar__minus_00_dot_37_bar__plus_00_dot_09_bar__minus_02_dot_83)\n        (isReceptacleObject Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91)\n        (isReceptacleObject Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74)\n        (openable Drawer_bar__minus_00_dot_37_bar__plus_00_dot_29_bar__minus_02_dot_85)\n        (openable Drawer_bar__minus_01_dot_16_bar__plus_00_dot_79_bar__minus_02_dot_85)\n        (openable Drawer_bar__minus_00_dot_37_bar__plus_00_dot_79_bar__minus_02_dot_85)\n        (openable Cabinet_bar__plus_02_dot_06_bar__plus_01_dot_50_bar__minus_02_dot_64)\n        (openable Cabinet_bar__minus_03_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64)\n        \n        (atLocation agent1 loc_bar__minus_5_bar_11_bar_3_bar_30)\n        \n        (cleanable Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91)\n        (cleanable Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74)\n        \n        (heatable Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91)\n        (heatable Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74)\n        (coolable Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91)\n        (coolable Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74)\n        \n        \n        (toggleable DeskLamp_bar__plus_01_dot_87_bar__plus_00_dot_55_bar__plus_02_dot_42)\n        \n        \n        \n        \n        (inReceptacle Laptop_bar__plus_00_dot_67_bar__plus_00_dot_83_bar__plus_00_dot_98 Bed_bar__plus_00_dot_99_bar__plus_00_dot_03_bar__plus_00_dot_67)\n        (inReceptacle Pillow_bar__plus_01_dot_24_bar__plus_00_dot_94_bar__plus_00_dot_98 Bed_bar__plus_00_dot_99_bar__plus_00_dot_03_bar__plus_00_dot_67)\n        (inReceptacle Laptop_bar__plus_00_dot_10_bar__plus_00_dot_83_bar__plus_00_dot_98 Bed_bar__plus_00_dot_99_bar__plus_00_dot_03_bar__plus_00_dot_67)\n        (inReceptacle Pillow_bar__plus_01_dot_82_bar__plus_00_dot_87_bar__plus_01_dot_15 Bed_bar__plus_00_dot_99_bar__plus_00_dot_03_bar__plus_00_dot_67)\n        (inReceptacle CreditCard_bar__minus_03_dot_26_bar__plus_00_dot_19_bar__plus_03_dot_51 ArmChair_bar__minus_03_dot_37_bar__plus_00_dot_00_bar__plus_03_dot_38)\n        (inReceptacle Book_bar__minus_02_dot_63_bar__plus_00_dot_08_bar__minus_02_dot_90 Cabinet_bar__minus_03_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64)\n        (inReceptacle Pencil_bar__minus_00_dot_28_bar__plus_00_dot_59_bar__minus_02_dot_83 Drawer_bar__minus_00_dot_37_bar__plus_00_dot_79_bar__minus_02_dot_85)\n        (inReceptacle DeskLamp_bar__plus_01_dot_87_bar__plus_00_dot_55_bar__plus_02_dot_42 SideTable_bar__plus_01_dot_78_bar__plus_00_dot_02_bar__plus_02_dot_38)\n        (inReceptacle RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_56_bar__plus_02_dot_39 SideTable_bar__plus_01_dot_78_bar__plus_00_dot_02_bar__plus_02_dot_38)\n        (inReceptacle KeyChain_bar__plus_01_dot_61_bar__plus_00_dot_55_bar__plus_02_dot_44 SideTable_bar__plus_01_dot_78_bar__plus_00_dot_02_bar__plus_02_dot_38)\n        (inReceptacle AlarmClock_bar__plus_01_dot_55_bar__plus_00_dot_55_bar__plus_02_dot_32 SideTable_bar__plus_01_dot_78_bar__plus_00_dot_02_bar__plus_02_dot_38)\n        (inReceptacle Pen_bar__minus_01_dot_07_bar__plus_00_dot_59_bar__minus_02_dot_88 Drawer_bar__minus_01_dot_16_bar__plus_00_dot_79_bar__minus_02_dot_85)\n        (inReceptacle HousePlant_bar__minus_00_dot_20_bar__plus_01_dot_10_bar__minus_03_dot_08 Cabinet_bar__plus_02_dot_06_bar__plus_01_dot_50_bar__minus_02_dot_64)\n        (inReceptacle Pencil_bar__minus_00_dot_19_bar__plus_00_dot_09_bar__minus_02_dot_93 Drawer_bar__minus_00_dot_37_bar__plus_00_dot_29_bar__minus_02_dot_85)\n        (inReceptacle CellPhone_bar__minus_00_dot_37_bar__plus_00_dot_09_bar__minus_02_dot_83 Drawer_bar__minus_00_dot_37_bar__plus_00_dot_29_bar__minus_02_dot_85)\n        (inReceptacle Book_bar__minus_00_dot_54_bar__plus_00_dot_08_bar__minus_02_dot_93 Drawer_bar__minus_00_dot_37_bar__plus_00_dot_29_bar__minus_02_dot_85)\n        (inReceptacle HousePlant_bar__minus_00_dot_20_bar__plus_01_dot_10_bar__minus_03_dot_08 Desk_bar__minus_01_dot_27_bar__plus_01_dot_17_bar__minus_02_dot_99)\n        (inReceptacle Pencil_bar__minus_03_dot_40_bar__plus_00_dot_38_bar__minus_02_dot_84 GarbageCan_bar__minus_03_dot_34_bar__plus_00_dot_04_bar__minus_02_dot_80)\n        \n        \n        (receptacleAtLocation ArmChair_bar__minus_00_dot_94_bar__plus_00_dot_00_bar__plus_03_dot_61 loc_bar__minus_3_bar_10_bar_0_bar_60)\n        (receptacleAtLocation ArmChair_bar__minus_02_dot_17_bar__plus_00_dot_00_bar__plus_03_dot_63 loc_bar__minus_8_bar_10_bar_0_bar_60)\n        (receptacleAtLocation ArmChair_bar__minus_03_dot_37_bar__plus_00_dot_00_bar__plus_03_dot_38 loc_bar__minus_9_bar_12_bar_3_bar_60)\n        (receptacleAtLocation Bed_bar__plus_00_dot_99_bar__plus_00_dot_03_bar__plus_00_dot_67 loc_bar__minus_2_bar_3_bar_1_bar_45)\n        (receptacleAtLocation Cabinet_bar__plus_02_dot_06_bar__plus_01_dot_50_bar__minus_02_dot_64 loc_bar_4_bar__minus_8_bar_1_bar_15)\n        (receptacleAtLocation Cabinet_bar__minus_03_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64 loc_bar__minus_11_bar__minus_8_bar_2_bar_15)\n        (receptacleAtLocation Desk_bar__minus_01_dot_27_bar__plus_01_dot_17_bar__minus_02_dot_99 loc_bar__minus_4_bar__minus_9_bar_2_bar_30)\n        (receptacleAtLocation Drawer_bar__minus_00_dot_37_bar__plus_00_dot_29_bar__minus_02_dot_85 loc_bar__minus_6_bar__minus_9_bar_1_bar_45)\n        (receptacleAtLocation Drawer_bar__minus_00_dot_37_bar__plus_00_dot_79_bar__minus_02_dot_85 loc_bar__minus_5_bar__minus_9_bar_1_bar_45)\n        (receptacleAtLocation Drawer_bar__minus_01_dot_16_bar__plus_00_dot_29_bar__minus_02_dot_85 loc_bar__minus_5_bar__minus_9_bar_2_bar_60)\n        (receptacleAtLocation Drawer_bar__minus_01_dot_16_bar__plus_00_dot_79_bar__minus_02_dot_85 loc_bar__minus_7_bar__minus_9_bar_2_bar_60)\n        (receptacleAtLocation GarbageCan_bar__minus_03_dot_34_bar__plus_00_dot_04_bar__minus_02_dot_80 loc_bar__minus_13_bar__minus_9_bar_2_bar_60)\n        (receptacleAtLocation LaundryHamper_bar__plus_01_dot_83_bar__plus_00_dot_04_bar__plus_03_dot_69 loc_bar_7_bar_12_bar_0_bar_60)\n        (receptacleAtLocation Safe_bar__plus_01_dot_72_bar__plus_00_dot_05_bar__minus_03_dot_05 loc_bar_7_bar__minus_8_bar_2_bar_60)\n        (receptacleAtLocation SideTable_bar__plus_01_dot_78_bar__plus_00_dot_02_bar__plus_02_dot_38 loc_bar_6_bar_13_bar_2_bar_60)\n        (objectAtLocation Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74 loc_bar__minus_13_bar__minus_9_bar_2_bar_60)\n        (objectAtLocation RemoteControl_bar__minus_03_dot_43_bar__plus_00_dot_19_bar__plus_03_dot_31 loc_bar__minus_13_bar_11_bar_0_bar_60)\n        (objectAtLocation Laptop_bar__plus_00_dot_10_bar__plus_00_dot_83_bar__plus_00_dot_98 loc_bar__minus_2_bar_3_bar_1_bar_45)\n        (objectAtLocation Pencil_bar__minus_03_dot_40_bar__plus_00_dot_38_bar__minus_02_dot_84 loc_bar__minus_13_bar__minus_9_bar_2_bar_60)\n        (objectAtLocation Book_bar__minus_00_dot_54_bar__plus_00_dot_08_bar__minus_02_dot_93 loc_bar__minus_6_bar__minus_9_bar_1_bar_45)\n        (objectAtLocation RemoteControl_bar__minus_01_dot_01_bar__plus_00_dot_19_bar__plus_03_dot_48 loc_bar__minus_4_bar_12_bar_0_bar_60)\n        (objectAtLocation Pencil_bar__minus_00_dot_28_bar__plus_00_dot_59_bar__minus_02_dot_83 loc_bar__minus_5_bar__minus_9_bar_1_bar_45)\n        (objectAtLocation Book_bar__minus_02_dot_63_bar__plus_00_dot_08_bar__minus_02_dot_90 loc_bar__minus_11_bar__minus_8_bar_2_bar_15)\n        (objectAtLocation LaundryHamperLid_bar__plus_01_dot_83_bar__plus_00_dot_79_bar__plus_03_dot_69 loc_bar_7_bar_12_bar_0_bar_45)\n        (objectAtLocation AlarmClock_bar__plus_01_dot_55_bar__plus_00_dot_55_bar__plus_02_dot_32 loc_bar_6_bar_13_bar_2_bar_60)\n        (objectAtLocation CellPhone_bar__minus_00_dot_37_bar__plus_00_dot_09_bar__minus_02_dot_83 loc_bar__minus_6_bar__minus_9_bar_1_bar_45)\n        (objectAtLocation LightSwitch_bar__minus_00_dot_16_bar__plus_01_dot_28_bar__plus_04_dot_00 loc_bar__minus_1_bar_14_bar_0_bar_45)\n        (objectAtLocation Pillow_bar__plus_01_dot_24_bar__plus_00_dot_94_bar__plus_00_dot_98 loc_bar__minus_2_bar_3_bar_1_bar_45)\n        (objectAtLocation Pillow_bar__plus_01_dot_82_bar__plus_00_dot_87_bar__plus_01_dot_15 loc_bar__minus_2_bar_3_bar_1_bar_45)\n        (objectAtLocation TeddyBear_bar__plus_01_dot_87_bar__plus_00_dot_04_bar__minus_00_dot_56 loc_bar_7_bar__minus_4_bar_0_bar_60)\n        (objectAtLocation CreditCard_bar__minus_03_dot_26_bar__plus_00_dot_19_bar__plus_03_dot_51 loc_bar__minus_9_bar_12_bar_3_bar_60)\n        (objectAtLocation KeyChain_bar__plus_01_dot_61_bar__plus_00_dot_55_bar__plus_02_dot_44 loc_bar_6_bar_13_bar_2_bar_60)\n        (objectAtLocation Pencil_bar__minus_00_dot_19_bar__plus_00_dot_09_bar__minus_02_dot_93 loc_bar__minus_6_bar__minus_9_bar_1_bar_45)\n        (objectAtLocation Laptop_bar__plus_00_dot_67_bar__plus_00_dot_83_bar__plus_00_dot_98 loc_bar__minus_2_bar_3_bar_1_bar_45)\n        (objectAtLocation HousePlant_bar__minus_00_dot_20_bar__plus_01_dot_10_bar__minus_03_dot_08 loc_bar__minus_4_bar__minus_9_bar_2_bar_30)\n        (objectAtLocation DeskLamp_bar__plus_01_dot_87_bar__plus_00_dot_55_bar__plus_02_dot_42 loc_bar_6_bar_13_bar_2_bar_60)\n        (objectAtLocation RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_56_bar__plus_02_dot_39 loc_bar_6_bar_13_bar_2_bar_60)\n        (objectAtLocation Mirror_bar__minus_01_dot_60_bar__plus_01_dot_45_bar__plus_04_dot_00 loc_bar__minus_6_bar_13_bar_0_bar_15)\n        (objectAtLocation Television_bar__minus_03_dot_88_bar__plus_01_dot_77_bar__plus_02_dot_12 loc_bar__minus_13_bar_8_bar_3_bar_0)\n        (objectAtLocation CD_bar__minus_02_dot_87_bar__plus_00_dot_08_bar__minus_03_dot_06 loc_bar__minus_11_bar__minus_8_bar_2_bar_60)\n        (objectAtLocation Pen_bar__minus_01_dot_07_bar__plus_00_dot_59_bar__minus_02_dot_88 loc_bar__minus_7_bar__minus_9_bar_2_bar_60)\n        (objectAtLocation Window_bar__minus_01_dot_95_bar__plus_02_dot_15_bar__minus_03_dot_30 loc_bar__minus_7_bar__minus_9_bar_2_bar__minus_30)\n        (objectAtLocation Window_bar__minus_00_dot_47_bar__plus_02_dot_15_bar__minus_03_dot_30 loc_bar__minus_2_bar__minus_9_bar_2_bar__minus_30)\n        (objectAtLocation Window_bar__minus_03_dot_89_bar__plus_01_dot_75_bar__plus_00_dot_58 loc_bar__minus_13_bar_2_bar_3_bar_0)\n        (objectAtLocation Window_bar__minus_03_dot_89_bar__plus_01_dot_75_bar__minus_00_dot_68 loc_bar__minus_13_bar__minus_3_bar_3_bar_0)\n        (objectAtLocation Chair_bar__minus_02_dot_07_bar__plus_00_dot_04_bar__minus_02_dot_69 loc_bar__minus_7_bar__minus_9_bar_2_bar_60)\n        (objectAtLocation Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91 loc_bar_5_bar__minus_8_bar_2_bar_60)\n        (objectAtLocation Blinds_bar__minus_00_dot_48_bar__plus_02_dot_79_bar__minus_03_dot_25 loc_bar__minus_2_bar__minus_9_bar_2_bar__minus_30)\n        (objectAtLocation Blinds_bar__minus_01_dot_96_bar__plus_02_dot_79_bar__minus_03_dot_25 loc_bar__minus_7_bar__minus_9_bar_2_bar__minus_30)\n        (objectAtLocation Blinds_bar__minus_03_dot_80_bar__plus_02_dot_51_bar__plus_00_dot_60 loc_bar__minus_13_bar_2_bar_3_bar__minus_30)\n        (objectAtLocation Blinds_bar__minus_03_dot_80_bar__plus_02_dot_51_bar__minus_00_dot_65 loc_bar__minus_13_bar__minus_3_bar_3_bar__minus_30)\n        )\n    \n\n                (:goal\n                    (and\n                        (exists (?r - receptacle)\n                            (exists (?o1 - object)\n                                (and\n                                    (objectType ?o1 LaptopType)\n                                    (receptacleType ?r DeskType)\n                                    (inReceptacle ?o1 ?r)\n                                    (exists (?o2 - object)\n                                        (and\n                                            (not (= ?o1 ?o2))\n                                            (objectType ?o2 LaptopType)\n                                            (receptacleType ?r DeskType)\n                                            (inReceptacle ?o2 ?r)\n                                        )\n                                    )\n                                )\n                            )\n                        )\n                    )\n                )\n            )\n            ", "solvable": true, "walkthrough": ["go to bed 1", "take laptop 2 from bed 1", "go to desk 1", "move laptop 2 to desk 1", "go to bed 1", "take laptop 1 from bed 1", "go to desk 1", "move laptop 1 to desk 1"]}