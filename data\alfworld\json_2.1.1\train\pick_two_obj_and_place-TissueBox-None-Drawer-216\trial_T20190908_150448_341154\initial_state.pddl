
(define (problem plan_trial_T20190908_150448_341154)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__minus_03_dot_78_bar__plus_01_dot_31_bar__plus_00_dot_46 - object
        Chair_bar__minus_03_dot_83_bar__plus_00_dot_00_bar__minus_00_dot_22 - object
        Chair_bar__minus_04_dot_40_bar__plus_00_dot_00_bar__plus_00_dot_28 - object
        CreditCard_bar__plus_00_dot_04_bar__plus_00_dot_13_bar__plus_01_dot_09 - object
        CreditCard_bar__plus_01_dot_37_bar__plus_00_dot_46_bar__plus_01_dot_07 - object
        CreditCard_bar__minus_01_dot_23_bar__plus_00_dot_13_bar__plus_01_dot_20 - object
        DeskLamp_bar__plus_01_dot_66_bar__plus_00_dot_65_bar__minus_01_dot_61 - object
        FloorLamp_bar__minus_04_dot_57_bar__plus_00_dot_00_bar__minus_01_dot_62 - object
        HousePlant_bar__plus_01_dot_56_bar__plus_00_dot_65_bar__plus_01_dot_17 - object
        KeyChain_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__minus_01_dot_41 - object
        Laptop_bar__plus_00_dot_43_bar__plus_00_dot_54_bar__minus_01_dot_19 - object
        LightSwitch_bar__minus_02_dot_46_bar__plus_01_dot_23_bar__minus_01_dot_95 - object
        Newspaper_bar__plus_00_dot_97_bar__plus_00_dot_66_bar__plus_01_dot_04 - object
        Painting_bar__minus_00_dot_70_bar__plus_01_dot_84_bar__minus_01_dot_91 - object
        Painting_bar__minus_04_dot_91_bar__plus_01_dot_76_bar__minus_00_dot_10 - object
        Pillow_bar__minus_01_dot_83_bar__plus_00_dot_59_bar__minus_01_dot_25 - object
        RemoteControl_bar__minus_04_dot_17_bar__plus_01_dot_06_bar__plus_00_dot_45 - object
        Statue_bar__plus_00_dot_10_bar__plus_01_dot_00_bar__plus_01_dot_23 - object
        Statue_bar__minus_01_dot_29_bar__plus_00_dot_89_bar__plus_01_dot_22 - object
        Television_bar__minus_00_dot_55_bar__plus_01_dot_52_bar__plus_01_dot_45 - object
        TissueBox_bar__minus_00_dot_18_bar__plus_00_dot_13_bar__plus_01_dot_20 - object
        TissueBox_bar__minus_03_dot_84_bar__plus_01_dot_07_bar__plus_00_dot_13 - object
        Vase_bar__plus_01_dot_75_bar__plus_00_dot_67_bar__minus_01_dot_41 - object
        Vase_bar__minus_00_dot_52_bar__plus_00_dot_11_bar__plus_01_dot_20 - object
        Vase_bar__minus_00_dot_97_bar__plus_00_dot_11_bar__plus_01_dot_20 - object
        Window_bar__plus_00_dot_99_bar__plus_01_dot_50_bar__plus_01_dot_47 - object
        Window_bar__minus_02_dot_01_bar__plus_01_dot_50_bar__plus_01_dot_47 - object
        ArmChair_bar__minus_01_dot_83_bar__plus_00_dot_00_bar__minus_01_dot_35 - receptacle
        DiningTable_bar__minus_03_dot_94_bar__plus_00_dot_00_bar__plus_00_dot_26 - receptacle
        Drawer_bar__plus_01_dot_12_bar__plus_00_dot_52_bar__plus_01_dot_07 - receptacle
        Drawer_bar__plus_01_dot_58_bar__plus_00_dot_52_bar__minus_01_dot_46 - receptacle
        Drawer_bar__minus_00_dot_16_bar__plus_00_dot_73_bar__plus_01_dot_03 - receptacle
        Drawer_bar__minus_01_dot_02_bar__plus_00_dot_73_bar__plus_01_dot_03 - receptacle
        GarbageCan_bar__minus_04_dot_58_bar__plus_00_dot_00_bar__plus_01_dot_13 - receptacle
        Shelf_bar__minus_00_dot_63_bar__plus_00_dot_12_bar__plus_01_dot_20 - receptacle
        SideTable_bar__plus_01_dot_10_bar__plus_00_dot_00_bar__plus_01_dot_12 - receptacle
        SideTable_bar__plus_01_dot_58_bar_00_dot_00_bar__minus_01_dot_49 - receptacle
        SideTable_bar__minus_00_dot_59_bar__plus_00_dot_01_bar__plus_01_dot_23 - receptacle
        Sofa_bar__plus_00_dot_06_bar__plus_00_dot_00_bar__minus_01_dot_39 - receptacle
        loc_bar_4_bar_2_bar_0_bar_15 - location
        loc_bar__minus_16_bar_4_bar_3_bar_60 - location
        loc_bar__minus_15_bar__minus_6_bar_3_bar_60 - location
        loc_bar_4_bar_0_bar_0_bar_45 - location
        loc_bar__minus_17_bar__minus_2_bar_3_bar__minus_15 - location
        loc_bar__minus_16_bar__minus_3_bar_0_bar_60 - location
        loc_bar_4_bar__minus_1_bar_2_bar_45 - location
        loc_bar__minus_5_bar_1_bar_0_bar_45 - location
        loc_bar__minus_1_bar_2_bar_0_bar_60 - location
        loc_bar__minus_17_bar__minus_2_bar_0_bar_60 - location
        loc_bar__minus_15_bar__minus_3_bar_0_bar_60 - location
        loc_bar_0_bar_1_bar_0_bar_60 - location
        loc_bar__minus_7_bar__minus_2_bar_2_bar_60 - location
        loc_bar_4_bar_2_bar_0_bar_60 - location
        loc_bar__minus_2_bar_3_bar_0_bar_15 - location
        loc_bar_6_bar__minus_2_bar_2_bar_60 - location
        loc_bar__minus_3_bar__minus_2_bar_2_bar_0 - location
        loc_bar__minus_11_bar__minus_6_bar_2_bar_45 - location
        loc_bar__minus_2_bar__minus_1_bar_2_bar_60 - location
        loc_bar__minus_8_bar_3_bar_0_bar_15 - location
        loc_bar_0_bar_1_bar_0_bar_45 - location
        loc_bar__minus_12_bar_2_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType Drawer_bar__plus_01_dot_58_bar__plus_00_dot_52_bar__minus_01_dot_46 DrawerType)
        (receptacleType GarbageCan_bar__minus_04_dot_58_bar__plus_00_dot_00_bar__plus_01_dot_13 GarbageCanType)
        (receptacleType SideTable_bar__plus_01_dot_10_bar__plus_00_dot_00_bar__plus_01_dot_12 SideTableType)
        (receptacleType ArmChair_bar__minus_01_dot_83_bar__plus_00_dot_00_bar__minus_01_dot_35 ArmChairType)
        (receptacleType SideTable_bar__minus_00_dot_59_bar__plus_00_dot_01_bar__plus_01_dot_23 SideTableType)
        (receptacleType SideTable_bar__plus_01_dot_58_bar_00_dot_00_bar__minus_01_dot_49 SideTableType)
        (receptacleType Sofa_bar__plus_00_dot_06_bar__plus_00_dot_00_bar__minus_01_dot_39 SofaType)
        (receptacleType DiningTable_bar__minus_03_dot_94_bar__plus_00_dot_00_bar__plus_00_dot_26 DiningTableType)
        (receptacleType Drawer_bar__minus_01_dot_02_bar__plus_00_dot_73_bar__plus_01_dot_03 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_16_bar__plus_00_dot_73_bar__plus_01_dot_03 DrawerType)
        (receptacleType Drawer_bar__plus_01_dot_12_bar__plus_00_dot_52_bar__plus_01_dot_07 DrawerType)
        (receptacleType Shelf_bar__minus_00_dot_63_bar__plus_00_dot_12_bar__plus_01_dot_20 ShelfType)
        (objectType Painting_bar__minus_04_dot_91_bar__plus_01_dot_76_bar__minus_00_dot_10 PaintingType)
        (objectType TissueBox_bar__minus_03_dot_84_bar__plus_01_dot_07_bar__plus_00_dot_13 TissueBoxType)
        (objectType Chair_bar__minus_04_dot_40_bar__plus_00_dot_00_bar__plus_00_dot_28 ChairType)
        (objectType KeyChain_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__minus_01_dot_41 KeyChainType)
        (objectType Window_bar__minus_02_dot_01_bar__plus_01_dot_50_bar__plus_01_dot_47 WindowType)
        (objectType Laptop_bar__plus_00_dot_43_bar__plus_00_dot_54_bar__minus_01_dot_19 LaptopType)
        (objectType CreditCard_bar__plus_00_dot_04_bar__plus_00_dot_13_bar__plus_01_dot_09 CreditCardType)
        (objectType Vase_bar__plus_01_dot_75_bar__plus_00_dot_67_bar__minus_01_dot_41 VaseType)
        (objectType CreditCard_bar__minus_01_dot_23_bar__plus_00_dot_13_bar__plus_01_dot_20 CreditCardType)
        (objectType RemoteControl_bar__minus_04_dot_17_bar__plus_01_dot_06_bar__plus_00_dot_45 RemoteControlType)
        (objectType Pillow_bar__minus_01_dot_83_bar__plus_00_dot_59_bar__minus_01_dot_25 PillowType)
        (objectType DeskLamp_bar__plus_01_dot_66_bar__plus_00_dot_65_bar__minus_01_dot_61 DeskLampType)
        (objectType Chair_bar__minus_03_dot_83_bar__plus_00_dot_00_bar__minus_00_dot_22 ChairType)
        (objectType Window_bar__plus_00_dot_99_bar__plus_01_dot_50_bar__plus_01_dot_47 WindowType)
        (objectType Statue_bar__minus_01_dot_29_bar__plus_00_dot_89_bar__plus_01_dot_22 StatueType)
        (objectType CreditCard_bar__plus_01_dot_37_bar__plus_00_dot_46_bar__plus_01_dot_07 CreditCardType)
        (objectType Vase_bar__minus_00_dot_97_bar__plus_00_dot_11_bar__plus_01_dot_20 VaseType)
        (objectType Vase_bar__minus_00_dot_52_bar__plus_00_dot_11_bar__plus_01_dot_20 VaseType)
        (objectType Newspaper_bar__plus_00_dot_97_bar__plus_00_dot_66_bar__plus_01_dot_04 NewspaperType)
        (objectType FloorLamp_bar__minus_04_dot_57_bar__plus_00_dot_00_bar__minus_01_dot_62 FloorLampType)
        (objectType TissueBox_bar__minus_00_dot_18_bar__plus_00_dot_13_bar__plus_01_dot_20 TissueBoxType)
        (objectType HousePlant_bar__plus_01_dot_56_bar__plus_00_dot_65_bar__plus_01_dot_17 HousePlantType)
        (objectType Television_bar__minus_00_dot_55_bar__plus_01_dot_52_bar__plus_01_dot_45 TelevisionType)
        (objectType Painting_bar__minus_00_dot_70_bar__plus_01_dot_84_bar__minus_01_dot_91 PaintingType)
        (objectType LightSwitch_bar__minus_02_dot_46_bar__plus_01_dot_23_bar__minus_01_dot_95 LightSwitchType)
        (objectType Statue_bar__plus_00_dot_10_bar__plus_01_dot_00_bar__plus_01_dot_23 StatueType)
        (objectType Box_bar__minus_03_dot_78_bar__plus_01_dot_31_bar__plus_00_dot_46 BoxType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain GarbageCanType NewspaperType)
        (canContain GarbageCanType TissueBoxType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType StatueType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType StatueType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType StatueType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain DiningTableType NewspaperType)
        (canContain DiningTableType VaseType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType TissueBoxType)
        (canContain DiningTableType StatueType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType StatueType)
        (pickupable TissueBox_bar__minus_03_dot_84_bar__plus_01_dot_07_bar__plus_00_dot_13)
        (pickupable KeyChain_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__minus_01_dot_41)
        (pickupable Laptop_bar__plus_00_dot_43_bar__plus_00_dot_54_bar__minus_01_dot_19)
        (pickupable CreditCard_bar__plus_00_dot_04_bar__plus_00_dot_13_bar__plus_01_dot_09)
        (pickupable Vase_bar__plus_01_dot_75_bar__plus_00_dot_67_bar__minus_01_dot_41)
        (pickupable CreditCard_bar__minus_01_dot_23_bar__plus_00_dot_13_bar__plus_01_dot_20)
        (pickupable RemoteControl_bar__minus_04_dot_17_bar__plus_01_dot_06_bar__plus_00_dot_45)
        (pickupable Pillow_bar__minus_01_dot_83_bar__plus_00_dot_59_bar__minus_01_dot_25)
        (pickupable Statue_bar__minus_01_dot_29_bar__plus_00_dot_89_bar__plus_01_dot_22)
        (pickupable CreditCard_bar__plus_01_dot_37_bar__plus_00_dot_46_bar__plus_01_dot_07)
        (pickupable Vase_bar__minus_00_dot_97_bar__plus_00_dot_11_bar__plus_01_dot_20)
        (pickupable Vase_bar__minus_00_dot_52_bar__plus_00_dot_11_bar__plus_01_dot_20)
        (pickupable Newspaper_bar__plus_00_dot_97_bar__plus_00_dot_66_bar__plus_01_dot_04)
        (pickupable TissueBox_bar__minus_00_dot_18_bar__plus_00_dot_13_bar__plus_01_dot_20)
        (pickupable Statue_bar__plus_00_dot_10_bar__plus_01_dot_00_bar__plus_01_dot_23)
        (pickupable Box_bar__minus_03_dot_78_bar__plus_01_dot_31_bar__plus_00_dot_46)
        (isReceptacleObject Box_bar__minus_03_dot_78_bar__plus_01_dot_31_bar__plus_00_dot_46)
        (openable Drawer_bar__plus_01_dot_58_bar__plus_00_dot_52_bar__minus_01_dot_46)
        (openable Drawer_bar__minus_01_dot_02_bar__plus_00_dot_73_bar__plus_01_dot_03)
        (openable Drawer_bar__minus_00_dot_16_bar__plus_00_dot_73_bar__plus_01_dot_03)
        (openable Drawer_bar__plus_01_dot_12_bar__plus_00_dot_52_bar__plus_01_dot_07)
        
        (atLocation agent1 loc_bar__minus_12_bar_2_bar_2_bar_30)
        
        
        
        
        
        
        
        (toggleable DeskLamp_bar__plus_01_dot_66_bar__plus_00_dot_65_bar__minus_01_dot_61)
        (toggleable FloorLamp_bar__minus_04_dot_57_bar__plus_00_dot_00_bar__minus_01_dot_62)
        
        
        
        
        (inReceptacle Laptop_bar__plus_00_dot_43_bar__plus_00_dot_54_bar__minus_01_dot_19 Sofa_bar__plus_00_dot_06_bar__plus_00_dot_00_bar__minus_01_dot_39)
        (inReceptacle KeyChain_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__minus_01_dot_41 Drawer_bar__plus_01_dot_58_bar__plus_00_dot_52_bar__minus_01_dot_46)
        (inReceptacle Pillow_bar__minus_01_dot_83_bar__plus_00_dot_59_bar__minus_01_dot_25 ArmChair_bar__minus_01_dot_83_bar__plus_00_dot_00_bar__minus_01_dot_35)
        (inReceptacle Vase_bar__plus_01_dot_75_bar__plus_00_dot_67_bar__minus_01_dot_41 SideTable_bar__plus_01_dot_58_bar_00_dot_00_bar__minus_01_dot_49)
        (inReceptacle DeskLamp_bar__plus_01_dot_66_bar__plus_00_dot_65_bar__minus_01_dot_61 SideTable_bar__plus_01_dot_58_bar_00_dot_00_bar__minus_01_dot_49)
        (inReceptacle RemoteControl_bar__minus_04_dot_17_bar__plus_01_dot_06_bar__plus_00_dot_45 DiningTable_bar__minus_03_dot_94_bar__plus_00_dot_00_bar__plus_00_dot_26)
        (inReceptacle Box_bar__minus_03_dot_78_bar__plus_01_dot_31_bar__plus_00_dot_46 DiningTable_bar__minus_03_dot_94_bar__plus_00_dot_00_bar__plus_00_dot_26)
        (inReceptacle TissueBox_bar__minus_03_dot_84_bar__plus_01_dot_07_bar__plus_00_dot_13 DiningTable_bar__minus_03_dot_94_bar__plus_00_dot_00_bar__plus_00_dot_26)
        (inReceptacle HousePlant_bar__plus_01_dot_56_bar__plus_00_dot_65_bar__plus_01_dot_17 SideTable_bar__plus_01_dot_10_bar__plus_00_dot_00_bar__plus_01_dot_12)
        (inReceptacle Newspaper_bar__plus_00_dot_97_bar__plus_00_dot_66_bar__plus_01_dot_04 SideTable_bar__plus_01_dot_10_bar__plus_00_dot_00_bar__plus_01_dot_12)
        (inReceptacle Statue_bar__plus_00_dot_10_bar__plus_01_dot_00_bar__plus_01_dot_23 SideTable_bar__minus_00_dot_59_bar__plus_00_dot_01_bar__plus_01_dot_23)
        (inReceptacle Statue_bar__minus_01_dot_29_bar__plus_00_dot_89_bar__plus_01_dot_22 SideTable_bar__minus_00_dot_59_bar__plus_00_dot_01_bar__plus_01_dot_23)
        (inReceptacle CreditCard_bar__plus_01_dot_37_bar__plus_00_dot_46_bar__plus_01_dot_07 Drawer_bar__plus_01_dot_12_bar__plus_00_dot_52_bar__plus_01_dot_07)
        (inReceptacle Vase_bar__minus_00_dot_97_bar__plus_00_dot_11_bar__plus_01_dot_20 Shelf_bar__minus_00_dot_63_bar__plus_00_dot_12_bar__plus_01_dot_20)
        (inReceptacle CreditCard_bar__minus_01_dot_23_bar__plus_00_dot_13_bar__plus_01_dot_20 Shelf_bar__minus_00_dot_63_bar__plus_00_dot_12_bar__plus_01_dot_20)
        (inReceptacle Vase_bar__minus_00_dot_52_bar__plus_00_dot_11_bar__plus_01_dot_20 Shelf_bar__minus_00_dot_63_bar__plus_00_dot_12_bar__plus_01_dot_20)
        (inReceptacle CreditCard_bar__plus_00_dot_04_bar__plus_00_dot_13_bar__plus_01_dot_09 Shelf_bar__minus_00_dot_63_bar__plus_00_dot_12_bar__plus_01_dot_20)
        (inReceptacle TissueBox_bar__minus_00_dot_18_bar__plus_00_dot_13_bar__plus_01_dot_20 Shelf_bar__minus_00_dot_63_bar__plus_00_dot_12_bar__plus_01_dot_20)
        
        
        (receptacleAtLocation ArmChair_bar__minus_01_dot_83_bar__plus_00_dot_00_bar__minus_01_dot_35 loc_bar__minus_7_bar__minus_2_bar_2_bar_60)
        (receptacleAtLocation DiningTable_bar__minus_03_dot_94_bar__plus_00_dot_00_bar__plus_00_dot_26 loc_bar__minus_16_bar__minus_3_bar_0_bar_60)
        (receptacleAtLocation Drawer_bar__plus_01_dot_12_bar__plus_00_dot_52_bar__plus_01_dot_07 loc_bar_4_bar_0_bar_0_bar_45)
        (receptacleAtLocation Drawer_bar__plus_01_dot_58_bar__plus_00_dot_52_bar__minus_01_dot_46 loc_bar_4_bar__minus_1_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_16_bar__plus_00_dot_73_bar__plus_01_dot_03 loc_bar_0_bar_1_bar_0_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_02_bar__plus_00_dot_73_bar__plus_01_dot_03 loc_bar__minus_5_bar_1_bar_0_bar_45)
        (receptacleAtLocation GarbageCan_bar__minus_04_dot_58_bar__plus_00_dot_00_bar__plus_01_dot_13 loc_bar__minus_16_bar_4_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__minus_00_dot_63_bar__plus_00_dot_12_bar__plus_01_dot_20 loc_bar_0_bar_1_bar_0_bar_60)
        (receptacleAtLocation SideTable_bar__plus_01_dot_10_bar__plus_00_dot_00_bar__plus_01_dot_12 loc_bar_4_bar_2_bar_0_bar_60)
        (receptacleAtLocation SideTable_bar__plus_01_dot_58_bar_00_dot_00_bar__minus_01_dot_49 loc_bar_6_bar__minus_2_bar_2_bar_60)
        (receptacleAtLocation SideTable_bar__minus_00_dot_59_bar__plus_00_dot_01_bar__plus_01_dot_23 loc_bar__minus_1_bar_2_bar_0_bar_60)
        (receptacleAtLocation Sofa_bar__plus_00_dot_06_bar__plus_00_dot_00_bar__minus_01_dot_39 loc_bar__minus_2_bar__minus_1_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__plus_01_dot_37_bar__plus_00_dot_46_bar__plus_01_dot_07 loc_bar_4_bar_0_bar_0_bar_45)
        (objectAtLocation TissueBox_bar__minus_03_dot_84_bar__plus_01_dot_07_bar__plus_00_dot_13 loc_bar__minus_16_bar__minus_3_bar_0_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_23_bar__plus_00_dot_13_bar__plus_01_dot_20 loc_bar_0_bar_1_bar_0_bar_60)
        (objectAtLocation Box_bar__minus_03_dot_78_bar__plus_01_dot_31_bar__plus_00_dot_46 loc_bar__minus_16_bar__minus_3_bar_0_bar_60)
        (objectAtLocation DeskLamp_bar__plus_01_dot_66_bar__plus_00_dot_65_bar__minus_01_dot_61 loc_bar_6_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Vase_bar__minus_00_dot_97_bar__plus_00_dot_11_bar__plus_01_dot_20 loc_bar_0_bar_1_bar_0_bar_60)
        (objectAtLocation TissueBox_bar__minus_00_dot_18_bar__plus_00_dot_13_bar__plus_01_dot_20 loc_bar_0_bar_1_bar_0_bar_60)
        (objectAtLocation Newspaper_bar__plus_00_dot_97_bar__plus_00_dot_66_bar__plus_01_dot_04 loc_bar_4_bar_2_bar_0_bar_60)
        (objectAtLocation KeyChain_bar__plus_01_dot_58_bar__plus_00_dot_46_bar__minus_01_dot_41 loc_bar_4_bar__minus_1_bar_2_bar_45)
        (objectAtLocation Vase_bar__plus_01_dot_75_bar__plus_00_dot_67_bar__minus_01_dot_41 loc_bar_6_bar__minus_2_bar_2_bar_60)
        (objectAtLocation FloorLamp_bar__minus_04_dot_57_bar__plus_00_dot_00_bar__minus_01_dot_62 loc_bar__minus_15_bar__minus_6_bar_3_bar_60)
        (objectAtLocation Statue_bar__minus_01_dot_29_bar__plus_00_dot_89_bar__plus_01_dot_22 loc_bar__minus_1_bar_2_bar_0_bar_60)
        (objectAtLocation LightSwitch_bar__minus_02_dot_46_bar__plus_01_dot_23_bar__minus_01_dot_95 loc_bar__minus_11_bar__minus_6_bar_2_bar_45)
        (objectAtLocation Television_bar__minus_00_dot_55_bar__plus_01_dot_52_bar__plus_01_dot_45 loc_bar__minus_2_bar_3_bar_0_bar_15)
        (objectAtLocation RemoteControl_bar__minus_04_dot_17_bar__plus_01_dot_06_bar__plus_00_dot_45 loc_bar__minus_16_bar__minus_3_bar_0_bar_60)
        (objectAtLocation Pillow_bar__minus_01_dot_83_bar__plus_00_dot_59_bar__minus_01_dot_25 loc_bar__minus_7_bar__minus_2_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__plus_00_dot_04_bar__plus_00_dot_13_bar__plus_01_dot_09 loc_bar_0_bar_1_bar_0_bar_60)
        (objectAtLocation Laptop_bar__plus_00_dot_43_bar__plus_00_dot_54_bar__minus_01_dot_19 loc_bar__minus_2_bar__minus_1_bar_2_bar_60)
        (objectAtLocation HousePlant_bar__plus_01_dot_56_bar__plus_00_dot_65_bar__plus_01_dot_17 loc_bar_4_bar_2_bar_0_bar_60)
        (objectAtLocation Window_bar__minus_02_dot_01_bar__plus_01_dot_50_bar__plus_01_dot_47 loc_bar__minus_8_bar_3_bar_0_bar_15)
        (objectAtLocation Window_bar__plus_00_dot_99_bar__plus_01_dot_50_bar__plus_01_dot_47 loc_bar_4_bar_2_bar_0_bar_15)
        (objectAtLocation Chair_bar__minus_03_dot_83_bar__plus_00_dot_00_bar__minus_00_dot_22 loc_bar__minus_15_bar__minus_3_bar_0_bar_60)
        (objectAtLocation Chair_bar__minus_04_dot_40_bar__plus_00_dot_00_bar__plus_00_dot_28 loc_bar__minus_17_bar__minus_2_bar_0_bar_60)
        (objectAtLocation Vase_bar__minus_00_dot_52_bar__plus_00_dot_11_bar__plus_01_dot_20 loc_bar_0_bar_1_bar_0_bar_60)
        (objectAtLocation Painting_bar__minus_00_dot_70_bar__plus_01_dot_84_bar__minus_01_dot_91 loc_bar__minus_3_bar__minus_2_bar_2_bar_0)
        (objectAtLocation Statue_bar__plus_00_dot_10_bar__plus_01_dot_00_bar__plus_01_dot_23 loc_bar__minus_1_bar_2_bar_0_bar_60)
        (objectAtLocation Painting_bar__minus_04_dot_91_bar__plus_01_dot_76_bar__minus_00_dot_10 loc_bar__minus_17_bar__minus_2_bar_3_bar__minus_15)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 TissueBoxType)
                                    (receptacleType ?r DrawerType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 TissueBoxType)
                                            (receptacleType ?r DrawerType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            