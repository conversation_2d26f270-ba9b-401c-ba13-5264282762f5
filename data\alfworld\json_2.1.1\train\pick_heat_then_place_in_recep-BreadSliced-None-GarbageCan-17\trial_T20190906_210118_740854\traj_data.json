{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000158.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000159.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000160.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000161.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000162.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000165.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000166.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000183.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000184.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000185.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000186.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000187.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000188.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000192.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000195.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000196.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000197.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000198.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000199.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000200.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000203.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000206.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000207.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000208.png", "low_idx": 31}, {"high_idx": 9, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 9, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 9, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 9, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 9, "image_name": "000000213.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000216.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000219.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000220.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000221.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000222.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000223.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000224.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000225.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000226.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 9, "image_name": "000000228.png", "low_idx": 34}, {"high_idx": 9, "image_name": "000000229.png", "low_idx": 34}, {"high_idx": 9, "image_name": "000000230.png", "low_idx": 34}, {"high_idx": 9, "image_name": "000000231.png", "low_idx": 34}, {"high_idx": 9, "image_name": "000000232.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000233.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000234.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000235.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000236.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000237.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000238.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000239.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000240.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000241.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000242.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000243.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000244.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000247.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000248.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000249.png", "low_idx": 35}, {"high_idx": 9, "image_name": "000000250.png", "low_idx": 36}, {"high_idx": 9, "image_name": "000000251.png", "low_idx": 36}, {"high_idx": 9, "image_name": "000000252.png", "low_idx": 36}, {"high_idx": 9, "image_name": "000000253.png", "low_idx": 36}, {"high_idx": 9, "image_name": "000000254.png", "low_idx": 36}, {"high_idx": 9, "image_name": "000000255.png", "low_idx": 36}, {"high_idx": 9, "image_name": "000000256.png", "low_idx": 37}, {"high_idx": 9, "image_name": "000000257.png", "low_idx": 37}, {"high_idx": 9, "image_name": "000000258.png", "low_idx": 37}, {"high_idx": 9, "image_name": "000000259.png", "low_idx": 37}, {"high_idx": 9, "image_name": "000000260.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000261.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000262.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000263.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000264.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000265.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000266.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000267.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000268.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000269.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000270.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000271.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000272.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000273.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000274.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000275.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000276.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000277.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000278.png", "low_idx": 39}, {"high_idx": 10, "image_name": "000000279.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000280.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000281.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000282.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000283.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000284.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000285.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000286.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000287.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000288.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000289.png", "low_idx": 40}, {"high_idx": 10, "image_name": "000000290.png", "low_idx": 41}, {"high_idx": 10, "image_name": "000000291.png", "low_idx": 41}, {"high_idx": 10, "image_name": "000000292.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000293.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000294.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000295.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000296.png", "low_idx": 44}, {"high_idx": 10, "image_name": "000000297.png", "low_idx": 44}, {"high_idx": 10, "image_name": "000000298.png", "low_idx": 45}, {"high_idx": 10, "image_name": "000000299.png", "low_idx": 45}, {"high_idx": 10, "image_name": "000000300.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000301.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000302.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000303.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000304.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000305.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000306.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000307.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000308.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000309.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000310.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000311.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000312.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000313.png", "low_idx": 48}, {"high_idx": 10, "image_name": "000000314.png", "low_idx": 48}, {"high_idx": 10, "image_name": "000000315.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000316.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000317.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000318.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000319.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000320.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000321.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000322.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000323.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000324.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000325.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000326.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000327.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000328.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000329.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000330.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000331.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000332.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000333.png", "low_idx": 53}, {"high_idx": 11, "image_name": "000000334.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000335.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000336.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000337.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000338.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000339.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000340.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000341.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000342.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000343.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000344.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000345.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000346.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000347.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000348.png", "low_idx": 54}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-2|8|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-4.05016136, -4.05016136, 7.60913324, 7.60913324, 3.249259472, 3.249259472]], "coordinateReceptacleObjectId": ["DiningTable", [-4.8448, -4.8448, 9.836, 9.836, 3.1376, 3.1376]], "forceVisible": true, "objectId": "Bread|-01.01|+00.81|+01.90"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "countertop"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-4.05016136, -4.05016136, 7.60913324, 7.60913324, 3.249259472, 3.249259472]], "coordinateReceptacleObjectId": ["CounterTop", [-4.0076, -4.0076, -2.676, -2.676, 3.7904, 3.7904]], "forceVisible": true, "objectId": "Bread|-01.01|+00.81|+01.90", "receptacleObjectId": "CounterTop|-01.00|+00.95|-00.67"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 4, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [-4.54261256, -4.54261256, -3.268929004, -3.268929004, 3.648568152, 3.648568152]], "coordinateReceptacleObjectId": ["CounterTop", [-4.0076, -4.0076, -2.676, -2.676, 3.7904, 3.7904]], "forceVisible": true, "objectId": "ButterKnife|-01.14|+00.91|-00.82"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 5, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-4.05016136, -4.05016136, 7.60913324, 7.60913324, 3.249259472, 3.249259472]], "forceVisible": true, "objectId": "Bread|-01.01|+00.81|+01.90"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "countertop"]}, "high_idx": 6, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [-4.54261256, -4.54261256, -3.268929004, -3.268929004, 3.648568152, 3.648568152]], "coordinateReceptacleObjectId": ["CounterTop", [-4.0076, -4.0076, -2.676, -2.676, 3.7904, 3.7904]], "forceVisible": true, "objectId": "ButterKnife|-01.14|+00.91|-00.82", "receptacleObjectId": "CounterTop|-01.00|+00.95|-00.67"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-4.05016136, -4.05016136, 7.60913324, 7.60913324, 3.249259472, 3.249259472]], "coordinateReceptacleObjectId": ["CounterTop", [-4.0076, -4.0076, -2.676, -2.676, 3.7904, 3.7904]], "forceVisible": true, "objectId": "Bread|-01.01|+00.81|+01.90|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|3|6|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "garbagecan"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-4.05016136, -4.05016136, 7.60913324, 7.60913324, 3.249259472, 3.249259472]], "coordinateReceptacleObjectId": ["GarbageCan", [5.18714428, 5.18714428, 5.8631954, 5.8631954, 0.032000004, 0.032000004]], "forceVisible": true, "objectId": "Bread|-01.01|+00.81|+01.90|BreadSliced_1", "receptacleObjectId": "GarbageCan|+01.30|+00.01|+01.47"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 12, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.01|+00.81|+01.90"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [81, 114, 163, 162], "mask": [[34003, 5], [34031, 12], [34297, 55], [34593, 63], [34890, 69], [35188, 72], [35487, 74], [35786, 75], [36085, 77], [36385, 77], [36684, 79], [36984, 79], [37284, 79], [37583, 80], [37883, 80], [38182, 81], [38482, 82], [38782, 82], [39082, 82], [39382, 81], [39682, 81], [39982, 81], [40281, 82], [40581, 82], [40881, 83], [41181, 83], [41481, 83], [41781, 83], [42081, 83], [42381, 83], [42681, 83], [42981, 83], [43281, 83], [43581, 83], [43881, 83], [44181, 82], [44481, 82], [44781, 82], [45081, 82], [45382, 81], [45682, 81], [45982, 81], [46282, 81], [46582, 81], [46883, 80], [47183, 80], [47484, 78], [47784, 78], [48085, 76], [48392, 66]], "point": [122, 137]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.01|+00.81|+01.90", "placeStationary": true, "receptacleObjectId": "CounterTop|-01.00|+00.95|-00.67"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 103, 299, 239], "mask": [[30603, 26], [30638, 2], [30645, 255], [30903, 26], [30939, 2], [30946, 254], [31204, 25], [31246, 254], [31505, 24], [31547, 253], [31805, 25], [31847, 283], [32147, 284], [32447, 284], [32747, 284], [33042, 2], [33046, 286], [33342, 290], [33643, 290], [33943, 290], [34244, 289], [34545, 288], [34845, 288], [35144, 289], [35443, 292], [35741, 843], [36589, 289], [37008, 165], [37309, 164], [37609, 191], [37909, 191], [38209, 191], [38509, 191], [38808, 192], [39108, 192], [39408, 192], [39708, 192], [40008, 192], [40308, 192], [40607, 193], [40907, 193], [41207, 193], [41507, 193], [41807, 193], [42107, 193], [42406, 194], [42706, 194], [43006, 194], [43306, 194], [43606, 194], [43905, 195], [44205, 195], [44505, 195], [44805, 195], [45105, 195], [45405, 195], [45704, 196], [46004, 196], [46304, 196], [46604, 196], [46904, 196], [47204, 196], [47503, 197], [47803, 197], [48103, 197], [48403, 197], [48703, 197], [49003, 197], [49302, 198], [49602, 198], [49902, 50], [49968, 132], [50202, 32], [50276, 124], [50502, 22], [50582, 118], [50801, 16], [50884, 116], [51101, 13], [51187, 113], [51401, 10], [51489, 111], [51701, 7], [51791, 109], [52001, 6], [52093, 107], [52301, 4], [52395, 105], [52600, 4], [52696, 104], [52900, 4], [52996, 104], [53200, 3], [53296, 104], [53500, 3], [53597, 103], [53800, 3], [53897, 103], [54100, 3], [54198, 102], [54399, 3], [54498, 102], [54699, 3], [54798, 102], [54999, 2], [55099, 101], [55299, 2], [55399, 101], [55599, 2], [55699, 101], [55899, 2], [55999, 101], [56198, 2], [56300, 100], [56498, 2], [56600, 100], [56798, 2], [56900, 100], [57098, 2], [57200, 100], [57398, 1], [57500, 100], [57697, 2], [57801, 99], [57997, 2], [58101, 99], [58297, 2], [58401, 34], [58438, 62], [58597, 2], [58701, 32], [58748, 52], [58897, 2], [59001, 32], [59055, 45], [59197, 1], [59301, 33], [59356, 44], [59496, 2], [59602, 32], [59657, 43], [59796, 2], [59902, 32], [59958, 42], [60096, 2], [60202, 32], [60259, 41], [60396, 2], [60502, 33], [60560, 40], [60696, 2], [60802, 33], [60860, 36], [60996, 1], [61103, 32], [61295, 2], [61403, 33], [61595, 2], [61703, 33], [61783, 15], [61895, 2], [62003, 33], [62062, 38], [62195, 2], [62303, 33], [62362, 38], [62495, 2], [62603, 34], [62662, 38], [62795, 2], [62903, 34], [62962, 38], [63094, 2], [63204, 33], [63262, 38], [63394, 2], [63504, 35], [63562, 38], [63694, 2], [63804, 39], [63862, 38], [63994, 2], [64104, 50], [64161, 39], [64294, 2], [64404, 96], [64593, 3], [64704, 96], [64893, 3], [65004, 96], [65192, 4], [65305, 95], [65492, 4], [65605, 95], [65790, 6], [65905, 191], [66205, 191], [66505, 190], [66805, 190], [67105, 190], [67405, 190], [67705, 190], [68006, 189], [68306, 189], [68606, 189], [68906, 188], [69206, 188], [69506, 188], [69806, 188], [70106, 188], [70407, 187], [70707, 187], [71007, 187], [71307, 187], [71607, 93]], "point": [149, 166]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|-01.14|+00.91|-00.82"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [273, 122, 299, 125], "mask": [[36584, 5], [36878, 22], [37173, 27], [37473, 27]], "point": [286, 122]}}, "high_idx": 4}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-01.01|+00.81|+01.90"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [212, 123, 285, 200], "mask": [[36825, 25], [37121, 34], [37419, 38], [37718, 41], [38017, 43], [38316, 45], [38615, 47], [38915, 48], [39215, 49], [39514, 51], [39813, 52], [40113, 53], [40413, 53], [40713, 54], [41012, 55], [41312, 56], [41612, 56], [41913, 55], [42213, 56], [42513, 56], [42813, 57], [43114, 56], [43414, 57], [43714, 57], [44014, 58], [44314, 58], [44615, 58], [44915, 58], [45215, 58], [45515, 59], [45816, 58], [46116, 59], [46416, 60], [46716, 60], [47016, 61], [47317, 61], [47617, 61], [47917, 61], [48217, 62], [48518, 61], [48818, 62], [49118, 62], [49418, 63], [49719, 62], [50019, 63], [50319, 63], [50619, 63], [50920, 62], [51220, 63], [51520, 63], [51820, 63], [52120, 64], [52421, 63], [52721, 63], [53021, 64], [53322, 63], [53622, 63], [53922, 63], [54223, 62], [54523, 62], [54823, 63], [55123, 63], [55424, 61], [55724, 61], [56024, 60], [56324, 60], [56625, 58], [56925, 57], [57225, 57], [57525, 57], [57826, 56], [58126, 56], [58426, 56], [58727, 54], [59027, 54], [59328, 52], [59628, 6], [59637, 42], [59970, 7]], "point": [248, 160]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|-01.14|+00.91|-00.82", "placeStationary": true, "receptacleObjectId": "CounterTop|-01.00|+00.95|-00.67"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 103, 299, 239], "mask": [[30603, 26], [30638, 2], [30645, 255], [30903, 26], [30939, 2], [30946, 254], [31204, 25], [31246, 254], [31505, 24], [31547, 253], [31805, 25], [31847, 283], [32147, 284], [32447, 284], [32747, 284], [33042, 2], [33046, 286], [33342, 290], [33643, 290], [33943, 290], [34244, 289], [34545, 288], [34845, 288], [35144, 289], [35443, 292], [35741, 180], [35945, 273], [36249, 268], [36552, 263], [36854, 46], [37008, 107], [37155, 45], [37309, 105], [37456, 44], [37609, 104], [37757, 43], [37909, 104], [38058, 42], [38209, 103], [38359, 41], [38509, 103], [38660, 40], [38808, 103], [38961, 39], [39108, 102], [39262, 38], [39408, 102], [39562, 38], [39708, 102], [39863, 37], [40008, 102], [40163, 37], [40308, 102], [40464, 36], [40607, 103], [40764, 36], [40907, 103], [41064, 36], [41207, 103], [41365, 35], [41507, 104], [41665, 35], [41807, 104], [41966, 34], [42107, 104], [42266, 34], [42406, 106], [42566, 34], [42706, 106], [42867, 33], [43006, 106], [43168, 32], [43306, 106], [43468, 32], [43606, 107], [43769, 31], [43905, 108], [44069, 31], [44205, 108], [44369, 31], [44505, 108], [44670, 30], [44805, 109], [44970, 30], [45105, 109], [45270, 30], [45405, 109], [45571, 29], [45704, 110], [45871, 29], [46004, 111], [46172, 28], [46304, 111], [46473, 27], [46604, 111], [46773, 27], [46904, 111], [47074, 26], [47204, 111], [47374, 26], [47503, 112], [47675, 25], [47803, 113], [47975, 25], [48103, 113], [48276, 24], [48403, 113], [48576, 24], [48703, 114], [48877, 23], [49003, 114], [49177, 23], [49302, 115], [49478, 22], [49602, 115], [49778, 22], [49902, 115], [50078, 22], [50202, 115], [50378, 22], [50502, 116], [50679, 21], [50801, 117], [50979, 21], [51101, 117], [51279, 21], [51401, 117], [51580, 20], [51701, 118], [51880, 20], [52001, 118], [52180, 20], [52301, 118], [52481, 19], [52600, 120], [52781, 19], [52900, 120], [53081, 19], [53200, 120], [53381, 19], [53500, 121], [53681, 19], [53800, 121], [53981, 19], [54100, 121], [54282, 18], [54399, 122], [54581, 19], [54699, 123], [54881, 19], [54999, 123], [55181, 19], [55299, 123], [55480, 20], [55599, 123], [55779, 21], [55899, 124], [56078, 22], [56198, 125], [56378, 22], [56498, 125], [56678, 22], [56798, 125], [56979, 21], [57098, 126], [57278, 22], [57398, 126], [57578, 22], [57697, 127], [57878, 22], [57997, 128], [58177, 23], [58297, 128], [58477, 23], [58597, 129], [58776, 24], [58897, 131], [59074, 26], [59197, 137], [59356, 44], [59496, 138], [59657, 43], [59796, 138], [59958, 42], [60096, 138], [60259, 41], [60396, 139], [60560, 40], [60696, 139], [60860, 36], [60996, 139], [61295, 141], [61595, 141], [61783, 15], [61895, 141], [62062, 38], [62195, 141], [62362, 38], [62495, 142], [62662, 38], [62795, 142], [62962, 38], [63094, 143], [63262, 38], [63394, 145], [63562, 38], [63694, 149], [63862, 38], [63994, 160], [64161, 39], [64294, 206], [64593, 207], [64893, 207], [65192, 208], [65492, 208], [65790, 5910]], "point": [149, 170]}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.01|+00.81|+01.90|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [216, 139, 269, 164], "mask": [[41638, 16], [41932, 28], [42228, 35], [42525, 40], [42824, 42], [43122, 45], [43421, 16], [43460, 8], [43720, 12], [43764, 5], [44019, 9], [44066, 3], [44318, 8], [44368, 1], [44619, 5], [44669, 1], [44919, 3], [44969, 1], [45219, 3], [45518, 3], [45818, 2], [46118, 2], [46417, 3], [46717, 3], [47017, 3], [47317, 2], [47617, 2], [47916, 3], [48216, 2], [48516, 2], [48817, 1], [49117, 1]], "point": [242, 143]}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.01|+00.81|+01.90|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 241], [28529, 241], [28829, 241], [29128, 241], [29427, 242], [29727, 242], [30026, 242], [30326, 242], [30625, 243], [30924, 243], [31224, 243], [31523, 243], [31823, 243], [32122, 244], [32421, 244], [32721, 244], [33020, 244], [33320, 244], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 110], [25050, 125], [25236, 103], [25359, 115], [25535, 99], [25663, 111], [25834, 97], [25966, 108], [26134, 95], [26268, 105], [26433, 95], [26569, 104], [26733, 94], [26870, 102], [27032, 94], [27171, 101], [27331, 94], [27471, 101], [27631, 94], [27772, 99], [27930, 95], [28072, 99], [28230, 94], [28372, 99], [28529, 95], [28673, 97], [28829, 95], [28973, 97], [29128, 96], [29273, 96], [29427, 98], [29572, 97], [29727, 98], [29872, 97], [30026, 99], [30172, 96], [30326, 99], [30471, 97], [30625, 100], [30771, 97], [30924, 101], [31071, 96], [31224, 101], [31371, 96], [31523, 102], [31671, 95], [31823, 102], [31971, 95], [32122, 103], [32271, 95], [32421, 104], [32571, 94], [32721, 105], [32871, 94], [33020, 106], [33171, 93], [33320, 106], [33471, 93], [33619, 107], [33770, 94], [33918, 110], [34069, 94], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.01|+00.81|+01.90|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [124, 84, 172, 114], "mask": [[25046, 4], [25339, 20], [25634, 29], [25931, 35], [26229, 39], [26528, 41], [26827, 43], [27126, 45], [27425, 46], [27725, 47], [28025, 47], [28324, 48], [28624, 49], [28924, 49], [29224, 49], [29525, 47], [29825, 47], [30125, 47], [30425, 46], [30725, 46], [31025, 46], [31325, 46], [31625, 46], [31925, 46], [32225, 46], [32525, 46], [32826, 45], [33126, 45], [33426, 45], [33726, 44], [34028, 41]], "point": [148, 98]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 241], [28529, 241], [28829, 241], [29128, 241], [29427, 242], [29727, 242], [30026, 242], [30326, 242], [30625, 243], [30924, 243], [31224, 243], [31523, 243], [31823, 243], [32122, 244], [32421, 244], [32721, 244], [33020, 244], [33320, 244], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 9}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.01|+00.81|+01.90|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "GarbageCan|+01.30|+00.01|+01.47"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [108, 104, 206, 238], "mask": [[31046, 43], [31340, 51], [31638, 55], [31935, 59], [32233, 61], [32532, 63], [32831, 64], [33130, 65], [33429, 67], [33728, 68], [34027, 69], [34325, 72], [34624, 73], [34923, 74], [35223, 74], [35522, 75], [35822, 75], [36121, 77], [36420, 78], [36720, 78], [37019, 79], [37319, 79], [37618, 80], [37918, 80], [38217, 81], [38516, 83], [38816, 83], [39115, 84], [39415, 84], [39715, 84], [40014, 85], [40314, 85], [40614, 85], [40913, 86], [41213, 87], [41513, 87], [41812, 88], [42112, 88], [42411, 89], [42711, 89], [43011, 89], [43310, 90], [43610, 91], [43910, 91], [44210, 91], [44510, 91], [44810, 91], [45110, 91], [45410, 91], [45710, 91], [46010, 91], [46309, 93], [46609, 93], [46909, 93], [47209, 93], [47509, 93], [47809, 93], [48109, 93], [48409, 93], [48709, 93], [49009, 94], [49309, 94], [49609, 94], [49908, 95], [50208, 95], [50508, 95], [50808, 95], [51108, 95], [51409, 94], [51709, 95], [52009, 95], [52309, 95], [52609, 95], [52909, 95], [53210, 94], [53510, 94], [53810, 94], [54110, 94], [54410, 95], [54710, 95], [55010, 95], [55311, 94], [55611, 94], [55911, 94], [56212, 93], [56512, 93], [56812, 94], [57113, 93], [57413, 93], [57713, 93], [58014, 92], [58314, 92], [58614, 92], [58915, 91], [59215, 91], [59516, 90], [59816, 91], [60116, 91], [60417, 90], [60718, 89], [61019, 88], [61319, 88], [61620, 87], [61921, 86], [62221, 86], [62522, 85], [62822, 84], [63122, 84], [63423, 83], [63723, 83], [64023, 82], [64324, 81], [64624, 81], [64924, 81], [65225, 79], [65525, 78], [65825, 77], [66126, 75], [66426, 74], [66726, 73], [67027, 68], [67327, 68], [67628, 66], [67928, 66], [68228, 65], [68529, 64], [68830, 62], [69131, 60], [69431, 60], [69732, 58], [70033, 57], [70334, 55], [70634, 10], [70653, 35], [70977, 11], [71285, 2]], "point": [157, 170]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan17", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": 0.75, "y": 0.908999562, "z": 3.25}, "object_poses": [{"objectName": "SprayBottle_4f8d0eee", "position": {"x": 1.284095, "y": 0.8857041, "z": 2.36918736}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": -1.26391506, "y": 1.33328843, "z": 0.9880001}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": 1.450345, "y": 0.9312309, "z": 2.15356255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": -1.47583234, "y": 0.746611655, "z": 2.83014464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": 1.128434, "y": 0.9092117, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": 1.128434, "y": 0.9115421, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": 1.117845, "y": 0.8863421, "z": 2.36918736}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": 1.088089, "y": 0.116571963, "z": 1.15741265}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 1.15598023, "y": 0.777128, "z": 0.332766771}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 1.36722, "y": 0.8874294, "z": 2.477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": 1.36534023, "y": 0.9120486, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": 1.36534023, "y": 0.9264999, "z": -0.144580841}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": -0.968678534, "y": 0.9270999, "z": -0.430464923}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.5029, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": 1.52327764, "y": 0.9456665, "z": 0.4229527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -0.08244832, "y": 0.793559432, "z": -0.494339257}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": 1.20097, "y": 0.9082927, "z": 2.692625}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": -1.19785714, "y": 0.771618962, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -1.01254034, "y": 0.7986288, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -1.28665, "y": 0.961828768, "z": -0.477608353}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": 1.21904373, "y": 0.117188632, "z": 0.732311}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": -1.2639153, "y": 1.70037234, "z": 1.0864985}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": -1.29051554, "y": 0.8205948, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": -1.01254034, "y": 0.812314868, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": -1.10519874, "y": 0.750342965, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": 1.284095, "y": 0.886848569, "z": 2.261375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": -1.357, "y": 0.7396263, "z": 3.024}, "rotation": {"x": 0.0, "y": 240.000229, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": 1.128434, "y": 0.961228848, "z": -0.144580841}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": -1.32594109, "y": 1.6566093, "z": -0.128822207}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": 1.284095, "y": 0.8840117, "z": 2.692625}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": -1.32097471, "y": 0.82212615, "z": 0.8833124}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.802, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.9998245, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": 0.951595068, "y": 0.8876286, "z": 2.15356255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": -1.513145, "y": 0.913028657, "z": -0.6474203}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": -1.19785714, "y": 0.763899863, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": 1.28637147, "y": 0.90994066, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -0.144537389, "y": 0.818769932, "z": -0.6125}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": -1.35736418, "y": 1.65497172, "z": 0.184292}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": 1.450345, "y": 0.9082927, "z": 2.692625}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -1.43509459, "y": 1.67112386, "z": 0.883266032}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": 1.444309, "y": 0.9078062, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": -1.47583234, "y": 0.7452062, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": 0.951595068, "y": 0.9312309, "z": 2.261375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": -1.13565314, "y": 0.912142038, "z": -0.817232251}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": 1.117845, "y": 0.885864258, "z": 2.80043745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_a0065d6c", "position": {"x": -1.332, "y": 0.9, "z": -0.699}, "rotation": {"x": 0.0, "y": 345.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 0.103818983, "y": 0.7596856, "z": -0.65188694}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": -1.01254034, "y": 0.749030352, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": 1.03472006, "y": 0.881300032, "z": 2.80043745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3689024817, "scene_num": 17}, "task_id": "trial_T20190906_210118_740854", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1RLO9LNUJIW5S_3EG49X351XTSU4K2T6BB16UR0GEX6V", "high_descs": ["Turn right and walk a step then take three steps left and walk to the white table in front of you with bread on it.", "Pick up the loaf of bread.", "Turn left and walk to the counter.", "Put the bread on the counter above the spatula.", "Pick up the knife that's above and to the right of the loaf of bread.", "Cut the top half of the loaf of bread into slices.", "Put the knife on the edge of the counter in front of you horizontally.", "Pick up a slice of bread from the middle of the loaf.", "Turn right to face the microwave.", "Cook the bread in the microwave then take it out and close the microwave door.", "Turn right and walk just past the fridge and then turn right and walk to the red garbage can.", "Throw the cooked slice of bread away."], "task_desc": "Put a microwaved slice of bread in the oven.", "votes": [1, 1]}, {"assignment_id": "A1DMXEJGJY02E1_3OS46CRSLIG0QFF7TRATDLX6KU3V61", "high_descs": ["Walk to the table to your right.", "Pick up the loaf of bread on the left corner of the table.", "Go the the counter to your left.", "Put the bread down on the counter to the right of the sink.", "Pick up a knife.", "Use the knife to slice half the loaf of bread.", "Put the knife down on the counter between the bread and the sink.", "Pick up a slice of bread.", "Turn to the right to face the microwave.", "Microwave the slice of bread, then take it out of the microwave.", "Turn around and walk to the trash can behind you.", "Put the slice of bread into the trash can."], "task_desc": "Throw away a microwaved slice of bread.", "votes": [1, 1]}, {"assignment_id": "A2QPMJ4GADLUW2_3SPJ033424KTI4PJILR2TO65OGPYJH", "high_descs": ["Turn right and go to the white table across the room.", "Pick up the loaf of bread on the table.", "Turn left and walk forward to the counter", "Place the bread on the counter by the spatula.", "Pick up the knife in the sink.", "Slice half of the bread loaf with the knife.", "Place the knife down on the counter.", "Pick up a slice of bread from the counter.", "Turn right and face the microwave.", "Place the bread slice in the microwave and cook it.", "Turn right and go to the waste basket underneath the toaster.", "Place the bread slice in the waste basket."], "task_desc": "Throw a warm bread slice in the waste basket. ", "votes": [1, 1]}]}}