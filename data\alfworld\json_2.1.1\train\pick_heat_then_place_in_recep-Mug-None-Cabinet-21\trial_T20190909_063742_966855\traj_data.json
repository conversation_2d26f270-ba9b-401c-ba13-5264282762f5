{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000296.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000298.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000299.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 57}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["coffeemachine"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|1|-5|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [2.792798996, 2.792798996, -5.44079876, -5.44079876, 3.764000176, 3.764000176]], "coordinateReceptacleObjectId": ["CoffeeMachine", [3.5, 3.5, -5.024, -5.024, 3.456, 3.456]], "forceVisible": true, "objectId": "Mug|+00.70|+00.94|-01.36"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-8|-11|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-2|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [2.792798996, 2.792798996, -5.44079876, -5.44079876, 3.764000176, 3.764000176]], "coordinateReceptacleObjectId": ["Cabinet", [2.0289164, 2.0289164, 0.627202988, 0.627202988, 1.383646, 1.383646]], "forceVisible": true, "objectId": "Mug|+00.70|+00.94|-01.36", "receptacleObjectId": "Cabinet|+00.51|+00.35|+00.16"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+00.70|+00.94|-01.36"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [174, 87, 216, 131], "mask": [[25980, 5], [26279, 15], [26578, 29], [26878, 30], [27177, 32], [27477, 32], [27776, 34], [28076, 34], [28376, 35], [28676, 35], [28976, 39], [29276, 40], [29576, 41], [29876, 41], [30176, 35], [30214, 3], [30475, 35], [30514, 3], [30775, 35], [30814, 3], [31075, 35], [31113, 4], [31375, 34], [31413, 3], [31675, 34], [31712, 4], [31975, 33], [32011, 4], [32275, 33], [32310, 5], [32575, 33], [32610, 4], [32875, 32], [32909, 4], [33175, 32], [33208, 4], [33475, 31], [33507, 4], [33775, 36], [34075, 34], [34375, 33], [34675, 32], [34975, 30], [35275, 29], [35574, 30], [35874, 29], [36175, 28], [36475, 28], [36775, 27], [37076, 26], [37376, 26], [37677, 24], [37978, 22], [38279, 20], [38580, 18], [38882, 15], [39184, 10]], "point": [195, 108]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+00.70|+00.94|-01.36", "placeStationary": true, "receptacleObjectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 188], [31250, 85], [31341, 97], [31550, 84], [31643, 95], [31849, 84], [31944, 94], [32149, 83], [32245, 93], [32448, 83], [32546, 92], [32748, 83], [32846, 93], [33047, 84], [33147, 92], [33347, 84], [33447, 92], [33646, 85], [33747, 92], [33946, 85], [34047, 91], [34245, 86], [34347, 91], [34545, 86], [34646, 92], [34844, 88], [34946, 92], [35144, 89], [35245, 93], [35443, 91], [35544, 93], [35743, 92], [35843, 94], [36043, 95], [36140, 97], [36342, 195], [36642, 194], [36941, 195], [37241, 195], [37540, 195], [37840, 195], [38139, 196], [38439, 195], [38738, 196], [39038, 196], [39337, 197], [39637, 196], [39936, 197], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 108], [31068, 70], [31250, 85], [31341, 14], [31370, 68], [31550, 84], [31643, 11], [31672, 66], [31849, 84], [31944, 9], [31973, 65], [32149, 83], [32245, 8], [32273, 65], [32448, 83], [32546, 7], [32574, 64], [32748, 83], [32846, 6], [32876, 63], [33047, 84], [33147, 5], [33178, 61], [33347, 84], [33447, 6], [33473, 2], [33478, 61], [33646, 85], [33747, 6], [33773, 3], [33778, 61], [33946, 85], [34047, 6], [34073, 3], [34078, 60], [34245, 86], [34347, 6], [34373, 3], [34378, 60], [34545, 86], [34646, 7], [34673, 3], [34678, 60], [34844, 88], [34946, 7], [34973, 3], [34978, 60], [35144, 89], [35245, 8], [35273, 2], [35277, 61], [35443, 91], [35544, 9], [35573, 2], [35577, 60], [35743, 92], [35843, 10], [35872, 2], [35876, 61], [36043, 95], [36140, 13], [36172, 2], [36176, 61], [36342, 111], [36472, 1], [36475, 62], [36642, 111], [36774, 62], [36941, 112], [37074, 62], [37241, 112], [37372, 64], [37540, 113], [37672, 63], [37840, 113], [37971, 64], [38139, 114], [38271, 64], [38439, 115], [38571, 63], [38738, 116], [38871, 63], [39038, 117], [39170, 64], [39337, 119], [39469, 65], [39637, 121], [39767, 66], [39936, 126], [40063, 70], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+00.70|+00.94|-01.36"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [152, 104, 177, 134], "mask": [[31058, 10], [31355, 15], [31654, 18], [31953, 20], [32253, 20], [32553, 21], [32852, 24], [33152, 26], [33453, 20], [33475, 3], [33753, 20], [33776, 2], [34053, 20], [34076, 2], [34353, 20], [34376, 2], [34653, 20], [34676, 2], [34953, 20], [34976, 2], [35253, 20], [35275, 2], [35553, 20], [35575, 2], [35853, 19], [35874, 2], [36153, 19], [36174, 2], [36453, 19], [36473, 2], [36753, 21], [37053, 21], [37353, 19], [37653, 19], [37953, 18], [38253, 18], [38554, 17], [38854, 17], [39155, 15], [39456, 13], [39758, 9], [40062, 1]], "point": [164, 118]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 162], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 188], [31250, 85], [31341, 97], [31550, 84], [31643, 95], [31849, 84], [31944, 94], [32149, 83], [32245, 93], [32448, 83], [32546, 92], [32748, 83], [32846, 93], [33047, 84], [33147, 92], [33347, 84], [33447, 92], [33646, 85], [33747, 92], [33946, 85], [34047, 91], [34245, 86], [34347, 91], [34545, 86], [34646, 92], [34844, 88], [34946, 92], [35144, 89], [35245, 93], [35443, 91], [35544, 93], [35743, 92], [35843, 94], [36043, 95], [36140, 97], [36342, 195], [36642, 194], [36941, 195], [37241, 195], [37540, 195], [37840, 195], [38139, 196], [38439, 195], [38738, 196], [39038, 196], [39337, 197], [39637, 196], [39936, 197], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+00.51|+00.35|+00.16"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [19, 126, 135, 207], "mask": [[37519, 113], [37819, 113], [38119, 113], [38420, 112], [38720, 112], [39021, 111], [39321, 111], [39622, 110], [39922, 110], [40223, 109], [40523, 109], [40823, 109], [41124, 108], [41424, 108], [41725, 108], [42025, 108], [42326, 107], [42626, 107], [42927, 106], [43227, 106], [43528, 105], [43828, 105], [44129, 104], [44429, 104], [44729, 104], [45030, 103], [45330, 103], [45631, 102], [45931, 102], [46232, 102], [46532, 102], [46833, 101], [47133, 101], [47434, 100], [47734, 100], [48035, 99], [48335, 99], [48635, 99], [48936, 98], [49236, 98], [49537, 97], [49837, 97], [50138, 96], [50438, 96], [50739, 95], [51039, 96], [51340, 95], [51640, 95], [51941, 94], [52241, 94], [52541, 94], [52842, 93], [53142, 93], [53443, 92], [53743, 92], [54044, 91], [54344, 91], [54645, 90], [54945, 90], [55246, 89], [55546, 90], [55847, 89], [56147, 89], [56448, 88], [56748, 88], [57048, 88], [57349, 87], [57649, 87], [57950, 86], [58250, 86], [58551, 85], [58851, 82], [59152, 79], [59452, 77], [59753, 75], [60053, 73], [60354, 71], [60654, 70], [60954, 69], [61255, 67], [61555, 66], [61887, 33]], "point": [77, 165]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+00.70|+00.94|-01.36", "placeStationary": true, "receptacleObjectId": "Cabinet|+00.51|+00.35|+00.16"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 126, 132, 294], "mask": [[37518, 2], [37817, 3], [37823, 104], [38117, 4], [38122, 105], [38416, 5], [38423, 104], [38716, 6], [38723, 104], [39015, 7], [39024, 104], [39315, 8], [39324, 104], [39614, 9], [39624, 104], [39913, 10], [39925, 103], [40213, 11], [40225, 103], [40512, 12], [40526, 102], [40812, 13], [40826, 102], [41111, 14], [41127, 101], [41411, 15], [41427, 101], [41710, 16], [41728, 100], [42010, 17], [42028, 100], [42309, 18], [42329, 99], [42608, 20], [42629, 99], [42908, 20], [42929, 100], [43207, 21], [43230, 99], [43507, 22], [43530, 99], [43806, 23], [43831, 98], [44106, 24], [44131, 98], [44405, 25], [44432, 97], [44704, 27], [44732, 97], [45004, 27], [45033, 96], [45303, 29], [45333, 96], [45603, 29], [45633, 96], [45902, 31], [45934, 95], [46202, 31], [46234, 95], [46501, 33], [46535, 95], [46801, 33], [46835, 95], [47100, 34], [47136, 94], [47400, 35], [47436, 94], [47700, 35], [47737, 93], [48000, 36], [48037, 93], [48300, 36], [48338, 92], [48600, 37], [48638, 85], [48727, 3], [48900, 37], [48938, 48], [49011, 12], [49027, 3], [49200, 38], [49239, 45], [49313, 10], [49327, 3], [49500, 38], [49539, 44], [49613, 10], [49628, 2], [49800, 39], [49840, 42], [49913, 10], [49928, 2], [50100, 39], [50140, 42], [50214, 9], [50228, 3], [50400, 39], [50441, 40], [50514, 9], [50529, 2], [50700, 40], [50741, 40], [50814, 9], [50829, 2], [51000, 40], [51042, 39], [51114, 8], [51129, 2], [51300, 41], [51342, 38], [51414, 8], [51429, 2], [51600, 41], [51643, 37], [51714, 8], [51729, 2], [51900, 42], [51943, 25], [51970, 9], [52014, 9], [52029, 2], [52200, 42], [52243, 24], [52271, 8], [52314, 10], [52328, 3], [52500, 43], [52544, 23], [52571, 8], [52614, 17], [52800, 43], [52844, 23], [52872, 7], [52913, 18], [53100, 44], [53145, 22], [53173, 6], [53213, 18], [53400, 44], [53445, 22], [53473, 5], [53513, 18], [53700, 45], [53746, 21], [53774, 4], [53813, 18], [54000, 45], [54046, 21], [54074, 4], [54112, 20], [54300, 45], [54347, 21], [54375, 3], [54412, 20], [54600, 46], [54647, 21], [54675, 3], [54712, 20], [54900, 46], [54948, 20], [54975, 3], [55011, 21], [55200, 47], [55248, 20], [55275, 3], [55311, 21], [55500, 47], [55548, 20], [55574, 4], [55611, 21], [55800, 48], [55849, 20], [55873, 5], [55910, 22], [56100, 48], [56149, 30], [56209, 23], [56400, 49], [56450, 33], [56506, 26], [56700, 49], [56750, 38], [56805, 27], [57000, 50], [57051, 41], [57102, 30], [57300, 50], [57351, 81], [57600, 50], [57652, 81], [57900, 51], [57952, 81], [58200, 51], [58253, 80], [58500, 52], [58553, 80], [58800, 52], [58853, 80], [59100, 53], [59154, 77], [59400, 53], [59454, 75], [59700, 54], [59755, 73], [60000, 54], [60055, 71], [60300, 55], [60356, 69], [60600, 55], [60656, 68], [60900, 55], [60957, 66], [61200, 56], [61257, 65], [61500, 56], [61800, 56], [62100, 56], [62400, 55], [62700, 55], [63000, 55], [63300, 54], [63600, 54], [63900, 54], [64200, 54], [64500, 53], [64800, 53], [65100, 53], [65400, 52], [65700, 52], [66000, 52], [66300, 51], [66600, 51], [66900, 51], [67200, 51], [67500, 50], [67800, 50], [68100, 50], [68400, 49], [68700, 49], [69000, 49], [69300, 49], [69600, 48], [69900, 48], [70200, 48], [70500, 47], [70800, 47], [71100, 47], [71400, 46], [71700, 46], [72000, 46], [72300, 46], [72600, 45], [72900, 45], [73200, 45], [73500, 44], [73800, 44], [74100, 44], [74400, 43], [74700, 43], [75000, 43], [75300, 43], [75600, 42], [75900, 42], [76200, 42], [76500, 41], [76800, 42], [77100, 42], [77400, 41], [77700, 41], [78000, 41], [78300, 41], [78600, 40], [78900, 40], [79200, 40], [79500, 39], [79800, 39], [80100, 39], [80401, 37], [80702, 36], [81003, 35], [81304, 34], [81605, 32], [81906, 31], [82207, 30], [82508, 28], [82809, 27], [83110, 26], [83411, 24], [83712, 23], [84013, 22], [84315, 20], [84616, 18], [84917, 17], [85218, 16], [85519, 14], [85820, 13], [86121, 12], [86422, 11], [86723, 9], [87024, 8], [87325, 6], [87626, 4], [87927, 3]], "point": [66, 204]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+00.51|+00.35|+00.16"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 126, 133, 294], "mask": [[37518, 2], [37817, 3], [37823, 104], [38117, 4], [38122, 105], [38416, 5], [38423, 104], [38716, 6], [38723, 104], [39015, 7], [39024, 104], [39315, 8], [39324, 104], [39614, 9], [39624, 104], [39913, 10], [39925, 103], [40213, 11], [40225, 103], [40512, 12], [40526, 102], [40812, 13], [40826, 102], [41111, 14], [41127, 101], [41411, 15], [41427, 101], [41710, 16], [41728, 100], [42010, 17], [42028, 100], [42309, 18], [42329, 99], [42608, 20], [42629, 99], [42908, 20], [42929, 100], [43207, 21], [43230, 99], [43507, 22], [43530, 99], [43806, 23], [43831, 98], [44106, 24], [44131, 98], [44405, 25], [44432, 97], [44704, 27], [44732, 97], [45004, 27], [45033, 96], [45303, 29], [45333, 96], [45603, 29], [45633, 96], [45902, 31], [45934, 95], [46202, 31], [46234, 95], [46501, 33], [46535, 95], [46801, 33], [46835, 95], [47100, 34], [47136, 94], [47400, 35], [47436, 94], [47700, 35], [47737, 93], [48000, 36], [48037, 93], [48300, 36], [48338, 92], [48600, 37], [48638, 85], [48727, 3], [48900, 37], [48938, 48], [49011, 12], [49027, 3], [49200, 38], [49239, 45], [49313, 10], [49327, 3], [49500, 38], [49539, 44], [49613, 10], [49628, 2], [49800, 39], [49840, 42], [49913, 10], [49928, 2], [50100, 39], [50140, 42], [50214, 9], [50228, 3], [50400, 39], [50441, 40], [50514, 9], [50529, 2], [50700, 40], [50741, 40], [50814, 9], [50829, 2], [51000, 40], [51042, 39], [51114, 8], [51129, 2], [51300, 41], [51342, 38], [51414, 8], [51429, 2], [51600, 41], [51643, 37], [51714, 8], [51729, 2], [51900, 42], [51943, 25], [51970, 9], [52014, 9], [52029, 2], [52200, 42], [52243, 24], [52271, 8], [52314, 10], [52328, 3], [52500, 43], [52544, 23], [52571, 8], [52614, 17], [52800, 43], [52844, 23], [52872, 7], [52913, 18], [53100, 44], [53145, 22], [53173, 6], [53213, 7], [53228, 3], [53400, 44], [53445, 22], [53473, 5], [53513, 6], [53529, 2], [53700, 45], [53746, 21], [53774, 4], [53813, 5], [53829, 2], [54000, 45], [54046, 21], [54074, 4], [54112, 5], [54130, 2], [54300, 45], [54347, 21], [54375, 3], [54412, 5], [54430, 2], [54600, 46], [54647, 21], [54675, 3], [54712, 5], [54730, 2], [54900, 46], [54948, 20], [54975, 3], [55011, 6], [55030, 2], [55200, 47], [55248, 20], [55275, 3], [55311, 6], [55330, 2], [55500, 47], [55548, 20], [55574, 4], [55611, 7], [55630, 2], [55800, 48], [55849, 20], [55873, 5], [55910, 8], [55930, 2], [56100, 48], [56149, 30], [56209, 9], [56230, 2], [56400, 49], [56450, 33], [56506, 12], [56530, 2], [56700, 49], [56750, 38], [56805, 14], [56830, 2], [57000, 50], [57051, 41], [57102, 17], [57130, 2], [57300, 50], [57351, 68], [57430, 2], [57600, 50], [57652, 67], [57729, 4], [57900, 51], [57952, 68], [58028, 5], [58200, 51], [58253, 69], [58326, 7], [58500, 52], [58553, 80], [58800, 52], [58853, 80], [59100, 53], [59154, 79], [59400, 53], [59454, 79], [59700, 54], [59755, 78], [60000, 54], [60055, 78], [60300, 55], [60356, 77], [60600, 55], [60656, 77], [60900, 55], [60957, 76], [61200, 56], [61257, 77], [61500, 56], [61800, 56], [62100, 56], [62400, 55], [62700, 55], [63000, 55], [63300, 54], [63600, 54], [63900, 54], [64200, 54], [64500, 53], [64800, 53], [65100, 53], [65400, 52], [65700, 52], [66000, 52], [66300, 51], [66600, 51], [66900, 51], [67200, 51], [67500, 50], [67800, 50], [68100, 50], [68400, 49], [68700, 49], [69000, 49], [69300, 49], [69600, 48], [69900, 48], [70200, 48], [70500, 47], [70800, 47], [71100, 47], [71400, 46], [71700, 46], [72000, 46], [72300, 46], [72600, 45], [72900, 45], [73200, 45], [73500, 44], [73800, 44], [74100, 44], [74400, 43], [74700, 43], [75000, 43], [75300, 43], [75600, 42], [75900, 42], [76200, 42], [76500, 41], [76800, 42], [77100, 42], [77400, 41], [77700, 41], [78000, 41], [78300, 41], [78600, 40], [78900, 40], [79200, 40], [79500, 39], [79800, 39], [80100, 39], [80401, 37], [80702, 36], [81003, 35], [81304, 34], [81605, 32], [81906, 31], [82207, 30], [82508, 28], [82809, 27], [83110, 26], [83411, 24], [83712, 23], [84013, 22], [84315, 20], [84616, 18], [84917, 17], [85218, 16], [85519, 14], [85820, 13], [86121, 12], [86422, 11], [86723, 9], [87024, 8], [87325, 6], [87626, 4], [87927, 3]], "point": [66, 204]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan21", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 0.0, "y": 0.869696259, "z": -1.75}, "object_poses": [{"objectName": "Potato_027e0a30", "position": {"x": 0.785668969, "y": 1.19178081, "z": -1.72512519}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "DishSponge_11e4138d", "position": {"x": -2.13780355, "y": 0.7013536, "z": -1.30794287}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_11e4138d", "position": {"x": 0.671831965, "y": 0.760119259, "z": 0.00290554762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_0ad5c562", "position": {"x": -1.88688087, "y": 0.70183146, "z": -1.19986916}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_8142ce7b", "position": {"x": 0.8662456, "y": 0.7755549, "z": -0.252836943}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_8142ce7b", "position": {"x": 0.9509703, "y": 0.898599863, "z": -0.03906952}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_93a81026", "position": {"x": 0.7204354, "y": 0.7616844, "z": -0.061030075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_93a81026", "position": {"x": 0.781749845, "y": 0.6665257, "z": 0.6306019}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_34f12e87", "position": {"x": -1.88688087, "y": 0.7023379, "z": -0.875647962}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_34f12e87", "position": {"x": 0.841818154, "y": 0.665148556, "z": 0.508934}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_cde1951c", "position": {"x": -2.097, "y": 0.9306, "z": 1.049}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_c79ff911", "position": {"x": 0.630847335, "y": 0.8091614, "z": -0.845712662}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Cup_cb0de209", "position": {"x": 0.6692861, "y": 0.6639574, "z": -2.19087481}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Cup_cb0de209", "position": {"x": 0.6616132, "y": 0.6632297, "z": 0.630601943}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_2b33f66e", "position": {"x": 0.1490823, "y": 0.9045869, "z": 0.9297057}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": 0.785668552, "y": 1.42301631, "z": -1.88037515}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": 0.877196848, "y": 0.9319887, "z": -0.245861}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_d7803c74", "position": {"x": -1.33408761, "y": 0.0515287854, "z": -3.6110003}, "rotation": {"x": 0.0, "y": 269.999878, "z": 0.0}}, {"objectName": "SoapBottle_d7803c74", "position": {"x": -1.57801187, "y": 0.695854843, "z": -3.36120987}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_87875872", "position": {"x": 0.787663, "y": 0.01202476, "z": -0.315426648}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_87875872", "position": {"x": 0.842995167, "y": 0.01202476, "z": -0.8333649}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_0ad3089c", "position": {"x": 0.149083853, "y": 0.9872266, "z": 1.1870122}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Apple_093f2b24", "position": {"x": 0.7468742, "y": 0.7188177, "z": -2.035625}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_093f2b24", "position": {"x": 0.7151491, "y": 0.826046169, "z": -0.7906662}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Pot_cde1951c", "position": {"x": -0.05182445, "y": 0.899744749, "z": 1.01547563}, "rotation": {"x": 0.0, "y": 90.00033, "z": 0.0}}, {"objectName": "Plate_2b33f66e", "position": {"x": -1.71959925, "y": 0.705755532, "z": -1.19986916}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_34f12e87", "position": {"x": -1.57801187, "y": 0.7008235, "z": -3.529788}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_d7803c74", "position": {"x": -1.05636072, "y": 0.8999009, "z": 1.10125017}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Apple_093f2b24", "position": {"x": 0.901886463, "y": 0.71809, "z": 0.569767952}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_63693e0a", "position": {"x": -1.552, "y": 0.9306, "z": 0.785}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_c79ff911", "position": {"x": 0.7994509, "y": 0.8091614, "z": -0.8457127}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Spatula_8142ce7b", "position": {"x": 0.721681535, "y": 0.6795999, "z": 0.326432049}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Potato_027e0a30", "position": {"x": -1.94914436, "y": 0.832557559, "z": -3.70596051}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_cb0de209", "position": {"x": 0.7573, "y": 0.7711859, "z": -0.680573344}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "SaltShaker_886fcaf3", "position": {"x": 0.6769987, "y": 0.0120247006, "z": 0.07674818}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_87875872", "position": {"x": 0.6216665, "y": 0.01202476, "z": -0.8987274}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_0ad5c562", "position": {"x": 0.6558765, "y": 0.8836421, "z": -0.03906952}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_11e4138d", "position": {"x": 0.841601849, "y": 0.7721204, "z": -0.570480466}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Bread_42314d78", "position": {"x": -0.8739218, "y": 0.931037, "z": 0.978991032}, "rotation": {"x": 0.029805772, "y": 24.2316341, "z": 359.895}}, {"objectName": "Lettuce_0ad3089c", "position": {"x": 0.6616132, "y": 0.7512058, "z": 0.4481}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": -1.18791234, "y": 0.100637436, "z": -3.611}, "rotation": {"x": 0.0, "y": 269.999878, "z": 0.0}}, {"objectName": "Spoon_93a81026", "position": {"x": 0.715149164, "y": 0.7736856, "z": -0.5704804}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Knife_33d4fba0", "position": {"x": 1.070188, "y": 0.9066805, "z": -0.6870161}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_288ff1e3", "position": {"x": 0.698199749, "y": 0.941000044, "z": -1.36019969}, "rotation": {"x": 0.0, "y": 270.000122, "z": 0.0}}, {"objectName": "Bowl_737b77b7", "position": {"x": 0.6769987, "y": 0.0141527653, "z": -0.119339228}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3026829850, "scene_num": 21}, "task_id": "trial_T20190909_063742_966855", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2A028LRDJB7ZB_33UKMF931CBZ0A8SGH7OOZL12LATTC", "high_descs": ["Turn left then face the coffee maker", "Pick up the mug on the coffee maker", "Turn right then head to the  microwave ", "Open the microwave heat up the mug then take it out and close the microwave", "Turn left then head to the sink cabinet ", "Open the left side of the cabinet put in the mug then close it"], "task_desc": "Put the heated mug in the sink cabinet", "votes": [1, 1]}, {"assignment_id": "AJQGWGESKQT4Y_3A1COHJ8NMMQXHP9DPF4JKMQDEY8HW", "high_descs": ["Move to the left to face the coffee maker on the counter.", "Pick the mug up from under the coffee maker.", "Turn around and cross the room and go to the left to face the microwave.", "Put the cup in the microwave and shut the door and then open the door and pick the cup up again.", "Cross the room and face the cabinets below the sink.", "Put the mug in the lower left cabinet."], "task_desc": "Put a mug in a cabinet.", "votes": [1, 1]}, {"assignment_id": "A1ELPYAFO7MANS_3DEL4X4EL9C2YDG6VXVKLJYMUJIYXR", "high_descs": ["Take a step left to the coffee maker.", "Take the mug out of the coffee maker.", "Turn around and veer left to the microwave.", "Heat the mug in the microwave.", "Turn around and veer right to the sink.", "Place the mug in the lower right cabinet, in front of the rightmost salt shaker."], "task_desc": "Place a heated mug in a cabinet.", "votes": [1, 1]}]}}