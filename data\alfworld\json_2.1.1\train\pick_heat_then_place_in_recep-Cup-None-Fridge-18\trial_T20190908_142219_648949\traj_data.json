{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000108.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000110.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000111.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000112.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000206.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000265.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000266.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000267.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000268.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000269.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000270.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000271.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000272.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000273.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000274.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000275.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000276.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000340.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000341.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000342.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000343.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000344.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000345.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000346.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000347.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000348.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000349.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000350.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000351.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000352.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000353.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000354.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000355.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000356.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000357.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000358.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000359.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000360.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000361.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000362.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000363.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000364.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000365.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000366.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000367.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000368.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000369.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000370.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000371.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000372.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000373.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000374.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000375.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000376.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000377.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000378.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000379.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000380.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000381.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000382.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000383.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000384.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000385.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000386.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000387.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000388.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000389.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000390.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000391.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000392.png", "low_idx": 66}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|2|4|2|0"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [2.49097228, 2.49097228, 1.35668, 1.35668, 6.30294848, 6.30294848]], "coordinateReceptacleObjectId": ["Cabinet", [1.0104148, 1.0104148, 1.736188888, 1.736188888, 8.53794, 8.53794]], "forceVisible": true, "objectId": "Cup|+00.62|+01.58|+00.34"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|6|2|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|4|22|1|30"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [2.49097228, 2.49097228, 1.35668, 1.35668, 6.30294848, 6.30294848]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [8.14, 8.14, 22.504, 22.504, 0.0, 0.0]], "forceVisible": true, "objectId": "Cup|+00.62|+01.58|+00.34", "receptacleObjectId": "<PERSON><PERSON>|+02.04|+00.00|+05.63"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+00.25|+02.13|+00.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 263, 160], "mask": [[35, 229], [335, 229], [635, 229], [935, 229], [1235, 229], [1535, 229], [1835, 229], [2135, 229], [2435, 229], [2735, 229], [3035, 229], [3335, 229], [3635, 229], [3935, 229], [4235, 229], [4535, 229], [4835, 229], [5135, 229], [5435, 229], [5735, 229], [6035, 229], [6335, 229], [6635, 229], [6935, 229], [7235, 229], [7535, 229], [7835, 229], [8135, 229], [8435, 229], [8735, 229], [9035, 229], [9335, 229], [9635, 229], [9935, 229], [10235, 229], [10535, 229], [10835, 229], [11135, 229], [11435, 229], [11735, 229], [12035, 229], [12335, 229], [12635, 229], [12935, 229], [13235, 229], [13535, 229], [13835, 229], [14135, 229], [14435, 229], [14735, 229], [15035, 229], [15335, 229], [15635, 229], [15935, 229], [16235, 229], [16535, 229], [16835, 229], [17135, 229], [17435, 229], [17735, 229], [18035, 229], [18335, 229], [18635, 229], [18935, 229], [19235, 229], [19535, 229], [19835, 229], [20135, 229], [20435, 229], [20735, 229], [21035, 229], [21335, 229], [21635, 229], [21935, 229], [22235, 229], [22535, 229], [22835, 229], [23135, 229], [23435, 229], [23735, 229], [24035, 229], [24335, 229], [24635, 229], [24935, 229], [25235, 229], [25535, 229], [25835, 229], [26135, 229], [26435, 229], [26735, 229], [27035, 229], [27335, 229], [27635, 229], [27935, 229], [28235, 229], [28535, 229], [28835, 229], [29135, 229], [29435, 229], [29735, 229], [30035, 229], [30335, 229], [30635, 229], [30935, 229], [31235, 229], [31535, 229], [31835, 229], [32135, 229], [32435, 229], [32735, 229], [33035, 229], [33335, 229], [33635, 229], [33935, 229], [34235, 229], [34535, 229], [34835, 229], [35135, 229], [35435, 229], [35735, 229], [36035, 229], [36335, 229], [36635, 229], [36935, 229], [37235, 229], [37535, 229], [37835, 229], [38135, 229], [38435, 229], [38735, 229], [39035, 229], [39335, 229], [39635, 229], [39935, 229], [40235, 229], [40535, 229], [40835, 229], [41135, 229], [41435, 229], [41735, 229], [42035, 229], [42335, 229], [42635, 229], [42935, 229], [43235, 229], [43535, 229], [43835, 229], [44135, 229], [44435, 229], [44735, 229], [45035, 229], [45335, 229], [45635, 229], [45935, 229], [46235, 229], [46535, 229], [46835, 229], [47136, 228], [47436, 227], [47737, 226]], "point": [149, 79]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.62|+01.58|+00.34"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [78, 82, 124, 150], "mask": [[24387, 22], [24682, 33], [24980, 39], [25278, 43], [25578, 45], [25878, 46], [26178, 47], [26478, 47], [26778, 47], [27079, 45], [27379, 45], [27679, 45], [27979, 45], [28279, 45], [28579, 45], [28879, 45], [29179, 44], [29480, 43], [29780, 43], [30080, 43], [30380, 43], [30680, 43], [30980, 43], [31280, 42], [31581, 41], [31881, 41], [32181, 41], [32481, 41], [32781, 41], [33081, 41], [33381, 40], [33682, 39], [33982, 39], [34282, 39], [34582, 39], [34882, 39], [35182, 39], [35482, 38], [35783, 37], [36083, 37], [36383, 37], [36683, 37], [36983, 37], [37283, 37], [37583, 37], [37884, 35], [38184, 35], [38484, 35], [38784, 35], [39084, 35], [39384, 35], [39684, 35], [39985, 33], [40285, 33], [40585, 33], [40885, 33], [41185, 33], [41485, 33], [41786, 31], [42087, 29], [42387, 29], [42687, 29], [42987, 29], [43287, 29], [43587, 29], [43887, 29], [44188, 28], [44488, 27], [44788, 27]], "point": [101, 115]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+00.25|+02.13|+00.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [39, 1, 299, 163], "mask": [[39, 220], [261, 39], [339, 220], [561, 39], [639, 220], [861, 39], [939, 220], [1161, 39], [1239, 220], [1461, 39], [1539, 220], [1761, 39], [1839, 220], [2061, 39], [2139, 220], [2361, 39], [2439, 220], [2661, 39], [2739, 220], [2961, 39], [3039, 220], [3261, 39], [3339, 220], [3561, 39], [3639, 220], [3861, 39], [3939, 220], [4161, 39], [4239, 220], [4461, 39], [4539, 220], [4761, 39], [4839, 220], [5061, 39], [5139, 220], [5361, 39], [5439, 220], [5661, 39], [5739, 220], [5961, 39], [6039, 220], [6261, 39], [6339, 220], [6561, 39], [6639, 220], [6861, 39], [6939, 220], [7161, 39], [7239, 220], [7461, 39], [7539, 220], [7761, 39], [7839, 220], [8061, 39], [8139, 220], [8361, 39], [8439, 220], [8661, 39], [8739, 220], [8961, 39], [9039, 220], [9261, 39], [9339, 220], [9561, 39], [9639, 220], [9861, 39], [9939, 220], [10161, 39], [10239, 220], [10461, 39], [10539, 220], [10761, 39], [10839, 220], [11061, 39], [11139, 220], [11361, 39], [11439, 220], [11661, 39], [11739, 220], [11961, 39], [12039, 220], [12261, 39], [12339, 220], [12561, 39], [12639, 220], [12861, 39], [12939, 220], [13161, 39], [13239, 220], [13461, 39], [13539, 220], [13761, 39], [13839, 220], [14061, 39], [14139, 220], [14361, 39], [14439, 220], [14661, 39], [14739, 220], [14961, 39], [15039, 220], [15261, 39], [15339, 220], [15561, 39], [15639, 220], [15861, 39], [15939, 220], [16161, 39], [16239, 220], [16461, 39], [16539, 220], [16761, 39], [16839, 220], [17061, 39], [17139, 220], [17361, 39], [17439, 220], [17661, 39], [17739, 220], [17961, 39], [18039, 220], [18261, 39], [18339, 220], [18561, 39], [18639, 220], [18861, 39], [18939, 220], [19161, 39], [19239, 220], [19461, 39], [19539, 220], [19761, 39], [19839, 220], [20061, 39], [20139, 220], [20361, 39], [20439, 220], [20661, 39], [20739, 220], [20961, 39], [21039, 220], [21261, 39], [21339, 220], [21561, 39], [21639, 220], [21861, 39], [21939, 220], [22161, 39], [22239, 220], [22461, 39], [22539, 220], [22761, 39], [22839, 220], [23061, 39], [23139, 220], [23361, 39], [23439, 220], [23661, 39], [23739, 220], [23961, 39], [24039, 220], [24261, 39], [24339, 220], [24561, 39], [24639, 220], [24861, 39], [24939, 220], [25161, 39], [25239, 220], [25461, 39], [25539, 220], [25761, 39], [25839, 220], [26061, 39], [26139, 220], [26361, 39], [26439, 220], [26661, 39], [26739, 220], [26961, 39], [27039, 220], [27261, 39], [27339, 220], [27561, 39], [27639, 220], [27861, 39], [27939, 220], [28161, 39], [28239, 220], [28461, 39], [28539, 220], [28761, 39], [28839, 220], [29061, 39], [29139, 220], [29361, 39], [29439, 220], [29661, 39], [29739, 220], [29961, 39], [30039, 220], [30261, 39], [30339, 220], [30561, 39], [30639, 220], [30861, 39], [30939, 220], [31161, 39], [31239, 220], [31461, 39], [31539, 220], [31761, 39], [31839, 220], [32061, 39], [32139, 220], [32361, 39], [32439, 220], [32661, 39], [32739, 220], [32961, 39], [33039, 220], [33261, 39], [33339, 220], [33561, 39], [33639, 220], [33861, 39], [33939, 220], [34161, 39], [34239, 220], [34461, 39], [34539, 220], [34761, 39], [34839, 220], [35061, 39], [35139, 220], [35361, 39], [35439, 220], [35661, 39], [35739, 220], [35961, 39], [36039, 220], [36261, 39], [36339, 220], [36561, 39], [36639, 220], [36861, 39], [36939, 220], [37161, 39], [37239, 220], [37461, 39], [37539, 220], [37761, 39], [37839, 220], [38061, 39], [38139, 220], [38361, 39], [38439, 220], [38661, 39], [38739, 220], [38961, 39], [39039, 220], [39261, 39], [39339, 220], [39561, 39], [39639, 220], [39861, 39], [39939, 220], [40161, 39], [40239, 220], [40461, 39], [40539, 220], [40761, 39], [40839, 220], [41061, 39], [41139, 220], [41361, 39], [41439, 220], [41661, 39], [41739, 220], [41961, 39], [42039, 220], [42261, 39], [42339, 220], [42561, 39], [42639, 220], [42861, 39], [42939, 220], [43161, 39], [43239, 220], [43461, 39], [43539, 220], [43761, 39], [43839, 220], [44061, 39], [44139, 220], [44361, 39], [44439, 220], [44661, 39], [44739, 71], [44890, 69], [44961, 39], [45039, 70], [45191, 68], [45261, 39], [45339, 70], [45491, 68], [45561, 39], [45639, 70], [45791, 68], [45861, 39], [46161, 39], [46461, 39], [46761, 39], [47061, 39], [47361, 39], [47662, 38], [47963, 37], [48274, 26], [48586, 14], [48898, 2]], "point": [169, 81]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.62|+01.58|+00.34", "placeStationary": true, "receptacleObjectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 171], [26788, 172], [27087, 173], [27387, 76], [27469, 91], [27686, 76], [27770, 89], [27985, 76], [28071, 88], [28285, 76], [28371, 88], [28584, 76], [28672, 87], [28883, 77], [28972, 87], [29183, 76], [29272, 86], [29482, 77], [29573, 85], [29781, 78], [29873, 85], [30081, 77], [30173, 85], [30380, 78], [30473, 84], [30679, 79], [30773, 84], [30978, 80], [31073, 84], [31278, 81], [31373, 84], [31577, 82], [31672, 85], [31876, 83], [31972, 84], [32176, 84], [32272, 84], [32475, 85], [32571, 85], [32775, 86], [32870, 86], [33075, 87], [33169, 86], [33375, 90], [33466, 89], [33675, 180], [33975, 180], [34275, 180], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 29], [45081, 28], [45381, 28], [45681, 28], [45981, 29], [46281, 29], [46582, 28], [46882, 29], [47182, 29], [47482, 29], [47782, 29], [48082, 29], [48383, 28], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 41], [23553, 109], [23795, 39], [23856, 106], [24094, 39], [24157, 105], [24394, 38], [24458, 104], [24693, 39], [24758, 104], [24992, 41], [25057, 104], [25292, 41], [25357, 104], [25591, 42], [25657, 104], [25890, 43], [25957, 104], [26190, 43], [26257, 103], [26489, 44], [26557, 103], [26788, 46], [26856, 104], [27087, 47], [27156, 104], [27387, 47], [27456, 7], [27469, 91], [27686, 48], [27756, 6], [27770, 89], [27985, 49], [28056, 5], [28071, 88], [28285, 49], [28356, 5], [28371, 88], [28584, 51], [28656, 4], [28672, 87], [28883, 52], [28955, 5], [28972, 87], [29183, 52], [29255, 4], [29272, 86], [29482, 53], [29555, 4], [29573, 85], [29781, 54], [29855, 4], [29873, 85], [30081, 54], [30155, 3], [30173, 85], [30380, 56], [30455, 3], [30473, 84], [30679, 57], [30754, 4], [30773, 84], [30978, 58], [31054, 4], [31073, 84], [31278, 58], [31354, 5], [31373, 84], [31577, 59], [31654, 5], [31672, 85], [31876, 60], [31954, 5], [31972, 84], [32176, 61], [32254, 6], [32272, 84], [32475, 62], [32554, 6], [32571, 85], [32775, 62], [32853, 8], [32870, 86], [33075, 63], [33153, 9], [33169, 86], [33375, 63], [33453, 12], [33466, 89], [33675, 63], [33752, 103], [33975, 63], [34052, 103], [34275, 64], [34352, 103], [34576, 64], [34650, 104], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.62|+01.58|+00.34"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [132, 79, 157, 116], "mask": [[23537, 16], [23834, 22], [24133, 24], [24432, 26], [24732, 26], [25033, 24], [25333, 24], [25633, 24], [25933, 24], [26233, 24], [26533, 24], [26834, 22], [27134, 22], [27434, 22], [27734, 22], [28034, 22], [28334, 22], [28635, 21], [28935, 20], [29235, 20], [29535, 20], [29835, 20], [30135, 20], [30436, 19], [30736, 18], [31036, 18], [31336, 18], [31636, 18], [31936, 18], [32237, 17], [32537, 17], [32837, 16], [33138, 15], [33438, 15], [33738, 14], [34038, 14], [34339, 13], [34640, 10]], "point": [144, 96]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 171], [26788, 172], [27087, 173], [27387, 76], [27469, 91], [27686, 76], [27770, 89], [27985, 76], [28071, 88], [28285, 76], [28371, 88], [28584, 76], [28672, 87], [28883, 77], [28972, 87], [29183, 76], [29272, 86], [29482, 77], [29573, 85], [29781, 78], [29873, 85], [30081, 77], [30173, 85], [30380, 78], [30473, 84], [30679, 79], [30773, 84], [30978, 80], [31073, 84], [31278, 81], [31373, 84], [31577, 82], [31672, 85], [31876, 83], [31972, 84], [32176, 84], [32272, 84], [32475, 85], [32571, 85], [32775, 86], [32870, 86], [33075, 87], [33169, 86], [33375, 90], [33466, 89], [33675, 180], [33975, 180], [34275, 180], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 31], [46281, 30], [46582, 27], [46882, 26], [47182, 25], [47482, 24], [47782, 23], [48082, 22], [48383, 20], [48683, 19], [48983, 19], [49283, 18], [49583, 18], [49883, 17], [50183, 17], [50484, 15], [50784, 15], [51084, 15], [51384, 15], [51684, 15], [51984, 15], [52285, 13], [52585, 13], [52885, 13], [53185, 13], [53485, 13], [53785, 14], [54085, 14], [54386, 14], [54686, 14], [54986, 14], [55286, 15], [55586, 15], [55886, 16], [56187, 15], [56487, 15], [56787, 16], [57087, 16], [57387, 16], [57687, 17], [57987, 17], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+02.04|+00.00|+05.63"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 43339], [43361, 273], [43666, 263], [43971, 255], [44274, 249], [44577, 243], [44880, 237], [45183, 233], [45484, 115], [45600, 114], [45786, 113], [45900, 112], [46088, 111], [46200, 111], [46389, 109], [46500, 109], [46691, 107], [46800, 108], [46992, 106], [47100, 107], [47293, 104], [47400, 106], [47594, 103], [47700, 105], [47895, 102], [48000, 104], [48196, 100], [48300, 103], [48497, 99], [48600, 102], [48798, 98], [48900, 102], [49098, 97], [49200, 101], [49399, 96], [49500, 101], [49699, 96], [49800, 100], [50000, 94], [50100, 100], [50300, 94], [50400, 99], [50601, 93], [50700, 99], [50901, 92], [51000, 99], [51201, 92], [51300, 99], [51501, 92], [51600, 99], [51801, 91], [51900, 99], [52101, 91], [52200, 98], [52402, 90], [52500, 98], [52702, 89], [52800, 98], [53002, 89], [53100, 98], [53302, 89], [53400, 98], [53602, 88], [53700, 99], [53901, 89], [54000, 99], [54201, 89], [54300, 100], [54500, 89], [54600, 100], [54800, 89], [54900, 100], [55100, 89], [55200, 101], [55399, 89], [55500, 101], [55699, 89], [55800, 102], [55998, 90], [56100, 102], [56298, 88], [56400, 102], [56598, 88], [56700, 103], [56897, 89], [57000, 103], [57197, 88], [57300, 103], [57497, 88], [57600, 104], [57796, 89], [57900, 104], [58096, 88], [58200, 104], [58396, 88], [58500, 104], [58696, 88], [58800, 105], [58995, 89], [59100, 105], [59295, 89], [59400, 105], [59595, 90], [59700, 106], [59894, 92], [60000, 106], [60194, 92], [60300, 106], [60494, 91], [60600, 107], [60793, 92], [60900, 107], [61093, 92], [61200, 107], [61393, 91], [61500, 107], [61693, 91], [61800, 108], [61992, 92], [62100, 108], [62292, 91], [62400, 108], [62592, 91], [62700, 109], [62891, 90], [63000, 109], [63191, 89], [63300, 109], [63491, 89], [63600, 110], [63790, 90], [63900, 110], [64090, 89], [64200, 110], [64390, 89], [64500, 110], [64690, 89], [64800, 111], [64989, 89], [65100, 111], [65289, 89], [65400, 111], [65589, 89], [65700, 112], [65888, 89], [66000, 112], [66188, 89], [66300, 112], [66488, 89], [66600, 113], [66787, 89], [66900, 113], [67087, 89], [67200, 113], [67387, 89], [67500, 113], [67687, 88], [67800, 114], [67986, 89], [68100, 114], [68286, 89], [68400, 114], [68586, 88], [68700, 115], [68885, 89], [69000, 115], [69185, 89], [69300, 115], [69485, 88], [69600, 116], [69784, 89], [69900, 116], [70084, 89], [70200, 116], [70384, 88], [70500, 116], [70684, 88], [70800, 117], [70983, 89], [71100, 117], [71283, 88], [71400, 117], [71583, 88], [71700, 118], [71882, 89], [72000, 118], [72182, 88], [72300, 118], [72482, 88], [72600, 118], [72782, 88], [72900, 119], [73081, 88], [73200, 119], [73381, 88], [73500, 119], [73681, 88], [73800, 120], [73980, 88], [74100, 120], [74280, 88], [74400, 120], [74580, 88], [74700, 121], [74879, 88], [75000, 121], [75179, 88], [75300, 121], [75479, 88], [75600, 121], [75779, 87], [75900, 122], [76078, 88], [76200, 122], [76378, 88], [76500, 123], [76677, 88], [76800, 123], [76977, 88], [77100, 123], [77277, 88], [77400, 124], [77576, 88], [77700, 124], [77876, 88], [78000, 125], [78175, 89], [78300, 126], [78474, 89], [78600, 126], [78774, 89], [78900, 127], [79073, 90], [79200, 127], [79373, 89], [79500, 127], [79673, 89], [79800, 128], [79972, 90], [80100, 128], [80272, 89], [80400, 128], [80572, 89], [80700, 129], [80871, 90], [81000, 130], [81170, 90], [81300, 130], [81470, 90], [81600, 131], [81769, 91], [81900, 132], [82068, 91], [82200, 133], [82367, 92], [82500, 134], [82666, 93], [82800, 136], [82964, 94], [83100, 137], [83263, 95], [83400, 139], [83561, 97], [83700, 142], [83858, 99], [84000, 147], [84153, 104], [84300, 257], [84600, 256], [84900, 256], [85200, 256], [85500, 255], [85800, 255], [86100, 255], [86400, 254], [86700, 254], [87000, 254], [87300, 253], [87600, 253], [87900, 253], [88200, 252], [88500, 252], [88800, 252], [89100, 251], [89400, 251], [89700, 251]], "point": [149, 143]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.62|+01.58|+00.34", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|+02.04|+00.00|+05.63"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[36, 71], [121, 12], [166, 37], [228, 72], [336, 70], [422, 11], [465, 38], [528, 72], [636, 70], [723, 10], [765, 37], [828, 72], [936, 69], [1024, 9], [1065, 37], [1129, 71], [1236, 69], [1325, 9], [1365, 37], [1429, 71], [1536, 69], [1626, 8], [1665, 36], [1729, 71], [1836, 69], [1926, 8], [1965, 36], [2029, 71], [2136, 69], [2227, 7], [2265, 36], [2329, 71], [2435, 70], [2527, 8], [2564, 37], [2628, 72], [2735, 71], [2827, 8], [2864, 37], [2928, 72], [3035, 71], [3127, 8], [3164, 37], [3228, 72], [3335, 71], [3426, 9], [3464, 37], [3528, 72], [3634, 73], [3725, 11], [3763, 38], [3827, 73], [3934, 73], [4025, 11], [4063, 38], [4127, 73], [4234, 74], [4324, 12], [4363, 38], [4427, 73], [4533, 76], [4623, 14], [4663, 38], [4726, 74], [4833, 78], [4922, 15], [4962, 39], [5026, 74], [5133, 79], [5220, 18], [5262, 40], [5325, 75], [5432, 84], [5518, 20], [5562, 40], [5624, 76], [5732, 106], [5861, 42], [5924, 76], [6027, 112], [6161, 43], [6223, 77], [6310, 129], [6460, 45], [6522, 218], [6760, 46], [6821, 220], [7059, 50], [7119, 234], [7357, 35982], [43361, 273], [43666, 263], [43971, 255], [44274, 249], [44577, 243], [44880, 237], [45183, 233], [45484, 230], [45786, 226], [46088, 223], [46389, 220], [46691, 217], [46992, 215], [47293, 213], [47594, 211], [47895, 209], [48196, 207], [48497, 205], [48798, 204], [49098, 203], [49399, 202], [49699, 201], [50000, 200], [50300, 199], [50601, 198], [50901, 198], [51201, 198], [51501, 198], [51801, 198], [52101, 197], [52402, 196], [52702, 196], [53002, 196], [53302, 196], [53602, 197], [53901, 198], [54201, 199], [54500, 200], [54800, 200], [55100, 201], [55399, 202], [55699, 203], [55998, 204], [56298, 204], [56598, 205], [56897, 206], [57197, 206], [57497, 88], [57586, 118], [57796, 89], [57887, 117], [58096, 88], [58187, 117], [58396, 88], [58488, 116], [58696, 88], [58789, 116], [58995, 89], [59089, 116], [59295, 89], [59390, 115], [59595, 90], [59691, 115], [59894, 92], [59992, 114], [60194, 92], [60292, 114], [60494, 91], [60593, 114], [60793, 92], [60894, 113], [61093, 92], [61194, 113], [61393, 91], [61495, 112], [61693, 91], [61796, 112], [61992, 92], [62097, 111], [62292, 91], [62397, 111], [62592, 91], [62698, 111], [62891, 90], [62999, 110], [63191, 89], [63299, 110], [63491, 89], [63600, 110], [63790, 90], [63900, 110], [64090, 89], [64200, 110], [64390, 89], [64500, 110], [64690, 89], [64800, 111], [64989, 89], [65100, 111], [65289, 89], [65400, 111], [65589, 89], [65700, 112], [65888, 89], [66000, 112], [66188, 89], [66300, 112], [66488, 89], [66600, 113], [66787, 89], [66900, 113], [67087, 89], [67200, 113], [67387, 89], [67500, 113], [67687, 88], [67800, 114], [67986, 89], [68100, 114], [68286, 89], [68400, 114], [68586, 88], [68700, 115], [68885, 89], [69000, 115], [69185, 89], [69300, 115], [69485, 88], [69600, 116], [69784, 89], [69900, 116], [70084, 89], [70200, 116], [70384, 88], [70500, 116], [70684, 88], [70800, 117], [70983, 89], [71100, 117], [71283, 88], [71400, 117], [71583, 88], [71700, 118], [71882, 89], [72000, 118], [72182, 88], [72300, 118], [72482, 88], [72600, 118], [72782, 88], [72900, 119], [73081, 88], [73200, 119], [73381, 88], [73500, 119], [73681, 88], [73800, 120], [73980, 88], [74100, 120], [74280, 88], [74400, 120], [74580, 88], [74700, 121], [74879, 88], [75000, 121], [75179, 88], [75300, 121], [75479, 88], [75600, 121], [75779, 87], [75900, 122], [76078, 88], [76200, 122], [76378, 88], [76500, 123], [76677, 88], [76800, 123], [76977, 88], [77100, 123], [77277, 88], [77400, 124], [77576, 88], [77700, 124], [77876, 88], [78000, 125], [78175, 89], [78300, 126], [78474, 89], [78600, 126], [78774, 89], [78900, 127], [79073, 90], [79200, 127], [79373, 89], [79500, 127], [79673, 89], [79800, 128], [79972, 90], [80100, 128], [80272, 89], [80400, 128], [80572, 89], [80700, 129], [80871, 90], [81000, 130], [81170, 90], [81300, 130], [81470, 90], [81600, 131], [81769, 91], [81900, 132], [82068, 91], [82200, 133], [82367, 92], [82500, 134], [82666, 93], [82800, 136], [82964, 94], [83100, 137], [83263, 95], [83400, 139], [83561, 97], [83700, 142], [83858, 99], [84000, 147], [84153, 104], [84300, 257], [84600, 256], [84900, 256], [85200, 256], [85500, 255], [85800, 255], [86100, 255], [86400, 254], [86700, 254], [87000, 254], [87300, 253], [87600, 253], [87900, 253], [88200, 252], [88500, 252], [88800, 252], [89100, 251], [89400, 251], [89700, 251]], "point": [149, 143]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+02.04|+00.00|+05.63"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[36, 71], [121, 12], [166, 8], [228, 72], [336, 70], [422, 11], [465, 9], [502, 1], [528, 72], [636, 70], [723, 10], [765, 10], [828, 72], [936, 69], [1024, 9], [1065, 10], [1129, 71], [1236, 69], [1325, 9], [1365, 10], [1429, 71], [1536, 69], [1626, 8], [1665, 10], [1729, 71], [1836, 69], [1926, 8], [1965, 10], [2029, 71], [2136, 69], [2227, 7], [2265, 10], [2329, 71], [2435, 70], [2527, 8], [2564, 11], [2628, 72], [2735, 71], [2827, 8], [2864, 11], [2928, 72], [3035, 71], [3127, 8], [3164, 11], [3228, 72], [3335, 71], [3426, 9], [3464, 11], [3500, 1], [3528, 72], [3634, 73], [3725, 11], [3763, 12], [3800, 1], [3827, 73], [3934, 73], [4025, 11], [4063, 13], [4099, 2], [4127, 73], [4234, 74], [4324, 12], [4363, 13], [4399, 2], [4427, 73], [4533, 76], [4623, 14], [4663, 13], [4699, 2], [4726, 74], [4833, 78], [4922, 15], [4962, 15], [4998, 3], [5026, 74], [5133, 79], [5220, 18], [5262, 15], [5298, 4], [5325, 75], [5432, 84], [5518, 20], [5562, 15], [5598, 4], [5624, 76], [5732, 106], [5861, 16], [5898, 5], [5924, 76], [6027, 112], [6161, 16], [6198, 6], [6223, 77], [6310, 129], [6460, 17], [6498, 7], [6522, 218], [6760, 26], [6790, 16], [6821, 220], [7059, 50], [7119, 234], [7357, 50228], [57586, 299], [57887, 297], [58187, 297], [58488, 296], [58789, 295], [59089, 295], [59390, 295], [59691, 295], [59992, 294], [60292, 293], [60593, 292], [60894, 291], [61194, 290], [61495, 289], [61796, 288], [62097, 286], [62397, 286], [62698, 283], [62999, 281], [63299, 281], [63600, 280], [63900, 279], [64200, 279], [64500, 279], [64800, 278], [65100, 278], [65400, 278], [65700, 277], [66000, 277], [66300, 277], [66600, 276], [66900, 276], [67200, 276], [67500, 275], [67800, 275], [68100, 275], [68400, 274], [68700, 274], [69000, 274], [69300, 273], [69600, 273], [69900, 273], [70200, 272], [70500, 272], [70800, 272], [71100, 271], [71400, 271], [71700, 271], [72000, 270], [72300, 270], [72600, 270], [72900, 269], [73200, 269], [73500, 269], [73800, 268], [74100, 268], [74400, 268], [74700, 267], [75000, 267], [75300, 267], [75600, 266], [75900, 266], [76200, 266], [76500, 265], [76800, 265], [77100, 265], [77400, 264], [77700, 264], [78000, 264], [78300, 263], [78600, 263], [78900, 263], [79200, 262], [79500, 262], [79800, 262], [80100, 261], [80400, 261], [80700, 261], [81000, 260], [81300, 260], [81600, 260], [81900, 259], [82200, 259], [82500, 259], [82800, 258], [83100, 258], [83400, 258], [83700, 257], [84000, 257], [84300, 257], [84600, 256], [84900, 256], [85200, 256], [85500, 255], [85800, 255], [86100, 255], [86400, 254], [86700, 254], [87000, 254], [87300, 253], [87600, 253], [87900, 253], [88200, 252], [88500, 252], [88800, 252], [89100, 251], [89400, 251], [89700, 251]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan18", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 0.0, "y": 0.900999844, "z": 3.5}, "object_poses": [{"objectName": "Pan_08687688", "position": {"x": 1.559894, "y": 0.988599956, "z": 4.2942}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": 2.09459472, "y": 1.55049694, "z": 5.626}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": -0.09347224, "y": 0.7949763, "z": 6.633133}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 1.44258, "y": 0.890598357, "z": 2.47406888}, "rotation": {"x": 2.27491e-21, "y": 89.99999, "z": 1.40334183e-14}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": 1.40386784, "y": 0.9927421, "z": 3.51128745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": 0.8978806, "y": 1.58063388, "z": 0.295221567}, "rotation": {"x": 0.0, "y": 0.0, "z": -1.70754731e-06}}, {"objectName": "Pot_73d89a5b", "position": {"x": 1.317894, "y": 0.988599956, "z": 4.2942}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": -0.6113827, "y": 0.9938294, "z": 0.486808717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": 1.238102, "y": 0.7716507, "z": 4.753865}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": 1.40386784, "y": 1.0077, "z": 0.4407184}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": 0.192699984, "y": 0.7869452, "z": 0.618839443}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": 0.168983042, "y": 1.13568866, "z": 0.21329695}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": 1.33202, "y": 0.9463248, "z": 2.52536821}, "rotation": {"x": 2.27491e-21, "y": 89.99999, "z": 1.40334183e-14}}, {"objectName": "Cup_8e8a26e1", "position": {"x": -0.6217506, "y": 0.756232858, "z": 6.354039}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": 0.124673754, "y": 0.8013998, "z": 0.451717436}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": -0.481585175, "y": 0.8021047, "z": 0.5639391}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": 2.13723, "y": 1.56507456, "z": 5.251}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 1.13578975, "y": 0.9927622, "z": 4.624859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 0.08082789, "y": 0.7601505, "z": 6.89582157}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Bowl_9f9e1c57", "position": {"x": 0.0552175343, "y": 0.143181443, "z": 0.582382739}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9f9e1c57", "position": {"x": 0.148357332, "y": 0.759126246, "z": 6.60979748}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 1.57384741, "y": 0.135627449, "z": 3.14388132}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 1.40386784, "y": 0.987700045, "z": 1.57757056}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 1.20079792, "y": 1.57459259, "z": 0.11942786}, "rotation": {"x": 0.0, "y": 0.0, "z": -1.70754731e-06}}, {"objectName": "Bread_b7525b79", "position": {"x": 0.05866337, "y": 0.829698145, "z": 6.261198}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Bowl_9f9e1c57", "position": {"x": 2.00932431, "y": 1.51464689, "z": 6.001}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_b7525b79", "position": {"x": -0.2899366, "y": 0.829698145, "z": 6.78657436}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": -0.00508391857, "y": 0.810111, "z": 6.54474449}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": 1.2923851, "y": 0.801405668, "z": 3.22306514}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": -0.0113787055, "y": 0.7722481, "z": 0.3960101}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 2.00932431, "y": 1.59014213, "z": 5.501}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": 1.50018167, "y": 1.5785743, "z": 1.28809607}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 1.48978925, "y": 2.20769954, "z": 3.79425335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 1.49322724, "y": 0.987700045, "z": 0.213347912}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": 1.4499042, "y": 0.9321, "z": 2.05034876}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 1.49530947, "y": 2.20716763, "z": 4.26298046}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": 1.67194414, "y": 1.0077, "z": 2.95079255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_bd2376ff", "position": {"x": 1.90733, "y": 0.185002416, "z": 6.40956926}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_73d89a5b", "position": {"x": 1.32, "y": 0.988599956, "z": 3.848}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": -0.6217506, "y": 0.7949763, "z": 6.479039}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": -0.4212135, "y": 0.760130465, "z": 6.30539227}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 0.3321669, "y": 0.139143527, "z": 0.582382739}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_08687688", "position": {"x": 1.3131808, "y": 0.9855794, "z": 4.28467369}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": 0.9990971, "y": 1.04216564, "z": 0.120407164}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 1.49008977, "y": 0.140191734, "z": 2.23386478}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_8e8a26e1", "position": {"x": 0.62274307, "y": 1.57573712, "z": 0.33917}, "rotation": {"x": 0.0, "y": 0.0, "z": -1.70754731e-06}}, {"objectName": "Spoon_cefc2847", "position": {"x": -0.221677959, "y": 0.7612178, "z": 6.941612}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": -0.0137853771, "y": 1.57459259, "z": 0.163176268}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 418466745, "scene_num": 18}, "task_id": "trial_T20190908_142219_648949", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1198W1SPF1R4_3K772S5NPB276IZ0QS14BUICO8TEHG", "high_descs": ["Turn right towards the brown cabinet and open the cabinet door.", "Take out the white cup from the cabinet and close the door.", "Turn right towards the microwave and open the microwave door.", "Put the white cup into the microwave and close the door. Cook the potato inside the microwave for 5 minutes. Open the microwave door and take out the white cup. Close the cabinet door.", "Turn left and walk across the room towards the black table. Stop at the refrigerator. Turn right to the refrigerator and open the door.", "Put the white cup into the refrigerator and close the door."], "task_desc": "Put a white cup into the refrigerator and close the refrigerator door. ", "votes": [1, 1, 1]}, {"assignment_id": "A1O3TWBUDONVLO_3Z9WI9EOZ2FASIRAMEC79PVH86IHKY", "high_descs": ["Turn around and walk across the room and look up to face the cabinet above the left side of the microwave.", "Pick up the cup from the cabinet above the left side of the microwave.", "Step to the right and look down to face the microwave.", "Heat the cup in the microwave and remove it.", "Turn around and walk across the room and then turn right and step forward to face the fridge.", "Place the cup in the fridge."], "task_desc": "To heat a cup as well as place it in the fridge.", "votes": [1, 1, 1]}, {"assignment_id": "ARB8SJAWXUWTD_3CP1TO84PWS3V93PW65UKW6XMWK25T", "high_descs": ["Go to the kitchen cupboards", "Take a glass out of the cupboard", "Go to the microwave", "Heat the cup in the microwave", "Go to the fridge", "Place the heated cup in the fridge"], "task_desc": "Place a heated cup in a fridge", "votes": [1, 0, 1]}]}}