{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 56}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 57}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000298.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000299.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000300.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000301.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000302.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000303.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000304.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000305.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000306.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000307.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000308.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000309.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000310.png", "low_idx": 58}, {"high_idx": 7, "image_name": "000000311.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000312.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000313.png", "low_idx": 59}, {"high_idx": 7, "image_name": "000000314.png", "low_idx": 59}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "ToiletPaper", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["toilet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-5|5|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-7.39435912, -7.39435912, 3.5171, 3.5171, 4.17053128, 4.17053128]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-6.188, -6.188, 2.744, 2.744, 0.0037424564, 0.0037424564]], "forceVisible": true, "objectId": "ToiletPaper|-01.85|+01.04|+00.88"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|0|12|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-7.39435912, -7.39435912, 3.5171, 3.5171, 4.17053128, 4.17053128]], "coordinateReceptacleObjectId": ["Drawer", [-5.3416, -5.3416, 13.318, 13.318, 1.2052, 1.2052]], "forceVisible": true, "objectId": "ToiletPaper|-01.85|+01.04|+00.88", "receptacleObjectId": "Drawer|-01.34|+00.30|+03.33"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-4|11|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-6.17569828, -6.17569828, 10.90728952, 10.90728952, 2.912, 2.912]], "coordinateReceptacleObjectId": ["CounterTop", [-6.436, -6.436, 11.324, 11.324, 2.8064, 2.8064]], "forceVisible": true, "objectId": "ToiletPaper|-01.54|+00.73|+02.73"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|0|12|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-6.17569828, -6.17569828, 10.90728952, 10.90728952, 2.912, 2.912]], "coordinateReceptacleObjectId": ["Drawer", [-5.3416, -5.3416, 13.318, 13.318, 1.2052, 1.2052]], "forceVisible": true, "objectId": "ToiletPaper|-01.54|+00.73|+02.73", "receptacleObjectId": "Drawer|-01.34|+00.30|+03.33"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-01.85|+01.04|+00.88"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [8, 102, 35, 140], "mask": [[30313, 6], [30611, 10], [30910, 11], [31209, 13], [31508, 14], [31808, 15], [32109, 14], [32409, 15], [32710, 14], [33010, 15], [33311, 14], [33611, 15], [33912, 14], [34212, 14], [34513, 14], [34813, 14], [35114, 14], [35414, 14], [35715, 14], [36015, 14], [36316, 14], [36616, 14], [36917, 14], [37217, 14], [37518, 14], [37818, 14], [38119, 14], [38419, 14], [38720, 13], [39021, 13], [39321, 13], [39622, 13], [39922, 13], [40223, 13], [40523, 13], [40824, 11], [41124, 11], [41425, 8], [41726, 6]], "point": [21, 120]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.34|+00.30|+03.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [111, 133, 285, 155], "mask": [[39711, 175], [40011, 174], [40311, 174], [40611, 173], [40911, 173], [41212, 171], [41512, 171], [41812, 170], [42112, 170], [42412, 169], [42712, 169], [43012, 168], [43313, 167], [43613, 166], [43913, 166], [44213, 165], [44513, 165], [44813, 164], [45113, 164], [45414, 162], [45714, 162], [46014, 161], [46314, 161]], "point": [198, 143]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-01.85|+01.04|+00.88", "placeStationary": true, "receptacleObjectId": "Drawer|-01.34|+00.30|+03.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [105, 134, 299, 190], "mask": [[40016, 153], [40178, 2], [40315, 154], [40476, 5], [40615, 153], [40775, 6], [40915, 153], [41075, 7], [41215, 152], [41375, 7], [41515, 152], [41674, 9], [41815, 151], [41974, 9], [42115, 151], [42273, 11], [42414, 152], [42573, 11], [42714, 151], [42872, 13], [43014, 151], [43172, 13], [43314, 150], [43471, 15], [43614, 150], [43771, 16], [43914, 149], [44070, 17], [44214, 149], [44370, 18], [44513, 150], [44670, 18], [44813, 151], [44969, 20], [45113, 151], [45269, 20], [45413, 177], [45713, 177], [46013, 178], [46313, 178], [46612, 180], [46912, 180], [47212, 181], [47512, 181], [47812, 182], [48112, 183], [48412, 183], [48712, 184], [49011, 185], [49311, 186], [49611, 186], [49911, 187], [50205, 195], [50505, 195], [50805, 195], [51105, 195], [51405, 195], [51706, 194], [52006, 194], [52306, 194], [52606, 194], [52906, 194], [53206, 194], [53507, 192], [53807, 192], [54107, 191], [54407, 190], [54707, 190], [55008, 188], [55308, 188], [55608, 187], [55908, 186], [56208, 186], [56509, 184], [56809, 183]], "point": [202, 161]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.34|+00.30|+03.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [105, 134, 299, 190], "mask": [[40016, 153], [40178, 2], [40315, 154], [40476, 5], [40615, 153], [40775, 6], [40915, 153], [41075, 7], [41215, 152], [41375, 7], [41515, 152], [41674, 9], [41815, 151], [41974, 9], [42115, 151], [42273, 11], [42414, 152], [42573, 11], [42714, 151], [42872, 13], [43014, 151], [43172, 13], [43314, 150], [43471, 15], [43614, 150], [43771, 16], [43914, 149], [44070, 17], [44214, 149], [44370, 18], [44513, 150], [44670, 18], [44813, 151], [44969, 20], [45113, 151], [45269, 20], [45413, 177], [45713, 177], [46013, 178], [46313, 178], [46612, 180], [46912, 180], [47212, 181], [47512, 181], [47812, 150], [47967, 27], [48112, 149], [48267, 28], [48412, 149], [48567, 28], [48712, 148], [48867, 29], [49011, 149], [49166, 30], [49311, 149], [49466, 31], [49611, 148], [49765, 32], [49911, 148], [50065, 33], [50205, 195], [50505, 195], [50805, 195], [51105, 195], [51405, 195], [51706, 194], [52006, 194], [52306, 194], [52606, 194], [52906, 194], [53206, 194], [53507, 192], [53807, 192], [54107, 191], [54407, 190], [54707, 190], [55008, 188], [55308, 188], [55608, 187], [55908, 186], [56208, 186], [56509, 184], [56809, 183]], "point": [202, 161]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-01.54|+00.73|+02.73"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [129, 109, 157, 149], "mask": [[32539, 10], [32837, 14], [33134, 18], [33432, 22], [33732, 23], [34031, 25], [34331, 26], [34630, 27], [34929, 28], [35229, 28], [35529, 29], [35829, 29], [36129, 29], [36429, 29], [36729, 29], [37029, 29], [37329, 29], [37629, 29], [37929, 29], [38230, 28], [38530, 28], [38830, 28], [39130, 28], [39430, 28], [39730, 28], [40030, 28], [40330, 28], [40631, 27], [40931, 27], [41231, 26], [41531, 26], [41831, 26], [42132, 25], [42432, 24], [42732, 24], [43032, 23], [43333, 22], [43635, 19], [43936, 16], [44237, 13], [44540, 8]], "point": [143, 128]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.34|+00.30|+03.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [111, 133, 285, 155], "mask": [[39711, 175], [40011, 174], [40311, 174], [40611, 173], [40911, 173], [41212, 171], [41512, 171], [41812, 170], [42112, 170], [42412, 169], [42712, 169], [43012, 168], [43313, 167], [43613, 166], [43913, 166], [44213, 165], [44513, 165], [44813, 164], [45113, 164], [45414, 162], [45714, 162], [46014, 161], [46314, 161]], "point": [198, 143]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-01.54|+00.73|+02.73", "placeStationary": true, "receptacleObjectId": "Drawer|-01.34|+00.30|+03.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [105, 134, 299, 190], "mask": [[40016, 153], [40178, 2], [40315, 154], [40476, 5], [40615, 153], [40775, 6], [40915, 153], [41075, 7], [41215, 152], [41375, 7], [41515, 152], [41674, 9], [41815, 151], [41974, 9], [42115, 151], [42273, 11], [42414, 152], [42573, 11], [42714, 151], [42872, 13], [43014, 151], [43172, 13], [43314, 150], [43471, 15], [43614, 150], [43771, 16], [43914, 149], [44070, 17], [44214, 149], [44370, 18], [44513, 150], [44670, 18], [44813, 151], [44969, 20], [45113, 151], [45269, 20], [45413, 177], [45713, 177], [46013, 178], [46313, 178], [46612, 180], [46912, 180], [47212, 181], [47512, 181], [47812, 150], [47967, 27], [48112, 149], [48267, 28], [48412, 149], [48567, 28], [48712, 148], [48867, 29], [49011, 149], [49166, 30], [49311, 149], [49466, 31], [49611, 148], [49765, 32], [49911, 148], [50065, 33], [50205, 195], [50505, 195], [50805, 195], [51105, 195], [51405, 195], [51706, 194], [52006, 194], [52306, 194], [52606, 194], [52906, 194], [53206, 194], [53507, 192], [53807, 192], [54107, 191], [54407, 190], [54707, 190], [55008, 188], [55308, 188], [55608, 187], [55908, 186], [56208, 186], [56509, 184], [56809, 183]], "point": [202, 161]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.34|+00.30|+03.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [105, 134, 299, 190], "mask": [[40016, 153], [40178, 2], [40315, 154], [40476, 5], [40615, 153], [40775, 6], [40915, 153], [41075, 7], [41215, 152], [41375, 7], [41515, 152], [41674, 9], [41815, 151], [41974, 9], [42115, 151], [42273, 11], [42414, 152], [42573, 11], [42714, 151], [42872, 13], [43014, 151], [43172, 13], [43314, 150], [43471, 15], [43614, 150], [43771, 16], [43914, 149], [44070, 17], [44214, 149], [44370, 18], [44513, 150], [44670, 18], [44813, 151], [44969, 20], [45113, 151], [45269, 20], [45413, 177], [45713, 177], [46013, 178], [46313, 178], [46612, 127], [46745, 47], [46912, 125], [47047, 45], [47212, 124], [47349, 44], [47512, 124], [47650, 43], [47812, 123], [47951, 11], [47967, 27], [48112, 123], [48251, 10], [48267, 28], [48412, 123], [48552, 9], [48567, 28], [48712, 122], [48852, 8], [48867, 29], [49011, 123], [49151, 9], [49166, 30], [49311, 123], [49451, 9], [49466, 31], [49611, 122], [49751, 8], [49765, 32], [49911, 122], [50050, 9], [50065, 33], [50205, 195], [50505, 195], [50805, 195], [51105, 195], [51405, 195], [51706, 194], [52006, 194], [52306, 194], [52606, 194], [52906, 194], [53206, 194], [53507, 192], [53807, 192], [54107, 191], [54407, 190], [54707, 190], [55008, 188], [55308, 188], [55608, 187], [55908, 186], [56208, 186], [56509, 184], [56809, 183]], "point": [202, 161]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan416", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 0.25, "y": 0.901934743, "z": 3.5}, "object_poses": [{"objectName": "Cloth_2476119a", "position": {"x": -1.52943707, "y": 0.243341655, "z": 3.062128}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_aee4958e", "position": {"x": -1.88718176, "y": 0.772414267, "z": 2.977418}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_aee4958e", "position": {"x": -1.715553, "y": 0.772414267, "z": 2.977418}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_03f7f1cb", "position": {"x": -1.54392457, "y": 0.7316718, "z": 2.87717962}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_03f7f1cb", "position": {"x": -1.87387276, "y": 1.0463047, "z": 0.68255955}, "rotation": {"x": 0.0, "y": 90.0004959, "z": 0.0}}, {"objectName": "HandTowel_83a5c12b", "position": {"x": -1.9617, "y": 1.5886662, "z": 1.32331872}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "ToiletPaper_ce90debb", "position": {"x": -1.80055928, "y": 0.03893638, "z": 1.3741399}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Candle_aee4958e", "position": {"x": -1.84859037, "y": 1.0870471, "z": 0.8137032}, "rotation": {"x": 0.0, "y": 90.0004959, "z": 0.0}}, {"objectName": "HandTowel_83a5c12b", "position": {"x": -1.513, "y": 1.461, "z": 3.96224976}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plunger_922e82e8", "position": {"x": -1.685, "y": 0.0009356141, "z": 0.327}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Towel_18af53c8", "position": {"x": 0.871, "y": 1.172, "z": 2.3569}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_03f7f1cb", "position": {"x": -1.88718176, "y": 0.7316718, "z": 2.77694154}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBar_fe649227", "position": {"x": -1.48806894, "y": 0.238199383, "z": 2.26658773}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_ce90debb", "position": {"x": -1.54392457, "y": 0.728, "z": 2.72682238}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_2476119a", "position": {"x": -1.715553, "y": 0.736211956, "z": 2.8270607}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_4b0f5684", "position": {"x": -1.84858978, "y": 1.04263282, "z": 0.879275}, "rotation": {"x": 0.0, "y": 90.0004959, "z": 0.0}}, {"objectName": "ScrubBrush_100e631d", "position": {"x": -1.849, "y": 0.0009356141, "z": 0.137}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_da02cdb4", "position": {"x": -1.77274871, "y": 1.04703689, "z": 0.551414967}, "rotation": {"x": 0.0, "y": 270.0005, "z": 0.0}}], "object_toggles": [], "random_seed": 301753200, "scene_num": 416}, "task_id": "trial_T20190908_013116_583250", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A2871R3LEPWMMK_3IAEQB9FMH1CQDURTON35TQC95VWD4", "high_descs": ["Turn left, hang a right at the shower and walk to the gray container on the floor.", "Pick up the empty toilet paper roll from the right corner of the toilet tank to your left.", "Turn around, hang a left at the shower and face left just before the wall to reach the drawers under the vanity.", "Place the empty toilet paper roll inside the right drawer on the right side and close the drawer.", "Turn left, hang a right just before the shower and head to the counter.", "Pick up the roll of toilet paper in between the sink and the blue bottle on the counter.", "Turn around, walk towards the door and turn around again to face the drawers.", "Place the roll of toilet paper inside the right drawer to the right of the empty roll."], "task_desc": "Place an empty and a full toilet paper roll in a drawer.", "votes": [1, 1]}, {"assignment_id": "A1GVTH5YS3WOK0_3IXEICO7950LDNBIOWAEC39LOO76TB", "high_descs": ["Turn left, walk towards the wall, and then turn right to find a bin to the right of the toilet.", "Take out the cardboard roll from the bin.", "Turn around, head towards the door, turn left towards the wall, and then turn left to face two sinks.", "Place the cardboard roll in the drawer below the right sink.", "Take a few step towards the middle of the two sinks.", "Pick up the toilet paper on the sink counter.", "Take a step backwards from where you are at.", "Place the toilet paper  in the drawer below the right sink."], "task_desc": "Place the toilet paper along with the cardboard roll in the drawer below the right cabinet.", "votes": [1, 1]}, {"assignment_id": "A1O3TWBUDONVLO_3RWE2M8QWKRS4ZKJPB8LU3T2M3R0NV", "high_descs": ["Turn left and walk across the room, then turn right and walk forward to face the trash can.", "Pick up the empty toilet paper roll on the back of the sink.", "Turn right and cross the room and turn left to face the right side sink.", "Place the empty toilet paper roll in the drawer below the right side sink.", "Step to the left to face the toilet paper roll between the sinks.", "Pick up the toilet paper roll on the sink.", "Step back to face the drawer below the right side sink.", "Put the toilet paper roll in the drawer beside the empty toilet paper roll."], "task_desc": "Put a full and empty toilet paper roll in the drawer under the right side sink.", "votes": [1, 1]}]}}