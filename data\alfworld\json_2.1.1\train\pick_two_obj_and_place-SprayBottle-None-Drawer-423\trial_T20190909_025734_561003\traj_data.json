{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 38}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "SprayBottle", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|11|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["spraybottle"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["SprayBottle", [-0.622688, -0.622688, 11.482688, 11.482688, 3.2556052, 3.2556052]], "coordinateReceptacleObjectId": ["CounterTop", [-1.108576296, -1.108576296, 7.72422504, 7.72422504, 3.1427888, 3.1427888]], "forceVisible": true, "objectId": "SprayBottle|-00.16|+00.81|+02.87"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-11|7|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["spraybottle", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["SprayBottle", [-0.622688, -0.622688, 11.482688, 11.482688, 3.2556052, 3.2556052]], "coordinateReceptacleObjectId": ["Drawer", [-8.997444, -8.997444, 1.1200004, 1.1200004, 1.276000144, 1.276000144]], "forceVisible": true, "objectId": "SprayBottle|-00.16|+00.81|+02.87", "receptacleObjectId": "Drawer|-02.25|+00.32|+00.28"}}, {"discrete_action": {"action": "GotoLocation", "args": ["toilet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-11|9|0|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["spraybottle"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["SprayBottle", [-11.319984, -11.319984, 12.66593552, 12.66593552, 3.8229888, 3.8229888]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-11.368, -11.368, 11.02, 11.02, 0.003943652, 0.003943652]], "forceVisible": true, "objectId": "SprayBottle|-02.83|+00.96|+03.17"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-11|7|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["spraybottle", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["SprayBottle", [-11.319984, -11.319984, 12.66593552, 12.66593552, 3.8229888, 3.8229888]], "coordinateReceptacleObjectId": ["Drawer", [-8.997444, -8.997444, 1.1200004, 1.1200004, 1.276000144, 1.276000144]], "forceVisible": true, "objectId": "SprayBottle|-02.83|+00.96|+03.17", "receptacleObjectId": "Drawer|-02.25|+00.32|+00.28"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "SprayBottle|-00.16|+00.81|+02.87"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [115, 83, 125, 142], "mask": [[24717, 3], [25017, 3], [25317, 3], [25616, 4], [25916, 4], [26216, 4], [26516, 4], [26816, 4], [27116, 4], [27416, 4], [27715, 5], [28015, 5], [28315, 5], [28616, 4], [28916, 5], [29216, 5], [29516, 5], [29816, 6], [30116, 6], [30416, 6], [30716, 6], [31016, 6], [31316, 6], [31616, 6], [31916, 6], [32216, 6], [32516, 7], [32816, 8], [33116, 8], [33416, 9], [33716, 9], [34016, 9], [34316, 9], [34616, 9], [34916, 9], [35216, 9], [35516, 10], [35816, 10], [36116, 10], [36416, 10], [36716, 10], [37016, 10], [37316, 10], [37616, 10], [37917, 9], [38217, 9], [38517, 9], [38817, 9], [39117, 9], [39417, 9], [39717, 9], [40017, 9], [40317, 9], [40618, 8], [40918, 8], [41218, 8], [41518, 8], [41818, 7], [42118, 7], [42419, 6]], "point": [120, 111]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-02.25|+00.32|+00.28"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [25, 139, 120, 169], "mask": [[41425, 93], [41725, 92], [42026, 91], [42326, 91], [42627, 89], [42927, 89], [43227, 89], [43528, 88], [43828, 88], [44129, 87], [44429, 87], [44730, 85], [45030, 86], [45331, 86], [45631, 87], [45932, 88], [46232, 88], [46533, 87], [46833, 87], [47133, 87], [47434, 86], [47734, 86], [48035, 85], [48335, 85], [48636, 85], [48936, 85], [49237, 84], [49537, 84], [49838, 83], [50138, 83], [50439, 82]], "point": [72, 153]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "SprayBottle|-00.16|+00.81|+02.87", "placeStationary": true, "receptacleObjectId": "Drawer|-02.25|+00.32|+00.28"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 139, 117, 227], "mask": [[41430, 85], [41729, 86], [42029, 86], [42328, 87], [42628, 87], [42927, 88], [43227, 88], [43526, 90], [43826, 90], [44125, 91], [44425, 91], [44725, 90], [45024, 92], [45324, 92], [45623, 93], [45923, 94], [46222, 95], [46522, 95], [46821, 96], [47121, 96], [47420, 97], [47720, 97], [48019, 98], [48319, 99], [48618, 100], [48918, 100], [49217, 101], [49517, 101], [49816, 102], [50116, 101], [50415, 102], [50715, 102], [51014, 103], [51314, 103], [51613, 104], [51913, 104], [52212, 105], [52512, 104], [52811, 105], [53111, 105], [53410, 106], [53710, 106], [54010, 106], [54309, 107], [54609, 107], [54908, 108], [55208, 107], [55507, 108], [55807, 108], [56106, 109], [56406, 109], [56705, 110], [57005, 110], [57304, 111], [57604, 111], [57903, 111], [58203, 111], [58500, 114], [58800, 114], [59100, 114], [59400, 114], [59700, 114], [60000, 114], [60300, 113], [60600, 113], [60900, 113], [61200, 113], [61500, 113], [61800, 113], [62101, 111], [62402, 110], [62703, 109], [63003, 108], [63304, 107], [63605, 105], [63906, 104], [64206, 104], [64507, 103], [64808, 102], [65109, 100], [65409, 100], [65710, 99], [66011, 98], [66311, 98], [66612, 97], [66913, 96], [67214, 95], [67514, 95], [67815, 94]], "point": [58, 182]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-02.25|+00.32|+00.28"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 139, 117, 227], "mask": [[41430, 85], [41729, 86], [42029, 86], [42328, 87], [42628, 87], [42927, 88], [43227, 88], [43526, 90], [43826, 90], [44125, 91], [44425, 91], [44725, 91], [45024, 92], [45324, 92], [45623, 93], [45923, 94], [46222, 95], [46522, 95], [46821, 96], [47121, 96], [47420, 97], [47720, 97], [48019, 98], [48319, 99], [48618, 100], [48918, 100], [49217, 101], [49517, 101], [49816, 102], [50116, 101], [50415, 102], [50715, 102], [51014, 103], [51314, 103], [51613, 104], [51913, 104], [52212, 105], [52512, 104], [52811, 105], [53111, 105], [53410, 106], [53710, 19], [53742, 74], [54010, 19], [54043, 73], [54309, 22], [54342, 74], [54609, 24], [54641, 75], [54908, 25], [54934, 1], [54942, 74], [55208, 24], [55233, 4], [55243, 72], [55507, 30], [55545, 70], [55807, 30], [55846, 69], [56106, 32], [56147, 68], [56406, 33], [56448, 67], [56705, 34], [56749, 66], [57005, 33], [57050, 65], [57304, 33], [57351, 64], [57604, 32], [57652, 63], [57903, 32], [57952, 62], [58203, 31], [58253, 61], [58500, 114], [58800, 114], [59100, 114], [59400, 114], [59700, 114], [60000, 114], [60300, 113], [60600, 113], [60900, 113], [61200, 113], [61500, 113], [61800, 113], [62101, 112], [62402, 111], [62703, 110], [63003, 110], [63304, 109], [63605, 108], [63906, 107], [64206, 107], [64507, 107], [64808, 106], [65109, 105], [65409, 105], [65710, 104], [66011, 104], [66311, 104], [66612, 103], [66913, 102], [67214, 101], [67514, 101], [67815, 100]], "point": [58, 182]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "SprayBottle|-02.83|+00.96|+03.17"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [126, 112, 134, 173], "mask": [[33429, 2], [33728, 3], [34028, 3], [34327, 4], [34627, 4], [34927, 4], [35227, 4], [35527, 4], [35827, 4], [36127, 4], [36427, 4], [36727, 4], [37027, 4], [37327, 5], [37627, 5], [37927, 5], [38227, 5], [38527, 5], [38827, 5], [39127, 5], [39427, 5], [39727, 6], [40027, 6], [40327, 6], [40626, 7], [40926, 7], [41226, 7], [41526, 7], [41826, 8], [42126, 8], [42426, 8], [42726, 8], [43026, 8], [43326, 8], [43626, 8], [43926, 9], [44226, 9], [44526, 9], [44826, 9], [45126, 9], [45426, 9], [45726, 9], [46026, 9], [46326, 9], [46626, 9], [46926, 9], [47226, 9], [47526, 9], [47826, 9], [48126, 9], [48427, 8], [48727, 8], [49027, 8], [49327, 8], [49627, 8], [49927, 8], [50227, 8], [50527, 8], [50827, 8], [51127, 7], [51427, 7], [51728, 6]], "point": [130, 141]}}, "high_idx": 5}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-02.25|+00.32|+00.28"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [25, 139, 120, 169], "mask": [[41425, 93], [41725, 93], [42026, 92], [42326, 92], [42627, 91], [42927, 91], [43227, 91], [43528, 91], [43828, 91], [44129, 90], [44429, 90], [44730, 89], [45030, 89], [45331, 88], [45631, 88], [45932, 88], [46232, 88], [46533, 87], [46833, 87], [47133, 87], [47434, 86], [47734, 86], [48035, 85], [48335, 85], [48636, 85], [48936, 85], [49237, 84], [49537, 84], [49838, 83], [50138, 83], [50439, 81]], "point": [72, 153]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "SprayBottle|-02.83|+00.96|+03.17", "placeStationary": true, "receptacleObjectId": "Drawer|-02.25|+00.32|+00.28"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 139, 117, 227], "mask": [[41430, 85], [41729, 86], [42029, 86], [42328, 87], [42628, 87], [42927, 88], [43227, 88], [43526, 90], [43826, 90], [44125, 91], [44425, 91], [44725, 91], [45024, 92], [45324, 92], [45623, 93], [45923, 94], [46222, 95], [46522, 95], [46821, 96], [47121, 96], [47420, 97], [47720, 97], [48019, 98], [48319, 99], [48618, 100], [48918, 100], [49217, 101], [49517, 101], [49816, 102], [50116, 101], [50415, 102], [50715, 102], [51014, 103], [51314, 103], [51613, 104], [51913, 104], [52212, 105], [52512, 104], [52811, 105], [53111, 105], [53410, 106], [53710, 19], [53742, 74], [54010, 19], [54043, 73], [54309, 22], [54342, 74], [54609, 24], [54641, 75], [54908, 25], [54934, 1], [54942, 74], [55208, 24], [55233, 4], [55243, 72], [55507, 30], [55545, 70], [55807, 30], [55846, 69], [56106, 32], [56147, 68], [56406, 33], [56448, 66], [56705, 34], [56749, 65], [57005, 33], [57050, 64], [57304, 33], [57351, 62], [57604, 32], [57652, 61], [57903, 32], [57952, 61], [58203, 31], [58253, 60], [58500, 113], [58800, 113], [59100, 112], [59400, 112], [59700, 112], [60000, 112], [60300, 112], [60600, 112], [60900, 112], [61200, 112], [61500, 112], [61800, 112], [62101, 111], [62402, 110], [62703, 109], [63003, 109], [63304, 108], [63605, 107], [63906, 106], [64206, 106], [64507, 105], [64808, 104], [65109, 103], [65409, 103], [65710, 102], [66011, 101], [66311, 101], [66612, 100], [66913, 99], [67214, 98], [67514, 99], [67815, 98]], "point": [58, 182]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-02.25|+00.32|+00.28"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 139, 117, 227], "mask": [[41430, 85], [41729, 86], [42029, 86], [42328, 87], [42628, 87], [42927, 88], [43227, 88], [43526, 90], [43826, 90], [44125, 91], [44425, 91], [44725, 91], [45024, 92], [45324, 92], [45623, 93], [45923, 94], [46222, 95], [46522, 95], [46821, 96], [47121, 96], [47420, 97], [47720, 97], [48019, 98], [48319, 99], [48618, 100], [48918, 100], [49217, 18], [49247, 71], [49517, 18], [49548, 70], [49816, 22], [49848, 70], [50116, 23], [50147, 70], [50415, 24], [50440, 1], [50447, 70], [50715, 23], [50739, 4], [50749, 68], [51014, 28], [51050, 67], [51314, 29], [51351, 66], [51613, 31], [51652, 65], [51913, 31], [51953, 64], [52212, 33], [52254, 63], [52512, 32], [52555, 61], [52811, 31], [52856, 60], [53111, 30], [53156, 60], [53410, 31], [53457, 59], [53710, 19], [53757, 59], [54010, 19], [54058, 58], [54309, 22], [54358, 58], [54609, 24], [54659, 57], [54908, 25], [54934, 1], [54959, 57], [55208, 24], [55233, 4], [55259, 56], [55507, 30], [55560, 55], [55807, 30], [55860, 55], [56106, 32], [56160, 55], [56406, 33], [56460, 55], [56705, 34], [56759, 56], [57005, 33], [57058, 57], [57304, 33], [57351, 64], [57604, 32], [57652, 63], [57903, 32], [57952, 62], [58203, 31], [58253, 61], [58500, 114], [58800, 114], [59100, 114], [59400, 114], [59700, 114], [60000, 114], [60300, 113], [60600, 113], [60900, 113], [61200, 113], [61500, 113], [61800, 113], [62101, 112], [62402, 111], [62703, 110], [63003, 110], [63304, 109], [63605, 108], [63906, 107], [64206, 107], [64507, 107], [64808, 106], [65109, 105], [65409, 105], [65710, 104], [66011, 104], [66311, 104], [66612, 103], [66913, 102], [67214, 101], [67514, 101], [67815, 100]], "point": [58, 181]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan423", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -1.25, "y": 0.9019853, "z": 2.0}, "object_poses": [{"objectName": "Cloth_3812449a", "position": {"x": -2.03704929, "y": 0.8119264, "z": 0.2175}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBar_57e826c8", "position": {"x": -1.81649792, "y": 0.01789141, "z": 0.109304518}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBar_57e826c8", "position": {"x": -2.19714975, "y": 0.8130697, "z": 0.0925}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_ce702ff7", "position": {"x": -0.155672, "y": 0.8139013, "z": 2.870672}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_10d8a609", "position": {"x": -0.03430009, "y": 1.53700006, "z": 3.10500026}, "rotation": {"x": 0.0, "y": 90.0003, "z": 0.0}}, {"objectName": "Towel_1c670900", "position": {"x": -1.513, "y": 1.496, "z": 0.109}, "rotation": {"x": 0.0, "y": 1.70754709e-06, "z": 0.0}}, {"objectName": "Cloth_3812449a", "position": {"x": -3.60650039, "y": 0.190482587, "z": 1.4148283}, "rotation": {"x": 1.40334191e-14, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBar_57e826c8", "position": {"x": -1.74300432, "y": 0.01789141, "z": 0.194347724}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_2c74e5b0", "position": {"x": -2.11709952, "y": 0.809999943, "z": 0.404999971}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plunger_a91f9bf0", "position": {"x": -2.53099918, "y": 0.000522919, "z": 3.026999}, "rotation": {"x": -0.00118147617, "y": 0.000440507982, "z": 0.0007804241}}, {"objectName": "Candle_f9e80138", "position": {"x": -3.12214971, "y": 0.813007534, "z": 0.467499971}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_01c27901", "position": {"x": -2.954996, "y": 0.9549351, "z": 3.193276}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "ToiletPaper_0148fbc8", "position": {"x": -2.40702558, "y": 0.146770835, "z": 3.2182}, "rotation": {"x": 2.94521351e-06, "y": -6.770324e-05, "z": 270.081}}, {"objectName": "SprayBottle_ce702ff7", "position": {"x": -2.829996, "y": 0.9557472, "z": 3.16648388}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "ScrubBrush_a6463e50", "position": {"x": -2.33284664, "y": 0.0009859279, "z": 3.163393}, "rotation": {"x": 8.48140044e-05, "y": -2.5633768e-05, "z": 0.000171870561}}], "object_toggles": [], "random_seed": 640917344, "scene_num": 423}, "task_id": "trial_T20190909_025734_561003", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "AFPMG8TLP1TND_33F859I56949ZFNGG1ZEAMCRLKDBHS", "high_descs": ["turn left and walk towards the door then turn right towards the mirror", "pick up the spray bottle below the mirror on the counter", "turn right and walk straight then turn right again and walk towards the toilet and turn left towards the sink ", "open the drawer and put the bottle inside and close it", "turn around and walk towards the toilet and look up", "pick up the spray bottle on the top of the toilet", "turn around and walk straight to the sink", "open the drawer and put the spray bottle inside and close it"], "task_desc": "put spray bottle's away inside the drawer below the sink", "votes": [1, 1]}, {"assignment_id": "A2A028LRDJB7ZB_3IFS6Q0HJLA8CWF2X53U0NN6PLLSIA", "high_descs": ["Turn left the head to the end of the sink counter", "Pick up the spray bottle on the counter", "Turn right then head to the drawers of the sink", "Open the sink drawer put in the spray bottle then close it", "Turn left then head to the toilet tank", "Pick up the spray bottle on the tank", "Turn left then face the sink drawer", "Put the spray bottle in the sink drawer"], "task_desc": "Put the two spray bottle in the sink drawer", "votes": [1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3LUY3GC632REAKIEMXV3ETSMOLWP7P", "high_descs": ["move to the left a bit and face the bathroom sink counter to the left of the bathroom sink", "grab the spray bottle off of the top of the bathroom counter top", "turn around and walk over to the bathroom sink cabinet on the left side of the room", "place the spray bottle inside the bottom left pullout drawer there", "turn left around and walk forwards to the toilet up ahead", "grab the spray bottle off of the back side of the top of the toilet there", "turn around and walk forwards a bit to the bathroom sink cabinet", "place the spray bottle in the bottom left pullout drawer"], "task_desc": "place two spray bottles inside of the bottom left sink cabinet drawer", "votes": [1, 1]}]}}