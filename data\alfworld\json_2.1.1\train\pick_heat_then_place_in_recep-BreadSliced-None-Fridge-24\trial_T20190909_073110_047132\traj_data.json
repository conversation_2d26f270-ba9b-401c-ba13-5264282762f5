{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000199.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000200.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000201.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000202.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000204.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000206.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000207.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000289.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000290.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000291.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000330.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000331.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000332.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000333.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000335.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000336.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000337.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000338.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000339.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000340.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000341.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000342.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000343.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000344.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000345.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000346.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000347.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000348.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000349.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000350.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000351.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000352.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000353.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000354.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000355.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000356.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000357.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000358.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000359.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000360.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000361.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000362.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000363.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000364.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000365.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000366.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000367.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000368.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000369.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000370.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000371.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000372.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000373.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000374.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000375.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000376.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000377.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000378.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000379.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000380.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000381.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000382.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000383.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000384.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000385.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000386.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000387.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000388.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000389.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000390.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000391.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000392.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000393.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000394.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000395.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000396.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000397.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000398.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000399.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000400.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000401.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000402.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000403.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000404.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000405.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000406.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000407.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000408.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000409.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000410.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000411.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000412.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000413.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000414.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000415.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000416.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000417.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000418.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000419.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000420.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000421.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000422.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000423.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000424.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000425.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000426.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000427.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000428.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000429.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000430.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000431.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000432.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000433.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000434.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000435.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000436.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000437.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000438.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000439.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000440.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000441.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000442.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000443.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000444.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000445.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000446.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000447.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000448.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000449.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000450.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000451.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000452.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000453.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000454.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000455.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000456.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000457.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000458.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000459.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000460.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000461.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000462.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000463.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000464.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000465.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000466.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000467.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000468.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000469.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000470.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000471.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000472.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000473.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000474.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000475.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000476.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000477.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000478.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000479.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000480.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000481.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000482.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000483.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000484.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000485.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000486.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000487.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000488.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000489.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000490.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000491.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000492.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000493.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000494.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000495.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000496.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000497.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000498.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000499.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000500.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000501.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000502.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000503.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000504.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000505.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000506.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000507.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000508.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000509.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000510.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000511.png", "low_idx": 83}, {"high_idx": 11, "image_name": "000000512.png", "low_idx": 83}, {"high_idx": 11, "image_name": "000000513.png", "low_idx": 83}, {"high_idx": 11, "image_name": "000000514.png", "low_idx": 83}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|1|11|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [3.501492, 3.501492, 10.52786, 10.52786, 3.2925684, 3.2925684]], "coordinateReceptacleObjectId": ["CounterTop", [3.4584, 3.4584, 9.4872, 9.4872, 3.4344, 3.4344]], "forceVisible": true, "objectId": "ButterKnife|+00.88|+00.82|+02.63"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-7|6|1|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-4.74204636, -4.74204636, 5.81047488, 5.81047488, 3.8152024, 3.8152024]], "forceVisible": true, "objectId": "Bread|-01.19|+00.95|+01.45"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-6|13|0|30"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "microwave"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [3.501492, 3.501492, 10.52786, 10.52786, 3.2925684, 3.2925684]], "coordinateReceptacleObjectId": ["Microwave", [-6.116, -6.116, 15.5288, 15.5288, 5.00112532, 5.00112532]], "forceVisible": true, "objectId": "ButterKnife|+00.88|+00.82|+02.63", "receptacleObjectId": "Microwave|-01.53|+01.25|+03.88"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-7|6|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-4.74204636, -4.74204636, 5.81047488, 5.81047488, 3.8152024, 3.8152024]], "coordinateReceptacleObjectId": ["DiningTable", [-3.5, -3.5, 5.296, 5.296, 0.024, 0.024]], "forceVisible": true, "objectId": "Bread|-01.19|+00.95|+01.45|BreadSliced_5"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-6|13|0|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-6.116, -6.116, 15.5288, 15.5288, 5.00112532, 5.00112532]], "forceVisible": true, "objectId": "Microwave|-01.53|+01.25|+03.88"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-7|9|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "fridge"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-4.74204636, -4.74204636, 5.81047488, 5.81047488, 3.8152024, 3.8152024]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-10.572, -10.572, 8.536, 8.536, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-01.19|+00.95|+01.45|BreadSliced_5", "receptacleObjectId": "<PERSON><PERSON>|-02.64|+00.00|+02.13"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|+00.88|+00.82|+02.63"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [179, 80, 186, 135], "mask": [[23879, 3], [24179, 3], [24479, 4], [24779, 4], [25079, 4], [25379, 5], [25679, 5], [25979, 6], [26279, 6], [26579, 6], [26879, 6], [27179, 6], [27479, 6], [27779, 6], [28079, 6], [28380, 5], [28680, 5], [28980, 5], [29280, 5], [29580, 5], [29880, 5], [30180, 5], [30480, 5], [30780, 4], [31080, 4], [31380, 4], [31680, 4], [31981, 3], [32281, 3], [32581, 3], [32881, 3], [33181, 3], [33481, 3], [33781, 3], [34081, 3], [34381, 4], [34681, 4], [34981, 4], [35281, 4], [35581, 4], [35880, 6], [36180, 6], [36480, 6], [36780, 6], [37080, 6], [37380, 6], [37680, 7], [37980, 7], [38280, 7], [38581, 6], [38881, 6], [39181, 6], [39481, 6], [39781, 6], [40082, 5], [40382, 4]], "point": [182, 106]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-01.19|+00.95|+01.45"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [122, 139, 207, 205], "mask": [[41564, 1], [41855, 19], [42151, 27], [42448, 33], [42746, 37], [43043, 42], [43342, 45], [43640, 48], [43939, 50], [44237, 54], [44536, 56], [44835, 58], [45134, 60], [45434, 61], [45733, 63], [46032, 65], [46331, 67], [46630, 69], [46930, 70], [47229, 71], [47528, 73], [47828, 74], [48127, 75], [48427, 76], [48726, 78], [49026, 78], [49325, 80], [49625, 80], [49924, 81], [50224, 82], [50524, 82], [50823, 83], [51123, 84], [51423, 84], [51722, 85], [52022, 85], [52322, 85], [52622, 85], [52922, 86], [53222, 86], [53522, 85], [53822, 85], [54123, 84], [54423, 84], [54723, 83], [55024, 82], [55324, 82], [55624, 81], [55925, 80], [56225, 79], [56526, 78], [56827, 76], [57127, 76], [57428, 74], [57728, 73], [58029, 71], [58330, 69], [58632, 66], [58933, 64], [59234, 61], [59536, 58], [59838, 54], [60140, 50], [60442, 46], [60746, 39], [61049, 33], [61354, 22]], "point": [164, 171]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|+00.88|+00.82|+02.63", "placeStationary": true, "receptacleObjectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 69], [32198, 57], [32259, 133], [32400, 58], [32569, 123], [32700, 51], [32869, 123], [33000, 50], [33169, 122], [33300, 154], [33457, 134], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 5}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.19|+00.95|+01.45|BreadSliced_5"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [134, 197, 196, 213], "mask": [[58953, 27], [59246, 40], [59541, 49], [59839, 54], [60137, 57], [60435, 61], [60734, 63], [61034, 63], [61335, 61], [61636, 59], [61938, 56], [62239, 53], [62541, 50], [62843, 45], [63147, 38], [63450, 32], [63756, 19]], "point": [165, 204]}}, "high_idx": 7}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.19|+00.95|+01.45|BreadSliced_5", "placeStationary": true, "receptacleObjectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 69], [32198, 57], [32259, 133], [32400, 58], [32569, 123], [32700, 51], [32869, 123], [33000, 50], [33169, 122], [33300, 154], [33457, 134], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 6126], [6133, 293], [6434, 291], [6735, 289], [7036, 288], [7337, 286], [7638, 284], [7939, 283], [8239, 283], [8540, 282], [8840, 281], [9141, 280], [9442, 279], [9742, 279], [10043, 278], [10343, 277], [10644, 276], [10944, 276], [11244, 276], [11544, 276], [11845, 275], [12145, 274], [12445, 274], [12745, 274], [13046, 273], [13346, 273], [13646, 273], [13947, 272], [14247, 272], [14547, 272], [14847, 272], [15147, 272], [15447, 272], [15747, 272], [16047, 272], [16347, 272], [16647, 273], [16947, 273], [17248, 272], [17548, 272], [17848, 272], [18148, 272], [18448, 272], [18748, 272], [19048, 272], [19348, 272], [19647, 273], [19947, 273], [20247, 273], [20547, 273], [20847, 273], [21147, 273], [21447, 273], [21747, 273], [22047, 273], [22347, 273], [22647, 274], [22947, 274], [23246, 275], [23546, 275], [23846, 275], [24146, 275], [24446, 276], [24746, 276], [25045, 154], [25200, 122], [25345, 154], [25500, 122], [25645, 154], [25800, 122], [25944, 154], [26100, 123], [26244, 154], [26400, 123], [26544, 154], [26700, 123], [26843, 155], [27000, 123], [27143, 154], [27300, 123], [27443, 154], [27600, 124], [27742, 155], [27900, 124], [28042, 154], [28200, 125], [28341, 155], [28500, 125], [28640, 156], [28800, 126], [28939, 157], [29100, 126], [29239, 156], [29400, 127], [29538, 157], [29700, 128], [29836, 159], [30000, 130], [30135, 159], [30300, 132], [30433, 161], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 69], [32198, 57], [32259, 133], [32400, 58], [32569, 123], [32700, 51], [32869, 123], [33000, 50], [33169, 122], [33300, 154], [33457, 134], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-6.116, -6.116, 15.5288, 15.5288, 5.00112532, 5.00112532]], "forceVisible": true, "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-6.116, -6.116, 15.5288, 15.5288, 5.00112532, 5.00112532]], "forceVisible": true, "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.19|+00.95|+01.45|BreadSliced_5"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [119, 21, 147, 102], "mask": [[6126, 7], [6426, 8], [6725, 10], [7024, 12], [7324, 13], [7623, 15], [7922, 17], [8222, 17], [8522, 18], [8822, 18], [9121, 20], [9421, 21], [9721, 21], [10021, 22], [10321, 22], [10620, 24], [10920, 24], [11220, 24], [11520, 24], [11820, 25], [12120, 25], [12419, 26], [12719, 26], [13019, 27], [13319, 27], [13619, 27], [13919, 28], [14219, 28], [14519, 28], [14819, 28], [15119, 28], [15419, 28], [15719, 28], [16019, 28], [16319, 28], [16619, 28], [16920, 27], [17220, 28], [17520, 28], [17820, 28], [18120, 28], [18420, 28], [18720, 28], [19020, 28], [19320, 28], [19620, 27], [19920, 27], [20220, 27], [20520, 27], [20820, 27], [21120, 27], [21420, 27], [21720, 27], [22020, 27], [22320, 27], [22620, 27], [22921, 26], [23221, 25], [23521, 25], [23821, 25], [24121, 25], [24421, 25], [24722, 24], [25022, 23], [25322, 23], [25622, 23], [25922, 22], [26223, 21], [26523, 21], [26823, 20], [27123, 20], [27423, 20], [27724, 18], [28024, 18], [28325, 16], [28625, 15], [28926, 13], [29226, 13], [29527, 11], [29828, 8], [30130, 5], [30432, 1]], "point": [133, 60]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 69], [32198, 57], [32259, 133], [32400, 58], [32569, 123], [32700, 51], [32869, 123], [33000, 50], [33169, 122], [33300, 154], [33457, 134], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.64|+00.00|+02.13"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 212], "mask": [[0, 4499], [4500, 298], [4800, 298], [5100, 297], [5400, 297], [5700, 296], [6000, 296], [6300, 295], [6600, 295], [6900, 294], [7200, 294], [7500, 293], [7800, 293], [8100, 292], [8400, 292], [8700, 291], [9000, 291], [9300, 290], [9600, 290], [9900, 289], [10200, 289], [10500, 288], [10800, 288], [11100, 287], [11400, 286], [11700, 286], [12000, 285], [12300, 285], [12600, 284], [12900, 284], [13200, 283], [13500, 283], [13800, 282], [14100, 282], [14400, 281], [14700, 281], [15000, 280], [15300, 280], [15600, 279], [15900, 279], [16200, 278], [16500, 278], [16800, 277], [17100, 277], [17400, 276], [17700, 276], [18000, 275], [18300, 274], [18600, 274], [18900, 273], [19200, 273], [19500, 272], [19800, 272], [20100, 271], [20400, 271], [20700, 270], [21000, 270], [21300, 269], [21600, 269], [21900, 268], [22200, 268], [22500, 267], [22800, 267], [23100, 266], [23400, 266], [23700, 265], [24000, 265], [24300, 264], [24600, 264], [24900, 263], [25200, 262], [25500, 262], [25800, 261], [26100, 261], [26400, 260], [26700, 260], [27000, 259], [27300, 259], [27600, 258], [27900, 258], [28200, 257], [28500, 257], [28800, 256], [29100, 256], [29400, 255], [29700, 255], [30000, 254], [30300, 254], [30600, 253], [30900, 253], [31200, 252], [31500, 252], [31800, 251], [32100, 250], [32400, 250], [32700, 249], [33000, 249], [33300, 248], [33600, 248], [33900, 247], [34200, 247], [34500, 246], [34800, 246], [35100, 245], [35400, 245], [35700, 244], [36000, 244], [36300, 243], [36600, 243], [36900, 242], [37200, 242], [37500, 241], [37800, 241], [38100, 240], [38400, 240], [38700, 239], [39000, 238], [39300, 238], [39600, 237], [39900, 237], [40200, 236], [40500, 236], [40800, 235], [41100, 235], [41400, 234], [41700, 234], [42000, 233], [42300, 233], [42600, 232], [42901, 231], [43202, 229], [43503, 228], [43804, 226], [44105, 225], [44406, 223], [44707, 222], [45008, 220], [45309, 219], [45610, 217], [45911, 215], [46212, 214], [46513, 212], [46814, 211], [47115, 209], [47416, 208], [47717, 206], [48018, 205], [48319, 203], [48619, 203], [48920, 201], [49221, 200], [49522, 198], [49823, 197], [50124, 195], [50425, 194], [50726, 192], [51027, 191], [51328, 189], [51629, 188], [51930, 186], [52231, 185], [52532, 183], [52833, 181], [53134, 180], [53435, 178], [53736, 177], [54037, 175], [54338, 174], [54638, 173], [54939, 172], [55240, 170], [55541, 169], [55842, 167], [56143, 166], [56444, 164], [56745, 163], [57046, 161], [57347, 160], [57648, 158], [57949, 157], [58250, 155], [58551, 154], [58852, 152], [59153, 150], [59454, 149], [59755, 147], [60056, 146], [60356, 145], [60657, 144], [60958, 142], [61259, 141], [61560, 139], [61861, 138], [62162, 136], [62467, 128], [62802, 5], [62809, 5], [63103, 3], [63109, 5], [63403, 3], [63410, 3]], "point": [149, 105]}}, "high_idx": 11}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.19|+00.95|+01.45|BreadSliced_5", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.64|+00.00|+02.13"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 8587], [8602, 251], [8902, 251], [9202, 251], [9502, 250], [9802, 250], [10102, 250], [10402, 250], [10702, 250], [11001, 251], [11301, 251], [11601, 251], [11900, 252], [12200, 252], [12500, 253], [12799, 254], [13099, 254], [13398, 255], [13697, 257], [13997, 257], [14296, 258], [14595, 260], [14894, 262], [15193, 264], [15492, 265], [15791, 267], [16091, 268], [16389, 271], [16687, 274], [16986, 276], [17284, 280], [17582, 284], [17881, 286], [18178, 40818], [58997, 299], [59297, 299], [59597, 299], [59897, 299], [60197, 299], [60497, 299], [60797, 767], [61566, 129], [61697, 167], [61867, 128], [61998, 165], [62167, 128], [62298, 165], [62467, 128], [62598, 165], [62898, 165], [63198, 165], [63498, 165], [63798, 164], [64098, 164], [64398, 164], [64698, 164], [64998, 164], [65299, 163], [65599, 162], [65899, 162], [66199, 162], [66499, 162], [66799, 162], [67099, 162], [67399, 161], [67699, 161], [67999, 101], [68102, 58], [68299, 101], [68404, 56], [68600, 100], [68706, 54], [68900, 100], [69008, 52], [69200, 100], [69310, 49], [69500, 100], [69612, 47], [69800, 100], [69915, 44], [70100, 100], [70217, 42], [70400, 100], [70519, 39], [70700, 100], [70821, 37], [71000, 100], [71123, 35], [71300, 100], [71425, 32], [71601, 99], [71727, 30], [71901, 99], [72029, 28], [72201, 99], [72331, 26], [72501, 99], [72633, 24], [72801, 99], [72936, 21], [73102, 98], [73238, 18], [73404, 96], [73540, 16], [73706, 94], [74007, 93], [74308, 92], [74610, 90], [74911, 89], [75212, 88], [75512, 88], [75812, 88], [76111, 89], [76411, 89], [76711, 89], [77010, 90], [77309, 91], [77608, 92], [77907, 93], [78205, 95], [78504, 96], [78803, 97], [79103, 97], [79403, 97], [79703, 97], [80003, 97], [80303, 97], [80603, 97], [80903, 97], [81204, 96], [81504, 96], [81804, 96], [82104, 96], [82404, 96], [82704, 96], [83004, 96], [83304, 96], [83604, 96], [83904, 96], [84204, 96], [84505, 95], [84805, 95], [85105, 95], [85405, 95], [85705, 95], [86005, 95], [86305, 95], [86605, 95], [86905, 95], [87205, 95], [87505, 95], [87806, 94], [88106, 94], [88406, 94], [88706, 94], [89006, 94], [89306, 94], [89606, 94], [89906, 94]], "point": [149, 149]}}, "high_idx": 11}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.64|+00.00|+02.13"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 8587], [8602, 251], [8902, 251], [9202, 251], [9502, 250], [9802, 250], [10102, 250], [10402, 250], [10702, 250], [11001, 251], [11301, 251], [11601, 251], [11900, 252], [12200, 252], [12500, 253], [12799, 254], [13099, 254], [13398, 255], [13697, 257], [13997, 257], [14296, 258], [14595, 260], [14894, 216], [15131, 25], [15193, 213], [15437, 20], [15492, 211], [15740, 17], [15791, 210], [16043, 15], [16091, 207], [16345, 14], [16389, 208], [16647, 13], [16687, 208], [16948, 13], [16986, 208], [17250, 12], [17284, 209], [17551, 13], [17582, 210], [17852, 14], [17881, 211], [18152, 15], [18178, 214], [18453, 240], [18753, 241], [19052, 243], [19351, 246], [19649, 252], [19946, 261], [20240, 38756], [58997, 299], [59297, 299], [59597, 299], [59897, 299], [60197, 299], [60497, 299], [60797, 767], [61566, 129], [61697, 167], [61867, 128], [61998, 165], [62167, 128], [62298, 165], [62467, 128], [62598, 165], [62898, 165], [63198, 165], [63498, 165], [63798, 164], [64098, 164], [64398, 164], [64698, 164], [64998, 164], [65299, 163], [65599, 162], [65899, 162], [66199, 162], [66499, 162], [66799, 162], [67099, 162], [67399, 161], [67699, 161], [67999, 101], [68102, 58], [68299, 101], [68404, 56], [68600, 100], [68706, 54], [68900, 100], [69008, 52], [69200, 100], [69310, 49], [69500, 100], [69612, 47], [69800, 100], [69915, 44], [70100, 100], [70217, 42], [70400, 100], [70519, 39], [70700, 100], [70821, 37], [71000, 100], [71123, 35], [71300, 100], [71425, 32], [71601, 99], [71727, 30], [71901, 99], [72029, 28], [72201, 99], [72331, 26], [72501, 99], [72633, 24], [72801, 99], [72936, 21], [73101, 99], [73238, 18], [73401, 99], [73540, 16], [73701, 99], [74001, 99], [74301, 99], [74601, 99], [74902, 98], [75202, 98], [75502, 98], [75802, 98], [76102, 98], [76402, 98], [76702, 98], [77002, 98], [77302, 98], [77602, 98], [77902, 98], [78203, 97], [78503, 97], [78803, 97], [79103, 97], [79403, 97], [79703, 97], [80003, 97], [80303, 97], [80603, 97], [80903, 97], [81204, 96], [81504, 96], [81804, 96], [82104, 96], [82404, 96], [82704, 96], [83004, 96], [83304, 96], [83604, 96], [83904, 96], [84204, 96], [84505, 95], [84805, 95], [85105, 95], [85405, 95], [85705, 95], [86005, 95], [86305, 95], [86605, 95], [86905, 95], [87205, 95], [87505, 95], [87806, 94], [88106, 94], [88406, 94], [88706, 94], [89006, 94], [89306, 94], [89606, 94], [89906, 94]], "point": [149, 149]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan24", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 0.25, "y": 0.9009992, "z": 2.75}, "object_poses": [{"objectName": "Potato_75837e5e", "position": {"x": -2.85124445, "y": 0.8661358, "z": 3.405481}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_eb577cac", "position": {"x": 0.9133, "y": 0.850800037, "z": 1.3754}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_0cf79ab5", "position": {"x": 1.02499807, "y": 0.8236486, "z": 0.9395552}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_0cf79ab5", "position": {"x": -0.6570292, "y": 0.9011693, "z": 1.14349651}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_ace268e1", "position": {"x": -1.31763232, "y": 0.9368028, "z": 1.14349663}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_45eb0ae6", "position": {"x": -0.789149761, "y": 0.9002957, "z": 1.55565929}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_9f68a721", "position": {"x": 0.875373, "y": 0.8225346, "z": 2.4585216}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_f44f174d", "position": {"x": -2.117177, "y": 0.12556994, "z": 3.71177149}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_0c71376c", "position": {"x": -1.30777466, "y": 1.679924, "z": 3.81946182}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_0c71376c", "position": {"x": 0.800560534, "y": 0.819406152, "z": 2.4585216}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6dc531a2", "position": {"x": -2.63610482, "y": 1.07125568, "z": 2.22177148}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Lettuce_6dc531a2", "position": {"x": -2.57799959, "y": 0.8430006, "z": 2.33388615}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Mug_2e53b233", "position": {"x": -1.31763232, "y": 0.895620763, "z": 1.24653733}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Apple_21fad42f", "position": {"x": -2.63610482, "y": 1.25216186, "z": 2.165714}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Plate_656d9f06", "position": {"x": 0.8600002, "y": 0.8124938, "z": 0.975999832}, "rotation": {"x": -0.0022766788, "y": -3.48791873e-05, "z": 0.00636182539}}, {"objectName": "Fork_0cf79ab5", "position": {"x": -0.6570292, "y": 0.9011693, "z": 1.24653721}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bread_6b465e10", "position": {"x": -1.18551159, "y": 0.9538006, "z": 1.45261872}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_8e927511", "position": {"x": 0.950185537, "y": 0.8380999, "z": 2.111635}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_d2e883cd", "position": {"x": -0.789149761, "y": 1.004906, "z": 1.45261872}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_75837e5e", "position": {"x": 0.650935531, "y": 0.866135836, "z": 1.02844477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_9f68a721", "position": {"x": -2.82449841, "y": 1.398208, "z": 3.85810161}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_ace268e1", "position": {"x": 0.960505664, "y": 0.0604299344, "z": 2.91849279}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_45eb0ae6", "position": {"x": -1.20406818, "y": 1.68329275, "z": 3.9755044}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_eb577cac", "position": {"x": 0.9133, "y": 0.850800037, "z": 1.7688}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_0c71376c", "position": {"x": -1.31763232, "y": 0.896926939, "z": 0.9374153}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_b017884c", "position": {"x": -2.667, "y": 0.812, "z": 3.74}, "rotation": {"x": 0.0, "y": 137.145508, "z": 0.0}}, {"objectName": "PepperShaker_22dad048", "position": {"x": -2.50414944, "y": 1.84556532, "z": 1.85300994}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_af7bbed3", "position": {"x": 0.875373, "y": 0.8231421, "z": 2.631965}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_facc9b39", "position": {"x": -2.46178961, "y": 1.06406629, "z": 2.33388638}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "DishSponge_a0957e87", "position": {"x": 0.737736, "y": 0.692014158, "z": 2.19927835}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6dc531a2", "position": {"x": -0.5807226, "y": 0.9566, "z": 1.3583}, "rotation": {"x": 31.8225, "y": 1.20829975e-06, "z": 4.23861047e-06}}, {"objectName": "Spoon_ac730c84", "position": {"x": -1.31763232, "y": 0.901750147, "z": 1.04045594}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_21fad42f", "position": {"x": -0.524908543, "y": 0.961789846, "z": 1.55565929}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_b53567ce", "position": {"x": -0.6570292, "y": 0.923701346, "z": 1.04045582}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_2e53b233", "position": {"x": 0.742631435, "y": 0.120642006, "z": 1.059386}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_f44f174d", "position": {"x": 0.7430601, "y": 0.124247313, "z": 2.15953541}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 654686165, "scene_num": 24}, "task_id": "trial_T20190909_073110_047132", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AISNLDPD2DFEG_34YB12FSQ1FRIAW2NK7532BKIKHMG8", "high_descs": ["Turn right to the counter", "Pick up the knife on the counter", "Turn around, go forward, turn left and go to the fridge", "Slice the bread on the table ", "Turn left and go to the microwave", "Put the knife in the microwave", "Turn around, go to the table ", "Pick up a bread slice from the table ", "Turn left and go to the microwave", "Put the bread slice in the microwave, start the microwave, wait, pick up the bread slice from the microwave", "Turn around, go forward, turn right and go to the fridge", "Put the bread in the fridge"], "task_desc": "Slice a bread, cook a slice, put it in the fridge", "votes": [1, 1]}, {"assignment_id": "A1O3TWBUDONVLO_37M28K1J0T407BDEYN23RO80GUNJA1", "high_descs": ["Turn right to face the end of the counter.", "Pick up the knife on the counter.", "Turn around and cross the room to face the bread on the end of the white table on the left side of the room.", "Cut the bread on the table into slices.", "Turn left and walk forward to face the microwave.", "Place the knife in the microwave.", "Turn around and cross the room and turn left to face the bread on the white table.", "Pick up the bread on the white table.", "Turn left and walk forward to face the microwave.", "Heat the bread slice in the microwave and remove it.", "Turn left and cross the room to face the fridge.", "Place the bread slice in the fridge."], "task_desc": "To heat a slice of bread and place it in the fridge.", "votes": [1, 1]}, {"assignment_id": "AO33H4GL9KZX9_3DL65MZB8G6GPAOK40N303LWBPMCEL", "high_descs": ["Turn right to go to the gray counter in front.", "Pick up the gray knife on the left of the salt shaker.", "Turn around the go to the white table on the left.", "Slice the bread on the table. ", "Turn left and go to the sink. ", "Place the gray knife in the microwave above the sink. ", "Turn around and go back to the white table with the sliced bread.", "Pick up the end piece of the bread from the table. ", "Turn left and go back to the microwave above the sink.", "Microwave the bread slice inside the microwave next to the knife and take it out. ", "Turn around and go to the front of the refrigerator. ", "Put the bread slice on the shelf in the refrigerator next to the tomato."], "task_desc": "Put a heated slice of bread into the fridge. ", "votes": [1, 1]}]}}