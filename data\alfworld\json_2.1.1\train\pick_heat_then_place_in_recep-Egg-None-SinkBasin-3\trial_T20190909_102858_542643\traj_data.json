{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000296.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000298.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000299.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 45}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "SinkBasin", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|1|7|1|15"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [3.7540184, 3.7540184, 7.12522556, 7.12522556, 6.86377812, 6.86377812]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [4.052, 4.052, 7.668, 7.668, 0.911987304, 0.911987304]], "forceVisible": true, "objectId": "Egg|+00.94|+01.72|+01.78"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|3|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "sinkbasin"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [3.7540184, 3.7540184, 7.12522556, 7.12522556, 6.86377812, 6.86377812]], "coordinateReceptacleObjectId": ["SinkBasin", [-7.34220268, -7.34220268, -2.168930532, -2.168930532, 4.16, 4.16]], "forceVisible": true, "objectId": "Egg|+00.94|+01.72|+01.78", "receptacleObjectId": "Sink|-01.99|+01.14|-00.98|SinkBasin"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+01.01|+00.23|+01.92"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 90000]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+00.94|+01.72|+01.78"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [125, 96, 150, 129], "mask": [[28635, 6], [28933, 10], [29232, 12], [29531, 14], [29830, 16], [30129, 18], [30429, 18], [30728, 20], [31028, 21], [31327, 22], [31627, 22], [31926, 24], [32226, 24], [32526, 24], [32826, 25], [33125, 26], [33425, 26], [33725, 26], [34025, 26], [34325, 26], [34625, 26], [34926, 25], [35226, 25], [35526, 24], [35826, 24], [36127, 23], [36427, 22], [36728, 20], [37029, 19], [37329, 18], [37630, 16], [37931, 14], [38233, 11], [38535, 7]], "point": [137, 111]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+01.01|+00.23|+01.92"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 145], [154, 2], [166, 70], [268, 176], [469, 67], [568, 175], [771, 64], [868, 174], [1071, 64], [1168, 174], [1372, 63], [1468, 173], [1673, 62], [1768, 173], [1974, 61], [2068, 172], [2274, 61], [2367, 173], [2574, 61], [2667, 173], [2875, 60], [2967, 173], [3175, 60], [3267, 173], [3475, 61], [3566, 173], [3776, 60], [3865, 174], [4076, 60], [4165, 8455], [12622, 286], [12926, 280], [13228, 277], [13530, 273], [13832, 270], [14135, 265], [14436, 264], [14737, 263], [15038, 262], [15339, 261], [15640, 260], [15941, 259], [16241, 259], [16542, 258], [16843, 257], [17144, 256], [17444, 256], [17745, 255], [18045, 255], [18346, 254], [18646, 254], [18947, 253], [19248, 252], [19548, 252], [19849, 251], [20149, 251], [20449, 251], [20749, 251], [21048, 252], [21348, 252], [21648, 252], [21948, 252], [22248, 252], [22548, 252], [22848, 252], [23148, 252], [23447, 253], [23747, 253], [24047, 253], [24346, 254], [24646, 254], [24946, 254], [25245, 255], [25545, 255], [25844, 256], [26143, 257], [26442, 258], [26742, 258], [27041, 259], [27340, 260], [27639, 261], [27938, 262], [28237, 263], [28536, 264], [28835, 265], [29134, 266], [29432, 268], [29730, 270], [30027, 273], [30325, 276], [30622, 38523], [69155, 288], [69458, 283], [69759, 280], [70061, 277], [70362, 275], [70663, 273], [70964, 271], [71265, 269], [71565, 269], [71866, 267], [72167, 265], [72468, 264], [72768, 263], [73069, 262], [73369, 261], [73670, 260], [73970, 260], [74271, 258], [74571, 258], [74871, 257], [75172, 256], [75472, 256], [75772, 256], [76072, 255], [76372, 255], [76673, 254], [76973, 254], [77273, 254], [77573, 254], [77873, 254], [78173, 254], [78473, 254], [78773, 254], [79073, 254], [79373, 254], [79673, 254], [79973, 254], [80273, 255], [80573, 255], [80872, 256], [81172, 256], [81472, 257], [81771, 258], [82071, 258], [82371, 259], [82670, 260], [82970, 261], [83269, 262], [83569, 263], [83868, 265], [84167, 266], [84467, 267], [84766, 269], [85065, 271], [85364, 274], [85662, 277], [85961, 279], [86260, 283], [86558, 287], [86855, 3145]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28219, 195], [28518, 197], [28817, 199], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+00.94|+01.72|+01.78", "placeStationary": true, "receptacleObjectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 211], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 78], [34886, 128], [35100, 73], [35190, 124], [35400, 70], [35492, 122], [35700, 68], [35792, 122], [36000, 67], [36093, 121], [36300, 66], [36393, 120], [36600, 65], [36697, 116], [36900, 65], [36999, 114], [37200, 65], [37299, 114], [37500, 66], [37593, 3], [37600, 113], [37800, 66], [37893, 4], [37900, 113], [38100, 66], [38193, 4], [38200, 113], [38400, 66], [38494, 3], [38500, 113], [38700, 66], [38794, 3], [38800, 112], [39000, 67], [39094, 3], [39100, 112], [39300, 67], [39394, 3], [39400, 112], [39600, 67], [39694, 3], [39700, 112], [39900, 67], [39994, 3], [40000, 112], [40200, 67], [40294, 3], [40299, 113], [40500, 67], [40594, 2], [40599, 113], [40800, 68], [40898, 114], [41100, 68], [41198, 113], [41400, 68], [41497, 114], [41700, 68], [41796, 115], [42000, 68], [42095, 116], [42300, 68], [42395, 116], [42600, 69], [42695, 116], [42900, 69], [42995, 116], [43200, 69], [43295, 115], [43500, 69], [43595, 115], [43800, 69], [43894, 116], [44100, 70], [44193, 117], [44400, 70], [44492, 118], [44700, 72], [44790, 120], [45000, 74], [45087, 123], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 25], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 14], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 211], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 78], [34886, 128], [35100, 73], [35190, 124], [35400, 70], [35492, 122], [35700, 68], [35792, 122], [36000, 67], [36093, 121], [36300, 66], [36393, 120], [36600, 65], [36697, 116], [36900, 65], [36999, 114], [37200, 65], [37299, 114], [37500, 66], [37593, 3], [37600, 113], [37800, 66], [37893, 4], [37900, 32], [37936, 77], [38100, 66], [38193, 4], [38200, 30], [38238, 75], [38400, 66], [38494, 3], [38500, 28], [38539, 74], [38700, 66], [38794, 3], [38800, 28], [38840, 72], [39000, 67], [39094, 3], [39100, 27], [39140, 72], [39300, 67], [39394, 3], [39400, 26], [39441, 71], [39600, 67], [39694, 3], [39700, 26], [39742, 70], [39900, 67], [39994, 3], [40000, 26], [40042, 70], [40200, 67], [40294, 3], [40299, 26], [40342, 70], [40500, 67], [40594, 2], [40599, 26], [40643, 69], [40800, 68], [40898, 27], [40943, 69], [41100, 68], [41198, 27], [41243, 68], [41400, 68], [41497, 28], [41543, 68], [41700, 68], [41796, 29], [41843, 68], [42000, 68], [42095, 30], [42143, 68], [42300, 68], [42395, 30], [42442, 69], [42600, 69], [42695, 31], [42742, 69], [42900, 69], [42995, 31], [43042, 69], [43200, 69], [43295, 32], [43341, 69], [43500, 69], [43595, 32], [43640, 70], [43800, 69], [43894, 34], [43939, 71], [44100, 70], [44193, 37], [44238, 72], [44400, 70], [44492, 40], [44536, 74], [44700, 72], [44790, 120], [45000, 74], [45087, 123], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 25], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 14], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28219, 195], [28518, 197], [28817, 199], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28219, 195], [28518, 197], [28817, 199], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28219, 195], [28518, 197], [28817, 199], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+00.94|+01.72|+01.78"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [125, 127, 142, 149], "mask": [[37932, 4], [38230, 8], [38528, 11], [38828, 12], [39127, 13], [39426, 15], [39726, 16], [40026, 16], [40325, 17], [40625, 18], [40925, 18], [41225, 18], [41525, 18], [41825, 18], [42125, 18], [42425, 17], [42726, 16], [43026, 16], [43327, 14], [43627, 13], [43928, 11], [44230, 8], [44532, 4]], "point": [133, 137]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 211], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 78], [34886, 128], [35100, 73], [35190, 124], [35400, 70], [35492, 122], [35700, 68], [35792, 122], [36000, 67], [36093, 121], [36300, 66], [36393, 120], [36600, 65], [36697, 116], [36900, 65], [36999, 114], [37200, 65], [37299, 114], [37500, 66], [37593, 3], [37600, 113], [37800, 66], [37893, 4], [37900, 113], [38100, 66], [38193, 4], [38200, 113], [38400, 66], [38494, 3], [38500, 113], [38700, 66], [38794, 3], [38800, 112], [39000, 67], [39094, 3], [39100, 112], [39300, 67], [39394, 3], [39400, 112], [39600, 67], [39694, 3], [39700, 112], [39900, 67], [39994, 3], [40000, 112], [40200, 67], [40294, 3], [40299, 113], [40500, 67], [40594, 2], [40599, 113], [40800, 68], [40898, 114], [41100, 68], [41198, 113], [41400, 68], [41497, 114], [41700, 68], [41796, 115], [42000, 68], [42095, 116], [42300, 68], [42395, 116], [42600, 69], [42695, 116], [42900, 69], [42995, 116], [43200, 69], [43295, 115], [43500, 69], [43595, 115], [43800, 69], [43894, 116], [44100, 70], [44193, 117], [44400, 70], [44492, 118], [44700, 72], [44790, 120], [45000, 74], [45087, 123], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 25], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 14], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+00.94|+01.72|+01.78", "placeStationary": true, "receptacleObjectId": "Sink|-01.99|+01.14|-00.98|SinkBasin"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [152, 99, 256, 149], "mask": [[29562, 13], [29601, 24], [29860, 15], [29900, 29], [30158, 17], [30200, 31], [30456, 20], [30500, 33], [30755, 22], [30799, 35], [31054, 23], [31098, 38], [31353, 24], [31397, 40], [31653, 24], [31697, 41], [31952, 26], [31996, 43], [32252, 26], [32296, 43], [32552, 26], [32595, 45], [32852, 26], [32895, 46], [33152, 26], [33194, 47], [33452, 26], [33494, 48], [33752, 26], [33794, 48], [34052, 25], [34094, 48], [34352, 25], [34393, 50], [34652, 25], [34694, 49], [34952, 25], [34994, 50], [35252, 25], [35294, 50], [35552, 25], [35593, 1], [35597, 47], [35852, 25], [35897, 48], [36152, 26], [36198, 47], [36452, 27], [36492, 2], [36498, 48], [36752, 28], [36791, 3], [36798, 48], [37052, 29], [37085, 2], [37090, 4], [37098, 49], [37352, 43], [37398, 49], [37652, 43], [37698, 49], [37952, 43], [37998, 50], [38252, 44], [38298, 50], [38552, 44], [38599, 50], [38852, 44], [38899, 50], [39152, 45], [39199, 50], [39452, 45], [39499, 51], [39752, 45], [39799, 51], [40052, 46], [40099, 52], [40352, 46], [40399, 52], [40652, 46], [40700, 51], [40952, 46], [41000, 52], [41252, 46], [41300, 52], [41552, 35], [41590, 8], [41601, 52], [41852, 34], [41891, 6], [41901, 52], [42153, 33], [42191, 5], [42202, 52], [42456, 30], [42491, 5], [42503, 51], [42758, 29], [42791, 5], [42803, 51], [43060, 27], [43092, 4], [43104, 51], [43363, 24], [43392, 4], [43397, 1], [43400, 1], [43402, 1], [43404, 51], [43667, 20], [43692, 5], [43698, 1], [43700, 1], [43702, 1], [43704, 52], [43971, 17], [43992, 5], [43998, 1], [44000, 1], [44002, 1], [44004, 52], [44275, 13], [44292, 6], [44299, 1], [44301, 1], [44303, 53], [44578, 10], [44592, 6], [44599, 1], [44601, 1], [44603, 1], [44605, 52]], "point": [204, 123]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan3", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -0.75, "y": 1.12401652, "z": 2.0}, "object_poses": [{"objectName": "Potato_e8912d85", "position": {"x": 0.9822708, "y": 1.9858737, "z": 1.49441}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": -1.817123, "y": 0.343378425, "z": 0.211331308}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": 1.06577992, "y": 0.7413571, "z": 1.05461586}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": 1.02584076, "y": 1.32354856, "z": -0.942471862}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": 0.9818125, "y": 0.954838037, "z": 0.983438134}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -1.6980927, "y": 1.0521791, "z": -0.587838531}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.66056311, "y": 1.33799994, "z": -2.461287}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": 1.285346, "y": 1.33799994, "z": -2.794527}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.2394, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": -1.81848335, "y": 0.341211677, "z": 1.42058778}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -1.23730922, "y": 1.323614, "z": -3.31436586}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.16594541, "y": 1.32319188, "z": -2.9817524}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.6457262, "y": 0.420213044, "z": -1.02086711}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 0.9822707, "y": 1.98004115, "z": 1.73000717}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 1.02731991, "y": 1.241721, "z": 1.23849058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -1.40105689, "y": 1.32130814, "z": 0.770108938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": 0.900508642, "y": 0.3432796, "z": -1.9577744}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": -1.53081059, "y": 1.31800008, "z": -3.24110079}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": 0.77839607, "y": 1.2028, "z": 1.23849058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": -1.817123, "y": 0.33881402, "z": 0.6130607}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": 1.27624369, "y": 1.25668371, "z": 1.23849058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.6575036, "y": 0.32906872, "z": 2.27545214}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": -1.53081059, "y": 1.39739525, "z": -2.72122478}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -1.53081059, "y": 1.3294332, "z": -1.94141138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.9200685, "y": 1.35270047, "z": 0.120481014}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -1.75051689, "y": 0.3421222, "z": 1.00736094}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": -1.363688, "y": 1.36275363, "z": -1.50393641}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.602987, "y": 0.344005942, "z": -1.9633193}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.82631838, "y": 1.06453836, "z": -0.5422326}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": -1.95454407, "y": 1.04844725, "z": -0.587838531}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -0.6309, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": 0.9385046, "y": 1.71594453, "z": 1.78130639}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_9d168802", "position": {"x": -1.88114953, "y": 0.327762932, "z": -2.03532434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": -1.95251179, "y": 0.338814139, "z": 0.7134931}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": 0.6717187, "y": 0.3459003, "z": -2.37100124}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": 1.01704478, "y": 1.83334172, "z": 2.11270857}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.73758006, "y": 1.343986, "z": -1.04922915}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_ddd73f57", "position": {"x": 0.349309921, "y": 1.42728543, "z": -3.12197924}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": -1.38003659, "y": 1.35692108, "z": -2.870881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -2.04982066, "y": 1.323614, "z": -2.981163}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": 0.8613707, "y": 1.2028, "z": 1.25445545}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": 0.9836396, "y": 0.4601643, "z": 0.6816628}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": 0.420673847, "y": 1.32412946, "z": -3.34372234}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.634, "y": 0.32906872, "z": 2.110548}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 1.14845026, "y": 1.3054105, "z": 1.0165621}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 2996946258, "scene_num": 3}, "task_id": "trial_T20190909_102858_542643", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AJQGWGESKQT4Y_354GIDR5ZEXX4SEAKDV4KDHR889006", "high_descs": ["Turn to the right and face the fridge.", "Take the egg out of the fridge.", "Move to the right and face the microwave.", "Put the egg in the microwave and shut the door and then open the door and take the egg out again.", "Turn around and cross the room to stand at the sink.", "Put the egg in the sink."], "task_desc": "Put a heated egg in the sink.", "votes": [1, 1]}, {"assignment_id": "A20FCMWP43CVIU_3HMIGG0U4OXCJCFYNPJS5KHT3IJ8YJ", "high_descs": ["walk to face fridge", "pick up egg from fridge", "walk to face microwave", "heat and remove egg from microwave", "walk to face sink", "put egg inside sink"], "task_desc": "put heated egg inside sink", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3JV9LGBJWW5FIDHE00MIV60PDSGGOH", "high_descs": ["Walk over to the fridge.", "Open the fridge and take the egg out of the fridge, then close the fridge.", "Walk over to the microwave to the right of the fridge.", "Microwave the egg for a couple seconds to heat it up, then pick it back up and close the microwave.", "Turn right and walk forward, then hang a right and walk over to the sink.", "Put the heated egg into the sink basin."], "task_desc": "Put a heated egg in the sink.", "votes": [1, 1]}]}}