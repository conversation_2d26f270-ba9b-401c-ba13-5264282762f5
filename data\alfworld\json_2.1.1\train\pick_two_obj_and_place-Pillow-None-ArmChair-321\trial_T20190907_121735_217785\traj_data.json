{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000299.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000300.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000301.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000302.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000303.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000304.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000305.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000306.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000307.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000308.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000309.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000310.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000311.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000312.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000313.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000314.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000315.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000316.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000317.png", "low_idx": 62}, {"high_idx": 7, "image_name": "000000318.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000319.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000320.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000321.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000322.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000323.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000324.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000325.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000326.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000327.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000328.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000329.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000330.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000331.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000332.png", "low_idx": 63}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Pillow", "parent_target": "ArmChair", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["bed"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|5|-4|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["pillow"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pillow", [4.68605852, 4.68605852, -1.72146952, -1.72146952, 2.7083828, 2.7083828]], "coordinateReceptacleObjectId": ["Bed", [3.764, 3.764, 0.584, 0.584, 0.0, 0.0]], "forceVisible": true, "objectId": "Pillow|+01.17|+00.68|-00.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["armchair"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|7|-8|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["pillow", "armchair"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pillow", [4.68605852, 4.68605852, -1.72146952, -1.72146952, 2.7083828, 2.7083828]], "coordinateReceptacleObjectId": ["ArmChair", [2.804, 2.804, -9.068, -9.068, 0.055999998, 0.055999998]], "forceVisible": true, "objectId": "Pillow|+01.17|+00.68|-00.43", "receptacleObjectId": "ArmC<PERSON>r|+00.70|+00.01|-02.27"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bed"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|9|1|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["pillow"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pillow", [5.49703644, 5.49703644, 1.3524892, 1.3524892, 2.7083828, 2.7083828]], "coordinateReceptacleObjectId": ["Bed", [3.764, 3.764, 0.584, 0.584, 0.0, 0.0]], "forceVisible": true, "objectId": "Pillow|+01.37|+00.68|+00.34"}}, {"discrete_action": {"action": "GotoLocation", "args": ["armchair"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|7|-8|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["pillow", "armchair"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pillow", [5.49703644, 5.49703644, 1.3524892, 1.3524892, 2.7083828, 2.7083828]], "coordinateReceptacleObjectId": ["ArmChair", [2.804, 2.804, -9.068, -9.068, 0.055999998, 0.055999998]], "forceVisible": true, "objectId": "Pillow|+01.37|+00.68|+00.34", "receptacleObjectId": "ArmC<PERSON>r|+00.70|+00.01|-02.27"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Pillow|+01.17|+00.68|-00.43"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [80, 104, 181, 179], "mask": [[30993, 81], [31291, 85], [31590, 87], [31889, 89], [32189, 89], [32488, 91], [32788, 91], [33088, 91], [33388, 91], [33688, 91], [33988, 91], [34288, 91], [34587, 92], [34887, 92], [35187, 92], [35487, 93], [35787, 93], [36087, 93], [36387, 93], [36686, 94], [36986, 94], [37286, 94], [37586, 94], [37886, 94], [38186, 94], [38486, 94], [38785, 95], [39085, 95], [39385, 95], [39685, 95], [39985, 95], [40285, 96], [40584, 97], [40884, 97], [41184, 97], [41484, 97], [41784, 97], [42084, 97], [42384, 97], [42684, 97], [42983, 98], [43283, 98], [43583, 98], [43883, 98], [44183, 98], [44482, 99], [44782, 99], [45082, 99], [45382, 100], [45682, 100], [45982, 100], [46282, 100], [46581, 101], [46881, 101], [47181, 101], [47481, 101], [47781, 101], [48080, 102], [48380, 102], [48680, 102], [48980, 102], [49280, 102], [49580, 102], [49881, 101], [50181, 101], [50481, 101], [50781, 101], [51081, 100], [51382, 99], [51682, 99], [51983, 98], [52283, 97], [52585, 94], [52886, 93], [53187, 90], [53489, 9], [53568, 7]], "point": [130, 140]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pillow|+01.17|+00.68|-00.43", "placeStationary": true, "receptacleObjectId": "ArmC<PERSON>r|+00.70|+00.01|-02.27"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 217, 165], "mask": [[0, 142], [300, 143], [600, 144], [900, 145], [1200, 146], [1500, 147], [1800, 148], [2100, 149], [2400, 150], [2700, 151], [3000, 152], [3300, 153], [3600, 154], [3900, 155], [4200, 156], [4500, 157], [4800, 158], [5100, 159], [5400, 160], [5700, 161], [6000, 162], [6300, 163], [6600, 164], [6900, 165], [7200, 166], [7500, 167], [7800, 167], [8100, 168], [8400, 169], [8700, 170], [9000, 171], [9300, 171], [9600, 172], [9900, 173], [10200, 174], [10500, 175], [10800, 175], [11100, 176], [11400, 177], [11700, 178], [12000, 179], [12300, 180], [12600, 180], [12900, 181], [13200, 182], [13500, 183], [13800, 183], [14100, 184], [14400, 184], [14700, 185], [15000, 185], [15300, 186], [15600, 186], [15900, 187], [16200, 187], [16500, 188], [16800, 188], [17100, 189], [17400, 189], [17700, 190], [18000, 190], [18300, 191], [18600, 191], [18900, 192], [19200, 192], [19500, 193], [19800, 193], [20100, 193], [20400, 194], [20700, 194], [21000, 194], [21300, 195], [21600, 195], [21900, 195], [22200, 196], [22500, 196], [22800, 196], [23100, 197], [23400, 197], [23700, 197], [24000, 197], [24300, 198], [24600, 198], [24900, 199], [25200, 199], [25500, 199], [25800, 200], [26100, 200], [26400, 201], [26700, 202], [27000, 203], [27300, 204], [27600, 205], [27901, 205], [28201, 206], [28501, 207], [28802, 206], [29102, 207], [29403, 207], [29703, 165], [29869, 42], [30004, 164], [30169, 43], [30304, 165], [30470, 42], [30604, 165], [30770, 43], [30905, 165], [31071, 42], [31205, 165], [31372, 42], [31506, 157], [31664, 5], [31673, 42], [31806, 157], [31968, 2], [31973, 42], [32107, 155], [32270, 3], [32274, 42], [32407, 158], [32569, 47], [32707, 162], [32871, 1], [32873, 44], [33008, 154], [33171, 2], [33174, 44], [33308, 156], [33471, 1], [33475, 43], [33609, 159], [33769, 4], [33776, 42], [33909, 165], [34080, 38], [34209, 165], [34380, 38], [34510, 164], [34680, 38], [34810, 164], [34979, 39], [35111, 166], [35279, 39], [35411, 207], [35712, 206], [36012, 206], [36313, 205], [36613, 205], [36914, 204], [37214, 203], [37515, 202], [37815, 202], [38116, 200], [38416, 200], [38717, 198], [39017, 198], [39318, 197], [39618, 196], [39919, 195], [40219, 194], [40520, 193], [40820, 193], [41121, 191], [41421, 191], [41722, 190], [42022, 189], [42323, 188], [42624, 186], [42924, 186], [43225, 185], [43525, 183], [43826, 181], [44126, 179], [44427, 177], [44728, 174], [45028, 172], [45329, 169], [45629, 24], [45665, 131], [45930, 20], [46230, 18], [46531, 14], [46831, 12], [47132, 9], [47432, 9], [47733, 7], [48033, 6], [48334, 5], [48634, 4], [48935, 3], [49235, 2]], "point": [108, 82]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Pillow|+01.37|+00.68|+00.34"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [133, 123, 207, 192], "mask": [[36781, 1], [37040, 54], [37338, 58], [37637, 60], [37936, 62], [38236, 62], [38536, 62], [38836, 62], [39136, 62], [39436, 63], [39736, 63], [40036, 63], [40336, 63], [40636, 63], [40936, 63], [41236, 64], [41536, 64], [41836, 64], [42136, 64], [42435, 65], [42735, 66], [43035, 66], [43335, 66], [43635, 66], [43935, 66], [44235, 67], [44535, 67], [44835, 67], [45135, 67], [45435, 67], [45735, 68], [46035, 68], [46335, 68], [46635, 68], [46934, 69], [47234, 70], [47534, 70], [47834, 70], [48134, 70], [48434, 70], [48734, 71], [49034, 71], [49334, 71], [49634, 71], [49934, 72], [50234, 72], [50534, 72], [50834, 72], [51134, 72], [51433, 74], [51733, 74], [52033, 74], [52333, 74], [52633, 75], [52933, 75], [53233, 74], [53533, 74], [53833, 74], [54133, 74], [54434, 73], [54734, 73], [55034, 73], [55334, 72], [55634, 72], [55935, 71], [56234, 72], [56535, 70], [56836, 68], [57136, 68], [57439, 62]], "point": [170, 156]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pillow|+01.37|+00.68|+00.34", "placeStationary": true, "receptacleObjectId": "ArmC<PERSON>r|+00.70|+00.01|-02.27"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 217, 179], "mask": [[0, 142], [300, 143], [600, 144], [900, 145], [1200, 146], [1500, 147], [1800, 148], [2100, 149], [2400, 150], [2700, 151], [3000, 152], [3300, 153], [3600, 154], [3900, 155], [4200, 156], [4500, 157], [4800, 158], [5100, 159], [5400, 160], [5700, 161], [6000, 162], [6300, 163], [6600, 164], [6900, 165], [7200, 166], [7500, 167], [7800, 167], [8100, 168], [8400, 169], [8700, 170], [9000, 171], [9300, 171], [9600, 172], [9900, 173], [10200, 174], [10500, 175], [10800, 175], [11100, 176], [11400, 177], [11700, 178], [12000, 179], [12300, 180], [12600, 180], [12900, 181], [13200, 182], [13500, 183], [13800, 183], [14100, 184], [14400, 184], [14700, 185], [15000, 185], [15300, 186], [15600, 186], [15900, 187], [16200, 187], [16500, 188], [16800, 188], [17100, 189], [17400, 189], [17700, 190], [18000, 190], [18300, 191], [18600, 191], [18900, 192], [19200, 192], [19500, 70], [19571, 122], [19800, 63], [19872, 121], [20100, 60], [20174, 119], [20400, 58], [20475, 119], [20700, 57], [20777, 117], [21000, 55], [21078, 116], [21300, 53], [21379, 116], [21600, 54], [21680, 115], [21900, 53], [21981, 114], [22200, 53], [22282, 114], [22500, 53], [22583, 113], [22800, 53], [22883, 113], [23100, 53], [23184, 113], [23400, 53], [23485, 112], [23700, 53], [23785, 112], [24000, 53], [24086, 111], [24300, 53], [24386, 112], [24600, 53], [24687, 111], [24900, 53], [24988, 111], [25200, 53], [25289, 110], [25500, 53], [25589, 110], [25800, 53], [25890, 110], [26100, 53], [26191, 109], [26400, 53], [26491, 110], [26700, 54], [26792, 110], [27000, 54], [27093, 110], [27300, 55], [27393, 111], [27600, 55], [27694, 111], [27901, 55], [27994, 112], [28201, 55], [28294, 113], [28501, 56], [28595, 113], [28802, 56], [28895, 113], [29102, 56], [29196, 113], [29403, 55], [29497, 113], [29703, 56], [29798, 70], [29869, 42], [30004, 55], [30099, 69], [30169, 43], [30304, 56], [30399, 70], [30470, 42], [30604, 56], [30700, 69], [30770, 43], [30905, 55], [31001, 69], [31071, 42], [31205, 56], [31301, 69], [31372, 42], [31506, 55], [31602, 61], [31664, 5], [31673, 42], [31806, 56], [31902, 61], [31968, 2], [31973, 42], [32107, 56], [32203, 59], [32270, 3], [32274, 42], [32407, 56], [32503, 62], [32569, 47], [32707, 57], [32804, 65], [32871, 1], [32873, 44], [33008, 56], [33104, 58], [33171, 2], [33174, 44], [33308, 56], [33405, 59], [33471, 1], [33475, 43], [33609, 55], [33705, 63], [33769, 4], [33776, 42], [33909, 56], [34006, 68], [34080, 38], [34209, 56], [34307, 67], [34380, 38], [34510, 56], [34608, 66], [34680, 38], [34810, 56], [34908, 66], [34979, 39], [35111, 56], [35209, 68], [35279, 39], [35411, 56], [35509, 109], [35712, 56], [35810, 108], [36012, 57], [36110, 108], [36313, 56], [36410, 108], [36613, 56], [36710, 108], [36914, 56], [37011, 107], [37214, 56], [37311, 106], [37515, 55], [37611, 106], [37815, 56], [37911, 106], [38116, 55], [38211, 105], [38416, 56], [38511, 105], [38717, 55], [38811, 104], [39017, 56], [39112, 103], [39318, 56], [39412, 103], [39618, 57], [39712, 102], [39919, 56], [40013, 101], [40219, 57], [40313, 100], [40520, 56], [40613, 100], [40820, 57], [40913, 100], [41121, 56], [41213, 99], [41421, 56], [41513, 99], [41722, 56], [41814, 98], [42022, 56], [42114, 97], [42323, 56], [42415, 96], [42624, 55], [42715, 95], [42924, 56], [43015, 95], [43225, 55], [43315, 95], [43525, 56], [43615, 93], [43826, 56], [43915, 92], [44126, 57], [44215, 90], [44427, 56], [44515, 89], [44728, 56], [44815, 87], [45028, 56], [45115, 85], [45329, 56], [45415, 83], [45629, 56], [45715, 81], [45930, 55], [46015, 79], [46230, 56], [46314, 78], [46531, 55], [46614, 76], [46831, 56], [46914, 74], [47132, 56], [47214, 72], [47432, 57], [47513, 71], [47733, 57], [47812, 71], [48033, 58], [48110, 71], [48334, 27], [48372, 20], [48409, 70], [48634, 24], [48935, 20], [49235, 17], [49536, 14], [49836, 13], [50136, 13], [50437, 11], [50737, 10], [51038, 8], [51338, 7], [51638, 6], [51939, 5], [52239, 4], [52540, 3], [52840, 2], [53140, 2], [53441, 1]], "point": [108, 89]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan321", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": 2.75, "y": 0.9009992, "z": 1.25}, "object_poses": [{"objectName": "TissueBox_528d8e8a", "position": {"x": 3.239, "y": 0.779699564, "z": -1.32210469}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_a02909a5", "position": {"x": 2.77649736, "y": 0.7779894, "z": -1.32210469}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_a02909a5", "position": {"x": 0.2917841, "y": 0.705565, "z": 1.29679394}, "rotation": {"x": 0.0, "y": 90.0007, "z": 0.0}}, {"objectName": "CreditCard_2946225d", "position": {"x": 3.7015028, "y": 0.777996, "z": -1.50070155}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_2946225d", "position": {"x": 3.54733515, "y": 0.779769242, "z": -1.32210469}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_1a9bddbb", "position": {"x": 0.841575861, "y": 0.434596568, "z": -1.86336613}, "rotation": {"x": 0.0, "y": 330.0002, "z": 0.0}}, {"objectName": "Book_9dcb1956", "position": {"x": 0.7660267, "y": 0.5961157, "z": -0.430367261}, "rotation": {"x": 0.0, "y": 2.3905659e-05, "z": 0.0}}, {"objectName": "Pillow_b8577ff9", "position": {"x": 0.9687709, "y": 0.6770957, "z": 0.338122427}, "rotation": {"x": 0.0, "y": 2.3905659e-05, "z": 0.0}}, {"objectName": "Pillow_b8577ff9", "position": {"x": 1.17151463, "y": 0.6770957, "z": -0.43036738}, "rotation": {"x": 0.0, "y": 2.3905659e-05, "z": 0.0}}, {"objectName": "AlarmClock_55797a1f", "position": {"x": 3.7015028, "y": 0.779816031, "z": -1.76859689}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "AlarmClock_55797a1f", "position": {"x": 2.77649736, "y": 0.779816031, "z": -1.41140318}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Book_9dcb1956", "position": {"x": 1.171515, "y": 0.5961157, "z": 0.722367167}, "rotation": {"x": 0.0, "y": 2.3905659e-05, "z": 0.0}}, {"objectName": "KeyChain_1a9bddbb", "position": {"x": 0.357359678, "y": 0.706087, "z": 1.04700708}, "rotation": {"x": 0.0, "y": 90.0007, "z": 0.0}}, {"objectName": "Pillow_801065cc", "position": {"x": 0.463, "y": 0.6389, "z": 0.408}, "rotation": {"x": 0.0, "y": 101.363876, "z": 0.0}}, {"objectName": "Pillow_b8577ff9", "position": {"x": 1.37425911, "y": 0.6770957, "z": 0.3381223}, "rotation": {"x": 0.0, "y": 2.3905659e-05, "z": 0.0}}, {"objectName": "CreditCard_2946225d", "position": {"x": 0.355434418, "y": 0.7055716, "z": -1.27100337}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Pen_369f1c16", "position": {"x": 2.930665, "y": 0.783652842, "z": -1.76859689}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_7030f200", "position": {"x": 3.641, "y": 0.0525373258, "z": -0.983999968}, "rotation": {"x": 0.0, "y": 210.000015, "z": 0.0}}, {"objectName": "TissueBox_528d8e8a", "position": {"x": 0.303489864, "y": 0.6985756, "z": -0.7837758}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_f3c3a308", "position": {"x": 3.452, "y": 0.7733102, "z": -1.667}, "rotation": {"x": 0.0, "y": 210.000275, "z": 0.0}}, {"objectName": "CellPhone_a02909a5", "position": {"x": 3.08483267, "y": 0.7797626, "z": -1.67929852}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "AlarmClock_55797a1f", "position": {"x": 3.08483267, "y": 0.7780428, "z": -1.85789537}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CD_f2466fdb", "position": {"x": 0.3528759, "y": 0.6148637, "z": -0.940516353}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}], "object_toggles": [], "random_seed": 1533414457, "scene_num": 321}, "task_id": "trial_T20190907_121735_217785", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A2KAGFQU28JY43_382M9COHEKW4QFJMUJJMTNN0OLVEUU", "high_descs": ["Walk across the room making a right in front of the desk and walk towards the night stand by the bed, then turn to your right so that you are facing the bed.", "Pick up the cushion on the edge of the bed closest to you. ", "Turn around and face the chair in the corner. ", "Place the cushion on the seat of the chair. ", "Turn around and walk back to the end of the bed, facing the head board.", "Pick up the cushion closest to you, at the end of the bed. ", "Turn to your left and walk back to the chair in the corner.", "Place the cushion on the chair next to the other cushion. "], "task_desc": "Put two cushion's on the chair in the corner. ", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3XLBSAQ9Z7T03ZYMDY4O45AY4G6Z7I", "high_descs": ["Walk across the room to the wooden table, then turn right and walk over to the middle of the bed.", "Pick up the closest purple pillow off of the bed.", "Turn right and take a step, then turn right and walk forward, then turn right and face the chair in the corner of the room.", "Place the purple pillow standing up on left side of the seat of the chair.", "Turn around and walk around to the end of the bed.", "Pick up the closest purple pillow off of the bed.", "Turn left and walk over to the chair in the corner of the room.", "Place the pillow standing up on the seat of the chair to the right of the pillow that is already there."], "task_desc": "Move two pillows to the seat of a chair.", "votes": [1, 1]}, {"assignment_id": "A3T9K2RY06UO14_3YT88D1N0BFNDLYDBLYO49A5V5Y3KM", "high_descs": ["Turn around and go to the bed.", "Pick up a cushion on the bed. ", "Turn around and go to the chair by the window.", "Put the cushion on the chair. ", "Turn around and go to the bed.", "Pick up a cushion on the bed. ", "Turn around and go to the chair by the window.", "Put the cushion on the chair. "], "task_desc": "Move two cushions from the bed to the chair by the window. ", "votes": [1, 1]}, {"assignment_id": "A22ON3MIIG0IJA_31IBVUNM9VGW988IUKDX7RAJEYPVFY", "high_descs": ["Walk to the table then turn right and walk to the corner of the bed.", "Pick up the cushion from the bed.", "Turn around and walk to the brown chair.", "Stand the cushion on the left side of the brown chair.", "Turn right and walk to the end of the bed.", "Pick up the cushion closest to the end of the bed.", "Turn around and walk to the brown chair in the corner.", "Stand the cushion on the right side of the brown chair."], "task_desc": "Put two cushions on the brown chair.", "votes": [1, 1]}, {"assignment_id": "A19LVWX8ZLO6CS_3FTYUGLFSX2IDUF7XN0UFTDZPXYD5P", "high_descs": ["Walk forward towards the table, turn right, walk forward, turn right to be on the side of the bed.", "Pick up the purple cushion on the bed in front of you.", "Turn around walk to the gray sofa.", "Place the purple cushion on the sofa.", "Turn around and head towards the door, turn left at the foot of the bed.", "Pick up the purple cushion closest to the bottom of the bed.", "Turn left and return to the gray sofa.", "Place the cushion to the right on the cushion that is already on the sofa."], "task_desc": "Take two cushions and place them on the gray sofa.", "votes": [1, 1]}, {"assignment_id": "A3T9K2RY06UO14_3IKZ72A5B7XJ13Z9STR6XAG6SXCNF2", "high_descs": ["Turn and go to the bed.", "Pick up a cushion on the bed. ", "Turn around and go to the chair in the corner. ", "Put the cushion on the chair in the corner.", "Turn around and go to the bed.", "Pick up a cushion on the bed. ", "Turn around and go to the chair in the corner. ", "Put the cushion on the chair in the corner."], "task_desc": "Move two cushions from the bed to the chair in the corner.", "votes": [1, 1]}]}}