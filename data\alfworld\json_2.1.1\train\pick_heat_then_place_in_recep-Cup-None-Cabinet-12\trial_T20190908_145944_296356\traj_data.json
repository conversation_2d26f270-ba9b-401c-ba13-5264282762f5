{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000108.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000110.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000111.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000340.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000341.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000342.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000343.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000344.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000345.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000346.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000347.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000348.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000349.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000350.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000351.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000352.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000353.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000354.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000355.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000356.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000357.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000358.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000359.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000360.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000361.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000362.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000363.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000364.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000365.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000366.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000367.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000368.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000369.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000370.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000371.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000372.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000373.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000374.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000375.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000376.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000377.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000378.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000379.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000380.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000381.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000382.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000383.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000384.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000385.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000386.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000387.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000388.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000389.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000390.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000391.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000392.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000393.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000394.png", "low_idx": 58}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|10|1|15"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [7.0010662, 7.0010662, 10.10453416, 10.10453416, 5.99874304, 5.99874304]], "coordinateReceptacleObjectId": ["Cabinet", [6.00204132, 6.00204132, 10.764084, 10.764084, 7.24115516, 7.24115516]], "forceVisible": true, "objectId": "Cup|+01.75|+01.50|+02.53"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|1|6|1|-15"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [7.0010662, 7.0010662, 10.10453416, 10.10453416, 5.99874304, 5.99874304]], "coordinateReceptacleObjectId": ["Cabinet", [6.00313236, 6.00313236, 8.32905484, 8.32905484, 8.45383264, 8.45383264]], "forceVisible": true, "objectId": "Cup|+01.75|+01.50|+02.53", "receptacleObjectId": "Cabinet|+01.50|+02.11|+02.08"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+01.50|+01.81|+02.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [38, 1, 206, 131], "mask": [[38, 169], [338, 169], [638, 169], [938, 169], [1239, 168], [1539, 168], [1839, 168], [2139, 168], [2439, 168], [2739, 168], [3039, 168], [3339, 168], [3639, 168], [3939, 167], [4240, 166], [4540, 166], [4840, 166], [5140, 166], [5440, 166], [5740, 166], [6040, 166], [6340, 166], [6640, 166], [6940, 166], [7241, 165], [7541, 165], [7841, 165], [8141, 165], [8441, 165], [8741, 165], [9041, 165], [9341, 165], [9641, 165], [9941, 164], [10242, 163], [10542, 163], [10842, 163], [11142, 163], [11442, 163], [11742, 163], [12042, 163], [12342, 163], [12642, 163], [12942, 163], [13243, 162], [13543, 162], [13843, 162], [14143, 162], [14443, 162], [14743, 162], [15043, 162], [15343, 162], [15643, 161], [15943, 161], [16244, 160], [16544, 160], [16844, 160], [17144, 160], [17444, 160], [17744, 160], [18044, 160], [18344, 160], [18644, 160], [18944, 160], [19245, 159], [19545, 159], [19845, 159], [20145, 159], [20445, 159], [20745, 159], [21045, 159], [21345, 159], [21645, 158], [21945, 158], [22246, 157], [22546, 157], [22846, 157], [23146, 157], [23446, 157], [23746, 157], [24046, 157], [24346, 157], [24646, 157], [24946, 157], [25247, 156], [25547, 156], [25847, 156], [26147, 156], [26447, 156], [26747, 156], [27047, 156], [27347, 155], [27647, 155], [27947, 155], [28248, 154], [28548, 154], [28848, 154], [29148, 154], [29448, 154], [29748, 154], [30048, 154], [30348, 154], [30648, 154], [30948, 154], [31249, 153], [31549, 153], [31849, 153], [32149, 153], [32449, 153], [32749, 153], [33049, 153], [33349, 152], [33649, 152], [33949, 152], [34250, 151], [34550, 151], [34850, 151], [35150, 151], [35450, 151], [35750, 151], [36050, 151], [36350, 151], [36650, 151], [36950, 151], [37251, 150], [37551, 150], [37851, 150], [38151, 150], [38451, 150], [38751, 149], [39053, 146]], "point": [122, 65]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+01.75|+01.50|+02.53"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [121, 42, 158, 109], "mask": [[12434, 11], [12722, 35], [13021, 38], [13321, 38], [13621, 38], [13921, 38], [14221, 38], [14521, 38], [14821, 38], [15121, 38], [15422, 37], [15722, 36], [16022, 36], [16322, 36], [16622, 36], [16922, 36], [17222, 36], [17522, 36], [17823, 35], [18123, 35], [18423, 35], [18723, 34], [19023, 34], [19323, 34], [19623, 34], [19923, 34], [20224, 33], [20524, 33], [20824, 33], [21124, 33], [21424, 33], [21724, 32], [22024, 32], [22324, 32], [22625, 31], [22925, 31], [23225, 31], [23525, 31], [23825, 31], [24125, 31], [24425, 31], [24725, 30], [25026, 29], [25326, 29], [25626, 29], [25926, 29], [26226, 29], [26526, 29], [26826, 29], [27126, 29], [27427, 27], [27727, 27], [28027, 27], [28327, 27], [28627, 27], [28927, 27], [29227, 27], [29527, 27], [29828, 26], [30128, 26], [30428, 25], [30728, 25], [31028, 25], [31328, 25], [31628, 25], [31928, 25], [32229, 24], [32529, 23]], "point": [139, 74]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+01.50|+01.81|+02.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 185, 158], "mask": [[0, 43], [51, 135], [300, 43], [351, 135], [600, 44], [651, 135], [900, 44], [951, 135], [1200, 44], [1251, 135], [1500, 44], [1552, 134], [1800, 44], [1852, 134], [2100, 44], [2152, 134], [2400, 44], [2452, 134], [2700, 44], [2752, 134], [3000, 44], [3052, 134], [3300, 44], [3352, 133], [3600, 44], [3652, 133], [3900, 45], [3952, 133], [4200, 45], [4252, 133], [4500, 45], [4552, 133], [4800, 45], [4853, 132], [5100, 45], [5153, 132], [5400, 45], [5453, 132], [5700, 45], [5753, 132], [6000, 45], [6053, 132], [6300, 45], [6353, 132], [6600, 45], [6653, 132], [6900, 46], [6953, 132], [7200, 46], [7253, 132], [7500, 46], [7553, 132], [7800, 46], [7853, 132], [8100, 46], [8153, 132], [8400, 46], [8454, 131], [8700, 46], [8754, 131], [9000, 46], [9054, 131], [9300, 46], [9354, 131], [9600, 46], [9654, 131], [9900, 46], [9954, 131], [10200, 47], [10254, 131], [10500, 47], [10554, 131], [10800, 47], [10854, 131], [11100, 47], [11154, 131], [11400, 47], [11454, 131], [11700, 47], [11755, 130], [12000, 47], [12055, 130], [12300, 47], [12355, 130], [12600, 47], [12655, 129], [12900, 47], [12955, 129], [13200, 48], [13255, 129], [13500, 48], [13555, 129], [13800, 48], [13855, 129], [14100, 48], [14155, 129], [14400, 48], [14455, 129], [14700, 48], [14755, 129], [15000, 48], [15056, 128], [15300, 48], [15356, 128], [15600, 48], [15656, 128], [15900, 48], [15956, 128], [16200, 48], [16256, 128], [16500, 49], [16556, 128], [16800, 49], [16856, 128], [17100, 49], [17156, 128], [17400, 49], [17456, 128], [17700, 49], [17756, 128], [18000, 49], [18056, 128], [18300, 49], [18356, 128], [18600, 49], [18657, 127], [18900, 49], [18957, 127], [19200, 49], [19257, 127], [19500, 50], [19557, 127], [19800, 50], [19857, 127], [20100, 50], [20157, 127], [20400, 50], [20457, 127], [20700, 50], [20757, 127], [21000, 50], [21057, 127], [21300, 50], [21357, 127], [21600, 50], [21657, 127], [21900, 50], [21958, 125], [22200, 50], [22258, 125], [22500, 50], [22558, 125], [22800, 51], [22858, 125], [23100, 51], [23158, 125], [23400, 51], [23458, 125], [23700, 51], [23758, 125], [24000, 51], [24058, 125], [24300, 51], [24358, 125], [24600, 51], [24658, 125], [24900, 51], [24958, 125], [25200, 51], [25259, 124], [25500, 51], [25559, 124], [25800, 52], [25859, 124], [26100, 52], [26159, 124], [26400, 52], [26459, 124], [26700, 52], [26759, 124], [27000, 52], [27059, 124], [27300, 52], [27359, 124], [27600, 52], [27659, 124], [27900, 52], [27959, 124], [28200, 52], [28259, 124], [28500, 52], [28559, 124], [28800, 52], [28860, 123], [29100, 53], [29160, 123], [29400, 53], [29460, 123], [29700, 53], [29760, 123], [30000, 53], [30060, 123], [30300, 53], [30360, 123], [30600, 53], [30660, 123], [30900, 53], [30960, 123], [31200, 53], [31260, 123], [31500, 53], [31560, 122], [31800, 53], [31860, 122], [32100, 54], [32161, 121], [32400, 54], [32461, 121], [32700, 54], [32761, 121], [33000, 54], [33061, 121], [33300, 54], [33361, 121], [33600, 54], [33661, 121], [33900, 54], [33961, 121], [34200, 54], [34261, 121], [34500, 54], [34561, 121], [34800, 54], [34861, 121], [35100, 54], [35161, 121], [35400, 55], [35462, 120], [35700, 55], [35762, 120], [36000, 55], [36062, 120], [36300, 55], [36362, 120], [36600, 55], [36662, 120], [36900, 55], [36962, 120], [37200, 55], [37500, 55], [37800, 55], [38100, 55], [38400, 56], [38700, 55], [39000, 54], [39300, 52], [39600, 50], [39900, 48], [40200, 46], [40500, 44], [40800, 42], [41100, 41], [41400, 39], [41700, 37], [42000, 35], [42300, 33], [42600, 31], [42900, 29], [43200, 27], [43500, 25], [43800, 23], [44100, 21], [44400, 19], [44700, 18], [45000, 16], [45300, 14], [45600, 12], [45900, 10], [46200, 8], [46500, 6], [46800, 4], [47100, 2]], "point": [92, 78]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+01.75|+01.50|+02.53", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 93], [7916, 142], [8100, 89], [8222, 136], [8400, 88], [8525, 133], [8700, 88], [8826, 132], [9000, 88], [9126, 132], [9300, 88], [9429, 129], [9600, 88], [9732, 126], [9900, 88], [10033, 124], [10200, 88], [10326, 4], [10334, 123], [10500, 88], [10626, 5], [10635, 122], [10800, 89], [10926, 5], [10935, 122], [11100, 89], [11226, 6], [11235, 122], [11400, 89], [11526, 6], [11535, 122], [11700, 89], [11826, 6], [11835, 122], [12000, 89], [12126, 5], [12135, 122], [12300, 89], [12426, 5], [12435, 122], [12600, 89], [12726, 5], [12735, 122], [12900, 89], [13026, 5], [13034, 122], [13200, 90], [13326, 4], [13334, 122], [13500, 90], [13626, 4], [13634, 122], [13800, 90], [13926, 4], [13933, 123], [14100, 90], [14226, 3], [14233, 123], [14400, 90], [14526, 3], [14533, 123], [14700, 90], [14826, 3], [14832, 124], [15000, 90], [15126, 3], [15132, 124], [15300, 90], [15426, 2], [15432, 124], [15600, 90], [15726, 2], [15732, 124], [15900, 90], [16026, 2], [16031, 124], [16200, 90], [16326, 1], [16331, 124], [16500, 90], [16626, 1], [16631, 124], [16800, 90], [16930, 125], [17100, 91], [17230, 125], [17400, 91], [17530, 125], [17700, 91], [17829, 126], [18000, 91], [18129, 126], [18300, 91], [18428, 127], [18600, 91], [18727, 128], [18900, 91], [19027, 127], [19200, 91], [19326, 128], [19500, 91], [19626, 128], [19800, 91], [19926, 128], [20100, 91], [20226, 128], [20400, 91], [20526, 128], [20700, 92], [20826, 128], [21000, 92], [21126, 128], [21300, 92], [21426, 128], [21600, 92], [21726, 128], [21900, 92], [22026, 127], [22200, 92], [22326, 127], [22500, 92], [22625, 128], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[50, 211], [350, 211], [650, 211], [950, 210], [1250, 210], [1550, 210], [1850, 210], [2150, 210], [2450, 210], [2750, 210], [3050, 210], [3350, 210], [3650, 210], [3950, 209], [4250, 209], [4550, 209], [4800, 1], [4850, 209], [5100, 1], [5150, 209], [5400, 1], [5450, 209], [5700, 1], [5750, 209], [6000, 2], [6050, 209], [6300, 2], [6350, 209], [6600, 2], [6650, 209], [6900, 2], [6950, 208], [7200, 3], [7250, 208], [7500, 3], [7550, 208], [7800, 3], [7850, 43], [7916, 142], [8100, 3], [8150, 39], [8222, 136], [8400, 4], [8450, 38], [8525, 133], [8700, 4], [8750, 38], [8826, 132], [9000, 4], [9050, 38], [9126, 132], [9300, 4], [9350, 38], [9429, 129], [9600, 5], [9650, 38], [9732, 126], [9900, 5], [9950, 38], [10033, 124], [10200, 5], [10250, 38], [10326, 4], [10334, 123], [10500, 5], [10550, 38], [10626, 5], [10635, 122], [10800, 5], [10850, 39], [10926, 5], [10935, 122], [11100, 6], [11150, 39], [11226, 6], [11235, 122], [11400, 6], [11450, 39], [11526, 6], [11535, 122], [11700, 6], [11750, 39], [11826, 6], [11835, 122], [12000, 6], [12050, 39], [12126, 5], [12135, 122], [12300, 7], [12350, 39], [12426, 5], [12435, 122], [12600, 7], [12650, 39], [12726, 5], [12735, 122], [12900, 7], [12950, 39], [13026, 5], [13034, 122], [13200, 7], [13250, 40], [13326, 4], [13334, 122], [13500, 8], [13550, 40], [13626, 4], [13634, 122], [13800, 8], [13850, 40], [13926, 4], [13933, 123], [14100, 8], [14150, 40], [14226, 3], [14233, 123], [14400, 8], [14450, 40], [14526, 3], [14533, 123], [14700, 9], [14750, 40], [14826, 3], [14832, 124], [15000, 9], [15050, 40], [15126, 3], [15132, 124], [15300, 9], [15350, 40], [15426, 2], [15432, 124], [15600, 9], [15650, 40], [15726, 2], [15732, 124], [15900, 10], [15950, 40], [16026, 2], [16031, 124], [16200, 10], [16250, 40], [16326, 1], [16331, 124], [16500, 10], [16549, 41], [16626, 1], [16631, 124], [16800, 10], [16849, 41], [16930, 125], [17100, 11], [17149, 42], [17230, 125], [17400, 11], [17449, 42], [17530, 125], [17700, 11], [17749, 42], [17829, 126], [18000, 11], [18049, 42], [18129, 126], [18300, 11], [18349, 42], [18428, 127], [18600, 12], [18649, 42], [18727, 128], [18900, 12], [18949, 42], [19027, 127], [19200, 12], [19249, 42], [19326, 128], [19500, 12], [19549, 42], [19626, 128], [19800, 13], [19849, 42], [19926, 128], [20100, 13], [20149, 42], [20226, 128], [20400, 13], [20449, 42], [20526, 128], [20700, 13], [20749, 43], [20826, 128], [21000, 14], [21049, 43], [21126, 128], [21300, 21], [21348, 44], [21426, 128], [21600, 92], [21726, 128], [21900, 92], [22026, 127], [22200, 92], [22326, 127], [22500, 92], [22625, 128], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+01.75|+01.50|+02.53"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [0, 1, 49, 72], "mask": [[0, 50], [300, 50], [600, 50], [900, 50], [1200, 50], [1500, 50], [1800, 50], [2100, 50], [2400, 50], [2700, 50], [3000, 50], [3300, 50], [3600, 50], [3900, 50], [4200, 50], [4500, 50], [4801, 49], [5101, 49], [5401, 49], [5701, 49], [6002, 48], [6302, 48], [6602, 48], [6902, 48], [7203, 47], [7503, 47], [7803, 47], [8103, 47], [8404, 46], [8704, 46], [9004, 46], [9304, 46], [9605, 45], [9905, 45], [10205, 45], [10505, 45], [10805, 45], [11106, 44], [11406, 44], [11706, 44], [12006, 44], [12307, 43], [12607, 43], [12907, 43], [13207, 43], [13508, 42], [13808, 42], [14108, 42], [14408, 42], [14709, 41], [15009, 41], [15309, 41], [15609, 41], [15910, 40], [16210, 40], [16510, 39], [16810, 39], [17111, 38], [17411, 38], [17711, 38], [18011, 38], [18311, 38], [18612, 37], [18912, 37], [19212, 37], [19512, 37], [19813, 36], [20113, 36], [20413, 36], [20713, 36], [21014, 35], [21321, 27]], "point": [24, 35]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 93], [7916, 142], [8100, 89], [8222, 136], [8400, 88], [8525, 133], [8700, 88], [8826, 132], [9000, 88], [9126, 132], [9300, 88], [9429, 129], [9600, 88], [9732, 126], [9900, 88], [10033, 124], [10200, 88], [10326, 4], [10334, 123], [10500, 88], [10626, 5], [10635, 122], [10800, 89], [10926, 5], [10935, 122], [11100, 89], [11226, 6], [11235, 122], [11400, 89], [11526, 6], [11535, 122], [11700, 89], [11826, 6], [11835, 122], [12000, 89], [12126, 5], [12135, 122], [12300, 89], [12426, 5], [12435, 122], [12600, 89], [12726, 5], [12735, 122], [12900, 89], [13026, 5], [13034, 122], [13200, 90], [13326, 4], [13334, 122], [13500, 90], [13626, 4], [13634, 122], [13800, 90], [13926, 4], [13933, 123], [14100, 90], [14226, 3], [14233, 123], [14400, 90], [14526, 3], [14533, 123], [14700, 90], [14826, 3], [14832, 124], [15000, 90], [15126, 3], [15132, 124], [15300, 90], [15426, 2], [15432, 124], [15600, 90], [15726, 2], [15732, 124], [15900, 90], [16026, 2], [16031, 124], [16200, 90], [16326, 1], [16331, 124], [16500, 90], [16626, 1], [16631, 124], [16800, 90], [16930, 125], [17100, 91], [17230, 125], [17400, 91], [17530, 125], [17700, 91], [17829, 126], [18000, 91], [18129, 126], [18300, 91], [18428, 127], [18600, 91], [18727, 128], [18900, 91], [19027, 127], [19200, 91], [19326, 128], [19500, 91], [19626, 128], [19800, 91], [19926, 128], [20100, 91], [20226, 128], [20400, 91], [20526, 128], [20700, 92], [20826, 128], [21000, 92], [21126, 128], [21300, 92], [21426, 128], [21600, 92], [21726, 128], [21900, 92], [22026, 127], [22200, 92], [22326, 127], [22500, 92], [22625, 128], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+01.50|+02.11|+02.08"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [33, 81, 119, 147], "mask": [[24041, 79], [24341, 79], [24641, 79], [24941, 79], [25240, 80], [25540, 80], [25840, 80], [26140, 80], [26440, 80], [26740, 80], [27040, 80], [27340, 80], [27639, 81], [27939, 81], [28239, 81], [28539, 81], [28839, 81], [29139, 81], [29439, 81], [29739, 81], [30038, 81], [30338, 81], [30638, 81], [30938, 81], [31238, 81], [31538, 81], [31838, 81], [32138, 81], [32438, 81], [32737, 82], [33037, 82], [33337, 82], [33637, 82], [33937, 82], [34237, 82], [34537, 82], [34837, 82], [35136, 83], [35436, 83], [35736, 83], [36036, 83], [36336, 83], [36636, 83], [36936, 83], [37236, 83], [37535, 84], [37835, 84], [38135, 84], [38435, 84], [38735, 84], [39035, 83], [39335, 83], [39635, 83], [39934, 84], [40234, 84], [40534, 84], [40834, 84], [41134, 84], [41434, 84], [41734, 84], [42034, 84], [42334, 84], [42633, 84], [42933, 81], [43233, 78], [43533, 75], [43834, 73]], "point": [76, 113]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+01.75|+01.50|+02.53", "placeStationary": true, "receptacleObjectId": "Cabinet|+01.50|+02.11|+02.08"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 27, 114, 146], "mask": [[7800, 1], [8100, 1], [8400, 2], [8700, 3], [9000, 4], [9300, 5], [9600, 5], [9900, 6], [10200, 7], [10500, 8], [10800, 8], [11100, 9], [11400, 10], [11700, 11], [12000, 12], [12300, 12], [12600, 13], [12900, 14], [13200, 15], [13500, 15], [13800, 16], [14100, 17], [14400, 18], [14700, 19], [15000, 19], [15300, 20], [15600, 21], [15900, 22], [16200, 22], [16500, 23], [16800, 24], [17100, 25], [17400, 25], [17700, 26], [18000, 27], [18300, 28], [18600, 29], [18900, 29], [19200, 30], [19500, 31], [19800, 32], [20100, 32], [20400, 33], [20700, 34], [21000, 35], [21300, 36], [21600, 36], [21900, 37], [22200, 38], [22500, 39], [22800, 39], [23100, 40], [23400, 41], [23700, 42], [24000, 42], [24300, 43], [24600, 42], [24900, 42], [24945, 70], [25200, 42], [25245, 70], [25500, 42], [25545, 70], [25800, 42], [25845, 70], [26100, 42], [26145, 70], [26400, 42], [26444, 71], [26700, 42], [26744, 71], [27000, 42], [27044, 71], [27300, 41], [27344, 71], [27600, 41], [27644, 71], [27900, 41], [27944, 71], [28200, 41], [28244, 71], [28500, 41], [28544, 71], [28800, 41], [28843, 72], [29100, 41], [29143, 72], [29400, 41], [29443, 72], [29700, 40], [29743, 71], [30000, 40], [30043, 71], [30300, 40], [30343, 71], [30600, 40], [30643, 71], [30900, 40], [30943, 71], [31200, 40], [31243, 71], [31500, 40], [31542, 72], [31800, 40], [31843, 71], [32100, 40], [32400, 39], [32700, 39], [33000, 39], [33042, 72], [33300, 39], [33342, 72], [33600, 39], [33642, 72], [33900, 39], [33941, 73], [34200, 39], [34241, 73], [34500, 39], [34541, 73], [34800, 38], [34841, 73], [35100, 38], [35141, 73], [35400, 38], [35441, 73], [35700, 38], [35741, 73], [36000, 38], [36041, 73], [36300, 38], [36341, 73], [36600, 38], [36640, 74], [36900, 38], [36940, 74], [37200, 37], [37240, 74], [37500, 37], [37540, 73], [37800, 37], [37840, 73], [38101, 36], [38140, 73], [38403, 34], [38440, 73], [38704, 33], [38740, 73], [39006, 31], [39040, 73], [39308, 29], [39339, 74], [39609, 28], [39639, 74], [39911, 25], [39939, 74], [40213, 23], [40239, 74], [40514, 22], [40539, 74], [40816, 20], [40839, 74], [41117, 19], [41139, 74], [41419, 17], [41439, 74], [41721, 15], [41738, 75], [42022, 14], [42038, 75], [42324, 11], [42338, 75], [42625, 10], [42638, 75], [42927, 8], [42938, 75], [43229, 6], [43238, 73], [43530, 5]], "point": [57, 85]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+01.50|+02.11|+02.08"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 27, 114, 146], "mask": [[7800, 1], [8100, 1], [8400, 2], [8700, 3], [9000, 4], [9300, 5], [9600, 5], [9900, 6], [10200, 7], [10500, 8], [10800, 8], [11100, 9], [11400, 10], [11700, 11], [12000, 12], [12300, 12], [12600, 13], [12900, 14], [13200, 15], [13500, 15], [13800, 16], [14100, 17], [14400, 18], [14700, 19], [15000, 19], [15300, 20], [15600, 21], [15900, 22], [16200, 22], [16500, 23], [16800, 24], [17100, 25], [17400, 25], [17700, 26], [18000, 27], [18300, 28], [18600, 29], [18900, 29], [19200, 30], [19500, 31], [19800, 32], [20100, 32], [20400, 33], [20700, 34], [21000, 35], [21300, 36], [21600, 36], [21900, 37], [22200, 38], [22500, 39], [22800, 39], [23100, 40], [23400, 41], [23700, 42], [24000, 42], [24300, 43], [24600, 42], [24900, 42], [24945, 70], [25200, 42], [25245, 70], [25500, 42], [25545, 70], [25800, 42], [25845, 70], [26100, 42], [26145, 70], [26400, 42], [26444, 71], [26700, 42], [26744, 71], [27000, 42], [27044, 71], [27300, 41], [27344, 71], [27600, 41], [27644, 71], [27900, 41], [27944, 71], [28200, 41], [28244, 71], [28500, 41], [28544, 71], [28800, 41], [28843, 72], [29100, 41], [29143, 72], [29400, 41], [29443, 72], [29700, 40], [29743, 71], [30000, 40], [30043, 71], [30300, 40], [30343, 71], [30600, 40], [30643, 71], [30900, 40], [30943, 71], [31200, 40], [31243, 71], [31500, 40], [31542, 72], [31800, 40], [31843, 71], [32100, 40], [32400, 39], [32700, 39], [33000, 39], [33042, 72], [33300, 39], [33342, 72], [33600, 39], [33642, 72], [33900, 39], [33941, 73], [34200, 39], [34241, 52], [34301, 13], [34500, 39], [34541, 48], [34606, 8], [34800, 38], [34841, 47], [34907, 7], [35100, 38], [35141, 47], [35208, 6], [35400, 38], [35441, 47], [35508, 6], [35700, 38], [35741, 47], [35808, 6], [36000, 38], [36041, 47], [36108, 6], [36300, 38], [36341, 48], [36408, 6], [36600, 38], [36640, 49], [36708, 6], [36900, 38], [36940, 49], [37008, 6], [37200, 37], [37240, 49], [37308, 6], [37500, 37], [37540, 49], [37607, 6], [37800, 37], [37840, 49], [37907, 6], [38101, 36], [38140, 49], [38207, 6], [38403, 34], [38440, 49], [38507, 6], [38704, 33], [38740, 49], [38807, 6], [39006, 31], [39040, 49], [39107, 6], [39308, 29], [39339, 50], [39406, 7], [39609, 28], [39639, 50], [39706, 7], [39911, 25], [39939, 50], [40006, 7], [40213, 23], [40239, 50], [40306, 7], [40514, 22], [40539, 50], [40606, 7], [40816, 20], [40839, 50], [40906, 7], [41117, 19], [41139, 50], [41205, 8], [41419, 17], [41439, 50], [41505, 8], [41721, 15], [41738, 52], [41805, 8], [42022, 14], [42038, 52], [42105, 8], [42324, 11], [42338, 52], [42405, 8], [42625, 10], [42638, 52], [42705, 8], [42927, 8], [42938, 52], [43004, 9], [43229, 6], [43238, 52], [43304, 9], [43530, 5]], "point": [57, 85]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan12", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 0.25, "y": 0.9009999, "z": -1.0}, "object_poses": [{"objectName": "Pan_9f7abbea", "position": {"x": -0.9376832, "y": 0.5654289, "z": 0.11900004}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": 1.42390835, "y": 0.06976977, "z": 2.879911}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_d40bfead", "position": {"x": -1.043155, "y": 0.938448548, "z": 0.765623748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_d40bfead", "position": {"x": -0.7236026, "y": 0.747385561, "z": 0.847031236}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.79279995, "y": 0.7479664, "z": 2.3811}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -1.043155, "y": 0.9390294, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -0.9648372, "y": 0.8136976, "z": 0.348953933}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.75026655, "y": 1.49968576, "z": 2.52613354}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -1.11623383, "y": 1.500578, "z": 0.6922504}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -1.0766772, "y": 1.83225048, "z": 0.303504229}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -0.791773558, "y": 0.08874029, "z": 0.9856334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.689003944, "y": 0.7688297, "z": 1.05093741}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -1.12788, "y": 0.9598927, "z": 2.234908}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": 1.346072, "y": 0.09737432, "z": 0.373630881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -0.827398658, "y": 0.7431432, "z": 0.711093843}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": 1.66985226, "y": 1.02830076, "z": 0.8063456}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.971268654, "y": 1.38705444, "z": 0.04234863}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -1.211005, "y": 0.9329, "z": 2.3864}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": 1.42227757, "y": 0.8384428, "z": 1.36970234}, "rotation": {"x": -1.40334208e-14, "y": 9.659347e-06, "z": 4.25675352e-22}}, {"objectName": "Apple_d0e3153d", "position": {"x": 1.45694256, "y": 0.119435936, "z": 2.97771883}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": 1.46078944, "y": 1.01439834, "z": 1.677775}, "rotation": {"x": 5.162539, "y": 59.5351868, "z": 356.178833}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.30961347, "y": 0.100299239, "z": 0.171664327}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.9230002, "y": 0.990676045, "z": 2.26549983}, "rotation": {"x": 4.161468e-05, "y": 302.470367, "z": 6.4569067e-06}}, {"objectName": "Fork_d40bfead", "position": {"x": -0.8965961, "y": 0.747115731, "z": 0.847031236}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -0.899038553, "y": 0.6270374, "z": -0.110953823}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": 1.62637722, "y": 1.50050771, "z": 1.0115906}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.8657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": -0.79279995, "y": 0.7618369, "z": 0.914999962}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": 1.44873142, "y": 0.7688297, "z": 0.780662537}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -1.19292426, "y": 1.82503581, "z": -0.08169362}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": 1.3622911, "y": 0.9871304, "z": 2.59703}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -0.712255, "y": 0.9342062, "z": 2.3864}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -0.8769051, "y": 0.9379421, "z": 0.6879356}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": 1.43918228, "y": 0.970557153, "z": 1.00726593}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": 1.37759125, "y": 1.05943835, "z": 0.0120204445}, "rotation": {"x": 1.462218e-05, "y": -5.519249e-05, "z": -1.0589958e-05}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": -1.211005, "y": 0.937464237, "z": 2.159162}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.55604517, "y": 0.105368823, "z": 2.945116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -1.12137806, "y": 1.49968576, "z": 2.2165885}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pan_9f7abbea", "position": {"x": -0.971269131, "y": 1.4989289, "z": 0.195651278}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.793779969, "y": 0.9390294, "z": 1.0763762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": 1.65219212, "y": 1.59147274, "z": 1.59899747}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 1160125892, "scene_num": 12}, "task_id": "trial_T20190908_145944_296356", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2NGMLBFZ3YQP5_39RP059MEKAN642TBQQA150L89PMBY", "high_descs": ["Turn right, walk to wall, turn right to face espresso machine.", "Open left hand door of cabinet above espresso machine, remove glass.", "Turn right, walk a little to the sink, turn left to face sink and microwave.", "Put glass in microwave, turn microwave on, take glass out of the microwave.", "Turn all the way around to face the sink again.", "Open left hand door of cabinet above microwave, put glass on bottom shelf."], "task_desc": "Put a heated glass in the cabinet about the microwave.", "votes": [1, 1]}, {"assignment_id": "A36DK84J5YJ942_3L4D84MIL297W9ZU8LNE7OB2ZS8HJE", "high_descs": ["Move to the coffee maker to the left of the sink", "pick up a cup from the counter", "move to the sink to the right", "heat the cup in the microwave", "move away from the sink a bit", "put the cup in the cabinet above the sink"], "task_desc": "Put a heated cup in the cabinet above the sink.", "votes": [1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3VA45EW49Q4AT95DM9CYNXCHHXB1O7", "high_descs": ["Turn right and walk almost to the wall in front of you but stop short at the coffee maker and turn right and walk to it.", "Get the glass from the cupboard all the way to the left above the coffee maker.", "Move a couple steps to your right so you are standing looking at the middle of the sink.", "Warm the cup in the microwave then get it back out and close the door.", "Take a step backwards and look up.", "Put the cup in the cupboard above and to the left of the microwave and close the door."], "task_desc": "Put a warm cup in the cupboard.", "votes": [1, 1]}]}}