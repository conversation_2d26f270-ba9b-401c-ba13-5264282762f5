{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000197.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000198.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000199.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000200.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000201.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000202.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000203.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000206.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000207.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000262.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000268.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000269.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000270.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000271.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000272.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000273.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000274.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000275.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000276.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000277.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000278.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000279.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000280.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000281.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000282.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000283.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000284.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000285.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000286.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000287.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000288.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000289.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000290.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000291.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000292.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000293.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000294.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000295.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000296.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000297.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000298.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000299.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000300.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000301.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000302.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000303.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000304.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000305.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000306.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000307.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000308.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000309.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000310.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000311.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000312.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000313.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000314.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000315.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000316.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000317.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000318.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000319.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000320.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000321.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000322.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000323.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000324.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000325.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000326.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000327.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000328.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000329.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000330.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000331.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000332.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000333.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000334.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000335.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000336.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000337.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000338.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000339.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000340.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000341.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000342.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000343.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000344.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000345.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000346.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000347.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000348.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000349.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000350.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000351.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000352.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000353.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000354.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000355.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000356.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000357.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000358.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000359.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000360.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000361.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000362.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000363.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000364.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000365.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000366.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000367.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000368.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000369.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000370.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000371.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000372.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000373.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000374.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000375.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000376.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000377.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000378.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000379.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000380.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000381.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000382.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000383.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000384.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000385.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000386.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000387.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000388.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000389.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000390.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000391.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000392.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000393.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000394.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000395.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000396.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000397.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000398.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000399.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000400.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000401.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000402.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000403.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000404.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000405.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000406.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000407.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000408.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000409.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000410.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000411.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000412.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000413.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000414.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000415.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000416.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000417.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000418.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000419.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000420.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000421.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000422.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000423.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000424.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000425.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000426.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000427.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000428.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000429.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000430.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000431.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000432.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000433.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000434.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000435.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000436.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000437.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000438.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000439.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000440.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000441.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000442.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000443.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000444.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000445.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000446.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000447.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000448.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000449.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000450.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000451.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000452.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000453.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000454.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000455.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000456.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000457.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000458.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000459.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000460.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000461.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000462.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000463.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000464.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000465.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000466.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000467.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000468.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000469.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000470.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000471.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000472.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000473.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000474.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000475.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000476.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000477.png", "low_idx": 91}, {"high_idx": 11, "image_name": "000000478.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000479.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000480.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000481.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000482.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000483.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000484.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000485.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000486.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000487.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000488.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000489.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000490.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000491.png", "low_idx": 92}, {"high_idx": 11, "image_name": "000000492.png", "low_idx": 92}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|2|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [6.90489244, 6.90489244, -6.86917736, -6.86917736, 3.644968, 3.644968]], "coordinateReceptacleObjectId": ["CounterTop", [3.956, 3.956, -6.064, -6.064, 3.7868, 3.7868]], "forceVisible": true, "objectId": "ButterKnife|+01.73|+00.91|-01.72"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|1|1|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [6.46, 6.46, 3.044, 3.044, 3.9081588, 3.9081588]], "forceVisible": true, "objectId": "Bread|+01.62|+00.98|+00.76"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "microwave"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [6.90489244, 6.90489244, -6.86917736, -6.86917736, 3.644968, 3.644968]], "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "ButterKnife|+01.73|+00.91|-01.72", "receptacleObjectId": "Microwave|+01.75|+00.90|-00.84"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|4|1|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [6.46, 6.46, 3.044, 3.044, 3.9081588, 3.9081588]], "coordinateReceptacleObjectId": ["CounterTop", [3.956, 3.956, 4.068, 4.068, 3.7844, 3.7844]], "forceVisible": true, "objectId": "Bread|+01.62|+00.98|+00.76|BreadSliced_5"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|6|10|2|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "garbagecan"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [6.46, 6.46, 3.044, 3.044, 3.9081588, 3.9081588]], "coordinateReceptacleObjectId": ["GarbageCan", [7.08, 7.08, 6.74, 6.74, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|+01.62|+00.98|+00.76|BreadSliced_5", "receptacleObjectId": "GarbageCan|+01.77|+00.00|+01.69"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|+01.73|+00.91|-01.72"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [0, 156, 3, 162], "mask": [[46502, 2], [46801, 3], [47100, 4], [47400, 3], [47700, 2], [48000, 2], [48300, 1]], "point": [1, 158]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|+01.62|+00.98|+00.76"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [0, 114, 34, 191], "mask": [[33900, 2], [34200, 3], [34500, 3], [34800, 4], [35100, 5], [35400, 6], [35700, 7], [36000, 8], [36300, 9], [36600, 10], [36900, 10], [37200, 11], [37500, 11], [37800, 12], [38100, 12], [38400, 13], [38700, 14], [39000, 14], [39300, 15], [39600, 16], [39900, 16], [40200, 17], [40500, 18], [40800, 18], [41100, 19], [41400, 20], [41700, 20], [42000, 21], [42300, 21], [42600, 22], [42900, 23], [43200, 24], [43500, 24], [43800, 25], [44100, 26], [44400, 26], [44700, 27], [45000, 27], [45300, 28], [45600, 29], [45900, 29], [46200, 30], [46500, 30], [46800, 31], [47100, 31], [47400, 32], [47700, 33], [48000, 33], [48300, 33], [48600, 34], [48900, 34], [49200, 35], [49500, 35], [49800, 35], [50100, 35], [50400, 35], [50700, 35], [51000, 34], [51300, 34], [51600, 33], [51900, 32], [52200, 32], [52500, 31], [52800, 29], [53100, 27], [53400, 26], [53700, 24], [54000, 22], [54300, 20], [54600, 18], [54900, 16], [55200, 15], [55500, 13], [55800, 10], [56100, 8], [56400, 6], [56700, 4], [57000, 2]], "point": [17, 151]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|+01.73|+00.91|-01.72", "placeStationary": true, "receptacleObjectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 249], [24050, 250], [24349, 251], [24649, 251], [24948, 252], [25248, 252], [25547, 253], [25847, 253], [26146, 254], [26446, 254], [26745, 255], [27045, 255], [27344, 256], [27644, 256], [27943, 257], [28243, 257], [28542, 258], [28842, 258], [29141, 259], [29441, 258], [29740, 259], [30039, 259], [30339, 259], [30638, 259], [30938, 259], [31237, 259], [31537, 259], [31836, 259], [32136, 259], [32435, 259], [32735, 259], [33034, 259], [33334, 259], [33633, 259], [33933, 259], [34232, 260], [34532, 259], [34831, 260], [35131, 259], [35430, 260], [35730, 259], [36029, 260], [36328, 260], [36628, 260], [36927, 260], [37227, 260], [37526, 260], [37826, 260], [38125, 260], [38425, 260], [38724, 260], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 68], [75900, 64], [76200, 61], [76500, 60], [76800, 59], [77100, 59], [77400, 58], [77700, 58], [78000, 58], [78300, 58], [78600, 60], [78900, 62], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 249], [24050, 250], [24349, 251], [24649, 251], [24948, 252], [25248, 252], [25547, 253], [25847, 253], [26146, 254], [26446, 254], [26745, 255], [27045, 255], [27344, 256], [27644, 256], [27943, 257], [28243, 257], [28542, 258], [28842, 258], [29141, 259], [29441, 258], [29740, 259], [30039, 259], [30339, 259], [30638, 259], [30938, 259], [31237, 259], [31537, 259], [31836, 259], [32136, 259], [32435, 259], [32735, 259], [33034, 259], [33334, 259], [33633, 259], [33933, 259], [34232, 260], [34532, 259], [34831, 260], [35131, 259], [35430, 260], [35730, 259], [36029, 260], [36328, 260], [36628, 105], [36744, 144], [36927, 99], [37055, 28], [37096, 91], [37227, 94], [37399, 88], [37526, 93], [37699, 87], [37826, 93], [37999, 87], [38125, 155], [38297, 88], [38425, 260], [38724, 260], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+01.62|+00.98|+00.76|BreadSliced_5"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [0, 145, 37, 196], "mask": [[43206, 8], [43503, 16], [43800, 20], [44100, 22], [44400, 23], [44700, 24], [45000, 24], [45300, 24], [45600, 24], [45900, 25], [46200, 26], [46500, 27], [46800, 29], [47100, 30], [47400, 31], [47700, 32], [48000, 33], [48300, 34], [48600, 34], [48900, 35], [49200, 36], [49500, 36], [49800, 37], [50100, 37], [50400, 38], [50700, 38], [51000, 38], [51300, 38], [51600, 38], [51900, 38], [52200, 38], [52500, 37], [52800, 36], [53100, 36], [53400, 35], [53700, 34], [54000, 33], [54300, 32], [54600, 30], [54900, 28], [55200, 26], [55500, 24], [55800, 23], [56100, 21], [56400, 19], [56700, 17], [57000, 15], [57300, 13], [57600, 11], [57900, 9], [58200, 7], [58500, 5]], "point": [18, 169]}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+01.62|+00.98|+00.76|BreadSliced_5", "placeStationary": true, "receptacleObjectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 249], [24050, 250], [24349, 251], [24649, 251], [24948, 252], [25248, 252], [25547, 253], [25847, 253], [26146, 254], [26446, 254], [26745, 255], [27045, 255], [27344, 256], [27644, 256], [27943, 257], [28243, 257], [28542, 258], [28842, 258], [29141, 259], [29441, 258], [29740, 259], [30039, 259], [30339, 259], [30638, 259], [30938, 259], [31237, 259], [31537, 259], [31836, 259], [32136, 259], [32435, 259], [32735, 259], [33034, 259], [33334, 259], [33633, 259], [33933, 259], [34232, 260], [34532, 259], [34831, 260], [35131, 259], [35430, 260], [35730, 259], [36029, 260], [36328, 260], [36628, 105], [36744, 144], [36927, 99], [37055, 28], [37096, 91], [37227, 94], [37399, 88], [37526, 93], [37699, 87], [37826, 93], [37999, 87], [38125, 155], [38297, 88], [38425, 260], [38724, 260], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 130], [23908, 92], [24050, 130], [24209, 91], [24349, 130], [24510, 90], [24649, 128], [24811, 89], [24948, 129], [25112, 88], [25248, 128], [25412, 88], [25547, 128], [25712, 88], [25847, 128], [26012, 88], [26146, 128], [26312, 88], [26446, 128], [26612, 88], [26745, 129], [26912, 88], [27045, 129], [27211, 89], [27344, 130], [27511, 89], [27644, 131], [27811, 89], [27943, 131], [28110, 90], [28243, 131], [28411, 89], [28542, 131], [28711, 89], [28842, 131], [29011, 89], [29141, 131], [29312, 88], [29441, 131], [29612, 87], [29740, 131], [29912, 87], [30039, 132], [30213, 85], [30339, 131], [30513, 85], [30638, 132], [30813, 84], [30938, 132], [31113, 84], [31237, 132], [31413, 83], [31537, 132], [31713, 83], [31836, 133], [32013, 82], [32136, 133], [32313, 82], [32435, 134], [32613, 81], [32735, 133], [32913, 81], [33034, 134], [33213, 80], [33334, 134], [33512, 81], [33633, 135], [33812, 80], [33933, 135], [34112, 80], [34232, 136], [34412, 80], [34532, 136], [34711, 80], [34831, 138], [35011, 80], [35131, 142], [35307, 83], [35430, 260], [35730, 259], [36029, 260], [36328, 260], [36628, 105], [36744, 144], [36927, 99], [37055, 28], [37096, 91], [37227, 94], [37399, 88], [37526, 93], [37699, 87], [37826, 93], [37999, 87], [38125, 155], [38297, 88], [38425, 260], [38724, 260], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+01.62|+00.98|+00.76|BreadSliced_5"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [168, 80, 212, 118], "mask": [[23881, 27], [24180, 29], [24479, 31], [24777, 34], [25077, 35], [25376, 36], [25675, 37], [25975, 37], [26274, 38], [26574, 38], [26874, 38], [27174, 37], [27474, 37], [27775, 36], [28074, 36], [28374, 37], [28673, 38], [28973, 38], [29272, 40], [29572, 40], [29871, 41], [30171, 42], [30470, 43], [30770, 43], [31070, 43], [31369, 44], [31669, 44], [31969, 44], [32269, 44], [32569, 44], [32868, 45], [33168, 45], [33468, 44], [33768, 44], [34068, 44], [34368, 44], [34668, 43], [34969, 42], [35273, 34]], "point": [190, 98]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 249], [24050, 250], [24349, 251], [24649, 251], [24948, 252], [25248, 252], [25547, 253], [25847, 253], [26146, 254], [26446, 254], [26745, 255], [27045, 255], [27344, 256], [27644, 256], [27943, 257], [28243, 257], [28542, 258], [28842, 258], [29141, 259], [29441, 258], [29740, 259], [30039, 259], [30339, 259], [30638, 259], [30938, 259], [31237, 259], [31537, 259], [31836, 259], [32136, 259], [32435, 259], [32735, 259], [33034, 259], [33334, 259], [33633, 259], [33933, 259], [34232, 260], [34532, 259], [34831, 260], [35131, 259], [35430, 260], [35730, 259], [36029, 260], [36328, 260], [36628, 105], [36744, 144], [36927, 99], [37055, 28], [37096, 91], [37227, 94], [37399, 88], [37526, 93], [37699, 87], [37826, 93], [37999, 87], [38125, 155], [38297, 88], [38425, 260], [38724, 260], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 9}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+01.62|+00.98|+00.76|BreadSliced_5", "placeStationary": true, "receptacleObjectId": "GarbageCan|+01.77|+00.00|+01.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [56, 69, 129, 187], "mask": [[20478, 44], [20774, 51], [21073, 53], [21373, 54], [21672, 55], [21971, 57], [22270, 58], [22570, 58], [22870, 59], [23170, 58], [23470, 58], [23769, 59], [24069, 59], [24369, 59], [24669, 59], [24968, 60], [25268, 60], [25568, 60], [25868, 61], [26168, 61], [26467, 62], [26767, 62], [27067, 62], [27367, 62], [27667, 63], [27966, 64], [28266, 64], [28566, 64], [28866, 64], [29165, 65], [29465, 65], [29765, 65], [30065, 65], [30365, 65], [30664, 66], [30964, 66], [31264, 66], [31564, 66], [31864, 66], [32163, 67], [32463, 67], [32763, 67], [33063, 67], [33363, 67], [33662, 68], [33962, 68], [34262, 68], [34562, 67], [34861, 68], [35161, 68], [35461, 68], [35761, 68], [36061, 68], [36360, 69], [36660, 69], [36960, 69], [37260, 69], [37560, 69], [37859, 70], [38159, 70], [38459, 70], [38759, 70], [39058, 71], [39358, 53], [39415, 14], [39658, 51], [39718, 11], [39958, 50], [40019, 10], [40258, 49], [40319, 10], [40557, 49], [40619, 10], [40857, 49], [40919, 10], [41157, 48], [41219, 10], [41457, 48], [41519, 10], [41757, 48], [41818, 11], [42056, 49], [42118, 10], [42356, 49], [42418, 10], [42656, 49], [42718, 10], [42956, 49], [43017, 11], [43257, 48], [43317, 11], [43557, 49], [43614, 14], [43857, 71], [44157, 71], [44457, 71], [44758, 70], [45058, 70], [45360, 68], [45661, 67], [45962, 66], [46262, 66], [46563, 65], [46863, 65], [47163, 65], [47464, 64], [47764, 64], [48065, 63], [48365, 62], [48665, 62], [48966, 61], [49266, 61], [49567, 60], [49868, 59], [50169, 58], [50469, 58], [50770, 57], [51071, 56], [51372, 55], [51674, 53], [51976, 51], [52278, 49], [52580, 47], [52881, 46], [53183, 44], [53484, 43], [53786, 41], [54086, 41], [54387, 39], [54688, 38], [54989, 36], [55290, 35], [55591, 32], [55893, 28]], "point": [92, 127]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan14", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -0.75, "y": 0.9009992, "z": 1.5}, "object_poses": [{"objectName": "Potato_6d0d5c3c", "position": {"x": -0.2955938, "y": 1.10429347, "z": -1.38275528}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_38537d23", "position": {"x": 0.988999963, "y": 0.9106421, "z": 1.00428188}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 1.8659904, "y": 0.910764158, "z": -1.63160944}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_bb5e3511", "position": {"x": 1.807, "y": 0.9098, "z": -0.3203}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Spoon_89592edf", "position": {"x": 0.356796145, "y": 0.9123294, "z": -1.35207963}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 1.51657152, "y": 0.9261998, "z": -1.63161039}, "rotation": {"x": 0.0, "y": 269.999817, "z": 0.0}}, {"objectName": "Cup_5ecc05ec", "position": {"x": -0.692971647, "y": 1.3871752, "z": -1.66452444}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_5ecc05ec", "position": {"x": 0.476415873, "y": 0.9088847, "z": 1.18092084}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Knife_06a240e2", "position": {"x": 0.7327088, "y": 0.9430372, "z": 0.8276406}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "SoapBottle_fd471042", "position": {"x": 1.76943791, "y": 1.65207028, "z": -0.449611485}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_fd471042", "position": {"x": 1.8789382, "y": 2.13420439, "z": 1.12150455}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_817a54e2", "position": {"x": 1.73962343, "y": 0.134060889, "z": 1.58830917}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_817a54e2", "position": {"x": 0.22012496, "y": 0.974455357, "z": 0.915959358}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 1.75787544, "y": 0.9056, "z": 1.00428414}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 1.762083, "y": 0.905599952, "z": 0.5189738}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_e61d6e1d", "position": {"x": 1.86598969, "y": 1.01113224, "z": -1.37455559}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_e8bf9352", "position": {"x": -0.692971647, "y": 0.748195, "z": -1.59446669}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_fd471042", "position": {"x": 1.76208329, "y": 0.908764064, "z": 0.415256381}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_5ecc05ec", "position": {"x": 1.50158334, "y": 0.9088847, "z": 1.09260368}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Egg_85efe221", "position": {"x": 1.10124719, "y": 0.7958097, "z": -1.48894024}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_192d20c7", "position": {"x": 1.451921, "y": 0.9111485, "z": 0.415255427}, "rotation": {"x": 0.0, "y": 269.999817, "z": 0.0}}, {"objectName": "Ladle_4b0a72cf", "position": {"x": 1.20112443, "y": 0.92670095, "z": 0.7435573}, "rotation": {"x": 3.369621, "y": 0.0883946642, "z": 346.9858}}, {"objectName": "Apple_5b80d804", "position": {"x": 0.8403779, "y": 0.968506932, "z": -1.24763942}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 1.91716433, "y": 0.905599952, "z": 0.467115581}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pan_7013969f", "position": {"x": 1.28159308, "y": 0.900000036, "z": 1.19070256}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_817a54e2", "position": {"x": 0.7327076, "y": 0.974455357, "z": 1.18092167}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 0.7327081, "y": 0.9255999, "z": 1.00428116}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Potato_6d0d5c3c", "position": {"x": -0.7415557, "y": 1.72320378, "z": -1.55943775}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_06a240e2", "position": {"x": 1.13971, "y": 0.7610128, "z": -1.58487988}, "rotation": {"x": 271.548, "y": 81.95655, "z": 278.2198}}, {"objectName": "ButterKnife_38537d23", "position": {"x": 1.72622311, "y": 0.911242, "z": -1.71729434}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bread_8f8a19bf", "position": {"x": 1.615, "y": 0.9770397, "z": 0.761}, "rotation": {"x": 2.193904e-06, "y": 50.52559, "z": 2.0719106e-05}}, {"objectName": "Lettuce_e61d6e1d", "position": {"x": -0.741555452, "y": 0.5604041, "z": -1.412512}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 1.65633857, "y": 0.906199932, "z": -1.46024084}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_bb5e3511", "position": {"x": 1.807, "y": 0.9098, "z": 0.075}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 1.77, "y": 0.06976977, "z": 1.73184538}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_89592edf", "position": {"x": 1.72622263, "y": 0.9123293, "z": -1.54592526}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Bowl_7266264e", "position": {"x": 1.80571866, "y": 1.63194954, "z": -0.9739291}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f9995e03", "position": {"x": 1.84167039, "y": 1.64890611, "z": -0.257166266}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3544338132, "scene_num": 14}, "task_id": "trial_T20190908_135505_631381", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1O3TWBUDONVLO_3300DTYQT58KJBY39IOYK8ADFXGQEJ", "high_descs": ["Turn right and walk across the room to face the sink.", "Pick up the knife in the sink.", "Turn to the left and step left to face the counter to the left of the stove.", "Cut the loaf of bread on the counter into slices.", "Take a step to the right to face the microwave.", "Place the knife in the microwave.", "Turn left and step forward to face the bread on the counter.", "Pick up a slice of bread on the counter.", "Take a step to the right to face the microwave.", "Heat the bread in the microwave and then remove it.", "Walk around to the opposite side of the counter to face the trash can on the floor.", "Place the slice of bread in the trash can on the floor."], "task_desc": "TO heat a bread slice and place it in the trash can.", "votes": [1, 1, 1]}, {"assignment_id": "AJQGWGESKQT4Y_3P1L2B7AD4GV45VFBRJY8Y5X9UGOLF", "high_descs": ["Go right and then left and then right again to face the sink.", "Pick the butter knife up from the sink.", "Go to the left to stand at the counter to the left of the stove.", "Slice the bread on the counter.", "Go right and then turn to face the microwave on the left.", "Put the knife in the microwave and close the door.", "Go left and then turn to the right to face the counter to the left of the stove.", "Pick up a slice of bread from the counter.", "Go right and turn to the left to face the microwave.", "Put the bread in the microwave and shut the door and then open the door, take the bread out again and shut the door.", "Go left and then left again and then right to go around the counter and then turn to the right to face the black trash can.", "Put the bread in the trash can."], "task_desc": "Put a heated slice of bread in the trash can.", "votes": [1, 1, 1]}, {"assignment_id": "ARB8SJAWXUWTD_3BXQMRHWK2PANSXX7PWM7HCBRLMUMS", "high_descs": ["Go to the sink", "Pick up the butterknife", "Go to the left of the stove", "Cut the loaf of bread", "Go to the microwave", "Put the butterknife in the microwave", "Go to the left of the stove", "Pick up a slice of bread", "Go to the microwave", "Heat the bread", "Go to the black bin", "Put the slice of bread in the bin"], "task_desc": "Place a heated slice of bread in a bin", "votes": [1, 1, 0]}]}}