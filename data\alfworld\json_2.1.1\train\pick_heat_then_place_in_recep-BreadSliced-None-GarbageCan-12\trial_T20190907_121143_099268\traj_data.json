{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000104.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000107.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000108.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000109.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000110.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000111.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000262.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000268.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000269.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000270.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000271.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000272.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000273.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000274.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000275.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000276.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000277.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000278.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000279.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000280.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000281.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000282.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000283.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000284.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000285.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000286.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000287.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000288.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000289.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000290.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000291.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000292.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000293.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000294.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000295.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000322.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000323.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000324.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000325.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000326.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000327.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000328.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000329.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000330.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000331.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000332.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000333.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000334.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000335.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000336.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000337.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000338.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000339.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000340.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000341.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000342.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000343.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000344.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000345.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000346.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000347.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000348.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000349.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000350.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000351.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000352.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000353.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000354.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000355.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000356.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000357.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000358.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000359.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000360.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000361.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000362.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000363.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000364.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000365.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000366.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000367.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000368.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000369.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000370.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000371.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000372.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000373.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000374.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000375.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000376.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000377.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000378.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000379.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000380.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000381.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000382.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000383.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000384.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000385.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000386.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000387.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000388.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000389.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000390.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000391.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000392.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000393.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000394.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000395.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000396.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000397.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000398.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000399.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000400.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000401.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000402.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000403.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000404.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000405.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000406.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000407.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000408.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000409.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000410.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000411.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000412.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000413.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000414.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000415.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000416.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000417.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000418.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000419.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000420.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000421.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000422.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000423.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000424.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000425.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000426.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000427.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000428.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000429.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000430.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000431.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000432.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000433.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000434.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000435.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000436.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000437.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000438.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000439.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000440.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000441.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000442.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000443.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000444.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000445.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000446.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000447.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000448.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000449.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000450.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000451.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000452.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000453.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000454.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000455.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000456.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000457.png", "low_idx": 77}, {"high_idx": 11, "image_name": "000000458.png", "low_idx": 77}, {"high_idx": 11, "image_name": "000000459.png", "low_idx": 77}, {"high_idx": 11, "image_name": "000000460.png", "low_idx": 77}, {"high_idx": 11, "image_name": "000000461.png", "low_idx": 77}, {"high_idx": 11, "image_name": "000000462.png", "low_idx": 77}, {"high_idx": 11, "image_name": "000000463.png", "low_idx": 77}, {"high_idx": 11, "image_name": "000000464.png", "low_idx": 77}, {"high_idx": 11, "image_name": "000000465.png", "low_idx": 77}, {"high_idx": 11, "image_name": "000000466.png", "low_idx": 77}, {"high_idx": 11, "image_name": "000000467.png", "low_idx": 77}, {"high_idx": 11, "image_name": "000000468.png", "low_idx": 77}, {"high_idx": 11, "image_name": "000000469.png", "low_idx": 77}, {"high_idx": 11, "image_name": "000000470.png", "low_idx": 77}, {"high_idx": 11, "image_name": "000000471.png", "low_idx": 77}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-1|5|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [-3.5076204, -3.5076204, 4.61625768, 4.61625768, 3.7517684, 3.7517684]], "coordinateReceptacleObjectId": ["CounterTop", [-3.888, -3.888, 3.684, 3.684, 3.8936, 3.8936]], "forceVisible": true, "objectId": "ButterKnife|-00.88|+00.94|+01.15"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-1|3|3|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-3.50762, -3.50762, 3.3732476, 3.3732476, 4.02294064, 4.02294064]], "forceVisible": true, "objectId": "Bread|-00.88|+01.01|+00.84"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "microwave"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [-3.5076204, -3.5076204, 4.61625768, 4.61625768, 3.7517684, 3.7517684]], "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "ButterKnife|-00.88|+00.94|+01.15", "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-1|3|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-3.50762, -3.50762, 3.3732476, 3.3732476, 4.02294064, 4.02294064]], "coordinateReceptacleObjectId": ["CounterTop", [-3.888, -3.888, 3.684, 3.684, 3.8936, 3.8936]], "forceVisible": true, "objectId": "Bread|-00.88|+01.01|+00.84|BreadSliced_2"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|4|11|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "garbagecan"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-3.50762, -3.50762, 3.3732476, 3.3732476, 4.02294064, 4.02294064]], "coordinateReceptacleObjectId": ["GarbageCan", [5.80777024, 5.80777024, 11.65005396, 11.65005396, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-00.88|+01.01|+00.84|BreadSliced_2", "receptacleObjectId": "GarbageCan|+01.45|+00.00|+02.91"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|-00.88|+00.94|+01.15"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [115, 128, 126, 184], "mask": [[38225, 2], [38525, 2], [38825, 2], [39125, 2], [39425, 2], [39725, 2], [40025, 2], [40324, 3], [40624, 3], [40924, 2], [41221, 1], [41224, 2], [41521, 1], [41523, 3], [41821, 5], [42121, 5], [42421, 4], [42721, 4], [43021, 4], [43321, 4], [43621, 4], [43922, 2], [44222, 2], [44522, 2], [44822, 2], [45123, 1], [45423, 1], [52915, 3], [53215, 6], [53516, 5], [53816, 5], [54116, 5], [54416, 4], [54716, 4], [55017, 3]], "point": [123, 151]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-00.88|+01.01|+00.84"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [134, 121, 228, 171], "mask": [[36164, 8], [36180, 13], [36203, 5], [36448, 9], [36460, 16], [36478, 17], [36500, 11], [36745, 68], [37043, 71], [37341, 80], [37640, 82], [37939, 84], [38239, 85], [38538, 86], [38838, 87], [39138, 87], [39438, 87], [39738, 87], [40037, 89], [40336, 90], [40636, 90], [40936, 90], [41235, 91], [41535, 92], [41835, 92], [42135, 93], [42435, 93], [42735, 93], [43034, 94], [43334, 93], [43634, 93], [43934, 93], [44234, 94], [44534, 94], [44834, 94], [45134, 95], [45434, 95], [45734, 95], [46034, 94], [46334, 94], [46634, 94], [46934, 94], [47234, 94], [47534, 94], [47834, 94], [48134, 94], [48434, 94], [48734, 94], [49034, 93], [49335, 92], [49637, 89], [49938, 88], [50239, 85], [50541, 81], [50843, 22], [50866, 23], [50893, 25], [51151, 3], [51169, 16]], "point": [181, 145]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|-00.88|+00.94|+01.15", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 33], [18039, 216], [18300, 28], [18352, 203], [18600, 24], [18659, 196], [18900, 20], [18964, 190], [19200, 17], [19268, 54], [19333, 121], [19500, 13], [19576, 31], [19638, 116], [19800, 10], [19884, 9], [19940, 114], [20100, 9], [20241, 113], [20400, 9], [20541, 113], [20700, 9], [20841, 113], [21000, 9], [21141, 113], [21300, 10], [21441, 113], [21600, 30], [21741, 113], [21900, 97], [22040, 113], [22200, 107], [22338, 115], [22500, 122], [22633, 120], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 5}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.88|+01.01|+00.84|BreadSliced_2"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [154, 121, 168, 170], "mask": [[36158, 7], [36454, 11], [36754, 11], [37054, 11], [37354, 11], [37654, 11], [37954, 11], [38254, 11], [38554, 11], [38854, 12], [39154, 12], [39454, 12], [39754, 12], [40054, 12], [40354, 12], [40654, 12], [40954, 12], [41254, 12], [41555, 11], [41855, 12], [42155, 12], [42455, 12], [42755, 12], [43055, 12], [43355, 12], [43655, 12], [43955, 12], [44255, 12], [44555, 12], [44855, 12], [45156, 11], [45456, 11], [45756, 12], [46056, 12], [46356, 12], [46656, 12], [46956, 12], [47256, 12], [47556, 12], [47856, 12], [48157, 11], [48457, 11], [48757, 11], [49057, 11], [49357, 11], [49657, 11], [49957, 12], [50257, 12], [50557, 12], [50858, 11]], "point": [161, 144]}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.88|+01.01|+00.84|BreadSliced_2", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 33], [18039, 216], [18300, 28], [18352, 203], [18600, 24], [18659, 196], [18900, 20], [18964, 190], [19200, 17], [19268, 54], [19333, 121], [19500, 13], [19576, 31], [19638, 116], [19800, 10], [19884, 9], [19940, 114], [20100, 9], [20241, 113], [20400, 9], [20541, 113], [20700, 9], [20841, 113], [21000, 9], [21141, 113], [21300, 10], [21441, 113], [21600, 30], [21741, 113], [21900, 97], [22040, 113], [22200, 107], [22338, 115], [22500, 122], [22633, 120], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 68], [99, 162], [300, 68], [402, 159], [600, 68], [704, 157], [900, 68], [1005, 155], [1200, 68], [1305, 155], [1500, 68], [1606, 154], [1800, 68], [1908, 1], [1916, 144], [2100, 68], [2219, 141], [2400, 68], [2522, 138], [2700, 68], [2823, 137], [3000, 68], [3124, 136], [3300, 68], [3425, 135], [3600, 68], [3725, 135], [3900, 68], [4026, 133], [4200, 68], [4327, 132], [4500, 68], [4628, 131], [4800, 68], [4928, 131], [5100, 68], [5228, 131], [5400, 68], [5528, 131], [5700, 68], [5829, 130], [6000, 68], [6129, 130], [6300, 68], [6429, 130], [6600, 68], [6729, 130], [6900, 69], [7030, 128], [7200, 69], [7330, 128], [7500, 69], [7631, 127], [7800, 69], [7931, 127], [8100, 69], [8231, 127], [8400, 69], [8531, 127], [8700, 69], [8832, 126], [9000, 69], [9132, 126], [9300, 69], [9432, 126], [9600, 69], [9733, 125], [9900, 69], [10033, 124], [10200, 69], [10333, 124], [10500, 70], [10634, 123], [10800, 70], [10934, 123], [11100, 70], [11234, 123], [11400, 70], [11534, 123], [11700, 70], [11834, 123], [12000, 70], [12134, 123], [12300, 70], [12434, 123], [12600, 70], [12734, 123], [12900, 70], [13034, 122], [13200, 70], [13333, 123], [13500, 70], [13633, 123], [13800, 71], [13933, 123], [14100, 71], [14233, 123], [14400, 71], [14533, 123], [14700, 71], [14832, 124], [15000, 71], [15132, 124], [15300, 71], [15432, 124], [15600, 71], [15732, 124], [15900, 71], [16031, 124], [16200, 71], [16331, 124], [16500, 71], [16631, 124], [16800, 71], [16930, 125], [17100, 71], [17230, 125], [17400, 72], [17529, 126], [17700, 72], [17829, 126], [18000, 33], [18039, 33], [18128, 127], [18300, 28], [18352, 20], [18427, 128], [18600, 24], [18659, 13], [18727, 128], [18900, 20], [18964, 8], [19026, 128], [19200, 17], [19268, 4], [19333, 121], [19500, 13], [19638, 116], [19800, 10], [19940, 114], [20100, 9], [20241, 113], [20400, 9], [20541, 113], [20700, 9], [20841, 113], [21000, 9], [21141, 113], [21300, 10], [21441, 113], [21600, 30], [21741, 113], [21900, 74], [22040, 113], [22200, 74], [22299, 8], [22338, 115], [22500, 74], [22595, 27], [22633, 120], [22800, 77], [22890, 163], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.88|+01.01|+00.84|BreadSliced_2"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [68, 1, 133, 77], "mask": [[68, 31], [368, 34], [668, 36], [968, 37], [1268, 37], [1568, 38], [1868, 40], [1909, 7], [2168, 51], [2468, 54], [2768, 55], [3068, 56], [3368, 57], [3668, 57], [3968, 58], [4268, 59], [4568, 60], [4868, 60], [5168, 60], [5468, 60], [5768, 61], [6068, 61], [6368, 61], [6668, 61], [6969, 61], [7269, 61], [7569, 62], [7869, 62], [8169, 62], [8469, 62], [8769, 63], [9069, 63], [9369, 63], [9669, 64], [9969, 64], [10269, 64], [10570, 64], [10870, 64], [11170, 64], [11470, 64], [11770, 64], [12070, 64], [12370, 64], [12670, 64], [12970, 64], [13270, 63], [13570, 63], [13871, 62], [14171, 62], [14471, 62], [14771, 61], [15071, 61], [15371, 61], [15671, 61], [15971, 60], [16271, 60], [16571, 60], [16871, 59], [17171, 59], [17472, 57], [17772, 57], [18072, 56], [18372, 55], [18672, 55], [18972, 54], [19272, 50], [19576, 31], [19884, 9], [21974, 23], [22274, 25], [22574, 21], [22877, 13]], "point": [100, 38]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 33], [18039, 216], [18300, 28], [18352, 203], [18600, 24], [18659, 196], [18900, 20], [18964, 190], [19200, 17], [19268, 54], [19333, 121], [19500, 13], [19576, 31], [19638, 116], [19800, 10], [19884, 9], [19940, 114], [20100, 9], [20241, 113], [20400, 9], [20541, 113], [20700, 9], [20841, 113], [21000, 9], [21141, 113], [21300, 10], [21441, 113], [21600, 30], [21741, 113], [21900, 97], [22040, 113], [22200, 107], [22338, 115], [22500, 122], [22633, 120], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.88|+01.01|+00.84|BreadSliced_2", "placeStationary": true, "receptacleObjectId": "GarbageCan|+01.45|+00.00|+02.91"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [64, 137, 150, 241], "mask": [[40886, 55], [41182, 62], [41479, 69], [41778, 70], [42077, 72], [42376, 74], [42675, 75], [42975, 76], [43275, 76], [43575, 76], [43874, 77], [44174, 77], [44474, 77], [44774, 77], [45074, 77], [45374, 77], [45673, 78], [45973, 78], [46273, 78], [46573, 78], [46873, 78], [47173, 78], [47472, 79], [47772, 79], [48072, 79], [48372, 79], [48672, 79], [48971, 80], [49271, 80], [49571, 80], [49871, 80], [50171, 80], [50471, 80], [50770, 81], [51070, 81], [51370, 81], [51670, 81], [51970, 81], [52270, 81], [52569, 82], [52869, 82], [53169, 82], [53469, 82], [53769, 82], [54069, 82], [54368, 83], [54668, 83], [54968, 83], [55268, 83], [55568, 83], [55868, 83], [56167, 84], [56467, 84], [56767, 84], [57067, 84], [57367, 84], [57667, 53], [57724, 27], [57966, 51], [58026, 25], [58266, 50], [58328, 23], [58566, 49], [58629, 22], [58866, 48], [58929, 22], [59166, 48], [59230, 21], [59466, 47], [59530, 21], [59765, 48], [59831, 20], [60065, 47], [60131, 20], [60365, 47], [60431, 20], [60665, 47], [60731, 20], [60965, 47], [61031, 20], [61265, 48], [61331, 20], [61564, 49], [61630, 21], [61864, 49], [61930, 21], [62164, 50], [62229, 22], [62464, 51], [62528, 23], [62764, 52], [62827, 24], [63064, 54], [63126, 25], [63365, 85], [63665, 85], [63965, 84], [64266, 82], [64566, 82], [64868, 80], [65170, 61], [65233, 15], [65473, 55], [65775, 51], [66077, 47], [66379, 42], [66682, 38], [66985, 33], [67287, 30], [67589, 27], [67891, 24], [68193, 21], [68494, 19], [68795, 18], [69095, 17], [69396, 15], [69697, 14], [69998, 12], [70298, 12], [70599, 11], [70900, 9], [71200, 9], [71501, 8], [71801, 8], [72104, 4]], "point": [107, 188]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan12", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 1.0, "y": 0.9009999, "z": 2.5}, "object_poses": [{"objectName": "Potato_22312ae0", "position": {"x": 1.51607227, "y": 0.9705572, "z": 2.02297}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.79279995, "y": 0.7479664, "z": 0.847031236}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -1.12788, "y": 0.9390294, "z": 2.310654}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.8657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.58995283, "y": 1.499687, "z": 2.24829674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.74609518, "y": 1.502705, "z": 0.918661}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": 1.38253057, "y": 0.105463147, "z": 0.171664327}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -1.043155, "y": 0.9598927, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.30788827, "y": 1.07142246, "z": 0.274840862}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.66985226, "y": 0.9372058, "z": 0.9402925}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -0.99543643, "y": 1.94781125, "z": 1.55199826}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": 1.41413271, "y": 0.7428734, "z": 1.12050617}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.04122126, "y": 1.497415, "z": 2.5241127}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -0.893249, "y": 0.195201755, "z": 0.282503963}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": -0.9763272, "y": 0.615049839, "z": 0.118999928}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": 1.46078944, "y": 1.01439834, "z": 1.677775}, "rotation": {"x": 5.162539, "y": 59.5351868, "z": 356.178833}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": -1.15446556, "y": 1.82803547, "z": -0.03552404}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.876905, "y": 1.00573516, "z": 0.8433119}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_d40bfead", "position": {"x": -0.96002996, "y": 0.938448548, "z": 0.8433119}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -1.209405, "y": 0.999118, "z": 1.0763762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": -0.753566861, "y": 0.08065146, "z": 0.9153209}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.4704}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": -0.876905, "y": 0.9528999, "z": 0.6879356}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.758201241, "y": 0.7688297, "z": 2.51703739}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": 1.78703427, "y": 0.9342061, "z": 1.45886016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": 1.48997676, "y": 0.119435936, "z": 2.91251349}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -0.7937801, "y": 0.9342062, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -0.8769051, "y": 0.9379421, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": 1.59296227, "y": 0.970557153, "z": 0.8063456}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -0.804073453, "y": 0.0866650939, "z": 2.31314516}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": 1.379534, "y": 0.7461314, "z": 0.848631263}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": -0.712255, "y": 0.9730633, "z": 2.3864}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.45544767, "y": 0.0995715857, "z": -0.0303022265}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_9f7abbea", "position": {"x": 1.3781, "y": 1.73753989, "z": 0.3929}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.792800069, "y": 0.7476966, "z": 0.9829687}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": 1.38810134, "y": 0.8384428, "z": 1.31293225}, "rotation": {"x": 1.40334191e-14, "y": 180.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1847204746, "scene_num": 12}, "task_id": "trial_T20190907_121143_099268", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A30M9SZYJKFDS9_3Z9WI9EOZ2527T5CQ6NQ83BFEJEKHQ", "high_descs": ["turn to the right and across the room", "pick up the knife from the counter ", "turn to the left ", "cut the bread into slices ", "turn to the right and across the room ", "open the microwave and put the knife inside the microwave ", "turn to the right and across the room", "grab a couple of slices of bread ", "turn to the right and across the room", "open the microwave door put the bread inside heat up the bread and open the microwave door ", "get the bread out from the microwave turn to the left and across the room", "throw it to the garbage can"], "task_desc": "cut into slices a loaf of bread take some slices heat them up throw them to the garbage", "votes": [1, 1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_32SVAV9L3IQH18PRZULZAV79QVQA34", "high_descs": ["Turn left, go to the sink, then turn right to face the counter between the stove and the fridge.", "Pick up the butter knife on the counter, nearest to the pepper.", "Turn left and the turn to face to the bread on the counter.", "Slice the bread with the knife.", "Go to the sink behind you, and look up at the microwave.", "Put the butter knife in the microwave.", "Turn around, and return to the counter between the fridge and the stove.", "Pick up a slice of bread from the counter.", "Turn around and go to the microwave.", "Heat the slice of bread in the microwave.", "Turn left, and go to the trash can at the end of the counter.", "Put the slice of bread in the trash can."], "task_desc": "Put a slice of heated bread in the trash.", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_34V1S5K3GVI2T824QQ5XL389U3O693", "high_descs": ["Turn left and begin walking across the room, then hang a right and walk over to the oven.", "Pick up the butter knife closest to the oven on the counter to the left of the oven.", "Turn left and begin walking forward, then turn right and walk up to the counter to the right of the fridge.", "Slice up the loaf of bread on the counter.", "Turn right and begin walking across the room, then hang a right and walk up to the sink and look up at the microwave.", "Open the microwave and put the butter knife inside, then close the door.", "Turn right and begin walking across the room, then hang a right and walk up to the sliced loaf of bread on the counter.", "Pick up a piece of sliced bread off of the counter.", "Turn right and begin walking across the room, then hang a right and walk up to the sink and look up at the microwave.", "Open the microwave and put the piece of sliced bread inside, heat it up for a couple seconds then remove it and close the microwave door.", "Turn left and walk over to the small silver bin in the corner of the room.", "Put the heated slice of toast into the silver bin."], "task_desc": "Put a heated slice of toast in a silver bin.", "votes": [1, 1]}, {"assignment_id": "A3C81THYYSBGVD_379J5II41RX17PWSL5RL20RHBVHELH", "high_descs": ["Turn left then head towards the counter by the stove in front of you.", "Take the knife from the counter in front of you.", "Turn left then face the bread on the counter to your right.", "Slice the bread in front of you on the counter.", "Turn around and face the microwave on top of the sink.", "Place the knife in the microwave and close it.", "Turn around and head towards the bread on the counter in front of you.", "Take a slice of bread from the counter in front of you.", "Turn around and head towards the microwave on top of the sink.", "Heat up the slice of bread in the microwave, take out the slice of bread and close the microwave.", "Turn left then face the steel trash can on your right.", "Place the slice of bread in the steel trash can in front of you."], "task_desc": "Place a hot slice of bread in a steel trash can.", "votes": [1, 1]}, {"assignment_id": "A2NGMLBFZ3YQP5_3JRJSWSMQK2KRL63FZNL6EX0BH03EI", "high_descs": ["Turn left, walk past stove, turn right, walk to counter to the left of stove.", "Pick knife up from the counter.", "Turn left, turn right to face counter.", "Slice the bread that's on the counter.", "Turn right, turn right, walk to microwave.", "Put knife in the microwave.", "Turn right, turn right, cross room to counter with bread on it.", "Pick up bread from the counter.", "Turn right, turn right, walk to microwave.", "Put bread in microwave, turn microwave on, take bread out of microwave.", "Turn left, walk to garbage can, turn right to face can.", " Throw bread in the garbage can."], "task_desc": "Warm a knife and slice of bread, throw bread in the garbage.", "votes": [0, 1, 1]}, {"assignment_id": "A35P2RX8HKZM96_3R6BYFZZPATODXDDUZEFVC74UFYXFG", "high_descs": ["Turn left, then right, and approach the left side of the stove.", "Take the silver knife on the counter.", "Turn left, then right, and approach the bread on the counter.", "Slice the bread on the counter.", "Turn around and walk to the sink.", "Put the knife in the microwave above the sink.", "Turn around and walk back to the bread on the counter.", "Take a slice of bread on the counter.", "Turn around and walk back to the fridge.", "Put the bread in the microwave, close the door, and turn it on. Remove it when it's done and close the door.", "Turn left and walk to the trash can on the right.", "Put the bread in the trash."], "task_desc": "Put a microwaved bread slice in the trash can.", "votes": [1, 1, 0]}]}}