{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000257.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000258.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000259.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000260.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000261.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000262.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000268.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000269.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000270.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000271.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000272.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000273.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000274.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000275.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000276.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000277.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000278.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000279.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000280.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000281.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000282.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000283.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000284.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000285.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000286.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000287.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000288.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000289.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000290.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000291.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000292.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000293.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000294.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000295.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000330.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000331.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000332.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000333.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 51}, {"high_idx": 9, "image_name": "000000335.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000336.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000337.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000338.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000339.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000340.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000341.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000342.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000343.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000344.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000345.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000346.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000347.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000348.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000349.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000350.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000351.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000352.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000353.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000354.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000355.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000356.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000357.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000358.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000359.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000360.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000361.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000362.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000363.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000364.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000365.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000366.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000367.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000368.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000369.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000370.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000371.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000372.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000373.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000374.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000375.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000376.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000377.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000378.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000379.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000380.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000381.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000382.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000383.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000384.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000385.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000386.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000387.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000388.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000389.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000390.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000391.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000392.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000393.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000394.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000395.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000396.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000397.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000398.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000399.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000400.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000401.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000402.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000403.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000404.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000405.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000406.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000407.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000408.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000409.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000410.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000411.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000412.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000413.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000414.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000415.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000416.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000417.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000418.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000419.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000420.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000421.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000422.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000423.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000424.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000425.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000426.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000427.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000428.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000429.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000430.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000431.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000432.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000433.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000434.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000435.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000436.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000437.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000438.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000439.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000440.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000441.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000442.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000443.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000444.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000445.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000446.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000447.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000448.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000449.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000450.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000451.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000452.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000453.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000454.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000455.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000456.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000457.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000458.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000459.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000460.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000461.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000462.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000463.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000464.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000465.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000466.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000467.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000468.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000469.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000470.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000471.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000472.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000473.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000474.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000475.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000476.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000477.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000478.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000479.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000480.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000481.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000482.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000483.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000484.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000485.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000486.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000487.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000488.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000489.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000490.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000491.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000492.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000493.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000494.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000495.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000496.png", "low_idx": 76}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-5|8|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [-2.648344, -2.648344, 7.6828704, 7.6828704, 4.18875836, 4.18875836]], "coordinateReceptacleObjectId": ["DiningTable", [-2.48, -2.48, 9.944, 9.944, 0.0698738544, 0.0698738544]], "forceVisible": true, "objectId": "ButterKnife|-00.66|+01.05|+01.92"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|9|1|60"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-3.634574176, -3.634574176, 8.57996464, 8.57996464, 4.53558828, 4.53558828]], "forceVisible": true, "objectId": "Bread|-00.91|+01.13|+02.14"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|7|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [-2.648344, -2.648344, 7.6828704, 7.6828704, 4.18875836, 4.18875836]], "coordinateReceptacleObjectId": ["Cabinet", [-4.006524, -4.006524, 2.9368168, 2.9368168, 1.57375586, 1.57375586]], "forceVisible": true, "objectId": "ButterKnife|-00.66|+01.05|+01.92", "receptacleObjectId": "Cabinet|-01.00|+00.39|+00.73"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-5|9|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-3.634574176, -3.634574176, 8.57996464, 8.57996464, 4.53558828, 4.53558828]], "coordinateReceptacleObjectId": ["DiningTable", [-2.48, -2.48, 9.944, 9.944, 0.0698738544, 0.0698738544]], "forceVisible": true, "objectId": "Bread|-00.91|+01.13|+02.14|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-6|4|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-1.468, -1.468, 1.72, 1.72, 4.4524064, 4.4524064]], "forceVisible": true, "objectId": "Microwave|-00.37|+01.11|+00.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-15|6|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "garbagecan"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-3.634574176, -3.634574176, 8.57996464, 8.57996464, 4.53558828, 4.53558828]], "coordinateReceptacleObjectId": ["GarbageCan", [-14.784332, -14.784332, 8.0351286, 8.0351286, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-00.91|+01.13|+02.14|BreadSliced_1", "receptacleObjectId": "GarbageCan|-03.70|+00.00|+02.01"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|-00.66|+01.05|+01.92"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [172, 110, 179, 125], "mask": [[32872, 3], [33172, 4], [33472, 4], [33772, 5], [34072, 5], [34373, 5], [34673, 6], [34973, 6], [35273, 6], [35573, 7], [35873, 7], [36173, 7], [36473, 7], [36773, 7], [37073, 4], [37373, 1]], "point": [175, 116]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-00.91|+01.13|+02.14"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [139, 82, 266, 161], "mask": [[24509, 10], [24764, 72], [25056, 89], [25351, 101], [25648, 105], [25946, 109], [26245, 110], [26544, 112], [26844, 112], [27143, 114], [27443, 114], [27743, 115], [28043, 115], [28343, 115], [28642, 117], [28942, 117], [29242, 117], [29542, 118], [29841, 119], [30141, 120], [30441, 120], [30741, 121], [31040, 122], [31340, 122], [31640, 122], [31940, 122], [32240, 123], [32540, 123], [32840, 123], [33140, 124], [33440, 124], [33740, 124], [34040, 125], [34339, 126], [34639, 126], [34939, 126], [35239, 127], [35539, 127], [35839, 127], [36139, 127], [36439, 128], [36739, 128], [37039, 128], [37339, 128], [37639, 127], [37939, 127], [38239, 126], [38539, 126], [38839, 125], [39139, 125], [39439, 124], [39739, 124], [40039, 123], [40339, 123], [40639, 123], [40939, 123], [41239, 123], [41539, 123], [41839, 122], [42139, 122], [42439, 121], [42739, 121], [43039, 120], [43339, 120], [43639, 119], [43939, 118], [44240, 116], [44540, 116], [44840, 115], [45140, 114], [45440, 114], [45740, 113], [46040, 112], [46341, 110], [46641, 110], [46942, 108], [47243, 106], [47544, 104], [47846, 99], [48151, 20], [48178, 18], [48212, 26]], "point": [202, 120]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-01.00|+00.39|+00.73"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 119, 96, 206], "mask": [[35400, 75], [35700, 75], [36000, 75], [36300, 75], [36600, 76], [36900, 76], [37200, 76], [37501, 75], [37801, 76], [38102, 75], [38402, 75], [38703, 74], [39003, 75], [39304, 74], [39604, 74], [39905, 74], [40205, 74], [40506, 73], [40806, 73], [41107, 73], [41407, 73], [41708, 72], [42009, 71], [42309, 72], [42610, 71], [42910, 71], [43211, 70], [43511, 71], [43812, 70], [44112, 70], [44413, 69], [44713, 70], [45014, 69], [45314, 69], [45615, 68], [45915, 69], [46216, 68], [46516, 68], [46817, 67], [47117, 68], [47418, 67], [47718, 67], [48019, 67], [48320, 66], [48620, 66], [48921, 65], [49221, 66], [49522, 65], [49822, 65], [50123, 64], [50423, 65], [50724, 64], [51024, 64], [51325, 63], [51625, 64], [51926, 63], [52226, 63], [52527, 62], [52827, 63], [53128, 62], [53428, 62], [53729, 61], [54030, 61], [54330, 61], [54631, 60], [54931, 60], [55232, 60], [55532, 60], [55833, 59], [56133, 59], [56434, 59], [56734, 59], [57035, 58], [57335, 59], [57636, 58], [57936, 58], [58237, 57], [58537, 58], [58838, 57], [59138, 57], [59439, 56], [59740, 56], [60040, 56], [60341, 55], [60641, 55], [60942, 55], [61242, 55], [61543, 54]], "point": [48, 161]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|-00.66|+01.05|+01.92", "placeStationary": true, "receptacleObjectId": "Cabinet|-01.00|+00.39|+00.73"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 121, 93, 259], "mask": [[36000, 1], [36300, 2], [36600, 2], [36608, 64], [36900, 3], [36909, 64], [37200, 3], [37209, 64], [37500, 4], [37510, 63], [37800, 4], [37810, 63], [38100, 5], [38111, 63], [38400, 5], [38411, 63], [38700, 6], [38712, 62], [39000, 6], [39012, 62], [39300, 7], [39313, 62], [39600, 7], [39613, 62], [39900, 8], [39914, 61], [40200, 8], [40214, 62], [40500, 9], [40515, 61], [40800, 9], [40815, 61], [41100, 10], [41116, 60], [41400, 10], [41416, 61], [41700, 11], [41717, 60], [42000, 11], [42017, 60], [42300, 12], [42318, 59], [42600, 12], [42618, 60], [42900, 13], [42918, 60], [43200, 13], [43219, 59], [43500, 14], [43519, 60], [43800, 15], [43820, 59], [44100, 15], [44120, 59], [44400, 16], [44421, 58], [44700, 16], [44721, 59], [45000, 17], [45022, 58], [45300, 17], [45322, 58], [45600, 18], [45623, 57], [45900, 18], [45923, 58], [46200, 19], [46224, 57], [46500, 19], [46524, 52], [46800, 20], [46825, 50], [47100, 20], [47125, 48], [47400, 21], [47426, 45], [47700, 21], [47726, 44], [48000, 22], [48027, 41], [48300, 22], [48327, 41], [48600, 23], [48628, 40], [48900, 23], [48928, 39], [49200, 24], [49229, 38], [49500, 24], [49529, 38], [49800, 25], [49830, 36], [50100, 25], [50130, 36], [50400, 26], [50431, 30], [50465, 1], [50700, 26], [50731, 28], [51000, 27], [51032, 25], [51300, 27], [51332, 24], [51600, 28], [51633, 22], [51669, 1], [51900, 28], [51933, 22], [52200, 29], [52234, 21], [52273, 9], [52500, 29], [52534, 21], [52573, 9], [52800, 30], [52835, 20], [52870, 2], [52873, 8], [53100, 30], [53135, 20], [53171, 1], [53173, 8], [53400, 31], [53436, 20], [53471, 1], [53473, 8], [53700, 31], [53736, 20], [53773, 8], [54000, 32], [54037, 19], [54072, 8], [54300, 33], [54337, 20], [54372, 8], [54600, 33], [54638, 19], [54671, 9], [54900, 34], [54938, 20], [54971, 9], [55200, 34], [55239, 19], [55270, 9], [55500, 35], [55539, 20], [55569, 10], [55800, 35], [55840, 20], [55868, 11], [56100, 36], [56140, 23], [56164, 15], [56189, 1], [56400, 36], [56441, 38], [56489, 1], [56700, 37], [56741, 49], [57000, 37], [57042, 49], [57300, 38], [57342, 49], [57600, 38], [57643, 48], [57900, 39], [57943, 49], [58200, 39], [58244, 48], [58500, 40], [58544, 48], [58800, 40], [58845, 47], [59100, 41], [59145, 48], [59400, 41], [59446, 47], [59700, 42], [59746, 47], [60000, 42], [60047, 46], [60300, 43], [60347, 47], [60600, 43], [60900, 44], [61200, 44], [61500, 45], [61800, 44], [62100, 44], [62400, 44], [62700, 43], [63000, 43], [63300, 43], [63600, 42], [63900, 42], [64200, 41], [64500, 41], [64800, 41], [65100, 40], [65400, 40], [65700, 40], [66000, 39], [66300, 39], [66600, 39], [66900, 38], [67200, 38], [67500, 38], [67800, 37], [68100, 37], [68400, 37], [68700, 36], [69000, 36], [69301, 35], [69602, 33], [69903, 32], [70204, 31], [70504, 30], [70805, 29], [71106, 28], [71407, 26], [71708, 25], [72009, 24], [72310, 22], [72610, 22], [72911, 21], [73212, 19], [73513, 18], [73814, 17], [74115, 15], [74415, 15], [74716, 14], [75017, 12], [75318, 11], [75619, 10], [75920, 8], [76220, 8], [76521, 7], [76822, 5], [77123, 4], [77424, 3]], "point": [46, 189]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-01.00|+00.39|+00.73"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 121, 93, 259], "mask": [[36000, 1], [36300, 2], [36600, 2], [36608, 64], [36900, 3], [36909, 64], [37200, 3], [37209, 64], [37500, 4], [37510, 63], [37800, 4], [37810, 63], [38100, 5], [38111, 63], [38400, 5], [38411, 63], [38700, 6], [38712, 62], [39000, 6], [39012, 62], [39300, 7], [39313, 62], [39600, 7], [39613, 62], [39900, 8], [39914, 61], [40200, 8], [40214, 62], [40500, 9], [40515, 61], [40800, 9], [40815, 61], [41100, 10], [41116, 60], [41400, 10], [41416, 61], [41700, 11], [41717, 60], [42000, 11], [42017, 60], [42300, 12], [42318, 59], [42600, 12], [42618, 60], [42900, 13], [42918, 60], [43200, 13], [43219, 59], [43500, 14], [43519, 60], [43800, 15], [43820, 59], [44100, 15], [44120, 59], [44400, 16], [44421, 58], [44700, 16], [44721, 59], [45000, 17], [45022, 58], [45300, 17], [45322, 58], [45600, 18], [45623, 57], [45900, 18], [45923, 58], [46200, 19], [46224, 57], [46500, 19], [46524, 52], [46800, 20], [46825, 50], [47100, 20], [47125, 48], [47400, 21], [47426, 45], [47700, 21], [47726, 44], [48000, 22], [48027, 41], [48300, 22], [48327, 41], [48600, 23], [48628, 40], [48900, 23], [48928, 39], [49200, 24], [49229, 38], [49500, 24], [49529, 38], [49800, 25], [49830, 36], [50100, 25], [50130, 36], [50400, 26], [50431, 30], [50465, 1], [50700, 26], [50731, 28], [51000, 27], [51032, 25], [51300, 27], [51332, 24], [51600, 28], [51633, 22], [51669, 1], [51900, 28], [51933, 22], [52200, 29], [52234, 21], [52273, 9], [52500, 29], [52534, 21], [52573, 9], [52800, 30], [52835, 20], [52870, 2], [52873, 8], [53100, 30], [53135, 20], [53171, 1], [53173, 8], [53400, 31], [53436, 20], [53471, 1], [53473, 8], [53700, 31], [53736, 20], [53773, 8], [54000, 32], [54037, 19], [54072, 8], [54300, 33], [54337, 20], [54372, 8], [54600, 33], [54638, 19], [54671, 9], [54900, 34], [54938, 20], [54971, 9], [55200, 34], [55239, 19], [55270, 9], [55500, 35], [55539, 20], [55569, 10], [55800, 35], [55840, 20], [55868, 11], [56100, 36], [56140, 23], [56164, 15], [56189, 1], [56400, 36], [56441, 38], [56489, 1], [56700, 37], [56741, 49], [57000, 37], [57042, 49], [57300, 38], [57342, 49], [57600, 38], [57643, 13], [57670, 13], [57690, 1], [57900, 39], [57943, 10], [57991, 1], [58200, 39], [58244, 32], [58290, 2], [58500, 40], [58544, 48], [58800, 40], [58845, 47], [59100, 41], [59145, 48], [59400, 41], [59446, 47], [59700, 42], [59746, 47], [60000, 42], [60047, 46], [60300, 43], [60347, 47], [60600, 43], [60900, 44], [61200, 44], [61500, 45], [61800, 44], [62100, 44], [62400, 44], [62700, 43], [63000, 43], [63300, 43], [63600, 42], [63900, 42], [64200, 41], [64500, 41], [64800, 41], [65100, 40], [65400, 40], [65700, 40], [66000, 39], [66300, 39], [66600, 39], [66900, 38], [67200, 38], [67500, 38], [67800, 37], [68100, 37], [68400, 37], [68700, 36], [69000, 36], [69301, 35], [69602, 33], [69903, 32], [70204, 31], [70504, 30], [70805, 29], [71106, 28], [71407, 26], [71708, 25], [72009, 24], [72310, 22], [72610, 22], [72911, 21], [73212, 19], [73513, 18], [73814, 17], [74115, 15], [74415, 15], [74716, 14], [75017, 12], [75318, 11], [75619, 10], [75920, 8], [76220, 8], [76521, 7], [76822, 5], [77123, 4], [77424, 3]], "point": [46, 189]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.91|+01.13|+02.14|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [197, 83, 220, 161], "mask": [[24802, 12], [25102, 13], [25402, 13], [25703, 13], [26003, 13], [26304, 13], [26604, 13], [26904, 13], [27204, 14], [27504, 14], [27804, 14], [28105, 13], [28405, 13], [28705, 14], [29005, 14], [29305, 14], [29605, 14], [29905, 14], [30205, 14], [30506, 13], [30806, 14], [31106, 14], [31406, 14], [31706, 14], [32006, 14], [32306, 14], [32606, 14], [32906, 14], [33206, 14], [33506, 14], [33806, 14], [34106, 14], [34407, 13], [34707, 13], [35007, 13], [35307, 14], [35607, 14], [35907, 14], [36206, 15], [36506, 15], [36806, 15], [37106, 14], [37406, 14], [37706, 14], [38006, 14], [38306, 14], [38606, 14], [38906, 14], [39206, 13], [39505, 14], [39805, 14], [40105, 13], [40404, 14], [40704, 14], [41004, 14], [41304, 14], [41604, 13], [41904, 13], [42204, 13], [42503, 13], [42803, 13], [43103, 13], [43402, 13], [43702, 13], [44001, 13], [44301, 13], [44601, 12], [44900, 13], [45200, 13], [45500, 12], [45799, 13], [46099, 12], [46399, 12], [46698, 12], [46998, 12], [47298, 11], [47597, 12], [47897, 11], [48197, 11]], "point": [208, 121]}}, "high_idx": 7}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [223, 27, 299, 143], "mask": [[8042, 58], [8331, 69], [8631, 69], [8931, 69], [9231, 69], [9531, 69], [9831, 69], [10130, 70], [10430, 70], [10730, 70], [11030, 70], [11330, 70], [11630, 70], [11930, 70], [12230, 70], [12529, 71], [12829, 71], [13129, 71], [13429, 71], [13729, 71], [14029, 71], [14329, 71], [14628, 72], [14928, 72], [15228, 72], [15528, 72], [15828, 72], [16128, 72], [16428, 72], [16727, 73], [17027, 73], [17327, 73], [17627, 73], [17927, 73], [18227, 73], [18527, 73], [18826, 74], [19126, 74], [19426, 74], [19726, 74], [20026, 74], [20326, 74], [20626, 74], [20925, 75], [21225, 75], [21525, 75], [21825, 75], [22125, 75], [22425, 75], [22725, 75], [23024, 76], [23324, 76], [23624, 76], [23924, 76], [24224, 76], [24524, 76], [24824, 76], [25123, 77], [25423, 77], [25723, 77], [26023, 77], [26323, 77], [26624, 76], [26925, 75], [27225, 75], [27526, 74], [27827, 73], [28128, 72], [28429, 71], [28730, 70], [29030, 70], [29331, 69], [29632, 68], [29933, 67], [30234, 66], [30535, 65], [30835, 65], [31136, 64], [31437, 63], [31738, 62], [32039, 61], [32340, 60], [32641, 59], [32941, 59], [33242, 58], [33543, 57], [33844, 56], [34145, 55], [34446, 54], [34746, 54], [35047, 53], [35348, 52], [35649, 51], [35950, 50], [36251, 49], [36551, 49], [36852, 48], [37153, 47], [37454, 46], [37755, 45], [38056, 44], [38356, 44], [38657, 43], [38958, 42], [39259, 41], [39560, 40], [39861, 39], [40161, 39], [40462, 33], [40497, 3], [40763, 31], [40798, 2], [41075, 19], [41099, 1], [41376, 18], [41683, 8], [41983, 7], [42283, 7], [42583, 7], [42886, 3]], "point": [261, 84]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.91|+01.13|+02.14|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [146, 27, 299, 143], "mask": [[7946, 95], [8042, 58], [8246, 154], [8546, 154], [8846, 154], [9146, 154], [9446, 154], [9746, 154], [10046, 154], [10346, 154], [10646, 154], [10946, 154], [11246, 154], [11546, 154], [11846, 154], [12146, 154], [12446, 154], [12746, 154], [13046, 154], [13346, 154], [13646, 154], [13946, 154], [14246, 154], [14546, 154], [14846, 154], [15146, 154], [15446, 154], [15746, 154], [16046, 154], [16346, 154], [16646, 154], [16946, 154], [17246, 154], [17546, 154], [17846, 154], [18146, 154], [18446, 154], [18746, 154], [19046, 154], [19346, 154], [19646, 154], [19946, 154], [20246, 154], [20546, 154], [20846, 154], [21146, 154], [21446, 154], [21746, 154], [22046, 154], [22346, 154], [22646, 154], [22946, 154], [23246, 154], [23546, 154], [23846, 154], [24146, 154], [24446, 154], [24746, 154], [25046, 154], [25346, 154], [25646, 154], [25946, 154], [26246, 154], [26631, 69], [26932, 68], [27233, 67], [27534, 66], [27835, 65], [28136, 64], [28437, 63], [28738, 62], [29039, 61], [29339, 61], [29640, 60], [29941, 59], [30242, 58], [30543, 57], [30844, 56], [31145, 55], [31446, 54], [31747, 53], [32048, 52], [32349, 51], [32650, 50], [32950, 50], [33251, 49], [33552, 48], [33853, 47], [34154, 46], [34454, 46], [34754, 46], [35053, 47], [35353, 47], [35653, 47], [35953, 47], [36253, 47], [36552, 48], [36852, 48], [37153, 47], [37454, 46], [37755, 45], [38056, 44], [38356, 44], [38657, 43], [38958, 42], [39259, 41], [39560, 40], [39861, 39], [40161, 39], [40462, 33], [40497, 3], [40763, 31], [40798, 2], [41075, 19], [41099, 1], [41376, 18], [41683, 8], [41983, 7], [42283, 7], [42583, 7], [42886, 3]], "point": [222, 84]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [146, 27, 299, 143], "mask": [[7946, 95], [8042, 58], [8246, 154], [8546, 154], [8846, 154], [9146, 154], [9446, 154], [9746, 154], [10046, 154], [10346, 154], [10646, 154], [10946, 154], [11246, 154], [11546, 154], [11846, 154], [12146, 154], [12446, 154], [12746, 154], [13046, 154], [13346, 154], [13646, 154], [13946, 154], [14246, 154], [14546, 154], [14846, 108], [14962, 38], [15146, 106], [15263, 37], [15446, 106], [15563, 37], [15746, 105], [15864, 36], [16046, 105], [16165, 35], [16346, 104], [16465, 35], [16646, 104], [16765, 35], [16946, 104], [17064, 36], [17246, 104], [17364, 36], [17546, 103], [17663, 37], [17846, 103], [17963, 37], [18146, 103], [18263, 37], [18446, 102], [18563, 37], [18746, 102], [18863, 37], [19046, 102], [19162, 38], [19346, 102], [19462, 38], [19646, 102], [19762, 38], [19946, 102], [20062, 38], [20246, 101], [20361, 39], [20546, 101], [20661, 39], [20846, 101], [20961, 39], [21146, 101], [21261, 39], [21446, 101], [21561, 39], [21746, 100], [21860, 40], [22046, 100], [22160, 40], [22346, 100], [22460, 40], [22646, 100], [22760, 40], [22946, 100], [23060, 40], [23246, 100], [23359, 41], [23546, 100], [23658, 42], [23846, 100], [23958, 42], [24146, 100], [24258, 42], [24446, 101], [24558, 42], [24746, 102], [24858, 42], [25046, 103], [25158, 42], [25346, 104], [25458, 42], [25646, 106], [25758, 42], [25946, 107], [26058, 42], [26246, 154], [26631, 69], [26932, 68], [27233, 67], [27534, 66], [27835, 65], [28136, 64], [28437, 63], [28738, 62], [29039, 61], [29339, 61], [29640, 60], [29941, 59], [30242, 58], [30543, 57], [30844, 56], [31145, 55], [31446, 54], [31747, 53], [32048, 52], [32349, 51], [32650, 50], [32950, 50], [33251, 49], [33552, 48], [33853, 47], [34154, 46], [34454, 46], [34754, 46], [35053, 47], [35353, 47], [35653, 47], [35953, 47], [36253, 47], [36552, 48], [36852, 48], [37153, 47], [37454, 46], [37755, 45], [38056, 44], [38356, 44], [38657, 43], [38958, 42], [39259, 41], [39560, 40], [39861, 39], [40161, 39], [40462, 33], [40497, 3], [40763, 31], [40798, 2], [41075, 19], [41099, 1], [41376, 18], [41683, 8], [41983, 7], [42283, 7], [42583, 7], [42886, 3]], "point": [222, 84]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-1.468, -1.468, 1.72, 1.72, 4.4524064, 4.4524064]], "forceVisible": true, "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [223, 27, 299, 143], "mask": [[8042, 58], [8331, 69], [8631, 69], [8931, 69], [9231, 69], [9531, 69], [9831, 69], [10130, 70], [10430, 70], [10730, 70], [11030, 70], [11330, 70], [11630, 70], [11930, 70], [12230, 70], [12529, 71], [12829, 71], [13129, 71], [13429, 71], [13729, 71], [14029, 71], [14329, 71], [14628, 72], [14928, 72], [15228, 72], [15528, 72], [15828, 72], [16128, 72], [16428, 72], [16727, 73], [17027, 73], [17327, 73], [17627, 73], [17927, 73], [18227, 73], [18527, 73], [18826, 74], [19126, 74], [19426, 74], [19726, 74], [20026, 74], [20326, 74], [20626, 74], [20925, 75], [21225, 75], [21525, 75], [21825, 75], [22125, 75], [22425, 75], [22725, 75], [23024, 76], [23324, 76], [23624, 76], [23924, 76], [24224, 76], [24524, 76], [24824, 76], [25123, 77], [25423, 77], [25723, 77], [26023, 77], [26323, 77], [26624, 76], [26925, 75], [27225, 75], [27526, 74], [27827, 73], [28128, 72], [28429, 71], [28730, 70], [29030, 70], [29331, 69], [29632, 68], [29933, 67], [30234, 66], [30535, 65], [30835, 65], [31136, 64], [31437, 63], [31738, 62], [32039, 61], [32340, 60], [32641, 59], [32941, 59], [33242, 58], [33543, 57], [33844, 56], [34145, 55], [34446, 54], [34746, 54], [35047, 53], [35348, 52], [35649, 51], [35950, 50], [36251, 49], [36551, 49], [36852, 48], [37153, 47], [37454, 46], [37755, 45], [38056, 44], [38356, 44], [38657, 43], [38958, 42], [39259, 41], [39560, 40], [39861, 39], [40161, 39], [40462, 33], [40497, 3], [40763, 31], [40798, 2], [41075, 19], [41099, 1], [41376, 18], [41683, 8], [41983, 7], [42283, 7], [42583, 7], [42886, 3]], "point": [261, 84]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-1.468, -1.468, 1.72, 1.72, 4.4524064, 4.4524064]], "forceVisible": true, "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [223, 27, 299, 143], "mask": [[8042, 58], [8331, 69], [8631, 69], [8931, 69], [9231, 69], [9531, 69], [9831, 69], [10130, 70], [10430, 70], [10730, 70], [11030, 70], [11330, 70], [11630, 70], [11930, 70], [12230, 70], [12529, 71], [12829, 71], [13129, 71], [13429, 71], [13729, 71], [14029, 71], [14329, 71], [14628, 72], [14928, 72], [15228, 72], [15528, 72], [15828, 72], [16128, 72], [16428, 72], [16727, 73], [17027, 73], [17327, 73], [17627, 73], [17927, 73], [18227, 73], [18527, 73], [18826, 74], [19126, 74], [19426, 74], [19726, 74], [20026, 74], [20326, 74], [20626, 74], [20925, 75], [21225, 75], [21525, 75], [21825, 75], [22125, 75], [22425, 75], [22725, 75], [23024, 76], [23324, 76], [23624, 76], [23924, 76], [24224, 76], [24524, 76], [24824, 76], [25123, 77], [25423, 77], [25723, 77], [26023, 77], [26323, 77], [26624, 76], [26925, 75], [27225, 75], [27526, 74], [27827, 73], [28128, 72], [28429, 71], [28730, 70], [29030, 70], [29331, 69], [29632, 68], [29933, 67], [30234, 66], [30535, 65], [30835, 65], [31136, 64], [31437, 63], [31738, 62], [32039, 61], [32340, 60], [32641, 59], [32941, 59], [33242, 58], [33543, 57], [33844, 56], [34145, 55], [34446, 54], [34746, 54], [35047, 53], [35348, 52], [35649, 51], [35950, 50], [36251, 49], [36551, 49], [36852, 48], [37153, 47], [37454, 46], [37755, 45], [38056, 44], [38356, 44], [38657, 43], [38958, 42], [39259, 41], [39560, 40], [39861, 39], [40161, 39], [40462, 33], [40497, 3], [40763, 31], [40798, 2], [41075, 19], [41099, 1], [41376, 18], [41683, 8], [41983, 7], [42283, 7], [42583, 7], [42886, 3]], "point": [261, 84]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [223, 27, 299, 143], "mask": [[8042, 58], [8331, 69], [8631, 69], [8931, 69], [9231, 69], [9531, 69], [9831, 69], [10130, 70], [10430, 70], [10730, 70], [11030, 70], [11330, 70], [11630, 70], [11930, 70], [12230, 70], [12529, 71], [12829, 71], [13129, 71], [13429, 71], [13729, 71], [14029, 71], [14329, 71], [14628, 72], [14928, 72], [15228, 72], [15528, 72], [15828, 72], [16128, 72], [16428, 72], [16727, 73], [17027, 73], [17327, 73], [17627, 73], [17927, 73], [18227, 73], [18527, 73], [18826, 74], [19126, 74], [19426, 74], [19726, 74], [20026, 74], [20326, 74], [20626, 74], [20925, 75], [21225, 75], [21525, 75], [21825, 75], [22125, 75], [22425, 75], [22725, 75], [23024, 76], [23324, 76], [23624, 76], [23924, 76], [24224, 76], [24524, 76], [24824, 76], [25123, 77], [25423, 77], [25723, 77], [26023, 77], [26323, 77], [26624, 76], [26925, 75], [27225, 75], [27526, 74], [27827, 73], [28128, 72], [28429, 71], [28730, 70], [29030, 70], [29331, 69], [29632, 68], [29933, 67], [30234, 66], [30535, 65], [30835, 65], [31136, 64], [31437, 63], [31738, 62], [32039, 61], [32340, 60], [32641, 59], [32941, 59], [33242, 58], [33543, 57], [33844, 56], [34145, 55], [34446, 54], [34746, 54], [35047, 53], [35348, 52], [35649, 51], [35950, 50], [36251, 49], [36551, 49], [36852, 48], [37153, 47], [37454, 46], [37755, 45], [38056, 44], [38356, 44], [38657, 43], [38958, 42], [39259, 41], [39560, 40], [39861, 39], [40161, 39], [40462, 33], [40497, 3], [40763, 31], [40798, 2], [41075, 19], [41099, 1], [41376, 18], [41683, 8], [41983, 7], [42283, 7], [42583, 7], [42886, 3]], "point": [261, 84]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.91|+01.13|+02.14|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [246, 50, 264, 87], "mask": [[14954, 8], [15252, 11], [15552, 11], [15851, 13], [16151, 14], [16450, 15], [16750, 15], [17050, 14], [17350, 14], [17649, 14], [17949, 14], [18249, 14], [18548, 15], [18848, 15], [19148, 14], [19448, 14], [19748, 14], [20048, 14], [20347, 14], [20647, 14], [20947, 14], [21247, 14], [21547, 14], [21846, 14], [22146, 14], [22446, 14], [22746, 14], [23046, 14], [23346, 13], [23646, 12], [23946, 12], [24246, 12], [24547, 11], [24848, 10], [25149, 9], [25450, 8], [25752, 6], [26053, 5]], "point": [255, 67]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [146, 27, 299, 143], "mask": [[7946, 95], [8042, 58], [8246, 154], [8546, 154], [8846, 154], [9146, 154], [9446, 154], [9746, 154], [10046, 154], [10346, 154], [10646, 154], [10946, 154], [11246, 154], [11546, 154], [11846, 154], [12146, 154], [12446, 154], [12746, 154], [13046, 154], [13346, 154], [13646, 154], [13946, 154], [14246, 154], [14546, 154], [14846, 154], [15146, 154], [15446, 154], [15746, 154], [16046, 154], [16346, 154], [16646, 154], [16946, 154], [17246, 154], [17546, 154], [17846, 154], [18146, 154], [18446, 154], [18746, 154], [19046, 154], [19346, 154], [19646, 154], [19946, 154], [20246, 154], [20546, 154], [20846, 154], [21146, 154], [21446, 154], [21746, 154], [22046, 154], [22346, 154], [22646, 154], [22946, 154], [23246, 154], [23546, 154], [23846, 154], [24146, 154], [24446, 154], [24746, 154], [25046, 154], [25346, 154], [25646, 154], [25946, 154], [26246, 154], [26631, 69], [26932, 68], [27233, 67], [27534, 66], [27835, 65], [28136, 64], [28437, 63], [28738, 62], [29039, 61], [29339, 61], [29640, 60], [29941, 59], [30242, 58], [30543, 57], [30844, 56], [31145, 55], [31446, 54], [31747, 53], [32048, 52], [32349, 51], [32650, 50], [32950, 50], [33251, 49], [33552, 48], [33853, 47], [34154, 46], [34454, 46], [34754, 46], [35053, 47], [35353, 47], [35653, 47], [35953, 47], [36253, 47], [36552, 48], [36852, 48], [37153, 47], [37454, 46], [37755, 45], [38056, 44], [38356, 44], [38657, 43], [38958, 42], [39259, 41], [39560, 40], [39861, 39], [40161, 39], [40462, 33], [40497, 3], [40763, 31], [40798, 2], [41075, 19], [41099, 1], [41376, 18], [41683, 8], [41983, 7], [42283, 7], [42583, 7], [42886, 3]], "point": [222, 84]}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.91|+01.13|+02.14|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "GarbageCan|-03.70|+00.00|+02.01"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [98, 127, 228, 222], "mask": [[37915, 94], [38213, 99], [38511, 103], [38809, 106], [39108, 108], [39407, 110], [39706, 112], [40006, 113], [40305, 115], [40604, 116], [40903, 118], [41203, 118], [41503, 119], [41803, 119], [42102, 120], [42402, 121], [42702, 121], [43002, 121], [43302, 121], [43602, 121], [43901, 123], [44201, 123], [44501, 123], [44801, 123], [45101, 123], [45401, 123], [45701, 124], [46001, 124], [46300, 125], [46600, 125], [46900, 125], [47200, 125], [47500, 126], [47800, 126], [48100, 126], [48400, 126], [48700, 126], [48999, 127], [49299, 128], [49599, 128], [49899, 128], [50199, 128], [50499, 128], [50799, 129], [51099, 129], [51398, 130], [51698, 130], [51998, 130], [52298, 130], [52598, 131], [52898, 131], [53198, 131], [53498, 131], [53798, 131], [54098, 131], [54398, 131], [54698, 131], [54998, 131], [55298, 131], [55598, 131], [55898, 131], [56198, 131], [56499, 130], [56799, 129], [57099, 128], [57400, 127], [57701, 125], [58001, 125], [58302, 123], [58603, 121], [58903, 121], [59204, 119], [59505, 117], [59807, 113], [60109, 109], [60410, 107], [60712, 103], [61016, 93], [61317, 89], [61618, 87], [61919, 85], [62220, 83], [62521, 80], [62822, 78], [63123, 76], [63423, 74], [63724, 72], [64025, 70], [64326, 18], [64359, 35], [64627, 8], [64669, 23], [64928, 1], [64972, 19], [65274, 16], [65577, 12], [65879, 8], [66182, 4], [66484, 1]], "point": [163, 173]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan4", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -3.5, "y": 0.9009999, "z": 1.5}, "object_poses": [{"objectName": "Ladle_f4537974", "position": {"x": -0.5388072, "y": 1.08740556, "z": 3.26635885}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_f4537974", "position": {"x": -0.29224965, "y": 1.08740556, "z": 1.9207176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_130c30e2", "position": {"x": -1.2749939, "y": 0.04900515, "z": 0.5514504}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_130c30e2", "position": {"x": -1.55366719, "y": 0.931120455, "z": 0.359536}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_ab8a6692", "position": {"x": -3.475, "y": 1.1332, "z": 0.2271}, "rotation": {"x": 0.0, "y": 90.00039, "z": 0.0}}, {"objectName": "Pot_ab8a6692", "position": {"x": -1.19964325, "y": 0.9359711, "z": 0.498063982}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9dba57b8", "position": {"x": -2.35645461, "y": 0.0968874544, "z": 0.5459646}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_9dba57b8", "position": {"x": -1.4651612, "y": 0.932685435, "z": 0.290272}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7b1a480e", "position": {"x": -2.01135373, "y": 1.12290716, "z": 0.567671955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ca5fe19d", "position": {"x": -0.5388072, "y": 1.04675734, "z": 2.817812}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ca5fe19d", "position": {"x": -1.19579625, "y": 0.0479601622, "z": 0.381197035}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e6e9017d", "position": {"x": -0.7158578, "y": 1.15733731, "z": 0.47828263}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_e61894bc", "position": {"x": -2.63934517, "y": 0.0911935046, "z": 0.579597354}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_4cc43937", "position": {"x": -1.48333192, "y": 0.0456396937, "z": 0.6084629}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_4cc43937", "position": {"x": -1.601334, "y": 0.0456396937, "z": 0.665083647}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7c6826ef", "position": {"x": -0.785364747, "y": 1.12561512, "z": 3.26635885}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7c6826ef", "position": {"x": -0.785364747, "y": 1.12452459, "z": 3.04208541}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ff353859", "position": {"x": -1.11659849, "y": 0.0444408655, "z": 0.5514504}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1f60da4f", "position": {"x": -0.662086, "y": 1.04878652, "z": 2.817812}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_58fe0a5f", "position": {"x": -0.5388072, "y": 1.04768693, "z": 3.04208541}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_ab8a6692", "position": {"x": -1.55366719, "y": 0.9359711, "z": 0.567328}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_f4537974", "position": {"x": -1.92272818, "y": 1.13970661, "z": 0.377472818}, "rotation": {"x": 350.9303, "y": 45.1053543, "z": 351.7555}}, {"objectName": "Potato_4679e4a5", "position": {"x": -3.22959447, "y": 0.759465456, "z": 2.77829432}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Plate_ca5fe19d", "position": {"x": -3.50640655, "y": 1.28527939, "z": 2.84919739}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_d5b8c410", "position": {"x": -0.908643544, "y": 1.13389707, "z": 2.14499116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7c6826ef", "position": {"x": -0.785364747, "y": 1.12561512, "z": 1.9207176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_0271e4d1", "position": {"x": -1.88567913, "y": 1.1399, "z": 0.473335981}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_447fa38c", "position": {"x": -0.266, "y": 1.038, "z": 2.561}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7b2f7cfb", "position": {"x": -3.79859781, "y": 0.142692849, "z": 1.94883811}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7b1a480e", "position": {"x": -3.50640631, "y": 1.28476727, "z": 3.061901}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_e61894bc", "position": {"x": -0.908643544, "y": 1.04345369, "z": 2.59353828}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e6e9017d", "position": {"x": -0.77180773, "y": 1.15733731, "z": 0.296814859}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bowl_9496ee5e", "position": {"x": -0.495003, "y": 1.038238, "z": 2.223838}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_4cc43937", "position": {"x": -1.48333192, "y": 0.0456396937, "z": 0.4952214}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8393cb4d", "position": {"x": -0.662086, "y": 1.04718959, "z": 1.9207176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_f6bdaee7", "position": {"x": -3.37359357, "y": 0.97924006, "z": 3.20370388}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_1473e57e", "position": {"x": -3.77883434, "y": 1.16548562, "z": 0.473335981}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_130c30e2", "position": {"x": -2.46303129, "y": 0.9025028, "z": 0.5785587}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9dba57b8", "position": {"x": -1.28814924, "y": 0.932685435, "z": 0.290272}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ff353859", "position": {"x": -0.377898723, "y": 1.09474707, "z": 3.19400048}, "rotation": {"x": -1.37450661e-05, "y": 270.000336, "z": 2.46871823e-05}}], "object_toggles": [], "random_seed": 4033077400, "scene_num": 4}, "task_id": "trial_T20190908_050136_778730", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A255A9FFZD8PQW_32N49TQG3JZ1DHRAJ4R5TQZSH2IAVA", "high_descs": ["Turn left and walk forward to the table. ", "Pick up the knife off of the table. ", "Step to the left and face the table. ", "Slice the bread on the table.", "Turn around to the left and go to the counter.", "Open the cupboard and place the knife inside. ", "Turn around to the left and then right to fave t he table again. ", "Pick up the slice of bread. ", "Turn around to the left. Walk forward then go to the microwave.", "Open the microwave and heat the slice of bread. ", "Turn to the left and walk forward to the bin. Turn to the right to face the bin. ", "Place the toast inside the bin. "], "task_desc": "Toast a slice of bread in the microwave and place in bin.", "votes": [1, 1]}, {"assignment_id": "AFU00NU09CFXE_33SA9F9TR0BHIJ8RPJYNBB8X9Q4WEN", "high_descs": ["Cross the room to stand in front of the loaf of bread on the brown table. ", "Pick up the butter knife from behind the head of lettuce.", "Carry the knife a step to the left.", "Slice the right half of the loaf of bread with the knife.", "Turn right and carry the knife to stand a bit in front of the sink.", "Open the cupboard on the left beneath the sink and place the knife inside in front of the mug.", "Turn around and move back to the table to stand by the bread again.", "Pick up a slice of bread from the center of the loaf.", "Turn and carry the slice of bread to the kitchen sink, and then turn your body so you're facing the microwave to the left of the sink.", "Open the microwave, place the bread inside, turn it on, and then remove the bread once heated. ", "Carry the bread to the trash bin, which is to the left of the fridge. ", "Place the bread in the trash bin."], "task_desc": "Throw away a slice of heated bread.", "votes": [1, 1]}, {"assignment_id": "AU34T9OMHN4Z4_3M23Y66PO5OKYNY3ZW5O2YDZMFIS6H", "high_descs": ["Move to the table just ahead to the left of the microwave", "Pick up the knife from under the lettuce on the table", "Move slightly to the left", "Cut the bread into slices", "Turn to the counter on the right", "Open the cabinet, put the knife below the sponge and close the cabinet", "Turn around and go back to the table with the bread", "Pick up a slice of bread", "Move to the microwave to the right of the table", "Open the microwave, put the slice of bread in, close the microwave, turn it on, wait, open the microwave, remove the slice of bread and then close the microwave", "Turn around and go to the trash can to the right of the fridge", "put the slice of bread in the trash"], "task_desc": "Putting a piece of bread in the trash", "votes": [1, 1]}]}}