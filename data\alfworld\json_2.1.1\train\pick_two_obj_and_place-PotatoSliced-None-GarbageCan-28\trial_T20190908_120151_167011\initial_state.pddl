
(define (problem plan_trial_T20190908_120151_167011)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Apple_bar__minus_02_dot_39_bar__plus_00_dot_16_bar__minus_03_dot_54 - object
        Apple_bar__minus_03_dot_20_bar__plus_00_dot_82_bar__minus_03_dot_12 - object
        Blinds_bar__minus_01_dot_37_bar__plus_02_dot_21_bar__minus_03_dot_96 - object
        Bowl_bar__minus_00_dot_21_bar__plus_01_dot_94_bar__minus_00_dot_71 - object
        Bowl_bar__minus_02_dot_56_bar__plus_00_dot_89_bar__minus_00_dot_63 - object
        Bread_bar__minus_04_dot_12_bar__plus_00_dot_77_bar__minus_00_dot_37 - object
        ButterKnife_bar__minus_01_dot_69_bar__plus_00_dot_94_bar__minus_03_dot_67 - object
        ButterKnife_bar__minus_04_dot_04_bar__plus_00_dot_76_bar__minus_03_dot_11 - object
        Cup_bar__minus_00_dot_14_bar__plus_01_dot_50_bar__minus_03_dot_14 - object
        DishSponge_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_02_dot_85 - object
        DishSponge_bar__minus_01_dot_12_bar__plus_00_dot_75_bar__minus_03_dot_51 - object
        Egg_bar__minus_03_dot_12_bar__plus_00_dot_80_bar__minus_03_dot_29 - object
        Faucet_bar__minus_00_dot_39_bar__plus_00_dot_93_bar__minus_03_dot_61 - object
        Fork_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_01_dot_58 - object
        Fork_bar__minus_03_dot_57_bar__plus_00_dot_90_bar__minus_00_dot_46 - object
        Knife_bar__minus_04_dot_06_bar__plus_00_dot_72_bar__minus_00_dot_37 - object
        Lettuce_bar__minus_00_dot_32_bar__plus_01_dot_58_bar__minus_00_dot_88 - object
        LightSwitch_bar__minus_04_dot_37_bar__plus_01_dot_49_bar__minus_01_dot_03 - object
        Mug_bar__minus_00_dot_20_bar__plus_01_dot_93_bar__minus_01_dot_95 - object
        Mug_bar__minus_00_dot_27_bar__plus_01_dot_57_bar__minus_01_dot_94 - object
        Mug_bar__minus_02_dot_51_bar__plus_00_dot_97_bar__minus_03_dot_65 - object
        Pan_bar__minus_03_dot_20_bar__plus_00_dot_76_bar__minus_03_dot_53 - object
        PepperShaker_bar__minus_03_dot_70_bar__plus_00_dot_76_bar__minus_02_dot_97 - object
        Plate_bar__minus_00_dot_36_bar__plus_01_dot_50_bar__minus_00_dot_65 - object
        Plate_bar__minus_00_dot_51_bar__plus_00_dot_09_bar__minus_02_dot_74 - object
        Plate_bar__minus_02_dot_76_bar__plus_00_dot_91_bar__minus_00_dot_38 - object
        Potato_bar__minus_00_dot_33_bar__plus_01_dot_32_bar__minus_00_dot_65 - object
        Potato_bar__minus_00_dot_39_bar__plus_00_dot_97_bar__minus_01_dot_41 - object
        Pot_bar__minus_00_dot_22_bar__plus_00_dot_96_bar__minus_01_dot_85 - object
        Pot_bar__minus_03_dot_36_bar__plus_00_dot_90_bar__minus_00_dot_38 - object
        SaltShaker_bar__minus_00_dot_58_bar__plus_00_dot_08_bar__minus_01_dot_53 - object
        Sink_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_03_dot_39 - object
        SoapBottle_bar__minus_00_dot_86_bar__plus_00_dot_08_bar__minus_03_dot_41 - object
        SoapBottle_bar__minus_01_dot_08_bar__plus_00_dot_08_bar__minus_03_dot_45 - object
        SoapBottle_bar__minus_03_dot_66_bar__plus_00_dot_76_bar__minus_03_dot_63 - object
        Spatula_bar__minus_04_dot_00_bar__plus_00_dot_71_bar__minus_00_dot_31 - object
        Spoon_bar__minus_03_dot_23_bar__plus_00_dot_76_bar__minus_03_dot_06 - object
        StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_01_dot_83 - object
        StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_01_dot_98 - object
        StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_02_dot_13 - object
        StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_02_dot_29 - object
        Tomato_bar__minus_00_dot_29_bar__plus_01_dot_34_bar__minus_00_dot_99 - object
        Window_bar__minus_01_dot_39_bar__plus_01_dot_61_bar__minus_04_dot_11 - object
        Cabinet_bar__minus_00_dot_33_bar__plus_01_dot_89_bar__minus_02_dot_51 - receptacle
        Cabinet_bar__minus_00_dot_34_bar__plus_01_dot_89_bar__minus_01_dot_29 - receptacle
        Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_00_dot_39 - receptacle
        Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_27 - receptacle
        Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_63 - receptacle
        Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_02_dot_50 - receptacle
        Cabinet_bar__minus_00_dot_35_bar__plus_01_dot_89_bar__minus_03_dot_29 - receptacle
        Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_01_dot_61 - receptacle
        Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_02_dot_51 - receptacle
        Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_03_dot_01 - receptacle
        Cabinet_bar__minus_01_dot_01_bar__plus_00_dot_39_bar__minus_03_dot_37 - receptacle
        CoffeeMachine_bar__minus_02_dot_51_bar__plus_00_dot_93_bar__minus_03_dot_80 - receptacle
        CounterTop_bar__minus_00_dot_33_bar__plus_00_dot_98_bar__minus_01_dot_45 - receptacle
        CounterTop_bar__minus_01_dot_94_bar__plus_00_dot_98_bar__minus_03_dot_67 - receptacle
        DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44 - receptacle
        DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32 - receptacle
        Drawer_bar__minus_00_dot_48_bar__plus_00_dot_78_bar__minus_02_dot_74 - receptacle
        Drawer_bar__minus_00_dot_50_bar__plus_00_dot_78_bar__minus_01_dot_45 - receptacle
        Drawer_bar__minus_01_dot_15_bar__plus_00_dot_78_bar__minus_03_dot_50 - receptacle
        Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65 - receptacle
        GarbageCan_bar__minus_02_dot_42_bar__plus_00_dot_00_bar__minus_03_dot_54 - receptacle
        Microwave_bar__minus_00_dot_22_bar__plus_01_dot_47_bar__minus_02_dot_06 - receptacle
        SideTable_bar__minus_04_dot_07_bar__plus_00_dot_73_bar__minus_00_dot_31 - receptacle
        Sink_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_03_dot_39_bar_SinkBasin - receptacle
        StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_01_dot_85 - receptacle
        StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_02_dot_24 - receptacle
        StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_01_dot_85 - receptacle
        StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_02_dot_24 - receptacle
        Toaster_bar__minus_01_dot_97_bar__plus_00_dot_93_bar__minus_03_dot_76 - receptacle
        loc_bar__minus_6_bar__minus_12_bar_2_bar_0 - location
        loc_bar__minus_6_bar__minus_4_bar_1_bar__minus_15 - location
        loc_bar__minus_8_bar__minus_11_bar_1_bar_30 - location
        loc_bar__minus_4_bar__minus_6_bar_1_bar_45 - location
        loc_bar__minus_6_bar__minus_12_bar_2_bar_45 - location
        loc_bar__minus_5_bar__minus_3_bar_1_bar_60 - location
        loc_bar__minus_6_bar__minus_12_bar_2_bar__minus_30 - location
        loc_bar__minus_7_bar__minus_10_bar_2_bar_60 - location
        loc_bar__minus_4_bar__minus_12_bar_2_bar_60 - location
        loc_bar__minus_4_bar__minus_8_bar_1_bar_15 - location
        loc_bar__minus_12_bar__minus_5_bar_0_bar_60 - location
        loc_bar__minus_4_bar__minus_7_bar_1_bar_45 - location
        loc_bar__minus_6_bar__minus_9_bar_1_bar_60 - location
        loc_bar__minus_5_bar__minus_10_bar_1_bar_45 - location
        loc_bar__minus_14_bar__minus_9_bar_2_bar_45 - location
        loc_bar__minus_4_bar__minus_6_bar_1_bar__minus_15 - location
        loc_bar__minus_6_bar__minus_6_bar_1_bar__minus_15 - location
        loc_bar__minus_4_bar__minus_9_bar_1_bar_45 - location
        loc_bar__minus_4_bar__minus_7_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_10_bar_1_bar__minus_30 - location
        loc_bar__minus_15_bar__minus_4_bar_3_bar_15 - location
        loc_bar__minus_8_bar__minus_12_bar_2_bar_45 - location
        loc_bar__minus_10_bar__minus_12_bar_2_bar_45 - location
        loc_bar__minus_7_bar__minus_11_bar_1_bar_45 - location
        loc_bar__minus_4_bar__minus_8_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_9_bar_2_bar__minus_15 - location
        loc_bar__minus_4_bar__minus_7_bar_1_bar__minus_30 - location
        loc_bar__minus_15_bar__minus_5_bar_0_bar_45 - location
        loc_bar__minus_4_bar__minus_12_bar_1_bar_60 - location
        loc_bar__minus_10_bar__minus_12_bar_2_bar_60 - location
        loc_bar__minus_4_bar__minus_12_bar_1_bar__minus_15 - location
        loc_bar__minus_4_bar__minus_6_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_9_bar_1_bar_60 - location
        loc_bar__minus_6_bar__minus_6_bar_1_bar_60 - location
        loc_bar__minus_7_bar__minus_9_bar_0_bar_30 - location
        )
    

(:init


        (receptacleType CounterTop_bar__minus_01_dot_94_bar__plus_00_dot_98_bar__minus_03_dot_67 CounterTopType)
        (receptacleType SideTable_bar__minus_04_dot_07_bar__plus_00_dot_73_bar__minus_00_dot_31 SideTableType)
        (receptacleType Cabinet_bar__minus_00_dot_35_bar__plus_01_dot_89_bar__minus_03_dot_29 CabinetType)
        (receptacleType Sink_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_03_dot_39_bar_SinkBasin SinkBasinType)
        (receptacleType DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44 DiningTableType)
        (receptacleType Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_00_dot_39 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_33_bar__plus_01_dot_89_bar__minus_02_dot_51 CabinetType)
        (receptacleType CoffeeMachine_bar__minus_02_dot_51_bar__plus_00_dot_93_bar__minus_03_dot_80 CoffeeMachineType)
        (receptacleType Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_02_dot_50 CabinetType)
        (receptacleType CounterTop_bar__minus_00_dot_33_bar__plus_00_dot_98_bar__minus_01_dot_45 CounterTopType)
        (receptacleType Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_27 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_01_dot_61 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_03_dot_01 CabinetType)
        (receptacleType StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_01_dot_85 StoveBurnerType)
        (receptacleType Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_02_dot_51 CabinetType)
        (receptacleType Drawer_bar__minus_01_dot_15_bar__plus_00_dot_78_bar__minus_03_dot_50 DrawerType)
        (receptacleType StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_02_dot_24 StoveBurnerType)
        (receptacleType Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65 FridgeType)
        (receptacleType Drawer_bar__minus_00_dot_50_bar__plus_00_dot_78_bar__minus_01_dot_45 DrawerType)
        (receptacleType Microwave_bar__minus_00_dot_22_bar__plus_01_dot_47_bar__minus_02_dot_06 MicrowaveType)
        (receptacleType Drawer_bar__minus_00_dot_48_bar__plus_00_dot_78_bar__minus_02_dot_74 DrawerType)
        (receptacleType Cabinet_bar__minus_00_dot_34_bar__plus_01_dot_89_bar__minus_01_dot_29 CabinetType)
        (receptacleType Cabinet_bar__minus_01_dot_01_bar__plus_00_dot_39_bar__minus_03_dot_37 CabinetType)
        (receptacleType StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_01_dot_85 StoveBurnerType)
        (receptacleType GarbageCan_bar__minus_02_dot_42_bar__plus_00_dot_00_bar__minus_03_dot_54 GarbageCanType)
        (receptacleType Toaster_bar__minus_01_dot_97_bar__plus_00_dot_93_bar__minus_03_dot_76 ToasterType)
        (receptacleType StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_02_dot_24 StoveBurnerType)
        (receptacleType Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_63 CabinetType)
        (receptacleType DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32 DiningTableType)
        (objectType Spoon_bar__minus_03_dot_23_bar__plus_00_dot_76_bar__minus_03_dot_06 SpoonType)
        (objectType Apple_bar__minus_02_dot_39_bar__plus_00_dot_16_bar__minus_03_dot_54 AppleType)
        (objectType SoapBottle_bar__minus_00_dot_86_bar__plus_00_dot_08_bar__minus_03_dot_41 SoapBottleType)
        (objectType StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_02_dot_29 StoveKnobType)
        (objectType StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_01_dot_98 StoveKnobType)
        (objectType Bread_bar__minus_04_dot_12_bar__plus_00_dot_77_bar__minus_00_dot_37 BreadType)
        (objectType SoapBottle_bar__minus_03_dot_66_bar__plus_00_dot_76_bar__minus_03_dot_63 SoapBottleType)
        (objectType Egg_bar__minus_03_dot_12_bar__plus_00_dot_80_bar__minus_03_dot_29 EggType)
        (objectType Lettuce_bar__minus_00_dot_32_bar__plus_01_dot_58_bar__minus_00_dot_88 LettuceType)
        (objectType StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_01_dot_83 StoveKnobType)
        (objectType ButterKnife_bar__minus_04_dot_04_bar__plus_00_dot_76_bar__minus_03_dot_11 ButterKnifeType)
        (objectType Potato_bar__minus_00_dot_33_bar__plus_01_dot_32_bar__minus_00_dot_65 PotatoType)
        (objectType Tomato_bar__minus_00_dot_29_bar__plus_01_dot_34_bar__minus_00_dot_99 TomatoType)
        (objectType Plate_bar__minus_02_dot_76_bar__plus_00_dot_91_bar__minus_00_dot_38 PlateType)
        (objectType Bowl_bar__minus_00_dot_21_bar__plus_01_dot_94_bar__minus_00_dot_71 BowlType)
        (objectType Bowl_bar__minus_02_dot_56_bar__plus_00_dot_89_bar__minus_00_dot_63 BowlType)
        (objectType Pot_bar__minus_03_dot_36_bar__plus_00_dot_90_bar__minus_00_dot_38 PotType)
        (objectType Fork_bar__minus_03_dot_57_bar__plus_00_dot_90_bar__minus_00_dot_46 ForkType)
        (objectType Knife_bar__minus_04_dot_06_bar__plus_00_dot_72_bar__minus_00_dot_37 KnifeType)
        (objectType DishSponge_bar__minus_01_dot_12_bar__plus_00_dot_75_bar__minus_03_dot_51 DishSpongeType)
        (objectType Plate_bar__minus_00_dot_51_bar__plus_00_dot_09_bar__minus_02_dot_74 PlateType)
        (objectType LightSwitch_bar__minus_04_dot_37_bar__plus_01_dot_49_bar__minus_01_dot_03 LightSwitchType)
        (objectType ButterKnife_bar__minus_01_dot_69_bar__plus_00_dot_94_bar__minus_03_dot_67 ButterKnifeType)
        (objectType Potato_bar__minus_00_dot_39_bar__plus_00_dot_97_bar__minus_01_dot_41 PotatoType)
        (objectType Apple_bar__minus_03_dot_20_bar__plus_00_dot_82_bar__minus_03_dot_12 AppleType)
        (objectType Fork_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_01_dot_58 ForkType)
        (objectType Pot_bar__minus_00_dot_22_bar__plus_00_dot_96_bar__minus_01_dot_85 PotType)
        (objectType Window_bar__minus_01_dot_39_bar__plus_01_dot_61_bar__minus_04_dot_11 WindowType)
        (objectType Mug_bar__minus_00_dot_20_bar__plus_01_dot_93_bar__minus_01_dot_95 MugType)
        (objectType Cup_bar__minus_00_dot_14_bar__plus_01_dot_50_bar__minus_03_dot_14 CupType)
        (objectType DishSponge_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_02_dot_85 DishSpongeType)
        (objectType Plate_bar__minus_00_dot_36_bar__plus_01_dot_50_bar__minus_00_dot_65 PlateType)
        (objectType SaltShaker_bar__minus_00_dot_58_bar__plus_00_dot_08_bar__minus_01_dot_53 SaltShakerType)
        (objectType Mug_bar__minus_00_dot_27_bar__plus_01_dot_57_bar__minus_01_dot_94 MugType)
        (objectType Sink_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_03_dot_39 SinkType)
        (objectType Pan_bar__minus_03_dot_20_bar__plus_00_dot_76_bar__minus_03_dot_53 PanType)
        (objectType PepperShaker_bar__minus_03_dot_70_bar__plus_00_dot_76_bar__minus_02_dot_97 PepperShakerType)
        (objectType Blinds_bar__minus_01_dot_37_bar__plus_02_dot_21_bar__minus_03_dot_96 BlindsType)
        (objectType Mug_bar__minus_02_dot_51_bar__plus_00_dot_97_bar__minus_03_dot_65 MugType)
        (objectType Spatula_bar__minus_04_dot_00_bar__plus_00_dot_71_bar__minus_00_dot_31 SpatulaType)
        (objectType StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_02_dot_13 StoveKnobType)
        (objectType SoapBottle_bar__minus_01_dot_08_bar__plus_00_dot_08_bar__minus_03_dot_45 SoapBottleType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain SideTableType SaltShakerType)
        (canContain SideTableType BreadType)
        (canContain SideTableType DishSpongeType)
        (canContain SideTableType BowlType)
        (canContain SideTableType PotType)
        (canContain SideTableType MugType)
        (canContain SideTableType EggType)
        (canContain SideTableType ForkType)
        (canContain SideTableType SpoonType)
        (canContain SideTableType SoapBottleType)
        (canContain SideTableType LettuceType)
        (canContain SideTableType PotatoType)
        (canContain SideTableType ButterKnifeType)
        (canContain SideTableType CupType)
        (canContain SideTableType PlateType)
        (canContain SideTableType PepperShakerType)
        (canContain SideTableType TomatoType)
        (canContain SideTableType KnifeType)
        (canContain SideTableType AppleType)
        (canContain SideTableType PanType)
        (canContain SideTableType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain SinkBasinType DishSpongeType)
        (canContain SinkBasinType BowlType)
        (canContain SinkBasinType PotType)
        (canContain SinkBasinType MugType)
        (canContain SinkBasinType EggType)
        (canContain SinkBasinType ForkType)
        (canContain SinkBasinType SpoonType)
        (canContain SinkBasinType LettuceType)
        (canContain SinkBasinType PotatoType)
        (canContain SinkBasinType ButterKnifeType)
        (canContain SinkBasinType CupType)
        (canContain SinkBasinType PlateType)
        (canContain SinkBasinType TomatoType)
        (canContain SinkBasinType KnifeType)
        (canContain SinkBasinType AppleType)
        (canContain SinkBasinType PanType)
        (canContain SinkBasinType SpatulaType)
        (canContain DiningTableType SaltShakerType)
        (canContain DiningTableType BreadType)
        (canContain DiningTableType DishSpongeType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType PotType)
        (canContain DiningTableType MugType)
        (canContain DiningTableType EggType)
        (canContain DiningTableType ForkType)
        (canContain DiningTableType SpoonType)
        (canContain DiningTableType SoapBottleType)
        (canContain DiningTableType LettuceType)
        (canContain DiningTableType PotatoType)
        (canContain DiningTableType ButterKnifeType)
        (canContain DiningTableType CupType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType PepperShakerType)
        (canContain DiningTableType TomatoType)
        (canContain DiningTableType KnifeType)
        (canContain DiningTableType AppleType)
        (canContain DiningTableType PanType)
        (canContain DiningTableType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CoffeeMachineType MugType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain FridgeType BreadType)
        (canContain FridgeType BowlType)
        (canContain FridgeType PotType)
        (canContain FridgeType MugType)
        (canContain FridgeType EggType)
        (canContain FridgeType LettuceType)
        (canContain FridgeType PotatoType)
        (canContain FridgeType CupType)
        (canContain FridgeType PlateType)
        (canContain FridgeType TomatoType)
        (canContain FridgeType AppleType)
        (canContain FridgeType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain MicrowaveType MugType)
        (canContain MicrowaveType PotatoType)
        (canContain MicrowaveType EggType)
        (canContain MicrowaveType AppleType)
        (canContain MicrowaveType TomatoType)
        (canContain MicrowaveType BreadType)
        (canContain MicrowaveType CupType)
        (canContain MicrowaveType PlateType)
        (canContain MicrowaveType BowlType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain GarbageCanType BreadType)
        (canContain GarbageCanType DishSpongeType)
        (canContain GarbageCanType EggType)
        (canContain GarbageCanType SoapBottleType)
        (canContain GarbageCanType LettuceType)
        (canContain GarbageCanType PotatoType)
        (canContain GarbageCanType TomatoType)
        (canContain GarbageCanType AppleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DiningTableType SaltShakerType)
        (canContain DiningTableType BreadType)
        (canContain DiningTableType DishSpongeType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType PotType)
        (canContain DiningTableType MugType)
        (canContain DiningTableType EggType)
        (canContain DiningTableType ForkType)
        (canContain DiningTableType SpoonType)
        (canContain DiningTableType SoapBottleType)
        (canContain DiningTableType LettuceType)
        (canContain DiningTableType PotatoType)
        (canContain DiningTableType ButterKnifeType)
        (canContain DiningTableType CupType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType PepperShakerType)
        (canContain DiningTableType TomatoType)
        (canContain DiningTableType KnifeType)
        (canContain DiningTableType AppleType)
        (canContain DiningTableType PanType)
        (canContain DiningTableType SpatulaType)
        (pickupable Spoon_bar__minus_03_dot_23_bar__plus_00_dot_76_bar__minus_03_dot_06)
        (pickupable Apple_bar__minus_02_dot_39_bar__plus_00_dot_16_bar__minus_03_dot_54)
        (pickupable SoapBottle_bar__minus_00_dot_86_bar__plus_00_dot_08_bar__minus_03_dot_41)
        (pickupable Bread_bar__minus_04_dot_12_bar__plus_00_dot_77_bar__minus_00_dot_37)
        (pickupable SoapBottle_bar__minus_03_dot_66_bar__plus_00_dot_76_bar__minus_03_dot_63)
        (pickupable Egg_bar__minus_03_dot_12_bar__plus_00_dot_80_bar__minus_03_dot_29)
        (pickupable Lettuce_bar__minus_00_dot_32_bar__plus_01_dot_58_bar__minus_00_dot_88)
        (pickupable ButterKnife_bar__minus_04_dot_04_bar__plus_00_dot_76_bar__minus_03_dot_11)
        (pickupable Potato_bar__minus_00_dot_33_bar__plus_01_dot_32_bar__minus_00_dot_65)
        (pickupable Tomato_bar__minus_00_dot_29_bar__plus_01_dot_34_bar__minus_00_dot_99)
        (pickupable Plate_bar__minus_02_dot_76_bar__plus_00_dot_91_bar__minus_00_dot_38)
        (pickupable Bowl_bar__minus_00_dot_21_bar__plus_01_dot_94_bar__minus_00_dot_71)
        (pickupable Bowl_bar__minus_02_dot_56_bar__plus_00_dot_89_bar__minus_00_dot_63)
        (pickupable Pot_bar__minus_03_dot_36_bar__plus_00_dot_90_bar__minus_00_dot_38)
        (pickupable Fork_bar__minus_03_dot_57_bar__plus_00_dot_90_bar__minus_00_dot_46)
        (pickupable Knife_bar__minus_04_dot_06_bar__plus_00_dot_72_bar__minus_00_dot_37)
        (pickupable DishSponge_bar__minus_01_dot_12_bar__plus_00_dot_75_bar__minus_03_dot_51)
        (pickupable Plate_bar__minus_00_dot_51_bar__plus_00_dot_09_bar__minus_02_dot_74)
        (pickupable ButterKnife_bar__minus_01_dot_69_bar__plus_00_dot_94_bar__minus_03_dot_67)
        (pickupable Potato_bar__minus_00_dot_39_bar__plus_00_dot_97_bar__minus_01_dot_41)
        (pickupable Apple_bar__minus_03_dot_20_bar__plus_00_dot_82_bar__minus_03_dot_12)
        (pickupable Fork_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_01_dot_58)
        (pickupable Pot_bar__minus_00_dot_22_bar__plus_00_dot_96_bar__minus_01_dot_85)
        (pickupable Mug_bar__minus_00_dot_20_bar__plus_01_dot_93_bar__minus_01_dot_95)
        (pickupable Cup_bar__minus_00_dot_14_bar__plus_01_dot_50_bar__minus_03_dot_14)
        (pickupable DishSponge_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_02_dot_85)
        (pickupable Plate_bar__minus_00_dot_36_bar__plus_01_dot_50_bar__minus_00_dot_65)
        (pickupable SaltShaker_bar__minus_00_dot_58_bar__plus_00_dot_08_bar__minus_01_dot_53)
        (pickupable Mug_bar__minus_00_dot_27_bar__plus_01_dot_57_bar__minus_01_dot_94)
        (pickupable Pan_bar__minus_03_dot_20_bar__plus_00_dot_76_bar__minus_03_dot_53)
        (pickupable PepperShaker_bar__minus_03_dot_70_bar__plus_00_dot_76_bar__minus_02_dot_97)
        (pickupable Mug_bar__minus_02_dot_51_bar__plus_00_dot_97_bar__minus_03_dot_65)
        (pickupable Spatula_bar__minus_04_dot_00_bar__plus_00_dot_71_bar__minus_00_dot_31)
        (pickupable SoapBottle_bar__minus_01_dot_08_bar__plus_00_dot_08_bar__minus_03_dot_45)
        (isReceptacleObject Plate_bar__minus_02_dot_76_bar__plus_00_dot_91_bar__minus_00_dot_38)
        (isReceptacleObject Bowl_bar__minus_00_dot_21_bar__plus_01_dot_94_bar__minus_00_dot_71)
        (isReceptacleObject Bowl_bar__minus_02_dot_56_bar__plus_00_dot_89_bar__minus_00_dot_63)
        (isReceptacleObject Pot_bar__minus_03_dot_36_bar__plus_00_dot_90_bar__minus_00_dot_38)
        (isReceptacleObject Plate_bar__minus_00_dot_51_bar__plus_00_dot_09_bar__minus_02_dot_74)
        (isReceptacleObject Pot_bar__minus_00_dot_22_bar__plus_00_dot_96_bar__minus_01_dot_85)
        (isReceptacleObject Mug_bar__minus_00_dot_20_bar__plus_01_dot_93_bar__minus_01_dot_95)
        (isReceptacleObject Cup_bar__minus_00_dot_14_bar__plus_01_dot_50_bar__minus_03_dot_14)
        (isReceptacleObject Plate_bar__minus_00_dot_36_bar__plus_01_dot_50_bar__minus_00_dot_65)
        (isReceptacleObject Mug_bar__minus_00_dot_27_bar__plus_01_dot_57_bar__minus_01_dot_94)
        (isReceptacleObject Pan_bar__minus_03_dot_20_bar__plus_00_dot_76_bar__minus_03_dot_53)
        (isReceptacleObject Mug_bar__minus_02_dot_51_bar__plus_00_dot_97_bar__minus_03_dot_65)
        (openable Cabinet_bar__minus_00_dot_35_bar__plus_01_dot_89_bar__minus_03_dot_29)
        (openable Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_00_dot_39)
        (openable Cabinet_bar__minus_00_dot_33_bar__plus_01_dot_89_bar__minus_02_dot_51)
        (openable Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_27)
        (openable Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_01_dot_61)
        (openable Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_03_dot_01)
        (openable Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_02_dot_51)
        (openable Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65)
        (openable Microwave_bar__minus_00_dot_22_bar__plus_01_dot_47_bar__minus_02_dot_06)
        (openable Drawer_bar__minus_00_dot_48_bar__plus_00_dot_78_bar__minus_02_dot_74)
        (openable Cabinet_bar__minus_00_dot_34_bar__plus_01_dot_89_bar__minus_01_dot_29)
        (openable Cabinet_bar__minus_01_dot_01_bar__plus_00_dot_39_bar__minus_03_dot_37)
        
        (atLocation agent1 loc_bar__minus_7_bar__minus_9_bar_0_bar_30)
        
        (cleanable Spoon_bar__minus_03_dot_23_bar__plus_00_dot_76_bar__minus_03_dot_06)
        (cleanable Apple_bar__minus_02_dot_39_bar__plus_00_dot_16_bar__minus_03_dot_54)
        (cleanable Egg_bar__minus_03_dot_12_bar__plus_00_dot_80_bar__minus_03_dot_29)
        (cleanable Lettuce_bar__minus_00_dot_32_bar__plus_01_dot_58_bar__minus_00_dot_88)
        (cleanable ButterKnife_bar__minus_04_dot_04_bar__plus_00_dot_76_bar__minus_03_dot_11)
        (cleanable Potato_bar__minus_00_dot_33_bar__plus_01_dot_32_bar__minus_00_dot_65)
        (cleanable Tomato_bar__minus_00_dot_29_bar__plus_01_dot_34_bar__minus_00_dot_99)
        (cleanable Plate_bar__minus_02_dot_76_bar__plus_00_dot_91_bar__minus_00_dot_38)
        (cleanable Bowl_bar__minus_00_dot_21_bar__plus_01_dot_94_bar__minus_00_dot_71)
        (cleanable Bowl_bar__minus_02_dot_56_bar__plus_00_dot_89_bar__minus_00_dot_63)
        (cleanable Pot_bar__minus_03_dot_36_bar__plus_00_dot_90_bar__minus_00_dot_38)
        (cleanable Fork_bar__minus_03_dot_57_bar__plus_00_dot_90_bar__minus_00_dot_46)
        (cleanable Knife_bar__minus_04_dot_06_bar__plus_00_dot_72_bar__minus_00_dot_37)
        (cleanable DishSponge_bar__minus_01_dot_12_bar__plus_00_dot_75_bar__minus_03_dot_51)
        (cleanable Plate_bar__minus_00_dot_51_bar__plus_00_dot_09_bar__minus_02_dot_74)
        (cleanable ButterKnife_bar__minus_01_dot_69_bar__plus_00_dot_94_bar__minus_03_dot_67)
        (cleanable Potato_bar__minus_00_dot_39_bar__plus_00_dot_97_bar__minus_01_dot_41)
        (cleanable Apple_bar__minus_03_dot_20_bar__plus_00_dot_82_bar__minus_03_dot_12)
        (cleanable Fork_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_01_dot_58)
        (cleanable Pot_bar__minus_00_dot_22_bar__plus_00_dot_96_bar__minus_01_dot_85)
        (cleanable Mug_bar__minus_00_dot_20_bar__plus_01_dot_93_bar__minus_01_dot_95)
        (cleanable Cup_bar__minus_00_dot_14_bar__plus_01_dot_50_bar__minus_03_dot_14)
        (cleanable DishSponge_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_02_dot_85)
        (cleanable Plate_bar__minus_00_dot_36_bar__plus_01_dot_50_bar__minus_00_dot_65)
        (cleanable Mug_bar__minus_00_dot_27_bar__plus_01_dot_57_bar__minus_01_dot_94)
        (cleanable Pan_bar__minus_03_dot_20_bar__plus_00_dot_76_bar__minus_03_dot_53)
        (cleanable Mug_bar__minus_02_dot_51_bar__plus_00_dot_97_bar__minus_03_dot_65)
        (cleanable Spatula_bar__minus_04_dot_00_bar__plus_00_dot_71_bar__minus_00_dot_31)
        
        (heatable Apple_bar__minus_02_dot_39_bar__plus_00_dot_16_bar__minus_03_dot_54)
        (heatable Bread_bar__minus_04_dot_12_bar__plus_00_dot_77_bar__minus_00_dot_37)
        (heatable Egg_bar__minus_03_dot_12_bar__plus_00_dot_80_bar__minus_03_dot_29)
        (heatable Potato_bar__minus_00_dot_33_bar__plus_01_dot_32_bar__minus_00_dot_65)
        (heatable Tomato_bar__minus_00_dot_29_bar__plus_01_dot_34_bar__minus_00_dot_99)
        (heatable Plate_bar__minus_02_dot_76_bar__plus_00_dot_91_bar__minus_00_dot_38)
        (heatable Plate_bar__minus_00_dot_51_bar__plus_00_dot_09_bar__minus_02_dot_74)
        (heatable Potato_bar__minus_00_dot_39_bar__plus_00_dot_97_bar__minus_01_dot_41)
        (heatable Apple_bar__minus_03_dot_20_bar__plus_00_dot_82_bar__minus_03_dot_12)
        (heatable Mug_bar__minus_00_dot_20_bar__plus_01_dot_93_bar__minus_01_dot_95)
        (heatable Cup_bar__minus_00_dot_14_bar__plus_01_dot_50_bar__minus_03_dot_14)
        (heatable Plate_bar__minus_00_dot_36_bar__plus_01_dot_50_bar__minus_00_dot_65)
        (heatable Mug_bar__minus_00_dot_27_bar__plus_01_dot_57_bar__minus_01_dot_94)
        (heatable Mug_bar__minus_02_dot_51_bar__plus_00_dot_97_bar__minus_03_dot_65)
        (coolable Apple_bar__minus_02_dot_39_bar__plus_00_dot_16_bar__minus_03_dot_54)
        (coolable Bread_bar__minus_04_dot_12_bar__plus_00_dot_77_bar__minus_00_dot_37)
        (coolable Egg_bar__minus_03_dot_12_bar__plus_00_dot_80_bar__minus_03_dot_29)
        (coolable Lettuce_bar__minus_00_dot_32_bar__plus_01_dot_58_bar__minus_00_dot_88)
        (coolable Potato_bar__minus_00_dot_33_bar__plus_01_dot_32_bar__minus_00_dot_65)
        (coolable Tomato_bar__minus_00_dot_29_bar__plus_01_dot_34_bar__minus_00_dot_99)
        (coolable Plate_bar__minus_02_dot_76_bar__plus_00_dot_91_bar__minus_00_dot_38)
        (coolable Bowl_bar__minus_00_dot_21_bar__plus_01_dot_94_bar__minus_00_dot_71)
        (coolable Bowl_bar__minus_02_dot_56_bar__plus_00_dot_89_bar__minus_00_dot_63)
        (coolable Pot_bar__minus_03_dot_36_bar__plus_00_dot_90_bar__minus_00_dot_38)
        (coolable Plate_bar__minus_00_dot_51_bar__plus_00_dot_09_bar__minus_02_dot_74)
        (coolable Potato_bar__minus_00_dot_39_bar__plus_00_dot_97_bar__minus_01_dot_41)
        (coolable Apple_bar__minus_03_dot_20_bar__plus_00_dot_82_bar__minus_03_dot_12)
        (coolable Pot_bar__minus_00_dot_22_bar__plus_00_dot_96_bar__minus_01_dot_85)
        (coolable Mug_bar__minus_00_dot_20_bar__plus_01_dot_93_bar__minus_01_dot_95)
        (coolable Cup_bar__minus_00_dot_14_bar__plus_01_dot_50_bar__minus_03_dot_14)
        (coolable Plate_bar__minus_00_dot_36_bar__plus_01_dot_50_bar__minus_00_dot_65)
        (coolable Mug_bar__minus_00_dot_27_bar__plus_01_dot_57_bar__minus_01_dot_94)
        (coolable Pan_bar__minus_03_dot_20_bar__plus_00_dot_76_bar__minus_03_dot_53)
        (coolable Mug_bar__minus_02_dot_51_bar__plus_00_dot_97_bar__minus_03_dot_65)
        
        (isCool Potato_bar__minus_00_dot_33_bar__plus_01_dot_32_bar__minus_00_dot_65)
        
        
        
        (sliceable Apple_bar__minus_02_dot_39_bar__plus_00_dot_16_bar__minus_03_dot_54)
        (sliceable Bread_bar__minus_04_dot_12_bar__plus_00_dot_77_bar__minus_00_dot_37)
        (sliceable Egg_bar__minus_03_dot_12_bar__plus_00_dot_80_bar__minus_03_dot_29)
        (sliceable Lettuce_bar__minus_00_dot_32_bar__plus_01_dot_58_bar__minus_00_dot_88)
        (sliceable Potato_bar__minus_00_dot_33_bar__plus_01_dot_32_bar__minus_00_dot_65)
        (sliceable Tomato_bar__minus_00_dot_29_bar__plus_01_dot_34_bar__minus_00_dot_99)
        (sliceable Potato_bar__minus_00_dot_39_bar__plus_00_dot_97_bar__minus_01_dot_41)
        (sliceable Apple_bar__minus_03_dot_20_bar__plus_00_dot_82_bar__minus_03_dot_12)
        
        (inReceptacle ButterKnife_bar__minus_01_dot_69_bar__plus_00_dot_94_bar__minus_03_dot_67 CounterTop_bar__minus_01_dot_94_bar__plus_00_dot_98_bar__minus_03_dot_67)
        (inReceptacle Mug_bar__minus_02_dot_51_bar__plus_00_dot_97_bar__minus_03_dot_65 CounterTop_bar__minus_01_dot_94_bar__plus_00_dot_98_bar__minus_03_dot_67)
        (inReceptacle DishSponge_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_02_dot_85 CounterTop_bar__minus_01_dot_94_bar__plus_00_dot_98_bar__minus_03_dot_67)
        (inReceptacle Bowl_bar__minus_02_dot_56_bar__plus_00_dot_89_bar__minus_00_dot_63 DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44)
        (inReceptacle Pot_bar__minus_03_dot_36_bar__plus_00_dot_90_bar__minus_00_dot_38 DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44)
        (inReceptacle Plate_bar__minus_02_dot_76_bar__plus_00_dot_91_bar__minus_00_dot_38 DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44)
        (inReceptacle Fork_bar__minus_03_dot_57_bar__plus_00_dot_90_bar__minus_00_dot_46 DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44)
        (inReceptacle Spoon_bar__minus_03_dot_23_bar__plus_00_dot_76_bar__minus_03_dot_06 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle Pan_bar__minus_03_dot_20_bar__plus_00_dot_76_bar__minus_03_dot_53 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle PepperShaker_bar__minus_03_dot_70_bar__plus_00_dot_76_bar__minus_02_dot_97 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle Apple_bar__minus_03_dot_20_bar__plus_00_dot_82_bar__minus_03_dot_12 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle SoapBottle_bar__minus_03_dot_66_bar__plus_00_dot_76_bar__minus_03_dot_63 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle Egg_bar__minus_03_dot_12_bar__plus_00_dot_80_bar__minus_03_dot_29 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle ButterKnife_bar__minus_04_dot_04_bar__plus_00_dot_76_bar__minus_03_dot_11 DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32)
        (inReceptacle Pot_bar__minus_00_dot_22_bar__plus_00_dot_96_bar__minus_01_dot_85 StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_01_dot_85)
        (inReceptacle Pot_bar__minus_00_dot_22_bar__plus_00_dot_96_bar__minus_01_dot_85 StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_01_dot_85)
        (inReceptacle DishSponge_bar__minus_01_dot_12_bar__plus_00_dot_75_bar__minus_03_dot_51 Drawer_bar__minus_01_dot_15_bar__plus_00_dot_78_bar__minus_03_dot_50)
        (inReceptacle Bowl_bar__minus_00_dot_21_bar__plus_01_dot_94_bar__minus_00_dot_71 Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_00_dot_39)
        (inReceptacle Mug_bar__minus_00_dot_20_bar__plus_01_dot_93_bar__minus_01_dot_95 Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_63)
        (inReceptacle Plate_bar__minus_00_dot_51_bar__plus_00_dot_09_bar__minus_02_dot_74 Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_02_dot_51)
        (inReceptacle SoapBottle_bar__minus_00_dot_86_bar__plus_00_dot_08_bar__minus_03_dot_41 Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_03_dot_01)
        (inReceptacle SoapBottle_bar__minus_01_dot_08_bar__plus_00_dot_08_bar__minus_03_dot_45 Cabinet_bar__minus_01_dot_01_bar__plus_00_dot_39_bar__minus_03_dot_37)
        (inReceptacle SaltShaker_bar__minus_00_dot_58_bar__plus_00_dot_08_bar__minus_01_dot_53 Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_01_dot_61)
        (inReceptacle Cup_bar__minus_00_dot_14_bar__plus_01_dot_50_bar__minus_03_dot_14 Cabinet_bar__minus_00_dot_33_bar__plus_01_dot_89_bar__minus_02_dot_51)
        (inReceptacle Spatula_bar__minus_04_dot_00_bar__plus_00_dot_71_bar__minus_00_dot_31 SideTable_bar__minus_04_dot_07_bar__plus_00_dot_73_bar__minus_00_dot_31)
        (inReceptacle Bread_bar__minus_04_dot_12_bar__plus_00_dot_77_bar__minus_00_dot_37 SideTable_bar__minus_04_dot_07_bar__plus_00_dot_73_bar__minus_00_dot_31)
        (inReceptacle Knife_bar__minus_04_dot_06_bar__plus_00_dot_72_bar__minus_00_dot_37 SideTable_bar__minus_04_dot_07_bar__plus_00_dot_73_bar__minus_00_dot_31)
        (inReceptacle Fork_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_01_dot_58 CounterTop_bar__minus_00_dot_33_bar__plus_00_dot_98_bar__minus_01_dot_45)
        (inReceptacle Potato_bar__minus_00_dot_39_bar__plus_00_dot_97_bar__minus_01_dot_41 CounterTop_bar__minus_00_dot_33_bar__plus_00_dot_98_bar__minus_01_dot_45)
        (inReceptacle Apple_bar__minus_02_dot_39_bar__plus_00_dot_16_bar__minus_03_dot_54 GarbageCan_bar__minus_02_dot_42_bar__plus_00_dot_00_bar__minus_03_dot_54)
        (inReceptacle Mug_bar__minus_02_dot_51_bar__plus_00_dot_97_bar__minus_03_dot_65 CoffeeMachine_bar__minus_02_dot_51_bar__plus_00_dot_93_bar__minus_03_dot_80)
        (inReceptacle Potato_bar__minus_00_dot_33_bar__plus_01_dot_32_bar__minus_00_dot_65 Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65)
        (inReceptacle Tomato_bar__minus_00_dot_29_bar__plus_01_dot_34_bar__minus_00_dot_99 Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65)
        (inReceptacle Lettuce_bar__minus_00_dot_32_bar__plus_01_dot_58_bar__minus_00_dot_88 Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65)
        (inReceptacle Plate_bar__minus_00_dot_36_bar__plus_01_dot_50_bar__minus_00_dot_65 Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65)
        (inReceptacle Mug_bar__minus_00_dot_27_bar__plus_01_dot_57_bar__minus_01_dot_94 Microwave_bar__minus_00_dot_22_bar__plus_01_dot_47_bar__minus_02_dot_06)
        
        
        (receptacleAtLocation Cabinet_bar__minus_00_dot_33_bar__plus_01_dot_89_bar__minus_02_dot_51 loc_bar__minus_4_bar__minus_12_bar_1_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_34_bar__plus_01_dot_89_bar__minus_01_dot_29 loc_bar__minus_4_bar__minus_6_bar_1_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_00_dot_39 loc_bar__minus_6_bar__minus_4_bar_1_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_27 loc_bar__minus_6_bar__minus_6_bar_1_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_01_dot_63 loc_bar__minus_4_bar__minus_7_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_34_bar__plus_02_dot_11_bar__minus_02_dot_50 loc_bar__minus_4_bar__minus_10_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_35_bar__plus_01_dot_89_bar__minus_03_dot_29 loc_bar__minus_4_bar__minus_9_bar_2_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_01_dot_61 loc_bar__minus_6_bar__minus_6_bar_1_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_02_dot_51 loc_bar__minus_6_bar__minus_9_bar_1_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_63_bar__plus_00_dot_39_bar__minus_03_dot_01 loc_bar__minus_7_bar__minus_11_bar_1_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_01_bar__plus_00_dot_39_bar__minus_03_dot_37 loc_bar__minus_7_bar__minus_10_bar_2_bar_60)
        (receptacleAtLocation CoffeeMachine_bar__minus_02_dot_51_bar__plus_00_dot_93_bar__minus_03_dot_80 loc_bar__minus_10_bar__minus_12_bar_2_bar_45)
        (receptacleAtLocation CounterTop_bar__minus_00_dot_33_bar__plus_00_dot_98_bar__minus_01_dot_45 loc_bar__minus_4_bar__minus_6_bar_1_bar_45)
        (receptacleAtLocation CounterTop_bar__minus_01_dot_94_bar__plus_00_dot_98_bar__minus_03_dot_67 loc_bar__minus_6_bar__minus_12_bar_2_bar_45)
        (receptacleAtLocation DiningTable_bar__minus_03_dot_03_bar__plus_00_dot_00_bar__minus_00_dot_44 loc_bar__minus_12_bar__minus_5_bar_0_bar_60)
        (receptacleAtLocation DiningTable_bar__minus_03_dot_62_bar__plus_00_dot_67_bar__minus_03_dot_32 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_48_bar__plus_00_dot_78_bar__minus_02_dot_74 loc_bar__minus_5_bar__minus_10_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_50_bar__plus_00_dot_78_bar__minus_01_dot_45 loc_bar__minus_4_bar__minus_6_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_15_bar__plus_00_dot_78_bar__minus_03_dot_50 loc_bar__minus_4_bar__minus_12_bar_2_bar_60)
        (receptacleAtLocation Fridge_bar__minus_00_dot_31_bar__plus_00_dot_00_bar__minus_00_dot_65 loc_bar__minus_5_bar__minus_3_bar_1_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_02_dot_42_bar__plus_00_dot_00_bar__minus_03_dot_54 loc_bar__minus_10_bar__minus_12_bar_2_bar_60)
        (receptacleAtLocation Microwave_bar__minus_00_dot_22_bar__plus_01_dot_47_bar__minus_02_dot_06 loc_bar__minus_4_bar__minus_8_bar_1_bar_15)
        (receptacleAtLocation SideTable_bar__minus_04_dot_07_bar__plus_00_dot_73_bar__minus_00_dot_31 loc_bar__minus_15_bar__minus_5_bar_0_bar_45)
        (receptacleAtLocation Sink_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_03_dot_39_bar_SinkBasin loc_bar__minus_8_bar__minus_11_bar_1_bar_30)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_01_dot_85 loc_bar__minus_4_bar__minus_7_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_22_bar__plus_00_dot_92_bar__minus_02_dot_24 loc_bar__minus_4_bar__minus_9_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_01_dot_85 loc_bar__minus_4_bar__minus_7_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_44_bar__plus_00_dot_92_bar__minus_02_dot_24 loc_bar__minus_4_bar__minus_9_bar_1_bar_45)
        (receptacleAtLocation Toaster_bar__minus_01_dot_97_bar__plus_00_dot_93_bar__minus_03_dot_76 loc_bar__minus_8_bar__minus_12_bar_2_bar_45)
        (objectAtLocation Bowl_bar__minus_00_dot_21_bar__plus_01_dot_94_bar__minus_00_dot_71 loc_bar__minus_6_bar__minus_4_bar_1_bar__minus_15)
        (objectAtLocation Mug_bar__minus_00_dot_20_bar__plus_01_dot_93_bar__minus_01_dot_95 loc_bar__minus_4_bar__minus_7_bar_1_bar__minus_30)
        (objectAtLocation Potato_bar__minus_00_dot_33_bar__plus_01_dot_32_bar__minus_00_dot_65 loc_bar__minus_5_bar__minus_3_bar_1_bar_60)
        (objectAtLocation DishSponge_bar__minus_01_dot_12_bar__plus_00_dot_75_bar__minus_03_dot_51 loc_bar__minus_4_bar__minus_12_bar_2_bar_60)
        (objectAtLocation Apple_bar__minus_02_dot_39_bar__plus_00_dot_16_bar__minus_03_dot_54 loc_bar__minus_10_bar__minus_12_bar_2_bar_60)
        (objectAtLocation ButterKnife_bar__minus_01_dot_69_bar__plus_00_dot_94_bar__minus_03_dot_67 loc_bar__minus_6_bar__minus_12_bar_2_bar_45)
        (objectAtLocation Plate_bar__minus_00_dot_51_bar__plus_00_dot_09_bar__minus_02_dot_74 loc_bar__minus_6_bar__minus_9_bar_1_bar_60)
        (objectAtLocation Fork_bar__minus_03_dot_57_bar__plus_00_dot_90_bar__minus_00_dot_46 loc_bar__minus_12_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Pot_bar__minus_00_dot_22_bar__plus_00_dot_96_bar__minus_01_dot_85 loc_bar__minus_4_bar__minus_7_bar_1_bar_45)
        (objectAtLocation SoapBottle_bar__minus_00_dot_86_bar__plus_00_dot_08_bar__minus_03_dot_41 loc_bar__minus_7_bar__minus_11_bar_1_bar_45)
        (objectAtLocation Mug_bar__minus_02_dot_51_bar__plus_00_dot_97_bar__minus_03_dot_65 loc_bar__minus_10_bar__minus_12_bar_2_bar_45)
        (objectAtLocation SoapBottle_bar__minus_03_dot_66_bar__plus_00_dot_76_bar__minus_03_dot_63 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation Plate_bar__minus_00_dot_36_bar__plus_01_dot_50_bar__minus_00_dot_65 loc_bar__minus_5_bar__minus_3_bar_1_bar_60)
        (objectAtLocation Sink_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_03_dot_39 loc_bar__minus_4_bar__minus_12_bar_1_bar_60)
        (objectAtLocation SoapBottle_bar__minus_01_dot_08_bar__plus_00_dot_08_bar__minus_03_dot_45 loc_bar__minus_7_bar__minus_10_bar_2_bar_60)
        (objectAtLocation Pot_bar__minus_03_dot_36_bar__plus_00_dot_90_bar__minus_00_dot_38 loc_bar__minus_12_bar__minus_5_bar_0_bar_60)
        (objectAtLocation StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_01_dot_83 loc_bar__minus_4_bar__minus_7_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_02_dot_13 loc_bar__minus_4_bar__minus_9_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_02_dot_29 loc_bar__minus_4_bar__minus_9_bar_1_bar_60)
        (objectAtLocation StoveKnob_bar__minus_00_dot_62_bar__plus_00_dot_90_bar__minus_01_dot_98 loc_bar__minus_4_bar__minus_8_bar_1_bar_60)
        (objectAtLocation Fork_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_01_dot_58 loc_bar__minus_4_bar__minus_6_bar_1_bar_45)
        (objectAtLocation Cup_bar__minus_00_dot_14_bar__plus_01_dot_50_bar__minus_03_dot_14 loc_bar__minus_4_bar__minus_12_bar_1_bar__minus_15)
        (objectAtLocation Tomato_bar__minus_00_dot_29_bar__plus_01_dot_34_bar__minus_00_dot_99 loc_bar__minus_5_bar__minus_3_bar_1_bar_60)
        (objectAtLocation Pan_bar__minus_03_dot_20_bar__plus_00_dot_76_bar__minus_03_dot_53 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation Plate_bar__minus_02_dot_76_bar__plus_00_dot_91_bar__minus_00_dot_38 loc_bar__minus_12_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Window_bar__minus_01_dot_39_bar__plus_01_dot_61_bar__minus_04_dot_11 loc_bar__minus_6_bar__minus_12_bar_2_bar_0)
        (objectAtLocation LightSwitch_bar__minus_04_dot_37_bar__plus_01_dot_49_bar__minus_01_dot_03 loc_bar__minus_15_bar__minus_4_bar_3_bar_15)
        (objectAtLocation Bread_bar__minus_04_dot_12_bar__plus_00_dot_77_bar__minus_00_dot_37 loc_bar__minus_15_bar__minus_5_bar_0_bar_45)
        (objectAtLocation Spatula_bar__minus_04_dot_00_bar__plus_00_dot_71_bar__minus_00_dot_31 loc_bar__minus_15_bar__minus_5_bar_0_bar_45)
        (objectAtLocation Knife_bar__minus_04_dot_06_bar__plus_00_dot_72_bar__minus_00_dot_37 loc_bar__minus_15_bar__minus_5_bar_0_bar_45)
        (objectAtLocation SaltShaker_bar__minus_00_dot_58_bar__plus_00_dot_08_bar__minus_01_dot_53 loc_bar__minus_6_bar__minus_6_bar_1_bar_60)
        (objectAtLocation PepperShaker_bar__minus_03_dot_70_bar__plus_00_dot_76_bar__minus_02_dot_97 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation ButterKnife_bar__minus_04_dot_04_bar__plus_00_dot_76_bar__minus_03_dot_11 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation Apple_bar__minus_03_dot_20_bar__plus_00_dot_82_bar__minus_03_dot_12 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation DishSponge_bar__minus_00_dot_32_bar__plus_00_dot_94_bar__minus_02_dot_85 loc_bar__minus_6_bar__minus_12_bar_2_bar_45)
        (objectAtLocation Potato_bar__minus_00_dot_39_bar__plus_00_dot_97_bar__minus_01_dot_41 loc_bar__minus_4_bar__minus_6_bar_1_bar_45)
        (objectAtLocation Egg_bar__minus_03_dot_12_bar__plus_00_dot_80_bar__minus_03_dot_29 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation Spoon_bar__minus_03_dot_23_bar__plus_00_dot_76_bar__minus_03_dot_06 loc_bar__minus_14_bar__minus_9_bar_2_bar_45)
        (objectAtLocation Lettuce_bar__minus_00_dot_32_bar__plus_01_dot_58_bar__minus_00_dot_88 loc_bar__minus_5_bar__minus_3_bar_1_bar_60)
        (objectAtLocation Mug_bar__minus_00_dot_27_bar__plus_01_dot_57_bar__minus_01_dot_94 loc_bar__minus_4_bar__minus_8_bar_1_bar_15)
        (objectAtLocation Blinds_bar__minus_01_dot_37_bar__plus_02_dot_21_bar__minus_03_dot_96 loc_bar__minus_6_bar__minus_12_bar_2_bar__minus_30)
        (objectAtLocation Bowl_bar__minus_02_dot_56_bar__plus_00_dot_89_bar__minus_00_dot_63 loc_bar__minus_12_bar__minus_5_bar_0_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (sliceable ?o1)
                                    (isSliced ?o1)
                                    (objectType ?o1 PotatoType)
                                    (receptacleType ?r GarbageCanType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))

                                            (sliceable ?o2)
                                            (isSliced ?o2)
                                            (objectType ?o2 PotatoType)
                                            (receptacleType ?r GarbageCanType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            