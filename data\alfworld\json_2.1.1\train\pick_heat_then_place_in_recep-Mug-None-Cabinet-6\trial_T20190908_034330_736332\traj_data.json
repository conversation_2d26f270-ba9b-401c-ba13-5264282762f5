{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000098.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000099.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000100.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000101.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000110.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000111.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000112.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000113.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000114.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000128.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000129.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000130.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000131.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000205.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000206.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000207.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000209.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000210.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000211.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000212.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000213.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000214.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000215.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000216.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000217.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000220.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000221.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000222.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000223.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000224.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000225.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000226.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000265.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000266.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000267.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000268.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000269.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000270.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000271.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000272.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000273.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000274.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000275.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000276.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000277.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000278.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000279.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000280.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000281.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000282.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000283.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000284.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000285.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000286.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000287.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000288.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000289.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000290.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000291.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000292.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000293.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000294.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000295.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000296.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000340.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000341.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000342.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000343.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000344.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000345.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000346.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000347.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000348.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000349.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000350.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000351.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000352.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000353.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000354.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000355.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000356.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000357.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000358.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000359.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000360.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000361.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000362.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000363.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000364.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000365.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000366.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000367.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000368.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000369.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000370.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000371.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000372.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000373.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000374.png", "low_idx": 57}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-7|3|3|0"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [-10.70972156, -10.70972156, 2.8080004, 2.8080004, 6.13200044, 6.13200044]], "coordinateReceptacleObjectId": ["Cabinet", [-9.784, -9.784, 1.620000244, 1.620000244, 7.79599952, 7.79599952]], "forceVisible": true, "objectId": "Mug|-02.68|+01.53|+00.70"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|3|-30"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [-10.70972156, -10.70972156, 2.8080004, 2.8080004, 6.13200044, 6.13200044]], "coordinateReceptacleObjectId": ["Cabinet", [-9.784, -9.784, 11.7, 11.7, 7.79599952, 7.79599952]], "forceVisible": true, "objectId": "Mug|-02.68|+01.53|+00.70", "receptacleObjectId": "Cabinet|-02.45|+01.95|+02.93"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-02.45|+01.95|+00.41"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [22, 1, 241, 169], "mask": [[22, 220], [322, 220], [622, 220], [922, 220], [1222, 220], [1522, 220], [1822, 220], [2122, 220], [2422, 220], [2722, 220], [3022, 220], [3322, 220], [3622, 220], [3922, 220], [4222, 220], [4522, 220], [4822, 220], [5122, 220], [5422, 220], [5722, 220], [6022, 220], [6322, 220], [6622, 220], [6922, 220], [7222, 220], [7522, 220], [7822, 220], [8122, 220], [8422, 220], [8722, 220], [9022, 220], [9322, 220], [9622, 220], [9922, 220], [10222, 220], [10522, 220], [10822, 220], [11122, 220], [11422, 220], [11722, 220], [12022, 220], [12322, 220], [12622, 220], [12922, 220], [13222, 220], [13522, 220], [13822, 220], [14122, 220], [14422, 220], [14722, 220], [15022, 220], [15322, 220], [15622, 220], [15922, 220], [16222, 220], [16522, 220], [16822, 220], [17122, 220], [17422, 220], [17722, 220], [18022, 220], [18322, 220], [18622, 220], [18922, 220], [19222, 220], [19522, 220], [19822, 220], [20122, 220], [20422, 220], [20722, 220], [21022, 220], [21322, 220], [21622, 220], [21922, 220], [22222, 220], [22522, 220], [22822, 220], [23122, 220], [23422, 220], [23722, 220], [24022, 220], [24322, 220], [24622, 220], [24922, 220], [25222, 220], [25522, 220], [25822, 220], [26122, 220], [26422, 220], [26722, 220], [27022, 220], [27322, 220], [27622, 220], [27922, 220], [28222, 220], [28522, 220], [28822, 220], [29122, 220], [29422, 220], [29722, 220], [30022, 220], [30322, 220], [30622, 220], [30922, 220], [31222, 220], [31522, 220], [31822, 220], [32122, 220], [32422, 220], [32722, 220], [33022, 220], [33322, 220], [33622, 220], [33922, 220], [34222, 220], [34522, 220], [34822, 220], [35122, 220], [35422, 220], [35722, 220], [36022, 220], [36322, 220], [36622, 220], [36922, 220], [37222, 220], [37522, 220], [37822, 220], [38122, 220], [38422, 220], [38722, 220], [39022, 220], [39322, 220], [39622, 220], [39922, 220], [40222, 220], [40522, 220], [40822, 220], [41122, 220], [41422, 220], [41722, 220], [42022, 220], [42322, 220], [42622, 220], [42922, 220], [43222, 220], [43522, 220], [43822, 220], [44122, 220], [44422, 220], [44722, 220], [45022, 220], [45322, 220], [45622, 220], [45922, 220], [46222, 220], [46522, 220], [46822, 220], [47122, 220], [47422, 220], [47722, 220], [48022, 220], [48322, 220], [48622, 220], [48922, 220], [49222, 220], [49522, 220], [49822, 220], [50122, 220], [50422, 220]], "point": [131, 84]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-02.68|+01.53|+00.70"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [124, 136, 148, 164], "mask": [[40624, 25], [40924, 25], [41224, 25], [41524, 25], [41824, 25], [42124, 25], [42424, 25], [42724, 25], [43024, 25], [43324, 25], [43624, 25], [43924, 25], [44225, 24], [44525, 23], [44825, 23], [45125, 23], [45425, 23], [45725, 23], [46025, 23], [46325, 23], [46625, 23], [46925, 23], [47225, 23], [47525, 23], [47825, 23], [48125, 23], [48425, 23], [48725, 23], [49025, 23]], "point": [136, 149]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-02.45|+01.95|+00.41"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 240, 173], "mask": [[0, 241], [300, 241], [600, 241], [900, 241], [1200, 241], [1500, 241], [1800, 241], [2100, 241], [2400, 241], [2700, 241], [3000, 241], [3300, 241], [3600, 241], [3900, 241], [4200, 241], [4500, 241], [4800, 241], [5100, 241], [5400, 241], [5700, 241], [6000, 241], [6300, 241], [6600, 241], [6900, 241], [7200, 241], [7500, 241], [7800, 241], [8100, 241], [8400, 241], [8700, 241], [9000, 241], [9300, 241], [9600, 241], [9900, 241], [10200, 241], [10500, 241], [10800, 241], [11100, 241], [11400, 241], [11700, 241], [12000, 241], [12300, 241], [12600, 241], [12900, 241], [13200, 241], [13500, 241], [13800, 241], [14100, 241], [14400, 241], [14700, 241], [15000, 241], [15300, 241], [15600, 241], [15900, 241], [16200, 241], [16500, 241], [16800, 241], [17100, 241], [17400, 241], [17700, 241], [18000, 241], [18300, 241], [18600, 241], [18900, 241], [19200, 241], [19500, 241], [19800, 241], [20100, 241], [20400, 241], [20700, 241], [21000, 241], [21300, 241], [21600, 241], [21900, 241], [22200, 241], [22500, 241], [22800, 241], [23100, 241], [23400, 241], [23700, 241], [24000, 241], [24300, 241], [24600, 241], [24900, 241], [25200, 241], [25500, 241], [25800, 241], [26100, 241], [26400, 241], [26700, 241], [27000, 241], [27300, 241], [27600, 241], [27900, 241], [28200, 241], [28500, 241], [28800, 241], [29100, 241], [29400, 241], [29700, 241], [30000, 241], [30300, 241], [30600, 241], [30900, 241], [31200, 241], [31500, 241], [31800, 241], [32100, 241], [32400, 241], [32700, 241], [33000, 241], [33300, 241], [33600, 241], [33900, 241], [34200, 241], [34500, 241], [34800, 241], [35100, 241], [35400, 241], [35700, 241], [36000, 241], [36300, 241], [36600, 241], [36900, 241], [37200, 241], [37500, 241], [37800, 241], [38100, 241], [38400, 241], [38700, 241], [39000, 241], [39300, 241], [39600, 241], [39900, 241], [40200, 241], [40500, 241], [40800, 241], [41100, 241], [41400, 241], [41700, 241], [42000, 241], [42300, 241], [42600, 241], [42900, 241], [43200, 241], [43500, 241], [43800, 241], [44100, 241], [44400, 241], [44700, 241], [45000, 241], [45300, 241], [45600, 241], [45900, 241], [46200, 241], [46500, 241], [46800, 241], [47100, 241], [47400, 241], [47700, 241], [48000, 241], [48300, 241], [48600, 241], [48900, 241], [49200, 241], [49500, 241], [49800, 241], [50100, 241], [50400, 26], [50700, 23], [51000, 16], [51300, 10], [51600, 3]], "point": [120, 86]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-02.68|+01.53|+00.70", "placeStationary": true, "receptacleObjectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 235], [23400, 234], [23700, 234], [24000, 234], [24300, 234], [24600, 233], [24900, 233], [25200, 233], [25500, 233], [25800, 232], [26100, 232], [26400, 232], [26700, 232], [27000, 231], [27300, 231], [27600, 231], [27900, 231], [28200, 114], [28319, 111], [28500, 113], [28621, 109], [28800, 112], [28922, 108], [29100, 111], [29222, 108], [29400, 111], [29523, 106], [29700, 110], [29824, 105], [30000, 110], [30124, 105], [30300, 110], [30424, 105], [30600, 110], [30724, 104], [30900, 110], [31025, 103], [31200, 110], [31325, 103], [31500, 110], [31624, 104], [31800, 110], [31924, 103], [32100, 111], [32224, 103], [32400, 111], [32523, 104], [32700, 112], [32823, 104], [33000, 113], [33122, 104], [33300, 115], [33420, 106], [33600, 226], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 139], [22951, 84], [23100, 136], [23254, 81], [23400, 134], [23555, 79], [23700, 133], [23856, 78], [24000, 132], [24157, 77], [24300, 132], [24458, 76], [24600, 132], [24758, 75], [24900, 132], [25061, 72], [25200, 132], [25362, 71], [25500, 132], [25663, 70], [25800, 132], [25958, 2], [25963, 69], [26100, 132], [26257, 4], [26264, 68], [26400, 132], [26557, 4], [26564, 68], [26700, 132], [26857, 4], [26863, 69], [27000, 132], [27157, 4], [27163, 68], [27300, 133], [27457, 4], [27463, 68], [27600, 133], [27757, 4], [27763, 68], [27900, 133], [28057, 3], [28063, 68], [28200, 114], [28319, 14], [28357, 3], [28362, 68], [28500, 113], [28621, 12], [28657, 3], [28662, 68], [28800, 112], [28922, 11], [28957, 2], [28961, 69], [29100, 111], [29222, 11], [29257, 2], [29261, 69], [29400, 111], [29523, 10], [29557, 1], [29561, 68], [29700, 110], [29824, 9], [29860, 69], [30000, 110], [30124, 9], [30159, 70], [30300, 110], [30424, 10], [30458, 71], [30600, 110], [30724, 10], [30757, 71], [30900, 110], [31025, 9], [31056, 72], [31200, 110], [31325, 9], [31356, 72], [31500, 110], [31624, 10], [31656, 72], [31800, 110], [31924, 10], [31956, 71], [32100, 111], [32224, 10], [32256, 71], [32400, 111], [32523, 12], [32556, 71], [32700, 112], [32823, 12], [32855, 72], [33000, 113], [33122, 14], [33154, 72], [33300, 115], [33420, 17], [33453, 73], [33600, 139], [33751, 75], [33900, 143], [34046, 79], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-02.68|+01.53|+00.70"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [132, 77, 163, 114], "mask": [[22939, 12], [23236, 18], [23534, 21], [23833, 23], [24132, 25], [24432, 26], [24732, 26], [25032, 29], [25332, 30], [25632, 31], [25932, 26], [25960, 3], [26232, 25], [26261, 3], [26532, 25], [26561, 3], [26832, 25], [26861, 2], [27132, 25], [27161, 2], [27433, 24], [27461, 2], [27733, 24], [27761, 2], [28033, 24], [28060, 3], [28333, 24], [28360, 2], [28633, 24], [28660, 2], [28933, 24], [28959, 2], [29233, 24], [29259, 2], [29533, 24], [29558, 3], [29833, 27], [30133, 26], [30434, 24], [30734, 23], [31034, 22], [31334, 22], [31634, 22], [31934, 22], [32234, 22], [32535, 21], [32835, 20], [33136, 18], [33437, 16], [33739, 12], [34043, 3]], "point": [147, 94]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 235], [23400, 234], [23700, 234], [24000, 234], [24300, 234], [24600, 233], [24900, 233], [25200, 233], [25500, 233], [25800, 232], [26100, 232], [26400, 232], [26700, 232], [27000, 231], [27300, 231], [27600, 231], [27900, 231], [28200, 114], [28319, 111], [28500, 113], [28621, 109], [28800, 112], [28922, 108], [29100, 111], [29222, 108], [29400, 111], [29523, 106], [29700, 110], [29824, 105], [30000, 110], [30124, 105], [30300, 110], [30424, 105], [30600, 110], [30724, 104], [30900, 110], [31025, 103], [31200, 110], [31325, 103], [31500, 110], [31624, 104], [31800, 110], [31924, 103], [32100, 111], [32224, 103], [32400, 111], [32523, 104], [32700, 112], [32823, 104], [33000, 113], [33122, 104], [33300, 115], [33420, 106], [33600, 226], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-02.45|+01.95|+02.93"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [79, 62, 299, 300], "mask": [[18408, 152], [18707, 153], [19007, 154], [19307, 154], [19607, 154], [19907, 154], [20206, 156], [20506, 156], [20806, 156], [21106, 157], [21406, 157], [21706, 157], [22006, 158], [22306, 158], [22606, 158], [22905, 159], [23205, 160], [23505, 160], [23805, 160], [24105, 161], [24405, 161], [24705, 161], [25005, 162], [25304, 163], [25604, 163], [25904, 163], [26204, 164], [26504, 164], [26804, 164], [27104, 165], [27404, 165], [27704, 165], [28003, 167], [28303, 167], [28603, 167], [28903, 168], [29203, 168], [29503, 168], [29803, 168], [30103, 169], [30402, 170], [30702, 170], [31002, 171], [31302, 171], [31602, 171], [31902, 172], [32202, 172], [32502, 172], [32802, 172], [33101, 174], [33401, 174], [33701, 174], [34001, 175], [34301, 175], [34601, 175], [34901, 176], [35201, 176], [35500, 177], [35800, 178], [36100, 178], [36400, 178], [36700, 178], [37000, 179], [37300, 179], [37600, 179], [37900, 180], [38199, 181], [38499, 181], [38799, 182], [39099, 182], [39399, 182], [39699, 182], [39999, 183], [40299, 183], [40598, 184], [40898, 185], [41198, 185], [41498, 185], [41798, 186], [42098, 186], [42398, 186], [42698, 187], [42997, 188], [43297, 188], [43597, 188], [43897, 189], [44197, 189], [44497, 189], [44797, 190], [45097, 190], [45397, 190], [45696, 192], [45996, 192], [46296, 192], [46596, 192], [46896, 193], [47196, 193], [47496, 193], [47796, 194], [48095, 195], [48395, 195], [48695, 196], [48995, 196], [49295, 196], [49595, 197], [49895, 197], [50195, 197], [50495, 197], [50794, 199], [51094, 199], [51394, 199], [51694, 200], [51994, 200], [52294, 200], [52594, 201], [52894, 201], [53193, 202], [53493, 202], [53793, 203], [54093, 203], [54393, 203], [54693, 204], [54993, 204], [55293, 204], [55593, 205], [55892, 206], [56192, 206], [56492, 207], [56792, 207], [57092, 207], [57392, 207], [57692, 208], [57992, 50], [58058, 142], [58291, 47], [58362, 138], [58591, 45], [58664, 136], [58891, 42], [58967, 133], [59191, 40], [59269, 131], [59491, 38], [59571, 129], [59791, 37], [59872, 128], [60091, 35], [60174, 126], [60391, 34], [60475, 125], [60691, 33], [60776, 124], [60990, 33], [61077, 123], [61290, 32], [61378, 122], [61590, 31], [61679, 121], [61890, 30], [61980, 120], [62190, 29], [62281, 119], [62490, 28], [62582, 118], [62790, 28], [62882, 118], [63090, 27], [63183, 117], [63389, 28], [63483, 117], [63689, 27], [63784, 116], [63989, 26], [64085, 115], [64289, 26], [64385, 115], [64589, 25], [64686, 114], [64889, 25], [64986, 114], [65189, 25], [65286, 114], [65489, 25], [65586, 114], [65788, 25], [65887, 113], [66088, 25], [66187, 113], [66388, 25], [66487, 113], [66688, 19], [66787, 113], [66988, 16], [67088, 112], [67288, 14], [67388, 112], [67588, 12], [67688, 112], [67888, 11], [67988, 112], [68188, 11], [68288, 112], [68487, 12], [68588, 112], [68787, 11], [68888, 112], [69087, 11], [69188, 112], [69387, 10], [69487, 113], [69687, 10], [69705, 8], [69787, 113], [69987, 10], [70005, 8], [70087, 113], [70287, 10], [70305, 8], [70387, 113], [70587, 11], [70606, 7], [70687, 113], [70886, 12], [70906, 8], [70986, 114], [71186, 12], [71207, 7], [71286, 114], [71486, 13], [71508, 6], [71586, 114], [71786, 13], [71809, 5], [71886, 114], [72086, 14], [72110, 5], [72185, 115], [72386, 15], [72411, 4], [72485, 115], [72686, 15], [72712, 3], [72785, 115], [72986, 16], [73013, 3], [73084, 116], [73286, 17], [73314, 2], [73384, 116], [73585, 19], [73684, 116], [73885, 20], [73983, 117], [74185, 21], [74283, 117], [74485, 22], [74583, 117], [74785, 23], [74882, 118], [75085, 24], [75182, 118], [75385, 25], [75482, 118], [75685, 26], [75781, 119], [75984, 29], [76081, 119], [76284, 30], [76381, 119], [76584, 33], [76680, 120], [76884, 36], [76980, 120], [77184, 36], [77280, 120], [77484, 36], [77580, 120], [77784, 37], [77879, 121], [78084, 37], [78179, 121], [78384, 37], [78479, 121], [78683, 39], [78778, 122], [78983, 39], [79078, 122], [79283, 39], [79378, 122], [79583, 40], [79677, 123], [79883, 40], [79977, 123], [80183, 40], [80277, 123], [80483, 41], [80576, 124], [80783, 41], [80876, 124], [81082, 42], [81176, 124], [81382, 43], [81475, 125], [81682, 43], [81775, 125], [81982, 43], [82075, 125], [82282, 44], [82374, 126], [82582, 45], [82673, 127], [82882, 45], [82973, 127], [83182, 46], [83272, 128], [83482, 47], [83571, 129], [83781, 49], [83870, 130], [84081, 50], [84169, 131], [84381, 51], [84468, 132], [84681, 52], [84767, 133], [84981, 53], [85066, 134], [85281, 55], [85364, 136], [85581, 57], [85662, 138], [85881, 59], [85960, 140], [86180, 62], [86258, 142], [86480, 69], [86551, 149], [86780, 220], [87080, 220], [87380, 220], [87680, 220], [87980, 220], [88280, 220], [88580, 220], [88879, 221], [89179, 221], [89479, 221], [89779, 221]], "point": [189, 180]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-02.68|+01.53|+00.70", "placeStationary": true, "receptacleObjectId": "Cabinet|-02.45|+01.95|+02.93"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [79, 1, 299, 300], "mask": [[284, 16], [584, 16], [884, 16], [1183, 17], [1483, 17], [1782, 18], [2082, 18], [2381, 19], [2681, 19], [2980, 20], [3280, 20], [3579, 21], [3879, 21], [4179, 21], [4478, 22], [4778, 22], [5077, 23], [5377, 23], [5676, 24], [5976, 24], [6275, 25], [6575, 25], [6875, 25], [7174, 26], [7474, 26], [7773, 27], [8073, 27], [8372, 28], [8672, 28], [8971, 29], [9271, 29], [9570, 30], [9870, 30], [10170, 30], [10469, 31], [10769, 31], [11068, 32], [11368, 32], [11667, 33], [11967, 33], [12266, 34], [12566, 34], [12866, 34], [13165, 35], [13465, 35], [13764, 36], [14064, 36], [14363, 37], [14663, 37], [14962, 38], [15262, 38], [15562, 38], [15861, 39], [16161, 39], [16460, 40], [16760, 40], [17059, 41], [17359, 41], [17658, 42], [17958, 42], [18257, 43], [18557, 43], [18858, 42], [19007, 193], [19307, 193], [19607, 193], [19907, 193], [20206, 194], [20506, 194], [20806, 194], [21106, 194], [21406, 194], [21706, 194], [22006, 194], [22306, 194], [22606, 194], [22905, 195], [23205, 195], [23505, 195], [23805, 195], [24105, 195], [24405, 195], [24705, 195], [25005, 195], [25304, 196], [25604, 196], [25904, 196], [26204, 196], [26504, 196], [26804, 196], [27104, 196], [27404, 196], [27704, 196], [28003, 197], [28303, 197], [28603, 197], [28903, 197], [29203, 197], [29503, 197], [29803, 197], [30103, 197], [30402, 198], [30702, 198], [31002, 198], [31302, 198], [31602, 198], [31902, 198], [32202, 198], [32502, 198], [32802, 198], [33101, 199], [33401, 199], [33701, 199], [34001, 199], [34301, 199], [34601, 199], [34901, 199], [35201, 199], [35500, 200], [35800, 200], [36100, 200], [36400, 200], [36700, 200], [37000, 200], [37300, 200], [37600, 200], [37900, 200], [38199, 201], [38499, 201], [38799, 201], [39099, 201], [39399, 201], [39699, 201], [39999, 201], [40299, 201], [40598, 202], [40898, 202], [41198, 202], [41498, 202], [41798, 202], [42098, 202], [42398, 202], [42698, 202], [42997, 203], [43297, 203], [43597, 203], [43897, 203], [44197, 203], [44497, 203], [44797, 203], [45097, 203], [45397, 203], [45696, 204], [45996, 204], [46296, 204], [46596, 204], [46896, 204], [47196, 204], [47496, 204], [47796, 204], [48095, 205], [48395, 205], [48695, 205], [48995, 205], [49295, 205], [49595, 205], [49895, 205], [50195, 205], [50495, 205], [50794, 206], [51094, 206], [51394, 206], [51694, 206], [51994, 206], [52294, 206], [52594, 206], [52894, 206], [53193, 207], [53493, 207], [53793, 207], [54093, 207], [54393, 207], [54693, 207], [54993, 207], [55293, 207], [55593, 207], [55892, 208], [56192, 208], [56492, 208], [56792, 208], [57092, 208], [57392, 208], [57692, 208], [57992, 50], [58058, 142], [58291, 47], [58362, 138], [58591, 45], [58664, 136], [58891, 42], [58967, 133], [59191, 40], [59269, 131], [59491, 38], [59571, 129], [59791, 37], [59872, 128], [60091, 35], [60174, 126], [60391, 34], [60475, 125], [60691, 33], [60776, 124], [60990, 33], [61077, 123], [61290, 32], [61378, 122], [61590, 31], [61679, 121], [61890, 30], [61980, 120], [62190, 29], [62281, 119], [62490, 28], [62582, 118], [62790, 28], [62882, 118], [63090, 27], [63183, 117], [63389, 28], [63483, 117], [63689, 27], [63784, 116], [63989, 26], [64085, 115], [64289, 26], [64385, 115], [64589, 25], [64686, 114], [64889, 25], [64986, 114], [65189, 25], [65286, 114], [65489, 25], [65586, 114], [65788, 25], [65887, 113], [66088, 25], [66187, 113], [66388, 25], [66487, 113], [66688, 19], [66787, 113], [66988, 16], [67088, 112], [67288, 14], [67388, 112], [67588, 12], [67688, 112], [67888, 11], [67988, 112], [68188, 11], [68288, 112], [68487, 12], [68588, 112], [68787, 11], [68888, 112], [69087, 11], [69188, 112], [69387, 10], [69487, 113], [69687, 10], [69705, 8], [69787, 113], [69987, 10], [70005, 8], [70087, 113], [70287, 10], [70305, 8], [70387, 113], [70587, 11], [70606, 7], [70687, 113], [70886, 12], [70906, 8], [70986, 114], [71186, 12], [71207, 7], [71286, 114], [71486, 13], [71508, 6], [71586, 114], [71786, 13], [71809, 5], [71886, 114], [72086, 14], [72110, 5], [72185, 115], [72386, 15], [72411, 4], [72485, 115], [72686, 15], [72712, 3], [72785, 78], [72867, 33], [72986, 16], [73013, 3], [73084, 78], [73167, 33], [73286, 17], [73314, 2], [73384, 78], [73470, 30], [73585, 19], [73684, 75], [73774, 26], [73885, 20], [73983, 74], [74076, 24], [74185, 21], [74283, 72], [74377, 23], [74485, 22], [74583, 72], [74678, 22], [74785, 23], [74882, 72], [74978, 22], [75085, 24], [75182, 72], [75279, 21], [75385, 25], [75482, 72], [75579, 21], [75685, 26], [75781, 73], [75879, 21], [75984, 29], [76081, 73], [76179, 21], [76284, 30], [76381, 74], [76479, 21], [76584, 33], [76680, 75], [76778, 22], [76884, 36], [76980, 76], [77077, 23], [77184, 36], [77280, 78], [77378, 22], [77484, 36], [77580, 78], [77678, 22], [77784, 37], [77879, 79], [77978, 22], [78084, 37], [78179, 79], [78278, 22], [78384, 37], [78479, 79], [78579, 21], [78683, 39], [78778, 81], [78879, 21], [78983, 39], [79078, 81], [79179, 21], [79283, 39], [79378, 81], [79479, 21], [79583, 40], [79677, 82], [79780, 20], [79883, 40], [79977, 82], [80080, 20], [80183, 40], [80277, 82], [80380, 20], [80483, 41], [80576, 84], [80680, 20], [80783, 41], [80876, 84], [80981, 19], [81082, 42], [81176, 84], [81281, 19], [81382, 43], [81475, 85], [81581, 19], [81682, 43], [81775, 85], [81881, 19], [81982, 43], [82075, 86], [82182, 18], [82282, 44], [82374, 87], [82482, 18], [82582, 45], [82673, 88], [82782, 18], [82882, 45], [82973, 88], [83083, 17], [83182, 46], [83272, 89], [83383, 17], [83482, 47], [83571, 90], [83683, 17], [83781, 49], [83870, 91], [83984, 16], [84081, 50], [84169, 92], [84284, 16], [84381, 51], [84468, 93], [84585, 15], [84681, 52], [84767, 94], [84885, 15], [84981, 53], [85066, 95], [85185, 15], [85281, 55], [85364, 97], [85486, 14], [85581, 57], [85662, 99], [85786, 14], [85881, 59], [85960, 101], [86087, 13], [86180, 62], [86258, 103], [86387, 13], [86480, 69], [86551, 110], [86687, 13], [86780, 181], [86987, 13], [87080, 182], [87288, 12], [87380, 182], [87588, 12], [87680, 182], [87888, 12], [87980, 182], [88188, 12], [88280, 183], [88488, 12], [88580, 183], [88789, 11], [88879, 184], [89089, 11], [89179, 184], [89389, 11], [89479, 184], [89689, 11], [89779, 185], [89989, 11]], "point": [189, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-02.45|+01.95|+02.93"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [79, 1, 299, 300], "mask": [[284, 16], [584, 16], [884, 16], [1183, 17], [1483, 17], [1782, 18], [2082, 18], [2381, 19], [2681, 19], [2980, 20], [3280, 20], [3579, 21], [3879, 21], [4179, 21], [4478, 22], [4778, 22], [5077, 23], [5377, 23], [5676, 24], [5976, 24], [6275, 25], [6575, 25], [6875, 25], [7174, 26], [7474, 26], [7773, 27], [8073, 27], [8372, 28], [8672, 28], [8971, 29], [9271, 29], [9570, 30], [9870, 30], [10170, 30], [10469, 31], [10769, 31], [11068, 32], [11368, 32], [11667, 33], [11967, 33], [12266, 34], [12566, 34], [12866, 34], [13165, 35], [13465, 35], [13764, 36], [14064, 36], [14363, 37], [14663, 37], [14962, 38], [15262, 38], [15562, 38], [15861, 39], [16161, 39], [16460, 40], [16760, 40], [17059, 41], [17359, 41], [17658, 42], [17958, 42], [18257, 43], [18557, 43], [18858, 42], [19007, 193], [19307, 193], [19607, 193], [19907, 193], [20206, 194], [20506, 194], [20806, 194], [21106, 194], [21406, 194], [21706, 194], [22006, 194], [22306, 194], [22606, 194], [22905, 195], [23205, 195], [23505, 195], [23805, 195], [24105, 195], [24405, 195], [24705, 195], [25005, 195], [25304, 196], [25604, 196], [25904, 196], [26204, 196], [26504, 196], [26804, 196], [27104, 196], [27404, 196], [27704, 196], [28003, 197], [28303, 197], [28603, 197], [28903, 197], [29203, 197], [29503, 197], [29803, 197], [30103, 197], [30402, 198], [30702, 198], [31002, 198], [31302, 198], [31602, 198], [31902, 198], [32202, 198], [32502, 198], [32802, 198], [33101, 199], [33401, 199], [33701, 199], [34001, 199], [34301, 199], [34601, 199], [34901, 199], [35201, 199], [35500, 200], [35800, 200], [36100, 200], [36400, 200], [36700, 200], [37000, 200], [37300, 200], [37600, 200], [37900, 200], [38199, 201], [38499, 201], [38799, 201], [39099, 201], [39399, 201], [39699, 201], [39999, 201], [40299, 201], [40598, 202], [40898, 202], [41198, 202], [41498, 202], [41798, 202], [42098, 202], [42398, 202], [42698, 202], [42997, 203], [43297, 203], [43597, 203], [43897, 203], [44197, 203], [44497, 203], [44797, 203], [45097, 203], [45397, 203], [45696, 204], [45996, 204], [46296, 204], [46596, 204], [46896, 204], [47196, 204], [47496, 204], [47796, 204], [48095, 205], [48395, 205], [48695, 205], [48995, 205], [49295, 205], [49595, 205], [49895, 205], [50195, 205], [50495, 205], [50794, 206], [51094, 206], [51394, 206], [51694, 206], [51994, 206], [52294, 206], [52594, 206], [52894, 206], [53193, 207], [53493, 207], [53793, 207], [54093, 207], [54393, 207], [54693, 207], [54993, 207], [55293, 207], [55593, 207], [55892, 208], [56192, 208], [56492, 208], [56792, 208], [57092, 208], [57392, 208], [57692, 208], [57992, 208], [58291, 209], [58591, 209], [58891, 209], [59191, 209], [59491, 209], [59791, 209], [60091, 209], [60391, 209], [60691, 209], [60990, 210], [61290, 210], [61590, 210], [61890, 210], [62190, 210], [62490, 210], [62790, 210], [63090, 210], [63389, 211], [63689, 211], [63989, 211], [64289, 211], [64589, 211], [64889, 211], [65189, 211], [65489, 211], [65788, 212], [66088, 212], [66388, 212], [66688, 212], [66988, 212], [67288, 212], [67588, 212], [67888, 212], [68188, 212], [68487, 213], [68787, 213], [69087, 213], [69387, 213], [69687, 213], [69987, 213], [70287, 213], [70587, 213], [70886, 214], [71186, 214], [71486, 214], [71786, 214], [72086, 214], [72386, 214], [72686, 177], [72867, 33], [72986, 176], [73167, 33], [73286, 176], [73470, 30], [73585, 174], [73774, 26], [73885, 172], [74076, 24], [74185, 170], [74377, 23], [74485, 170], [74678, 22], [74785, 169], [74978, 22], [75085, 169], [75279, 21], [75385, 169], [75579, 21], [75685, 169], [75879, 21], [75984, 170], [76179, 21], [76284, 171], [76479, 21], [76584, 171], [76778, 22], [76884, 172], [77077, 23], [77184, 174], [77378, 22], [77484, 174], [77678, 22], [77784, 174], [77978, 22], [78084, 174], [78278, 22], [78384, 174], [78579, 21], [78683, 176], [78879, 21], [78983, 176], [79179, 21], [79283, 176], [79479, 21], [79583, 176], [79780, 20], [79883, 176], [80080, 20], [80183, 176], [80380, 20], [80483, 177], [80680, 20], [80783, 177], [80981, 19], [81082, 178], [81281, 19], [81382, 178], [81581, 19], [81682, 178], [81881, 19], [81982, 47], [82054, 107], [82182, 18], [82282, 43], [82358, 103], [82482, 18], [82582, 43], [82658, 103], [82782, 18], [82882, 43], [82958, 103], [83083, 17], [83182, 43], [83258, 103], [83383, 17], [83482, 43], [83558, 103], [83683, 17], [83781, 44], [83858, 103], [83984, 16], [84081, 44], [84158, 103], [84284, 16], [84381, 44], [84458, 103], [84585, 15], [84681, 44], [84758, 103], [84885, 15], [84981, 44], [85058, 103], [85185, 15], [85281, 44], [85358, 103], [85486, 14], [85581, 44], [85658, 103], [85786, 14], [85881, 44], [85958, 103], [86087, 13], [86180, 45], [86258, 103], [86387, 13], [86480, 45], [86558, 103], [86687, 13], [86780, 45], [86858, 103], [86987, 13], [87080, 45], [87158, 104], [87288, 12], [87380, 45], [87458, 104], [87588, 12], [87680, 45], [87758, 104], [87888, 12], [87980, 45], [88058, 104], [88188, 12], [88280, 45], [88358, 105], [88488, 12], [88580, 45], [88658, 105], [88789, 11], [88879, 46], [88958, 105], [89089, 11], [89179, 46], [89258, 105], [89389, 11], [89479, 46], [89558, 105], [89689, 11], [89779, 46], [89858, 106], [89989, 11]], "point": [189, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan6", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 0.5, "y": 0.9009992, "z": 2.5}, "object_poses": [{"objectName": "DishSponge_1f5d88fd", "position": {"x": -2.25749445, "y": 0.7729672, "z": -0.0254001468}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": 1.47762811, "y": 0.9211553, "z": -1.25529552}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": -0.08153126, "y": 0.9109421, "z": 1.76401663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_0884e440", "position": {"x": 1.4123, "y": 0.9295, "z": -0.3359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": -0.08153126, "y": 0.911448538, "z": 0.423983335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": 1.68401384, "y": 0.9122294, "z": 0.406000018}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": 1.84612942, "y": 0.9120293, "z": -0.89784646}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": -0.211414054, "y": 0.925899863, "z": 0.870661139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": 1.59833741, "y": 0.9417285, "z": 0.4652969}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": -2.538456, "y": 1.02850568, "z": 2.3803}, "rotation": {"x": 0.0, "y": 89.99982, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": -2.57825041, "y": 1.29822564, "z": -0.8853781}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": -2.63846517, "y": 1.53581285, "z": 2.12945318}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": -2.2879, "y": 0.799445331, "z": 1.32221115}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_9b863229", "position": {"x": -0.6010624, "y": 0.9532293, "z": 0.423983335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": 1.35352933, "y": 0.924196362, "z": -1.23936892}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": -2.68515635, "y": 1.53261185, "z": 1.48311257}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -0.08153126, "y": 0.994053841, "z": 1.31733882}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_209e2ea9", "position": {"x": 0.0483515263, "y": 0.996719658, "z": 0.423983335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_db7f1139", "position": {"x": -2.485795, "y": 1.2954129, "z": -0.7909757}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": -2.43956757, "y": 1.36202419, "z": -0.5077681}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": -2.43956757, "y": 1.36202419, "z": -0.9797808}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_adb25946", "position": {"x": 0.0, "y": 0.9, "z": 0.948}, "rotation": {"x": 0.0, "y": 29.9999466, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": -0.341296852, "y": 0.9362496, "z": 0.6473222}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": -2.466795, "y": 0.6563896, "z": -0.514033556}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.6099, "y": 1.9309833, "z": -0.9765876}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": -2.45561719, "y": 0.911448538, "z": 0.9311123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_209e2ea9", "position": {"x": -2.289484, "y": 0.996719658, "z": 0.9311123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_7b0d90af", "position": {"x": 1.7355001, "y": 1.66621935, "z": -0.3350352}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": 1.52019107, "y": 0.21585986, "z": 0.6239377}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_0884e440", "position": {"x": 0.8544904, "y": 0.900000036, "z": -1.790359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_b563bb4b", "position": {"x": -2.20641732, "y": 0.9472332, "z": 1.830605}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": 0.445000052, "y": 1.65874982, "z": -1.7664001}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": 0.0483515263, "y": 0.994053841, "z": 0.6473222}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": 1.42669582, "y": 0.9164428, "z": -1.09619033}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": 1.5166707, "y": 0.9128651, "z": -0.7152878}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": -0.211414054, "y": 0.925899863, "z": 1.76401663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_40552007", "position": {"x": -2.704817, "y": 1.01518536, "z": 0.331450462}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": -2.62175035, "y": 0.9109421, "z": 0.03161955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": -2.564922, "y": 1.53261185, "z": 2.84076881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": 1.12799323, "y": 0.770697355, "z": -1.39473009}, "rotation": {"x": 1.40334208e-14, "y": 225.000031, "z": 0.0}}, {"objectName": "Pot_bc458cba", "position": {"x": 1.6916, "y": 0.9295, "z": 0.0642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": 1.38372624, "y": 0.9197595, "z": -0.9991344}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "Tomato_9b863229", "position": {"x": -2.485795, "y": 1.13154221, "z": -0.885378242}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_db7f1139", "position": {"x": -2.67743039, "y": 1.53300011, "z": 0.7020001}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1852149693, "scene_num": 6}, "task_id": "trial_T20190908_034330_736332", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AJQGWGESKQT4Y_3018Q3ZVOL79KAATAKMVSTAPWKTARH", "high_descs": ["Go left and then left again and then turn to face the counter with the bread on it and look up to the cabinets above.", "Open the cabinet and take the mug out of the cabinet.", "Go to the right and turn to the left to face the microwave.", "Put the cup in the microwave and shut the door, then open the door and take the mug out and shut the door.", "Look up to the cabinet on the right, above the microwave.", "Open the cabinet on the right, above the microwave and put the mug in and shut the door."], "task_desc": "Put a warmed mug in the cabinet.", "votes": [1, 1, 1]}, {"assignment_id": "AKW57KYG90X61_33M4IA01QJILGNIMLUE9C1LNX5ORXO", "high_descs": ["turn left and locate the overhead cupboard", "take out the mug ", "turn right and locate the oven", "place the mug to heat and then take it out", "head for the cupboard over the oven", "place the mug inside the cupboard"], "task_desc": "place a hot mug inside the cupboard", "votes": [1, 1, 1]}, {"assignment_id": "A1P0XSCJ9XAV74_3TE3O85733PKF7FPX5TGMBRRKWER24", "high_descs": ["Go to the left and take a step. Turn to the left and walk to the end of the counter and turn to the right. Walk straight ahead to the counter and look up at the cabinet.", "Take out the mug from the cabinet.", "Turn around and then back to the left to walk towards the wall. Make a left so you are in front of the microwave.", "Open the microwave and place the mug off to the right and shut the door. When it's done take the mug back out.", "Raise the mug up to the cabinet above the microwave.", "Place the mug inside the cabinet and shut the door."], "task_desc": "To move the mug from the cabinet to the microwave into another cabinet.", "votes": [1, 0, 1]}]}}