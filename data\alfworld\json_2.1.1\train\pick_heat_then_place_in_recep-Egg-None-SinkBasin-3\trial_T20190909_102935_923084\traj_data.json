{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 49}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "SinkBasin", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-2|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-7.56172512, -7.56172512, -1.621660232, -1.621660232, 4.36749648, 4.36749648]], "coordinateReceptacleObjectId": ["SinkBasin", [-7.34220268, -7.34220268, -2.168930532, -2.168930532, 4.16, 4.16]], "forceVisible": true, "objectId": "Egg|-01.89|+01.09|-00.41"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|3|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "sinkbasin"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-7.56172512, -7.56172512, -1.621660232, -1.621660232, 4.36749648, 4.36749648]], "coordinateReceptacleObjectId": ["SinkBasin", [-7.34220268, -7.34220268, -2.168930532, -2.168930532, 4.16, 4.16]], "forceVisible": true, "objectId": "Egg|-01.89|+01.09|-00.41", "receptacleObjectId": "Sink|-01.99|+01.14|-00.98|SinkBasin"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.89|+01.09|-00.41"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [164, 111, 179, 129], "mask": [[33172, 1], [33469, 6], [33767, 10], [34067, 10], [34366, 12], [34665, 14], [34965, 14], [35264, 15], [35564, 15], [35864, 16], [36164, 16], [36464, 15], [36764, 15], [37064, 15], [37365, 14], [37665, 13], [37966, 11], [38267, 9], [38569, 6]], "point": [171, 119]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28219, 195], [28518, 197], [28817, 199], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.89|+01.09|-00.41", "placeStationary": true, "receptacleObjectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 211], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 94], [33102, 113], [33300, 87], [33408, 107], [33600, 83], [33711, 104], [33900, 80], [34013, 102], [34200, 78], [34314, 100], [34500, 76], [34614, 100], [34800, 75], [34915, 99], [35100, 74], [35215, 99], [35400, 74], [35515, 99], [35700, 73], [35815, 99], [36000, 73], [36115, 99], [36300, 73], [36415, 98], [36600, 74], [36715, 98], [36900, 74], [37015, 98], [37200, 74], [37315, 98], [37500, 74], [37615, 98], [37800, 74], [37915, 98], [38100, 75], [38215, 98], [38400, 75], [38514, 99], [38700, 76], [38814, 98], [39000, 76], [39114, 98], [39300, 76], [39413, 99], [39600, 77], [39713, 99], [39900, 77], [40012, 100], [40200, 78], [40312, 100], [40500, 79], [40611, 101], [40800, 80], [40911, 101], [41100, 80], [41210, 101], [41400, 81], [41509, 102], [41700, 82], [41808, 103], [42000, 84], [42108, 103], [42300, 85], [42407, 104], [42600, 86], [42705, 106], [42900, 87], [43003, 108], [43200, 23], [43224, 66], [43300, 110], [43500, 22], [43525, 185], [43800, 22], [43825, 185], [44100, 21], [44126, 184], [44400, 21], [44427, 183], [44700, 21], [44727, 183], [45000, 21], [45027, 183], [45300, 21], [45327, 183], [45600, 21], [45628, 181], [45900, 21], [45928, 181], [46200, 21], [46228, 181], [46500, 21], [46528, 181], [46800, 22], [46829, 180], [47100, 22], [47129, 180], [47400, 22], [47429, 180], [47700, 23], [47730, 178], [48000, 23], [48030, 178], [48300, 23], [48330, 178], [48600, 24], [48630, 178], [48900, 24], [48930, 178], [49200, 24], [49229, 179], [49500, 24], [49528, 180], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 25], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 14], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 211], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 94], [33102, 113], [33300, 87], [33408, 107], [33600, 83], [33711, 104], [33900, 80], [34013, 102], [34200, 78], [34314, 100], [34500, 76], [34614, 100], [34800, 75], [34915, 99], [35100, 74], [35215, 99], [35400, 74], [35515, 99], [35700, 73], [35815, 99], [36000, 73], [36115, 99], [36300, 73], [36415, 98], [36600, 74], [36715, 98], [36900, 74], [37015, 98], [37200, 74], [37315, 98], [37500, 74], [37615, 98], [37800, 74], [37915, 17], [37936, 77], [38100, 75], [38215, 15], [38238, 75], [38400, 75], [38514, 14], [38539, 74], [38700, 76], [38814, 14], [38840, 72], [39000, 76], [39114, 13], [39140, 72], [39300, 76], [39413, 13], [39441, 71], [39600, 77], [39713, 13], [39742, 70], [39900, 77], [40012, 14], [40042, 70], [40200, 78], [40312, 13], [40342, 70], [40500, 79], [40611, 14], [40643, 69], [40800, 80], [40911, 14], [40943, 69], [41100, 80], [41210, 15], [41243, 68], [41400, 81], [41509, 16], [41543, 68], [41700, 82], [41808, 17], [41843, 68], [42000, 84], [42108, 17], [42143, 68], [42300, 85], [42407, 18], [42442, 69], [42600, 86], [42705, 21], [42742, 69], [42900, 87], [43003, 23], [43042, 69], [43200, 23], [43224, 66], [43300, 27], [43341, 69], [43500, 22], [43525, 102], [43640, 70], [43800, 22], [43825, 103], [43939, 71], [44100, 21], [44126, 104], [44238, 72], [44400, 21], [44427, 105], [44536, 74], [44700, 21], [44727, 183], [45000, 21], [45027, 183], [45300, 21], [45327, 183], [45600, 21], [45628, 181], [45900, 21], [45928, 181], [46200, 21], [46228, 181], [46500, 21], [46528, 181], [46800, 22], [46829, 180], [47100, 22], [47129, 180], [47400, 22], [47429, 180], [47700, 23], [47730, 178], [48000, 23], [48030, 178], [48300, 23], [48330, 178], [48600, 24], [48630, 178], [48900, 24], [48930, 178], [49200, 24], [49229, 179], [49500, 24], [49528, 180], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 25], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 14], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28219, 195], [28518, 197], [28817, 199], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28219, 195], [28518, 197], [28817, 199], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28219, 195], [28518, 197], [28817, 199], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.89|+01.09|-00.41"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [125, 127, 142, 149], "mask": [[37932, 4], [38230, 8], [38528, 11], [38828, 12], [39127, 13], [39426, 15], [39726, 16], [40026, 16], [40325, 17], [40625, 18], [40925, 18], [41225, 18], [41525, 18], [41825, 18], [42125, 18], [42425, 17], [42726, 16], [43026, 16], [43327, 14], [43627, 13], [43928, 11], [44230, 8], [44532, 4]], "point": [133, 137]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 211], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 94], [33102, 113], [33300, 87], [33408, 107], [33600, 83], [33711, 104], [33900, 80], [34013, 102], [34200, 78], [34314, 100], [34500, 76], [34614, 100], [34800, 75], [34915, 99], [35100, 74], [35215, 99], [35400, 74], [35515, 99], [35700, 73], [35815, 99], [36000, 73], [36115, 99], [36300, 73], [36415, 98], [36600, 74], [36715, 98], [36900, 74], [37015, 98], [37200, 74], [37315, 98], [37500, 74], [37615, 98], [37800, 74], [37915, 98], [38100, 75], [38215, 98], [38400, 75], [38514, 99], [38700, 76], [38814, 98], [39000, 76], [39114, 98], [39300, 76], [39413, 99], [39600, 77], [39713, 99], [39900, 77], [40012, 100], [40200, 78], [40312, 100], [40500, 79], [40611, 101], [40800, 80], [40911, 101], [41100, 80], [41210, 101], [41400, 81], [41509, 102], [41700, 82], [41808, 103], [42000, 84], [42108, 103], [42300, 85], [42407, 104], [42600, 86], [42705, 106], [42900, 87], [43003, 108], [43200, 23], [43224, 66], [43300, 110], [43500, 22], [43525, 185], [43800, 22], [43825, 185], [44100, 21], [44126, 184], [44400, 21], [44427, 183], [44700, 21], [44727, 183], [45000, 21], [45027, 183], [45300, 21], [45327, 183], [45600, 21], [45628, 181], [45900, 21], [45928, 181], [46200, 21], [46228, 181], [46500, 21], [46528, 181], [46800, 22], [46829, 180], [47100, 22], [47129, 180], [47400, 22], [47429, 180], [47700, 23], [47730, 178], [48000, 23], [48030, 178], [48300, 23], [48330, 178], [48600, 24], [48630, 178], [48900, 24], [48930, 178], [49200, 24], [49229, 179], [49500, 24], [49528, 180], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 25], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 14], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.89|+01.09|-00.41", "placeStationary": true, "receptacleObjectId": "Sink|-01.99|+01.14|-00.98|SinkBasin"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [152, 99, 256, 149], "mask": [[29562, 63], [29860, 69], [30158, 73], [30456, 77], [30755, 79], [31054, 82], [31353, 37], [31395, 42], [31653, 37], [31695, 43], [31952, 39], [31995, 44], [32252, 39], [32295, 44], [32552, 39], [32595, 45], [32852, 40], [32895, 46], [33152, 40], [33195, 46], [33452, 41], [33495, 47], [33752, 22], [33778, 15], [33796, 46], [34052, 21], [34079, 1], [34081, 13], [34096, 46], [34352, 18], [34383, 11], [34396, 47], [34652, 17], [34685, 10], [34696, 47], [34952, 16], [34985, 10], [34996, 48], [35252, 16], [35286, 9], [35297, 47], [35552, 15], [35586, 8], [35597, 47], [35852, 15], [35886, 8], [35898, 47], [36152, 15], [36186, 7], [36199, 46], [36452, 15], [36486, 6], [36500, 46], [36752, 15], [36787, 5], [36801, 45], [37052, 14], [37087, 5], [37101, 46], [37352, 15], [37387, 5], [37401, 46], [37652, 15], [37687, 6], [37701, 46], [37952, 15], [37986, 8], [38001, 47], [38252, 15], [38286, 10], [38299, 49], [38552, 16], [38586, 63], [38852, 17], [38885, 64], [39152, 17], [39184, 65], [39452, 18], [39483, 67], [39752, 20], [39776, 2], [39781, 69], [40052, 99], [40352, 99], [40652, 99], [40952, 100], [41252, 100], [41552, 101], [41852, 47], [41900, 53], [42153, 45], [42201, 53], [42456, 42], [42502, 52], [42758, 40], [42802, 52], [43060, 38], [43103, 52], [43363, 35], [43403, 52], [43667, 31], [43703, 53], [43971, 28], [44003, 53], [44275, 24], [44303, 53], [44578, 22], [44603, 54]], "point": [204, 123]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan3", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.0, "y": 1.12401652, "z": 2.0}, "object_poses": [{"objectName": "Pan_9d168802", "position": {"x": -1.6817342, "y": 0.343423635, "z": 0.6130607}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": -1.61458409, "y": 0.343378425, "z": 1.00736094}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": 0.5068307, "y": 1.32392883, "z": -1.312883}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.74942863, "y": 0.3447429, "z": 0.412196}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -1.79031563, "y": 1.32404137, "z": 1.74455106}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -1.95454407, "y": 1.0521791, "z": -0.542232633}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -1.60347974, "y": 0.4237007, "z": -1.348922}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.53081059, "y": 1.33799994, "z": -1.68147337}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.40105808, "y": 1.33799994, "z": -2.461287}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": 0.492037773, "y": 1.3294332, "z": -3.232851}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.2394, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.89043128, "y": 1.09187412, "z": -0.405415058}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.40105689, "y": 1.36382449, "z": 0.770108938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.66056263, "y": 1.36382449, "z": 0.120481014}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -1.68255043, "y": 0.344428062, "z": 1.52389443}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -2.00441241, "y": 1.36631966, "z": -1.11794019}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.79031563, "y": 1.35270047, "z": -1.68147337}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": 1.02731991, "y": 1.20799184, "z": 1.2225256}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.61049628, "y": 0.280376852, "z": 2.110548}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 1.07256258, "y": 1.55844033, "z": 1.78658593}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": -1.89043128, "y": 1.08497071, "z": -0.633444369}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -2.04982138, "y": 1.32130814, "z": 1.094923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": -1.913827, "y": 0.338814139, "z": -1.86363292}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": -1.92006814, "y": 1.39739525, "z": -2.981163}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.40105689, "y": 1.39912164, "z": 1.094923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.9710001, "y": 1.51951933, "z": 1.539174}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.79031563, "y": 1.37188375, "z": 0.770108938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": -1.79031563, "y": 1.39739525, "z": 1.4197371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.6309, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.66056263, "y": 1.35270047, "z": 0.445294976}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": 1.18117619, "y": 1.30871868, "z": 0.965037942}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": -2.04982066, "y": 1.36275363, "z": -2.20134926}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -2.179573, "y": 1.32319188, "z": -2.461287}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -2.00441241, "y": 1.33716774, "z": -1.25536227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": -1.6140399, "y": 0.341211677, "z": 0.110898912}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -0.125, "y": 1.3324, "z": -2.973}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.91546834, "y": 1.37627959, "z": -1.25536227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_9d168802", "position": {"x": -1.88114953, "y": 0.327762932, "z": -2.03532434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": 0.900508642, "y": 0.339971542, "z": -1.75116086}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.66056263, "y": 1.32392883, "z": 0.770108938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.4514004, "y": 1.39912164, "z": -2.870881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.648636, "y": 1.343986, "z": -1.3240732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_ddd73f57", "position": {"x": 0.9443453, "y": 1.31208539, "z": 1.25445545}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 0.420673847, "y": 1.35692108, "z": -2.90023613}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": 0.6365833, "y": 1.323614, "z": -2.05370474}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": 1.19326913, "y": 1.2028, "z": 1.23849058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": -1.363688, "y": 1.32256436, "z": -0.09864402}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -1.6980927, "y": 1.0521791, "z": -0.542232633}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.61049628, "y": 0.32906872, "z": 2.23422623}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.965999663, "y": 1.3713994, "z": -1.53499949}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 2322749159, "scene_num": 3}, "task_id": "trial_T20190909_102935_923084", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A31681CCEVDIH3_3DR23U6WE85XBRQVQMITPDL0ZJ3ET3", "high_descs": ["Make a left to walk to the sink.", "Pick up the egg from the sink.", "Turn around to walk to the microwave.", "Heat up the egg in the microwave, removing it afterwards.", "Turn around to walk to the sink.", "Put the egg back in the sink."], "task_desc": "Heat up an egg in the microwave to put back in the sink.", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3X87C8JFV92LTU3LAD839PESG3SQSH", "high_descs": ["Turn left and walk forward, then turn right to face the sink.", "Pick up the egg out of the sink.", "Turn right and walk forward, then turn right and walk over to the microwave.", "Open the microwave and put the egg inside then close the door and turn on the microwave, after a couple seconds open the microwave back up and take the now heated egg out, then close the microwave.", "Turn right and walk forward, then hang a right and walk up to the kitchen sink.", "Put the heated egg in the sink basin."], "task_desc": "Put a heated egg in the sink.", "votes": [1, 1]}, {"assignment_id": "A2871R3LEPWMMK_3XXU1SWE8PMT55CSCFSX9JT4HT6A0Q", "high_descs": ["Turn left and walk to the sink on the right.", "Pick up the egg from the right side of the sink.", "Turn right and walk to the black stand with the microwave to the right.", "Put the egg inside the microwave, heat it, remove it, and close the door.", "Turn right and walk to the sink on the right.", "Put the heated egg on the right side of the sink."], "task_desc": "Place a heated egg inside a sink.", "votes": [1, 1]}]}}