{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 31}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-2|4|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-4.77667524, -4.77667524, 4.37075092, 4.37075092, 1.3632348, 1.3632348]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-5.204, -5.204, 3.952, 3.952, 0.032000004, 0.032000004]], "forceVisible": true, "objectId": "Cup|-01.19|+00.34|+01.09"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-4.77667524, -4.77667524, 4.37075092, 4.37075092, 1.3632348, 1.3632348]], "coordinateReceptacleObjectId": ["CounterTop", [-4.0076, -4.0076, -2.676, -2.676, 3.7904, 3.7904]], "forceVisible": true, "objectId": "Cup|-01.19|+00.34|+01.09", "receptacleObjectId": "CounterTop|-01.00|+00.95|-00.67"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 233], "mask": [[0, 56399], [56400, 298], [56700, 297], [57000, 295], [57300, 294], [57601, 292], [57902, 289], [58204, 286], [58505, 284], [58807, 280], [59108, 278], [59409, 276], [59711, 272], [60012, 270], [60314, 267], [60615, 264], [60916, 262], [61218, 259], [61519, 256], [61821, 253], [62122, 251], [62423, 248], [62725, 245], [63026, 243], [63327, 240], [63629, 237], [63930, 235], [64232, 231], [64533, 229], [64834, 227], [65136, 223], [65437, 221], [65739, 218], [66040, 215], [66341, 213], [66643, 210], [66944, 207], [67246, 204], [67547, 202], [67848, 199], [68150, 196], [68451, 194], [68752, 191], [69054, 188], [69355, 186], [69657, 182]], "point": [149, 116]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.19|+00.34|+01.09"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [156, 145, 179, 159], "mask": [[43356, 24], [43656, 24], [43957, 23], [44257, 22], [44558, 21], [44858, 20], [45159, 18], [45459, 17], [45760, 15], [46060, 15], [46361, 13], [46661, 12], [46962, 10], [47263, 9], [47564, 6]], "point": [167, 151]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 22306], [22339, 13], [22454, 152], [22639, 13], [22658, 5], [22743, 4], [22754, 152], [22939, 12], [22957, 6], [23044, 4], [23055, 151], [23239, 12], [23256, 7], [23344, 6], [23356, 150], [23539, 12], [23556, 7], [23644, 6], [23656, 150], [23839, 12], [23855, 8], [23944, 7], [23956, 150], [24139, 12], [24156, 7], [24245, 6], [24257, 149], [24438, 13], [24456, 7], [24545, 6], [24556, 151], [24738, 13], [24757, 6], [24845, 6], [24856, 151], [25037, 15], [25059, 4], [25145, 4], [25156, 152], [25337, 15], [25456, 153], [25636, 17], [25755, 155], [25935, 19], [26054, 157], [26234, 22], [26353, 159], [26533, 25], [26650, 163], [26832, 31], [26940, 1], [26946, 168], [27131, 36], [27239, 177], [27429, 38], [27539, 180], [27726, 41], [27838, 229], [28137, 230], [28436, 231], [28736, 231], [29035, 232], [29335, 232], [29634, 233], [29934, 234], [30234, 234], [30533, 235], [30833, 235], [31132, 237], [31432, 237], [31731, 239], [32031, 239], [32330, 241], [32630, 242], [32930, 242], [33229, 244], [33528, 246], [33827, 248], [34127, 249], [34426, 251], [34725, 253], [35024, 256], [35322, 259], [35621, 262], [35919, 266], [36217, 271], [36515, 276], [36812, 283], [37108, 6209], [43335, 165], [43501, 116], [43635, 165], [43802, 115], [43936, 164], [44103, 115], [44236, 164], [44404, 114], [44536, 164], [44705, 113], [44836, 164], [45006, 112], [45136, 164], [45307, 111], [45436, 164], [45608, 111], [45736, 164], [45909, 110], [46036, 164], [46210, 109], [46336, 164], [46511, 108], [46636, 164], [46812, 107], [46936, 164], [47113, 107], [47237, 163], [47414, 106], [47537, 163], [47715, 105], [47836, 164], [48016, 104], [48136, 164], [48317, 104], [48436, 164], [48618, 103], [48735, 165], [48919, 103], [49034, 166], [49220, 104], [49333, 167], [49521, 105], [49630, 170], [49822, 278], [50123, 277], [50424, 276], [50724, 276], [51025, 275], [51326, 274], [51627, 273], [51928, 272], [52229, 271], [52530, 270], [52831, 269], [53132, 268], [53433, 267], [53734, 266], [54035, 265], [54336, 264], [54637, 263], [54938, 262], [55239, 261], [55540, 108], [55652, 148], [55841, 99], [55960, 140], [56142, 93], [56265, 135], [56443, 89], [56568, 132], [56744, 85], [56871, 129], [57045, 82], [57173, 127], [57346, 78], [57476, 124], [57647, 75], [57778, 122], [57948, 73], [58079, 121], [58249, 70], [58381, 119], [58549, 69], [58682, 118], [58850, 66], [58984, 116], [59151, 64], [59285, 115], [59452, 61], [59587, 113], [59753, 59], [59888, 112], [60054, 57], [60189, 111], [60355, 55], [60490, 110], [60656, 53], [60791, 109], [60957, 51], [61092, 108], [61258, 49], [61393, 107], [61559, 47], [61694, 106], [61860, 45], [61995, 105], [62162, 43], [62295, 39], [62335, 65], [62635, 65], [62935, 65], [63235, 65], [63535, 65], [63835, 65], [64135, 65], [64436, 64], [64736, 64], [65036, 64], [65336, 64], [65636, 64], [65936, 64], [66237, 63], [66537, 63], [66837, 63], [67137, 63], [67437, 63], [67737, 63], [68038, 62], [68338, 62], [68638, 62], [68938, 62], [69238, 62], [69538, 62], [69839, 61], [70139, 61], [70439, 61], [70739, 61], [71039, 61], [71339, 61], [71640, 60], [71940, 60], [72240, 60], [72540, 60], [72840, 60], [73140, 60], [73441, 59], [73741, 59], [74041, 59], [74341, 59], [74641, 59], [74941, 59], [75242, 58], [75542, 58], [75842, 58], [76142, 58], [76442, 58], [76742, 58], [77043, 57], [77343, 57], [77643, 57], [77943, 57], [78243, 57], [78543, 57], [78844, 56], [79144, 56], [79444, 56], [79744, 56], [80044, 56], [80344, 56], [80645, 55], [80945, 55], [81245, 55], [81545, 55], [81845, 55], [82145, 55], [82446, 54], [82746, 54], [83046, 54], [83346, 54], [83646, 54], [83946, 54], [84247, 53], [84547, 53], [84847, 53], [85147, 53], [85447, 53], [85747, 53], [86048, 52], [86348, 52], [86648, 52], [86948, 52], [87248, 52], [87548, 52], [87849, 51], [88149, 51], [88449, 51], [88749, 51], [89049, 51], [89349, 51], [89650, 50], [89950, 50]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.19|+00.34|+01.09", "placeStationary": true, "receptacleObjectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 241], [28529, 241], [28829, 241], [29128, 241], [29427, 242], [29727, 242], [30026, 242], [30326, 242], [30625, 243], [30924, 243], [31224, 243], [31523, 243], [31823, 243], [32122, 244], [32421, 244], [32721, 244], [33020, 244], [33320, 244], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 92], [19959, 122], [20146, 90], [20261, 120], [20445, 89], [20563, 118], [20744, 89], [20864, 116], [21044, 88], [21165, 115], [21343, 88], [21466, 113], [21643, 88], [21766, 113], [21942, 88], [22066, 113], [22241, 89], [22366, 112], [22541, 89], [22666, 112], [22840, 91], [22966, 111], [23140, 91], [23266, 111], [23439, 91], [23566, 111], [23739, 91], [23866, 110], [24038, 92], [24166, 110], [24337, 94], [24466, 110], [24637, 94], [24766, 109], [24936, 95], [25066, 109], [25236, 95], [25365, 109], [25535, 97], [25665, 109], [25834, 99], [25964, 110], [26134, 99], [26264, 109], [26433, 100], [26563, 110], [26733, 101], [26863, 109], [27032, 102], [27163, 109], [27331, 103], [27462, 110], [27631, 104], [27762, 109], [27930, 105], [28062, 109], [28230, 105], [28362, 109], [28529, 107], [28661, 109], [28829, 107], [28961, 109], [29128, 108], [29261, 108], [29427, 110], [29560, 109], [29727, 110], [29860, 109], [30026, 111], [30160, 108], [30326, 111], [30459, 109], [30625, 113], [30759, 109], [30924, 114], [31059, 108], [31224, 114], [31359, 108], [31523, 116], [31658, 108], [31823, 116], [31958, 108], [32122, 117], [32258, 108], [32421, 119], [32557, 108], [32721, 119], [32857, 108], [33020, 122], [33155, 109], [33320, 124], [33453, 111], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.19|+00.34|+01.09"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [130, 67, 165, 112], "mask": [[19938, 21], [20236, 25], [20534, 29], [20833, 31], [21132, 33], [21431, 35], [21731, 35], [22030, 36], [22330, 36], [22630, 36], [22931, 35], [23231, 35], [23530, 36], [23830, 36], [24130, 36], [24431, 35], [24731, 35], [25031, 35], [25331, 34], [25632, 33], [25933, 31], [26233, 31], [26533, 30], [26834, 29], [27134, 29], [27434, 28], [27735, 27], [28035, 27], [28335, 27], [28636, 25], [28936, 25], [29236, 25], [29537, 23], [29837, 23], [30137, 23], [30437, 22], [30738, 21], [31038, 21], [31338, 21], [31639, 19], [31939, 19], [32239, 19], [32540, 17], [32840, 17], [33142, 13], [33444, 9]], "point": [147, 88]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 241], [28529, 241], [28829, 241], [29128, 241], [29427, 242], [29727, 242], [30026, 242], [30326, 242], [30625, 243], [30924, 243], [31224, 243], [31523, 243], [31823, 243], [32122, 244], [32421, 244], [32721, 244], [33020, 244], [33320, 244], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.19|+00.34|+01.09", "placeStationary": true, "receptacleObjectId": "CounterTop|-01.00|+00.95|-00.67"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 103, 299, 239], "mask": [[30603, 26], [30638, 2], [30645, 255], [30903, 26], [30939, 2], [30946, 254], [31204, 25], [31246, 254], [31505, 24], [31547, 253], [31805, 25], [31847, 283], [32147, 284], [32447, 284], [32747, 284], [33042, 2], [33046, 286], [33342, 290], [33643, 290], [33943, 290], [34244, 289], [34545, 288], [34845, 288], [35144, 289], [35443, 292], [35741, 226], [35977, 289], [36279, 286], [36581, 284], [36882, 18], [37008, 156], [37183, 17], [37309, 156], [37483, 17], [37609, 155], [37784, 16], [37909, 155], [38084, 16], [38209, 154], [38383, 17], [38509, 154], [38683, 17], [38808, 154], [38982, 18], [39108, 154], [39281, 19], [39408, 153], [39581, 19], [39708, 153], [39880, 20], [40008, 152], [40180, 20], [40308, 151], [40480, 20], [40607, 151], [40779, 21], [40907, 149], [41079, 21], [41207, 147], [41379, 21], [41507, 146], [41678, 22], [41807, 144], [41978, 22], [42107, 143], [42277, 23], [42406, 143], [42578, 22], [42706, 142], [42878, 22], [43006, 141], [43178, 22], [43306, 140], [43479, 21], [43606, 139], [43779, 21], [43905, 139], [44079, 21], [44205, 139], [44379, 21], [44505, 138], [44680, 20], [44805, 137], [44980, 20], [45105, 137], [45280, 20], [45405, 136], [45580, 20], [45704, 136], [45880, 20], [46004, 136], [46180, 20], [46304, 135], [46480, 20], [46604, 135], [46780, 20], [46904, 135], [47080, 20], [47204, 134], [47380, 20], [47503, 135], [47680, 20], [47803, 135], [47980, 20], [48103, 134], [48280, 20], [48403, 134], [48579, 21], [48703, 134], [48879, 21], [49003, 134], [49179, 21], [49302, 134], [49479, 21], [49602, 134], [49779, 21], [49902, 134], [50078, 22], [50202, 43], [50255, 80], [50378, 22], [50502, 35], [50563, 72], [50678, 22], [50801, 32], [50867, 68], [50977, 23], [51101, 29], [51170, 65], [51277, 23], [51401, 26], [51473, 62], [51577, 23], [51701, 23], [51776, 58], [51876, 24], [52001, 22], [52077, 57], [52175, 25], [52301, 20], [52379, 55], [52475, 25], [52600, 19], [52681, 53], [52774, 26], [52900, 17], [52983, 51], [53073, 27], [53200, 15], [53285, 49], [53373, 27], [53500, 14], [53586, 48], [53672, 28], [53800, 13], [53887, 47], [53972, 28], [54100, 12], [54188, 45], [54271, 29], [54399, 12], [54489, 45], [54570, 30], [54699, 11], [54790, 44], [54870, 30], [54999, 10], [55091, 43], [55169, 31], [55299, 8], [55393, 41], [55468, 32], [55599, 7], [55694, 40], [55767, 33], [55899, 6], [55995, 40], [56066, 34], [56198, 7], [56295, 40], [56365, 35], [56498, 6], [56596, 40], [56665, 35], [56798, 5], [56897, 40], [56964, 36], [57098, 5], [57197, 41], [57263, 37], [57398, 4], [57498, 41], [57562, 38], [57697, 4], [57799, 41], [57861, 39], [57997, 4], [58099, 43], [58160, 40], [58297, 3], [58400, 44], [58459, 41], [58597, 2], [58701, 45], [58757, 43], [58897, 2], [59001, 99], [59197, 1], [59302, 98], [59496, 2], [59602, 98], [59796, 2], [59902, 98], [60096, 1], [60203, 97], [60396, 1], [60503, 97], [60696, 1], [60803, 97], [61104, 96], [61295, 1], [61404, 96], [61595, 1], [61704, 96], [62005, 95], [62305, 95], [62605, 95], [62906, 94], [63206, 94], [63506, 94], [63806, 94], [64106, 94], [64406, 94], [64593, 1], [64706, 94], [64893, 1], [65006, 94], [65192, 2], [65306, 94], [65492, 2], [65606, 94], [65790, 4], [65906, 188], [66206, 188], [66506, 188], [66806, 188], [67106, 189], [67405, 190], [67705, 190], [68005, 191], [68304, 192], [68604, 192], [68904, 192], [69204, 193], [69503, 194], [69803, 194], [70103, 194], [70403, 195], [70702, 196], [71002, 196], [71302, 196], [71602, 98]], "point": [149, 166]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan17", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 0.5, "y": 0.908999562, "z": 1.75}, "object_poses": [{"objectName": "SprayBottle_4f8d0eee", "position": {"x": 1.18936872, "y": 0.08565579, "z": 1.409267}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": 1.03472006, "y": 0.8857041, "z": 2.58481264}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": -1.263916, "y": 1.68188834, "z": 1.28973031}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": 0.951595068, "y": 0.8840117, "z": 2.477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": 1.20097, "y": 0.8840117, "z": 2.477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": -0.0824483, "y": 0.7581714, "z": -0.533726156}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": -1.29051554, "y": 0.7489421, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": 1.20097, "y": 0.8863421, "z": 2.261375}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": 1.262696, "y": 0.116571963, "z": 1.081711}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": 1.20740271, "y": 0.912428737, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": -1.16246939, "y": 0.342489481, "z": 0.8833128}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": -1.10519874, "y": 0.750755668, "z": 2.08785558}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": 1.36722, "y": 0.886848569, "z": 2.15356255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": 1.284095, "y": 0.9012999, "z": 2.36918736}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": -1.513145, "y": 0.9270998, "z": -0.5625143}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.802, "y": 0.9393, "z": -0.47308147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -1.383174, "y": 0.783066452, "z": 2.273428}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 1.28637147, "y": 0.9123855, "z": -0.144580841}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 1.20740271, "y": 0.9123855, "z": 0.4229527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": 1.117845, "y": 0.9082927, "z": 2.58481264}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": -1.19785714, "y": 0.7708927, "z": 2.459}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -1.32097471, "y": 0.800886452, "z": 0.8833124}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": 0.951595068, "y": 0.936028838, "z": 2.80043745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": 1.444309, "y": 0.9128286, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": -1.32097435, "y": 1.635398, "z": 0.7816504}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": 1.128434, "y": 0.982468545, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": -1.19785714, "y": 0.8198685, "z": 2.83014464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": -1.29444218, "y": 2.14979625, "z": 0.819549739}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": 1.450345, "y": 0.8826062, "z": 2.261375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -1.13565314, "y": 0.907099962, "z": -0.732326269}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -1.32097507, "y": 1.63195741, "z": 0.9848823}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": -1.20685446, "y": 1.48467243, "z": 0.77862525}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": -0.813686848, "y": 0.9128167, "z": -0.5099766}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": -1.19785714, "y": 0.7501748, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": 0.802, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.9998245, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -1.14979517, "y": 1.68668628, "z": 0.984882832}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 1.128434, "y": 0.9123855, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": -1.21115148, "y": 0.9098116, "z": -0.477608353}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": -1.01254034, "y": 0.8205948, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": -1.20685577, "y": 0.7516495, "z": 1.19737518}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": -1.19785714, "y": 0.7502286, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": 0.951595068, "y": 0.8872287, "z": 2.261375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": 0.0417298935, "y": 0.773556054, "z": -0.6125}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": -1.176719, "y": 0.115498424, "z": 0.248357609}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": 1.35799479, "y": 0.146455675, "z": 1.50386286}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": -1.19416881, "y": 0.3408087, "z": 1.09268773}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": 1.28637147, "y": 0.9334927, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -0.144537389, "y": 0.792722642, "z": -0.7306608}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": 1.17539263, "y": 0.112166226, "z": 0.732311}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": 1.20097, "y": 0.8826062, "z": 2.80043745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": -0.0203592, "y": 0.803515, "z": -0.691273868}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": 1.36722, "y": 0.8863421, "z": 2.58481264}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": -0.91988194, "y": 0.7491905, "z": 2.08785558}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_a0065d6c", "position": {"x": 0.5029, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": -1.10519874, "y": 0.750755668, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": -0.955160856, "y": 1.65560138, "z": -0.7249964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -1.26391411, "y": 1.2833575, "z": 0.6739376}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 1533624507, "scene_num": 17}, "task_id": "trial_T20190908_131013_005229", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3PPLDHC3CG0YN_3C5W7UE9CIH24WRSCU79ROBFL1OXM4", "high_descs": ["Turn around and walk to the fridge. ", "Open the fridge door, pick up the cup from the second shelf, close the door. ", "Walk to the microwave to the left of the fridge. ", "Open the microwave door, put the cup inside of the microwave, close the door, heat the cup, open the door, take the cup out, close the door. ", "Turn left and face the counter to the left of the sink. ", "Put the cup down on the counter to the left of the sink. "], "task_desc": "To heat a cup and put it on the counter next to the sink. ", "votes": [1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3VW6495TLMRRYI6G0THM4X5VWHDYYI", "high_descs": ["Turn around and take two steps then turn right and walk to the fridge.", "Open the fridge and get the cup that's to the right of the wine bottle then close the fridge door.", "Turn left and walk to the counter then turn right and walk to the microwave.", "Warm the cup in the microwave then take it back out and close the door.", "Turn left to face the counter.", "Put the glass to the right of the sink and to the left of the glass bottle slightly above the glass bottle."], "task_desc": "Put a cold glass on the counter.", "votes": [1, 1]}, {"assignment_id": "A2A028LRDJB7ZB_3Y4W8Q93L2B7WDGKZM7OKDR0WCIDVT", "high_descs": ["Turn left then head to the fridge ", "Open the fridge, take the cup then close the fridge", "Turn left walk straight then head to the microwave, ", "Open the  microwave warm up the cup then put it out and close the microwave", "Turn to your left then face the counter beside the sink", "Put the cup on the counter beside the bottle"], "task_desc": "Put the warm cup on the sink counter", "votes": [1, 1]}]}}