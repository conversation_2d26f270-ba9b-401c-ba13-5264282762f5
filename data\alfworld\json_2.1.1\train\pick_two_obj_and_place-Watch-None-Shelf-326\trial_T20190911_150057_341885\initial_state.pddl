
(define (problem plan_trial_T20190911_150057_341885)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__plus_00_dot_86_bar__plus_01_dot_14_bar__minus_02_dot_28 - object
        AlarmClock_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_02_dot_25 - object
        AlarmClock_bar__minus_01_dot_52_bar__plus_00_dot_62_bar__plus_00_dot_50 - object
        BaseballBat_bar__minus_02_dot_98_bar__plus_00_dot_02_bar__minus_02_dot_70 - object
        BasketBall_bar__minus_02_dot_72_bar__plus_00_dot_12_bar__minus_01_dot_93 - object
        Blinds_bar__plus_02_dot_29_bar__plus_02_dot_07_bar__minus_03_dot_18 - object
        Blinds_bar__minus_01_dot_00_bar__plus_02_dot_07_bar__minus_03_dot_18 - object
        Book_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_01_dot_16 - object
        Bowl_bar__minus_02_dot_98_bar__plus_01_dot_14_bar__minus_01_dot_50 - object
        CD_bar__plus_00_dot_95_bar__plus_01_dot_14_bar__minus_02_dot_52 - object
        CellPhone_bar__plus_00_dot_95_bar__plus_01_dot_14_bar__minus_02_dot_09 - object
        CellPhone_bar__minus_00_dot_18_bar__plus_00_dot_62_bar__plus_00_dot_25 - object
        CellPhone_bar__minus_01_dot_38_bar__plus_00_dot_61_bar__plus_00_dot_50 - object
        Chair_bar__plus_02_dot_73_bar__plus_00_dot_00_bar__minus_01_dot_21 - object
        Chair_bar__plus_02_dot_85_bar__plus_00_dot_00_bar__plus_00_dot_16 - object
        Cloth_bar__plus_00_dot_71_bar_00_dot_00_bar__minus_02_dot_75 - object
        Cloth_bar__plus_00_dot_91_bar__plus_00_dot_03_bar__minus_02_dot_70 - object
        Cloth_bar__plus_00_dot_97_bar_00_dot_00_bar__minus_02_dot_40 - object
        CreditCard_bar__minus_01_dot_59_bar__plus_00_dot_61_bar__plus_00_dot_30 - object
        CreditCard_bar__minus_01_dot_63_bar__plus_00_dot_08_bar__plus_00_dot_37 - object
        CreditCard_bar__minus_01_dot_73_bar__plus_00_dot_61_bar__plus_00_dot_50 - object
        KeyChain_bar__plus_00_dot_89_bar__plus_01_dot_41_bar__minus_02_dot_65 - object
        KeyChain_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_01_dot_97 - object
        KeyChain_bar__minus_01_dot_37_bar__plus_00_dot_08_bar__plus_00_dot_35 - object
        Lamp_bar__plus_03_dot_24_bar__plus_00_dot_79_bar__minus_00_dot_65 - object
        Laptop_bar__minus_00_dot_63_bar__plus_00_dot_61_bar__minus_00_dot_32 - object
        Laptop_bar__minus_02_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_25 - object
        LightSwitch_bar__minus_00_dot_14_bar__plus_01_dot_33_bar__plus_00_dot_60 - object
        Mirror_bar__plus_00_dot_45_bar__plus_01_dot_49_bar__plus_00_dot_62 - object
        Mug_bar__plus_03_dot_33_bar__plus_00_dot_79_bar__minus_01_dot_97 - object
        Mug_bar__minus_02_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_70 - object
        Mug_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_33 - object
        Pencil_bar__plus_03_dot_15_bar__plus_00_dot_80_bar__minus_01_dot_12 - object
        Pencil_bar__minus_01_dot_45_bar__plus_00_dot_62_bar__plus_00_dot_38 - object
        Pencil_bar__minus_02_dot_85_bar__plus_01_dot_42_bar__minus_02_dot_74 - object
        Pen_bar__minus_02_dot_85_bar__plus_01_dot_15_bar__minus_01_dot_50 - object
        Pillow_bar__minus_00_dot_78_bar__plus_00_dot_69_bar__minus_00_dot_88 - object
        Pillow_bar__minus_02_dot_76_bar__plus_00_dot_68_bar__minus_01_dot_16 - object
        Poster_bar__plus_03_dot_40_bar__plus_01_dot_70_bar__minus_00_dot_79 - object
        Poster_bar__plus_03_dot_40_bar__plus_01_dot_86_bar__minus_01_dot_98 - object
        TeddyBear_bar__minus_02_dot_65_bar__plus_00_dot_62_bar__minus_00_dot_40 - object
        TennisRacket_bar__minus_03_dot_02_bar__plus_00_dot_30_bar__minus_02_dot_20 - object
        Watch_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_00_dot_56 - object
        Watch_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_02_dot_25 - object
        Window_bar__plus_02_dot_28_bar__plus_00_dot_93_bar__minus_03_dot_18 - object
        Window_bar__minus_01_dot_02_bar__plus_00_dot_93_bar__minus_03_dot_19 - object
        Bed_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__minus_00_dot_55 - receptacle
        Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55 - receptacle
        DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42 - receptacle
        Drawer_bar__minus_01_dot_52_bar__plus_00_dot_15_bar__plus_00_dot_36 - receptacle
        Drawer_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36 - receptacle
        GarbageCan_bar__plus_01_dot_69_bar_00_dot_00_bar__minus_02_dot_79 - receptacle
        Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_01_dot_62 - receptacle
        Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_02_dot_16 - receptacle
        Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_02_dot_73 - receptacle
        Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_62 - receptacle
        Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_02_dot_16 - receptacle
        Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_02_dot_73 - receptacle
        Shelf_bar__plus_03_dot_04_bar__plus_00_dot_53_bar__minus_00_dot_56 - receptacle
        Shelf_bar__plus_03_dot_04_bar__plus_00_dot_53_bar__minus_02_dot_26 - receptacle
        Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_01_dot_62 - receptacle
        Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_02_dot_19 - receptacle
        Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_02_dot_74 - receptacle
        Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_62 - receptacle
        Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_19 - receptacle
        Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_74 - receptacle
        SideTable_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36 - receptacle
        loc_bar_1_bar__minus_10_bar_1_bar_15 - location
        loc_bar__minus_9_bar__minus_8_bar_3_bar_15 - location
        loc_bar__minus_9_bar__minus_10_bar_3_bar_30 - location
        loc_bar__minus_9_bar__minus_10_bar_0_bar_45 - location
        loc_bar_3_bar__minus_3_bar_3_bar_45 - location
        loc_bar_1_bar_0_bar_0_bar_30 - location
        loc_bar_9_bar__minus_11_bar_2_bar_60 - location
        loc_bar_9_bar__minus_11_bar_2_bar__minus_30 - location
        loc_bar_1_bar__minus_10_bar_1_bar_30 - location
        loc_bar_2_bar_0_bar_0_bar_15 - location
        loc_bar_9_bar__minus_1_bar_1_bar_60 - location
        loc_bar_1_bar__minus_6_bar_1_bar_30 - location
        loc_bar_1_bar__minus_9_bar_1_bar_15 - location
        loc_bar__minus_4_bar__minus_11_bar_2_bar__minus_30 - location
        loc_bar_9_bar__minus_8_bar_1_bar_60 - location
        loc_bar_10_bar__minus_2_bar_1_bar_0 - location
        loc_bar__minus_6_bar__minus_3_bar_0_bar_60 - location
        loc_bar_10_bar__minus_7_bar_0_bar_60 - location
        loc_bar__minus_6_bar__minus_2_bar_0_bar_60 - location
        loc_bar__minus_6_bar__minus_1_bar_0_bar_60 - location
        loc_bar__minus_9_bar__minus_10_bar_3_bar_60 - location
        loc_bar__minus_8_bar__minus_8_bar_3_bar_30 - location
        loc_bar_9_bar__minus_11_bar_3_bar_60 - location
        loc_bar__minus_9_bar__minus_8_bar_3_bar_60 - location
        loc_bar__minus_9_bar__minus_9_bar_3_bar_60 - location
        loc_bar_1_bar__minus_10_bar_1_bar_60 - location
        loc_bar__minus_9_bar__minus_10_bar_3_bar_15 - location
        loc_bar__minus_9_bar__minus_9_bar_3_bar_30 - location
        loc_bar_1_bar__minus_6_bar_1_bar_15 - location
        loc_bar_10_bar__minus_8_bar_1_bar__minus_15 - location
        loc_bar_1_bar__minus_9_bar_1_bar_30 - location
        loc_bar_8_bar__minus_5_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_11_bar_2_bar_60 - location
        loc_bar__minus_9_bar__minus_9_bar_3_bar_15 - location
        loc_bar_9_bar_0_bar_1_bar_60 - location
        loc_bar_8_bar__minus_5_bar_0_bar_30 - location
        )
    

(:init


        (receptacleType Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_01_dot_62 ShelfType)
        (receptacleType Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55 BedType)
        (receptacleType Drawer_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36 DrawerType)
        (receptacleType Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_01_dot_62 ShelfType)
        (receptacleType Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_02_dot_19 ShelfType)
        (receptacleType SideTable_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36 SideTableType)
        (receptacleType Shelf_bar__plus_03_dot_04_bar__plus_00_dot_53_bar__minus_02_dot_26 ShelfType)
        (receptacleType GarbageCan_bar__plus_01_dot_69_bar_00_dot_00_bar__minus_02_dot_79 GarbageCanType)
        (receptacleType Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_62 ShelfType)
        (receptacleType Drawer_bar__minus_01_dot_52_bar__plus_00_dot_15_bar__plus_00_dot_36 DrawerType)
        (receptacleType Bed_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__minus_00_dot_55 BedType)
        (receptacleType Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_02_dot_74 ShelfType)
        (receptacleType Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_02_dot_73 ShelfType)
        (receptacleType Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_19 ShelfType)
        (receptacleType Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_62 ShelfType)
        (receptacleType Shelf_bar__plus_03_dot_04_bar__plus_00_dot_53_bar__minus_00_dot_56 ShelfType)
        (receptacleType Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_74 ShelfType)
        (receptacleType Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_02_dot_16 ShelfType)
        (receptacleType DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42 DiningTableType)
        (receptacleType Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_02_dot_73 ShelfType)
        (receptacleType Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_02_dot_16 ShelfType)
        (objectType Book_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_01_dot_16 BookType)
        (objectType Chair_bar__plus_02_dot_73_bar__plus_00_dot_00_bar__minus_01_dot_21 ChairType)
        (objectType Pen_bar__minus_02_dot_85_bar__plus_01_dot_15_bar__minus_01_dot_50 PenType)
        (objectType CreditCard_bar__minus_01_dot_59_bar__plus_00_dot_61_bar__plus_00_dot_30 CreditCardType)
        (objectType Cloth_bar__plus_00_dot_71_bar_00_dot_00_bar__minus_02_dot_75 ClothType)
        (objectType BasketBall_bar__minus_02_dot_72_bar__plus_00_dot_12_bar__minus_01_dot_93 BasketBallType)
        (objectType AlarmClock_bar__plus_00_dot_86_bar__plus_01_dot_14_bar__minus_02_dot_28 AlarmClockType)
        (objectType TennisRacket_bar__minus_03_dot_02_bar__plus_00_dot_30_bar__minus_02_dot_20 TennisRacketType)
        (objectType Pencil_bar__plus_03_dot_15_bar__plus_00_dot_80_bar__minus_01_dot_12 PencilType)
        (objectType AlarmClock_bar__minus_01_dot_52_bar__plus_00_dot_62_bar__plus_00_dot_50 AlarmClockType)
        (objectType Mug_bar__minus_02_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_70 MugType)
        (objectType CreditCard_bar__minus_01_dot_73_bar__plus_00_dot_61_bar__plus_00_dot_50 CreditCardType)
        (objectType KeyChain_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_01_dot_97 KeyChainType)
        (objectType AlarmClock_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_02_dot_25 AlarmClockType)
        (objectType KeyChain_bar__minus_01_dot_37_bar__plus_00_dot_08_bar__plus_00_dot_35 KeyChainType)
        (objectType Chair_bar__plus_02_dot_85_bar__plus_00_dot_00_bar__plus_00_dot_16 ChairType)
        (objectType CreditCard_bar__minus_01_dot_63_bar__plus_00_dot_08_bar__plus_00_dot_37 CreditCardType)
        (objectType Cloth_bar__plus_00_dot_91_bar__plus_00_dot_03_bar__minus_02_dot_70 ClothType)
        (objectType Pencil_bar__minus_02_dot_85_bar__plus_01_dot_42_bar__minus_02_dot_74 PencilType)
        (objectType Mug_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_33 MugType)
        (objectType Watch_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_00_dot_56 WatchType)
        (objectType BaseballBat_bar__minus_02_dot_98_bar__plus_00_dot_02_bar__minus_02_dot_70 BaseballBatType)
        (objectType Poster_bar__plus_03_dot_40_bar__plus_01_dot_70_bar__minus_00_dot_79 PosterType)
        (objectType Mirror_bar__plus_00_dot_45_bar__plus_01_dot_49_bar__plus_00_dot_62 MirrorType)
        (objectType Watch_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_02_dot_25 WatchType)
        (objectType Window_bar__plus_02_dot_28_bar__plus_00_dot_93_bar__minus_03_dot_18 WindowType)
        (objectType CellPhone_bar__minus_01_dot_38_bar__plus_00_dot_61_bar__plus_00_dot_50 CellPhoneType)
        (objectType Pencil_bar__minus_01_dot_45_bar__plus_00_dot_62_bar__plus_00_dot_38 PencilType)
        (objectType Laptop_bar__minus_02_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_25 LaptopType)
        (objectType Bowl_bar__minus_02_dot_98_bar__plus_01_dot_14_bar__minus_01_dot_50 BowlType)
        (objectType Pillow_bar__minus_02_dot_76_bar__plus_00_dot_68_bar__minus_01_dot_16 PillowType)
        (objectType Poster_bar__plus_03_dot_40_bar__plus_01_dot_86_bar__minus_01_dot_98 PosterType)
        (objectType KeyChain_bar__plus_00_dot_89_bar__plus_01_dot_41_bar__minus_02_dot_65 KeyChainType)
        (objectType TeddyBear_bar__minus_02_dot_65_bar__plus_00_dot_62_bar__minus_00_dot_40 TeddyBearType)
        (objectType Cloth_bar__plus_00_dot_97_bar_00_dot_00_bar__minus_02_dot_40 ClothType)
        (objectType Blinds_bar__plus_02_dot_29_bar__plus_02_dot_07_bar__minus_03_dot_18 BlindsType)
        (objectType Blinds_bar__minus_01_dot_00_bar__plus_02_dot_07_bar__minus_03_dot_18 BlindsType)
        (objectType Mug_bar__plus_03_dot_33_bar__plus_00_dot_79_bar__minus_01_dot_97 MugType)
        (objectType CellPhone_bar__minus_00_dot_18_bar__plus_00_dot_62_bar__plus_00_dot_25 CellPhoneType)
        (objectType LightSwitch_bar__minus_00_dot_14_bar__plus_01_dot_33_bar__plus_00_dot_60 LightSwitchType)
        (objectType Window_bar__minus_01_dot_02_bar__plus_00_dot_93_bar__minus_03_dot_19 WindowType)
        (objectType Pillow_bar__minus_00_dot_78_bar__plus_00_dot_69_bar__minus_00_dot_88 PillowType)
        (objectType CellPhone_bar__plus_00_dot_95_bar__plus_01_dot_14_bar__minus_02_dot_09 CellPhoneType)
        (objectType Laptop_bar__minus_00_dot_63_bar__plus_00_dot_61_bar__minus_00_dot_32 LaptopType)
        (objectType CD_bar__plus_00_dot_95_bar__plus_01_dot_14_bar__minus_02_dot_52 CDType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain BedType BasketBallType)
        (canContain BedType BaseballBatType)
        (canContain BedType TennisRacketType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType WatchType)
        (canContain SideTableType BowlType)
        (canContain SideTableType CDType)
        (canContain SideTableType MugType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType BasketBallType)
        (canContain SideTableType BaseballBatType)
        (canContain SideTableType TennisRacketType)
        (canContain SideTableType ClothType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType ClothType)
        (canContain GarbageCanType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain BedType BasketBallType)
        (canContain BedType BaseballBatType)
        (canContain BedType TennisRacketType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DiningTableType PenType)
        (canContain DiningTableType BookType)
        (canContain DiningTableType WatchType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType CDType)
        (canContain DiningTableType MugType)
        (canContain DiningTableType CellPhoneType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType BasketBallType)
        (canContain DiningTableType BaseballBatType)
        (canContain DiningTableType TennisRacketType)
        (canContain DiningTableType ClothType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType PencilType)
        (canContain DiningTableType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType WatchType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (pickupable Book_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_01_dot_16)
        (pickupable Pen_bar__minus_02_dot_85_bar__plus_01_dot_15_bar__minus_01_dot_50)
        (pickupable CreditCard_bar__minus_01_dot_59_bar__plus_00_dot_61_bar__plus_00_dot_30)
        (pickupable Cloth_bar__plus_00_dot_71_bar_00_dot_00_bar__minus_02_dot_75)
        (pickupable BasketBall_bar__minus_02_dot_72_bar__plus_00_dot_12_bar__minus_01_dot_93)
        (pickupable AlarmClock_bar__plus_00_dot_86_bar__plus_01_dot_14_bar__minus_02_dot_28)
        (pickupable TennisRacket_bar__minus_03_dot_02_bar__plus_00_dot_30_bar__minus_02_dot_20)
        (pickupable Pencil_bar__plus_03_dot_15_bar__plus_00_dot_80_bar__minus_01_dot_12)
        (pickupable AlarmClock_bar__minus_01_dot_52_bar__plus_00_dot_62_bar__plus_00_dot_50)
        (pickupable Mug_bar__minus_02_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_70)
        (pickupable CreditCard_bar__minus_01_dot_73_bar__plus_00_dot_61_bar__plus_00_dot_50)
        (pickupable KeyChain_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_01_dot_97)
        (pickupable AlarmClock_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_02_dot_25)
        (pickupable KeyChain_bar__minus_01_dot_37_bar__plus_00_dot_08_bar__plus_00_dot_35)
        (pickupable CreditCard_bar__minus_01_dot_63_bar__plus_00_dot_08_bar__plus_00_dot_37)
        (pickupable Cloth_bar__plus_00_dot_91_bar__plus_00_dot_03_bar__minus_02_dot_70)
        (pickupable Pencil_bar__minus_02_dot_85_bar__plus_01_dot_42_bar__minus_02_dot_74)
        (pickupable Mug_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_33)
        (pickupable Watch_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_00_dot_56)
        (pickupable BaseballBat_bar__minus_02_dot_98_bar__plus_00_dot_02_bar__minus_02_dot_70)
        (pickupable Watch_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_02_dot_25)
        (pickupable CellPhone_bar__minus_01_dot_38_bar__plus_00_dot_61_bar__plus_00_dot_50)
        (pickupable Pencil_bar__minus_01_dot_45_bar__plus_00_dot_62_bar__plus_00_dot_38)
        (pickupable Laptop_bar__minus_02_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_25)
        (pickupable Bowl_bar__minus_02_dot_98_bar__plus_01_dot_14_bar__minus_01_dot_50)
        (pickupable Pillow_bar__minus_02_dot_76_bar__plus_00_dot_68_bar__minus_01_dot_16)
        (pickupable KeyChain_bar__plus_00_dot_89_bar__plus_01_dot_41_bar__minus_02_dot_65)
        (pickupable TeddyBear_bar__minus_02_dot_65_bar__plus_00_dot_62_bar__minus_00_dot_40)
        (pickupable Cloth_bar__plus_00_dot_97_bar_00_dot_00_bar__minus_02_dot_40)
        (pickupable Mug_bar__plus_03_dot_33_bar__plus_00_dot_79_bar__minus_01_dot_97)
        (pickupable CellPhone_bar__minus_00_dot_18_bar__plus_00_dot_62_bar__plus_00_dot_25)
        (pickupable Pillow_bar__minus_00_dot_78_bar__plus_00_dot_69_bar__minus_00_dot_88)
        (pickupable CellPhone_bar__plus_00_dot_95_bar__plus_01_dot_14_bar__minus_02_dot_09)
        (pickupable Laptop_bar__minus_00_dot_63_bar__plus_00_dot_61_bar__minus_00_dot_32)
        (pickupable CD_bar__plus_00_dot_95_bar__plus_01_dot_14_bar__minus_02_dot_52)
        (isReceptacleObject Mug_bar__minus_02_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_70)
        (isReceptacleObject Mug_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_33)
        (isReceptacleObject Bowl_bar__minus_02_dot_98_bar__plus_01_dot_14_bar__minus_01_dot_50)
        (isReceptacleObject Mug_bar__plus_03_dot_33_bar__plus_00_dot_79_bar__minus_01_dot_97)
        (openable Drawer_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36)
        (openable Drawer_bar__minus_01_dot_52_bar__plus_00_dot_15_bar__plus_00_dot_36)
        
        (atLocation agent1 loc_bar_8_bar__minus_5_bar_0_bar_30)
        
        (cleanable Cloth_bar__plus_00_dot_71_bar_00_dot_00_bar__minus_02_dot_75)
        (cleanable Mug_bar__minus_02_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_70)
        (cleanable Cloth_bar__plus_00_dot_91_bar__plus_00_dot_03_bar__minus_02_dot_70)
        (cleanable Mug_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_33)
        (cleanable Bowl_bar__minus_02_dot_98_bar__plus_01_dot_14_bar__minus_01_dot_50)
        (cleanable Cloth_bar__plus_00_dot_97_bar_00_dot_00_bar__minus_02_dot_40)
        (cleanable Mug_bar__plus_03_dot_33_bar__plus_00_dot_79_bar__minus_01_dot_97)
        
        (heatable Mug_bar__minus_02_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_70)
        (heatable Mug_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_33)
        (heatable Mug_bar__plus_03_dot_33_bar__plus_00_dot_79_bar__minus_01_dot_97)
        (coolable Mug_bar__minus_02_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_70)
        (coolable Mug_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_33)
        (coolable Bowl_bar__minus_02_dot_98_bar__plus_01_dot_14_bar__minus_01_dot_50)
        (coolable Mug_bar__plus_03_dot_33_bar__plus_00_dot_79_bar__minus_01_dot_97)
        
        
        
        
        
        
        
        (inReceptacle Watch_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_02_dot_25 DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42)
        (inReceptacle KeyChain_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_01_dot_97 DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42)
        (inReceptacle AlarmClock_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_02_dot_25 DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42)
        (inReceptacle Mug_bar__plus_03_dot_33_bar__plus_00_dot_79_bar__minus_01_dot_97 DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42)
        (inReceptacle Watch_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_00_dot_56 DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42)
        (inReceptacle Pencil_bar__plus_03_dot_15_bar__plus_00_dot_80_bar__minus_01_dot_12 DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42)
        (inReceptacle Pillow_bar__minus_02_dot_76_bar__plus_00_dot_68_bar__minus_01_dot_16 Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55)
        (inReceptacle Book_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_01_dot_16 Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55)
        (inReceptacle Laptop_bar__minus_02_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_25 Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55)
        (inReceptacle TeddyBear_bar__minus_02_dot_65_bar__plus_00_dot_62_bar__minus_00_dot_40 Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55)
        (inReceptacle CreditCard_bar__minus_01_dot_63_bar__plus_00_dot_08_bar__plus_00_dot_37 Drawer_bar__minus_01_dot_52_bar__plus_00_dot_15_bar__plus_00_dot_36)
        (inReceptacle KeyChain_bar__minus_01_dot_37_bar__plus_00_dot_08_bar__plus_00_dot_35 Drawer_bar__minus_01_dot_52_bar__plus_00_dot_15_bar__plus_00_dot_36)
        (inReceptacle Pillow_bar__minus_00_dot_78_bar__plus_00_dot_69_bar__minus_00_dot_88 Bed_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__minus_00_dot_55)
        (inReceptacle CellPhone_bar__minus_00_dot_18_bar__plus_00_dot_62_bar__plus_00_dot_25 Bed_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__minus_00_dot_55)
        (inReceptacle Laptop_bar__minus_00_dot_63_bar__plus_00_dot_61_bar__minus_00_dot_32 Bed_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__minus_00_dot_55)
        (inReceptacle CreditCard_bar__minus_01_dot_73_bar__plus_00_dot_61_bar__plus_00_dot_50 SideTable_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36)
        (inReceptacle CellPhone_bar__minus_01_dot_38_bar__plus_00_dot_61_bar__plus_00_dot_50 SideTable_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36)
        (inReceptacle Pencil_bar__minus_01_dot_45_bar__plus_00_dot_62_bar__plus_00_dot_38 SideTable_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36)
        (inReceptacle CreditCard_bar__minus_01_dot_59_bar__plus_00_dot_61_bar__plus_00_dot_30 SideTable_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36)
        (inReceptacle AlarmClock_bar__minus_01_dot_52_bar__plus_00_dot_62_bar__plus_00_dot_50 SideTable_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36)
        (inReceptacle CellPhone_bar__plus_00_dot_95_bar__plus_01_dot_14_bar__minus_02_dot_09 Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_02_dot_16)
        (inReceptacle AlarmClock_bar__plus_00_dot_86_bar__plus_01_dot_14_bar__minus_02_dot_28 Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_02_dot_16)
        (inReceptacle Mug_bar__minus_02_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_70 Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_62)
        (inReceptacle Mug_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_33 Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_19)
        (inReceptacle Pencil_bar__minus_02_dot_85_bar__plus_01_dot_42_bar__minus_02_dot_74 Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_74)
        (inReceptacle KeyChain_bar__plus_00_dot_89_bar__plus_01_dot_41_bar__minus_02_dot_65 Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_02_dot_73)
        (inReceptacle Pen_bar__minus_02_dot_85_bar__plus_01_dot_15_bar__minus_01_dot_50 Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_01_dot_62)
        (inReceptacle Bowl_bar__minus_02_dot_98_bar__plus_01_dot_14_bar__minus_01_dot_50 Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_01_dot_62)
        
        
        (receptacleAtLocation Bed_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__minus_00_dot_55 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (receptacleAtLocation Bed_bar__minus_02_dot_46_bar__plus_00_dot_01_bar__minus_00_dot_55 loc_bar__minus_9_bar__minus_10_bar_0_bar_45)
        (receptacleAtLocation DiningTable_bar__plus_03_dot_16_bar__minus_00_dot_01_bar__minus_01_dot_42 loc_bar_8_bar__minus_5_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_52_bar__plus_00_dot_15_bar__plus_00_dot_36 loc_bar__minus_6_bar__minus_3_bar_0_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36 loc_bar__minus_6_bar__minus_2_bar_0_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_01_dot_69_bar_00_dot_00_bar__minus_02_dot_79 loc_bar_9_bar__minus_11_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_01_dot_62 loc_bar_1_bar__minus_6_bar_1_bar_30)
        (receptacleAtLocation Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_02_dot_16 loc_bar_1_bar__minus_9_bar_1_bar_30)
        (receptacleAtLocation Shelf_bar__plus_00_dot_92_bar__plus_01_dot_13_bar__minus_02_dot_73 loc_bar_1_bar__minus_10_bar_1_bar_30)
        (receptacleAtLocation Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_62 loc_bar_1_bar__minus_6_bar_1_bar_15)
        (receptacleAtLocation Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_02_dot_16 loc_bar_1_bar__minus_9_bar_1_bar_15)
        (receptacleAtLocation Shelf_bar__plus_00_dot_92_bar__plus_01_dot_41_bar__minus_02_dot_73 loc_bar_1_bar__minus_10_bar_1_bar_15)
        (receptacleAtLocation Shelf_bar__plus_03_dot_04_bar__plus_00_dot_53_bar__minus_00_dot_56 loc_bar_9_bar__minus_1_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_03_dot_04_bar__plus_00_dot_53_bar__minus_02_dot_26 loc_bar_9_bar__minus_8_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_01_dot_62 loc_bar__minus_8_bar__minus_8_bar_3_bar_30)
        (receptacleAtLocation Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_02_dot_19 loc_bar__minus_9_bar__minus_9_bar_3_bar_30)
        (receptacleAtLocation Shelf_bar__minus_02_dot_95_bar__plus_01_dot_13_bar__minus_02_dot_74 loc_bar__minus_9_bar__minus_10_bar_3_bar_30)
        (receptacleAtLocation Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_01_dot_62 loc_bar__minus_9_bar__minus_8_bar_3_bar_15)
        (receptacleAtLocation Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_19 loc_bar__minus_9_bar__minus_9_bar_3_bar_15)
        (receptacleAtLocation Shelf_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_74 loc_bar__minus_9_bar__minus_10_bar_3_bar_15)
        (receptacleAtLocation SideTable_bar__minus_01_dot_52_bar__plus_00_dot_42_bar__plus_00_dot_36 loc_bar__minus_6_bar__minus_1_bar_0_bar_60)
        (objectAtLocation Mug_bar__plus_03_dot_33_bar__plus_00_dot_79_bar__minus_01_dot_97 loc_bar_8_bar__minus_5_bar_1_bar_60)
        (objectAtLocation CellPhone_bar__minus_01_dot_38_bar__plus_00_dot_61_bar__plus_00_dot_50 loc_bar__minus_6_bar__minus_1_bar_0_bar_60)
        (objectAtLocation Watch_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_00_dot_56 loc_bar_8_bar__minus_5_bar_1_bar_60)
        (objectAtLocation AlarmClock_bar__plus_03_dot_02_bar__plus_00_dot_79_bar__minus_02_dot_25 loc_bar_8_bar__minus_5_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_73_bar__plus_00_dot_61_bar__plus_00_dot_50 loc_bar__minus_6_bar__minus_1_bar_0_bar_60)
        (objectAtLocation Pencil_bar__plus_03_dot_15_bar__plus_00_dot_80_bar__minus_01_dot_12 loc_bar_8_bar__minus_5_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__minus_01_dot_37_bar__plus_00_dot_08_bar__plus_00_dot_35 loc_bar__minus_6_bar__minus_3_bar_0_bar_60)
        (objectAtLocation Laptop_bar__minus_00_dot_63_bar__plus_00_dot_61_bar__minus_00_dot_32 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation AlarmClock_bar__plus_00_dot_86_bar__plus_01_dot_14_bar__minus_02_dot_28 loc_bar_1_bar__minus_9_bar_1_bar_30)
        (objectAtLocation KeyChain_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_01_dot_97 loc_bar_8_bar__minus_5_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_59_bar__plus_00_dot_61_bar__plus_00_dot_30 loc_bar__minus_6_bar__minus_1_bar_0_bar_60)
        (objectAtLocation Pencil_bar__minus_01_dot_45_bar__plus_00_dot_62_bar__plus_00_dot_38 loc_bar__minus_6_bar__minus_1_bar_0_bar_60)
        (objectAtLocation CellPhone_bar__minus_00_dot_18_bar__plus_00_dot_62_bar__plus_00_dot_25 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation Mug_bar__minus_02_dot_95_bar__plus_01_dot_41_bar__minus_02_dot_33 loc_bar__minus_9_bar__minus_9_bar_3_bar_15)
        (objectAtLocation Chair_bar__plus_02_dot_73_bar__plus_00_dot_00_bar__minus_01_dot_21 loc_bar_10_bar__minus_7_bar_0_bar_60)
        (objectAtLocation Chair_bar__plus_02_dot_85_bar__plus_00_dot_00_bar__plus_00_dot_16 loc_bar_9_bar_0_bar_1_bar_60)
        (objectAtLocation Book_bar__minus_02_dot_16_bar__plus_00_dot_60_bar__minus_01_dot_16 loc_bar__minus_9_bar__minus_10_bar_0_bar_45)
        (objectAtLocation Laptop_bar__minus_02_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_25 loc_bar__minus_9_bar__minus_10_bar_0_bar_45)
        (objectAtLocation KeyChain_bar__plus_00_dot_89_bar__plus_01_dot_41_bar__minus_02_dot_65 loc_bar_1_bar__minus_10_bar_1_bar_15)
        (objectAtLocation Cloth_bar__plus_00_dot_97_bar_00_dot_00_bar__minus_02_dot_40 loc_bar_1_bar__minus_10_bar_1_bar_60)
        (objectAtLocation BaseballBat_bar__minus_02_dot_98_bar__plus_00_dot_02_bar__minus_02_dot_70 loc_bar__minus_9_bar__minus_10_bar_3_bar_60)
        (objectAtLocation BasketBall_bar__minus_02_dot_72_bar__plus_00_dot_12_bar__minus_01_dot_93 loc_bar__minus_9_bar__minus_8_bar_3_bar_60)
        (objectAtLocation Pillow_bar__minus_00_dot_78_bar__plus_00_dot_69_bar__minus_00_dot_88 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation Pillow_bar__minus_02_dot_76_bar__plus_00_dot_68_bar__minus_01_dot_16 loc_bar__minus_9_bar__minus_10_bar_0_bar_45)
        (objectAtLocation TennisRacket_bar__minus_03_dot_02_bar__plus_00_dot_30_bar__minus_02_dot_20 loc_bar__minus_9_bar__minus_9_bar_3_bar_60)
        (objectAtLocation Pencil_bar__minus_02_dot_85_bar__plus_01_dot_42_bar__minus_02_dot_74 loc_bar__minus_9_bar__minus_10_bar_3_bar_15)
        (objectAtLocation CreditCard_bar__minus_01_dot_63_bar__plus_00_dot_08_bar__plus_00_dot_37 loc_bar__minus_6_bar__minus_3_bar_0_bar_60)
        (objectAtLocation Cloth_bar__plus_00_dot_91_bar__plus_00_dot_03_bar__minus_02_dot_70 loc_bar_1_bar__minus_10_bar_1_bar_60)
        (objectAtLocation TeddyBear_bar__minus_02_dot_65_bar__plus_00_dot_62_bar__minus_00_dot_40 loc_bar__minus_9_bar__minus_10_bar_0_bar_45)
        (objectAtLocation LightSwitch_bar__minus_00_dot_14_bar__plus_01_dot_33_bar__plus_00_dot_60 loc_bar_1_bar_0_bar_0_bar_30)
        (objectAtLocation Poster_bar__plus_03_dot_40_bar__plus_01_dot_86_bar__minus_01_dot_98 loc_bar_10_bar__minus_8_bar_1_bar__minus_15)
        (objectAtLocation Poster_bar__plus_03_dot_40_bar__plus_01_dot_70_bar__minus_00_dot_79 loc_bar_10_bar__minus_2_bar_1_bar_0)
        (objectAtLocation Pen_bar__minus_02_dot_85_bar__plus_01_dot_15_bar__minus_01_dot_50 loc_bar__minus_8_bar__minus_8_bar_3_bar_30)
        (objectAtLocation Cloth_bar__plus_00_dot_71_bar_00_dot_00_bar__minus_02_dot_75 loc_bar_1_bar__minus_10_bar_1_bar_60)
        (objectAtLocation AlarmClock_bar__minus_01_dot_52_bar__plus_00_dot_62_bar__plus_00_dot_50 loc_bar__minus_6_bar__minus_1_bar_0_bar_60)
        (objectAtLocation Watch_bar__plus_03_dot_15_bar__plus_00_dot_79_bar__minus_02_dot_25 loc_bar_8_bar__minus_5_bar_1_bar_60)
        (objectAtLocation Mirror_bar__plus_00_dot_45_bar__plus_01_dot_49_bar__plus_00_dot_62 loc_bar_2_bar_0_bar_0_bar_15)
        (objectAtLocation CellPhone_bar__plus_00_dot_95_bar__plus_01_dot_14_bar__minus_02_dot_09 loc_bar_1_bar__minus_9_bar_1_bar_30)
        (objectAtLocation CD_bar__plus_00_dot_95_bar__plus_01_dot_14_bar__minus_02_dot_52 loc_bar_1_bar__minus_10_bar_1_bar_30)
        (objectAtLocation Window_bar__plus_02_dot_28_bar__plus_00_dot_93_bar__minus_03_dot_18 loc_bar_9_bar__minus_11_bar_2_bar_60)
        (objectAtLocation Window_bar__minus_01_dot_02_bar__plus_00_dot_93_bar__minus_03_dot_19 loc_bar__minus_4_bar__minus_11_bar_2_bar_60)
        (objectAtLocation Mug_bar__minus_02_dot_92_bar__plus_01_dot_41_bar__minus_01_dot_70 loc_bar__minus_9_bar__minus_8_bar_3_bar_15)
        (objectAtLocation Blinds_bar__minus_01_dot_00_bar__plus_02_dot_07_bar__minus_03_dot_18 loc_bar__minus_4_bar__minus_11_bar_2_bar__minus_30)
        (objectAtLocation Blinds_bar__plus_02_dot_29_bar__plus_02_dot_07_bar__minus_03_dot_18 loc_bar_9_bar__minus_11_bar_2_bar__minus_30)
        (objectAtLocation Bowl_bar__minus_02_dot_98_bar__plus_01_dot_14_bar__minus_01_dot_50 loc_bar__minus_8_bar__minus_8_bar_3_bar_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 WatchType)
                                    (receptacleType ?r ShelfType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 WatchType)
                                            (receptacleType ?r ShelfType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            