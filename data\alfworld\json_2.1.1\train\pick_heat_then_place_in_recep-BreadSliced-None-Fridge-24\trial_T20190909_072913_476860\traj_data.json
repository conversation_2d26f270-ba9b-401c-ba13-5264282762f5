{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000280.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000281.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000282.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000283.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000284.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000285.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000286.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000287.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000288.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000289.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000290.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000291.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000292.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000293.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000294.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000295.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000330.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000331.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000332.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000333.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000335.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000336.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000337.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000338.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000339.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000340.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000341.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000342.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000343.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000344.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000345.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000346.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000347.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000348.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000349.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000350.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000351.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000352.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000353.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000354.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000355.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000356.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000357.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000358.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000359.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000360.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000361.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000362.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000363.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000364.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000365.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000366.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000367.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000368.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000369.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000370.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000371.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000372.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000373.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000374.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000375.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000376.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000377.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000378.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000379.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000380.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000381.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000382.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000383.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000384.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000385.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000386.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000387.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000388.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000389.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000390.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000391.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000392.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000393.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000394.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000395.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000396.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000397.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000398.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000399.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000400.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000401.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000402.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000403.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000404.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000405.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000406.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000407.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000408.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000409.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000410.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000411.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000412.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000413.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000414.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000415.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000416.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000417.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000418.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000419.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000420.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000421.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000422.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000423.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000424.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000425.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000426.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000427.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000428.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000429.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000430.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000431.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000432.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000433.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000434.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000435.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000436.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000437.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000438.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000439.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000440.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000441.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000442.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000443.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000444.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000445.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000446.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000447.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000448.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000449.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000450.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000451.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000452.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000453.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000454.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000455.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000456.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000457.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000458.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000459.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000460.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000461.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000462.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000463.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000464.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000465.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000466.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000467.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000468.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000469.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000470.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000471.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000472.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000473.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000474.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000475.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000476.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000477.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000478.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000479.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000480.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000481.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000482.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000483.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000484.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000485.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000486.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000487.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000488.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000489.png", "low_idx": 79}, {"high_idx": 11, "image_name": "000000490.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000491.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000492.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000493.png", "low_idx": 80}, {"high_idx": 11, "image_name": "000000494.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000495.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000496.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000497.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000498.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000499.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000500.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000501.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000502.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000503.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000504.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000505.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000506.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000507.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000508.png", "low_idx": 81}, {"high_idx": 11, "image_name": "000000509.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000510.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000511.png", "low_idx": 82}, {"high_idx": 11, "image_name": "000000512.png", "low_idx": 82}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-8|12|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [-9.16402, -9.16402, 14.46397688, 14.46397688, 3.276860952, 3.276860952]], "coordinateReceptacleObjectId": ["CounterTop", [-8.324, -8.324, 15.196, 15.196, 3.4344, 3.4344]], "forceVisible": true, "objectId": "Knife|-02.29|+00.82|+03.62"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|10|1|60"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [3.311998128, 3.311998128, 9.79999544, 9.79999544, 3.4459684, 3.4459684]], "forceVisible": true, "objectId": "Bread|+00.83|+00.86|+02.45"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-3|10|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [-9.16402, -9.16402, 14.46397688, 14.46397688, 3.276860952, 3.276860952]], "coordinateReceptacleObjectId": ["Cabinet", [-4.54557848, -4.54557848, 14.064229, 14.064229, 1.540134548, 1.540134548]], "forceVisible": true, "objectId": "Knife|-02.29|+00.82|+03.62", "receptacleObjectId": "Cabinet|-01.14|+00.39|+03.52"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|1|10|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [3.311998128, 3.311998128, 9.79999544, 9.79999544, 3.4459684, 3.4459684]], "coordinateReceptacleObjectId": ["CounterTop", [3.4584, 3.4584, 9.4872, 9.4872, 3.4344, 3.4344]], "forceVisible": true, "objectId": "Bread|+00.83|+00.86|+02.45|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-6|13|0|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-6.116, -6.116, 15.5288, 15.5288, 5.00112532, 5.00112532]], "forceVisible": true, "objectId": "Microwave|-01.53|+01.25|+03.88"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-7|9|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "fridge"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [3.311998128, 3.311998128, 9.79999544, 9.79999544, 3.4459684, 3.4459684]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-10.572, -10.572, 8.536, 8.536, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|+00.83|+00.86|+02.45|BreadSliced_1", "receptacleObjectId": "<PERSON><PERSON>|-02.64|+00.00|+02.13"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|-02.29|+00.82|+03.62"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [16, 105, 103, 114], "mask": [[31268, 4], [31275, 28], [31526, 78], [31820, 84], [32116, 88], [32417, 86], [32721, 46], [32781, 6], [32795, 7], [33027, 40], [33097, 3], [33334, 32], [33646, 20], [33961, 4]], "point": [59, 108]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|+00.83|+00.86|+02.45"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [126, 84, 201, 149], "mask": [[25056, 17], [25351, 27], [25647, 34], [25945, 39], [26242, 43], [26541, 46], [26839, 50], [27137, 54], [27436, 56], [27734, 59], [28033, 61], [28332, 63], [28631, 65], [28930, 67], [29230, 68], [29529, 69], [29828, 71], [30128, 72], [30427, 73], [30727, 73], [31027, 74], [31326, 75], [31626, 75], [31926, 76], [32226, 76], [32526, 76], [32826, 76], [33126, 76], [33426, 76], [33726, 76], [34026, 76], [34326, 76], [34626, 76], [34926, 76], [35226, 76], [35526, 76], [35827, 75], [36127, 75], [36427, 75], [36728, 74], [37028, 74], [37328, 73], [37629, 72], [37929, 72], [38230, 70], [38530, 70], [38831, 69], [39132, 67], [39432, 67], [39733, 65], [40034, 63], [40335, 62], [40636, 60], [40936, 59], [41238, 56], [41539, 54], [41841, 51], [42142, 49], [42444, 46], [42746, 43], [43048, 39], [43350, 35], [43652, 31], [43954, 26], [44257, 20], [44561, 12]], "point": [163, 115]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-01.14|+00.39|+03.52"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 137, 92, 198], "mask": [[40800, 77], [41100, 77], [41400, 77], [41700, 77], [42000, 78], [42300, 78], [42600, 78], [42901, 77], [43202, 77], [43502, 77], [43803, 76], [44103, 77], [44404, 76], [44704, 76], [45005, 75], [45306, 75], [45606, 75], [45907, 74], [46207, 74], [46508, 74], [46808, 74], [47109, 73], [47409, 73], [47710, 73], [48011, 72], [48311, 72], [48612, 72], [48912, 72], [49213, 71], [49513, 71], [49814, 71], [50114, 71], [50415, 70], [50716, 69], [51016, 70], [51317, 69], [51617, 69], [51918, 69], [52218, 69], [52519, 68], [52820, 67], [53120, 68], [53421, 67], [53721, 67], [54022, 66], [54322, 67], [54623, 66], [54923, 66], [55224, 65], [55525, 65], [55825, 65], [56126, 64], [56426, 65], [56727, 64], [57027, 64], [57328, 63], [57628, 64], [57929, 63], [58230, 62], [58530, 62], [58831, 62], [59131, 62]], "point": [46, 166]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|-02.29|+00.82|+03.62", "placeStationary": true, "receptacleObjectId": "Cabinet|-01.14|+00.39|+03.52"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 137, 93, 257], "mask": [[40875, 2], [41100, 78], [41400, 78], [41701, 77], [42001, 78], [42302, 77], [42602, 77], [42903, 76], [43204, 76], [43504, 76], [43805, 75], [44105, 75], [44406, 75], [44706, 75], [45007, 74], [45307, 74], [45608, 74], [45909, 73], [46209, 73], [46510, 73], [46810, 73], [47111, 72], [47411, 72], [47712, 72], [48012, 72], [48313, 71], [48614, 70], [48914, 71], [49215, 70], [49515, 70], [49816, 69], [50116, 70], [50417, 69], [50717, 69], [51018, 68], [51318, 69], [51619, 68], [51920, 67], [52220, 68], [52521, 67], [52821, 67], [53122, 66], [53422, 67], [53723, 66], [54023, 66], [54324, 65], [54625, 65], [54925, 65], [55226, 64], [55526, 64], [55827, 64], [56127, 64], [56428, 63], [56728, 64], [57029, 63], [57330, 62], [57630, 62], [57931, 62], [58231, 62], [58532, 61], [58857, 36], [59157, 37], [59456, 38], [59756, 37], [60056, 37], [60355, 38], [60655, 38], [60955, 38], [61256, 37], [61556, 36], [61857, 35], [62157, 35], [62458, 34], [62758, 34], [63059, 32], [63359, 32], [63659, 32], [63960, 31], [64260, 31], [64561, 29], [64861, 29], [65162, 28], [65462, 28], [65763, 27], [66063, 27], [66364, 25], [66664, 25], [66965, 24], [67265, 24], [67565, 24], [67866, 22], [68166, 22], [68467, 21], [68767, 21], [69068, 20], [69368, 20], [69669, 18], [69969, 18], [70270, 17], [70570, 17], [70870, 17], [71171, 15], [71471, 15], [71772, 14], [72072, 14], [72373, 13], [72673, 13], [72974, 11], [73274, 11], [73575, 10], [73875, 10], [74176, 9], [74476, 8], [74776, 8], [75077, 7], [75377, 7], [75678, 6], [75978, 6], [76279, 4], [76579, 4], [76880, 3]], "point": [46, 195]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-01.14|+00.39|+03.52"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 137, 93, 259], "mask": [[40875, 2], [41100, 78], [41400, 78], [41701, 77], [42001, 78], [42302, 77], [42602, 77], [42903, 76], [43204, 76], [43504, 76], [43805, 75], [44105, 75], [44406, 75], [44706, 75], [45007, 74], [45307, 74], [45608, 74], [45909, 73], [46209, 73], [46510, 73], [46810, 73], [47111, 72], [47411, 72], [47712, 72], [48012, 72], [48313, 71], [48614, 70], [48914, 71], [49215, 70], [49515, 70], [49816, 69], [50116, 70], [50417, 69], [50717, 69], [51018, 68], [51318, 69], [51619, 68], [51920, 67], [52220, 68], [52521, 67], [52821, 67], [53122, 66], [53422, 67], [53723, 66], [54023, 66], [54324, 65], [54625, 65], [54925, 65], [55226, 64], [55526, 64], [55827, 11], [55847, 44], [56127, 9], [56160, 31], [56428, 9], [56459, 32], [56728, 30], [56759, 33], [57029, 63], [57330, 62], [57630, 62], [57931, 62], [58231, 62], [58532, 61], [58857, 36], [59157, 37], [59456, 38], [59756, 37], [60056, 37], [60355, 38], [60655, 38], [60955, 38], [61256, 37], [61556, 36], [61857, 35], [62157, 35], [62458, 34], [62758, 34], [63059, 32], [63359, 32], [63659, 32], [63960, 31], [64260, 31], [64561, 29], [64861, 29], [65162, 28], [65462, 28], [65763, 27], [66063, 27], [66364, 25], [66664, 25], [66965, 24], [67265, 24], [67565, 24], [67866, 22], [68166, 22], [68467, 21], [68767, 21], [69068, 20], [69368, 20], [69669, 18], [69969, 18], [70270, 17], [70570, 17], [70870, 17], [71171, 15], [71471, 15], [71772, 14], [72072, 14], [72373, 13], [72673, 13], [72974, 11], [73274, 11], [73575, 10], [73875, 10], [74176, 9], [74476, 8], [74776, 8], [75077, 7], [75377, 7], [75678, 6], [75978, 6], [76279, 4], [76579, 4], [76880, 3], [77180, 3], [77481, 1]], "point": [46, 195]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+00.83|+00.86|+02.45|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [155, 84, 166, 149], "mask": [[25056, 9], [25355, 10], [25655, 10], [25955, 11], [26255, 11], [26555, 11], [26855, 11], [27155, 11], [27455, 11], [27755, 11], [28055, 11], [28355, 11], [28655, 11], [28955, 11], [29255, 11], [29555, 11], [29855, 11], [30155, 11], [30455, 11], [30755, 11], [31055, 12], [31355, 12], [31655, 12], [31955, 12], [32255, 12], [32555, 12], [32855, 12], [33155, 12], [33455, 12], [33755, 12], [34055, 12], [34355, 12], [34655, 12], [34955, 12], [35255, 12], [35555, 12], [35855, 12], [36155, 12], [36455, 12], [36755, 12], [37055, 12], [37355, 12], [37655, 12], [37955, 12], [38255, 12], [38555, 12], [38855, 12], [39155, 12], [39455, 12], [39755, 12], [40055, 12], [40355, 12], [40655, 12], [40955, 12], [41255, 12], [41555, 12], [41855, 12], [42155, 12], [42455, 12], [42755, 12], [43055, 12], [43355, 12], [43655, 12], [43956, 11], [44259, 8], [44563, 3]], "point": [160, 115]}}, "high_idx": 7}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+00.83|+00.86|+02.45|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 21394], [21422, 268], [21726, 261], [22032, 249], [22338, 237], [22641, 232], [22943, 228], [23246, 223], [23548, 219], [23850, 215], [24152, 211], [24453, 209], [24755, 206], [25057, 142], [25200, 59], [25358, 141], [25500, 58], [25660, 139], [25800, 57], [25961, 137], [26100, 56], [26262, 136], [26400, 54], [26563, 135], [26700, 54], [26864, 134], [27000, 53], [27165, 132], [27300, 52], [27466, 131], [27600, 51], [27767, 130], [27900, 51], [28068, 128], [28200, 50], [28369, 127], [28500, 49], [28670, 126], [28800, 49], [28970, 126], [29100, 48], [29271, 124], [29400, 47], [29571, 124], [29700, 47], [29872, 123], [30000, 47], [30172, 122], [30300, 47], [30472, 122], [30600, 47], [30772, 122], [30900, 47], [31073, 121], [31200, 46], [31372, 121], [31500, 46], [31672, 121], [31800, 47], [31971, 122], [32100, 47], [32271, 121], [32400, 48], [32570, 122], [32700, 48], [32870, 122], [33000, 49], [33169, 122], [33300, 50], [33468, 123], [33600, 52], [33767, 124], [33900, 54], [34064, 127], [34200, 62], [34358, 132], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [152, 80]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-6.116, -6.116, 15.5288, 15.5288, 5.00112532, 5.00112532]], "forceVisible": true, "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-6.116, -6.116, 15.5288, 15.5288, 5.00112532, 5.00112532]], "forceVisible": true, "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+00.83|+00.86|+02.45|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [46, 72, 172, 115], "mask": [[21394, 28], [21690, 36], [21987, 45], [22281, 57], [22575, 66], [22873, 70], [23171, 75], [23469, 79], [23767, 83], [24065, 87], [24363, 90], [24662, 93], [24961, 96], [25259, 99], [25558, 102], [25857, 104], [26156, 106], [26454, 109], [26754, 110], [27053, 112], [27352, 114], [27651, 116], [27951, 117], [28250, 119], [28549, 121], [28849, 121], [29148, 123], [29447, 124], [29747, 125], [30047, 125], [30347, 125], [30647, 125], [30947, 126], [31246, 126], [31546, 126], [31847, 124], [32147, 124], [32448, 122], [32748, 122], [33049, 120], [33350, 118], [33652, 115], [33954, 110], [34262, 96]], "point": [109, 92]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.64|+00.00|+02.13"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 212], "mask": [[0, 4499], [4500, 298], [4800, 298], [5100, 297], [5400, 297], [5700, 296], [6000, 296], [6300, 295], [6600, 295], [6900, 294], [7200, 294], [7500, 293], [7800, 293], [8100, 292], [8400, 292], [8700, 291], [9000, 291], [9300, 290], [9600, 290], [9900, 289], [10200, 289], [10500, 288], [10800, 288], [11100, 287], [11400, 286], [11700, 286], [12000, 285], [12300, 285], [12600, 284], [12900, 284], [13200, 283], [13500, 283], [13800, 282], [14100, 282], [14400, 281], [14700, 281], [15000, 280], [15300, 280], [15600, 279], [15900, 279], [16200, 278], [16500, 278], [16800, 277], [17100, 277], [17400, 276], [17700, 276], [18000, 275], [18300, 274], [18600, 274], [18900, 273], [19200, 273], [19500, 272], [19800, 272], [20100, 271], [20400, 271], [20700, 270], [21000, 270], [21300, 269], [21600, 269], [21900, 268], [22200, 268], [22500, 267], [22800, 267], [23100, 266], [23400, 266], [23700, 265], [24000, 265], [24300, 264], [24600, 264], [24900, 263], [25200, 262], [25500, 262], [25800, 261], [26100, 261], [26400, 260], [26700, 260], [27000, 259], [27300, 259], [27600, 258], [27900, 258], [28200, 257], [28500, 257], [28800, 256], [29100, 256], [29400, 255], [29700, 255], [30000, 254], [30300, 254], [30600, 253], [30900, 253], [31200, 252], [31500, 252], [31800, 251], [32100, 250], [32400, 250], [32700, 249], [33000, 249], [33300, 248], [33600, 248], [33900, 247], [34200, 247], [34500, 246], [34800, 246], [35100, 245], [35400, 245], [35700, 244], [36000, 244], [36300, 243], [36600, 243], [36900, 242], [37200, 242], [37500, 241], [37800, 241], [38100, 240], [38400, 240], [38700, 239], [39000, 238], [39300, 238], [39600, 237], [39900, 237], [40200, 236], [40500, 236], [40800, 235], [41100, 235], [41400, 234], [41700, 234], [42000, 233], [42300, 233], [42600, 232], [42901, 231], [43202, 229], [43503, 228], [43804, 226], [44105, 225], [44406, 223], [44707, 222], [45008, 220], [45309, 219], [45610, 217], [45911, 215], [46212, 214], [46513, 212], [46814, 211], [47115, 209], [47416, 208], [47717, 206], [48018, 205], [48319, 203], [48619, 203], [48920, 201], [49221, 200], [49522, 198], [49823, 197], [50124, 195], [50425, 194], [50726, 192], [51027, 191], [51328, 189], [51629, 188], [51930, 186], [52231, 185], [52532, 183], [52833, 181], [53134, 180], [53435, 178], [53736, 177], [54037, 175], [54338, 174], [54638, 173], [54939, 172], [55240, 170], [55541, 169], [55842, 167], [56143, 166], [56444, 164], [56745, 163], [57046, 161], [57347, 160], [57648, 158], [57949, 157], [58250, 155], [58551, 154], [58852, 152], [59153, 150], [59454, 149], [59755, 147], [60056, 146], [60356, 145], [60657, 144], [60958, 142], [61259, 141], [61560, 139], [61861, 138], [62162, 136], [62467, 128], [62802, 5], [62809, 5], [63103, 3], [63109, 5], [63403, 3], [63410, 3]], "point": [149, 105]}}, "high_idx": 11}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+00.83|+00.86|+02.45|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.64|+00.00|+02.13"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 8019], [8024, 284], [8324, 263], [8624, 241], [8924, 242], [9223, 243], [9522, 245], [9822, 245], [10121, 247], [10421, 248], [10720, 250], [11019, 252], [11318, 253], [11617, 255], [11916, 257], [12214, 260], [12513, 263], [12811, 265], [13110, 267], [13408, 270], [13706, 273], [14004, 277], [14303, 280], [14601, 284], [14899, 44097], [58997, 299], [59297, 299], [59597, 299], [59897, 299], [60197, 299], [60497, 299], [60797, 767], [61566, 129], [61697, 167], [61867, 128], [61998, 165], [62167, 128], [62298, 165], [62467, 128], [62598, 165], [62898, 165], [63198, 165], [63498, 165], [63798, 164], [64098, 164], [64398, 164], [64698, 164], [64998, 164], [65299, 163], [65599, 162], [65899, 162], [66199, 162], [66499, 162], [66799, 162], [67099, 162], [67399, 161], [67699, 161], [67999, 101], [68102, 58], [68299, 101], [68404, 56], [68600, 100], [68706, 54], [68900, 100], [69008, 52], [69200, 100], [69310, 49], [69500, 100], [69612, 47], [69800, 100], [69915, 44], [70100, 100], [70217, 42], [70400, 100], [70519, 39], [70702, 98], [70821, 37], [71005, 95], [71123, 35], [71308, 92], [71425, 32], [71612, 88], [71727, 30], [71914, 86], [72029, 28], [72216, 84], [72331, 26], [72518, 82], [72633, 24], [72820, 80], [72936, 21], [73121, 79], [73238, 18], [73423, 77], [73540, 16], [73725, 75], [74027, 73], [74328, 72], [74629, 71], [74930, 70], [75231, 69], [75532, 68], [75833, 67], [76134, 66], [76435, 65], [76735, 65], [77036, 64], [77337, 63], [77637, 63], [77938, 62], [78239, 61], [78539, 61], [78840, 60], [79140, 60], [79441, 59], [79741, 59], [80042, 58], [80342, 58], [80643, 57], [80943, 57], [81244, 56], [81544, 56], [81845, 55], [82145, 55], [82446, 54], [82746, 54], [83046, 54], [83347, 53], [83647, 53], [83947, 53], [84248, 52], [84547, 53], [84847, 53], [85146, 54], [85445, 55], [85744, 56], [86041, 59], [86338, 62], [86631, 69], [86905, 95], [87205, 95], [87505, 95], [87806, 94], [88106, 94], [88406, 94], [88706, 94], [89006, 94], [89306, 94], [89606, 94], [89906, 94]], "point": [149, 149]}}, "high_idx": 11}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.64|+00.00|+02.13"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 8019], [8024, 284], [8324, 263], [8624, 241], [8924, 242], [9223, 243], [9522, 245], [9822, 245], [10121, 247], [10421, 248], [10720, 250], [11019, 252], [11318, 253], [11617, 255], [11916, 257], [12214, 260], [12513, 263], [12811, 265], [13110, 267], [13408, 270], [13706, 273], [14004, 277], [14303, 280], [14601, 284], [14899, 12549], [27467, 274], [27775, 261], [28079, 254], [28382, 248], [28684, 244], [28986, 240], [29288, 237], [29589, 234], [29891, 231], [30192, 230], [30492, 229], [30793, 228], [31093, 228], [31393, 228], [31693, 228], [31993, 229], [32292, 230], [32592, 230], [32892, 231], [33192, 231], [33491, 235], [33789, 25207], [58997, 299], [59297, 299], [59597, 299], [59897, 299], [60197, 299], [60497, 299], [60797, 767], [61566, 129], [61697, 167], [61867, 128], [61998, 165], [62167, 128], [62298, 165], [62467, 128], [62598, 165], [62898, 165], [63198, 165], [63498, 165], [63798, 164], [64098, 164], [64398, 164], [64698, 164], [64998, 164], [65299, 163], [65599, 162], [65899, 162], [66199, 162], [66499, 162], [66799, 162], [67099, 162], [67399, 161], [67699, 161], [67999, 101], [68102, 58], [68299, 101], [68404, 56], [68600, 100], [68706, 54], [68900, 100], [69008, 52], [69200, 100], [69310, 49], [69500, 100], [69612, 47], [69800, 100], [69915, 44], [70100, 100], [70217, 42], [70400, 100], [70519, 39], [70700, 100], [70821, 37], [71000, 100], [71123, 35], [71300, 100], [71425, 32], [71601, 99], [71727, 30], [71901, 99], [72029, 28], [72201, 99], [72331, 26], [72501, 99], [72633, 24], [72801, 99], [72936, 21], [73101, 99], [73238, 18], [73401, 99], [73540, 16], [73701, 99], [74001, 99], [74301, 99], [74601, 99], [74902, 98], [75202, 98], [75502, 98], [75802, 98], [76102, 98], [76402, 98], [76702, 98], [77002, 98], [77302, 98], [77602, 98], [77902, 98], [78203, 97], [78503, 97], [78803, 97], [79103, 97], [79403, 97], [79703, 97], [80003, 97], [80303, 97], [80603, 97], [80903, 97], [81204, 96], [81504, 96], [81804, 96], [82104, 96], [82404, 96], [82704, 96], [83004, 96], [83304, 96], [83604, 96], [83904, 96], [84204, 96], [84505, 95], [84805, 95], [85105, 95], [85405, 95], [85705, 95], [86005, 95], [86305, 95], [86605, 95], [86905, 95], [87205, 95], [87505, 95], [87806, 94], [88106, 94], [88406, 94], [88706, 94], [89006, 94], [89306, 94], [89606, 94], [89906, 94]], "point": [149, 149]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan24", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -0.25, "y": 0.9009992, "z": 3.0}, "object_poses": [{"objectName": "ButterKnife_af7bbed3", "position": {"x": -2.85124445, "y": 0.823142052, "z": 3.554316}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_8e927511", "position": {"x": -0.6570292, "y": 0.9156206, "z": 1.14349651}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_ace268e1", "position": {"x": -0.92127043, "y": 0.9368028, "z": 1.24653733}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_ace268e1", "position": {"x": -2.5198946, "y": 1.22717476, "z": 2.33388638}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Plate_656d9f06", "position": {"x": -2.54120779, "y": 1.63797593, "z": 2.72291422}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_45eb0ae6", "position": {"x": -2.57799983, "y": 0.766912639, "z": 2.10965657}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Cup_45eb0ae6", "position": {"x": -2.3863554, "y": 1.39844823, "z": 3.9368124}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_9f68a721", "position": {"x": -0.9212704, "y": 0.900055349, "z": 1.55565929}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_facc9b39", "position": {"x": -2.63610482, "y": 1.06406629, "z": 2.165714}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "PepperShaker_22dad048", "position": {"x": -1.66776383, "y": 0.124594808, "z": 3.713698}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_22dad048", "position": {"x": -1.31763232, "y": 0.896926939, "z": 1.04045594}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_0c71376c", "position": {"x": -0.6570292, "y": 0.896926939, "z": 1.14349651}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_0c71376c", "position": {"x": -1.31763232, "y": 0.896926939, "z": 0.9374153}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_21fad42f", "position": {"x": 0.950185537, "y": 0.884269059, "z": 2.631965}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_656d9f06", "position": {"x": -1.359628, "y": 1.687042, "z": 3.897483}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_0cf79ab5", "position": {"x": -1.457582, "y": 0.8236485, "z": 3.54598141}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_6b465e10", "position": {"x": 0.827999532, "y": 0.8614921, "z": 2.44999886}, "rotation": {"x": -0.0002476499, "y": 7.31222462e-05, "z": 0.000221443188}}, {"objectName": "Spatula_8e927511", "position": {"x": -1.31763232, "y": 0.9156206, "z": 1.349578}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_d2e883cd", "position": {"x": -0.7891498, "y": 1.004906, "z": 1.24653733}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_75837e5e", "position": {"x": -0.9212704, "y": 0.9436566, "z": 1.349578}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_9f68a721", "position": {"x": -0.6570291, "y": 0.900055349, "z": 1.55565929}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_ace268e1", "position": {"x": 0.800560534, "y": 0.8592821, "z": 0.8951105}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_45eb0ae6", "position": {"x": -1.138281, "y": 0.8227749, "z": 3.637175}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_eb577cac", "position": {"x": 0.6428, "y": 0.850800037, "z": 1.3754}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_0c71376c", "position": {"x": -0.6740408, "y": 1.23781919, "z": 3.85770059}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_b017884c", "position": {"x": -2.667, "y": 0.812, "z": 3.74}, "rotation": {"x": 0.0, "y": 137.145508, "z": 0.0}}, {"objectName": "PepperShaker_22dad048", "position": {"x": -1.31763232, "y": 0.896926939, "z": 1.55565929}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "ButterKnife_af7bbed3", "position": {"x": 1.02499807, "y": 0.8231421, "z": 0.984}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_facc9b39", "position": {"x": 0.950185537, "y": 0.8916735, "z": 2.19835663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0957e87", "position": {"x": 0.8206999, "y": 0.125206232, "z": 0.981317639}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6dc531a2", "position": {"x": -2.57799983, "y": 0.8430006, "z": 2.27782869}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Spoon_ac730c84", "position": {"x": 0.8044446, "y": 0.6930648, "z": 2.49641538}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_21fad42f", "position": {"x": -1.31763232, "y": 0.961789846, "z": 1.14349663}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_b53567ce", "position": {"x": -2.291005, "y": 0.819215238, "z": 3.61599422}, "rotation": {"x": 89.97203, "y": 0.0265246946, "z": 0.0}}, {"objectName": "Mug_2e53b233", "position": {"x": -1.83695292, "y": 0.697622, "z": 3.7747}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_f44f174d", "position": {"x": -2.5198946, "y": 0.993675232, "z": 2.38994384}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}], "object_toggles": [], "random_seed": 3410260654, "scene_num": 24}, "task_id": "trial_T20190909_072913_476860", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A271V19C06841K_37Q970SNZHZXCQJ4PYIO8O6K8KT1SC", "high_descs": ["turn left, walk straight, turn right to the counter", "pick up knife from the counter", "turn around, turn left to the counter", "cut in to bread with the knife", "turn around, walk to the sink", "open cabinet door, place knife in to cabinet, close door", "turn around, walk straight to the counter", "pick up slice of bread from counter", "turn around, walk straight, turn right to the microwave", "open door, place bread in to microwave, close door, cook, open door, remove bread, close door", "turn around, turn right to the fridge ", "open door, place bread in to fridge, close door"], "task_desc": "pick up knife, cut bread, cook bread in microwave, place bread in to fridge ", "votes": [1, 1]}, {"assignment_id": "A3HL2LL0LEPZT8_3CP1TO84PWS3V93PW65UKW6XM6B254", "high_descs": ["Turn left, go forward to the oven, turn right.", "Take the knife on the counter in front of the toaster.", "Turn around, go forward a step, turn left, go forward to the counter to the left of the stove top.", "Cut the bread on the counter into slices.", "Turn around, go forward, turn right before passing the sink.", "Put the knife in the cabinet beneath the sink.", "Turn right, go forward to the counter with the bread.", "Take a slice of bread from the counter.", "Turn around, go forward, turn right to the sink, go to the sink.", "Heat the bread in the microwave above the sink. Take the bread from the microwave.", "Turn around, go forward, turn right at the fridge. Go to the fridge.", "Put the bread in front of the lettuce in the fridge."], "task_desc": "Put a hot slice of bread in a fridge.", "votes": [1, 1]}, {"assignment_id": "A1O3TWBUDONVLO_3TXD01ZLD78UJ28R1LWQLNVB50OU4W", "high_descs": ["Turn left and walk forward and turn right to face the knife on the counter.", "Pick up the knife on the counter.", "Turn right and cross the room to face the bread on the counter.", "Cut the bread on the counter into slices.", "Turn left and step forward to face the right side cabinet under the sink.", "Place the knife in the right side cabinet.", "Turn right to face the bread on the counter.", "Pick up a slice of bread on the counter.", "Turn left and step forward to face the microwave.", "Heat the slice of bread in the microwave and remove it.", "Turn left and step forward to face the fridge.", "Place the slice of bread in the fridge."], "task_desc": "To heat a slice of bread and place it in the fridge.", "votes": [1, 1]}]}}