
(define (problem plan_trial_T20190908_230154_566315)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Apple_bar__minus_01_dot_50_bar__plus_01_dot_25_bar__minus_00_dot_91 - object
        Bowl_bar__minus_01_dot_28_bar__plus_01_dot_66_bar__minus_01_dot_61 - object
        Bread_bar__plus_00_dot_29_bar__plus_00_dot_97_bar__plus_00_dot_53 - object
        Bread_bar__plus_00_dot_40_bar__plus_00_dot_96_bar__plus_00_dot_11 - object
        ButterKnife_bar__plus_00_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_74 - object
        ButterKnife_bar__plus_00_dot_52_bar__plus_00_dot_93_bar__plus_01_dot_45 - object
        Chair_bar__minus_00_dot_54_bar__plus_00_dot_61_bar__plus_01_dot_26 - object
        Cup_bar__plus_00_dot_29_bar__plus_00_dot_93_bar__plus_00_dot_99 - object
        Cup_bar__minus_01_dot_12_bar__plus_00_dot_13_bar__minus_01_dot_54 - object
        Cup_bar__minus_01_dot_61_bar__plus_00_dot_86_bar__minus_00_dot_91 - object
        DishSponge_bar__plus_00_dot_33_bar__plus_01_dot_66_bar__minus_02_dot_16 - object
        Egg_bar__minus_00_dot_06_bar__plus_00_dot_82_bar__minus_02_dot_12 - object
        Egg_bar__minus_01_dot_32_bar__plus_00_dot_91_bar__minus_00_dot_70 - object
        Faucet_bar__minus_00_dot_14_bar__plus_01_dot_03_bar__minus_02_dot_22 - object
        Fork_bar__plus_01_dot_62_bar__plus_00_dot_91_bar__minus_01_dot_92 - object
        Knife_bar__plus_01_dot_74_bar__plus_00_dot_94_bar__minus_01_dot_56 - object
        Knife_bar__minus_00_dot_06_bar__plus_00_dot_79_bar__minus_02_dot_03 - object
        Knife_bar__minus_00_dot_23_bar__plus_00_dot_94_bar__minus_01_dot_71 - object
        Ladle_bar__plus_00_dot_07_bar__plus_00_dot_98_bar__plus_01_dot_45 - object
        Lettuce_bar__plus_01_dot_85_bar__plus_00_dot_99_bar__minus_02_dot_09 - object
        Lettuce_bar__minus_01_dot_43_bar__plus_00_dot_93_bar__minus_00_dot_54 - object
        Lettuce_bar__minus_01_dot_55_bar__plus_01_dot_26_bar__minus_00_dot_50 - object
        LightSwitch_bar__plus_00_dot_11_bar__plus_01_dot_32_bar__plus_02_dot_75 - object
        Mug_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_08 - object
        Pan_bar__plus_01_dot_82_bar__plus_00_dot_95_bar__minus_00_dot_35 - object
        PepperShaker_bar__plus_01_dot_36_bar__plus_00_dot_88_bar__plus_02_dot_51 - object
        PepperShaker_bar__plus_01_dot_56_bar__plus_00_dot_91_bar__minus_01_dot_45 - object
        PepperShaker_bar__minus_00_dot_74_bar__plus_00_dot_11_bar__minus_01_dot_87 - object
        Plate_bar__plus_01_dot_63_bar__plus_00_dot_12_bar__minus_01_dot_40 - object
        Plate_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_76 - object
        Plate_bar__minus_01_dot_50_bar__plus_01_dot_19_bar__minus_00_dot_70 - object
        Potato_bar__plus_00_dot_52_bar__plus_00_dot_96_bar__plus_00_dot_53 - object
        Potato_bar__minus_01_dot_44_bar__plus_00_dot_89_bar__minus_01_dot_01 - object
        Pot_bar__plus_01_dot_57_bar__plus_00_dot_95_bar__minus_00_dot_76 - object
        SaltShaker_bar__minus_01_dot_03_bar__plus_00_dot_12_bar__minus_01_dot_46 - object
        Sink_bar__minus_00_dot_11_bar__plus_00_dot_89_bar__minus_02_dot_01 - object
        SoapBottle_bar__plus_01_dot_83_bar__plus_00_dot_91_bar__minus_01_dot_80 - object
        SoapBottle_bar__plus_01_dot_87_bar__plus_01_dot_66_bar__minus_01_dot_87 - object
        Spatula_bar__plus_00_dot_41_bar__plus_00_dot_95_bar__plus_01_dot_22 - object
        Spatula_bar__minus_00_dot_05_bar__plus_00_dot_95_bar__plus_00_dot_31 - object
        Spoon_bar__plus_00_dot_07_bar__plus_00_dot_93_bar__plus_01_dot_22 - object
        Spoon_bar__plus_00_dot_52_bar__plus_00_dot_93_bar__plus_00_dot_99 - object
        StoveKnob_bar__plus_02_dot_04_bar__plus_01_dot_09_bar__minus_00_dot_25 - object
        StoveKnob_bar__plus_02_dot_04_bar__plus_01_dot_09_bar__minus_00_dot_41 - object
        StoveKnob_bar__plus_02_dot_04_bar__plus_01_dot_09_bar__minus_00_dot_65 - object
        StoveKnob_bar__plus_02_dot_04_bar__plus_01_dot_09_bar__minus_00_dot_82 - object
        Tomato_bar__plus_01_dot_87_bar__plus_00_dot_14_bar__plus_00_dot_12 - object
        Window_bar__plus_02_dot_11_bar__plus_01_dot_50_bar__plus_01_dot_07 - object
        WineBottle_bar__plus_01_dot_83_bar__plus_00_dot_91_bar__minus_01_dot_21 - object
        WineBottle_bar__plus_01_dot_95_bar__plus_01_dot_66_bar__minus_01_dot_48 - object
        Cabinet_bar__plus_00_dot_20_bar__plus_02_dot_02_bar__minus_02_dot_00 - receptacle
        Cabinet_bar__plus_01_dot_18_bar__plus_02_dot_02_bar__minus_02_dot_00 - receptacle
        Cabinet_bar__plus_01_dot_38_bar__plus_00_dot_47_bar__minus_01_dot_69 - receptacle
        Cabinet_bar__plus_01_dot_39_bar__plus_00_dot_47_bar__minus_01_dot_06 - receptacle
        Cabinet_bar__plus_01_dot_72_bar__plus_02_dot_02_bar__minus_02_dot_00 - receptacle
        Cabinet_bar__plus_01_dot_75_bar__plus_02_dot_02_bar__minus_01_dot_03 - receptacle
        Cabinet_bar__plus_01_dot_75_bar__plus_02_dot_02_bar__minus_01_dot_40 - receptacle
        Cabinet_bar__minus_00_dot_46_bar__plus_02_dot_27_bar__minus_02_dot_00 - receptacle
        Cabinet_bar__minus_00_dot_82_bar__plus_00_dot_47_bar__minus_01_dot_69 - receptacle
        Cabinet_bar__minus_00_dot_84_bar__plus_00_dot_47_bar__minus_01_dot_67 - receptacle
        Cabinet_bar__minus_01_dot_10_bar__plus_02_dot_02_bar__minus_02_dot_00 - receptacle
        Cabinet_bar__minus_01_dot_15_bar__plus_02_dot_02_bar__minus_01_dot_98 - receptacle
        Cabinet_bar__minus_01_dot_15_bar__plus_02_dot_27_bar__minus_00_dot_83 - receptacle
        CoffeeMachine_bar__plus_01_dot_37_bar__plus_00_dot_90_bar__minus_02_dot_11 - receptacle
        CounterTop_bar__plus_00_dot_23_bar__plus_00_dot_95_bar__minus_02_dot_00 - receptacle
        DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68 - receptacle
        Drawer_bar__minus_00_dot_82_bar__plus_00_dot_75_bar__minus_01_dot_69 - receptacle
        Fridge_bar__minus_01_dot_50_bar__plus_00_dot_00_bar__minus_00_dot_70 - receptacle
        GarbageCan_bar__plus_01_dot_87_bar__plus_00_dot_00_bar__plus_00_dot_13 - receptacle
        Microwave_bar__minus_01_dot_23_bar__plus_00_dot_90_bar__minus_01_dot_68 - receptacle
        Shelf_bar__plus_01_dot_55_bar__plus_00_dot_16_bar__plus_02_dot_48 - receptacle
        Shelf_bar__plus_01_dot_55_bar__plus_00_dot_54_bar__plus_02_dot_48 - receptacle
        Shelf_bar__plus_01_dot_55_bar__plus_00_dot_87_bar__plus_02_dot_48 - receptacle
        Sink_bar__minus_00_dot_11_bar__plus_00_dot_89_bar__minus_02_dot_01_bar_SinkBasin - receptacle
        StoveBurner_bar__plus_01_dot_57_bar__plus_00_dot_94_bar__minus_00_dot_36 - receptacle
        StoveBurner_bar__plus_01_dot_57_bar__plus_00_dot_94_bar__minus_00_dot_76 - receptacle
        StoveBurner_bar__plus_01_dot_85_bar__plus_00_dot_94_bar__minus_00_dot_36 - receptacle
        StoveBurner_bar__plus_01_dot_85_bar__plus_00_dot_94_bar__minus_00_dot_76 - receptacle
        Toaster_bar__plus_00_dot_78_bar__plus_00_dot_90_bar__minus_02_dot_08 - receptacle
        loc_bar_4_bar__minus_5_bar_1_bar__minus_30 - location
        loc_bar_3_bar__minus_3_bar_2_bar_45 - location
        loc_bar_4_bar__minus_3_bar_1_bar_45 - location
        loc_bar_1_bar_9_bar_0_bar_30 - location
        loc_bar__minus_1_bar__minus_5_bar_3_bar_30 - location
        loc_bar__minus_1_bar__minus_3_bar_2_bar_45 - location
        loc_bar__minus_4_bar_3_bar_1_bar_45 - location
        loc_bar__minus_4_bar_5_bar_1_bar_60 - location
        loc_bar_6_bar_8_bar_0_bar_60 - location
        loc_bar__minus_1_bar__minus_5_bar_2_bar__minus_30 - location
        loc_bar_4_bar_6_bar_0_bar_60 - location
        loc_bar__minus_1_bar__minus_5_bar_2_bar_45 - location
        loc_bar_5_bar_1_bar_1_bar_60 - location
        loc_bar_4_bar__minus_5_bar_2_bar_45 - location
        loc_bar__minus_2_bar__minus_3_bar_3_bar__minus_30 - location
        loc_bar_6_bar_4_bar_1_bar_15 - location
        loc_bar_4_bar__minus_2_bar_1_bar_30 - location
        loc_bar_1_bar__minus_5_bar_2_bar__minus_30 - location
        loc_bar_4_bar__minus_1_bar_1_bar_30 - location
        loc_bar__minus_2_bar__minus_3_bar_3_bar_60 - location
        loc_bar_0_bar__minus_4_bar_3_bar_60 - location
        loc_bar_4_bar__minus_4_bar_1_bar__minus_30 - location
        loc_bar_5_bar_5_bar_0_bar_45 - location
        loc_bar_2_bar__minus_5_bar_1_bar_60 - location
        loc_bar__minus_2_bar__minus_4_bar_2_bar__minus_15 - location
        loc_bar_4_bar__minus_5_bar_2_bar__minus_30 - location
        loc_bar__minus_1_bar__minus_5_bar_3_bar_60 - location
        loc_bar_3_bar__minus_5_bar_2_bar_45 - location
        loc_bar__minus_1_bar__minus_5_bar_3_bar__minus_15 - location
        loc_bar_4_bar__minus_3_bar_1_bar_30 - location
        loc_bar_4_bar__minus_1_bar_1_bar_45 - location
        loc_bar_0_bar__minus_5_bar_2_bar_45 - location
        loc_bar_2_bar__minus_5_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType Cabinet_bar__plus_01_dot_38_bar__plus_00_dot_47_bar__minus_01_dot_69 CabinetType)
        (receptacleType Shelf_bar__plus_01_dot_55_bar__plus_00_dot_87_bar__plus_02_dot_48 ShelfType)
        (receptacleType Cabinet_bar__minus_01_dot_10_bar__plus_02_dot_02_bar__minus_02_dot_00 CabinetType)
        (receptacleType Fridge_bar__minus_01_dot_50_bar__plus_00_dot_00_bar__minus_00_dot_70 FridgeType)
        (receptacleType Sink_bar__minus_00_dot_11_bar__plus_00_dot_89_bar__minus_02_dot_01_bar_SinkBasin SinkBasinType)
        (receptacleType CoffeeMachine_bar__plus_01_dot_37_bar__plus_00_dot_90_bar__minus_02_dot_11 CoffeeMachineType)
        (receptacleType Cabinet_bar__minus_01_dot_15_bar__plus_02_dot_27_bar__minus_00_dot_83 CabinetType)
        (receptacleType Shelf_bar__plus_01_dot_55_bar__plus_00_dot_16_bar__plus_02_dot_48 ShelfType)
        (receptacleType DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68 DiningTableType)
        (receptacleType StoveBurner_bar__plus_01_dot_57_bar__plus_00_dot_94_bar__minus_00_dot_76 StoveBurnerType)
        (receptacleType StoveBurner_bar__plus_01_dot_85_bar__plus_00_dot_94_bar__minus_00_dot_76 StoveBurnerType)
        (receptacleType Cabinet_bar__plus_01_dot_75_bar__plus_02_dot_02_bar__minus_01_dot_40 CabinetType)
        (receptacleType Cabinet_bar__plus_01_dot_39_bar__plus_00_dot_47_bar__minus_01_dot_06 CabinetType)
        (receptacleType StoveBurner_bar__plus_01_dot_85_bar__plus_00_dot_94_bar__minus_00_dot_36 StoveBurnerType)
        (receptacleType Toaster_bar__plus_00_dot_78_bar__plus_00_dot_90_bar__minus_02_dot_08 ToasterType)
        (receptacleType Cabinet_bar__minus_00_dot_46_bar__plus_02_dot_27_bar__minus_02_dot_00 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_84_bar__plus_00_dot_47_bar__minus_01_dot_67 CabinetType)
        (receptacleType Cabinet_bar__plus_01_dot_75_bar__plus_02_dot_02_bar__minus_01_dot_03 CabinetType)
        (receptacleType Microwave_bar__minus_01_dot_23_bar__plus_00_dot_90_bar__minus_01_dot_68 MicrowaveType)
        (receptacleType Cabinet_bar__plus_01_dot_18_bar__plus_02_dot_02_bar__minus_02_dot_00 CabinetType)
        (receptacleType Cabinet_bar__minus_01_dot_15_bar__plus_02_dot_02_bar__minus_01_dot_98 CabinetType)
        (receptacleType Cabinet_bar__plus_00_dot_20_bar__plus_02_dot_02_bar__minus_02_dot_00 CabinetType)
        (receptacleType Shelf_bar__plus_01_dot_55_bar__plus_00_dot_54_bar__plus_02_dot_48 ShelfType)
        (receptacleType Cabinet_bar__minus_00_dot_82_bar__plus_00_dot_47_bar__minus_01_dot_69 CabinetType)
        (receptacleType GarbageCan_bar__plus_01_dot_87_bar__plus_00_dot_00_bar__plus_00_dot_13 GarbageCanType)
        (receptacleType StoveBurner_bar__plus_01_dot_57_bar__plus_00_dot_94_bar__minus_00_dot_36 StoveBurnerType)
        (receptacleType CounterTop_bar__plus_00_dot_23_bar__plus_00_dot_95_bar__minus_02_dot_00 CounterTopType)
        (receptacleType Cabinet_bar__plus_01_dot_72_bar__plus_02_dot_02_bar__minus_02_dot_00 CabinetType)
        (receptacleType Drawer_bar__minus_00_dot_82_bar__plus_00_dot_75_bar__minus_01_dot_69 DrawerType)
        (objectType SoapBottle_bar__plus_01_dot_83_bar__plus_00_dot_91_bar__minus_01_dot_80 SoapBottleType)
        (objectType Knife_bar__plus_01_dot_74_bar__plus_00_dot_94_bar__minus_01_dot_56 KnifeType)
        (objectType Spatula_bar__plus_00_dot_41_bar__plus_00_dot_95_bar__plus_01_dot_22 SpatulaType)
        (objectType ButterKnife_bar__plus_00_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_74 ButterKnifeType)
        (objectType Bread_bar__plus_00_dot_29_bar__plus_00_dot_97_bar__plus_00_dot_53 BreadType)
        (objectType StoveKnob_bar__plus_02_dot_04_bar__plus_01_dot_09_bar__minus_00_dot_25 StoveKnobType)
        (objectType Knife_bar__minus_00_dot_06_bar__plus_00_dot_79_bar__minus_02_dot_03 KnifeType)
        (objectType PepperShaker_bar__plus_01_dot_56_bar__plus_00_dot_91_bar__minus_01_dot_45 PepperShakerType)
        (objectType WineBottle_bar__plus_01_dot_95_bar__plus_01_dot_66_bar__minus_01_dot_48 WineBottleType)
        (objectType Plate_bar__plus_01_dot_63_bar__plus_00_dot_12_bar__minus_01_dot_40 PlateType)
        (objectType Potato_bar__plus_00_dot_52_bar__plus_00_dot_96_bar__plus_00_dot_53 PotatoType)
        (objectType Apple_bar__minus_01_dot_50_bar__plus_01_dot_25_bar__minus_00_dot_91 AppleType)
        (objectType SaltShaker_bar__minus_01_dot_03_bar__plus_00_dot_12_bar__minus_01_dot_46 SaltShakerType)
        (objectType Pan_bar__plus_01_dot_82_bar__plus_00_dot_95_bar__minus_00_dot_35 PanType)
        (objectType PepperShaker_bar__plus_01_dot_36_bar__plus_00_dot_88_bar__plus_02_dot_51 PepperShakerType)
        (objectType Potato_bar__minus_01_dot_44_bar__plus_00_dot_89_bar__minus_01_dot_01 PotatoType)
        (objectType Lettuce_bar__minus_01_dot_43_bar__plus_00_dot_93_bar__minus_00_dot_54 LettuceType)
        (objectType Bowl_bar__minus_01_dot_28_bar__plus_01_dot_66_bar__minus_01_dot_61 BowlType)
        (objectType Spatula_bar__minus_00_dot_05_bar__plus_00_dot_95_bar__plus_00_dot_31 SpatulaType)
        (objectType Plate_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_76 PlateType)
        (objectType StoveKnob_bar__plus_02_dot_04_bar__plus_01_dot_09_bar__minus_00_dot_41 StoveKnobType)
        (objectType ButterKnife_bar__plus_00_dot_52_bar__plus_00_dot_93_bar__plus_01_dot_45 ButterKnifeType)
        (objectType Egg_bar__minus_01_dot_32_bar__plus_00_dot_91_bar__minus_00_dot_70 EggType)
        (objectType Egg_bar__minus_00_dot_06_bar__plus_00_dot_82_bar__minus_02_dot_12 EggType)
        (objectType SoapBottle_bar__plus_01_dot_87_bar__plus_01_dot_66_bar__minus_01_dot_87 SoapBottleType)
        (objectType WineBottle_bar__plus_01_dot_83_bar__plus_00_dot_91_bar__minus_01_dot_21 WineBottleType)
        (objectType StoveKnob_bar__plus_02_dot_04_bar__plus_01_dot_09_bar__minus_00_dot_82 StoveKnobType)
        (objectType Window_bar__plus_02_dot_11_bar__plus_01_dot_50_bar__plus_01_dot_07 WindowType)
        (objectType Chair_bar__minus_00_dot_54_bar__plus_00_dot_61_bar__plus_01_dot_26 ChairType)
        (objectType Bread_bar__plus_00_dot_40_bar__plus_00_dot_96_bar__plus_00_dot_11 BreadType)
        (objectType Cup_bar__plus_00_dot_29_bar__plus_00_dot_93_bar__plus_00_dot_99 CupType)
        (objectType Cup_bar__minus_01_dot_12_bar__plus_00_dot_13_bar__minus_01_dot_54 CupType)
        (objectType Mug_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_08 MugType)
        (objectType Knife_bar__minus_00_dot_23_bar__plus_00_dot_94_bar__minus_01_dot_71 KnifeType)
        (objectType Lettuce_bar__minus_01_dot_55_bar__plus_01_dot_26_bar__minus_00_dot_50 LettuceType)
        (objectType Pot_bar__plus_01_dot_57_bar__plus_00_dot_95_bar__minus_00_dot_76 PotType)
        (objectType DishSponge_bar__plus_00_dot_33_bar__plus_01_dot_66_bar__minus_02_dot_16 DishSpongeType)
        (objectType Spoon_bar__plus_00_dot_52_bar__plus_00_dot_93_bar__plus_00_dot_99 SpoonType)
        (objectType LightSwitch_bar__plus_00_dot_11_bar__plus_01_dot_32_bar__plus_02_dot_75 LightSwitchType)
        (objectType StoveKnob_bar__plus_02_dot_04_bar__plus_01_dot_09_bar__minus_00_dot_65 StoveKnobType)
        (objectType Fork_bar__plus_01_dot_62_bar__plus_00_dot_91_bar__minus_01_dot_92 ForkType)
        (objectType Tomato_bar__plus_01_dot_87_bar__plus_00_dot_14_bar__plus_00_dot_12 TomatoType)
        (objectType Plate_bar__minus_01_dot_50_bar__plus_01_dot_19_bar__minus_00_dot_70 PlateType)
        (objectType Ladle_bar__plus_00_dot_07_bar__plus_00_dot_98_bar__plus_01_dot_45 LadleType)
        (objectType Sink_bar__minus_00_dot_11_bar__plus_00_dot_89_bar__minus_02_dot_01 SinkType)
        (objectType PepperShaker_bar__minus_00_dot_74_bar__plus_00_dot_11_bar__minus_01_dot_87 PepperShakerType)
        (objectType Cup_bar__minus_01_dot_61_bar__plus_00_dot_86_bar__minus_00_dot_91 CupType)
        (objectType Spoon_bar__plus_00_dot_07_bar__plus_00_dot_93_bar__plus_01_dot_22 SpoonType)
        (objectType Lettuce_bar__plus_01_dot_85_bar__plus_00_dot_99_bar__minus_02_dot_09 LettuceType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain ShelfType SaltShakerType)
        (canContain ShelfType DishSpongeType)
        (canContain ShelfType BowlType)
        (canContain ShelfType PotType)
        (canContain ShelfType WineBottleType)
        (canContain ShelfType MugType)
        (canContain ShelfType SoapBottleType)
        (canContain ShelfType CupType)
        (canContain ShelfType PlateType)
        (canContain ShelfType PepperShakerType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain FridgeType BreadType)
        (canContain FridgeType BowlType)
        (canContain FridgeType PotType)
        (canContain FridgeType WineBottleType)
        (canContain FridgeType MugType)
        (canContain FridgeType EggType)
        (canContain FridgeType LettuceType)
        (canContain FridgeType PotatoType)
        (canContain FridgeType CupType)
        (canContain FridgeType PlateType)
        (canContain FridgeType TomatoType)
        (canContain FridgeType AppleType)
        (canContain FridgeType PanType)
        (canContain SinkBasinType DishSpongeType)
        (canContain SinkBasinType BowlType)
        (canContain SinkBasinType PotType)
        (canContain SinkBasinType MugType)
        (canContain SinkBasinType EggType)
        (canContain SinkBasinType LadleType)
        (canContain SinkBasinType ForkType)
        (canContain SinkBasinType SpoonType)
        (canContain SinkBasinType LettuceType)
        (canContain SinkBasinType PotatoType)
        (canContain SinkBasinType ButterKnifeType)
        (canContain SinkBasinType CupType)
        (canContain SinkBasinType PlateType)
        (canContain SinkBasinType TomatoType)
        (canContain SinkBasinType KnifeType)
        (canContain SinkBasinType AppleType)
        (canContain SinkBasinType PanType)
        (canContain SinkBasinType SpatulaType)
        (canContain CoffeeMachineType MugType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain ShelfType SaltShakerType)
        (canContain ShelfType DishSpongeType)
        (canContain ShelfType BowlType)
        (canContain ShelfType PotType)
        (canContain ShelfType WineBottleType)
        (canContain ShelfType MugType)
        (canContain ShelfType SoapBottleType)
        (canContain ShelfType CupType)
        (canContain ShelfType PlateType)
        (canContain ShelfType PepperShakerType)
        (canContain DiningTableType SaltShakerType)
        (canContain DiningTableType BreadType)
        (canContain DiningTableType DishSpongeType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType PotType)
        (canContain DiningTableType WineBottleType)
        (canContain DiningTableType MugType)
        (canContain DiningTableType EggType)
        (canContain DiningTableType LadleType)
        (canContain DiningTableType ForkType)
        (canContain DiningTableType SpoonType)
        (canContain DiningTableType SoapBottleType)
        (canContain DiningTableType LettuceType)
        (canContain DiningTableType PotatoType)
        (canContain DiningTableType ButterKnifeType)
        (canContain DiningTableType CupType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType PepperShakerType)
        (canContain DiningTableType TomatoType)
        (canContain DiningTableType KnifeType)
        (canContain DiningTableType AppleType)
        (canContain DiningTableType PanType)
        (canContain DiningTableType SpatulaType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain MicrowaveType MugType)
        (canContain MicrowaveType PotatoType)
        (canContain MicrowaveType EggType)
        (canContain MicrowaveType AppleType)
        (canContain MicrowaveType TomatoType)
        (canContain MicrowaveType BreadType)
        (canContain MicrowaveType CupType)
        (canContain MicrowaveType PlateType)
        (canContain MicrowaveType BowlType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain ShelfType SaltShakerType)
        (canContain ShelfType DishSpongeType)
        (canContain ShelfType BowlType)
        (canContain ShelfType PotType)
        (canContain ShelfType WineBottleType)
        (canContain ShelfType MugType)
        (canContain ShelfType SoapBottleType)
        (canContain ShelfType CupType)
        (canContain ShelfType PlateType)
        (canContain ShelfType PepperShakerType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain GarbageCanType BreadType)
        (canContain GarbageCanType DishSpongeType)
        (canContain GarbageCanType WineBottleType)
        (canContain GarbageCanType EggType)
        (canContain GarbageCanType SoapBottleType)
        (canContain GarbageCanType LettuceType)
        (canContain GarbageCanType PotatoType)
        (canContain GarbageCanType TomatoType)
        (canContain GarbageCanType AppleType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType WineBottleType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType LadleType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType WineBottleType)
        (canContain CabinetType MugType)
        (canContain CabinetType LadleType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType LadleType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (pickupable SoapBottle_bar__plus_01_dot_83_bar__plus_00_dot_91_bar__minus_01_dot_80)
        (pickupable Knife_bar__plus_01_dot_74_bar__plus_00_dot_94_bar__minus_01_dot_56)
        (pickupable Spatula_bar__plus_00_dot_41_bar__plus_00_dot_95_bar__plus_01_dot_22)
        (pickupable ButterKnife_bar__plus_00_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_74)
        (pickupable Bread_bar__plus_00_dot_29_bar__plus_00_dot_97_bar__plus_00_dot_53)
        (pickupable Knife_bar__minus_00_dot_06_bar__plus_00_dot_79_bar__minus_02_dot_03)
        (pickupable PepperShaker_bar__plus_01_dot_56_bar__plus_00_dot_91_bar__minus_01_dot_45)
        (pickupable WineBottle_bar__plus_01_dot_95_bar__plus_01_dot_66_bar__minus_01_dot_48)
        (pickupable Plate_bar__plus_01_dot_63_bar__plus_00_dot_12_bar__minus_01_dot_40)
        (pickupable Potato_bar__plus_00_dot_52_bar__plus_00_dot_96_bar__plus_00_dot_53)
        (pickupable Apple_bar__minus_01_dot_50_bar__plus_01_dot_25_bar__minus_00_dot_91)
        (pickupable SaltShaker_bar__minus_01_dot_03_bar__plus_00_dot_12_bar__minus_01_dot_46)
        (pickupable Pan_bar__plus_01_dot_82_bar__plus_00_dot_95_bar__minus_00_dot_35)
        (pickupable PepperShaker_bar__plus_01_dot_36_bar__plus_00_dot_88_bar__plus_02_dot_51)
        (pickupable Potato_bar__minus_01_dot_44_bar__plus_00_dot_89_bar__minus_01_dot_01)
        (pickupable Lettuce_bar__minus_01_dot_43_bar__plus_00_dot_93_bar__minus_00_dot_54)
        (pickupable Bowl_bar__minus_01_dot_28_bar__plus_01_dot_66_bar__minus_01_dot_61)
        (pickupable Spatula_bar__minus_00_dot_05_bar__plus_00_dot_95_bar__plus_00_dot_31)
        (pickupable Plate_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_76)
        (pickupable ButterKnife_bar__plus_00_dot_52_bar__plus_00_dot_93_bar__plus_01_dot_45)
        (pickupable Egg_bar__minus_01_dot_32_bar__plus_00_dot_91_bar__minus_00_dot_70)
        (pickupable Egg_bar__minus_00_dot_06_bar__plus_00_dot_82_bar__minus_02_dot_12)
        (pickupable SoapBottle_bar__plus_01_dot_87_bar__plus_01_dot_66_bar__minus_01_dot_87)
        (pickupable WineBottle_bar__plus_01_dot_83_bar__plus_00_dot_91_bar__minus_01_dot_21)
        (pickupable Bread_bar__plus_00_dot_40_bar__plus_00_dot_96_bar__plus_00_dot_11)
        (pickupable Cup_bar__plus_00_dot_29_bar__plus_00_dot_93_bar__plus_00_dot_99)
        (pickupable Cup_bar__minus_01_dot_12_bar__plus_00_dot_13_bar__minus_01_dot_54)
        (pickupable Mug_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_08)
        (pickupable Knife_bar__minus_00_dot_23_bar__plus_00_dot_94_bar__minus_01_dot_71)
        (pickupable Lettuce_bar__minus_01_dot_55_bar__plus_01_dot_26_bar__minus_00_dot_50)
        (pickupable Pot_bar__plus_01_dot_57_bar__plus_00_dot_95_bar__minus_00_dot_76)
        (pickupable DishSponge_bar__plus_00_dot_33_bar__plus_01_dot_66_bar__minus_02_dot_16)
        (pickupable Spoon_bar__plus_00_dot_52_bar__plus_00_dot_93_bar__plus_00_dot_99)
        (pickupable Fork_bar__plus_01_dot_62_bar__plus_00_dot_91_bar__minus_01_dot_92)
        (pickupable Tomato_bar__plus_01_dot_87_bar__plus_00_dot_14_bar__plus_00_dot_12)
        (pickupable Plate_bar__minus_01_dot_50_bar__plus_01_dot_19_bar__minus_00_dot_70)
        (pickupable Ladle_bar__plus_00_dot_07_bar__plus_00_dot_98_bar__plus_01_dot_45)
        (pickupable PepperShaker_bar__minus_00_dot_74_bar__plus_00_dot_11_bar__minus_01_dot_87)
        (pickupable Cup_bar__minus_01_dot_61_bar__plus_00_dot_86_bar__minus_00_dot_91)
        (pickupable Spoon_bar__plus_00_dot_07_bar__plus_00_dot_93_bar__plus_01_dot_22)
        (pickupable Lettuce_bar__plus_01_dot_85_bar__plus_00_dot_99_bar__minus_02_dot_09)
        (isReceptacleObject Plate_bar__plus_01_dot_63_bar__plus_00_dot_12_bar__minus_01_dot_40)
        (isReceptacleObject Pan_bar__plus_01_dot_82_bar__plus_00_dot_95_bar__minus_00_dot_35)
        (isReceptacleObject Bowl_bar__minus_01_dot_28_bar__plus_01_dot_66_bar__minus_01_dot_61)
        (isReceptacleObject Plate_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_76)
        (isReceptacleObject Cup_bar__plus_00_dot_29_bar__plus_00_dot_93_bar__plus_00_dot_99)
        (isReceptacleObject Cup_bar__minus_01_dot_12_bar__plus_00_dot_13_bar__minus_01_dot_54)
        (isReceptacleObject Mug_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_08)
        (isReceptacleObject Pot_bar__plus_01_dot_57_bar__plus_00_dot_95_bar__minus_00_dot_76)
        (isReceptacleObject Plate_bar__minus_01_dot_50_bar__plus_01_dot_19_bar__minus_00_dot_70)
        (isReceptacleObject Cup_bar__minus_01_dot_61_bar__plus_00_dot_86_bar__minus_00_dot_91)
        (openable Cabinet_bar__plus_01_dot_38_bar__plus_00_dot_47_bar__minus_01_dot_69)
        (openable Cabinet_bar__minus_01_dot_10_bar__plus_02_dot_02_bar__minus_02_dot_00)
        (openable Fridge_bar__minus_01_dot_50_bar__plus_00_dot_00_bar__minus_00_dot_70)
        (openable Cabinet_bar__plus_01_dot_75_bar__plus_02_dot_02_bar__minus_01_dot_40)
        (openable Cabinet_bar__plus_01_dot_39_bar__plus_00_dot_47_bar__minus_01_dot_06)
        (openable Cabinet_bar__minus_00_dot_84_bar__plus_00_dot_47_bar__minus_01_dot_67)
        (openable Cabinet_bar__plus_01_dot_75_bar__plus_02_dot_02_bar__minus_01_dot_03)
        (openable Microwave_bar__minus_01_dot_23_bar__plus_00_dot_90_bar__minus_01_dot_68)
        (openable Cabinet_bar__plus_01_dot_18_bar__plus_02_dot_02_bar__minus_02_dot_00)
        (openable Cabinet_bar__minus_01_dot_15_bar__plus_02_dot_02_bar__minus_01_dot_98)
        (openable Cabinet_bar__plus_00_dot_20_bar__plus_02_dot_02_bar__minus_02_dot_00)
        (openable Cabinet_bar__minus_00_dot_82_bar__plus_00_dot_47_bar__minus_01_dot_69)
        (openable Cabinet_bar__plus_01_dot_72_bar__plus_02_dot_02_bar__minus_02_dot_00)
        
        (atLocation agent1 loc_bar_2_bar__minus_5_bar_3_bar_30)
        
        (cleanable Knife_bar__plus_01_dot_74_bar__plus_00_dot_94_bar__minus_01_dot_56)
        (cleanable Spatula_bar__plus_00_dot_41_bar__plus_00_dot_95_bar__plus_01_dot_22)
        (cleanable ButterKnife_bar__plus_00_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_74)
        (cleanable Knife_bar__minus_00_dot_06_bar__plus_00_dot_79_bar__minus_02_dot_03)
        (cleanable Plate_bar__plus_01_dot_63_bar__plus_00_dot_12_bar__minus_01_dot_40)
        (cleanable Potato_bar__plus_00_dot_52_bar__plus_00_dot_96_bar__plus_00_dot_53)
        (cleanable Apple_bar__minus_01_dot_50_bar__plus_01_dot_25_bar__minus_00_dot_91)
        (cleanable Pan_bar__plus_01_dot_82_bar__plus_00_dot_95_bar__minus_00_dot_35)
        (cleanable Potato_bar__minus_01_dot_44_bar__plus_00_dot_89_bar__minus_01_dot_01)
        (cleanable Lettuce_bar__minus_01_dot_43_bar__plus_00_dot_93_bar__minus_00_dot_54)
        (cleanable Bowl_bar__minus_01_dot_28_bar__plus_01_dot_66_bar__minus_01_dot_61)
        (cleanable Spatula_bar__minus_00_dot_05_bar__plus_00_dot_95_bar__plus_00_dot_31)
        (cleanable Plate_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_76)
        (cleanable ButterKnife_bar__plus_00_dot_52_bar__plus_00_dot_93_bar__plus_01_dot_45)
        (cleanable Egg_bar__minus_01_dot_32_bar__plus_00_dot_91_bar__minus_00_dot_70)
        (cleanable Egg_bar__minus_00_dot_06_bar__plus_00_dot_82_bar__minus_02_dot_12)
        (cleanable Cup_bar__plus_00_dot_29_bar__plus_00_dot_93_bar__plus_00_dot_99)
        (cleanable Cup_bar__minus_01_dot_12_bar__plus_00_dot_13_bar__minus_01_dot_54)
        (cleanable Mug_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_08)
        (cleanable Knife_bar__minus_00_dot_23_bar__plus_00_dot_94_bar__minus_01_dot_71)
        (cleanable Lettuce_bar__minus_01_dot_55_bar__plus_01_dot_26_bar__minus_00_dot_50)
        (cleanable Pot_bar__plus_01_dot_57_bar__plus_00_dot_95_bar__minus_00_dot_76)
        (cleanable DishSponge_bar__plus_00_dot_33_bar__plus_01_dot_66_bar__minus_02_dot_16)
        (cleanable Spoon_bar__plus_00_dot_52_bar__plus_00_dot_93_bar__plus_00_dot_99)
        (cleanable Fork_bar__plus_01_dot_62_bar__plus_00_dot_91_bar__minus_01_dot_92)
        (cleanable Tomato_bar__plus_01_dot_87_bar__plus_00_dot_14_bar__plus_00_dot_12)
        (cleanable Plate_bar__minus_01_dot_50_bar__plus_01_dot_19_bar__minus_00_dot_70)
        (cleanable Ladle_bar__plus_00_dot_07_bar__plus_00_dot_98_bar__plus_01_dot_45)
        (cleanable Cup_bar__minus_01_dot_61_bar__plus_00_dot_86_bar__minus_00_dot_91)
        (cleanable Spoon_bar__plus_00_dot_07_bar__plus_00_dot_93_bar__plus_01_dot_22)
        (cleanable Lettuce_bar__plus_01_dot_85_bar__plus_00_dot_99_bar__minus_02_dot_09)
        
        (heatable Bread_bar__plus_00_dot_29_bar__plus_00_dot_97_bar__plus_00_dot_53)
        (heatable Plate_bar__plus_01_dot_63_bar__plus_00_dot_12_bar__minus_01_dot_40)
        (heatable Potato_bar__plus_00_dot_52_bar__plus_00_dot_96_bar__plus_00_dot_53)
        (heatable Apple_bar__minus_01_dot_50_bar__plus_01_dot_25_bar__minus_00_dot_91)
        (heatable Potato_bar__minus_01_dot_44_bar__plus_00_dot_89_bar__minus_01_dot_01)
        (heatable Plate_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_76)
        (heatable Egg_bar__minus_01_dot_32_bar__plus_00_dot_91_bar__minus_00_dot_70)
        (heatable Egg_bar__minus_00_dot_06_bar__plus_00_dot_82_bar__minus_02_dot_12)
        (heatable Bread_bar__plus_00_dot_40_bar__plus_00_dot_96_bar__plus_00_dot_11)
        (heatable Cup_bar__plus_00_dot_29_bar__plus_00_dot_93_bar__plus_00_dot_99)
        (heatable Cup_bar__minus_01_dot_12_bar__plus_00_dot_13_bar__minus_01_dot_54)
        (heatable Mug_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_08)
        (heatable Tomato_bar__plus_01_dot_87_bar__plus_00_dot_14_bar__plus_00_dot_12)
        (heatable Plate_bar__minus_01_dot_50_bar__plus_01_dot_19_bar__minus_00_dot_70)
        (heatable Cup_bar__minus_01_dot_61_bar__plus_00_dot_86_bar__minus_00_dot_91)
        (coolable Bread_bar__plus_00_dot_29_bar__plus_00_dot_97_bar__plus_00_dot_53)
        (coolable WineBottle_bar__plus_01_dot_95_bar__plus_01_dot_66_bar__minus_01_dot_48)
        (coolable Plate_bar__plus_01_dot_63_bar__plus_00_dot_12_bar__minus_01_dot_40)
        (coolable Potato_bar__plus_00_dot_52_bar__plus_00_dot_96_bar__plus_00_dot_53)
        (coolable Apple_bar__minus_01_dot_50_bar__plus_01_dot_25_bar__minus_00_dot_91)
        (coolable Pan_bar__plus_01_dot_82_bar__plus_00_dot_95_bar__minus_00_dot_35)
        (coolable Potato_bar__minus_01_dot_44_bar__plus_00_dot_89_bar__minus_01_dot_01)
        (coolable Lettuce_bar__minus_01_dot_43_bar__plus_00_dot_93_bar__minus_00_dot_54)
        (coolable Bowl_bar__minus_01_dot_28_bar__plus_01_dot_66_bar__minus_01_dot_61)
        (coolable Plate_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_76)
        (coolable Egg_bar__minus_01_dot_32_bar__plus_00_dot_91_bar__minus_00_dot_70)
        (coolable Egg_bar__minus_00_dot_06_bar__plus_00_dot_82_bar__minus_02_dot_12)
        (coolable WineBottle_bar__plus_01_dot_83_bar__plus_00_dot_91_bar__minus_01_dot_21)
        (coolable Bread_bar__plus_00_dot_40_bar__plus_00_dot_96_bar__plus_00_dot_11)
        (coolable Cup_bar__plus_00_dot_29_bar__plus_00_dot_93_bar__plus_00_dot_99)
        (coolable Cup_bar__minus_01_dot_12_bar__plus_00_dot_13_bar__minus_01_dot_54)
        (coolable Mug_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_08)
        (coolable Lettuce_bar__minus_01_dot_55_bar__plus_01_dot_26_bar__minus_00_dot_50)
        (coolable Pot_bar__plus_01_dot_57_bar__plus_00_dot_95_bar__minus_00_dot_76)
        (coolable Tomato_bar__plus_01_dot_87_bar__plus_00_dot_14_bar__plus_00_dot_12)
        (coolable Plate_bar__minus_01_dot_50_bar__plus_01_dot_19_bar__minus_00_dot_70)
        (coolable Cup_bar__minus_01_dot_61_bar__plus_00_dot_86_bar__minus_00_dot_91)
        (coolable Lettuce_bar__plus_01_dot_85_bar__plus_00_dot_99_bar__minus_02_dot_09)
        
        
        
        
        
        (sliceable Bread_bar__plus_00_dot_29_bar__plus_00_dot_97_bar__plus_00_dot_53)
        (sliceable Potato_bar__plus_00_dot_52_bar__plus_00_dot_96_bar__plus_00_dot_53)
        (sliceable Apple_bar__minus_01_dot_50_bar__plus_01_dot_25_bar__minus_00_dot_91)
        (sliceable Potato_bar__minus_01_dot_44_bar__plus_00_dot_89_bar__minus_01_dot_01)
        (sliceable Lettuce_bar__minus_01_dot_43_bar__plus_00_dot_93_bar__minus_00_dot_54)
        (sliceable Egg_bar__minus_01_dot_32_bar__plus_00_dot_91_bar__minus_00_dot_70)
        (sliceable Egg_bar__minus_00_dot_06_bar__plus_00_dot_82_bar__minus_02_dot_12)
        (sliceable Bread_bar__plus_00_dot_40_bar__plus_00_dot_96_bar__plus_00_dot_11)
        (sliceable Lettuce_bar__minus_01_dot_55_bar__plus_01_dot_26_bar__minus_00_dot_50)
        (sliceable Tomato_bar__plus_01_dot_87_bar__plus_00_dot_14_bar__plus_00_dot_12)
        (sliceable Lettuce_bar__plus_01_dot_85_bar__plus_00_dot_99_bar__minus_02_dot_09)
        
        (inReceptacle Pan_bar__plus_01_dot_82_bar__plus_00_dot_95_bar__minus_00_dot_35 StoveBurner_bar__plus_01_dot_85_bar__plus_00_dot_94_bar__minus_00_dot_36)
        (inReceptacle Pot_bar__plus_01_dot_57_bar__plus_00_dot_95_bar__minus_00_dot_76 StoveBurner_bar__plus_01_dot_57_bar__plus_00_dot_94_bar__minus_00_dot_76)
        (inReceptacle Bowl_bar__minus_01_dot_28_bar__plus_01_dot_66_bar__minus_01_dot_61 Cabinet_bar__minus_01_dot_15_bar__plus_02_dot_02_bar__minus_01_dot_98)
        (inReceptacle DishSponge_bar__plus_00_dot_33_bar__plus_01_dot_66_bar__minus_02_dot_16 Cabinet_bar__plus_00_dot_20_bar__plus_02_dot_02_bar__minus_02_dot_00)
        (inReceptacle WineBottle_bar__plus_01_dot_95_bar__plus_01_dot_66_bar__minus_01_dot_48 Cabinet_bar__plus_01_dot_75_bar__plus_02_dot_02_bar__minus_01_dot_40)
        (inReceptacle SoapBottle_bar__plus_01_dot_87_bar__plus_01_dot_66_bar__minus_01_dot_87 Cabinet_bar__plus_01_dot_75_bar__plus_02_dot_02_bar__minus_01_dot_40)
        (inReceptacle PepperShaker_bar__minus_00_dot_74_bar__plus_00_dot_11_bar__minus_01_dot_87 Cabinet_bar__minus_00_dot_82_bar__plus_00_dot_47_bar__minus_01_dot_69)
        (inReceptacle Plate_bar__plus_01_dot_63_bar__plus_00_dot_12_bar__minus_01_dot_40 Cabinet_bar__plus_01_dot_39_bar__plus_00_dot_47_bar__minus_01_dot_06)
        (inReceptacle SaltShaker_bar__minus_01_dot_03_bar__plus_00_dot_12_bar__minus_01_dot_46 Cabinet_bar__minus_00_dot_84_bar__plus_00_dot_47_bar__minus_01_dot_67)
        (inReceptacle Cup_bar__minus_01_dot_12_bar__plus_00_dot_13_bar__minus_01_dot_54 Cabinet_bar__minus_00_dot_84_bar__plus_00_dot_47_bar__minus_01_dot_67)
        (inReceptacle Pan_bar__plus_01_dot_82_bar__plus_00_dot_95_bar__minus_00_dot_35 StoveBurner_bar__plus_01_dot_85_bar__plus_00_dot_94_bar__minus_00_dot_76)
        (inReceptacle Spatula_bar__minus_00_dot_05_bar__plus_00_dot_95_bar__plus_00_dot_31 DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68)
        (inReceptacle Spatula_bar__plus_00_dot_41_bar__plus_00_dot_95_bar__plus_01_dot_22 DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68)
        (inReceptacle Potato_bar__plus_00_dot_52_bar__plus_00_dot_96_bar__plus_00_dot_53 DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68)
        (inReceptacle Spoon_bar__plus_00_dot_52_bar__plus_00_dot_93_bar__plus_00_dot_99 DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68)
        (inReceptacle Ladle_bar__plus_00_dot_07_bar__plus_00_dot_98_bar__plus_01_dot_45 DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68)
        (inReceptacle Bread_bar__plus_00_dot_40_bar__plus_00_dot_96_bar__plus_00_dot_11 DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68)
        (inReceptacle Cup_bar__plus_00_dot_29_bar__plus_00_dot_93_bar__plus_00_dot_99 DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68)
        (inReceptacle Bread_bar__plus_00_dot_29_bar__plus_00_dot_97_bar__plus_00_dot_53 DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68)
        (inReceptacle Mug_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_08 DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68)
        (inReceptacle Spoon_bar__plus_00_dot_07_bar__plus_00_dot_93_bar__plus_01_dot_22 DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68)
        (inReceptacle Plate_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_76 DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68)
        (inReceptacle ButterKnife_bar__plus_00_dot_52_bar__plus_00_dot_93_bar__plus_01_dot_45 DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68)
        (inReceptacle PepperShaker_bar__plus_01_dot_56_bar__plus_00_dot_91_bar__minus_01_dot_45 CounterTop_bar__plus_00_dot_23_bar__plus_00_dot_95_bar__minus_02_dot_00)
        (inReceptacle SoapBottle_bar__plus_01_dot_83_bar__plus_00_dot_91_bar__minus_01_dot_80 CounterTop_bar__plus_00_dot_23_bar__plus_00_dot_95_bar__minus_02_dot_00)
        (inReceptacle Knife_bar__plus_01_dot_74_bar__plus_00_dot_94_bar__minus_01_dot_56 CounterTop_bar__plus_00_dot_23_bar__plus_00_dot_95_bar__minus_02_dot_00)
        (inReceptacle WineBottle_bar__plus_01_dot_83_bar__plus_00_dot_91_bar__minus_01_dot_21 CounterTop_bar__plus_00_dot_23_bar__plus_00_dot_95_bar__minus_02_dot_00)
        (inReceptacle Fork_bar__plus_01_dot_62_bar__plus_00_dot_91_bar__minus_01_dot_92 CounterTop_bar__plus_00_dot_23_bar__plus_00_dot_95_bar__minus_02_dot_00)
        (inReceptacle ButterKnife_bar__plus_00_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_74 CounterTop_bar__plus_00_dot_23_bar__plus_00_dot_95_bar__minus_02_dot_00)
        (inReceptacle Knife_bar__minus_00_dot_23_bar__plus_00_dot_94_bar__minus_01_dot_71 CounterTop_bar__plus_00_dot_23_bar__plus_00_dot_95_bar__minus_02_dot_00)
        (inReceptacle Lettuce_bar__plus_01_dot_85_bar__plus_00_dot_99_bar__minus_02_dot_09 CounterTop_bar__plus_00_dot_23_bar__plus_00_dot_95_bar__minus_02_dot_00)
        (inReceptacle Tomato_bar__plus_01_dot_87_bar__plus_00_dot_14_bar__plus_00_dot_12 GarbageCan_bar__plus_01_dot_87_bar__plus_00_dot_00_bar__plus_00_dot_13)
        (inReceptacle PepperShaker_bar__plus_01_dot_36_bar__plus_00_dot_88_bar__plus_02_dot_51 Shelf_bar__plus_01_dot_55_bar__plus_00_dot_87_bar__plus_02_dot_48)
        (inReceptacle Potato_bar__minus_01_dot_44_bar__plus_00_dot_89_bar__minus_01_dot_01 Fridge_bar__minus_01_dot_50_bar__plus_00_dot_00_bar__minus_00_dot_70)
        (inReceptacle Apple_bar__minus_01_dot_50_bar__plus_01_dot_25_bar__minus_00_dot_91 Fridge_bar__minus_01_dot_50_bar__plus_00_dot_00_bar__minus_00_dot_70)
        (inReceptacle Plate_bar__minus_01_dot_50_bar__plus_01_dot_19_bar__minus_00_dot_70 Fridge_bar__minus_01_dot_50_bar__plus_00_dot_00_bar__minus_00_dot_70)
        (inReceptacle Cup_bar__minus_01_dot_61_bar__plus_00_dot_86_bar__minus_00_dot_91 Fridge_bar__minus_01_dot_50_bar__plus_00_dot_00_bar__minus_00_dot_70)
        (inReceptacle Lettuce_bar__minus_01_dot_43_bar__plus_00_dot_93_bar__minus_00_dot_54 Fridge_bar__minus_01_dot_50_bar__plus_00_dot_00_bar__minus_00_dot_70)
        (inReceptacle Lettuce_bar__minus_01_dot_55_bar__plus_01_dot_26_bar__minus_00_dot_50 Fridge_bar__minus_01_dot_50_bar__plus_00_dot_00_bar__minus_00_dot_70)
        (inReceptacle Egg_bar__minus_01_dot_32_bar__plus_00_dot_91_bar__minus_00_dot_70 Fridge_bar__minus_01_dot_50_bar__plus_00_dot_00_bar__minus_00_dot_70)
        (inReceptacle Knife_bar__minus_00_dot_06_bar__plus_00_dot_79_bar__minus_02_dot_03 Sink_bar__minus_00_dot_11_bar__plus_00_dot_89_bar__minus_02_dot_01_bar_SinkBasin)
        (inReceptacle Egg_bar__minus_00_dot_06_bar__plus_00_dot_82_bar__minus_02_dot_12 Sink_bar__minus_00_dot_11_bar__plus_00_dot_89_bar__minus_02_dot_01_bar_SinkBasin)
        
        
        (receptacleAtLocation Cabinet_bar__plus_00_dot_20_bar__plus_02_dot_02_bar__minus_02_dot_00 loc_bar_1_bar__minus_5_bar_2_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_18_bar__plus_02_dot_02_bar__minus_02_dot_00 loc_bar_4_bar__minus_5_bar_2_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_38_bar__plus_00_dot_47_bar__minus_01_dot_69 loc_bar_3_bar__minus_3_bar_2_bar_45)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_39_bar__plus_00_dot_47_bar__minus_01_dot_06 loc_bar_2_bar__minus_5_bar_1_bar_60)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_72_bar__plus_02_dot_02_bar__minus_02_dot_00 loc_bar_4_bar__minus_5_bar_2_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_75_bar__plus_02_dot_02_bar__minus_01_dot_03 loc_bar_4_bar__minus_4_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__plus_01_dot_75_bar__plus_02_dot_02_bar__minus_01_dot_40 loc_bar_4_bar__minus_5_bar_1_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_46_bar__plus_02_dot_27_bar__minus_02_dot_00 loc_bar__minus_1_bar__minus_5_bar_2_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_82_bar__plus_00_dot_47_bar__minus_01_dot_69 loc_bar__minus_1_bar__minus_3_bar_2_bar_45)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_84_bar__plus_00_dot_47_bar__minus_01_dot_67 loc_bar_0_bar__minus_4_bar_3_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_10_bar__plus_02_dot_02_bar__minus_02_dot_00 loc_bar__minus_2_bar__minus_4_bar_2_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_15_bar__plus_02_dot_02_bar__minus_01_dot_98 loc_bar__minus_1_bar__minus_5_bar_3_bar__minus_15)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_15_bar__plus_02_dot_27_bar__minus_00_dot_83 loc_bar__minus_2_bar__minus_3_bar_3_bar__minus_30)
        (receptacleAtLocation CoffeeMachine_bar__plus_01_dot_37_bar__plus_00_dot_90_bar__minus_02_dot_11 loc_bar_4_bar__minus_5_bar_2_bar_45)
        (receptacleAtLocation CounterTop_bar__plus_00_dot_23_bar__plus_00_dot_95_bar__minus_02_dot_00 loc_bar_3_bar__minus_5_bar_2_bar_45)
        (receptacleAtLocation DiningTable_bar__plus_00_dot_42_bar__plus_00_dot_01_bar__plus_00_dot_68 loc_bar__minus_4_bar_3_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_82_bar__plus_00_dot_75_bar__minus_01_dot_69 loc_bar__minus_1_bar__minus_5_bar_3_bar_60)
        (receptacleAtLocation Fridge_bar__minus_01_dot_50_bar__plus_00_dot_00_bar__minus_00_dot_70 loc_bar__minus_2_bar__minus_3_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_01_dot_87_bar__plus_00_dot_00_bar__plus_00_dot_13 loc_bar_5_bar_1_bar_1_bar_60)
        (receptacleAtLocation Microwave_bar__minus_01_dot_23_bar__plus_00_dot_90_bar__minus_01_dot_68 loc_bar__minus_1_bar__minus_5_bar_3_bar_30)
        (receptacleAtLocation Shelf_bar__plus_01_dot_55_bar__plus_00_dot_16_bar__plus_02_dot_48 loc_bar_4_bar_6_bar_0_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_55_bar__plus_00_dot_54_bar__plus_02_dot_48 loc_bar_5_bar_5_bar_0_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_55_bar__plus_00_dot_87_bar__plus_02_dot_48 loc_bar_6_bar_8_bar_0_bar_60)
        (receptacleAtLocation Sink_bar__minus_00_dot_11_bar__plus_00_dot_89_bar__minus_02_dot_01_bar_SinkBasin loc_bar__minus_1_bar__minus_5_bar_2_bar_45)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_57_bar__plus_00_dot_94_bar__minus_00_dot_36 loc_bar_4_bar__minus_1_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_57_bar__plus_00_dot_94_bar__minus_00_dot_76 loc_bar_4_bar__minus_3_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_85_bar__plus_00_dot_94_bar__minus_00_dot_36 loc_bar_4_bar__minus_1_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__plus_01_dot_85_bar__plus_00_dot_94_bar__minus_00_dot_76 loc_bar_4_bar__minus_3_bar_1_bar_45)
        (receptacleAtLocation Toaster_bar__plus_00_dot_78_bar__plus_00_dot_90_bar__minus_02_dot_08 loc_bar_3_bar__minus_5_bar_2_bar_45)
        (objectAtLocation Knife_bar__minus_00_dot_23_bar__plus_00_dot_94_bar__minus_01_dot_71 loc_bar_3_bar__minus_5_bar_2_bar_45)
        (objectAtLocation Spoon_bar__plus_00_dot_07_bar__plus_00_dot_93_bar__plus_01_dot_22 loc_bar__minus_4_bar_3_bar_1_bar_45)
        (objectAtLocation Egg_bar__minus_00_dot_06_bar__plus_00_dot_82_bar__minus_02_dot_12 loc_bar__minus_1_bar__minus_5_bar_2_bar_45)
        (objectAtLocation ButterKnife_bar__plus_00_dot_52_bar__plus_00_dot_93_bar__plus_01_dot_45 loc_bar__minus_4_bar_3_bar_1_bar_45)
        (objectAtLocation PepperShaker_bar__plus_01_dot_36_bar__plus_00_dot_88_bar__plus_02_dot_51 loc_bar_6_bar_8_bar_0_bar_60)
        (objectAtLocation Cup_bar__plus_00_dot_29_bar__plus_00_dot_93_bar__plus_00_dot_99 loc_bar__minus_4_bar_3_bar_1_bar_45)
        (objectAtLocation Plate_bar__plus_01_dot_63_bar__plus_00_dot_12_bar__minus_01_dot_40 loc_bar_2_bar__minus_5_bar_1_bar_60)
        (objectAtLocation Spatula_bar__plus_00_dot_41_bar__plus_00_dot_95_bar__plus_01_dot_22 loc_bar__minus_4_bar_3_bar_1_bar_45)
        (objectAtLocation WineBottle_bar__plus_01_dot_95_bar__plus_01_dot_66_bar__minus_01_dot_48 loc_bar_4_bar__minus_5_bar_1_bar__minus_30)
        (objectAtLocation Lettuce_bar__minus_01_dot_43_bar__plus_00_dot_93_bar__minus_00_dot_54 loc_bar__minus_2_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Bread_bar__plus_00_dot_40_bar__plus_00_dot_96_bar__plus_00_dot_11 loc_bar__minus_4_bar_3_bar_1_bar_45)
        (objectAtLocation SoapBottle_bar__plus_01_dot_87_bar__plus_01_dot_66_bar__minus_01_dot_87 loc_bar_4_bar__minus_5_bar_1_bar__minus_30)
        (objectAtLocation Potato_bar__minus_01_dot_44_bar__plus_00_dot_89_bar__minus_01_dot_01 loc_bar__minus_2_bar__minus_3_bar_3_bar_60)
        (objectAtLocation PepperShaker_bar__plus_01_dot_56_bar__plus_00_dot_91_bar__minus_01_dot_45 loc_bar_3_bar__minus_5_bar_2_bar_45)
        (objectAtLocation Lettuce_bar__minus_01_dot_55_bar__plus_01_dot_26_bar__minus_00_dot_50 loc_bar__minus_2_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Knife_bar__plus_01_dot_74_bar__plus_00_dot_94_bar__minus_01_dot_56 loc_bar_3_bar__minus_5_bar_2_bar_45)
        (objectAtLocation Plate_bar__minus_01_dot_50_bar__plus_01_dot_19_bar__minus_00_dot_70 loc_bar__minus_2_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Cup_bar__minus_01_dot_61_bar__plus_00_dot_86_bar__minus_00_dot_91 loc_bar__minus_2_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Sink_bar__minus_00_dot_11_bar__plus_00_dot_89_bar__minus_02_dot_01 loc_bar_0_bar__minus_5_bar_2_bar_45)
        (objectAtLocation Apple_bar__minus_01_dot_50_bar__plus_01_dot_25_bar__minus_00_dot_91 loc_bar__minus_2_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Potato_bar__plus_00_dot_52_bar__plus_00_dot_96_bar__plus_00_dot_53 loc_bar__minus_4_bar_3_bar_1_bar_45)
        (objectAtLocation Bowl_bar__minus_01_dot_28_bar__plus_01_dot_66_bar__minus_01_dot_61 loc_bar__minus_1_bar__minus_5_bar_3_bar__minus_15)
        (objectAtLocation SoapBottle_bar__plus_01_dot_83_bar__plus_00_dot_91_bar__minus_01_dot_80 loc_bar_3_bar__minus_5_bar_2_bar_45)
        (objectAtLocation Fork_bar__plus_01_dot_62_bar__plus_00_dot_91_bar__minus_01_dot_92 loc_bar_3_bar__minus_5_bar_2_bar_45)
        (objectAtLocation LightSwitch_bar__plus_00_dot_11_bar__plus_01_dot_32_bar__plus_02_dot_75 loc_bar_1_bar_9_bar_0_bar_30)
        (objectAtLocation Ladle_bar__plus_00_dot_07_bar__plus_00_dot_98_bar__plus_01_dot_45 loc_bar__minus_4_bar_3_bar_1_bar_45)
        (objectAtLocation Bread_bar__plus_00_dot_29_bar__plus_00_dot_97_bar__plus_00_dot_53 loc_bar__minus_4_bar_3_bar_1_bar_45)
        (objectAtLocation Lettuce_bar__plus_01_dot_85_bar__plus_00_dot_99_bar__minus_02_dot_09 loc_bar_3_bar__minus_5_bar_2_bar_45)
        (objectAtLocation Window_bar__plus_02_dot_11_bar__plus_01_dot_50_bar__plus_01_dot_07 loc_bar_6_bar_4_bar_1_bar_15)
        (objectAtLocation WineBottle_bar__plus_01_dot_83_bar__plus_00_dot_91_bar__minus_01_dot_21 loc_bar_3_bar__minus_5_bar_2_bar_45)
        (objectAtLocation Spatula_bar__minus_00_dot_05_bar__plus_00_dot_95_bar__plus_00_dot_31 loc_bar__minus_4_bar_3_bar_1_bar_45)
        (objectAtLocation Plate_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_76 loc_bar__minus_4_bar_3_bar_1_bar_45)
        (objectAtLocation SaltShaker_bar__minus_01_dot_03_bar__plus_00_dot_12_bar__minus_01_dot_46 loc_bar_0_bar__minus_4_bar_3_bar_60)
        (objectAtLocation Pot_bar__plus_01_dot_57_bar__plus_00_dot_95_bar__minus_00_dot_76 loc_bar_4_bar__minus_3_bar_1_bar_45)
        (objectAtLocation Cup_bar__minus_01_dot_12_bar__plus_00_dot_13_bar__minus_01_dot_54 loc_bar_0_bar__minus_4_bar_3_bar_60)
        (objectAtLocation PepperShaker_bar__minus_00_dot_74_bar__plus_00_dot_11_bar__minus_01_dot_87 loc_bar__minus_1_bar__minus_3_bar_2_bar_45)
        (objectAtLocation Pan_bar__plus_01_dot_82_bar__plus_00_dot_95_bar__minus_00_dot_35 loc_bar_4_bar__minus_3_bar_1_bar_45)
        (objectAtLocation ButterKnife_bar__plus_00_dot_00_bar__plus_00_dot_91_bar__minus_01_dot_74 loc_bar_3_bar__minus_5_bar_2_bar_45)
        (objectAtLocation StoveKnob_bar__plus_02_dot_04_bar__plus_01_dot_09_bar__minus_00_dot_41 loc_bar_4_bar__minus_2_bar_1_bar_30)
        (objectAtLocation StoveKnob_bar__plus_02_dot_04_bar__plus_01_dot_09_bar__minus_00_dot_25 loc_bar_4_bar__minus_1_bar_1_bar_30)
        (objectAtLocation StoveKnob_bar__plus_02_dot_04_bar__plus_01_dot_09_bar__minus_00_dot_65 loc_bar_4_bar__minus_3_bar_1_bar_30)
        (objectAtLocation StoveKnob_bar__plus_02_dot_04_bar__plus_01_dot_09_bar__minus_00_dot_82 loc_bar_4_bar__minus_3_bar_1_bar_30)
        (objectAtLocation Tomato_bar__plus_01_dot_87_bar__plus_00_dot_14_bar__plus_00_dot_12 loc_bar_5_bar_1_bar_1_bar_60)
        (objectAtLocation Chair_bar__minus_00_dot_54_bar__plus_00_dot_61_bar__plus_01_dot_26 loc_bar__minus_4_bar_5_bar_1_bar_60)
        (objectAtLocation DishSponge_bar__plus_00_dot_33_bar__plus_01_dot_66_bar__minus_02_dot_16 loc_bar_1_bar__minus_5_bar_2_bar__minus_30)
        (objectAtLocation Egg_bar__minus_01_dot_32_bar__plus_00_dot_91_bar__minus_00_dot_70 loc_bar__minus_2_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Spoon_bar__plus_00_dot_52_bar__plus_00_dot_93_bar__plus_00_dot_99 loc_bar__minus_4_bar_3_bar_1_bar_45)
        (objectAtLocation Knife_bar__minus_00_dot_06_bar__plus_00_dot_79_bar__minus_02_dot_03 loc_bar__minus_1_bar__minus_5_bar_2_bar_45)
        (objectAtLocation Mug_bar__minus_00_dot_05_bar__plus_00_dot_93_bar__plus_00_dot_08 loc_bar__minus_4_bar_3_bar_1_bar_45)
        )
    

        (:goal
            (and
                (exists (?r - receptacle)
                    (exists (?o - object)
                        (and
                            (heatable ?o)
                            (objectType ?o MugType)
                            (receptacleType ?r CabinetType)
                            (isHot ?o)
                            (inReceptacle ?o ?r)
                        )
                    )
                )
            )
        )
    )
    