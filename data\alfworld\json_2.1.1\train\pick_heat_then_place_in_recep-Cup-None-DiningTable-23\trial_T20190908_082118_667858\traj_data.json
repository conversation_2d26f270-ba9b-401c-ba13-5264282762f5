{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000265.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000266.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000267.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000268.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000269.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000270.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000271.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000272.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000273.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000340.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000341.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000342.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000343.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000344.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000345.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000346.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000347.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000348.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000349.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000350.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000351.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000352.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000353.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000354.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000355.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000356.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000357.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000358.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000359.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000360.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000361.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000362.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000363.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000364.png", "low_idx": 45}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "DiningTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-12|2|-15"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-4.40363836, -4.40363836, -15.09297084, -15.09297084, 7.87466288, 7.87466288]], "coordinateReceptacleObjectId": ["Cabinet", [-3.5332708, -3.5332708, -14.75, -14.75, 8.56046104, 8.56046104]], "forceVisible": true, "objectId": "Cup|-01.10|+01.97|-03.77"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-12|2|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-5.284, -5.284, -15.412, -15.412, 6.084, 6.084]], "forceVisible": true, "objectId": "Microwave|-01.32|+01.52|-03.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-7|3|30"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-4.40363836, -4.40363836, -15.09297084, -15.09297084, 7.87466288, 7.87466288]], "coordinateReceptacleObjectId": ["DiningTable", [-9.73199272, -9.73199272, -6.78581428, -6.78581428, 3.5828, 3.5828]], "forceVisible": true, "objectId": "Cup|-01.10|+01.97|-03.77", "receptacleObjectId": "DiningTable|-02.43|+00.90|-01.70"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.88|+02.14|-03.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [110, 1, 257, 86], "mask": [[114, 134], [414, 134], [714, 134], [1014, 135], [1314, 135], [1614, 135], [1914, 135], [2214, 135], [2513, 136], [2813, 136], [3113, 136], [3413, 136], [3713, 137], [4013, 137], [4313, 137], [4613, 137], [4913, 137], [5213, 137], [5513, 137], [5813, 137], [6113, 138], [6413, 138], [6713, 138], [7013, 138], [7313, 138], [7613, 138], [7913, 138], [8213, 138], [8513, 139], [8813, 139], [9112, 140], [9412, 140], [9712, 140], [10012, 140], [10312, 140], [10612, 140], [10912, 140], [11212, 141], [11512, 141], [11812, 141], [12112, 141], [12412, 141], [12712, 141], [13012, 141], [13312, 141], [13612, 142], [13912, 142], [14212, 142], [14512, 142], [14812, 142], [15112, 142], [15412, 142], [15712, 142], [16011, 144], [16311, 144], [16611, 144], [16911, 144], [17211, 144], [17511, 144], [17811, 144], [18111, 144], [18411, 144], [18711, 145], [19011, 145], [19311, 145], [19611, 145], [19911, 145], [20211, 145], [20511, 145], [20811, 145], [21111, 146], [21411, 146], [21711, 146], [22011, 146], [22311, 146], [22611, 146], [22910, 147], [23210, 147], [23510, 148], [23810, 148], [24110, 148], [24410, 148], [24710, 148], [25010, 148], [25310, 148], [25611, 146]], "point": [183, 42]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.10|+01.97|-03.77"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [163, 52, 195, 85], "mask": [[15477, 9], [15773, 17], [16070, 22], [16368, 26], [16667, 27], [16966, 29], [17265, 31], [17564, 32], [17864, 32], [18164, 32], [18463, 33], [18764, 32], [19064, 31], [19364, 31], [19664, 30], [19965, 28], [20266, 25], [20568, 22], [20869, 20], [21171, 17], [21472, 15], [21774, 11], [22075, 9], [22377, 6], [22678, 4], [22979, 2], [23279, 2], [23579, 2], [23879, 2], [24179, 2], [24479, 2], [24779, 2], [25079, 3], [25380, 2]], "point": [179, 67]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.88|+02.14|-03.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [82, 1, 254, 85], "mask": [[82, 163], [382, 163], [683, 163], [983, 163], [1283, 163], [1583, 163], [1884, 162], [2184, 162], [2484, 162], [2785, 161], [3085, 30], [3116, 130], [3385, 30], [3416, 131], [3686, 29], [3716, 131], [3986, 29], [4016, 131], [4286, 29], [4316, 131], [4587, 28], [4616, 131], [4887, 160], [5187, 160], [5488, 159], [5788, 159], [6088, 160], [6388, 160], [6689, 159], [6989, 26], [7289, 26], [7590, 25], [7890, 25], [8190, 25], [8491, 24], [8791, 158], [9091, 158], [9392, 157], [9692, 157], [9992, 157], [10292, 22], [10315, 134], [10593, 21], [10615, 134], [10893, 21], [10915, 134], [11193, 21], [11215, 135], [11494, 20], [11515, 135], [11794, 20], [11815, 135], [12094, 156], [12395, 155], [12695, 155], [12995, 155], [13296, 154], [13596, 155], [13896, 155], [14197, 154], [14497, 154], [14797, 154], [15097, 154], [15398, 153], [15698, 153], [15998, 153], [16299, 153], [16599, 153], [16899, 153], [17200, 152], [17500, 13], [17514, 138], [17800, 13], [17814, 138], [18101, 12], [18114, 138], [18401, 12], [18414, 138], [18701, 12], [18714, 138], [19001, 12], [19014, 139], [19302, 151], [19602, 151], [19902, 151], [20203, 150], [20503, 150], [20803, 150], [21104, 149], [21404, 150], [21704, 150], [22005, 149], [22305, 149], [22605, 149], [22906, 148], [23206, 148], [23506, 148], [23806, 18], [23923, 31], [24107, 17], [24223, 32], [24407, 17], [24523, 32], [24707, 5], [24713, 11], [24823, 32], [25008, 4], [25013, 11], [25123, 32], [25309, 3], [25313, 11], [25423, 32]], "point": [168, 42]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.10|+01.97|-03.77", "placeStationary": true, "receptacleObjectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 102], "mask": [[0, 1293], [1413, 179], [1714, 177], [2015, 176], [2315, 176], [2615, 177], [2914, 178], [3214, 178], [3514, 178], [3814, 178], [4114, 178], [4414, 178], [4714, 178], [5014, 178], [5314, 178], [5614, 178], [5914, 178], [6214, 178], [6514, 178], [6814, 178], [7114, 178], [7414, 178], [7714, 178], [8013, 179], [8313, 180], [8613, 180], [8913, 180], [9213, 180], [9513, 180], [9813, 180], [10113, 180], [10413, 180], [10713, 180], [11013, 180], [11313, 180], [11613, 180], [11913, 180], [12213, 180], [12513, 180], [12813, 180], [13112, 181], [13412, 181], [13712, 181], [14012, 87], [14100, 94], [14312, 87], [14400, 94], [14612, 87], [14700, 94], [14912, 87], [15000, 94], [15212, 87], [15300, 94], [15512, 87], [15600, 94], [15812, 87], [15900, 94], [16112, 86], [16200, 94], [16412, 86], [16500, 94], [16712, 86], [16800, 94], [17012, 86], [17100, 94], [17312, 86], [17400, 94], [17612, 86], [17700, 94], [17912, 86], [18000, 94], [18211, 86], [18300, 94], [18511, 86], [18600, 94], [18811, 86], [18900, 94], [19111, 86], [19200, 94], [19411, 86], [19500, 94], [19711, 86], [19800, 95], [20011, 86], [20100, 95], [20237, 35], [20311, 86], [20400, 95], [20520, 68], [20611, 85], [20700, 296], [21000, 296], [21300, 296], [21600, 296], [21900, 296], [22200, 296], [22500, 295], [22800, 295], [23100, 295], [23400, 295], [23700, 295], [24000, 295], [24300, 295], [24600, 294], [24900, 294], [25200, 294], [25500, 294], [25800, 59], [25861, 233], [26100, 44], [26161, 233], [26400, 29], [26461, 232], [26700, 15], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [149, 67]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 102], "mask": [[0, 1293], [1413, 179], [1714, 177], [2015, 176], [2315, 176], [2615, 177], [2914, 178], [3214, 178], [3514, 178], [3814, 178], [4114, 178], [4414, 178], [4714, 178], [5014, 178], [5314, 178], [5614, 178], [5914, 178], [6214, 178], [6514, 178], [6814, 178], [7114, 178], [7414, 178], [7714, 178], [8013, 179], [8313, 180], [8613, 180], [8913, 180], [9213, 180], [9513, 180], [9813, 180], [10113, 180], [10413, 180], [10713, 180], [11013, 180], [11313, 180], [11613, 180], [11913, 180], [12213, 180], [12513, 180], [12813, 180], [13112, 181], [13412, 181], [13712, 181], [14012, 87], [14100, 94], [14312, 87], [14400, 94], [14612, 87], [14700, 94], [14912, 87], [15000, 94], [15212, 87], [15300, 94], [15512, 87], [15600, 94], [15812, 87], [15900, 94], [16112, 86], [16200, 94], [16412, 86], [16500, 94], [16712, 86], [16800, 94], [17012, 86], [17100, 94], [17312, 86], [17400, 94], [17612, 86], [17700, 94], [17912, 86], [18000, 94], [18211, 86], [18300, 94], [18511, 86], [18600, 94], [18811, 86], [18900, 94], [19111, 86], [19200, 94], [19411, 86], [19500, 94], [19711, 86], [19800, 95], [20011, 86], [20100, 95], [20237, 35], [20311, 86], [20400, 95], [20520, 68], [20611, 85], [20700, 296], [21000, 296], [21300, 296], [21600, 296], [21900, 296], [22200, 296], [22500, 295], [22800, 295], [23100, 295], [23400, 295], [23700, 295], [24000, 295], [24300, 295], [24600, 294], [24900, 294], [25200, 294], [25500, 294], [25800, 59], [25861, 233], [26100, 44], [26161, 233], [26400, 29], [26461, 232], [26700, 15], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [149, 67]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-5.284, -5.284, -15.412, -15.412, 6.084, 6.084]], "forceVisible": true, "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-5.284, -5.284, -15.412, -15.412, 6.084, 6.084]], "forceVisible": true, "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.10|+01.97|-03.77"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [133, 10, 171, 62], "mask": [[2843, 20], [3138, 30], [3435, 35], [3734, 38], [4033, 39], [4333, 39], [4633, 39], [4934, 37], [5235, 35], [5536, 33], [5837, 31], [6138, 30], [6438, 29], [6739, 27], [7040, 25], [7341, 23], [7642, 21], [7943, 19], [8244, 17], [8545, 15], [8846, 13], [9147, 11], [9448, 10], [9748, 9], [10049, 7], [10350, 5], [10651, 3], [10951, 3], [11251, 3], [11551, 3], [11851, 3], [12151, 3], [12451, 3], [12751, 3], [13051, 3], [13351, 3], [13651, 3], [13951, 3], [14251, 3], [14551, 3], [14851, 3], [15151, 3], [15451, 3], [15751, 3], [16051, 3], [16351, 3], [16651, 3], [16951, 3], [17251, 3], [17551, 3], [17850, 5], [18140, 25], [18440, 25]], "point": [152, 35]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 102], "mask": [[0, 1293], [1413, 179], [1714, 177], [2015, 176], [2315, 176], [2615, 177], [2914, 178], [3214, 178], [3514, 178], [3814, 178], [4114, 178], [4414, 178], [4714, 178], [5014, 178], [5314, 178], [5614, 178], [5914, 178], [6214, 178], [6514, 178], [6814, 178], [7114, 178], [7414, 178], [7714, 178], [8013, 179], [8313, 180], [8613, 180], [8913, 180], [9213, 180], [9513, 180], [9813, 180], [10113, 180], [10413, 180], [10713, 180], [11013, 180], [11313, 180], [11613, 180], [11913, 180], [12213, 180], [12513, 180], [12813, 180], [13112, 181], [13412, 181], [13712, 181], [14012, 87], [14100, 94], [14312, 87], [14400, 94], [14612, 87], [14700, 94], [14912, 87], [15000, 94], [15212, 87], [15300, 94], [15512, 87], [15600, 94], [15812, 87], [15900, 94], [16112, 86], [16200, 94], [16412, 86], [16500, 94], [16712, 86], [16800, 94], [17012, 86], [17100, 94], [17312, 86], [17400, 94], [17612, 86], [17700, 94], [17912, 86], [18000, 94], [18211, 86], [18300, 94], [18511, 86], [18600, 94], [18811, 86], [18900, 94], [19111, 86], [19200, 94], [19411, 86], [19500, 94], [19711, 86], [19800, 95], [20011, 86], [20100, 95], [20237, 35], [20311, 86], [20400, 95], [20520, 68], [20611, 85], [20700, 296], [21000, 296], [21300, 296], [21600, 296], [21900, 296], [22200, 296], [22500, 295], [22800, 295], [23100, 295], [23400, 295], [23700, 295], [24000, 295], [24300, 295], [24600, 294], [24900, 294], [25200, 294], [25500, 294], [25800, 59], [25861, 233], [26100, 44], [26161, 233], [26400, 29], [26461, 232], [26700, 15], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [149, 67]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.10|+01.97|-03.77", "placeStationary": true, "receptacleObjectId": "DiningTable|-02.43|+00.90|-01.70"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 114, 299, 300], "mask": [[33929, 23], [33959, 12], [33992, 23], [34035, 3], [34067, 52], [34150, 50], [34228, 24], [34259, 12], [34293, 1], [34296, 19], [34335, 4], [34366, 33], [34400, 19], [34451, 49], [34527, 25], [34560, 11], [34593, 1], [34595, 20], [34634, 6], [34665, 34], [34701, 18], [34753, 21], [34775, 25], [34826, 26], [34860, 11], [34895, 21], [34933, 8], [34963, 36], [35001, 19], [35054, 19], [35076, 24], [35124, 28], [35160, 12], [35194, 23], [35232, 11], [35262, 38], [35301, 19], [35354, 19], [35378, 22], [35423, 29], [35461, 11], [35494, 25], [35531, 13], [35560, 40], [35602, 19], [35655, 18], [35679, 21], [35722, 30], [35760, 12], [35793, 27], [35830, 17], [35858, 42], [35902, 18], [35956, 18], [35980, 20], [36021, 33], [36056, 16], [36093, 28], [36129, 20], [36155, 46], [36202, 17], [36257, 17], [36275, 1], [36282, 18], [36320, 52], [36393, 29], [36428, 73], [36503, 16], [36557, 20], [36583, 17], [36619, 54], [36693, 31], [36727, 75], [36803, 14], [36858, 20], [36885, 15], [36918, 55], [36993, 32], [37026, 76], [37104, 13], [37157, 23], [37186, 14], [37217, 56], [37293, 32], [37326, 77], [37404, 13], [37427, 3], [37455, 27], [37488, 12], [37516, 57], [37593, 32], [37626, 77], [37704, 13], [37727, 14], [37743, 40], [37789, 11], [37815, 58], [37893, 32], [37926, 89], [38027, 14], [38044, 39], [38091, 9], [38114, 60], [38193, 24], [38229, 85], [38328, 13], [38345, 38], [38392, 8], [38413, 61], [38493, 20], [38533, 81], [38629, 13], [38645, 38], [38693, 7], [38712, 62], [38793, 18], [38834, 79], [38929, 14], [38946, 38], [38995, 5], [39011, 64], [39093, 7], [39145, 68], [39229, 16], [39247, 38], [39296, 4], [39310, 65], [39393, 3], [39448, 64], [39529, 17], [39548, 39], [39597, 3], [39608, 67], [39693, 1], [39750, 62], [39829, 18], [39848, 41], [39898, 2], [39907, 69], [40051, 61], [40129, 19], [40149, 42], [40199, 1], [40206, 70], [40351, 56], [40410, 1], [40429, 10], [40462, 31], [40499, 1], [40505, 72], [40652, 55], [40729, 6], [40767, 29], [40804, 74], [40952, 19], [40973, 35], [41030, 4], [41069, 31], [41103, 77], [41187, 3], [41252, 19], [41274, 34], [41330, 4], [41371, 29], [41402, 87], [41552, 19], [41575, 34], [41630, 4], [41673, 27], [41701, 88], [41852, 19], [41875, 35], [41930, 4], [41974, 115], [42152, 19], [42176, 33], [42230, 4], [42275, 113], [42452, 19], [42476, 33], [42530, 5], [42576, 112], [42752, 19], [42777, 31], [42830, 5], [42877, 60], [42938, 49], [43052, 19], [43077, 31], [43130, 6], [43178, 57], [43239, 48], [43352, 19], [43377, 31], [43430, 6], [43478, 56], [43538, 48], [43652, 19], [43678, 29], [43730, 7], [43779, 54], [43837, 49], [43952, 19], [43978, 29], [44030, 8], [44080, 52], [44137, 49], [44252, 19], [44278, 29], [44330, 8], [44381, 51], [44436, 49], [44552, 19], [44579, 27], [44630, 9], [44681, 50], [44735, 50], [44852, 19], [44879, 27], [44930, 10], [44982, 48], [45034, 50], [45152, 17], [45179, 26], [45230, 11], [45282, 48], [45333, 24], [45361, 23], [45452, 17], [45480, 25], [45530, 13], [45582, 47], [45632, 19], [45666, 17], [45752, 17], [45780, 25], [45830, 15], [45882, 46], [45931, 17], [45968, 15], [46052, 17], [46075, 1], [46080, 25], [46130, 18], [46180, 48], [46230, 16], [46269, 14], [46352, 17], [46375, 1], [46381, 24], [46431, 24], [46473, 54], [46529, 15], [46569, 14], [46652, 16], [46676, 1], [46681, 24], [46731, 95], [46829, 14], [46870, 13], [46952, 16], [46976, 1], [46981, 24], [47031, 94], [47128, 14], [47170, 13], [47251, 16], [47276, 1], [47282, 24], [47331, 93], [47427, 13], [47469, 14], [47550, 17], [47576, 1], [47582, 25], [47630, 58], [47690, 32], [47726, 14], [47769, 15], [47849, 18], [47876, 1], [47882, 26], [47929, 56], [47996, 25], [48026, 14], [48068, 18], [48148, 19], [48177, 1], [48183, 27], [48229, 53], [48299, 21], [48325, 14], [48367, 21], [48447, 20], [48477, 1], [48483, 30], [48527, 54], [48600, 19], [48624, 16], [48666, 23], [48747, 20], [48777, 2], [48783, 34], [48823, 57], [48900, 18], [48923, 17], [48965, 24], [49047, 19], [49077, 2], [49084, 95], [49200, 17], [49223, 18], [49264, 26], [49346, 20], [49377, 2], [49384, 94], [49500, 16], [49522, 20], [49563, 27], [49659, 7], [49677, 1], [49685, 93], [49813, 3], [49821, 23], [49862, 29], [49977, 1], [49985, 92], [50120, 27], [50161, 31], [50277, 3], [50285, 92], [50419, 31], [50460, 33], [50577, 45], [50625, 52], [50719, 33], [50759, 35], [50879, 42], [50926, 50], [51019, 36], [51058, 37], [51181, 41], [51227, 49], [51319, 38], [51358, 38], [51484, 38], [51527, 49], [51619, 38], [51659, 38], [51786, 37], [51827, 49], [51918, 39], [51959, 40], [52087, 37], [52128, 48], [52218, 39], [52259, 42], [52388, 36], [52428, 49], [52517, 40], [52559, 44], [52689, 36], [52729, 48], [52816, 42], [52860, 50], [52990, 36], [53029, 48], [53116, 42], [53160, 49], [53291, 36], [53330, 47], [53415, 43], [53460, 48], [53592, 35], [53630, 48], [53714, 44], [53767, 41], [53892, 36], [53930, 48], [54013, 43], [54069, 39], [54192, 37], [54231, 48], [54313, 41], [54369, 40], [54491, 38], [54531, 48], [54612, 41], [54670, 39], [54791, 39], [54832, 48], [54911, 41], [54970, 39], [55091, 39], [55133, 48], [55199, 1], [55210, 41], [55270, 39], [55391, 40], [55433, 49], [55497, 3], [55510, 41], [55569, 40], [55691, 39], [55734, 50], [55796, 4], [55809, 42], [55869, 40], [55991, 38], [56035, 52], [56094, 6], [56108, 43], [56167, 44], [56289, 40], [56337, 63], [56407, 44], [56466, 46], [56588, 40], [56638, 62], [56706, 46], [56764, 49], [56887, 41], [56938, 62], [57005, 50], [57060, 54], [57186, 42], [57239, 61], [57304, 112], [57484, 45], [57539, 61], [57602, 115], [57783, 47], [57840, 178], [58082, 49], [58140, 180], [58380, 52], [58440, 181], [58679, 56], [58738, 184], [58978, 245], [59277, 248], [59575, 251], [59874, 253], [60173, 256], [60471, 259], [60770, 261], [61069, 263], [61368, 266], [61666, 269], [61965, 271], [62264, 274], [62562, 277], [62861, 279], [63160, 281], [63459, 284], [63757, 287], [64056, 289], [64355, 291], [64654, 294], [64952, 296], [65252, 148], [65423, 91], [65554, 100], [65669, 8], [65693, 7], [65723, 91], [65856, 98], [65969, 8], [65994, 6], [66022, 92], [66158, 96], [66269, 8], [66295, 5], [66321, 93], [66459, 95], [66571, 6], [66592, 2], [66596, 4], [66621, 93], [66760, 94], [66868, 1], [66872, 5], [66891, 4], [66896, 4], [66920, 94], [67060, 94], [67168, 2], [67172, 5], [67191, 4], [67196, 4], [67220, 95], [67361, 93], [67467, 4], [67472, 4], [67496, 4], [67519, 98], [67661, 92], [67767, 2], [67772, 4], [67789, 1], [67794, 6], [67819, 99], [67961, 92], [68071, 5], [68089, 11], [68118, 102], [68261, 92], [68365, 11], [68388, 12], [68417, 104], [68560, 93], [68665, 12], [68688, 12], [68717, 105], [68860, 94], [68964, 14], [68987, 14], [69016, 108], [69160, 95], [69264, 16], [69287, 14], [69316, 109], [69460, 96], [69564, 19], [69584, 18], [69615, 112], [69760, 98], [69862, 40], [69914, 114], [70060, 142], [70214, 115], [70359, 144], [70513, 118], [70658, 145], [70813, 119], [70957, 147], [71112, 122], [71256, 148], [71412, 123], [71555, 149], [71711, 126], [71854, 151], [72010, 128], [72153, 152], [72310, 129], [72452, 154], [72609, 132], [72752, 154], [72909, 132], [73059, 147], [73208, 130], [73362, 145], [73508, 128], [73664, 269], [73967, 265], [74268, 263], [74569, 260], [74871, 257], [75172, 255], [75473, 253], [75774, 252], [76074, 251], [76375, 250], [76675, 249], [76976, 248], [77276, 248], [77576, 248], [77876, 137], [78054, 70], [78193, 70], [78264, 49], [78328, 1], [78355, 69], [78494, 120], [78628, 1], [78655, 69], [78794, 120], [78927, 2], [78955, 69], [79095, 119], [79227, 2], [79255, 70], [79395, 29], [79427, 88], [79527, 3], [79555, 70], [79695, 27], [79730, 85], [79826, 4], [79855, 70], [79996, 26], [80031, 85], [80126, 4], [80155, 71], [80296, 25], [80332, 84], [80425, 5], [80456, 71], [80596, 25], [80633, 83], [80725, 5], [80756, 72], [80896, 25], [80933, 84], [81024, 6], [81043, 1], [81050, 79], [81196, 22], [81233, 84], [81324, 6], [81342, 88], [81496, 21], [81533, 85], [81623, 8], [81641, 90], [81796, 21], [81819, 2], [81833, 85], [81923, 8], [81939, 94], [82096, 22], [82119, 2], [82133, 85], [82222, 9], [82237, 98], [82396, 23], [82433, 86], [82522, 9], [82536, 101], [82695, 25], [82732, 87], [82821, 10], [82834, 105], [82995, 26], [83032, 88], [83121, 10], [83132, 113], [83255, 4], [83294, 26], [83331, 229], [83594, 27], [83631, 229], [83893, 28], [83930, 231], [84192, 30], [84230, 232], [84492, 31], [84529, 235], [84791, 34], [84828, 237], [85089, 278], [85388, 281], [85686, 287], [85983, 2817], [88801, 299], [89101, 299], [89402, 298], [89702, 298]], "point": [142, 212]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan23", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.5, "y": 0.9009995, "z": -1.75}, "object_poses": [{"objectName": "Glassbottle_76ad74d1", "position": {"x": -2.5527606, "y": 0.9241918, "z": -2.11539078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_638e2261", "position": {"x": -2.78088546, "y": 0.9261279, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_7baac0ce", "position": {"x": -0.447167248, "y": 0.828534245, "z": -2.155798}, "rotation": {"x": 6.06642669e-21, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "DishSponge_b4789065", "position": {"x": -0.4471672, "y": 0.7967616, "z": -2.00827861}, "rotation": {"x": 6.06642669e-21, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "DishSponge_b4789065", "position": {"x": -0.2540328, "y": 0.7967616, "z": -2.00827861}, "rotation": {"x": 6.06642669e-21, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "ButterKnife_f1c487d3", "position": {"x": -0.6719994, "y": 0.9052365, "z": -3.55599117}, "rotation": {"x": 0.00225457852, "y": 1.59999536e-06, "z": -7.7593e-05}}, {"objectName": "ButterKnife_f1c487d3", "position": {"x": -2.32463574, "y": 0.9147259, "z": -2.345861}, "rotation": {"x": 359.993256, "y": -2.58304972e-05, "z": -0.000186965146}}, {"objectName": "WineBottle_cbf780b9", "position": {"x": -1.975072, "y": 0.02789003, "z": -3.72718143}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_cbf780b9", "position": {"x": -0.600459456, "y": 0.9167287, "z": -3.55599165}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_3622ecde", "position": {"x": -2.5527606, "y": 0.9240236, "z": -1.19351614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_3622ecde", "position": {"x": -0.528919458, "y": 0.9163485, "z": -3.83909845}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_a6c5bfa2", "position": {"x": -2.09651065, "y": 0.9246045, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_a6c5bfa2", "position": {"x": -2.438698, "y": 0.9246045, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_d25a57c3", "position": {"x": -2.32463574, "y": 0.9465557, "z": -1.65445352}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_dc085d43", "position": {"x": -2.438698, "y": 0.9258191, "z": -1.19351614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_dc085d43", "position": {"x": -2.78088546, "y": 0.9258191, "z": -1.19351614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_3611fe48", "position": {"x": -2.09651065, "y": 0.92246145, "z": -2.11539078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_3611fe48", "position": {"x": -2.5527606, "y": 0.92246145, "z": -1.88492227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_53032dff", "position": {"x": -0.457379431, "y": 0.914355755, "z": -3.83909845}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_91c0655d", "position": {"x": -0.421354771, "y": 0.0800629854, "z": -3.2678628}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7d6a9ffb", "position": {"x": -0.743539453, "y": 1.00867426, "z": -3.55599165}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_fbb38b83", "position": {"x": -0.3439546, "y": 1.151585, "z": -0.6653124}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SaltShaker_895229e9", "position": {"x": -2.2105732, "y": 0.919781268, "z": -1.65445352}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b8151885", "position": {"x": -2.78088546, "y": 0.919781268, "z": -2.34585953}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b8151885", "position": {"x": -1.549066, "y": 1.96598554, "z": -3.851097}, "rotation": {"x": 0.0, "y": 0.0, "z": -1.70754731e-06}}, {"objectName": "Apple_c99eb5e1", "position": {"x": -2.09651065, "y": 0.9969592, "z": -1.19351614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7d6a9ffb", "position": {"x": -2.562599, "y": 1.0021, "z": -1.6777}, "rotation": {"x": 36.3855, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_76ad74d1", "position": {"x": -0.384493, "y": 1.53147435, "z": -3.61721277}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Egg_6d28f5dd", "position": {"x": -0.2950922, "y": 1.13178051, "z": -1.08406258}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pen_638e2261", "position": {"x": -2.09651065, "y": 0.9261279, "z": -1.65445352}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_3622ecde", "position": {"x": -0.3506, "y": 0.7977459, "z": -2.08203816}, "rotation": {"x": 6.06642669e-21, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "SoapBottle_53032dff", "position": {"x": -2.32463574, "y": 0.922030866, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_9e9e972c", "position": {"x": -2.78088546, "y": 0.923815966, "z": -2.11539078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_cbf780b9", "position": {"x": -1.8812623, "y": 0.02789003, "z": -3.72718143}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_cdf5a5fc", "position": {"x": -0.3, "y": 0.915, "z": -2.544}, "rotation": {"x": 0.0, "y": 22.9034386, "z": 0.0}}, {"objectName": "Bread_fbb38b83", "position": {"x": -0.414670467, "y": 0.952905238, "z": -3.62644219}, "rotation": {"x": 359.652771, "y": 0.0154604986, "z": 359.828461}}, {"objectName": "Tomato_3d47a8d4", "position": {"x": -0.343954, "y": 1.45466161, "z": -0.4559376}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pan_8d50963e", "position": {"x": -1.5155, "y": 0.9357, "z": -3.4974}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_3611fe48", "position": {"x": -1.10090959, "y": 1.96866572, "z": -3.77324271}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_45f18f99", "position": {"x": -2.2385025, "y": 0.913393736, "z": -1.882062}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_895229e9", "position": {"x": -0.422205657, "y": 0.07763165, "z": -1.75826645}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_7baac0ce", "position": {"x": -0.2950922, "y": 1.42218721, "z": -1.08406234}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "PepperShaker_b8151885", "position": {"x": -0.747807443, "y": 0.0786262751, "z": -3.70181918}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f1c487d3", "position": {"x": -0.371308923, "y": 0.9039049, "z": -3.44393826}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b4789065", "position": {"x": -0.3835091, "y": 0.0808897, "z": -2.20135021}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_a6c5bfa2", "position": {"x": -2.438698, "y": 0.9246045, "z": -1.88492227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_dc085d43", "position": {"x": -2.11866426, "y": 0.913972, "z": -2.42543244}, "rotation": {"x": -1.45081713e-05, "y": 0.0268426463, "z": 1.14886257e-06}}, {"objectName": "Knife_d25a57c3", "position": {"x": -2.66682315, "y": 0.9465557, "z": -0.963047445}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_80763932", "position": {"x": -0.441682875, "y": 1.09384954, "z": -0.979374647}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bowl_91c0655d", "position": {"x": -0.295091242, "y": 1.38988817, "z": -0.7700001}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 3606688198, "scene_num": 23}, "task_id": "trial_T20190908_082118_667858", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2A028LRDJB7ZB_35DR22AR5G1TXLXKA4DDU3I7R1Z3X7", "high_descs": ["Turn left,move forward then turn right head to the cabinet above the microwave", "Open the left cabinet above the microwave, take the glass then close the cabinet", "Make a step on your right closer to the microwave", "Open the microwave, put in the glass then take it out, close the microwave", "Turn right move forward turn left on the table", "Put the glass on the table beside the other glass"], "task_desc": "Put the heated glass on the table", "votes": [1, 1, 1]}, {"assignment_id": "AM2KK02JXXW48_3KB8R4ZV1HONEU670NSFO0GOC2JGBK", "high_descs": ["Move forward and to the left, to the microwave above the stove. ", "Take the glass out of the cabinet above the microwave. ", "Bring the glass to the microwave.", "Heat the glass in the microwave. ", "Turn around with the the heated glass, bring the glass to the table, turn left to face the table.", "Put the glass on the table behind the other glass. "], "task_desc": "Put a heated glass on the table. ", "votes": [1, 1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3PEIJLRY6WAQOJZ8KOOFZ0T4PTEXWQ", "high_descs": ["Turn left and walk up to the sink, then turn right and walk over to the oven, then look up at the upper cabinets.", "Open the leftmost cabinet door above the microwave and remove the cocktail glass from inside, then close the door.", "Turn right and take a step forward, then turn left and look up at the microwave.", "Put the glass in the microwave and microwave the glass for a couple seconds then remove it from the microwave and close the microwave door.", "Turn around and walk forward, then turn left to face the kitchen island.", "Put the heated glass on the kitchen island to the left of the large metal pot."], "task_desc": "Put a heated glass on the kitchen island.", "votes": [1, 1, 0]}]}}