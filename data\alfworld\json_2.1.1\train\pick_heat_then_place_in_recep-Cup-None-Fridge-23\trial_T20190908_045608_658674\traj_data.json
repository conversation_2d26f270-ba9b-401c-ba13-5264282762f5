{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000113.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000114.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000265.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000266.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000267.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000268.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000269.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000270.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000271.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000272.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000273.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000274.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000340.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000341.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000342.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000343.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000344.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000345.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000346.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000347.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000348.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000349.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000350.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000351.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000352.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000353.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000354.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000355.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000356.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000357.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000358.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000359.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000360.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000361.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000362.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000363.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000364.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000365.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000366.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000367.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000368.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000369.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000370.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000371.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000372.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000373.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000374.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000375.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000376.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000377.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000378.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000379.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000380.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000381.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000382.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000383.png", "low_idx": 66}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|1|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-1.571272968, -1.571272968, -2.6612492, -2.6612492, 5.55934668, 5.55934668]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-1.32, -1.32, -3.08, -3.08, 0.0, 0.0]], "forceVisible": true, "objectId": "Cup|-00.39|+01.39|-00.67"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-12|2|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-5.284, -5.284, -15.412, -15.412, 6.084, 6.084]], "forceVisible": true, "objectId": "Microwave|-01.32|+01.52|-03.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-1.571272968, -1.571272968, -2.6612492, -2.6612492, 5.55934668, 5.55934668]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-1.32, -1.32, -3.08, -3.08, 0.0, 0.0]], "forceVisible": true, "objectId": "Cup|-00.39|+01.39|-00.67", "receptacleObjectId": "<PERSON><PERSON>|-00.33|+00.00|-00.77"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.33|+00.00|-00.77"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 90000]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.39|+01.39|-00.67"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [84, 30, 136, 95], "mask": [[8804, 19], [9091, 42], [9386, 49], [9684, 53], [9984, 52], [10284, 52], [10585, 50], [10886, 48], [11187, 46], [11488, 44], [11789, 42], [12091, 39], [12392, 38], [12693, 36], [12994, 34], [13295, 32], [13596, 30], [13897, 28], [14198, 26], [14499, 24], [14800, 23], [15101, 21], [15402, 19], [15703, 17], [16004, 15], [16305, 13], [16606, 11], [16908, 8], [17209, 7], [17510, 5], [17811, 3], [18111, 3], [18411, 3], [18711, 3], [19011, 3], [19311, 3], [19611, 3], [19911, 3], [20211, 3], [20511, 3], [20812, 2], [21112, 3], [21412, 3], [21712, 3], [22012, 3], [22312, 3], [22612, 3], [22912, 3], [23212, 3], [23512, 3], [23812, 3], [24112, 3], [24412, 3], [24712, 3], [25013, 2], [25313, 2], [25607, 17], [25903, 24], [26201, 28], [26499, 31], [26798, 33], [27098, 32], [27398, 31], [27700, 27], [28001, 24], [28305, 15]], "point": [111, 61]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.33|+00.00|-00.77"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 8713], [8732, 268], [9039, 261], [9340, 260], [9640, 260], [9940, 260], [10239, 261], [10538, 262], [10837, 263], [11137, 263], [11436, 264], [11735, 265], [12035, 265], [12334, 266], [12633, 267], [12932, 268], [13232, 268], [13531, 269], [13830, 119], [13966, 135], [14129, 114], [14273, 129], [14429, 112], [14575, 128], [14728, 112], [14875, 130], [15027, 114], [15178, 128], [15327, 114], [15480, 128], [15626, 115], [15775, 1], [15781, 128], [15925, 116], [16075, 3], [16082, 128], [16224, 117], [16375, 4], [16382, 130], [16524, 117], [16674, 6], [16683, 130], [16823, 118], [16974, 6], [16983, 132], [17122, 119], [17274, 5], [17282, 134], [17421, 120], [17574, 5], [17582, 135], [17721, 121], [17874, 5], [17882, 136], [18021, 121], [18174, 4], [18182, 136], [18321, 121], [18474, 4], [18481, 137], [18622, 120], [18774, 3], [18781, 137], [18922, 120], [19073, 4], [19080, 139], [19222, 120], [19373, 4], [19380, 139], [19522, 120], [19673, 3], [19680, 139], [19822, 120], [19973, 3], [19979, 140], [20123, 119], [20273, 3], [20279, 141], [20423, 119], [20573, 2], [20579, 141], [20723, 119], [20873, 2], [20878, 142], [21023, 119], [21173, 1], [21178, 142], [21324, 118], [21473, 1], [21477, 144], [21624, 118], [21777, 144], [21924, 118], [22076, 145], [22224, 118], [22376, 145], [22525, 117], [22675, 147], [22825, 117], [22975, 147], [23125, 117], [23274, 148], [23425, 117], [23573, 149], [23726, 116], [23872, 151], [24026, 116], [24172, 151], [24326, 116], [24472, 151], [24626, 116], [24772, 151], [24927, 115], [25072, 152], [25227, 116], [25372, 151], [25540, 103], [25672, 146], [25842, 101], [25972, 142], [26143, 100], [26272, 140], [26443, 100], [26572, 138], [26743, 101], [26871, 138], [27042, 103], [27170, 139], [27340, 108], [27467, 142], [27637, 116], [27762, 148], [27934, 279], [28228, 13985], [42228, 281], [42534, 271], [42838, 266], [43141, 261], [43442, 176], [43624, 4], [43629, 72], [43744, 173], [43928, 71], [44046, 170], [44230, 67], [44347, 165], [44531, 65], [44649, 162], [44831, 64], [44950, 159], [45132, 62], [45251, 157], [45434, 59], [45552, 155], [45736, 56], [45853, 154], [46036, 55], [46154, 152], [46337, 53], [46455, 150], [46638, 51], [46756, 149], [46939, 49], [47057, 148], [47239, 49], [47358, 146], [47540, 47], [47659, 145], [47840, 46], [47960, 143], [48140, 46], [48261, 142], [48441, 44], [48561, 142], [48741, 44], [48862, 141], [49041, 43], [49163, 140], [49341, 43], [49464, 139], [49641, 43], [49764, 139], [49941, 5], [49954, 29], [50065, 138], [50263, 20], [50365, 138], [50567, 16], [50666, 137], [50870, 13], [50966, 137], [51173, 9], [51267, 136], [51475, 7], [51567, 137], [51778, 4], [51868, 136], [52080, 2], [52168, 137], [52381, 1], [52468, 137], [52769, 137], [53069, 137], [53370, 136], [53670, 137], [53970, 138], [54270, 139], [54570, 139], [54870, 139], [55170, 138], [55470, 138], [55771, 136], [56071, 136], [56371, 135], [56670, 135], [56970, 135], [57270, 134], [57570, 134], [57869, 135], [58169, 135], [58468, 137], [58768, 137], [58995, 1], [59068, 137], [59295, 2], [59366, 139], [59595, 4], [59665, 140], [59895, 5], [59964, 141], [60195, 7], [60262, 143], [60495, 9], [60561, 144], [60795, 11], [60859, 146], [61095, 14], [61157, 148], [61395, 17], [61455, 151], [61694, 21], [61752, 155], [61993, 26], [62048, 159], [62293, 33], [62341, 167], [62592, 217], [62891, 219], [63190, 220], [63490, 221], [63789, 223], [64088, 225], [64387, 226], [64687, 228], [64985, 231], [65284, 234], [65582, 238], [65880, 242], [66178, 246], [66476, 250], [66774, 254], [67072, 258], [67370, 266], [67664, 279], [67957, 290], [68253, 294], [68553, 294], [68853, 294], [69153, 294], [69453, 295], [69752, 296], [70052, 296], [70352, 296], [70652, 296], [70952, 296], [71252, 293], [71555, 285], [71860, 278], [72162, 274], [72464, 270], [72766, 266], [73068, 264], [73368, 263], [73669, 261], [73970, 259], [74271, 257], [74572, 255], [74873, 253], [75174, 252], [75474, 252], [75774, 251], [76075, 250], [76375, 250], [76675, 249], [76976, 248], [77276, 248], [77576, 248], [77876, 248], [78176, 248], [78476, 248], [78776, 248], [79076, 248], [79376, 249], [79675, 250], [79975, 250], [80275, 250], [80575, 251], [80874, 253], [81173, 254], [81473, 255], [81772, 256], [82072, 257], [82371, 259], [82670, 261], [82969, 263], [83268, 266], [83566, 269], [83865, 272], [84163, 276], [84461, 282], [84757, 291], [85052, 4948]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.39|+01.39|-00.67", "placeStationary": true, "receptacleObjectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 102], "mask": [[0, 1293], [1413, 179], [1714, 177], [2015, 176], [2315, 176], [2615, 177], [2914, 178], [3214, 178], [3514, 178], [3814, 178], [4114, 178], [4414, 178], [4714, 178], [5014, 178], [5314, 178], [5614, 178], [5914, 178], [6214, 178], [6514, 178], [6814, 178], [7114, 178], [7414, 178], [7714, 178], [8013, 179], [8313, 180], [8613, 180], [8913, 180], [9213, 180], [9513, 180], [9813, 180], [10113, 180], [10413, 180], [10713, 180], [11013, 180], [11313, 180], [11613, 180], [11913, 180], [12213, 180], [12513, 180], [12813, 180], [13112, 181], [13412, 181], [13712, 181], [14012, 87], [14100, 94], [14312, 87], [14400, 94], [14612, 87], [14700, 94], [14912, 87], [15000, 94], [15212, 87], [15300, 94], [15512, 87], [15600, 94], [15812, 87], [15900, 94], [16112, 86], [16200, 94], [16412, 86], [16500, 94], [16712, 86], [16800, 94], [17012, 86], [17100, 94], [17312, 86], [17400, 94], [17612, 86], [17700, 94], [17912, 86], [18000, 94], [18211, 86], [18300, 94], [18511, 86], [18600, 94], [18811, 86], [18900, 94], [19111, 86], [19200, 94], [19411, 86], [19500, 94], [19711, 86], [19800, 95], [20011, 86], [20100, 95], [20237, 35], [20311, 86], [20400, 95], [20520, 68], [20611, 85], [20700, 296], [21000, 296], [21300, 296], [21600, 296], [21900, 296], [22200, 296], [22500, 295], [22800, 295], [23100, 295], [23400, 295], [23700, 295], [24000, 295], [24300, 295], [24600, 294], [24900, 294], [25200, 294], [25500, 294], [25800, 59], [25861, 233], [26100, 44], [26161, 233], [26400, 29], [26461, 232], [26700, 15], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [149, 67]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 102], "mask": [[0, 1293], [1413, 179], [1714, 177], [2015, 176], [2315, 176], [2615, 177], [2914, 178], [3214, 178], [3514, 178], [3814, 178], [4114, 178], [4414, 178], [4714, 178], [5014, 178], [5314, 178], [5614, 178], [5914, 178], [6214, 178], [6514, 178], [6814, 178], [7114, 178], [7414, 178], [7714, 178], [8013, 179], [8313, 180], [8613, 180], [8913, 180], [9213, 180], [9513, 180], [9813, 180], [10113, 180], [10413, 180], [10713, 180], [11013, 180], [11313, 180], [11613, 180], [11913, 180], [12213, 180], [12513, 180], [12813, 180], [13112, 181], [13412, 181], [13712, 181], [14012, 87], [14100, 94], [14312, 87], [14400, 94], [14612, 87], [14700, 94], [14912, 87], [15000, 94], [15212, 87], [15300, 94], [15512, 87], [15600, 94], [15812, 87], [15900, 94], [16112, 86], [16200, 94], [16412, 86], [16500, 94], [16712, 86], [16800, 94], [17012, 86], [17100, 94], [17312, 86], [17400, 94], [17612, 86], [17700, 94], [17912, 86], [18000, 94], [18211, 86], [18300, 94], [18511, 86], [18600, 94], [18811, 86], [18900, 94], [19111, 86], [19200, 94], [19411, 86], [19500, 94], [19711, 86], [19800, 95], [20011, 86], [20100, 95], [20237, 35], [20311, 86], [20400, 95], [20520, 68], [20611, 85], [20700, 296], [21000, 296], [21300, 296], [21600, 296], [21900, 296], [22200, 296], [22500, 295], [22800, 295], [23100, 295], [23400, 295], [23700, 295], [24000, 295], [24300, 295], [24600, 294], [24900, 294], [25200, 294], [25500, 294], [25800, 59], [25861, 233], [26100, 44], [26161, 233], [26400, 29], [26461, 232], [26700, 15], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [149, 67]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-5.284, -5.284, -15.412, -15.412, 6.084, 6.084]], "forceVisible": true, "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-5.284, -5.284, -15.412, -15.412, 6.084, 6.084]], "forceVisible": true, "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.39|+01.39|-00.67"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [133, 10, 171, 62], "mask": [[2843, 20], [3138, 30], [3435, 35], [3734, 38], [4033, 39], [4333, 39], [4633, 39], [4934, 37], [5235, 35], [5536, 33], [5837, 31], [6138, 30], [6438, 29], [6739, 27], [7040, 25], [7341, 23], [7642, 21], [7943, 19], [8244, 17], [8545, 15], [8846, 13], [9147, 11], [9448, 10], [9748, 9], [10049, 7], [10350, 5], [10651, 3], [10951, 3], [11251, 3], [11551, 3], [11851, 3], [12151, 3], [12451, 3], [12751, 3], [13051, 3], [13351, 3], [13651, 3], [13951, 3], [14251, 3], [14551, 3], [14851, 3], [15151, 3], [15451, 3], [15751, 3], [16051, 3], [16351, 3], [16651, 3], [16951, 3], [17251, 3], [17551, 3], [17850, 5], [18140, 25], [18440, 25]], "point": [152, 35]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 102], "mask": [[0, 1293], [1413, 179], [1714, 177], [2015, 176], [2315, 176], [2615, 177], [2914, 178], [3214, 178], [3514, 178], [3814, 178], [4114, 178], [4414, 178], [4714, 178], [5014, 178], [5314, 178], [5614, 178], [5914, 178], [6214, 178], [6514, 178], [6814, 178], [7114, 178], [7414, 178], [7714, 178], [8013, 179], [8313, 180], [8613, 180], [8913, 180], [9213, 180], [9513, 180], [9813, 180], [10113, 180], [10413, 180], [10713, 180], [11013, 180], [11313, 180], [11613, 180], [11913, 180], [12213, 180], [12513, 180], [12813, 180], [13112, 181], [13412, 181], [13712, 181], [14012, 87], [14100, 94], [14312, 87], [14400, 94], [14612, 87], [14700, 94], [14912, 87], [15000, 94], [15212, 87], [15300, 94], [15512, 87], [15600, 94], [15812, 87], [15900, 94], [16112, 86], [16200, 94], [16412, 86], [16500, 94], [16712, 86], [16800, 94], [17012, 86], [17100, 94], [17312, 86], [17400, 94], [17612, 86], [17700, 94], [17912, 86], [18000, 94], [18211, 86], [18300, 94], [18511, 86], [18600, 94], [18811, 86], [18900, 94], [19111, 86], [19200, 94], [19411, 86], [19500, 94], [19711, 86], [19800, 95], [20011, 86], [20100, 95], [20237, 35], [20311, 86], [20400, 95], [20520, 68], [20611, 85], [20700, 296], [21000, 296], [21300, 296], [21600, 296], [21900, 296], [22200, 296], [22500, 295], [22800, 295], [23100, 295], [23400, 295], [23700, 295], [24000, 295], [24300, 295], [24600, 294], [24900, 294], [25200, 294], [25500, 294], [25800, 59], [25861, 233], [26100, 44], [26161, 233], [26400, 29], [26461, 232], [26700, 15], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [149, 67]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.33|+00.00|-00.77"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 241], "mask": [[0, 49641], [49659, 274], [49967, 261], [50272, 252], [50576, 245], [50879, 240], [51181, 235], [51484, 230], [51786, 227], [52087, 225], [52388, 223], [52689, 221], [52990, 110], [53101, 108], [53291, 109], [53402, 106], [53592, 108], [53703, 105], [53892, 108], [54005, 103], [54192, 108], [54306, 103], [54491, 109], [54607, 102], [54791, 109], [54908, 101], [55091, 109], [55210, 99], [55391, 109], [55511, 98], [55691, 109], [55812, 97], [55991, 108], [56113, 98], [56289, 109], [56414, 98], [56588, 108], [56716, 97], [56887, 108], [57017, 97], [57186, 108], [57318, 98], [57484, 108], [57619, 98], [57783, 108], [57920, 98], [58082, 108], [58222, 98], [58380, 108], [58523, 98], [58679, 108], [58824, 98], [58978, 108], [59125, 98], [59277, 108], [59427, 98], [59575, 108], [59731, 95], [59874, 106], [60031, 96], [60173, 106], [60331, 98], [60471, 108], [60630, 100], [60770, 109], [60930, 101], [61069, 111], [61231, 101], [61368, 111], [61535, 99], [61666, 109], [61837, 98], [61965, 108], [62138, 98], [62264, 108], [62439, 99], [62562, 108], [62740, 99], [62861, 108], [63041, 99], [63160, 108], [63343, 98], [63459, 107], [63644, 99], [63757, 108], [63945, 99], [64056, 108], [64246, 99], [64355, 107], [64547, 99], [64654, 107], [64849, 99], [64952, 108], [65150, 98], [65252, 107], [65451, 97], [65552, 105], [65752, 96], [65852, 104], [66054, 94], [66152, 103], [66355, 93], [66452, 101], [66656, 92], [66752, 100], [66957, 91], [67052, 99], [67258, 90], [67352, 97], [67560, 88], [67652, 96], [67861, 87], [67952, 95], [68162, 86], [68252, 93], [68463, 85], [68552, 92], [68764, 84], [68852, 91], [69066, 82], [69152, 89], [69367, 81], [69452, 88], [69668, 80], [69752, 87], [69969, 79], [70052, 85], [70270, 78], [70352, 85], [70570, 9], [70595, 53], [70652, 59], [70727, 10], [70870, 9], [70914, 34], [70952, 41], [71028, 9], [71170, 9], [71328, 9], [71470, 9], [71628, 9], [71770, 9], [71928, 9], [72070, 8], [72228, 9]], "point": [149, 120]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.39|+01.39|-00.67", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-00.33|+00.00|-00.77"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 103], [129, 72], [267, 135], [430, 70], [567, 135], [732, 67], [868, 133], [1034, 64], [1169, 131], [1334, 63], [1470, 130], [1635, 61], [1770, 129], [1936, 59], [2071, 128], [2236, 58], [2372, 127], [2537, 56], [2672, 126], [2838, 55], [2973, 125], [3138, 54], [3273, 125], [3439, 53], [3574, 124], [3739, 52], [3874, 124], [4039, 51], [4175, 122], [4339, 51], [4475, 122], [4640, 49], [4776, 121], [4940, 49], [5076, 121], [5240, 48], [5377, 120], [5540, 48], [5677, 120], [5840, 48], [5977, 120], [6140, 47], [6278, 119], [6440, 47], [6578, 119], [6740, 47], [6878, 120], [7039, 47], [7179, 119], [7339, 47], [7479, 119], [7639, 47], [7779, 119], [7939, 47], [8079, 120], [8239, 46], [8379, 120], [8539, 46], [8679, 121], [8838, 47], [8980, 120], [9138, 47], [9280, 121], [9438, 47], [9580, 121], [9737, 48], [9880, 122], [10037, 48], [10180, 122], [10337, 48], [10480, 123], [10636, 49], [10780, 123], [10936, 49], [11080, 124], [11235, 50], [11380, 125], [11534, 52], [11680, 126], [11834, 52], [11980, 126], [12133, 53], [12279, 128], [12432, 55], [12579, 129], [12731, 56], [12879, 131], [13030, 57], [13179, 132], [13329, 58], [13479, 133], [13627, 61], [13779, 135], [13925, 64], [14078, 140], [14221, 69], [14378, 212], [14677, 214], [14977, 215], [15276, 217], [15576, 218], [15875, 220], [16175, 221], [16474, 223], [16773, 225], [17073, 226], [17371, 230], [17670, 232], [17968, 236], [18267, 239], [18565, 242], [18863, 247], [19161, 252], [19459, 257], [19756, 264], [20052, 273], [20347, 29294], [49659, 274], [49967, 261], [50272, 252], [50576, 245], [50879, 240], [51181, 235], [51484, 230], [51786, 227], [52087, 225], [52388, 223], [52689, 221], [52990, 219], [53291, 217], [53592, 216], [53892, 216], [54192, 217], [54491, 218], [54791, 218], [55091, 218], [55391, 218], [55691, 218], [55991, 220], [56289, 223], [56588, 225], [56887, 227], [57186, 230], [57484, 233], [57783, 235], [58082, 238], [58380, 241], [58679, 243], [58978, 245], [59277, 248], [59575, 251], [59874, 253], [60173, 157], [60331, 98], [60471, 259], [60770, 109], [60880, 151], [61069, 161], [61231, 101], [61368, 111], [61480, 49], [61535, 99], [61666, 109], [61780, 49], [61837, 98], [61965, 108], [62081, 48], [62138, 98], [62264, 108], [62381, 48], [62439, 99], [62562, 108], [62681, 47], [62740, 99], [62861, 108], [62981, 47], [63041, 99], [63160, 108], [63282, 46], [63343, 98], [63459, 107], [63582, 46], [63644, 99], [63757, 108], [63882, 45], [63945, 99], [64056, 108], [64182, 45], [64246, 99], [64355, 107], [64483, 44], [64547, 99], [64654, 107], [64783, 44], [64849, 99], [64952, 108], [65083, 44], [65150, 98], [65252, 107], [65383, 43], [65451, 97], [65552, 105], [65684, 42], [65752, 96], [65852, 104], [65984, 42], [66054, 94], [66152, 103], [66284, 42], [66355, 93], [66452, 101], [66585, 40], [66656, 92], [66752, 100], [66885, 40], [66957, 91], [67052, 99], [67185, 40], [67258, 90], [67352, 97], [67485, 40], [67560, 88], [67652, 96], [67786, 38], [67861, 87], [67952, 95], [68086, 38], [68162, 86], [68252, 93], [68386, 38], [68463, 85], [68552, 92], [68686, 38], [68764, 84], [68852, 91], [68987, 36], [69066, 82], [69152, 89], [69287, 36], [69367, 81], [69452, 88], [69587, 36], [69668, 80], [69752, 87], [69887, 36], [69969, 79], [70052, 85], [70188, 34], [70270, 78], [70352, 85], [70488, 34], [70570, 9], [70595, 53], [70652, 59], [70727, 10], [70788, 34], [70870, 9], [70914, 34], [70952, 41], [71028, 9], [71088, 34], [71170, 9], [71328, 9], [71389, 33], [71470, 9], [71628, 9], [71689, 32], [71770, 9], [71928, 9], [71989, 32], [72070, 8], [72228, 9], [72289, 32], [72590, 31], [72890, 30], [73190, 30], [73490, 30], [73791, 29], [74091, 28], [74391, 28], [74691, 28], [74992, 27], [75292, 26], [75592, 26], [75893, 25], [76193, 25], [76493, 24], [76793, 24], [77094, 23], [77394, 23], [77694, 23], [77994, 22], [78295, 21], [78595, 21], [78895, 21], [79195, 20], [79496, 19], [79796, 19], [80096, 19], [80396, 18], [80697, 17], [80997, 17], [81297, 17], [81597, 16], [81898, 15], [82198, 15], [82498, 15], [82798, 14], [83099, 13], [83399, 13], [83699, 13], [83999, 13], [84300, 11], [84600, 11], [84900, 11], [85200, 11], [85500, 10], [85800, 10], [86100, 10], [86400, 10], [86700, 9], [87000, 9], [87300, 9], [87600, 9], [87900, 8], [88200, 8], [88500, 8], [88800, 8], [89100, 7], [89400, 7], [89700, 7]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.33|+00.00|-00.77"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 103], [129, 19], [169, 32], [267, 135], [430, 15], [472, 28], [567, 135], [732, 11], [775, 24], [868, 133], [1034, 7], [1076, 22], [1169, 131], [1334, 6], [1377, 20], [1470, 130], [1635, 4], [1679, 17], [1770, 129], [1936, 2], [1980, 15], [2071, 128], [2281, 13], [2372, 127], [2582, 11], [2672, 126], [2882, 11], [2973, 125], [3183, 9], [3273, 125], [3483, 9], [3574, 124], [3783, 8], [3874, 124], [4084, 6], [4175, 122], [4383, 7], [4475, 122], [4683, 6], [4776, 121], [4983, 6], [5076, 121], [5283, 5], [5377, 120], [5583, 5], [5677, 120], [5882, 6], [5977, 120], [6182, 5], [6278, 119], [6481, 6], [6578, 119], [6780, 7], [6878, 120], [7079, 7], [7179, 119], [7339, 2], [7378, 8], [7479, 119], [7639, 3], [7677, 9], [7779, 119], [7939, 4], [7975, 11], [8079, 120], [8239, 6], [8273, 12], [8379, 120], [8539, 7], [8571, 14], [8679, 121], [8838, 10], [8870, 15], [8980, 120], [9138, 11], [9168, 17], [9280, 121], [9438, 13], [9466, 19], [9580, 121], [9737, 15], [9765, 20], [9880, 122], [10037, 17], [10063, 22], [10180, 122], [10337, 18], [10361, 24], [10480, 123], [10636, 21], [10660, 25], [10780, 123], [10936, 21], [10960, 25], [11080, 124], [11235, 22], [11260, 25], [11380, 125], [11534, 23], [11559, 27], [11680, 126], [11834, 23], [11859, 27], [11980, 126], [12133, 24], [12159, 27], [12279, 128], [12432, 25], [12459, 28], [12579, 129], [12731, 26], [12759, 28], [12879, 131], [13030, 27], [13059, 28], [13179, 132], [13329, 28], [13359, 28], [13479, 133], [13627, 30], [13659, 29], [13779, 135], [13925, 32], [13959, 30], [14078, 140], [14221, 36], [14259, 31], [14378, 177], [14560, 30], [14677, 174], [14864, 27], [14977, 171], [15166, 26], [15276, 171], [15468, 25], [15576, 170], [15769, 25], [15875, 170], [16070, 25], [16175, 169], [16371, 25], [16474, 170], [16671, 26], [16773, 170], [16971, 27], [17073, 170], [17272, 27], [17371, 172], [17572, 29], [17670, 173], [17872, 30], [17968, 176], [18171, 33], [18267, 177], [18471, 35], [18565, 180], [18771, 36], [18863, 183], [19070, 40], [19161, 186], [19369, 44], [19459, 189], [19668, 48], [19756, 194], [19965, 55], [20052, 200], [20263, 62], [20347, 39983], [60331, 548], [60880, 350], [61231, 248], [61480, 49], [61535, 240], [61780, 49], [61837, 236], [62081, 48], [62138, 234], [62381, 48], [62439, 231], [62681, 47], [62740, 229], [62981, 47], [63041, 227], [63282, 46], [63343, 223], [63582, 46], [63644, 221], [63882, 45], [63945, 219], [64182, 45], [64246, 216], [64483, 44], [64547, 214], [64783, 44], [64849, 211], [65083, 44], [65150, 209], [65383, 43], [65451, 206], [65684, 42], [65752, 204], [65984, 42], [66054, 201], [66284, 42], [66355, 198], [66585, 40], [66656, 196], [66885, 40], [66957, 194], [67185, 40], [67258, 191], [67485, 40], [67560, 188], [67786, 38], [67861, 186], [68086, 38], [68162, 183], [68386, 38], [68463, 181], [68686, 38], [68764, 179], [68987, 36], [69066, 175], [69287, 36], [69367, 173], [69587, 36], [69668, 171], [69887, 36], [69969, 168], [70188, 34], [70270, 167], [70488, 34], [70570, 9], [70595, 116], [70727, 10], [70788, 34], [70870, 9], [70914, 79], [71028, 9], [71088, 34], [71170, 9], [71328, 9], [71389, 33], [71470, 9], [71628, 9], [71689, 32], [71770, 9], [71928, 9], [71989, 32], [72070, 8], [72228, 9], [72289, 32], [72590, 31], [72890, 30], [73190, 30], [73490, 30], [73791, 29], [74091, 28], [74391, 28], [74691, 28], [74992, 27], [75292, 26], [75592, 26], [75893, 25], [76193, 25], [76493, 24], [76793, 24], [77094, 23], [77394, 23], [77694, 23], [77994, 22], [78295, 21], [78595, 21], [78895, 21], [79195, 20], [79496, 19], [79796, 19], [80096, 19], [80396, 18], [80697, 17], [80997, 17], [81297, 17], [81597, 16], [81898, 15], [82198, 15], [82498, 15], [82798, 14], [83099, 13], [83399, 13], [83699, 13], [83999, 13], [84300, 11], [84600, 11], [84900, 11], [85200, 11], [85500, 10], [85800, 10], [86100, 10], [86400, 10], [86700, 9], [87000, 9], [87300, 9], [87600, 9], [87900, 8], [88200, 8], [88500, 8], [88800, 8], [89100, 7], [89400, 7], [89700, 7]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan23", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -2.5, "y": 0.9009995, "z": -3.0}, "object_poses": [{"objectName": "Glassbottle_76ad74d1", "position": {"x": -0.815079451, "y": 0.916516662, "z": -3.62676835}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_9e9e972c", "position": {"x": -0.8372178, "y": 0.746967137, "z": -3.527333}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pen_638e2261", "position": {"x": -2.78088546, "y": 0.9261279, "z": -0.963047445}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b4789065", "position": {"x": -1.15374041, "y": 1.96924365, "z": -3.81217}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b4789065", "position": {"x": -1.49623513, "y": 1.96924365, "z": -3.77324247}, "rotation": {"x": 0.0, "y": 0.0, "z": -1.70754731e-06}}, {"objectName": "ButterKnife_f1c487d3", "position": {"x": -2.66682315, "y": 0.923517168, "z": -2.11539078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_cdf5a5fc", "position": {"x": -2.09651065, "y": 0.938474953, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_3622ecde", "position": {"x": -2.32463574, "y": 0.9240236, "z": -1.65445352}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_3622ecde", "position": {"x": -2.2105732, "y": 0.9240236, "z": -2.34585953}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_a6c5bfa2", "position": {"x": -0.3506, "y": 0.798326731, "z": -2.00827861}, "rotation": {"x": 6.06642669e-21, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Spoon_a6c5bfa2", "position": {"x": -2.438698, "y": 0.9246045, "z": -2.34585953}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_dc085d43", "position": {"x": -0.382428348, "y": 0.0833691955, "z": -3.2678628}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_3611fe48", "position": {"x": -0.3085291, "y": 1.68983662, "z": -0.5606251}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Cup_3611fe48", "position": {"x": -0.392818242, "y": 1.38983667, "z": -0.6653123}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Cup_3611fe48", "position": {"x": -0.392817646, "y": 1.38983667, "z": -0.455937445}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBottle_53032dff", "position": {"x": -2.5527606, "y": 0.922030866, "z": -0.963047445}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_53032dff", "position": {"x": -0.344812572, "y": 0.07988125, "z": -1.75826645}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_3d47a8d4", "position": {"x": -0.26852265, "y": 1.75466156, "z": -0.874687552}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_3d47a8d4", "position": {"x": -2.78088546, "y": 0.9872864, "z": -2.11539078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_91c0655d", "position": {"x": -2.438698, "y": 0.922512949, "z": -1.19351614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_fbb38b83", "position": {"x": -0.343955547, "y": 1.151585, "z": -0.9793749}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SaltShaker_895229e9", "position": {"x": -2.66682315, "y": 0.919781268, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b8151885", "position": {"x": -0.205475584, "y": 1.966825, "z": -1.93858767}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_c99eb5e1", "position": {"x": -0.671999454, "y": 0.9892841, "z": -3.62676835}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_c99eb5e1", "position": {"x": -2.006342, "y": 0.100445457, "z": -3.79954958}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_c99eb5e1", "position": {"x": -1.15165472, "y": 1.70371366, "z": -3.90336013}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7d6a9ffb", "position": {"x": -2.562599, "y": 1.0021, "z": -1.6777}, "rotation": {"x": 36.3855, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_76ad74d1", "position": {"x": -0.457379431, "y": 0.916516662, "z": -3.909875}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_6d28f5dd", "position": {"x": -0.5083166, "y": 0.948731, "z": -3.16759253}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_638e2261", "position": {"x": -0.02635637, "y": 0.9184528, "z": -1.90095294}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_3622ecde", "position": {"x": -2.66682315, "y": 0.9240236, "z": -1.19351614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_53032dff", "position": {"x": -2.5527606, "y": 0.922030866, "z": -2.11539078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_9e9e972c", "position": {"x": -2.438698, "y": 0.923815966, "z": -1.88492227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_cbf780b9", "position": {"x": -2.2105732, "y": 0.9244038, "z": -1.88492227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_cdf5a5fc", "position": {"x": -0.3, "y": 0.915, "z": -2.544}, "rotation": {"x": 0.0, "y": 22.9034386, "z": 0.0}}, {"objectName": "Bread_fbb38b83", "position": {"x": -0.415000081, "y": 0.95292455, "z": -3.62599945}, "rotation": {"x": -0.00018144137, "y": -4.23772544e-05, "z": -0.000234511288}}, {"objectName": "Tomato_3d47a8d4", "position": {"x": -0.3439546, "y": 1.16266084, "z": -0.6653124}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pan_8d50963e", "position": {"x": -1.5155, "y": 0.9357, "z": -3.4974}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_3611fe48", "position": {"x": -2.438698, "y": 0.92246145, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_45f18f99", "position": {"x": -1.1294, "y": 0.9357, "z": -3.4986}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_895229e9", "position": {"x": -0.436777174, "y": 0.9121062, "z": -2.76492786}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_7baac0ce", "position": {"x": -0.222158864, "y": 0.947136939, "z": -3.368925}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b8151885", "position": {"x": -0.449686229, "y": 0.743420362, "z": -3.238363}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f1c487d3", "position": {"x": -2.66682315, "y": 0.923517168, "z": -2.34585953}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b4789065", "position": {"x": -0.7493532, "y": 0.7461905, "z": -3.527333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_a6c5bfa2", "position": {"x": -2.66682315, "y": 0.9246045, "z": -1.88492227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_dc085d43", "position": {"x": -0.295142442, "y": 1.5331018, "z": -3.63782334}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Knife_d25a57c3", "position": {"x": -2.09651065, "y": 0.9465557, "z": -2.34585953}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_80763932", "position": {"x": -0.295091242, "y": 1.38585031, "z": -0.7700001}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bowl_91c0655d", "position": {"x": -2.78088546, "y": 0.922512949, "z": -1.65445352}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 4213542907, "scene_num": 23}, "task_id": "trial_T20190908_045608_658674", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2DN53DIE3049T_38F5OAUN5QTECEYS7CYH4Y6T8E17HO", "high_descs": ["Turn around, then turn to the left, walk to the fridge on the right", "Open the fridge, pick up the martini glass from inside it, shut the doors", "Turn to the right, walk, to the microwave", "Open the microwave, place the martini glass inside, turn on the microwave, open the door and remove the glass", "Turn around, walk to the fridge on the right", "Open the doors and place the martini glass inside."], "task_desc": "Microwave and chill a martini glass", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A255A9FFZD8PQW_3SLE99ER0QUN5B1CQFG497WUNTVBZO", "high_descs": ["Turn around completely and walk to the right then turn and make another right and walk to the fridge. ", "Open the fridge and take out a martini glass. ", "Turn to the right and walk across to the oven to the microwave above it. ", "Place the martini glass inside the microwave and heat the glass. Take it out.", "Turn around and walk to the fridge. Turn right to face it.", "Open the fridge and place the martini glass inside. "], "task_desc": "Heat a chilled martini glass. ", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A1ELPYAFO7MANS_3H7Z272LXAO54GLIUJ7WWCQLQTSLP1", "high_descs": ["Turn around and walk to the counter, then veer left all the way to the fridge.", "Take the clear glass next to the black mug out of the fridge.", "Turn right and walk to the microwave.", "Heat the glass in the microwave.", "Turn around and walk back to the fridge and face it.", "Place the glass in the fridge between the tomato and the loaf of bread."], "task_desc": "Place a heated glass in a fridge.", "votes": [1, 1]}]}}