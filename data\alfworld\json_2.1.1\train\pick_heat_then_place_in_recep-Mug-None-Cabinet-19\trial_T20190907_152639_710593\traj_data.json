{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000266.png", "low_idx": 41}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["coffeemachine"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-8|-1|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [-11.72303392, -11.72303392, -1.432961344, -1.432961344, 3.0497104, 3.0497104]], "coordinateReceptacleObjectId": ["CoffeeMachine", [-12.416, -12.416, -0.74, -0.74, 2.828, 2.828]], "forceVisible": true, "objectId": "Mug|-02.93|+00.76|-00.36"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|-4|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [-11.72303392, -11.72303392, -1.432961344, -1.432961344, 3.0497104, 3.0497104]], "coordinateReceptacleObjectId": ["Cabinet", [-2.3084748, -2.3084748, -6.90270044, -6.90270044, 1.555380104, 1.555380104]], "forceVisible": true, "objectId": "Mug|-02.93|+00.76|-00.36", "receptacleObjectId": "Cabinet|-00.58|+00.39|-01.73"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-02.93|+00.76|-00.36"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [116, 110, 135, 138], "mask": [[32824, 5], [33120, 12], [33419, 15], [33717, 18], [34017, 18], [34316, 20], [34616, 20], [34916, 20], [35216, 20], [35516, 20], [35817, 18], [36117, 18], [36417, 18], [36717, 19], [37017, 19], [37317, 19], [37618, 18], [37918, 18], [38218, 18], [38518, 18], [38818, 18], [39118, 18], [39418, 18], [39719, 17], [40019, 16], [40319, 16], [40620, 14], [40921, 12], [41223, 7]], "point": [125, 123]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-02.93|+00.76|-00.36", "placeStationary": true, "receptacleObjectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 69], [20866, 134], [21091, 66], [21169, 131], [21391, 65], [21470, 130], [21690, 65], [21771, 129], [21990, 65], [22072, 128], [22290, 64], [22372, 128], [22589, 64], [22673, 127], [22889, 64], [22973, 127], [23189, 64], [23273, 126], [23488, 65], [23573, 126], [23788, 65], [23873, 125], [24088, 65], [24173, 125], [24387, 66], [24472, 126], [24687, 67], [24772, 125], [24987, 67], [25071, 126], [25286, 69], [25371, 125], [25586, 70], [25670, 126], [25886, 71], [25969, 126], [26185, 74], [26267, 128], [26485, 78], [26565, 129], [26785, 209], [27084, 209], [27384, 209], [27684, 209], [27983, 209], [28283, 209], [28583, 208], [28882, 209], [29182, 208], [29482, 208], [29782, 207], [30081, 208], [30381, 208], [30681, 207], [30980, 208], [31280, 207], [31580, 207], [31879, 207], [32179, 207], [32479, 206], [32778, 207], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45364, 52], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 18], [66988, 15], [67288, 13], [67587, 13], [67887, 12], [68187, 11], [68487, 11], [68787, 11], [69086, 11], [69386, 11], [69686, 11], [69986, 11], [70286, 12], [70309, 2], [70586, 12], [70610, 2], [70885, 14], [70910, 2], [71185, 15], [71211, 2], [71486, 15], [71786, 16], [72087, 16], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 69], [20866, 134], [21091, 66], [21169, 131], [21391, 65], [21470, 130], [21690, 65], [21771, 129], [21990, 59], [22053, 2], [22072, 128], [22290, 54], [22372, 128], [22589, 52], [22673, 127], [22889, 51], [22973, 127], [23189, 50], [23273, 126], [23488, 50], [23573, 126], [23788, 50], [23873, 125], [24088, 50], [24173, 125], [24387, 51], [24472, 126], [24687, 51], [24772, 125], [24987, 52], [25071, 126], [25286, 53], [25371, 125], [25586, 53], [25670, 126], [25886, 53], [25969, 126], [26185, 54], [26268, 127], [26485, 54], [26568, 126], [26785, 54], [26862, 3], [26867, 127], [27084, 55], [27162, 2], [27167, 126], [27384, 55], [27462, 2], [27467, 126], [27684, 55], [27762, 2], [27766, 127], [27983, 56], [28062, 1], [28066, 126], [28283, 56], [28365, 127], [28583, 57], [28665, 126], [28882, 58], [28964, 127], [29182, 58], [29263, 127], [29482, 58], [29562, 128], [29782, 58], [29862, 127], [30081, 59], [30162, 127], [30381, 59], [30462, 127], [30681, 59], [30762, 126], [30980, 60], [31062, 126], [31280, 60], [31361, 126], [31580, 60], [31661, 126], [31879, 62], [31960, 126], [32179, 63], [32260, 126], [32479, 64], [32558, 127], [32778, 67], [32857, 128], [33078, 72], [33151, 133], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45364, 52], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-02.93|+00.76|-00.36"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [138, 74, 168, 111], "mask": [[22049, 4], [22344, 14], [22641, 19], [22940, 22], [23239, 24], [23538, 25], [23838, 26], [24138, 28], [24438, 30], [24738, 25], [24765, 4], [25039, 24], [25067, 2], [25339, 24], [25367, 2], [25639, 24], [25667, 2], [25939, 24], [25966, 3], [26239, 24], [26266, 2], [26539, 24], [26565, 3], [26839, 23], [26865, 2], [27139, 23], [27164, 3], [27439, 23], [27464, 3], [27739, 23], [27764, 2], [28039, 23], [28063, 3], [28339, 26], [28640, 25], [28940, 24], [29240, 23], [29540, 22], [29840, 22], [30140, 22], [30440, 22], [30740, 22], [31040, 22], [31340, 21], [31640, 21], [31941, 19], [32242, 18], [32543, 15], [32845, 12], [33150, 1]], "point": [153, 91]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 208], [20192, 208], [20492, 208], [20791, 69], [20866, 134], [21091, 66], [21169, 131], [21391, 65], [21470, 130], [21690, 65], [21771, 129], [21990, 65], [22072, 128], [22290, 64], [22372, 128], [22589, 64], [22673, 127], [22889, 64], [22973, 127], [23189, 64], [23273, 126], [23488, 65], [23573, 126], [23788, 65], [23873, 125], [24088, 65], [24173, 125], [24387, 66], [24472, 126], [24687, 67], [24772, 125], [24987, 67], [25071, 126], [25286, 69], [25371, 125], [25586, 70], [25670, 126], [25886, 71], [25969, 126], [26185, 74], [26267, 128], [26485, 78], [26565, 129], [26785, 209], [27084, 209], [27384, 209], [27684, 209], [27983, 209], [28283, 209], [28583, 208], [28882, 209], [29182, 208], [29482, 208], [29782, 207], [30081, 208], [30381, 208], [30681, 207], [30980, 208], [31280, 207], [31580, 207], [31879, 207], [32179, 207], [32479, 206], [32778, 207], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44465, 51], [44765, 51], [45065, 51], [45364, 52], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 36], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 18], [66988, 15], [67288, 13], [67587, 13], [67887, 12], [68187, 11], [68487, 11], [68787, 11], [69086, 11], [69386, 11], [69686, 11], [69986, 11], [70286, 12], [70309, 2], [70586, 12], [70610, 2], [70885, 14], [70910, 2], [71185, 15], [71211, 2], [71486, 15], [71786, 16], [72087, 16], [72387, 17]], "point": [179, 129]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.58|+00.39|-01.73"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [162, 110, 280, 184], "mask": [[32867, 114], [33166, 115], [33466, 115], [33766, 114], [34066, 114], [34366, 114], [34666, 113], [34966, 113], [35266, 112], [35566, 112], [35866, 111], [36166, 111], [36466, 110], [36766, 110], [37066, 110], [37365, 110], [37665, 110], [37965, 109], [38265, 109], [38565, 108], [38865, 108], [39165, 108], [39465, 107], [39765, 107], [40065, 106], [40365, 106], [40665, 105], [40965, 105], [41265, 104], [41565, 104], [41865, 104], [42165, 103], [42465, 103], [42764, 103], [43064, 103], [43364, 102], [43664, 102], [43964, 102], [44264, 101], [44564, 101], [44864, 100], [45164, 100], [45464, 99], [45764, 99], [46064, 98], [46364, 98], [46664, 98], [46964, 97], [47264, 97], [47564, 96], [47864, 96], [48164, 95], [48463, 96], [48763, 95], [49063, 95], [49363, 95], [49663, 94], [49963, 94], [50263, 93], [50563, 93], [50863, 92], [51163, 92], [51463, 92], [51763, 91], [52063, 91], [52363, 90], [52663, 90], [52963, 89], [53263, 89], [53563, 88], [53862, 89], [54162, 89], [54462, 88], [54762, 88], [55063, 86]], "point": [221, 146]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-02.93|+00.76|-00.36", "placeStationary": true, "receptacleObjectId": "Cabinet|-00.58|+00.39|-01.73"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [163, 111, 299, 274], "mask": [[33280, 2], [33467, 112], [33580, 3], [33767, 111], [33879, 5], [34067, 111], [34179, 5], [34367, 110], [34478, 7], [34667, 110], [34778, 7], [34966, 120], [35266, 110], [35377, 10], [35566, 110], [35677, 10], [35866, 109], [35976, 12], [36166, 109], [36276, 12], [36466, 108], [36575, 14], [36766, 108], [36875, 15], [37066, 124], [37366, 107], [37474, 17], [37666, 107], [37774, 17], [37966, 106], [38073, 19], [38266, 106], [38373, 20], [38566, 105], [38672, 21], [38866, 105], [38972, 22], [39166, 128], [39466, 104], [39571, 24], [39766, 130], [40066, 103], [40170, 26], [40365, 104], [40470, 27], [40665, 103], [40769, 28], [40965, 103], [41069, 29], [41265, 134], [41565, 102], [41668, 31], [41865, 135], [42165, 101], [42267, 33], [42465, 101], [42567, 33], [42765, 100], [42866, 34], [43065, 100], [43166, 34], [43365, 135], [43665, 99], [43765, 35], [43965, 135], [44265, 98], [44364, 36], [44565, 98], [44664, 36], [44865, 97], [44963, 37], [45165, 97], [45263, 37], [45465, 96], [45562, 38], [45764, 97], [45862, 38], [46064, 136], [46364, 96], [46461, 39], [46664, 136], [46964, 95], [47060, 40], [47264, 95], [47360, 40], [47564, 94], [47659, 41], [47864, 94], [47959, 41], [48164, 136], [48464, 93], [48558, 42], [48764, 136], [49064, 92], [49157, 43], [49364, 92], [49457, 43], [49664, 91], [49756, 44], [49964, 91], [50056, 44], [50264, 136], [50564, 90], [50655, 45], [50864, 136], [51163, 90], [51254, 46], [51463, 90], [51554, 46], [51763, 89], [51853, 47], [52063, 89], [52153, 47], [52363, 137], [52663, 88], [52752, 48], [52963, 137], [53263, 87], [53351, 49], [53563, 137], [53863, 86], [53950, 50], [54163, 86], [54250, 50], [54549, 51], [54849, 51], [55149, 51], [55449, 51], [55749, 51], [56050, 50], [56350, 50], [56650, 50], [56951, 49], [57251, 49], [57551, 49], [57852, 48], [58152, 48], [58452, 48], [58753, 47], [59053, 47], [59353, 47], [59654, 46], [59954, 46], [60254, 46], [60555, 45], [60855, 45], [61155, 45], [61456, 44], [61756, 44], [62056, 44], [62357, 43], [62657, 43], [62957, 43], [63258, 42], [63558, 42], [63858, 42], [64159, 41], [64459, 41], [64759, 41], [65060, 40], [65360, 40], [65660, 40], [65961, 39], [66261, 39], [66561, 39], [66862, 38], [67162, 38], [67462, 38], [67763, 37], [68063, 37], [68363, 37], [68664, 36], [68964, 36], [69264, 36], [69565, 35], [69865, 35], [70165, 35], [70466, 34], [70766, 34], [71066, 34], [71367, 33], [71667, 33], [71967, 33], [72268, 32], [72568, 32], [72868, 32], [73169, 31], [73469, 31], [73769, 31], [74070, 30], [74370, 30], [74670, 30], [74971, 29], [75271, 29], [75571, 29], [75872, 28], [76172, 28], [76472, 27], [76773, 25], [77073, 24], [77373, 23], [77674, 21], [77974, 20], [78274, 19], [78575, 17], [78875, 16], [79175, 15], [79476, 13], [79776, 12], [80076, 11], [80377, 9], [80677, 9], [80978, 7], [81278, 6], [81578, 5], [81879, 3], [82179, 2]], "point": [231, 180]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.58|+00.39|-01.73"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [163, 111, 299, 274], "mask": [[33280, 2], [33467, 112], [33580, 3], [33767, 111], [33879, 5], [34067, 111], [34179, 5], [34367, 110], [34478, 7], [34667, 110], [34778, 7], [34966, 120], [35266, 110], [35377, 10], [35566, 110], [35677, 10], [35866, 109], [35976, 12], [36166, 109], [36276, 12], [36466, 108], [36575, 14], [36766, 108], [36875, 15], [37066, 124], [37366, 107], [37474, 17], [37666, 107], [37774, 17], [37966, 106], [38073, 19], [38266, 106], [38373, 20], [38566, 105], [38672, 21], [38866, 105], [38972, 22], [39166, 128], [39466, 104], [39571, 24], [39766, 130], [40066, 103], [40170, 26], [40365, 104], [40470, 27], [40665, 103], [40769, 28], [40965, 103], [41069, 29], [41265, 134], [41565, 102], [41668, 31], [41865, 135], [42165, 101], [42267, 33], [42465, 101], [42567, 33], [42765, 100], [42866, 34], [43065, 100], [43166, 34], [43365, 135], [43665, 99], [43765, 35], [43965, 135], [44265, 98], [44364, 36], [44565, 98], [44664, 36], [44865, 97], [44963, 37], [45165, 97], [45263, 37], [45465, 96], [45562, 38], [45764, 97], [45862, 38], [46064, 136], [46364, 96], [46461, 39], [46664, 136], [46964, 95], [47060, 40], [47264, 95], [47360, 40], [47564, 94], [47659, 41], [47864, 9], [47877, 81], [47959, 41], [48164, 7], [48179, 121], [48464, 6], [48480, 77], [48558, 42], [48764, 5], [48781, 119], [49064, 5], [49081, 75], [49157, 43], [49364, 5], [49381, 75], [49457, 43], [49664, 5], [49681, 74], [49756, 44], [49964, 5], [49981, 74], [50056, 44], [50264, 5], [50281, 119], [50564, 5], [50580, 74], [50655, 45], [50864, 5], [50880, 120], [51163, 6], [51180, 73], [51254, 46], [51463, 6], [51480, 73], [51554, 46], [51763, 6], [51780, 72], [51853, 47], [52063, 6], [52080, 72], [52153, 47], [52363, 7], [52379, 121], [52663, 8], [52678, 73], [52752, 48], [52963, 10], [52977, 123], [53263, 87], [53351, 49], [53563, 137], [53863, 86], [53950, 50], [54163, 86], [54250, 50], [54549, 51], [54849, 51], [55149, 51], [55449, 51], [55749, 51], [56050, 50], [56350, 50], [56650, 50], [56951, 49], [57251, 49], [57551, 49], [57852, 48], [58152, 48], [58452, 48], [58753, 47], [59053, 47], [59353, 47], [59654, 46], [59954, 46], [60254, 46], [60555, 45], [60855, 45], [61155, 45], [61456, 44], [61756, 44], [62056, 44], [62357, 43], [62657, 43], [62957, 43], [63258, 42], [63558, 42], [63858, 42], [64159, 41], [64459, 41], [64759, 41], [65060, 40], [65360, 40], [65660, 40], [65961, 39], [66261, 39], [66561, 39], [66862, 38], [67162, 38], [67462, 38], [67763, 37], [68063, 37], [68363, 37], [68664, 36], [68964, 36], [69264, 36], [69565, 35], [69865, 35], [70165, 35], [70466, 34], [70766, 34], [71066, 34], [71367, 33], [71667, 33], [71967, 33], [72268, 32], [72568, 32], [72868, 32], [73169, 31], [73469, 31], [73769, 31], [74070, 30], [74370, 30], [74670, 30], [74971, 29], [75271, 29], [75571, 29], [75872, 28], [76172, 28], [76472, 27], [76773, 25], [77073, 24], [77373, 23], [77674, 21], [77974, 20], [78274, 19], [78575, 17], [78875, 16], [79175, 15], [79476, 13], [79776, 12], [80076, 11], [80377, 9], [80677, 9], [80978, 7], [81278, 6], [81578, 5], [81879, 3], [82179, 2]], "point": [231, 180]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan19", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -1.75, "y": 0.9016907, "z": -2.25}, "object_poses": [{"objectName": "DishSponge_78f219bc", "position": {"x": -1.63819706, "y": 0.790120363, "z": -3.66151619}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -0.257330954, "y": 0.915564239, "z": -1.49366212}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -2.80945253, "y": 0.7138123, "z": -0.6407361}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -3.14090919, "y": 0.913742, "z": -3.428062}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -0.09526098, "y": 0.9171294, "z": -1.67770243}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -0.09526098, "y": 0.9171294, "z": -1.43231547}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -3.29236221, "y": 0.717515945, "z": -0.370999932}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_410beacd", "position": {"x": -0.4135693, "y": 0.08081764, "z": -0.760731459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -0.3286835, "y": 0.911850154, "z": -3.112597}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -1.11558414, "y": 0.0782674551, "z": -3.63568282}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -0.338365972, "y": 0.915596247, "z": -0.4902684}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_697b561f", "position": {"x": -0.3242583, "y": 1.29472315, "z": -3.75812984}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Bowl_697b561f", "position": {"x": -3.16700172, "y": 0.911298037, "z": -3.72987866}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -1.431937, "y": 0.0792369246, "z": -3.59502673}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -1.05887711, "y": 0.910006046, "z": -3.72987866}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_77e17ce3", "position": {"x": -0.6509867, "y": 0.954571664, "z": -3.87812114}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -2.27053785, "y": 0.9087, "z": -3.95224237}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -1.26282239, "y": 0.9086999, "z": -3.58163619}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -0.193027079, "y": 0.1224366, "z": -0.157844722}, "rotation": {"x": 0.0, "y": 269.999969, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -2.62912345, "y": 0.956339836, "z": -3.804}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_fd5c5c80", "position": {"x": -0.1653, "y": 0.9759, "z": -2.0309}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Egg_b0ce4484", "position": {"x": -3.07920814, "y": 1.04940462, "z": -1.81790459}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_410beacd", "position": {"x": -1.91450834, "y": 0.7908815, "z": -3.790042}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_77e17ce3", "position": {"x": -3.22503686, "y": 0.954571664, "z": -2.92600584}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -0.338365942, "y": 0.91654855, "z": -1.61635566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_4966d0cc", "position": {"x": -0.3946991, "y": 0.9731094, "z": -2.41475344}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_2b74ee3a", "position": {"x": -1.63819706, "y": 0.8343529, "z": -3.790042}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_908150ce", "position": {"x": -2.44983053, "y": 1.0015142, "z": -3.65575743}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_f34461c5", "position": {"x": -3.05678177, "y": 0.9632834, "z": -3.32765055}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -0.0652731955, "y": 1.055421, "z": -0.8022116}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -2.76525831, "y": 0.7165637, "z": -0.596542}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -0.496938, "y": 0.9286998, "z": -2.8113637}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -3.31319165, "y": 1.288454, "z": -2.80594254}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_46bffc5f", "position": {"x": -0.471111983, "y": 0.778778732, "z": -2.91359067}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -3.39329171, "y": 0.910006046, "z": -2.82559443}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -2.43923759, "y": 0.7170095, "z": -0.433500051}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -0.4470415, "y": 0.911850154, "z": -3.87812114}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -0.0763015747, "y": 0.913264155, "z": -3.012186}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -0.530175269, "y": 0.7480171, "z": -0.7613697}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -2.93075848, "y": 0.7624276, "z": -0.358240336}, "rotation": {"x": -0.00214235066, "y": 134.999557, "z": -0.00130094273}}, {"objectName": "Bowl_697b561f", "position": {"x": -3.182703, "y": 1.3965019, "z": -1.87947488}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 1749394892, "scene_num": 19}, "task_id": "trial_T20190907_152639_710593", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1HKHM4NVAO98H_34MAJL3QP742ZJ9AQTB8XSL9YCN34X", "high_descs": ["turn left and walk to the door, then turn left and face the table", "grab a black cup off of the table", "turn around and walk to the microwave up ahead and to the right", "place the cup inside of the microwave, microwave it then take it back out", "turn around and walk to the table, then turn around to face the microwave again", "place the cup in the cabinet bottom right of the microwave"], "task_desc": "place the a microwaved cup in the cabinet under the microwave on the right", "votes": [1, 1]}, {"assignment_id": "A1GVTH5YS3WOK0_34QN5IT0T2871SXFJ9J2C637XMD08K", "high_descs": ["Turn left towards the door and then turn left towards the coffee maker on the round table.", "Pick up the cup on the coffee maker.", "Turn around and walk towards the microwave.", "Place the cup in the microwave, heat it up, and take it back out.", "Take a step backwards to find the right cabinet below the microwave.", "Open the cabinet, place the cup in it, and close the cabinet."], "task_desc": "Place a warmed black cup in the cabinet beneath the microwave.", "votes": [1, 1]}, {"assignment_id": "A35P2RX8HKZM96_3VNXK88KKFZN8YH5RYD5XVO8BQ09V6", "high_descs": ["Turn left and walk to the table on the left.", "Pick up the coffee cup on the table.", "Turn around and walk to the microwave.", "Open the door, put the cup inside, then turn it on. Remove the cup when it's done and close the door.", "Turn completely around and walk to the cabinet under the microwave,", "Open the cabinet, place the cup inside, then close the cabinet."], "task_desc": "Place a microwave coffee cup in the cabinet.", "votes": [1, 1]}]}}