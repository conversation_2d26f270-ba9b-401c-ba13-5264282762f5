{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000285.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000286.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000287.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000288.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000289.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000290.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000291.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000292.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000293.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000294.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000295.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000330.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000331.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000332.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000333.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000335.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000336.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000337.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000338.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000339.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000340.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000341.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000342.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000343.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000344.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000345.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000346.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000347.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000348.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000349.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000350.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000351.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000352.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000353.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000354.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000355.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000356.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000357.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000358.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000359.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000360.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000361.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000362.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000363.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000364.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000365.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000366.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000367.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000368.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000369.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000370.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000371.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000372.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000373.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000374.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000375.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000376.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000377.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000378.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000379.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000380.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000381.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000382.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000383.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000384.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000385.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000386.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000387.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000388.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000389.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000390.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000391.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000392.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000393.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000394.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000395.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000396.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000397.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000398.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000399.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000400.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000401.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000402.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000403.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000404.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000405.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000406.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000407.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000408.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000409.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000410.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000411.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000412.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000413.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000414.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000415.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000416.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000417.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000418.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000419.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000420.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000421.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000422.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000423.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000424.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000425.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000426.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000427.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000428.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000429.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000430.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000431.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000432.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000433.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000434.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000435.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000436.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000437.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000438.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000439.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000440.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000441.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000442.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000443.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000444.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000445.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000446.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000447.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000448.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000449.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000450.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000451.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000452.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000453.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000454.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000455.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000456.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000457.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000458.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000459.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000460.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000461.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000462.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000463.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000464.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000465.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000466.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000467.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000468.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000469.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000470.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000471.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000472.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000473.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000474.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000475.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000476.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000477.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000478.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000479.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000480.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000481.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000482.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000483.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000484.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000485.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000486.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000487.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000488.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000489.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000490.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000491.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000492.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000493.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000494.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000495.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000496.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000497.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000498.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000499.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000500.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000501.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000502.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000503.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000504.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000505.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000506.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000507.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000508.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000509.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000510.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000511.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000512.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000513.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000514.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000515.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000516.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000517.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000518.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000519.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000520.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000521.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000522.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000523.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000524.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000525.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000526.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000527.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000528.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000529.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000530.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000531.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000532.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000533.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000534.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000535.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000536.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000537.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000538.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000539.png", "low_idx": 86}, {"high_idx": 11, "image_name": "000000540.png", "low_idx": 87}, {"high_idx": 11, "image_name": "000000541.png", "low_idx": 87}, {"high_idx": 11, "image_name": "000000542.png", "low_idx": 87}, {"high_idx": 11, "image_name": "000000543.png", "low_idx": 87}, {"high_idx": 11, "image_name": "000000544.png", "low_idx": 87}, {"high_idx": 11, "image_name": "000000545.png", "low_idx": 87}, {"high_idx": 11, "image_name": "000000546.png", "low_idx": 87}, {"high_idx": 11, "image_name": "000000547.png", "low_idx": 87}, {"high_idx": 11, "image_name": "000000548.png", "low_idx": 87}, {"high_idx": 11, "image_name": "000000549.png", "low_idx": 87}, {"high_idx": 11, "image_name": "000000550.png", "low_idx": 87}, {"high_idx": 11, "image_name": "000000551.png", "low_idx": 87}, {"high_idx": 11, "image_name": "000000552.png", "low_idx": 87}, {"high_idx": 11, "image_name": "000000553.png", "low_idx": 87}, {"high_idx": 11, "image_name": "000000554.png", "low_idx": 87}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-5|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [-7.41128, -7.41128, -5.31415652, -5.31415652, 3.0791928, 3.0791928]], "coordinateReceptacleObjectId": ["SinkBasin", [-7.6324, -7.6324, -5.9552, -5.9552, 3.0368, 3.0368]], "forceVisible": true, "objectId": "ButterKnife|-01.85|+00.77|-01.33"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-5|0|30"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-0.789180516, -0.789180516, -2.0261916, -2.0261916, 4.73626088, 4.73626088]], "forceVisible": true, "objectId": "Bread|-00.20|+01.18|-00.51"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|0|-5|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [-7.41128, -7.41128, -5.31415652, -5.31415652, 3.0791928, 3.0791928]], "coordinateReceptacleObjectId": ["Cabinet", [2.72, 2.72, -8.8, -8.8, 1.9799999, 1.9799999]], "forceVisible": true, "objectId": "ButterKnife|-01.85|+00.77|-01.33", "receptacleObjectId": "Cabinet|+00.68|+00.50|-02.20"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-5|0|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-0.789180516, -0.789180516, -2.0261916, -2.0261916, 4.73626088, 4.73626088]], "coordinateReceptacleObjectId": ["CounterTop", [-0.316, -0.316, -0.004, -0.004, 4.5884, 4.5884]], "forceVisible": true, "objectId": "Bread|-00.20|+01.18|-00.51|BreadSliced_8"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-7|2|0"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.948, -0.948, -10.1004, -10.1004, 6.756, 6.756]], "forceVisible": true, "objectId": "Microwave|-00.24|+01.69|-02.53"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-6|7|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "garbagecan"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-0.789180516, -0.789180516, -2.0261916, -2.0261916, 4.73626088, 4.73626088]], "coordinateReceptacleObjectId": ["GarbageCan", [-7.74, -7.74, 8.116, 8.116, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-00.20|+01.18|-00.51|BreadSliced_8", "receptacleObjectId": "GarbageCan|-01.94|+00.00|+02.03"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 12, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|-01.85|+00.77|-01.33"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [94, 116, 163, 121], "mask": [[34605, 15], [34900, 26], [34950, 12], [35195, 69], [35494, 70], [35794, 70], [36144, 19]], "point": [128, 117]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-00.20|+01.18|-00.51"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [148, 115, 188, 184], "mask": [[34359, 11], [34656, 19], [34954, 23], [35253, 25], [35551, 28], [35851, 29], [36150, 31], [36449, 32], [36749, 33], [37049, 33], [37349, 33], [37649, 34], [37949, 34], [38249, 34], [38549, 35], [38848, 36], [39148, 36], [39448, 36], [39748, 36], [40048, 37], [40348, 37], [40648, 37], [40948, 37], [41248, 38], [41548, 38], [41848, 38], [42148, 38], [42448, 38], [42748, 38], [43048, 39], [43348, 39], [43648, 39], [43948, 39], [44248, 40], [44548, 40], [44848, 40], [45148, 40], [45448, 41], [45748, 41], [46048, 41], [46348, 41], [46648, 40], [46949, 39], [47249, 39], [47549, 39], [47849, 38], [48149, 38], [48450, 37], [48750, 36], [49049, 37], [49349, 37], [49649, 37], [49949, 37], [50249, 37], [50549, 37], [50849, 37], [51149, 36], [51449, 36], [51749, 36], [52050, 35], [52350, 35], [52650, 35], [52950, 35], [53250, 35], [53551, 34], [53851, 33], [54152, 31], [54453, 29], [54756, 24], [55065, 5]], "point": [168, 148]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+00.68|+00.50|-02.20"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 114, 116, 208], "mask": [[33900, 103], [34200, 103], [34500, 103], [34800, 103], [35100, 103], [35400, 103], [35700, 104], [36000, 104], [36300, 104], [36600, 104], [36900, 104], [37201, 103], [37501, 103], [37802, 103], [38102, 103], [38403, 102], [38703, 102], [39004, 101], [39304, 101], [39605, 101], [39905, 101], [40206, 100], [40506, 100], [40807, 99], [41107, 99], [41408, 99], [41708, 99], [42009, 98], [42309, 98], [42610, 97], [42910, 97], [43211, 97], [43512, 96], [43812, 96], [44113, 95], [44413, 95], [44714, 94], [45014, 94], [45315, 94], [45615, 94], [45916, 93], [46216, 93], [46517, 92], [46817, 92], [47118, 92], [47418, 92], [47719, 91], [48019, 91], [48320, 90], [48620, 90], [48921, 90], [49221, 90], [49522, 89], [49823, 88], [50123, 88], [50424, 87], [50724, 88], [51025, 87], [51325, 87], [51626, 86], [51926, 86], [52227, 85], [52527, 86], [52828, 85], [53128, 85], [53429, 84], [53729, 84], [54030, 83], [54330, 83], [54631, 83], [54931, 83], [55232, 82], [55532, 82], [55833, 81], [56134, 80], [56434, 81], [56735, 80], [57035, 80], [57336, 79], [57636, 79], [57937, 78], [58237, 79], [58538, 78], [58838, 78], [59139, 77], [59439, 77], [59740, 76], [60040, 77], [60341, 76], [60641, 76], [60942, 75], [61242, 75], [61543, 74], [61843, 74], [62144, 73]], "point": [58, 160]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|-01.85|+00.77|-01.33", "placeStationary": true, "receptacleObjectId": "Cabinet|+00.68|+00.50|-02.20"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 114, 116, 280], "mask": [[33900, 102], [34200, 103], [34500, 103], [34800, 103], [35100, 103], [35400, 103], [35700, 103], [36000, 104], [36300, 104], [36600, 104], [36900, 104], [37200, 104], [37500, 104], [37800, 105], [38100, 105], [38400, 105], [38700, 105], [39000, 105], [39300, 105], [39600, 105], [39900, 106], [40200, 106], [40500, 106], [40800, 106], [41100, 106], [41400, 106], [41700, 107], [42000, 107], [42300, 107], [42600, 107], [42900, 107], [43200, 107], [43500, 108], [43800, 108], [44100, 108], [44400, 108], [44700, 108], [45000, 108], [45300, 109], [45600, 109], [45900, 109], [46200, 109], [46500, 109], [46800, 109], [47100, 109], [47400, 110], [47700, 110], [48000, 110], [48300, 110], [48600, 110], [48900, 110], [49200, 111], [49500, 111], [49800, 111], [50100, 111], [50400, 111], [50700, 111], [51000, 112], [51300, 112], [51600, 112], [51900, 112], [52200, 112], [52500, 112], [52800, 113], [53100, 113], [53400, 113], [53700, 113], [54000, 113], [54300, 113], [54600, 114], [54900, 114], [55200, 114], [55500, 114], [55800, 114], [56100, 114], [56400, 114], [56700, 115], [57000, 115], [57300, 115], [57600, 115], [57900, 115], [58200, 115], [58500, 116], [58800, 116], [59100, 116], [59400, 116], [59700, 116], [60000, 116], [60300, 117], [60600, 117], [60900, 117], [61200, 46], [61500, 46], [61800, 46], [62100, 45], [62400, 45], [62700, 45], [63000, 44], [63300, 44], [63600, 44], [63900, 43], [64200, 43], [64500, 43], [64800, 42], [65100, 42], [65400, 42], [65700, 41], [66000, 41], [66300, 41], [66600, 40], [66900, 40], [67200, 40], [67500, 39], [67800, 39], [68100, 39], [68400, 38], [68700, 38], [69000, 38], [69300, 37], [69600, 37], [69900, 37], [70200, 36], [70500, 36], [70800, 36], [71100, 35], [71400, 35], [71700, 35], [72000, 34], [72300, 34], [72600, 34], [72900, 33], [73200, 33], [73500, 33], [73800, 32], [74100, 32], [74400, 32], [74700, 31], [75000, 31], [75300, 31], [75600, 30], [75900, 30], [76200, 30], [76500, 29], [76800, 29], [77100, 29], [77400, 28], [77700, 28], [78000, 28], [78300, 27], [78600, 27], [78900, 27], [79201, 25], [79502, 24], [79803, 23], [80104, 21], [80405, 20], [80706, 19], [81007, 17], [81308, 16], [81609, 15], [81910, 13], [82211, 12], [82512, 11], [82813, 9], [83114, 8], [83415, 7], [83716, 5]], "point": [58, 196]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+00.68|+00.50|-02.20"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 114, 116, 280], "mask": [[33900, 102], [34200, 103], [34500, 103], [34800, 103], [35100, 103], [35400, 103], [35700, 103], [36000, 104], [36300, 104], [36600, 104], [36900, 104], [37200, 104], [37500, 104], [37800, 105], [38100, 105], [38400, 105], [38700, 105], [39000, 105], [39300, 105], [39600, 105], [39900, 106], [40200, 106], [40500, 106], [40800, 106], [41100, 106], [41400, 106], [41700, 107], [42000, 107], [42300, 107], [42600, 107], [42900, 107], [43200, 107], [43500, 108], [43800, 108], [44100, 108], [44400, 108], [44700, 108], [45000, 108], [45300, 109], [45600, 109], [45900, 109], [46200, 109], [46500, 109], [46800, 109], [47100, 109], [47400, 110], [47700, 110], [48000, 110], [48300, 110], [48600, 110], [48900, 110], [49200, 111], [49500, 111], [49800, 111], [50100, 111], [50400, 111], [50700, 111], [51000, 112], [51300, 112], [51600, 112], [51900, 112], [52200, 112], [52500, 112], [52800, 113], [53100, 113], [53400, 113], [53700, 113], [54000, 113], [54300, 113], [54600, 114], [54900, 114], [55200, 114], [55500, 114], [55800, 114], [56100, 114], [56400, 114], [56700, 115], [57000, 115], [57300, 115], [57600, 115], [57900, 115], [58200, 115], [58500, 77], [58584, 32], [58800, 72], [58892, 7], [58910, 6], [59100, 70], [59211, 5], [59400, 97], [59510, 6], [59700, 116], [60000, 116], [60300, 117], [60600, 117], [60900, 117], [61200, 46], [61500, 46], [61800, 46], [62100, 45], [62400, 45], [62700, 45], [63000, 44], [63300, 44], [63600, 44], [63900, 43], [64200, 43], [64500, 43], [64800, 42], [65100, 42], [65400, 42], [65700, 41], [66000, 41], [66300, 41], [66600, 40], [66900, 40], [67200, 40], [67500, 39], [67800, 39], [68100, 39], [68400, 38], [68700, 38], [69000, 38], [69300, 37], [69600, 37], [69900, 37], [70200, 36], [70500, 36], [70800, 36], [71100, 35], [71400, 35], [71700, 35], [72000, 34], [72300, 34], [72600, 34], [72900, 33], [73200, 33], [73500, 33], [73800, 32], [74100, 32], [74400, 32], [74700, 31], [75000, 31], [75300, 31], [75600, 30], [75900, 30], [76200, 30], [76500, 29], [76800, 29], [77100, 29], [77400, 28], [77700, 28], [78000, 28], [78300, 27], [78600, 27], [78900, 27], [79201, 25], [79502, 24], [79803, 23], [80104, 21], [80405, 20], [80706, 19], [81007, 17], [81308, 16], [81609, 15], [81910, 13], [82211, 12], [82512, 11], [82813, 9], [83114, 8], [83415, 7], [83716, 5]], "point": [58, 196]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.20|+01.18|-00.51|BreadSliced_8"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [152, 156, 185, 190], "mask": [[46668, 2], [46964, 11], [47262, 16], [47559, 20], [47857, 24], [48156, 26], [48455, 28], [48754, 31], [49053, 32], [49352, 33], [49652, 34], [49952, 34], [50252, 34], [50552, 34], [50852, 34], [51152, 34], [51452, 34], [51752, 34], [52052, 34], [52352, 34], [52652, 34], [52952, 34], [53252, 34], [53552, 34], [53852, 34], [54152, 34], [54452, 33], [54752, 33], [55052, 33], [55352, 33], [55652, 32], [55952, 32], [56253, 16], [56274, 10], [56554, 13], [56576, 8], [56857, 8], [56878, 2]], "point": [168, 172]}}, "high_idx": 7}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.20|+01.18|-00.51|BreadSliced_8", "placeStationary": true, "receptacleObjectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 85], [7888, 79], [8100, 81], [8191, 76], [8400, 78], [8493, 74], [8700, 76], [8795, 72], [9000, 75], [9097, 70], [9300, 73], [9399, 68], [9600, 72], [9700, 67], [9900, 71], [10001, 66], [10200, 70], [10302, 65], [10500, 70], [10602, 65], [10800, 69], [10903, 64], [11100, 69], [11203, 64], [11400, 69], [11504, 63], [11700, 69], [11804, 63], [12000, 69], [12104, 63], [12300, 69], [12404, 63], [12600, 68], [12704, 63], [12900, 68], [13004, 63], [13200, 68], [13303, 64], [13500, 68], [13603, 64], [13800, 68], [13903, 64], [14100, 68], [14203, 64], [14400, 68], [14503, 64], [14700, 68], [14803, 64], [15000, 67], [15103, 64], [15300, 67], [15403, 64], [15600, 68], [15703, 64], [15900, 68], [16003, 64], [16200, 68], [16304, 63], [16500, 68], [16604, 63], [16800, 68], [16904, 63], [17100, 68], [17204, 63], [17400, 68], [17503, 63], [17700, 68], [17803, 63], [18000, 68], [18103, 63], [18300, 68], [18403, 63], [18600, 68], [18703, 63], [18900, 68], [19003, 63], [19200, 69], [19303, 63], [19500, 69], [19603, 63], [19800, 69], [19903, 63], [20100, 70], [20202, 64], [20400, 71], [20499, 67], [20700, 74], [20796, 70], [21000, 81], [21091, 75], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [67, 49]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.948, -0.948, -10.1004, -10.1004, 6.756, 6.756]], "forceVisible": true, "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.948, -0.948, -10.1004, -10.1004, 6.756, 6.756]], "forceVisible": true, "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.20|+01.18|-00.51|BreadSliced_8"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [67, 27, 103, 71], "mask": [[7885, 3], [8181, 10], [8478, 15], [8776, 19], [9075, 22], [9373, 26], [9672, 28], [9971, 30], [10270, 32], [10570, 32], [10869, 34], [11169, 34], [11469, 35], [11769, 35], [12069, 35], [12369, 35], [12668, 36], [12968, 36], [13268, 35], [13568, 35], [13868, 35], [14168, 35], [14468, 35], [14768, 35], [15067, 36], [15367, 36], [15668, 35], [15968, 35], [16268, 36], [16568, 36], [16868, 36], [17168, 36], [17468, 35], [17768, 35], [18068, 35], [18368, 35], [18668, 35], [18968, 35], [19269, 34], [19569, 34], [19869, 34], [20170, 32], [20471, 28], [20774, 22], [21081, 10]], "point": [85, 48]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.20|+01.18|-00.51|BreadSliced_8", "placeStationary": true, "receptacleObjectId": "GarbageCan|-01.94|+00.00|+02.03"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [155, 163, 266, 236], "mask": [[48768, 75], [49065, 81], [49363, 85], [49661, 89], [49960, 92], [50260, 93], [50559, 94], [50858, 96], [51158, 97], [51457, 99], [51757, 99], [52056, 101], [52356, 101], [52656, 102], [52956, 102], [53256, 102], [53555, 104], [53855, 104], [54155, 104], [54455, 104], [54755, 104], [55055, 105], [55355, 105], [55655, 105], [55955, 105], [56255, 106], [56555, 31], [56589, 72], [56855, 30], [56890, 71], [57155, 29], [57190, 71], [57455, 29], [57490, 72], [57755, 26], [57790, 72], [58056, 23], [58090, 72], [58356, 22], [58390, 72], [58656, 21], [58691, 72], [58956, 21], [58992, 71], [59256, 20], [59292, 71], [59556, 20], [59592, 71], [59856, 20], [59893, 70], [60156, 20], [60193, 71], [60456, 19], [60493, 71], [60756, 19], [60792, 72], [61056, 19], [61092, 72], [61356, 18], [61392, 73], [61656, 18], [61691, 74], [61956, 18], [61991, 74], [62256, 18], [62290, 75], [62556, 17], [62590, 76], [62856, 17], [62890, 76], [63156, 17], [63189, 77], [63456, 17], [63489, 77], [63756, 18], [63788, 78], [64056, 18], [64088, 78], [64356, 19], [64387, 79], [64656, 20], [64687, 80], [64957, 20], [64985, 82], [65257, 22], [65284, 83], [65557, 109], [65858, 108], [66162, 103], [66464, 101], [66765, 100], [67067, 97], [67368, 96], [67670, 92], [67971, 90], [68272, 88], [68573, 85], [68874, 83], [69175, 78], [69476, 60], [69777, 55], [70078, 51], [70379, 47], [70679, 43]], "point": [210, 198]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan1", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 0.25, "y": 0.9009995, "z": 1.5}, "object_poses": [{"objectName": "Potato_dc7b7f7e", "position": {"x": -0.3354904, "y": 1.14208543, "z": 0.7573217}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": 0.453817844, "y": 0.907696068, "z": -2.71444964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": -0.3354904, "y": 1.10859609, "z": -0.5065479}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_30762d8a", "position": {"x": -1.79754007, "y": 0.8049194, "z": -1.50123394}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_30762d8a", "position": {"x": -0.197295129, "y": 1.14676332, "z": -0.7593218}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_811f3b02", "position": {"x": 0.582687557, "y": 0.949768364, "z": -2.25355029}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_811f3b02", "position": {"x": -1.96337986, "y": 0.8088245, "z": -1.24219179}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": -0.059099853, "y": 1.11258078, "z": 0.7573217}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b714915a", "position": {"x": 0.0790954158, "y": 1.10790622, "z": -0.7593218}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b714915a", "position": {"x": 0.835848451, "y": 0.138542533, "z": -2.28615522}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_35d530e8", "position": {"x": -0.197295129, "y": 1.18406522, "z": -0.5065479}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_35d530e8", "position": {"x": -0.84807694, "y": 0.983865142, "z": -2.32982731}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": -2.03855228, "y": 0.5910905, "z": 1.25131321}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": -2.122703, "y": 1.55355263, "z": 1.09256268}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_89238ee6", "position": {"x": 0.8404269, "y": 0.911248565, "z": -2.56081653}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Fork_89238ee6", "position": {"x": -0.9956643, "y": 0.9119485, "z": -2.63708615}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cd066502", "position": {"x": 0.8929328, "y": 0.143670291, "z": -2.43111968}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": 1.47836733, "y": 0.5480585, "z": -2.4593513}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": -1.85282, "y": 0.7697982, "z": -1.32853913}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": 0.582687557, "y": 0.9116288, "z": -2.637633}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": -1.81492758, "y": 0.129461944, "z": 0.20343703}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": -0.3354904, "y": 1.11116433, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Book_f5814e4b", "position": {"x": 0.155, "y": 1.1, "z": 0.617}, "rotation": {"x": 0.0, "y": 315.826447, "z": 0.0}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": 0.8404269, "y": 0.9114167, "z": -2.71444964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_c9d70d0f", "position": {"x": 0.969297051, "y": 0.93604964, "z": -2.407184}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_35d530e8", "position": {"x": 0.355485976, "y": 1.18406522, "z": -0.00100004673}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_89238ee6", "position": {"x": -1.96337986, "y": 0.7703047, "z": -1.58758128}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_dc7b7f7e", "position": {"x": -1.99919128, "y": 0.2571984, "z": 1.16860032}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": -0.059099853, "y": 1.11258078, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7ed44c70", "position": {"x": -0.0361, "y": 0.950499952, "z": -2.5762}, "rotation": {"x": 0.0, "y": 270.000061, "z": 0.0}}, {"objectName": "Pan_e8d2711b", "position": {"x": -1.937702, "y": 0.911009431, "z": -2.25621462}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Plate_ba789e3c", "position": {"x": -0.9120744, "y": 0.133919358, "z": -2.35227013}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_811f3b02", "position": {"x": -1.58601356, "y": 0.9504683, "z": -2.5602715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_d1ae33eb", "position": {"x": -1.20018578, "y": 0.139160156, "z": -2.35227013}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_30762d8a", "position": {"x": -1.99939144, "y": 1.53813827, "z": 1.09256268}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": -1.82734108, "y": 0.7909139, "z": -0.2527132}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Statue_1a4cbefa", "position": {"x": 1.955, "y": 0.168442056, "z": -2.543}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": -1.96227539, "y": 0.0489416756, "z": 1.93331647}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_9e34d9fc", "position": {"x": -0.4652, "y": 0.950499952, "z": -2.372}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": -2.10055685, "y": 0.9263998, "z": -2.58564019}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_2c38299b", "position": {"x": -1.14325166, "y": 1.01568532, "z": -2.5602715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_77fad61b", "position": {"x": -0.473685682, "y": 1.10948181, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_5ffb3206", "position": {"x": -1.65551662, "y": 0.133998871, "z": -1.389955}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_9960ee75", "position": {"x": 1.47836614, "y": 0.5496789, "z": -2.666446}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "SaltShaker_b714915a", "position": {"x": -0.898136437, "y": 1.65260625, "z": -2.56874371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_816f44ce", "position": {"x": 0.615701437, "y": 1.65630627, "z": -2.667972}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e166a32f", "position": {"x": 0.324948162, "y": 0.99032253, "z": -2.40718341}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": -0.197295129, "y": 1.11164212, "z": -0.00100004673}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": -2.0404954, "y": 1.55355263, "z": 0.9250771}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": 1.89161468, "y": 0.880395353, "z": -2.5364325}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Spoon_cd066502", "position": {"x": 0.2172907, "y": 1.11272943, "z": -0.253773928}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": -1.58601356, "y": 0.906399965, "z": -2.406642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2121161256, "scene_num": 1}, "task_id": "trial_T20190909_054256_787965", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A31681CCEVDIH3_39DD6S19JS2TX3ZUWE7BB1MHK89EZP", "high_descs": ["Turn around, walking around the kitchen island to the sink.", "Pick up the knife from the sink. ", "Turn to face the countertop of the kitchen island.", "Slice the loaf of bread on the countertop with the knife.", "Turn around to face the stove.", "Put the knife in the lower cabinet to the left of the stove.", "Turn around to face the loaf of bread on the countertop.", "Pick up a slice of bread from the sliced loaf of bread on the countertop.", "Turn around to walk to the microwave above the stove.", "Heat up the slice of bread in the microwave, removing it afterwards.", "Turn right and walk around the island, hanging a right to reach the trash can by the fridge.", "Put the slice of bread in the trash can."], "task_desc": "Heat up a slice of bread in the microwave to put in the trash can.", "votes": [1, 1, 1]}, {"assignment_id": "A2KAGFQU28JY43_39U1BHVTDOIU1TAY121X6MH38N4T34", "high_descs": ["Turn to your left and go to the refrigerator, then turn to your left again and go to the sink on your right. ", "Pick up the knife between the egg and the tomato, in the sink. ", "Turn around and walk straight, then turn to your right at the island. ", "Cut the loaf of bread, behind the egg, on the island, into slices. ", "Turn around and go to the bottom cabinet to the right of the stove. ", "Place the knife in the bottom cabinet to the left of the stove. ", "Turn around and go back to the island with the bread on it. ", "Pick up one of the slices of bread. ", "Turn around and go to the microwave, above the stove, in front of you. ", "Place the slice of bread in the microwave, heat it up, and remove the slice of bread from the microwave. ", "Turn to your right and go to the sink , then turn right again and go across the room to the garbage bin on the floor, just past the refrigerator, on your right. ", "Place the potato slice in the garbage can. "], "task_desc": "Put a knife in a cabinet, and a warm slice of bread in the garbage. ", "votes": [1, 1, 0]}, {"assignment_id": "A6SR4BU227GUH_3VP0C6EFSJNPLH7WB7YJRBYVO6TM6L", "high_descs": ["take a left turn around the counter top as you move to the refrigerator, then face the counter top on your right near the sink", "pick up the knife in the sink", "turn around, then face the short side of the middle counter top", "slice the bread behind the egg on the counter top", "turn around and face the cabinet to the left of the oven", "open the cabinet door and place the knife in the cabinet", "turn around to face the middle counter top again", "pick up a slice of bread on the counter top", "turn around to face the stove top, then look up at the microwave", "place the bread slice in the microwave for several seconds before taking it out", "turn right, go straight past the fridge, then look to your left at the black trash can", "throw the slice of bread in the bin to the right of the bottle"], "task_desc": "slice some bread and heat it up in the microwave, next dispose of it", "votes": [1, 0, 1]}]}}