{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000096.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000097.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000098.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000099.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000108.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000110.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000111.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000112.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 1, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000205.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000206.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000207.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000208.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000209.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000210.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000265.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000266.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000267.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000268.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000269.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000270.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000271.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000272.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000273.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000274.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000275.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000276.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000277.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000278.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000279.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000280.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000333.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000334.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000335.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000336.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000337.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000338.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000339.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000340.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000341.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000342.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000343.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000344.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000345.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000346.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000347.png", "low_idx": 61}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|18|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-2.260024, -2.260024, 18.46, 18.46, 3.695838212, 3.695838212]], "coordinateReceptacleObjectId": ["CounterTop", [-1.384, -1.384, 22.412, 22.412, 3.8424, 3.8424]], "forceVisible": true, "objectId": "Cup|-00.57|+00.92|+04.62"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-11|10|2|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-2.260024, -2.260024, 18.46, 18.46, 3.695838212, 3.695838212]], "coordinateReceptacleObjectId": ["CounterTop", [-7.4884, -7.4884, 7.2, 7.2, 3.836, 3.836]], "forceVisible": true, "objectId": "Cup|-00.57|+00.92|+04.62", "receptacleObjectId": "CounterTop|-01.87|+00.96|+01.80"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.57|+00.92|+04.62"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [83, 93, 129, 145], "mask": [[27705, 6], [27999, 18], [28297, 22], [28595, 26], [28893, 30], [29191, 33], [29490, 35], [29789, 37], [30088, 39], [30387, 41], [30686, 42], [30985, 44], [31285, 44], [31585, 44], [31884, 45], [32184, 46], [32483, 47], [32783, 47], [33083, 47], [33383, 47], [33683, 47], [33983, 47], [34283, 46], [34583, 46], [34883, 46], [35184, 45], [35484, 45], [35785, 44], [36086, 43], [36386, 42], [36687, 41], [36987, 41], [37288, 39], [37589, 38], [37889, 37], [38190, 35], [38491, 34], [38792, 33], [39092, 34], [39393, 33], [39695, 31], [39996, 30], [40297, 28], [40597, 28], [40898, 27], [41198, 26], [41499, 24], [41800, 23], [42100, 22], [42401, 19], [42703, 16], [43005, 12], [43309, 3]], "point": [106, 118]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.57|+00.92|+04.62", "placeStationary": true, "receptacleObjectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 125], [14226, 138], [14400, 109], [14544, 120], [14700, 106], [14849, 115], [15000, 105], [15150, 114], [15300, 105], [15450, 114], [15600, 106], [15750, 114], [15900, 106], [16050, 114], [16200, 106], [16350, 114], [16500, 106], [16650, 114], [16800, 106], [16949, 114], [17100, 107], [17249, 114], [17400, 107], [17549, 114], [17700, 107], [17849, 114], [18000, 107], [18149, 114], [18300, 107], [18449, 114], [18600, 107], [18749, 114], [18900, 108], [19048, 115], [19200, 108], [19348, 115], [19500, 108], [19648, 114], [19800, 109], [19947, 115], [20100, 109], [20247, 115], [20400, 110], [20546, 116], [20700, 110], [20846, 116], [21000, 111], [21145, 117], [21300, 111], [21445, 117], [21600, 111], [21745, 117], [21900, 111], [22044, 118], [22200, 112], [22344, 117], [22500, 112], [22644, 117], [22800, 112], [22944, 117], [23100, 112], [23244, 117], [23400, 112], [23544, 117], [23700, 113], [23843, 118], [24000, 113], [24143, 118], [24300, 113], [24443, 118], [24600, 113], [24743, 118], [24900, 113], [25043, 118], [25200, 114], [25343, 117], [25500, 114], [25643, 117], [25800, 114], [25943, 117], [26100, 114], [26243, 117], [26400, 114], [26543, 117], [26700, 114], [26843, 117], [27000, 114], [27143, 117], [27300, 124], [27431, 129], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [145, 70]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.57|+00.92|+04.62"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [105, 48, 149, 92], "mask": [[14225, 1], [14509, 35], [14806, 43], [15105, 45], [15405, 45], [15706, 44], [16006, 44], [16306, 44], [16606, 44], [16906, 43], [17207, 42], [17507, 42], [17807, 42], [18107, 42], [18407, 42], [18707, 42], [19008, 40], [19308, 40], [19608, 40], [19909, 38], [20209, 38], [20510, 36], [20810, 36], [21111, 34], [21411, 34], [21711, 34], [22011, 33], [22312, 32], [22612, 32], [22912, 32], [23212, 32], [23512, 32], [23813, 30], [24113, 30], [24413, 30], [24713, 30], [25013, 30], [25314, 29], [25614, 29], [25914, 29], [26214, 29], [26514, 29], [26814, 29], [27114, 29], [27424, 7]], "point": [127, 69]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.57|+00.92|+04.62", "placeStationary": true, "receptacleObjectId": "CounterTop|-01.87|+00.96|+01.80"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [74, 99, 298, 212], "mask": [[29501, 84], [29615, 16], [29633, 13], [29801, 85], [29914, 16], [29934, 13], [30108, 8], [30117, 69], [30213, 17], [30235, 12], [30410, 5], [30418, 68], [30513, 18], [30535, 13], [30711, 3], [30718, 56], [30777, 10], [30812, 19], [30836, 12], [31012, 2], [31018, 14], [31037, 37], [31078, 9], [31111, 21], [31136, 13], [31312, 2], [31318, 13], [31338, 36], [31378, 10], [31411, 21], [31437, 12], [31612, 1], [31618, 13], [31638, 37], [31678, 10], [31710, 23], [31737, 13], [31918, 13], [31938, 37], [31978, 11], [32009, 25], [32037, 13], [32218, 13], [32238, 37], [32278, 11], [32309, 25], [32338, 13], [32518, 13], [32538, 37], [32578, 10], [32608, 27], [32638, 13], [32818, 12], [32839, 36], [32879, 9], [32908, 28], [32938, 14], [33118, 12], [33140, 35], [33179, 8], [33209, 28], [33239, 13], [33419, 11], [33440, 35], [33479, 7], [33509, 28], [33539, 14], [33719, 10], [33740, 36], [33779, 7], [33809, 29], [33840, 10], [33851, 2], [34019, 10], [34040, 36], [34079, 7], [34110, 28], [34141, 4], [34319, 10], [34340, 36], [34379, 6], [34410, 29], [34441, 2], [34619, 10], [34641, 35], [34679, 6], [34710, 29], [34919, 10], [34941, 35], [34980, 6], [35009, 29], [35219, 10], [35241, 35], [35279, 9], [35308, 29], [35519, 10], [35541, 36], [35579, 12], [35604, 33], [35819, 10], [35841, 36], [35879, 57], [36120, 9], [36141, 36], [36179, 56], [36420, 9], [36442, 35], [36479, 56], [36720, 9], [36742, 15], [36758, 19], [36779, 55], [37020, 9], [37042, 15], [37058, 20], [37080, 54], [37320, 9], [37342, 15], [37358, 20], [37380, 53], [37620, 9], [37642, 15], [37658, 20], [37680, 53], [37894, 1], [37920, 9], [37942, 15], [37958, 20], [37980, 53], [38194, 2], [38220, 9], [38243, 14], [38258, 20], [38280, 52], [38494, 2], [38520, 9], [38542, 15], [38558, 20], [38580, 52], [38794, 2], [38820, 10], [38841, 16], [38858, 20], [38880, 52], [39093, 3], [39121, 9], [39141, 16], [39158, 20], [39181, 51], [39393, 3], [39421, 11], [39439, 18], [39458, 20], [39481, 51], [39693, 4], [39721, 36], [39758, 20], [39781, 51], [39993, 4], [40021, 36], [40058, 20], [40081, 51], [40292, 5], [40321, 36], [40358, 20], [40381, 51], [40592, 5], [40621, 36], [40658, 20], [40681, 51], [40892, 5], [40921, 36], [40958, 18], [40984, 48], [41192, 5], [41221, 36], [41258, 15], [41287, 46], [41491, 7], [41521, 36], [41558, 14], [41589, 44], [41791, 7], [41822, 50], [41889, 45], [42091, 7], [42122, 50], [42189, 45], [42391, 7], [42422, 50], [42489, 46], [42691, 7], [42722, 50], [42790, 46], [42990, 9], [43022, 50], [43090, 47], [43166, 1], [43290, 9], [43322, 51], [43390, 48], [43465, 3], [43590, 9], [43622, 51], [43690, 49], [43764, 4], [43890, 9], [43922, 51], [43990, 51], [44062, 7], [44189, 10], [44222, 51], [44291, 52], [44361, 8], [44489, 11], [44522, 51], [44590, 56], [44658, 12], [44789, 11], [44823, 102], [44926, 1], [44928, 1], [44930, 1], [44932, 38], [45089, 11], [45121, 104], [45226, 1], [45228, 1], [45230, 1], [45232, 39], [45388, 12], [45421, 104], [45526, 1], [45528, 1], [45530, 41], [45688, 12], [45720, 37], [45759, 66], [45826, 1], [45828, 1], [45830, 2], [45833, 39], [45988, 13], [46020, 37], [46059, 66], [46126, 1], [46128, 2], [46131, 1], [46133, 39], [46288, 13], [46320, 37], [46359, 66], [46426, 1], [46429, 1], [46431, 1], [46433, 39], [46587, 14], [46619, 38], [46660, 65], [46726, 2], [46729, 1], [46731, 1], [46733, 40], [46887, 14], [46919, 38], [46960, 65], [47027, 1], [47029, 1], [47031, 1], [47034, 39], [47187, 14], [47218, 39], [47260, 65], [47327, 1], [47329, 1], [47332, 1], [47334, 40], [47487, 15], [47517, 40], [47560, 66], [47627, 1], [47629, 1], [47632, 1], [47634, 40], [47787, 16], [47816, 41], [47860, 66], [47927, 1], [47929, 2], [47932, 1], [47934, 41], [48086, 18], [48114, 43], [48160, 66], [48227, 1], [48230, 1], [48232, 1], [48235, 40], [48386, 21], [48411, 46], [48460, 66], [48527, 1], [48530, 1], [48533, 1], [48535, 41], [48686, 71], [48760, 66], [48835, 41], [48986, 71], [49060, 66], [49136, 41], [49285, 72], [49360, 67], [49436, 41], [49585, 72], [49660, 67], [49736, 42], [49885, 72], [49960, 68], [50036, 42], [50185, 72], [50260, 69], [50335, 43], [50484, 73], [50560, 71], [50635, 44], [50784, 73], [50860, 72], [50935, 44], [51084, 73], [51160, 73], [51235, 45], [51384, 73], [51461, 72], [51536, 44], [51683, 74], [51761, 73], [51836, 45], [51983, 74], [52061, 73], [52136, 45], [52283, 56], [52361, 73], [52436, 46], [52583, 49], [52668, 67], [52737, 45], [52883, 44], [52973, 62], [53037, 46], [53182, 41], [53277, 58], [53337, 46], [53482, 38], [53580, 55], [53638, 46], [53782, 36], [53882, 53], [53938, 46], [54082, 34], [54184, 51], [54238, 46], [54381, 33], [54486, 50], [54538, 47], [54681, 30], [54789, 5], [54821, 15], [54839, 46], [54981, 29], [55125, 11], [55139, 47], [55281, 28], [55422, 14], [55440, 46], [55580, 28], [55718, 18], [55740, 47], [55880, 27], [56013, 23], [56040, 47], [56180, 26], [56307, 30], [56341, 47], [56480, 26], [56601, 36], [56641, 47], [56779, 26], [56895, 42], [56941, 48], [57079, 25], [57196, 41], [57242, 47], [57379, 25], [57496, 41], [57542, 48], [57679, 25], [57796, 42], [57842, 48], [57979, 25], [58096, 42], [58143, 47], [58278, 26], [58396, 42], [58443, 48], [58578, 26], [58696, 42], [58743, 48], [58878, 26], [58996, 42], [59044, 48], [59178, 26], [59296, 42], [59344, 48], [59477, 27], [59596, 43], [59644, 49], [59777, 28], [59895, 44], [59945, 48], [60077, 28], [60195, 44], [60245, 49], [60377, 28], [60495, 44], [60546, 48], [60676, 29], [60795, 44], [60846, 49], [60976, 29], [61095, 45], [61146, 49], [61276, 30], [61394, 46], [61447, 49], [61576, 30], [61694, 46], [61747, 49], [61875, 31], [61994, 46], [62047, 50], [62175, 31], [62294, 47], [62347, 50], [62475, 32], [62593, 48], [62647, 50], [62775, 32], [62893, 49], [62947, 51], [63075, 32], [63193, 50], [63247, 51], [63374, 33], [63493, 51], [63546, 53]], "point": [186, 154]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan13", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -0.25, "y": 0.8995012, "z": 1.0}, "object_poses": [{"objectName": "Pan_94f6c891", "position": {"x": -4.26, "y": 0.9231095, "z": 5.418301}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -0.503100038, "y": 0.746293068, "z": 4.849569}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.334957659, "y": 0.958568037, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -0.181592077, "y": 0.924664259, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -4.121773, "y": 0.7905213, "z": 3.34761667}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -4.033492, "y": 0.9244287, "z": 4.381836}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -2.469, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -4.335503, "y": 0.9246294, "z": 4.381836}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -2.109505, "y": 0.9246294, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -4.26, "y": 0.938499868, "z": 4.640952}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -1.86012983, "y": 0.938499868, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -4.411006, "y": 0.9593395, "z": 5.15918446}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -1.777005, "y": 0.9532003, "z": 1.87550259}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -3.77482176, "y": 0.07924658, "z": 5.310297}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -3.712, "y": 0.125750929, "z": 6.375}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -4.17237, "y": 0.9805429, "z": 2.30013466}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -4.19639969, "y": 0.7879045, "z": 3.159811}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -0.259896517, "y": 1.57780421, "z": 3.47016573}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -0.532724559, "y": 0.7423081, "z": 6.07497931}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -3.19417953, "y": 0.7411709, "z": 5.2215476}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -0.565006, "y": 0.92140615, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -0.4018539, "y": 1.16598535, "z": 3.85050058}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -0.30111897, "y": 1.4313854, "z": 3.35549974}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -0.301119268, "y": 1.76541138, "z": 3.79428244}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -0.4098, "y": 0.9556, "z": 6.55700064}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -0.2632102, "y": 2.1295867, "z": 5.36475945}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -3.67572927, "y": 0.104547471, "z": 6.251528}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -3.85912442, "y": 0.9769704, "z": 1.87549877}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -1.777005, "y": 0.9532003, "z": 2.02650762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.181592077, "y": 0.958568037, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -2.02637982, "y": 0.924048543, "z": 1.9510051}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -0.301118255, "y": 1.33290422, "z": 3.60299969}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Plate_53fee662", "position": {"x": -3.44932818, "y": 1.45406246, "z": 1.63855672}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -0.401855379, "y": 1.16598535, "z": 3.35549927}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -4.38188171, "y": 1.0030545, "z": 2.76642179}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -2.109505, "y": 0.980542958, "z": 1.8}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -3.4198, "y": 0.08336198, "z": 1.889157}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -4.335503, "y": 0.938499868, "z": 4.12272}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_aa4cec24", "position": {"x": -1.610755, "y": 1.0277853, "z": 1.87550259}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -0.565006, "y": 0.923959553, "z": 4.615}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -3.80141521, "y": 0.747251868, "z": 5.1626}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pan_94f6c891", "position": {"x": -2.45436978, "y": 0.9335794, "z": 2.0230875}, "rotation": {"x": 0.0, "y": 240.000244, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -2.92400026, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -1.905484, "y": 1.44954264, "z": 1.67120421}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -1.94325483, "y": 0.9232217, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -1.69388, "y": 0.9198061, "z": 1.72449732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -0.334957659, "y": 0.9251421, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -0.334957659, "y": 0.924664259, "z": 5.932334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -3.85256, "y": 0.924629331, "z": 5.182719}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -3.613587, "y": 0.918499947, "z": 5.259397}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2255001063, "scene_num": 13}, "task_id": "trial_T20190907_013525_231713", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AM2KK02JXXW48_32UTUBMZ7JD6WRW2PIR1O1JYYZNBVJ", "high_descs": ["Move to the counter left of the fridge. ", "Pick up the black cup from the counter. ", "Bring the cup to the microwave. ", "Heat the cup in the microwave. ", "Bring the cup to the counter left of the stove.", "Put the cup on the counter."], "task_desc": "Put a heated cup on the counter left of the stove. ", "votes": [1, 1]}, {"assignment_id": "AUTYWXILTACCR_3CTOC39K3A7A3KTIVC12GBM8KM2J7X", "high_descs": ["Turn around and walk towards the counter on your right.", "Pick up a cup from the counter.", "Turn left and walk towards the microwave.", "Warm the cup in the microwave.", "Walk to the counter to the left of the microwave.", "Place the cup on the counter."], "task_desc": "Place a warmed cup on a counter.", "votes": [1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3IJXV6UZ100OQSTRM8E89RY88ISIR2", "high_descs": ["turn and walk to the kitchen counter next to the fridge", "grab a black cup off of the kitchen counter", "turn and walk over to the stove", "place the black cup in the microwave above the stove, microwave it and take it back out", "turn and walk to the kitchen counter to the left of the stove", "place the black cup on the kitchen counter"], "task_desc": "place a heated cup on top of the kitchen counter", "votes": [1, 1]}]}}