{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000340.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000341.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000342.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000343.png", "low_idx": 66}, {"high_idx": 4, "image_name": "000000344.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000345.png", "low_idx": 67}, {"high_idx": 5, "image_name": "000000346.png", "low_idx": 67}, {"high_idx": 5, "image_name": "000000347.png", "low_idx": 67}, {"high_idx": 5, "image_name": "000000348.png", "low_idx": 67}, {"high_idx": 5, "image_name": "000000349.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000350.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000351.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000352.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000353.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000354.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000355.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000356.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000357.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000358.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000359.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000360.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000361.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000362.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000363.png", "low_idx": 68}, {"high_idx": 5, "image_name": "000000364.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000365.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000366.png", "low_idx": 69}, {"high_idx": 5, "image_name": "000000367.png", "low_idx": 69}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-11|-2|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-11.12354184, -11.12354184, -3.85218978, -3.85218978, 3.6898458, 3.6898458]], "coordinateReceptacleObjectId": ["DiningTable", [-9.73199272, -9.73199272, -6.78581428, -6.78581428, 3.5828, 3.5828]], "forceVisible": true, "objectId": "Cup|-02.78|+00.92|-00.96"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-12|2|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-5.284, -5.284, -15.412, -15.412, 6.084, 6.084]], "forceVisible": true, "objectId": "Microwave|-01.32|+01.52|-03.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-11.12354184, -11.12354184, -3.85218978, -3.85218978, 3.6898458, 3.6898458]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-1.32, -1.32, -3.08, -3.08, 0.0, 0.0]], "forceVisible": true, "objectId": "Cup|-02.78|+00.92|-00.96", "receptacleObjectId": "<PERSON><PERSON>|-00.33|+00.00|-00.77"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-02.78|+00.92|-00.96"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [141, 84, 182, 136], "mask": [[25056, 11], [25352, 18], [25650, 23], [25948, 27], [26246, 30], [26546, 31], [26845, 33], [27144, 35], [27443, 37], [27742, 39], [28042, 39], [28342, 39], [28641, 41], [28941, 41], [29241, 41], [29541, 42], [29841, 41], [30141, 41], [30441, 41], [30742, 40], [31042, 40], [31342, 40], [31643, 38], [31943, 37], [32244, 36], [32545, 34], [32846, 32], [33147, 30], [33449, 26], [33751, 22], [34052, 20], [34356, 12], [34659, 3], [34955, 10], [35253, 14], [35551, 18], [35850, 20], [36149, 22], [36449, 22], [36748, 24], [37048, 24], [37348, 24], [37648, 25], [37948, 24], [38248, 24], [38548, 24], [38848, 24], [39149, 22], [39450, 20], [39751, 19], [40052, 16], [40354, 13], [40656, 8]], "point": [161, 109]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-02.78|+00.92|-00.96", "placeStationary": true, "receptacleObjectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 102], "mask": [[0, 1293], [1413, 179], [1714, 177], [2015, 176], [2315, 176], [2615, 177], [2914, 178], [3214, 178], [3514, 178], [3814, 178], [4114, 178], [4414, 178], [4714, 178], [5014, 178], [5314, 178], [5614, 178], [5914, 178], [6214, 178], [6514, 178], [6814, 178], [7114, 178], [7414, 178], [7714, 178], [8013, 179], [8313, 180], [8613, 180], [8913, 180], [9213, 180], [9513, 180], [9813, 180], [10113, 180], [10413, 180], [10713, 180], [11013, 180], [11313, 180], [11613, 180], [11913, 180], [12213, 180], [12513, 180], [12813, 180], [13112, 181], [13412, 181], [13712, 181], [14012, 87], [14100, 94], [14312, 87], [14400, 94], [14612, 87], [14700, 94], [14912, 87], [15000, 94], [15212, 87], [15300, 94], [15512, 87], [15600, 94], [15812, 87], [15900, 94], [16112, 86], [16200, 94], [16412, 86], [16500, 94], [16712, 86], [16800, 94], [17012, 86], [17100, 94], [17312, 86], [17400, 94], [17612, 86], [17700, 94], [17912, 86], [18000, 94], [18211, 86], [18300, 94], [18511, 86], [18600, 94], [18811, 86], [18900, 94], [19111, 86], [19200, 94], [19411, 86], [19500, 94], [19711, 86], [19800, 95], [20011, 86], [20100, 95], [20237, 35], [20311, 86], [20400, 95], [20520, 68], [20611, 85], [20700, 296], [21000, 296], [21300, 296], [21600, 296], [21900, 296], [22200, 296], [22500, 295], [22800, 295], [23100, 295], [23400, 295], [23700, 295], [24000, 295], [24300, 295], [24600, 294], [24900, 294], [25200, 294], [25500, 294], [25800, 59], [25861, 233], [26100, 44], [26161, 233], [26400, 29], [26461, 232], [26700, 15], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [149, 67]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 102], "mask": [[0, 1293], [1413, 179], [1714, 177], [2015, 176], [2315, 176], [2615, 177], [2914, 178], [3214, 178], [3514, 178], [3814, 178], [4114, 178], [4414, 178], [4714, 178], [5014, 178], [5314, 178], [5614, 178], [5914, 178], [6214, 178], [6514, 178], [6814, 178], [7114, 178], [7414, 178], [7714, 178], [8013, 179], [8313, 180], [8613, 180], [8913, 180], [9213, 180], [9513, 180], [9813, 180], [10113, 180], [10413, 180], [10713, 180], [11013, 180], [11313, 180], [11613, 180], [11913, 180], [12213, 180], [12513, 180], [12813, 180], [13112, 181], [13412, 181], [13712, 181], [14012, 87], [14100, 94], [14312, 87], [14400, 94], [14612, 87], [14700, 94], [14912, 87], [15000, 94], [15212, 87], [15300, 94], [15512, 87], [15600, 94], [15812, 87], [15900, 94], [16112, 86], [16200, 94], [16412, 86], [16500, 94], [16712, 86], [16800, 94], [17012, 86], [17100, 94], [17312, 86], [17400, 94], [17612, 86], [17700, 94], [17912, 86], [18000, 94], [18211, 86], [18300, 94], [18511, 86], [18600, 94], [18811, 86], [18900, 94], [19111, 86], [19200, 94], [19411, 86], [19500, 94], [19711, 86], [19800, 95], [20011, 86], [20100, 95], [20237, 35], [20311, 86], [20400, 95], [20520, 68], [20611, 85], [20700, 296], [21000, 296], [21300, 296], [21600, 296], [21900, 296], [22200, 296], [22500, 295], [22800, 295], [23100, 295], [23400, 295], [23700, 295], [24000, 295], [24300, 295], [24600, 294], [24900, 294], [25200, 294], [25500, 294], [25800, 59], [25861, 233], [26100, 44], [26161, 233], [26400, 29], [26461, 232], [26700, 15], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [149, 67]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-5.284, -5.284, -15.412, -15.412, 6.084, 6.084]], "forceVisible": true, "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-5.284, -5.284, -15.412, -15.412, 6.084, 6.084]], "forceVisible": true, "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-02.78|+00.92|-00.96"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [114, 10, 153, 62], "mask": [[2823, 20], [3118, 30], [3416, 34], [3714, 38], [4014, 39], [4314, 40], [4614, 39], [4915, 37], [5216, 36], [5517, 34], [5818, 32], [6119, 30], [6420, 28], [6721, 26], [7022, 24], [7323, 22], [7624, 20], [7924, 20], [8225, 18], [8526, 16], [8827, 14], [9128, 12], [9429, 10], [9730, 8], [10031, 6], [10332, 4], [10633, 2], [10933, 2], [11233, 2], [11533, 2], [11833, 2], [12133, 2], [12433, 2], [12733, 2], [13033, 2], [13333, 2], [13633, 2], [13933, 2], [14233, 2], [14533, 2], [14833, 2], [15133, 2], [15433, 2], [15733, 3], [16033, 3], [16333, 3], [16633, 3], [16933, 3], [17233, 3], [17533, 3], [17832, 5], [18122, 25], [18422, 25]], "point": [133, 35]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 102], "mask": [[0, 1293], [1413, 179], [1714, 177], [2015, 176], [2315, 176], [2615, 177], [2914, 178], [3214, 178], [3514, 178], [3814, 178], [4114, 178], [4414, 178], [4714, 178], [5014, 178], [5314, 178], [5614, 178], [5914, 178], [6214, 178], [6514, 178], [6814, 178], [7114, 178], [7414, 178], [7714, 178], [8013, 179], [8313, 180], [8613, 180], [8913, 180], [9213, 180], [9513, 180], [9813, 180], [10113, 180], [10413, 180], [10713, 180], [11013, 180], [11313, 180], [11613, 180], [11913, 180], [12213, 180], [12513, 180], [12813, 180], [13112, 181], [13412, 181], [13712, 181], [14012, 87], [14100, 94], [14312, 87], [14400, 94], [14612, 87], [14700, 94], [14912, 87], [15000, 94], [15212, 87], [15300, 94], [15512, 87], [15600, 94], [15812, 87], [15900, 94], [16112, 86], [16200, 94], [16412, 86], [16500, 94], [16712, 86], [16800, 94], [17012, 86], [17100, 94], [17312, 86], [17400, 94], [17612, 86], [17700, 94], [17912, 86], [18000, 94], [18211, 86], [18300, 94], [18511, 86], [18600, 94], [18811, 86], [18900, 94], [19111, 86], [19200, 94], [19411, 86], [19500, 94], [19711, 86], [19800, 95], [20011, 86], [20100, 95], [20237, 35], [20311, 86], [20400, 95], [20520, 68], [20611, 85], [20700, 296], [21000, 296], [21300, 296], [21600, 296], [21900, 296], [22200, 296], [22500, 295], [22800, 295], [23100, 295], [23400, 295], [23700, 295], [24000, 295], [24300, 295], [24600, 294], [24900, 294], [25200, 294], [25500, 294], [25800, 59], [25861, 233], [26100, 44], [26161, 233], [26400, 29], [26461, 232], [26700, 15], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [149, 67]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.33|+00.00|-00.77"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 241], "mask": [[0, 49641], [49659, 274], [49967, 261], [50272, 252], [50576, 245], [50879, 240], [51181, 235], [51484, 230], [51786, 227], [52087, 225], [52388, 223], [52689, 221], [52990, 110], [53101, 108], [53291, 109], [53402, 106], [53592, 108], [53703, 105], [53892, 108], [54005, 103], [54192, 108], [54306, 103], [54491, 109], [54607, 102], [54791, 109], [54908, 101], [55091, 109], [55210, 99], [55391, 109], [55511, 98], [55691, 109], [55812, 97], [55991, 108], [56113, 98], [56289, 109], [56414, 98], [56588, 108], [56716, 97], [56887, 108], [57017, 97], [57186, 108], [57318, 98], [57484, 108], [57619, 98], [57783, 108], [57920, 98], [58082, 108], [58222, 98], [58380, 108], [58523, 98], [58679, 108], [58824, 98], [58978, 108], [59125, 98], [59277, 108], [59427, 98], [59575, 108], [59731, 95], [59874, 106], [60031, 96], [60173, 106], [60331, 98], [60471, 108], [60630, 100], [60770, 109], [60930, 101], [61069, 111], [61231, 101], [61368, 111], [61535, 99], [61666, 109], [61837, 98], [61965, 108], [62138, 98], [62264, 108], [62439, 99], [62562, 108], [62740, 99], [62861, 108], [63041, 99], [63160, 108], [63343, 98], [63459, 107], [63644, 99], [63757, 108], [63945, 99], [64056, 108], [64246, 99], [64355, 107], [64547, 99], [64654, 107], [64849, 99], [64952, 108], [65150, 98], [65252, 107], [65451, 97], [65552, 105], [65752, 96], [65852, 104], [66054, 94], [66152, 103], [66355, 93], [66452, 101], [66656, 92], [66752, 100], [66957, 91], [67052, 99], [67258, 90], [67352, 97], [67560, 88], [67652, 96], [67861, 87], [67952, 95], [68162, 86], [68252, 93], [68463, 85], [68552, 92], [68764, 84], [68852, 91], [69066, 82], [69152, 89], [69367, 81], [69452, 88], [69668, 80], [69752, 87], [69969, 79], [70052, 85], [70270, 78], [70352, 85], [70570, 9], [70595, 53], [70652, 59], [70727, 10], [70870, 9], [70914, 34], [70952, 41], [71028, 9], [71170, 9], [71328, 9], [71470, 9], [71628, 9], [71770, 9], [71928, 9], [72070, 8], [72228, 9]], "point": [149, 120]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-02.78|+00.92|-00.96", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-00.33|+00.00|-00.77"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 172], [225, 246], [526, 244], [827, 241], [1128, 239], [1429, 237], [1730, 235], [2031, 233], [2332, 231], [2633, 229], [2933, 228], [3234, 226], [3535, 224], [3835, 224], [4136, 222], [4437, 220], [4737, 220], [5038, 218], [5339, 216], [5639, 216], [5940, 214], [6240, 214], [6541, 212], [6841, 212], [7141, 211], [7442, 210], [7742, 210], [8042, 209], [8343, 208], [8643, 208], [8943, 208], [9244, 206], [9544, 206], [9844, 206], [10144, 206], [10445, 205], [10745, 204], [11045, 204], [11345, 204], [11645, 204], [11945, 204], [12246, 203], [12546, 203], [12846, 204], [13146, 204], [13446, 204], [13746, 204], [14046, 205], [14346, 205], [14646, 205], [14945, 207], [15245, 207], [15545, 208], [15845, 208], [16145, 209], [16445, 210], [16744, 211], [17044, 212], [17344, 213], [17643, 215], [17943, 216], [18242, 218], [18542, 219], [18841, 221], [19141, 222], [19440, 224], [19739, 227], [20038, 229], [20337, 232], [20636, 234], [20934, 238], [21232, 243], [21531, 246], [21829, 251], [22126, 257], [22423, 264], [22719, 273], [23015, 26626], [49659, 274], [49967, 261], [50272, 252], [50576, 245], [50879, 240], [51181, 235], [51484, 230], [51786, 227], [52087, 225], [52388, 223], [52689, 221], [52990, 219], [53291, 217], [53592, 216], [53892, 216], [54192, 217], [54491, 218], [54791, 218], [55091, 218], [55391, 218], [55691, 218], [55991, 220], [56289, 223], [56588, 225], [56887, 227], [57186, 230], [57484, 233], [57783, 235], [58082, 238], [58380, 241], [58679, 243], [58978, 245], [59277, 248], [59575, 251], [59874, 253], [60173, 157], [60331, 98], [60471, 259], [60770, 109], [60880, 151], [61069, 161], [61231, 101], [61368, 111], [61480, 49], [61535, 99], [61666, 109], [61780, 49], [61837, 98], [61965, 108], [62081, 48], [62138, 98], [62264, 108], [62381, 48], [62439, 99], [62562, 108], [62681, 47], [62740, 99], [62861, 108], [62981, 47], [63041, 99], [63160, 108], [63282, 46], [63343, 98], [63459, 107], [63582, 46], [63644, 99], [63757, 108], [63882, 45], [63945, 99], [64056, 108], [64182, 45], [64246, 99], [64355, 107], [64483, 44], [64547, 99], [64654, 107], [64783, 44], [64849, 99], [64952, 108], [65083, 44], [65150, 98], [65252, 107], [65383, 43], [65451, 97], [65552, 105], [65684, 42], [65752, 96], [65852, 104], [65984, 42], [66054, 94], [66152, 103], [66284, 42], [66355, 93], [66452, 101], [66585, 40], [66656, 92], [66752, 100], [66885, 40], [66957, 91], [67052, 99], [67185, 40], [67258, 90], [67352, 97], [67485, 40], [67560, 88], [67652, 96], [67786, 38], [67861, 87], [67952, 95], [68086, 38], [68162, 86], [68252, 93], [68386, 38], [68463, 85], [68552, 92], [68686, 38], [68764, 84], [68852, 91], [68987, 36], [69066, 82], [69152, 89], [69287, 36], [69367, 81], [69452, 88], [69587, 36], [69668, 80], [69752, 87], [69887, 36], [69969, 79], [70052, 85], [70188, 34], [70270, 78], [70352, 85], [70488, 34], [70570, 9], [70595, 53], [70652, 59], [70727, 10], [70788, 34], [70870, 9], [70914, 34], [70952, 41], [71028, 9], [71088, 34], [71170, 9], [71328, 9], [71389, 33], [71470, 9], [71628, 9], [71689, 32], [71770, 9], [71928, 9], [71989, 32], [72070, 8], [72228, 9], [72289, 32], [72590, 31], [72890, 30], [73190, 30], [73490, 30], [73791, 29], [74091, 28], [74391, 28], [74691, 28], [74992, 27], [75292, 26], [75592, 26], [75893, 25], [76193, 25], [76493, 24], [76793, 24], [77094, 23], [77394, 23], [77694, 23], [77994, 22], [78295, 21], [78595, 21], [78895, 21], [79195, 20], [79496, 19], [79796, 19], [80096, 19], [80396, 18], [80697, 17], [80997, 17], [81297, 17], [81597, 16], [81898, 15], [82198, 15], [82498, 15], [82798, 14], [83099, 13], [83399, 13], [83699, 13], [83999, 13], [84300, 11], [84600, 11], [84900, 11], [85200, 11], [85500, 10], [85800, 10], [86100, 10], [86400, 10], [86700, 9], [87000, 9], [87300, 9], [87600, 9], [87900, 8], [88200, 8], [88500, 8], [88800, 8], [89100, 7], [89400, 7], [89700, 7]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.33|+00.00|-00.77"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 103], [125, 47], [225, 175], [427, 44], [526, 171], [730, 40], [827, 169], [1031, 37], [1128, 166], [1332, 35], [1429, 164], [1633, 33], [1730, 162], [1934, 31], [2031, 160], [2235, 29], [2332, 158], [2536, 27], [2633, 156], [2836, 26], [2933, 156], [3136, 25], [3234, 154], [3437, 23], [3535, 153], [3737, 22], [3835, 153], [4037, 22], [4136, 152], [4337, 21], [4437, 151], [4637, 20], [4737, 151], [4936, 21], [5038, 150], [5236, 20], [5339, 149], [5536, 19], [5639, 149], [5835, 20], [5940, 149], [6134, 20], [6240, 150], [6433, 21], [6541, 150], [6732, 21], [6841, 151], [7031, 22], [7141, 152], [7330, 22], [7442, 152], [7629, 23], [7742, 154], [7927, 25], [8042, 156], [8226, 25], [8343, 157], [8525, 26], [8643, 159], [8824, 27], [8943, 161], [9123, 28], [9244, 162], [9421, 29], [9544, 164], [9720, 30], [9844, 166], [10019, 31], [10144, 168], [10318, 32], [10445, 169], [10617, 33], [10745, 169], [10917, 32], [11045, 169], [11217, 32], [11345, 169], [11517, 32], [11645, 169], [11817, 32], [11945, 170], [12117, 32], [12246, 169], [12417, 32], [12546, 169], [12718, 31], [12846, 169], [13018, 32], [13146, 169], [13318, 32], [13446, 169], [13618, 32], [13746, 169], [13918, 32], [14046, 170], [14218, 33], [14346, 170], [14522, 29], [14646, 166], [14826, 25], [14945, 165], [15128, 24], [15245, 163], [15429, 23], [15545, 162], [15730, 23], [15845, 161], [16031, 22], [16145, 160], [16332, 22], [16445, 160], [16632, 23], [16744, 160], [16932, 23], [17044, 160], [17233, 23], [17344, 160], [17533, 24], [17643, 161], [17832, 26], [17943, 161], [18132, 27], [18242, 162], [18431, 29], [18542, 163], [18731, 30], [18841, 165], [19030, 32], [19141, 166], [19329, 34], [19440, 168], [19627, 37], [19739, 171], [19925, 41], [20038, 174], [20223, 44], [20337, 232], [20636, 234], [20934, 238], [21232, 243], [21531, 246], [21829, 251], [22126, 257], [22423, 264], [22719, 273], [23015, 37315], [60331, 548], [60880, 350], [61231, 248], [61480, 49], [61535, 240], [61780, 49], [61837, 236], [62081, 48], [62138, 234], [62381, 48], [62439, 231], [62681, 47], [62740, 229], [62981, 47], [63041, 227], [63282, 46], [63343, 223], [63582, 46], [63644, 221], [63882, 45], [63945, 219], [64182, 45], [64246, 216], [64483, 44], [64547, 214], [64783, 44], [64849, 211], [65083, 44], [65150, 209], [65383, 43], [65451, 206], [65684, 42], [65752, 204], [65984, 42], [66054, 201], [66284, 42], [66355, 198], [66585, 40], [66656, 196], [66885, 40], [66957, 194], [67185, 40], [67258, 191], [67485, 40], [67560, 188], [67786, 38], [67861, 186], [68086, 38], [68162, 183], [68386, 38], [68463, 181], [68686, 38], [68764, 179], [68987, 36], [69066, 175], [69287, 36], [69367, 173], [69587, 36], [69668, 171], [69887, 36], [69969, 168], [70188, 34], [70270, 167], [70488, 34], [70570, 9], [70595, 116], [70727, 10], [70788, 34], [70870, 9], [70914, 79], [71028, 9], [71088, 34], [71170, 9], [71328, 9], [71389, 33], [71470, 9], [71628, 9], [71689, 32], [71770, 9], [71928, 9], [71989, 32], [72070, 8], [72228, 9], [72289, 32], [72590, 31], [72890, 30], [73190, 30], [73490, 30], [73791, 29], [74091, 28], [74391, 28], [74691, 28], [74992, 27], [75292, 26], [75592, 26], [75893, 25], [76193, 25], [76493, 24], [76793, 24], [77094, 23], [77394, 23], [77694, 23], [77994, 22], [78295, 21], [78595, 21], [78895, 21], [79195, 20], [79496, 19], [79796, 19], [80096, 19], [80396, 18], [80697, 17], [80997, 17], [81297, 17], [81597, 16], [81898, 15], [82198, 15], [82498, 15], [82798, 14], [83099, 13], [83399, 13], [83699, 13], [83999, 13], [84300, 11], [84600, 11], [84900, 11], [85200, 11], [85500, 10], [85800, 10], [86100, 10], [86400, 10], [86700, 9], [87000, 9], [87300, 9], [87600, 9], [87900, 8], [88200, 8], [88500, 8], [88800, 8], [89100, 7], [89400, 7], [89700, 7]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan23", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -3.75, "y": 0.9009995, "z": -3.0}, "object_poses": [{"objectName": "Glassbottle_76ad74d1", "position": {"x": -0.436777174, "y": 0.9165167, "z": -2.56359529}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_76ad74d1", "position": {"x": -0.671999454, "y": 0.916516662, "z": -3.55599165}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_638e2261", "position": {"x": -2.66682315, "y": 0.9261279, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_7baac0ce", "position": {"x": -0.388544142, "y": 1.72218716, "z": -0.6653123}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Potato_7baac0ce", "position": {"x": -1.309959, "y": 1.66156638, "z": -3.90336013}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b4789065", "position": {"x": -0.2307572, "y": 1.97008312, "z": -1.77351022}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b4789065", "position": {"x": -2.2105732, "y": 0.9230393, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f1c487d3", "position": {"x": -2.438698, "y": 0.923517168, "z": -1.88492227}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "WineBottle_cbf780b9", "position": {"x": -2.78088546, "y": 0.9244038, "z": -2.11539078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_cbf780b9", "position": {"x": -0.370761573, "y": 1.53168643, "z": -3.76842332}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Pot_45f18f99", "position": {"x": -0.3439546, "y": 1.39024866, "z": -0.6653124}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spatula_cdf5a5fc", "position": {"x": -2.32463574, "y": 0.938474953, "z": -1.65445352}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_cdf5a5fc", "position": {"x": -2.438698, "y": 0.938474953, "z": -1.88492227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_a6c5bfa2", "position": {"x": -0.600443959, "y": 0.904205859, "z": -3.62671828}, "rotation": {"x": 0.317450434, "y": -1.29908049e-05, "z": 359.902832}}, {"objectName": "Plate_dc085d43", "position": {"x": -2.78088546, "y": 0.9258191, "z": -1.65445352}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_dc085d43", "position": {"x": -0.4609022, "y": 0.08366948, "z": -2.24718785}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_6d28f5dd", "position": {"x": -0.343954, "y": 1.42378128, "z": -0.4559376}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_3d47a8d4", "position": {"x": -2.5527606, "y": 0.9872864, "z": -0.963047445}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7d6a9ffb", "position": {"x": -2.66682315, "y": 1.01634932, "z": -2.34585953}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_fbb38b83", "position": {"x": -0.392818868, "y": 1.151585, "z": -0.874687254}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SaltShaker_895229e9", "position": {"x": -2.09651065, "y": 0.919781268, "z": -2.11539078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_895229e9", "position": {"x": -0.385839432, "y": 0.912106156, "z": -3.83909845}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_80763932", "position": {"x": -2.2105732, "y": 0.9184751, "z": -1.88492227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_c99eb5e1", "position": {"x": -0.3085297, "y": 1.76433444, "z": -0.77}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_c99eb5e1", "position": {"x": -2.006342, "y": 0.100445457, "z": -3.76336551}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_c99eb5e1", "position": {"x": -0.528919458, "y": 0.9892841, "z": -3.909875}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7d6a9ffb", "position": {"x": -2.438698, "y": 1.01634932, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_76ad74d1", "position": {"x": -0.815079451, "y": 0.916516662, "z": -3.76832175}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_6d28f5dd", "position": {"x": -2.66682315, "y": 0.956406057, "z": -2.11539078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_638e2261", "position": {"x": -2.78088546, "y": 0.9261279, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_3622ecde", "position": {"x": -2.09651065, "y": 0.9240236, "z": -1.65445352}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_53032dff", "position": {"x": -0.344812572, "y": 0.07988125, "z": -1.71242881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_9e9e972c", "position": {"x": -0.5082214, "y": 0.7474724, "z": -3.238363}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "WineBottle_cbf780b9", "position": {"x": -0.4992076, "y": 0.08195382, "z": -3.29917526}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_cdf5a5fc", "position": {"x": -0.350599945, "y": 0.8121972, "z": -1.8607595}, "rotation": {"x": 6.06642669e-21, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Bread_fbb38b83", "position": {"x": -0.418225169, "y": 0.9552672, "z": -3.62473321}, "rotation": {"x": 0.159515187, "y": 359.592163, "z": 359.015625}}, {"objectName": "Tomato_3d47a8d4", "position": {"x": -2.438698, "y": 0.9872864, "z": -1.19351614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_8d50963e", "position": {"x": -1.1294, "y": 0.9357, "z": -3.4986}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_3611fe48", "position": {"x": -2.78088546, "y": 0.92246145, "z": -0.963047445}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_45f18f99", "position": {"x": -1.5155, "y": 0.9357, "z": -3.4974}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_895229e9", "position": {"x": -2.09651065, "y": 0.919781268, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_7baac0ce", "position": {"x": -1.8812623, "y": 0.0582982339, "z": -3.76336551}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b8151885", "position": {"x": -2.2105732, "y": 0.919781268, "z": -1.19351614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f1c487d3", "position": {"x": -0.3506, "y": 0.7972394, "z": -2.08203816}, "rotation": {"x": 6.06642669e-21, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "DishSponge_b4789065", "position": {"x": -1.549066, "y": 1.96924365, "z": -3.77324247}, "rotation": {"x": 0.0, "y": 0.0, "z": -1.70754731e-06}}, {"objectName": "Spoon_a6c5bfa2", "position": {"x": -2.09651065, "y": 0.9246045, "z": -2.34585953}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_dc085d43", "position": {"x": -2.66682315, "y": 0.9258191, "z": -1.88492227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_d25a57c3", "position": {"x": -2.26999664, "y": 0.920386732, "z": -2.14300442}, "rotation": {"x": 90.0, "y": 315.0028, "z": 0.0}}, {"objectName": "Mug_80763932", "position": {"x": -0.447167248, "y": 0.792197347, "z": -2.155798}, "rotation": {"x": 6.06642669e-21, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Bowl_91c0655d", "position": {"x": -1.10090959, "y": 1.96871722, "z": -3.85109758}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3073424136, "scene_num": 23}, "task_id": "trial_T20190908_045659_449231", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AKW57KYG90X61_34S9DKFK766P1UG4XJJUMVNMYO9YND", "high_descs": ["move right and head for the table", "pick up the glass cup from the table", "head forward to the oven", "place the glass cup to heat and take it out", "turn around and head for the fridge", "place the glass cup in the fridge"], "task_desc": "place a hot glass cup in the fridge", "votes": [1, 1]}, {"assignment_id": "A2KAGFQU28JY43_392CY0QWG48HPMRTBWIV1YNYHPFI4X", "high_descs": ["Turn to your right and walk to the wall, then turn right at the kitchen island and right again so that your are facing the end of the island. ", "Pick up the Martini glass on the right corner of the island. ", "Turn to your left, walk towards the refrigerator and make a right, then go to the microwave above the stove, straight ahead of you. ", "Place the Martini glass in the microwave, wait a moment for it to heat up, then remove the Martini glass from the icrowave. ", "Turn around and go to the refrigerator at the end of the kitchen, on your right. ", "Place the Martini glass in the refrigerator, on the top shelf, to the left of the bread. "], "task_desc": "Put a warm Martini glass in the refrigerator. ", "votes": [1, 1]}, {"assignment_id": "A24RGLS3BRZ60J_3I02618YA3N8N68C3XZXNZM42GUPUZ", "high_descs": ["Turn to the right and right again to face the island on the right.", "Pick up the glass from the right side of the island. ", "Carry the glass and turn to the left and go to the stove.", "Open the microwave above and place the glass inside. Then open the door and take out the glass. Shut the door.", "Carry the glass and turn around. Move to the fridge on the right side.", "Open the fridge and place the glass inside. Shut the door."], "task_desc": "Heat a glass and place it in the fridge.", "votes": [1, 1]}]}}