{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000096.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000097.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000098.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000108.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000110.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000111.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000112.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000113.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000114.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000115.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000116.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000117.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000118.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000119.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000120.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000121.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000205.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000206.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000207.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000208.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000209.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000210.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000211.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000265.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000266.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000267.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000268.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000269.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000270.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000271.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000272.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000273.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000274.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000275.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000276.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000277.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000278.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000279.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000280.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000281.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000334.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000335.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000336.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000337.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000338.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000339.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000340.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000341.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000342.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000343.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000344.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000345.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000346.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000347.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000348.png", "low_idx": 64}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-15|18|0|15"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-15.22902584, -15.22902584, 22.046436, 22.046436, 5.83083008, 5.83083008]], "coordinateReceptacleObjectId": ["Cabinet", [-16.452, -16.452, 21.2872, 21.2872, 7.744, 7.744]], "forceVisible": true, "objectId": "Cup|-03.81|+01.46|+05.51"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-11|10|2|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-15.22902584, -15.22902584, 22.046436, 22.046436, 5.83083008, 5.83083008]], "coordinateReceptacleObjectId": ["CounterTop", [-7.4884, -7.4884, 7.2, 7.2, 3.836, 3.836]], "forceVisible": true, "objectId": "Cup|-03.81|+01.46|+05.51", "receptacleObjectId": "CounterTop|-01.87|+00.96|+01.80"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-04.11|+01.94|+05.32"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [22, 1, 193, 124], "mask": [[22, 172], [322, 172], [622, 172], [922, 172], [1222, 172], [1522, 172], [1822, 172], [2122, 172], [2422, 172], [2723, 171], [3023, 171], [3323, 171], [3623, 171], [3923, 171], [4223, 170], [4523, 170], [4823, 170], [5124, 169], [5424, 169], [5724, 169], [6024, 169], [6324, 169], [6624, 169], [6924, 169], [7224, 169], [7524, 169], [7825, 168], [8125, 168], [8425, 168], [8725, 168], [9025, 168], [9325, 168], [9625, 168], [9925, 168], [10225, 168], [10526, 167], [10826, 167], [11126, 167], [11426, 167], [11726, 166], [12026, 166], [12326, 166], [12626, 166], [12927, 165], [13227, 165], [13527, 165], [13827, 165], [14127, 165], [14427, 165], [14727, 165], [15027, 165], [15327, 165], [15628, 164], [15928, 164], [16228, 164], [16528, 164], [16828, 164], [17128, 164], [17428, 164], [17728, 164], [18028, 164], [18329, 163], [18629, 163], [18929, 163], [19229, 163], [19529, 162], [19829, 162], [20129, 162], [20429, 162], [20729, 162], [21030, 161], [21330, 161], [21630, 161], [21930, 161], [22230, 161], [22530, 161], [22830, 161], [23130, 161], [23431, 160], [23731, 160], [24031, 160], [24331, 160], [24631, 160], [24931, 160], [25231, 160], [25531, 160], [25831, 160], [26132, 159], [26432, 159], [26732, 159], [27032, 158], [27332, 158], [27632, 158], [27932, 158], [28232, 158], [28532, 158], [28833, 157], [29133, 157], [29433, 157], [29733, 157], [30033, 157], [30333, 157], [30633, 157], [30933, 157], [31234, 156], [31534, 156], [31834, 156], [32134, 156], [32434, 156], [32734, 156], [33034, 156], [33334, 156], [33634, 156], [33935, 155], [34235, 155], [34535, 155], [34835, 154], [35135, 154], [35435, 154], [35735, 154], [36035, 154], [36335, 154], [36636, 153], [36936, 153]], "point": [107, 61]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-03.81|+01.46|+05.51"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [118, 83, 150, 113], "mask": [[24718, 33], [25018, 33], [25319, 32], [25619, 32], [25919, 32], [26219, 32], [26519, 31], [26819, 31], [27120, 30], [27420, 30], [27720, 30], [28020, 30], [28321, 28], [28621, 27], [28922, 26], [29222, 25], [29523, 24], [29823, 24], [30123, 24], [30423, 24], [30723, 23], [31024, 22], [31324, 22], [31624, 22], [31924, 22], [32224, 22], [32524, 22], [32824, 22], [33124, 22], [33424, 22], [33728, 14]], "point": [134, 97]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-04.11|+01.94|+05.32"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 189, 142], "mask": [[0, 24], [29, 161], [300, 24], [329, 161], [600, 24], [629, 161], [900, 24], [929, 160], [1200, 24], [1229, 160], [1500, 25], [1529, 160], [1800, 25], [1829, 160], [2100, 25], [2129, 160], [2400, 25], [2430, 159], [2700, 25], [2730, 159], [3000, 25], [3030, 159], [3300, 25], [3330, 159], [3600, 25], [3630, 159], [3900, 26], [3930, 159], [4200, 26], [4230, 159], [4500, 26], [4530, 159], [4800, 26], [4830, 159], [5100, 26], [5131, 158], [5400, 26], [5431, 158], [5700, 26], [5731, 158], [6000, 26], [6031, 158], [6300, 26], [6331, 158], [6600, 27], [6631, 158], [6900, 27], [6931, 158], [7200, 27], [7231, 158], [7500, 27], [7531, 158], [7800, 27], [7832, 157], [8100, 27], [8400, 27], [8700, 27], [9000, 27], [9300, 28], [9600, 28], [9632, 156], [9900, 28], [9932, 156], [10200, 28], [10232, 156], [10500, 28], [10533, 155], [10800, 28], [10833, 155], [11100, 28], [11133, 155], [11400, 28], [11433, 155], [11700, 28], [11733, 155], [12000, 29], [12033, 155], [12300, 29], [12333, 155], [12600, 29], [12633, 155], [12900, 29], [12933, 155], [13200, 29], [13234, 154], [13500, 29], [13534, 154], [13800, 29], [13834, 154], [14100, 29], [14134, 154], [14400, 29], [14434, 154], [14700, 30], [14734, 154], [15000, 30], [15034, 154], [15300, 30], [15334, 154], [15600, 30], [15634, 154], [15900, 30], [15934, 154], [16200, 30], [16235, 153], [16500, 30], [16535, 153], [16800, 30], [16835, 153], [17100, 30], [17135, 153], [17400, 31], [17435, 153], [17700, 31], [17735, 153], [18000, 31], [18035, 152], [18300, 31], [18335, 152], [18600, 31], [18635, 152], [18900, 31], [18936, 151], [19200, 31], [19236, 151], [19500, 31], [19536, 151], [19800, 31], [19836, 151], [20100, 32], [20136, 151], [20400, 32], [20436, 151], [20700, 32], [20736, 151], [21000, 32], [21036, 151], [21300, 32], [21336, 151], [21600, 32], [21637, 150], [21900, 32], [21937, 150], [22200, 32], [22237, 150], [22500, 32], [22537, 150], [22800, 33], [22837, 150], [23100, 33], [23137, 150], [23400, 33], [23437, 150], [23700, 33], [23737, 150], [24000, 33], [24037, 150], [24300, 33], [24338, 149], [24600, 33], [24638, 149], [24900, 33], [24938, 149], [25200, 34], [25238, 149], [25500, 34], [25538, 149], [25800, 34], [25838, 149], [26100, 34], [26138, 149], [26400, 34], [26438, 148], [26700, 34], [26738, 148], [27000, 34], [27038, 148], [27300, 34], [27339, 147], [27600, 34], [27639, 147], [27900, 35], [27939, 147], [28200, 35], [28239, 147], [28500, 35], [28539, 147], [28800, 35], [28839, 147], [29100, 35], [29139, 147], [29400, 35], [29439, 147], [29700, 35], [29739, 147], [30000, 35], [30040, 146], [30300, 35], [30340, 146], [30600, 36], [30640, 146], [30900, 36], [30940, 146], [31200, 36], [31240, 146], [31500, 36], [31540, 146], [31800, 36], [31840, 146], [32100, 36], [32140, 146], [32400, 36], [32440, 146], [32700, 36], [32741, 145], [33000, 36], [33041, 145], [33300, 37], [33341, 145], [33600, 37], [33641, 145], [33900, 37], [33941, 145], [34200, 37], [34241, 145], [34500, 37], [34541, 145], [34800, 37], [34841, 145], [35100, 37], [35141, 144], [35400, 37], [35442, 143], [35700, 37], [35742, 143], [36000, 38], [36042, 143], [36300, 38], [36600, 38], [36900, 37], [37200, 36], [37500, 34], [37800, 32], [38100, 30], [38400, 28], [38700, 26], [39000, 24], [39300, 22], [39600, 20], [39900, 18], [40200, 16], [40500, 14], [40800, 12], [41100, 10], [41400, 8], [41700, 6], [42000, 4], [42300, 2]], "point": [94, 70]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-03.81|+01.46|+05.51", "placeStationary": true, "receptacleObjectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 125], [14226, 138], [14400, 109], [14544, 120], [14700, 106], [14849, 115], [15000, 105], [15150, 114], [15300, 105], [15450, 114], [15600, 106], [15750, 114], [15900, 106], [16050, 114], [16200, 106], [16350, 114], [16500, 106], [16650, 114], [16800, 106], [16949, 114], [17100, 107], [17249, 114], [17400, 107], [17549, 114], [17700, 107], [17849, 114], [18000, 107], [18149, 114], [18300, 107], [18449, 114], [18600, 107], [18749, 114], [18900, 108], [19048, 115], [19200, 108], [19348, 115], [19500, 108], [19648, 114], [19800, 109], [19947, 115], [20100, 109], [20247, 115], [20400, 110], [20546, 116], [20700, 110], [20846, 116], [21000, 111], [21145, 117], [21300, 111], [21445, 117], [21600, 111], [21745, 117], [21900, 111], [22044, 118], [22200, 112], [22344, 117], [22500, 112], [22644, 117], [22800, 112], [22944, 117], [23100, 112], [23244, 117], [23400, 112], [23544, 117], [23700, 113], [23843, 118], [24000, 113], [24143, 118], [24300, 113], [24443, 118], [24600, 113], [24743, 118], [24900, 113], [25043, 118], [25200, 114], [25343, 117], [25500, 114], [25643, 117], [25800, 114], [25943, 117], [26100, 114], [26243, 117], [26400, 114], [26543, 117], [26700, 114], [26843, 117], [27000, 114], [27143, 117], [27300, 124], [27431, 129], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [145, 70]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-03.81|+01.46|+05.51"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [105, 48, 149, 92], "mask": [[14225, 1], [14509, 35], [14806, 43], [15105, 45], [15405, 45], [15706, 44], [16006, 44], [16306, 44], [16606, 44], [16906, 43], [17207, 42], [17507, 42], [17807, 42], [18107, 42], [18407, 42], [18707, 42], [19008, 40], [19308, 40], [19608, 40], [19909, 38], [20209, 38], [20510, 36], [20810, 36], [21111, 34], [21411, 34], [21711, 34], [22011, 33], [22312, 32], [22612, 32], [22912, 32], [23212, 32], [23512, 32], [23813, 30], [24113, 30], [24413, 30], [24713, 30], [25013, 30], [25314, 29], [25614, 29], [25914, 29], [26214, 29], [26514, 29], [26814, 29], [27114, 29], [27424, 7]], "point": [127, 69]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-03.81|+01.46|+05.51", "placeStationary": true, "receptacleObjectId": "CounterTop|-01.87|+00.96|+01.80"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [74, 99, 298, 212], "mask": [[29501, 34], [29575, 71], [29801, 34], [29876, 71], [30100, 35], [30176, 71], [30400, 34], [30476, 51], [30543, 5], [30700, 34], [30776, 51], [30844, 4], [31000, 34], [31076, 51], [31145, 4], [31299, 35], [31376, 50], [31445, 4], [31599, 34], [31676, 50], [31746, 4], [31899, 34], [31976, 50], [32046, 4], [32199, 34], [32276, 50], [32346, 5], [32499, 34], [32576, 51], [32647, 4], [32798, 35], [32876, 51], [32947, 5], [33098, 35], [33175, 52], [33248, 4], [33398, 35], [33475, 53], [33548, 5], [33698, 35], [33787, 41], [33849, 4], [33997, 17], [34017, 17], [34074, 6], [34104, 25], [34149, 4], [34297, 17], [34317, 17], [34374, 12], [34406, 23], [34450, 4], [34597, 17], [34617, 17], [34673, 19], [34706, 23], [34750, 4], [34897, 16], [34917, 17], [34973, 28], [35004, 26], [35050, 5], [35196, 17], [35217, 18], [35272, 58], [35351, 4], [35496, 17], [35516, 19], [35572, 58], [35651, 5], [35796, 17], [35816, 20], [35871, 60], [35952, 4], [36096, 17], [36116, 21], [36170, 61], [36252, 5], [36395, 17], [36416, 22], [36469, 62], [36553, 4], [36695, 17], [36716, 23], [36768, 64], [36852, 6], [36995, 17], [37016, 24], [37067, 65], [37152, 6], [37295, 17], [37316, 26], [37366, 66], [37452, 7], [37595, 17], [37615, 29], [37664, 69], [37751, 8], [37894, 18], [37915, 31], [37963, 96], [38194, 17], [38215, 34], [38261, 99], [38494, 17], [38515, 38], [38558, 102], [38794, 17], [38815, 146], [39093, 19], [39114, 147], [39393, 19], [39414, 148], [39693, 18], [39713, 149], [39993, 18], [40013, 150], [40292, 19], [40313, 150], [40592, 19], [40613, 151], [40892, 19], [40913, 151], [41192, 19], [41213, 152], [41491, 19], [41513, 152], [41791, 19], [41813, 152], [42091, 19], [42113, 153], [42391, 19], [42412, 27], [42474, 92], [42691, 19], [42712, 29], [42774, 93], [42990, 20], [43012, 155], [43290, 19], [43312, 156], [43590, 19], [43612, 156], [43890, 19], [43912, 17], [43934, 135], [44189, 20], [44212, 15], [44237, 64], [44309, 60], [44489, 20], [44512, 14], [44538, 41], [44633, 37], [44789, 20], [44812, 14], [44839, 40], [44933, 37], [45089, 19], [45112, 13], [45140, 39], [45234, 37], [45388, 16], [45415, 10], [45440, 39], [45534, 37], [45688, 14], [45718, 6], [45741, 38], [45835, 37], [45988, 13], [46018, 6], [46041, 38], [46135, 37], [46288, 12], [46318, 6], [46341, 37], [46435, 37], [46587, 13], [46618, 5], [46642, 36], [46736, 37], [46887, 13], [46918, 5], [46942, 36], [47036, 37], [47187, 12], [47218, 5], [47243, 35], [47337, 37], [47487, 12], [47518, 5], [47543, 35], [47637, 37], [47787, 12], [47817, 5], [47843, 35], [47938, 37], [48086, 13], [48117, 5], [48144, 34], [48238, 37], [48386, 12], [48417, 5], [48444, 34], [48538, 38], [48686, 12], [48717, 4], [48744, 34], [48839, 37], [48986, 12], [49017, 4], [49044, 34], [49139, 38], [49285, 13], [49317, 4], [49344, 34], [49440, 37], [49585, 13], [49615, 6], [49644, 34], [49740, 38], [49885, 36], [49944, 34], [50040, 38], [50185, 36], [50244, 34], [50341, 37], [50484, 37], [50544, 34], [50641, 38], [50784, 37], [50844, 34], [50941, 38], [51084, 37], [51143, 35], [51242, 38], [51384, 37], [51443, 35], [51542, 38], [51683, 38], [51743, 36], [51842, 39], [51983, 39], [52043, 36], [52142, 39], [52283, 39], [52361, 18], [52443, 39], [52583, 39], [52668, 11], [52743, 39], [52883, 39], [52973, 6], [53043, 40], [53182, 41], [53277, 2], [53344, 39], [53482, 38], [53644, 40], [53782, 36], [53944, 40], [54082, 34], [54244, 40], [54381, 33], [54544, 41], [54681, 30], [54845, 40], [54981, 29], [55145, 41], [55281, 28], [55445, 41], [55580, 28], [55745, 42], [55880, 27], [56046, 41], [56180, 26], [56346, 42], [56480, 26], [56646, 42], [56779, 26], [56946, 43], [57079, 25], [57246, 43], [57379, 25], [57546, 44], [57679, 25], [57845, 45], [57979, 25], [58140, 50], [58278, 26], [58434, 57], [58578, 26], [58696, 6], [58729, 62], [58878, 26], [58996, 96], [59178, 26], [59296, 96], [59477, 27], [59596, 97], [59777, 28], [59895, 98], [60077, 28], [60195, 99], [60377, 28], [60495, 99], [60676, 29], [60795, 100], [60976, 29], [61095, 100], [61276, 30], [61394, 102], [61576, 30], [61694, 102], [61875, 31], [61994, 103], [62175, 31], [62294, 103], [62475, 32], [62593, 104], [62775, 32], [62893, 105], [63075, 32], [63193, 105], [63374, 33], [63493, 106]], "point": [186, 147]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan13", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.0, "y": 0.8995012, "z": 1.75}, "object_poses": [{"objectName": "Pan_94f6c891", "position": {"x": -2.92400026, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Pan_94f6c891", "position": {"x": -4.411006, "y": 0.9231095, "z": 5.418301}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -3.481279, "y": 0.747251868, "z": 5.1626}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -1.69388, "y": 0.956968, "z": 1.9510051}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -4.18449736, "y": 0.956968, "z": 3.86360288}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -0.5030999, "y": 0.7463716, "z": 6.48951054}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -4.13299274, "y": 0.755717635, "z": 2.32430434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -0.258274853, "y": 0.92564857, "z": 5.603}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -4.517821, "y": 0.9240485, "z": 3.19908571}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -3.25512767, "y": 0.924629331, "z": 5.41275358}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -1.86012983, "y": 0.9246294, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -0.565006, "y": 0.923959553, "z": 6.26166725}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -3.80725646, "y": 1.45770752, "z": 5.511609}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -3.67572927, "y": 0.104547471, "z": 6.375}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -4.1590867, "y": 0.8267966, "z": 3.41021824}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Plate_53fee662", "position": {"x": -1.94325483, "y": 0.924325943, "z": 1.9510051}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -0.411640465, "y": 0.9548003, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -0.258274853, "y": 0.9548003, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -4.159086, "y": 0.84800005, "z": 3.22241282}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -4.031869, "y": 0.980542958, "z": 2.02650452}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -3.472925, "y": 0.7432668, "z": 1.95702887}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -0.181592077, "y": 1.02052867, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -1.777005, "y": 1.01892865, "z": 1.72449732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -3.85912442, "y": 1.00305462, "z": 1.79999614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -3.639459, "y": 0.122178406, "z": 6.29268551}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -3.73307371, "y": 0.9769704, "z": 5.33607531}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -4.084459, "y": 0.8267966, "z": 3.34761667}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Apple_d89eec12", "position": {"x": -3.74827051, "y": 0.122178406, "z": 6.45731449}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -4.26, "y": 0.9532003, "z": 3.86360288}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.218675315, "y": 1.71932483, "z": 3.67356014}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -3.73307371, "y": 0.9240485, "z": 5.48943138}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -0.258274853, "y": 0.922047436, "z": 4.615}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_53fee662", "position": {"x": -0.301118672, "y": 1.5816828, "z": 3.47016573}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -0.2956057, "y": 1.16598535, "z": 3.23174882}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -0.295604616, "y": 1.15011144, "z": 3.60299969}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -3.49410057, "y": 0.9805429, "z": 5.48943138}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -3.34089112, "y": 0.924428642, "z": 2.02650452}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -1.610755, "y": 0.938499868, "z": 1.72449732}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_aa4cec24", "position": {"x": -3.784541, "y": 0.172993273, "z": 6.29268551}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -0.3487285, "y": 1.06941628, "z": 3.97425079}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -1.777005, "y": 0.92379117, "z": 1.8}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_94f6c891", "position": {"x": -2.469, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -0.396172553, "y": 0.912319362, "z": 5.93382072}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -0.284452319, "y": 2.13267446, "z": 6.60010052}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -3.85912442, "y": 0.9232217, "z": 1.573488}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -4.31204462, "y": 0.919806063, "z": 2.30013466}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -4.18449736, "y": 0.9235421, "z": 4.90006828}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -2.109505, "y": 0.923064232, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -4.10899448, "y": 0.9246294, "z": 4.381836}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -0.4098, "y": 0.9556, "z": 6.55700064}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}], "object_toggles": [], "random_seed": 248788060, "scene_num": 13}, "task_id": "trial_T20190907_013610_766116", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AM2KK02JXXW48_33PPO7FECYWUPIAM47JO42H4TLUDIS", "high_descs": ["Move to the corner counter by the tomato.", "Pick up the black cup in the cabinet above the counter. ", "Bring the cup to the microwave.", "Heat the cup in the microwave.", "Bring the heated cup to the counter left of the stove.", "Put the cup on the counter left of the stove. "], "task_desc": "Put a heated cup on the counter left of the stove. ", "votes": [1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3E47SOBEYTDKKQ484BODKWZM8MBCIM", "high_descs": ["walk forwards to the fridge then turn left and walk over to the kitchen counter on your right", "grab a cup out of the cabinet above the kitchen counter on your left", "turn around and walk forwards over to the stove", "place the cup inside the microwave above the stove, microwave it, then take it back out", "turn left and walk over to the end of the kitchen counter on your right", "place the cup down on the kitchen counter"], "task_desc": "place a microwaved cup down on the kitchen counter", "votes": [1, 1]}, {"assignment_id": "A22051NK9RQ4FV_3WS1NTTKE1TGG772RNP8MYKTJ5A0FE", "high_descs": ["Turn left and go to the counter facing the two apples.", "Open the left cabinet door, pull out the cup in the cabinet, close the cabinet door.", "Turn around, take the cup to the microwave above the stove.", "Open the microwave door. Put the cup inside the microwave. Close the microwave door. Set the microwave for 3 seconds and heat the cup. Open the microwave door. Take out the cup. Close the microwave door.", "Turn left and go to the end of the counter top on the left side of the stove.", "Put the cup on the counter top to the right of the blue plate."], "task_desc": "Take a cup from the cabinet, microwave it for three seconds, put cup on counter top.", "votes": [1, 1]}]}}