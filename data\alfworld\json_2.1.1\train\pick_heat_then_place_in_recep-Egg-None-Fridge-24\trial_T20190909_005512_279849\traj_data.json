{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 44}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-3|8|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-3.156599044, -3.156599044, 6.22263716, 6.22263716, 3.7472112, 3.7472112]], "coordinateReceptacleObjectId": ["DiningTable", [-3.5, -3.5, 5.296, 5.296, 0.024, 0.024]], "forceVisible": true, "objectId": "Egg|-00.79|+00.94|+01.56"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-6|13|0|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-6.116, -6.116, 15.5288, 15.5288, 5.00112532, 5.00112532]], "forceVisible": true, "objectId": "Microwave|-01.53|+01.25|+03.88"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|9|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-3.156599044, -3.156599044, 6.22263716, 6.22263716, 3.7472112, 3.7472112]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-10.572, -10.572, 8.536, 8.536, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|-00.79|+00.94|+01.56", "receptacleObjectId": "<PERSON><PERSON>|-02.64|+00.00|+02.13"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.79|+00.94|+01.56"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [152, 119, 173, 141], "mask": [[35560, 7], [35858, 11], [36157, 13], [36456, 15], [36755, 17], [37054, 18], [37354, 19], [37653, 20], [37953, 20], [38253, 21], [38552, 22], [38852, 22], [39152, 22], [39452, 22], [39753, 20], [40053, 20], [40353, 20], [40654, 18], [40954, 18], [41255, 16], [41556, 14], [41857, 12], [42160, 6]], "point": [162, 129]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.79|+00.94|+01.56", "placeStationary": true, "receptacleObjectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 21753], [21759, 292], [22061, 288], [22363, 285], [22664, 283], [22965, 281], [23265, 281], [23566, 279], [23867, 278], [24167, 277], [24468, 276], [24768, 275], [25068, 131], [25200, 143], [25369, 130], [25500, 143], [25669, 130], [25800, 142], [25969, 129], [26100, 142], [26270, 128], [26400, 142], [26570, 128], [26700, 142], [26870, 128], [27000, 142], [27170, 127], [27300, 142], [27470, 127], [27600, 142], [27770, 127], [27900, 142], [28070, 126], [28200, 142], [28370, 126], [28500, 142], [28669, 127], [28800, 142], [28969, 127], [29100, 143], [29269, 126], [29400, 143], [29568, 127], [29700, 144], [29868, 127], [30000, 144], [30167, 127], [30300, 145], [30467, 127], [30600, 146], [30766, 128], [30900, 146], [31065, 129], [31200, 147], [31364, 129], [31500, 149], [31663, 130], [31800, 150], [31961, 132], [32100, 152], [32259, 133], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [143, 82]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-6.116, -6.116, 15.5288, 15.5288, 5.00112532, 5.00112532]], "forceVisible": true, "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-6.116, -6.116, 15.5288, 15.5288, 5.00112532, 5.00112532]], "forceVisible": true, "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.79|+00.94|+01.56"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [142, 73, 169, 108], "mask": [[21753, 6], [22051, 10], [22349, 14], [22648, 16], [22947, 18], [23246, 19], [23546, 20], [23845, 22], [24145, 22], [24444, 24], [24744, 24], [25043, 25], [25343, 26], [25643, 26], [25942, 27], [26242, 28], [26542, 28], [26842, 28], [27142, 28], [27442, 28], [27742, 28], [28042, 28], [28342, 28], [28642, 27], [28942, 27], [29243, 26], [29543, 25], [29844, 24], [30144, 23], [30445, 22], [30746, 20], [31046, 19], [31347, 17], [31649, 14], [31950, 11], [32252, 7]], "point": [155, 89]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.53|+01.25|+03.88"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 170], "mask": [[0, 25199], [25200, 299], [25500, 299], [25800, 298], [26100, 298], [26400, 298], [26700, 298], [27000, 297], [27300, 297], [27600, 297], [27900, 296], [28200, 296], [28500, 296], [28800, 296], [29100, 295], [29400, 295], [29700, 295], [30000, 294], [30300, 294], [30600, 294], [30900, 294], [31200, 293], [31500, 293], [31800, 293], [32100, 292], [32400, 292], [32700, 292], [33000, 291], [33300, 291], [33600, 291], [33900, 291], [34200, 290], [34500, 290], [34800, 290], [35100, 289], [35400, 289], [35700, 289], [36000, 289], [36300, 288], [36600, 288], [36900, 288], [37200, 287], [37500, 287], [37800, 287], [38100, 287], [38400, 286], [38700, 286], [39000, 286], [39300, 285], [39600, 285], [39900, 285], [40200, 285], [40500, 284], [40800, 284], [41100, 284], [41400, 283], [41700, 283], [42000, 283], [42300, 283], [42600, 282], [42900, 282], [43200, 282], [43500, 281], [43800, 281], [44100, 281], [44400, 281], [44700, 280], [45000, 280], [45300, 280], [45600, 279], [45900, 279], [46200, 279], [46500, 278], [46800, 278], [47100, 278], [47400, 277], [47700, 277], [48000, 277], [48300, 277], [48600, 276], [48900, 276], [49200, 276], [49500, 275], [49800, 275], [50100, 275], [50400, 275], [50700, 274]], "point": [149, 84]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.64|+00.00|+02.13"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 212], "mask": [[0, 4499], [4500, 298], [4800, 298], [5100, 297], [5400, 297], [5700, 296], [6000, 296], [6300, 295], [6600, 295], [6900, 294], [7200, 294], [7500, 293], [7800, 293], [8100, 292], [8400, 292], [8700, 291], [9000, 291], [9300, 290], [9600, 290], [9900, 289], [10200, 289], [10500, 288], [10800, 288], [11100, 287], [11400, 286], [11700, 286], [12000, 285], [12300, 285], [12600, 284], [12900, 284], [13200, 283], [13500, 283], [13800, 282], [14100, 282], [14400, 281], [14700, 281], [15000, 280], [15300, 280], [15600, 279], [15900, 279], [16200, 278], [16500, 278], [16800, 277], [17100, 277], [17400, 276], [17700, 276], [18000, 275], [18300, 274], [18600, 274], [18900, 273], [19200, 273], [19500, 272], [19800, 272], [20100, 271], [20400, 271], [20700, 270], [21000, 270], [21300, 269], [21600, 269], [21900, 268], [22200, 268], [22500, 267], [22800, 267], [23100, 266], [23400, 266], [23700, 265], [24000, 265], [24300, 264], [24600, 264], [24900, 263], [25200, 262], [25500, 262], [25800, 261], [26100, 261], [26400, 260], [26700, 260], [27000, 259], [27300, 259], [27600, 258], [27900, 258], [28200, 257], [28500, 257], [28800, 256], [29100, 256], [29400, 255], [29700, 255], [30000, 254], [30300, 254], [30600, 253], [30900, 253], [31200, 252], [31500, 252], [31800, 251], [32100, 250], [32400, 250], [32700, 249], [33000, 249], [33300, 248], [33600, 248], [33900, 247], [34200, 247], [34500, 246], [34800, 246], [35100, 245], [35400, 245], [35700, 244], [36000, 244], [36300, 243], [36600, 243], [36900, 242], [37200, 242], [37500, 241], [37800, 241], [38100, 240], [38400, 240], [38700, 239], [39000, 238], [39300, 238], [39600, 237], [39900, 237], [40200, 236], [40500, 236], [40800, 235], [41100, 235], [41400, 234], [41700, 234], [42000, 233], [42300, 233], [42600, 232], [42901, 231], [43202, 229], [43503, 228], [43804, 226], [44105, 225], [44406, 223], [44707, 222], [45008, 220], [45309, 219], [45610, 217], [45911, 215], [46212, 214], [46513, 212], [46814, 211], [47115, 209], [47416, 208], [47717, 206], [48018, 205], [48319, 203], [48619, 203], [48920, 201], [49221, 200], [49522, 198], [49823, 197], [50124, 195], [50425, 194], [50726, 192], [51027, 191], [51328, 189], [51629, 188], [51930, 186], [52231, 185], [52532, 183], [52833, 181], [53134, 180], [53435, 178], [53736, 177], [54037, 175], [54338, 174], [54638, 173], [54939, 172], [55240, 170], [55541, 169], [55842, 167], [56143, 166], [56444, 164], [56745, 163], [57046, 161], [57347, 160], [57648, 158], [57949, 157], [58250, 155], [58551, 154], [58852, 152], [59153, 150], [59454, 149], [59755, 147], [60056, 146], [60356, 145], [60657, 144], [60958, 142], [61259, 141], [61560, 139], [61861, 138], [62162, 136], [62467, 128], [62802, 5], [62809, 5], [63103, 3], [63109, 5], [63403, 3], [63410, 3]], "point": [149, 105]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.79|+00.94|+01.56", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.64|+00.00|+02.13"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 7897], [7903, 294], [8215, 282], [8536, 261], [8847, 250], [9146, 251], [9446, 252], [9746, 252], [10046, 252], [10346, 253], [10646, 253], [10945, 254], [11245, 255], [11545, 255], [11845, 255], [12145, 256], [12444, 258], [12744, 259], [13044, 260], [13344, 262], [13643, 264], [13943, 265], [14242, 267], [14541, 270], [14840, 272], [15137, 277], [15435, 280], [15733, 284], [16031, 287], [16329, 291], [16627, 295], [16926, 8386], [25321, 292], [25638, 275], [25972, 242], [26271, 244], [26570, 245], [26870, 246], [27169, 248], [27468, 250], [27767, 252], [28065, 256], [28363, 259], [28662, 263], [28959, 268], [29256, 274], [29553, 282], [29847, 29149], [58997, 299], [59297, 299], [59597, 299], [59897, 299], [60197, 299], [60497, 299], [60797, 767], [61566, 129], [61697, 167], [61867, 128], [61998, 165], [62167, 128], [62298, 165], [62467, 128], [62598, 165], [62898, 165], [63198, 165], [63498, 165], [63798, 164], [64098, 164], [64398, 164], [64698, 164], [64998, 164], [65299, 163], [65599, 162], [65899, 162], [66199, 162], [66499, 162], [66799, 162], [67099, 162], [67399, 161], [67699, 161], [67999, 101], [68102, 58], [68299, 101], [68404, 56], [68600, 100], [68706, 54], [68900, 100], [69008, 52], [69200, 100], [69310, 49], [69500, 100], [69612, 47], [69800, 100], [69915, 44], [70100, 100], [70217, 42], [70400, 100], [70519, 39], [70700, 100], [70821, 37], [71000, 100], [71123, 35], [71300, 100], [71425, 32], [71601, 99], [71727, 30], [71901, 99], [72029, 28], [72201, 99], [72331, 26], [72501, 99], [72633, 24], [72801, 99], [72936, 21], [73101, 99], [73238, 18], [73401, 99], [73540, 16], [73701, 99], [74001, 99], [74301, 99], [74601, 99], [74902, 98], [75202, 98], [75502, 98], [75802, 98], [76102, 98], [76402, 98], [76702, 98], [77002, 98], [77302, 98], [77602, 98], [77902, 98], [78203, 97], [78503, 97], [78803, 97], [79103, 97], [79403, 97], [79703, 97], [80003, 97], [80303, 97], [80603, 97], [80903, 97], [81204, 96], [81504, 96], [81804, 96], [82104, 96], [82404, 96], [82704, 96], [83004, 96], [83304, 96], [83604, 96], [83904, 96], [84204, 96], [84505, 95], [84805, 95], [85105, 95], [85405, 95], [85705, 95], [86005, 95], [86305, 95], [86605, 95], [86905, 95], [87205, 95], [87505, 95], [87806, 94], [88106, 94], [88406, 94], [88706, 94], [89006, 94], [89306, 94], [89606, 94], [89906, 94]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.64|+00.00|+02.13"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 7897], [7903, 294], [8215, 282], [8536, 261], [8847, 250], [9146, 251], [9446, 252], [9746, 252], [10046, 252], [10346, 253], [10646, 253], [10945, 254], [11245, 255], [11545, 255], [11845, 255], [12145, 256], [12444, 13], [12462, 240], [12744, 11], [12764, 239], [13044, 9], [13065, 239], [13344, 8], [13366, 240], [13643, 9], [13667, 240], [13943, 8], [13967, 241], [14242, 9], [14268, 241], [14541, 9], [14568, 243], [14840, 9], [14869, 243], [15137, 12], [15169, 245], [15435, 14], [15469, 246], [15733, 16], [15769, 248], [16031, 18], [16069, 249], [16329, 20], [16369, 251], [16627, 22], [16669, 253], [16926, 23], [16969, 280], [17269, 280], [17569, 280], [17869, 281], [18168, 282], [18468, 283], [18767, 285], [19066, 287], [19365, 289], [19664, 292], [19962, 5350], [25321, 292], [25638, 275], [25972, 242], [26271, 244], [26570, 245], [26870, 246], [27169, 248], [27468, 250], [27767, 252], [28065, 256], [28363, 259], [28662, 263], [28959, 268], [29256, 274], [29553, 282], [29847, 29149], [58997, 299], [59297, 299], [59597, 299], [59897, 299], [60197, 299], [60497, 299], [60797, 767], [61566, 129], [61697, 167], [61867, 128], [61998, 165], [62167, 128], [62298, 165], [62467, 128], [62598, 165], [62898, 165], [63198, 165], [63498, 165], [63798, 164], [64098, 164], [64398, 164], [64698, 164], [64998, 164], [65299, 163], [65599, 162], [65899, 162], [66199, 162], [66499, 162], [66799, 162], [67099, 162], [67399, 161], [67699, 161], [67999, 101], [68102, 58], [68299, 101], [68404, 56], [68600, 100], [68706, 54], [68900, 100], [69008, 52], [69200, 100], [69310, 49], [69500, 100], [69612, 47], [69800, 100], [69915, 44], [70100, 100], [70217, 42], [70400, 100], [70519, 39], [70700, 100], [70821, 37], [71000, 100], [71123, 35], [71300, 100], [71425, 32], [71601, 99], [71727, 30], [71901, 99], [72029, 28], [72201, 99], [72331, 26], [72501, 99], [72633, 24], [72801, 99], [72936, 21], [73101, 99], [73238, 18], [73401, 99], [73540, 16], [73701, 99], [74001, 99], [74301, 99], [74601, 99], [74902, 98], [75202, 98], [75502, 98], [75802, 98], [76102, 98], [76402, 98], [76702, 98], [77002, 98], [77302, 98], [77602, 98], [77902, 98], [78203, 97], [78503, 97], [78803, 97], [79103, 97], [79403, 97], [79703, 97], [80003, 97], [80303, 97], [80603, 97], [80903, 97], [81204, 96], [81504, 96], [81804, 96], [82104, 96], [82404, 96], [82704, 96], [83004, 96], [83304, 96], [83604, 96], [83904, 96], [84204, 96], [84505, 95], [84805, 95], [85105, 95], [85405, 95], [85705, 95], [86005, 95], [86305, 95], [86605, 95], [86905, 95], [87205, 95], [87505, 95], [87806, 94], [88106, 94], [88406, 94], [88706, 94], [89006, 94], [89306, 94], [89606, 94], [89906, 94]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan24", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -0.25, "y": 0.9009992, "z": 3.25}, "object_poses": [{"objectName": "Spatula_8e927511", "position": {"x": 0.875373, "y": 0.8380999, "z": 0.984}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_ac730c84", "position": {"x": -2.0870223, "y": 0.700063646, "z": 3.69917965}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_ac730c84", "position": {"x": -2.49774671, "y": 0.8242294, "z": 3.94783449}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_0cf79ab5", "position": {"x": 0.6359025, "y": 0.692998469, "z": 2.1634078}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_656d9f06", "position": {"x": -1.76773334, "y": 0.131712854, "z": 3.6752}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_45eb0ae6", "position": {"x": 0.650935531, "y": 0.822774947, "z": 0.9395552}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_b53567ce", "position": {"x": -0.657029152, "y": 0.923701346, "z": 1.3495779}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_9f68a721", "position": {"x": 0.950185537, "y": 0.8225346, "z": 2.631965}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_facc9b39", "position": {"x": -2.67724085, "y": 1.47676623, "z": 2.165714}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Bowl_f44f174d", "position": {"x": -2.5802207, "y": 1.63273418, "z": 2.75881338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_0c71376c", "position": {"x": -0.524908543, "y": 0.896926939, "z": 1.55565929}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_0c71376c", "position": {"x": -2.46327543, "y": 1.39507949, "z": 3.9368124}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6dc531a2", "position": {"x": 0.875373, "y": 0.8988629, "z": 2.4585216}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_2e53b233", "position": {"x": -2.69420981, "y": 1.18599272, "z": 2.165714}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Plate_656d9f06", "position": {"x": -1.69768572, "y": 1.687042, "z": 3.897483}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_0cf79ab5", "position": {"x": 1.02499807, "y": 0.8236486, "z": 0.984}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_6b465e10", "position": {"x": -2.57799983, "y": 0.8204176, "z": 2.22177148}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Spatula_8e927511", "position": {"x": 0.800560534, "y": 0.8380999, "z": 2.3718}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_d2e883cd", "position": {"x": -2.49774671, "y": 0.92738533, "z": 3.57574821}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_75837e5e", "position": {"x": -0.5249086, "y": 0.9436566, "z": 1.04045582}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_9f68a721", "position": {"x": 0.9315196, "y": 0.0236824788, "z": 2.91849279}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_ace268e1", "position": {"x": -0.789149761, "y": 0.9368028, "z": 1.55565929}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_45eb0ae6", "position": {"x": -1.18551159, "y": 0.9002957, "z": 1.55565929}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_eb577cac", "position": {"x": 0.6428, "y": 0.850800037, "z": 1.3754}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_0c71376c", "position": {"x": -1.053391, "y": 0.896926939, "z": 1.55565929}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_b017884c", "position": {"x": -2.667, "y": 0.812, "z": 3.74}, "rotation": {"x": 0.0, "y": 137.145508, "z": 0.0}}, {"objectName": "PepperShaker_22dad048", "position": {"x": -2.7091186, "y": 1.39507961, "z": 3.818746}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_af7bbed3", "position": {"x": -1.18551159, "y": 0.900662839, "z": 1.45261872}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_facc9b39", "position": {"x": 1.02499807, "y": 0.8916735, "z": 2.28507829}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0957e87", "position": {"x": -1.31763232, "y": 0.900185, "z": 1.24653733}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_6dc531a2", "position": {"x": -2.46178961, "y": 1.07125568, "z": 2.165714}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Spoon_ac730c84", "position": {"x": -1.18551159, "y": 0.901750147, "z": 1.45261872}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_21fad42f", "position": {"x": 0.875373, "y": 0.884269059, "z": 2.28507829}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_b53567ce", "position": {"x": -0.9212704, "y": 0.923701346, "z": 1.349578}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_2e53b233", "position": {"x": -2.581316, "y": 1.84425914, "z": 2.00841713}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_f44f174d", "position": {"x": -2.17529941, "y": 1.39695573, "z": 3.9368124}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2339116516, "scene_num": 24}, "task_id": "trial_T20190909_005512_279849", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3F7G1FSFWQPLE_3570Y55XZSARCRLG6NVGXXYRRPRYGW", "high_descs": ["Turn left and walk forward, then turn left and walk over to the white table.", "Pick up the egg off of the white table.", "Turn right and walk towards the fridge, then hang a right and walk over to the sink and look up at the microwave.", "Heat up the egg in the microwave then take it out and close the microwave.", "Turn around and walk towards the wall, then turn right and walk up to the fridge.", "Open the fridge and put the heated egg inside then close the door."], "task_desc": "Put a heated egg in the fridge.", "votes": [1, 1, 1]}, {"assignment_id": "A20FCMWP43CVIU_3WYGZ5XF3Z6ZIR5QX8O6D32JHKUKSN", "high_descs": ["turn to face square kitchen table", "pick up brown egg from kitchen table", "walk to face microwave", "cook egg in microwave, remove egg from microwave", "walk left to face fridge", "put egg inside fridge"], "task_desc": "put a cooked egg inside a fridge", "votes": [1, 1, 1]}, {"assignment_id": "AM2KK02JXXW48_30LB5CDZNF1U6DK0O78WQCKDQ2AZ0P", "high_descs": ["Turn around move to the table with the microwave on it.", "Pick up the egg on the table. ", "Turn around, bring the egg to the microwave above the sink. ", "Heat the egg in the microwave. ", "Turn around with the heated egg, bring it to the fridge on the right.", "Put the heated egg in the fridge."], "task_desc": "Put a heated egg in the fridge. ", "votes": [1, 0, 1]}]}}