{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000197.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000198.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000199.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000204.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000206.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 42}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "ToiletPaper", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-9|10|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-8.76954748, -8.76954748, 6.18024968, 6.18024968, 3.292, 3.292]], "coordinateReceptacleObjectId": ["CounterTop", [-8.712, -8.712, 7.56, 7.56, 3.128, 3.128]], "forceVisible": true, "objectId": "ToiletPaper|-02.19|+00.82|+01.55"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-6|14|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-8.76954748, -8.76954748, 6.18024968, 6.18024968, 3.292, 3.292]], "coordinateReceptacleObjectId": ["Drawer", [-5.23028, -5.23028, 7.562752, 7.562752, 1.3516, 1.3516]], "forceVisible": true, "objectId": "ToiletPaper|-02.19|+00.82|+01.55", "receptacleObjectId": "Drawer|-01.31|+00.34|+01.89"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-6|15|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-4.17296172, -4.17296172, 14.81329344, 14.81329344, 0.463021904, 0.463021904]], "coordinateReceptacleObjectId": ["GarbageCan", [-4.26, -4.26, 14.848, 14.848, -0.002419114, -0.002419114]], "forceVisible": true, "objectId": "ToiletPaper|-01.04|+00.12|+03.70"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-6|14|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-4.17296172, -4.17296172, 14.81329344, 14.81329344, 0.463021904, 0.463021904]], "coordinateReceptacleObjectId": ["Drawer", [-5.23028, -5.23028, 7.562752, 7.562752, 1.3516, 1.3516]], "forceVisible": true, "objectId": "ToiletPaper|-01.04|+00.12|+03.70", "receptacleObjectId": "Drawer|-01.31|+00.34|+01.89"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-02.19|+00.82|+01.55"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [133, 98, 140, 124], "mask": [[29235, 3], [29533, 7], [29833, 8], [30133, 8], [30433, 8], [30733, 8], [31033, 8], [31333, 8], [31633, 8], [31933, 8], [32233, 8], [32533, 8], [32833, 8], [33133, 8], [33433, 8], [33733, 8], [34034, 7], [34334, 7], [34634, 7], [34934, 7], [35234, 7], [35534, 7], [35834, 7], [36134, 7], [36434, 7], [36735, 6], [37037, 2]], "point": [136, 110]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.31|+00.34|+01.89"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [59, 123, 183, 149], "mask": [[36659, 125], [36960, 124], [37260, 124], [37560, 124], [37861, 123], [38161, 123], [38461, 122], [38761, 122], [39062, 121], [39362, 121], [39662, 121], [39963, 120], [40263, 120], [40563, 120], [40864, 118], [41164, 118], [41464, 118], [41765, 117], [42065, 117], [42365, 117], [42666, 116], [42966, 116], [43266, 115], [43567, 114], [43867, 114], [44167, 114], [44468, 113]], "point": [121, 135]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-02.19|+00.82|+01.55", "placeStationary": true, "receptacleObjectId": "Drawer|-01.31|+00.34|+01.89"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [41, 123, 190, 196], "mask": [[36664, 116], [36964, 116], [37263, 117], [37563, 118], [37863, 118], [38162, 119], [38462, 119], [38761, 120], [39061, 120], [39361, 120], [39660, 121], [39960, 122], [40260, 122], [40559, 123], [40859, 123], [41158, 124], [41458, 124], [41758, 124], [42057, 126], [42357, 126], [42657, 126], [42956, 127], [43256, 127], [43556, 127], [43855, 128], [44155, 128], [44454, 130], [44754, 130], [45054, 130], [45353, 131], [45653, 131], [45953, 131], [46252, 132], [46552, 132], [46851, 134], [47151, 134], [47451, 134], [47750, 135], [48050, 135], [48350, 135], [48649, 136], [48949, 137], [49248, 138], [49548, 138], [49848, 138], [50147, 139], [50442, 148], [50741, 150], [51041, 150], [51342, 149], [51642, 149], [51943, 147], [52243, 147], [52543, 147], [52844, 146], [53144, 146], [53445, 145], [53745, 144], [54046, 143], [54346, 143], [54647, 142], [54947, 142], [55248, 140], [55548, 140], [55848, 140], [56149, 139], [56449, 139], [56750, 138], [57050, 137], [57351, 136], [57651, 136], [57952, 135], [58252, 135], [58553, 133]], "point": [115, 158]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.31|+00.34|+01.89"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [41, 123, 190, 196], "mask": [[36664, 116], [36964, 116], [37263, 117], [37563, 118], [37863, 118], [38162, 119], [38462, 119], [38761, 120], [39061, 120], [39361, 120], [39660, 121], [39960, 122], [40260, 122], [40559, 123], [40859, 123], [41158, 124], [41458, 124], [41758, 124], [42057, 126], [42357, 126], [42657, 126], [42956, 127], [43256, 127], [43556, 127], [43855, 128], [44155, 128], [44454, 130], [44754, 130], [45054, 130], [45353, 131], [45653, 131], [45953, 131], [46252, 132], [46552, 132], [46851, 134], [47151, 134], [47451, 134], [47750, 21], [47775, 110], [48050, 20], [48076, 109], [48350, 20], [48376, 109], [48649, 22], [48677, 108], [48949, 22], [48977, 109], [49248, 23], [49277, 109], [49548, 23], [49577, 109], [49848, 24], [49878, 108], [50147, 25], [50178, 108], [50442, 148], [50741, 150], [51041, 150], [51342, 149], [51642, 149], [51943, 147], [52243, 147], [52543, 147], [52844, 146], [53144, 146], [53445, 145], [53745, 144], [54046, 143], [54346, 143], [54647, 142], [54947, 142], [55248, 140], [55548, 140], [55848, 140], [56149, 139], [56449, 139], [56750, 138], [57050, 137], [57351, 136], [57651, 136], [57952, 135], [58252, 135], [58553, 133]], "point": [115, 158]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-01.04|+00.12|+03.70"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [149, 193, 167, 215], "mask": [[57758, 2], [58054, 9], [58353, 12], [58652, 14], [58951, 15], [59250, 17], [59550, 18], [59850, 18], [60150, 18], [60449, 19], [60749, 19], [61049, 19], [61349, 19], [61649, 19], [61949, 19], [62249, 18], [62549, 18], [62849, 18], [63150, 17], [63450, 17], [63751, 15], [64051, 14], [64352, 12]], "point": [158, 203]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.31|+00.34|+01.89"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [59, 123, 183, 149], "mask": [[36659, 125], [36960, 124], [37260, 124], [37560, 124], [37861, 123], [38161, 123], [38461, 122], [38761, 122], [39062, 121], [39362, 121], [39662, 121], [39963, 120], [40263, 120], [40563, 120], [40864, 118], [41164, 118], [41464, 118], [41765, 117], [42065, 117], [42365, 117], [42666, 116], [42966, 116], [43266, 115], [43567, 114], [43867, 114], [44167, 114], [44468, 113]], "point": [121, 135]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-01.04|+00.12|+03.70", "placeStationary": true, "receptacleObjectId": "Drawer|-01.31|+00.34|+01.89"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [41, 123, 190, 196], "mask": [[36664, 116], [36964, 116], [37263, 117], [37563, 118], [37863, 118], [38162, 119], [38462, 119], [38761, 120], [39061, 120], [39361, 120], [39660, 121], [39960, 122], [40260, 122], [40559, 123], [40859, 123], [41158, 124], [41458, 124], [41758, 124], [42057, 126], [42357, 126], [42657, 126], [42956, 127], [43256, 127], [43556, 127], [43855, 128], [44155, 128], [44454, 130], [44754, 130], [45054, 130], [45353, 131], [45653, 131], [45953, 131], [46252, 132], [46552, 132], [46851, 134], [47151, 134], [47451, 134], [47750, 21], [47775, 110], [48050, 20], [48076, 109], [48350, 20], [48376, 109], [48649, 22], [48677, 108], [48949, 22], [48977, 109], [49248, 23], [49277, 109], [49548, 23], [49577, 109], [49848, 24], [49878, 108], [50147, 25], [50178, 108], [50442, 148], [50741, 150], [51041, 150], [51342, 149], [51642, 149], [51943, 147], [52243, 147], [52543, 147], [52844, 146], [53144, 146], [53445, 145], [53745, 144], [54046, 143], [54346, 143], [54647, 142], [54947, 142], [55248, 140], [55548, 140], [55848, 140], [56149, 139], [56449, 139], [56750, 138], [57050, 137], [57351, 136], [57651, 136], [57952, 135], [58252, 135], [58553, 133]], "point": [115, 158]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.31|+00.34|+01.89"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [41, 123, 190, 196], "mask": [[36664, 116], [36964, 116], [37263, 117], [37563, 118], [37863, 118], [38162, 119], [38462, 119], [38761, 120], [39061, 120], [39361, 120], [39660, 121], [39960, 122], [40260, 122], [40559, 123], [40859, 123], [41158, 124], [41458, 124], [41758, 124], [42057, 126], [42357, 126], [42657, 126], [42956, 127], [43256, 18], [43281, 102], [43556, 16], [43583, 100], [43855, 16], [43884, 99], [44155, 15], [44184, 99], [44454, 15], [44485, 99], [44754, 15], [44785, 99], [45054, 14], [45085, 99], [45353, 16], [45385, 99], [45653, 16], [45686, 98], [45953, 16], [45986, 98], [46252, 17], [46286, 98], [46552, 18], [46586, 98], [46851, 19], [46887, 98], [47151, 19], [47187, 98], [47451, 20], [47487, 98], [47750, 21], [47787, 98], [48050, 20], [48088, 97], [48350, 20], [48387, 98], [48649, 22], [48687, 98], [48949, 22], [48987, 99], [49248, 23], [49285, 101], [49548, 23], [49584, 102], [49848, 24], [49883, 103], [50147, 25], [50178, 108], [50442, 148], [50741, 150], [51041, 150], [51342, 149], [51642, 149], [51943, 147], [52243, 147], [52543, 147], [52844, 146], [53144, 146], [53445, 145], [53745, 144], [54046, 143], [54346, 143], [54647, 142], [54947, 142], [55248, 140], [55548, 140], [55848, 140], [56149, 139], [56449, 139], [56750, 138], [57050, 137], [57351, 136], [57651, 136], [57952, 135], [58252, 135], [58553, 133]], "point": [115, 158]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan410", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -4.0, "y": 0.900394559, "z": 4.25}, "object_poses": [{"objectName": "Cloth_b248458c", "position": {"x": -1.306401, "y": 0.542599261, "z": 1.88399}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_b248458c", "position": {"x": -1.01799989, "y": 1.0493046, "z": 4.460442}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SprayBottle_185a3fef", "position": {"x": -1.08675945, "y": 0.121599719, "z": 3.60881066}, "rotation": {"x": 0.0, "y": -0.000160509444, "z": 0.0}}, {"objectName": "SprayBottle_185a3fef", "position": {"x": -1.5554347, "y": 0.8274041, "z": 2.148717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Towel_a7a469d6", "position": {"x": -4.7391, "y": 1.52200007, "z": 2.425}, "rotation": {"x": 0.0, "y": 90.00005, "z": 0.0}}, {"objectName": "ToiletPaper_9fe07971", "position": {"x": -0.968899965, "y": 0.888, "z": 3.9605}, "rotation": {"x": 0.0, "y": 270.0, "z": 270.0}}, {"objectName": "ToiletPaper_9fe07971", "position": {"x": -1.92269456, "y": 0.823, "z": 1.75698686}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_fe7b7393", "position": {"x": -1.40108585, "y": 0.54674536, "z": 1.78052616}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_3ac7a1fe", "position": {"x": -3.726, "y": 1.646, "z": 1.33030009}, "rotation": {"x": 0.0, "y": 180.000015, "z": 0.0}}, {"objectName": "Plunger_2aa32165", "position": {"x": -1.06452632, "y": -0.0006047785, "z": 4.838613}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBar_20ca2d0e", "position": {"x": -2.28228426, "y": 0.8260697, "z": 1.96891129}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_9fe07971", "position": {"x": -1.04324043, "y": 0.115755476, "z": 3.70332336}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cloth_b248458c", "position": {"x": -1.58807158, "y": 0.887613654, "z": 1.82760775}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_5eb36cc2", "position": {"x": -2.19238687, "y": 0.823, "z": 1.54506242}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Towel_a7a469d6", "position": {"x": -4.7391, "y": 1.52200007, "z": 3.359}, "rotation": {"x": 0.0, "y": 90.00005, "z": 0.0}}, {"objectName": "ScrubBrush_57fbbbc9", "position": {"x": -1.06544423, "y": -0.0006047785, "z": 4.05289841}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_e9cac663", "position": {"x": -2.10248947, "y": 0.8283692, "z": 1.43910027}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_185a3fef", "position": {"x": -2.855518, "y": 0.8274041, "z": 2.13265753}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2260086527, "scene_num": 410}, "task_id": "trial_T20190908_111346_211082", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3SFPSMFRSRTB3_33F859I56949ZFNGG1ZEAMCRLBUHBX", "high_descs": ["Go to toilet, turn right.  Proceed to counter.", "Pick up candle at back of counter, close to the wall. ", "Turn left, proceed to sink on your right.  Turn right to face sink.", "Open second drawer down, closest to wall.  Put candle in drawer in the left lower corner.  Shut drawer.", "Turn around, proceed to garbage can on right.  Turn right to face garbage can.", "Pick up toilet paper out of garbage can.", "Turn right, go to drawers.  Open bottom left side drawer.", "Put toilet paper in drawer, directly above candle, close drawer."], "task_desc": "Put candle and toilet paper in left side of a drawer.", "votes": [1, 1, 1]}, {"assignment_id": "A1CY7IOJ9YH136_369J354OFG1PT7BA040OE6EYSBS6GQ", "high_descs": ["turn right, go to counter in between the two sinks", "pick up orange object from counter next to blue bottle", "go to sink to the left", "open middle drawer under left sink, place orange object in left side of middle drawer, close drawer", "turn around, go to black bin next to toilet", "pick up toilet paper from black bin", "turn around, go to middle drawer under left sink", "open middle drawer under left sink, place toilet paper behind orange object in middle drawer, close drawer"], "task_desc": "Place orange object and toilet paper in middle drawer below sink", "votes": [0, 1, 1]}, {"assignment_id": "A11LSO6D7BMY99_39ASUFLU60Y4S8ZFVWJQEG66CY4EX1", "high_descs": ["turn to your right, go to the double sinks", "pick up the brown cup", "open the door that is to your left under the sink closest to the wall", "put the cup into the drawer", "turn around, go to the black trash can that is next to the toilet", "take the toilet paper out of the trash can", "go back to the drawer with the cup; open the drawer", "put the toilet paper in the drawer next to the cup; shut the drawer"], "task_desc": "put a cup ad toilet paper in a drawer", "votes": [1, 0, 1]}]}}