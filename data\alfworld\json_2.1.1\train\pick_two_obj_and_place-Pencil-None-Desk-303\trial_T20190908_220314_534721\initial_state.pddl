
(define (problem plan_trial_T20190908_220314_534721)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__plus_00_dot_77_bar__plus_00_dot_73_bar__minus_02_dot_76 - object
        BaseballBat_bar__minus_01_dot_63_bar__plus_00_dot_65_bar__plus_00_dot_42 - object
        Blinds_bar__plus_00_dot_45_bar__plus_02_dot_16_bar__minus_02_dot_85 - object
        Book_bar__minus_00_dot_79_bar__plus_00_dot_42_bar__minus_01_dot_61 - object
        Book_bar__minus_01_dot_02_bar__plus_00_dot_42_bar__minus_02_dot_10 - object
        Box_bar__plus_02_dot_00_bar__plus_00_dot_32_bar__minus_01_dot_04 - object
        CD_bar__plus_00_dot_74_bar__plus_00_dot_72_bar__minus_02_dot_55 - object
        CD_bar__plus_01_dot_45_bar__plus_00_dot_05_bar__minus_02_dot_30 - object
        CellPhone_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_65 - object
        CellPhone_bar__plus_00_dot_54_bar__plus_00_dot_41_bar__minus_02_dot_55 - object
        Chair_bar__minus_01_dot_56_bar__plus_00_dot_00_bar__minus_00_dot_31 - object
        Cloth_bar__plus_02_dot_18_bar__plus_00_dot_00_bar__minus_00_dot_57 - object
        CreditCard_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_47 - object
        CreditCard_bar__plus_01_dot_92_bar__plus_01_dot_58_bar__minus_02_dot_30 - object
        CreditCard_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_19 - object
        DeskLamp_bar__minus_01_dot_85_bar__plus_00_dot_59_bar__minus_00_dot_79 - object
        KeyChain_bar__minus_01_dot_52_bar__plus_00_dot_60_bar__minus_00_dot_82 - object
        Laptop_bar__minus_01_dot_76_bar__plus_00_dot_59_bar__minus_00_dot_11 - object
        LightSwitch_bar__minus_00_dot_64_bar__plus_01_dot_15_bar__plus_00_dot_50 - object
        Mirror_bar__minus_01_dot_07_bar__plus_01_dot_47_bar__plus_00_dot_50 - object
        Mug_bar__plus_01_dot_32_bar__plus_00_dot_35_bar__minus_02_dot_32 - object
        Pencil_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_76 - object
        Pencil_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_02 - object
        Pen_bar__minus_01_dot_78_bar__plus_00_dot_60_bar__minus_00_dot_53 - object
        Pen_bar__minus_01_dot_85_bar__plus_00_dot_60_bar__minus_00_dot_39 - object
        Pillow_bar__minus_01_dot_55_bar__plus_00_dot_46_bar__minus_01_dot_83 - object
        Poster_bar__plus_00_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64 - object
        TennisRacket_bar__plus_02_dot_24_bar__plus_00_dot_28_bar__minus_01_dot_90 - object
        Vase_bar__plus_00_dot_54_bar__plus_00_dot_73_bar__minus_02_dot_71 - object
        Vase_bar__minus_01_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_39 - object
        Window_bar__plus_00_dot_44_bar__plus_01_dot_40_bar__minus_02_dot_86 - object
        Bed_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_01_dot_85 - receptacle
        Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37 - receptacle
        Drawer_bar__plus_00_dot_45_bar__plus_00_dot_20_bar__minus_02_dot_43 - receptacle
        Drawer_bar__plus_00_dot_45_bar__plus_00_dot_50_bar__minus_02_dot_43 - receptacle
        Drawer_bar__minus_01_dot_79_bar__plus_00_dot_10_bar__minus_01_dot_15 - receptacle
        Drawer_bar__minus_01_dot_79_bar__plus_00_dot_25_bar__minus_01_dot_15 - receptacle
        Drawer_bar__minus_01_dot_79_bar__plus_00_dot_39_bar__minus_01_dot_15 - receptacle
        Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65 - receptacle
        GarbageCan_bar__plus_02_dot_22_bar__plus_00_dot_00_bar__plus_00_dot_31 - receptacle
        Shelf_bar__plus_01_dot_31_bar__plus_00_dot_05_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_31_bar__plus_00_dot_35_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_31_bar__plus_00_dot_97_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_31_bar__plus_01_dot_17_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_31_bar__plus_01_dot_37_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_31_bar__plus_01_dot_57_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_84_bar__plus_00_dot_05_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_84_bar__plus_00_dot_35_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_84_bar__plus_00_dot_97_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_84_bar__plus_01_dot_17_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_84_bar__plus_01_dot_37_bar__minus_02_dot_30 - receptacle
        Shelf_bar__plus_01_dot_84_bar__plus_01_dot_57_bar__minus_02_dot_30 - receptacle
        SideTable_bar__minus_01_dot_82_bar__plus_00_dot_00_bar__minus_01_dot_14 - receptacle
        loc_bar_7_bar__minus_7_bar_2_bar_45 - location
        loc_bar__minus_4_bar__minus_1_bar_3_bar_60 - location
        loc_bar_2_bar__minus_8_bar_2_bar_60 - location
        loc_bar_3_bar__minus_5_bar_2_bar_60 - location
        loc_bar__minus_4_bar__minus_3_bar_3_bar_60 - location
        loc_bar__minus_4_bar__minus_2_bar_2_bar_45 - location
        loc_bar_2_bar__minus_8_bar_2_bar__minus_30 - location
        loc_bar_7_bar_0_bar_1_bar_60 - location
        loc_bar__minus_2_bar__minus_2_bar_3_bar_45 - location
        loc_bar_5_bar__minus_7_bar_2_bar_15 - location
        loc_bar__minus_4_bar_0_bar_0_bar_15 - location
        loc_bar_7_bar__minus_7_bar_2_bar_15 - location
        loc_bar__minus_4_bar_0_bar_3_bar_60 - location
        loc_bar_5_bar__minus_5_bar_2_bar_60 - location
        loc_bar__minus_3_bar__minus_3_bar_3_bar_60 - location
        loc_bar_6_bar__minus_6_bar_2_bar_60 - location
        loc_bar_7_bar__minus_6_bar_2_bar_60 - location
        loc_bar_1_bar__minus_8_bar_2_bar_15 - location
        loc_bar_7_bar__minus_7_bar_2_bar_30 - location
        loc_bar_6_bar__minus_4_bar_1_bar_60 - location
        loc_bar__minus_3_bar__minus_2_bar_3_bar_60 - location
        loc_bar_5_bar__minus_7_bar_2_bar_45 - location
        loc_bar_7_bar__minus_2_bar_1_bar_60 - location
        loc_bar_7_bar__minus_7_bar_1_bar_60 - location
        loc_bar_5_bar__minus_7_bar_2_bar_30 - location
        loc_bar__minus_3_bar_0_bar_0_bar_45 - location
        loc_bar__minus_3_bar__minus_3_bar_3_bar_45 - location
        loc_bar_2_bar__minus_8_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType Shelf_bar__plus_01_dot_84_bar__plus_01_dot_57_bar__minus_02_dot_30 ShelfType)
        (receptacleType Drawer_bar__minus_01_dot_79_bar__plus_00_dot_25_bar__minus_01_dot_15 DrawerType)
        (receptacleType Shelf_bar__plus_01_dot_84_bar__plus_01_dot_17_bar__minus_02_dot_30 ShelfType)
        (receptacleType SideTable_bar__minus_01_dot_82_bar__plus_00_dot_00_bar__minus_01_dot_14 SideTableType)
        (receptacleType Shelf_bar__plus_01_dot_31_bar__plus_00_dot_35_bar__minus_02_dot_30 ShelfType)
        (receptacleType Drawer_bar__plus_00_dot_45_bar__plus_00_dot_50_bar__minus_02_dot_43 DrawerType)
        (receptacleType Shelf_bar__plus_01_dot_31_bar__plus_01_dot_37_bar__minus_02_dot_30 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_84_bar__plus_01_dot_37_bar__minus_02_dot_30 ShelfType)
        (receptacleType Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65 DresserType)
        (receptacleType Shelf_bar__plus_01_dot_84_bar__plus_00_dot_05_bar__minus_02_dot_30 ShelfType)
        (receptacleType Drawer_bar__plus_00_dot_45_bar__plus_00_dot_20_bar__minus_02_dot_43 DrawerType)
        (receptacleType Drawer_bar__minus_01_dot_79_bar__plus_00_dot_39_bar__minus_01_dot_15 DrawerType)
        (receptacleType Shelf_bar__plus_01_dot_31_bar__plus_01_dot_17_bar__minus_02_dot_30 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_31_bar__plus_00_dot_97_bar__minus_02_dot_30 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_84_bar__plus_00_dot_97_bar__minus_02_dot_30 ShelfType)
        (receptacleType GarbageCan_bar__plus_02_dot_22_bar__plus_00_dot_00_bar__plus_00_dot_31 GarbageCanType)
        (receptacleType Bed_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_01_dot_85 BedType)
        (receptacleType Shelf_bar__plus_01_dot_31_bar__plus_01_dot_57_bar__minus_02_dot_30 ShelfType)
        (receptacleType Drawer_bar__minus_01_dot_79_bar__plus_00_dot_10_bar__minus_01_dot_15 DrawerType)
        (receptacleType Shelf_bar__plus_01_dot_31_bar__plus_00_dot_05_bar__minus_02_dot_30 ShelfType)
        (receptacleType Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37 DeskType)
        (receptacleType Shelf_bar__plus_01_dot_84_bar__plus_00_dot_35_bar__minus_02_dot_30 ShelfType)
        (objectType Window_bar__plus_00_dot_44_bar__plus_01_dot_40_bar__minus_02_dot_86 WindowType)
        (objectType AlarmClock_bar__plus_00_dot_77_bar__plus_00_dot_73_bar__minus_02_dot_76 AlarmClockType)
        (objectType CD_bar__plus_00_dot_74_bar__plus_00_dot_72_bar__minus_02_dot_55 CDType)
        (objectType Pen_bar__minus_01_dot_78_bar__plus_00_dot_60_bar__minus_00_dot_53 PenType)
        (objectType KeyChain_bar__minus_01_dot_52_bar__plus_00_dot_60_bar__minus_00_dot_82 KeyChainType)
        (objectType LightSwitch_bar__minus_00_dot_64_bar__plus_01_dot_15_bar__plus_00_dot_50 LightSwitchType)
        (objectType CreditCard_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_47 CreditCardType)
        (objectType Laptop_bar__minus_01_dot_76_bar__plus_00_dot_59_bar__minus_00_dot_11 LaptopType)
        (objectType Vase_bar__minus_01_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_39 VaseType)
        (objectType Pencil_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_76 PencilType)
        (objectType DeskLamp_bar__minus_01_dot_85_bar__plus_00_dot_59_bar__minus_00_dot_79 DeskLampType)
        (objectType TennisRacket_bar__plus_02_dot_24_bar__plus_00_dot_28_bar__minus_01_dot_90 TennisRacketType)
        (objectType Chair_bar__minus_01_dot_56_bar__plus_00_dot_00_bar__minus_00_dot_31 ChairType)
        (objectType CreditCard_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_19 CreditCardType)
        (objectType Box_bar__plus_02_dot_00_bar__plus_00_dot_32_bar__minus_01_dot_04 BoxType)
        (objectType Pencil_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_02 PencilType)
        (objectType BaseballBat_bar__minus_01_dot_63_bar__plus_00_dot_65_bar__plus_00_dot_42 BaseballBatType)
        (objectType CreditCard_bar__plus_01_dot_92_bar__plus_01_dot_58_bar__minus_02_dot_30 CreditCardType)
        (objectType Pillow_bar__minus_01_dot_55_bar__plus_00_dot_46_bar__minus_01_dot_83 PillowType)
        (objectType Book_bar__minus_01_dot_02_bar__plus_00_dot_42_bar__minus_02_dot_10 BookType)
        (objectType Blinds_bar__plus_00_dot_45_bar__plus_02_dot_16_bar__minus_02_dot_85 BlindsType)
        (objectType Vase_bar__plus_00_dot_54_bar__plus_00_dot_73_bar__minus_02_dot_71 VaseType)
        (objectType Pen_bar__minus_01_dot_85_bar__plus_00_dot_60_bar__minus_00_dot_39 PenType)
        (objectType Poster_bar__plus_00_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64 PosterType)
        (objectType CellPhone_bar__plus_00_dot_54_bar__plus_00_dot_41_bar__minus_02_dot_55 CellPhoneType)
        (objectType CD_bar__plus_01_dot_45_bar__plus_00_dot_05_bar__minus_02_dot_30 CDType)
        (objectType Book_bar__minus_00_dot_79_bar__plus_00_dot_42_bar__minus_01_dot_61 BookType)
        (objectType Mug_bar__plus_01_dot_32_bar__plus_00_dot_35_bar__minus_02_dot_32 MugType)
        (objectType Cloth_bar__plus_02_dot_18_bar__plus_00_dot_00_bar__minus_00_dot_57 ClothType)
        (objectType Mirror_bar__minus_01_dot_07_bar__plus_01_dot_47_bar__plus_00_dot_50 MirrorType)
        (objectType CellPhone_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_65 CellPhoneType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType VaseType)
        (canContain SideTableType CDType)
        (canContain SideTableType MugType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType BaseballBatType)
        (canContain SideTableType TennisRacketType)
        (canContain SideTableType ClothType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DresserType PenType)
        (canContain DresserType BookType)
        (canContain DresserType VaseType)
        (canContain DresserType CDType)
        (canContain DresserType MugType)
        (canContain DresserType BoxType)
        (canContain DresserType CellPhoneType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType TennisRacketType)
        (canContain DresserType ClothType)
        (canContain DresserType LaptopType)
        (canContain DresserType PencilType)
        (canContain DresserType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType ClothType)
        (canContain GarbageCanType PencilType)
        (canContain BedType BaseballBatType)
        (canContain BedType TennisRacketType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DeskType PenType)
        (canContain DeskType BookType)
        (canContain DeskType VaseType)
        (canContain DeskType CDType)
        (canContain DeskType MugType)
        (canContain DeskType BoxType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType CreditCardType)
        (canContain DeskType TennisRacketType)
        (canContain DeskType ClothType)
        (canContain DeskType LaptopType)
        (canContain DeskType PencilType)
        (canContain DeskType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType VaseType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (pickupable AlarmClock_bar__plus_00_dot_77_bar__plus_00_dot_73_bar__minus_02_dot_76)
        (pickupable CD_bar__plus_00_dot_74_bar__plus_00_dot_72_bar__minus_02_dot_55)
        (pickupable Pen_bar__minus_01_dot_78_bar__plus_00_dot_60_bar__minus_00_dot_53)
        (pickupable KeyChain_bar__minus_01_dot_52_bar__plus_00_dot_60_bar__minus_00_dot_82)
        (pickupable CreditCard_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_47)
        (pickupable Laptop_bar__minus_01_dot_76_bar__plus_00_dot_59_bar__minus_00_dot_11)
        (pickupable Vase_bar__minus_01_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_39)
        (pickupable Pencil_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_76)
        (pickupable TennisRacket_bar__plus_02_dot_24_bar__plus_00_dot_28_bar__minus_01_dot_90)
        (pickupable CreditCard_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_19)
        (pickupable Box_bar__plus_02_dot_00_bar__plus_00_dot_32_bar__minus_01_dot_04)
        (pickupable Pencil_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_02)
        (pickupable BaseballBat_bar__minus_01_dot_63_bar__plus_00_dot_65_bar__plus_00_dot_42)
        (pickupable CreditCard_bar__plus_01_dot_92_bar__plus_01_dot_58_bar__minus_02_dot_30)
        (pickupable Pillow_bar__minus_01_dot_55_bar__plus_00_dot_46_bar__minus_01_dot_83)
        (pickupable Book_bar__minus_01_dot_02_bar__plus_00_dot_42_bar__minus_02_dot_10)
        (pickupable Vase_bar__plus_00_dot_54_bar__plus_00_dot_73_bar__minus_02_dot_71)
        (pickupable Pen_bar__minus_01_dot_85_bar__plus_00_dot_60_bar__minus_00_dot_39)
        (pickupable CellPhone_bar__plus_00_dot_54_bar__plus_00_dot_41_bar__minus_02_dot_55)
        (pickupable CD_bar__plus_01_dot_45_bar__plus_00_dot_05_bar__minus_02_dot_30)
        (pickupable Book_bar__minus_00_dot_79_bar__plus_00_dot_42_bar__minus_01_dot_61)
        (pickupable Mug_bar__plus_01_dot_32_bar__plus_00_dot_35_bar__minus_02_dot_32)
        (pickupable Cloth_bar__plus_02_dot_18_bar__plus_00_dot_00_bar__minus_00_dot_57)
        (pickupable CellPhone_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_65)
        (isReceptacleObject Box_bar__plus_02_dot_00_bar__plus_00_dot_32_bar__minus_01_dot_04)
        (isReceptacleObject Mug_bar__plus_01_dot_32_bar__plus_00_dot_35_bar__minus_02_dot_32)
        (openable Drawer_bar__minus_01_dot_79_bar__plus_00_dot_25_bar__minus_01_dot_15)
        (openable Drawer_bar__minus_01_dot_79_bar__plus_00_dot_39_bar__minus_01_dot_15)
        (openable Drawer_bar__minus_01_dot_79_bar__plus_00_dot_10_bar__minus_01_dot_15)
        
        (atLocation agent1 loc_bar_2_bar__minus_8_bar_2_bar_30)
        
        (cleanable Mug_bar__plus_01_dot_32_bar__plus_00_dot_35_bar__minus_02_dot_32)
        (cleanable Cloth_bar__plus_02_dot_18_bar__plus_00_dot_00_bar__minus_00_dot_57)
        
        (heatable Mug_bar__plus_01_dot_32_bar__plus_00_dot_35_bar__minus_02_dot_32)
        (coolable Mug_bar__plus_01_dot_32_bar__plus_00_dot_35_bar__minus_02_dot_32)
        
        
        (toggleable DeskLamp_bar__minus_01_dot_85_bar__plus_00_dot_59_bar__minus_00_dot_79)
        
        
        
        
        (inReceptacle CellPhone_bar__plus_00_dot_54_bar__plus_00_dot_41_bar__minus_02_dot_55 Drawer_bar__plus_00_dot_45_bar__plus_00_dot_50_bar__minus_02_dot_43)
        (inReceptacle Window_bar__plus_00_dot_44_bar__plus_01_dot_40_bar__minus_02_dot_86 Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65)
        (inReceptacle Vase_bar__plus_00_dot_54_bar__plus_00_dot_73_bar__minus_02_dot_71 Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65)
        (inReceptacle AlarmClock_bar__plus_00_dot_77_bar__plus_00_dot_73_bar__minus_02_dot_76 Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65)
        (inReceptacle CD_bar__plus_00_dot_74_bar__plus_00_dot_72_bar__minus_02_dot_55 Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65)
        (inReceptacle Pencil_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_76 Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65)
        (inReceptacle CellPhone_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_65 Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65)
        (inReceptacle CreditCard_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_47 Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65)
        (inReceptacle Laptop_bar__minus_01_dot_76_bar__plus_00_dot_59_bar__minus_00_dot_11 Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37)
        (inReceptacle Vase_bar__minus_01_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_39 Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37)
        (inReceptacle Pen_bar__minus_01_dot_78_bar__plus_00_dot_60_bar__minus_00_dot_53 Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37)
        (inReceptacle Pen_bar__minus_01_dot_85_bar__plus_00_dot_60_bar__minus_00_dot_39 Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37)
        (inReceptacle KeyChain_bar__minus_01_dot_52_bar__plus_00_dot_60_bar__minus_00_dot_82 Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37)
        (inReceptacle DeskLamp_bar__minus_01_dot_85_bar__plus_00_dot_59_bar__minus_00_dot_79 Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37)
        (inReceptacle CreditCard_bar__plus_01_dot_92_bar__plus_01_dot_58_bar__minus_02_dot_30 Shelf_bar__plus_01_dot_84_bar__plus_01_dot_57_bar__minus_02_dot_30)
        (inReceptacle Pencil_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_02 SideTable_bar__minus_01_dot_82_bar__plus_00_dot_00_bar__minus_01_dot_14)
        (inReceptacle CreditCard_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_19 SideTable_bar__minus_01_dot_82_bar__plus_00_dot_00_bar__minus_01_dot_14)
        (inReceptacle CD_bar__plus_01_dot_45_bar__plus_00_dot_05_bar__minus_02_dot_30 Shelf_bar__plus_01_dot_31_bar__plus_00_dot_05_bar__minus_02_dot_30)
        (inReceptacle Mug_bar__plus_01_dot_32_bar__plus_00_dot_35_bar__minus_02_dot_32 Shelf_bar__plus_01_dot_31_bar__plus_00_dot_35_bar__minus_02_dot_30)
        (inReceptacle Pillow_bar__minus_01_dot_55_bar__plus_00_dot_46_bar__minus_01_dot_83 Bed_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_01_dot_85)
        (inReceptacle Book_bar__minus_01_dot_02_bar__plus_00_dot_42_bar__minus_02_dot_10 Bed_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_01_dot_85)
        (inReceptacle Book_bar__minus_00_dot_79_bar__plus_00_dot_42_bar__minus_01_dot_61 Bed_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_01_dot_85)
        
        
        (receptacleAtLocation Bed_bar__minus_01_dot_05_bar__plus_00_dot_00_bar__minus_01_dot_85 loc_bar__minus_4_bar__minus_2_bar_2_bar_45)
        (receptacleAtLocation Desk_bar__minus_01_dot_71_bar__plus_00_dot_00_bar__minus_00_dot_37 loc_bar__minus_3_bar__minus_2_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_45_bar__plus_00_dot_20_bar__minus_02_dot_43 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_45_bar__plus_00_dot_50_bar__minus_02_dot_43 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_79_bar__plus_00_dot_10_bar__minus_01_dot_15 loc_bar__minus_2_bar__minus_2_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_79_bar__plus_00_dot_25_bar__minus_01_dot_15 loc_bar__minus_3_bar__minus_3_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_79_bar__plus_00_dot_39_bar__minus_01_dot_15 loc_bar__minus_3_bar__minus_3_bar_3_bar_45)
        (receptacleAtLocation Dresser_bar__plus_00_dot_44_bar__plus_00_dot_04_bar__minus_02_dot_65 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_02_dot_22_bar__plus_00_dot_00_bar__plus_00_dot_31 loc_bar_7_bar_0_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_31_bar__plus_00_dot_05_bar__minus_02_dot_30 loc_bar_3_bar__minus_5_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_31_bar__plus_00_dot_35_bar__minus_02_dot_30 loc_bar_7_bar__minus_6_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_31_bar__plus_00_dot_97_bar__minus_02_dot_30 loc_bar_5_bar__minus_7_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_31_bar__plus_01_dot_17_bar__minus_02_dot_30 loc_bar_5_bar__minus_7_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_31_bar__plus_01_dot_37_bar__minus_02_dot_30 loc_bar_5_bar__minus_7_bar_2_bar_30)
        (receptacleAtLocation Shelf_bar__plus_01_dot_31_bar__plus_01_dot_57_bar__minus_02_dot_30 loc_bar_5_bar__minus_7_bar_2_bar_15)
        (receptacleAtLocation Shelf_bar__plus_01_dot_84_bar__plus_00_dot_05_bar__minus_02_dot_30 loc_bar_5_bar__minus_5_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_84_bar__plus_00_dot_35_bar__minus_02_dot_30 loc_bar_6_bar__minus_6_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_84_bar__plus_00_dot_97_bar__minus_02_dot_30 loc_bar_7_bar__minus_7_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_84_bar__plus_01_dot_17_bar__minus_02_dot_30 loc_bar_7_bar__minus_7_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_84_bar__plus_01_dot_37_bar__minus_02_dot_30 loc_bar_7_bar__minus_7_bar_2_bar_30)
        (receptacleAtLocation Shelf_bar__plus_01_dot_84_bar__plus_01_dot_57_bar__minus_02_dot_30 loc_bar_7_bar__minus_7_bar_2_bar_15)
        (receptacleAtLocation SideTable_bar__minus_01_dot_82_bar__plus_00_dot_00_bar__minus_01_dot_14 loc_bar__minus_4_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Pencil_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_02 loc_bar__minus_4_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Pen_bar__minus_01_dot_85_bar__plus_00_dot_60_bar__minus_00_dot_39 loc_bar__minus_3_bar__minus_2_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_79_bar__plus_00_dot_51_bar__minus_01_dot_19 loc_bar__minus_4_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Vase_bar__plus_00_dot_54_bar__plus_00_dot_73_bar__minus_02_dot_71 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation CellPhone_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_65 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Book_bar__minus_00_dot_79_bar__plus_00_dot_42_bar__minus_01_dot_61 loc_bar__minus_4_bar__minus_2_bar_2_bar_45)
        (objectAtLocation CreditCard_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_47 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Box_bar__plus_02_dot_00_bar__plus_00_dot_32_bar__minus_01_dot_04 loc_bar_6_bar__minus_4_bar_1_bar_60)
        (objectAtLocation Chair_bar__minus_01_dot_56_bar__plus_00_dot_00_bar__minus_00_dot_31 loc_bar__minus_4_bar__minus_1_bar_3_bar_60)
        (objectAtLocation Book_bar__minus_01_dot_02_bar__plus_00_dot_42_bar__minus_02_dot_10 loc_bar__minus_4_bar__minus_2_bar_2_bar_45)
        (objectAtLocation Pillow_bar__minus_01_dot_55_bar__plus_00_dot_46_bar__minus_01_dot_83 loc_bar__minus_4_bar__minus_2_bar_2_bar_45)
        (objectAtLocation CellPhone_bar__plus_00_dot_54_bar__plus_00_dot_41_bar__minus_02_dot_55 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Vase_bar__minus_01_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_39 loc_bar__minus_3_bar__minus_2_bar_3_bar_60)
        (objectAtLocation DeskLamp_bar__minus_01_dot_85_bar__plus_00_dot_59_bar__minus_00_dot_79 loc_bar__minus_3_bar__minus_2_bar_3_bar_60)
        (objectAtLocation BaseballBat_bar__minus_01_dot_63_bar__plus_00_dot_65_bar__plus_00_dot_42 loc_bar__minus_4_bar_0_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__plus_01_dot_92_bar__plus_01_dot_58_bar__minus_02_dot_30 loc_bar_7_bar__minus_7_bar_2_bar_15)
        (objectAtLocation TennisRacket_bar__plus_02_dot_24_bar__plus_00_dot_28_bar__minus_01_dot_90 loc_bar_7_bar__minus_7_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__minus_01_dot_52_bar__plus_00_dot_60_bar__minus_00_dot_82 loc_bar__minus_3_bar__minus_2_bar_3_bar_60)
        (objectAtLocation LightSwitch_bar__minus_00_dot_64_bar__plus_01_dot_15_bar__plus_00_dot_50 loc_bar__minus_3_bar_0_bar_0_bar_45)
        (objectAtLocation AlarmClock_bar__plus_00_dot_77_bar__plus_00_dot_73_bar__minus_02_dot_76 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Poster_bar__plus_00_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64 loc_bar_1_bar__minus_8_bar_2_bar_15)
        (objectAtLocation Cloth_bar__plus_02_dot_18_bar__plus_00_dot_00_bar__minus_00_dot_57 loc_bar_7_bar__minus_2_bar_1_bar_60)
        (objectAtLocation Laptop_bar__minus_01_dot_76_bar__plus_00_dot_59_bar__minus_00_dot_11 loc_bar__minus_3_bar__minus_2_bar_3_bar_60)
        (objectAtLocation Mirror_bar__minus_01_dot_07_bar__plus_01_dot_47_bar__plus_00_dot_50 loc_bar__minus_4_bar_0_bar_0_bar_15)
        (objectAtLocation CD_bar__plus_00_dot_74_bar__plus_00_dot_72_bar__minus_02_dot_55 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation CD_bar__plus_01_dot_45_bar__plus_00_dot_05_bar__minus_02_dot_30 loc_bar_3_bar__minus_5_bar_2_bar_60)
        (objectAtLocation Pen_bar__minus_01_dot_78_bar__plus_00_dot_60_bar__minus_00_dot_53 loc_bar__minus_3_bar__minus_2_bar_3_bar_60)
        (objectAtLocation Window_bar__plus_00_dot_44_bar__plus_01_dot_40_bar__minus_02_dot_86 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Pencil_bar__plus_00_dot_19_bar__plus_00_dot_73_bar__minus_02_dot_76 loc_bar_2_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Mug_bar__plus_01_dot_32_bar__plus_00_dot_35_bar__minus_02_dot_32 loc_bar_7_bar__minus_6_bar_2_bar_60)
        (objectAtLocation Blinds_bar__plus_00_dot_45_bar__plus_02_dot_16_bar__minus_02_dot_85 loc_bar_2_bar__minus_8_bar_2_bar__minus_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PencilType)
                                    (receptacleType ?r DeskType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PencilType)
                                            (receptacleType ?r DeskType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            