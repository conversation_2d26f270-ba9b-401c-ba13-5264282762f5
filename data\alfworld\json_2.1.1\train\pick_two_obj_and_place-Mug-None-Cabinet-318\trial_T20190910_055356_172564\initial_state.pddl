
(define (problem plan_trial_T20190910_055356_172564)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__plus_02_dot_00_bar__plus_00_dot_73_bar__plus_00_dot_48 - object
        AlarmClock_bar__minus_00_dot_78_bar__plus_00_dot_68_bar__plus_01_dot_80 - object
        AlarmClock_bar__minus_00_dot_86_bar__plus_00_dot_67_bar__plus_03_dot_25 - object
        Blinds_bar__plus_01_dot_55_bar__plus_02_dot_44_bar__minus_01_dot_89 - object
        Book_bar__plus_01_dot_61_bar__plus_00_dot_73_bar__minus_00_dot_75 - object
        Book_bar__minus_00_dot_63_bar__plus_01_dot_84_bar__plus_01_dot_63 - object
        Book_bar__minus_00_dot_67_bar__plus_00_dot_07_bar__plus_02_dot_24 - object
        Box_bar__minus_00_dot_49_bar__plus_00_dot_20_bar__minus_00_dot_29 - object
        CD_bar__plus_01_dot_50_bar__plus_00_dot_43_bar__plus_01_dot_88 - object
        CellPhone_bar__plus_01_dot_67_bar__plus_00_dot_44_bar__plus_01_dot_88 - object
        CellPhone_bar__minus_00_dot_70_bar__plus_00_dot_74_bar__minus_01_dot_32 - object
        Chair_bar__plus_01_dot_48_bar__plus_00_dot_43_bar__plus_00_dot_22 - object
        Chair_bar__minus_00_dot_25_bar__plus_00_dot_43_bar__plus_01_dot_46 - object
        CreditCard_bar__plus_01_dot_59_bar__plus_00_dot_44_bar__plus_01_dot_61 - object
        CreditCard_bar__minus_00_dot_48_bar__plus_00_dot_67_bar__plus_03_dot_25 - object
        DeskLamp_bar__plus_02_dot_01_bar__plus_00_dot_72_bar__plus_00_dot_71 - object
        KeyChain_bar__minus_00_dot_63_bar__plus_00_dot_98_bar__minus_01_dot_51 - object
        Laptop_bar__plus_01_dot_61_bar__plus_00_dot_72_bar__plus_00_dot_48 - object
        Laptop_bar__minus_00_dot_63_bar__plus_01_dot_84_bar__plus_02_dot_48 - object
        Laptop_bar__minus_00_dot_86_bar__plus_00_dot_67_bar__plus_02_dot_99 - object
        LightSwitch_bar__plus_02_dot_20_bar__plus_01_dot_26_bar__plus_01_dot_99 - object
        Mirror_bar__plus_02_dot_23_bar__plus_01_dot_26_bar__plus_02_dot_66 - object
        Mug_bar__minus_00_dot_51_bar__plus_00_dot_68_bar__plus_01_dot_80 - object
        Mug_bar__minus_00_dot_78_bar__plus_00_dot_68_bar__plus_02_dot_36 - object
        Painting_bar__plus_02_dot_20_bar__plus_01_dot_77_bar__minus_00_dot_10 - object
        Pencil_bar__minus_00_dot_51_bar__plus_00_dot_68_bar__plus_02_dot_36 - object
        Pencil_bar__minus_00_dot_58_bar__plus_00_dot_98_bar__minus_01_dot_32 - object
        Pen_bar__minus_00_dot_48_bar__plus_00_dot_67_bar__plus_02_dot_82 - object
        Pillow_bar__minus_00_dot_60_bar__plus_01_dot_59_bar__minus_00_dot_94 - object
        Pillow_bar__minus_00_dot_63_bar__plus_01_dot_87_bar__plus_03_dot_05 - object
        Pillow_bar__minus_00_dot_87_bar__plus_01_dot_59_bar__minus_00_dot_50 - object
        TennisRacket_bar__minus_01_dot_35_bar__plus_00_dot_29_bar__plus_00_dot_23 - object
        Window_bar__plus_01_dot_58_bar__plus_01_dot_56_bar__minus_01_dot_95 - object
        ArmChair_bar__plus_01_dot_83_bar__plus_00_dot_01_bar__plus_01_dot_61 - receptacle
        Bed_bar__minus_00_dot_87_bar__plus_00_dot_00_bar__minus_00_dot_92 - receptacle
        Bed_bar__minus_00_dot_88_bar__plus_01_dot_65_bar__plus_02_dot_17 - receptacle
        Cabinet_bar__minus_00_dot_36_bar__plus_00_dot_26_bar__plus_02_dot_53 - receptacle
        Desk_bar__plus_01_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_12 - receptacle
        Desk_bar__minus_00_dot_65_bar__plus_00_dot_01_bar__plus_01_dot_80 - receptacle
        Drawer_bar__plus_01_dot_65_bar__plus_00_dot_15_bar__minus_00_dot_36 - receptacle
        Drawer_bar__plus_01_dot_65_bar__plus_00_dot_15_bar__minus_00_dot_78 - receptacle
        Drawer_bar__plus_01_dot_65_bar__plus_00_dot_36_bar__minus_00_dot_36 - receptacle
        Drawer_bar__plus_01_dot_65_bar__plus_00_dot_36_bar__minus_00_dot_78 - receptacle
        Drawer_bar__plus_01_dot_65_bar__plus_00_dot_57_bar__minus_00_dot_36 - receptacle
        Drawer_bar__plus_01_dot_65_bar__plus_00_dot_57_bar__minus_00_dot_78 - receptacle
        Drawer_bar__minus_00_dot_57_bar__plus_00_dot_57_bar__plus_02_dot_24 - receptacle
        Drawer_bar__minus_00_dot_58_bar__plus_00_dot_15_bar__plus_02_dot_96 - receptacle
        Drawer_bar__minus_00_dot_58_bar__plus_00_dot_37_bar__plus_02_dot_96 - receptacle
        Drawer_bar__minus_00_dot_58_bar__plus_00_dot_56_bar__plus_02_dot_96 - receptacle
        Drawer_bar__minus_00_dot_70_bar__plus_00_dot_22_bar__minus_01_dot_32 - receptacle
        Drawer_bar__minus_00_dot_70_bar__plus_00_dot_54_bar__minus_01_dot_32 - receptacle
        Drawer_bar__minus_00_dot_70_bar__plus_00_dot_83_bar__minus_01_dot_32 - receptacle
        Dresser_bar__minus_00_dot_66_bar__minus_00_dot_01_bar__plus_02_dot_97 - receptacle
        Dresser_bar__minus_00_dot_77_bar__minus_00_dot_01_bar__minus_01_dot_31 - receptacle
        GarbageCan_bar__plus_02_dot_03_bar__plus_00_dot_00_bar__minus_01_dot_19 - receptacle
        loc_bar_1_bar_7_bar_0_bar_45 - location
        loc_bar_2_bar__minus_4_bar_1_bar_60 - location
        loc_bar_1_bar__minus_5_bar_3_bar_60 - location
        loc_bar_1_bar__minus_1_bar_3_bar_60 - location
        loc_bar__minus_4_bar_2_bar_3_bar_60 - location
        loc_bar_2_bar__minus_1_bar_1_bar_45 - location
        loc_bar_6_bar__minus_5_bar_2_bar_15 - location
        loc_bar_3_bar__minus_3_bar_1_bar_45 - location
        loc_bar_1_bar_5_bar_0_bar_45 - location
        loc_bar_7_bar_10_bar_2_bar_45 - location
        loc_bar_4_bar_0_bar_1_bar_0 - location
        loc_bar_6_bar__minus_5_bar_2_bar__minus_30 - location
        loc_bar_2_bar_11_bar_3_bar_45 - location
        loc_bar_1_bar__minus_5_bar_3_bar_45 - location
        loc_bar_1_bar_6_bar_3_bar_60 - location
        loc_bar_4_bar_1_bar_1_bar_60 - location
        loc_bar_1_bar_7_bar_3_bar_60 - location
        loc_bar_7_bar_11_bar_1_bar_45 - location
        loc_bar_2_bar__minus_3_bar_1_bar_45 - location
        loc_bar_2_bar__minus_1_bar_1_bar_60 - location
        loc_bar_4_bar_9_bar_3_bar_0 - location
        loc_bar_4_bar_6_bar_1_bar_60 - location
        loc_bar_3_bar__minus_2_bar_1_bar_45 - location
        loc_bar_2_bar_7_bar_3_bar_60 - location
        loc_bar_1_bar_12_bar_3_bar_60 - location
        loc_bar_1_bar__minus_3_bar_3_bar_60 - location
        loc_bar_1_bar_8_bar_0_bar_60 - location
        loc_bar_6_bar__minus_5_bar_1_bar_60 - location
        loc_bar_1_bar_11_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType Drawer_bar__minus_00_dot_70_bar__plus_00_dot_83_bar__minus_01_dot_32 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_58_bar__plus_00_dot_37_bar__plus_02_dot_96 DrawerType)
        (receptacleType Dresser_bar__minus_00_dot_77_bar__minus_00_dot_01_bar__minus_01_dot_31 DresserType)
        (receptacleType GarbageCan_bar__plus_02_dot_03_bar__plus_00_dot_00_bar__minus_01_dot_19 GarbageCanType)
        (receptacleType Drawer_bar__plus_01_dot_65_bar__plus_00_dot_57_bar__minus_00_dot_36 DrawerType)
        (receptacleType Drawer_bar__plus_01_dot_65_bar__plus_00_dot_36_bar__minus_00_dot_78 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_57_bar__plus_00_dot_57_bar__plus_02_dot_24 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_70_bar__plus_00_dot_22_bar__minus_01_dot_32 DrawerType)
        (receptacleType Bed_bar__minus_00_dot_88_bar__plus_01_dot_65_bar__plus_02_dot_17 BedType)
        (receptacleType Dresser_bar__minus_00_dot_66_bar__minus_00_dot_01_bar__plus_02_dot_97 DresserType)
        (receptacleType Drawer_bar__plus_01_dot_65_bar__plus_00_dot_15_bar__minus_00_dot_78 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_70_bar__plus_00_dot_54_bar__minus_01_dot_32 DrawerType)
        (receptacleType Desk_bar__plus_01_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_12 DeskType)
        (receptacleType Drawer_bar__plus_01_dot_65_bar__plus_00_dot_15_bar__minus_00_dot_36 DrawerType)
        (receptacleType Drawer_bar__plus_01_dot_65_bar__plus_00_dot_57_bar__minus_00_dot_78 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_58_bar__plus_00_dot_56_bar__plus_02_dot_96 DrawerType)
        (receptacleType Cabinet_bar__minus_00_dot_36_bar__plus_00_dot_26_bar__plus_02_dot_53 CabinetType)
        (receptacleType Bed_bar__minus_00_dot_87_bar__plus_00_dot_00_bar__minus_00_dot_92 BedType)
        (receptacleType Desk_bar__minus_00_dot_65_bar__plus_00_dot_01_bar__plus_01_dot_80 DeskType)
        (receptacleType Drawer_bar__plus_01_dot_65_bar__plus_00_dot_36_bar__minus_00_dot_36 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_58_bar__plus_00_dot_15_bar__plus_02_dot_96 DrawerType)
        (receptacleType ArmChair_bar__plus_01_dot_83_bar__plus_00_dot_01_bar__plus_01_dot_61 ArmChairType)
        (objectType Pillow_bar__minus_00_dot_87_bar__plus_01_dot_59_bar__minus_00_dot_50 PillowType)
        (objectType AlarmClock_bar__minus_00_dot_86_bar__plus_00_dot_67_bar__plus_03_dot_25 AlarmClockType)
        (objectType Mug_bar__minus_00_dot_78_bar__plus_00_dot_68_bar__plus_02_dot_36 MugType)
        (objectType Pencil_bar__minus_00_dot_58_bar__plus_00_dot_98_bar__minus_01_dot_32 PencilType)
        (objectType Pillow_bar__minus_00_dot_60_bar__plus_01_dot_59_bar__minus_00_dot_94 PillowType)
        (objectType CreditCard_bar__plus_01_dot_59_bar__plus_00_dot_44_bar__plus_01_dot_61 CreditCardType)
        (objectType Chair_bar__minus_00_dot_25_bar__plus_00_dot_43_bar__plus_01_dot_46 ChairType)
        (objectType Mug_bar__minus_00_dot_51_bar__plus_00_dot_68_bar__plus_01_dot_80 MugType)
        (objectType Pen_bar__minus_00_dot_48_bar__plus_00_dot_67_bar__plus_02_dot_82 PenType)
        (objectType DeskLamp_bar__plus_02_dot_01_bar__plus_00_dot_72_bar__plus_00_dot_71 DeskLampType)
        (objectType Book_bar__minus_00_dot_63_bar__plus_01_dot_84_bar__plus_01_dot_63 BookType)
        (objectType Chair_bar__plus_01_dot_48_bar__plus_00_dot_43_bar__plus_00_dot_22 ChairType)
        (objectType Window_bar__plus_01_dot_58_bar__plus_01_dot_56_bar__minus_01_dot_95 WindowType)
        (objectType Painting_bar__plus_02_dot_20_bar__plus_01_dot_77_bar__minus_00_dot_10 PaintingType)
        (objectType AlarmClock_bar__plus_02_dot_00_bar__plus_00_dot_73_bar__plus_00_dot_48 AlarmClockType)
        (objectType CellPhone_bar__minus_00_dot_70_bar__plus_00_dot_74_bar__minus_01_dot_32 CellPhoneType)
        (objectType CreditCard_bar__minus_00_dot_48_bar__plus_00_dot_67_bar__plus_03_dot_25 CreditCardType)
        (objectType CellPhone_bar__plus_01_dot_67_bar__plus_00_dot_44_bar__plus_01_dot_88 CellPhoneType)
        (objectType Pillow_bar__minus_00_dot_63_bar__plus_01_dot_87_bar__plus_03_dot_05 PillowType)
        (objectType Laptop_bar__minus_00_dot_86_bar__plus_00_dot_67_bar__plus_02_dot_99 LaptopType)
        (objectType LightSwitch_bar__plus_02_dot_20_bar__plus_01_dot_26_bar__plus_01_dot_99 LightSwitchType)
        (objectType Mirror_bar__plus_02_dot_23_bar__plus_01_dot_26_bar__plus_02_dot_66 MirrorType)
        (objectType Laptop_bar__minus_00_dot_63_bar__plus_01_dot_84_bar__plus_02_dot_48 LaptopType)
        (objectType Blinds_bar__plus_01_dot_55_bar__plus_02_dot_44_bar__minus_01_dot_89 BlindsType)
        (objectType AlarmClock_bar__minus_00_dot_78_bar__plus_00_dot_68_bar__plus_01_dot_80 AlarmClockType)
        (objectType TennisRacket_bar__minus_01_dot_35_bar__plus_00_dot_29_bar__plus_00_dot_23 TennisRacketType)
        (objectType Box_bar__minus_00_dot_49_bar__plus_00_dot_20_bar__minus_00_dot_29 BoxType)
        (objectType Book_bar__plus_01_dot_61_bar__plus_00_dot_73_bar__minus_00_dot_75 BookType)
        (objectType KeyChain_bar__minus_00_dot_63_bar__plus_00_dot_98_bar__minus_01_dot_51 KeyChainType)
        (objectType Book_bar__minus_00_dot_67_bar__plus_00_dot_07_bar__plus_02_dot_24 BookType)
        (objectType CD_bar__plus_01_dot_50_bar__plus_00_dot_43_bar__plus_01_dot_88 CDType)
        (objectType Laptop_bar__plus_01_dot_61_bar__plus_00_dot_72_bar__plus_00_dot_48 LaptopType)
        (objectType Pencil_bar__minus_00_dot_51_bar__plus_00_dot_68_bar__plus_02_dot_36 PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DresserType PenType)
        (canContain DresserType BookType)
        (canContain DresserType CDType)
        (canContain DresserType MugType)
        (canContain DresserType BoxType)
        (canContain DresserType CellPhoneType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType TennisRacketType)
        (canContain DresserType LaptopType)
        (canContain DresserType PencilType)
        (canContain DresserType AlarmClockType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain BedType TennisRacketType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain DresserType PenType)
        (canContain DresserType BookType)
        (canContain DresserType CDType)
        (canContain DresserType MugType)
        (canContain DresserType BoxType)
        (canContain DresserType CellPhoneType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType TennisRacketType)
        (canContain DresserType LaptopType)
        (canContain DresserType PencilType)
        (canContain DresserType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DeskType PenType)
        (canContain DeskType BookType)
        (canContain DeskType CDType)
        (canContain DeskType MugType)
        (canContain DeskType BoxType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType CreditCardType)
        (canContain DeskType TennisRacketType)
        (canContain DeskType LaptopType)
        (canContain DeskType PencilType)
        (canContain DeskType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain CabinetType BookType)
        (canContain CabinetType CDType)
        (canContain CabinetType MugType)
        (canContain CabinetType BoxType)
        (canContain BedType TennisRacketType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain DeskType PenType)
        (canContain DeskType BookType)
        (canContain DeskType CDType)
        (canContain DeskType MugType)
        (canContain DeskType BoxType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType CreditCardType)
        (canContain DeskType TennisRacketType)
        (canContain DeskType LaptopType)
        (canContain DeskType PencilType)
        (canContain DeskType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType CellPhoneType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (pickupable Pillow_bar__minus_00_dot_87_bar__plus_01_dot_59_bar__minus_00_dot_50)
        (pickupable AlarmClock_bar__minus_00_dot_86_bar__plus_00_dot_67_bar__plus_03_dot_25)
        (pickupable Mug_bar__minus_00_dot_78_bar__plus_00_dot_68_bar__plus_02_dot_36)
        (pickupable Pencil_bar__minus_00_dot_58_bar__plus_00_dot_98_bar__minus_01_dot_32)
        (pickupable Pillow_bar__minus_00_dot_60_bar__plus_01_dot_59_bar__minus_00_dot_94)
        (pickupable CreditCard_bar__plus_01_dot_59_bar__plus_00_dot_44_bar__plus_01_dot_61)
        (pickupable Mug_bar__minus_00_dot_51_bar__plus_00_dot_68_bar__plus_01_dot_80)
        (pickupable Pen_bar__minus_00_dot_48_bar__plus_00_dot_67_bar__plus_02_dot_82)
        (pickupable Book_bar__minus_00_dot_63_bar__plus_01_dot_84_bar__plus_01_dot_63)
        (pickupable AlarmClock_bar__plus_02_dot_00_bar__plus_00_dot_73_bar__plus_00_dot_48)
        (pickupable CellPhone_bar__minus_00_dot_70_bar__plus_00_dot_74_bar__minus_01_dot_32)
        (pickupable CreditCard_bar__minus_00_dot_48_bar__plus_00_dot_67_bar__plus_03_dot_25)
        (pickupable CellPhone_bar__plus_01_dot_67_bar__plus_00_dot_44_bar__plus_01_dot_88)
        (pickupable Pillow_bar__minus_00_dot_63_bar__plus_01_dot_87_bar__plus_03_dot_05)
        (pickupable Laptop_bar__minus_00_dot_86_bar__plus_00_dot_67_bar__plus_02_dot_99)
        (pickupable Laptop_bar__minus_00_dot_63_bar__plus_01_dot_84_bar__plus_02_dot_48)
        (pickupable AlarmClock_bar__minus_00_dot_78_bar__plus_00_dot_68_bar__plus_01_dot_80)
        (pickupable TennisRacket_bar__minus_01_dot_35_bar__plus_00_dot_29_bar__plus_00_dot_23)
        (pickupable Box_bar__minus_00_dot_49_bar__plus_00_dot_20_bar__minus_00_dot_29)
        (pickupable Book_bar__plus_01_dot_61_bar__plus_00_dot_73_bar__minus_00_dot_75)
        (pickupable KeyChain_bar__minus_00_dot_63_bar__plus_00_dot_98_bar__minus_01_dot_51)
        (pickupable Book_bar__minus_00_dot_67_bar__plus_00_dot_07_bar__plus_02_dot_24)
        (pickupable CD_bar__plus_01_dot_50_bar__plus_00_dot_43_bar__plus_01_dot_88)
        (pickupable Laptop_bar__plus_01_dot_61_bar__plus_00_dot_72_bar__plus_00_dot_48)
        (pickupable Pencil_bar__minus_00_dot_51_bar__plus_00_dot_68_bar__plus_02_dot_36)
        (isReceptacleObject Mug_bar__minus_00_dot_78_bar__plus_00_dot_68_bar__plus_02_dot_36)
        (isReceptacleObject Mug_bar__minus_00_dot_51_bar__plus_00_dot_68_bar__plus_01_dot_80)
        (isReceptacleObject Box_bar__minus_00_dot_49_bar__plus_00_dot_20_bar__minus_00_dot_29)
        (openable Drawer_bar__minus_00_dot_58_bar__plus_00_dot_37_bar__plus_02_dot_96)
        (openable Drawer_bar__plus_01_dot_65_bar__plus_00_dot_57_bar__minus_00_dot_36)
        (openable Drawer_bar__plus_01_dot_65_bar__plus_00_dot_36_bar__minus_00_dot_78)
        (openable Drawer_bar__minus_00_dot_57_bar__plus_00_dot_57_bar__plus_02_dot_24)
        (openable Drawer_bar__plus_01_dot_65_bar__plus_00_dot_15_bar__minus_00_dot_78)
        (openable Drawer_bar__plus_01_dot_65_bar__plus_00_dot_15_bar__minus_00_dot_36)
        (openable Drawer_bar__plus_01_dot_65_bar__plus_00_dot_57_bar__minus_00_dot_78)
        (openable Drawer_bar__minus_00_dot_58_bar__plus_00_dot_56_bar__plus_02_dot_96)
        (openable Cabinet_bar__minus_00_dot_36_bar__plus_00_dot_26_bar__plus_02_dot_53)
        (openable Drawer_bar__plus_01_dot_65_bar__plus_00_dot_36_bar__minus_00_dot_36)
        (openable Drawer_bar__minus_00_dot_58_bar__plus_00_dot_15_bar__plus_02_dot_96)
        
        (atLocation agent1 loc_bar_1_bar_11_bar_2_bar_30)
        
        (cleanable Mug_bar__minus_00_dot_78_bar__plus_00_dot_68_bar__plus_02_dot_36)
        (cleanable Mug_bar__minus_00_dot_51_bar__plus_00_dot_68_bar__plus_01_dot_80)
        
        (heatable Mug_bar__minus_00_dot_78_bar__plus_00_dot_68_bar__plus_02_dot_36)
        (heatable Mug_bar__minus_00_dot_51_bar__plus_00_dot_68_bar__plus_01_dot_80)
        (coolable Mug_bar__minus_00_dot_78_bar__plus_00_dot_68_bar__plus_02_dot_36)
        (coolable Mug_bar__minus_00_dot_51_bar__plus_00_dot_68_bar__plus_01_dot_80)
        
        
        (toggleable DeskLamp_bar__plus_02_dot_01_bar__plus_00_dot_72_bar__plus_00_dot_71)
        
        
        
        
        (inReceptacle Book_bar__minus_00_dot_63_bar__plus_01_dot_84_bar__plus_01_dot_63 Bed_bar__minus_00_dot_88_bar__plus_01_dot_65_bar__plus_02_dot_17)
        (inReceptacle Pillow_bar__minus_00_dot_63_bar__plus_01_dot_87_bar__plus_03_dot_05 Bed_bar__minus_00_dot_88_bar__plus_01_dot_65_bar__plus_02_dot_17)
        (inReceptacle Laptop_bar__minus_00_dot_63_bar__plus_01_dot_84_bar__plus_02_dot_48 Bed_bar__minus_00_dot_88_bar__plus_01_dot_65_bar__plus_02_dot_17)
        (inReceptacle CellPhone_bar__plus_01_dot_67_bar__plus_00_dot_44_bar__plus_01_dot_88 ArmChair_bar__plus_01_dot_83_bar__plus_00_dot_01_bar__plus_01_dot_61)
        (inReceptacle CD_bar__plus_01_dot_50_bar__plus_00_dot_43_bar__plus_01_dot_88 ArmChair_bar__plus_01_dot_83_bar__plus_00_dot_01_bar__plus_01_dot_61)
        (inReceptacle CreditCard_bar__plus_01_dot_59_bar__plus_00_dot_44_bar__plus_01_dot_61 ArmChair_bar__plus_01_dot_83_bar__plus_00_dot_01_bar__plus_01_dot_61)
        (inReceptacle Book_bar__minus_00_dot_67_bar__plus_00_dot_07_bar__plus_02_dot_24 Cabinet_bar__minus_00_dot_36_bar__plus_00_dot_26_bar__plus_02_dot_53)
        (inReceptacle Mug_bar__minus_00_dot_78_bar__plus_00_dot_68_bar__plus_02_dot_36 Desk_bar__minus_00_dot_65_bar__plus_00_dot_01_bar__plus_01_dot_80)
        (inReceptacle AlarmClock_bar__minus_00_dot_78_bar__plus_00_dot_68_bar__plus_01_dot_80 Desk_bar__minus_00_dot_65_bar__plus_00_dot_01_bar__plus_01_dot_80)
        (inReceptacle Mug_bar__minus_00_dot_51_bar__plus_00_dot_68_bar__plus_01_dot_80 Desk_bar__minus_00_dot_65_bar__plus_00_dot_01_bar__plus_01_dot_80)
        (inReceptacle Pencil_bar__minus_00_dot_51_bar__plus_00_dot_68_bar__plus_02_dot_36 Desk_bar__minus_00_dot_65_bar__plus_00_dot_01_bar__plus_01_dot_80)
        (inReceptacle Pillow_bar__minus_00_dot_60_bar__plus_01_dot_59_bar__minus_00_dot_94 Bed_bar__minus_00_dot_87_bar__plus_00_dot_00_bar__minus_00_dot_92)
        (inReceptacle Pillow_bar__minus_00_dot_87_bar__plus_01_dot_59_bar__minus_00_dot_50 Bed_bar__minus_00_dot_87_bar__plus_00_dot_00_bar__minus_00_dot_92)
        (inReceptacle CreditCard_bar__minus_00_dot_48_bar__plus_00_dot_67_bar__plus_03_dot_25 Dresser_bar__minus_00_dot_66_bar__minus_00_dot_01_bar__plus_02_dot_97)
        (inReceptacle Laptop_bar__minus_00_dot_86_bar__plus_00_dot_67_bar__plus_02_dot_99 Dresser_bar__minus_00_dot_66_bar__minus_00_dot_01_bar__plus_02_dot_97)
        (inReceptacle AlarmClock_bar__minus_00_dot_86_bar__plus_00_dot_67_bar__plus_03_dot_25 Dresser_bar__minus_00_dot_66_bar__minus_00_dot_01_bar__plus_02_dot_97)
        (inReceptacle Pen_bar__minus_00_dot_48_bar__plus_00_dot_67_bar__plus_02_dot_82 Dresser_bar__minus_00_dot_66_bar__minus_00_dot_01_bar__plus_02_dot_97)
        (inReceptacle KeyChain_bar__minus_00_dot_63_bar__plus_00_dot_98_bar__minus_01_dot_51 Dresser_bar__minus_00_dot_77_bar__minus_00_dot_01_bar__minus_01_dot_31)
        (inReceptacle Pencil_bar__minus_00_dot_58_bar__plus_00_dot_98_bar__minus_01_dot_32 Dresser_bar__minus_00_dot_77_bar__minus_00_dot_01_bar__minus_01_dot_31)
        (inReceptacle Book_bar__plus_01_dot_61_bar__plus_00_dot_73_bar__minus_00_dot_75 Desk_bar__plus_01_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_12)
        (inReceptacle Laptop_bar__plus_01_dot_61_bar__plus_00_dot_72_bar__plus_00_dot_48 Desk_bar__plus_01_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_12)
        (inReceptacle AlarmClock_bar__plus_02_dot_00_bar__plus_00_dot_73_bar__plus_00_dot_48 Desk_bar__plus_01_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_12)
        (inReceptacle DeskLamp_bar__plus_02_dot_01_bar__plus_00_dot_72_bar__plus_00_dot_71 Desk_bar__plus_01_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_12)
        (inReceptacle CellPhone_bar__minus_00_dot_70_bar__plus_00_dot_74_bar__minus_01_dot_32 Drawer_bar__minus_00_dot_70_bar__plus_00_dot_83_bar__minus_01_dot_32)
        
        
        (receptacleAtLocation ArmChair_bar__plus_01_dot_83_bar__plus_00_dot_01_bar__plus_01_dot_61 loc_bar_4_bar_6_bar_1_bar_60)
        (receptacleAtLocation Bed_bar__minus_00_dot_87_bar__plus_00_dot_00_bar__minus_00_dot_92 loc_bar_1_bar__minus_3_bar_3_bar_60)
        (receptacleAtLocation Bed_bar__minus_00_dot_88_bar__plus_01_dot_65_bar__plus_02_dot_17 loc_bar_4_bar_9_bar_3_bar_0)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_36_bar__plus_00_dot_26_bar__plus_02_dot_53 loc_bar_2_bar_7_bar_3_bar_60)
        (receptacleAtLocation Desk_bar__plus_01_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_12 loc_bar_4_bar_1_bar_1_bar_60)
        (receptacleAtLocation Desk_bar__minus_00_dot_65_bar__plus_00_dot_01_bar__plus_01_dot_80 loc_bar_1_bar_7_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__plus_01_dot_65_bar__plus_00_dot_15_bar__minus_00_dot_36 loc_bar_2_bar__minus_4_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_01_dot_65_bar__plus_00_dot_15_bar__minus_00_dot_78 loc_bar_2_bar__minus_1_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_01_dot_65_bar__plus_00_dot_36_bar__minus_00_dot_36 loc_bar_2_bar__minus_3_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_01_dot_65_bar__plus_00_dot_36_bar__minus_00_dot_78 loc_bar_2_bar__minus_1_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_01_dot_65_bar__plus_00_dot_57_bar__minus_00_dot_36 loc_bar_3_bar__minus_3_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_01_dot_65_bar__plus_00_dot_57_bar__minus_00_dot_78 loc_bar_3_bar__minus_2_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_57_bar__plus_00_dot_57_bar__plus_02_dot_24 loc_bar_1_bar_5_bar_0_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_58_bar__plus_00_dot_15_bar__plus_02_dot_96 loc_bar_1_bar_7_bar_0_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_58_bar__plus_00_dot_37_bar__plus_02_dot_96 loc_bar_1_bar_8_bar_0_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_58_bar__plus_00_dot_56_bar__plus_02_dot_96 loc_bar_2_bar_11_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_70_bar__plus_00_dot_22_bar__minus_01_dot_32 loc_bar_1_bar__minus_5_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_70_bar__plus_00_dot_54_bar__minus_01_dot_32 loc_bar_1_bar__minus_5_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_70_bar__plus_00_dot_83_bar__minus_01_dot_32 loc_bar_1_bar__minus_5_bar_3_bar_45)
        (receptacleAtLocation Dresser_bar__minus_00_dot_66_bar__minus_00_dot_01_bar__plus_02_dot_97 loc_bar_1_bar_12_bar_3_bar_60)
        (receptacleAtLocation Dresser_bar__minus_00_dot_77_bar__minus_00_dot_01_bar__minus_01_dot_31 loc_bar_1_bar__minus_5_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_02_dot_03_bar__plus_00_dot_00_bar__minus_01_dot_19 loc_bar_6_bar__minus_5_bar_1_bar_60)
        (objectAtLocation Mug_bar__minus_00_dot_51_bar__plus_00_dot_68_bar__plus_01_dot_80 loc_bar_1_bar_7_bar_3_bar_60)
        (objectAtLocation Laptop_bar__minus_00_dot_63_bar__plus_01_dot_84_bar__plus_02_dot_48 loc_bar_4_bar_9_bar_3_bar_0)
        (objectAtLocation Pencil_bar__minus_00_dot_58_bar__plus_00_dot_98_bar__minus_01_dot_32 loc_bar_1_bar__minus_5_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__minus_00_dot_48_bar__plus_00_dot_67_bar__plus_03_dot_25 loc_bar_1_bar_12_bar_3_bar_60)
        (objectAtLocation CellPhone_bar__plus_01_dot_67_bar__plus_00_dot_44_bar__plus_01_dot_88 loc_bar_4_bar_6_bar_1_bar_60)
        (objectAtLocation Pillow_bar__minus_00_dot_60_bar__plus_01_dot_59_bar__minus_00_dot_94 loc_bar_1_bar__minus_3_bar_3_bar_60)
        (objectAtLocation AlarmClock_bar__minus_00_dot_78_bar__plus_00_dot_68_bar__plus_01_dot_80 loc_bar_1_bar_7_bar_3_bar_60)
        (objectAtLocation Book_bar__minus_00_dot_67_bar__plus_00_dot_07_bar__plus_02_dot_24 loc_bar_2_bar_7_bar_3_bar_60)
        (objectAtLocation AlarmClock_bar__plus_02_dot_00_bar__plus_00_dot_73_bar__plus_00_dot_48 loc_bar_4_bar_1_bar_1_bar_60)
        (objectAtLocation Laptop_bar__minus_00_dot_86_bar__plus_00_dot_67_bar__plus_02_dot_99 loc_bar_1_bar_12_bar_3_bar_60)
        (objectAtLocation Book_bar__minus_00_dot_63_bar__plus_01_dot_84_bar__plus_01_dot_63 loc_bar_4_bar_9_bar_3_bar_0)
        (objectAtLocation Chair_bar__minus_00_dot_25_bar__plus_00_dot_43_bar__plus_01_dot_46 loc_bar_1_bar_6_bar_3_bar_60)
        (objectAtLocation Book_bar__plus_01_dot_61_bar__plus_00_dot_73_bar__minus_00_dot_75 loc_bar_4_bar_1_bar_1_bar_60)
        (objectAtLocation Chair_bar__plus_01_dot_48_bar__plus_00_dot_43_bar__plus_00_dot_22 loc_bar_4_bar_1_bar_1_bar_60)
        (objectAtLocation Box_bar__minus_00_dot_49_bar__plus_00_dot_20_bar__minus_00_dot_29 loc_bar_1_bar__minus_1_bar_3_bar_60)
        (objectAtLocation DeskLamp_bar__plus_02_dot_01_bar__plus_00_dot_72_bar__plus_00_dot_71 loc_bar_4_bar_1_bar_1_bar_60)
        (objectAtLocation TennisRacket_bar__minus_01_dot_35_bar__plus_00_dot_29_bar__plus_00_dot_23 loc_bar__minus_4_bar_2_bar_3_bar_60)
        (objectAtLocation Pen_bar__minus_00_dot_48_bar__plus_00_dot_67_bar__plus_02_dot_82 loc_bar_1_bar_12_bar_3_bar_60)
        (objectAtLocation AlarmClock_bar__minus_00_dot_86_bar__plus_00_dot_67_bar__plus_03_dot_25 loc_bar_1_bar_12_bar_3_bar_60)
        (objectAtLocation Pillow_bar__minus_00_dot_63_bar__plus_01_dot_87_bar__plus_03_dot_05 loc_bar_4_bar_9_bar_3_bar_0)
        (objectAtLocation Pillow_bar__minus_00_dot_87_bar__plus_01_dot_59_bar__minus_00_dot_50 loc_bar_1_bar__minus_3_bar_3_bar_60)
        (objectAtLocation CellPhone_bar__minus_00_dot_70_bar__plus_00_dot_74_bar__minus_01_dot_32 loc_bar_1_bar__minus_5_bar_3_bar_45)
        (objectAtLocation Painting_bar__plus_02_dot_20_bar__plus_01_dot_77_bar__minus_00_dot_10 loc_bar_4_bar_0_bar_1_bar_0)
        (objectAtLocation KeyChain_bar__minus_00_dot_63_bar__plus_00_dot_98_bar__minus_01_dot_51 loc_bar_1_bar__minus_5_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__plus_01_dot_59_bar__plus_00_dot_44_bar__plus_01_dot_61 loc_bar_4_bar_6_bar_1_bar_60)
        (objectAtLocation Mirror_bar__plus_02_dot_23_bar__plus_01_dot_26_bar__plus_02_dot_66 loc_bar_7_bar_11_bar_1_bar_45)
        (objectAtLocation CD_bar__plus_01_dot_50_bar__plus_00_dot_43_bar__plus_01_dot_88 loc_bar_4_bar_6_bar_1_bar_60)
        (objectAtLocation LightSwitch_bar__plus_02_dot_20_bar__plus_01_dot_26_bar__plus_01_dot_99 loc_bar_7_bar_10_bar_2_bar_45)
        (objectAtLocation Window_bar__plus_01_dot_58_bar__plus_01_dot_56_bar__minus_01_dot_95 loc_bar_6_bar__minus_5_bar_2_bar_15)
        (objectAtLocation Pencil_bar__minus_00_dot_51_bar__plus_00_dot_68_bar__plus_02_dot_36 loc_bar_1_bar_7_bar_3_bar_60)
        (objectAtLocation Laptop_bar__plus_01_dot_61_bar__plus_00_dot_72_bar__plus_00_dot_48 loc_bar_4_bar_1_bar_1_bar_60)
        (objectAtLocation Mug_bar__minus_00_dot_78_bar__plus_00_dot_68_bar__plus_02_dot_36 loc_bar_1_bar_7_bar_3_bar_60)
        (objectAtLocation Blinds_bar__plus_01_dot_55_bar__plus_02_dot_44_bar__minus_01_dot_89 loc_bar_6_bar__minus_5_bar_2_bar__minus_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 MugType)
                                    (receptacleType ?r CabinetType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 MugType)
                                            (receptacleType ?r CabinetType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            