{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000317.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000318.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000319.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000320.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000321.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000322.png", "low_idx": 53}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "DiningTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-9|1|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-5.5650816, -5.5650816, 1.022149084, 1.022149084, 3.7065436, 3.7065436]], "coordinateReceptacleObjectId": ["DiningTable", [-5.38, -5.38, 1.332, 1.332, 0.0562074184, 0.0562074184]], "forceVisible": true, "objectId": "Egg|-01.39|+00.93|+00.26"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-10|9|3|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-13.94, -13.94, 8.732, 8.732, 5.723384, 5.723384]], "forceVisible": true, "objectId": "Microwave|-03.49|+01.43|+02.18"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|2|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-5.5650816, -5.5650816, 1.022149084, 1.022149084, 3.7065436, 3.7065436]], "coordinateReceptacleObjectId": ["DiningTable", [-5.38, -5.38, 1.332, 1.332, 0.0562074184, 0.0562074184]], "forceVisible": true, "objectId": "Egg|-01.39|+00.93|+00.26", "receptacleObjectId": "DiningTable|-01.35|+00.01|+00.33"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.39|+00.93|+00.26"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [142, 111, 155, 127], "mask": [[33146, 6], [33444, 9], [33744, 10], [34043, 11], [34342, 13], [34642, 13], [34942, 14], [35242, 14], [35542, 14], [35842, 14], [36142, 14], [36442, 13], [36742, 13], [37043, 12], [37344, 10], [37645, 8], [37946, 5]], "point": [148, 118]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [17, 14, 240, 126], "mask": [[3921, 168], [4090, 40], [4221, 168], [4390, 41], [4521, 210], [4820, 211], [5117, 224], [5417, 224], [5717, 224], [6017, 224], [6317, 224], [6618, 223], [6918, 223], [7218, 223], [7518, 223], [7818, 223], [8118, 222], [8418, 222], [8718, 222], [9018, 222], [9319, 221], [9619, 221], [9919, 221], [10219, 221], [10519, 221], [10819, 221], [11119, 221], [11419, 221], [11720, 219], [12020, 219], [12320, 219], [12620, 219], [12920, 219], [13220, 219], [13520, 219], [13820, 219], [14121, 218], [14421, 218], [14721, 218], [15021, 218], [15321, 217], [15621, 217], [15921, 217], [16221, 217], [16522, 216], [16822, 216], [17122, 216], [17422, 216], [17722, 216], [18022, 216], [18322, 216], [18622, 216], [18922, 215], [19223, 214], [19523, 214], [19823, 214], [20123, 214], [20423, 214], [20723, 214], [21023, 214], [21323, 214], [21624, 213], [21924, 213], [22224, 213], [22524, 212], [22824, 212], [23124, 212], [23424, 212], [23724, 212], [24025, 211], [24325, 211], [24625, 211], [24925, 211], [25225, 211], [25525, 211], [25825, 211], [26125, 210], [26425, 210], [26726, 209], [27026, 209], [27326, 209], [27626, 209], [27926, 209], [28226, 209], [28526, 209], [28826, 209], [29127, 208], [29427, 208], [29727, 207], [30027, 207], [30327, 207], [30627, 207], [30927, 207], [31227, 207], [31528, 206], [31828, 206], [32128, 206], [32428, 206], [32728, 206], [33028, 206], [33328, 205], [33628, 205], [33929, 204], [34229, 204], [34529, 204], [34829, 204], [35129, 204], [35429, 204], [35742, 178], [36062, 7], [36203, 7], [36362, 7], [36503, 6], [36662, 7], [36803, 6], [36962, 7], [37103, 6], [37262, 7], [37403, 6], [37563, 6], [37703, 6]], "point": [128, 69]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.39|+00.93|+00.26", "placeStationary": true, "receptacleObjectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 240, 131], "mask": [[0, 2], [300, 4], [600, 6], [900, 8], [1200, 9], [1500, 11], [1800, 13], [2100, 15], [2400, 17], [2700, 19], [3000, 21], [3300, 22], [3600, 24], [3900, 26], [4090, 40], [4200, 28], [4390, 41], [4500, 30], [4689, 42], [4800, 231], [5100, 241], [5400, 241], [5700, 241], [6000, 241], [6300, 241], [6600, 241], [6900, 241], [7200, 241], [7500, 241], [7800, 241], [8100, 240], [8400, 240], [8700, 240], [9000, 240], [9300, 240], [9600, 240], [9900, 240], [10200, 240], [10500, 240], [10800, 240], [11100, 240], [11400, 240], [11700, 239], [12000, 239], [12300, 239], [12600, 239], [12900, 239], [13200, 239], [13500, 239], [13800, 239], [14100, 239], [14400, 239], [14700, 239], [15000, 239], [15300, 238], [15600, 238], [15900, 238], [16200, 238], [16500, 238], [16800, 238], [17100, 238], [17400, 238], [17700, 238], [18000, 238], [18300, 98], [18399, 139], [18600, 99], [18700, 138], [18900, 86], [19006, 131], [19200, 83], [19313, 124], [19500, 83], [19615, 122], [19800, 82], [19916, 121], [20100, 81], [20217, 120], [20400, 81], [20517, 120], [20700, 81], [20817, 120], [21000, 81], [21117, 120], [21300, 81], [21417, 13], [21456, 81], [21600, 81], [21717, 8], [21762, 75], [21900, 82], [22017, 8], [22062, 75], [22200, 82], [22317, 8], [22362, 75], [22500, 82], [22617, 8], [22662, 74], [22800, 83], [22917, 8], [22962, 74], [23100, 83], [23216, 9], [23262, 74], [23400, 83], [23516, 9], [23562, 74], [23700, 83], [23816, 9], [23861, 75], [24000, 84], [24116, 10], [24161, 75], [24300, 84], [24415, 11], [24461, 75], [24600, 84], [24715, 12], [24760, 76], [24900, 84], [25015, 13], [25059, 77], [25200, 84], [25315, 13], [25359, 77], [25500, 85], [25615, 13], [25659, 77], [25800, 85], [25915, 14], [25958, 78], [26100, 85], [26215, 15], [26257, 78], [26400, 85], [26515, 16], [26556, 79], [26700, 86], [26814, 18], [26855, 80], [27000, 88], [27113, 19], [27155, 80], [27300, 89], [27411, 21], [27455, 80], [27600, 91], [27707, 28], [27752, 83], [27900, 235], [28200, 235], [28500, 235], [28800, 235], [29100, 235], [29400, 235], [29700, 234], [30000, 234], [30300, 234], [30600, 234], [30900, 234], [31200, 234], [31500, 234], [31800, 234], [32100, 234], [32400, 234], [32700, 234], [33000, 234], [33300, 233], [33600, 233], [33900, 233], [34200, 233], [34500, 233], [34800, 233], [35100, 233], [35400, 233], [35700, 33], [35742, 178], [36000, 30], [36062, 7], [36203, 7], [36300, 27], [36362, 7], [36503, 6], [36600, 24], [36662, 7], [36803, 6], [36900, 21], [36962, 7], [37103, 6], [37200, 18], [37262, 7], [37403, 6], [37500, 15], [37563, 6], [37703, 6], [37800, 13], [38100, 10], [38400, 7], [38700, 4], [39000, 1]], "point": [120, 65]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 240, 131], "mask": [[0, 2], [300, 4], [600, 6], [900, 8], [1200, 9], [1500, 11], [1800, 13], [2100, 15], [2400, 17], [2700, 19], [3000, 21], [3300, 22], [3600, 24], [3900, 26], [4090, 40], [4200, 28], [4390, 41], [4500, 30], [4689, 42], [4800, 231], [5100, 241], [5400, 241], [5700, 241], [6000, 241], [6300, 241], [6600, 241], [6900, 241], [7200, 241], [7500, 241], [7800, 241], [8100, 240], [8400, 240], [8700, 240], [9000, 240], [9300, 240], [9600, 240], [9900, 240], [10200, 240], [10500, 240], [10800, 240], [11100, 240], [11400, 240], [11700, 239], [12000, 239], [12300, 239], [12600, 239], [12900, 239], [13200, 239], [13500, 239], [13800, 239], [14100, 239], [14400, 239], [14700, 239], [15000, 239], [15300, 238], [15600, 238], [15900, 238], [16200, 238], [16500, 238], [16800, 238], [17100, 238], [17400, 238], [17700, 238], [18000, 238], [18300, 98], [18399, 139], [18600, 99], [18700, 138], [18900, 86], [19006, 131], [19200, 83], [19313, 124], [19500, 83], [19615, 122], [19800, 82], [19916, 121], [20100, 81], [20217, 120], [20400, 81], [20517, 120], [20700, 81], [20817, 120], [21000, 81], [21117, 120], [21300, 81], [21417, 13], [21456, 81], [21600, 81], [21717, 8], [21762, 75], [21900, 82], [22017, 8], [22062, 75], [22200, 82], [22317, 8], [22362, 75], [22500, 82], [22617, 8], [22662, 74], [22800, 83], [22917, 8], [22962, 74], [23100, 83], [23216, 9], [23262, 74], [23400, 83], [23516, 9], [23563, 73], [23700, 83], [23816, 9], [23864, 72], [24000, 84], [24116, 10], [24164, 72], [24300, 84], [24415, 11], [24465, 71], [24600, 84], [24715, 12], [24765, 71], [24900, 84], [25015, 13], [25066, 70], [25200, 84], [25315, 13], [25366, 70], [25500, 85], [25615, 13], [25666, 70], [25800, 85], [25915, 14], [25966, 70], [26100, 85], [26215, 15], [26267, 68], [26400, 85], [26515, 16], [26567, 68], [26700, 86], [26814, 18], [26867, 68], [27000, 88], [27113, 19], [27167, 68], [27300, 89], [27411, 21], [27466, 69], [27600, 91], [27707, 28], [27766, 69], [27900, 150], [28066, 69], [28200, 150], [28366, 69], [28500, 151], [28665, 70], [28800, 151], [28964, 71], [29100, 152], [29263, 72], [29400, 153], [29562, 73], [29700, 155], [29861, 73], [30000, 234], [30300, 234], [30600, 234], [30900, 234], [31200, 234], [31500, 234], [31800, 234], [32100, 234], [32400, 234], [32700, 234], [33000, 234], [33300, 233], [33600, 233], [33900, 233], [34200, 233], [34500, 233], [34800, 233], [35100, 233], [35400, 233], [35700, 33], [35742, 178], [36000, 30], [36062, 7], [36203, 7], [36300, 27], [36362, 7], [36503, 6], [36600, 24], [36662, 7], [36803, 6], [36900, 21], [36962, 7], [37103, 6], [37200, 18], [37262, 7], [37403, 6], [37500, 15], [37563, 6], [37703, 6], [37800, 13], [38100, 10], [38400, 7], [38700, 4], [39000, 1]], "point": [120, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-13.94, -13.94, 8.732, 8.732, 5.723384, 5.723384]], "forceVisible": true, "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [17, 14, 240, 126], "mask": [[3921, 168], [4090, 40], [4221, 168], [4390, 41], [4521, 210], [4820, 211], [5117, 224], [5417, 224], [5717, 224], [6017, 224], [6317, 224], [6618, 223], [6918, 223], [7218, 223], [7518, 223], [7818, 223], [8118, 222], [8418, 222], [8718, 222], [9018, 222], [9319, 221], [9619, 221], [9919, 221], [10219, 221], [10519, 221], [10819, 221], [11119, 221], [11419, 221], [11720, 219], [12020, 219], [12320, 219], [12620, 219], [12920, 219], [13220, 219], [13520, 219], [13820, 219], [14121, 218], [14421, 218], [14721, 218], [15021, 218], [15321, 217], [15621, 217], [15921, 217], [16221, 217], [16522, 216], [16822, 216], [17122, 216], [17422, 216], [17722, 216], [18022, 216], [18322, 216], [18622, 216], [18922, 215], [19223, 214], [19523, 214], [19823, 214], [20123, 214], [20423, 214], [20723, 214], [21023, 214], [21323, 214], [21624, 213], [21924, 213], [22224, 213], [22524, 212], [22824, 212], [23124, 212], [23424, 212], [23724, 212], [24025, 211], [24325, 211], [24625, 211], [24925, 211], [25225, 211], [25525, 211], [25825, 211], [26125, 210], [26425, 210], [26726, 209], [27026, 209], [27326, 209], [27626, 209], [27926, 209], [28226, 209], [28526, 209], [28826, 209], [29127, 208], [29427, 208], [29727, 207], [30027, 207], [30327, 207], [30627, 207], [30927, 207], [31227, 207], [31528, 206], [31828, 206], [32128, 206], [32428, 206], [32728, 206], [33028, 206], [33328, 205], [33628, 205], [33929, 204], [34229, 204], [34529, 204], [34829, 204], [35129, 204], [35429, 204], [35742, 178], [36062, 7], [36203, 7], [36362, 7], [36503, 6], [36662, 7], [36803, 6], [36962, 7], [37103, 6], [37262, 7], [37403, 6], [37563, 6], [37703, 6]], "point": [128, 69]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-13.94, -13.94, 8.732, 8.732, 5.723384, 5.723384]], "forceVisible": true, "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [17, 14, 240, 126], "mask": [[3921, 168], [4090, 40], [4221, 168], [4390, 41], [4521, 210], [4820, 211], [5117, 224], [5417, 224], [5717, 224], [6017, 224], [6317, 224], [6618, 223], [6918, 223], [7218, 223], [7518, 223], [7818, 223], [8118, 222], [8418, 222], [8718, 222], [9018, 222], [9319, 221], [9619, 221], [9919, 221], [10219, 221], [10519, 221], [10819, 221], [11119, 221], [11419, 221], [11720, 219], [12020, 219], [12320, 219], [12620, 219], [12920, 219], [13220, 219], [13520, 219], [13820, 219], [14121, 218], [14421, 218], [14721, 218], [15021, 218], [15321, 217], [15621, 217], [15921, 217], [16221, 217], [16522, 216], [16822, 216], [17122, 216], [17422, 216], [17722, 216], [18022, 216], [18322, 216], [18622, 216], [18922, 215], [19223, 214], [19523, 214], [19823, 214], [20123, 214], [20423, 214], [20723, 214], [21023, 214], [21323, 214], [21624, 213], [21924, 213], [22224, 213], [22524, 212], [22824, 212], [23124, 212], [23424, 212], [23724, 212], [24025, 211], [24325, 211], [24625, 211], [24925, 211], [25225, 211], [25525, 211], [25825, 211], [26125, 210], [26425, 210], [26726, 209], [27026, 209], [27326, 209], [27626, 209], [27926, 209], [28226, 209], [28526, 209], [28826, 209], [29127, 208], [29427, 208], [29727, 207], [30027, 207], [30327, 207], [30627, 207], [30927, 207], [31227, 207], [31528, 206], [31828, 206], [32128, 206], [32428, 206], [32728, 206], [33028, 206], [33328, 205], [33628, 205], [33929, 204], [34229, 204], [34529, 204], [34829, 204], [35129, 204], [35429, 204], [35742, 178], [36062, 7], [36203, 7], [36362, 7], [36503, 6], [36662, 7], [36803, 6], [36962, 7], [37103, 6], [37262, 7], [37403, 6], [37563, 6], [37703, 6]], "point": [128, 69]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [17, 14, 240, 126], "mask": [[3921, 168], [4090, 40], [4221, 168], [4390, 41], [4521, 210], [4820, 211], [5117, 224], [5417, 224], [5717, 224], [6017, 224], [6317, 224], [6618, 223], [6918, 223], [7218, 223], [7518, 223], [7818, 223], [8118, 222], [8418, 222], [8718, 222], [9018, 222], [9319, 221], [9619, 221], [9919, 221], [10219, 221], [10519, 221], [10819, 221], [11119, 221], [11419, 221], [11720, 219], [12020, 219], [12320, 219], [12620, 219], [12920, 219], [13220, 219], [13520, 219], [13820, 219], [14121, 218], [14421, 218], [14721, 218], [15021, 218], [15321, 217], [15621, 217], [15921, 217], [16221, 217], [16522, 216], [16822, 216], [17122, 216], [17422, 216], [17722, 216], [18022, 216], [18322, 216], [18622, 216], [18922, 215], [19223, 214], [19523, 214], [19823, 214], [20123, 214], [20423, 214], [20723, 214], [21023, 214], [21323, 214], [21624, 213], [21924, 213], [22224, 213], [22524, 212], [22824, 212], [23124, 212], [23424, 212], [23724, 212], [24025, 211], [24325, 211], [24625, 211], [24925, 211], [25225, 211], [25525, 211], [25825, 211], [26125, 210], [26425, 210], [26726, 209], [27026, 209], [27326, 209], [27626, 209], [27926, 209], [28226, 209], [28526, 209], [28826, 209], [29127, 208], [29427, 208], [29727, 207], [30027, 207], [30327, 207], [30627, 207], [30927, 207], [31227, 207], [31528, 206], [31828, 206], [32128, 206], [32428, 206], [32728, 206], [33028, 206], [33328, 205], [33628, 205], [33929, 204], [34229, 204], [34529, 204], [34829, 204], [35129, 204], [35429, 204], [35742, 178], [36062, 7], [36203, 7], [36362, 7], [36503, 6], [36662, 7], [36803, 6], [36962, 7], [37103, 6], [37262, 7], [37403, 6], [37563, 6], [37703, 6]], "point": [128, 69]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.39|+00.93|+00.26"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [149, 77, 166, 100], "mask": [[22957, 2], [23254, 7], [23553, 10], [23852, 12], [24152, 12], [24451, 14], [24751, 14], [25050, 16], [25350, 16], [25649, 17], [25949, 17], [26249, 18], [26549, 18], [26849, 18], [27149, 18], [27449, 17], [27749, 17], [28050, 16], [28350, 16], [28651, 14], [28951, 13], [29252, 11], [29553, 9], [29855, 6]], "point": [157, 87]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 240, 131], "mask": [[0, 2], [300, 4], [600, 6], [900, 8], [1200, 9], [1500, 11], [1800, 13], [2100, 15], [2400, 17], [2700, 19], [3000, 21], [3300, 22], [3600, 24], [3900, 26], [4090, 40], [4200, 28], [4390, 41], [4500, 30], [4689, 42], [4800, 231], [5100, 241], [5400, 241], [5700, 241], [6000, 241], [6300, 241], [6600, 241], [6900, 241], [7200, 241], [7500, 241], [7800, 241], [8100, 240], [8400, 240], [8700, 240], [9000, 240], [9300, 240], [9600, 240], [9900, 240], [10200, 240], [10500, 240], [10800, 240], [11100, 240], [11400, 240], [11700, 239], [12000, 239], [12300, 239], [12600, 239], [12900, 239], [13200, 239], [13500, 239], [13800, 239], [14100, 239], [14400, 239], [14700, 239], [15000, 239], [15300, 238], [15600, 238], [15900, 238], [16200, 238], [16500, 238], [16800, 238], [17100, 238], [17400, 238], [17700, 238], [18000, 238], [18300, 98], [18399, 139], [18600, 99], [18700, 138], [18900, 86], [19006, 131], [19200, 83], [19313, 124], [19500, 83], [19615, 122], [19800, 82], [19916, 121], [20100, 81], [20217, 120], [20400, 81], [20517, 120], [20700, 81], [20817, 120], [21000, 81], [21117, 120], [21300, 81], [21417, 13], [21456, 81], [21600, 81], [21717, 8], [21762, 75], [21900, 82], [22017, 8], [22062, 75], [22200, 82], [22317, 8], [22362, 75], [22500, 82], [22617, 8], [22662, 74], [22800, 83], [22917, 8], [22962, 74], [23100, 83], [23216, 9], [23262, 74], [23400, 83], [23516, 9], [23562, 74], [23700, 83], [23816, 9], [23861, 75], [24000, 84], [24116, 10], [24161, 75], [24300, 84], [24415, 11], [24461, 75], [24600, 84], [24715, 12], [24760, 76], [24900, 84], [25015, 13], [25059, 77], [25200, 84], [25315, 13], [25359, 77], [25500, 85], [25615, 13], [25659, 77], [25800, 85], [25915, 14], [25958, 78], [26100, 85], [26215, 15], [26257, 78], [26400, 85], [26515, 16], [26556, 79], [26700, 86], [26814, 18], [26855, 80], [27000, 88], [27113, 19], [27155, 80], [27300, 89], [27411, 21], [27455, 80], [27600, 91], [27707, 28], [27752, 83], [27900, 235], [28200, 235], [28500, 235], [28800, 235], [29100, 235], [29400, 235], [29700, 234], [30000, 234], [30300, 234], [30600, 234], [30900, 234], [31200, 234], [31500, 234], [31800, 234], [32100, 234], [32400, 234], [32700, 234], [33000, 234], [33300, 233], [33600, 233], [33900, 233], [34200, 233], [34500, 233], [34800, 233], [35100, 233], [35400, 233], [35700, 33], [35742, 178], [36000, 30], [36062, 7], [36203, 7], [36300, 27], [36362, 7], [36503, 6], [36600, 24], [36662, 7], [36803, 6], [36900, 21], [36962, 7], [37103, 6], [37200, 18], [37262, 7], [37403, 6], [37500, 15], [37563, 6], [37703, 6], [37800, 13], [38100, 10], [38400, 7], [38700, 4], [39000, 1]], "point": [120, 65]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.39|+00.93|+00.26", "placeStationary": true, "receptacleObjectId": "DiningTable|-01.35|+00.01|+00.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 201, 208], "mask": [[13, 2], [41, 115], [172, 12], [312, 2], [343, 113], [472, 12], [612, 1], [644, 93], [741, 15], [772, 12], [945, 91], [1042, 14], [1072, 12], [1246, 89], [1343, 13], [1373, 11], [1547, 88], [1643, 13], [1673, 11], [1848, 87], [1943, 13], [1973, 11], [2149, 85], [2244, 12], [2273, 12], [2449, 85], [2544, 12], [2573, 12], [2749, 85], [2845, 11], [2873, 12], [3050, 84], [3145, 11], [3173, 12], [3351, 83], [3445, 11], [3473, 12], [3652, 82], [3745, 12], [3773, 12], [3953, 81], [4045, 12], [4073, 12], [4254, 80], [4345, 12], [4373, 12], [4555, 78], [4647, 10], [4673, 12], [4856, 75], [4949, 37], [5156, 74], [5250, 36], [5457, 72], [5551, 35], [5758, 68], [5852, 34], [6058, 67], [6153, 33], [6359, 65], [6454, 32], [6659, 35], [6696, 27], [6754, 32], [6959, 35], [6995, 2], [7000, 22], [7055, 31], [7260, 36], [7299, 2], [7305, 17], [7355, 31], [7560, 35], [7598, 2], [7603, 2], [7655, 32], [7801, 1], [7860, 35], [7897, 2], [7902, 2], [7914, 7], [7956, 31], [8101, 1], [8160, 38], [8201, 2], [8212, 8], [8256, 31], [8400, 2], [8460, 42], [8511, 10], [8556, 31], [8700, 2], [8760, 61], [8857, 30], [9000, 2], [9060, 55], [9117, 4], [9157, 30], [9300, 2], [9360, 54], [9418, 3], [9457, 30], [9600, 3], [9660, 54], [9717, 4], [9757, 30], [9900, 4], [9960, 54], [10017, 4], [10057, 30], [10200, 5], [10260, 54], [10317, 5], [10357, 31], [10500, 4], [10560, 54], [10617, 5], [10657, 31], [10800, 4], [10860, 54], [10917, 5], [10956, 32], [11100, 4], [11159, 54], [11217, 5], [11256, 32], [11400, 4], [11459, 54], [11517, 5], [11556, 32], [11700, 4], [11759, 54], [11817, 5], [11856, 32], [12000, 4], [12058, 55], [12117, 6], [12155, 33], [12300, 4], [12357, 56], [12416, 7], [12455, 33], [12600, 4], [12657, 56], [12716, 7], [12754, 34], [12900, 4], [12958, 55], [13016, 8], [13054, 35], [13200, 4], [13258, 55], [13316, 9], [13353, 36], [13500, 5], [13557, 55], [13616, 9], [13652, 37], [13800, 5], [13857, 55], [13916, 9], [13952, 37], [14100, 5], [14157, 55], [14216, 9], [14252, 37], [14400, 6], [14457, 56], [14515, 10], [14552, 37], [14700, 6], [14758, 55], [14815, 11], [14852, 37], [15000, 7], [15060, 53], [15115, 11], [15152, 37], [15300, 7], [15361, 52], [15415, 11], [15452, 37], [15600, 8], [15662, 51], [15715, 11], [15752, 38], [15900, 9], [15963, 49], [16014, 12], [16052, 38], [16200, 9], [16263, 49], [16314, 12], [16352, 38], [16500, 10], [16564, 48], [16614, 12], [16652, 38], [16800, 10], [16865, 47], [16914, 12], [16952, 38], [17100, 11], [17165, 47], [17214, 13], [17252, 38], [17400, 12], [17466, 46], [17514, 13], [17552, 38], [17700, 13], [17767, 45], [17814, 13], [17852, 38], [18000, 14], [18069, 43], [18114, 13], [18152, 38], [18300, 15], [18370, 42], [18414, 13], [18451, 40], [18600, 16], [18671, 40], [18714, 13], [18751, 40], [18900, 15], [18972, 39], [19014, 13], [19051, 40], [19200, 15], [19272, 39], [19314, 14], [19351, 40], [19500, 15], [19573, 38], [19614, 14], [19652, 39], [19800, 14], [19874, 37], [19914, 14], [19952, 39], [20100, 14], [20174, 36], [20214, 14], [20252, 39], [20400, 14], [20474, 33], [20518, 10], [20552, 39], [20700, 13], [20775, 29], [20820, 8], [20852, 39], [21000, 13], [21075, 29], [21120, 8], [21151, 41], [21300, 13], [21375, 28], [21420, 8], [21451, 41], [21600, 13], [21675, 28], [21720, 9], [21750, 42], [21900, 13], [21976, 27], [22020, 10], [22049, 43], [22200, 13], [22276, 27], [22320, 11], [22348, 44], [22500, 12], [22576, 27], [22620, 13], [22647, 45], [22800, 12], [22876, 26], [22919, 16], [22945, 47], [23100, 12], [23176, 26], [23219, 73], [23400, 11], [23476, 26], [23519, 73], [23700, 11], [23776, 26], [23819, 74], [24000, 11], [24076, 26], [24118, 75], [24300, 11], [24375, 118], [24600, 11], [24675, 118], [24900, 11], [24975, 118], [25200, 11], [25275, 118], [25500, 11], [25574, 119], [25800, 11], [25874, 119], [26100, 11], [26174, 119], [26400, 11], [26473, 3], [26507, 87], [26700, 12], [26773, 2], [26860, 34], [27000, 13], [27073, 2], [27163, 31], [27300, 13], [27373, 3], [27465, 29], [27600, 13], [27673, 4], [27767, 27], [27900, 13], [27973, 6], [27984, 31], [28068, 26], [28200, 13], [28273, 9], [28283, 32], [28369, 25], [28500, 13], [28573, 43], [28670, 24], [28800, 13], [28873, 43], [28960, 34], [29100, 13], [29172, 45], [29232, 63], [29400, 13], [29472, 123], [29700, 13], [29772, 123], [30000, 13], [30071, 124], [30300, 13], [30371, 124], [30600, 14], [30671, 124], [30900, 14], [30971, 124], [31200, 15], [31270, 125], [31500, 15], [31570, 125], [31800, 16], [31870, 126], [32100, 17], [32170, 126], [32400, 17], [32470, 126], [32700, 18], [32770, 126], [33000, 18], [33070, 126], [33300, 19], [33369, 127], [33600, 20], [33669, 127], [33900, 21], [33969, 127], [34200, 22], [34268, 129], [34500, 23], [34567, 130], [34800, 24], [34865, 132], [35100, 26], [35164, 133], [35400, 28], [35462, 135], [35700, 33], [35758, 28], [35796, 101], [36000, 41], [36042, 40], [36098, 99], [36300, 80], [36401, 96], [36600, 79], [36730, 67], [36900, 79], [37042, 56], [37200, 80], [37300, 10], [37343, 55], [37500, 80], [37598, 19], [37643, 55], [37800, 81], [37897, 26], [37942, 56], [38100, 83], [38196, 35], [38241, 57], [38400, 85], [38494, 104], [38700, 198], [39000, 198], [39300, 198], [39600, 199], [39900, 199], [40200, 199], [40500, 199], [40800, 199], [41100, 199], [41400, 199], [41700, 199], [42000, 199], [42300, 200], [42600, 200], [42900, 200], [43200, 200], [43500, 200], [43800, 200], [44100, 200], [44400, 200], [44700, 200], [45000, 201], [45300, 201], [45600, 201], [45900, 201], [46200, 201], [46500, 201], [46800, 201], [47100, 201], [47400, 201], [47700, 202], [48000, 202], [48300, 202], [48600, 202], [48900, 202], [49200, 202], [49500, 202], [49800, 202], [50100, 202], [50400, 202], [50700, 201], [51023, 9], [51155, 6], [51324, 9], [51455, 6], [51625, 9], [51755, 6], [51926, 9], [52055, 6], [52227, 8], [52355, 6], [52528, 8], [52655, 6], [52829, 8], [52955, 6], [53130, 8], [53255, 5], [53431, 8], [53555, 5], [53732, 8], [53855, 5], [54033, 8], [54155, 5], [54334, 8], [54455, 5], [54635, 8], [54755, 5], [54936, 8], [55055, 5], [55237, 8], [55355, 5], [55538, 8], [55655, 5], [55839, 7], [55955, 5], [56140, 7], [56255, 5], [56441, 7], [56555, 4], [56742, 7], [56855, 4], [57043, 7], [57154, 5], [57344, 7], [57454, 5], [57645, 7], [57754, 5], [57946, 7], [58054, 5], [58247, 7], [58354, 5], [58548, 7], [58654, 5], [58849, 7], [58954, 5], [59150, 6], [59254, 5], [59451, 6], [59554, 5], [59752, 6], [59854, 5], [60053, 6], [60154, 4], [60354, 6], [60454, 4], [60655, 7], [60754, 4], [60956, 6], [61054, 5], [61256, 7], [61354, 5], [61557, 7], [61653, 5], [61858, 6], [61954, 4], [62159, 4], [62254, 4]], "point": [100, 103]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan15", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -2.75, "y": 0.914953351, "z": 1.0}, "object_poses": [{"objectName": "Potato_08de48c7", "position": {"x": -0.790436268, "y": 0.755253553, "z": 3.8170836}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Potato_08de48c7", "position": {"x": -1.22447133, "y": 0.120157927, "z": 3.85227823}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_73731673", "position": {"x": -2.40283132, "y": 0.915064156, "z": 3.99884}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_73731673", "position": {"x": -3.176847, "y": 0.915364265, "z": 1.60797334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_657c04b2", "position": {"x": -3.176847, "y": 0.915542066, "z": 3.82773781}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -1.78763235, "y": 0.8924014, "z": 0.358577967}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_fd79f50b", "position": {"x": -3.15634537, "y": 0.7617906, "z": 2.80603576}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_58cf1806", "position": {"x": -0.9949086, "y": 0.892602, "z": 0.358577877}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_72bf05b8", "position": {"x": -1.523391, "y": 0.8920212, "z": 0.358577937}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_72bf05b8", "position": {"x": -1.66600478, "y": 0.7901047, "z": 3.7}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_46b03e61", "position": {"x": -3.1432, "y": 0.9668, "z": 1.9715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -1.129757, "y": 0.121757984, "z": 3.820289}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -3.65481567, "y": 0.9155869, "z": 1.18968439}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -0.790437758, "y": 1.54152393, "z": 4.067756}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Knife_4b738b3b", "position": {"x": -1.12702918, "y": 0.9150003, "z": 0.358577877}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_4b738b3b", "position": {"x": -1.41854239, "y": 0.9390276, "z": 3.89094138}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_0e75bb4f", "position": {"x": -3.537117, "y": 1.54518771, "z": 2.22469831}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "Bowl_0e75bb4f", "position": {"x": -0.0895634, "y": 0.9851263, "z": 4.11414957}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "SaltShaker_d1c7827d", "position": {"x": -3.176847, "y": 0.9121062, "z": 1.5243156}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_94539d9a", "position": {"x": -3.15581083, "y": 0.7423218, "z": 1.20286059}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_0fed2699", "position": {"x": -3.55922174, "y": 0.9971903, "z": 1.44065785}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_0fed2699", "position": {"x": -1.12702918, "y": 0.9728629, "z": 0.152496547}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_0af3d2bb", "position": {"x": -2.30927181, "y": 0.07601178, "z": 3.58648038}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -1.3912704, "y": 0.9266359, "z": 0.255537271}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_4b738b3b", "position": {"x": -1.60824549, "y": 0.9390276, "z": 4.14679575}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_72bf05b8", "position": {"x": -1.72990179, "y": 0.7901047, "z": 3.662686}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Bread_0fed2699", "position": {"x": -1.3912704, "y": 0.9728629, "z": 0.04945591}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_08de48c7", "position": {"x": -0.206373975, "y": 1.018604, "z": 3.80125523}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Plate_a5e935d4", "position": {"x": -3.27244067, "y": 0.9197459, "z": 1.18968439}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_46b03e61", "position": {"x": -0.6736249, "y": 0.9887026, "z": 3.98898864}, "rotation": {"x": 0.0, "y": 269.999664, "z": 0.0}}, {"objectName": "Kettle_a09ab4be", "position": {"x": -3.4247, "y": 0.9668, "z": 2.3743}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -3.156548, "y": 0.08242285, "z": 2.75202847}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_0e75bb4f", "position": {"x": -3.081253, "y": 0.9143891, "z": 3.336}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_66cbdca0", "position": {"x": -0.440000236, "y": 1.61039352, "z": 4.033449}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -1.25914979, "y": 0.8912595, "z": 0.4616186}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_fd79f50b", "position": {"x": -1.3912704, "y": 0.9064725, "z": 0.3585779}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SoapBottle_da34a2c3", "position": {"x": -1.3912704, "y": 0.8913755, "z": 0.461618632}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_94931e65", "position": {"x": -3.463628, "y": 1.001532, "z": 2.84426212}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_7e5e2cad", "position": {"x": -2.61477637, "y": 0.902653933, "z": 3.62041235}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_8bd3d3d2", "position": {"x": -3.47386122, "y": 1.60627162, "z": 2.06840754}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "SaltShaker_d1c7827d", "position": {"x": -3.176847, "y": 0.911806166, "z": 3.17208719}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_94539d9a", "position": {"x": -3.27041769, "y": 0.07987678, "z": 1.59131527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_657c04b2", "position": {"x": -3.36803436, "y": 0.915542066, "z": 3.82773781}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_73731673", "position": {"x": -1.65551162, "y": 0.891036868, "z": 0.564659357}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_58cf1806", "position": {"x": -1.92159271, "y": 0.790685534, "z": 3.737314}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_0af3d2bb", "position": {"x": -3.4245863, "y": 0.945124567, "z": 3.275126}, "rotation": {"x": 359.7497, "y": 89.87062, "z": 0.007993305}}], "object_toggles": [], "random_seed": 773170630, "scene_num": 15}, "task_id": "trial_T20190908_042125_390836", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A20FCMWP43CVIU_32EYX73OY3QTZJORGP3MSUMJ3NVURN", "high_descs": ["turn right, walk to face the right side of the white table", "pick up the brown egg from the white table", "turn around, walk to face the stove and microwave oven", "open the microwave oven door, place egg inside, close door, cook egg, open door, pick up egg, close door", "walk over to white table, face the left side of the table", "put the egg on the table to the right of the large copper cup"], "task_desc": "put a cooked egg on a table", "votes": [1, 1]}, {"assignment_id": "A3MXBLYX2CDGTP_3MX2NQ3YCCBWB0KOON1690GR5OMX55", "high_descs": ["Turn around and walk to the white table on your left.", "Pick up the egg from the table.", "Turn left and walk towards the stove, turn left to face the stove.", "Cook the egg in the microwave above the stove, remove the egg from the microwave.", "Turn around and walk towards the door then turn right and walk to the white table.", "Put the egg on the white table."], "task_desc": "Cook an egg in the microwave, put the egg on the table.", "votes": [1, 1]}, {"assignment_id": "A27AK750Y9M9KH_3YWRV122CVGNHE9VHGXM1DREDWAU8G", "high_descs": ["Turn right and head towards the white kitchen table. ", "Pick up the egg on top of the white kitchen table. ", "Turn left and head towards the oven on the left. ", "Place the egg in the microwave oven above the stove and heat it. ", "Take out the egg and head back towards the white kitchen table, next to the spoon sitting on top of it. ", "Place the egg next to the cup sitting on top of the white kitchen table. "], "task_desc": "Microwave an egg and place it on a table. ", "votes": [1, 1]}]}}