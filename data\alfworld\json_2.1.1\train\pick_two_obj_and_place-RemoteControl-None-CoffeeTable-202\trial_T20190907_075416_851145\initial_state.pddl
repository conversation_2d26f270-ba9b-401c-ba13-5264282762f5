
(define (problem plan_trial_T20190907_075416_851145)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Book_bar__plus_00_dot_75_bar__plus_00_dot_72_bar__plus_00_dot_50 - object
        Book_bar__minus_01_dot_60_bar__plus_00_dot_55_bar__plus_00_dot_47 - object
        Book_bar__minus_01_dot_60_bar__plus_00_dot_55_bar__plus_00_dot_74 - object
        Box_bar__plus_00_dot_33_bar__plus_00_dot_89_bar__plus_00_dot_49 - object
        CreditCard_bar__minus_01_dot_34_bar__plus_00_dot_45_bar__plus_02_dot_52 - object
        CreditCard_bar__minus_01_dot_50_bar__plus_00_dot_71_bar__plus_03_dot_87 - object
        CreditCard_bar__minus_01_dot_85_bar__plus_00_dot_55_bar__plus_00_dot_65 - object
        Curtains_bar__minus_03_dot_97_bar__plus_02_dot_26_bar__plus_01_dot_45 - object
        FloorLamp_bar__minus_03_dot_69_bar__plus_00_dot_00_bar__plus_04_dot_01 - object
        HousePlant_bar__minus_02_dot_02_bar__plus_00_dot_44_bar__plus_02_dot_36 - object
        KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_71_bar__plus_00_dot_58 - object
        Laptop_bar__minus_01_dot_20_bar__plus_00_dot_71_bar__plus_03_dot_76 - object
        Laptop_bar__minus_01_dot_71_bar__plus_00_dot_45_bar__plus_02_dot_52 - object
        LightSwitch_bar__minus_00_dot_13_bar__plus_01_dot_41_bar__plus_04_dot_35 - object
        Painting_bar__minus_01_dot_10_bar__plus_01_dot_77_bar__minus_00_dot_02 - object
        Painting_bar__minus_03_dot_05_bar__plus_01_dot_69_bar__plus_04_dot_34 - object
        Pillow_bar__minus_00_dot_51_bar__plus_00_dot_66_bar__plus_00_dot_43 - object
        Pillow_bar__minus_01_dot_10_bar__plus_00_dot_64_bar__plus_00_dot_74 - object
        Pillow_bar__minus_03_dot_20_bar__plus_00_dot_65_bar__plus_00_dot_66 - object
        RemoteControl_bar__minus_01_dot_50_bar__plus_00_dot_71_bar__plus_03_dot_65 - object
        RemoteControl_bar__minus_01_dot_85_bar__plus_00_dot_55_bar__plus_00_dot_47 - object
        Statue_bar__minus_01_dot_46_bar__plus_00_dot_45_bar__plus_02_dot_28 - object
        Statue_bar__minus_01_dot_71_bar__plus_00_dot_45_bar__plus_02_dot_20 - object
        Television_bar__minus_01_dot_51_bar__plus_01_dot_19_bar__plus_04_dot_13 - object
        Watch_bar__plus_00_dot_63_bar__plus_00_dot_71_bar__plus_00_dot_18 - object
        Window_bar__minus_04_dot_05_bar__plus_01_dot_20_bar__plus_01_dot_31 - object
        ArmChair_bar__minus_03_dot_08_bar__plus_00_dot_47_bar__plus_00_dot_37 - receptacle
        CoffeeTable_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__plus_02_dot_36 - receptacle
        GarbageCan_bar__minus_00_dot_38_bar__plus_00_dot_00_bar__plus_04_dot_10 - receptacle
        SideTable_bar__plus_00_dot_63_bar__plus_00_dot_00_bar__plus_00_dot_34 - receptacle
        Sofa_bar__minus_01_dot_16_bar__plus_00_dot_00_bar__plus_00_dot_45 - receptacle
        TVStand_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_03_dot_87 - receptacle
        loc_bar__minus_1_bar_14_bar_0_bar_15 - location
        loc_bar__minus_5_bar_7_bar_2_bar_45 - location
        loc_bar__minus_12_bar_15_bar_0_bar_0 - location
        loc_bar_2_bar_15_bar_3_bar_60 - location
        loc_bar__minus_12_bar_6_bar_2_bar_45 - location
        loc_bar__minus_12_bar_6_bar_3_bar__minus_30 - location
        loc_bar__minus_4_bar_5_bar_2_bar_0 - location
        loc_bar__minus_6_bar_12_bar_0_bar_60 - location
        loc_bar__minus_14_bar_14_bar_0_bar_60 - location
        loc_bar__minus_7_bar_5_bar_2_bar_60 - location
        loc_bar_3_bar_6_bar_2_bar_60 - location
        loc_bar__minus_12_bar_5_bar_3_bar_30 - location
        loc_bar__minus_6_bar_5_bar_0_bar_60 - location
        loc_bar_2_bar_4_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType GarbageCan_bar__minus_00_dot_38_bar__plus_00_dot_00_bar__plus_04_dot_10 GarbageCanType)
        (receptacleType CoffeeTable_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__plus_02_dot_36 CoffeeTableType)
        (receptacleType Sofa_bar__minus_01_dot_16_bar__plus_00_dot_00_bar__plus_00_dot_45 SofaType)
        (receptacleType SideTable_bar__plus_00_dot_63_bar__plus_00_dot_00_bar__plus_00_dot_34 SideTableType)
        (receptacleType ArmChair_bar__minus_03_dot_08_bar__plus_00_dot_47_bar__plus_00_dot_37 ArmChairType)
        (receptacleType TVStand_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_03_dot_87 TVStandType)
        (objectType Pillow_bar__minus_03_dot_20_bar__plus_00_dot_65_bar__plus_00_dot_66 PillowType)
        (objectType Box_bar__plus_00_dot_33_bar__plus_00_dot_89_bar__plus_00_dot_49 BoxType)
        (objectType Television_bar__minus_01_dot_51_bar__plus_01_dot_19_bar__plus_04_dot_13 TelevisionType)
        (objectType FloorLamp_bar__minus_03_dot_69_bar__plus_00_dot_00_bar__plus_04_dot_01 FloorLampType)
        (objectType HousePlant_bar__minus_02_dot_02_bar__plus_00_dot_44_bar__plus_02_dot_36 HousePlantType)
        (objectType CreditCard_bar__minus_01_dot_34_bar__plus_00_dot_45_bar__plus_02_dot_52 CreditCardType)
        (objectType Pillow_bar__minus_01_dot_10_bar__plus_00_dot_64_bar__plus_00_dot_74 PillowType)
        (objectType Watch_bar__plus_00_dot_63_bar__plus_00_dot_71_bar__plus_00_dot_18 WatchType)
        (objectType CreditCard_bar__minus_01_dot_85_bar__plus_00_dot_55_bar__plus_00_dot_65 CreditCardType)
        (objectType KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_71_bar__plus_00_dot_58 KeyChainType)
        (objectType Laptop_bar__minus_01_dot_20_bar__plus_00_dot_71_bar__plus_03_dot_76 LaptopType)
        (objectType Curtains_bar__minus_03_dot_97_bar__plus_02_dot_26_bar__plus_01_dot_45 CurtainsType)
        (objectType Book_bar__minus_01_dot_60_bar__plus_00_dot_55_bar__plus_00_dot_47 BookType)
        (objectType RemoteControl_bar__minus_01_dot_85_bar__plus_00_dot_55_bar__plus_00_dot_47 RemoteControlType)
        (objectType Window_bar__minus_04_dot_05_bar__plus_01_dot_20_bar__plus_01_dot_31 WindowType)
        (objectType CreditCard_bar__minus_01_dot_50_bar__plus_00_dot_71_bar__plus_03_dot_87 CreditCardType)
        (objectType Statue_bar__minus_01_dot_46_bar__plus_00_dot_45_bar__plus_02_dot_28 StatueType)
        (objectType LightSwitch_bar__minus_00_dot_13_bar__plus_01_dot_41_bar__plus_04_dot_35 LightSwitchType)
        (objectType Book_bar__plus_00_dot_75_bar__plus_00_dot_72_bar__plus_00_dot_50 BookType)
        (objectType Book_bar__minus_01_dot_60_bar__plus_00_dot_55_bar__plus_00_dot_74 BookType)
        (objectType Painting_bar__minus_03_dot_05_bar__plus_01_dot_69_bar__plus_04_dot_34 PaintingType)
        (objectType Statue_bar__minus_01_dot_71_bar__plus_00_dot_45_bar__plus_02_dot_20 StatueType)
        (objectType Pillow_bar__minus_00_dot_51_bar__plus_00_dot_66_bar__plus_00_dot_43 PillowType)
        (objectType Painting_bar__minus_01_dot_10_bar__plus_01_dot_77_bar__minus_00_dot_02 PaintingType)
        (objectType Laptop_bar__minus_01_dot_71_bar__plus_00_dot_45_bar__plus_02_dot_52 LaptopType)
        (objectType RemoteControl_bar__minus_01_dot_50_bar__plus_00_dot_71_bar__plus_03_dot_65 RemoteControlType)
        (canContain CoffeeTableType BookType)
        (canContain CoffeeTableType WatchType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType BookType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType CreditCardType)
        (canContain SideTableType BookType)
        (canContain SideTableType WatchType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (pickupable Pillow_bar__minus_03_dot_20_bar__plus_00_dot_65_bar__plus_00_dot_66)
        (pickupable Box_bar__plus_00_dot_33_bar__plus_00_dot_89_bar__plus_00_dot_49)
        (pickupable CreditCard_bar__minus_01_dot_34_bar__plus_00_dot_45_bar__plus_02_dot_52)
        (pickupable Pillow_bar__minus_01_dot_10_bar__plus_00_dot_64_bar__plus_00_dot_74)
        (pickupable Watch_bar__plus_00_dot_63_bar__plus_00_dot_71_bar__plus_00_dot_18)
        (pickupable CreditCard_bar__minus_01_dot_85_bar__plus_00_dot_55_bar__plus_00_dot_65)
        (pickupable KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_71_bar__plus_00_dot_58)
        (pickupable Laptop_bar__minus_01_dot_20_bar__plus_00_dot_71_bar__plus_03_dot_76)
        (pickupable Book_bar__minus_01_dot_60_bar__plus_00_dot_55_bar__plus_00_dot_47)
        (pickupable RemoteControl_bar__minus_01_dot_85_bar__plus_00_dot_55_bar__plus_00_dot_47)
        (pickupable CreditCard_bar__minus_01_dot_50_bar__plus_00_dot_71_bar__plus_03_dot_87)
        (pickupable Statue_bar__minus_01_dot_46_bar__plus_00_dot_45_bar__plus_02_dot_28)
        (pickupable Book_bar__plus_00_dot_75_bar__plus_00_dot_72_bar__plus_00_dot_50)
        (pickupable Book_bar__minus_01_dot_60_bar__plus_00_dot_55_bar__plus_00_dot_74)
        (pickupable Statue_bar__minus_01_dot_71_bar__plus_00_dot_45_bar__plus_02_dot_20)
        (pickupable Pillow_bar__minus_00_dot_51_bar__plus_00_dot_66_bar__plus_00_dot_43)
        (pickupable Laptop_bar__minus_01_dot_71_bar__plus_00_dot_45_bar__plus_02_dot_52)
        (pickupable RemoteControl_bar__minus_01_dot_50_bar__plus_00_dot_71_bar__plus_03_dot_65)
        (isReceptacleObject Box_bar__plus_00_dot_33_bar__plus_00_dot_89_bar__plus_00_dot_49)
        
        
        (atLocation agent1 loc_bar_2_bar_4_bar_3_bar_30)
        
        
        
        
        
        
        
        (toggleable FloorLamp_bar__minus_03_dot_69_bar__plus_00_dot_00_bar__plus_04_dot_01)
        
        
        
        
        (inReceptacle Laptop_bar__minus_01_dot_20_bar__plus_00_dot_71_bar__plus_03_dot_76 TVStand_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_03_dot_87)
        (inReceptacle Television_bar__minus_01_dot_51_bar__plus_01_dot_19_bar__plus_04_dot_13 TVStand_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_03_dot_87)
        (inReceptacle RemoteControl_bar__minus_01_dot_50_bar__plus_00_dot_71_bar__plus_03_dot_65 TVStand_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_03_dot_87)
        (inReceptacle CreditCard_bar__minus_01_dot_50_bar__plus_00_dot_71_bar__plus_03_dot_87 TVStand_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_03_dot_87)
        (inReceptacle Pillow_bar__minus_03_dot_20_bar__plus_00_dot_65_bar__plus_00_dot_66 ArmChair_bar__minus_03_dot_08_bar__plus_00_dot_47_bar__plus_00_dot_37)
        (inReceptacle HousePlant_bar__minus_02_dot_02_bar__plus_00_dot_44_bar__plus_02_dot_36 CoffeeTable_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__plus_02_dot_36)
        (inReceptacle Statue_bar__minus_01_dot_46_bar__plus_00_dot_45_bar__plus_02_dot_28 CoffeeTable_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__plus_02_dot_36)
        (inReceptacle Laptop_bar__minus_01_dot_71_bar__plus_00_dot_45_bar__plus_02_dot_52 CoffeeTable_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__plus_02_dot_36)
        (inReceptacle Statue_bar__minus_01_dot_71_bar__plus_00_dot_45_bar__plus_02_dot_20 CoffeeTable_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__plus_02_dot_36)
        (inReceptacle CreditCard_bar__minus_01_dot_34_bar__plus_00_dot_45_bar__plus_02_dot_52 CoffeeTable_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__plus_02_dot_36)
        (inReceptacle Pillow_bar__minus_01_dot_10_bar__plus_00_dot_64_bar__plus_00_dot_74 Sofa_bar__minus_01_dot_16_bar__plus_00_dot_00_bar__plus_00_dot_45)
        (inReceptacle Pillow_bar__minus_00_dot_51_bar__plus_00_dot_66_bar__plus_00_dot_43 Sofa_bar__minus_01_dot_16_bar__plus_00_dot_00_bar__plus_00_dot_45)
        (inReceptacle Book_bar__minus_01_dot_60_bar__plus_00_dot_55_bar__plus_00_dot_47 Sofa_bar__minus_01_dot_16_bar__plus_00_dot_00_bar__plus_00_dot_45)
        (inReceptacle CreditCard_bar__minus_01_dot_85_bar__plus_00_dot_55_bar__plus_00_dot_65 Sofa_bar__minus_01_dot_16_bar__plus_00_dot_00_bar__plus_00_dot_45)
        (inReceptacle Book_bar__minus_01_dot_60_bar__plus_00_dot_55_bar__plus_00_dot_74 Sofa_bar__minus_01_dot_16_bar__plus_00_dot_00_bar__plus_00_dot_45)
        (inReceptacle Book_bar__plus_00_dot_75_bar__plus_00_dot_72_bar__plus_00_dot_50 SideTable_bar__plus_00_dot_63_bar__plus_00_dot_00_bar__plus_00_dot_34)
        (inReceptacle Box_bar__plus_00_dot_33_bar__plus_00_dot_89_bar__plus_00_dot_49 SideTable_bar__plus_00_dot_63_bar__plus_00_dot_00_bar__plus_00_dot_34)
        (inReceptacle Watch_bar__plus_00_dot_63_bar__plus_00_dot_71_bar__plus_00_dot_18 SideTable_bar__plus_00_dot_63_bar__plus_00_dot_00_bar__plus_00_dot_34)
        (inReceptacle KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_71_bar__plus_00_dot_58 SideTable_bar__plus_00_dot_63_bar__plus_00_dot_00_bar__plus_00_dot_34)
        
        
        (receptacleAtLocation ArmChair_bar__minus_03_dot_08_bar__plus_00_dot_47_bar__plus_00_dot_37 loc_bar__minus_12_bar_6_bar_2_bar_45)
        (receptacleAtLocation CoffeeTable_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__plus_02_dot_36 loc_bar__minus_6_bar_5_bar_0_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_00_dot_38_bar__plus_00_dot_00_bar__plus_04_dot_10 loc_bar_2_bar_15_bar_3_bar_60)
        (receptacleAtLocation SideTable_bar__plus_00_dot_63_bar__plus_00_dot_00_bar__plus_00_dot_34 loc_bar_3_bar_6_bar_2_bar_60)
        (receptacleAtLocation Sofa_bar__minus_01_dot_16_bar__plus_00_dot_00_bar__plus_00_dot_45 loc_bar__minus_5_bar_7_bar_2_bar_45)
        (receptacleAtLocation TVStand_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_03_dot_87 loc_bar__minus_6_bar_12_bar_0_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_50_bar__plus_00_dot_71_bar__plus_03_dot_87 loc_bar__minus_6_bar_12_bar_0_bar_60)
        (objectAtLocation RemoteControl_bar__minus_01_dot_50_bar__plus_00_dot_71_bar__plus_03_dot_65 loc_bar__minus_6_bar_12_bar_0_bar_60)
        (objectAtLocation Statue_bar__minus_01_dot_71_bar__plus_00_dot_45_bar__plus_02_dot_20 loc_bar__minus_6_bar_5_bar_0_bar_60)
        (objectAtLocation Pillow_bar__minus_00_dot_51_bar__plus_00_dot_66_bar__plus_00_dot_43 loc_bar__minus_5_bar_7_bar_2_bar_45)
        (objectAtLocation Laptop_bar__minus_01_dot_71_bar__plus_00_dot_45_bar__plus_02_dot_52 loc_bar__minus_6_bar_5_bar_0_bar_60)
        (objectAtLocation Book_bar__plus_00_dot_75_bar__plus_00_dot_72_bar__plus_00_dot_50 loc_bar_3_bar_6_bar_2_bar_60)
        (objectAtLocation Pillow_bar__minus_01_dot_10_bar__plus_00_dot_64_bar__plus_00_dot_74 loc_bar__minus_5_bar_7_bar_2_bar_45)
        (objectAtLocation CreditCard_bar__minus_01_dot_85_bar__plus_00_dot_55_bar__plus_00_dot_65 loc_bar__minus_5_bar_7_bar_2_bar_45)
        (objectAtLocation Book_bar__minus_01_dot_60_bar__plus_00_dot_55_bar__plus_00_dot_47 loc_bar__minus_5_bar_7_bar_2_bar_45)
        (objectAtLocation Painting_bar__minus_03_dot_05_bar__plus_01_dot_69_bar__plus_04_dot_34 loc_bar__minus_12_bar_15_bar_0_bar_0)
        (objectAtLocation Curtains_bar__minus_03_dot_97_bar__plus_02_dot_26_bar__plus_01_dot_45 loc_bar__minus_12_bar_6_bar_3_bar__minus_30)
        (objectAtLocation Book_bar__minus_01_dot_60_bar__plus_00_dot_55_bar__plus_00_dot_74 loc_bar__minus_5_bar_7_bar_2_bar_45)
        (objectAtLocation Box_bar__plus_00_dot_33_bar__plus_00_dot_89_bar__plus_00_dot_49 loc_bar_3_bar_6_bar_2_bar_60)
        (objectAtLocation Painting_bar__minus_01_dot_10_bar__plus_01_dot_77_bar__minus_00_dot_02 loc_bar__minus_4_bar_5_bar_2_bar_0)
        (objectAtLocation LightSwitch_bar__minus_00_dot_13_bar__plus_01_dot_41_bar__plus_04_dot_35 loc_bar__minus_1_bar_14_bar_0_bar_15)
        (objectAtLocation Television_bar__minus_01_dot_51_bar__plus_01_dot_19_bar__plus_04_dot_13 loc_bar__minus_6_bar_12_bar_0_bar_60)
        (objectAtLocation HousePlant_bar__minus_02_dot_02_bar__plus_00_dot_44_bar__plus_02_dot_36 loc_bar__minus_6_bar_5_bar_0_bar_60)
        (objectAtLocation Laptop_bar__minus_01_dot_20_bar__plus_00_dot_71_bar__plus_03_dot_76 loc_bar__minus_6_bar_12_bar_0_bar_60)
        (objectAtLocation KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_71_bar__plus_00_dot_58 loc_bar_3_bar_6_bar_2_bar_60)
        (objectAtLocation Pillow_bar__minus_03_dot_20_bar__plus_00_dot_65_bar__plus_00_dot_66 loc_bar__minus_12_bar_6_bar_2_bar_45)
        (objectAtLocation Statue_bar__minus_01_dot_46_bar__plus_00_dot_45_bar__plus_02_dot_28 loc_bar__minus_6_bar_5_bar_0_bar_60)
        (objectAtLocation FloorLamp_bar__minus_03_dot_69_bar__plus_00_dot_00_bar__plus_04_dot_01 loc_bar__minus_14_bar_14_bar_0_bar_60)
        (objectAtLocation RemoteControl_bar__minus_01_dot_85_bar__plus_00_dot_55_bar__plus_00_dot_47 loc_bar__minus_7_bar_5_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_34_bar__plus_00_dot_45_bar__plus_02_dot_52 loc_bar__minus_6_bar_5_bar_0_bar_60)
        (objectAtLocation Watch_bar__plus_00_dot_63_bar__plus_00_dot_71_bar__plus_00_dot_18 loc_bar_3_bar_6_bar_2_bar_60)
        (objectAtLocation Window_bar__minus_04_dot_05_bar__plus_01_dot_20_bar__plus_01_dot_31 loc_bar__minus_12_bar_5_bar_3_bar_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 RemoteControlType)
                                    (receptacleType ?r CoffeeTableType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 RemoteControlType)
                                            (receptacleType ?r CoffeeTableType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            