
(define (problem plan_trial_T20190908_131643_379617)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Apple_bar__minus_01_dot_09_bar__plus_00_dot_85_bar__plus_00_dot_28 - object
        Bowl_bar__minus_01_dot_75_bar__plus_01_dot_77_bar__plus_00_dot_25 - object
        Bowl_bar__minus_02_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_31 - object
        Bowl_bar__minus_02_dot_79_bar__plus_00_dot_30_bar__plus_04_dot_50 - object
        Bread_bar__minus_02_dot_81_bar__plus_01_dot_24_bar__plus_04_dot_43 - object
        ButterKnife_bar__minus_00_dot_23_bar__plus_00_dot_90_bar__plus_03_dot_13 - object
        ButterKnife_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__plus_03_dot_54 - object
        Cup_bar__minus_02_dot_89_bar__plus_00_dot_63_bar__plus_04_dot_65 - object
        DishSponge_bar__minus_01_dot_09_bar__plus_00_dot_80_bar__plus_00_dot_45 - object
        Egg_bar__minus_02_dot_76_bar__plus_01_dot_22_bar__plus_04_dot_20 - object
        Faucet_bar__minus_01_dot_08_bar__plus_00_dot_92_bar__plus_00_dot_03 - object
        Fork_bar__minus_02_dot_38_bar__plus_00_dot_90_bar__plus_02_dot_57 - object
        Knife_bar__minus_00_dot_49_bar__plus_00_dot_69_bar__plus_00_dot_46 - object
        Knife_bar__minus_00_dot_49_bar__plus_00_dot_93_bar__plus_02_dot_53 - object
        Knife_bar__minus_02_dot_48_bar__plus_00_dot_93_bar__plus_02_dot_71 - object
        Lettuce_bar__minus_00_dot_23_bar__plus_00_dot_98_bar__plus_02_dot_53 - object
        Lettuce_bar__minus_02_dot_81_bar__plus_00_dot_71_bar__plus_04_dot_35 - object
        LightSwitch_bar__minus_00_dot_07_bar__plus_01_dot_45_bar__plus_02_dot_00 - object
        Mug_bar__minus_02_dot_78_bar__plus_01_dot_00_bar__plus_03_dot_71 - object
        Pan_bar__minus_00_dot_25_bar__plus_00_dot_93_bar__plus_04_dot_65 - object
        PepperShaker_bar__minus_02_dot_27_bar__plus_00_dot_66_bar__plus_00_dot_36 - object
        Plate_bar__minus_00_dot_45_bar__plus_00_dot_89_bar__plus_02_dot_93 - object
        Plate_bar__minus_01_dot_19_bar__plus_01_dot_78_bar__plus_00_dot_21 - object
        Plate_bar__minus_02_dot_79_bar__plus_00_dot_90_bar__plus_01_dot_91 - object
        Potato_bar__minus_02_dot_82_bar__plus_00_dot_08_bar__plus_00_dot_45 - object
        Pot_bar__minus_00_dot_54_bar__plus_00_dot_93_bar__plus_04_dot_27 - object
        SaltShaker_bar__minus_02_dot_44_bar__plus_00_dot_92_bar__plus_00_dot_43 - object
        Sink_bar__minus_01_dot_11_bar__plus_00_dot_93_bar__plus_00_dot_34 - object
        SoapBottle_bar__minus_03_dot_00_bar__plus_00_dot_90_bar__plus_02_dot_31 - object
        Spatula_bar__minus_00_dot_49_bar__plus_00_dot_92_bar__plus_02_dot_33 - object
        Spoon_bar__minus_02_dot_69_bar__plus_00_dot_90_bar__plus_02_dot_18 - object
        StoveKnob_bar__minus_00_dot_06_bar__plus_01_dot_09_bar__plus_04_dot_22 - object
        StoveKnob_bar__minus_00_dot_06_bar__plus_01_dot_09_bar__plus_04_dot_38 - object
        StoveKnob_bar__minus_00_dot_06_bar__plus_01_dot_09_bar__plus_04_dot_63 - object
        StoveKnob_bar__minus_00_dot_06_bar__plus_01_dot_09_bar__plus_04_dot_78 - object
        Tomato_bar__minus_00_dot_66_bar__plus_00_dot_96_bar__plus_03_dot_33 - object
        Tomato_bar__minus_02_dot_86_bar__plus_01_dot_00_bar__plus_03_dot_21 - object
        Window_bar__minus_03_dot_08_bar__plus_01_dot_59_bar__plus_01_dot_36 - object
        Cabinet_bar__minus_00_dot_48_bar__plus_02_dot_05_bar__plus_00_dot_39 - receptacle
        Cabinet_bar__minus_00_dot_52_bar__plus_02_dot_05_bar__plus_00_dot_39 - receptacle
        Cabinet_bar__minus_00_dot_56_bar__plus_00_dot_49_bar__plus_00_dot_65 - receptacle
        Cabinet_bar__minus_00_dot_60_bar__plus_00_dot_49_bar__plus_00_dot_65 - receptacle
        Cabinet_bar__minus_01_dot_48_bar__plus_02_dot_05_bar__plus_00_dot_39 - receptacle
        Cabinet_bar__minus_01_dot_51_bar__plus_02_dot_05_bar__plus_00_dot_39 - receptacle
        Cabinet_bar__minus_01_dot_55_bar__plus_00_dot_49_bar__plus_00_dot_65 - receptacle
        Cabinet_bar__minus_01_dot_59_bar__plus_00_dot_49_bar__plus_00_dot_65 - receptacle
        Cabinet_bar__minus_02_dot_47_bar__plus_02_dot_05_bar__plus_00_dot_39 - receptacle
        Cabinet_bar__minus_02_dot_55_bar__plus_00_dot_49_bar__plus_00_dot_65 - receptacle
        CoffeeMachine_bar__minus_02_dot_96_bar__plus_00_dot_93_bar__plus_03_dot_61 - receptacle
        CounterTop_bar__minus_01_dot_37_bar__plus_00_dot_95_bar__plus_00_dot_35 - receptacle
        CounterTop_bar__minus_02_dot_78_bar__plus_00_dot_97_bar__plus_03_dot_49 - receptacle
        DiningTable_bar__minus_00_dot_47_bar__plus_00_dot_01_bar__plus_03_dot_00 - receptacle
        DiningTable_bar__minus_02_dot_76_bar__plus_00_dot_01_bar__plus_02_dot_36 - receptacle
        Drawer_bar__minus_00_dot_33_bar__plus_00_dot_70_bar__plus_00_dot_41 - receptacle
        Drawer_bar__minus_01_dot_82_bar__plus_00_dot_70_bar__plus_00_dot_41 - receptacle
        Drawer_bar__minus_02_dot_32_bar__plus_00_dot_70_bar__plus_00_dot_41 - receptacle
        Fridge_bar__minus_02_dot_80_bar__plus_00_dot_00_bar__plus_04_dot_43 - receptacle
        GarbageCan_bar__minus_02_dot_82_bar__plus_00_dot_00_bar__plus_00_dot_37 - receptacle
        Microwave_bar__minus_00_dot_36_bar__plus_00_dot_91_bar__plus_00_dot_22 - receptacle
        Sink_bar__minus_01_dot_11_bar__plus_00_dot_93_bar__plus_00_dot_34_bar_SinkBasin - receptacle
        StoveBurner_bar__minus_00_dot_25_bar__plus_00_dot_92_bar__plus_04_dot_27 - receptacle
        StoveBurner_bar__minus_00_dot_25_bar__plus_00_dot_92_bar__plus_04_dot_67 - receptacle
        StoveBurner_bar__minus_00_dot_54_bar__plus_00_dot_92_bar__plus_04_dot_27 - receptacle
        StoveBurner_bar__minus_00_dot_54_bar__plus_00_dot_92_bar__plus_04_dot_67 - receptacle
        Toaster_bar__minus_00_dot_29_bar__plus_00_dot_89_bar__plus_03_dot_56 - receptacle
        loc_bar__minus_5_bar_4_bar_2_bar__minus_30 - location
        loc_bar__minus_4_bar_17_bar_1_bar_60 - location
        loc_bar__minus_6_bar_5_bar_2_bar_45 - location
        loc_bar__minus_8_bar_4_bar_2_bar__minus_30 - location
        loc_bar__minus_7_bar_4_bar_2_bar__minus_30 - location
        loc_bar__minus_3_bar_5_bar_2_bar_30 - location
        loc_bar__minus_3_bar_6_bar_2_bar_45 - location
        loc_bar__minus_8_bar_14_bar_3_bar_45 - location
        loc_bar__minus_10_bar_5_bar_3_bar_0 - location
        loc_bar__minus_3_bar_7_bar_1_bar_15 - location
        loc_bar__minus_7_bar_4_bar_2_bar_60 - location
        loc_bar__minus_4_bar_14_bar_1_bar_45 - location
        loc_bar__minus_4_bar_4_bar_2_bar_45 - location
        loc_bar__minus_4_bar_5_bar_2_bar_45 - location
        loc_bar__minus_3_bar_4_bar_2_bar_60 - location
        loc_bar__minus_4_bar_17_bar_1_bar_30 - location
        loc_bar__minus_8_bar_14_bar_3_bar_30 - location
        loc_bar__minus_3_bar_5_bar_2_bar__minus_30 - location
        loc_bar__minus_7_bar_17_bar_3_bar_60 - location
        loc_bar__minus_8_bar_5_bar_2_bar_45 - location
        loc_bar__minus_3_bar_5_bar_2_bar_45 - location
        loc_bar__minus_4_bar_17_bar_1_bar_45 - location
        loc_bar__minus_5_bar_11_bar_1_bar_60 - location
        loc_bar__minus_8_bar_10_bar_3_bar_60 - location
        loc_bar__minus_10_bar_4_bar_2_bar_60 - location
        loc_bar__minus_3_bar_4_bar_2_bar__minus_30 - location
        loc_bar__minus_7_bar_13_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType Cabinet_bar__minus_01_dot_55_bar__plus_00_dot_49_bar__plus_00_dot_65 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_52_bar__plus_02_dot_05_bar__plus_00_dot_39 CabinetType)
        (receptacleType StoveBurner_bar__minus_00_dot_25_bar__plus_00_dot_92_bar__plus_04_dot_67 StoveBurnerType)
        (receptacleType CounterTop_bar__minus_02_dot_78_bar__plus_00_dot_97_bar__plus_03_dot_49 CounterTopType)
        (receptacleType StoveBurner_bar__minus_00_dot_25_bar__plus_00_dot_92_bar__plus_04_dot_27 StoveBurnerType)
        (receptacleType Microwave_bar__minus_00_dot_36_bar__plus_00_dot_91_bar__plus_00_dot_22 MicrowaveType)
        (receptacleType DiningTable_bar__minus_00_dot_47_bar__plus_00_dot_01_bar__plus_03_dot_00 DiningTableType)
        (receptacleType CoffeeMachine_bar__minus_02_dot_96_bar__plus_00_dot_93_bar__plus_03_dot_61 CoffeeMachineType)
        (receptacleType Sink_bar__minus_01_dot_11_bar__plus_00_dot_93_bar__plus_00_dot_34_bar_SinkBasin SinkBasinType)
        (receptacleType Cabinet_bar__minus_00_dot_60_bar__plus_00_dot_49_bar__plus_00_dot_65 CabinetType)
        (receptacleType Toaster_bar__minus_00_dot_29_bar__plus_00_dot_89_bar__plus_03_dot_56 ToasterType)
        (receptacleType Cabinet_bar__minus_00_dot_56_bar__plus_00_dot_49_bar__plus_00_dot_65 CabinetType)
        (receptacleType Cabinet_bar__minus_01_dot_48_bar__plus_02_dot_05_bar__plus_00_dot_39 CabinetType)
        (receptacleType Cabinet_bar__minus_00_dot_48_bar__plus_02_dot_05_bar__plus_00_dot_39 CabinetType)
        (receptacleType Fridge_bar__minus_02_dot_80_bar__plus_00_dot_00_bar__plus_04_dot_43 FridgeType)
        (receptacleType Cabinet_bar__minus_02_dot_47_bar__plus_02_dot_05_bar__plus_00_dot_39 CabinetType)
        (receptacleType Drawer_bar__minus_02_dot_32_bar__plus_00_dot_70_bar__plus_00_dot_41 DrawerType)
        (receptacleType StoveBurner_bar__minus_00_dot_54_bar__plus_00_dot_92_bar__plus_04_dot_67 StoveBurnerType)
        (receptacleType Drawer_bar__minus_01_dot_82_bar__plus_00_dot_70_bar__plus_00_dot_41 DrawerType)
        (receptacleType GarbageCan_bar__minus_02_dot_82_bar__plus_00_dot_00_bar__plus_00_dot_37 GarbageCanType)
        (receptacleType CounterTop_bar__minus_01_dot_37_bar__plus_00_dot_95_bar__plus_00_dot_35 CounterTopType)
        (receptacleType Cabinet_bar__minus_01_dot_59_bar__plus_00_dot_49_bar__plus_00_dot_65 CabinetType)
        (receptacleType StoveBurner_bar__minus_00_dot_54_bar__plus_00_dot_92_bar__plus_04_dot_27 StoveBurnerType)
        (receptacleType Cabinet_bar__minus_01_dot_51_bar__plus_02_dot_05_bar__plus_00_dot_39 CabinetType)
        (receptacleType DiningTable_bar__minus_02_dot_76_bar__plus_00_dot_01_bar__plus_02_dot_36 DiningTableType)
        (receptacleType Drawer_bar__minus_00_dot_33_bar__plus_00_dot_70_bar__plus_00_dot_41 DrawerType)
        (receptacleType Cabinet_bar__minus_02_dot_55_bar__plus_00_dot_49_bar__plus_00_dot_65 CabinetType)
        (objectType Plate_bar__minus_00_dot_45_bar__plus_00_dot_89_bar__plus_02_dot_93 PlateType)
        (objectType Knife_bar__minus_02_dot_48_bar__plus_00_dot_93_bar__plus_02_dot_71 KnifeType)
        (objectType Bowl_bar__minus_01_dot_75_bar__plus_01_dot_77_bar__plus_00_dot_25 BowlType)
        (objectType Bread_bar__minus_02_dot_81_bar__plus_01_dot_24_bar__plus_04_dot_43 BreadType)
        (objectType Window_bar__minus_03_dot_08_bar__plus_01_dot_59_bar__plus_01_dot_36 WindowType)
        (objectType Cup_bar__minus_02_dot_89_bar__plus_00_dot_63_bar__plus_04_dot_65 CupType)
        (objectType Knife_bar__minus_00_dot_49_bar__plus_00_dot_93_bar__plus_02_dot_53 KnifeType)
        (objectType Lettuce_bar__minus_02_dot_81_bar__plus_00_dot_71_bar__plus_04_dot_35 LettuceType)
        (objectType Bowl_bar__minus_02_dot_79_bar__plus_00_dot_30_bar__plus_04_dot_50 BowlType)
        (objectType Plate_bar__minus_02_dot_79_bar__plus_00_dot_90_bar__plus_01_dot_91 PlateType)
        (objectType Pan_bar__minus_00_dot_25_bar__plus_00_dot_93_bar__plus_04_dot_65 PanType)
        (objectType Spoon_bar__minus_02_dot_69_bar__plus_00_dot_90_bar__plus_02_dot_18 SpoonType)
        (objectType Sink_bar__minus_01_dot_11_bar__plus_00_dot_93_bar__plus_00_dot_34 SinkType)
        (objectType Plate_bar__minus_01_dot_19_bar__plus_01_dot_78_bar__plus_00_dot_21 PlateType)
        (objectType Apple_bar__minus_01_dot_09_bar__plus_00_dot_85_bar__plus_00_dot_28 AppleType)
        (objectType PepperShaker_bar__minus_02_dot_27_bar__plus_00_dot_66_bar__plus_00_dot_36 PepperShakerType)
        (objectType Lettuce_bar__minus_00_dot_23_bar__plus_00_dot_98_bar__plus_02_dot_53 LettuceType)
        (objectType LightSwitch_bar__minus_00_dot_07_bar__plus_01_dot_45_bar__plus_02_dot_00 LightSwitchType)
        (objectType SaltShaker_bar__minus_02_dot_44_bar__plus_00_dot_92_bar__plus_00_dot_43 SaltShakerType)
        (objectType StoveKnob_bar__minus_00_dot_06_bar__plus_01_dot_09_bar__plus_04_dot_63 StoveKnobType)
        (objectType StoveKnob_bar__minus_00_dot_06_bar__plus_01_dot_09_bar__plus_04_dot_78 StoveKnobType)
        (objectType StoveKnob_bar__minus_00_dot_06_bar__plus_01_dot_09_bar__plus_04_dot_38 StoveKnobType)
        (objectType Tomato_bar__minus_00_dot_66_bar__plus_00_dot_96_bar__plus_03_dot_33 TomatoType)
        (objectType DishSponge_bar__minus_01_dot_09_bar__plus_00_dot_80_bar__plus_00_dot_45 DishSpongeType)
        (objectType StoveKnob_bar__minus_00_dot_06_bar__plus_01_dot_09_bar__plus_04_dot_22 StoveKnobType)
        (objectType Tomato_bar__minus_02_dot_86_bar__plus_01_dot_00_bar__plus_03_dot_21 TomatoType)
        (objectType Fork_bar__minus_02_dot_38_bar__plus_00_dot_90_bar__plus_02_dot_57 ForkType)
        (objectType Egg_bar__minus_02_dot_76_bar__plus_01_dot_22_bar__plus_04_dot_20 EggType)
        (objectType Mug_bar__minus_02_dot_78_bar__plus_01_dot_00_bar__plus_03_dot_71 MugType)
        (objectType Potato_bar__minus_02_dot_82_bar__plus_00_dot_08_bar__plus_00_dot_45 PotatoType)
        (objectType Pot_bar__minus_00_dot_54_bar__plus_00_dot_93_bar__plus_04_dot_27 PotType)
        (objectType Spatula_bar__minus_00_dot_49_bar__plus_00_dot_92_bar__plus_02_dot_33 SpatulaType)
        (objectType ButterKnife_bar__minus_00_dot_23_bar__plus_00_dot_90_bar__plus_03_dot_13 ButterKnifeType)
        (objectType ButterKnife_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__plus_03_dot_54 ButterKnifeType)
        (objectType Bowl_bar__minus_02_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_31 BowlType)
        (objectType Knife_bar__minus_00_dot_49_bar__plus_00_dot_69_bar__plus_00_dot_46 KnifeType)
        (objectType SoapBottle_bar__minus_03_dot_00_bar__plus_00_dot_90_bar__plus_02_dot_31 SoapBottleType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain MicrowaveType MugType)
        (canContain MicrowaveType PotatoType)
        (canContain MicrowaveType EggType)
        (canContain MicrowaveType AppleType)
        (canContain MicrowaveType TomatoType)
        (canContain MicrowaveType BreadType)
        (canContain MicrowaveType CupType)
        (canContain MicrowaveType PlateType)
        (canContain MicrowaveType BowlType)
        (canContain DiningTableType SaltShakerType)
        (canContain DiningTableType BreadType)
        (canContain DiningTableType DishSpongeType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType PotType)
        (canContain DiningTableType MugType)
        (canContain DiningTableType EggType)
        (canContain DiningTableType ForkType)
        (canContain DiningTableType SpoonType)
        (canContain DiningTableType SoapBottleType)
        (canContain DiningTableType LettuceType)
        (canContain DiningTableType PotatoType)
        (canContain DiningTableType ButterKnifeType)
        (canContain DiningTableType CupType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType PepperShakerType)
        (canContain DiningTableType TomatoType)
        (canContain DiningTableType KnifeType)
        (canContain DiningTableType AppleType)
        (canContain DiningTableType PanType)
        (canContain DiningTableType SpatulaType)
        (canContain CoffeeMachineType MugType)
        (canContain SinkBasinType DishSpongeType)
        (canContain SinkBasinType BowlType)
        (canContain SinkBasinType PotType)
        (canContain SinkBasinType MugType)
        (canContain SinkBasinType EggType)
        (canContain SinkBasinType ForkType)
        (canContain SinkBasinType SpoonType)
        (canContain SinkBasinType LettuceType)
        (canContain SinkBasinType PotatoType)
        (canContain SinkBasinType ButterKnifeType)
        (canContain SinkBasinType CupType)
        (canContain SinkBasinType PlateType)
        (canContain SinkBasinType TomatoType)
        (canContain SinkBasinType KnifeType)
        (canContain SinkBasinType AppleType)
        (canContain SinkBasinType PanType)
        (canContain SinkBasinType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain FridgeType BreadType)
        (canContain FridgeType BowlType)
        (canContain FridgeType PotType)
        (canContain FridgeType MugType)
        (canContain FridgeType EggType)
        (canContain FridgeType LettuceType)
        (canContain FridgeType PotatoType)
        (canContain FridgeType CupType)
        (canContain FridgeType PlateType)
        (canContain FridgeType TomatoType)
        (canContain FridgeType AppleType)
        (canContain FridgeType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain GarbageCanType BreadType)
        (canContain GarbageCanType DishSpongeType)
        (canContain GarbageCanType EggType)
        (canContain GarbageCanType SoapBottleType)
        (canContain GarbageCanType LettuceType)
        (canContain GarbageCanType PotatoType)
        (canContain GarbageCanType TomatoType)
        (canContain GarbageCanType AppleType)
        (canContain CounterTopType SaltShakerType)
        (canContain CounterTopType BreadType)
        (canContain CounterTopType DishSpongeType)
        (canContain CounterTopType BowlType)
        (canContain CounterTopType PotType)
        (canContain CounterTopType MugType)
        (canContain CounterTopType EggType)
        (canContain CounterTopType ForkType)
        (canContain CounterTopType SpoonType)
        (canContain CounterTopType SoapBottleType)
        (canContain CounterTopType LettuceType)
        (canContain CounterTopType PotatoType)
        (canContain CounterTopType ButterKnifeType)
        (canContain CounterTopType CupType)
        (canContain CounterTopType PlateType)
        (canContain CounterTopType PepperShakerType)
        (canContain CounterTopType TomatoType)
        (canContain CounterTopType KnifeType)
        (canContain CounterTopType AppleType)
        (canContain CounterTopType PanType)
        (canContain CounterTopType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain StoveBurnerType PotType)
        (canContain StoveBurnerType PanType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (canContain DiningTableType SaltShakerType)
        (canContain DiningTableType BreadType)
        (canContain DiningTableType DishSpongeType)
        (canContain DiningTableType BowlType)
        (canContain DiningTableType PotType)
        (canContain DiningTableType MugType)
        (canContain DiningTableType EggType)
        (canContain DiningTableType ForkType)
        (canContain DiningTableType SpoonType)
        (canContain DiningTableType SoapBottleType)
        (canContain DiningTableType LettuceType)
        (canContain DiningTableType PotatoType)
        (canContain DiningTableType ButterKnifeType)
        (canContain DiningTableType CupType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType PepperShakerType)
        (canContain DiningTableType TomatoType)
        (canContain DiningTableType KnifeType)
        (canContain DiningTableType AppleType)
        (canContain DiningTableType PanType)
        (canContain DiningTableType SpatulaType)
        (canContain DrawerType SaltShakerType)
        (canContain DrawerType DishSpongeType)
        (canContain DrawerType ForkType)
        (canContain DrawerType SpoonType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ButterKnifeType)
        (canContain DrawerType PepperShakerType)
        (canContain DrawerType KnifeType)
        (canContain DrawerType SpatulaType)
        (canContain CabinetType SaltShakerType)
        (canContain CabinetType DishSpongeType)
        (canContain CabinetType BowlType)
        (canContain CabinetType PotType)
        (canContain CabinetType MugType)
        (canContain CabinetType SoapBottleType)
        (canContain CabinetType CupType)
        (canContain CabinetType PlateType)
        (canContain CabinetType PepperShakerType)
        (canContain CabinetType PanType)
        (pickupable Plate_bar__minus_00_dot_45_bar__plus_00_dot_89_bar__plus_02_dot_93)
        (pickupable Knife_bar__minus_02_dot_48_bar__plus_00_dot_93_bar__plus_02_dot_71)
        (pickupable Bowl_bar__minus_01_dot_75_bar__plus_01_dot_77_bar__plus_00_dot_25)
        (pickupable Bread_bar__minus_02_dot_81_bar__plus_01_dot_24_bar__plus_04_dot_43)
        (pickupable Cup_bar__minus_02_dot_89_bar__plus_00_dot_63_bar__plus_04_dot_65)
        (pickupable Knife_bar__minus_00_dot_49_bar__plus_00_dot_93_bar__plus_02_dot_53)
        (pickupable Lettuce_bar__minus_02_dot_81_bar__plus_00_dot_71_bar__plus_04_dot_35)
        (pickupable Bowl_bar__minus_02_dot_79_bar__plus_00_dot_30_bar__plus_04_dot_50)
        (pickupable Plate_bar__minus_02_dot_79_bar__plus_00_dot_90_bar__plus_01_dot_91)
        (pickupable Pan_bar__minus_00_dot_25_bar__plus_00_dot_93_bar__plus_04_dot_65)
        (pickupable Spoon_bar__minus_02_dot_69_bar__plus_00_dot_90_bar__plus_02_dot_18)
        (pickupable Plate_bar__minus_01_dot_19_bar__plus_01_dot_78_bar__plus_00_dot_21)
        (pickupable Apple_bar__minus_01_dot_09_bar__plus_00_dot_85_bar__plus_00_dot_28)
        (pickupable PepperShaker_bar__minus_02_dot_27_bar__plus_00_dot_66_bar__plus_00_dot_36)
        (pickupable Lettuce_bar__minus_00_dot_23_bar__plus_00_dot_98_bar__plus_02_dot_53)
        (pickupable SaltShaker_bar__minus_02_dot_44_bar__plus_00_dot_92_bar__plus_00_dot_43)
        (pickupable Tomato_bar__minus_00_dot_66_bar__plus_00_dot_96_bar__plus_03_dot_33)
        (pickupable DishSponge_bar__minus_01_dot_09_bar__plus_00_dot_80_bar__plus_00_dot_45)
        (pickupable Tomato_bar__minus_02_dot_86_bar__plus_01_dot_00_bar__plus_03_dot_21)
        (pickupable Fork_bar__minus_02_dot_38_bar__plus_00_dot_90_bar__plus_02_dot_57)
        (pickupable Egg_bar__minus_02_dot_76_bar__plus_01_dot_22_bar__plus_04_dot_20)
        (pickupable Mug_bar__minus_02_dot_78_bar__plus_01_dot_00_bar__plus_03_dot_71)
        (pickupable Potato_bar__minus_02_dot_82_bar__plus_00_dot_08_bar__plus_00_dot_45)
        (pickupable Pot_bar__minus_00_dot_54_bar__plus_00_dot_93_bar__plus_04_dot_27)
        (pickupable Spatula_bar__minus_00_dot_49_bar__plus_00_dot_92_bar__plus_02_dot_33)
        (pickupable ButterKnife_bar__minus_00_dot_23_bar__plus_00_dot_90_bar__plus_03_dot_13)
        (pickupable ButterKnife_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__plus_03_dot_54)
        (pickupable Bowl_bar__minus_02_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_31)
        (pickupable Knife_bar__minus_00_dot_49_bar__plus_00_dot_69_bar__plus_00_dot_46)
        (pickupable SoapBottle_bar__minus_03_dot_00_bar__plus_00_dot_90_bar__plus_02_dot_31)
        (isReceptacleObject Plate_bar__minus_00_dot_45_bar__plus_00_dot_89_bar__plus_02_dot_93)
        (isReceptacleObject Bowl_bar__minus_01_dot_75_bar__plus_01_dot_77_bar__plus_00_dot_25)
        (isReceptacleObject Cup_bar__minus_02_dot_89_bar__plus_00_dot_63_bar__plus_04_dot_65)
        (isReceptacleObject Bowl_bar__minus_02_dot_79_bar__plus_00_dot_30_bar__plus_04_dot_50)
        (isReceptacleObject Plate_bar__minus_02_dot_79_bar__plus_00_dot_90_bar__plus_01_dot_91)
        (isReceptacleObject Pan_bar__minus_00_dot_25_bar__plus_00_dot_93_bar__plus_04_dot_65)
        (isReceptacleObject Plate_bar__minus_01_dot_19_bar__plus_01_dot_78_bar__plus_00_dot_21)
        (isReceptacleObject Mug_bar__minus_02_dot_78_bar__plus_01_dot_00_bar__plus_03_dot_71)
        (isReceptacleObject Pot_bar__minus_00_dot_54_bar__plus_00_dot_93_bar__plus_04_dot_27)
        (isReceptacleObject Bowl_bar__minus_02_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_31)
        (openable Cabinet_bar__minus_00_dot_52_bar__plus_02_dot_05_bar__plus_00_dot_39)
        (openable Microwave_bar__minus_00_dot_36_bar__plus_00_dot_91_bar__plus_00_dot_22)
        (openable Cabinet_bar__minus_01_dot_48_bar__plus_02_dot_05_bar__plus_00_dot_39)
        (openable Cabinet_bar__minus_00_dot_48_bar__plus_02_dot_05_bar__plus_00_dot_39)
        (openable Fridge_bar__minus_02_dot_80_bar__plus_00_dot_00_bar__plus_04_dot_43)
        (openable Cabinet_bar__minus_02_dot_47_bar__plus_02_dot_05_bar__plus_00_dot_39)
        (openable Drawer_bar__minus_02_dot_32_bar__plus_00_dot_70_bar__plus_00_dot_41)
        (openable Drawer_bar__minus_01_dot_82_bar__plus_00_dot_70_bar__plus_00_dot_41)
        (openable Cabinet_bar__minus_01_dot_51_bar__plus_02_dot_05_bar__plus_00_dot_39)
        (openable Drawer_bar__minus_00_dot_33_bar__plus_00_dot_70_bar__plus_00_dot_41)
        
        (atLocation agent1 loc_bar__minus_7_bar_13_bar_3_bar_30)
        
        (cleanable Plate_bar__minus_00_dot_45_bar__plus_00_dot_89_bar__plus_02_dot_93)
        (cleanable Knife_bar__minus_02_dot_48_bar__plus_00_dot_93_bar__plus_02_dot_71)
        (cleanable Bowl_bar__minus_01_dot_75_bar__plus_01_dot_77_bar__plus_00_dot_25)
        (cleanable Cup_bar__minus_02_dot_89_bar__plus_00_dot_63_bar__plus_04_dot_65)
        (cleanable Knife_bar__minus_00_dot_49_bar__plus_00_dot_93_bar__plus_02_dot_53)
        (cleanable Lettuce_bar__minus_02_dot_81_bar__plus_00_dot_71_bar__plus_04_dot_35)
        (cleanable Bowl_bar__minus_02_dot_79_bar__plus_00_dot_30_bar__plus_04_dot_50)
        (cleanable Plate_bar__minus_02_dot_79_bar__plus_00_dot_90_bar__plus_01_dot_91)
        (cleanable Pan_bar__minus_00_dot_25_bar__plus_00_dot_93_bar__plus_04_dot_65)
        (cleanable Spoon_bar__minus_02_dot_69_bar__plus_00_dot_90_bar__plus_02_dot_18)
        (cleanable Plate_bar__minus_01_dot_19_bar__plus_01_dot_78_bar__plus_00_dot_21)
        (cleanable Apple_bar__minus_01_dot_09_bar__plus_00_dot_85_bar__plus_00_dot_28)
        (cleanable Lettuce_bar__minus_00_dot_23_bar__plus_00_dot_98_bar__plus_02_dot_53)
        (cleanable Tomato_bar__minus_00_dot_66_bar__plus_00_dot_96_bar__plus_03_dot_33)
        (cleanable DishSponge_bar__minus_01_dot_09_bar__plus_00_dot_80_bar__plus_00_dot_45)
        (cleanable Tomato_bar__minus_02_dot_86_bar__plus_01_dot_00_bar__plus_03_dot_21)
        (cleanable Fork_bar__minus_02_dot_38_bar__plus_00_dot_90_bar__plus_02_dot_57)
        (cleanable Egg_bar__minus_02_dot_76_bar__plus_01_dot_22_bar__plus_04_dot_20)
        (cleanable Mug_bar__minus_02_dot_78_bar__plus_01_dot_00_bar__plus_03_dot_71)
        (cleanable Potato_bar__minus_02_dot_82_bar__plus_00_dot_08_bar__plus_00_dot_45)
        (cleanable Pot_bar__minus_00_dot_54_bar__plus_00_dot_93_bar__plus_04_dot_27)
        (cleanable Spatula_bar__minus_00_dot_49_bar__plus_00_dot_92_bar__plus_02_dot_33)
        (cleanable ButterKnife_bar__minus_00_dot_23_bar__plus_00_dot_90_bar__plus_03_dot_13)
        (cleanable ButterKnife_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__plus_03_dot_54)
        (cleanable Bowl_bar__minus_02_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_31)
        (cleanable Knife_bar__minus_00_dot_49_bar__plus_00_dot_69_bar__plus_00_dot_46)
        
        (heatable Plate_bar__minus_00_dot_45_bar__plus_00_dot_89_bar__plus_02_dot_93)
        (heatable Bread_bar__minus_02_dot_81_bar__plus_01_dot_24_bar__plus_04_dot_43)
        (heatable Cup_bar__minus_02_dot_89_bar__plus_00_dot_63_bar__plus_04_dot_65)
        (heatable Plate_bar__minus_02_dot_79_bar__plus_00_dot_90_bar__plus_01_dot_91)
        (heatable Plate_bar__minus_01_dot_19_bar__plus_01_dot_78_bar__plus_00_dot_21)
        (heatable Apple_bar__minus_01_dot_09_bar__plus_00_dot_85_bar__plus_00_dot_28)
        (heatable Tomato_bar__minus_00_dot_66_bar__plus_00_dot_96_bar__plus_03_dot_33)
        (heatable Tomato_bar__minus_02_dot_86_bar__plus_01_dot_00_bar__plus_03_dot_21)
        (heatable Egg_bar__minus_02_dot_76_bar__plus_01_dot_22_bar__plus_04_dot_20)
        (heatable Mug_bar__minus_02_dot_78_bar__plus_01_dot_00_bar__plus_03_dot_71)
        (heatable Potato_bar__minus_02_dot_82_bar__plus_00_dot_08_bar__plus_00_dot_45)
        (coolable Plate_bar__minus_00_dot_45_bar__plus_00_dot_89_bar__plus_02_dot_93)
        (coolable Bowl_bar__minus_01_dot_75_bar__plus_01_dot_77_bar__plus_00_dot_25)
        (coolable Bread_bar__minus_02_dot_81_bar__plus_01_dot_24_bar__plus_04_dot_43)
        (coolable Cup_bar__minus_02_dot_89_bar__plus_00_dot_63_bar__plus_04_dot_65)
        (coolable Lettuce_bar__minus_02_dot_81_bar__plus_00_dot_71_bar__plus_04_dot_35)
        (coolable Bowl_bar__minus_02_dot_79_bar__plus_00_dot_30_bar__plus_04_dot_50)
        (coolable Plate_bar__minus_02_dot_79_bar__plus_00_dot_90_bar__plus_01_dot_91)
        (coolable Pan_bar__minus_00_dot_25_bar__plus_00_dot_93_bar__plus_04_dot_65)
        (coolable Plate_bar__minus_01_dot_19_bar__plus_01_dot_78_bar__plus_00_dot_21)
        (coolable Apple_bar__minus_01_dot_09_bar__plus_00_dot_85_bar__plus_00_dot_28)
        (coolable Lettuce_bar__minus_00_dot_23_bar__plus_00_dot_98_bar__plus_02_dot_53)
        (coolable Tomato_bar__minus_00_dot_66_bar__plus_00_dot_96_bar__plus_03_dot_33)
        (coolable Tomato_bar__minus_02_dot_86_bar__plus_01_dot_00_bar__plus_03_dot_21)
        (coolable Egg_bar__minus_02_dot_76_bar__plus_01_dot_22_bar__plus_04_dot_20)
        (coolable Mug_bar__minus_02_dot_78_bar__plus_01_dot_00_bar__plus_03_dot_71)
        (coolable Potato_bar__minus_02_dot_82_bar__plus_00_dot_08_bar__plus_00_dot_45)
        (coolable Pot_bar__minus_00_dot_54_bar__plus_00_dot_93_bar__plus_04_dot_27)
        (coolable Bowl_bar__minus_02_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_31)
        
        (isCool Bread_bar__minus_02_dot_81_bar__plus_01_dot_24_bar__plus_04_dot_43)
        
        
        
        (sliceable Bread_bar__minus_02_dot_81_bar__plus_01_dot_24_bar__plus_04_dot_43)
        (sliceable Lettuce_bar__minus_02_dot_81_bar__plus_00_dot_71_bar__plus_04_dot_35)
        (sliceable Apple_bar__minus_01_dot_09_bar__plus_00_dot_85_bar__plus_00_dot_28)
        (sliceable Lettuce_bar__minus_00_dot_23_bar__plus_00_dot_98_bar__plus_02_dot_53)
        (sliceable Tomato_bar__minus_00_dot_66_bar__plus_00_dot_96_bar__plus_03_dot_33)
        (sliceable Tomato_bar__minus_02_dot_86_bar__plus_01_dot_00_bar__plus_03_dot_21)
        (sliceable Egg_bar__minus_02_dot_76_bar__plus_01_dot_22_bar__plus_04_dot_20)
        (sliceable Potato_bar__minus_02_dot_82_bar__plus_00_dot_08_bar__plus_00_dot_45)
        
        (inReceptacleObject DishSponge_bar__minus_01_dot_09_bar__plus_00_dot_80_bar__plus_00_dot_45 Plate_bar__minus_01_dot_19_bar__plus_01_dot_78_bar__plus_00_dot_21)
        (inReceptacleObject Sink_bar__minus_01_dot_11_bar__plus_00_dot_93_bar__plus_00_dot_34 Plate_bar__minus_01_dot_19_bar__plus_01_dot_78_bar__plus_00_dot_21)
        (inReceptacle Knife_bar__minus_00_dot_49_bar__plus_00_dot_69_bar__plus_00_dot_46 Drawer_bar__minus_00_dot_33_bar__plus_00_dot_70_bar__plus_00_dot_41)
        (inReceptacle Bowl_bar__minus_01_dot_75_bar__plus_01_dot_77_bar__plus_00_dot_25 Cabinet_bar__minus_01_dot_51_bar__plus_02_dot_05_bar__plus_00_dot_39)
        (inReceptacle SaltShaker_bar__minus_02_dot_44_bar__plus_00_dot_92_bar__plus_00_dot_43 CounterTop_bar__minus_01_dot_37_bar__plus_00_dot_95_bar__plus_00_dot_35)
        (inReceptacle Pan_bar__minus_00_dot_25_bar__plus_00_dot_93_bar__plus_04_dot_65 StoveBurner_bar__minus_00_dot_25_bar__plus_00_dot_92_bar__plus_04_dot_67)
        (inReceptacle Pot_bar__minus_00_dot_54_bar__plus_00_dot_93_bar__plus_04_dot_27 StoveBurner_bar__minus_00_dot_54_bar__plus_00_dot_92_bar__plus_04_dot_27)
        (inReceptacle Plate_bar__minus_02_dot_79_bar__plus_00_dot_90_bar__plus_01_dot_91 DiningTable_bar__minus_02_dot_76_bar__plus_00_dot_01_bar__plus_02_dot_36)
        (inReceptacle Knife_bar__minus_02_dot_48_bar__plus_00_dot_93_bar__plus_02_dot_71 DiningTable_bar__minus_02_dot_76_bar__plus_00_dot_01_bar__plus_02_dot_36)
        (inReceptacle Spoon_bar__minus_02_dot_69_bar__plus_00_dot_90_bar__plus_02_dot_18 DiningTable_bar__minus_02_dot_76_bar__plus_00_dot_01_bar__plus_02_dot_36)
        (inReceptacle SoapBottle_bar__minus_03_dot_00_bar__plus_00_dot_90_bar__plus_02_dot_31 DiningTable_bar__minus_02_dot_76_bar__plus_00_dot_01_bar__plus_02_dot_36)
        (inReceptacle Bowl_bar__minus_02_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_31 DiningTable_bar__minus_02_dot_76_bar__plus_00_dot_01_bar__plus_02_dot_36)
        (inReceptacle Fork_bar__minus_02_dot_38_bar__plus_00_dot_90_bar__plus_02_dot_57 DiningTable_bar__minus_02_dot_76_bar__plus_00_dot_01_bar__plus_02_dot_36)
        (inReceptacle Plate_bar__minus_00_dot_45_bar__plus_00_dot_89_bar__plus_02_dot_93 DiningTable_bar__minus_00_dot_47_bar__plus_00_dot_01_bar__plus_03_dot_00)
        (inReceptacle Lettuce_bar__minus_00_dot_23_bar__plus_00_dot_98_bar__plus_02_dot_53 DiningTable_bar__minus_00_dot_47_bar__plus_00_dot_01_bar__plus_03_dot_00)
        (inReceptacle Spatula_bar__minus_00_dot_49_bar__plus_00_dot_92_bar__plus_02_dot_33 DiningTable_bar__minus_00_dot_47_bar__plus_00_dot_01_bar__plus_03_dot_00)
        (inReceptacle ButterKnife_bar__minus_00_dot_23_bar__plus_00_dot_90_bar__plus_03_dot_13 DiningTable_bar__minus_00_dot_47_bar__plus_00_dot_01_bar__plus_03_dot_00)
        (inReceptacle ButterKnife_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__plus_03_dot_54 DiningTable_bar__minus_00_dot_47_bar__plus_00_dot_01_bar__plus_03_dot_00)
        (inReceptacle Knife_bar__minus_00_dot_49_bar__plus_00_dot_93_bar__plus_02_dot_53 DiningTable_bar__minus_00_dot_47_bar__plus_00_dot_01_bar__plus_03_dot_00)
        (inReceptacle Tomato_bar__minus_00_dot_66_bar__plus_00_dot_96_bar__plus_03_dot_33 DiningTable_bar__minus_00_dot_47_bar__plus_00_dot_01_bar__plus_03_dot_00)
        (inReceptacle Pan_bar__minus_00_dot_25_bar__plus_00_dot_93_bar__plus_04_dot_65 StoveBurner_bar__minus_00_dot_25_bar__plus_00_dot_92_bar__plus_04_dot_27)
        (inReceptacle Tomato_bar__minus_02_dot_86_bar__plus_01_dot_00_bar__plus_03_dot_21 CounterTop_bar__minus_02_dot_78_bar__plus_00_dot_97_bar__plus_03_dot_49)
        (inReceptacle Mug_bar__minus_02_dot_78_bar__plus_01_dot_00_bar__plus_03_dot_71 CounterTop_bar__minus_02_dot_78_bar__plus_00_dot_97_bar__plus_03_dot_49)
        (inReceptacle PepperShaker_bar__minus_02_dot_27_bar__plus_00_dot_66_bar__plus_00_dot_36 Drawer_bar__minus_02_dot_32_bar__plus_00_dot_70_bar__plus_00_dot_41)
        (inReceptacle Potato_bar__minus_02_dot_82_bar__plus_00_dot_08_bar__plus_00_dot_45 GarbageCan_bar__minus_02_dot_82_bar__plus_00_dot_00_bar__plus_00_dot_37)
        (inReceptacle Bread_bar__minus_02_dot_81_bar__plus_01_dot_24_bar__plus_04_dot_43 Fridge_bar__minus_02_dot_80_bar__plus_00_dot_00_bar__plus_04_dot_43)
        (inReceptacle Egg_bar__minus_02_dot_76_bar__plus_01_dot_22_bar__plus_04_dot_20 Fridge_bar__minus_02_dot_80_bar__plus_00_dot_00_bar__plus_04_dot_43)
        (inReceptacle Cup_bar__minus_02_dot_89_bar__plus_00_dot_63_bar__plus_04_dot_65 Fridge_bar__minus_02_dot_80_bar__plus_00_dot_00_bar__plus_04_dot_43)
        (inReceptacle Lettuce_bar__minus_02_dot_81_bar__plus_00_dot_71_bar__plus_04_dot_35 Fridge_bar__minus_02_dot_80_bar__plus_00_dot_00_bar__plus_04_dot_43)
        (inReceptacle Bowl_bar__minus_02_dot_79_bar__plus_00_dot_30_bar__plus_04_dot_50 Fridge_bar__minus_02_dot_80_bar__plus_00_dot_00_bar__plus_04_dot_43)
        (inReceptacle DishSponge_bar__minus_01_dot_09_bar__plus_00_dot_80_bar__plus_00_dot_45 Sink_bar__minus_01_dot_11_bar__plus_00_dot_93_bar__plus_00_dot_34_bar_SinkBasin)
        (inReceptacle Plate_bar__minus_01_dot_19_bar__plus_01_dot_78_bar__plus_00_dot_21 Sink_bar__minus_01_dot_11_bar__plus_00_dot_93_bar__plus_00_dot_34_bar_SinkBasin)
        (inReceptacle Apple_bar__minus_01_dot_09_bar__plus_00_dot_85_bar__plus_00_dot_28 Sink_bar__minus_01_dot_11_bar__plus_00_dot_93_bar__plus_00_dot_34_bar_SinkBasin)
        (inReceptacle Mug_bar__minus_02_dot_78_bar__plus_01_dot_00_bar__plus_03_dot_71 CoffeeMachine_bar__minus_02_dot_96_bar__plus_00_dot_93_bar__plus_03_dot_61)
        
        
        (receptacleAtLocation Cabinet_bar__minus_00_dot_48_bar__plus_02_dot_05_bar__plus_00_dot_39 loc_bar__minus_3_bar_5_bar_2_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_52_bar__plus_02_dot_05_bar__plus_00_dot_39 loc_bar__minus_3_bar_4_bar_2_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_56_bar__plus_00_dot_49_bar__plus_00_dot_65 loc_bar__minus_3_bar_4_bar_2_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_00_dot_60_bar__plus_00_dot_49_bar__plus_00_dot_65 loc_bar__minus_3_bar_4_bar_2_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_48_bar__plus_02_dot_05_bar__plus_00_dot_39 loc_bar__minus_5_bar_4_bar_2_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_51_bar__plus_02_dot_05_bar__plus_00_dot_39 loc_bar__minus_7_bar_4_bar_2_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_55_bar__plus_00_dot_49_bar__plus_00_dot_65 loc_bar__minus_7_bar_4_bar_2_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_01_dot_59_bar__plus_00_dot_49_bar__plus_00_dot_65 loc_bar__minus_7_bar_4_bar_2_bar_60)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_47_bar__plus_02_dot_05_bar__plus_00_dot_39 loc_bar__minus_8_bar_4_bar_2_bar__minus_30)
        (receptacleAtLocation Cabinet_bar__minus_02_dot_55_bar__plus_00_dot_49_bar__plus_00_dot_65 loc_bar__minus_10_bar_4_bar_2_bar_60)
        (receptacleAtLocation CoffeeMachine_bar__minus_02_dot_96_bar__plus_00_dot_93_bar__plus_03_dot_61 loc_bar__minus_8_bar_14_bar_3_bar_30)
        (receptacleAtLocation CounterTop_bar__minus_01_dot_37_bar__plus_00_dot_95_bar__plus_00_dot_35 loc_bar__minus_8_bar_5_bar_2_bar_45)
        (receptacleAtLocation CounterTop_bar__minus_02_dot_78_bar__plus_00_dot_97_bar__plus_03_dot_49 loc_bar__minus_8_bar_14_bar_3_bar_45)
        (receptacleAtLocation DiningTable_bar__minus_00_dot_47_bar__plus_00_dot_01_bar__plus_03_dot_00 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (receptacleAtLocation DiningTable_bar__minus_02_dot_76_bar__plus_00_dot_01_bar__plus_02_dot_36 loc_bar__minus_8_bar_10_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_33_bar__plus_00_dot_70_bar__plus_00_dot_41 loc_bar__minus_3_bar_6_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_82_bar__plus_00_dot_70_bar__plus_00_dot_41 loc_bar__minus_4_bar_5_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_32_bar__plus_00_dot_70_bar__plus_00_dot_41 loc_bar__minus_6_bar_5_bar_2_bar_45)
        (receptacleAtLocation Fridge_bar__minus_02_dot_80_bar__plus_00_dot_00_bar__plus_04_dot_43 loc_bar__minus_7_bar_17_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_02_dot_82_bar__plus_00_dot_00_bar__plus_00_dot_37 loc_bar__minus_10_bar_4_bar_2_bar_60)
        (receptacleAtLocation Microwave_bar__minus_00_dot_36_bar__plus_00_dot_91_bar__plus_00_dot_22 loc_bar__minus_3_bar_5_bar_2_bar_30)
        (receptacleAtLocation Sink_bar__minus_01_dot_11_bar__plus_00_dot_93_bar__plus_00_dot_34_bar_SinkBasin loc_bar__minus_3_bar_5_bar_2_bar_45)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_25_bar__plus_00_dot_92_bar__plus_04_dot_27 loc_bar__minus_4_bar_17_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_25_bar__plus_00_dot_92_bar__plus_04_dot_67 loc_bar__minus_4_bar_17_bar_1_bar_45)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_54_bar__plus_00_dot_92_bar__plus_04_dot_27 loc_bar__minus_4_bar_17_bar_1_bar_60)
        (receptacleAtLocation StoveBurner_bar__minus_00_dot_54_bar__plus_00_dot_92_bar__plus_04_dot_67 loc_bar__minus_4_bar_17_bar_1_bar_60)
        (receptacleAtLocation Toaster_bar__minus_00_dot_29_bar__plus_00_dot_89_bar__plus_03_dot_56 loc_bar__minus_4_bar_14_bar_1_bar_45)
        (objectAtLocation Bowl_bar__minus_02_dot_58_bar__plus_00_dot_90_bar__plus_02_dot_31 loc_bar__minus_8_bar_10_bar_3_bar_60)
        (objectAtLocation ButterKnife_bar__minus_00_dot_57_bar__plus_00_dot_90_bar__plus_03_dot_54 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (objectAtLocation Tomato_bar__minus_00_dot_66_bar__plus_00_dot_96_bar__plus_03_dot_33 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (objectAtLocation Knife_bar__minus_02_dot_48_bar__plus_00_dot_93_bar__plus_02_dot_71 loc_bar__minus_8_bar_10_bar_3_bar_60)
        (objectAtLocation Lettuce_bar__minus_02_dot_81_bar__plus_00_dot_71_bar__plus_04_dot_35 loc_bar__minus_7_bar_17_bar_3_bar_60)
        (objectAtLocation Plate_bar__minus_00_dot_45_bar__plus_00_dot_89_bar__plus_02_dot_93 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (objectAtLocation Bowl_bar__minus_01_dot_75_bar__plus_01_dot_77_bar__plus_00_dot_25 loc_bar__minus_7_bar_4_bar_2_bar__minus_30)
        (objectAtLocation Knife_bar__minus_00_dot_49_bar__plus_00_dot_93_bar__plus_02_dot_53 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (objectAtLocation Plate_bar__minus_01_dot_19_bar__plus_01_dot_78_bar__plus_00_dot_21 loc_bar__minus_3_bar_5_bar_2_bar_45)
        (objectAtLocation Sink_bar__minus_01_dot_11_bar__plus_00_dot_93_bar__plus_00_dot_34 loc_bar__minus_4_bar_4_bar_2_bar_45)
        (objectAtLocation Egg_bar__minus_02_dot_76_bar__plus_01_dot_22_bar__plus_04_dot_20 loc_bar__minus_7_bar_17_bar_3_bar_60)
        (objectAtLocation StoveKnob_bar__minus_00_dot_06_bar__plus_01_dot_09_bar__plus_04_dot_63 loc_bar__minus_4_bar_17_bar_1_bar_30)
        (objectAtLocation StoveKnob_bar__minus_00_dot_06_bar__plus_01_dot_09_bar__plus_04_dot_78 loc_bar__minus_4_bar_17_bar_1_bar_30)
        (objectAtLocation StoveKnob_bar__minus_00_dot_06_bar__plus_01_dot_09_bar__plus_04_dot_38 loc_bar__minus_4_bar_17_bar_1_bar_30)
        (objectAtLocation StoveKnob_bar__minus_00_dot_06_bar__plus_01_dot_09_bar__plus_04_dot_22 loc_bar__minus_4_bar_17_bar_1_bar_30)
        (objectAtLocation Plate_bar__minus_02_dot_79_bar__plus_00_dot_90_bar__plus_01_dot_91 loc_bar__minus_8_bar_10_bar_3_bar_60)
        (objectAtLocation Fork_bar__minus_02_dot_38_bar__plus_00_dot_90_bar__plus_02_dot_57 loc_bar__minus_8_bar_10_bar_3_bar_60)
        (objectAtLocation Bread_bar__minus_02_dot_81_bar__plus_01_dot_24_bar__plus_04_dot_43 loc_bar__minus_7_bar_17_bar_3_bar_60)
        (objectAtLocation Apple_bar__minus_01_dot_09_bar__plus_00_dot_85_bar__plus_00_dot_28 loc_bar__minus_3_bar_5_bar_2_bar_45)
        (objectAtLocation Lettuce_bar__minus_00_dot_23_bar__plus_00_dot_98_bar__plus_02_dot_53 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (objectAtLocation LightSwitch_bar__minus_00_dot_07_bar__plus_01_dot_45_bar__plus_02_dot_00 loc_bar__minus_3_bar_7_bar_1_bar_15)
        (objectAtLocation Window_bar__minus_03_dot_08_bar__plus_01_dot_59_bar__plus_01_dot_36 loc_bar__minus_10_bar_5_bar_3_bar_0)
        (objectAtLocation Spatula_bar__minus_00_dot_49_bar__plus_00_dot_92_bar__plus_02_dot_33 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (objectAtLocation Knife_bar__minus_00_dot_49_bar__plus_00_dot_69_bar__plus_00_dot_46 loc_bar__minus_3_bar_6_bar_2_bar_45)
        (objectAtLocation Potato_bar__minus_02_dot_82_bar__plus_00_dot_08_bar__plus_00_dot_45 loc_bar__minus_10_bar_4_bar_2_bar_60)
        (objectAtLocation Tomato_bar__minus_02_dot_86_bar__plus_01_dot_00_bar__plus_03_dot_21 loc_bar__minus_8_bar_14_bar_3_bar_45)
        (objectAtLocation Cup_bar__minus_02_dot_89_bar__plus_00_dot_63_bar__plus_04_dot_65 loc_bar__minus_7_bar_17_bar_3_bar_60)
        (objectAtLocation SaltShaker_bar__minus_02_dot_44_bar__plus_00_dot_92_bar__plus_00_dot_43 loc_bar__minus_8_bar_5_bar_2_bar_45)
        (objectAtLocation PepperShaker_bar__minus_02_dot_27_bar__plus_00_dot_66_bar__plus_00_dot_36 loc_bar__minus_6_bar_5_bar_2_bar_45)
        (objectAtLocation ButterKnife_bar__minus_00_dot_23_bar__plus_00_dot_90_bar__plus_03_dot_13 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (objectAtLocation SoapBottle_bar__minus_03_dot_00_bar__plus_00_dot_90_bar__plus_02_dot_31 loc_bar__minus_8_bar_10_bar_3_bar_60)
        (objectAtLocation Pan_bar__minus_00_dot_25_bar__plus_00_dot_93_bar__plus_04_dot_65 loc_bar__minus_4_bar_17_bar_1_bar_45)
        (objectAtLocation DishSponge_bar__minus_01_dot_09_bar__plus_00_dot_80_bar__plus_00_dot_45 loc_bar__minus_3_bar_5_bar_2_bar_45)
        (objectAtLocation Pot_bar__minus_00_dot_54_bar__plus_00_dot_93_bar__plus_04_dot_27 loc_bar__minus_4_bar_17_bar_1_bar_60)
        (objectAtLocation Spoon_bar__minus_02_dot_69_bar__plus_00_dot_90_bar__plus_02_dot_18 loc_bar__minus_8_bar_10_bar_3_bar_60)
        (objectAtLocation Mug_bar__minus_02_dot_78_bar__plus_01_dot_00_bar__plus_03_dot_71 loc_bar__minus_8_bar_14_bar_3_bar_30)
        (objectAtLocation Bowl_bar__minus_02_dot_79_bar__plus_00_dot_30_bar__plus_04_dot_50 loc_bar__minus_7_bar_17_bar_3_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o - object)
                                (and
                                    (sliceable ?o)
                                    (isSliced ?o)
                                    (heatable ?o)
                                    (objectType ?o BreadType)
                                    (receptacleType ?r FridgeType)
                                    (isHot ?o)
                                    (inReceptacle ?o ?r)
                                )
                            )
                        )
                    )
                )
            )
            