{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 32}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-5|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-0.843211888, -0.843211888, -8.64457892, -8.64457892, 3.2720388, 3.2720388]], "coordinateReceptacleObjectId": ["SinkBasin", [-0.5932, -0.5932, -8.1056, -8.1056, 3.0156, 3.0156]], "forceVisible": true, "objectId": "Egg|-00.21|+00.82|-02.16"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-5|3|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.936, -4.936, -6.724, -6.724, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.23|+00.90|-01.68"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-3|-3|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-0.843211888, -0.843211888, -8.64457892, -8.64457892, 3.2720388, 3.2720388]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-5.98224832, -5.98224832, -2.812, -2.812, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|-00.21|+00.82|-02.16", "receptacleObjectId": "<PERSON><PERSON>|-01.50|+00.00|-00.70"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.21|+00.82|-02.16"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [143, 132, 160, 153], "mask": [[39452, 1], [39749, 7], [40047, 11], [40346, 13], [40645, 15], [40945, 15], [41244, 17], [41544, 17], [41844, 17], [42143, 18], [42443, 18], [42743, 18], [43043, 18], [43344, 17], [43644, 17], [43944, 16], [44245, 15], [44545, 14], [44846, 13], [45147, 11], [45448, 9], [45749, 6]], "point": [151, 141]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.21|+00.82|-02.16", "placeStationary": true, "receptacleObjectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 85, 133, 185], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54087, 31], [54388, 30], [54688, 29], [54988, 29], [55288, 29]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 85, 133, 185], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 50], [39057, 77], [39300, 49], [39359, 75], [39600, 48], [39660, 74], [39900, 47], [39961, 73], [40200, 47], [40262, 72], [40500, 46], [40563, 71], [40800, 46], [40863, 71], [41100, 45], [41164, 70], [41400, 45], [41464, 70], [41700, 45], [41765, 69], [42000, 45], [42065, 69], [42300, 45], [42365, 69], [42600, 45], [42666, 68], [42900, 45], [42966, 68], [43200, 45], [43266, 68], [43500, 45], [43566, 68], [43800, 46], [43866, 68], [44100, 46], [44165, 69], [44400, 46], [44465, 69], [44700, 47], [44765, 69], [45000, 47], [45064, 70], [45300, 48], [45364, 70], [45600, 49], [45663, 71], [45900, 50], [45962, 72], [46200, 52], [46261, 73], [46500, 55], [46558, 75], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54087, 31], [54388, 30], [54688, 29], [54988, 29], [55288, 29]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.936, -4.936, -6.724, -6.724, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.936, -4.936, -6.724, -6.724, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.21|+00.82|-02.16"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [45, 131, 65, 156], "mask": [[39050, 7], [39349, 10], [39648, 12], [39947, 14], [40247, 15], [40546, 17], [40846, 17], [41145, 19], [41445, 19], [41745, 20], [42045, 20], [42345, 20], [42645, 21], [42945, 21], [43245, 21], [43545, 21], [43846, 20], [44146, 19], [44446, 19], [44747, 18], [45047, 17], [45348, 16], [45649, 14], [45950, 12], [46252, 9], [46555, 3]], "point": [55, 142]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 85, 133, 185], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54087, 31], [54388, 30], [54688, 29], [54988, 29], [55288, 29]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-01.50|+00.00|-00.70"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 235], "mask": [[0, 41700], [41701, 299], [42002, 298], [42303, 297], [42604, 296], [42905, 295], [43206, 294], [43507, 293], [43808, 292], [44109, 291], [44410, 290], [44710, 290], [45011, 289], [45312, 288], [45613, 287], [45914, 286], [46215, 285], [46516, 284], [46817, 283], [47118, 282], [47419, 281], [47720, 280], [48021, 279], [48322, 278], [48623, 277], [48923, 277], [49224, 276], [49525, 275], [49826, 274], [50127, 273], [50428, 272], [50729, 271], [51030, 270], [51331, 269], [51632, 268], [51933, 267], [52234, 265], [52535, 263], [52836, 261], [53136, 260], [53437, 257], [53738, 255], [54039, 253], [54340, 251], [54641, 249], [54942, 246], [55243, 244], [55544, 242], [55845, 240], [56146, 238], [56447, 235], [56748, 233], [57048, 232], [57349, 230], [57650, 228], [57951, 225], [58252, 223], [58553, 221], [58854, 219], [59155, 217], [59456, 214], [59757, 212], [60058, 210], [60359, 208], [60660, 206], [60961, 203], [61261, 202], [61562, 200], [61863, 198], [62164, 194], [62465, 189], [62766, 187], [63067, 185], [63368, 183], [63669, 181], [63970, 178], [64271, 176], [64572, 174], [64873, 172], [65174, 170], [65474, 169], [65775, 167], [66076, 164], [66377, 162], [66678, 160], [66979, 158], [67280, 156], [67582, 66], [67653, 81], [67888, 55], [67957, 71], [68193, 48], [68259, 64], [68499, 40], [68561, 56], [68805, 32], [68863, 48], [69111, 25], [69164, 41], [69417, 18], [69465, 34], [69723, 11], [69766, 26], [70032, 1], [70067, 16], [70368, 3]], "point": [149, 117]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.21|+00.82|-02.16", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-01.50|+00.00|-00.70"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 128], [210, 218], [509, 219], [809, 220], [1109, 220], [1408, 221], [1708, 222], [2008, 222], [2307, 223], [2607, 224], [2907, 224], [3206, 226], [3506, 226], [3805, 228], [4104, 230], [4404, 230], [4703, 232], [5002, 234], [5302, 235], [5601, 237], [5900, 239], [6199, 241], [6498, 243], [6797, 245], [7096, 247], [7395, 250], [7694, 252], [7993, 254], [8292, 257], [8591, 260], [8890, 262], [9188, 266], [9486, 270], [9784, 278], [10080, 5974], [16071, 9], [16107, 248], [16371, 10], [16406, 249], [16671, 10], [16706, 250], [16970, 11], [17005, 251], [17270, 11], [17305, 252], [17569, 12], [17604, 254], [17868, 13], [17904, 257], [18166, 16], [18203, 279], [18503, 279], [18802, 280], [19102, 281], [19401, 282], [19700, 281], [20000, 279], [20299, 279], [20600, 277], [20901, 276], [21202, 274], [21502, 274], [21802, 274], [22103, 273], [22403, 273], [22703, 274], [23003, 274], [23303, 274], [23602, 276], [23901, 278], [24201, 279], [24500, 282], [24798, 286], [25096, 294], [25391, 17808], [43200, 298], [43500, 297], [43800, 296], [44100, 295], [44400, 295], [44700, 294], [45000, 293], [45300, 292], [45600, 291], [45900, 290], [46200, 289], [46500, 288], [46800, 287], [47100, 286], [47400, 285], [47700, 284], [48000, 283], [48300, 282], [48600, 281], [48900, 280], [49200, 279], [49500, 278], [49800, 277], [50100, 276], [50400, 275], [50700, 274], [51000, 274], [51300, 273], [51600, 272], [51900, 271], [52200, 270], [52500, 269], [52800, 268], [53100, 267], [53400, 266], [53700, 265], [54000, 264], [54300, 263], [54600, 262], [54900, 261], [55200, 260], [55500, 259], [55800, 258], [56100, 257], [56400, 256], [56700, 255], [57000, 254], [57300, 254], [57600, 253], [57900, 252], [58200, 251], [58500, 250], [58800, 249], [59100, 248], [59400, 247], [59700, 246], [60000, 245], [60300, 244], [60600, 243], [60900, 242], [61200, 241], [61500, 240], [61800, 239], [62100, 238], [62400, 237], [62700, 236], [63000, 235], [63300, 234], [63600, 233], [63900, 88], [64200, 88], [64500, 88], [64800, 88], [65100, 88], [65400, 88], [65700, 88], [66000, 88], [66300, 88], [66600, 88], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 87], [68700, 87], [69000, 87], [69300, 87], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 86], [71100, 86], [71400, 86], [71700, 86], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 85], [73500, 85], [73800, 85], [74100, 85], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 84], [75900, 84], [76200, 84], [76500, 84], [76800, 84], [77100, 83], [77400, 83], [77700, 83], [78000, 83], [78300, 83], [78600, 83], [78900, 83], [79200, 83], [79500, 82], [79800, 82], [80100, 82], [80400, 82], [80700, 82], [81000, 82], [81300, 82], [81600, 82], [81900, 81], [82200, 81], [82500, 81], [82800, 81], [83100, 81], [83400, 81], [83700, 81], [84000, 81], [84300, 81], [84600, 80], [84900, 80], [85200, 80], [85500, 80], [85800, 80], [86100, 80], [86400, 80], [86700, 80], [87000, 79], [87300, 79], [87600, 79], [87900, 79], [88200, 79], [88500, 79], [88800, 79], [89100, 79], [89400, 79], [89700, 78]], "point": [149, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-01.50|+00.00|-00.70"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 128], [210, 218], [509, 219], [809, 220], [1109, 220], [1408, 221], [1708, 222], [2008, 222], [2307, 223], [2607, 224], [2907, 224], [3206, 226], [3506, 226], [3805, 228], [4104, 230], [4404, 230], [4703, 232], [5002, 234], [5302, 235], [5601, 237], [5900, 239], [6199, 241], [6498, 243], [6797, 245], [7096, 247], [7395, 250], [7694, 252], [7993, 254], [8292, 257], [8591, 260], [8890, 262], [9188, 266], [9486, 270], [9784, 278], [10080, 5974], [16071, 9], [16107, 248], [16371, 10], [16406, 249], [16671, 10], [16706, 250], [16970, 11], [17005, 251], [17270, 11], [17305, 252], [17569, 12], [17604, 254], [17868, 13], [17904, 257], [18166, 16], [18203, 279], [18503, 279], [18802, 280], [19102, 281], [19401, 282], [19700, 281], [20000, 279], [20299, 279], [20600, 277], [20901, 276], [21202, 274], [21502, 274], [21802, 274], [22103, 273], [22403, 273], [22703, 274], [23003, 274], [23303, 274], [23602, 276], [23901, 278], [24201, 279], [24500, 282], [24798, 286], [25096, 294], [25391, 272], [25667, 293], [25969, 290], [26271, 286], [26572, 284], [26873, 283], [27173, 282], [27474, 280], [27774, 280], [28075, 278], [28375, 278], [28676, 277], [28976, 276], [29276, 276], [29576, 276], [29876, 276], [30176, 276], [30476, 276], [30776, 276], [31076, 277], [31376, 277], [31675, 278], [31975, 279], [32275, 279], [32574, 281], [32873, 282], [33172, 285], [33471, 287], [33770, 289], [34068, 9131], [43200, 298], [43500, 297], [43800, 296], [44100, 295], [44400, 295], [44700, 294], [45000, 293], [45300, 292], [45600, 291], [45900, 290], [46200, 289], [46500, 288], [46800, 287], [47100, 286], [47400, 285], [47700, 284], [48000, 283], [48300, 282], [48600, 281], [48900, 280], [49200, 279], [49500, 278], [49800, 277], [50100, 276], [50400, 275], [50700, 274], [51000, 274], [51300, 273], [51600, 272], [51900, 271], [52200, 270], [52500, 269], [52800, 268], [53100, 267], [53400, 266], [53700, 265], [54000, 264], [54300, 263], [54600, 262], [54900, 261], [55200, 260], [55500, 259], [55800, 258], [56100, 257], [56400, 256], [56700, 255], [57000, 254], [57300, 254], [57600, 253], [57900, 252], [58200, 251], [58500, 250], [58800, 249], [59100, 248], [59400, 247], [59700, 246], [60000, 245], [60300, 244], [60600, 243], [60900, 242], [61200, 241], [61500, 240], [61800, 239], [62100, 238], [62400, 237], [62700, 236], [63000, 235], [63300, 234], [63600, 233], [63900, 88], [64200, 88], [64500, 88], [64800, 88], [65100, 88], [65400, 88], [65700, 88], [66000, 88], [66300, 88], [66600, 88], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 87], [68700, 87], [69000, 87], [69300, 87], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 86], [71100, 86], [71400, 86], [71700, 86], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 85], [73500, 85], [73800, 85], [74100, 85], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 84], [75900, 84], [76200, 84], [76500, 84], [76800, 84], [77100, 83], [77400, 83], [77700, 83], [78000, 83], [78300, 83], [78600, 83], [78900, 83], [79200, 83], [79500, 82], [79800, 82], [80100, 82], [80400, 82], [80700, 82], [81000, 82], [81300, 82], [81600, 82], [81900, 81], [82200, 81], [82500, 81], [82800, 81], [83100, 81], [83400, 81], [83700, 81], [84000, 81], [84300, 81], [84600, 80], [84900, 80], [85200, 80], [85500, 80], [85800, 80], [86100, 80], [86400, 80], [86700, 80], [87000, 79], [87300, 79], [87600, 79], [87900, 79], [88200, 79], [88500, 79], [88800, 79], [89100, 79], [89400, 79], [89700, 78]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan20", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -0.75, "y": 0.9009992, "z": 0.0}, "object_poses": [{"objectName": "Pan_bc4f8581", "position": {"x": 1.8451, "y": 0.948799968, "z": -0.3554}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_4e602a34", "position": {"x": -1.61053944, "y": 1.5339905, "z": -0.8065041}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "DishSponge_c9c37476", "position": {"x": -1.07279778, "y": 0.9130642, "z": -2.1156342}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_c9c37476", "position": {"x": 1.46438551, "y": 0.8803951, "z": 2.45643163}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8db17fc4", "position": {"x": -0.06476809, "y": 0.764498234, "z": -2.0264}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_620f4eaa", "position": {"x": 0.921726346, "y": 0.9144287, "z": -1.75133562}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_620f4eaa", "position": {"x": -0.772480667, "y": 0.914428651, "z": -2.13435817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_33e0bb4d", "position": {"x": -0.137785524, "y": 0.779456, "z": -2.07131481}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Egg_8bcb7893", "position": {"x": 0.180688992, "y": 0.984198034, "z": 1.220139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_8bcb7893", "position": {"x": -0.210802972, "y": 0.8180097, "z": -2.16114473}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_416e93ae", "position": {"x": 0.294566572, "y": 0.9537251, "z": 1.44883978}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_de47916c", "position": {"x": -1.53399181, "y": 1.55648851, "z": -0.599496365}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_11e2985b", "position": {"x": 0.522321761, "y": 0.929298162, "z": 0.30533576}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_11e2985b", "position": {"x": 0.06681141, "y": 0.925586164, "z": 0.534036636}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_980e4fc9", "position": {"x": 1.46283293, "y": 0.549664557, "z": 2.4829}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_980e4fc9", "position": {"x": 0.180688992, "y": 0.9272505, "z": 0.99143815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_725f4a28", "position": {"x": -1.22462988, "y": 2.14389086, "z": -1.20280457}, "rotation": {"x": -1.40334208e-14, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_6ea32579", "position": {"x": 1.87379873, "y": 0.8771373, "z": 2.53793621}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6ea32579", "position": {"x": 1.36203218, "y": 0.8771372, "z": 2.51076818}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b4d69cc9", "position": {"x": -1.49556208, "y": 1.22883, "z": -0.703}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bread_b4d69cc9", "position": {"x": 1.850235, "y": 0.958645463, "z": -2.002}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_792121da", "position": {"x": -0.356837839, "y": 0.8353815, "z": -2.11622977}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_4e602a34", "position": {"x": -1.61046267, "y": 0.8887561, "z": -0.7030003}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_980e4fc9", "position": {"x": 0.06681141, "y": 0.9235385, "z": 0.30533576}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_11e2985b", "position": {"x": -0.346631646, "y": 0.9121536, "z": -1.72573566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_a1e5049a", "position": {"x": 0.454769254, "y": 0.9017246, "z": -1.88692629}, "rotation": {"x": 359.871155, "y": -0.0006618115, "z": 359.679535}}, {"objectName": "Ladle_bd8f96dd", "position": {"x": -1.3162322, "y": 1.70403624, "z": -1.66749954}, "rotation": {"x": -1.40334208e-14, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_b4d69cc9", "position": {"x": -0.0470661819, "y": 0.972077966, "z": 0.99143815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_aebda124", "position": {"x": 1.64783418, "y": 0.9946568, "z": -1.329551}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_620f4eaa", "position": {"x": 1.87252116, "y": 0.08746629, "z": 0.00628031045}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_33e0bb4d", "position": {"x": -0.0470661819, "y": 0.9419324, "z": 0.30533576}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d1e14b30", "position": {"x": 0.38467744, "y": 1.66237748, "z": -2.16299129}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_725f4a28", "position": {"x": -0.963619769, "y": 0.909806132, "z": -2.2244463}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_d7116d9f", "position": {"x": 1.5675, "y": 0.948799968, "z": -0.7576}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_5f9ea4fe", "position": {"x": -1.49556231, "y": 0.8596686, "z": -0.599495769}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_6ea32579", "position": {"x": 1.87602162, "y": 1.65459275, "z": -1.24065936}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_bc4f8581", "position": {"x": 1.90938592, "y": 1.65724719, "z": -1.60884356}, "rotation": {"x": 0.0, "y": 0.0, "z": -7.062251e-30}}, {"objectName": "ButterKnife_8db17fc4", "position": {"x": 0.408444166, "y": 0.9269746, "z": 0.30533576}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_de47916c", "position": {"x": 0.408444166, "y": 0.9824485, "z": 0.534036636}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_c9c37476", "position": {"x": 1.67023325, "y": 0.552622855, "z": 2.431126}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_8bcb7893", "position": {"x": 0.294566572, "y": 0.984198034, "z": 0.7627374}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_a85d92ad", "position": {"x": 0.408444166, "y": 0.9317739, "z": 0.076635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_416e93ae", "position": {"x": 0.522321761, "y": 0.9500131, "z": 0.534036636}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_0e2bb95a", "position": {"x": -0.356837839, "y": 0.759456158, "z": -1.98148525}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}], "object_toggles": [], "random_seed": 2253428388, "scene_num": 20}, "task_id": "trial_T20190907_224538_972488", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A320QA9HJFUOZO_373ERPL3YRPEZ692GKDCNY0HKA1TRA", "high_descs": ["Turn right and walk up to the kitchen sink", "Pick up the egg from the kitchen sink", "Turn right and go up to the microwave on the counter", "Cook the egg in the microwave then remove it", "Turn right and walk a few feet over to the fridge", "Put the egg into the fridge"], "task_desc": "Put the cooked egg in the fridge", "votes": [1, 1]}, {"assignment_id": "A2KAGFQU28JY43_3GLB5JMZF0CGTR4REJJLJQ4OFGKDGZ", "high_descs": ["Turn to your right and go to the kitchen sink. ", "Pick up the egg in the sink, to the left of the tomato. ", "Turn to the microwave on your right. ", "Place the egg in the microwave, heat it up, then remove the egg from the microwave. ", "Move over to your right so that you are in front of the refrigerator to your right, ", "Place the egg in the refrigerator, on the second shelf, in front of the glass. "], "task_desc": "Put a warm egg in the refrigerator. ", "votes": [1, 1]}, {"assignment_id": "A2ALWT2BUSXD83_3OXV7EAXLH7GE6DO9H4NUQ5NUPH36E", "high_descs": ["Turn to your right and walk to the sink.", "Grab the egg from the sink.", "Turn to your right and walk to the microwave.", "Place the egg in the microwave, turn the microwave on and wait before removing the egg.", "Approach the fridge on your right.", "Place the egg in the fridge."], "task_desc": "Place a warmed egg in the fridge.", "votes": [1, 1]}]}}