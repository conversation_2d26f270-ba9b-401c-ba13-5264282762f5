{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000103.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000104.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000105.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000107.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000157.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000158.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000159.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000160.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000161.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000162.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000163.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000193.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000196.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000197.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000199.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000200.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000201.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000202.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000203.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000206.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000207.png", "low_idx": 28}, {"high_idx": 8, "image_name": "000000208.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000211.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000212.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000213.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000214.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000216.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000217.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000218.png", "low_idx": 29}, {"high_idx": 8, "image_name": "000000219.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000220.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000221.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000222.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000223.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000224.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000225.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000226.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000227.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000228.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000229.png", "low_idx": 30}, {"high_idx": 8, "image_name": "000000230.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000231.png", "low_idx": 31}, {"high_idx": 8, "image_name": "000000232.png", "low_idx": 32}, {"high_idx": 8, "image_name": "000000233.png", "low_idx": 32}, {"high_idx": 8, "image_name": "000000234.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000235.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000236.png", "low_idx": 34}, {"high_idx": 8, "image_name": "000000237.png", "low_idx": 34}, {"high_idx": 8, "image_name": "000000238.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000239.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000240.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000241.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000242.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000243.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000244.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000247.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000248.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000249.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000250.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000251.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000252.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000253.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000254.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000255.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000256.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000257.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000258.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000259.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000260.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000261.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000262.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000268.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000269.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000270.png", "low_idx": 42}, {"high_idx": 9, "image_name": "000000271.png", "low_idx": 42}, {"high_idx": 9, "image_name": "000000272.png", "low_idx": 42}, {"high_idx": 9, "image_name": "000000273.png", "low_idx": 42}, {"high_idx": 9, "image_name": "000000274.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000275.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000276.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000277.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000278.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000279.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000280.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000281.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000282.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000283.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000284.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000285.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000286.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000287.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000288.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000289.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000290.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000291.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000292.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000293.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000294.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000295.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000296.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000297.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000298.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000299.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000300.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000301.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000302.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000303.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000304.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000305.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000306.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000307.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000308.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000309.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000310.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000311.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000312.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000313.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000314.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000315.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000316.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000317.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000318.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000319.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000320.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000321.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000322.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000323.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000324.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000325.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000326.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000327.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000328.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000329.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000330.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000331.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000332.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000333.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000334.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000335.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000336.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000337.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000338.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000339.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000340.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000341.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000342.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000343.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000344.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000345.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000346.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000347.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000348.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000349.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000350.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000351.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000352.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000353.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000354.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000355.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000356.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000357.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000358.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000359.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000360.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000361.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000362.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000363.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000364.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000365.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000366.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000367.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000368.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000369.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000370.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000371.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000372.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000373.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000374.png", "low_idx": 53}, {"high_idx": 11, "image_name": "000000375.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000376.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000377.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000378.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000379.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000380.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000381.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000382.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000383.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000384.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000385.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000386.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000387.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000388.png", "low_idx": 54}, {"high_idx": 11, "image_name": "000000389.png", "low_idx": 54}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|4|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [-6.12323904, -6.12323904, 4.379692, 4.379692, 5.41080188, 5.41080188]], "coordinateReceptacleObjectId": ["CounterTop", [-7.236, -7.236, 4.728, 4.728, 5.434, 5.434]], "forceVisible": true, "objectId": "Knife|-01.53|+01.35|+01.09"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|7|3|30"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-7.680274, -7.680274, 6.97820424, 6.97820424, 5.589581, 5.589581]], "forceVisible": true, "objectId": "Bread|-01.92|+01.40|+01.74"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|7|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [-6.12323904, -6.12323904, 4.379692, 4.379692, 5.41080188, 5.41080188]], "coordinateReceptacleObjectId": ["Cabinet", [-5.84518432, -5.84518432, 5.244, 5.244, 3.100034236, 3.100034236]], "forceVisible": true, "objectId": "Knife|-01.53|+01.35|+01.09", "receptacleObjectId": "Cabinet|-01.46|+00.78|+01.31"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-4|7|3|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-7.680274, -7.680274, 6.97820424, 6.97820424, 5.589581, 5.589581]], "coordinateReceptacleObjectId": ["CounterTop", [-7.236, -7.236, 4.728, 4.728, 5.434, 5.434]], "forceVisible": true, "objectId": "Bread|-01.92|+01.40|+01.74|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|1|3|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|1|4|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "sidetable"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-7.680274, -7.680274, 6.97820424, 6.97820424, 5.589581, 5.589581]], "coordinateReceptacleObjectId": ["SideTable", [4.088, 4.088, 3.4844, 3.4844, 4.9732, 4.9732]], "forceVisible": true, "objectId": "Bread|-01.92|+01.40|+01.74|BreadSliced_1", "receptacleObjectId": "SideTable|+01.02|+01.24|+00.87"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|-01.53|+01.35|+01.09"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [180, 87, 192, 174], "mask": [[25980, 1], [26280, 1], [26580, 1], [26880, 1], [27180, 1], [27480, 2], [27780, 2], [28080, 2], [28381, 1], [28681, 1], [28981, 1], [29281, 1], [29581, 2], [29881, 2], [30181, 2], [30481, 2], [30781, 2], [31081, 2], [31381, 2], [31681, 2], [31981, 3], [32282, 2], [32582, 2], [32882, 2], [33182, 2], [33482, 2], [33782, 2], [34082, 2], [34382, 3], [34682, 3], [34982, 3], [35282, 3], [35583, 2], [35883, 2], [36183, 2], [36483, 2], [36783, 2], [37083, 3], [37383, 3], [37683, 3], [37983, 3], [38283, 3], [38583, 3], [38884, 2], [39184, 2], [39484, 3], [39784, 3], [40084, 3], [40384, 4], [40684, 4], [40984, 4], [41284, 4], [41584, 5], [41885, 4], [42185, 4], [42485, 4], [42785, 4], [43085, 4], [43385, 4], [43685, 5], [43985, 5], [44285, 5], [44585, 5], [44885, 5], [45185, 5], [45486, 4], [45786, 5], [46086, 5], [46386, 5], [46686, 5], [46986, 5], [47286, 5], [47586, 5], [47886, 6], [48186, 6], [48487, 5], [48787, 5], [49087, 5], [49387, 5], [49687, 5], [49987, 5], [50287, 5], [50587, 6], [50887, 6], [51188, 5], [51488, 4], [51788, 4], [52089, 2]], "point": [185, 129]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-01.92|+01.40|+01.74"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [109, 108, 189, 146], "mask": [[32236, 27], [32528, 43], [32821, 55], [33118, 61], [33416, 66], [33715, 69], [34013, 72], [34312, 74], [34612, 75], [34911, 77], [35210, 78], [35510, 79], [35809, 80], [36109, 80], [36409, 80], [36709, 81], [37009, 81], [37309, 80], [37609, 80], [37909, 79], [38209, 79], [38509, 79], [38809, 79], [39110, 78], [39410, 78], [39710, 78], [40010, 77], [40310, 77], [40610, 77], [40910, 77], [41211, 76], [41511, 76], [41811, 76], [42112, 74], [42412, 74], [42712, 73], [43013, 71], [43315, 68], [43621, 6], [43642, 13], [43667, 11]], "point": [149, 126]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-01.46|+00.78|+01.31"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 102, 122, 199], "mask": [[30357, 54], [30641, 71], [30925, 87], [31210, 102], [31500, 112], [31800, 112], [32100, 112], [32400, 112], [32700, 112], [33000, 113], [33300, 113], [33600, 113], [33900, 113], [34200, 113], [34500, 113], [34800, 113], [35100, 113], [35400, 114], [35700, 114], [36000, 114], [36300, 114], [36600, 114], [36900, 114], [37200, 114], [37500, 114], [37800, 115], [38100, 115], [38400, 115], [38700, 115], [39000, 115], [39300, 115], [39600, 115], [39900, 115], [40200, 116], [40500, 116], [40800, 116], [41100, 116], [41400, 116], [41700, 116], [42000, 116], [42300, 116], [42600, 117], [42900, 117], [43200, 117], [43500, 117], [43800, 117], [44100, 117], [44400, 117], [44700, 117], [45000, 118], [45300, 118], [45600, 118], [45900, 118], [46200, 118], [46500, 118], [46800, 118], [47100, 118], [47400, 119], [47701, 118], [48001, 118], [48302, 117], [48602, 117], [48903, 116], [49204, 115], [49504, 115], [49805, 115], [50105, 115], [50406, 114], [50707, 113], [51007, 113], [51308, 112], [51608, 112], [51909, 111], [52210, 111], [52510, 111], [52811, 110], [53111, 110], [53412, 109], [53713, 108], [54013, 108], [54314, 107], [54614, 108], [54915, 107], [55216, 106], [55516, 106], [55817, 105], [56118, 104], [56418, 104], [56719, 103], [57020, 103], [57320, 103], [57621, 102], [57921, 102], [58222, 101], [58523, 100], [58823, 100], [59124, 99], [59424, 99]], "point": [61, 149]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|-01.53|+01.35|+01.09", "placeStationary": true, "receptacleObjectId": "Cabinet|-01.46|+00.78|+01.31"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 102, 124, 300], "mask": [[30357, 57], [30641, 73], [30925, 89], [31210, 104], [31500, 114], [31800, 114], [32100, 114], [32400, 114], [32700, 115], [33000, 115], [33300, 115], [33600, 115], [33900, 115], [34200, 115], [34500, 115], [34800, 115], [35100, 116], [35400, 116], [35700, 116], [36000, 116], [36300, 116], [36600, 116], [36900, 116], [37200, 116], [37500, 116], [37800, 117], [38100, 117], [38400, 117], [38700, 117], [39000, 117], [39300, 117], [39600, 117], [39900, 117], [40200, 118], [40500, 118], [40800, 118], [41100, 118], [41400, 118], [41700, 118], [42000, 118], [42300, 118], [42600, 118], [42900, 119], [43200, 119], [43500, 119], [43800, 119], [44100, 119], [44400, 119], [44700, 119], [45001, 118], [45301, 119], [45602, 118], [45903, 117], [46203, 117], [46504, 83], [46597, 23], [46804, 81], [46897, 23], [47105, 76], [47197, 23], [47405, 74], [47497, 23], [47706, 72], [47797, 23], [48007, 70], [48096, 25], [48307, 69], [48396, 25], [48608, 67], [48696, 25], [48908, 66], [48996, 25], [49209, 64], [49296, 25], [49509, 63], [49595, 26], [49810, 62], [49895, 26], [50111, 61], [50195, 26], [50411, 61], [50495, 27], [50712, 59], [50795, 27], [51012, 59], [51094, 28], [51313, 57], [51394, 28], [51614, 56], [51694, 28], [51914, 56], [51994, 28], [52215, 56], [52294, 28], [52515, 56], [52593, 29], [52816, 56], [52893, 29], [53116, 56], [53193, 30], [53417, 56], [53493, 30], [53718, 55], [53793, 30], [54018, 56], [54092, 31], [54319, 57], [54392, 31], [54619, 59], [54692, 31], [54920, 60], [54992, 31], [55220, 61], [55292, 31], [55521, 103], [55822, 102], [56122, 102], [56423, 101], [56723, 101], [57024, 100], [57325, 99], [57625, 99], [57926, 98], [58226, 99], [58527, 98], [58889, 36], [59189, 36], [59489, 36], [59789, 36], [60088, 37], [60388, 37], [60688, 37], [60988, 37], [61288, 37], [61587, 38], [61887, 38], [62187, 38], [62487, 37], [62787, 37], [63086, 38], [63386, 38], [63686, 38], [63986, 38], [64286, 38], [64585, 39], [64885, 39], [65185, 39], [65485, 39], [65785, 39], [66084, 40], [66384, 39], [66684, 39], [66984, 39], [67284, 39], [67583, 40], [67883, 40], [68183, 40], [68483, 40], [68783, 40], [69082, 41], [69382, 41], [69682, 41], [69982, 40], [70283, 39], [70583, 39], [70884, 38], [71184, 38], [71484, 38], [71785, 37], [72085, 37], [72385, 37], [72686, 36], [72986, 36], [73287, 35], [73587, 35], [73887, 34], [74188, 33], [74488, 33], [74789, 32], [75089, 32], [75389, 32], [75690, 31], [75990, 31], [76290, 31], [76591, 30], [79895, 17], [80195, 25], [80496, 24], [80796, 24], [81097, 23], [81397, 22], [81697, 22], [81998, 21], [82298, 21], [82599, 20], [82899, 20], [83199, 20], [83500, 19], [83800, 19], [84100, 19], [84401, 18], [84701, 18], [85002, 16], [85302, 16], [85602, 16], [85903, 15], [86203, 15], [86504, 15], [86804, 15], [87104, 15], [87405, 14], [87705, 15], [88005, 15], [88306, 14], [88606, 13], [88907, 10], [88918, 1], [89207, 10], [89507, 10], [89808, 9]], "point": [62, 195]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-01.46|+00.78|+01.31"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 102, 124, 300], "mask": [[30357, 57], [30641, 73], [30925, 89], [31210, 104], [31500, 114], [31800, 114], [32100, 114], [32400, 114], [32700, 115], [33000, 115], [33300, 115], [33600, 115], [33900, 115], [34200, 115], [34500, 115], [34800, 115], [35100, 116], [35400, 116], [35700, 116], [36000, 116], [36300, 116], [36600, 116], [36900, 116], [37200, 116], [37500, 116], [37800, 117], [38100, 117], [38400, 117], [38700, 117], [39000, 117], [39300, 117], [39600, 117], [39900, 117], [40200, 118], [40500, 118], [40800, 118], [41100, 118], [41400, 118], [41700, 118], [42000, 118], [42300, 118], [42600, 118], [42900, 119], [43200, 119], [43500, 119], [43800, 119], [44100, 119], [44400, 119], [44700, 119], [45001, 118], [45301, 119], [45602, 118], [45903, 117], [46203, 117], [46504, 83], [46597, 23], [46804, 81], [46897, 23], [47105, 76], [47197, 23], [47405, 54], [47460, 19], [47497, 23], [47706, 53], [47761, 17], [47797, 23], [48007, 52], [48061, 16], [48096, 25], [48307, 52], [48361, 15], [48396, 25], [48608, 51], [48662, 13], [48696, 25], [48908, 52], [48962, 12], [48996, 25], [49209, 51], [49262, 11], [49296, 25], [49509, 52], [49563, 9], [49595, 26], [49810, 51], [49863, 9], [49895, 26], [50111, 50], [50163, 9], [50195, 26], [50411, 51], [50464, 8], [50495, 27], [50712, 50], [50764, 7], [50795, 27], [51012, 51], [51064, 7], [51094, 28], [51313, 50], [51365, 5], [51394, 28], [51614, 50], [51665, 5], [51694, 28], [51914, 50], [51965, 5], [51994, 28], [52215, 49], [52266, 5], [52294, 28], [52515, 49], [52566, 5], [52593, 29], [52816, 48], [52866, 6], [52893, 29], [53116, 48], [53167, 5], [53193, 30], [53417, 47], [53467, 6], [53493, 30], [53718, 46], [53768, 5], [53793, 30], [54018, 47], [54068, 6], [54092, 31], [54319, 46], [54368, 8], [54392, 31], [54619, 46], [54669, 9], [54692, 31], [54920, 46], [54969, 11], [54992, 31], [55220, 46], [55269, 12], [55292, 31], [55521, 46], [55570, 54], [55822, 45], [55870, 54], [56122, 46], [56170, 54], [56423, 45], [56470, 54], [56723, 46], [56771, 53], [57024, 45], [57071, 53], [57325, 45], [57371, 53], [57625, 99], [57926, 98], [58226, 99], [58527, 98], [58889, 36], [59189, 36], [59489, 36], [59789, 36], [60088, 37], [60388, 37], [60688, 37], [60988, 37], [61288, 37], [61587, 38], [61887, 38], [62187, 38], [62487, 37], [62787, 37], [63086, 38], [63386, 38], [63686, 38], [63986, 38], [64286, 38], [64585, 39], [64885, 39], [65185, 39], [65485, 39], [65785, 39], [66084, 40], [66384, 39], [66684, 39], [66984, 39], [67284, 39], [67583, 40], [67883, 40], [68183, 40], [68483, 40], [68783, 40], [69082, 41], [69382, 41], [69682, 41], [69982, 40], [70283, 39], [70583, 39], [70884, 38], [71184, 38], [71484, 38], [71785, 37], [72085, 37], [72385, 37], [72686, 36], [72986, 36], [73287, 35], [73587, 35], [73887, 34], [74188, 33], [74488, 33], [74789, 32], [75089, 32], [75389, 32], [75690, 31], [75990, 31], [76290, 31], [76591, 30], [76891, 30], [77192, 29], [77492, 28], [77792, 28], [78093, 27], [78393, 27], [78694, 26], [78994, 26], [79294, 26], [79595, 25], [79895, 25], [80195, 25], [80496, 24], [80796, 24], [81097, 23], [81397, 22], [81697, 22], [81998, 21], [82298, 21], [82599, 20], [82899, 20], [83199, 20], [83500, 19], [83800, 19], [84100, 19], [84401, 18], [84701, 18], [85002, 16], [85302, 16], [85602, 16], [85903, 15], [86203, 15], [86504, 15], [86804, 15], [87104, 15], [87405, 14], [87705, 15], [88005, 15], [88306, 14], [88606, 13], [88907, 10], [88918, 1], [89207, 10], [89507, 10], [89808, 9]], "point": [62, 195]}}, "high_idx": 5}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.92|+01.40|+01.74|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [141, 108, 147, 147], "mask": [[32243, 4], [32542, 5], [32842, 5], [33142, 5], [33442, 5], [33742, 5], [34042, 5], [34342, 5], [34642, 5], [34942, 5], [35242, 5], [35542, 5], [35841, 6], [36141, 7], [36441, 7], [36741, 7], [37041, 7], [37341, 7], [37641, 7], [37941, 7], [38241, 7], [38541, 7], [38842, 6], [39142, 6], [39442, 6], [39742, 6], [40042, 6], [40342, 6], [40642, 6], [40942, 6], [41242, 6], [41542, 6], [41842, 5], [42142, 5], [42442, 5], [42742, 5], [43042, 5], [43342, 5], [43642, 5], [43945, 2]], "point": [144, 126]}}, "high_idx": 7}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.92|+01.40|+01.74|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 109], [35827, 87], [36000, 107], [36129, 85], [36300, 106], [36431, 82], [36600, 105], [36732, 81], [36900, 104], [37033, 80], [37200, 103], [37333, 80], [37500, 103], [37634, 79], [37800, 102], [37935, 78], [38100, 102], [38235, 78], [38400, 101], [38535, 78], [38700, 101], [38835, 77], [39000, 101], [39135, 77], [39300, 101], [39435, 77], [39600, 101], [39735, 77], [39900, 102], [40035, 77], [40200, 102], [40335, 77], [40500, 103], [40634, 78], [40800, 103], [40934, 78], [41100, 103], [41234, 77], [41400, 103], [41534, 77], [41700, 104], [41835, 76], [42000, 104], [42135, 76], [42300, 104], [42435, 76], [42600, 104], [42735, 76], [42900, 104], [43035, 76], [43200, 104], [43335, 75], [43500, 103], [43635, 75], [43800, 103], [43935, 75], [44100, 103], [44236, 74], [44400, 103], [44536, 74], [44700, 103], [44836, 74], [45000, 104], [45135, 75], [45300, 104], [45435, 75], [45600, 105], [45734, 75], [45900, 117], [46022, 87], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.92|+01.40|+01.74|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [101, 120, 135, 154], "mask": [[35809, 18], [36107, 22], [36406, 25], [36705, 27], [37004, 29], [37303, 30], [37603, 31], [37902, 33], [38202, 33], [38501, 34], [38801, 34], [39101, 34], [39401, 34], [39701, 34], [40002, 33], [40302, 33], [40603, 31], [40903, 31], [41203, 31], [41503, 31], [41804, 31], [42104, 31], [42404, 31], [42704, 31], [43004, 31], [43304, 31], [43603, 32], [43903, 32], [44203, 33], [44503, 33], [44803, 33], [45104, 31], [45404, 31], [45705, 29], [46017, 5]], "point": [118, 136]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.92|+01.40|+01.74|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "SideTable|+01.02|+01.24|+00.87"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [40, 72, 299, 188], "mask": [[21383, 18], [21683, 18], [21982, 19], [22282, 5], [22582, 5], [22881, 6], [23181, 5], [23481, 5], [23780, 6], [24080, 5], [24379, 6], [24679, 6], [24979, 5], [25278, 6], [25578, 6], [25878, 5], [26177, 6], [26477, 6], [26777, 5], [26802, 1], [27076, 6], [27102, 1], [27376, 6], [27401, 2], [27675, 6], [27701, 3], [27975, 6], [28001, 3], [28183, 2], [28275, 6], [28301, 3], [28483, 3], [28574, 6], [28601, 3], [28783, 3], [28874, 6], [28902, 2], [29084, 3], [29174, 6], [29202, 2], [29385, 3], [29473, 6], [29503, 2], [29685, 3], [29773, 6], [29804, 1], [29986, 3], [30072, 7], [30104, 1], [30287, 3], [30372, 6], [30587, 3], [30672, 6], [30888, 3], [30971, 7], [31189, 2], [31271, 6], [31489, 3], [31571, 6], [31790, 3], [31870, 7], [32091, 2], [32170, 6], [32391, 3], [32470, 6], [32692, 3], [32769, 7], [32993, 2], [33069, 6], [33293, 3], [33368, 7], [33594, 3], [33668, 7], [33895, 2], [33968, 6], [34140, 1], [34195, 3], [34267, 7], [34317, 21], [34430, 22], [34496, 3], [34567, 7], [34612, 30], [34726, 30], [34797, 2], [34867, 6], [34909, 36], [35024, 36], [35097, 3], [35166, 7], [35206, 42], [35321, 42], [35398, 2], [35466, 7], [35503, 48], [35619, 47], [35699, 1], [35765, 7], [35801, 51], [35917, 51], [35999, 1], [36065, 7], [36100, 54], [36216, 54], [36365, 7], [36398, 57], [36515, 58], [36664, 7], [36696, 61], [36814, 61], [36964, 7], [36994, 65], [37112, 65], [37264, 7], [37293, 66], [37412, 66], [37563, 7], [37592, 68], [37711, 68], [37863, 7], [37891, 70], [38011, 70], [38163, 7], [38190, 72], [38310, 72], [38462, 7], [38489, 74], [38610, 73], [38762, 7], [38788, 76], [38909, 76], [39061, 8], [39087, 77], [39209, 77], [39361, 7], [39386, 79], [39508, 79], [39661, 7], [39686, 79], [39808, 80], [39960, 8], [39985, 80], [40108, 81], [40260, 7], [40285, 81], [40408, 81], [40560, 7], [40584, 38], [40628, 38], [40708, 38], [40752, 38], [40859, 8], [40884, 36], [40930, 36], [41008, 36], [41055, 36], [41159, 7], [41183, 36], [41231, 36], [41308, 36], [41356, 36], [41458, 8], [41483, 35], [41532, 35], [41608, 36], [41657, 36], [41758, 8], [41782, 36], [41832, 35], [41908, 36], [41958, 35], [42058, 7], [42082, 36], [42132, 35], [42209, 35], [42258, 36], [42357, 8], [42382, 36], [42431, 36], [42509, 36], [42558, 36], [42657, 8], [42682, 37], [42730, 37], [42810, 37], [42858, 36], [42957, 7], [42982, 39], [43028, 38], [43110, 39], [43156, 39], [43256, 8], [43282, 84], [43411, 84], [43556, 8], [43582, 84], [43711, 84], [43856, 7], [43882, 84], [44012, 84], [44155, 8], [44182, 84], [44312, 84], [44455, 8], [44482, 84], [44612, 84], [44754, 8], [44782, 83], [44913, 83], [45054, 8], [45083, 82], [45214, 82], [45354, 8], [45383, 81], [45515, 81], [45653, 8], [45684, 79], [45816, 80], [45953, 8], [45984, 79], [46117, 79], [46253, 8], [46285, 77], [46418, 77], [46552, 8], [46585, 76], [46719, 76], [46852, 8], [46886, 75], [47020, 75], [47151, 9], [47186, 74], [47321, 74], [47451, 8], [47487, 72], [47622, 72], [47751, 8], [47788, 70], [47924, 69], [48050, 9], [48089, 67], [48226, 67], [48350, 8], [48390, 65], [48527, 65], [48650, 8], [48691, 62], [48829, 62], [48949, 9], [48993, 59], [49130, 60], [49249, 8], [49294, 57], [49432, 57], [49549, 8], [49595, 54], [49734, 54], [49848, 9], [49897, 50], [50037, 49], [50148, 8], [50200, 44], [50340, 44], [50447, 9], [50502, 39], [50643, 39], [50747, 9], [50805, 34], [50946, 34], [51047, 8], [51107, 29], [51249, 28], [51346, 9], [51415, 13], [51557, 13], [51646, 55], [51946, 106], [52245, 158], [52545, 209], [52844, 256], [53144, 256], [53444, 256], [53743, 257], [54043, 257], [54343, 257], [54642, 258], [54942, 258], [55242, 258], [55541, 259], [55841, 259], [56140, 260]], "point": [164, 131]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan3", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -0.5, "y": 1.12401652, "z": 0.25}, "object_poses": [{"objectName": "WineBottle_560d3534", "position": {"x": -1.6575036, "y": 0.2811138, "z": 2.27545214}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.34542334, "y": 1.32392883, "z": -0.5670749}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -2.04982138, "y": 1.32404137, "z": 2.069365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.53081059, "y": 1.32354856, "z": -3.24110079}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": 0.420673847, "y": 1.32354856, "z": -2.90023613}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -1.53081059, "y": 1.32412946, "z": -2.461287}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -1.79031563, "y": 1.32412946, "z": 1.74455106}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.56123328, "y": 0.437571168, "z": -0.561244547}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.53081059, "y": 1.33799994, "z": -1.94141138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.125, "y": 1.3324, "z": -2.973}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.38003659, "y": 1.36382449, "z": -2.76001024}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": -1.665155, "y": 0.341211677, "z": -2.162692}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -1.66056263, "y": 1.323614, "z": 1.4197371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.53080976, "y": 1.35270047, "z": 1.094923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.727323, "y": 0.344005942, "z": -1.86363292}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 0.9114412, "y": 2.06274128, "z": 2.05290937}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -1.4514004, "y": 1.32130814, "z": -2.870881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -1.88481736, "y": 0.3421222, "z": 0.5126284}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": 0.5068307, "y": 1.39912164, "z": -1.68329394}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.3086729, "y": 1.39912164, "z": -3.31436586}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": -2.179573, "y": 1.318, "z": -2.461287}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.900508642, "y": 0.339971423, "z": -2.37100124}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": 0.93334806, "y": 1.99500382, "z": 1.78890634}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": -1.9200685, "y": 1.39739525, "z": 1.74455106}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.6309, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -2.00441241, "y": 1.36631966, "z": -1.11794019}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -1.88481736, "y": 0.3421222, "z": 0.311763644}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": 1.1555934, "y": 1.36275363, "z": -2.42411566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.665155, "y": 0.344005942, "z": -2.362065}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.38003659, "y": 1.32354856, "z": -3.203495}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": -1.91546834, "y": 1.33285284, "z": -0.9805181}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -0.2394, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.82652426, "y": 1.37396622, "z": -1.04922915}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_9d168802", "position": {"x": -1.88114953, "y": 0.327762932, "z": -2.03532434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": -1.23730922, "y": 1.31800008, "z": -3.09262371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": 0.6365833, "y": 1.32392883, "z": -1.68329394}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.82631838, "y": 1.14011145, "z": -0.5422326}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.82652426, "y": 1.343986, "z": -1.11794019}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_ddd73f57", "position": {"x": -2.17957425, "y": 1.42728543, "z": 1.74455106}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 1.07256258, "y": 1.55844033, "z": 1.848439}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -1.75051689, "y": 0.344428062, "z": 1.317281}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": -1.0945816, "y": 1.31800008, "z": -3.31436586}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": -1.665155, "y": 0.343378425, "z": -1.9633193}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": 0.5068307, "y": 1.32412946, "z": -2.42411566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.634, "y": 0.32906872, "z": 2.151774}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": -1.602987, "y": 0.33881402, "z": -1.76394641}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3300705972, "scene_num": 3}, "task_id": "trial_T20190907_050601_836947", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A35P2RX8HKZM96_3A0EX8ZRNB5N0LR7UTELV0NSH7XYB6", "high_descs": ["Turn left and walk to the counter.", "Pick up the knife on the counter.", "Turn right then turn left and approach the bread on the counter.", "Slice the bread on the counter.", "Make a 360 degree turn and walk to the cabinet under the counter.", "Open the right cabinet, place the knife inside, and close the door.", "Look up at the bread on the counter.", "Take a slice of bread from the loaf.", "Turn around and walk to the microwave.", "Open the door, place the bread inside, close the door, and turn the microwave on. Remove the bread when done.", "Take a step to the left.", "Place the slice of bread on the counter."], "task_desc": "Place a slice of microwaved bread on the counter.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A3C81THYYSBGVD_3HVVDCPGTH9NW7LOUF56VUTENKGTYM", "high_descs": ["Move forward and face the counter on your left.", "Pick up the knife from the counter in front of you.", "Turn right then face the bread on top of the counter to your left.", "Slice the bread on the counter in front of you.", "Step back and open the cabinet under the counter in front of you.", "Place the knife in the cabinet in front of you and close it.", "Face the bread on top of the counter in front of you.", "Take a slice of bread from the counter in front of you.", "Turn around and head towards the microwave in front of you.", "Heat up the slice of bread, take out the bread and close the microwave.", "Turn left then face the counter with the microwave on your right.", "Place the slice of bread on the counter with the microwave in front of you."], "task_desc": "Heat up a slice of bread in the microwave.", "votes": [1, 1, 1, 1, 0]}, {"assignment_id": "A1OWHPMKE7YAGL_3NVC2EB65TGIXQNUF0ZFDHPGTPZ3YJ", "high_descs": ["Go forward, then turn to the left and go to the table. ", "Pick up the big knife that is on the table. ", "Move to the right at the table to where the bread is. ", "Use the knife to cut the bread into slices. ", "Back up and go to the left to the cupboard under the black plate. ", "Open the cupboard, put the knife inside, close the cupboard. ", "Go back to the bread on the counter to the right. ", "Pick up a slice of bread. ", "Turn around and go across the room to the microwave. ", "Open the microwave, put the bread inside and turn it on to cook, remove the bread from the microwave, close the microwave.", "Back up and move slightly to the left.", "Put the bread on the left side of the counter in front of the microwave. "], "task_desc": "Put a cooked slice of bread on a counter. ", "votes": [1, 1]}]}}