{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000317.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000318.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000319.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000320.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000321.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000322.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000323.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000324.png", "low_idx": 43}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [-0.1882647276, -0.1882647276, 0.30654, 0.30654, 3.702578, 3.702578]], "coordinateReceptacleObjectId": ["DiningTable", [1.66, 1.66, 2.712, 2.712, 0.052, 0.052]], "forceVisible": true, "objectId": "Mug|-00.05|+00.93|+00.08"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-5|3|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.936, -4.936, -6.724, -6.724, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.23|+00.90|-01.68"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-5|3|-15"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [-0.1882647276, -0.1882647276, 0.30654, 0.30654, 3.702578, 3.702578]], "coordinateReceptacleObjectId": ["Cabinet", [-4.6, -4.6, -7.9199996, -7.9199996, 8.08, 8.08]], "forceVisible": true, "objectId": "Mug|-00.05|+00.93|+00.08", "receptacleObjectId": "Cabinet|-01.15|+02.02|-01.98"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-00.05|+00.93|+00.08"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [103, 94, 140, 142], "mask": [[28021, 4], [28316, 14], [28613, 19], [28911, 23], [29210, 25], [29508, 28], [29807, 30], [30106, 32], [30406, 33], [30705, 34], [31004, 36], [31304, 36], [31604, 36], [31904, 36], [32203, 37], [32503, 37], [32803, 37], [33103, 37], [33404, 36], [33704, 36], [34004, 37], [34304, 37], [34605, 36], [34905, 36], [35205, 36], [35505, 36], [35806, 35], [36106, 35], [36406, 35], [36706, 35], [37007, 34], [37307, 34], [37607, 34], [37907, 34], [38208, 33], [38508, 33], [38808, 33], [39108, 33], [39409, 32], [39709, 31], [40009, 31], [40310, 29], [40610, 29], [40911, 27], [41212, 25], [41513, 23], [41814, 20], [42116, 16], [42419, 10]], "point": [121, 117]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-00.05|+00.93|+00.08", "placeStationary": true, "receptacleObjectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 85, 133, 185], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54087, 31], [54388, 30], [54688, 29], [54988, 29], [55288, 29]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 85, 133, 185], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 51], [38463, 71], [38700, 47], [38765, 69], [39000, 45], [39066, 68], [39300, 43], [39366, 68], [39600, 42], [39666, 68], [39900, 41], [39969, 65], [40200, 40], [40271, 63], [40500, 40], [40572, 62], [40800, 40], [40867, 2], [40873, 61], [41100, 41], [41167, 3], [41173, 61], [41400, 41], [41467, 3], [41473, 61], [41700, 41], [41768, 3], [41773, 61], [42000, 41], [42068, 3], [42073, 61], [42300, 42], [42368, 3], [42374, 60], [42600, 42], [42668, 3], [42674, 60], [42900, 42], [42968, 3], [42973, 61], [43200, 42], [43269, 2], [43273, 61], [43500, 42], [43569, 2], [43573, 61], [43800, 43], [43869, 1], [43873, 61], [44100, 43], [44172, 62], [44400, 43], [44471, 63], [44700, 43], [44770, 64], [45000, 44], [45070, 64], [45300, 44], [45370, 64], [45600, 44], [45670, 64], [45900, 44], [45970, 64], [46200, 45], [46270, 64], [46500, 45], [46569, 64], [46800, 45], [46868, 65], [47100, 45], [47167, 66], [47400, 46], [47465, 68], [47700, 47], [47763, 70], [48000, 49], [48060, 73], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54087, 31], [54388, 30], [54688, 29], [54988, 29], [55288, 29]], "point": [66, 132]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.936, -4.936, -6.724, -6.724, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.936, -4.936, -6.724, -6.724, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-00.05|+00.93|+00.08"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [40, 129, 73, 161], "mask": [[38451, 12], [38747, 18], [39045, 21], [39343, 23], [39642, 24], [39941, 28], [40240, 31], [40540, 32], [40840, 27], [40869, 4], [41141, 26], [41170, 3], [41441, 26], [41470, 3], [41741, 27], [41771, 2], [42041, 27], [42071, 2], [42342, 26], [42371, 3], [42642, 26], [42671, 3], [42942, 26], [42971, 2], [43242, 27], [43271, 2], [43542, 27], [43571, 2], [43843, 26], [43870, 3], [44143, 29], [44443, 28], [44743, 27], [45044, 26], [45344, 26], [45644, 26], [45944, 26], [46245, 25], [46545, 24], [46845, 23], [47145, 22], [47446, 19], [47747, 16], [48049, 11]], "point": [56, 144]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 85, 133, 185], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54087, 31], [54388, 30], [54688, 29], [54988, 29], [55288, 29]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-01.15|+02.02|-01.98"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 15, 95, 199], "mask": [[4200, 95], [4500, 96], [4800, 96], [5100, 96], [5400, 96], [5700, 96], [6000, 96], [6300, 96], [6600, 96], [6900, 96], [7200, 96], [7500, 96], [7800, 96], [8100, 96], [8400, 96], [8700, 96], [9000, 95], [9300, 95], [9600, 95], [9900, 95], [10200, 95], [10500, 95], [10800, 95], [11100, 95], [11400, 95], [11700, 95], [12000, 95], [12300, 95], [12600, 95], [12900, 95], [13200, 95], [13500, 95], [13800, 94], [14100, 94], [14400, 94], [14700, 94], [15000, 94], [15300, 94], [15600, 94], [15900, 94], [16200, 94], [16500, 94], [16800, 94], [17100, 94], [17400, 94], [17700, 94], [18000, 94], [18300, 93], [18600, 93], [18900, 93], [19200, 93], [19500, 93], [19800, 93], [20100, 93], [20400, 93], [20700, 93], [21000, 93], [21300, 93], [21600, 93], [21900, 93], [22200, 93], [22500, 93], [22800, 93], [23100, 92], [23400, 92], [23700, 92], [24000, 92], [24300, 92], [24600, 92], [24900, 92], [25200, 92], [25500, 92], [25800, 92], [26100, 92], [26400, 92], [26700, 92], [27000, 92], [27300, 92], [27600, 91], [27900, 91], [28200, 91], [28500, 91], [28800, 91], [29100, 91], [29400, 91], [29700, 91], [30000, 91], [30300, 91], [30600, 91], [30900, 91], [31200, 91], [31500, 91], [31800, 91], [32100, 91], [32400, 90], [32700, 90], [33000, 90], [33300, 90], [33600, 90], [33900, 90], [34200, 90], [34500, 90], [34800, 90], [35100, 90], [35400, 90], [35700, 90], [36000, 90], [36300, 90], [36600, 90], [36900, 90], [37200, 89], [37500, 89], [37800, 89], [38100, 89], [38400, 89], [38700, 89], [39000, 89], [39300, 89], [39600, 89], [39900, 89], [40200, 89], [40500, 89], [40800, 89], [41100, 89], [41400, 89], [41700, 88], [42000, 88], [42300, 88], [42600, 88], [42900, 88], [43200, 88], [43500, 88], [43800, 88], [44100, 88], [44400, 88], [44700, 88], [45000, 88], [45300, 88], [45600, 88], [45900, 88], [46200, 88], [46500, 87], [46800, 87], [47100, 87], [47400, 87], [47700, 87], [48000, 87], [48300, 87], [48600, 87], [48900, 87], [49200, 87], [49500, 87], [49800, 87], [50100, 87], [50400, 87], [50700, 87], [51000, 86], [51300, 86], [51600, 86], [51900, 86], [52200, 86], [52500, 86], [52800, 86], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 86], [54900, 86], [55200, 86], [55500, 86], [55800, 85], [56100, 85], [56400, 85], [56700, 85], [57000, 85], [57300, 85], [57600, 85], [57900, 85], [58200, 85], [58500, 85], [58800, 85], [59100, 85], [59400, 84]], "point": [47, 106]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-00.05|+00.93|+00.08", "placeStationary": true, "receptacleObjectId": "Cabinet|-01.15|+02.02|-01.98"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 25, 93, 197], "mask": [[7200, 94], [7500, 94], [7800, 94], [8100, 94], [8400, 94], [8700, 94], [9000, 94], [9300, 94], [9600, 94], [9900, 94], [10200, 94], [10500, 94], [10800, 93], [11100, 93], [11400, 93], [11700, 93], [12000, 93], [12300, 93], [12600, 93], [12900, 93], [13200, 93], [13500, 93], [13800, 93], [14100, 93], [14400, 93], [14700, 93], [15000, 93], [15300, 92], [15600, 92], [15900, 92], [16200, 92], [16500, 92], [16800, 92], [17100, 92], [17400, 92], [17700, 92], [18000, 92], [18300, 92], [18600, 92], [18900, 92], [19200, 92], [19500, 92], [19800, 92], [20100, 91], [20400, 91], [20700, 91], [21000, 91], [21300, 91], [21600, 91], [21900, 91], [22200, 91], [22500, 91], [22800, 91], [23100, 91], [23400, 91], [23700, 91], [24000, 91], [24300, 91], [24600, 90], [24900, 90], [25200, 90], [25500, 90], [25800, 90], [26100, 90], [26400, 90], [26700, 90], [27000, 90], [27300, 90], [27600, 90], [27900, 90], [28200, 90], [28500, 90], [28800, 90], [29100, 89], [29400, 89], [29700, 89], [30000, 89], [30300, 89], [30600, 89], [30900, 89], [31200, 89], [31500, 89], [31800, 89], [32100, 89], [32400, 89], [32700, 89], [33000, 89], [33300, 89], [33600, 88], [33900, 88], [34200, 88], [34500, 88], [34800, 88], [35100, 88], [35400, 88], [35700, 88], [36000, 88], [36300, 88], [36600, 88], [36900, 88], [37200, 88], [37500, 88], [37800, 88], [38100, 87], [38400, 87], [38700, 87], [39000, 87], [39300, 87], [39600, 87], [39900, 87], [40200, 87], [40500, 87], [40800, 87], [41100, 87], [41400, 87], [41700, 87], [42000, 87], [42300, 87], [42600, 86], [42900, 86], [43200, 86], [43500, 86], [43800, 86], [44100, 86], [44400, 86], [44700, 86], [45000, 86], [45300, 86], [45600, 86], [45900, 86], [46200, 86], [46500, 86], [46800, 86], [47100, 86], [47400, 85], [47700, 85], [48000, 85], [48300, 85], [48600, 85], [48900, 85], [49200, 85], [49500, 85], [49800, 85], [50100, 85], [50400, 85], [50700, 85], [51000, 85], [51300, 85], [51600, 85], [51900, 38], [51968, 16], [52200, 34], [52276, 8], [52500, 32], [52581, 3], [52800, 32], [53100, 32], [53400, 33], [53700, 33], [54000, 33], [54300, 34], [54600, 34], [54900, 34], [55200, 35], [55500, 35], [55800, 35], [56100, 36], [56183, 1], [56400, 36], [56700, 36], [57000, 36], [57082, 1], [57300, 37], [57382, 1], [57600, 37], [57681, 2], [57900, 37], [57981, 2], [58200, 37], [58281, 2], [58500, 37], [58580, 3], [58800, 38], [58880, 3]], "point": [46, 110]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-01.15|+02.02|-01.98"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 25, 93, 197], "mask": [[7200, 94], [7500, 94], [7800, 94], [8100, 94], [8400, 94], [8700, 94], [9000, 94], [9300, 94], [9600, 94], [9900, 94], [10200, 94], [10500, 94], [10800, 93], [11100, 93], [11400, 93], [11700, 93], [12000, 93], [12300, 93], [12600, 93], [12900, 93], [13200, 93], [13500, 93], [13800, 93], [14100, 93], [14400, 93], [14700, 93], [15000, 93], [15300, 92], [15600, 92], [15900, 92], [16200, 92], [16500, 92], [16800, 92], [17100, 92], [17400, 92], [17700, 92], [18000, 92], [18300, 92], [18600, 92], [18900, 92], [19200, 92], [19500, 92], [19800, 92], [20100, 91], [20400, 91], [20700, 91], [21000, 91], [21300, 91], [21600, 91], [21900, 91], [22200, 91], [22500, 91], [22800, 91], [23100, 91], [23400, 91], [23700, 91], [24000, 91], [24300, 91], [24600, 90], [24900, 90], [25200, 90], [25500, 90], [25800, 90], [26100, 90], [26400, 90], [26700, 90], [27000, 90], [27300, 90], [27600, 90], [27900, 90], [28200, 90], [28500, 90], [28800, 90], [29100, 89], [29400, 89], [29700, 89], [30000, 89], [30300, 89], [30600, 89], [30900, 89], [31200, 89], [31500, 89], [31800, 89], [32100, 89], [32400, 89], [32700, 89], [33000, 89], [33300, 89], [33600, 88], [33900, 88], [34200, 88], [34500, 88], [34800, 88], [35100, 88], [35400, 88], [35700, 88], [36000, 88], [36300, 88], [36600, 88], [36900, 88], [37200, 88], [37500, 88], [37800, 88], [38100, 87], [38400, 87], [38700, 87], [39000, 87], [39300, 87], [39600, 87], [39900, 87], [40200, 87], [40500, 87], [40800, 87], [41100, 87], [41400, 87], [41700, 87], [42000, 87], [42300, 87], [42600, 86], [42900, 86], [43200, 86], [43500, 86], [43800, 86], [44100, 86], [44400, 86], [44700, 86], [45000, 86], [45300, 86], [45600, 86], [45900, 86], [46200, 86], [46500, 86], [46800, 86], [47100, 86], [47400, 85], [47700, 85], [48000, 85], [48300, 85], [48600, 85], [48900, 85], [49200, 85], [49500, 85], [49800, 85], [50100, 85], [50406, 79], [50716, 69], [51021, 64], [51323, 62], [51627, 58], [51928, 10], [51968, 16], [52223, 2], [52228, 6], [52276, 8], [52523, 2], [52529, 3], [52581, 3], [52823, 2], [52829, 3], [53123, 2], [53129, 3], [53423, 2], [53428, 5], [53723, 2], [53728, 5], [54022, 3], [54028, 5], [54322, 2], [54328, 6], [54622, 2], [54628, 6], [54922, 2], [54927, 7], [55222, 1], [55227, 8], [55522, 1], [55526, 9], [55826, 9], [56125, 11], [56183, 1], [56424, 12], [56723, 13], [57021, 15], [57082, 1], [57321, 16], [57382, 1], [57621, 16], [57681, 2], [57921, 16], [57981, 2], [58221, 16], [58281, 2], [58521, 16], [58580, 3], [58820, 18], [58880, 3]], "point": [46, 110]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan20", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 0.5, "y": 0.9009992, "z": -1.25}, "object_poses": [{"objectName": "Potato_4e602a34", "position": {"x": 0.522321761, "y": 0.9599505, "z": 0.534036636}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8db17fc4", "position": {"x": 0.00282304, "y": 0.913542032, "z": -1.73793662}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_620f4eaa", "position": {"x": 1.82878232, "y": 0.914428651, "z": -1.21257389}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_33e0bb4d", "position": {"x": -0.0470661819, "y": 0.9456444, "z": 0.30533576}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_a85d92ad", "position": {"x": 0.522321761, "y": 0.9280619, "z": 0.99143815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_8bcb7893", "position": {"x": -1.32321119, "y": 0.9130036, "z": -0.702999532}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_5f9ea4fe", "position": {"x": -1.11647415, "y": 0.126789391, "z": -1.53853929}, "rotation": {"x": -1.40334208e-14, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_5f9ea4fe", "position": {"x": -1.61046207, "y": 0.8596686, "z": -0.9100088}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_d1e14b30", "position": {"x": -0.0470661819, "y": 0.934055865, "z": 0.7627374}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d1e14b30", "position": {"x": -1.49556208, "y": 1.18709588, "z": -0.703}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Knife_416e93ae", "position": {"x": -0.06476809, "y": 0.78753674, "z": -2.0264}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_416e93ae", "position": {"x": 1.73830819, "y": 0.936580539, "z": -1.56350493}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_11e2985b", "position": {"x": 1.82878232, "y": 0.9121536, "z": -1.79745913}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_aebda124", "position": {"x": 1.850235, "y": 0.994656861, "z": -2.08555484}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_aebda124", "position": {"x": -1.55301261, "y": 1.26484132, "z": -0.49599272}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_6ea32579", "position": {"x": -0.737933934, "y": 0.108194053, "z": -1.87334752}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6ea32579", "position": {"x": 1.55736017, "y": 0.909806132, "z": -1.446528}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b4d69cc9", "position": {"x": 0.294566572, "y": 0.972077966, "z": 0.534036636}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_792121da", "position": {"x": -1.49556148, "y": 1.25460982, "z": -0.9100075}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Potato_4e602a34", "position": {"x": -1.43811083, "y": 0.8887561, "z": -1.01351249}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_980e4fc9", "position": {"x": -1.27728152, "y": 1.6562798, "z": -1.60499954}, "rotation": {"x": -1.40334208e-14, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_11e2985b", "position": {"x": 1.87150991, "y": 1.65629137, "z": -1.86672354}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_a1e5049a", "position": {"x": 1.6181078, "y": 0.914048553, "z": -1.91844535}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Ladle_bd8f96dd", "position": {"x": 0.06681141, "y": 0.975006938, "z": 1.44883978}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b4d69cc9", "position": {"x": 0.399769247, "y": 0.9645044, "z": 0.111001275}, "rotation": {"x": 0.00259749033, "y": 0.00691786269, "z": 1.60184419}}, {"objectName": "Lettuce_aebda124", "position": {"x": -1.429, "y": 0.9252, "z": -0.542}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "WineBottle_620f4eaa", "position": {"x": 1.94726229, "y": 1.65856647, "z": -1.47990346}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_33e0bb4d", "position": {"x": 0.408444166, "y": 0.9456444, "z": 1.220139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d1e14b30", "position": {"x": 1.63150012, "y": 0.123936236, "z": -1.39772534}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_725f4a28", "position": {"x": -1.02880442, "y": 0.122877, "z": -1.46382689}, "rotation": {"x": -1.40334208e-14, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_d7116d9f", "position": {"x": 1.5675, "y": 0.948799968, "z": -0.7576}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_5f9ea4fe", "position": {"x": 0.294566572, "y": 0.930863, "z": 0.99143815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6ea32579", "position": {"x": 1.36203218, "y": 0.8771372, "z": 2.51076818}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_bc4f8581", "position": {"x": 1.82486081, "y": 0.9453135, "z": -0.3461054}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8db17fc4", "position": {"x": 0.522321761, "y": 0.9269746, "z": 1.44883978}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_de47916c", "position": {"x": 1.87252116, "y": 0.138341591, "z": 0.121947385}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_c9c37476", "position": {"x": 0.325175256, "y": 1.65853035, "z": -2.16299129}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_8bcb7893", "position": {"x": -0.06476809, "y": 0.8180097, "z": -2.11622977}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_a85d92ad", "position": {"x": 0.06681141, "y": 0.9280619, "z": 1.220139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_416e93ae", "position": {"x": -0.230146751, "y": 0.936580539, "z": -1.71353471}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_0e2bb95a", "position": {"x": -0.0470661819, "y": 0.9256445, "z": 0.076635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1601634273, "scene_num": 20}, "task_id": "trial_T20190908_230154_566315", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A20FCMWP43CVIU_3AMYWKA6YEDDLKS8ONDWJFN0J11O6L", "high_descs": ["walk to face right side of kitchen table", "pick up white mug from table", "turn to face microwave", "heat mug in microwave, remove mug from microwave", "gaze upward to cabinet above microwave", "put mug in cabinet"], "task_desc": "put heated mug into a cabinet", "votes": [1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3H7XDTSHKFINNMQGRMDSC0DBGH0GWO", "high_descs": ["Walk forward to the counter then turn right and walk to the counter then step to your left and walk one step forward then turn right.", "Pick up the mug that's in front of you.", "Turn right and walk to the counter in front of you then turn right.", "Warm the mug in the microwave then take it back out and close the door.", "Look up at the cupboard above the microwave.", "Open the cupboard above the sink and put the mug to the left of the clear bowl before closing the door."], "task_desc": "Put a warm mug in the cupboard.", "votes": [1, 1]}, {"assignment_id": "ALKQPW0O9C98N_3ZWFC4W1UXYC1QDZH8X47G5INNKRFW", "high_descs": ["Turn right to face the counter in the middle of the room", "Pick up the coffee cup from the counter", "Turn right and go to the microwave by the refrigerator", "Open the microwave door, put the mug in the microwave and close the door. Turn on the microwave and then open the door and take out the cup", "Look to the cabinets above the microwave", "Open the cabinet door, put the mug to the left of the bowl and close the door."], "task_desc": "Put a microwaved coffee mug in a cabinet", "votes": [1, 1]}]}}