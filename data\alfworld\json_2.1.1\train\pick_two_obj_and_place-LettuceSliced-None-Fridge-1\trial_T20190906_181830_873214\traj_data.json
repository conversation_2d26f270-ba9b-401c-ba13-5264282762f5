{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000205.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000206.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000207.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000208.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000209.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000210.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000211.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000212.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000213.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000214.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000215.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000216.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000217.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000218.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000219.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000220.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000221.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000222.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000223.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000224.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000225.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000317.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000318.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000319.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000320.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000321.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000322.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000323.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000324.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000325.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000326.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000327.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000328.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000329.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000330.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000331.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000332.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000333.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000334.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000335.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000336.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000337.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000338.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000339.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000340.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000341.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000342.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000343.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000344.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000345.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000346.png", "low_idx": 66}, {"high_idx": 6, "image_name": "000000347.png", "low_idx": 66}, {"high_idx": 6, "image_name": "000000348.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000349.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000350.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000351.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000352.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000353.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000354.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000355.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000356.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000357.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000358.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000359.png", "low_idx": 68}, {"high_idx": 6, "image_name": "000000360.png", "low_idx": 68}, {"high_idx": 6, "image_name": "000000361.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000362.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000363.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000364.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000365.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000366.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000367.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000368.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000369.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000370.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000371.png", "low_idx": 69}, {"high_idx": 7, "image_name": "000000372.png", "low_idx": 70}, {"high_idx": 7, "image_name": "000000373.png", "low_idx": 70}, {"high_idx": 7, "image_name": "000000374.png", "low_idx": 70}, {"high_idx": 7, "image_name": "000000375.png", "low_idx": 70}, {"high_idx": 7, "image_name": "000000376.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000377.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000378.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000379.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000380.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000381.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000382.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000383.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000384.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000385.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000386.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000387.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000388.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000389.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000390.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000391.png", "low_idx": 72}, {"high_idx": 7, "image_name": "000000392.png", "low_idx": 72}, {"high_idx": 7, "image_name": "000000393.png", "low_idx": 72}, {"high_idx": 7, "image_name": "000000394.png", "low_idx": 72}, {"high_idx": 8, "image_name": "000000395.png", "low_idx": 73}, {"high_idx": 8, "image_name": "000000396.png", "low_idx": 73}, {"high_idx": 8, "image_name": "000000397.png", "low_idx": 73}, {"high_idx": 8, "image_name": "000000398.png", "low_idx": 73}, {"high_idx": 8, "image_name": "000000399.png", "low_idx": 73}, {"high_idx": 8, "image_name": "000000400.png", "low_idx": 73}, {"high_idx": 8, "image_name": "000000401.png", "low_idx": 73}, {"high_idx": 8, "image_name": "000000402.png", "low_idx": 73}, {"high_idx": 8, "image_name": "000000403.png", "low_idx": 73}, {"high_idx": 8, "image_name": "000000404.png", "low_idx": 73}, {"high_idx": 8, "image_name": "000000405.png", "low_idx": 73}, {"high_idx": 8, "image_name": "000000406.png", "low_idx": 74}, {"high_idx": 8, "image_name": "000000407.png", "low_idx": 74}, {"high_idx": 8, "image_name": "000000408.png", "low_idx": 74}, {"high_idx": 8, "image_name": "000000409.png", "low_idx": 74}, {"high_idx": 8, "image_name": "000000410.png", "low_idx": 74}, {"high_idx": 8, "image_name": "000000411.png", "low_idx": 74}, {"high_idx": 8, "image_name": "000000412.png", "low_idx": 74}, {"high_idx": 8, "image_name": "000000413.png", "low_idx": 74}, {"high_idx": 8, "image_name": "000000414.png", "low_idx": 74}, {"high_idx": 8, "image_name": "000000415.png", "low_idx": 74}, {"high_idx": 8, "image_name": "000000416.png", "low_idx": 74}, {"high_idx": 8, "image_name": "000000417.png", "low_idx": 75}, {"high_idx": 8, "image_name": "000000418.png", "low_idx": 75}, {"high_idx": 8, "image_name": "000000419.png", "low_idx": 76}, {"high_idx": 8, "image_name": "000000420.png", "low_idx": 76}, {"high_idx": 8, "image_name": "000000421.png", "low_idx": 77}, {"high_idx": 8, "image_name": "000000422.png", "low_idx": 77}, {"high_idx": 8, "image_name": "000000423.png", "low_idx": 78}, {"high_idx": 8, "image_name": "000000424.png", "low_idx": 78}, {"high_idx": 8, "image_name": "000000425.png", "low_idx": 79}, {"high_idx": 8, "image_name": "000000426.png", "low_idx": 79}, {"high_idx": 8, "image_name": "000000427.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000428.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000429.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000430.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000431.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000432.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000433.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000434.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000435.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000436.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000437.png", "low_idx": 85}, {"high_idx": 8, "image_name": "000000438.png", "low_idx": 85}, {"high_idx": 9, "image_name": "000000439.png", "low_idx": 86}, {"high_idx": 9, "image_name": "000000440.png", "low_idx": 86}, {"high_idx": 9, "image_name": "000000441.png", "low_idx": 86}, {"high_idx": 9, "image_name": "000000442.png", "low_idx": 86}, {"high_idx": 9, "image_name": "000000443.png", "low_idx": 86}, {"high_idx": 9, "image_name": "000000444.png", "low_idx": 86}, {"high_idx": 9, "image_name": "000000445.png", "low_idx": 86}, {"high_idx": 9, "image_name": "000000446.png", "low_idx": 86}, {"high_idx": 9, "image_name": "000000447.png", "low_idx": 86}, {"high_idx": 9, "image_name": "000000448.png", "low_idx": 86}, {"high_idx": 9, "image_name": "000000449.png", "low_idx": 86}, {"high_idx": 9, "image_name": "000000450.png", "low_idx": 86}, {"high_idx": 9, "image_name": "000000451.png", "low_idx": 86}, {"high_idx": 9, "image_name": "000000452.png", "low_idx": 86}, {"high_idx": 9, "image_name": "000000453.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000454.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000455.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000456.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000457.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000458.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000459.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000460.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000461.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000462.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000463.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000464.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000465.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000466.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000467.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000468.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000469.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000470.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000471.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000472.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000473.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000474.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000475.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000476.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000477.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000478.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000479.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000480.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000481.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000482.png", "low_idx": 92}, {"high_idx": 10, "image_name": "000000483.png", "low_idx": 92}, {"high_idx": 10, "image_name": "000000484.png", "low_idx": 93}, {"high_idx": 10, "image_name": "000000485.png", "low_idx": 93}, {"high_idx": 10, "image_name": "000000486.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000487.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000488.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000489.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000490.png", "low_idx": 96}, {"high_idx": 10, "image_name": "000000491.png", "low_idx": 96}, {"high_idx": 10, "image_name": "000000492.png", "low_idx": 97}, {"high_idx": 10, "image_name": "000000493.png", "low_idx": 97}, {"high_idx": 10, "image_name": "000000494.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000495.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000496.png", "low_idx": 99}, {"high_idx": 10, "image_name": "000000497.png", "low_idx": 99}, {"high_idx": 10, "image_name": "000000498.png", "low_idx": 100}, {"high_idx": 10, "image_name": "000000499.png", "low_idx": 100}, {"high_idx": 10, "image_name": "000000500.png", "low_idx": 100}, {"high_idx": 10, "image_name": "000000501.png", "low_idx": 100}, {"high_idx": 10, "image_name": "000000502.png", "low_idx": 100}, {"high_idx": 10, "image_name": "000000503.png", "low_idx": 100}, {"high_idx": 10, "image_name": "000000504.png", "low_idx": 100}, {"high_idx": 10, "image_name": "000000505.png", "low_idx": 100}, {"high_idx": 10, "image_name": "000000506.png", "low_idx": 100}, {"high_idx": 10, "image_name": "000000507.png", "low_idx": 100}, {"high_idx": 10, "image_name": "000000508.png", "low_idx": 100}, {"high_idx": 10, "image_name": "000000509.png", "low_idx": 101}, {"high_idx": 10, "image_name": "000000510.png", "low_idx": 101}, {"high_idx": 10, "image_name": "000000511.png", "low_idx": 101}, {"high_idx": 10, "image_name": "000000512.png", "low_idx": 101}, {"high_idx": 10, "image_name": "000000513.png", "low_idx": 101}, {"high_idx": 10, "image_name": "000000514.png", "low_idx": 101}, {"high_idx": 10, "image_name": "000000515.png", "low_idx": 101}, {"high_idx": 10, "image_name": "000000516.png", "low_idx": 101}, {"high_idx": 10, "image_name": "000000517.png", "low_idx": 101}, {"high_idx": 10, "image_name": "000000518.png", "low_idx": 101}, {"high_idx": 10, "image_name": "000000519.png", "low_idx": 101}, {"high_idx": 11, "image_name": "000000520.png", "low_idx": 102}, {"high_idx": 11, "image_name": "000000521.png", "low_idx": 102}, {"high_idx": 11, "image_name": "000000522.png", "low_idx": 102}, {"high_idx": 11, "image_name": "000000523.png", "low_idx": 102}, {"high_idx": 11, "image_name": "000000524.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000525.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000526.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000527.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000528.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000529.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000530.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000531.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000532.png", "low_idx": 103}, {"high_idx": 11, "image_name": "000000533.png", "low_idx": 103}, {"high_idx": 12, "image_name": "000000534.png", "low_idx": 104}, {"high_idx": 12, "image_name": "000000535.png", "low_idx": 104}, {"high_idx": 12, "image_name": "000000536.png", "low_idx": 104}, {"high_idx": 12, "image_name": "000000537.png", "low_idx": 104}, {"high_idx": 12, "image_name": "000000538.png", "low_idx": 104}, {"high_idx": 12, "image_name": "000000539.png", "low_idx": 104}, {"high_idx": 12, "image_name": "000000540.png", "low_idx": 104}, {"high_idx": 12, "image_name": "000000541.png", "low_idx": 104}, {"high_idx": 12, "image_name": "000000542.png", "low_idx": 104}, {"high_idx": 12, "image_name": "000000543.png", "low_idx": 104}, {"high_idx": 12, "image_name": "000000544.png", "low_idx": 105}, {"high_idx": 12, "image_name": "000000545.png", "low_idx": 105}, {"high_idx": 12, "image_name": "000000546.png", "low_idx": 105}, {"high_idx": 12, "image_name": "000000547.png", "low_idx": 105}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Lettuce", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|-1|3|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [0.8691628, 0.8691628, -1.015095712, -1.015095712, 4.7648902, 4.7648902]], "coordinateReceptacleObjectId": ["CounterTop", [-0.316, -0.316, -0.004, -0.004, 4.5884, 4.5884]], "forceVisible": true, "objectId": "Lettuce|+00.22|+01.19|-00.25"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|4|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [0.8691628, 0.8691628, -1.015095712, -1.015095712, 4.7648902, 4.7648902]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-8.388, -8.388, 4.352, 4.352, 0.0, 0.0]], "forceVisible": true, "objectId": "Lettuce|+00.22|+01.19|-00.25", "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|5|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-1.3419616, -1.3419616, 3.0292868, 3.0292868, 4.7648902, 4.7648902]], "coordinateReceptacleObjectId": ["CounterTop", [-0.316, -0.316, -0.004, -0.004, 4.5884, 4.5884]], "forceVisible": true, "objectId": "Lettuce|-00.34|+01.19|+00.76"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-5|4|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-1.3419616, -1.3419616, 3.0292868, 3.0292868, 4.7648902, 4.7648902]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-8.388, -8.388, 4.352, 4.352, 0.0, 0.0]], "forceVisible": true, "objectId": "Lettuce|-00.34|+01.19|+00.76", "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-7|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 9, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [-6.34405424, -6.34405424, -10.241086, -10.241086, 3.645768164, 3.645768164]], "coordinateReceptacleObjectId": ["CounterTop", [-7.472, -7.472, -4.824, -4.824, 3.7876, 3.7876]], "forceVisible": true, "objectId": "ButterKnife|-01.59|+00.91|-02.56"}}, {"discrete_action": {"action": "GotoLocation", "args": ["lettuce"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-5|4|3|60"}}, {"discrete_action": {"action": "SliceObject", "args": ["lettuce"]}, "high_idx": 11, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Lettuce", [0.8691628, 0.8691628, -1.015095712, -1.015095712, 4.7648902, 4.7648902]], "forceVisible": true, "objectId": "Lettuce|+00.22|+01.19|-00.25"}}, {"discrete_action": {"action": "SliceObject", "args": ["lettuce"]}, "high_idx": 12, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Lettuce", [-1.3419616, -1.3419616, 3.0292868, 3.0292868, 4.7648902, 4.7648902]], "forceVisible": true, "objectId": "Lettuce|-00.34|+01.19|+00.76"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 13, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|+00.22|+01.19|-00.25"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [112, 116, 184, 161], "mask": [[34629, 9], [34925, 22], [35223, 27], [35522, 31], [35820, 36], [36118, 42], [36417, 47], [36717, 49], [37016, 52], [37315, 55], [37615, 57], [37914, 60], [38214, 61], [38513, 64], [38813, 66], [39113, 68], [39413, 70], [39713, 71], [40012, 72], [40312, 72], [40612, 72], [40912, 73], [41212, 73], [41512, 73], [41812, 72], [42112, 72], [42412, 72], [42713, 71], [43013, 70], [43313, 69], [43613, 68], [43913, 66], [44213, 64], [44514, 62], [44814, 60], [45115, 57], [45415, 55], [45716, 52], [46016, 50], [46317, 47], [46618, 43], [46919, 39], [47221, 34], [47523, 28], [47825, 22], [48127, 11]], "point": [148, 137]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 222], "mask": [[0, 17100], [17101, 299], [17402, 298], [17707, 293], [18008, 292], [18308, 292], [18609, 291], [18909, 291], [19210, 290], [19511, 289], [19811, 289], [20112, 288], [20412, 288], [20713, 287], [21014, 286], [21314, 286], [21615, 285], [21915, 285], [22216, 284], [22517, 283], [22817, 283], [23118, 282], [23418, 282], [23719, 281], [24020, 280], [24320, 280], [24621, 279], [24921, 279], [25222, 278], [25523, 277], [25823, 277], [26124, 276], [26424, 276], [26725, 275], [27025, 275], [27326, 274], [27627, 273], [27927, 273], [28228, 272], [28528, 272], [28829, 271], [29130, 270], [29430, 270], [29731, 269], [30031, 269], [30332, 268], [30633, 267], [30933, 267], [31234, 266], [31534, 266], [31835, 265], [32136, 264], [32436, 264], [32737, 263], [33037, 263], [33338, 262], [33639, 261], [33939, 261], [34240, 260], [34540, 260], [34841, 259], [35142, 258], [35442, 258], [35743, 257], [36043, 257], [36344, 256], [36645, 255], [36945, 255], [37246, 254], [37546, 254], [37847, 253], [38147, 253], [38448, 252], [38749, 251], [39049, 251], [39350, 250], [39650, 250], [39951, 249], [40252, 248], [40552, 248], [40853, 247], [41153, 247], [41454, 246], [41755, 245], [42055, 245], [42356, 244], [42656, 244], [42957, 243], [43258, 242], [43558, 242], [43859, 241], [44159, 241], [44460, 239], [44761, 237], [45061, 236], [45362, 234], [45662, 233], [45963, 231], [46264, 229], [46564, 228], [46865, 226], [47165, 225], [47466, 223], [47767, 221], [48067, 220], [48368, 218], [48668, 217], [48969, 215], [49270, 213], [49570, 212], [49871, 210], [50171, 209], [50472, 207], [50772, 206], [51073, 204], [51374, 202], [51674, 201], [51975, 199], [52275, 198], [52576, 196], [52877, 194], [53177, 193], [53478, 191], [53778, 190], [54079, 189], [54380, 187], [54680, 186], [54981, 184], [55281, 183], [55582, 181], [55883, 179], [56183, 178], [56484, 176], [56784, 175], [57085, 173], [57386, 171], [57686, 170], [57987, 168], [58287, 167], [58588, 165], [58889, 163], [59189, 162], [59489, 56], [59554, 96], [59789, 55], [59855, 94], [60089, 53], [60156, 92], [60389, 52], [60458, 89], [60689, 50], [60760, 86], [60989, 49], [61061, 84], [61289, 48], [61362, 82], [61588, 48], [61664, 79], [61888, 48], [61965, 77], [62188, 47], [62266, 75], [62489, 45], [62567, 74], [62790, 43], [62868, 72], [63091, 41], [63169, 70], [63391, 40], [63470, 68], [63693, 37], [63771, 65], [63996, 33], [64072, 60], [64300, 28], [64373, 56], [64603, 24], [64673, 53], [64907, 20], [64974, 48], [65212, 14], [65275, 42], [65518, 7], [65576, 35], [65877, 28], [66178, 19], [66478, 10]], "point": [149, 110]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|+00.22|+01.19|-00.25", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 31993], [32008, 284], [32309, 282], [32611, 280], [32911, 279], [33212, 278], [33512, 278], [33812, 278], [34112, 278], [34412, 278], [34712, 87], [34800, 190], [35012, 86], [35100, 190], [35311, 86], [35400, 191], [35610, 87], [35700, 191], [35910, 86], [36000, 192], [36209, 86], [36300, 192], [36509, 85], [36600, 194], [36808, 85], [36900, 195], [37106, 87], [37200, 198], [37404, 88], [37500, 291], [37800, 290], [38100, 289], [38400, 288], [38700, 288], [39000, 287], [39300, 286], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 278], [42600, 277], [42900, 276], [43200, 276], [43500, 275], [43800, 274], [44100, 273], [44400, 272], [44700, 272], [45000, 271], [45300, 270], [45600, 269], [45900, 268], [46200, 267], [46500, 267], [46800, 266], [47100, 265], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 240], [56700, 239], [57000, 239], [57300, 239], [57600, 239], [57900, 239], [58200, 239], [58500, 240], [58800, 240], [59100, 240], [59400, 145], [59554, 86], [59700, 144], [59855, 85], [60000, 142], [60156, 84], [60300, 141], [60458, 83], [60600, 139], [60760, 81], [60900, 138], [61061, 80], [61200, 137], [61362, 79], [61500, 136], [61664, 77], [61800, 136], [61965, 77], [62100, 135], [62266, 75], [62400, 134], [62567, 74], [62700, 89], [62790, 43], [62868, 72], [63000, 89], [63091, 41], [63169, 70], [63300, 89], [63391, 40], [63470, 68], [63600, 88], [63693, 37], [63771, 65], [63900, 88], [63996, 33], [64072, 60], [64200, 88], [64300, 28], [64373, 56], [64500, 88], [64603, 24], [64673, 53], [64800, 88], [64907, 20], [64974, 48], [65100, 88], [65212, 14], [65275, 42], [65400, 88], [65518, 7], [65576, 35], [65700, 88], [65877, 28], [66000, 87], [66178, 19], [66300, 87], [66478, 10], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16033], [16068, 265], [16367, 267], [16667, 267], [16967, 267], [17266, 268], [17566, 268], [17865, 269], [18165, 269], [18465, 270], [18764, 271], [19064, 271], [19364, 272], [19663, 273], [19963, 274], [20262, 275], [20562, 276], [20861, 278], [21161, 278], [21460, 280], [21759, 282], [22059, 283], [22358, 285], [22658, 286], [22957, 288], [23257, 288], [23557, 289], [23856, 291], [24155, 294], [24454, 7539], [32008, 284], [32309, 282], [32611, 280], [32911, 279], [33212, 278], [33512, 278], [33812, 278], [34112, 278], [34412, 278], [34712, 87], [34800, 190], [35012, 86], [35100, 190], [35311, 86], [35400, 191], [35610, 87], [35700, 191], [35910, 86], [36000, 192], [36209, 86], [36300, 192], [36509, 85], [36600, 194], [36808, 85], [36900, 195], [37106, 87], [37200, 198], [37404, 88], [37500, 291], [37800, 290], [38100, 289], [38400, 288], [38700, 288], [39000, 287], [39300, 286], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 278], [42600, 277], [42900, 276], [43200, 276], [43500, 275], [43800, 274], [44100, 273], [44400, 272], [44700, 272], [45000, 271], [45300, 270], [45600, 269], [45900, 268], [46200, 267], [46500, 267], [46800, 266], [47100, 265], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 240], [56700, 239], [57000, 239], [57300, 239], [57600, 239], [57900, 239], [58200, 239], [58500, 240], [58800, 240], [59100, 240], [59400, 240], [59700, 240], [60000, 240], [60300, 241], [60600, 241], [60900, 241], [61200, 241], [61500, 241], [61800, 242], [62100, 241], [62400, 241], [62700, 89], [62790, 150], [63000, 89], [63091, 148], [63300, 89], [63391, 147], [63600, 88], [63693, 143], [63900, 88], [63996, 136], [64200, 88], [64300, 129], [64500, 88], [64603, 123], [64800, 88], [64907, 115], [65100, 88], [65212, 105], [65400, 88], [65518, 93], [65700, 88], [65824, 81], [66000, 87], [66132, 65], [66300, 87], [66442, 46], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-00.34|+01.19|+00.76"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [155, 81, 209, 165], "mask": [[24180, 2], [24478, 9], [24775, 18], [25073, 23], [25371, 27], [25669, 30], [25968, 32], [26266, 36], [26565, 38], [26864, 40], [27163, 42], [27462, 43], [27761, 45], [28061, 45], [28360, 47], [28660, 47], [28959, 49], [29259, 49], [29558, 51], [29858, 51], [30158, 51], [30458, 51], [30757, 53], [31057, 53], [31357, 53], [31657, 53], [31957, 53], [32257, 53], [32557, 53], [32857, 53], [33157, 53], [33457, 53], [33756, 54], [34056, 54], [34356, 54], [34656, 54], [34955, 55], [35255, 55], [35555, 55], [35855, 55], [36155, 55], [36455, 55], [36755, 55], [37056, 54], [37356, 54], [37656, 54], [37956, 54], [38256, 53], [38556, 53], [38857, 52], [39157, 52], [39457, 52], [39758, 51], [40058, 51], [40359, 50], [40659, 50], [40960, 48], [41260, 48], [41560, 48], [41861, 46], [42162, 45], [42462, 45], [42763, 43], [43064, 42], [43364, 41], [43665, 40], [43966, 39], [44267, 37], [44567, 37], [44868, 35], [45169, 34], [45470, 32], [45771, 31], [46072, 30], [46373, 29], [46674, 27], [46975, 26], [47277, 23], [47578, 22], [47879, 20], [48180, 19], [48481, 18], [48782, 16], [49083, 13], [49385, 10]], "point": [182, 122]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 222], "mask": [[0, 17100], [17101, 299], [17402, 298], [17707, 293], [18008, 292], [18308, 292], [18609, 291], [18909, 291], [19210, 290], [19511, 289], [19811, 289], [20112, 288], [20412, 288], [20713, 287], [21014, 286], [21314, 286], [21615, 285], [21915, 285], [22216, 284], [22517, 283], [22817, 283], [23118, 282], [23418, 282], [23719, 281], [24020, 280], [24320, 280], [24621, 279], [24921, 279], [25222, 278], [25523, 277], [25823, 277], [26124, 276], [26424, 276], [26725, 275], [27025, 275], [27326, 274], [27627, 273], [27927, 273], [28228, 272], [28528, 272], [28829, 271], [29130, 270], [29430, 270], [29731, 269], [30031, 269], [30332, 268], [30633, 267], [30933, 267], [31234, 266], [31534, 266], [31835, 265], [32136, 264], [32436, 264], [32737, 263], [33037, 263], [33338, 262], [33639, 261], [33939, 261], [34240, 260], [34540, 260], [34841, 259], [35142, 258], [35442, 258], [35743, 257], [36043, 257], [36344, 256], [36645, 255], [36945, 255], [37246, 254], [37546, 254], [37847, 253], [38147, 253], [38448, 252], [38749, 251], [39049, 251], [39350, 250], [39650, 250], [39951, 249], [40252, 248], [40552, 248], [40853, 247], [41153, 247], [41454, 246], [41755, 245], [42055, 245], [42356, 244], [42656, 244], [42957, 243], [43258, 242], [43558, 242], [43859, 241], [44159, 241], [44460, 239], [44761, 237], [45061, 236], [45362, 234], [45662, 233], [45963, 231], [46264, 229], [46564, 228], [46865, 226], [47165, 225], [47466, 223], [47767, 221], [48067, 220], [48368, 218], [48668, 217], [48969, 215], [49270, 213], [49570, 212], [49871, 210], [50171, 209], [50472, 207], [50772, 206], [51073, 204], [51374, 202], [51674, 201], [51975, 199], [52275, 198], [52576, 196], [52877, 194], [53177, 193], [53478, 191], [53778, 190], [54079, 189], [54380, 187], [54680, 186], [54981, 184], [55281, 183], [55582, 181], [55883, 179], [56183, 178], [56484, 61], [56554, 106], [56784, 59], [56856, 103], [57085, 56], [57158, 100], [57386, 54], [57459, 98], [57686, 53], [57760, 96], [57987, 51], [58061, 94], [58287, 50], [58362, 92], [58588, 49], [58662, 91], [58889, 47], [58963, 89], [59189, 46], [59264, 87], [59489, 46], [59565, 85], [59789, 45], [59865, 84], [60089, 44], [60166, 82], [60389, 44], [60467, 80], [60689, 43], [60767, 79], [60989, 43], [61068, 77], [61289, 42], [61369, 75], [61588, 42], [61670, 73], [61888, 42], [61970, 72], [62188, 41], [62271, 70], [62489, 39], [62572, 69], [62790, 38], [62872, 68], [63091, 36], [63173, 66], [63391, 35], [63474, 64], [63693, 33], [63774, 62], [63996, 29], [64075, 57], [64300, 25], [64375, 54], [64603, 21], [64676, 50], [64907, 16], [64977, 45], [65212, 11], [65277, 40], [65518, 4], [65578, 33], [65878, 27], [66179, 18], [66479, 9]], "point": [149, 110]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-00.34|+01.19|+00.76", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16033], [16068, 265], [16367, 267], [16667, 267], [16967, 267], [17266, 268], [17566, 268], [17865, 269], [18165, 269], [18465, 270], [18764, 271], [19064, 271], [19364, 272], [19663, 273], [19963, 274], [20262, 275], [20562, 276], [20861, 278], [21161, 278], [21460, 280], [21759, 282], [22059, 283], [22358, 285], [22658, 286], [22957, 288], [23257, 288], [23557, 289], [23856, 291], [24155, 294], [24454, 7539], [32008, 284], [32309, 282], [32611, 280], [32911, 279], [33212, 278], [33512, 278], [33812, 278], [34112, 278], [34412, 278], [34712, 87], [34800, 190], [35012, 86], [35100, 190], [35311, 86], [35400, 191], [35610, 87], [35700, 191], [35910, 86], [36000, 192], [36209, 86], [36300, 192], [36509, 85], [36600, 194], [36808, 85], [36900, 195], [37106, 87], [37200, 198], [37404, 88], [37500, 291], [37800, 290], [38100, 289], [38400, 288], [38700, 288], [39000, 287], [39300, 286], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 278], [42600, 277], [42900, 276], [43200, 276], [43500, 275], [43800, 274], [44100, 273], [44400, 272], [44700, 272], [45000, 271], [45300, 270], [45600, 269], [45900, 268], [46200, 267], [46500, 267], [46800, 266], [47100, 265], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 145], [56554, 86], [56700, 143], [56856, 83], [57000, 141], [57158, 81], [57300, 140], [57459, 80], [57600, 139], [57760, 79], [57900, 138], [58061, 78], [58200, 137], [58362, 77], [58500, 137], [58662, 78], [58800, 136], [58963, 77], [59100, 135], [59264, 76], [59400, 135], [59565, 75], [59700, 134], [59865, 75], [60000, 133], [60166, 74], [60300, 133], [60467, 74], [60600, 132], [60767, 74], [60900, 132], [61068, 73], [61200, 131], [61369, 72], [61500, 130], [61670, 71], [61800, 130], [61970, 72], [62100, 129], [62271, 70], [62400, 128], [62572, 69], [62700, 89], [62790, 38], [62872, 68], [63000, 89], [63091, 36], [63173, 66], [63300, 89], [63391, 35], [63474, 64], [63600, 88], [63693, 33], [63774, 62], [63900, 88], [63996, 29], [64075, 57], [64200, 88], [64300, 25], [64375, 54], [64500, 88], [64603, 21], [64676, 50], [64800, 88], [64907, 16], [64977, 45], [65100, 88], [65212, 11], [65277, 40], [65400, 88], [65518, 4], [65578, 33], [65700, 88], [65878, 27], [66000, 87], [66179, 18], [66300, 87], [66479, 9], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16033], [16068, 5], [16109, 224], [16367, 7], [16409, 225], [16667, 7], [16709, 225], [16967, 7], [17009, 225], [17266, 8], [17308, 226], [17566, 8], [17608, 226], [17865, 10], [17908, 226], [18165, 10], [18208, 226], [18465, 10], [18508, 227], [18764, 12], [18807, 228], [19064, 12], [19107, 228], [19364, 13], [19407, 229], [19663, 14], [19706, 230], [19963, 15], [20006, 231], [20262, 16], [20306, 231], [20562, 17], [20605, 233], [20861, 19], [20905, 234], [21161, 19], [21204, 235], [21460, 21], [21504, 236], [21759, 23], [21803, 238], [22059, 24], [22103, 239], [22358, 26], [22402, 241], [22658, 27], [22702, 242], [22957, 29], [23002, 243], [23257, 30], [23301, 244], [23557, 32], [23601, 245], [23856, 34], [23900, 247], [24155, 36], [24199, 250], [24454, 38], [24498, 7495], [32008, 284], [32309, 282], [32611, 280], [32911, 279], [33212, 278], [33512, 278], [33812, 278], [34112, 278], [34412, 278], [34712, 87], [34800, 190], [35012, 86], [35100, 190], [35311, 86], [35400, 191], [35610, 87], [35700, 191], [35910, 86], [36000, 192], [36209, 86], [36300, 192], [36509, 85], [36600, 194], [36808, 85], [36900, 195], [37106, 87], [37200, 198], [37404, 88], [37500, 291], [37800, 290], [38100, 289], [38400, 288], [38700, 288], [39000, 287], [39300, 286], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 278], [42600, 277], [42900, 276], [43200, 276], [43500, 275], [43800, 274], [44100, 273], [44400, 272], [44700, 272], [45000, 271], [45300, 270], [45600, 269], [45900, 268], [46200, 267], [46500, 267], [46800, 266], [47100, 265], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 240], [56700, 239], [57000, 239], [57300, 239], [57600, 239], [57900, 239], [58200, 239], [58500, 240], [58800, 240], [59100, 240], [59400, 240], [59700, 240], [60000, 240], [60300, 241], [60600, 241], [60900, 241], [61200, 241], [61500, 241], [61800, 242], [62100, 241], [62400, 241], [62700, 89], [62790, 150], [63000, 89], [63091, 148], [63300, 89], [63391, 147], [63600, 88], [63693, 143], [63900, 88], [63996, 136], [64200, 88], [64300, 129], [64500, 88], [64603, 123], [64800, 88], [64907, 115], [65100, 88], [65212, 105], [65400, 88], [65518, 93], [65700, 88], [65824, 81], [66000, 87], [66132, 65], [66300, 87], [66442, 46], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 84], [75600, 83], [75900, 83], [76200, 83], [76500, 83], [76800, 83], [77100, 83], [77400, 83], [77700, 82], [78000, 82], [78300, 82], [78600, 82], [78900, 82], [79200, 82], [79500, 82], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 7}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|-01.59|+00.91|-02.56"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [200, 122, 266, 126], "mask": [[36509, 14], [36803, 31], [36848, 17], [37100, 67], [37400, 67], [37752, 13]], "point": [233, 123]}}, "high_idx": 9}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 222], "mask": [[0, 17100], [17101, 299], [17402, 298], [17707, 293], [18008, 292], [18308, 292], [18609, 291], [18909, 291], [19210, 290], [19511, 289], [19811, 289], [20112, 288], [20412, 288], [20713, 287], [21014, 286], [21314, 286], [21615, 285], [21915, 285], [22216, 284], [22517, 283], [22817, 283], [23118, 282], [23418, 282], [23719, 281], [24020, 280], [24320, 280], [24621, 279], [24921, 279], [25222, 278], [25523, 277], [25823, 277], [26124, 276], [26424, 276], [26725, 275], [27025, 275], [27326, 274], [27627, 273], [27927, 273], [28228, 272], [28528, 272], [28829, 271], [29130, 270], [29430, 270], [29731, 269], [30031, 269], [30332, 268], [30633, 267], [30933, 267], [31234, 266], [31534, 266], [31835, 265], [32136, 264], [32436, 264], [32737, 263], [33037, 263], [33338, 262], [33639, 261], [33939, 261], [34240, 260], [34540, 260], [34841, 259], [35142, 258], [35442, 258], [35743, 257], [36043, 257], [36344, 256], [36645, 255], [36945, 255], [37246, 254], [37546, 254], [37847, 253], [38147, 253], [38448, 252], [38749, 251], [39049, 251], [39350, 250], [39650, 250], [39951, 249], [40252, 248], [40552, 248], [40853, 247], [41153, 247], [41454, 246], [41755, 245], [42055, 245], [42356, 244], [42656, 244], [42957, 243], [43258, 242], [43558, 242], [43859, 241], [44159, 241], [44460, 239], [44761, 237], [45061, 236], [45362, 234], [45662, 233], [45963, 231], [46264, 229], [46564, 228], [46865, 226], [47165, 225], [47466, 223], [47767, 221], [48067, 220], [48368, 218], [48668, 217], [48969, 215], [49270, 213], [49570, 212], [49871, 210], [50171, 209], [50472, 207], [50772, 206], [51073, 204], [51374, 202], [51674, 201], [51975, 199], [52275, 198], [52576, 196], [52877, 194], [53177, 193], [53478, 191], [53778, 190], [54079, 189], [54380, 187], [54680, 186], [54981, 184], [55281, 183], [55582, 181], [55883, 179], [56183, 178], [56484, 176], [56784, 175], [57085, 173], [57386, 171], [57686, 170], [57987, 168], [58287, 167], [58588, 165], [58889, 163], [59189, 162], [59489, 161], [59789, 160], [60089, 159], [60389, 158], [60689, 157], [60989, 156], [61289, 155], [61588, 155], [61888, 154], [62188, 153], [62489, 152], [62790, 150], [63091, 148], [63391, 147], [63693, 143], [63996, 136], [64300, 129], [64603, 123], [64907, 115], [65212, 105], [65518, 93], [65824, 81], [66132, 65], [66442, 46]], "point": [149, 110]}}, "high_idx": 11}, {"api_action": {"action": "SliceObject", "objectId": "Lettuce|+00.22|+01.19|-00.25"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [133, 54, 167, 82], "mask": [[16033, 35], [16333, 34], [16634, 33], [16934, 33], [17234, 32], [17534, 32], [17834, 31], [18134, 31], [18434, 31], [18735, 29], [19035, 29], [19335, 29], [19636, 27], [19936, 27], [20237, 25], [20537, 25], [20838, 23], [21139, 22], [21439, 21], [21740, 19], [22041, 18], [22342, 16], [22643, 15], [22944, 13], [23245, 12], [23545, 12], [23846, 10], [24147, 8], [24449, 5]], "point": [150, 67]}}, "high_idx": 11}, {"api_action": {"action": "SliceObject", "objectId": "Lettuce|-00.34|+01.19|+00.76"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [173, 54, 208, 82], "mask": [[16073, 36], [16374, 35], [16674, 35], [16974, 35], [17274, 34], [17574, 34], [17875, 33], [18175, 33], [18475, 33], [18776, 31], [19076, 31], [19377, 30], [19677, 29], [19978, 28], [20278, 28], [20579, 26], [20880, 25], [21180, 24], [21481, 23], [21782, 21], [22083, 20], [22384, 18], [22685, 17], [22986, 16], [23287, 14], [23589, 12], [23890, 10], [24191, 8], [24492, 6]], "point": [190, 67]}}, "high_idx": 12}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.10|+00.00|+01.09"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 16034], [16067, 7], [16108, 226], [16368, 6], [16408, 226], [16668, 5], [16709, 225], [16968, 5], [17009, 225], [17268, 6], [17309, 225], [17568, 6], [17609, 225], [17868, 6], [17909, 225], [18168, 6], [18209, 225], [18467, 7], [18509, 226], [18767, 7], [18809, 226], [19067, 8], [19109, 227], [19367, 8], [19408, 228], [19667, 8], [19708, 228], [19966, 10], [20008, 228], [20266, 10], [20308, 228], [20565, 12], [20608, 229], [20865, 13], [20908, 229], [21165, 13], [21208, 230], [21464, 15], [21507, 231], [21764, 15], [21807, 232], [22064, 16], [22106, 234], [22363, 17], [22406, 234], [22662, 19], [22705, 236], [22962, 20], [23005, 236], [23261, 21], [23304, 238], [23560, 23], [23604, 239], [23860, 25], [23903, 241], [24159, 27], [24203, 242], [24459, 28], [24503, 243], [24758, 30], [24802, 245], [25058, 31], [25101, 247], [25357, 33], [25401, 248], [25656, 35], [25700, 250], [25955, 38], [25998, 5995], [32008, 284], [32309, 282], [32611, 280], [32911, 279], [33212, 278], [33512, 278], [33812, 278], [34112, 278], [34412, 278], [34712, 87], [34800, 190], [35012, 86], [35100, 190], [35311, 86], [35400, 191], [35610, 87], [35700, 191], [35910, 86], [36000, 192], [36209, 86], [36300, 192], [36509, 85], [36600, 194], [36808, 85], [36900, 195], [37106, 87], [37200, 198], [37404, 88], [37500, 291], [37800, 290], [38100, 289], [38400, 288], [38700, 288], [39000, 287], [39300, 286], [39600, 285], [39900, 284], [40200, 284], [40500, 283], [40800, 282], [41100, 281], [41400, 280], [41700, 280], [42000, 279], [42300, 278], [42600, 277], [42900, 276], [43200, 276], [43500, 275], [43800, 274], [44100, 273], [44400, 272], [44700, 272], [45000, 271], [45300, 270], [45600, 269], [45900, 268], [46200, 267], [46500, 267], [46800, 266], [47100, 265], [47400, 264], [47700, 263], [48000, 263], [48300, 262], [48600, 261], [48900, 260], [49200, 259], [49500, 259], [49800, 258], [50100, 257], [50400, 256], [50700, 255], [51000, 255], [51300, 254], [51600, 253], [51900, 252], [52200, 251], [52500, 251], [52800, 250], [53100, 249], [53400, 248], [53700, 247], [54000, 246], [54300, 246], [54600, 245], [54900, 244], [55200, 243], [55500, 242], [55800, 242], [56100, 241], [56400, 240], [56700, 239], [57000, 239], [57300, 239], [57600, 239], [57900, 239], [58200, 239], [58500, 240], [58800, 240], [59100, 240], [59400, 240], [59700, 240], [60000, 240], [60300, 241], [60600, 241], [60900, 241], [61200, 241], [61500, 241], [61800, 242], [62100, 241], [62400, 241], [62700, 89], [62790, 150], [63000, 89], [63091, 148], [63300, 89], [63391, 147], [63600, 88], [63693, 143], [63900, 88], [63996, 136], [64200, 88], [64300, 129], [64500, 88], [64603, 123], [64800, 88], [64907, 115], [65100, 88], [65212, 105], [65400, 88], [65518, 93], [65700, 88], [65824, 81], [66000, 87], [66132, 65], [66300, 87], [66442, 46], [66600, 87], [66900, 87], [67200, 87], [67500, 87], [67800, 87], [68100, 87], [68400, 86], [68700, 86], [69000, 86], [69300, 86], [69600, 86], [69900, 86], [70200, 86], [70500, 86], [70800, 85], [71100, 85], [71400, 85], [71700, 85], [72000, 85], [72300, 85], [72600, 85], [72900, 85], [73200, 84], [73500, 84], [73800, 84], [74100, 84], [74400, 84], [74700, 84], [75000, 84], [75300, 74], [75383, 1], [75600, 66], [75900, 63], [76200, 61], [76500, 59], [76800, 59], [77100, 59], [77400, 58], [77700, 58], [78000, 58], [78300, 58], [78600, 58], [78900, 59], [79200, 61], [79500, 63], [79800, 82], [80100, 81], [80400, 81], [80700, 81], [81000, 81], [81300, 81], [81600, 81], [81900, 81], [82200, 81], [82500, 80], [82800, 80], [83100, 80], [83400, 80], [83700, 80], [84000, 80], [84300, 80], [84600, 80], [84900, 79], [85200, 79], [85500, 79], [85800, 79], [86100, 79], [86400, 79], [86700, 79], [87000, 79], [87300, 78], [87600, 78], [87900, 78], [88200, 78], [88500, 78], [88800, 78], [89100, 78], [89400, 78], [89700, 77]], "point": [149, 149]}}, "high_idx": 12}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan1", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.25, "y": 0.9009995, "z": -1.0}, "object_poses": [{"objectName": "Pan_e8d2711b", "position": {"x": -0.4652, "y": 0.950499952, "z": -2.576}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Potato_dc7b7f7e", "position": {"x": -2.07000613, "y": 1.33652151, "z": 1.09256208}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_dc7b7f7e", "position": {"x": 0.582687557, "y": 0.9411854, "z": -2.25355029}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": -1.82528353, "y": 0.7911048, "z": 0.243079767}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_77fad61b", "position": {"x": 1.893168, "y": 0.5509404, "z": -2.40758}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Cup_77fad61b", "position": {"x": 0.506796241, "y": 0.138524652, "z": -2.33787346}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": -0.473685682, "y": 1.11258078, "z": 0.7573217}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": -0.898136437, "y": 1.6572808, "z": -2.67671752}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_816f44ce", "position": {"x": -1.6098721, "y": 0.150145382, "z": -0.245843768}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_816f44ce", "position": {"x": 0.8364754, "y": 0.3115425, "z": -2.38311172}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_35d530e8", "position": {"x": -0.197295129, "y": 1.18406522, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e166a32f", "position": {"x": -0.3354904, "y": 1.19122255, "z": 0.7573217}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e166a32f", "position": {"x": -0.197295129, "y": 1.19122255, "z": -0.5065479}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": -1.85282016, "y": 0.8203338, "z": -1.67392874}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_cd066502", "position": {"x": 0.582687557, "y": 0.9118294, "z": -2.56081653}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cd066502", "position": {"x": -1.85282, "y": 0.7708855, "z": -1.41488659}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": 1.12127006, "y": 0.157540783, "z": -2.28615522}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": -1.9377023, "y": 0.9263999, "z": -0.6989651}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": 0.2172907, "y": 1.11164212, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": 1.89161491, "y": 0.8817599, "z": -2.50926423}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": 0.355485976, "y": 1.1125288, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": -1.935, "y": 0.0475771464, "z": 2.12468362}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Book_f5814e4b", "position": {"x": 0.155, "y": 1.1, "z": 0.617}, "rotation": {"x": 0.0, "y": 315.826447, "z": 0.0}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": -1.290839, "y": 0.912116647, "z": -2.63708615}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_c9d70d0f", "position": {"x": -0.0590993166, "y": 1.13694966, "z": -0.001000002}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_35d530e8", "position": {"x": -2.070006, "y": 1.38169944, "z": 0.925078}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_89238ee6", "position": {"x": -1.69341922, "y": 0.9119485, "z": -2.03659749}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_dc7b7f7e", "position": {"x": -1.96227539, "y": 0.0784983039, "z": 1.98115826}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": 1.99686623, "y": 0.554039359, "z": -2.71822262}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Kettle_7ed44c70", "position": {"x": 0.2172907, "y": 1.11264133, "z": -0.7593218}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_e8d2711b", "position": {"x": -0.0361, "y": 0.950499952, "z": -2.3722}, "rotation": {"x": 0.0, "y": 270.000061, "z": 0.0}}, {"objectName": "Plate_ba789e3c", "position": {"x": -0.473685682, "y": 1.10851872, "z": 0.5045478}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_811f3b02", "position": {"x": -2.03855228, "y": 0.5795812, "z": 1.25131321}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Vase_d1ae33eb", "position": {"x": -1.00811148, "y": 0.139160156, "z": -2.45315886}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_30762d8a", "position": {"x": -1.85282016, "y": 0.8049194, "z": -1.58758128}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": -0.473685682, "y": 1.10859609, "z": -0.7593218}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_1a4cbefa", "position": {"x": 0.0790954158, "y": 1.1103971, "z": -0.00100004673}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": 2.096321, "y": 0.88176, "z": -2.563602}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Pot_9e34d9fc", "position": {"x": -1.219, "y": 0.9, "z": -2.356}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": -1.96337986, "y": 0.784756, "z": -1.24219179}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_2c38299b", "position": {"x": 0.324948162, "y": 1.01498532, "z": -2.40718341}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_77fad61b", "position": {"x": -0.328587532, "y": 1.7883817, "z": -2.48203}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_5ffb3206", "position": {"x": 0.3364076, "y": 0.138341784, "z": -2.33787346}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9960ee75", "position": {"x": 0.451126277, "y": 1.6566205, "z": -2.63230824}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b714915a", "position": {"x": -0.473685682, "y": 1.10790622, "z": -0.253773928}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_816f44ce", "position": {"x": -1.85627437, "y": 0.9077062, "z": -0.6989651}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e166a32f", "position": {"x": 0.2172907, "y": 1.19122255, "z": -0.253773928}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": -1.58601356, "y": 0.911442041, "z": -2.5602715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": 0.453817844, "y": 0.961277664, "z": -2.33036685}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": -1.51231253, "y": 1.65788186, "z": -2.53134537}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cd066502", "position": {"x": -0.8480768, "y": 0.912529349, "z": -2.5602715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": 0.711557269, "y": 0.9057, "z": -2.484}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1024119598, "scene_num": 1}, "task_id": "trial_T20190906_181830_873214", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A1O3TWBUDONVLO_3EWIJTFFVROOAT76WC524GZT8CHE0D", "high_descs": ["Turn left and walk across the room, then turn left and walk forward two steps and turn left to face the table.", "Pick up the closer head of lettuce on the table.", "Turn right and walk across the room, then turn left and walk across the room to face the fridge.", "Place the head of lettuce on the top shelf in the fridge.", "Turn around and step forward, then look to the right to face the counter.", "Pick up the head of lettuce on the counter.", "Turn right and walk forward to face the fridge.", "Place the head of lettuce beside the other head of lettuce.", "Turn left and walk across the room to face the counter.", "Pick up the knife on the counter.", "Turn around and walk across the room and turn left to face the fridge.", "Cut both heads of lettuce in the fridge into slices."], "task_desc": "To move two heads of lettuce to the fridge as well as cut them into slices.", "votes": [1, 1]}, {"assignment_id": "A1K5ILJMG439M5_3HL8HNGX48IYC1VFRJ81F9LMNFVF99", "high_descs": ["Turn left and go to face the kitchen island on the same side as the stools.", "Pick up the head of lettuce that is closest to the edge of the island.", "Go around the island with the lettuce and face the white fridge.", "Open the fridge and put the lettuce on the top shelf.", "Close the fridge and turn around to face the kitchen island again.", "Pick up the head of lettuce to the left of the soap dispenser.", "Go back to face the fridge with the lettuce.", "Open the fridge, then put the second head of lettuce to the right of the first on the same shelf.", "Turn left and move to face the kitchen counter on the right of the stove.", "Pick up the knife to the right of the pot on the counter.", "Bring the knife to the fridge.", "Open the fridge, use the knife to slice the lettuce, and then close the fridge."], "task_desc": "Cut the lettuce in the fridge.", "votes": [1, 1]}, {"assignment_id": "A3PGS1Q1XAY79I_3IGI0VL64A1D7GC2RWAWK4PKK5JONJ", "high_descs": ["Turn to your left and walk to the side of the kitchen island with the two black stools.", "Pick up the head of lettuce closest to you and to the right of the tea kettle.", "Turn right and walk to the fridge across from the other side of the island.", "Open the fridge door, place the head of lettuce on the top rack, close the fridge door.", "Turn to your right and walk to the end of the island with a head of lettuce on it.", "Pick up the head of lettuce.", "Turn to your right and walk to the fridge.", "Open the fridge door, place the head of lettuce to the right of the first head of lettuce, close the fridge door.", "Turn to your left and walk to the counter with a pan on it.", "Pick up the knife located behind and to the right of the pan.", "Turn around and walk to the fridge.", "Open the fridge door, slice the two heads of lettuce, close the fridge door."], "task_desc": "Move two heads of lettuce from the island to the fridge, slice both heads", "votes": [1, 1]}, {"assignment_id": "AZLZA0Q87TJZO_3DPNQGW4LOW1W2WS9V964VXS5TQ46G", "high_descs": ["Turn left and head to the other side of the island.", "Pick up the lettuce closest to the hand on the counter.", "Turn right and go to the refrigerator to the left.", "Open the refrigerator, place the lettuce on the top shelf so it is to the upper right of the tomato, then close the refrigerator door.", "Turn around and stand at the closest to you short end of the island.", "Pick up the lettuce to the left of the hand soap.", "Turn right and stand in front of the fridge.", "Open the refrigerator door and place the lettuce directly to the right of the other lettuce on the top shelf. Close the refrigerator door.", "Turn left and head to the counter directly in front of you.", "Pick up the knife to the right of the glass bottle.", "Turn around and go back to the fridge.", "Open the refrigerator door and adjust both heads of lettuce on the top shelf. Close the refrigerator door."], "task_desc": "Adjust two heads of lettuce on the top shelf of the fridge while holding a knife.", "votes": [1, 1]}, {"assignment_id": "AD0NVUGLDYDYN_3OUYGIZWRAFS7KWW5QLDYSYWJMZP0Q", "high_descs": ["Turn left and walk past the kitchen island, turn left to between the two stools", "Grab the nearest cabbage on the island ", "Turn right and go around the island to the refrigerator", "Open the refrigerator and put the cabbage on the first shelf from top", "Close the refrigerator and turn around walk to the side of the island", "Grab the cabbage on the island", "Turn right and walk to the refrigerator", "Open the refrigerator and put the cabbage on the right side of the other cabbage", "Close the refrigerator, turn left and walk to the counter by the stove", "Grab the knife by the pot", "Turn around and walk to the refrigerator", "Open the refrigerator on the left, close the refrigerator"], "task_desc": "Move the two cabbage on the kitchen island into the refrigerator, take the knife to the refrigerator.", "votes": [1, 1]}, {"assignment_id": "A3HL2LL0LEPZT8_3TESA3PJ34RJPV6Q2FGJFLBEHKEMMR", "high_descs": ["Turn left, walk straight past the island, turn left, walk to the black stools near the island, face the island counter.", "Take the lettuce closest to the hand on the island counter.", "Turn right, walk straight, turn left, walk towards the refrigerator. ", "Open the refrigerator, put the lettuce on the top rack, close the refrigerator.", "Turn around, go to the island.", "Take the lettuce on the island counter.", "Turn around, go to the refrigerator.", "Open the refrigerator, put the lettuce on the top rack on the right side above the tomato, close the refrigerator.", "Turn to the left, go to the counter to the right of the stove.", "Pick up the knife to the right of the pot on the counter.", "Turn around, go to the refrigerator.", "Open the refrigerator, cut both lettuce in the refrigerator, close the refrigerator."], "task_desc": "Put cut lettuce in the refrigerator.", "votes": [1, 1]}]}}