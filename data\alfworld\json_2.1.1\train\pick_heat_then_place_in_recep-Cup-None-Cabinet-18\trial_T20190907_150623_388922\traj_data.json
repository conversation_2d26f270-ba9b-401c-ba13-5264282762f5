{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000266.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 43}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-3|4|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-3.051196, -3.051196, 1.580834864, 1.580834864, 3.955378, 3.955378]], "coordinateReceptacleObjectId": ["CounterTop", [5.564, 5.564, 3.588, 3.588, 4.1128, 4.1128]], "forceVisible": true, "objectId": "Cup|-00.76|+00.99|+00.40"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|6|2|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|0|7|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-3.051196, -3.051196, 1.580834864, 1.580834864, 3.955378, 3.955378]], "coordinateReceptacleObjectId": ["Cabinet", [-3.102313756, -3.102313756, 2.974496604, 2.974496604, 1.69071698, 1.69071698]], "forceVisible": true, "objectId": "Cup|-00.76|+00.99|+00.40", "receptacleObjectId": "Cabinet|-00.78|+00.42|+00.74"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.76|+00.99|+00.40"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [134, 95, 175, 153], "mask": [[28347, 15], [28643, 22], [28941, 27], [29239, 31], [29538, 33], [29837, 35], [30136, 37], [30435, 39], [30734, 41], [31034, 41], [31334, 41], [31634, 42], [31934, 42], [32234, 41], [32534, 41], [32834, 41], [33135, 40], [33435, 39], [33735, 39], [34035, 39], [34336, 38], [34636, 37], [34936, 37], [35236, 37], [35536, 37], [35837, 35], [36137, 35], [36437, 35], [36737, 35], [37037, 34], [37338, 33], [37638, 33], [37938, 32], [38238, 32], [38539, 31], [38839, 31], [39139, 30], [39439, 30], [39739, 30], [40040, 29], [40340, 28], [40640, 28], [40940, 28], [41241, 27], [41541, 26], [41841, 26], [42141, 26], [42441, 26], [42742, 24], [43042, 24], [43343, 22], [43643, 22], [43943, 22], [44244, 20], [44544, 20], [44845, 18], [45146, 16], [45447, 14], [45749, 10]], "point": [154, 123]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.76|+00.99|+00.40", "placeStationary": true, "receptacleObjectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 171], [26788, 56], [26847, 113], [27087, 55], [27149, 111], [27387, 54], [27450, 110], [27686, 54], [27750, 109], [27985, 55], [28051, 108], [28285, 54], [28351, 108], [28584, 55], [28652, 107], [28883, 55], [28952, 107], [29183, 55], [29253, 105], [29482, 56], [29553, 105], [29781, 57], [29853, 105], [30081, 57], [30153, 105], [30380, 58], [30453, 104], [30679, 59], [30753, 104], [30978, 60], [31053, 104], [31278, 60], [31352, 105], [31577, 62], [31652, 105], [31876, 63], [31951, 105], [32176, 64], [32251, 105], [32475, 66], [32550, 106], [32775, 67], [32849, 107], [33075, 69], [33146, 109], [33375, 180], [33675, 180], [33975, 180], [34275, 180], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 25], [51084, 24], [51384, 22], [51684, 21], [51984, 20], [52285, 18], [52585, 18], [52885, 17], [53185, 16], [53485, 16], [53785, 15], [54085, 14], [54386, 12], [54686, 12], [54986, 11], [55286, 10], [55586, 10], [55886, 10], [56187, 8], [56487, 8], [56787, 8], [57087, 7], [57387, 7], [57687, 7], [57987, 6], [58288, 5], [58588, 5], [58888, 5], [59188, 4]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 86], [23597, 65], [23795, 84], [23901, 61], [24094, 84], [24202, 60], [24394, 83], [24503, 59], [24693, 85], [24803, 59], [24992, 86], [25103, 58], [25292, 86], [25403, 58], [25591, 87], [25703, 58], [25890, 88], [26002, 59], [26190, 88], [26302, 58], [26489, 89], [26602, 58], [26788, 56], [26847, 31], [26902, 58], [27087, 55], [27149, 29], [27201, 59], [27387, 54], [27450, 28], [27501, 59], [27686, 54], [27750, 29], [27801, 58], [27985, 55], [28051, 28], [28101, 58], [28285, 54], [28351, 28], [28400, 59], [28584, 55], [28652, 27], [28700, 59], [28883, 55], [28952, 27], [29000, 59], [29183, 55], [29253, 26], [29300, 58], [29482, 56], [29553, 26], [29599, 59], [29781, 57], [29853, 26], [29899, 59], [30081, 57], [30153, 26], [30199, 59], [30380, 58], [30453, 26], [30499, 58], [30679, 59], [30753, 26], [30798, 59], [30978, 60], [31053, 27], [31098, 59], [31278, 60], [31352, 28], [31398, 59], [31577, 62], [31652, 28], [31698, 59], [31876, 63], [31951, 29], [31997, 59], [32176, 64], [32251, 29], [32297, 59], [32475, 66], [32550, 30], [32597, 59], [32775, 67], [32849, 32], [32897, 59], [33075, 69], [33146, 35], [33196, 59], [33375, 106], [33496, 59], [33675, 106], [33795, 60], [33975, 106], [34095, 60], [34275, 107], [34395, 60], [34576, 107], [34694, 60], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.76|+00.99|+00.40"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [177, 79, 202, 116], "mask": [[23582, 15], [23879, 22], [24178, 24], [24477, 26], [24778, 25], [25078, 25], [25378, 25], [25678, 25], [25978, 24], [26278, 24], [26578, 24], [26878, 24], [27178, 23], [27478, 23], [27779, 22], [28079, 22], [28379, 21], [28679, 21], [28979, 21], [29279, 21], [29579, 20], [29879, 20], [30179, 20], [30479, 20], [30779, 19], [31080, 18], [31380, 18], [31680, 18], [31980, 17], [32280, 17], [32580, 17], [32881, 16], [33181, 15], [33481, 15], [33781, 14], [34081, 14], [34382, 13], [34683, 11]], "point": [189, 96]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 171], [26788, 56], [26847, 113], [27087, 55], [27149, 111], [27387, 54], [27450, 110], [27686, 54], [27750, 109], [27985, 55], [28051, 108], [28285, 54], [28351, 108], [28584, 55], [28652, 107], [28883, 55], [28952, 107], [29183, 55], [29253, 105], [29482, 56], [29553, 105], [29781, 57], [29853, 105], [30081, 57], [30153, 105], [30380, 58], [30453, 104], [30679, 59], [30753, 104], [30978, 60], [31053, 104], [31278, 60], [31352, 105], [31577, 62], [31652, 105], [31876, 63], [31951, 105], [32176, 64], [32251, 105], [32475, 66], [32550, 106], [32775, 67], [32849, 107], [33075, 69], [33146, 109], [33375, 180], [33675, 180], [33975, 180], [34275, 180], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 31], [46281, 30], [46582, 27], [46882, 26], [47182, 25], [47482, 24], [47782, 23], [48082, 22], [48383, 20], [48683, 19], [48983, 19], [49283, 18], [49583, 18], [49883, 17], [50183, 17], [50484, 15], [50784, 15], [51084, 15], [51384, 15], [51684, 15], [51984, 15], [52285, 13], [52585, 13], [52885, 13], [53185, 13], [53485, 13], [53785, 14], [54085, 14], [54386, 14], [54686, 14], [54986, 14], [55286, 15], [55586, 15], [55886, 16], [56187, 15], [56487, 15], [56787, 16], [57087, 16], [57387, 16], [57687, 17], [57987, 17], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.78|+00.42|+00.74"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [184, 128, 299, 198], "mask": [[38287, 1], [38587, 113], [38887, 113], [39186, 114], [39486, 114], [39786, 114], [40086, 114], [40386, 114], [40686, 114], [40986, 113], [41286, 113], [41585, 113], [41885, 113], [42185, 112], [42485, 112], [42785, 111], [43085, 111], [43385, 110], [43684, 110], [43984, 110], [44284, 109], [44584, 109], [44884, 108], [45184, 108], [45484, 107], [45786, 105], [46088, 102], [46389, 101], [46691, 98], [46992, 96], [47293, 95], [47594, 93], [47895, 92], [48196, 90], [48497, 89], [48798, 87], [49098, 87], [49399, 85], [49699, 85], [50000, 83], [50300, 82], [50601, 81], [50901, 80], [51201, 80], [51501, 79], [51801, 79], [52101, 78], [52402, 77], [52702, 76], [53002, 76], [53302, 75], [53602, 74], [53901, 75], [54201, 74], [54500, 75], [54800, 74], [55100, 74], [55399, 74], [55699, 74], [55998, 74], [56298, 74], [56598, 73], [56897, 73], [57197, 73], [57497, 72], [57796, 73], [58096, 72], [58396, 72], [58696, 71], [58995, 72], [59295, 71]], "point": [241, 162]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.76|+00.99|+00.40", "placeStationary": true, "receptacleObjectId": "Cabinet|-00.78|+00.42|+00.74"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [186, 129, 299, 290], "mask": [[38590, 18], [38616, 84], [38889, 19], [38916, 84], [39189, 19], [39215, 85], [39489, 18], [39515, 84], [39789, 18], [39815, 84], [40089, 18], [40115, 83], [40389, 18], [40415, 82], [40499, 1], [40689, 17], [40715, 82], [40799, 1], [40988, 18], [41015, 81], [41098, 2], [41288, 17], [41315, 81], [41398, 2], [41588, 17], [41615, 80], [41697, 3], [41888, 17], [41914, 81], [41997, 3], [42188, 17], [42214, 80], [42296, 4], [42488, 18], [42514, 80], [42595, 5], [42788, 19], [42813, 80], [42895, 5], [43087, 106], [43194, 6], [43387, 105], [43494, 6], [43687, 105], [43793, 7], [43987, 104], [44093, 7], [44287, 104], [44392, 8], [44587, 103], [44692, 8], [44887, 102], [44991, 9], [45186, 103], [45291, 9], [45486, 102], [45590, 10], [45786, 102], [45889, 11], [46088, 99], [46189, 11], [46389, 98], [46488, 12], [46691, 95], [46788, 12], [46992, 94], [47087, 13], [47293, 92], [47387, 13], [47594, 91], [47686, 14], [47895, 89], [47986, 14], [48196, 88], [48285, 15], [48497, 86], [48585, 15], [48798, 84], [48884, 16], [49098, 84], [49184, 16], [49399, 82], [49483, 17], [49699, 82], [49782, 18], [50000, 80], [50082, 18], [50300, 80], [50381, 19], [50601, 78], [50681, 19], [50901, 78], [50980, 20], [51201, 77], [51280, 20], [51501, 77], [51579, 21], [51801, 76], [51879, 21], [52101, 76], [52178, 22], [52402, 74], [52478, 22], [52702, 74], [52777, 23], [53002, 73], [53076, 24], [53302, 72], [53376, 24], [53602, 72], [53675, 25], [53901, 72], [53975, 25], [54201, 72], [54274, 26], [54500, 72], [54574, 26], [54800, 72], [54873, 27], [55100, 71], [55173, 27], [55399, 72], [55472, 28], [55699, 71], [55772, 28], [55998, 72], [56071, 29], [56298, 71], [56371, 29], [56598, 71], [56670, 30], [56897, 71], [56969, 31], [57197, 70], [57269, 31], [57497, 70], [57568, 32], [57796, 70], [57868, 32], [58096, 70], [58167, 33], [58396, 69], [58467, 33], [58696, 69], [58766, 34], [59066, 34], [59365, 35], [59665, 35], [59966, 34], [60266, 34], [60566, 34], [60867, 33], [61167, 33], [61468, 32], [61768, 32], [62068, 32], [62369, 31], [62669, 31], [62969, 31], [63270, 30], [63570, 30], [63870, 30], [64171, 29], [64471, 29], [64772, 28], [65072, 28], [65372, 28], [65673, 27], [65973, 27], [66273, 27], [66574, 26], [66874, 26], [67175, 25], [67475, 25], [67775, 25], [68076, 24], [68376, 24], [68676, 24], [68977, 23], [69277, 23], [69578, 22], [69878, 22], [70178, 22], [70479, 21], [70779, 21], [71079, 21], [71380, 20], [71680, 20], [71981, 19], [72281, 19], [72581, 19], [72882, 18], [73182, 18], [73482, 18], [73783, 17], [74083, 17], [74384, 16], [74684, 16], [74984, 16], [75285, 15], [75585, 15], [75885, 15], [76186, 14], [76486, 14], [76787, 13], [77087, 13], [77387, 13], [77688, 12], [77988, 12], [78288, 12], [78589, 11], [78889, 11], [79190, 10], [79490, 10], [79790, 10], [80091, 9], [80391, 9], [80691, 9], [80992, 8], [81292, 8], [81593, 7], [81893, 7], [82193, 7], [82494, 6], [82794, 6], [83094, 6], [83395, 5], [83695, 5], [83996, 4], [84296, 4], [84596, 4], [84897, 3], [85197, 3], [85497, 3], [85798, 2], [86098, 2], [86399, 1], [86699, 1], [86999, 1]], "point": [242, 195]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.78|+00.42|+00.74"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [180, 129, 299, 290], "mask": [[38590, 18], [38616, 84], [38889, 19], [38916, 84], [39189, 19], [39215, 85], [39489, 18], [39515, 84], [39789, 18], [39815, 84], [40089, 18], [40115, 83], [40389, 18], [40415, 82], [40499, 1], [40689, 17], [40715, 82], [40799, 1], [40988, 18], [41015, 81], [41098, 2], [41288, 17], [41315, 81], [41398, 2], [41588, 17], [41615, 80], [41697, 3], [41888, 17], [41914, 81], [41997, 3], [42188, 17], [42214, 80], [42296, 4], [42488, 18], [42514, 80], [42595, 5], [42788, 19], [42813, 80], [42895, 5], [43087, 106], [43194, 6], [43387, 105], [43494, 6], [43687, 105], [43793, 7], [43987, 104], [44093, 7], [44287, 104], [44392, 8], [44587, 103], [44692, 8], [44887, 102], [44991, 9], [45186, 103], [45291, 9], [45486, 102], [45590, 10], [45786, 102], [45889, 11], [46086, 101], [46189, 11], [46386, 101], [46488, 12], [46686, 100], [46788, 12], [46986, 100], [47087, 13], [47285, 100], [47387, 13], [47585, 100], [47686, 14], [47885, 99], [47986, 14], [48185, 99], [48285, 15], [48485, 98], [48585, 15], [48785, 97], [48884, 16], [49085, 27], [49113, 69], [49184, 16], [49384, 24], [49418, 63], [49483, 17], [49684, 23], [49720, 61], [49782, 18], [49984, 22], [50021, 59], [50082, 18], [50284, 21], [50322, 58], [50381, 19], [50584, 21], [50623, 56], [50681, 19], [50884, 21], [50923, 56], [50980, 20], [51184, 21], [51223, 55], [51280, 20], [51483, 22], [51524, 54], [51579, 21], [51783, 22], [51823, 54], [51879, 21], [52083, 22], [52123, 54], [52178, 22], [52383, 22], [52423, 53], [52478, 22], [52683, 22], [52722, 54], [52777, 23], [52983, 22], [53021, 54], [53076, 24], [53283, 22], [53321, 53], [53376, 24], [53582, 23], [53620, 54], [53675, 25], [53882, 23], [53920, 53], [53975, 25], [54182, 23], [54219, 54], [54274, 26], [54482, 23], [54519, 53], [54574, 26], [54782, 23], [54818, 54], [54873, 27], [55082, 23], [55118, 53], [55173, 27], [55382, 23], [55417, 54], [55472, 28], [55682, 23], [55716, 54], [55772, 28], [55981, 25], [56016, 54], [56071, 29], [56281, 25], [56315, 54], [56371, 29], [56581, 27], [56614, 55], [56670, 30], [56881, 87], [56969, 31], [57181, 86], [57269, 31], [57481, 86], [57568, 32], [57781, 85], [57868, 32], [58080, 86], [58167, 33], [58380, 85], [58467, 33], [58680, 85], [58766, 34], [59066, 34], [59365, 35], [59665, 35], [59966, 34], [60266, 34], [60566, 34], [60867, 33], [61167, 33], [61468, 32], [61768, 32], [62068, 32], [62369, 31], [62669, 31], [62969, 31], [63270, 30], [63570, 30], [63870, 30], [64171, 29], [64471, 29], [64772, 28], [65072, 28], [65372, 28], [65673, 27], [65973, 27], [66273, 27], [66574, 26], [66874, 26], [67175, 25], [67475, 25], [67775, 25], [68076, 24], [68376, 24], [68676, 24], [68977, 23], [69277, 23], [69578, 22], [69878, 22], [70178, 22], [70479, 21], [70779, 21], [71079, 21], [71380, 20], [71680, 20], [71981, 19], [72281, 19], [72581, 19], [72882, 18], [73182, 18], [73482, 18], [73783, 17], [74083, 17], [74384, 16], [74684, 16], [74984, 16], [75285, 15], [75585, 15], [75885, 15], [76186, 14], [76486, 14], [76787, 13], [77087, 13], [77387, 13], [77688, 12], [77988, 12], [78288, 12], [78589, 11], [78889, 11], [79190, 10], [79490, 10], [79790, 10], [80091, 9], [80391, 9], [80691, 9], [80992, 8], [81292, 8], [81593, 7], [81893, 7], [82193, 7], [82494, 6], [82794, 6], [83094, 6], [83395, 5], [83695, 5], [83996, 4], [84296, 4], [84596, 4], [84897, 3], [85197, 3], [85497, 3], [85798, 2], [86098, 2], [86399, 1], [86699, 1], [86999, 1]], "point": [239, 195]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan18", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -0.75, "y": 0.900999844, "z": 4.0}, "object_poses": [{"objectName": "Potato_5b11696a", "position": {"x": 0.3052411, "y": 0.7949763, "z": 6.541539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": 2.13723, "y": 1.55049694, "z": 5.376}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 0.8712312, "y": 0.7719865, "z": 0.5630982}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 2.05555582, "y": 0.08028135, "z": 6.46908}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": 1.559894, "y": 0.988599956, "z": 4.2942}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": 1.562, "y": 0.988599956, "z": 3.848}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": 1.22514915, "y": 0.9938295, "z": 0.6680888}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": 1.44257987, "y": 0.905632257, "z": 2.239636}, "rotation": {"x": 1.40334208e-14, "y": -9.659347e-06, "z": -1.71913052e-21}}, {"objectName": "Egg_8021024b", "position": {"x": -0.158254743, "y": 0.810111, "z": 6.604039}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": 0.2733106, "y": 1.14716923, "z": 0.245674372}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_8e8a26e1", "position": {"x": -0.1351898, "y": 0.7562329, "z": 6.23786259}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 1.238817, "y": 0.141221583, "z": 3.14388132}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 1.40633225, "y": 0.141221583, "z": 2.79405117}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": -0.3842585, "y": 1.02240038, "z": 0.6700087}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": 1.34822512, "y": 0.801405668, "z": 3.29175758}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 1.13578975, "y": 0.9927622, "z": 4.67067957}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 1.48978925, "y": 2.20716763, "z": 3.88713527}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 0.25760448, "y": 0.7550884, "z": 6.71904469}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 1.40031552, "y": 0.136815369, "z": 4.72489929}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 1.15505934, "y": 0.135627449, "z": 2.57228041}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": -0.4830967, "y": 0.139144063, "z": 0.201235235}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b7525b79", "position": {"x": 0.05866337, "y": 0.829698145, "z": 6.261198}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Bread_b7525b79", "position": {"x": -0.378325045, "y": 0.829698145, "z": 6.698186}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": 1.96668923, "y": 1.510609, "z": 5.876}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 2.00932431, "y": 1.59014213, "z": 6.001}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 2.09459472, "y": 1.59014213, "z": 5.501}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_9f9e1c57", "position": {"x": 0.124454886, "y": 0.143181443, "z": 0.4299235}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b7525b79", "position": {"x": -0.5356747, "y": 1.06230986, "z": 0.395208716}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": 1.92405415, "y": 1.56563163, "z": 5.751}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": -0.6217506, "y": 0.789788663, "z": 6.541539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": -0.413737565, "y": 0.771929264, "z": 0.395155728}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 0.150742471, "y": 0.8346215, "z": 6.666539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": 1.32, "y": 0.988599956, "z": 3.848}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 1.50018167, "y": 1.57812715, "z": 1.22711158}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 0.0157748461, "y": 0.7550884, "z": 6.74238}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": 1.49322653, "y": 1.0811137, "z": 3.32445574}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": -0.3842585, "y": 0.992762148, "z": 0.395208716}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": -0.481585175, "y": 0.7874043, "z": 0.5639391}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_bd2376ff", "position": {"x": 0.6962644, "y": 1.09698546, "z": 0.395208776}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_73d89a5b", "position": {"x": 0.734, "y": 0.981694, "z": 0.38}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": 0.3052411, "y": 0.7949763, "z": 6.291539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": 0.8476807, "y": 0.9927421, "z": 0.5784098}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 1.62120485, "y": 2.20210528, "z": 3.79425335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_08687688", "position": {"x": 1.3131808, "y": 0.9855794, "z": 4.28467369}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": 1.44258, "y": 0.9400979, "z": 2.03443742}, "rotation": {"x": 2.27491e-21, "y": 89.99999, "z": 1.40334183e-14}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 1.40406525, "y": 0.7712696, "z": 3.22306514}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_8e8a26e1", "position": {"x": -0.762799, "y": 0.9888445, "z": 0.395208716}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": 1.40386784, "y": 0.9938294, "z": 3.51128745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": 1.38469982, "y": 1.043094, "z": 0.8474003}, "rotation": {"x": 0.0, "y": 270.000122, "z": 0.0}}], "object_toggles": [], "random_seed": 180145083, "scene_num": 18}, "task_id": "trial_T20190907_150623_388922", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AJQGWGESKQT4Y_3H7Z272LXAO54GLIUJ7WWCQLPZ8PLW", "high_descs": ["Go right to face the counter where the knife is.", "Pick the glass up from the counter.", "Go left to stand in front of the microwave.", "Put the glass in the microwave, shut the door and then open the door and pick the glass up from the microwave.", "Move to the right and face the lower cabinets.", "Open the far right cabinet and put the glass in it and shut the door."], "task_desc": "Put a warmed glass in the cabinet.", "votes": [1, 1, 1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A320QA9HJFUOZO_3B1NLC6UG2DPIO7HJSAGJKLDTIAGP5", "high_descs": ["Turn right and walk up to the right side of the kitchen counter", "Pick up the cup from the counter", "Walk a few feet to the left and turn right to face the microwave", "Heat the cup in the microwave then remove it", "Turn in a circle and face back toward the microwave", "Open the bottom right cabinet and put the cup in it"], "task_desc": "Put the heated cup in the kitchen cabinet", "votes": [1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A2CT57W84KXX25_3YHH42UU5EW2GZHYDYS6HBPDHSO0LU", "high_descs": ["Walk to the counter on the right wall. ", "Pick up the white cup on the counter. ", "Walk to the microwave to the left. ", "Put the cup in the microwave, heat it for a few seconds, take the cup back out of the microwave. ", "Turn around, take a step, turn back around. ", "Put the cup in the last cabinet under the counter."], "task_desc": "Put the hot cup in the cabinet. ", "votes": [1, 0, 1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A23HZ18KTCK2DA_3XCC1ODXDOS17QHAZGJHITPU0SGQRL", "high_descs": ["Turn and go to the kitchen counter that is closest to the bag of trash on the floor.", "Pick up the cup that is near the edge of the counter.", "Turn and with the cup in hand go to the microwave.", "Open the microwave door. Place the cup on the microwave to the right of the egg.  Close the microwave door and turn the microwave on.  Open the door when finished and grab the cup.  Shut the microwave door.", "Turn to go to the cabinet that is below the microwave and the farthest to the right.", "Open the cabinet door.  Place the cup in the cabinet in the center of the shelf. Shut the cabinet door."], "task_desc": "Warm up a cup to put it away.", "votes": [1, 1]}]}}