{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 42}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "SinkBasin", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|1|7|1|15"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [4.06817912, 4.06817912, 6.8896284, 6.8896284, 6.86377812, 6.86377812]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [4.052, 4.052, 7.668, 7.668, 0.911987304, 0.911987304]], "forceVisible": true, "objectId": "Egg|+01.02|+01.72|+01.72"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|3|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "sinkbasin"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [4.06817912, 4.06817912, 6.8896284, 6.8896284, 6.86377812, 6.86377812]], "coordinateReceptacleObjectId": ["SinkBasin", [-7.34220268, -7.34220268, -2.168930532, -2.168930532, 4.16, 4.16]], "forceVisible": true, "objectId": "Egg|+01.02|+01.72|+01.72", "receptacleObjectId": "Sink|-01.99|+01.14|-00.98|SinkBasin"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+01.01|+00.23|+01.92"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 90000]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.02|+01.72|+01.72"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [148, 94, 170, 124], "mask": [[28059, 1], [28356, 7], [28654, 11], [28953, 13], [29252, 15], [29552, 15], [29851, 17], [30150, 19], [30450, 19], [30750, 19], [31049, 21], [31349, 21], [31648, 22], [31948, 23], [32248, 23], [32548, 23], [32848, 23], [33148, 23], [33448, 23], [33748, 23], [34048, 23], [34348, 22], [34649, 21], [34949, 21], [35250, 19], [35550, 19], [35851, 17], [36152, 15], [36453, 13], [36754, 11], [37056, 7]], "point": [159, 108]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+01.01|+00.23|+01.92"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 20165], [20169, 293], [20473, 287], [20775, 284], [21077, 280], [21378, 278], [21679, 276], [21979, 276], [22280, 274], [22581, 272], [22881, 272], [23181, 272], [23482, 271], [23782, 271], [24082, 271], [24383, 270], [24683, 270], [24983, 270], [25283, 270], [25583, 271], [25883, 271], [26183, 272], [26482, 273], [26782, 274], [27081, 276], [27380, 279], [27677, 284], [27971, 16282], [44309, 244], [44609, 244], [44909, 244], [45209, 244], [45508, 26], [45542, 211], [45808, 19], [45831, 2], [45842, 211], [46108, 18], [46148, 205], [46408, 17], [46449, 204], [46708, 16], [46750, 204], [47008, 16], [47051, 203], [47307, 16], [47351, 203], [47607, 16], [47652, 202], [47907, 15], [47952, 202], [48207, 15], [48252, 203], [48506, 16], [48553, 202], [48806, 15], [48853, 202], [49106, 15], [49153, 203], [49406, 15], [49453, 203], [49705, 16], [49753, 204], [50005, 16], [50052, 205], [50304, 18], [50352, 206], [50603, 19], [50652, 206], [50903, 19], [50952, 207], [51202, 21], [51251, 208], [51502, 22], [51551, 209], [51801, 24], [51850, 211], [52100, 26], [52149, 213], [52399, 28], [52448, 216], [52698, 30], [52747, 217], [52996, 34], [53046, 218], [53295, 41], [53343, 222], [53595, 271], [53894, 274], [54193, 278], [54490, 287], [54785, 14360], [69155, 288], [69458, 283], [69759, 280], [70061, 277], [70362, 275], [70663, 273], [70964, 271], [71265, 269], [71565, 269], [71866, 267], [72167, 265], [72468, 264], [72768, 263], [73069, 262], [73369, 261], [73670, 260], [73970, 260], [74271, 258], [74571, 258], [74871, 257], [75172, 256], [75472, 256], [75772, 256], [76072, 255], [76372, 255], [76673, 254], [76973, 254], [77273, 254], [77573, 254], [77873, 254], [78173, 254], [78473, 254], [78773, 254], [79073, 254], [79373, 254], [79673, 254], [79973, 254], [80273, 255], [80573, 255], [80872, 256], [81172, 256], [81472, 257], [81771, 258], [82071, 258], [82371, 259], [82670, 260], [82970, 261], [83269, 262], [83569, 263], [83868, 265], [84167, 266], [84467, 267], [84766, 269], [85065, 271], [85364, 274], [85662, 277], [85961, 279], [86260, 283], [86558, 287], [86855, 3145]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.02|+01.72|+01.72", "placeStationary": true, "receptacleObjectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 132], [37936, 77], [38100, 130], [38238, 75], [38400, 129], [38539, 74], [38700, 128], [38840, 72], [39000, 127], [39140, 72], [39300, 126], [39441, 71], [39600, 126], [39742, 70], [39900, 126], [40042, 70], [40200, 125], [40342, 70], [40500, 125], [40643, 69], [40800, 125], [40943, 69], [41100, 125], [41243, 68], [41400, 125], [41543, 68], [41700, 125], [41843, 68], [42000, 125], [42143, 68], [42300, 125], [42442, 69], [42600, 126], [42742, 69], [42900, 126], [43042, 69], [43200, 127], [43341, 69], [43500, 127], [43641, 69], [43800, 128], [43939, 71], [44100, 130], [44238, 72], [44400, 132], [44536, 74], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+01.02|+01.72|+01.72"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [125, 127, 142, 149], "mask": [[37932, 4], [38230, 8], [38529, 10], [38828, 12], [39127, 13], [39426, 15], [39726, 16], [40026, 16], [40325, 17], [40625, 18], [40925, 18], [41225, 18], [41525, 18], [41825, 18], [42125, 18], [42425, 17], [42726, 16], [43026, 16], [43327, 14], [43627, 14], [43928, 11], [44230, 8], [44532, 4]], "point": [133, 137]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+01.02|+01.72|+01.72", "placeStationary": true, "receptacleObjectId": "Sink|-01.99|+01.14|-00.98|SinkBasin"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [152, 99, 256, 149], "mask": [[29562, 15], [29595, 30], [29860, 16], [29896, 32], [30158, 17], [30197, 34], [30456, 19], [30498, 35], [30755, 20], [30798, 36], [31054, 20], [31099, 37], [31353, 21], [31399, 38], [31653, 21], [31699, 39], [31952, 22], [31999, 40], [32252, 22], [32299, 40], [32552, 23], [32599, 41], [32852, 23], [32898, 43], [33152, 23], [33198, 43], [33452, 23], [33497, 45], [33752, 24], [33797, 45], [34052, 24], [34096, 46], [34352, 25], [34395, 48], [34652, 25], [34695, 48], [34952, 26], [34994, 50], [35252, 26], [35293, 51], [35552, 27], [35592, 15], [35614, 30], [35852, 27], [35891, 13], [35917, 28], [36152, 29], [36190, 13], [36219, 26], [36452, 30], [36488, 13], [36522, 24], [36752, 48], [36824, 22], [37052, 47], [37125, 21], [37352, 46], [37425, 22], [37652, 45], [37726, 21], [37952, 45], [38026, 22], [38252, 45], [38327, 21], [38552, 45], [38627, 22], [38852, 46], [38927, 22], [39152, 47], [39227, 22], [39452, 48], [39526, 24], [39752, 49], [39826, 24], [40052, 51], [40125, 26], [40352, 52], [40424, 27], [40652, 54], [40722, 29], [40952, 57], [41020, 32], [41252, 100], [41552, 101], [41852, 101], [42153, 101], [42456, 98], [42758, 96], [43060, 95], [43363, 92], [43667, 89], [43971, 85], [44274, 82], [44578, 79]], "point": [202, 120]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan3", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 0.25, "y": 1.12401652, "z": 1.0}, "object_poses": [{"objectName": "Pan_9d168802", "position": {"x": -0.6309, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": 0.9777746, "y": 1.79697371, "z": 2.23050737}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": -1.82631838, "y": 1.10374343, "z": -0.4966268}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.817123, "y": 0.3447429, "z": 0.211331308}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.634, "y": 0.2811138, "z": 2.110548}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.53081059, "y": 1.32354856, "z": -2.461287}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": 0.277946, "y": 1.32412946, "z": -2.90023613}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": 0.8613707, "y": 1.20892942, "z": 1.2225256}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": 0.766335964, "y": 1.33799994, "z": -2.42411566}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": -2.179573, "y": 1.32039762, "z": -2.20134926}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": 0.900508642, "y": 0.345585465, "z": -2.26769447}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": 0.824245334, "y": 0.345585465, "z": -1.9577744}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.830428, "y": 1.357195, "z": -0.9805762}, "rotation": {"x": 0.387793064, "y": 180.034683, "z": 353.89032}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.34542334, "y": 1.32319188, "z": -1.03550565}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": -1.52276421, "y": 1.35692108, "z": -3.203495}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -1.665155, "y": 0.3421222, "z": -2.162692}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -1.74942863, "y": 0.3421222, "z": 0.412196}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": 1.19326913, "y": 1.2028, "z": 1.2225256}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": 1.02731991, "y": 1.2028, "z": 1.2225256}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": 0.896088362, "y": 1.39739525, "z": -2.794527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.95454407, "y": 1.09993339, "z": -0.587838531}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.0945816, "y": 1.37188375, "z": -3.31436586}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": 0.492037773, "y": 1.39739525, "z": -3.12197924}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.125, "y": 1.3324, "z": -2.973}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.3086729, "y": 1.35270047, "z": -2.870881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": 0.9710001, "y": 1.52282739, "z": 1.66288}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": 1.056315, "y": 1.79697371, "z": 1.99491012}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": 0.5634017, "y": 1.32319188, "z": -3.34372234}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.40105689, "y": 1.32354856, "z": 2.069365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": -1.40105808, "y": 1.32039762, "z": -2.20134926}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": 0.766335964, "y": 1.32404137, "z": -0.942471862}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": 1.01704478, "y": 1.71594453, "z": 1.7224071}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_9d168802", "position": {"x": -2.04982066, "y": 1.32260954, "z": -2.981163}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": 0.6365833, "y": 1.31800008, "z": -2.05370474}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": 0.277946, "y": 1.32392883, "z": -3.01110744}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.892016, "y": 1.4006691, "z": -1.09032226}, "rotation": {"x": 0.0264897719, "y": 0.289163023, "z": 355.8773}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.16594541, "y": 1.33799994, "z": -3.31436586}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_ddd73f57", "position": {"x": -1.34542334, "y": 1.42728543, "z": -1.50393641}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 1.02178133, "y": 1.55844033, "z": 1.477321}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -1.53081059, "y": 1.323614, "z": -1.94141138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": -1.92006814, "y": 1.31800008, "z": -1.68147337}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": 0.899722457, "y": 0.4601643, "z": 0.710978568}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -1.79031563, "y": 1.32412946, "z": 1.4197371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.6575036, "y": 0.32906872, "z": 2.27545214}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": -2.09335637, "y": 1.32930565, "z": -0.9805181}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3510381108, "scene_num": 3}, "task_id": "trial_T20190909_103025_202396", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A20FCMWP43CVIU_3D4CH1LGEDKCBQD8YB30YG50ISA9GA", "high_descs": ["walk to face fridge", "pick up egg from fridge", "walk to face microwave", "heat and remove egg from microwave", "walk to face sink", "put egg inside sink"], "task_desc": "put heated egg in sink basin", "votes": [1, 1]}, {"assignment_id": "A2YUCJ28XANFOX_3GS6S824STOTXE72EQQN2KM8H5FNWK", "high_descs": ["cross the kitchen to the fridge", "open the fridge and remove the egg from the shelf", "cross over to the microwave with the egg", "put the egg in the microwave and turn it on and then remove it", "take the egg across the kitchen to the sink", "Place the egg in the sink by the apple "], "task_desc": "Heat the egg and place it in the sink", "votes": [1, 1]}, {"assignment_id": "A12HZGOZQD5YK7_3T111IHZ5HH30GMMEMXCXKJ7K1E9RZ", "high_descs": ["Go left to the fridge.", "Pick up the egg in the fridge.", "Turn right and go to the microwave.", "Cook the egg in the microwave and pick it back up.", "Turn around and go to the sink.", "Put the egg in the sink."], "task_desc": "Put a cooked egg in the sink.", "votes": [1, 1]}]}}