{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000244.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000245.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000246.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000247.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000248.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000249.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000250.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000251.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000252.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000253.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000254.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000255.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000256.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000257.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000258.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000259.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000260.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000261.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000262.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000268.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000269.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000270.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000271.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000272.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000273.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000274.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000275.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000276.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000277.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000278.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000279.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000280.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000281.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000282.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000283.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000284.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000285.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000286.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000287.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000288.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000289.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000290.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000291.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000292.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000293.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000294.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000295.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000296.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000297.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000298.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000299.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000300.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000301.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000302.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000303.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000304.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000305.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000306.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000307.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000308.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000309.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000310.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000311.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000312.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000313.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000314.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000315.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000316.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000317.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000318.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000319.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000320.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000321.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000322.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000323.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000324.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000325.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000326.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000327.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000328.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000329.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000330.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000331.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000332.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000333.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000334.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000335.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000336.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000337.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000338.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000339.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000340.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000341.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000342.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000343.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000344.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000345.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000346.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000347.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000348.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000349.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000350.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000351.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000352.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000353.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000354.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000355.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000356.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000357.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000358.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000359.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000360.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000361.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000362.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000363.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000364.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000365.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000366.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000367.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000368.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000369.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000370.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000371.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000372.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000373.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000374.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000375.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000376.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000377.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000378.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000379.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000380.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000381.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000382.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000383.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000384.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000385.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000386.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000387.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000388.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000389.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000390.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000391.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000392.png", "low_idx": 71}, {"high_idx": 11, "image_name": "000000393.png", "low_idx": 72}, {"high_idx": 11, "image_name": "000000394.png", "low_idx": 72}, {"high_idx": 11, "image_name": "000000395.png", "low_idx": 72}, {"high_idx": 11, "image_name": "000000396.png", "low_idx": 72}, {"high_idx": 11, "image_name": "000000397.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000398.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000399.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000400.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000401.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000402.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000403.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000404.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000405.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000406.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000407.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000408.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000409.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000410.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000411.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000412.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000413.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000414.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000415.png", "low_idx": 74}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|3|2|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [4.82961084, 4.82961084, 2.826878, 2.826878, 3.6461684, 3.6461684]], "coordinateReceptacleObjectId": ["CounterTop", [5.1, 5.1, 0.26, 0.26, 3.788, 3.788]], "forceVisible": true, "objectId": "ButterKnife|+01.21|+00.91|+00.71"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|8|3|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-4.79142856, -4.79142856, 7.60913324, 7.60913324, 3.252164604, 3.252164604]], "forceVisible": true, "objectId": "Bread|-01.20|+00.81|+01.90"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "microwave"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [4.82961084, 4.82961084, 2.826878, 2.826878, 3.6461684, 3.6461684]], "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "ButterKnife|+01.21|+00.91|+00.71", "receptacleObjectId": "Microwave|-01.34|+00.90|+00.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-2|8|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-4.79142856, -4.79142856, 7.60913324, 7.60913324, 3.252164604, 3.252164604]], "coordinateReceptacleObjectId": ["DiningTable", [-4.8448, -4.8448, 9.836, 9.836, 3.1376, 3.1376]], "forceVisible": true, "objectId": "Bread|-01.20|+00.81|+01.90|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-2|4|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "fridge"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-4.79142856, -4.79142856, 7.60913324, 7.60913324, 3.252164604, 3.252164604]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-5.204, -5.204, 3.952, 3.952, 0.032000004, 0.032000004]], "forceVisible": true, "objectId": "Bread|-01.20|+00.81|+01.90|BreadSliced_1", "receptacleObjectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|+01.21|+00.91|+00.71"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [75, 98, 92, 167], "mask": [[29188, 2], [29487, 4], [29787, 4], [30087, 4], [30387, 5], [30687, 5], [30987, 5], [31286, 6], [31586, 6], [31886, 6], [32186, 6], [32486, 7], [32785, 8], [33085, 7], [33385, 7], [33685, 7], [33985, 7], [34284, 8], [34584, 7], [34884, 7], [35184, 7], [35484, 7], [35784, 6], [36084, 6], [36383, 7], [36683, 6], [36983, 6], [37283, 6], [37583, 5], [37883, 5], [38183, 4], [38482, 5], [38782, 5], [39082, 5], [39382, 4], [39682, 4], [39982, 4], [40281, 4], [40581, 4], [40881, 4], [41181, 4], [41480, 5], [41780, 4], [42080, 4], [42380, 4], [42680, 4], [42979, 5], [43279, 5], [43579, 5], [43879, 5], [44178, 6], [44478, 6], [44778, 6], [45077, 7], [45377, 7], [45677, 7], [45977, 6], [46276, 7], [46576, 7], [46876, 7], [47176, 7], [47476, 7], [47775, 8], [48075, 8], [48376, 6], [48677, 5], [48978, 4], [49279, 3], [49580, 2], [49880, 1]], "point": [83, 131]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-01.20|+00.81|+01.90"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [88, 147, 162, 189], "mask": [[43905, 24], [43931, 15], [44201, 53], [44497, 60], [44795, 63], [45094, 65], [45393, 67], [45692, 69], [45992, 69], [46291, 71], [46591, 71], [46890, 72], [47190, 72], [47490, 72], [47790, 72], [48089, 73], [48389, 73], [48689, 73], [48989, 73], [49289, 73], [49588, 74], [49888, 74], [50188, 75], [50488, 75], [50788, 75], [51088, 75], [51388, 75], [51688, 74], [51988, 74], [52288, 74], [52588, 74], [52888, 74], [53188, 74], [53489, 73], [53789, 73], [54089, 73], [54388, 74], [54689, 73], [54989, 73], [55289, 73], [55589, 72], [55890, 71], [56190, 71], [56492, 67]], "point": [125, 167]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|+01.21|+00.91|+00.71", "placeStationary": true, "receptacleObjectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 241], [28529, 241], [28829, 241], [29128, 241], [29427, 242], [29727, 242], [30026, 242], [30326, 242], [30625, 243], [30924, 243], [31224, 243], [31523, 243], [31823, 243], [32122, 244], [32421, 244], [32721, 244], [33020, 244], [33320, 244], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 3], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 241], [28529, 241], [28829, 241], [29128, 241], [29427, 242], [29727, 242], [30026, 242], [30326, 242], [30625, 243], [30924, 243], [31224, 243], [31523, 243], [31823, 243], [32122, 244], [32421, 244], [32721, 98], [32843, 31], [32881, 84], [33020, 93], [33185, 79], [33320, 91], [33485, 79], [33619, 92], [33738, 16], [33785, 79], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.20|+00.81|+01.90|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [119, 147, 127, 189], "mask": [[43922, 6], [44222, 6], [44521, 7], [44821, 7], [45121, 7], [45421, 7], [45721, 7], [46021, 7], [46321, 6], [46620, 7], [46920, 7], [47220, 7], [47520, 7], [47820, 7], [48120, 7], [48420, 7], [48720, 7], [49020, 7], [49320, 7], [49620, 7], [49920, 7], [50220, 7], [50520, 6], [50819, 7], [51119, 7], [51419, 7], [51719, 7], [52019, 7], [52319, 7], [52619, 7], [52919, 7], [53219, 7], [53519, 7], [53820, 7], [54120, 7], [54420, 7], [54720, 7], [55020, 7], [55320, 7], [55621, 6], [55921, 6], [56221, 6], [56521, 6]], "point": [123, 167]}}, "high_idx": 7}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.20|+00.81|+01.90|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 241], [28529, 241], [28829, 241], [29128, 241], [29427, 242], [29727, 242], [30026, 242], [30326, 242], [30625, 243], [30924, 243], [31224, 243], [31523, 243], [31823, 243], [32122, 244], [32421, 244], [32721, 98], [32843, 31], [32881, 84], [33020, 93], [33185, 79], [33320, 91], [33485, 79], [33619, 92], [33738, 16], [33785, 79], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 124], [22087, 92], [22241, 123], [22389, 89], [22541, 123], [22690, 88], [22840, 123], [22991, 86], [23140, 123], [23292, 85], [23439, 124], [23593, 84], [23739, 124], [23893, 83], [24038, 125], [24193, 83], [24337, 126], [24494, 82], [24637, 125], [24794, 81], [24936, 126], [25094, 81], [25236, 126], [25394, 80], [25535, 127], [25694, 80], [25834, 128], [25995, 79], [26134, 128], [26295, 78], [26433, 129], [26595, 78], [26733, 129], [26895, 77], [27032, 130], [27195, 77], [27331, 131], [27495, 77], [27631, 131], [27795, 76], [27930, 132], [28095, 76], [28230, 132], [28395, 76], [28529, 133], [28695, 75], [28829, 133], [28995, 75], [29128, 134], [29295, 74], [29427, 135], [29595, 74], [29727, 135], [29894, 75], [30026, 136], [30194, 74], [30326, 136], [30494, 74], [30625, 137], [30794, 74], [30924, 138], [31094, 73], [31224, 138], [31393, 74], [31523, 139], [31693, 73], [31823, 139], [31993, 73], [32122, 140], [32293, 73], [32421, 141], [32592, 73], [32721, 98], [32843, 19], [32891, 74], [33020, 93], [33191, 73], [33320, 91], [33490, 74], [33619, 92], [33738, 16], [33789, 75], [33918, 144], [34088, 75], [34218, 144], [34387, 76], [34517, 145], [34686, 77], [34817, 148], [34984, 78], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.20|+00.81|+01.90|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [162, 74, 194, 117], "mask": [[22066, 21], [22364, 25], [22664, 26], [22963, 28], [23263, 29], [23563, 30], [23863, 30], [24163, 30], [24463, 31], [24762, 32], [25062, 32], [25362, 32], [25662, 32], [25962, 33], [26262, 33], [26562, 33], [26862, 33], [27162, 33], [27462, 33], [27762, 33], [28062, 33], [28362, 33], [28662, 33], [28962, 33], [29262, 33], [29562, 33], [29862, 32], [30162, 32], [30462, 32], [30762, 32], [31062, 32], [31362, 31], [31662, 31], [31962, 31], [32262, 31], [32562, 30], [32862, 29], [33162, 29], [33462, 28], [33762, 27], [34062, 26], [34362, 25], [34662, 24], [34965, 19]], "point": [178, 94]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 241], [28529, 241], [28829, 241], [29128, 241], [29427, 242], [29727, 242], [30026, 242], [30326, 242], [30625, 243], [30924, 243], [31224, 243], [31523, 243], [31823, 243], [32122, 244], [32421, 244], [32721, 98], [32843, 31], [32881, 84], [33020, 93], [33185, 79], [33320, 91], [33485, 79], [33619, 92], [33738, 16], [33785, 79], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 9}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 233], "mask": [[0, 56399], [56400, 298], [56700, 297], [57000, 295], [57300, 294], [57601, 292], [57902, 289], [58204, 286], [58505, 284], [58807, 280], [59108, 278], [59409, 276], [59711, 272], [60012, 270], [60314, 267], [60615, 264], [60916, 262], [61218, 259], [61519, 256], [61821, 253], [62122, 251], [62423, 248], [62725, 245], [63026, 243], [63327, 240], [63629, 237], [63930, 235], [64232, 231], [64533, 229], [64834, 227], [65136, 223], [65437, 221], [65739, 218], [66040, 215], [66341, 213], [66643, 210], [66944, 207], [67246, 204], [67547, 202], [67848, 199], [68150, 196], [68451, 194], [68752, 191], [69054, 188], [69355, 186], [69657, 182]], "point": [149, 116]}}, "high_idx": 11}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.20|+00.81|+01.90|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 24764], [24780, 278], [25085, 269], [25389, 261], [25693, 256], [25994, 255], [26294, 254], [26595, 253], [26896, 252], [27196, 251], [27497, 250], [27797, 249], [28098, 248], [28398, 248], [28698, 248], [28999, 246], [29299, 246], [29599, 246], [29900, 245], [30200, 245], [30500, 245], [30800, 245], [31100, 245], [31400, 245], [31700, 245], [32000, 245], [32300, 245], [32600, 246], [32900, 246], [33200, 246], [33500, 247], [33799, 248], [34099, 248], [34399, 249], [34699, 249], [34998, 250], [35298, 251], [35598, 251], [35898, 252], [36197, 255], [36494, 262], [36790, 270], [37086, 280], [37381, 5960], [43354, 146], [43501, 139], [43655, 145], [43802, 137], [43955, 145], [44103, 136], [44256, 144], [44404, 123], [44528, 10], [44556, 144], [44705, 118], [44831, 7], [44857, 143], [45006, 116], [45133, 5], [45157, 143], [45307, 114], [45434, 4], [45457, 143], [45608, 113], [45734, 4], [45757, 143], [45909, 111], [46035, 3], [46057, 143], [46210, 110], [46336, 2], [46357, 143], [46511, 109], [46636, 2], [46657, 143], [46812, 108], [46937, 1], [46957, 143], [47113, 106], [47237, 1], [47257, 143], [47414, 105], [47537, 1], [47557, 143], [47715, 104], [47837, 2], [47857, 143], [48016, 103], [48137, 2], [48157, 143], [48317, 102], [48437, 2], [48457, 143], [48618, 101], [48737, 2], [48756, 144], [48919, 100], [49037, 2], [49056, 144], [49220, 100], [49337, 2], [49356, 144], [49521, 99], [49636, 3], [49656, 144], [49822, 99], [49936, 3], [49956, 144], [50123, 98], [50236, 3], [50256, 144], [50424, 98], [50535, 4], [50556, 144], [50724, 99], [50835, 4], [50856, 144], [51025, 99], [51133, 6], [51156, 144], [51326, 100], [51432, 8], [51456, 144], [51627, 113], [51755, 145], [51928, 113], [52054, 146], [52229, 113], [52354, 146], [52530, 113], [52652, 148], [52831, 115], [52949, 151], [53132, 268], [53433, 267], [53734, 266], [54035, 265], [54336, 264], [54637, 263], [54938, 262], [55239, 261], [55540, 260], [55841, 259], [56142, 258], [56443, 257], [56744, 256], [57045, 255], [57346, 254], [57647, 253], [57948, 252], [58249, 251], [58549, 251], [58850, 250], [59151, 249], [59452, 248], [59753, 247], [60054, 246], [60355, 245], [60656, 244], [60957, 243], [61258, 242], [61559, 241], [61860, 240], [62162, 172], [62335, 65], [62635, 65], [62935, 65], [63235, 65], [63535, 65], [63835, 65], [64135, 65], [64436, 64], [64736, 64], [65036, 64], [65336, 64], [65636, 64], [65936, 64], [66237, 63], [66537, 63], [66837, 63], [67137, 63], [67437, 63], [67737, 63], [68038, 62], [68338, 62], [68638, 62], [68938, 62], [69238, 62], [69538, 62], [69839, 61], [70139, 61], [70439, 61], [70739, 61], [71039, 61], [71339, 61], [71640, 60], [71940, 60], [72240, 60], [72540, 60], [72840, 60], [73140, 60], [73441, 59], [73741, 59], [74041, 59], [74341, 59], [74641, 59], [74941, 59], [75242, 58], [75542, 58], [75842, 58], [76142, 58], [76442, 58], [76742, 58], [77043, 57], [77343, 57], [77643, 57], [77943, 57], [78243, 57], [78543, 57], [78844, 56], [79144, 56], [79444, 56], [79744, 56], [80044, 56], [80344, 56], [80645, 55], [80945, 55], [81245, 55], [81545, 55], [81845, 55], [82145, 55], [82446, 54], [82746, 54], [83046, 54], [83346, 54], [83646, 54], [83946, 54], [84247, 53], [84547, 53], [84847, 53], [85147, 53], [85447, 53], [85747, 53], [86048, 52], [86348, 52], [86648, 52], [86948, 52], [87248, 52], [87548, 52], [87849, 51], [88149, 51], [88449, 51], [88749, 51], [89049, 51], [89349, 51], [89650, 50], [89950, 50]], "point": [149, 143]}}, "high_idx": 11}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 24763], [24779, 279], [25085, 269], [25389, 261], [25693, 256], [25994, 255], [26294, 254], [26595, 253], [26896, 252], [27196, 251], [27497, 250], [27797, 249], [28098, 248], [28398, 248], [28698, 248], [28999, 246], [29299, 234], [29599, 232], [29900, 231], [30200, 231], [30500, 230], [30800, 230], [31100, 230], [31400, 230], [31700, 230], [32000, 230], [32300, 230], [32600, 231], [32900, 231], [33200, 231], [33500, 231], [33799, 232], [34099, 232], [34399, 232], [34699, 232], [34998, 234], [35298, 234], [35598, 234], [35898, 234], [36197, 235], [36494, 238], [36790, 242], [37086, 246], [37360, 6], [37381, 251], [37659, 274], [37958, 275], [38257, 276], [38556, 278], [38854, 4487], [43354, 146], [43501, 139], [43655, 145], [43802, 137], [43955, 145], [44103, 136], [44256, 144], [44404, 123], [44528, 10], [44556, 144], [44705, 118], [44831, 7], [44857, 143], [45006, 116], [45133, 5], [45157, 143], [45307, 114], [45434, 4], [45457, 143], [45608, 113], [45734, 4], [45757, 143], [45909, 111], [46035, 3], [46057, 143], [46210, 110], [46336, 2], [46357, 143], [46511, 109], [46636, 2], [46657, 143], [46812, 108], [46937, 1], [46957, 143], [47113, 106], [47237, 1], [47257, 143], [47414, 105], [47537, 1], [47557, 143], [47715, 104], [47837, 2], [47857, 143], [48016, 103], [48137, 2], [48157, 143], [48317, 102], [48437, 2], [48457, 143], [48618, 101], [48737, 2], [48756, 144], [48919, 100], [49037, 2], [49056, 144], [49220, 100], [49337, 2], [49356, 144], [49521, 99], [49636, 3], [49656, 144], [49822, 99], [49936, 3], [49956, 144], [50123, 98], [50236, 3], [50256, 144], [50424, 98], [50535, 4], [50556, 144], [50724, 99], [50835, 4], [50856, 144], [51025, 99], [51133, 6], [51156, 144], [51326, 100], [51432, 8], [51456, 144], [51627, 113], [51755, 145], [51928, 113], [52054, 146], [52229, 113], [52354, 146], [52530, 113], [52652, 148], [52831, 115], [52949, 151], [53132, 268], [53433, 267], [53734, 266], [54035, 265], [54336, 264], [54637, 263], [54938, 262], [55239, 261], [55540, 260], [55841, 259], [56142, 258], [56443, 257], [56744, 256], [57045, 255], [57346, 254], [57647, 253], [57948, 252], [58249, 251], [58549, 251], [58850, 250], [59151, 249], [59452, 248], [59753, 247], [60054, 246], [60355, 245], [60656, 244], [60957, 243], [61258, 242], [61559, 241], [61860, 240], [62162, 172], [62335, 65], [62635, 65], [62935, 65], [63235, 65], [63535, 65], [63835, 65], [64135, 65], [64436, 64], [64736, 64], [65036, 64], [65336, 64], [65636, 64], [65936, 64], [66237, 63], [66537, 63], [66837, 63], [67137, 63], [67437, 63], [67737, 63], [68038, 62], [68338, 62], [68638, 62], [68938, 62], [69238, 62], [69538, 62], [69839, 61], [70139, 61], [70439, 61], [70739, 61], [71039, 61], [71339, 61], [71640, 60], [71940, 60], [72240, 60], [72540, 60], [72840, 60], [73140, 60], [73441, 59], [73741, 59], [74041, 59], [74341, 59], [74641, 59], [74941, 59], [75242, 58], [75542, 58], [75842, 58], [76142, 58], [76442, 58], [76742, 58], [77043, 57], [77343, 57], [77643, 57], [77943, 57], [78243, 57], [78543, 57], [78844, 56], [79144, 56], [79444, 56], [79744, 56], [80044, 56], [80344, 56], [80645, 55], [80945, 55], [81245, 55], [81545, 55], [81845, 55], [82145, 55], [82446, 54], [82746, 54], [83046, 54], [83346, 54], [83646, 54], [83946, 54], [84247, 53], [84547, 53], [84847, 53], [85147, 53], [85447, 53], [85747, 53], [86048, 52], [86348, 52], [86648, 52], [86948, 52], [87248, 52], [87548, 52], [87849, 51], [88149, 51], [88449, 51], [88749, 51], [89049, 51], [89349, 51], [89650, 50], [89950, 50]], "point": [149, 143]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan17", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 0.0, "y": 0.908999562, "z": 0.5}, "object_poses": [{"objectName": "Potato_edec52fd", "position": {"x": -1.13077056, "y": 0.386491656, "z": 0.883312941}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": 1.088089, "y": 0.115419507, "z": 1.11956179}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": 1.117845, "y": 0.8870167, "z": 2.477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": -0.813686848, "y": 0.9128167, "z": -0.907535136}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": 1.284095, "y": 0.8873413, "z": 2.36918736}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": 0.802, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.9998245, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": -1.19785714, "y": 0.7501748, "z": 3.015717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.5029, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": 1.444309, "y": 0.910747945, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": -1.29051554, "y": 0.750511765, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": -1.20685542, "y": 0.7520431, "z": 1.09268773}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": 0.951595068, "y": 0.936028838, "z": 2.15356255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": -1.10519874, "y": 0.7480669, "z": 2.273428}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": 1.20097, "y": 0.8826062, "z": 2.58481264}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": 1.52327764, "y": 0.9078062, "z": -0.144580841}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -0.0824483, "y": 0.7535562, "z": -0.533726156}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -1.13565314, "y": 0.907099962, "z": -0.732326269}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": -1.19785714, "y": 0.813041151, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": -1.2630949, "y": 1.6564405, "z": 0.184292}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": 1.36722, "y": 0.886848569, "z": 2.15356255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": 0.802, "y": 0.9393, "z": -0.47308147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -0.968678534, "y": 0.9618288, "z": -0.907535136}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 1.444309, "y": 0.9123855, "z": 0.4229527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": -1.0601548, "y": 0.9098116, "z": -0.477608353}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": -1.01254034, "y": 0.8198685, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": -1.20685518, "y": 1.42174935, "z": 0.9880003}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": -1.513145, "y": 0.913428545, "z": -0.477608353}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": -1.09907186, "y": 0.342489481, "z": 0.9880006}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": -1.29051554, "y": 0.764626145, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": -0.252610981, "y": 0.1150794, "z": -0.5777503}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -1.43365359, "y": 0.972304, "z": -0.907535136}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": 1.450345, "y": 0.885547936, "z": 2.58481264}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": 1.28637147, "y": 0.9334927, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": 0.146276712, "y": 0.9462664, "z": -0.933621049}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": -1.12367022, "y": 0.9084062, "z": -0.907535136}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": 1.0494653, "y": 0.9078062, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": 1.36534023, "y": 0.9564309, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": 1.20740271, "y": 0.9115421, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": 1.117845, "y": 0.885864258, "z": 2.15356255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_a0065d6c", "position": {"x": -1.332, "y": 0.9, "z": -0.699}, "rotation": {"x": 0.0, "y": 345.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 1.20097, "y": 0.8874294, "z": 2.15356255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": -1.01254034, "y": 0.748304069, "z": 3.015717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -1.38878727, "y": 1.65072381, "z": 0.146339446}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 66044228, "scene_num": 17}, "task_id": "trial_T20190907_144123_814906", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A17TKHT8FEVH0R_3WR9XG3T66SK02AXS5EYA9XP3D6741", "high_descs": ["Walk up to the counter in front of you", "Grab the small knife that's on the counter", "Turn around and head to the white table", "Cut the loaf of bread on the table into slices", "Turn left and head to the microwave", "Open the microwave and place the knife inside", "Turn right and stop at the white table", "Grab one of the slices of bread", "Turn left and head to the microwave", "Open the microwave the cook the bread inside. Take the bread out.", "Turn right and stop at the fridge", "Open the fridge. Place the slice of bread on the top rack. Close the fridge."], "task_desc": "Putting a slice of bread in the fridge", "votes": [1, 1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A3HL2LL0LEPZT8_38JBBYETQRR59HP7FDS4GJP6PA54EZ", "high_descs": ["Go forward to the counter.", "Take the small knife on the left of the counter.", "Turn left, go forward, turn left after passing the fridge on the left, go forward to the white table next to the fridge.", "Cut the bread on the table into slices.", "Turn left, go forward to the counter, turn right to face the microwave.", "Put the knife in the microwave.", "Turn right, go forward, turn left to the bread on the white table.", "Take a slice of bread from the table.", "Turn left, go forward to the counter, turn right to face the microwave.", "Heat the piece of bread in the microwave then take it out.", "Go to the fridge to the right.", "Put the bread in the upper shelf of the fridge."], "task_desc": "Put a heated slice of bread in a fridge.", "votes": [1, 1, 1, 1, 1, 1]}, {"assignment_id": "A3C81THYYSBGVD_3DZQRBDBSOWL14BLGU7QVV1FC1I3SE", "high_descs": ["Move forward towards the counter in front of you.", "Take the knife from the counter in front of you.", "Turn left, then left again and face the counter with the bread on it in front of you.", "Slice the bread on the counter.", "Turn left, then face the microwave on your right.", "Place the knife in the microwave in front of you and close it.", "Turn right then face the bread on the counter to your left.", "Take a slice of bread off of the counter in front of you.", "Turn left and face the microwave on your right.", "Heat up the slice of bread, take it out and close the microwave.", "Turn right and face the fridge on your left.", "Open the fridge, place the slice of bread inside and close the fridge."], "task_desc": "Place a hot slice of bread in the fridge.", "votes": [1, 1, 1, 1, 1, 1]}, {"assignment_id": "AFPMG8TLP1TND_3IXQG4FA2WF8ZKJZMHM5WJU188N9BN", "high_descs": ["Look down then go forward to the counter.", "Pick up the knife next to the salt ", "Turn around and then go right walk straight to the white table and turn left and walk forward.", "Cut the bread into 8 slices near the edge of the table", "Turn left and then walk forward to the counter then turn right toward the microwave.", "Open the microwave and put the knife inside and close the door.", "Turn right walk straight and then turn left at the table", "grab a slice of bread", "turn left then walk forward to the counter then turn right towards the microwave.", "open the microwave door and put the bread inside and shut the door, turn on the timer and open the door and remove the bread slice", "turn right and walk to the refrigerator and turn left and look down.", "open the refrigerator door and put the bread slice inside and shut the door."], "task_desc": "Cooking a slice of bread and then putting it into the refrigerator. ", "votes": [1, 1]}, {"assignment_id": "A1OWHPMKE7YAGL_3X08E93BHYZFBP06SJ2L1TH745E66W", "high_descs": ["Go forward to the counter. ", "Pick up the butter knife that is on the counter behind the salt. ", "Turn around and go right towards the other end of the room, then turn to the left and go to the table with the bread on it. ", "Use the butter knife to cut the bread into slices. ", "Turn to the left and go to the microwave on the other side of the fridge. ", "Open the microwave, but the butter knife inside, and close the microwave. ", "Turn to the right and go back to the table with the bread on it. ", "Pick up a slice of the bread. ", "Head back to the microwave.", "Open the microwave and put the bread inside, turn the microwave on to cook, then take the bread out of the microwave. ", "Turn to the right and go to the fridge.", "Open the fridge and put the bread inside, then close the fridge. "], "task_desc": "Put a cooked slice of bread inside a fridge. ", "votes": [1, 1]}]}}