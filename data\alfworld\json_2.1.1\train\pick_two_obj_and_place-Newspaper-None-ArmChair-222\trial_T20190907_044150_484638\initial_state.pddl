
(define (problem plan_trial_T20190907_044150_484638)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__plus_02_dot_81_bar__plus_00_dot_65_bar__plus_00_dot_25 - object
        CreditCard_bar__plus_00_dot_37_bar__plus_00_dot_24_bar__minus_00_dot_25 - object
        CreditCard_bar__plus_02_dot_89_bar__plus_00_dot_46_bar__plus_00_dot_45 - object
        CreditCard_bar__plus_03_dot_13_bar__plus_00_dot_60_bar__plus_01_dot_45 - object
        Curtains_bar__plus_01_dot_20_bar__plus_00_dot_00_bar__minus_02_dot_44 - object
        FloorLamp_bar__minus_01_dot_16_bar__plus_00_dot_00_bar__plus_01_dot_04 - object
        HousePlant_bar__plus_01_dot_92_bar__plus_01_dot_72_bar__minus_02_dot_19 - object
        KeyChain_bar__plus_00_dot_11_bar__plus_00_dot_37_bar__plus_00_dot_13 - object
        KeyChain_bar__minus_00_dot_05_bar__plus_00_dot_49_bar__minus_01_dot_66 - object
        Laptop_bar__plus_00_dot_23_bar__plus_00_dot_36_bar__minus_00_dot_31 - object
        LightSwitch_bar__minus_01_dot_01_bar__plus_01_dot_29_bar__plus_01_dot_95 - object
        Newspaper_bar__plus_01_dot_92_bar__plus_00_dot_88_bar__plus_01_dot_70 - object
        Newspaper_bar__plus_02_dot_96_bar__plus_00_dot_61_bar__plus_01_dot_63 - object
        Painting_bar__plus_03_dot_42_bar__plus_01_dot_71_bar__minus_00_dot_16 - object
        Painting_bar__minus_01_dot_45_bar__plus_01_dot_79_bar__minus_00_dot_34 - object
        Pillow_bar__minus_00_dot_88_bar__plus_00_dot_58_bar__minus_00_dot_80 - object
        RemoteControl_bar__plus_02_dot_66_bar__plus_00_dot_46_bar__plus_00_dot_65 - object
        Statue_bar__plus_00_dot_52_bar__plus_00_dot_42_bar__plus_01_dot_47 - object
        Statue_bar__minus_00_dot_63_bar__plus_00_dot_61_bar__minus_01_dot_60 - object
        Television_bar__plus_00_dot_86_bar__plus_00_dot_90_bar__plus_01_dot_73 - object
        Watch_bar__plus_00_dot_09_bar__plus_00_dot_24_bar__minus_00_dot_17 - object
        Window_bar__plus_00_dot_65_bar__plus_01_dot_55_bar__minus_02_dot_47 - object
        ArmChair_bar__plus_00_dot_22_bar__plus_00_dot_01_bar__minus_01_dot_73 - receptacle
        CoffeeTable_bar__plus_00_dot_23_bar__plus_00_dot_01_bar__minus_00_dot_17 - receptacle
        Drawer_bar__plus_00_dot_10_bar__plus_00_dot_05_bar__minus_00_dot_17 - receptacle
        Drawer_bar__plus_00_dot_10_bar__plus_00_dot_15_bar__minus_00_dot_17 - receptacle
        Drawer_bar__plus_00_dot_10_bar__plus_00_dot_26_bar__minus_00_dot_17 - receptacle
        Drawer_bar__plus_00_dot_35_bar__plus_00_dot_05_bar__minus_00_dot_17 - receptacle
        Drawer_bar__plus_00_dot_35_bar__plus_00_dot_15_bar__minus_00_dot_17 - receptacle
        Drawer_bar__plus_00_dot_35_bar__plus_00_dot_26_bar__minus_00_dot_17 - receptacle
        Drawer_bar__plus_02_dot_00_bar__plus_00_dot_77_bar__plus_01_dot_70 - receptacle
        GarbageCan_bar__plus_01_dot_30_bar__plus_00_dot_00_bar__minus_02_dot_19 - receptacle
        SideTable_bar__plus_02_dot_00_bar__plus_00_dot_00_bar__plus_01_dot_75 - receptacle
        SideTable_bar__plus_02_dot_96_bar__plus_00_dot_00_bar__plus_01_dot_57 - receptacle
        SideTable_bar__minus_00_dot_68_bar__plus_00_dot_00_bar__minus_01_dot_60 - receptacle
        Sofa_bar__plus_02_dot_87_bar_00_dot_00_bar__plus_00_dot_25 - receptacle
        Sofa_bar__minus_01_dot_07_bar_00_dot_00_bar__minus_00_dot_20 - receptacle
        TVStand_bar__plus_00_dot_84_bar_00_dot_00_bar__plus_01_dot_58 - receptacle
        loc_bar__minus_2_bar_4_bar_2_bar_60 - location
        loc_bar_6_bar_2_bar_0_bar_30 - location
        loc_bar__minus_1_bar__minus_3_bar_2_bar_60 - location
        loc_bar__minus_2_bar_6_bar_3_bar_30 - location
        loc_bar_1_bar__minus_4_bar_2_bar_60 - location
        loc_bar_6_bar__minus_3_bar_3_bar_45 - location
        loc_bar_5_bar__minus_7_bar_2_bar_60 - location
        loc_bar_3_bar_4_bar_2_bar_60 - location
        loc_bar_4_bar__minus_7_bar_2_bar_15 - location
        loc_bar__minus_1_bar_0_bar_3_bar_60 - location
        loc_bar_8_bar_5_bar_1_bar_60 - location
        loc_bar__minus_2_bar_4_bar_3_bar_60 - location
        loc_bar_7_bar_1_bar_1_bar_60 - location
        loc_bar_9_bar__minus_1_bar_1_bar_0 - location
        loc_bar_3_bar_2_bar_0_bar_60 - location
        loc_bar_7_bar_4_bar_0_bar_60 - location
        loc_bar__minus_1_bar__minus_1_bar_3_bar_0 - location
        loc_bar_6_bar__minus_3_bar_3_bar_60 - location
        loc_bar__minus_2_bar_4_bar_2_bar_45 - location
        loc_bar_6_bar__minus_7_bar_2_bar__minus_15 - location
        loc_bar_3_bar_2_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType SideTable_bar__minus_00_dot_68_bar__plus_00_dot_00_bar__minus_01_dot_60 SideTableType)
        (receptacleType Sofa_bar__minus_01_dot_07_bar_00_dot_00_bar__minus_00_dot_20 SofaType)
        (receptacleType Drawer_bar__plus_02_dot_00_bar__plus_00_dot_77_bar__plus_01_dot_70 DrawerType)
        (receptacleType Drawer_bar__plus_00_dot_35_bar__plus_00_dot_15_bar__minus_00_dot_17 DrawerType)
        (receptacleType Drawer_bar__plus_00_dot_10_bar__plus_00_dot_05_bar__minus_00_dot_17 DrawerType)
        (receptacleType CoffeeTable_bar__plus_00_dot_23_bar__plus_00_dot_01_bar__minus_00_dot_17 CoffeeTableType)
        (receptacleType Drawer_bar__plus_00_dot_35_bar__plus_00_dot_05_bar__minus_00_dot_17 DrawerType)
        (receptacleType TVStand_bar__plus_00_dot_84_bar_00_dot_00_bar__plus_01_dot_58 TVStandType)
        (receptacleType GarbageCan_bar__plus_01_dot_30_bar__plus_00_dot_00_bar__minus_02_dot_19 GarbageCanType)
        (receptacleType SideTable_bar__plus_02_dot_00_bar__plus_00_dot_00_bar__plus_01_dot_75 SideTableType)
        (receptacleType SideTable_bar__plus_02_dot_96_bar__plus_00_dot_00_bar__plus_01_dot_57 SideTableType)
        (receptacleType ArmChair_bar__plus_00_dot_22_bar__plus_00_dot_01_bar__minus_01_dot_73 ArmChairType)
        (receptacleType Sofa_bar__plus_02_dot_87_bar_00_dot_00_bar__plus_00_dot_25 SofaType)
        (receptacleType Drawer_bar__plus_00_dot_10_bar__plus_00_dot_26_bar__minus_00_dot_17 DrawerType)
        (receptacleType Drawer_bar__plus_00_dot_10_bar__plus_00_dot_15_bar__minus_00_dot_17 DrawerType)
        (receptacleType Drawer_bar__plus_00_dot_35_bar__plus_00_dot_26_bar__minus_00_dot_17 DrawerType)
        (objectType Laptop_bar__plus_00_dot_23_bar__plus_00_dot_36_bar__minus_00_dot_31 LaptopType)
        (objectType Newspaper_bar__plus_02_dot_96_bar__plus_00_dot_61_bar__plus_01_dot_63 NewspaperType)
        (objectType Watch_bar__plus_00_dot_09_bar__plus_00_dot_24_bar__minus_00_dot_17 WatchType)
        (objectType Newspaper_bar__plus_01_dot_92_bar__plus_00_dot_88_bar__plus_01_dot_70 NewspaperType)
        (objectType Statue_bar__plus_00_dot_52_bar__plus_00_dot_42_bar__plus_01_dot_47 StatueType)
        (objectType Painting_bar__minus_01_dot_45_bar__plus_01_dot_79_bar__minus_00_dot_34 PaintingType)
        (objectType CreditCard_bar__plus_00_dot_37_bar__plus_00_dot_24_bar__minus_00_dot_25 CreditCardType)
        (objectType CreditCard_bar__plus_03_dot_13_bar__plus_00_dot_60_bar__plus_01_dot_45 CreditCardType)
        (objectType Painting_bar__plus_03_dot_42_bar__plus_01_dot_71_bar__minus_00_dot_16 PaintingType)
        (objectType HousePlant_bar__plus_01_dot_92_bar__plus_01_dot_72_bar__minus_02_dot_19 HousePlantType)
        (objectType CreditCard_bar__plus_02_dot_89_bar__plus_00_dot_46_bar__plus_00_dot_45 CreditCardType)
        (objectType FloorLamp_bar__minus_01_dot_16_bar__plus_00_dot_00_bar__plus_01_dot_04 FloorLampType)
        (objectType RemoteControl_bar__plus_02_dot_66_bar__plus_00_dot_46_bar__plus_00_dot_65 RemoteControlType)
        (objectType Box_bar__plus_02_dot_81_bar__plus_00_dot_65_bar__plus_00_dot_25 BoxType)
        (objectType Television_bar__plus_00_dot_86_bar__plus_00_dot_90_bar__plus_01_dot_73 TelevisionType)
        (objectType Curtains_bar__plus_01_dot_20_bar__plus_00_dot_00_bar__minus_02_dot_44 CurtainsType)
        (objectType Statue_bar__minus_00_dot_63_bar__plus_00_dot_61_bar__minus_01_dot_60 StatueType)
        (objectType KeyChain_bar__plus_00_dot_11_bar__plus_00_dot_37_bar__plus_00_dot_13 KeyChainType)
        (objectType Pillow_bar__minus_00_dot_88_bar__plus_00_dot_58_bar__minus_00_dot_80 PillowType)
        (objectType LightSwitch_bar__minus_01_dot_01_bar__plus_01_dot_29_bar__plus_01_dot_95 LightSwitchType)
        (objectType Window_bar__plus_00_dot_65_bar__plus_01_dot_55_bar__minus_02_dot_47 WindowType)
        (objectType KeyChain_bar__minus_00_dot_05_bar__plus_00_dot_49_bar__minus_01_dot_66 KeyChainType)
        (canContain SideTableType WatchType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain CoffeeTableType WatchType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain GarbageCanType NewspaperType)
        (canContain SideTableType WatchType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WatchType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (pickupable Laptop_bar__plus_00_dot_23_bar__plus_00_dot_36_bar__minus_00_dot_31)
        (pickupable Newspaper_bar__plus_02_dot_96_bar__plus_00_dot_61_bar__plus_01_dot_63)
        (pickupable Watch_bar__plus_00_dot_09_bar__plus_00_dot_24_bar__minus_00_dot_17)
        (pickupable Newspaper_bar__plus_01_dot_92_bar__plus_00_dot_88_bar__plus_01_dot_70)
        (pickupable Statue_bar__plus_00_dot_52_bar__plus_00_dot_42_bar__plus_01_dot_47)
        (pickupable CreditCard_bar__plus_00_dot_37_bar__plus_00_dot_24_bar__minus_00_dot_25)
        (pickupable CreditCard_bar__plus_03_dot_13_bar__plus_00_dot_60_bar__plus_01_dot_45)
        (pickupable CreditCard_bar__plus_02_dot_89_bar__plus_00_dot_46_bar__plus_00_dot_45)
        (pickupable RemoteControl_bar__plus_02_dot_66_bar__plus_00_dot_46_bar__plus_00_dot_65)
        (pickupable Box_bar__plus_02_dot_81_bar__plus_00_dot_65_bar__plus_00_dot_25)
        (pickupable Statue_bar__minus_00_dot_63_bar__plus_00_dot_61_bar__minus_01_dot_60)
        (pickupable KeyChain_bar__plus_00_dot_11_bar__plus_00_dot_37_bar__plus_00_dot_13)
        (pickupable Pillow_bar__minus_00_dot_88_bar__plus_00_dot_58_bar__minus_00_dot_80)
        (pickupable KeyChain_bar__minus_00_dot_05_bar__plus_00_dot_49_bar__minus_01_dot_66)
        (isReceptacleObject Box_bar__plus_02_dot_81_bar__plus_00_dot_65_bar__plus_00_dot_25)
        (openable Drawer_bar__plus_02_dot_00_bar__plus_00_dot_77_bar__plus_01_dot_70)
        (openable Drawer_bar__plus_00_dot_35_bar__plus_00_dot_15_bar__minus_00_dot_17)
        (openable Drawer_bar__plus_00_dot_10_bar__plus_00_dot_05_bar__minus_00_dot_17)
        (openable Drawer_bar__plus_00_dot_35_bar__plus_00_dot_05_bar__minus_00_dot_17)
        (openable Drawer_bar__plus_00_dot_10_bar__plus_00_dot_26_bar__minus_00_dot_17)
        (openable Drawer_bar__plus_00_dot_10_bar__plus_00_dot_15_bar__minus_00_dot_17)
        (openable Drawer_bar__plus_00_dot_35_bar__plus_00_dot_26_bar__minus_00_dot_17)
        
        (atLocation agent1 loc_bar_3_bar_2_bar_2_bar_30)
        
        
        
        
        
        
        
        (toggleable FloorLamp_bar__minus_01_dot_16_bar__plus_00_dot_00_bar__plus_01_dot_04)
        
        
        
        
        (inReceptacle Newspaper_bar__plus_01_dot_92_bar__plus_00_dot_88_bar__plus_01_dot_70 SideTable_bar__plus_02_dot_00_bar__plus_00_dot_00_bar__plus_01_dot_75)
        (inReceptacle KeyChain_bar__plus_00_dot_11_bar__plus_00_dot_37_bar__plus_00_dot_13 CoffeeTable_bar__plus_00_dot_23_bar__plus_00_dot_01_bar__minus_00_dot_17)
        (inReceptacle Laptop_bar__plus_00_dot_23_bar__plus_00_dot_36_bar__minus_00_dot_31 CoffeeTable_bar__plus_00_dot_23_bar__plus_00_dot_01_bar__minus_00_dot_17)
        (inReceptacle Pillow_bar__minus_00_dot_88_bar__plus_00_dot_58_bar__minus_00_dot_80 Sofa_bar__minus_01_dot_07_bar_00_dot_00_bar__minus_00_dot_20)
        (inReceptacle Newspaper_bar__plus_02_dot_96_bar__plus_00_dot_61_bar__plus_01_dot_63 SideTable_bar__plus_02_dot_96_bar__plus_00_dot_00_bar__plus_01_dot_57)
        (inReceptacle CreditCard_bar__plus_03_dot_13_bar__plus_00_dot_60_bar__plus_01_dot_45 SideTable_bar__plus_02_dot_96_bar__plus_00_dot_00_bar__plus_01_dot_57)
        (inReceptacle Statue_bar__minus_00_dot_63_bar__plus_00_dot_61_bar__minus_01_dot_60 SideTable_bar__minus_00_dot_68_bar__plus_00_dot_00_bar__minus_01_dot_60)
        (inReceptacle Watch_bar__plus_00_dot_09_bar__plus_00_dot_24_bar__minus_00_dot_17 Drawer_bar__plus_00_dot_10_bar__plus_00_dot_26_bar__minus_00_dot_17)
        (inReceptacle KeyChain_bar__minus_00_dot_05_bar__plus_00_dot_49_bar__minus_01_dot_66 ArmChair_bar__plus_00_dot_22_bar__plus_00_dot_01_bar__minus_01_dot_73)
        (inReceptacle CreditCard_bar__plus_00_dot_37_bar__plus_00_dot_24_bar__minus_00_dot_25 Drawer_bar__plus_00_dot_35_bar__plus_00_dot_26_bar__minus_00_dot_17)
        (inReceptacle Statue_bar__plus_00_dot_52_bar__plus_00_dot_42_bar__plus_01_dot_47 TVStand_bar__plus_00_dot_84_bar_00_dot_00_bar__plus_01_dot_58)
        (inReceptacle Television_bar__plus_00_dot_86_bar__plus_00_dot_90_bar__plus_01_dot_73 TVStand_bar__plus_00_dot_84_bar_00_dot_00_bar__plus_01_dot_58)
        (inReceptacle Box_bar__plus_02_dot_81_bar__plus_00_dot_65_bar__plus_00_dot_25 Sofa_bar__plus_02_dot_87_bar_00_dot_00_bar__plus_00_dot_25)
        (inReceptacle CreditCard_bar__plus_02_dot_89_bar__plus_00_dot_46_bar__plus_00_dot_45 Sofa_bar__plus_02_dot_87_bar_00_dot_00_bar__plus_00_dot_25)
        (inReceptacle RemoteControl_bar__plus_02_dot_66_bar__plus_00_dot_46_bar__plus_00_dot_65 Sofa_bar__plus_02_dot_87_bar_00_dot_00_bar__plus_00_dot_25)
        
        
        (receptacleAtLocation ArmChair_bar__plus_00_dot_22_bar__plus_00_dot_01_bar__minus_01_dot_73 loc_bar_1_bar__minus_4_bar_2_bar_60)
        (receptacleAtLocation CoffeeTable_bar__plus_00_dot_23_bar__plus_00_dot_01_bar__minus_00_dot_17 loc_bar_3_bar_4_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_10_bar__plus_00_dot_05_bar__minus_00_dot_17 loc_bar__minus_2_bar_4_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_10_bar__plus_00_dot_15_bar__minus_00_dot_17 loc_bar__minus_2_bar_4_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__plus_00_dot_10_bar__plus_00_dot_26_bar__minus_00_dot_17 loc_bar__minus_2_bar_4_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__plus_00_dot_35_bar__plus_00_dot_05_bar__minus_00_dot_17 loc_bar_6_bar__minus_3_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_35_bar__plus_00_dot_15_bar__minus_00_dot_17 loc_bar_6_bar__minus_3_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_35_bar__plus_00_dot_26_bar__minus_00_dot_17 loc_bar_6_bar__minus_3_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__plus_02_dot_00_bar__plus_00_dot_77_bar__plus_01_dot_70 loc_bar_6_bar_2_bar_0_bar_30)
        (receptacleAtLocation GarbageCan_bar__plus_01_dot_30_bar__plus_00_dot_00_bar__minus_02_dot_19 loc_bar_5_bar__minus_7_bar_2_bar_60)
        (receptacleAtLocation SideTable_bar__plus_02_dot_00_bar__plus_00_dot_00_bar__plus_01_dot_75 loc_bar_7_bar_4_bar_0_bar_60)
        (receptacleAtLocation SideTable_bar__plus_02_dot_96_bar__plus_00_dot_00_bar__plus_01_dot_57 loc_bar_8_bar_5_bar_1_bar_60)
        (receptacleAtLocation SideTable_bar__minus_00_dot_68_bar__plus_00_dot_00_bar__minus_01_dot_60 loc_bar__minus_1_bar__minus_3_bar_2_bar_60)
        (receptacleAtLocation Sofa_bar__plus_02_dot_87_bar_00_dot_00_bar__plus_00_dot_25 loc_bar_7_bar_1_bar_1_bar_60)
        (receptacleAtLocation Sofa_bar__minus_01_dot_07_bar_00_dot_00_bar__minus_00_dot_20 loc_bar__minus_1_bar_0_bar_3_bar_60)
        (receptacleAtLocation TVStand_bar__plus_00_dot_84_bar_00_dot_00_bar__plus_01_dot_58 loc_bar_3_bar_2_bar_0_bar_60)
        (objectAtLocation Statue_bar__minus_00_dot_63_bar__plus_00_dot_61_bar__minus_01_dot_60 loc_bar__minus_1_bar__minus_3_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_00_dot_05_bar__plus_00_dot_49_bar__minus_01_dot_66 loc_bar_1_bar__minus_4_bar_2_bar_60)
        (objectAtLocation Newspaper_bar__plus_02_dot_96_bar__plus_00_dot_61_bar__plus_01_dot_63 loc_bar_8_bar_5_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__plus_02_dot_89_bar__plus_00_dot_46_bar__plus_00_dot_45 loc_bar_7_bar_1_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__plus_00_dot_37_bar__plus_00_dot_24_bar__minus_00_dot_25 loc_bar_6_bar__minus_3_bar_3_bar_45)
        (objectAtLocation Curtains_bar__plus_01_dot_20_bar__plus_00_dot_00_bar__minus_02_dot_44 loc_bar_5_bar__minus_7_bar_2_bar_60)
        (objectAtLocation Box_bar__plus_02_dot_81_bar__plus_00_dot_65_bar__plus_00_dot_25 loc_bar_7_bar_1_bar_1_bar_60)
        (objectAtLocation Laptop_bar__plus_00_dot_23_bar__plus_00_dot_36_bar__minus_00_dot_31 loc_bar_3_bar_4_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__plus_03_dot_13_bar__plus_00_dot_60_bar__plus_01_dot_45 loc_bar_8_bar_5_bar_1_bar_60)
        (objectAtLocation Newspaper_bar__plus_01_dot_92_bar__plus_00_dot_88_bar__plus_01_dot_70 loc_bar_7_bar_4_bar_0_bar_60)
        (objectAtLocation HousePlant_bar__plus_01_dot_92_bar__plus_01_dot_72_bar__minus_02_dot_19 loc_bar_6_bar__minus_7_bar_2_bar__minus_15)
        (objectAtLocation FloorLamp_bar__minus_01_dot_16_bar__plus_00_dot_00_bar__plus_01_dot_04 loc_bar__minus_2_bar_4_bar_3_bar_60)
        (objectAtLocation LightSwitch_bar__minus_01_dot_01_bar__plus_01_dot_29_bar__plus_01_dot_95 loc_bar__minus_2_bar_6_bar_3_bar_30)
        (objectAtLocation RemoteControl_bar__plus_02_dot_66_bar__plus_00_dot_46_bar__plus_00_dot_65 loc_bar_7_bar_1_bar_1_bar_60)
        (objectAtLocation Television_bar__plus_00_dot_86_bar__plus_00_dot_90_bar__plus_01_dot_73 loc_bar_3_bar_2_bar_0_bar_60)
        (objectAtLocation Painting_bar__plus_03_dot_42_bar__plus_01_dot_71_bar__minus_00_dot_16 loc_bar_9_bar__minus_1_bar_1_bar_0)
        (objectAtLocation Pillow_bar__minus_00_dot_88_bar__plus_00_dot_58_bar__minus_00_dot_80 loc_bar__minus_1_bar_0_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__plus_00_dot_11_bar__plus_00_dot_37_bar__plus_00_dot_13 loc_bar_3_bar_4_bar_2_bar_60)
        (objectAtLocation Painting_bar__minus_01_dot_45_bar__plus_01_dot_79_bar__minus_00_dot_34 loc_bar__minus_1_bar__minus_1_bar_3_bar_0)
        (objectAtLocation Watch_bar__plus_00_dot_09_bar__plus_00_dot_24_bar__minus_00_dot_17 loc_bar__minus_2_bar_4_bar_2_bar_45)
        (objectAtLocation Window_bar__plus_00_dot_65_bar__plus_01_dot_55_bar__minus_02_dot_47 loc_bar_4_bar__minus_7_bar_2_bar_15)
        (objectAtLocation Statue_bar__plus_00_dot_52_bar__plus_00_dot_42_bar__plus_01_dot_47 loc_bar_3_bar_2_bar_0_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 NewspaperType)
                                    (receptacleType ?r ArmChairType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 NewspaperType)
                                            (receptacleType ?r ArmChairType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            