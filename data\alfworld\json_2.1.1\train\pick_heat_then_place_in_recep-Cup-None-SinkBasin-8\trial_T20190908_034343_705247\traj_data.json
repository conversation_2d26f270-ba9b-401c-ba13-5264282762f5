{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000096.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000097.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000098.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000099.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000100.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000101.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000102.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000103.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000104.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000107.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000108.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000109.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000110.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000111.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000112.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000113.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000114.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000115.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000120.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000121.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000122.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000123.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000124.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000125.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000126.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000129.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000130.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000131.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000132.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000133.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000134.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000206.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000209.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000210.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000211.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000212.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000213.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000216.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000219.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000221.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000225.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000226.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000227.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000230.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000231.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000234.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000236.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000237.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000238.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000239.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000240.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000241.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000242.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000265.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000266.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000267.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000268.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000269.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000270.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000271.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000272.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000273.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000274.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000275.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000276.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000277.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000278.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000279.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000280.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000281.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000282.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000283.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000284.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000285.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000286.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000287.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000288.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000289.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000290.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000291.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000292.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000293.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000294.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000295.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000296.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000297.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000298.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000299.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000300.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000301.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000302.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000303.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000304.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000305.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000306.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000307.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000308.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000309.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000310.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000311.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000312.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000340.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000341.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000342.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000343.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000344.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000345.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000346.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000347.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000348.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000349.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000350.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000351.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000352.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000353.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000354.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000355.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000356.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000357.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000358.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000359.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000360.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000361.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000362.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000363.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000364.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000365.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000366.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000367.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000368.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000369.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000370.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000371.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000372.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000373.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000374.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000375.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000376.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000377.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000378.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000379.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000380.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000381.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000382.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000383.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000384.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000385.png", "low_idx": 60}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "SinkBasin", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-4|2|-15"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-3.81599188, -3.81599188, -7.5039468, -7.5039468, 7.40410852, 7.40410852]], "coordinateReceptacleObjectId": ["Cabinet", [-3.475200652, -3.475200652, -6.764, -6.764, 8.02, 8.02]], "forceVisible": true, "objectId": "Cup|-00.95|+01.85|-01.88"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|2|0|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-4|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "sinkbasin"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-3.81599188, -3.81599188, -7.5039468, -7.5039468, 7.40410852, 7.40410852]], "coordinateReceptacleObjectId": ["SinkBasin", [-0.25512844, -0.25512844, -6.85248328, -6.85248328, 3.0634928, 3.0634928]], "forceVisible": true, "objectId": "Cup|-00.95|+01.85|-01.88", "receptacleObjectId": "Sink|+00.16|+00.82|-01.80|SinkBasin"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.87|+02.01|-01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 23, 226, 123], "mask": [[6707, 112], [7007, 112], [7307, 112], [7607, 112], [7907, 112], [8207, 112], [8507, 112], [8807, 112], [9107, 112], [9407, 112], [9707, 113], [10007, 113], [10307, 113], [10607, 113], [10906, 114], [11206, 114], [11506, 114], [11806, 114], [12106, 114], [12406, 114], [12706, 114], [13006, 114], [13306, 115], [13606, 115], [13906, 115], [14206, 115], [14506, 115], [14806, 115], [15106, 115], [15406, 115], [15706, 115], [16006, 115], [16306, 115], [16606, 115], [16905, 117], [17205, 117], [17505, 117], [17805, 117], [18105, 117], [18405, 117], [18705, 117], [19005, 117], [19305, 117], [19605, 117], [19905, 117], [20205, 117], [20505, 117], [20805, 118], [21105, 118], [21405, 118], [21705, 118], [22005, 118], [22305, 118], [22605, 118], [22904, 119], [23204, 119], [23504, 119], [23804, 119], [24104, 119], [24404, 120], [24704, 120], [25004, 120], [25304, 120], [25604, 120], [25904, 120], [26204, 120], [26504, 120], [26804, 120], [27104, 120], [27404, 120], [27704, 120], [28004, 121], [28304, 121], [28603, 122], [28903, 122], [29203, 122], [29503, 122], [29803, 122], [30103, 122], [30403, 122], [30703, 122], [31003, 122], [31303, 122], [31603, 123], [31903, 123], [32203, 123], [32503, 123], [32803, 123], [33103, 123], [33403, 123], [33703, 123], [34003, 123], [34303, 123], [34602, 124], [34902, 124], [35202, 124], [35502, 125], [35802, 125], [36103, 123], [36403, 122], [36704, 23], [36748, 14], [36783, 40]], "point": [164, 72]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.95|+01.85|-01.88"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [123, 95, 151, 123], "mask": [[28332, 9], [28629, 16], [28927, 20], [29226, 23], [29525, 25], [29824, 26], [30124, 26], [30424, 27], [30724, 27], [31023, 28], [31323, 28], [31623, 29], [31923, 29], [32223, 29], [32523, 29], [32823, 29], [33123, 29], [33423, 29], [33723, 29], [34023, 28], [34324, 27], [34624, 27], [34924, 26], [35225, 25], [35525, 25], [35826, 23], [36126, 23], [36427, 21], [36727, 21]], "point": [137, 108]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.87|+02.01|-01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [54, 1, 222, 123], "mask": [[59, 44], [358, 45], [658, 45], [958, 46], [1258, 46], [1558, 46], [1858, 46], [2158, 46], [2458, 47], [2758, 47], [3057, 48], [3357, 48], [3657, 49], [3957, 49], [4257, 49], [4557, 49], [4857, 49], [5157, 50], [5457, 50], [5756, 51], [6056, 51], [6356, 51], [6656, 52], [6956, 52], [7256, 52], [7556, 52], [7856, 52], [8156, 52], [8455, 53], [8755, 53], [9055, 53], [9109, 107], [9355, 53], [9409, 107], [9655, 53], [9709, 107], [9955, 161], [10255, 161], [10555, 161], [10855, 162], [11154, 163], [11454, 163], [11754, 163], [12054, 163], [12354, 163], [12654, 163], [12955, 162], [13255, 162], [13556, 51], [13608, 109], [13856, 51], [13908, 109], [14157, 50], [14208, 109], [14457, 50], [14508, 110], [14758, 49], [14808, 110], [15058, 49], [15108, 110], [15359, 48], [15408, 110], [15659, 48], [15708, 110], [15960, 158], [16261, 157], [16561, 157], [16862, 156], [17162, 156], [17463, 155], [17763, 155], [18064, 154], [18364, 155], [18665, 154], [18965, 154], [19266, 153], [19566, 153], [19867, 39], [19907, 112], [20168, 38], [20207, 112], [20468, 38], [20507, 112], [20769, 37], [20807, 112], [21069, 37], [21107, 112], [21370, 36], [21407, 112], [21670, 36], [21707, 112], [21971, 35], [22007, 112], [22271, 149], [22572, 148], [22872, 148], [23173, 147], [23474, 146], [23774, 146], [24075, 145], [24375, 145], [24676, 144], [24976, 144], [25277, 143], [25577, 143], [25878, 27], [25906, 114], [26178, 27], [26206, 115], [26479, 26], [26506, 115], [26779, 26], [26806, 115], [27080, 25], [27106, 115], [27381, 24], [27406, 115], [27681, 24], [27706, 115], [27982, 23], [28006, 115], [28282, 87], [28378, 43], [28583, 82], [28682, 39], [28883, 80], [28983, 38], [29184, 77], [29285, 36], [29484, 76], [29585, 36], [29785, 75], [29886, 35], [30085, 74], [30186, 36], [30386, 73], [30486, 36], [30687, 72], [30786, 36], [30987, 72], [31087, 35], [31288, 70], [31387, 35], [31588, 70], [31687, 35], [31889, 15], [31905, 53], [31987, 35], [32189, 15], [32205, 53], [32287, 35], [32490, 14], [32505, 53], [32587, 35], [32790, 14], [32805, 53], [32887, 35], [33091, 13], [33105, 53], [33187, 35], [33391, 13], [33405, 53], [33487, 35], [33692, 12], [33705, 53], [33787, 35], [33992, 12], [34005, 54], [34087, 36], [34293, 11], [34305, 54], [34386, 37], [34594, 65], [34686, 37], [34894, 66], [34986, 37], [35195, 65], [35286, 37], [35495, 65], [35585, 38], [35796, 65], [35885, 38], [36096, 65], [36184, 39], [36397, 65], [36484, 39], [36704, 58], [36783, 40]], "point": [138, 61]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.95|+01.85|-01.88", "placeStationary": true, "receptacleObjectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 247], [20400, 247], [20700, 247], [21000, 247], [21300, 246], [21600, 246], [21900, 246], [22200, 246], [22500, 246], [22800, 246], [23100, 245], [23400, 245], [23700, 245], [24000, 245], [24300, 245], [24600, 244], [24900, 244], [25200, 244], [25500, 244], [25800, 244], [26100, 244], [26400, 243], [26700, 243], [27000, 243], [27300, 243], [27600, 243], [27900, 242], [28200, 242], [28500, 242], [28800, 242], [29100, 242], [29400, 242], [29700, 241], [30000, 241], [30300, 241], [30600, 241], [30900, 241], [31200, 240], [31500, 240], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 146], [16667, 82], [16800, 143], [16970, 79], [17100, 142], [17271, 78], [17400, 142], [17571, 78], [17700, 141], [17872, 77], [18000, 141], [18172, 76], [18300, 141], [18472, 76], [18600, 141], [18772, 76], [18900, 141], [19072, 76], [19200, 140], [19373, 75], [19500, 140], [19673, 75], [19800, 140], [19973, 74], [20100, 140], [20273, 74], [20400, 140], [20573, 74], [20700, 140], [20872, 75], [21000, 140], [21172, 75], [21300, 140], [21472, 74], [21600, 141], [21772, 74], [21900, 141], [22072, 74], [22200, 141], [22371, 75], [22500, 142], [22671, 75], [22800, 142], [22971, 75], [23100, 142], [23271, 74], [23400, 143], [23570, 75], [23700, 143], [23870, 75], [24000, 144], [24169, 76], [24300, 144], [24469, 76], [24600, 145], [24768, 76], [24900, 145], [25068, 76], [25200, 145], [25367, 77], [25500, 146], [25667, 77], [25800, 146], [25967, 77], [26100, 146], [26267, 77], [26400, 146], [26566, 77], [26700, 146], [26866, 77], [27000, 146], [27166, 77], [27300, 146], [27466, 77], [27600, 146], [27766, 77], [27900, 146], [28066, 76], [28200, 146], [28366, 76], [28500, 146], [28666, 76], [28800, 146], [28967, 75], [29100, 145], [29267, 75], [29400, 145], [29567, 75], [29700, 145], [29867, 74], [30000, 145], [30167, 74], [30300, 145], [30467, 74], [30600, 145], [30767, 74], [30900, 147], [31066, 75], [31200, 149], [31363, 77], [31500, 240], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.95|+01.85|-01.88"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [140, 56, 172, 105], "mask": [[16646, 21], [16943, 27], [17242, 29], [17542, 29], [17841, 31], [18141, 31], [18441, 31], [18741, 31], [19041, 31], [19340, 33], [19640, 33], [19940, 33], [20240, 33], [20540, 33], [20840, 32], [21140, 32], [21440, 32], [21741, 31], [22041, 31], [22341, 30], [22642, 29], [22942, 29], [23242, 29], [23543, 27], [23843, 27], [24144, 25], [24444, 25], [24745, 23], [25045, 23], [25345, 22], [25646, 21], [25946, 21], [26246, 21], [26546, 20], [26846, 20], [27146, 20], [27446, 20], [27746, 20], [28046, 20], [28346, 20], [28646, 20], [28946, 21], [29245, 22], [29545, 22], [29845, 22], [30145, 22], [30445, 22], [30745, 22], [31047, 19], [31349, 14]], "point": [156, 79]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 250], [16200, 250], [16500, 249], [16800, 249], [17100, 249], [17400, 249], [17700, 249], [18000, 248], [18300, 248], [18600, 248], [18900, 248], [19200, 248], [19500, 248], [19800, 247], [20100, 247], [20400, 247], [20700, 247], [21000, 247], [21300, 246], [21600, 246], [21900, 246], [22200, 246], [22500, 246], [22800, 246], [23100, 245], [23400, 245], [23700, 245], [24000, 245], [24300, 245], [24600, 244], [24900, 244], [25200, 244], [25500, 244], [25800, 244], [26100, 244], [26400, 243], [26700, 243], [27000, 243], [27300, 243], [27600, 243], [27900, 242], [28200, 242], [28500, 242], [28800, 242], [29100, 242], [29400, 242], [29700, 241], [30000, 241], [30300, 241], [30600, 241], [30900, 241], [31200, 240], [31500, 240], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.95|+01.85|-01.88", "placeStationary": true, "receptacleObjectId": "Sink|+00.16|+00.82|-01.80|SinkBasin"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [74, 140, 132, 185], "mask": [[41791, 37], [42089, 41], [42387, 44], [42686, 45], [42985, 47], [43285, 47], [43584, 49], [43884, 49], [44183, 50], [44483, 50], [44783, 50], [45082, 51], [45382, 51], [45682, 51], [45982, 51], [46281, 52], [46581, 51], [46881, 51], [47181, 51], [47480, 49], [47780, 47], [48080, 44], [48380, 42], [48679, 42], [48979, 40], [49279, 39], [49579, 38], [49878, 38], [50178, 37], [50478, 36], [50777, 36], [51077, 35], [51377, 35], [51677, 34], [51976, 35], [52276, 34], [52576, 34], [52876, 33], [53175, 34], [53475, 34], [53775, 34], [54075, 34], [54374, 35], [54674, 34], [54974, 34], [55274, 34]], "point": [103, 161]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan8", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 0.5, "y": 0.9009992, "z": 0.5}, "object_poses": [{"objectName": "Pan_c0048524", "position": {"x": 1.09300041, "y": 0.114818692, "z": -1.129}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_c0048524", "position": {"x": 1.40199959, "y": 1.65567374, "z": -0.5001875}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_abf5c390", "position": {"x": 0.5638487, "y": 1.654979, "z": -1.91353726}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_73e168e3", "position": {"x": -0.3728, "y": 0.7759144, "z": -1.56547654}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_1cf505ed", "position": {"x": 1.459939, "y": 0.256335258, "z": 1.05101991}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_7cca838f", "position": {"x": 1.41487837, "y": 1.16224217, "z": -1.34564734}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_7cca838f", "position": {"x": 1.34322, "y": 0.9113421, "z": -0.9908546}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b7024f32", "position": {"x": 1.03566837, "y": 0.7745936, "z": 0.539087236}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b7024f32", "position": {"x": 1.015, "y": 0.7744811, "z": -1.17864156}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": -2.07940626, "y": 0.911048532, "z": 0.118500233}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": -2.32376337, "y": 0.911048532, "z": 0.118500233}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_bfcb0336", "position": {"x": -0.95399797, "y": 1.85102713, "z": -1.8759867}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": 1.32553172, "y": 1.36813617, "z": 2.22600031}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Knife_af290bda", "position": {"x": -1.88603711, "y": 0.940200269, "z": -1.44825029}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_af290bda", "position": {"x": -2.32376337, "y": 0.940200269, "z": 0.3772499}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": -0.313962221, "y": 1.65547836, "z": -1.76446271}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_441a8bda", "position": {"x": 0.14961265, "y": 0.9596676, "z": -1.65757918}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_a0ece05a", "position": {"x": 1.260095, "y": 0.91185087, "z": -1.33170938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": -0.7183272, "y": 0.106764436, "z": -1.63276732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 1.52740741, "y": 1.36246753, "z": 2.35099983}, "rotation": {"x": 0.0, "y": 0.000173868219, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 0.183694571, "y": 0.9145499, "z": -1.75846982}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_b66a32b5", "position": {"x": 1.35616374, "y": 1.7549094, "z": 1.71318173}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_441a8bda", "position": {"x": -1.95722759, "y": 0.9506176, "z": 0.204750121}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_b308514b", "position": {"x": 1.03170657, "y": 0.115641475, "z": -0.699439943}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_af290bda", "position": {"x": -2.07940626, "y": 0.940200269, "z": 0.3772499}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": -0.603324533, "y": 1.65624368, "z": -1.839}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": 0.936764359, "y": 0.7755779, "z": 0.323113024}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Ladle_238538e2", "position": {"x": 0.8524788, "y": 0.95566237, "z": -1.53404093}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_1cf505ed", "position": {"x": 1.332814, "y": 1.859023, "z": 2.55745077}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_0f08525d", "position": {"x": 0.664081335, "y": 0.9804653, "z": -1.78968167}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_e75d4607", "position": {"x": -1.431, "y": 0.949799955, "z": -1.5257}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_73e168e3", "position": {"x": -1.8211565, "y": 0.7759144, "z": -1.50032341}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": 0.35893178, "y": 1.65057492, "z": -1.80173135}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_bfcb0336", "position": {"x": -1.08240128, "y": 1.85102713, "z": -1.8759867}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_e08d0eeb", "position": {"x": 0.2859404, "y": 0.9488791, "z": -1.55668843}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": -1.835049, "y": 0.925499856, "z": 0.291}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_a0ece05a", "position": {"x": 1.150191, "y": 0.118411779, "z": 0.430900037}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_abf5c390", "position": {"x": -1.73225582, "y": 0.909904063, "z": -1.53450024}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_c0048524", "position": {"x": 1.34259534, "y": 1.65567374, "z": -1.42190623}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": -1.73225582, "y": 0.91040343, "z": -1.87949979}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_316d55f0", "position": {"x": 0.9673953, "y": 0.9, "z": 0.249277115}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_7cca838f", "position": {"x": 0.9037963, "y": 0.775071442, "z": 0.43110013}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": -0.477255583, "y": 0.906299949, "z": -1.96010935}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b7024f32", "position": {"x": 1.494119, "y": 1.16176438, "z": 0.3709073}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_9b638b9f", "position": {"x": 1.37525809, "y": 1.24034131, "z": -0.659025431}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_b66a32b5", "position": {"x": 1.34153056, "y": 0.2492218, "z": 1.13796043}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_6f7c42a7", "position": {"x": 0.9370405, "y": 0.7726913, "z": -0.7328684}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 1.47693741, "y": 1.36246741, "z": 1.97599983}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 2688773089, "scene_num": 8}, "task_id": "trial_T20190908_034343_705247", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AM2KK02JXXW48_34S9DKFK766P1UG4XJJUMVNMYFZNYA", "high_descs": ["Turn around, move to the stove on the left. ", "Open up the left cabinet above the stove, pick up the left glass in that cabinet. ", "Turn around, bring the glass to the microwave on the right. ", "Heat the glass in the microwave. ", "Bring the heated glass back to the sink on the right.", "Put the heated glass in the sink. "], "task_desc": "Put a heated glass in the sink. ", "votes": [1, 1]}, {"assignment_id": "A1P0XSCJ9XAV74_3R0T90IZ1VT6OZYDENOAZKM6UEFCG1", "high_descs": ["Turn around and walk to the counter and turn left. Walk to the stove and look up at the cabinet.", "Take a glass from the cabinet.", "Turn around and take a step. Turn to your right and walk to the counter. Raise the glass up to the microwave.", "Put the glass inside the microwave and shut the door. After it's done take the glass back out.", "Turn around and take a couple steps to turn left to walk towards the sink.", "Put the glass in the sink."], "task_desc": "To take the glass from the microwave to put it in the sink.", "votes": [1, 1]}, {"assignment_id": "A2ALWT2BUSXD83_3VAR3R6G1SIS49PYLULD27ZPV9O8OH", "high_descs": ["Turn around and walk to the oven.", "Open the cupboard above the oven and remove a glass.", "Turn around and walk towards the microwave.", "Place the glass in the microwave, use the microwave to warm the glass, and remove the glass from the microwave.", "Turn around and walk to the sink.", "Place the glass in the sink."], "task_desc": "Place a warmed glass in the sink.", "votes": [1, 1]}]}}