{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000199.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000200.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000202.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000203.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000204.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 29}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|4|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-6.64225052, -6.64225052, 4.379692, 4.379692, 5.45529796, 5.45529796]], "coordinateReceptacleObjectId": ["CounterTop", [-7.236, -7.236, 4.728, 4.728, 5.434, 5.434]], "forceVisible": true, "objectId": "Egg|-01.66|+01.36|+01.09"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|3|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|1|4|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "sidetable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-6.64225052, -6.64225052, 4.379692, 4.379692, 5.45529796, 5.45529796]], "coordinateReceptacleObjectId": ["SideTable", [4.088, 4.088, 3.4844, 3.4844, 4.9732, 4.9732]], "forceVisible": true, "objectId": "Egg|-01.66|+01.36|+01.09", "receptacleObjectId": "SideTable|+01.02|+01.24|+00.87"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.66|+01.36|+01.09"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [170, 83, 192, 110], "mask": [[24779, 7], [25077, 11], [25376, 13], [25675, 15], [25974, 16], [26274, 17], [26573, 19], [26872, 20], [27172, 20], [27472, 21], [27771, 22], [28071, 22], [28371, 22], [28671, 22], [28971, 22], [29270, 23], [29570, 23], [29871, 22], [30171, 22], [30471, 21], [30771, 21], [31072, 19], [31372, 19], [31673, 17], [31974, 15], [32275, 13], [32576, 11], [32878, 7]], "point": [181, 95]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.66|+01.36|+01.09", "placeStationary": true, "receptacleObjectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 112], [32528, 87], [32700, 109], [32830, 85], [33000, 106], [33132, 83], [33300, 105], [33433, 82], [33600, 104], [33734, 81], [33900, 103], [34034, 81], [34200, 103], [34334, 80], [34500, 103], [34634, 80], [34800, 103], [34934, 80], [35100, 103], [35234, 80], [35400, 103], [35534, 80], [35700, 103], [35834, 80], [36000, 104], [36134, 80], [36300, 104], [36433, 80], [36600, 105], [36732, 81], [36900, 106], [37032, 81], [37200, 106], [37332, 81], [37500, 107], [37631, 82], [37800, 107], [37931, 82], [38100, 107], [38231, 82], [38400, 108], [38531, 82], [38700, 108], [38830, 82], [39000, 109], [39130, 82], [39300, 109], [39430, 82], [39600, 109], [39730, 82], [39900, 110], [40030, 82], [40200, 110], [40330, 82], [40500, 110], [40630, 82], [40800, 110], [40930, 82], [41100, 110], [41230, 81], [41400, 110], [41530, 81], [41700, 111], [41830, 81], [42000, 110], [42130, 81], [42300, 110], [42431, 80], [42600, 110], [42731, 80], [42900, 110], [43031, 80], [43200, 110], [43331, 79], [43500, 110], [43630, 80], [43800, 111], [43930, 80], [44100, 111], [44230, 80], [44400, 111], [44529, 81], [44700, 112], [44829, 81], [45000, 117], [45122, 88], [45300, 118], [45422, 88], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54174, 31], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 23], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 112], [32528, 87], [32700, 109], [32830, 85], [33000, 106], [33132, 83], [33300, 105], [33433, 82], [33600, 104], [33734, 81], [33900, 103], [34034, 81], [34200, 103], [34334, 80], [34500, 103], [34634, 80], [34800, 103], [34934, 80], [35100, 103], [35234, 80], [35400, 103], [35534, 80], [35700, 103], [35834, 80], [36000, 104], [36134, 80], [36300, 104], [36433, 80], [36600, 105], [36732, 81], [36900, 106], [37032, 81], [37200, 106], [37332, 81], [37500, 107], [37631, 82], [37800, 90], [37894, 13], [37931, 82], [38100, 88], [38196, 11], [38231, 82], [38400, 87], [38497, 11], [38531, 82], [38700, 86], [38798, 10], [38830, 82], [39000, 85], [39099, 10], [39130, 82], [39300, 85], [39400, 9], [39430, 82], [39600, 84], [39700, 9], [39730, 82], [39900, 84], [40001, 9], [40030, 82], [40200, 84], [40301, 9], [40330, 82], [40500, 83], [40601, 9], [40630, 82], [40800, 83], [40902, 8], [40930, 82], [41100, 83], [41202, 8], [41230, 81], [41400, 83], [41502, 8], [41530, 81], [41700, 83], [41802, 9], [41830, 81], [42000, 84], [42102, 8], [42130, 81], [42300, 84], [42402, 8], [42431, 80], [42600, 84], [42701, 9], [42731, 80], [42900, 85], [43001, 9], [43031, 80], [43200, 85], [43300, 10], [43331, 79], [43500, 86], [43600, 10], [43630, 80], [43800, 87], [43899, 12], [43930, 80], [44100, 88], [44197, 14], [44230, 80], [44400, 91], [44495, 16], [44529, 81], [44700, 112], [44829, 81], [45000, 117], [45122, 88], [45300, 118], [45422, 88], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54174, 31], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 23], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.66|+01.36|+01.09"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [83, 127, 101, 149], "mask": [[37890, 4], [38188, 8], [38487, 10], [38786, 12], [39085, 14], [39385, 15], [39684, 16], [39984, 17], [40284, 17], [40583, 18], [40883, 19], [41183, 19], [41483, 19], [41783, 19], [42084, 18], [42384, 18], [42684, 17], [42985, 16], [43285, 15], [43586, 14], [43887, 12], [44188, 9], [44491, 4]], "point": [92, 137]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 112], [32528, 87], [32700, 109], [32830, 85], [33000, 106], [33132, 83], [33300, 105], [33433, 82], [33600, 104], [33734, 81], [33900, 103], [34034, 81], [34200, 103], [34334, 80], [34500, 103], [34634, 80], [34800, 103], [34934, 80], [35100, 103], [35234, 80], [35400, 103], [35534, 80], [35700, 103], [35834, 80], [36000, 104], [36134, 80], [36300, 104], [36433, 80], [36600, 105], [36732, 81], [36900, 106], [37032, 81], [37200, 106], [37332, 81], [37500, 107], [37631, 82], [37800, 107], [37931, 82], [38100, 107], [38231, 82], [38400, 108], [38531, 82], [38700, 108], [38830, 82], [39000, 109], [39130, 82], [39300, 109], [39430, 82], [39600, 109], [39730, 82], [39900, 110], [40030, 82], [40200, 110], [40330, 82], [40500, 110], [40630, 82], [40800, 110], [40930, 82], [41100, 110], [41230, 81], [41400, 110], [41530, 81], [41700, 111], [41830, 81], [42000, 110], [42130, 81], [42300, 110], [42431, 80], [42600, 110], [42731, 80], [42900, 110], [43031, 80], [43200, 110], [43331, 79], [43500, 110], [43630, 80], [43800, 111], [43930, 80], [44100, 111], [44230, 80], [44400, 111], [44529, 81], [44700, 112], [44829, 81], [45000, 117], [45122, 88], [45300, 118], [45422, 88], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54174, 31], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 23], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.66|+01.36|+01.09", "placeStationary": true, "receptacleObjectId": "SideTable|+01.02|+01.24|+00.87"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [40, 72, 299, 188], "mask": [[21386, 15], [21687, 14], [21987, 14], [26802, 1], [27102, 1], [27401, 2], [27701, 3], [28001, 3], [28183, 2], [28301, 3], [28483, 2], [28601, 3], [28783, 3], [28901, 3], [29084, 3], [29202, 2], [29385, 2], [29503, 2], [29685, 3], [29804, 1], [29986, 3], [30104, 1], [30287, 2], [30587, 3], [30672, 1], [30888, 3], [30971, 2], [31189, 2], [31271, 2], [31489, 3], [31571, 2], [31790, 3], [31870, 4], [32091, 2], [32170, 4], [32391, 3], [32470, 3], [32692, 3], [32769, 4], [32993, 2], [33069, 5], [33293, 3], [33368, 6], [33594, 3], [33668, 6], [33895, 2], [33968, 6], [34195, 3], [34267, 7], [34317, 20], [34431, 20], [34496, 3], [34567, 7], [34613, 29], [34727, 29], [34796, 3], [34867, 6], [34909, 36], [35024, 36], [35097, 3], [35166, 7], [35206, 42], [35321, 42], [35398, 2], [35466, 7], [35503, 47], [35619, 47], [35698, 2], [35766, 6], [35801, 51], [35918, 50], [35999, 1], [36065, 7], [36100, 54], [36216, 54], [36365, 7], [36398, 57], [36515, 57], [36664, 7], [36696, 61], [36814, 61], [36964, 7], [36994, 64], [37112, 65], [37264, 7], [37293, 66], [37412, 66], [37563, 7], [37592, 68], [37711, 68], [37863, 7], [37891, 70], [38011, 70], [38163, 7], [38190, 72], [38310, 72], [38462, 7], [38490, 73], [38610, 73], [38762, 7], [38790, 74], [38909, 76], [39061, 8], [39090, 74], [39209, 77], [39361, 7], [39391, 74], [39508, 79], [39661, 7], [39691, 74], [39808, 80], [39960, 8], [39991, 74], [40108, 81], [40260, 7], [40291, 75], [40408, 81], [40560, 7], [40590, 32], [40628, 38], [40708, 38], [40752, 38], [40859, 8], [40890, 30], [40930, 36], [41008, 36], [41054, 37], [41159, 7], [41188, 31], [41231, 36], [41308, 36], [41356, 36], [41459, 7], [41486, 32], [41532, 35], [41608, 36], [41657, 36], [41758, 8], [41784, 34], [41832, 35], [41908, 36], [41958, 35], [42058, 7], [42084, 34], [42132, 35], [42209, 35], [42258, 36], [42357, 8], [42383, 35], [42431, 36], [42509, 36], [42558, 36], [42657, 8], [42683, 36], [42730, 37], [42810, 37], [42858, 36], [42957, 5], [42983, 38], [43028, 39], [43110, 39], [43156, 39], [43256, 2], [43283, 83], [43411, 84], [43556, 1], [43582, 84], [43711, 84], [43882, 84], [44011, 85], [44182, 84], [44312, 84], [44482, 84], [44612, 84], [44782, 83], [44913, 83], [45083, 82], [45214, 82], [45383, 81], [45515, 81], [45684, 79], [45816, 80], [45984, 79], [46117, 79], [46285, 77], [46418, 77], [46585, 76], [46719, 76], [46886, 75], [47020, 75], [47186, 74], [47321, 74], [47487, 72], [47622, 72], [47788, 70], [47924, 69], [48089, 67], [48225, 68], [48350, 1], [48390, 65], [48527, 65], [48650, 1], [48691, 63], [48829, 62], [48949, 2], [48993, 59], [49130, 60], [49249, 3], [49294, 57], [49432, 57], [49549, 3], [49595, 54], [49734, 54], [49848, 5], [49897, 50], [50037, 49], [50148, 6], [50200, 44], [50340, 44], [50447, 7], [50502, 40], [50642, 40], [50747, 8], [50805, 34], [50945, 35], [51047, 9], [51107, 29], [51248, 30], [51346, 9], [51415, 13], [51557, 13], [51646, 12], [51679, 20], [51946, 13], [51971, 3], [51979, 70], [52245, 17], [52270, 5], [52277, 123], [52545, 206], [52844, 256], [53144, 256], [53444, 256], [53743, 257], [54043, 257], [54343, 257], [54642, 258], [54942, 258], [55242, 258], [55541, 259], [55841, 259], [56140, 260]], "point": [164, 131]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan3", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.0, "y": 1.12401652, "z": -0.25}, "object_poses": [{"objectName": "Pan_9d168802", "position": {"x": -1.9200685, "y": 1.32260954, "z": 0.445294976}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": 0.9710001, "y": 1.56427288, "z": 1.66288}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -0.125, "y": 1.3324, "z": -2.973}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.16594541, "y": 1.32354856, "z": -2.870881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": 0.8613707, "y": 1.20892942, "z": 1.2225256}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -2.179573, "y": 1.33799994, "z": -2.981163}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.60347974, "y": 0.4350211, "z": -1.348922}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -2.04982138, "y": 1.3294332, "z": 0.770108938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": 1.11572361, "y": 1.30780816, "z": 0.861990035}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.82652426, "y": 1.35868657, "z": -0.9118071}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.82652426, "y": 1.3628422, "z": -1.25536227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.634, "y": 0.280376852, "z": 2.27545214}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.60347974, "y": 0.4227631, "z": -0.430022568}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 0.6347654, "y": 1.35692108, "z": -3.232851}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 0.5634017, "y": 1.35692108, "z": -3.12197924}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -2.179573, "y": 1.32130814, "z": -1.94141138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": 1.02731991, "y": 1.2028, "z": 1.23849058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": 0.349309921, "y": 1.31800008, "z": -3.232851}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": -1.53081059, "y": 1.39739525, "z": -2.20134926}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.53080976, "y": 1.39912164, "z": 1.74455106}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": 0.277946, "y": 1.39912164, "z": -3.01110744}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.5634017, "y": 1.31800008, "z": -2.78936434}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.970999956, "y": 1.51951933, "z": 1.848439}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": -1.79031563, "y": 1.39739525, "z": 2.069365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.6309, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.53081059, "y": 1.35270047, "z": -2.981163}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -1.88644969, "y": 0.3421222, "z": 1.11066759}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": -1.6980927, "y": 1.09080327, "z": -0.496626765}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -2.00441241, "y": 1.33681107, "z": -1.18665123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": 0.349309921, "y": 1.32354856, "z": -3.01110744}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": -1.38003659, "y": 1.32039762, "z": -3.203495}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": 0.747982, "y": 0.346012831, "z": -2.26769447}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.66056263, "y": 1.36382449, "z": 1.094923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_9d168802", "position": {"x": 1.05303526, "y": 0.344581038, "z": -1.85446763}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": -1.665155, "y": 0.338814139, "z": -1.9633193}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": 0.492037773, "y": 1.32392883, "z": -3.34372234}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": 1.1555934, "y": 1.39912164, "z": -2.05370474}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.16594541, "y": 1.33799994, "z": -3.42523718}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_ddd73f57", "position": {"x": -1.0945816, "y": 1.42728543, "z": -3.42523718}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 0.77839607, "y": 1.241721, "z": 1.23849058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -1.727323, "y": 0.344428062, "z": -2.26237869}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": 0.824245334, "y": 0.339971542, "z": -1.75116086}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": 0.9001643, "y": 0.459864318, "z": 1.09071338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -1.38003659, "y": 1.32412946, "z": -2.76001024}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": 1.01704478, "y": 1.80610383, "z": 2.23050737}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.420673847, "y": 1.3180002, "z": -2.78936434}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}], "object_toggles": [], "random_seed": 668193617, "scene_num": 3}, "task_id": "trial_T20190906_204416_817121", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AD0NVUGLDYDYN_37UEWGM5HWPDQMXTE1T2HSHMRL3R1F", "high_descs": ["Walk to the egg on the left", "Grab the egg on the counter", "Turn around, walk to the microwave", "Open the microwave, put the egg in, close the microwave, turn on the microwave, open the microwave, take the egg out, close the microwave", "Walk to the left a little ", "Put the egg in between the microwave and the refrigerator"], "task_desc": "Cook the egg in the microwave, put the cooked egg on the table", "votes": [1, 1]}, {"assignment_id": "AM2KK02JXXW48_3IJXV6UZ100OQSTRM8E89RY88NUIRE", "high_descs": ["Turn to face the counter on the left.", "Pick up the brown egg from the counter. ", "Bring the egg to the microwave.", "Heat the egg in the microwave. ", "Take the egg out of the microwave.", "Put the egg on the counter to the left of the microwave. "], "task_desc": "Put a heated egg on the counter left of the microwave. ", "votes": [1, 1]}, {"assignment_id": "AH00EJ6PK1H3A_3PW9OPU9PT1WM2G8ZBE1GK9EFTW21K", "high_descs": ["Walk forwards a pace and then turn to the counter on the left.", "Pick up the egg on the counter.", "Turn around and walk the egg over to the microwave.", "Open the microwave and put the egg in it next to the cup, close it, run it, and then remove the egg from the microwave.", "Take a step to the left and turn towards the microwave again.", "Place the egg next to the microwave."], "task_desc": "Put a microwaved egg next to the microwave.", "votes": [1, 1]}]}}