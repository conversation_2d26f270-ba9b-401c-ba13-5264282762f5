
(define (problem plan_trial_T20190907_174318_168269)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__plus_02_dot_11_bar__plus_00_dot_94_bar__minus_00_dot_17 - object
        Candle_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_66 - object
        Candle_bar__minus_01_dot_94_bar__plus_00_dot_45_bar__plus_00_dot_27 - object
        Chair_bar__plus_01_dot_46_bar_00_dot_00_bar__plus_00_dot_20 - object
        Chair_bar__plus_01_dot_64_bar_00_dot_00_bar__minus_00_dot_85 - object
        Chair_bar__plus_02_dot_22_bar_00_dot_00_bar__plus_00_dot_15 - object
        Chair_bar__plus_02_dot_29_bar_00_dot_00_bar__minus_00_dot_78 - object
        CreditCard_bar__minus_01_dot_73_bar__plus_00_dot_71_bar__plus_02_dot_88 - object
        CreditCard_bar__minus_02_dot_27_bar__plus_00_dot_45_bar__plus_00_dot_27 - object
        CreditCard_bar__minus_03_dot_17_bar__plus_00_dot_58_bar__plus_00_dot_47 - object
        FloorLamp_bar__minus_00_dot_33_bar__plus_00_dot_00_bar__plus_02_dot_73 - object
        HousePlant_bar__minus_03_dot_48_bar__plus_00_dot_71_bar__minus_01_dot_59 - object
        KeyChain_bar__minus_01_dot_86_bar__plus_00_dot_45_bar__plus_00_dot_02 - object
        KeyChain_bar__minus_03_dot_08_bar__plus_00_dot_53_bar__minus_00_dot_30 - object
        KeyChain_bar__minus_03_dot_52_bar__plus_00_dot_53_bar__minus_00_dot_81 - object
        Laptop_bar__minus_03_dot_34_bar__plus_00_dot_58_bar__minus_00_dot_81 - object
        LightSwitch_bar__minus_03_dot_90_bar__plus_01_dot_28_bar__plus_01_dot_68 - object
        Painting_bar__plus_04_dot_14_bar__plus_01_dot_59_bar__minus_00_dot_30 - object
        Painting_bar__minus_00_dot_06_bar__plus_01_dot_60_bar__plus_01_dot_73 - object
        Pillow_bar__minus_01_dot_92_bar__plus_00_dot_36_bar__minus_01_dot_50 - object
        Plate_bar__minus_02_dot_10_bar__plus_00_dot_45_bar__minus_00_dot_11 - object
        RemoteControl_bar__minus_01_dot_75_bar__plus_00_dot_23_bar__plus_02_dot_83 - object
        Statue_bar__plus_01_dot_44_bar__plus_00_dot_79_bar__minus_00_dot_29 - object
        Statue_bar__plus_02_dot_11_bar__plus_00_dot_79_bar__minus_00_dot_66 - object
        Television_bar__minus_01_dot_86_bar__plus_01_dot_20_bar__plus_02_dot_69 - object
        Vase_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_05 - object
        Vase_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_42 - object
        Vase_bar__minus_01_dot_48_bar__plus_00_dot_72_bar__plus_02_dot_83 - object
        WateringCan_bar__minus_03_dot_25_bar_00_dot_00_bar__minus_01_dot_86 - object
        Window_bar__plus_01_dot_48_bar__plus_01_dot_51_bar__minus_01_dot_93 - object
        Window_bar__plus_03_dot_48_bar__plus_01_dot_51_bar__minus_01_dot_93 - object
        Window_bar__minus_02_dot_00_bar__plus_01_dot_51_bar__minus_01_dot_93 - object
        ArmChair_bar__minus_00_dot_43_bar__plus_00_dot_00_bar__plus_01_dot_06 - receptacle
        ArmChair_bar__minus_00_dot_44_bar__plus_00_dot_00_bar__plus_01_dot_92 - receptacle
        ArmChair_bar__minus_02_dot_01_bar__plus_00_dot_00_bar__minus_01_dot_58 - receptacle
        CoffeeTable_bar__minus_02_dot_02_bar__minus_00_dot_01_bar__plus_00_dot_02 - receptacle
        DiningTable_bar__plus_01_dot_88_bar_00_dot_00_bar__minus_00_dot_29 - receptacle
        DiningTable_bar__minus_01_dot_88_bar__plus_00_dot_27_bar__plus_02_dot_79 - receptacle
        GarbageCan_bar__plus_00_dot_29_bar__plus_00_dot_00_bar__plus_01_dot_26 - receptacle
        Sofa_bar__minus_03_dot_44_bar__minus_00_dot_01_bar__minus_00_dot_04 - receptacle
        TVStand_bar__minus_01_dot_86_bar__minus_00_dot_01_bar__plus_02_dot_79 - receptacle
        loc_bar__minus_9_bar_7_bar_0_bar_60 - location
        loc_bar__minus_13_bar_7_bar_3_bar_30 - location
        loc_bar_9_bar_3_bar_2_bar_60 - location
        loc_bar_8_bar__minus_5_bar_0_bar_60 - location
        loc_bar_6_bar__minus_5_bar_2_bar_15 - location
        loc_bar__minus_6_bar_5_bar_1_bar_60 - location
        loc_bar__minus_5_bar_7_bar_1_bar_0 - location
        loc_bar__minus_11_bar_0_bar_3_bar_60 - location
        loc_bar__minus_4_bar_0_bar_3_bar_60 - location
        loc_bar_14_bar__minus_5_bar_2_bar_15 - location
        loc_bar__minus_5_bar_8_bar_1_bar_60 - location
        loc_bar__minus_11_bar__minus_6_bar_1_bar_15 - location
        loc_bar__minus_11_bar__minus_3_bar_3_bar_60 - location
        loc_bar__minus_11_bar__minus_6_bar_3_bar_45 - location
        loc_bar_7_bar__minus_5_bar_0_bar_60 - location
        loc_bar__minus_11_bar__minus_1_bar_3_bar_60 - location
        loc_bar_9_bar__minus_5_bar_0_bar_60 - location
        loc_bar_3_bar_4_bar_3_bar_60 - location
        loc_bar_14_bar__minus_1_bar_1_bar_0 - location
        loc_bar__minus_8_bar_7_bar_0_bar_60 - location
        loc_bar_6_bar_3_bar_2_bar_60 - location
        loc_bar__minus_11_bar__minus_6_bar_3_bar_60 - location
        loc_bar__minus_6_bar_8_bar_1_bar_60 - location
        loc_bar__minus_3_bar__minus_5_bar_3_bar_60 - location
        loc_bar__minus_5_bar_1_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType Sofa_bar__minus_03_dot_44_bar__minus_00_dot_01_bar__minus_00_dot_04 SofaType)
        (receptacleType DiningTable_bar__plus_01_dot_88_bar_00_dot_00_bar__minus_00_dot_29 DiningTableType)
        (receptacleType CoffeeTable_bar__minus_02_dot_02_bar__minus_00_dot_01_bar__plus_00_dot_02 CoffeeTableType)
        (receptacleType ArmChair_bar__minus_02_dot_01_bar__plus_00_dot_00_bar__minus_01_dot_58 ArmChairType)
        (receptacleType ArmChair_bar__minus_00_dot_44_bar__plus_00_dot_00_bar__plus_01_dot_92 ArmChairType)
        (receptacleType DiningTable_bar__minus_01_dot_88_bar__plus_00_dot_27_bar__plus_02_dot_79 DiningTableType)
        (receptacleType ArmChair_bar__minus_00_dot_43_bar__plus_00_dot_00_bar__plus_01_dot_06 ArmChairType)
        (receptacleType GarbageCan_bar__plus_00_dot_29_bar__plus_00_dot_00_bar__plus_01_dot_26 GarbageCanType)
        (receptacleType TVStand_bar__minus_01_dot_86_bar__minus_00_dot_01_bar__plus_02_dot_79 TVStandType)
        (objectType Window_bar__plus_03_dot_48_bar__plus_01_dot_51_bar__minus_01_dot_93 WindowType)
        (objectType Vase_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_05 VaseType)
        (objectType FloorLamp_bar__minus_00_dot_33_bar__plus_00_dot_00_bar__plus_02_dot_73 FloorLampType)
        (objectType KeyChain_bar__minus_01_dot_86_bar__plus_00_dot_45_bar__plus_00_dot_02 KeyChainType)
        (objectType CreditCard_bar__minus_03_dot_17_bar__plus_00_dot_58_bar__plus_00_dot_47 CreditCardType)
        (objectType Chair_bar__plus_01_dot_46_bar_00_dot_00_bar__plus_00_dot_20 ChairType)
        (objectType CreditCard_bar__minus_02_dot_27_bar__plus_00_dot_45_bar__plus_00_dot_27 CreditCardType)
        (objectType LightSwitch_bar__minus_03_dot_90_bar__plus_01_dot_28_bar__plus_01_dot_68 LightSwitchType)
        (objectType Plate_bar__minus_02_dot_10_bar__plus_00_dot_45_bar__minus_00_dot_11 PlateType)
        (objectType Statue_bar__plus_01_dot_44_bar__plus_00_dot_79_bar__minus_00_dot_29 StatueType)
        (objectType Painting_bar__plus_04_dot_14_bar__plus_01_dot_59_bar__minus_00_dot_30 PaintingType)
        (objectType Painting_bar__minus_00_dot_06_bar__plus_01_dot_60_bar__plus_01_dot_73 PaintingType)
        (objectType KeyChain_bar__minus_03_dot_52_bar__plus_00_dot_53_bar__minus_00_dot_81 KeyChainType)
        (objectType Chair_bar__plus_02_dot_22_bar_00_dot_00_bar__plus_00_dot_15 ChairType)
        (objectType KeyChain_bar__minus_03_dot_08_bar__plus_00_dot_53_bar__minus_00_dot_30 KeyChainType)
        (objectType Vase_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_42 VaseType)
        (objectType Window_bar__minus_02_dot_00_bar__plus_01_dot_51_bar__minus_01_dot_93 WindowType)
        (objectType Vase_bar__minus_01_dot_48_bar__plus_00_dot_72_bar__plus_02_dot_83 VaseType)
        (objectType CreditCard_bar__minus_01_dot_73_bar__plus_00_dot_71_bar__plus_02_dot_88 CreditCardType)
        (objectType Statue_bar__plus_02_dot_11_bar__plus_00_dot_79_bar__minus_00_dot_66 StatueType)
        (objectType HousePlant_bar__minus_03_dot_48_bar__plus_00_dot_71_bar__minus_01_dot_59 HousePlantType)
        (objectType Window_bar__plus_01_dot_48_bar__plus_01_dot_51_bar__minus_01_dot_93 WindowType)
        (objectType Chair_bar__plus_01_dot_64_bar_00_dot_00_bar__minus_00_dot_85 ChairType)
        (objectType WateringCan_bar__minus_03_dot_25_bar_00_dot_00_bar__minus_01_dot_86 WateringCanType)
        (objectType Chair_bar__plus_02_dot_29_bar_00_dot_00_bar__minus_00_dot_78 ChairType)
        (objectType Candle_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_66 CandleType)
        (objectType Pillow_bar__minus_01_dot_92_bar__plus_00_dot_36_bar__minus_01_dot_50 PillowType)
        (objectType Television_bar__minus_01_dot_86_bar__plus_01_dot_20_bar__plus_02_dot_69 TelevisionType)
        (objectType Candle_bar__minus_01_dot_94_bar__plus_00_dot_45_bar__plus_00_dot_27 CandleType)
        (objectType RemoteControl_bar__minus_01_dot_75_bar__plus_00_dot_23_bar__plus_02_dot_83 RemoteControlType)
        (objectType Laptop_bar__minus_03_dot_34_bar__plus_00_dot_58_bar__minus_00_dot_81 LaptopType)
        (objectType Box_bar__plus_02_dot_11_bar__plus_00_dot_94_bar__minus_00_dot_17 BoxType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType CreditCardType)
        (canContain DiningTableType CandleType)
        (canContain DiningTableType VaseType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType StatueType)
        (canContain DiningTableType WateringCanType)
        (canContain CoffeeTableType CandleType)
        (canContain CoffeeTableType VaseType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType PlateType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain CoffeeTableType WateringCanType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain DiningTableType CandleType)
        (canContain DiningTableType VaseType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType StatueType)
        (canContain DiningTableType WateringCanType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (pickupable Vase_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_05)
        (pickupable KeyChain_bar__minus_01_dot_86_bar__plus_00_dot_45_bar__plus_00_dot_02)
        (pickupable CreditCard_bar__minus_03_dot_17_bar__plus_00_dot_58_bar__plus_00_dot_47)
        (pickupable CreditCard_bar__minus_02_dot_27_bar__plus_00_dot_45_bar__plus_00_dot_27)
        (pickupable Plate_bar__minus_02_dot_10_bar__plus_00_dot_45_bar__minus_00_dot_11)
        (pickupable Statue_bar__plus_01_dot_44_bar__plus_00_dot_79_bar__minus_00_dot_29)
        (pickupable KeyChain_bar__minus_03_dot_52_bar__plus_00_dot_53_bar__minus_00_dot_81)
        (pickupable KeyChain_bar__minus_03_dot_08_bar__plus_00_dot_53_bar__minus_00_dot_30)
        (pickupable Vase_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_42)
        (pickupable Vase_bar__minus_01_dot_48_bar__plus_00_dot_72_bar__plus_02_dot_83)
        (pickupable CreditCard_bar__minus_01_dot_73_bar__plus_00_dot_71_bar__plus_02_dot_88)
        (pickupable Statue_bar__plus_02_dot_11_bar__plus_00_dot_79_bar__minus_00_dot_66)
        (pickupable WateringCan_bar__minus_03_dot_25_bar_00_dot_00_bar__minus_01_dot_86)
        (pickupable Candle_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_66)
        (pickupable Pillow_bar__minus_01_dot_92_bar__plus_00_dot_36_bar__minus_01_dot_50)
        (pickupable Candle_bar__minus_01_dot_94_bar__plus_00_dot_45_bar__plus_00_dot_27)
        (pickupable RemoteControl_bar__minus_01_dot_75_bar__plus_00_dot_23_bar__plus_02_dot_83)
        (pickupable Laptop_bar__minus_03_dot_34_bar__plus_00_dot_58_bar__minus_00_dot_81)
        (pickupable Box_bar__plus_02_dot_11_bar__plus_00_dot_94_bar__minus_00_dot_17)
        (isReceptacleObject Plate_bar__minus_02_dot_10_bar__plus_00_dot_45_bar__minus_00_dot_11)
        (isReceptacleObject Box_bar__plus_02_dot_11_bar__plus_00_dot_94_bar__minus_00_dot_17)
        
        
        (atLocation agent1 loc_bar__minus_5_bar_1_bar_2_bar_30)
        
        (cleanable Plate_bar__minus_02_dot_10_bar__plus_00_dot_45_bar__minus_00_dot_11)
        
        (heatable Plate_bar__minus_02_dot_10_bar__plus_00_dot_45_bar__minus_00_dot_11)
        (coolable Plate_bar__minus_02_dot_10_bar__plus_00_dot_45_bar__minus_00_dot_11)
        
        
        (toggleable FloorLamp_bar__minus_00_dot_33_bar__plus_00_dot_00_bar__plus_02_dot_73)
        
        
        
        
        (inReceptacle Vase_bar__minus_01_dot_48_bar__plus_00_dot_72_bar__plus_02_dot_83 TVStand_bar__minus_01_dot_86_bar__minus_00_dot_01_bar__plus_02_dot_79)
        (inReceptacle CreditCard_bar__minus_01_dot_73_bar__plus_00_dot_71_bar__plus_02_dot_88 TVStand_bar__minus_01_dot_86_bar__minus_00_dot_01_bar__plus_02_dot_79)
        (inReceptacle Television_bar__minus_01_dot_86_bar__plus_01_dot_20_bar__plus_02_dot_69 TVStand_bar__minus_01_dot_86_bar__minus_00_dot_01_bar__plus_02_dot_79)
        (inReceptacle Pillow_bar__minus_01_dot_92_bar__plus_00_dot_36_bar__minus_01_dot_50 ArmChair_bar__minus_02_dot_01_bar__plus_00_dot_00_bar__minus_01_dot_58)
        (inReceptacle Statue_bar__plus_01_dot_44_bar__plus_00_dot_79_bar__minus_00_dot_29 DiningTable_bar__plus_01_dot_88_bar_00_dot_00_bar__minus_00_dot_29)
        (inReceptacle Vase_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_05 DiningTable_bar__plus_01_dot_88_bar_00_dot_00_bar__minus_00_dot_29)
        (inReceptacle Statue_bar__plus_02_dot_11_bar__plus_00_dot_79_bar__minus_00_dot_66 DiningTable_bar__plus_01_dot_88_bar_00_dot_00_bar__minus_00_dot_29)
        (inReceptacle Vase_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_42 DiningTable_bar__plus_01_dot_88_bar_00_dot_00_bar__minus_00_dot_29)
        (inReceptacle Candle_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_66 DiningTable_bar__plus_01_dot_88_bar_00_dot_00_bar__minus_00_dot_29)
        (inReceptacle Box_bar__plus_02_dot_11_bar__plus_00_dot_94_bar__minus_00_dot_17 DiningTable_bar__plus_01_dot_88_bar_00_dot_00_bar__minus_00_dot_29)
        (inReceptacle CreditCard_bar__minus_03_dot_17_bar__plus_00_dot_58_bar__plus_00_dot_47 Sofa_bar__minus_03_dot_44_bar__minus_00_dot_01_bar__minus_00_dot_04)
        (inReceptacle Laptop_bar__minus_03_dot_34_bar__plus_00_dot_58_bar__minus_00_dot_81 Sofa_bar__minus_03_dot_44_bar__minus_00_dot_01_bar__minus_00_dot_04)
        (inReceptacle KeyChain_bar__minus_01_dot_86_bar__plus_00_dot_45_bar__plus_00_dot_02 CoffeeTable_bar__minus_02_dot_02_bar__minus_00_dot_01_bar__plus_00_dot_02)
        (inReceptacle Plate_bar__minus_02_dot_10_bar__plus_00_dot_45_bar__minus_00_dot_11 CoffeeTable_bar__minus_02_dot_02_bar__minus_00_dot_01_bar__plus_00_dot_02)
        (inReceptacle CreditCard_bar__minus_02_dot_27_bar__plus_00_dot_45_bar__plus_00_dot_27 CoffeeTable_bar__minus_02_dot_02_bar__minus_00_dot_01_bar__plus_00_dot_02)
        (inReceptacle Candle_bar__minus_01_dot_94_bar__plus_00_dot_45_bar__plus_00_dot_27 CoffeeTable_bar__minus_02_dot_02_bar__minus_00_dot_01_bar__plus_00_dot_02)
        (inReceptacle RemoteControl_bar__minus_01_dot_75_bar__plus_00_dot_23_bar__plus_02_dot_83 DiningTable_bar__minus_01_dot_88_bar__plus_00_dot_27_bar__plus_02_dot_79)
        
        
        (receptacleAtLocation ArmChair_bar__minus_00_dot_43_bar__plus_00_dot_00_bar__plus_01_dot_06 loc_bar__minus_6_bar_5_bar_1_bar_60)
        (receptacleAtLocation ArmChair_bar__minus_00_dot_44_bar__plus_00_dot_00_bar__plus_01_dot_92 loc_bar__minus_6_bar_8_bar_1_bar_60)
        (receptacleAtLocation ArmChair_bar__minus_02_dot_01_bar__plus_00_dot_00_bar__minus_01_dot_58 loc_bar__minus_3_bar__minus_5_bar_3_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_02_dot_02_bar__minus_00_dot_01_bar__plus_00_dot_02 loc_bar__minus_4_bar_0_bar_3_bar_60)
        (receptacleAtLocation DiningTable_bar__plus_01_dot_88_bar_00_dot_00_bar__minus_00_dot_29 loc_bar_8_bar__minus_5_bar_0_bar_60)
        (receptacleAtLocation DiningTable_bar__minus_01_dot_88_bar__plus_00_dot_27_bar__plus_02_dot_79 loc_bar__minus_9_bar_7_bar_0_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_00_dot_29_bar__plus_00_dot_00_bar__plus_01_dot_26 loc_bar_3_bar_4_bar_3_bar_60)
        (receptacleAtLocation Sofa_bar__minus_03_dot_44_bar__minus_00_dot_01_bar__minus_00_dot_04 loc_bar__minus_11_bar_0_bar_3_bar_60)
        (receptacleAtLocation TVStand_bar__minus_01_dot_86_bar__minus_00_dot_01_bar__plus_02_dot_79 loc_bar__minus_8_bar_7_bar_0_bar_60)
        (objectAtLocation KeyChain_bar__minus_01_dot_86_bar__plus_00_dot_45_bar__plus_00_dot_02 loc_bar__minus_4_bar_0_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__minus_03_dot_17_bar__plus_00_dot_58_bar__plus_00_dot_47 loc_bar__minus_11_bar_0_bar_3_bar_60)
        (objectAtLocation Vase_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_05 loc_bar_8_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Candle_bar__minus_01_dot_94_bar__plus_00_dot_45_bar__plus_00_dot_27 loc_bar__minus_4_bar_0_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_73_bar__plus_00_dot_71_bar__plus_02_dot_88 loc_bar__minus_8_bar_7_bar_0_bar_60)
        (objectAtLocation KeyChain_bar__minus_03_dot_52_bar__plus_00_dot_53_bar__minus_00_dot_81 loc_bar__minus_11_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Chair_bar__plus_01_dot_64_bar_00_dot_00_bar__minus_00_dot_85 loc_bar_7_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Box_bar__plus_02_dot_11_bar__plus_00_dot_94_bar__minus_00_dot_17 loc_bar_8_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Chair_bar__plus_02_dot_29_bar_00_dot_00_bar__minus_00_dot_78 loc_bar_9_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Chair_bar__plus_01_dot_46_bar_00_dot_00_bar__plus_00_dot_20 loc_bar_6_bar_3_bar_2_bar_60)
        (objectAtLocation Chair_bar__plus_02_dot_22_bar_00_dot_00_bar__plus_00_dot_15 loc_bar_9_bar_3_bar_2_bar_60)
        (objectAtLocation Candle_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_66 loc_bar_8_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Vase_bar__plus_02_dot_55_bar__plus_00_dot_78_bar__minus_00_dot_42 loc_bar_8_bar__minus_5_bar_0_bar_60)
        (objectAtLocation CreditCard_bar__minus_02_dot_27_bar__plus_00_dot_45_bar__plus_00_dot_27 loc_bar__minus_4_bar_0_bar_3_bar_60)
        (objectAtLocation Vase_bar__minus_01_dot_48_bar__plus_00_dot_72_bar__plus_02_dot_83 loc_bar__minus_8_bar_7_bar_0_bar_60)
        (objectAtLocation WateringCan_bar__minus_03_dot_25_bar_00_dot_00_bar__minus_01_dot_86 loc_bar__minus_11_bar__minus_6_bar_3_bar_60)
        (objectAtLocation LightSwitch_bar__minus_03_dot_90_bar__plus_01_dot_28_bar__plus_01_dot_68 loc_bar__minus_13_bar_7_bar_3_bar_30)
        (objectAtLocation Laptop_bar__minus_03_dot_34_bar__plus_00_dot_58_bar__minus_00_dot_81 loc_bar__minus_11_bar_0_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__minus_01_dot_75_bar__plus_00_dot_23_bar__plus_02_dot_83 loc_bar__minus_9_bar_7_bar_0_bar_60)
        (objectAtLocation FloorLamp_bar__minus_00_dot_33_bar__plus_00_dot_00_bar__plus_02_dot_73 loc_bar__minus_5_bar_8_bar_1_bar_60)
        (objectAtLocation Statue_bar__plus_01_dot_44_bar__plus_00_dot_79_bar__minus_00_dot_29 loc_bar_8_bar__minus_5_bar_0_bar_60)
        (objectAtLocation KeyChain_bar__minus_03_dot_08_bar__plus_00_dot_53_bar__minus_00_dot_30 loc_bar__minus_11_bar__minus_1_bar_3_bar_60)
        (objectAtLocation Painting_bar__minus_00_dot_06_bar__plus_01_dot_60_bar__plus_01_dot_73 loc_bar__minus_5_bar_7_bar_1_bar_0)
        (objectAtLocation Painting_bar__plus_04_dot_14_bar__plus_01_dot_59_bar__minus_00_dot_30 loc_bar_14_bar__minus_1_bar_1_bar_0)
        (objectAtLocation Pillow_bar__minus_01_dot_92_bar__plus_00_dot_36_bar__minus_01_dot_50 loc_bar__minus_3_bar__minus_5_bar_3_bar_60)
        (objectAtLocation Television_bar__minus_01_dot_86_bar__plus_01_dot_20_bar__plus_02_dot_69 loc_bar__minus_8_bar_7_bar_0_bar_60)
        (objectAtLocation HousePlant_bar__minus_03_dot_48_bar__plus_00_dot_71_bar__minus_01_dot_59 loc_bar__minus_11_bar__minus_6_bar_3_bar_45)
        (objectAtLocation Window_bar__plus_01_dot_48_bar__plus_01_dot_51_bar__minus_01_dot_93 loc_bar_6_bar__minus_5_bar_2_bar_15)
        (objectAtLocation Window_bar__minus_02_dot_00_bar__plus_01_dot_51_bar__minus_01_dot_93 loc_bar__minus_11_bar__minus_6_bar_1_bar_15)
        (objectAtLocation Window_bar__plus_03_dot_48_bar__plus_01_dot_51_bar__minus_01_dot_93 loc_bar_14_bar__minus_5_bar_2_bar_15)
        (objectAtLocation Plate_bar__minus_02_dot_10_bar__plus_00_dot_45_bar__minus_00_dot_11 loc_bar__minus_4_bar_0_bar_3_bar_60)
        (objectAtLocation Statue_bar__plus_02_dot_11_bar__plus_00_dot_79_bar__minus_00_dot_66 loc_bar_8_bar__minus_5_bar_0_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 StatueType)
                                    (receptacleType ?r CoffeeTableType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 StatueType)
                                            (receptacleType ?r CoffeeTableType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            