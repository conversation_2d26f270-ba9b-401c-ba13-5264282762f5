{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000150.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000151.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000152.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000153.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000154.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000155.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000158.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000159.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000160.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000161.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000162.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000163.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 66}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 66}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 68}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 68}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000299.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000300.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000301.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000302.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000303.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000304.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000305.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000306.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000307.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000308.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000309.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000310.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000311.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000312.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000313.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000314.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000315.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000316.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000317.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000318.png", "low_idx": 72}, {"high_idx": 7, "image_name": "000000319.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000320.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000321.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000322.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000323.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000324.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000325.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000326.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000327.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000328.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000329.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000330.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000331.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000332.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000333.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000334.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000335.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000336.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000337.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000338.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000339.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000340.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000341.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000342.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000343.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000344.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000345.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000346.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000347.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000348.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000349.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000350.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000351.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000352.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000353.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000354.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000355.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000356.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000357.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000358.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000359.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000360.png", "low_idx": 77}, {"high_idx": 7, "image_name": "000000361.png", "low_idx": 77}, {"high_idx": 7, "image_name": "000000362.png", "low_idx": 77}, {"high_idx": 7, "image_name": "000000363.png", "low_idx": 77}, {"high_idx": 7, "image_name": "000000364.png", "low_idx": 77}, {"high_idx": 7, "image_name": "000000365.png", "low_idx": 77}, {"high_idx": 7, "image_name": "000000366.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000367.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000368.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000369.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000370.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000371.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000372.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000373.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000374.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000375.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000376.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000377.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000378.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000379.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000380.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000381.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000382.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000383.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000384.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000385.png", "low_idx": 80}, {"high_idx": 7, "image_name": "000000386.png", "low_idx": 80}, {"high_idx": 7, "image_name": "000000387.png", "low_idx": 80}, {"high_idx": 7, "image_name": "000000388.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000389.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000390.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000391.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000392.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000393.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000394.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000395.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000396.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000397.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000398.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000399.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000400.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000401.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000402.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000403.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000404.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000405.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000406.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000407.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000408.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000409.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000410.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000411.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000412.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000413.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000414.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000415.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000416.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000417.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000418.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000419.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000420.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000421.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000422.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000423.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000424.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000425.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000426.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000427.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000428.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000429.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000430.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000431.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000432.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000433.png", "low_idx": 85}, {"high_idx": 8, "image_name": "000000434.png", "low_idx": 85}, {"high_idx": 8, "image_name": "000000435.png", "low_idx": 86}, {"high_idx": 8, "image_name": "000000436.png", "low_idx": 86}, {"high_idx": 8, "image_name": "000000437.png", "low_idx": 87}, {"high_idx": 8, "image_name": "000000438.png", "low_idx": 87}, {"high_idx": 8, "image_name": "000000439.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000440.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000441.png", "low_idx": 89}, {"high_idx": 8, "image_name": "000000442.png", "low_idx": 89}, {"high_idx": 8, "image_name": "000000443.png", "low_idx": 90}, {"high_idx": 8, "image_name": "000000444.png", "low_idx": 90}, {"high_idx": 8, "image_name": "000000445.png", "low_idx": 91}, {"high_idx": 8, "image_name": "000000446.png", "low_idx": 91}, {"high_idx": 8, "image_name": "000000447.png", "low_idx": 92}, {"high_idx": 8, "image_name": "000000448.png", "low_idx": 92}, {"high_idx": 8, "image_name": "000000449.png", "low_idx": 93}, {"high_idx": 8, "image_name": "000000450.png", "low_idx": 93}, {"high_idx": 8, "image_name": "000000451.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000452.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000453.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000454.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000455.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000456.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000457.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000458.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000459.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000460.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000461.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000462.png", "low_idx": 95}, {"high_idx": 8, "image_name": "000000463.png", "low_idx": 95}, {"high_idx": 8, "image_name": "000000464.png", "low_idx": 96}, {"high_idx": 8, "image_name": "000000465.png", "low_idx": 96}, {"high_idx": 8, "image_name": "000000466.png", "low_idx": 97}, {"high_idx": 8, "image_name": "000000467.png", "low_idx": 97}, {"high_idx": 8, "image_name": "000000468.png", "low_idx": 98}, {"high_idx": 8, "image_name": "000000469.png", "low_idx": 98}, {"high_idx": 8, "image_name": "000000470.png", "low_idx": 99}, {"high_idx": 8, "image_name": "000000471.png", "low_idx": 99}, {"high_idx": 8, "image_name": "000000472.png", "low_idx": 100}, {"high_idx": 8, "image_name": "000000473.png", "low_idx": 100}, {"high_idx": 8, "image_name": "000000474.png", "low_idx": 101}, {"high_idx": 8, "image_name": "000000475.png", "low_idx": 101}, {"high_idx": 8, "image_name": "000000476.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000477.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000478.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000479.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000480.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000481.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000482.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000483.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000484.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000485.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000486.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000487.png", "low_idx": 103}, {"high_idx": 8, "image_name": "000000488.png", "low_idx": 103}, {"high_idx": 8, "image_name": "000000489.png", "low_idx": 104}, {"high_idx": 8, "image_name": "000000490.png", "low_idx": 104}, {"high_idx": 8, "image_name": "000000491.png", "low_idx": 105}, {"high_idx": 8, "image_name": "000000492.png", "low_idx": 105}, {"high_idx": 8, "image_name": "000000493.png", "low_idx": 106}, {"high_idx": 8, "image_name": "000000494.png", "low_idx": 106}, {"high_idx": 8, "image_name": "000000495.png", "low_idx": 107}, {"high_idx": 8, "image_name": "000000496.png", "low_idx": 107}, {"high_idx": 8, "image_name": "000000497.png", "low_idx": 108}, {"high_idx": 8, "image_name": "000000498.png", "low_idx": 108}, {"high_idx": 8, "image_name": "000000499.png", "low_idx": 109}, {"high_idx": 8, "image_name": "000000500.png", "low_idx": 109}, {"high_idx": 8, "image_name": "000000501.png", "low_idx": 110}, {"high_idx": 8, "image_name": "000000502.png", "low_idx": 110}, {"high_idx": 8, "image_name": "000000503.png", "low_idx": 111}, {"high_idx": 8, "image_name": "000000504.png", "low_idx": 111}, {"high_idx": 8, "image_name": "000000505.png", "low_idx": 112}, {"high_idx": 8, "image_name": "000000506.png", "low_idx": 112}, {"high_idx": 8, "image_name": "000000507.png", "low_idx": 113}, {"high_idx": 8, "image_name": "000000508.png", "low_idx": 113}, {"high_idx": 8, "image_name": "000000509.png", "low_idx": 114}, {"high_idx": 8, "image_name": "000000510.png", "low_idx": 114}, {"high_idx": 8, "image_name": "000000511.png", "low_idx": 114}, {"high_idx": 8, "image_name": "000000512.png", "low_idx": 114}, {"high_idx": 8, "image_name": "000000513.png", "low_idx": 114}, {"high_idx": 8, "image_name": "000000514.png", "low_idx": 114}, {"high_idx": 8, "image_name": "000000515.png", "low_idx": 114}, {"high_idx": 8, "image_name": "000000516.png", "low_idx": 114}, {"high_idx": 8, "image_name": "000000517.png", "low_idx": 114}, {"high_idx": 8, "image_name": "000000518.png", "low_idx": 114}, {"high_idx": 8, "image_name": "000000519.png", "low_idx": 114}, {"high_idx": 8, "image_name": "000000520.png", "low_idx": 115}, {"high_idx": 8, "image_name": "000000521.png", "low_idx": 115}, {"high_idx": 8, "image_name": "000000522.png", "low_idx": 116}, {"high_idx": 8, "image_name": "000000523.png", "low_idx": 116}, {"high_idx": 8, "image_name": "000000524.png", "low_idx": 117}, {"high_idx": 8, "image_name": "000000525.png", "low_idx": 117}, {"high_idx": 8, "image_name": "000000526.png", "low_idx": 118}, {"high_idx": 8, "image_name": "000000527.png", "low_idx": 118}, {"high_idx": 8, "image_name": "000000528.png", "low_idx": 119}, {"high_idx": 8, "image_name": "000000529.png", "low_idx": 119}, {"high_idx": 8, "image_name": "000000530.png", "low_idx": 120}, {"high_idx": 8, "image_name": "000000531.png", "low_idx": 120}, {"high_idx": 8, "image_name": "000000532.png", "low_idx": 121}, {"high_idx": 8, "image_name": "000000533.png", "low_idx": 121}, {"high_idx": 8, "image_name": "000000534.png", "low_idx": 122}, {"high_idx": 8, "image_name": "000000535.png", "low_idx": 122}, {"high_idx": 8, "image_name": "000000536.png", "low_idx": 123}, {"high_idx": 8, "image_name": "000000537.png", "low_idx": 123}, {"high_idx": 8, "image_name": "000000538.png", "low_idx": 123}, {"high_idx": 8, "image_name": "000000539.png", "low_idx": 123}, {"high_idx": 8, "image_name": "000000540.png", "low_idx": 123}, {"high_idx": 8, "image_name": "000000541.png", "low_idx": 123}, {"high_idx": 8, "image_name": "000000542.png", "low_idx": 123}, {"high_idx": 8, "image_name": "000000543.png", "low_idx": 123}, {"high_idx": 8, "image_name": "000000544.png", "low_idx": 123}, {"high_idx": 8, "image_name": "000000545.png", "low_idx": 123}, {"high_idx": 8, "image_name": "000000546.png", "low_idx": 123}, {"high_idx": 8, "image_name": "000000547.png", "low_idx": 124}, {"high_idx": 8, "image_name": "000000548.png", "low_idx": 124}, {"high_idx": 8, "image_name": "000000549.png", "low_idx": 124}, {"high_idx": 8, "image_name": "000000550.png", "low_idx": 124}, {"high_idx": 8, "image_name": "000000551.png", "low_idx": 124}, {"high_idx": 8, "image_name": "000000552.png", "low_idx": 124}, {"high_idx": 8, "image_name": "000000553.png", "low_idx": 124}, {"high_idx": 8, "image_name": "000000554.png", "low_idx": 124}, {"high_idx": 8, "image_name": "000000555.png", "low_idx": 124}, {"high_idx": 8, "image_name": "000000556.png", "low_idx": 124}, {"high_idx": 8, "image_name": "000000557.png", "low_idx": 124}, {"high_idx": 9, "image_name": "000000558.png", "low_idx": 125}, {"high_idx": 9, "image_name": "000000559.png", "low_idx": 125}, {"high_idx": 9, "image_name": "000000560.png", "low_idx": 125}, {"high_idx": 9, "image_name": "000000561.png", "low_idx": 125}, {"high_idx": 9, "image_name": "000000562.png", "low_idx": 125}, {"high_idx": 9, "image_name": "000000563.png", "low_idx": 125}, {"high_idx": 9, "image_name": "000000564.png", "low_idx": 125}, {"high_idx": 9, "image_name": "000000565.png", "low_idx": 125}, {"high_idx": 9, "image_name": "000000566.png", "low_idx": 125}, {"high_idx": 9, "image_name": "000000567.png", "low_idx": 125}, {"high_idx": 9, "image_name": "000000568.png", "low_idx": 125}, {"high_idx": 9, "image_name": "000000569.png", "low_idx": 125}, {"high_idx": 9, "image_name": "000000570.png", "low_idx": 125}, {"high_idx": 9, "image_name": "000000571.png", "low_idx": 125}, {"high_idx": 9, "image_name": "000000572.png", "low_idx": 125}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "DiningTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-11|8|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [-10.933708, -10.933708, 11.53657152, 11.53657152, 3.5871436, 3.5871436]], "coordinateReceptacleObjectId": ["DiningTable", [-10.652, -10.652, 12.84, 12.84, 3.332, 3.332]], "forceVisible": true, "objectId": "Knife|-02.73|+00.90|+02.88"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-11|-4|1|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-7.84467888, -7.84467888, -3.7596472, -3.7596472, 3.982030152, 3.982030152]], "forceVisible": true, "objectId": "Bread|-01.96|+01.00|-00.94"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "countertop"]}, "high_idx": 4, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [-10.933708, -10.933708, 11.53657152, 11.53657152, 3.5871436, 3.5871436]], "coordinateReceptacleObjectId": ["CounterTop", [-7.464, -7.464, -2.456, -2.456, 3.7904, 3.7904]], "forceVisible": true, "objectId": "Knife|-02.73|+00.90|+02.88", "receptacleObjectId": "CounterTop|-01.87|+00.95|-00.61"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-7.84467888, -7.84467888, -3.7596472, -3.7596472, 3.982030152, 3.982030152]], "coordinateReceptacleObjectId": ["CounterTop", [-7.464, -7.464, -2.456, -2.456, 3.7904, 3.7904]], "forceVisible": true, "objectId": "Bread|-01.96|+01.00|-00.94|BreadSliced_2"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|5|-3|2|0"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [4.6016, 4.6016, -6.45, -6.45, 6.584, 6.584]], "forceVisible": true, "objectId": "Microwave|+01.15|+01.65|-01.61"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-15|12|1|30"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "diningtable"]}, "high_idx": 9, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-7.84467888, -7.84467888, -3.7596472, -3.7596472, 3.982030152, 3.982030152]], "coordinateReceptacleObjectId": ["DiningTable", [-10.652, -10.652, 12.84, 12.84, 3.332, 3.332]], "forceVisible": true, "objectId": "Bread|-01.96|+01.00|-00.94|BreadSliced_2", "receptacleObjectId": "DiningTable|-02.66|+00.83|+03.21"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 10, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|-02.73|+00.90|+02.88"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [104, 122, 183, 130], "mask": [[36442, 10], [36478, 1], [36732, 21], [36777, 5], [37024, 1], [37028, 25], [37063, 21], [37318, 9], [37329, 25], [37355, 29], [37613, 70], [37909, 70], [38206, 53], [38504, 37], [38809, 11]], "point": [143, 125]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-01.96|+01.00|-00.94"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [101, 95, 165, 142], "mask": [[28308, 9], [28325, 7], [28342, 1], [28351, 10], [28606, 56], [28905, 58], [29205, 58], [29504, 59], [29804, 60], [30104, 60], [30403, 61], [30703, 61], [31003, 61], [31302, 63], [31602, 63], [31902, 63], [32202, 63], [32501, 64], [32801, 64], [33101, 64], [33401, 64], [33701, 65], [34001, 65], [34301, 65], [34601, 65], [34901, 65], [35201, 65], [35501, 65], [35801, 65], [36101, 65], [36401, 65], [36701, 65], [37001, 65], [37301, 65], [37601, 65], [37901, 65], [38201, 65], [38501, 65], [38801, 65], [39102, 64], [39402, 64], [39702, 64], [40002, 64], [40302, 64], [40602, 63], [40903, 62], [41203, 62], [41503, 62], [41803, 62], [42104, 60], [42406, 57]], "point": [133, 117]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|-02.73|+00.90|+02.88", "placeStationary": true, "receptacleObjectId": "CounterTop|-01.87|+00.95|-00.61"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 200], "mask": [[169, 12], [470, 11], [770, 11], [1070, 11], [1370, 12], [1670, 12], [1970, 12], [2271, 12], [2571, 12], [2871, 12], [3171, 13], [3471, 14], [3772, 16], [4072, 20], [4372, 41], [4434, 2], [4672, 41], [4734, 1], [4972, 40], [5272, 40], [5573, 40], [5873, 40], [6173, 40], [6473, 41], [6773, 42], [7073, 45], [7374, 46], [7674, 39], [7974, 38], [8274, 36], [8574, 35], [8874, 35], [9175, 34], [9475, 34], [9775, 33], [10075, 33], [10375, 33], [10676, 32], [10976, 32], [11276, 32], [11576, 33], [11876, 34], [12176, 34], [12477, 34], [12777, 34], [13077, 34], [13377, 34], [13677, 35], [13977, 35], [14278, 35], [14578, 37], [14696, 2], [14878, 39], [14996, 2], [15178, 30], [15295, 4], [15478, 25], [15523, 77], [15778, 21], [15825, 75], [16079, 17], [16126, 74], [16379, 14], [16427, 73], [16679, 11], [16727, 73], [16979, 9], [17027, 73], [17279, 6], [17328, 72], [17580, 2], [17628, 72], [17928, 72], [18228, 72], [18528, 72], [18828, 72], [19128, 72], [19207, 172], [19427, 73], [19507, 172], [19727, 73], [19807, 173], [20026, 74], [20107, 175], [20326, 74], [20408, 176], [20625, 75], [20708, 179], [20924, 76], [21008, 182], [21223, 77], [21308, 188], [21520, 80], [21608, 195], [21816, 84], [21908, 292], [22208, 292], [22508, 292], [22807, 293], [23107, 293], [23407, 293], [23706, 294], [24006, 294], [24305, 295], [24604, 296], [24903, 297], [25202, 298], [25501, 2808], [28316, 13], [28330, 13], [28344, 13], [28362, 2], [28366, 240], [28634, 1], [28645, 1], [28654, 1], [28667, 238], [28968, 237], [29268, 41], [29316, 188], [29568, 39], [29611, 3], [29618, 186], [29869, 37], [29909, 8], [29920, 184], [30169, 32], [30226, 177], [30469, 31], [30528, 175], [30769, 29], [30831, 172], [31069, 27], [31133, 170], [31370, 25], [31435, 167], [31670, 23], [31737, 165], [31970, 22], [32039, 121], [32162, 40], [32270, 20], [32341, 118], [32462, 40], [32570, 19], [32643, 116], [32762, 39], [32870, 17], [32945, 113], [33062, 39], [33170, 16], [33247, 111], [33362, 39], [33471, 14], [33549, 108], [33662, 39], [33771, 14], [33849, 108], [33962, 39], [34071, 14], [34150, 106], [34262, 39], [34371, 15], [34450, 106], [34562, 39], [34671, 15], [34751, 104], [34861, 40], [34971, 15], [35051, 104], [35161, 40], [35271, 15], [35352, 103], [35460, 41], [35571, 15], [35652, 102], [35760, 41], [35871, 15], [35952, 102], [36059, 42], [36171, 15], [36253, 100], [36358, 43], [36471, 15], [36553, 100], [36658, 43], [36771, 16], [36854, 99], [36957, 44], [37071, 16], [37154, 98], [37256, 45], [37371, 16], [37455, 97], [37555, 46], [37671, 16], [37755, 96], [37855, 46], [37971, 16], [38055, 96], [38154, 47], [38271, 17], [38356, 94], [38454, 47], [38571, 17], [38656, 94], [38753, 48], [38871, 17], [38955, 95], [39053, 49], [39171, 17], [39254, 95], [39352, 50], [39471, 18], [39553, 96], [39652, 50], [39771, 19], [39852, 96], [39951, 51], [40071, 20], [40151, 97], [40251, 51], [40370, 22], [40450, 97], [40551, 51], [40670, 23], [40749, 97], [40850, 53], [40970, 25], [41048, 98], [41150, 53], [41234, 1], [41270, 26], [41347, 98], [41450, 53], [41534, 1], [41570, 28], [41646, 99], [41750, 53], [41834, 1], [41845, 1], [41870, 29], [41945, 99], [42049, 55], [42134, 1], [42145, 1], [42154, 1], [42169, 32], [42244, 100], [42349, 56], [42434, 1], [42445, 1], [42454, 1], [42462, 1], [42468, 35], [42543, 100], [42649, 71], [42734, 21], [42762, 43], [42841, 102], [42948, 158], [43140, 102], [43248, 160], [43438, 104], [43548, 162], [43735, 106], [43847, 173], [44034, 107], [44147, 176], [44331, 110], [44446, 294], [44745, 296], [45044, 14956]], "point": [149, 94]}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.96|+01.00|-00.94|BreadSliced_2"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [146, 96, 153, 142], "mask": [[28646, 8], [28946, 8], [29246, 8], [29546, 8], [29846, 8], [30146, 8], [30446, 8], [30746, 8], [31046, 8], [31346, 8], [31646, 8], [31946, 8], [32246, 8], [32546, 8], [32846, 8], [33146, 8], [33446, 8], [33746, 8], [34046, 8], [34346, 8], [34646, 8], [34946, 8], [35246, 8], [35546, 8], [35846, 8], [36146, 8], [36446, 8], [36746, 8], [37046, 8], [37346, 8], [37646, 8], [37946, 8], [38246, 8], [38546, 8], [38846, 8], [39146, 8], [39446, 8], [39746, 8], [40046, 8], [40346, 8], [40646, 8], [40946, 8], [41246, 8], [41546, 8], [41846, 8], [42146, 8], [42446, 8]], "point": [149, 118]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.15|+01.65|-01.61"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [76, 1, 297, 124], "mask": [[76, 222], [376, 222], [676, 222], [976, 222], [1276, 222], [1576, 222], [1876, 222], [2176, 222], [2476, 222], [2776, 222], [3076, 222], [3376, 222], [3676, 222], [3976, 222], [4276, 222], [4576, 222], [4876, 222], [5176, 222], [5476, 222], [5776, 222], [6076, 222], [6376, 222], [6676, 222], [6976, 222], [7276, 222], [7576, 222], [7876, 222], [8176, 222], [8476, 222], [8776, 222], [9076, 222], [9376, 222], [9676, 222], [9976, 222], [10276, 222], [10576, 222], [10876, 222], [11176, 222], [11476, 222], [11776, 222], [12076, 222], [12376, 222], [12676, 222], [12976, 222], [13276, 222], [13576, 222], [13876, 222], [14176, 222], [14476, 222], [14776, 222], [15076, 222], [15376, 222], [15676, 222], [15976, 222], [16276, 222], [16576, 222], [16876, 222], [17176, 222], [17476, 222], [17776, 222], [18076, 222], [18376, 222], [18676, 222], [18976, 222], [19276, 222], [19576, 222], [19876, 222], [20176, 222], [20476, 222], [20776, 222], [21076, 222], [21376, 222], [21676, 222], [21976, 222], [22276, 222], [22576, 222], [22876, 222], [23176, 222], [23476, 222], [23776, 222], [24076, 222], [24376, 222], [24676, 222], [24976, 222], [25276, 222], [25576, 222], [25876, 222], [26176, 222], [26476, 222], [26776, 222], [27076, 222], [27376, 222], [27676, 222], [27976, 222], [28276, 222], [28576, 222], [28876, 222], [29176, 222], [29476, 222], [29776, 222], [30076, 222], [30376, 222], [30676, 222], [30976, 222], [31276, 222], [31576, 222], [31876, 222], [32176, 222], [32477, 219], [32778, 215], [33080, 208], [33381, 209], [33681, 209], [33981, 209], [34281, 209], [34582, 205], [34884, 199], [35186, 193], [35488, 187], [35790, 183], [36092, 181], [36394, 169], [36564, 9], [36696, 163], [36864, 9], [36998, 157], [37164, 9]], "point": [186, 61]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.96|+01.00|-00.94|BreadSliced_2", "placeStationary": true, "receptacleObjectId": "Microwave|+01.15|+01.65|-01.61"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 297, 124], "mask": [[0, 81], [252, 46], [300, 81], [551, 47], [600, 81], [851, 47], [900, 82], [1150, 48], [1200, 82], [1449, 49], [1500, 298], [1800, 298], [2100, 298], [2400, 298], [2700, 298], [3000, 298], [3300, 298], [3600, 298], [3900, 298], [4200, 298], [4500, 298], [4800, 298], [5100, 298], [5400, 298], [5700, 298], [6000, 298], [6300, 298], [6600, 298], [6900, 298], [7200, 298], [7500, 298], [7800, 298], [8100, 298], [8400, 298], [8700, 298], [9000, 298], [9300, 298], [9600, 298], [9900, 298], [10200, 298], [10500, 298], [10800, 298], [11100, 298], [11400, 298], [11700, 298], [12000, 298], [12300, 298], [12600, 298], [12900, 298], [13200, 298], [13500, 298], [13800, 298], [14100, 298], [14400, 298], [14700, 298], [15000, 298], [15300, 298], [15600, 298], [15900, 298], [16200, 298], [16500, 298], [16800, 298], [17100, 298], [17400, 298], [17700, 298], [18000, 298], [18300, 298], [18600, 298], [18900, 298], [19200, 298], [19500, 298], [19800, 298], [20100, 298], [20400, 298], [20700, 298], [21000, 298], [21300, 298], [21600, 298], [21900, 298], [22200, 298], [22500, 298], [22800, 298], [23100, 298], [23401, 297], [23703, 295], [24005, 293], [24307, 291], [24609, 289], [24912, 286], [25214, 284], [25516, 282], [25818, 280], [26120, 278], [26422, 276], [26724, 274], [27026, 272], [27328, 270], [27630, 268], [27932, 266], [28234, 264], [28536, 262], [28839, 259], [29141, 257], [29443, 255], [29745, 253], [30047, 251], [30349, 249], [30651, 247], [30953, 245], [31255, 243], [31557, 241], [31859, 239], [32161, 237], [32464, 232], [32766, 227], [33068, 220], [33381, 209], [33681, 209], [33981, 209], [34281, 209], [34582, 205], [34884, 199], [35186, 193], [35488, 187], [35790, 183], [36092, 181], [36394, 169], [36564, 9], [36696, 163], [36864, 9], [36998, 157], [37164, 9]], "point": [148, 61]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.15|+01.65|-01.61"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 297, 124], "mask": [[0, 81], [252, 46], [300, 81], [551, 47], [600, 81], [851, 47], [900, 82], [1150, 48], [1200, 82], [1449, 49], [1500, 298], [1800, 298], [2100, 298], [2400, 298], [2700, 298], [3000, 298], [3300, 298], [3600, 298], [3900, 298], [4200, 298], [4500, 298], [4800, 298], [5100, 298], [5400, 298], [5700, 298], [6000, 298], [6300, 298], [6600, 298], [6900, 298], [7200, 298], [7500, 298], [7800, 298], [8100, 298], [8400, 298], [8700, 298], [9000, 298], [9300, 298], [9600, 298], [9900, 298], [10200, 298], [10500, 298], [10800, 298], [11100, 298], [11400, 298], [11700, 139], [11852, 146], [12000, 136], [12156, 142], [12300, 133], [12459, 139], [12600, 131], [12763, 135], [12900, 130], [13064, 134], [13200, 129], [13365, 133], [13500, 128], [13666, 132], [13800, 126], [13968, 130], [14100, 126], [14268, 130], [14400, 126], [14568, 130], [14700, 125], [14868, 130], [15000, 125], [15168, 130], [15300, 125], [15468, 130], [15600, 124], [15769, 129], [15900, 124], [16070, 128], [16200, 123], [16371, 127], [16500, 121], [16671, 127], [16800, 121], [16971, 127], [17100, 120], [17271, 127], [17400, 120], [17571, 127], [17700, 120], [17872, 126], [18000, 119], [18172, 126], [18300, 119], [18472, 126], [18600, 119], [18772, 126], [18900, 118], [19072, 126], [19200, 118], [19373, 125], [19500, 118], [19673, 125], [19800, 117], [19973, 125], [20100, 117], [20273, 125], [20400, 117], [20574, 124], [20700, 117], [20874, 124], [21000, 117], [21174, 124], [21300, 117], [21474, 124], [21600, 117], [21774, 124], [21900, 117], [22074, 124], [22200, 117], [22374, 124], [22500, 117], [22674, 124], [22800, 117], [22974, 124], [23100, 117], [23274, 124], [23401, 116], [23574, 124], [23703, 114], [23874, 124], [24005, 112], [24174, 124], [24307, 110], [24474, 124], [24609, 108], [24774, 124], [24912, 105], [25074, 124], [25214, 103], [25374, 124], [25516, 101], [25673, 125], [25818, 100], [25973, 125], [26120, 98], [26273, 125], [26422, 97], [26571, 127], [26724, 99], [26870, 128], [27026, 272], [27328, 270], [27630, 268], [27932, 266], [28234, 264], [28536, 262], [28839, 259], [29141, 257], [29443, 255], [29745, 253], [30047, 251], [30349, 249], [30651, 247], [30953, 245], [31255, 243], [31557, 241], [31859, 239], [32161, 237], [32464, 232], [32766, 227], [33068, 220], [33381, 209], [33681, 209], [33981, 209], [34281, 209], [34582, 205], [34884, 199], [35186, 193], [35488, 187], [35790, 183], [36092, 181], [36394, 169], [36564, 9], [36696, 163], [36864, 9], [36998, 157], [37164, 9]], "point": [152, 39]}}, "high_idx": 7}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [4.6016, 4.6016, -6.45, -6.45, 6.584, 6.584]], "forceVisible": true, "objectId": "Microwave|+01.15|+01.65|-01.61"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [76, 1, 297, 124], "mask": [[76, 222], [376, 222], [676, 222], [976, 222], [1276, 222], [1576, 222], [1876, 222], [2176, 222], [2476, 222], [2776, 222], [3076, 222], [3376, 222], [3676, 222], [3976, 222], [4276, 222], [4576, 222], [4876, 222], [5176, 222], [5476, 222], [5776, 222], [6076, 222], [6376, 222], [6676, 222], [6976, 222], [7276, 222], [7576, 222], [7876, 222], [8176, 222], [8476, 222], [8776, 222], [9076, 222], [9376, 222], [9676, 222], [9976, 222], [10276, 222], [10576, 222], [10876, 222], [11176, 222], [11476, 222], [11776, 222], [12076, 222], [12376, 222], [12676, 222], [12976, 222], [13276, 222], [13576, 222], [13876, 222], [14176, 222], [14476, 222], [14776, 222], [15076, 222], [15376, 222], [15676, 222], [15976, 222], [16276, 222], [16576, 222], [16876, 222], [17176, 222], [17476, 222], [17776, 222], [18076, 222], [18376, 222], [18676, 222], [18976, 222], [19276, 222], [19576, 222], [19876, 222], [20176, 222], [20476, 222], [20776, 222], [21076, 222], [21376, 222], [21676, 222], [21976, 222], [22276, 222], [22576, 222], [22876, 222], [23176, 222], [23476, 222], [23776, 222], [24076, 222], [24376, 222], [24676, 222], [24976, 222], [25276, 222], [25576, 222], [25876, 222], [26176, 222], [26476, 222], [26776, 222], [27076, 222], [27376, 222], [27676, 222], [27976, 222], [28276, 222], [28576, 222], [28876, 222], [29176, 222], [29476, 222], [29776, 222], [30076, 222], [30376, 222], [30676, 222], [30976, 222], [31276, 222], [31576, 222], [31876, 222], [32176, 222], [32477, 219], [32778, 215], [33080, 208], [33381, 209], [33681, 209], [33981, 209], [34281, 209], [34582, 205], [34884, 199], [35186, 193], [35488, 187], [35790, 183], [36092, 181], [36394, 169], [36564, 9], [36696, 163], [36864, 9], [36998, 157], [37164, 9]], "point": [186, 61]}}, "high_idx": 7}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [4.6016, 4.6016, -6.45, -6.45, 6.584, 6.584]], "forceVisible": true, "objectId": "Microwave|+01.15|+01.65|-01.61"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [76, 1, 297, 124], "mask": [[76, 222], [376, 222], [676, 222], [976, 222], [1276, 222], [1576, 222], [1876, 222], [2176, 222], [2476, 222], [2776, 222], [3076, 222], [3376, 222], [3676, 222], [3976, 222], [4276, 222], [4576, 222], [4876, 222], [5176, 222], [5476, 222], [5776, 222], [6076, 222], [6376, 222], [6676, 222], [6976, 222], [7276, 222], [7576, 222], [7876, 222], [8176, 222], [8476, 222], [8776, 222], [9076, 222], [9376, 222], [9676, 222], [9976, 222], [10276, 222], [10576, 222], [10876, 222], [11176, 222], [11476, 222], [11776, 222], [12076, 222], [12376, 222], [12676, 222], [12976, 222], [13276, 222], [13576, 222], [13876, 222], [14176, 222], [14476, 222], [14776, 222], [15076, 222], [15376, 222], [15676, 222], [15976, 222], [16276, 222], [16576, 222], [16876, 222], [17176, 222], [17476, 222], [17776, 222], [18076, 222], [18376, 222], [18676, 222], [18976, 222], [19276, 222], [19576, 222], [19876, 222], [20176, 222], [20476, 222], [20776, 222], [21076, 222], [21376, 222], [21676, 222], [21976, 222], [22276, 222], [22576, 222], [22876, 222], [23176, 222], [23476, 222], [23776, 222], [24076, 222], [24376, 222], [24676, 222], [24976, 222], [25276, 222], [25576, 222], [25876, 222], [26176, 222], [26476, 222], [26776, 222], [27076, 222], [27376, 222], [27676, 222], [27976, 222], [28276, 222], [28576, 222], [28876, 222], [29176, 222], [29476, 222], [29776, 222], [30076, 222], [30376, 222], [30676, 222], [30976, 222], [31276, 222], [31576, 222], [31876, 222], [32176, 222], [32477, 219], [32778, 215], [33080, 208], [33381, 209], [33681, 209], [33981, 209], [34281, 209], [34582, 205], [34884, 199], [35186, 193], [35488, 187], [35790, 183], [36092, 181], [36394, 169], [36564, 9], [36696, 163], [36864, 9], [36998, 157], [37164, 9]], "point": [186, 61]}}, "high_idx": 7}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.15|+01.65|-01.61"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [76, 1, 297, 124], "mask": [[76, 222], [376, 222], [676, 222], [976, 222], [1276, 222], [1576, 222], [1876, 222], [2176, 222], [2476, 222], [2776, 222], [3076, 222], [3376, 222], [3676, 222], [3976, 222], [4276, 222], [4576, 222], [4876, 222], [5176, 222], [5476, 222], [5776, 222], [6076, 222], [6376, 222], [6676, 222], [6976, 222], [7276, 222], [7576, 222], [7876, 222], [8176, 222], [8476, 222], [8776, 222], [9076, 222], [9376, 222], [9676, 222], [9976, 222], [10276, 222], [10576, 222], [10876, 222], [11176, 222], [11476, 222], [11776, 222], [12076, 222], [12376, 222], [12676, 222], [12976, 222], [13276, 222], [13576, 222], [13876, 222], [14176, 222], [14476, 222], [14776, 222], [15076, 222], [15376, 222], [15676, 222], [15976, 222], [16276, 222], [16576, 222], [16876, 222], [17176, 222], [17476, 222], [17776, 222], [18076, 222], [18376, 222], [18676, 222], [18976, 222], [19276, 222], [19576, 222], [19876, 222], [20176, 222], [20476, 222], [20776, 222], [21076, 222], [21376, 222], [21676, 222], [21976, 222], [22276, 222], [22576, 222], [22876, 222], [23176, 222], [23476, 222], [23776, 222], [24076, 222], [24376, 222], [24676, 222], [24976, 222], [25276, 222], [25576, 222], [25876, 222], [26176, 222], [26476, 222], [26776, 222], [27076, 222], [27376, 222], [27676, 222], [27976, 222], [28276, 222], [28576, 222], [28876, 222], [29176, 222], [29476, 222], [29776, 222], [30076, 222], [30376, 222], [30676, 222], [30976, 222], [31276, 222], [31576, 222], [31876, 222], [32176, 222], [32477, 219], [32778, 215], [33080, 208], [33381, 209], [33681, 209], [33981, 209], [34281, 209], [34582, 205], [34884, 199], [35186, 193], [35488, 187], [35790, 183], [36092, 181], [36394, 169], [36564, 9], [36696, 163], [36864, 9], [36998, 157], [37164, 9]], "point": [186, 61]}}, "high_idx": 7}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.96|+01.00|-00.94|BreadSliced_2"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [117, 40, 173, 90], "mask": [[11839, 13], [12136, 20], [12433, 26], [12731, 32], [13030, 34], [13329, 36], [13628, 38], [13926, 42], [14226, 42], [14526, 42], [14825, 43], [15125, 43], [15425, 43], [15724, 45], [16024, 46], [16323, 48], [16621, 50], [16921, 50], [17220, 51], [17520, 51], [17820, 52], [18119, 53], [18419, 53], [18719, 53], [19018, 54], [19318, 55], [19618, 55], [19917, 56], [20217, 56], [20517, 57], [20817, 57], [21117, 57], [21417, 57], [21717, 57], [22017, 57], [22317, 57], [22617, 57], [22917, 57], [23217, 57], [23517, 57], [23817, 57], [24117, 57], [24417, 57], [24717, 57], [25017, 57], [25317, 57], [25617, 56], [25918, 55], [26218, 55], [26519, 52], [26823, 47]], "point": [145, 64]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.15|+01.65|-01.61"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 297, 124], "mask": [[0, 81], [252, 46], [300, 81], [551, 47], [600, 81], [851, 47], [900, 82], [1150, 48], [1200, 82], [1449, 49], [1500, 298], [1800, 298], [2100, 298], [2400, 298], [2700, 298], [3000, 298], [3300, 298], [3600, 298], [3900, 298], [4200, 298], [4500, 298], [4800, 298], [5100, 298], [5400, 298], [5700, 298], [6000, 298], [6300, 298], [6600, 298], [6900, 298], [7200, 298], [7500, 298], [7800, 298], [8100, 298], [8400, 298], [8700, 298], [9000, 298], [9300, 298], [9600, 298], [9900, 298], [10200, 298], [10500, 298], [10800, 298], [11100, 298], [11400, 298], [11700, 298], [12000, 298], [12300, 298], [12600, 298], [12900, 298], [13200, 298], [13500, 298], [13800, 298], [14100, 298], [14400, 298], [14700, 298], [15000, 298], [15300, 298], [15600, 298], [15900, 298], [16200, 298], [16500, 298], [16800, 298], [17100, 298], [17400, 298], [17700, 298], [18000, 298], [18300, 298], [18600, 298], [18900, 298], [19200, 298], [19500, 298], [19800, 298], [20100, 298], [20400, 298], [20700, 298], [21000, 298], [21300, 298], [21600, 298], [21900, 298], [22200, 298], [22500, 298], [22800, 298], [23100, 298], [23401, 297], [23703, 295], [24005, 293], [24307, 291], [24609, 289], [24912, 286], [25214, 284], [25516, 282], [25818, 280], [26120, 278], [26422, 276], [26724, 274], [27026, 272], [27328, 270], [27630, 268], [27932, 266], [28234, 264], [28536, 262], [28839, 259], [29141, 257], [29443, 255], [29745, 253], [30047, 251], [30349, 249], [30651, 247], [30953, 245], [31255, 243], [31557, 241], [31859, 239], [32161, 237], [32464, 232], [32766, 227], [33068, 220], [33381, 209], [33681, 209], [33981, 209], [34281, 209], [34582, 205], [34884, 199], [35186, 193], [35488, 187], [35790, 183], [36092, 181], [36394, 169], [36564, 9], [36696, 163], [36864, 9], [36998, 157], [37164, 9]], "point": [148, 61]}}, "high_idx": 7}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.96|+01.00|-00.94|BreadSliced_2", "placeStationary": true, "receptacleObjectId": "DiningTable|-02.66|+00.83|+03.21"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 112, 261, 300], "mask": [[33416, 8], [33712, 18], [33991, 1], [34012, 18], [34282, 4], [34311, 19], [34352, 6], [34574, 5], [34582, 4], [34610, 20], [34652, 12], [34868, 11], [34883, 3], [34917, 6], [34952, 18], [35164, 16], [35184, 3], [35252, 21], [35460, 21], [35484, 4], [35552, 25], [35756, 25], [35785, 3], [35851, 29], [36051, 31], [36086, 3], [36151, 33], [36347, 36], [36386, 3], [36429, 1], [36450, 37], [36643, 40], [36687, 3], [36724, 1], [36727, 3], [36745, 46], [36940, 44], [36988, 2], [37024, 6], [37045, 49], [37237, 48], [37289, 1], [37325, 5], [37345, 51], [37535, 50], [37589, 2], [37625, 5], [37645, 53], [37832, 54], [37890, 1], [37925, 5], [37945, 53], [38129, 58], [38191, 1], [38225, 5], [38245, 53], [38426, 62], [38526, 5], [38545, 52], [38724, 63], [38826, 5], [38845, 52], [39022, 63], [39126, 5], [39145, 52], [39322, 45], [39381, 3], [39426, 5], [39445, 52], [39622, 41], [39726, 5], [39745, 52], [39811, 3], [39922, 38], [40026, 5], [40044, 53], [40111, 4], [40223, 35], [40326, 7], [40343, 54], [40410, 7], [40626, 11], [40638, 59], [40710, 8], [40926, 71], [41010, 10], [41226, 71], [41310, 11], [41404, 1], [41526, 71], [41610, 12], [41702, 3], [41826, 71], [41910, 14], [42000, 5], [42126, 71], [42209, 16], [42300, 5], [42426, 71], [42509, 18], [42600, 6], [42725, 72], [42809, 19], [42900, 5], [43025, 72], [43109, 21], [43200, 5], [43325, 72], [43408, 23], [43500, 4], [43625, 72], [43708, 24], [43800, 4], [43876, 3], [43925, 73], [44008, 26], [44100, 4], [44172, 7], [44225, 73], [44308, 27], [44400, 4], [44464, 16], [44524, 75], [44607, 30], [44700, 4], [44764, 16], [44824, 75], [44907, 31], [45000, 4], [45064, 16], [45124, 76], [45206, 32], [45300, 4], [45364, 16], [45424, 79], [45504, 35], [45600, 4], [45664, 13], [45680, 1], [45724, 116], [45900, 4], [45965, 12], [45980, 1], [45983, 1], [46024, 117], [46200, 4], [46265, 11], [46279, 2], [46282, 2], [46323, 119], [46500, 4], [46565, 11], [46578, 3], [46582, 2], [46623, 120], [46800, 4], [46865, 10], [46878, 6], [46923, 121], [47100, 4], [47164, 11], [47177, 8], [47223, 121], [47400, 4], [47464, 10], [47477, 8], [47523, 122], [47700, 5], [47764, 10], [47776, 9], [47823, 31], [47857, 89], [48000, 5], [48063, 10], [48076, 9], [48123, 29], [48157, 90], [48300, 5], [48363, 9], [48375, 11], [48423, 27], [48456, 92], [48600, 5], [48662, 10], [48675, 11], [48723, 24], [48754, 95], [48900, 5], [48962, 10], [48974, 12], [49023, 22], [49051, 98], [49200, 6], [49261, 11], [49273, 13], [49324, 19], [49349, 101], [49500, 6], [49561, 10], [49573, 13], [49625, 16], [49645, 106], [49800, 6], [49860, 11], [49873, 14], [49925, 13], [49942, 110], [50100, 7], [50159, 11], [50172, 14], [50225, 11], [50239, 114], [50400, 7], [50458, 12], [50472, 14], [50526, 7], [50537, 116], [50700, 8], [50758, 12], [50771, 14], [50825, 5], [50837, 20], [50864, 90], [51000, 8], [51056, 13], [51071, 14], [51125, 3], [51137, 18], [51166, 88], [51300, 42], [51343, 6], [51350, 19], [51371, 14], [51425, 1], [51437, 17], [51467, 87], [51600, 68], [51670, 15], [51724, 1], [51736, 17], [51768, 87], [51900, 68], [51970, 15], [52024, 1], [52035, 18], [52068, 87], [52200, 67], [52270, 16], [52323, 3], [52333, 20], [52369, 86], [52500, 67], [52569, 17], [52622, 31], [52669, 87], [52800, 65], [52870, 17], [52921, 32], [52970, 86], [53100, 61], [53173, 15], [53220, 34], [53270, 87], [53400, 59], [53473, 16], [53519, 36], [53572, 85], [53700, 59], [53773, 17], [53818, 39], [53874, 83], [54000, 58], [54072, 20], [54116, 39], [54174, 84], [54300, 57], [54372, 22], [54413, 42], [54466, 2], [54472, 86], [54600, 57], [54671, 26], [54710, 44], [54767, 2], [54771, 88], [54900, 56], [54970, 84], [55067, 92], [55200, 55], [55270, 84], [55366, 18], [55385, 74], [55500, 154], [55666, 12], [55684, 76], [55800, 154], [55966, 9], [55983, 77], [56100, 154], [56266, 6], [56282, 78], [56400, 154], [56567, 2], [56580, 81], [56700, 153], [56879, 82], [57000, 153], [57178, 84], [57300, 152], [57476, 86], [57600, 152], [57775, 87], [57900, 151], [58073, 89], [58200, 150], [58373, 89], [58500, 150], [58674, 88], [58800, 149], [58974, 88], [59100, 149], [59275, 87], [59400, 148], [59575, 87], [59700, 147], [59875, 86], [60000, 145], [60175, 86], [60300, 144], [60475, 86], [60600, 144], [60775, 86], [60901, 56], [60964, 81], [61075, 86], [61201, 55], [61272, 74], [61375, 86], [61502, 54], [61575, 71], [61676, 85], [61803, 52], [61876, 68], [61976, 85], [62103, 52], [62176, 66], [62276, 85], [62404, 50], [62476, 23], [62526, 14], [62576, 85], [62704, 50], [62831, 6], [62876, 85], [63004, 50], [63092, 11], [63131, 2], [63176, 85], [63304, 49], [63376, 54], [63475, 86], [63604, 49], [63675, 51], [63775, 86], [63904, 49], [63974, 48], [64075, 86], [64203, 54], [64273, 46], [64377, 83], [64502, 63], [64572, 44], [64679, 81], [64803, 112], [64980, 80], [65104, 110], [65281, 79], [65405, 108], [65582, 78], [65705, 106], [65883, 77], [66006, 104], [66184, 76], [66306, 103], [66486, 74], [66607, 101], [66787, 73], [66907, 100], [67088, 71], [67200, 1], [67208, 98], [67388, 71], [67500, 2], [67508, 98], [67689, 70], [67800, 3], [67809, 96], [67989, 70], [68100, 4], [68109, 96], [68290, 68], [68400, 5], [68410, 95], [68590, 68], [68700, 5], [68710, 95], [68891, 66], [69000, 6], [69011, 93], [69191, 66], [69300, 7], [69312, 92], [69492, 64], [69600, 7], [69612, 92], [69793, 63], [69900, 8], [69913, 91], [70093, 62], [70200, 8], [70214, 90], [70394, 61], [70500, 9], [70514, 89], [70694, 60], [70800, 9], [70815, 88], [70994, 59], [71100, 10], [71116, 86], [71294, 59], [71400, 10], [71416, 85], [71595, 57], [71700, 10], [71717, 84], [71896, 56], [72000, 10], [72018, 82], [72197, 54], [72300, 11], [72319, 81], [72498, 53], [72600, 11], [72619, 81], [72799, 51], [72900, 11], [72920, 79], [73100, 50], [73200, 12], [73221, 78], [73401, 48], [73500, 12], [73521, 77], [73701, 48], [73800, 13], [73822, 76], [74002, 46], [74100, 13], [74123, 75], [74302, 46], [74400, 13], [74423, 74], [74602, 45], [74700, 14], [74724, 73], [74903, 44], [75000, 14], [75025, 72], [75203, 43], [75300, 15], [75325, 72], [75504, 41], [75600, 16], [75626, 70], [75804, 41], [75900, 17], [75926, 70], [76105, 39], [76200, 18], [76227, 69], [76405, 39], [76500, 19], [76528, 68], [76706, 37], [76800, 21], [76829, 66], [77006, 37], [77100, 22], [77129, 66], [77306, 36], [77400, 23], [77429, 66], [77606, 36], [77700, 25], [77729, 66], [77907, 34], [78000, 94], [78207, 34], [78300, 94], [78507, 33], [78600, 94], [78807, 32], [78900, 94], [79107, 31], [79200, 93], [79408, 29], [79500, 93], [79708, 28], [79800, 93], [80008, 27], [80100, 93], [80308, 26], [80400, 92], [80609, 23], [80700, 92], [80909, 22], [81000, 92], [81209, 21], [81300, 92], [81509, 20], [81600, 92], [81809, 19], [81900, 91], [82110, 17], [82200, 91], [82410, 16], [82500, 91], [82710, 14], [82800, 91], [83010, 13], [83100, 91], [83310, 12], [83400, 90], [83610, 11], [83700, 90], [83910, 10], [84000, 90], [84210, 9], [84300, 90], [84510, 7], [84600, 90], [84810, 6], [84900, 90], [85110, 5], [85200, 90], [85410, 4], [85500, 90], [85710, 3], [85800, 90], [86010, 2], [86100, 90], [86310, 1], [86400, 90], [86700, 90], [87000, 90], [87300, 90], [87600, 90], [87900, 90], [88200, 90], [88500, 90], [88800, 90], [89100, 90], [89400, 90], [89700, 90]], "point": [130, 205]}}, "high_idx": 9}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan7", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -3.0, "y": 0.9009992, "z": 0.25}, "object_poses": [{"objectName": "Pan_aa05aa65", "position": {"x": -1.96116972, "y": 0.9117095, "z": -1.2658236}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_628dcab4", "position": {"x": 0.289695054, "y": 0.930619836, "z": -1.64601612}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_628dcab4", "position": {"x": -3.12239361, "y": 0.8748353, "z": 3.475125}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}, {"objectName": "WineBottle_471b4432", "position": {"x": -2.51074529, "y": 0.875721931, "z": 3.72847676}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_471b4432", "position": {"x": -2.32146621, "y": 0.875721931, "z": 3.073422}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_759492dd", "position": {"x": 0.06952658, "y": 0.943641961, "z": -1.42918253}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_759492dd", "position": {"x": -2.65919971, "y": 0.8935903, "z": 3.37033081}, "rotation": {"x": 0.0, "y": 4.829673e-06, "z": 0.0}}, {"objectName": "Fork_6f28e6b4", "position": {"x": 1.68764734, "y": 0.9106485, "z": -1.34649062}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_6f28e6b4", "position": {"x": 0.124568678, "y": 0.929190636, "z": -1.57373857}, "rotation": {"x": 0.0, "y": 6.83018925e-06, "z": 0.0}}, {"objectName": "Spoon_b16915eb", "position": {"x": -2.07234931, "y": 0.9132294, "z": 0.0378235579}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_da95a06a", "position": {"x": -2.14351916, "y": 0.876974463, "z": 3.02111435}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}, {"objectName": "Cup_da95a06a", "position": {"x": 0.0144844772, "y": 0.9289965, "z": -1.718294}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_82e11e5b", "position": {"x": -2.42038536, "y": 0.8827651, "z": 3.46069026}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Plate_82e11e5b", "position": {"x": -1.61283481, "y": 0.12375319, "z": -1.06641221}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_f7b250a5", "position": {"x": -2.733427, "y": 0.8967859, "z": 2.88414288}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_f7b250a5", "position": {"x": 1.76045752, "y": 0.932092667, "z": -1.528}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_30c4ced6", "position": {"x": -2.51074529, "y": 0.8752368, "z": 2.715276}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_30c4ced6", "position": {"x": -3.122394, "y": 0.8752368, "z": 2.96862864}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Bowl_9c57f915", "position": {"x": 3.63793278, "y": 0.5510504, "z": 1.44952691}, "rotation": {"x": 359.961, "y": 270.000061, "z": 0.03900441}}, {"objectName": "SaltShaker_ba969788", "position": {"x": 0.674581, "y": 0.112981141, "z": -1.48301554}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_32949c32", "position": {"x": 3.84502649, "y": 0.5490939, "z": 1.449528}, "rotation": {"x": 359.961, "y": 270.000061, "z": 0.03900441}}, {"objectName": "PepperShaker_32949c32", "position": {"x": -0.181993663, "y": 0.11800611, "z": -1.41324353}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_403e6980", "position": {"x": 0.5712522, "y": 0.9955075, "z": -1.77665782}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_403e6980", "position": {"x": -2.490333, "y": 0.9619979, "z": 3.22187638}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_403e6980", "position": {"x": -1.96116972, "y": 0.995507538, "z": -0.9399118}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_e4d9d6fe", "position": {"x": -0.33014673, "y": 0.7027583, "z": -1.57347214}, "rotation": {"x": 1.40334191e-14, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_23f7a2f7", "position": {"x": 0.709113, "y": 0.967284739, "z": -1.26742041}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_23f7a2f7", "position": {"x": 0.07906264, "y": 1.19902861, "z": 2.2069037}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Book_7abfb00f", "position": {"x": -0.9994687, "y": 1.65113246, "z": -1.71275}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_6f28e6b4", "position": {"x": 0.6387758, "y": 0.7190049, "z": -1.41019988}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_d317e149", "position": {"x": -0.03499955, "y": 0.85017705, "z": 2.2517426}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Egg_bdc82745", "position": {"x": -0.03500092, "y": 0.828299165, "z": 2.02334762}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Kettle_ef41be6a", "position": {"x": 0.9741, "y": 0.9576474, "z": -1.4196}, "rotation": {"x": 0.0, "y": 180.000061, "z": 0.0}}, {"objectName": "Vase_2fddf4c6", "position": {"x": 3.671401, "y": 0.168322966, "z": 1.84175575}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_da95a06a", "position": {"x": 0.289695024, "y": 0.927026153, "z": -1.50146055}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "WineBottle_471b4432", "position": {"x": 0.5499594, "y": 0.11760366, "z": -1.37086713}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_82e11e5b", "position": {"x": -1.83179677, "y": 1.66167462, "z": -1.674016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_759492dd", "position": {"x": -2.99693346, "y": 0.8935903, "z": 3.14764929}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_92b0ae81", "position": {"x": 1.57843232, "y": 0.9467783, "z": -1.255736}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_fcc17e74", "position": {"x": -1.60200489, "y": 0.901, "z": -0.172676325}, "rotation": {"x": 0.0, "y": 308.1196, "z": 0.0}}, {"objectName": "Knife_f7b250a5", "position": {"x": -2.95050049, "y": 0.9005831, "z": 3.035549}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "SaltShaker_ba969788", "position": {"x": -3.01742029, "y": 0.8748966, "z": 3.58009815}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}, {"objectName": "PepperShaker_32949c32", "position": {"x": -1.6496563, "y": 0.115884662, "z": -0.841544}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e46f4ce8", "position": {"x": -1.51645112, "y": 0.977197945, "z": -1.2658236}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_628dcab4", "position": {"x": -1.96116972, "y": 0.9121421, "z": -0.614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_403e6980", "position": {"x": -2.65919971, "y": 0.9619979, "z": 3.55961}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_b78f3af0", "position": {"x": -1.55786967, "y": 1.658576, "z": -1.74787152}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_bd179bb7", "position": {"x": 3.662063, "y": 0.880460143, "z": 1.75926125}, "rotation": {"x": 359.961, "y": 270.000061, "z": 0.03900441}}, {"objectName": "Apple_23f7a2f7", "position": {"x": -0.3675316, "y": 0.752335548, "z": -1.46836066}, "rotation": {"x": 1.40334191e-14, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_aa05aa65", "position": {"x": 1.3286, "y": 0.9576474, "z": -1.4201}, "rotation": {"x": 0.0, "y": 180.000061, "z": 0.0}}, {"objectName": "Spoon_b16915eb", "position": {"x": -2.72612023, "y": 0.8797198, "z": 3.049983}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "SoapBottle_30c4ced6", "position": {"x": -0.8448454, "y": 0.1931465, "z": 2.136}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_e4d9d6fe", "position": {"x": -0.3771889, "y": 1.56723821, "z": 1.92186511}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Statue_c0a5928e", "position": {"x": 3.676045, "y": 0.1683198, "z": 1.410345}, "rotation": {"x": 0.0, "y": 334.521, "z": 0.0}}, {"objectName": "Statue_bf0c7315", "position": {"x": -0.833000541, "y": 0.910897, "z": -1.572963}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9c57f915", "position": {"x": 0.079062, "y": 1.57036, "z": 2.10055065}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}], "object_toggles": [], "random_seed": 2420372502, "scene_num": 7}, "task_id": "trial_T20190906_200758_556616", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3HL2LL0LEPZT8_35DR22AR5G1TXLXKA4DDU3I7QSV3XK", "high_descs": ["Turn left, go to the round table.", "Take the knife closest to the blue right bottle from the table.", "Turn around, go to the door, turn left at the last stool to face the counter.", "Cut the bread on the counter into slices.", "Put the knife on the counter in front of the pan.", "Take a slice of bread from the counter.", "Turn left, go straight, when the counter ends turn right, go straight and to the right to the oven.", "Put the bread in the microwave above the oven. Turn the microwave on. Take the bread from the microwave.", "Turn right, go straight, turn right, go straight, at the end of the counter turn left, go to the window, turn right, go between the wall and the table and face the table.", "Put the bread to the left of the closest blue bottle on the table."], "task_desc": "Put a heated piece of bread on a table.", "votes": [1, 1]}, {"assignment_id": "ACSS93E03ZUGX_39LOEL67OVM3IKWC2W1LE41EZJE839", "high_descs": ["Turn around and walk towards the kitchen table.", "Pick the knife up from off of the table.", "Turn around, walk to the end of the room and turn left to face the counter.", "Slice the bread that's on the counter with the knife.", "Place the knife on the metal pan on the counter.", "Pick up a slice of bread from the counter.", "Turn to the left and walk around the counter to face the microwave above the stove.", "Open the microwave, place the slice of bread inside, close the microwave, turn on the microwave, wait a second, open the microwave and remove the slice of bread.", "Turn around and walk across the room to stand by the kitchen table.", "Place the slice of bread on the kitchen table."], "task_desc": "Place a slice of microwaved bread on the kitchen table.", "votes": [1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3QBD8R3Z240RL82W9RV0LVJKSLF4OO", "high_descs": ["turn around and walk to the dining table at the end of the room", "grab a knife off of the dining table", "turn around and walk to the end of the room, then turn left to face the kitchen counter", "slice the bread loaf on the counter", "place the knife down on the counter", "grab a slice of bread off of the counter", "turn around and walk to the end of the kitchen counter on the right, then turn right and walk a bit past the kitchen fridge on the left, then hang another right and walk over to the stove ahead", "place the slice of bread in the microwave above the kitchen stove, microwave it, then take it back out", "turn right and walk to the end of the kitchen counter on the left, then turn right and walk over to the kitchen table in the corner of the room", "place the slice of bread on top of the kitchen table"], "task_desc": "place a microwaved slice of bread on top of the kitchen table", "votes": [1, 1]}]}}