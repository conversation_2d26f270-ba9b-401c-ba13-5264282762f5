{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000323.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000324.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000325.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000326.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000327.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000328.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000329.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000330.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000331.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000332.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000333.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000334.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000335.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000336.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000337.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000338.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000339.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000340.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000341.png", "low_idx": 55}, {"high_idx": 5, "image_name": "000000342.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000343.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000344.png", "low_idx": 56}, {"high_idx": 5, "image_name": "000000345.png", "low_idx": 56}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|14|1|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-0.96991992, -0.96991992, 13.91699792, 13.91699792, 4.27766512, 4.27766512]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-1.298918724, -1.298918724, 14.412, 14.412, -0.00599211456, -0.00599211456]], "forceVisible": true, "objectId": "Cup|-00.24|+01.07|+03.48"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-11|10|2|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|15|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-0.96991992, -0.96991992, 13.91699792, 13.91699792, 4.27766512, 4.27766512]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-1.298918724, -1.298918724, 14.412, 14.412, -0.00599211456, -0.00599211456]], "forceVisible": true, "objectId": "Cup|-00.24|+01.07|+03.48", "receptacleObjectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 90000]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.24|+01.07|+03.48"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [138, 142, 174, 174], "mask": [[42438, 37], [42738, 37], [43038, 36], [43338, 36], [43639, 35], [43939, 35], [44239, 34], [44539, 34], [44839, 34], [45140, 33], [45440, 33], [45740, 32], [46041, 31], [46341, 30], [46642, 29], [46942, 28], [47243, 27], [47543, 27], [47843, 26], [48143, 26], [48443, 26], [48744, 25], [49044, 24], [49344, 24], [49644, 24], [49944, 24], [50244, 24], [50544, 24], [50845, 22], [51146, 20], [51448, 17], [51750, 12], [52056, 1]], "point": [156, 157]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 147], [234, 215], [532, 218], [830, 16512], [17345, 291], [17651, 278], [17930, 1], [17952, 276], [18256, 270], [18557, 267], [18859, 264], [19160, 262], [19461, 106], [19570, 151], [19761, 95], [19879, 142], [20062, 89], [20181, 139], [20362, 86], [20481, 139], [20662, 85], [20781, 138], [20963, 83], [21082, 137], [21263, 83], [21386, 133], [21563, 83], [21687, 131], [21863, 84], [21988, 130], [22163, 84], [22282, 2], [22289, 129], [22463, 84], [22582, 3], [22589, 129], [22763, 84], [22882, 4], [22890, 127], [23063, 85], [23182, 4], [23190, 127], [23363, 85], [23482, 5], [23490, 127], [23663, 85], [23782, 5], [23790, 127], [23963, 85], [24082, 5], [24090, 127], [24262, 87], [24382, 5], [24390, 127], [24562, 87], [24683, 4], [24690, 127], [24862, 87], [24983, 4], [24990, 127], [25162, 87], [25283, 4], [25290, 127], [25461, 89], [25583, 4], [25590, 128], [25761, 89], [25883, 4], [25889, 129], [26060, 90], [26183, 3], [26189, 129], [26360, 90], [26483, 3], [26489, 129], [26659, 92], [26783, 3], [26789, 130], [26958, 93], [27083, 3], [27089, 130], [27258, 93], [27383, 3], [27388, 132], [27557, 94], [27683, 2], [27688, 133], [27856, 96], [27983, 2], [27988, 134], [28155, 97], [28283, 1], [28287, 136], [28454, 98], [28587, 138], [28753, 99], [28886, 141], [29051, 102], [29186, 144], [29349, 104], [29485, 154], [29644, 109], [29784, 269], [30084, 270], [30384, 270], [30684, 270], [30984, 270], [31284, 271], [31584, 271], [31883, 272], [32182, 274], [32481, 276], [32778, 281], [33075, 11932], [45010, 295], [45314, 289], [45615, 287], [45916, 285], [46217, 283], [46517, 283], [46818, 282], [47119, 281], [47419, 281], [47719, 281], [48019, 281], [48319, 281], [48619, 281], [48919, 281], [49219, 281], [49518, 282], [49818, 282], [50117, 283], [50417, 283], [50716, 284], [51016, 284], [51315, 285], [51615, 285], [51914, 5], [51920, 128], [52052, 148], [52213, 6], [52227, 111], [52362, 138], [52512, 3], [52534, 98], [52668, 132], [52836, 94], [52970, 130], [53138, 89], [53273, 127], [53440, 85], [53575, 125], [53741, 81], [53878, 122], [54042, 77], [54181, 119], [54343, 75], [54482, 118], [54644, 73], [54783, 117], [54944, 71], [55085, 115], [55245, 69], [55386, 114], [55545, 68], [55687, 113], [55846, 65], [55989, 111], [56146, 64], [56290, 110], [56446, 63], [56591, 109], [56747, 60], [56893, 107], [57047, 60], [57193, 107], [57347, 59], [57494, 106], [57647, 58], [57795, 105], [57947, 58], [58095, 105], [58247, 57], [58396, 104], [58547, 57], [58696, 104], [58847, 56], [58997, 103], [59147, 56], [59297, 103], [59446, 56], [59598, 102], [59746, 56], [59898, 102], [60046, 55], [60199, 101], [60346, 55], [60499, 101], [60645, 55], [60800, 100], [60945, 55], [61100, 100], [61245, 55], [61400, 100], [61544, 56], [61700, 100], [61844, 56], [62000, 100], [62143, 57], [62300, 100], [62443, 58], [62599, 101], [62742, 59], [62899, 101], [63041, 60], [63199, 101], [63341, 60], [63499, 101], [63640, 61], [63799, 101], [63939, 62], [64099, 101], [64238, 63], [64399, 101], [64536, 65], [64699, 101], [64835, 66], [64999, 101], [65133, 68], [65299, 102], [65432, 70], [65598, 105], [65729, 73], [65898, 107], [66027, 75], [66198, 110], [66323, 80], [66497, 117], [66615, 88], [66797, 206], [67097, 207], [67396, 208], [67696, 208], [67996, 209], [68295, 210], [68595, 210], [68895, 211], [69194, 212], [69494, 212], [69794, 213], [70093, 214], [70393, 214], [70693, 215], [70992, 216], [71292, 216], [71592, 217], [71891, 219], [72190, 220], [72490, 221], [72789, 223], [73088, 225], [73387, 226], [73687, 227], [73986, 229], [74285, 230], [74585, 231], [74884, 232], [75184, 233], [75483, 234], [75783, 235], [76082, 236], [76382, 236], [76682, 237], [76981, 238], [77281, 238], [77581, 239], [77880, 240], [78180, 241], [78479, 242], [78779, 242], [79079, 242], [79379, 243], [79678, 244], [79978, 244], [80278, 244], [80578, 244], [80878, 245], [81177, 247], [81476, 248], [81776, 249], [82075, 251], [82374, 252], [82674, 253], [82973, 255], [83272, 257], [83571, 259], [83870, 262], [84168, 265], [84467, 268], [84765, 271], [85064, 274], [85362, 281], [85657, 4343]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.24|+01.07|+03.48", "placeStationary": true, "receptacleObjectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 125], [14226, 138], [14400, 109], [14544, 120], [14700, 106], [14849, 115], [15000, 105], [15150, 114], [15300, 105], [15450, 114], [15600, 106], [15750, 114], [15900, 106], [16050, 114], [16200, 106], [16350, 114], [16500, 106], [16650, 114], [16800, 106], [16949, 114], [17100, 107], [17249, 114], [17400, 107], [17549, 114], [17700, 107], [17849, 114], [18000, 107], [18149, 114], [18300, 107], [18449, 114], [18600, 107], [18749, 114], [18900, 108], [19048, 115], [19200, 108], [19348, 115], [19500, 108], [19648, 114], [19800, 109], [19947, 115], [20100, 109], [20247, 115], [20400, 110], [20546, 116], [20700, 110], [20846, 116], [21000, 111], [21145, 117], [21300, 111], [21445, 117], [21600, 111], [21745, 117], [21900, 111], [22044, 118], [22200, 112], [22344, 117], [22500, 112], [22644, 117], [22800, 112], [22944, 117], [23100, 112], [23244, 117], [23400, 112], [23544, 117], [23700, 113], [23843, 118], [24000, 113], [24143, 118], [24300, 113], [24443, 118], [24600, 113], [24743, 118], [24900, 113], [25043, 118], [25200, 114], [25343, 117], [25500, 114], [25643, 117], [25800, 114], [25943, 117], [26100, 114], [26243, 117], [26400, 114], [26543, 117], [26700, 114], [26843, 117], [27000, 114], [27143, 117], [27300, 124], [27431, 129], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [145, 70]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.24|+01.07|+03.48"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [105, 48, 149, 92], "mask": [[14225, 1], [14509, 35], [14806, 43], [15105, 45], [15405, 45], [15706, 44], [16006, 44], [16306, 44], [16606, 44], [16906, 43], [17207, 42], [17507, 42], [17807, 42], [18107, 42], [18407, 42], [18707, 42], [19008, 40], [19308, 40], [19608, 40], [19909, 38], [20209, 38], [20510, 36], [20810, 36], [21111, 34], [21411, 34], [21711, 34], [22011, 33], [22312, 32], [22612, 32], [22912, 32], [23212, 32], [23512, 32], [23813, 30], [24113, 30], [24413, 30], [24713, 30], [25013, 30], [25314, 29], [25614, 29], [25914, 29], [26214, 29], [26514, 29], [26814, 29], [27114, 29], [27424, 7]], "point": [127, 69]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 199], "mask": [[0, 18600], [18601, 299], [18901, 299], [19202, 298], [19503, 297], [19803, 297], [20104, 296], [20405, 295], [20705, 295], [21006, 294], [21307, 293], [21607, 293], [21908, 292], [22208, 292], [22509, 291], [22810, 290], [23110, 290], [23411, 289], [23712, 288], [24012, 288], [24313, 287], [24613, 287], [24914, 286], [25215, 285], [25515, 285], [25816, 284], [26117, 283], [26417, 283], [26718, 282], [27018, 282], [27319, 281], [27620, 280], [27920, 280], [28221, 279], [28522, 278], [28822, 278], [29123, 277], [29423, 277], [29724, 276], [30025, 275], [30325, 275], [30626, 274], [30927, 273], [31227, 273], [31528, 272], [31828, 272], [32129, 271], [32430, 270], [32730, 270], [33031, 269], [33332, 268], [33632, 268], [33933, 267], [34234, 266], [34534, 266], [34835, 265], [35135, 265], [35439, 261], [35739, 261], [36040, 260], [36339, 261], [36639, 261], [36939, 261], [37238, 262], [37539, 261], [37839, 261], [38142, 258], [38442, 258], [38743, 257], [39044, 256], [39344, 256], [39645, 255], [39945, 255], [40246, 254], [40547, 253], [40847, 253], [41148, 252], [41449, 251], [41749, 251], [42050, 250], [42350, 250], [42651, 249], [42952, 248], [43252, 248], [43553, 247], [43854, 246], [44154, 246], [44455, 245], [44755, 245], [45056, 244], [45357, 243], [45657, 243], [45958, 242], [46259, 241], [46559, 241], [46860, 240], [47160, 240], [47461, 239], [47762, 237], [48062, 236], [48363, 234], [48664, 232], [48964, 231], [49265, 229], [49565, 228], [49866, 225], [50167, 223], [50467, 222], [50768, 220], [51069, 218], [51369, 217], [51670, 215], [51971, 213], [52271, 68], [52361, 122], [52572, 60], [52668, 114], [52872, 55], [52973, 108], [53173, 50], [53277, 103], [53474, 46], [53580, 99], [53774, 44], [53882, 96], [54075, 41], [54184, 93], [54376, 38], [54486, 90], [54676, 35], [54789, 85], [54977, 33], [55090, 83], [55277, 32], [55391, 81], [55578, 30], [55692, 79], [55879, 28], [55993, 77], [56179, 27], [56294, 75], [56480, 26], [56594, 74], [56781, 24], [56895, 72], [57081, 23], [57196, 70], [57384, 20], [57496, 67], [57684, 11], [57796, 32], [57851, 12], [57984, 11], [58151, 12], [58284, 10], [58451, 12], [58584, 10], [58752, 11], [58885, 9], [59052, 10], [59185, 9], [59352, 10], [59485, 9], [59652, 10]], "point": [149, 99]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.24|+01.07|+03.48", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 266], "mask": [[0, 96], [138, 258], [438, 258], [738, 258], [1038, 258], [1337, 260], [1637, 260], [1937, 260], [2237, 260], [2537, 261], [2836, 262], [3136, 263], [3436, 263], [3736, 264], [4035, 265], [4335, 266], [4634, 267], [4934, 268], [5233, 270], [5532, 272], [5831, 274], [6130, 276], [6428, 281], [6727, 285], [7024, 29015], [36040, 598], [36639, 299], [36939, 599], [37539, 298], [37839, 298], [38142, 295], [38442, 295], [38743, 293], [39044, 292], [39344, 292], [39645, 291], [39945, 290], [40246, 289], [40547, 288], [40847, 287], [41148, 286], [41449, 285], [41749, 285], [42050, 283], [42350, 283], [42651, 282], [42952, 281], [43252, 280], [43553, 279], [43854, 278], [44154, 278], [44455, 276], [44755, 276], [45056, 275], [45357, 274], [45657, 273], [45958, 272], [46259, 271], [46559, 270], [46860, 269], [47160, 269], [47461, 268], [47762, 237], [48000, 28], [48062, 236], [48300, 28], [48363, 234], [48600, 28], [48664, 232], [48900, 28], [48964, 231], [49200, 27], [49265, 229], [49500, 27], [49565, 228], [49800, 27], [49866, 225], [50100, 27], [50167, 223], [50400, 26], [50467, 222], [50700, 26], [50768, 220], [51000, 26], [51069, 218], [51300, 25], [51369, 217], [51600, 25], [51670, 215], [51900, 25], [51971, 213], [52200, 25], [52271, 68], [52361, 122], [52500, 24], [52572, 60], [52668, 114], [52800, 24], [52872, 55], [52973, 108], [53100, 24], [53173, 50], [53277, 103], [53400, 24], [53474, 46], [53580, 99], [53700, 23], [53774, 44], [53882, 96], [54000, 23], [54075, 41], [54184, 93], [54300, 23], [54376, 38], [54486, 90], [54600, 23], [54676, 35], [54789, 85], [54900, 22], [54977, 33], [55090, 83], [55200, 22], [55277, 32], [55391, 81], [55500, 22], [55578, 30], [55692, 79], [55800, 22], [55879, 28], [55993, 77], [56100, 21], [56179, 27], [56294, 75], [56400, 21], [56480, 26], [56594, 74], [56700, 21], [56781, 24], [56895, 72], [57000, 20], [57081, 23], [57196, 70], [57300, 20], [57384, 20], [57496, 67], [57600, 20], [57684, 11], [57796, 32], [57851, 12], [57900, 20], [57984, 11], [58151, 12], [58200, 19], [58284, 10], [58451, 12], [58500, 19], [58584, 10], [58752, 11], [58800, 19], [58885, 9], [59052, 10], [59100, 19], [59185, 9], [59352, 10], [59400, 18], [59485, 9], [59652, 10], [59700, 18], [60000, 18], [60300, 18], [60600, 17], [60900, 17], [61200, 17], [61500, 17], [61800, 16], [62100, 16], [62400, 16], [62700, 15], [63000, 15], [63300, 15], [63600, 15], [63900, 14], [64200, 14], [64500, 14], [64800, 14], [65100, 13], [65400, 13], [65700, 13], [66000, 13], [66300, 12], [66600, 12], [66900, 12], [67200, 11], [67500, 11], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 10], [69300, 10], [69600, 9], [69900, 9], [70200, 9], [70500, 9], [70800, 8], [71100, 8], [71400, 8], [71700, 8], [72000, 7], [72300, 7], [72600, 7], [72900, 6], [73200, 6], [73500, 6], [73800, 6], [74100, 5], [74400, 5], [74700, 5], [75000, 5], [75300, 4], [75600, 4], [75900, 4], [76200, 4], [76500, 3], [76800, 3], [77100, 3], [77400, 3], [77700, 2], [78000, 2], [78300, 2], [78600, 1], [78900, 1], [79200, 1], [79500, 1]], "point": [149, 132]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 266], "mask": [[0, 96], [138, 45], [215, 181], [438, 45], [514, 182], [738, 45], [814, 182], [1038, 45], [1114, 182], [1337, 46], [1413, 184], [1637, 46], [1713, 184], [1937, 46], [2012, 185], [2237, 46], [2312, 185], [2537, 46], [2612, 186], [2836, 47], [2911, 187], [3136, 47], [3211, 188], [3436, 47], [3510, 189], [3736, 47], [3810, 190], [4035, 48], [4110, 190], [4335, 48], [4409, 192], [4634, 49], [4709, 192], [4934, 50], [5009, 193], [5233, 51], [5308, 195], [5532, 53], [5607, 197], [5831, 56], [5906, 199], [6130, 59], [6205, 201], [6428, 63], [6502, 207], [6727, 285], [7024, 29015], [36040, 598], [36639, 299], [36939, 599], [37539, 298], [37839, 298], [38142, 295], [38442, 295], [38743, 293], [39044, 292], [39344, 292], [39645, 291], [39945, 290], [40246, 289], [40547, 288], [40847, 287], [41148, 286], [41449, 285], [41749, 285], [42050, 283], [42350, 283], [42651, 282], [42952, 281], [43252, 280], [43553, 279], [43854, 278], [44154, 278], [44455, 276], [44755, 276], [45056, 275], [45357, 274], [45657, 273], [45958, 272], [46259, 271], [46559, 270], [46860, 269], [47160, 269], [47461, 268], [47762, 237], [48000, 28], [48062, 236], [48300, 28], [48363, 234], [48600, 28], [48664, 232], [48900, 28], [48964, 231], [49200, 27], [49265, 229], [49500, 27], [49565, 228], [49800, 27], [49866, 225], [50100, 27], [50167, 223], [50400, 26], [50467, 222], [50700, 26], [50768, 220], [51000, 26], [51069, 218], [51300, 25], [51369, 217], [51600, 25], [51670, 215], [51900, 25], [51971, 213], [52200, 25], [52271, 212], [52500, 24], [52572, 210], [52800, 24], [52872, 209], [53100, 24], [53173, 207], [53400, 24], [53474, 205], [53700, 23], [53774, 204], [54000, 23], [54075, 202], [54300, 23], [54376, 200], [54600, 23], [54676, 198], [54900, 22], [54977, 196], [55200, 22], [55277, 195], [55500, 22], [55578, 193], [55800, 22], [55879, 191], [56100, 21], [56179, 190], [56400, 21], [56480, 188], [56700, 21], [56781, 186], [57000, 20], [57081, 185], [57300, 20], [57384, 179], [57600, 20], [57684, 11], [57719, 109], [57851, 12], [57900, 20], [57984, 11], [58151, 12], [58200, 19], [58284, 10], [58451, 12], [58500, 19], [58584, 10], [58752, 11], [58800, 19], [58885, 9], [59052, 10], [59100, 19], [59185, 9], [59352, 10], [59400, 18], [59485, 9], [59652, 10], [59700, 18], [60000, 18], [60300, 18], [60600, 17], [60900, 17], [61200, 17], [61500, 17], [61800, 16], [62100, 16], [62400, 16], [62700, 15], [63000, 15], [63300, 15], [63600, 15], [63900, 14], [64200, 14], [64500, 14], [64800, 14], [65100, 13], [65400, 13], [65700, 13], [66000, 13], [66300, 12], [66600, 12], [66900, 12], [67200, 11], [67500, 11], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 10], [69300, 10], [69600, 9], [69900, 9], [70200, 9], [70500, 9], [70800, 8], [71100, 8], [71400, 8], [71700, 8], [72000, 7], [72300, 7], [72600, 7], [72900, 6], [73200, 6], [73500, 6], [73800, 6], [74100, 5], [74400, 5], [74700, 5], [75000, 5], [75300, 4], [75600, 4], [75900, 4], [76200, 4], [76500, 3], [76800, 3], [77100, 3], [77400, 3], [77700, 2], [78000, 2], [78300, 2], [78600, 1], [78900, 1], [79200, 1], [79500, 1]], "point": [149, 132]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan13", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -1.25, "y": 0.8995012, "z": 2.5}, "object_poses": [{"objectName": "Pan_94f6c891", "position": {"x": -3.49410057, "y": 0.9231094, "z": 5.33607531}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.295603484, "y": 1.10402477, "z": 3.97425056}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -0.488323241, "y": 0.924664259, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -4.17237, "y": 0.9230642, "z": 2.8829937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -2.02637982, "y": 0.924428642, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -0.334957659, "y": 0.926028669, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -2.469, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -4.31204462, "y": 0.924629331, "z": 2.41670632}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -4.210629, "y": 0.7706754, "z": 2.76808786}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -0.104909286, "y": 0.923959553, "z": 5.932334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -1.86012983, "y": 0.9223595, "z": 2.02650762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -0.24247998, "y": 1.06941628, "z": 3.47924948}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -4.31204462, "y": 0.9532002, "z": 2.76642179}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -2.109505, "y": 0.9532003, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -3.784541, "y": 0.06842968, "z": 6.45731449}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -0.454978883, "y": 1.12759972, "z": 3.85050082}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -3.712, "y": 0.125750929, "z": 6.33384275}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -4.411006, "y": 0.9198062, "z": 4.90006828}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -0.334957659, "y": 0.92140615, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -0.227427676, "y": 2.130077, "z": 3.871181}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -4.19640064, "y": 0.8863857, "z": 3.41021824}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Bread_b10200c3", "position": {"x": -0.3011191, "y": 1.76541138, "z": 3.85464358}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -0.3011179, "y": 1.33095682, "z": 3.72674966}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -0.4098, "y": 0.9556, "z": 6.55700064}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -0.565006, "y": 0.9609395, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -0.301119447, "y": 1.73932731, "z": 3.73392153}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -4.24220753, "y": 0.9532002, "z": 2.30013466}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -4.23371363, "y": 0.8244251, "z": 3.159811}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Fork_1dc44411", "position": {"x": -2.02637982, "y": 0.924048543, "z": 1.87550259}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -0.181592077, "y": 0.922047436, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_53fee662", "position": {"x": -0.301118851, "y": 1.5816828, "z": 3.40980458}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -4.26, "y": 1.01892865, "z": 4.12272}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -1.69388, "y": 1.00305462, "z": 1.8}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -0.218675047, "y": 1.39299977, "z": 3.23174953}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -4.204613, "y": 0.924428642, "z": 1.95100164}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -3.85912442, "y": 0.938499868, "z": 1.79999614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_aa4cec24", "position": {"x": -3.712, "y": 0.172993273, "z": 6.498472}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -0.258274853, "y": 0.923959553, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -0.334957659, "y": 0.9253912, "z": 5.932334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_94f6c891", "position": {"x": -2.45436978, "y": 0.9335794, "z": 2.0230875}, "rotation": {"x": 0.0, "y": 240.000244, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -2.92400026, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -3.36384058, "y": 0.0787394643, "z": 1.85061407}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -4.18449736, "y": 0.9232217, "z": 4.90006828}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -0.565006, "y": 0.92140615, "z": 6.26166725}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -4.033492, "y": 0.9235421, "z": 4.381836}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -4.0956, "y": 0.744429, "z": 4.660389}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -4.159086, "y": 0.7920865, "z": 3.22241282}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Mug_19433bed", "position": {"x": -1.610755, "y": 0.9185, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1707026000, "scene_num": 13}, "task_id": "trial_T20190908_070444_960595", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AJQGWGESKQT4Y_3II4UPYCOMO76GY5HFOXD48QLBGQDA", "high_descs": ["Turn left and then right to face the fridge.", "Open the fridge and take out the black cup on the shelf and shut the door.", "Turn around and go left to stand in front of the microwave above the stove.", "Put the cup in and shut the door and then open it and pick the mug up and shut the door again.", "Turn around and go right to stand in front of the fridge.", "Put the cup in the fridge and shut the door."], "task_desc": "Put a heated cup in the fridge.", "votes": [1, 1]}, {"assignment_id": "A36DK84J5YJ942_3L70J4KAZJ3FJ0ROSJRBN4TLHT0DAW", "high_descs": ["move to the fridge to the left of you`", "pick up a cup from the fridge", "move to the stove to the right of you", "heat the cup in the microwave", "move to the fridge to the left of you", "put the cup in the fridge"], "task_desc": "Put a heated cup in the fridge.", "votes": [1, 1]}, {"assignment_id": "A2871R3LEPWMMK_3PXX5PX6L0FM43T4EN05SE5LE8WABU", "high_descs": ["Turn left and walk to the black fridge on your right side.", "Open the fridge, remove the black glass from the middle shelf and close the doors.", "Turn around and walk to the white stove to your left.", "Put the glass inside the microwave above the stove, heat it a few seconds, remove it and close the door.", "Turn around and walk to the fridge to your right.", "Put the heated glass on the bottom shelf to the right of the tomato inside the fridge."], "task_desc": "Place a heated glass in a fridge.", "votes": [1, 1]}]}}