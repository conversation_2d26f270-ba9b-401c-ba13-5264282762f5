{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000082.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000083.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000084.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000085.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 33}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|3|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [2.4881, 2.4881, 3.3115376, 3.3115376, 3.635169744, 3.635169744]], "coordinateReceptacleObjectId": ["CounterTop", [4.844, 4.844, 1.856, 1.856, 3.7488, 3.7488]], "forceVisible": true, "objectId": "Cup|+00.62|+00.91|+00.83"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-1|5|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-2|3|0"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [2.4881, 2.4881, 3.3115376, 3.3115376, 3.635169744, 3.635169744]], "coordinateReceptacleObjectId": ["Cabinet", [-3.68, -3.68, -2.48, -2.48, 6.6622696, 6.6622696]], "forceVisible": true, "objectId": "Cup|+00.62|+00.91|+00.83", "receptacleObjectId": "Cabinet|-00.92|+01.67|-00.62"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.62|+00.91|+00.83"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [109, 135, 144, 172], "mask": [[40326, 3], [40620, 15], [40918, 19], [41216, 23], [41515, 25], [41814, 27], [42112, 30], [42412, 31], [42711, 32], [43011, 32], [43310, 34], [43610, 34], [43909, 35], [44209, 36], [44509, 36], [44809, 36], [45109, 36], [45409, 36], [45709, 36], [46009, 36], [46309, 36], [46609, 36], [46910, 35], [47210, 35], [47510, 34], [47810, 34], [48110, 34], [48411, 32], [48712, 31], [49012, 30], [49313, 29], [49614, 27], [49915, 25], [50215, 24], [50517, 21], [50819, 17], [51120, 14], [51424, 7]], "point": [126, 152]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.62|+00.91|+00.83", "placeStationary": true, "receptacleObjectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 210], [26190, 210], [26489, 211], [26789, 211], [27089, 131], [27223, 77], [27388, 128], [27527, 73], [27688, 127], [27828, 72], [27988, 126], [28129, 71], [28287, 126], [28430, 70], [28587, 126], [28731, 69], [28887, 125], [29032, 68], [29187, 125], [29332, 68], [29486, 126], [29632, 68], [29786, 125], [29932, 68], [30086, 125], [30232, 68], [30385, 126], [30532, 68], [30685, 126], [30832, 68], [30985, 126], [31132, 68], [31284, 127], [31431, 69], [31584, 128], [31731, 69], [31884, 128], [32030, 70], [32183, 130], [32330, 70], [32483, 131], [32629, 71], [32783, 132], [32928, 72], [33083, 133], [33226, 74], [33382, 136], [33524, 76], [33682, 218], [33982, 218], [34281, 219], [34581, 219], [34881, 219], [35180, 220], [35480, 220], [35780, 220], [36080, 220], [36379, 221], [36679, 221], [36979, 221], [37278, 222], [37578, 222], [37878, 222], [38177, 223], [38477, 223], [38777, 223], [39076, 224], [39376, 224], [39676, 224], [39976, 224], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 59], [62453, 58], [62752, 58], [63052, 57], [63352, 57], [63651, 57], [63951, 57], [64251, 57], [64550, 57], [64841, 66], [65141, 66], [65440, 66], [65740, 66], [66040, 65], [66339, 66], [66639, 66], [66939, 65], [67238, 66], [67538, 66], [67838, 65], [68138, 65], [68438, 65], [68738, 64], [69037, 65], [69337, 65], [69637, 65], [69938, 63], [70238, 63], [70538, 63], [70838, 63], [71138, 63], [71438, 63], [71738, 63], [72038, 63], [72338, 63], [72639, 63], [72939, 63], [73239, 63], [73539, 63], [73839, 63], [74139, 63], [74440, 62], [74740, 62], [75040, 62], [75341, 61], [75641, 61], [75941, 61], [76241, 62], [76542, 61], [76842, 61], [77142, 62], [77443, 61], [77743, 61], [78044, 60], [78344, 61], [78644, 61], [78945, 61], [79245, 61], [79546, 60], [79847, 59], [80148, 57], [80448, 57], [80749, 56], [81050, 55], [81351, 54], [81653, 52], [81954, 51], [82255, 50], [82556, 49], [82858, 46], [83159, 45], [83460, 44], [83762, 42], [84064, 40], [84365, 39], [84667, 37], [84969, 35], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 210], [26190, 210], [26489, 211], [26789, 211], [27089, 131], [27223, 77], [27388, 128], [27527, 73], [27688, 127], [27828, 72], [27988, 126], [28129, 71], [28287, 126], [28430, 70], [28587, 59], [28659, 54], [28731, 69], [28887, 57], [28961, 51], [29032, 68], [29187, 54], [29264, 48], [29332, 68], [29486, 54], [29565, 47], [29632, 68], [29786, 53], [29866, 45], [29932, 68], [30086, 52], [30168, 43], [30232, 68], [30385, 52], [30468, 43], [30532, 68], [30685, 52], [30768, 43], [30832, 68], [30985, 51], [31069, 42], [31132, 68], [31284, 52], [31369, 42], [31431, 69], [31584, 52], [31670, 42], [31731, 69], [31884, 51], [31970, 42], [32030, 70], [32183, 52], [32270, 43], [32330, 70], [32483, 52], [32570, 44], [32629, 71], [32783, 52], [32870, 45], [32928, 72], [33083, 52], [33170, 46], [33226, 74], [33382, 53], [33470, 48], [33524, 76], [33682, 53], [33770, 130], [33982, 53], [34070, 130], [34281, 54], [34370, 130], [34581, 54], [34670, 130], [34881, 54], [34970, 130], [35180, 56], [35269, 131], [35480, 56], [35569, 131], [35780, 56], [35869, 131], [36080, 57], [36168, 132], [36379, 58], [36468, 132], [36679, 59], [36767, 133], [36979, 59], [37067, 133], [37278, 61], [37366, 134], [37578, 62], [37665, 135], [37878, 63], [37964, 136], [38177, 65], [38263, 137], [38477, 66], [38562, 138], [38777, 68], [38860, 140], [39076, 72], [39157, 143], [39376, 224], [39676, 224], [39976, 224], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 60], [62453, 59], [62752, 60], [63052, 60], [63352, 60], [63651, 61], [63951, 61], [64251, 61], [64550, 62], [64841, 71], [65141, 70], [65440, 71], [65740, 71], [66040, 71], [66339, 72], [66639, 72], [66939, 72], [67238, 73], [67538, 72], [67838, 72], [68138, 72], [68438, 72], [68738, 72], [69037, 73], [69337, 73], [69637, 73], [69938, 72], [70238, 71], [70538, 71], [70838, 71], [71138, 71], [71438, 71], [71738, 71], [72038, 71], [72338, 71], [72639, 69], [72939, 69], [73239, 69], [73539, 69], [73839, 69], [74139, 69], [74440, 68], [74740, 68], [75040, 68], [75341, 66], [75641, 66], [75941, 66], [76241, 66], [76542, 65], [76842, 65], [77142, 65], [77443, 64], [77743, 63], [78044, 62], [78344, 62], [78644, 62], [78945, 61], [79245, 61], [79546, 60], [79847, 59], [80148, 57], [80448, 57], [80749, 56], [81050, 55], [81351, 54], [81653, 52], [81954, 51], [82255, 50], [82556, 49], [82858, 46], [83159, 45], [83460, 44], [83762, 42], [84064, 40], [84365, 39], [84667, 37], [84969, 35], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.136, -4.136, 5.716, 5.716, 3.5567708, 3.5567708]], "forceVisible": true, "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [103, 21, 299, 168], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22304, 196], [22604, 196], [22904, 196], [23204, 196], [23504, 196], [23804, 196], [24104, 196], [24405, 195], [24705, 195], [25005, 195], [25305, 195], [25605, 195], [25905, 195], [26205, 195], [26505, 195], [26805, 195], [27106, 194], [27406, 194], [27706, 194], [28006, 194], [28306, 194], [28606, 194], [28906, 194], [29206, 194], [29507, 193], [29807, 193], [30107, 193], [30407, 193], [30707, 193], [31007, 193], [31307, 193], [31608, 192], [31908, 192], [32208, 192], [32508, 192], [32808, 192], [33108, 192], [33408, 192], [33709, 191], [34009, 191], [34309, 191], [34609, 191], [34909, 191], [35209, 191], [35509, 191], [35810, 190], [36110, 190], [36410, 190], [36710, 190], [37010, 190], [37310, 190], [37610, 190], [37911, 189], [38211, 189], [38511, 189], [38811, 189], [39111, 189], [39411, 189], [39711, 189], [40012, 188], [40312, 188], [40612, 187], [40912, 187], [41212, 186], [41512, 186], [41812, 185], [42112, 184], [42413, 183], [42713, 182], [43013, 182], [43313, 181], [43613, 181], [43913, 180], [44213, 180], [44514, 178], [44814, 178], [45114, 177], [45414, 176], [45714, 176], [46014, 175], [46314, 175], [46615, 173], [46915, 173], [47215, 172], [47515, 172], [47815, 171], [48115, 171], [48416, 169], [48716, 169], [49016, 168], [49316, 168], [49616, 167], [49916, 167], [50217, 165]], "point": [201, 93]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.62|+00.91|+00.83"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [135, 96, 169, 131], "mask": [[28646, 13], [28944, 17], [29241, 23], [29540, 25], [29839, 27], [30138, 30], [30437, 31], [30737, 31], [31036, 33], [31336, 33], [31636, 34], [31935, 35], [32235, 35], [32535, 35], [32835, 35], [33135, 35], [33435, 35], [33735, 35], [34035, 35], [34335, 35], [34635, 35], [34935, 35], [35236, 33], [35536, 33], [35836, 33], [36137, 31], [36437, 31], [36738, 29], [37038, 29], [37339, 27], [37640, 25], [37941, 23], [38242, 21], [38543, 19], [38845, 15], [39148, 9]], "point": [152, 112]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.03|+00.89|+01.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [37, 21, 299, 294], "mask": [[6115, 173], [6415, 174], [6714, 176], [7014, 177], [7314, 178], [7614, 179], [7913, 181], [8213, 182], [8513, 183], [8813, 184], [9112, 186], [9412, 187], [9712, 188], [10011, 189], [10311, 189], [10611, 189], [10911, 189], [11210, 190], [11510, 190], [11810, 190], [12110, 190], [12409, 191], [12709, 191], [13009, 191], [13309, 191], [13608, 192], [13908, 192], [14208, 192], [14507, 193], [14807, 193], [15107, 193], [15407, 193], [15706, 194], [16006, 194], [16306, 194], [16606, 194], [16905, 195], [17205, 195], [17505, 195], [17804, 196], [18104, 196], [18404, 196], [18704, 196], [19003, 197], [19303, 197], [19603, 197], [19903, 197], [20203, 197], [20503, 197], [20803, 197], [21103, 197], [21403, 197], [21703, 197], [22003, 197], [22294, 206], [22593, 207], [22893, 207], [23193, 207], [23492, 208], [23792, 208], [24092, 208], [24391, 209], [24691, 209], [24991, 209], [25291, 209], [25590, 210], [25890, 210], [26190, 210], [26489, 211], [26789, 211], [27089, 131], [27223, 77], [27388, 128], [27527, 73], [27688, 127], [27828, 72], [27988, 126], [28129, 71], [28287, 126], [28430, 70], [28587, 126], [28731, 69], [28887, 125], [29032, 68], [29187, 125], [29332, 68], [29486, 126], [29632, 68], [29786, 125], [29932, 68], [30086, 125], [30232, 68], [30385, 126], [30532, 68], [30685, 126], [30832, 68], [30985, 126], [31132, 68], [31284, 127], [31431, 69], [31584, 128], [31731, 69], [31884, 128], [32030, 70], [32183, 130], [32330, 70], [32483, 131], [32629, 71], [32783, 132], [32928, 72], [33083, 133], [33226, 74], [33382, 136], [33524, 76], [33682, 218], [33982, 218], [34281, 219], [34581, 219], [34881, 219], [35180, 220], [35480, 220], [35780, 220], [36080, 220], [36379, 221], [36679, 221], [36979, 221], [37278, 222], [37578, 222], [37878, 222], [38177, 223], [38477, 223], [38777, 223], [39076, 224], [39376, 224], [39676, 224], [39976, 224], [40275, 225], [40575, 224], [40875, 224], [41174, 224], [41474, 224], [41774, 223], [42073, 223], [42373, 223], [42673, 222], [42972, 223], [43272, 222], [43572, 222], [43872, 221], [44171, 222], [44471, 221], [44771, 221], [45070, 221], [45370, 220], [45670, 220], [45969, 220], [46269, 220], [46569, 219], [46868, 220], [47168, 219], [47468, 219], [47768, 218], [48067, 219], [48367, 218], [48667, 218], [48966, 218], [49266, 218], [49566, 217], [49865, 218], [50165, 217], [50465, 52], [50765, 52], [51064, 53], [51364, 53], [51664, 53], [51963, 54], [52263, 53], [52563, 53], [52862, 54], [53162, 54], [53462, 54], [53761, 55], [54061, 55], [54361, 55], [54661, 55], [54960, 55], [55260, 55], [55560, 55], [55859, 56], [56159, 56], [56459, 56], [56758, 57], [57058, 57], [57358, 56], [57657, 57], [57957, 57], [58257, 57], [58557, 57], [58856, 58], [59156, 58], [59456, 58], [59755, 59], [60055, 58], [60355, 58], [60654, 59], [60954, 59], [61254, 59], [61553, 60], [61853, 60], [62153, 59], [62453, 58], [62752, 58], [63052, 57], [63352, 57], [63651, 57], [63951, 57], [64251, 57], [64550, 57], [64841, 66], [65141, 66], [65440, 66], [65740, 66], [66040, 65], [66339, 66], [66639, 66], [66939, 65], [67238, 66], [67538, 66], [67838, 65], [68138, 65], [68438, 65], [68738, 64], [69037, 65], [69337, 65], [69637, 65], [69938, 63], [70238, 63], [70538, 63], [70838, 63], [71138, 63], [71438, 63], [71738, 63], [72038, 63], [72338, 63], [72639, 63], [72939, 63], [73239, 63], [73539, 63], [73839, 63], [74139, 63], [74440, 62], [74740, 62], [75040, 62], [75341, 61], [75641, 61], [75941, 61], [76241, 62], [76542, 61], [76842, 61], [77142, 62], [77443, 61], [77743, 61], [78044, 60], [78344, 61], [78644, 61], [78945, 61], [79245, 61], [79546, 60], [79847, 59], [80148, 57], [80448, 57], [80749, 56], [81050, 55], [81351, 54], [81653, 52], [81954, 51], [82255, 50], [82556, 49], [82858, 46], [83159, 45], [83460, 44], [83762, 42], [84064, 40], [84365, 39], [84667, 37], [84969, 35], [85271, 32], [85573, 30], [85878, 28], [86179, 29], [86479, 31], [86780, 23], [86804, 6], [87080, 23], [87105, 5], [87381, 22], [87682, 21], [87982, 20]], "point": [168, 156]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.92|+01.67|-00.62"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 104, 300], "mask": [[0, 105], [300, 105], [600, 105], [900, 105], [1200, 105], [1500, 105], [1800, 105], [2100, 105], [2400, 105], [2700, 105], [3000, 105], [3300, 105], [3600, 105], [3900, 105], [4200, 105], [4500, 105], [4800, 105], [5100, 105], [5400, 105], [5700, 105], [6000, 105], [6300, 105], [6600, 105], [6900, 105], [7200, 105], [7500, 105], [7800, 105], [8100, 105], [8400, 105], [8700, 105], [9000, 105], [9300, 105], [9600, 105], [9900, 105], [10200, 105], [10500, 105], [10800, 105], [11100, 105], [11400, 105], [11700, 105], [12000, 105], [12300, 105], [12600, 105], [12900, 105], [13200, 105], [13500, 105], [13800, 105], [14100, 105], [14400, 105], [14700, 105], [15000, 105], [15300, 105], [15600, 105], [15900, 105], [16200, 105], [16500, 105], [16800, 105], [17100, 105], [17400, 105], [17700, 105], [18000, 105], [18300, 105], [18600, 105], [18900, 105], [19200, 105], [19500, 105], [19800, 105], [20100, 105], [20400, 105], [20700, 105], [21000, 105], [21300, 105], [21600, 105], [21900, 105], [22200, 105], [22500, 105], [22800, 105], [23100, 105], [23400, 105], [23700, 105], [24000, 105], [24300, 105], [24600, 105], [24900, 105], [25200, 105], [25500, 105], [25800, 105], [26100, 105], [26400, 105], [26700, 105], [27000, 105], [27300, 105], [27600, 105], [27900, 105], [28200, 105], [28500, 105], [28800, 105], [29100, 105], [29400, 105], [29700, 105], [30000, 105], [30300, 105], [30600, 105], [30900, 105], [31200, 105], [31500, 105], [31800, 105], [32100, 105], [32400, 105], [32700, 105], [33000, 105], [33300, 105], [33600, 105], [33900, 105], [34200, 105], [34500, 105], [34800, 105], [35100, 105], [35400, 105], [35700, 105], [36000, 105], [36300, 105], [36600, 105], [36900, 105], [37200, 105], [37500, 105], [37800, 105], [38100, 105], [38400, 105], [38700, 105], [39000, 105], [39300, 105], [39600, 105], [39900, 105], [40200, 105], [40500, 105], [40800, 105], [41100, 105], [41400, 105], [41700, 105], [42000, 105], [42300, 105], [42600, 105], [42900, 105], [43200, 105], [43500, 105], [43800, 105], [44100, 105], [44400, 105], [44700, 105], [45000, 105], [45300, 105], [45600, 105], [45900, 105], [46200, 105], [46500, 105], [46800, 105], [47100, 105], [47400, 105], [47700, 105], [48000, 105], [48300, 105], [48600, 105], [48900, 105], [49200, 105], [49500, 105], [49800, 105], [50100, 105], [50400, 105], [50700, 105], [51000, 105], [51300, 105], [51600, 105], [51900, 105], [52200, 105], [52500, 105], [52800, 105], [53100, 105], [53400, 105], [53700, 105], [54000, 105], [54300, 105], [54600, 105], [54900, 105], [55200, 105], [55500, 105], [55800, 105], [56100, 105], [56400, 105], [56700, 105], [57000, 105], [57300, 105], [57600, 105], [57900, 105], [58200, 105], [58500, 105], [58800, 105], [59100, 105], [59400, 105], [59700, 105], [60000, 105], [60300, 105], [60600, 105], [60900, 105], [61200, 105], [61500, 105], [61800, 105], [62100, 105], [62400, 105], [62700, 105], [63000, 105], [63300, 105], [63600, 105], [63900, 105], [64200, 105], [64500, 105], [64800, 105], [65100, 105], [65400, 105], [65700, 105], [66000, 105], [66300, 105], [66600, 105], [66900, 104], [67200, 104], [67500, 104], [67800, 103], [68100, 103], [68400, 103], [68700, 102], [69000, 102], [69300, 102], [69600, 102], [69900, 101], [70200, 101], [70500, 101], [70800, 101], [71100, 101], [71400, 101], [71700, 101], [72000, 101], [72300, 101], [72600, 102], [72900, 102], [73200, 102], [73500, 102], [73800, 102], [74100, 102], [74400, 102], [74700, 102], [75000, 102], [75300, 102], [75600, 102], [75900, 102], [76200, 103], [76500, 103], [76800, 103], [77100, 104], [77400, 104], [77700, 104], [78000, 104], [78300, 105], [78600, 105], [78900, 105], [79200, 105], [79500, 105], [79800, 105], [80100, 105], [80400, 105], [80700, 105], [81000, 105], [81300, 105], [81600, 105], [81900, 105], [82200, 105], [82500, 105], [82800, 105], [83100, 105], [83400, 105], [83700, 105], [84000, 105], [84300, 105], [84600, 105], [84900, 105], [85200, 105], [85500, 105], [85800, 105], [86100, 105], [86400, 105], [86700, 105], [87000, 104], [87300, 102], [87600, 99], [87900, 97], [88200, 95], [88500, 93], [88800, 91], [89100, 88], [89400, 86], [89700, 84]], "point": [52, 149]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.62|+00.91|+00.83", "placeStationary": true, "receptacleObjectId": "Cabinet|-00.92|+01.67|-00.62"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 191, 300], "mask": [[0, 101], [106, 86], [300, 101], [406, 86], [600, 101], [706, 86], [900, 101], [1006, 86], [1200, 101], [1306, 86], [1500, 101], [1606, 86], [1800, 101], [1906, 86], [2100, 101], [2206, 86], [2400, 101], [2506, 86], [2700, 101], [2806, 86], [3000, 101], [3106, 86], [3300, 101], [3406, 86], [3600, 101], [3706, 86], [3900, 101], [4006, 86], [4200, 101], [4306, 86], [4500, 101], [4606, 86], [4800, 101], [4906, 86], [5100, 101], [5206, 86], [5400, 101], [5506, 86], [5700, 101], [5806, 86], [6000, 101], [6106, 86], [6300, 101], [6406, 86], [6600, 101], [6706, 86], [6900, 101], [7006, 86], [7200, 101], [7306, 86], [7500, 101], [7606, 86], [7800, 101], [7906, 86], [8100, 101], [8206, 86], [8400, 101], [8506, 86], [8700, 101], [8806, 86], [9000, 101], [9106, 86], [9300, 101], [9406, 86], [9600, 101], [9706, 86], [9900, 101], [10006, 86], [10200, 101], [10306, 86], [10500, 101], [10606, 86], [10800, 101], [10906, 86], [11100, 101], [11206, 86], [11400, 101], [11506, 86], [11700, 101], [11806, 86], [12000, 101], [12106, 86], [12300, 101], [12406, 86], [12600, 101], [12706, 86], [12900, 101], [13006, 86], [13200, 101], [13306, 86], [13500, 101], [13606, 86], [13800, 101], [13906, 86], [14100, 101], [14206, 86], [14400, 101], [14506, 86], [14700, 101], [14806, 86], [15000, 101], [15106, 86], [15300, 101], [15406, 86], [15600, 101], [15706, 86], [15900, 101], [16006, 86], [16200, 101], [16306, 86], [16500, 101], [16606, 86], [16800, 101], [16906, 86], [17100, 101], [17206, 86], [17400, 101], [17506, 86], [17700, 101], [17806, 86], [18000, 101], [18106, 86], [18300, 101], [18406, 86], [18600, 101], [18706, 86], [18900, 101], [19006, 86], [19200, 101], [19306, 86], [19500, 101], [19606, 86], [19800, 101], [19906, 86], [20100, 101], [20206, 86], [20400, 101], [20506, 86], [20700, 101], [20806, 86], [21000, 101], [21106, 86], [21300, 101], [21406, 86], [21600, 101], [21706, 86], [21900, 101], [22006, 86], [22200, 101], [22306, 86], [22500, 101], [22606, 86], [22800, 101], [22906, 86], [23100, 101], [23206, 86], [23400, 101], [23506, 86], [23700, 101], [23806, 86], [24000, 101], [24106, 86], [24300, 101], [24406, 86], [24600, 101], [24706, 86], [24900, 101], [25006, 86], [25200, 101], [25306, 86], [25500, 101], [25606, 86], [25800, 101], [25906, 86], [26100, 101], [26206, 86], [26400, 101], [26506, 86], [26700, 101], [26806, 86], [27000, 101], [27106, 86], [27300, 101], [27406, 86], [27600, 101], [27706, 86], [27900, 101], [28006, 86], [28200, 101], [28306, 86], [28500, 101], [28606, 86], [28800, 101], [28906, 86], [29100, 101], [29206, 86], [29400, 101], [29506, 86], [29700, 101], [29806, 86], [30000, 101], [30106, 86], [30300, 101], [30406, 86], [30600, 101], [30706, 86], [30900, 101], [31006, 86], [31200, 101], [31306, 86], [31500, 101], [31606, 86], [31800, 101], [31906, 86], [32100, 101], [32206, 86], [32400, 101], [32506, 86], [32700, 101], [32806, 86], [33000, 101], [33106, 86], [33300, 101], [33406, 86], [33600, 101], [33706, 86], [33900, 101], [34006, 86], [34200, 101], [34306, 86], [34500, 101], [34606, 86], [34800, 101], [34906, 86], [35100, 101], [35206, 86], [35400, 101], [35506, 86], [35700, 101], [35806, 86], [36000, 101], [36106, 86], [36300, 101], [36406, 86], [36600, 101], [36706, 86], [36900, 101], [37006, 86], [37200, 101], [37306, 86], [37500, 101], [37606, 86], [37800, 101], [37906, 86], [38100, 101], [38206, 86], [38400, 101], [38506, 86], [38700, 101], [38806, 86], [39000, 101], [39106, 86], [39300, 101], [39406, 86], [39600, 101], [39706, 86], [39900, 101], [40006, 86], [40200, 101], [40306, 86], [40500, 101], [40606, 86], [40800, 101], [40906, 86], [41100, 101], [41206, 86], [41400, 101], [41506, 86], [41700, 101], [41806, 86], [42000, 101], [42106, 86], [42300, 101], [42406, 86], [42600, 101], [42706, 86], [42900, 101], [43006, 86], [43200, 101], [43306, 86], [43500, 101], [43606, 86], [43800, 101], [43906, 86], [44100, 101], [44206, 86], [44400, 101], [44506, 86], [44700, 101], [44806, 86], [45000, 101], [45106, 86], [45300, 101], [45406, 86], [45600, 101], [45706, 86], [45900, 101], [46006, 86], [46200, 101], [46306, 86], [46500, 101], [46606, 86], [46800, 101], [46906, 86], [47100, 101], [47206, 86], [47400, 101], [47506, 86], [47700, 101], [47806, 86], [48000, 101], [48106, 86], [48300, 101], [48406, 86], [48600, 101], [48706, 86], [48900, 101], [49006, 86], [49200, 101], [49306, 86], [49500, 101], [49606, 86], [49800, 101], [49906, 86], [50100, 101], [50206, 86], [50400, 101], [50506, 86], [50700, 101], [50806, 86], [51000, 101], [51106, 86], [51300, 101], [51406, 86], [51600, 101], [51706, 86], [51900, 101], [52006, 86], [52200, 101], [52306, 86], [52500, 101], [52606, 86], [52800, 101], [52906, 86], [53100, 101], [53206, 86], [53400, 101], [53506, 86], [53700, 101], [53806, 86], [54000, 101], [54106, 86], [54300, 101], [54406, 86], [54600, 101], [54706, 86], [54900, 101], [55006, 86], [55200, 101], [55306, 86], [55500, 101], [55606, 86], [55800, 101], [55906, 86], [56100, 101], [56206, 86], [56400, 101], [56506, 86], [56700, 101], [56806, 40], [56854, 38], [57000, 101], [57106, 34], [57160, 32], [57300, 101], [57406, 28], [57466, 26], [57600, 101], [57706, 26], [57768, 24], [57900, 101], [58006, 24], [58070, 22], [58200, 101], [58306, 22], [58372, 20], [58500, 101], [58606, 20], [58674, 18], [58800, 101], [58906, 19], [58975, 17], [59100, 101], [59206, 17], [59277, 15], [59400, 101], [59506, 15], [59579, 13], [59700, 101], [59806, 13], [59881, 11], [60000, 101], [60106, 12], [60182, 10], [60300, 101], [60406, 11], [60483, 9], [60600, 101], [60706, 11], [60783, 9], [60900, 101], [61006, 10], [61084, 8], [61200, 101], [61306, 9], [61385, 7], [61500, 101], [61606, 8], [61686, 6], [61800, 101], [61906, 7], [61987, 5], [62100, 101], [62206, 6], [62288, 4], [62400, 101], [62506, 5], [62589, 3], [62700, 101], [62806, 4], [62890, 2], [63000, 101], [63106, 3], [63191, 1], [63300, 101], [63406, 3], [63491, 1], [63600, 101], [63706, 2], [63900, 101], [64006, 2], [64200, 101], [64306, 2], [64500, 101], [64606, 1], [64800, 101], [64906, 1], [65100, 101], [65206, 1], [65400, 101], [65700, 101], [66000, 101], [66300, 101], [66600, 101], [66900, 101], [67200, 101], [67500, 101], [67800, 101], [68100, 101], [68400, 101], [68700, 101], [69000, 101], [69300, 101], [69600, 101], [69900, 101], [70200, 101], [70500, 101], [70800, 101], [71100, 101], [71400, 101], [71700, 101], [72000, 101], [72300, 101], [72600, 101], [72900, 101], [73200, 101], [73500, 101], [73800, 101], [74100, 101], [74400, 101], [74700, 101], [75000, 101], [75300, 101], [75600, 101], [75900, 101], [76200, 101], [76500, 101], [76800, 101], [77100, 101], [77400, 101], [77700, 101], [78000, 101], [78300, 101], [78600, 101], [78900, 101], [79200, 101], [79500, 101], [79606, 1], [79800, 101], [79906, 1], [80100, 101], [80206, 2], [80400, 101], [80506, 2], [80700, 101], [80806, 3], [80891, 1], [81000, 101], [81106, 4], [81190, 2], [81300, 101], [81406, 4], [81490, 2], [81600, 101], [81706, 5], [81789, 3], [81900, 101], [82006, 5], [82089, 3], [82200, 101], [82306, 6], [82388, 4], [82500, 101], [82606, 6], [82688, 4], [82800, 101], [82906, 7], [82987, 5], [83100, 101], [83206, 7], [83287, 5], [83400, 101], [83506, 8], [83586, 6], [83700, 101], [83806, 9], [83885, 7], [84000, 101], [84106, 10], [84184, 8], [84300, 101], [84406, 12], [84482, 10], [84600, 101], [84706, 13], [84781, 11], [84900, 101], [85006, 14], [85080, 12], [85200, 101], [85306, 16], [85378, 14], [85500, 101], [85606, 17], [85677, 15], [85800, 101], [85906, 18], [85976, 16], [86100, 101], [86206, 20], [86274, 18], [86400, 101], [86506, 21], [86573, 19], [86700, 101], [86806, 22], [86872, 20], [87000, 101], [87106, 24], [87170, 22], [87300, 98], [87407, 26], [87467, 25], [87600, 96], [87709, 29], [87762, 30], [87900, 94], [88011, 31], [88058, 34], [88200, 92], [88312, 35], [88353, 39], [88500, 90], [88614, 78], [88800, 87], [88915, 77], [89100, 85], [89217, 75], [89400, 83], [89518, 74], [89700, 81], [89820, 72]], "point": [95, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.92|+01.67|-00.62"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 191, 300], "mask": [[0, 101], [106, 86], [300, 101], [406, 86], [600, 101], [706, 86], [900, 101], [1006, 86], [1200, 101], [1306, 86], [1500, 101], [1606, 86], [1800, 101], [1906, 86], [2100, 101], [2206, 86], [2400, 101], [2506, 86], [2700, 101], [2806, 86], [3000, 101], [3106, 86], [3300, 101], [3406, 86], [3600, 101], [3706, 86], [3900, 101], [4006, 86], [4200, 101], [4306, 86], [4500, 101], [4606, 86], [4800, 101], [4906, 86], [5100, 101], [5206, 86], [5400, 101], [5506, 86], [5700, 101], [5806, 86], [6000, 101], [6106, 86], [6300, 101], [6406, 86], [6600, 101], [6706, 86], [6900, 101], [7006, 86], [7200, 101], [7306, 86], [7500, 101], [7606, 86], [7800, 101], [7906, 86], [8100, 101], [8206, 86], [8400, 101], [8506, 86], [8700, 101], [8806, 86], [9000, 101], [9106, 86], [9300, 101], [9406, 86], [9600, 101], [9706, 86], [9900, 101], [10006, 86], [10200, 101], [10306, 86], [10500, 101], [10606, 86], [10800, 101], [10906, 86], [11100, 101], [11206, 86], [11400, 101], [11506, 86], [11700, 101], [11806, 86], [12000, 101], [12106, 86], [12300, 101], [12406, 86], [12600, 101], [12706, 86], [12900, 101], [13006, 86], [13200, 101], [13306, 86], [13500, 101], [13606, 86], [13800, 101], [13906, 86], [14100, 101], [14206, 86], [14400, 101], [14506, 86], [14700, 101], [14806, 86], [15000, 101], [15106, 86], [15300, 101], [15406, 86], [15600, 101], [15706, 86], [15900, 101], [16006, 86], [16200, 101], [16306, 86], [16500, 101], [16606, 86], [16800, 101], [16906, 86], [17100, 101], [17206, 86], [17400, 101], [17506, 86], [17700, 101], [17806, 86], [18000, 101], [18106, 86], [18300, 101], [18406, 86], [18600, 101], [18706, 86], [18900, 101], [19006, 86], [19200, 101], [19306, 86], [19500, 101], [19606, 86], [19800, 101], [19906, 86], [20100, 101], [20206, 86], [20400, 101], [20506, 86], [20700, 101], [20806, 86], [21000, 101], [21106, 86], [21300, 101], [21406, 86], [21600, 101], [21706, 86], [21900, 101], [22006, 86], [22200, 101], [22306, 86], [22500, 101], [22606, 86], [22800, 101], [22906, 86], [23100, 101], [23206, 86], [23400, 101], [23506, 86], [23700, 101], [23806, 86], [24000, 101], [24106, 86], [24300, 101], [24406, 86], [24600, 101], [24706, 86], [24900, 101], [25006, 86], [25200, 101], [25306, 86], [25500, 101], [25606, 86], [25800, 101], [25906, 86], [26100, 101], [26206, 86], [26400, 101], [26506, 86], [26700, 101], [26806, 86], [27000, 101], [27106, 86], [27300, 101], [27406, 86], [27600, 101], [27706, 86], [27900, 101], [28006, 86], [28200, 101], [28306, 86], [28500, 101], [28606, 86], [28800, 101], [28906, 86], [29100, 101], [29206, 86], [29400, 101], [29506, 86], [29700, 101], [29806, 86], [30000, 101], [30106, 86], [30300, 101], [30406, 86], [30600, 101], [30706, 86], [30900, 101], [31006, 86], [31200, 101], [31306, 86], [31500, 101], [31606, 86], [31800, 101], [31906, 86], [32100, 101], [32206, 86], [32400, 101], [32506, 86], [32700, 101], [32806, 86], [33000, 101], [33106, 86], [33300, 101], [33406, 86], [33600, 101], [33706, 86], [33900, 101], [34006, 86], [34200, 101], [34306, 86], [34500, 101], [34606, 86], [34800, 101], [34906, 86], [35100, 101], [35206, 86], [35400, 101], [35506, 86], [35700, 101], [35806, 86], [36000, 101], [36106, 86], [36300, 101], [36406, 86], [36600, 101], [36706, 86], [36900, 101], [37006, 86], [37200, 101], [37306, 86], [37500, 101], [37606, 86], [37800, 101], [37906, 86], [38100, 101], [38206, 86], [38400, 101], [38506, 86], [38700, 101], [38806, 86], [39000, 101], [39106, 86], [39300, 101], [39406, 86], [39600, 101], [39706, 86], [39900, 101], [40006, 86], [40200, 101], [40306, 86], [40500, 101], [40606, 86], [40800, 101], [40906, 86], [41100, 101], [41206, 86], [41400, 101], [41506, 86], [41700, 101], [41806, 86], [42000, 101], [42106, 86], [42300, 101], [42406, 86], [42600, 101], [42706, 86], [42900, 101], [43006, 86], [43200, 101], [43306, 86], [43500, 101], [43606, 86], [43800, 101], [43906, 86], [44100, 101], [44206, 86], [44400, 101], [44506, 86], [44700, 101], [44806, 86], [45000, 101], [45106, 86], [45300, 101], [45406, 86], [45600, 101], [45706, 86], [45900, 101], [46006, 86], [46200, 101], [46306, 86], [46500, 101], [46606, 86], [46800, 101], [46906, 86], [47100, 101], [47206, 86], [47400, 101], [47506, 86], [47700, 101], [47806, 86], [48000, 101], [48106, 86], [48300, 101], [48406, 86], [48600, 101], [48706, 86], [48900, 101], [49006, 86], [49200, 101], [49306, 86], [49500, 101], [49606, 86], [49800, 101], [49906, 86], [50100, 101], [50206, 86], [50400, 101], [50506, 86], [50700, 101], [50806, 86], [51000, 101], [51106, 86], [51300, 101], [51406, 86], [51600, 101], [51706, 86], [51900, 101], [52006, 86], [52200, 101], [52306, 86], [52500, 101], [52606, 86], [52800, 101], [52906, 86], [53100, 101], [53206, 86], [53400, 101], [53506, 86], [53700, 101], [53806, 86], [54000, 101], [54106, 86], [54300, 101], [54406, 86], [54600, 101], [54706, 86], [54900, 101], [55006, 86], [55200, 101], [55306, 86], [55500, 101], [55606, 86], [55800, 101], [55906, 86], [56100, 101], [56206, 86], [56400, 101], [56506, 86], [56700, 101], [56806, 86], [57000, 101], [57106, 86], [57300, 101], [57406, 86], [57600, 101], [57706, 86], [57900, 101], [58006, 86], [58200, 101], [58306, 86], [58500, 101], [58606, 86], [58800, 101], [58906, 86], [59100, 101], [59206, 86], [59400, 101], [59506, 86], [59700, 101], [59806, 86], [60000, 101], [60106, 86], [60300, 101], [60406, 86], [60600, 101], [60706, 86], [60900, 101], [61006, 86], [61200, 101], [61306, 86], [61500, 101], [61606, 86], [61800, 101], [61906, 86], [62100, 101], [62206, 86], [62400, 101], [62506, 86], [62700, 101], [62806, 86], [63000, 101], [63106, 86], [63300, 101], [63406, 86], [63600, 101], [63706, 86], [63900, 101], [64006, 86], [64200, 101], [64306, 86], [64500, 101], [64606, 86], [64800, 101], [64906, 86], [65100, 101], [65206, 86], [65400, 101], [65506, 86], [65700, 101], [65806, 86], [66000, 101], [66106, 86], [66300, 101], [66406, 86], [66600, 101], [66706, 86], [66900, 101], [67006, 86], [67200, 101], [67306, 86], [67500, 101], [67606, 86], [67800, 101], [67906, 86], [68100, 101], [68206, 86], [68400, 101], [68506, 86], [68700, 101], [68806, 86], [69000, 101], [69106, 86], [69300, 101], [69406, 86], [69600, 101], [69706, 86], [69900, 101], [70006, 86], [70200, 101], [70306, 86], [70500, 101], [70606, 86], [70800, 101], [70906, 86], [71100, 101], [71206, 86], [71400, 101], [71506, 86], [71700, 101], [71806, 86], [72000, 101], [72106, 86], [72300, 101], [72406, 86], [72600, 101], [72706, 86], [72900, 101], [73006, 86], [73200, 56], [73265, 36], [73306, 86], [73500, 49], [73571, 30], [73606, 86], [73800, 44], [73874, 27], [73906, 86], [74100, 41], [74175, 26], [74206, 86], [74400, 38], [74476, 25], [74506, 86], [74700, 36], [74777, 24], [74806, 86], [75000, 34], [75077, 24], [75106, 86], [75300, 32], [75377, 24], [75406, 86], [75600, 31], [75678, 23], [75706, 86], [75900, 30], [75978, 23], [76006, 86], [76200, 29], [76278, 23], [76306, 86], [76500, 29], [76579, 22], [76606, 86], [76800, 28], [76879, 22], [76906, 86], [77100, 28], [77179, 22], [77206, 86], [77400, 27], [77479, 22], [77506, 86], [77700, 27], [77779, 22], [77806, 86], [78000, 27], [78079, 22], [78106, 86], [78300, 27], [78379, 22], [78406, 86], [78600, 26], [78679, 22], [78706, 86], [78900, 26], [78979, 22], [79006, 86], [79200, 26], [79279, 22], [79306, 86], [79500, 26], [79579, 22], [79606, 86], [79800, 26], [79879, 22], [79906, 86], [80100, 26], [80179, 22], [80206, 86], [80400, 26], [80479, 22], [80506, 86], [80700, 26], [80779, 22], [80806, 86], [81000, 26], [81078, 23], [81106, 86], [81300, 26], [81378, 23], [81406, 86], [81600, 26], [81678, 23], [81706, 86], [81900, 26], [81978, 23], [82006, 86], [82200, 26], [82277, 24], [82306, 86], [82500, 26], [82577, 24], [82606, 86], [82800, 26], [82877, 24], [82906, 86], [83100, 26], [83176, 25], [83206, 86], [83400, 27], [83476, 25], [83506, 86], [83700, 27], [83776, 25], [83806, 86], [84000, 27], [84076, 25], [84106, 86], [84300, 28], [84375, 26], [84406, 86], [84600, 28], [84674, 27], [84706, 86], [84900, 28], [84973, 28], [85006, 86], [85200, 29], [85273, 28], [85306, 86], [85500, 29], [85572, 29], [85606, 86], [85800, 29], [85871, 30], [85906, 86], [86100, 30], [86170, 31], [86206, 86], [86400, 30], [86469, 32], [86506, 86], [86700, 31], [86768, 33], [86806, 86], [87000, 32], [87067, 34], [87106, 86], [87300, 33], [87365, 33], [87407, 85], [87600, 34], [87663, 33], [87709, 83], [87900, 35], [87963, 31], [88011, 81], [88200, 37], [88261, 31], [88312, 80], [88500, 39], [88559, 31], [88614, 78], [88800, 43], [88856, 31], [88915, 77], [89100, 46], [89152, 33], [89217, 75], [89400, 83], [89518, 74], [89700, 81], [89820, 72]], "point": [95, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan30", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -0.25, "y": 0.9304675, "z": 1.0}, "object_poses": [{"objectName": "Potato_ed324e47", "position": {"x": 0.7736288, "y": 1.28645647, "z": -1.44805622}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ff5a3b19", "position": {"x": -0.826452255, "y": 0.937239945, "z": -0.9048703}, "rotation": {"x": 0.0, "y": 135.0, "z": 0.0}}, {"objectName": "Ladle_ff5a3b19", "position": {"x": -1.01550388, "y": 0.937239945, "z": -0.719089866}, "rotation": {"x": 0.0, "y": 135.0, "z": 0.0}}, {"objectName": "DishSponge_3cec4766", "position": {"x": -0.909206867, "y": 0.706138849, "z": -0.273891777}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_20771abe", "position": {"x": 3.06999969, "y": 0.748074353, "z": -0.100778744}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Kettle_449c396f", "position": {"x": -0.4198, "y": 0.902399957, "z": -1.2699}, "rotation": {"x": 0.0, "y": 315.000153, "z": 0.0}}, {"objectName": "Spoon_0fe289e3", "position": {"x": 2.98839283, "y": 0.713714, "z": 0.560307443}, "rotation": {"x": 0.0, "y": 180.000046, "z": 0.0}}, {"objectName": "Spoon_0fe289e3", "position": {"x": 3.06999969, "y": 0.748487055, "z": 0.03222303}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Fork_12a59a81", "position": {"x": 3.27791429, "y": 0.901348531, "z": -0.499501377}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_12a59a81", "position": {"x": 0.7810866, "y": 0.91150856, "z": 0.452516764}, "rotation": {"x": 0.0, "y": 36.8105049, "z": 0.0}}, {"objectName": "CellPhone_bf099a5c", "position": {"x": -0.7864864, "y": 0.323989272, "z": 1.69338763}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ba5c7501", "position": {"x": 0.0125830695, "y": 0.8953, "z": -1.33757544}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "SaltShaker_c39806f6", "position": {"x": 2.88865542, "y": 0.8971061, "z": -0.399919033}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6d2fd9f8", "position": {"x": -0.8067038, "y": 0.896606147, "z": -0.72269547}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Bowl_46f611f6", "position": {"x": 3.031842, "y": 1.46167791, "z": -1.18682539}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_46f611f6", "position": {"x": 0.760998249, "y": 1.67199469, "z": -1.41078711}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_5121dd32", "position": {"x": 3.35452032, "y": 0.90990293, "z": -0.151345015}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_bfd17fb4", "position": {"x": 2.96650839, "y": 0.937789261, "z": 0.947632849}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c285ee2", "position": {"x": 0.710450053, "y": 0.960635, "z": 0.6273035}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_be22aed5", "position": {"x": 0.622025, "y": 0.908792436, "z": 0.8278844}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_be22aed5", "position": {"x": 1.24237132, "y": 0.883928835, "z": -1.47598743}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_be22aed5", "position": {"x": 0.5559098, "y": 0.908792436, "z": 0.4991823}, "rotation": {"x": 0.0, "y": 36.8105049, "z": 0.0}}, {"objectName": "Plate_bf5eb4b5", "position": {"x": 3.02811241, "y": 0.190163016, "z": 0.7706443}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bf5eb4b5", "position": {"x": 0.9678644, "y": 0.923881, "z": 0.322243452}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_20771abe", "position": {"x": 3.11146, "y": 0.748074353, "z": 0.231725678}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_5121dd32", "position": {"x": 3.10161734, "y": 0.11010617, "z": 1.57320583}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Potato_ed324e47", "position": {"x": -1.08558643, "y": 1.03611887, "z": 1.52397478}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Fork_12a59a81", "position": {"x": -0.8683, "y": 0.518537641, "z": 0.07609176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ff5a3b19", "position": {"x": 3.02804923, "y": 0.212658644, "z": -0.404700667}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_7a7d333f", "position": {"x": -0.6002, "y": 0.902399957, "z": -1.0901}, "rotation": {"x": 0.0, "y": 134.999832, "z": 0.0}}, {"objectName": "Kettle_449c396f", "position": {"x": 3.02804971, "y": 0.176762164, "z": -0.828194141}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_64c860f2", "position": {"x": -1.15363979, "y": 0.9152999, "z": 0.399594069}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_3e58a77d", "position": {"x": -0.007081926, "y": 0.925918341, "z": -1.72861958}, "rotation": {"x": 0.0, "y": 135.0, "z": 0.0}}, {"objectName": "Lettuce_99ab2e9c", "position": {"x": 1.008, "y": 0.9516874, "z": -1.50391865}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_be22aed5", "position": {"x": 3.18144274, "y": 1.462396, "z": -1.74675}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_c39806f6", "position": {"x": 3.31527376, "y": 1.46086872, "z": -1.13785076}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c285ee2", "position": {"x": 0.7736288, "y": 1.30053151, "z": -1.559781}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6d2fd9f8", "position": {"x": 0.5472715, "y": 0.90440613, "z": 0.140089035}, "rotation": {"x": 0.0, "y": 36.8105049, "z": 0.0}}, {"objectName": "ButterKnife_db774b98", "position": {"x": 0.622025, "y": 0.90814203, "z": 0.527013063}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_1e1bef89", "position": {"x": 1.008, "y": 0.7851206, "z": -1.4332341}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_000ae861", "position": {"x": 0.341, "y": 0.8731569, "z": -1.649}, "rotation": {"x": 0.0, "y": 167.065247, "z": 0.0}}, {"objectName": "Bread_1e8ab5c8", "position": {"x": 1.53420079, "y": 0.9728919, "z": 0.48683244}, "rotation": {"x": 1.94071845e-06, "y": 289.287476, "z": -2.87591632e-07}}, {"objectName": "CellPhone_bf099a5c", "position": {"x": 0.3766, "y": 0.703083754, "z": -1.43432426}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_3cec4766", "position": {"x": -0.7864864, "y": 0.1306562, "z": 1.34340417}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_bfd17fb4", "position": {"x": 3.07, "y": 0.7900266, "z": 0.0987239555}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_0fe289e3", "position": {"x": 0.8873, "y": 0.909229338, "z": 0.8278844}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_46f611f6", "position": {"x": -0.86270237, "y": 0.8974154, "z": 0.846407056}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ba5c7501", "position": {"x": 0.20406355, "y": 1.25475049, "z": -1.74189985}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}], "object_toggles": [], "random_seed": 651695687, "scene_num": 30}, "task_id": "trial_T20190906_200308_075401", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A23HZ18KTCK2DA_33PPUNGG3BMALII1MC2U9M1Y4WLRZ1", "high_descs": ["Turn around and move to the kitchen island.", "Pick up the bowl on the left.", "Turn around and go to the microwave.", "Open the microwave and put the bowl in.  Turn the microwave on.  Take the bowl out of the microwave when done.", "Go to the cabinet to the left of the toaster.", "Open the cabinet.  Put the bowl in the cabinet on the bottom shelf."], "task_desc": "Warm up a bowl to put it away.", "votes": [1, 1, 1]}, {"assignment_id": "AM2KK02JXXW48_3BF51CHDTYR0U3Y8MDD8PJW6ZG70HO", "high_descs": ["Turn around and face the center island.", "Pick up the bowl on the left from the island. ", "Bring the bowl to the microwave. ", "Heat the bowl in the microwave. ", "Bring the bowl to the toaster. ", "Put the bowl in the cabinet to the left of the mirror. "], "task_desc": "Put a heated bowl in the cabinet left of the mirror. ", "votes": [1, 1, 1]}, {"assignment_id": "AUTYWXILTACCR_3WR9XG3T66SK02AXS5EYA9XP2SB47W", "high_descs": ["Turn to the counter behind you.", "Pick up a cup from the counter.", "Turn around and walk to the microwave ", "Warm the cup in the microwave.", "Turn to your left and walk towards the cabinet above the toaster.", "Place the cup in the cabinet."], "task_desc": "Put a warmed cup in a cabinet.", "votes": [1, 0, 1]}]}}