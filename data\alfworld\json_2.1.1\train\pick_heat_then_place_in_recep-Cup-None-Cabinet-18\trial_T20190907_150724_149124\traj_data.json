{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000296.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000298.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000299.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 46}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|3|4|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [2.699269056, 2.699269056, 2.3053656, 2.3053656, 0.559507848, 0.559507848]], "coordinateReceptacleObjectId": ["Cabinet", [1.775600672, 1.775600672, 2.974496604, 2.974496604, 1.69071698, 1.69071698]], "forceVisible": true, "objectId": "Cup|+00.67|+00.14|+00.58"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|6|2|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|0|7|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [2.699269056, 2.699269056, 2.3053656, 2.3053656, 0.559507848, 0.559507848]], "coordinateReceptacleObjectId": ["Cabinet", [-3.102313756, -3.102313756, 2.974496604, 2.974496604, 1.69071698, 1.69071698]], "forceVisible": true, "objectId": "Cup|+00.67|+00.14|+00.58", "receptacleObjectId": "Cabinet|-00.78|+00.42|+00.74"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+00.44|+00.42|+00.74"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [66, 214, 240, 246], "mask": [[63966, 175], [64267, 173], [64568, 171], [64869, 169], [65170, 167], [65470, 166], [65770, 164], [66070, 163], [66371, 161], [66675, 156], [66976, 154], [67277, 152], [67578, 150], [67879, 148], [68180, 146], [68481, 144], [68782, 142], [69083, 140], [69384, 138], [69685, 136], [69985, 135], [70286, 133], [70587, 131], [70888, 129], [71189, 127], [71490, 125], [71791, 122], [72092, 120], [72393, 118], [72694, 116], [72995, 114], [73296, 112], [73597, 110]], "point": [153, 229]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.67|+00.14|+00.58"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [157, 214, 171, 219], "mask": [[64057, 15], [64357, 14], [64658, 12], [64959, 10], [65260, 8], [65562, 3]], "point": [164, 215]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+00.44|+00.42|+00.74"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [70, 214, 259, 300], "mask": [[63970, 24], [64106, 27], [64139, 5], [64271, 22], [64407, 25], [64438, 6], [64572, 21], [64707, 24], [64736, 9], [64873, 19], [65008, 22], [65035, 10], [65174, 18], [65308, 21], [65334, 11], [65475, 16], [65609, 19], [65633, 12], [65776, 15], [65909, 18], [65932, 13], [66077, 13], [66210, 16], [66231, 15], [66378, 12], [66510, 15], [66530, 16], [66678, 12], [66810, 14], [66829, 17], [66979, 11], [67110, 13], [67128, 18], [67280, 9], [67411, 11], [67427, 19], [67581, 8], [67711, 10], [67726, 20], [67882, 7], [68011, 9], [68025, 22], [68183, 6], [68311, 8], [68324, 23], [68484, 4], [68612, 6], [68623, 24], [68785, 3], [68912, 5], [68922, 25], [69086, 2], [69212, 4], [69221, 26], [69387, 1], [69512, 3], [69520, 28], [69813, 1], [69819, 29], [70118, 30], [70417, 31], [70716, 32], [71015, 33], [71314, 35], [71613, 36], [71913, 36], [72213, 36], [72513, 36], [72813, 37], [73113, 37], [73413, 37], [73713, 37], [74013, 37], [74313, 37], [74613, 38], [74913, 38], [75213, 38], [75513, 38], [75813, 38], [76113, 39], [76413, 39], [76713, 39], [77013, 39], [77312, 40], [77612, 40], [77912, 41], [78211, 42], [78511, 42], [78811, 42], [79111, 42], [79410, 44], [79710, 44], [80010, 44], [80310, 44], [80609, 45], [80909, 46], [81209, 46], [81509, 46], [81809, 46], [82109, 46], [82409, 46], [82709, 47], [83009, 47], [83309, 47], [83610, 46], [83910, 46], [84210, 47], [84510, 47], [84810, 47], [85110, 47], [85410, 47], [85710, 47], [86010, 48], [86310, 48], [86611, 47], [86911, 47], [87211, 47], [87511, 48], [87811, 48], [88111, 48], [88411, 48], [88711, 48], [89011, 48], [89311, 49], [89612, 48], [89912, 48]], "point": [209, 268]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.67|+00.14|+00.58", "placeStationary": true, "receptacleObjectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 49], [26553, 107], [26788, 48], [26855, 105], [27087, 48], [27155, 105], [27387, 48], [27459, 101], [27686, 49], [27760, 99], [27985, 50], [28055, 3], [28060, 99], [28285, 50], [28355, 3], [28360, 99], [28584, 51], [28655, 4], [28660, 99], [28883, 52], [28955, 4], [28961, 98], [29183, 52], [29255, 4], [29260, 98], [29482, 53], [29555, 3], [29560, 98], [29781, 54], [29855, 3], [29860, 98], [30081, 54], [30155, 3], [30160, 98], [30380, 55], [30455, 2], [30459, 98], [30679, 56], [30755, 2], [30759, 98], [30978, 57], [31058, 99], [31278, 58], [31357, 100], [31577, 59], [31656, 101], [31876, 60], [31955, 101], [32176, 60], [32255, 101], [32475, 61], [32555, 101], [32775, 61], [32855, 101], [33075, 61], [33155, 100], [33375, 62], [33454, 101], [33675, 63], [33753, 102], [33975, 67], [34049, 106], [34275, 180], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 86], [23597, 65], [23795, 84], [23901, 61], [24094, 84], [24202, 60], [24394, 83], [24503, 59], [24693, 85], [24803, 59], [24992, 86], [25103, 58], [25292, 86], [25403, 58], [25591, 87], [25703, 58], [25890, 88], [26002, 59], [26190, 88], [26302, 58], [26489, 49], [26553, 25], [26602, 58], [26788, 48], [26855, 23], [26902, 58], [27087, 48], [27155, 23], [27201, 59], [27387, 48], [27459, 19], [27501, 59], [27686, 49], [27760, 19], [27801, 58], [27985, 50], [28055, 3], [28060, 19], [28101, 58], [28285, 50], [28355, 3], [28360, 19], [28400, 59], [28584, 51], [28655, 4], [28660, 19], [28700, 59], [28883, 52], [28955, 4], [28961, 18], [29000, 59], [29183, 52], [29255, 4], [29260, 19], [29300, 58], [29482, 53], [29555, 3], [29560, 19], [29599, 59], [29781, 54], [29855, 3], [29860, 19], [29899, 59], [30081, 54], [30155, 3], [30160, 19], [30199, 59], [30380, 55], [30455, 2], [30459, 20], [30499, 58], [30679, 56], [30755, 2], [30759, 20], [30798, 59], [30978, 57], [31058, 22], [31098, 59], [31278, 58], [31357, 23], [31398, 59], [31577, 59], [31656, 24], [31698, 59], [31876, 60], [31955, 25], [31997, 59], [32176, 60], [32255, 25], [32297, 59], [32475, 61], [32555, 25], [32597, 59], [32775, 61], [32855, 26], [32897, 59], [33075, 61], [33155, 26], [33196, 59], [33375, 62], [33454, 27], [33496, 59], [33675, 63], [33753, 28], [33795, 60], [33975, 67], [34049, 32], [34095, 60], [34275, 107], [34395, 60], [34576, 107], [34694, 60], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.67|+00.14|+00.58"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [177, 79, 202, 116], "mask": [[23582, 15], [23879, 22], [24178, 24], [24477, 26], [24778, 25], [25078, 25], [25378, 25], [25678, 25], [25978, 24], [26278, 24], [26578, 24], [26878, 24], [27178, 23], [27478, 23], [27779, 22], [28079, 22], [28379, 21], [28679, 21], [28979, 21], [29279, 21], [29579, 20], [29879, 20], [30179, 20], [30479, 20], [30779, 19], [31080, 18], [31380, 18], [31680, 18], [31980, 17], [32280, 17], [32580, 17], [32881, 16], [33181, 15], [33481, 15], [33781, 14], [34081, 14], [34382, 13], [34683, 11]], "point": [189, 96]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 49], [26553, 107], [26788, 48], [26855, 105], [27087, 48], [27155, 105], [27387, 48], [27459, 101], [27686, 49], [27760, 99], [27985, 50], [28055, 3], [28060, 99], [28285, 50], [28355, 3], [28360, 99], [28584, 51], [28655, 4], [28660, 99], [28883, 52], [28955, 4], [28961, 98], [29183, 52], [29255, 4], [29260, 98], [29482, 53], [29555, 3], [29560, 98], [29781, 54], [29855, 3], [29860, 98], [30081, 54], [30155, 3], [30160, 98], [30380, 55], [30455, 2], [30459, 98], [30679, 56], [30755, 2], [30759, 98], [30978, 57], [31058, 99], [31278, 58], [31357, 100], [31577, 59], [31656, 101], [31876, 60], [31955, 101], [32176, 60], [32255, 101], [32475, 61], [32555, 101], [32775, 61], [32855, 101], [33075, 61], [33155, 100], [33375, 62], [33454, 101], [33675, 63], [33753, 102], [33975, 67], [34049, 106], [34275, 180], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 31], [46281, 30], [46582, 27], [46882, 26], [47182, 25], [47482, 24], [47782, 23], [48082, 22], [48383, 20], [48683, 19], [48983, 19], [49283, 18], [49583, 18], [49883, 17], [50183, 17], [50484, 15], [50784, 15], [51084, 15], [51384, 15], [51684, 15], [51984, 15], [52285, 13], [52585, 13], [52885, 13], [53185, 13], [53485, 13], [53785, 14], [54085, 14], [54386, 14], [54686, 14], [54986, 14], [55286, 15], [55586, 15], [55886, 16], [56187, 15], [56487, 15], [56787, 16], [57087, 16], [57387, 16], [57687, 17], [57987, 17], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.78|+00.42|+00.74"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [184, 128, 299, 198], "mask": [[38287, 1], [38587, 113], [38887, 113], [39186, 114], [39486, 114], [39786, 114], [40086, 114], [40386, 114], [40686, 114], [40986, 113], [41286, 113], [41585, 113], [41885, 113], [42185, 112], [42485, 112], [42785, 111], [43085, 111], [43385, 110], [43684, 110], [43984, 110], [44284, 109], [44584, 109], [44884, 108], [45184, 108], [45484, 107], [45786, 105], [46088, 102], [46389, 101], [46691, 98], [46992, 96], [47293, 95], [47594, 93], [47895, 92], [48196, 90], [48497, 89], [48798, 87], [49098, 87], [49399, 85], [49699, 85], [50000, 83], [50300, 82], [50601, 81], [50901, 80], [51201, 80], [51501, 79], [51801, 79], [52101, 78], [52402, 77], [52702, 76], [53002, 76], [53302, 75], [53602, 74], [53901, 75], [54201, 74], [54500, 75], [54800, 74], [55100, 74], [55399, 74], [55699, 74], [55998, 74], [56298, 74], [56598, 73], [56897, 73], [57197, 73], [57497, 72], [57796, 73], [58096, 72], [58396, 72], [58696, 71], [58995, 72], [59295, 71]], "point": [241, 162]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.67|+00.14|+00.58", "placeStationary": true, "receptacleObjectId": "Cabinet|-00.78|+00.42|+00.74"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [186, 129, 299, 290], "mask": [[38590, 110], [38889, 111], [39189, 111], [39489, 110], [39789, 110], [40089, 109], [40389, 108], [40499, 1], [40689, 108], [40799, 1], [40988, 108], [41098, 2], [41288, 108], [41398, 2], [41588, 107], [41697, 3], [41888, 107], [41997, 3], [42188, 106], [42296, 4], [42488, 106], [42595, 5], [42788, 105], [42895, 5], [43087, 106], [43194, 6], [43387, 105], [43494, 6], [43687, 105], [43793, 7], [43987, 104], [44093, 7], [44287, 104], [44392, 8], [44587, 103], [44692, 8], [44887, 102], [44991, 9], [45186, 103], [45291, 9], [45486, 102], [45590, 10], [45786, 102], [45889, 11], [46088, 99], [46189, 11], [46389, 98], [46488, 12], [46691, 95], [46788, 12], [46992, 94], [47087, 13], [47293, 92], [47387, 13], [47594, 91], [47686, 14], [47895, 89], [47986, 14], [48196, 40], [48240, 44], [48285, 15], [48497, 38], [48541, 42], [48585, 15], [48798, 31], [48842, 40], [48884, 16], [49098, 27], [49127, 8], [49142, 40], [49184, 16], [49399, 36], [49442, 39], [49483, 17], [49699, 30], [49741, 40], [49782, 18], [50000, 29], [50040, 40], [50082, 18], [50300, 28], [50340, 40], [50381, 19], [50601, 27], [50640, 39], [50681, 19], [50901, 27], [50940, 39], [50980, 20], [51201, 27], [51239, 39], [51280, 20], [51501, 27], [51538, 40], [51579, 21], [51801, 26], [51839, 38], [51879, 21], [52101, 26], [52140, 37], [52178, 22], [52402, 25], [52440, 36], [52478, 22], [52702, 24], [52740, 36], [52777, 23], [53002, 24], [53040, 35], [53076, 24], [53302, 23], [53341, 33], [53376, 24], [53602, 23], [53641, 33], [53675, 25], [53901, 24], [53941, 32], [53975, 25], [54201, 25], [54241, 32], [54274, 26], [54500, 26], [54540, 32], [54574, 26], [54800, 26], [54840, 32], [54873, 27], [55100, 26], [55139, 32], [55173, 27], [55399, 28], [55439, 32], [55472, 28], [55699, 28], [55738, 32], [55772, 28], [55998, 29], [56038, 32], [56071, 29], [56298, 29], [56338, 31], [56371, 29], [56598, 30], [56637, 32], [56670, 30], [56897, 31], [56937, 31], [56969, 31], [57197, 31], [57237, 30], [57269, 31], [57497, 32], [57537, 30], [57568, 32], [57796, 70], [57868, 32], [58096, 70], [58167, 33], [58396, 69], [58467, 33], [58696, 69], [58766, 34], [59066, 34], [59365, 35], [59665, 35], [59966, 34], [60266, 34], [60566, 34], [60867, 33], [61167, 33], [61468, 32], [61768, 32], [62068, 32], [62369, 31], [62669, 31], [62969, 31], [63270, 30], [63570, 30], [63870, 30], [64171, 29], [64471, 29], [64772, 28], [65072, 28], [65372, 28], [65673, 27], [65973, 27], [66273, 27], [66574, 26], [66874, 26], [67175, 25], [67475, 25], [67775, 25], [68076, 24], [68376, 24], [68676, 24], [68977, 23], [69277, 23], [69578, 22], [69878, 22], [70178, 22], [70479, 21], [70779, 21], [71079, 21], [71380, 20], [71680, 20], [71981, 19], [72281, 19], [72581, 19], [72882, 18], [73182, 18], [73482, 18], [73783, 17], [74083, 17], [74384, 16], [74684, 16], [74984, 16], [75285, 15], [75585, 15], [75885, 15], [76186, 14], [76486, 14], [76787, 13], [77087, 13], [77387, 13], [77688, 12], [77988, 12], [78288, 12], [78589, 11], [78889, 11], [79190, 10], [79490, 10], [79790, 10], [80091, 9], [80391, 9], [80691, 9], [80992, 8], [81292, 8], [81593, 7], [81893, 7], [82193, 7], [82494, 6], [82794, 6], [83094, 6], [83395, 5], [83695, 5], [83996, 4], [84296, 4], [84596, 4], [84897, 3], [85197, 3], [85497, 3], [85798, 2], [86098, 2], [86399, 1], [86699, 1], [86999, 1]], "point": [242, 195]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.78|+00.42|+00.74"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [180, 129, 299, 290], "mask": [[38590, 110], [38889, 111], [39189, 111], [39489, 110], [39789, 110], [40089, 109], [40389, 108], [40499, 1], [40689, 108], [40799, 1], [40988, 108], [41098, 2], [41288, 108], [41398, 2], [41588, 107], [41697, 3], [41888, 107], [41997, 3], [42188, 106], [42296, 4], [42488, 106], [42595, 5], [42788, 105], [42895, 5], [43087, 106], [43194, 6], [43387, 105], [43494, 6], [43687, 105], [43793, 7], [43987, 104], [44093, 7], [44287, 104], [44392, 8], [44587, 103], [44692, 8], [44887, 102], [44991, 9], [45186, 103], [45291, 9], [45486, 102], [45590, 10], [45786, 102], [45889, 11], [46086, 101], [46189, 11], [46386, 101], [46488, 12], [46686, 100], [46788, 12], [46986, 100], [47087, 13], [47285, 100], [47387, 13], [47585, 100], [47686, 14], [47885, 99], [47986, 14], [48185, 51], [48240, 44], [48285, 15], [48485, 50], [48541, 42], [48585, 15], [48785, 44], [48842, 40], [48884, 16], [49085, 27], [49113, 12], [49127, 8], [49142, 40], [49184, 16], [49384, 24], [49418, 17], [49442, 39], [49483, 17], [49684, 23], [49720, 9], [49741, 40], [49782, 18], [49984, 22], [50021, 8], [50040, 40], [50082, 18], [50284, 21], [50322, 6], [50340, 40], [50381, 19], [50584, 21], [50623, 5], [50640, 39], [50681, 19], [50884, 21], [50923, 5], [50940, 39], [50980, 20], [51184, 21], [51223, 5], [51239, 39], [51280, 20], [51483, 22], [51524, 4], [51538, 40], [51579, 21], [51783, 22], [51823, 4], [51839, 38], [51879, 21], [52083, 22], [52123, 4], [52140, 37], [52178, 22], [52383, 22], [52423, 4], [52440, 36], [52478, 22], [52683, 22], [52722, 4], [52740, 36], [52777, 23], [52983, 22], [53021, 5], [53040, 35], [53076, 24], [53283, 22], [53321, 4], [53341, 33], [53376, 24], [53582, 23], [53620, 5], [53641, 33], [53675, 25], [53882, 23], [53920, 5], [53941, 32], [53975, 25], [54182, 23], [54219, 7], [54241, 32], [54274, 26], [54482, 23], [54519, 7], [54540, 32], [54574, 26], [54782, 23], [54818, 8], [54840, 32], [54873, 27], [55082, 23], [55118, 8], [55139, 32], [55173, 27], [55382, 23], [55417, 10], [55439, 32], [55472, 28], [55682, 23], [55716, 11], [55738, 32], [55772, 28], [55981, 25], [56016, 11], [56038, 32], [56071, 29], [56281, 25], [56315, 12], [56338, 31], [56371, 29], [56581, 27], [56614, 14], [56637, 32], [56670, 30], [56881, 47], [56937, 31], [56969, 31], [57181, 47], [57237, 30], [57269, 31], [57481, 48], [57537, 30], [57568, 32], [57781, 85], [57868, 32], [58080, 86], [58167, 33], [58380, 85], [58467, 33], [58680, 85], [58766, 34], [59066, 34], [59365, 35], [59665, 35], [59966, 34], [60266, 34], [60566, 34], [60867, 33], [61167, 33], [61468, 32], [61768, 32], [62068, 32], [62369, 31], [62669, 31], [62969, 31], [63270, 30], [63570, 30], [63870, 30], [64171, 29], [64471, 29], [64772, 28], [65072, 28], [65372, 28], [65673, 27], [65973, 27], [66273, 27], [66574, 26], [66874, 26], [67175, 25], [67475, 25], [67775, 25], [68076, 24], [68376, 24], [68676, 24], [68977, 23], [69277, 23], [69578, 22], [69878, 22], [70178, 22], [70479, 21], [70779, 21], [71079, 21], [71380, 20], [71680, 20], [71981, 19], [72281, 19], [72581, 19], [72882, 18], [73182, 18], [73482, 18], [73783, 17], [74083, 17], [74384, 16], [74684, 16], [74984, 16], [75285, 15], [75585, 15], [75885, 15], [76186, 14], [76486, 14], [76787, 13], [77087, 13], [77387, 13], [77688, 12], [77988, 12], [78288, 12], [78589, 11], [78889, 11], [79190, 10], [79490, 10], [79790, 10], [80091, 9], [80391, 9], [80691, 9], [80992, 8], [81292, 8], [81593, 7], [81893, 7], [82193, 7], [82494, 6], [82794, 6], [83094, 6], [83395, 5], [83695, 5], [83996, 4], [84296, 4], [84596, 4], [84897, 3], [85197, 3], [85497, 3], [85798, 2], [86098, 2], [86399, 1], [86699, 1], [86999, 1]], "point": [239, 195]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan18", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": 0.5, "y": 0.900999844, "z": 3.75}, "object_poses": [{"objectName": "DishSponge_6d7ef995", "position": {"x": 0.8712312, "y": 0.7719865, "z": 0.619083345}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": -0.09347224, "y": 0.7612178, "z": 6.633133}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": -0.6113827, "y": 0.9938294, "z": 0.486808717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": 0.0566475242, "y": 0.7869452, "z": 0.3960101}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": -0.486266434, "y": 0.7606369, "z": 6.151951}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": 0.3052411, "y": 0.7606369, "z": 6.541539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": -0.6113827, "y": 1.0427227, "z": 0.120408714}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_8e8a26e1", "position": {"x": 2.05195951, "y": 1.51175356, "z": 5.876}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_8e8a26e1", "position": {"x": 0.674817264, "y": 0.139876962, "z": 0.5763414}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 1.49008977, "y": 0.141221583, "z": 3.217805}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 0.212104708, "y": 0.7606825, "z": 6.196145}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": 1.40386784, "y": 1.02240038, "z": 3.23104}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": 0.9990971, "y": 1.04216564, "z": 0.2120077}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 1.40633225, "y": 0.135627449, "z": 1.93817019}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 1.15505934, "y": 0.135627449, "z": 3.14388132}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": -0.3842585, "y": 1.0811137, "z": 0.395208716}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": 1.49322724, "y": 1.0811137, "z": 0.213347912}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": -0.378325045, "y": 0.7550884, "z": 6.698186}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": 0.674817264, "y": 0.1387325, "z": 0.418435961}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 1.13578975, "y": 1.0672332, "z": 4.7165}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": -0.467251956, "y": 0.8346215, "z": 6.604039}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_9f9e1c57", "position": {"x": 2.05195951, "y": 1.51464689, "z": 5.626}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_b7525b79", "position": {"x": 1.09999228, "y": 1.0487318, "z": 0.4349975}, "rotation": {"x": -0.000633760646, "y": -0.0002460388, "z": 0.00370135019}}, {"objectName": "Egg_8021024b", "position": {"x": -0.31275335, "y": 0.810111, "z": 6.416539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": 0.058663398, "y": 0.789788663, "z": 6.261198}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": -0.221677959, "y": 0.7606369, "z": 6.941612}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 1.95673859, "y": 0.155250221, "z": 6.46908}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": 1.593, "y": 0.9816944, "z": 3.25}, "rotation": {"x": 0.0, "y": 255.000153, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 1.49008977, "y": 0.141221583, "z": 2.1599412}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 1.32257462, "y": 0.135627449, "z": 2.49835682}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": 1.4499042, "y": 0.9321, "z": 2.05034876}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": -0.5523339, "y": 0.144206226, "z": 0.658612132}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": 1.58258653, "y": 1.0077, "z": 0.4407184}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_bd2376ff", "position": {"x": 0.544847965, "y": 1.09698534, "z": 0.303608239}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_73d89a5b", "position": {"x": 1.559894, "y": 0.988599956, "z": 4.2942}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": 0.25760448, "y": 0.7949763, "z": 6.71904469}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": -0.6217506, "y": 0.760130465, "z": 6.416539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": -0.3842585, "y": 0.9877, "z": 0.6700087}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_08687688", "position": {"x": 1.32, "y": 0.988599956, "z": 3.848}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": -0.6870909, "y": 1.04216564, "z": 0.212008715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 0.2607262, "y": 0.7715096, "z": 0.618839443}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_8e8a26e1", "position": {"x": -0.221677959, "y": 0.756232858, "z": 6.787113}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": -0.0113787055, "y": 0.773074746, "z": 0.451717436}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": 0.2733106, "y": 1.09214664, "z": 0.245674372}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2613846173, "scene_num": 18}, "task_id": "trial_T20190907_150724_149124", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AISNLDPD2DFEG_3OUYGIZWRAFS7KWW5QLDYSYXMY3P0N", "high_descs": ["Turn left, go forward, turn right at the stove, go forward", "Open the bottom cupboard, take a glass, close the cupboard", "Turn around, go a bit forward, turn left, go a bit forward, turn left in front of the microwave", "Open the microwave, put the glass in it, close the microwave, start the microwave, open the microwave, get the glass, close the microwave", "Turn around, go a bit forward, turn left, go a bit forward, turn left to reach the rightmost cupboard", "Open the bottom cupboard, put the glass in the bottom shelf, close the cupboard"], "task_desc": "Put a glass in the microwave, put the glass in a cupboard", "votes": [1, 1, 1]}, {"assignment_id": "A320QA9HJFUOZO_3VAR3R6G1SIS49PYLULD27ZPVNPO8Q", "high_descs": ["Walk forward up to the kitchen counter past the dishwasher", "Open the bottom cabinet and remove the white cup from it", "Turn left and walk over to the microwave", "Heat the cup in the microwave then remove it", "Walk a couple feet from the counter then turn back toward it", "Open the bottom right cabinet and put the cup in it"], "task_desc": "Put the heated cup in the kitchen cabinet", "votes": [1, 1, 1]}, {"assignment_id": "A1KFTQRBMXSC13_3Z7ISHFUH3C43D35N3WUI56TFRYZ89", "high_descs": ["Walk straight ahead and open the lower cabinet. ", "Grab a cup from the cabinet.", "Turn right, walk to the microwave, open it and place the cup inside next to the mug. ", "Turn on the microwave and wait for it to turn off. ", "Open the microwave and remove the cup.", "Open the cabinet on the far right below the microwave and place the cup next to the soap dispenser. Close the cabinet door. "], "task_desc": "Find a cup in a cabinet, microwave it, place it inside a different cabinet.", "votes": [0, 1, 1]}]}}