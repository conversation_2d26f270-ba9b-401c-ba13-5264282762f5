
(define (problem plan_trial_T20190908_164354_508480)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Bathtub_bar__minus_00_dot_98_bar__minus_00_dot_72_bar__minus_02_dot_93 - object
        Candle_bar__minus_00_dot_12_bar__plus_01_dot_05_bar__minus_02_dot_01 - object
        Candle_bar__minus_02_dot_25_bar__plus_00_dot_68_bar__minus_01_dot_24 - object
        Cloth_bar__minus_00_dot_94_bar__plus_00_dot_06_bar__minus_02_dot_86 - object
        Cloth_bar__minus_01_dot_69_bar__plus_00_dot_06_bar__minus_02_dot_86 - object
        Cloth_bar__minus_01_dot_94_bar__plus_00_dot_06_bar__minus_02_dot_99 - object
        Faucet_bar__minus_00_dot_01_bar__plus_01_dot_72_bar__minus_02_dot_98 - object
        Faucet_bar__minus_00_dot_02_bar__plus_00_dot_47_bar__minus_02_dot_97 - object
        Faucet_bar__minus_02_dot_35_bar__plus_00_dot_96_bar__minus_02_dot_03 - object
        HandTowel_bar__minus_02_dot_37_bar__plus_01_dot_52_bar__minus_01_dot_51 - object
        LightSwitch_bar__minus_01_dot_25_bar__plus_01_dot_42_bar__plus_00_dot_00 - object
        Mirror_bar__minus_02_dot_45_bar__plus_01_dot_64_bar__minus_02_dot_03 - object
        Plunger_bar__minus_00_dot_12_bar__plus_00_dot_00_bar__minus_02_dot_38 - object
        ScrubBrush_bar__minus_00_dot_16_bar__plus_00_dot_00_bar__minus_02_dot_16 - object
        ShowerCurtain_bar__minus_02_dot_33_bar__plus_01_dot_96_bar__minus_02_dot_46 - object
        Sink_bar__minus_02_dot_10_bar_00_dot_00_bar__minus_02_dot_03 - object
        SoapBar_bar__minus_02_dot_20_bar__plus_00_dot_68_bar__minus_01_dot_47 - object
        SoapBottle_bar__minus_00_dot_14_bar__plus_01_dot_05_bar__minus_01_dot_81 - object
        SoapBottle_bar__minus_02_dot_11_bar__plus_00_dot_38_bar__minus_01_dot_31 - object
        SprayBottle_bar__minus_02_dot_07_bar__plus_00_dot_68_bar__minus_01_dot_24 - object
        SprayBottle_bar__minus_02_dot_34_bar__plus_00_dot_68_bar__minus_01_dot_39 - object
        TissueBox_bar__minus_02_dot_19_bar__plus_00_dot_67_bar__minus_01_dot_12 - object
        ToiletPaper_bar__minus_00_dot_07_bar__plus_00_dot_99_bar__minus_01_dot_34 - object
        ToiletPaper_bar__minus_00_dot_14_bar__plus_01_dot_04_bar__minus_01_dot_88 - object
        ToiletPaper_bar__minus_01_dot_20_bar__plus_00_dot_48_bar__minus_02_dot_53 - object
        Towel_bar__minus_00_dot_05_bar__plus_01_dot_85_bar__minus_01_dot_89 - object
        Bathtub_bar__minus_00_dot_98_bar__minus_00_dot_72_bar__minus_02_dot_93_bar_BathtubBasin - receptacle
        Drawer_bar__minus_02_dot_12_bar__plus_00_dot_17_bar__minus_01_dot_31 - receptacle
        Drawer_bar__minus_02_dot_12_bar__plus_00_dot_46_bar__minus_01_dot_31 - receptacle
        GarbageCan_bar__minus_00_dot_22_bar__plus_00_dot_00_bar__minus_01_dot_42 - receptacle
        HandTowelHolder_bar__minus_02_dot_41_bar__plus_01_dot_63_bar__minus_01_dot_51 - receptacle
        SideTable_bar__minus_02_dot_12_bar__plus_00_dot_46_bar__minus_01_dot_31 - receptacle
        Sink_bar__minus_02_dot_10_bar_00_dot_00_bar__minus_02_dot_03_bar_SinkBasin - receptacle
        ToiletPaperHanger_bar__plus_00_dot_00_bar__plus_00_dot_99_bar__minus_01_dot_40 - receptacle
        Toilet_bar__minus_00_dot_47_bar__plus_00_dot_00_bar__minus_01_dot_88 - receptacle
        TowelHolder_bar__plus_00_dot_06_bar__plus_01_dot_83_bar__minus_01_dot_89 - receptacle
        loc_bar__minus_6_bar__minus_6_bar_3_bar_0 - location
        loc_bar__minus_7_bar__minus_2_bar_2_bar_60 - location
        loc_bar__minus_5_bar__minus_9_bar_1_bar_60 - location
        loc_bar__minus_5_bar__minus_8_bar_2_bar_60 - location
        loc_bar__minus_6_bar__minus_8_bar_3_bar_0 - location
        loc_bar__minus_6_bar__minus_7_bar_3_bar_60 - location
        loc_bar__minus_5_bar__minus_9_bar_2_bar_60 - location
        loc_bar__minus_5_bar__minus_2_bar_0_bar_30 - location
        loc_bar__minus_3_bar__minus_5_bar_1_bar_45 - location
        loc_bar__minus_7_bar__minus_5_bar_3_bar_60 - location
        loc_bar__minus_5_bar__minus_8_bar_1_bar_60 - location
        loc_bar__minus_5_bar__minus_8_bar_1_bar__minus_15 - location
        loc_bar__minus_3_bar__minus_5_bar_1_bar_60 - location
        loc_bar__minus_6_bar__minus_8_bar_3_bar_60 - location
        loc_bar__minus_7_bar__minus_2_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType ToiletPaperHanger_bar__plus_00_dot_00_bar__plus_00_dot_99_bar__minus_01_dot_40 ToiletPaperHangerType)
        (receptacleType Toilet_bar__minus_00_dot_47_bar__plus_00_dot_00_bar__minus_01_dot_88 ToiletType)
        (receptacleType Bathtub_bar__minus_00_dot_98_bar__minus_00_dot_72_bar__minus_02_dot_93_bar_BathtubBasin BathtubBasinType)
        (receptacleType Drawer_bar__minus_02_dot_12_bar__plus_00_dot_46_bar__minus_01_dot_31 DrawerType)
        (receptacleType Drawer_bar__minus_02_dot_12_bar__plus_00_dot_17_bar__minus_01_dot_31 DrawerType)
        (receptacleType SideTable_bar__minus_02_dot_12_bar__plus_00_dot_46_bar__minus_01_dot_31 SideTableType)
        (receptacleType TowelHolder_bar__plus_00_dot_06_bar__plus_01_dot_83_bar__minus_01_dot_89 TowelHolderType)
        (receptacleType HandTowelHolder_bar__minus_02_dot_41_bar__plus_01_dot_63_bar__minus_01_dot_51 HandTowelHolderType)
        (receptacleType GarbageCan_bar__minus_00_dot_22_bar__plus_00_dot_00_bar__minus_01_dot_42 GarbageCanType)
        (receptacleType Sink_bar__minus_02_dot_10_bar_00_dot_00_bar__minus_02_dot_03_bar_SinkBasin SinkBasinType)
        (objectType Plunger_bar__minus_00_dot_12_bar__plus_00_dot_00_bar__minus_02_dot_38 PlungerType)
        (objectType Cloth_bar__minus_01_dot_69_bar__plus_00_dot_06_bar__minus_02_dot_86 ClothType)
        (objectType Candle_bar__minus_00_dot_12_bar__plus_01_dot_05_bar__minus_02_dot_01 CandleType)
        (objectType LightSwitch_bar__minus_01_dot_25_bar__plus_01_dot_42_bar__plus_00_dot_00 LightSwitchType)
        (objectType HandTowel_bar__minus_02_dot_37_bar__plus_01_dot_52_bar__minus_01_dot_51 HandTowelType)
        (objectType ToiletPaper_bar__minus_00_dot_14_bar__plus_01_dot_04_bar__minus_01_dot_88 ToiletPaperType)
        (objectType ScrubBrush_bar__minus_00_dot_16_bar__plus_00_dot_00_bar__minus_02_dot_16 ScrubBrushType)
        (objectType SoapBar_bar__minus_02_dot_20_bar__plus_00_dot_68_bar__minus_01_dot_47 SoapBarType)
        (objectType Bathtub_bar__minus_00_dot_98_bar__minus_00_dot_72_bar__minus_02_dot_93 BathtubType)
        (objectType SoapBottle_bar__minus_00_dot_14_bar__plus_01_dot_05_bar__minus_01_dot_81 SoapBottleType)
        (objectType SoapBottle_bar__minus_02_dot_11_bar__plus_00_dot_38_bar__minus_01_dot_31 SoapBottleType)
        (objectType Mirror_bar__minus_02_dot_45_bar__plus_01_dot_64_bar__minus_02_dot_03 MirrorType)
        (objectType Cloth_bar__minus_00_dot_94_bar__plus_00_dot_06_bar__minus_02_dot_86 ClothType)
        (objectType Sink_bar__minus_02_dot_10_bar_00_dot_00_bar__minus_02_dot_03 SinkType)
        (objectType ToiletPaper_bar__minus_00_dot_07_bar__plus_00_dot_99_bar__minus_01_dot_34 ToiletPaperType)
        (objectType SprayBottle_bar__minus_02_dot_34_bar__plus_00_dot_68_bar__minus_01_dot_39 SprayBottleType)
        (objectType ToiletPaper_bar__minus_01_dot_20_bar__plus_00_dot_48_bar__minus_02_dot_53 ToiletPaperType)
        (objectType Cloth_bar__minus_01_dot_94_bar__plus_00_dot_06_bar__minus_02_dot_99 ClothType)
        (objectType TissueBox_bar__minus_02_dot_19_bar__plus_00_dot_67_bar__minus_01_dot_12 TissueBoxType)
        (objectType Candle_bar__minus_02_dot_25_bar__plus_00_dot_68_bar__minus_01_dot_24 CandleType)
        (objectType SprayBottle_bar__minus_02_dot_07_bar__plus_00_dot_68_bar__minus_01_dot_24 SprayBottleType)
        (objectType Towel_bar__minus_00_dot_05_bar__plus_01_dot_85_bar__minus_01_dot_89 TowelType)
        (canContain ToiletPaperHangerType ToiletPaperType)
        (canContain ToiletType SoapBottleType)
        (canContain ToiletType HandTowelType)
        (canContain ToiletType ToiletPaperType)
        (canContain ToiletType ClothType)
        (canContain ToiletType CandleType)
        (canContain ToiletType SoapBarType)
        (canContain ToiletType SprayBottleType)
        (canContain ToiletType TissueBoxType)
        (canContain BathtubBasinType ClothType)
        (canContain BathtubBasinType HandTowelType)
        (canContain BathtubBasinType SoapBarType)
        (canContain DrawerType CandleType)
        (canContain DrawerType SoapBarType)
        (canContain DrawerType SprayBottleType)
        (canContain DrawerType ToiletPaperType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ClothType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType HandTowelType)
        (canContain DrawerType CandleType)
        (canContain DrawerType SoapBarType)
        (canContain DrawerType SprayBottleType)
        (canContain DrawerType ToiletPaperType)
        (canContain DrawerType SoapBottleType)
        (canContain DrawerType ClothType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType HandTowelType)
        (canContain SideTableType CandleType)
        (canContain SideTableType SoapBarType)
        (canContain SideTableType SprayBottleType)
        (canContain SideTableType ToiletPaperType)
        (canContain SideTableType SoapBottleType)
        (canContain SideTableType ClothType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType HandTowelType)
        (canContain TowelHolderType TowelType)
        (canContain HandTowelHolderType HandTowelType)
        (canContain GarbageCanType SoapBarType)
        (canContain GarbageCanType SprayBottleType)
        (canContain GarbageCanType ToiletPaperType)
        (canContain GarbageCanType SoapBottleType)
        (canContain GarbageCanType ClothType)
        (canContain GarbageCanType TissueBoxType)
        (canContain GarbageCanType HandTowelType)
        (canContain SinkBasinType SoapBarType)
        (canContain SinkBasinType ClothType)
        (canContain SinkBasinType HandTowelType)
        (pickupable Plunger_bar__minus_00_dot_12_bar__plus_00_dot_00_bar__minus_02_dot_38)
        (pickupable Cloth_bar__minus_01_dot_69_bar__plus_00_dot_06_bar__minus_02_dot_86)
        (pickupable Candle_bar__minus_00_dot_12_bar__plus_01_dot_05_bar__minus_02_dot_01)
        (pickupable HandTowel_bar__minus_02_dot_37_bar__plus_01_dot_52_bar__minus_01_dot_51)
        (pickupable ToiletPaper_bar__minus_00_dot_14_bar__plus_01_dot_04_bar__minus_01_dot_88)
        (pickupable ScrubBrush_bar__minus_00_dot_16_bar__plus_00_dot_00_bar__minus_02_dot_16)
        (pickupable SoapBar_bar__minus_02_dot_20_bar__plus_00_dot_68_bar__minus_01_dot_47)
        (pickupable SoapBottle_bar__minus_00_dot_14_bar__plus_01_dot_05_bar__minus_01_dot_81)
        (pickupable SoapBottle_bar__minus_02_dot_11_bar__plus_00_dot_38_bar__minus_01_dot_31)
        (pickupable Cloth_bar__minus_00_dot_94_bar__plus_00_dot_06_bar__minus_02_dot_86)
        (pickupable ToiletPaper_bar__minus_00_dot_07_bar__plus_00_dot_99_bar__minus_01_dot_34)
        (pickupable SprayBottle_bar__minus_02_dot_34_bar__plus_00_dot_68_bar__minus_01_dot_39)
        (pickupable ToiletPaper_bar__minus_01_dot_20_bar__plus_00_dot_48_bar__minus_02_dot_53)
        (pickupable Cloth_bar__minus_01_dot_94_bar__plus_00_dot_06_bar__minus_02_dot_99)
        (pickupable TissueBox_bar__minus_02_dot_19_bar__plus_00_dot_67_bar__minus_01_dot_12)
        (pickupable Candle_bar__minus_02_dot_25_bar__plus_00_dot_68_bar__minus_01_dot_24)
        (pickupable SprayBottle_bar__minus_02_dot_07_bar__plus_00_dot_68_bar__minus_01_dot_24)
        (pickupable Towel_bar__minus_00_dot_05_bar__plus_01_dot_85_bar__minus_01_dot_89)
        
        
        
        (atLocation agent1 loc_bar__minus_7_bar__minus_2_bar_2_bar_30)
        
        (cleanable Cloth_bar__minus_01_dot_69_bar__plus_00_dot_06_bar__minus_02_dot_86)
        (cleanable SoapBar_bar__minus_02_dot_20_bar__plus_00_dot_68_bar__minus_01_dot_47)
        (cleanable Cloth_bar__minus_00_dot_94_bar__plus_00_dot_06_bar__minus_02_dot_86)
        (cleanable Cloth_bar__minus_01_dot_94_bar__plus_00_dot_06_bar__minus_02_dot_99)
        
        
        
        
        
        
        
        
        
        
        (inReceptacle SoapBottle_bar__minus_02_dot_11_bar__plus_00_dot_38_bar__minus_01_dot_31 Drawer_bar__minus_02_dot_12_bar__plus_00_dot_46_bar__minus_01_dot_31)
        (inReceptacle SoapBar_bar__minus_02_dot_20_bar__plus_00_dot_68_bar__minus_01_dot_47 SideTable_bar__minus_02_dot_12_bar__plus_00_dot_46_bar__minus_01_dot_31)
        (inReceptacle TissueBox_bar__minus_02_dot_19_bar__plus_00_dot_67_bar__minus_01_dot_12 SideTable_bar__minus_02_dot_12_bar__plus_00_dot_46_bar__minus_01_dot_31)
        (inReceptacle SprayBottle_bar__minus_02_dot_34_bar__plus_00_dot_68_bar__minus_01_dot_39 SideTable_bar__minus_02_dot_12_bar__plus_00_dot_46_bar__minus_01_dot_31)
        (inReceptacle Candle_bar__minus_02_dot_25_bar__plus_00_dot_68_bar__minus_01_dot_24 SideTable_bar__minus_02_dot_12_bar__plus_00_dot_46_bar__minus_01_dot_31)
        (inReceptacle SprayBottle_bar__minus_02_dot_07_bar__plus_00_dot_68_bar__minus_01_dot_24 SideTable_bar__minus_02_dot_12_bar__plus_00_dot_46_bar__minus_01_dot_31)
        (inReceptacle Towel_bar__minus_00_dot_05_bar__plus_01_dot_85_bar__minus_01_dot_89 TowelHolder_bar__plus_00_dot_06_bar__plus_01_dot_83_bar__minus_01_dot_89)
        (inReceptacle HandTowel_bar__minus_02_dot_37_bar__plus_01_dot_52_bar__minus_01_dot_51 HandTowelHolder_bar__minus_02_dot_41_bar__plus_01_dot_63_bar__minus_01_dot_51)
        (inReceptacle Cloth_bar__minus_00_dot_94_bar__plus_00_dot_06_bar__minus_02_dot_86 Bathtub_bar__minus_00_dot_98_bar__minus_00_dot_72_bar__minus_02_dot_93_bar_BathtubBasin)
        (inReceptacle Cloth_bar__minus_01_dot_69_bar__plus_00_dot_06_bar__minus_02_dot_86 Bathtub_bar__minus_00_dot_98_bar__minus_00_dot_72_bar__minus_02_dot_93_bar_BathtubBasin)
        (inReceptacle Cloth_bar__minus_01_dot_94_bar__plus_00_dot_06_bar__minus_02_dot_99 Bathtub_bar__minus_00_dot_98_bar__minus_00_dot_72_bar__minus_02_dot_93_bar_BathtubBasin)
        (inReceptacle ToiletPaper_bar__minus_00_dot_14_bar__plus_01_dot_04_bar__minus_01_dot_88 Toilet_bar__minus_00_dot_47_bar__plus_00_dot_00_bar__minus_01_dot_88)
        (inReceptacle SoapBottle_bar__minus_00_dot_14_bar__plus_01_dot_05_bar__minus_01_dot_81 Toilet_bar__minus_00_dot_47_bar__plus_00_dot_00_bar__minus_01_dot_88)
        (inReceptacle Candle_bar__minus_00_dot_12_bar__plus_01_dot_05_bar__minus_02_dot_01 Toilet_bar__minus_00_dot_47_bar__plus_00_dot_00_bar__minus_01_dot_88)
        
        
        (receptacleAtLocation Bathtub_bar__minus_00_dot_98_bar__minus_00_dot_72_bar__minus_02_dot_93_bar_BathtubBasin loc_bar__minus_5_bar__minus_8_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_02_dot_12_bar__plus_00_dot_17_bar__minus_01_dot_31 loc_bar__minus_7_bar__minus_5_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__minus_02_dot_12_bar__plus_00_dot_46_bar__minus_01_dot_31 loc_bar__minus_7_bar__minus_5_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_00_dot_22_bar__plus_00_dot_00_bar__minus_01_dot_42 loc_bar__minus_3_bar__minus_5_bar_1_bar_60)
        (receptacleAtLocation HandTowelHolder_bar__minus_02_dot_41_bar__plus_01_dot_63_bar__minus_01_dot_51 loc_bar__minus_6_bar__minus_6_bar_3_bar_0)
        (receptacleAtLocation SideTable_bar__minus_02_dot_12_bar__plus_00_dot_46_bar__minus_01_dot_31 loc_bar__minus_7_bar__minus_2_bar_2_bar_60)
        (receptacleAtLocation Sink_bar__minus_02_dot_10_bar_00_dot_00_bar__minus_02_dot_03_bar_SinkBasin loc_bar__minus_6_bar__minus_7_bar_3_bar_60)
        (receptacleAtLocation ToiletPaperHanger_bar__plus_00_dot_00_bar__plus_00_dot_99_bar__minus_01_dot_40 loc_bar__minus_3_bar__minus_5_bar_1_bar_45)
        (receptacleAtLocation Toilet_bar__minus_00_dot_47_bar__plus_00_dot_00_bar__minus_01_dot_88 loc_bar__minus_5_bar__minus_8_bar_1_bar_60)
        (receptacleAtLocation TowelHolder_bar__plus_00_dot_06_bar__plus_01_dot_83_bar__minus_01_dot_89 loc_bar__minus_5_bar__minus_8_bar_1_bar__minus_15)
        (objectAtLocation Candle_bar__minus_00_dot_12_bar__plus_01_dot_05_bar__minus_02_dot_01 loc_bar__minus_5_bar__minus_8_bar_1_bar_60)
        (objectAtLocation SprayBottle_bar__minus_02_dot_07_bar__plus_00_dot_68_bar__minus_01_dot_24 loc_bar__minus_7_bar__minus_2_bar_2_bar_60)
        (objectAtLocation SoapBottle_bar__minus_00_dot_14_bar__plus_01_dot_05_bar__minus_01_dot_81 loc_bar__minus_5_bar__minus_8_bar_1_bar_60)
        (objectAtLocation Cloth_bar__minus_00_dot_94_bar__plus_00_dot_06_bar__minus_02_dot_86 loc_bar__minus_5_bar__minus_8_bar_2_bar_60)
        (objectAtLocation ToiletPaper_bar__minus_00_dot_14_bar__plus_01_dot_04_bar__minus_01_dot_88 loc_bar__minus_5_bar__minus_8_bar_1_bar_60)
        (objectAtLocation Cloth_bar__minus_01_dot_69_bar__plus_00_dot_06_bar__minus_02_dot_86 loc_bar__minus_5_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Bathtub_bar__minus_00_dot_98_bar__minus_00_dot_72_bar__minus_02_dot_93 loc_bar__minus_5_bar__minus_9_bar_2_bar_60)
        (objectAtLocation Sink_bar__minus_02_dot_10_bar_00_dot_00_bar__minus_02_dot_03 loc_bar__minus_6_bar__minus_8_bar_3_bar_60)
        (objectAtLocation Mirror_bar__minus_02_dot_45_bar__plus_01_dot_64_bar__minus_02_dot_03 loc_bar__minus_6_bar__minus_8_bar_3_bar_0)
        (objectAtLocation HandTowel_bar__minus_02_dot_37_bar__plus_01_dot_52_bar__minus_01_dot_51 loc_bar__minus_6_bar__minus_6_bar_3_bar_0)
        (objectAtLocation SoapBar_bar__minus_02_dot_20_bar__plus_00_dot_68_bar__minus_01_dot_47 loc_bar__minus_7_bar__minus_2_bar_2_bar_60)
        (objectAtLocation ToiletPaper_bar__minus_01_dot_20_bar__plus_00_dot_48_bar__minus_02_dot_53 loc_bar__minus_5_bar__minus_8_bar_2_bar_60)
        (objectAtLocation TissueBox_bar__minus_02_dot_19_bar__plus_00_dot_67_bar__minus_01_dot_12 loc_bar__minus_7_bar__minus_2_bar_2_bar_60)
        (objectAtLocation ToiletPaper_bar__minus_00_dot_07_bar__plus_00_dot_99_bar__minus_01_dot_34 loc_bar__minus_3_bar__minus_5_bar_1_bar_60)
        (objectAtLocation LightSwitch_bar__minus_01_dot_25_bar__plus_01_dot_42_bar__plus_00_dot_00 loc_bar__minus_5_bar__minus_2_bar_0_bar_30)
        (objectAtLocation Cloth_bar__minus_01_dot_94_bar__plus_00_dot_06_bar__minus_02_dot_99 loc_bar__minus_5_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Towel_bar__minus_00_dot_05_bar__plus_01_dot_85_bar__minus_01_dot_89 loc_bar__minus_5_bar__minus_8_bar_1_bar__minus_15)
        (objectAtLocation SoapBottle_bar__minus_02_dot_11_bar__plus_00_dot_38_bar__minus_01_dot_31 loc_bar__minus_7_bar__minus_5_bar_3_bar_60)
        (objectAtLocation SprayBottle_bar__minus_02_dot_34_bar__plus_00_dot_68_bar__minus_01_dot_39 loc_bar__minus_7_bar__minus_2_bar_2_bar_60)
        (objectAtLocation ScrubBrush_bar__minus_00_dot_16_bar__plus_00_dot_00_bar__minus_02_dot_16 loc_bar__minus_5_bar__minus_9_bar_1_bar_60)
        (objectAtLocation Plunger_bar__minus_00_dot_12_bar__plus_00_dot_00_bar__minus_02_dot_38 loc_bar__minus_5_bar__minus_9_bar_1_bar_60)
        (objectAtLocation Candle_bar__minus_02_dot_25_bar__plus_00_dot_68_bar__minus_01_dot_24 loc_bar__minus_7_bar__minus_2_bar_2_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 SprayBottleType)
                                    (receptacleType ?r GarbageCanType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 SprayBottleType)
                                            (receptacleType ?r GarbageCanType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            