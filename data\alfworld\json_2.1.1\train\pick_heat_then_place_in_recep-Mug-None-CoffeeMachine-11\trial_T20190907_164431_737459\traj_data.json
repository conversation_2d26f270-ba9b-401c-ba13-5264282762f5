{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000204.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000206.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000207.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 36}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "CoffeeMachine", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["coffeemachine"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|2|-1|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [2.4759972, 2.4759972, 1.8792001, 1.8792001, 3.78, 3.78]], "coordinateReceptacleObjectId": ["CoffeeMachine", [2.476, 2.476, 2.4, 2.4, 3.6, 3.6]], "forceVisible": true, "objectId": "Mug|+00.62|+00.95|+00.47"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-4|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}}, {"discrete_action": {"action": "GotoLocation", "args": ["coffeemachine"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|2|-1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "coffeemachine"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [2.4759972, 2.4759972, 1.8792001, 1.8792001, 3.78, 3.78]], "coordinateReceptacleObjectId": ["CoffeeMachine", [2.476, 2.476, 2.4, 2.4, 3.6, 3.6]], "forceVisible": true, "objectId": "Mug|+00.62|+00.95|+00.47", "receptacleObjectId": "CoffeeMachine|+00.62|+00.90|+00.60"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+00.62|+00.95|+00.47"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [172, 112, 204, 150], "mask": [[33482, 6], [33778, 14], [34076, 18], [34375, 21], [34674, 23], [34973, 25], [35273, 26], [35573, 26], [35873, 29], [36173, 31], [36473, 32], [36773, 27], [36802, 3], [37073, 26], [37102, 3], [37373, 26], [37402, 3], [37673, 26], [37701, 3], [37973, 25], [38000, 4], [38273, 25], [38300, 3], [38573, 25], [38599, 4], [38873, 25], [38899, 3], [39173, 24], [39198, 3], [39473, 28], [39773, 27], [40073, 26], [40373, 26], [40672, 26], [40972, 24], [41272, 24], [41572, 24], [41872, 24], [42172, 23], [42472, 23], [42772, 23], [43073, 22], [43373, 21], [43674, 20], [43975, 18], [44276, 16], [44578, 13], [44881, 7]], "point": [188, 130]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+00.62|+00.95|+00.47", "placeStationary": true, "receptacleObjectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 103], [25631, 29], [25687, 105], [25822, 103], [25933, 27], [25987, 105], [26122, 102], [26234, 25], [26288, 103], [26421, 102], [26535, 24], [26588, 103], [26720, 102], [26836, 23], [26888, 102], [27020, 102], [27137, 22], [27188, 102], [27319, 102], [27437, 21], [27489, 101], [27619, 102], [27738, 20], [27789, 100], [27918, 102], [28038, 20], [28089, 100], [28217, 103], [28338, 21], [28388, 100], [28517, 103], [28638, 21], [28688, 100], [28816, 104], [28938, 21], [28988, 99], [29115, 105], [29238, 21], [29288, 99], [29415, 105], [29538, 21], [29587, 101], [29714, 106], [29838, 21], [29887, 101], [30013, 108], [30138, 21], [30187, 101], [30313, 108], [30438, 21], [30486, 102], [30612, 110], [30738, 22], [30786, 102], [30911, 111], [31037, 23], [31085, 103], [31211, 112], [31337, 24], [31385, 103], [31510, 114], [31636, 25], [31684, 104], [31809, 116], [31935, 26], [31984, 104], [32109, 117], [32234, 5], [32246, 16], [32283, 105], [32408, 130], [32547, 15], [32582, 106], [32708, 128], [32848, 15], [32882, 106], [33007, 129], [33149, 16], [33181, 106], [33306, 129], [33450, 17], [33480, 107], [33606, 128], [33751, 18], [33777, 110], [33905, 129], [34051, 135], [34204, 129], [34352, 134], [34504, 129], [34652, 134], [34803, 130], [34952, 133], [35102, 131], [35252, 133], [35402, 131], [35552, 132], [35701, 132], [35852, 132], [36000, 133], [36152, 131], [36300, 133], [36452, 131], [36600, 133], [36752, 130], [36900, 134], [37051, 131], [37200, 134], [37351, 130], [37500, 135], [37650, 131], [37800, 136], [37949, 131], [38100, 137], [38248, 132], [38400, 139], [38546, 134], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 103], [25631, 29], [25687, 105], [25822, 103], [25933, 27], [25987, 105], [26122, 102], [26234, 25], [26288, 103], [26421, 102], [26535, 24], [26588, 103], [26720, 102], [26836, 23], [26888, 102], [27020, 102], [27137, 22], [27188, 102], [27319, 102], [27437, 21], [27489, 101], [27619, 102], [27738, 20], [27789, 100], [27918, 102], [28038, 20], [28089, 100], [28217, 89], [28314, 6], [28338, 21], [28388, 100], [28517, 85], [28618, 2], [28638, 21], [28688, 100], [28816, 84], [28938, 21], [28988, 99], [29115, 83], [29238, 21], [29288, 99], [29415, 82], [29538, 21], [29587, 101], [29714, 82], [29838, 21], [29887, 101], [30013, 82], [30138, 21], [30187, 101], [30313, 81], [30438, 21], [30486, 102], [30612, 82], [30738, 22], [30786, 102], [30911, 83], [31037, 23], [31085, 103], [31211, 83], [31337, 24], [31385, 103], [31510, 84], [31623, 1], [31636, 25], [31684, 104], [31809, 86], [31923, 2], [31935, 26], [31984, 104], [32109, 86], [32223, 3], [32234, 5], [32246, 16], [32283, 105], [32408, 87], [32523, 4], [32530, 8], [32547, 15], [32582, 106], [32708, 88], [32823, 4], [32829, 7], [32848, 15], [32882, 106], [33007, 89], [33123, 3], [33129, 7], [33149, 16], [33181, 106], [33306, 90], [33423, 3], [33429, 6], [33450, 17], [33480, 107], [33606, 90], [33723, 3], [33728, 6], [33751, 18], [33777, 110], [33905, 92], [34023, 2], [34028, 6], [34051, 135], [34204, 93], [34323, 2], [34328, 5], [34352, 134], [34504, 93], [34623, 2], [34627, 6], [34652, 134], [34803, 94], [34923, 1], [34927, 6], [34952, 133], [35102, 95], [35223, 1], [35227, 6], [35252, 133], [35402, 96], [35526, 7], [35552, 132], [35701, 97], [35826, 7], [35852, 132], [36000, 98], [36125, 8], [36152, 131], [36300, 98], [36424, 9], [36452, 131], [36600, 99], [36724, 9], [36752, 130], [36900, 99], [37024, 10], [37051, 131], [37200, 99], [37324, 10], [37351, 130], [37500, 99], [37624, 11], [37650, 131], [37800, 99], [37924, 12], [37949, 131], [38100, 100], [38224, 13], [38248, 132], [38400, 100], [38523, 16], [38546, 134], [38700, 100], [38823, 156], [39000, 101], [39122, 157], [39300, 101], [39421, 157], [39600, 102], [39720, 158], [39900, 104], [40019, 158], [40200, 106], [40316, 161], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+00.62|+00.95|+00.47"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [94, 95, 129, 135], "mask": [[28306, 8], [28602, 16], [28900, 20], [29198, 23], [29497, 25], [29796, 26], [30095, 28], [30394, 29], [30694, 29], [30994, 34], [31294, 35], [31594, 29], [31626, 3], [31895, 28], [31927, 3], [32195, 28], [32227, 3], [32495, 28], [32527, 3], [32796, 27], [32827, 2], [33096, 27], [33126, 3], [33396, 27], [33426, 3], [33696, 27], [33726, 2], [33997, 26], [34025, 3], [34297, 26], [34325, 3], [34597, 26], [34625, 2], [34897, 26], [34924, 3], [35197, 26], [35224, 3], [35498, 28], [35798, 28], [36098, 27], [36398, 26], [36699, 25], [36999, 25], [37299, 25], [37599, 25], [37899, 25], [38200, 24], [38500, 23], [38800, 23], [39101, 21], [39401, 20], [39702, 18], [40004, 15], [40306, 10]], "point": [111, 114]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 103], [25631, 29], [25687, 105], [25822, 103], [25933, 27], [25987, 105], [26122, 102], [26234, 25], [26288, 103], [26421, 102], [26535, 24], [26588, 103], [26720, 102], [26836, 23], [26888, 102], [27020, 102], [27137, 22], [27188, 102], [27319, 102], [27437, 21], [27489, 101], [27619, 102], [27738, 20], [27789, 100], [27918, 102], [28038, 20], [28089, 100], [28217, 103], [28338, 21], [28388, 100], [28517, 103], [28638, 21], [28688, 100], [28816, 104], [28938, 21], [28988, 99], [29115, 105], [29238, 21], [29288, 99], [29415, 105], [29538, 21], [29587, 101], [29714, 106], [29838, 21], [29887, 101], [30013, 108], [30138, 21], [30187, 101], [30313, 108], [30438, 21], [30486, 102], [30612, 110], [30738, 22], [30786, 102], [30911, 111], [31037, 23], [31085, 103], [31211, 112], [31337, 24], [31385, 103], [31510, 114], [31636, 25], [31684, 104], [31809, 116], [31935, 26], [31984, 104], [32109, 117], [32234, 5], [32246, 16], [32283, 105], [32408, 130], [32547, 15], [32582, 106], [32708, 128], [32848, 15], [32882, 106], [33007, 129], [33149, 16], [33181, 106], [33306, 129], [33450, 17], [33480, 107], [33606, 128], [33751, 18], [33777, 110], [33905, 129], [34051, 135], [34204, 129], [34352, 134], [34504, 129], [34652, 134], [34803, 130], [34952, 133], [35102, 131], [35252, 133], [35402, 131], [35552, 132], [35701, 132], [35852, 132], [36000, 133], [36152, 131], [36300, 133], [36452, 131], [36600, 133], [36752, 130], [36900, 134], [37051, 131], [37200, 134], [37351, 130], [37500, 135], [37650, 131], [37800, 136], [37949, 131], [38100, 137], [38248, 132], [38400, 139], [38546, 134], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+00.62|+00.95|+00.47", "placeStationary": true, "receptacleObjectId": "CoffeeMachine|+00.62|+00.90|+00.60"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [124, 1, 255, 162], "mask": [[160, 51], [457, 58], [756, 60], [1055, 63], [1355, 64], [1655, 64], [1948, 74], [2244, 82], [2542, 87], [2839, 93], [3137, 97], [3436, 100], [3734, 104], [4033, 106], [4332, 108], [4632, 109], [4931, 111], [5230, 113], [5529, 115], [5829, 115], [6128, 116], [6428, 117], [6728, 117], [7028, 118], [7327, 119], [7627, 120], [7927, 120], [8227, 121], [8527, 121], [8826, 122], [9126, 123], [9426, 123], [9726, 124], [10026, 124], [10326, 124], [10625, 126], [10925, 126], [11225, 127], [11525, 127], [11825, 127], [12125, 127], [12425, 128], [12725, 128], [13025, 128], [13325, 128], [13625, 129], [13925, 129], [14225, 129], [14524, 130], [14824, 131], [15124, 131], [15425, 130], [15725, 130], [16025, 130], [16325, 130], [16625, 131], [16925, 131], [17225, 131], [17525, 131], [17825, 131], [18125, 131], [18425, 131], [18725, 131], [19025, 131], [19325, 130], [19625, 130], [19925, 130], [20225, 129], [20525, 129], [20825, 129], [21125, 128], [21426, 127], [21726, 127], [22026, 127], [22326, 126], [22627, 124], [22927, 123], [23227, 123], [23527, 123], [23827, 123], [24127, 122], [24427, 122], [24727, 122], [25027, 121], [25328, 120], [25628, 120], [25928, 119], [26228, 119], [26528, 119], [26828, 118], [27128, 118], [27428, 118], [27728, 118], [28028, 117], [28328, 117], [28628, 117], [28928, 116], [29228, 116], [29529, 115], [29829, 114], [30129, 114], [30429, 114], [30729, 114], [31029, 113], [31329, 113], [31629, 113], [31929, 112], [32229, 112], [32529, 112], [32829, 111], [33129, 111], [33429, 111], [33730, 110], [34030, 109], [34330, 109], [34630, 109], [34930, 108], [35230, 108], [35530, 108], [35830, 107], [36130, 107], [36430, 107], [36730, 107], [37030, 106], [37330, 106], [37630, 106], [37930, 105], [38230, 105], [38530, 105], [38830, 105], [39130, 105], [39430, 105], [39730, 105], [40030, 104], [40330, 104], [40630, 104], [40930, 103], [41230, 103], [41531, 9], [41541, 92], [41831, 7], [41841, 91], [42131, 5], [42141, 83], [42227, 5], [42432, 1], [42441, 83], [42529, 2], [42742, 82], [43042, 81], [43342, 81], [43643, 80], [43943, 80], [44244, 79], [44544, 78], [44844, 78], [45145, 77], [45445, 76], [45746, 75], [46047, 73], [46348, 72], [46649, 70], [46949, 70], [47250, 68], [47551, 66], [47855, 59], [48160, 49], [48469, 32]], "point": [189, 80]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan11", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 0.0, "y": 0.9009992, "z": -0.25}, "object_poses": [{"objectName": "Kettle_7922e01e", "position": {"x": -2.47704625, "y": 0.9532663, "z": 0.614945531}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Kettle_7922e01e", "position": {"x": 1.68176985, "y": 1.32977593, "z": -1.80964673}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": -2.61406946, "y": 0.9527736, "z": 0.345098317}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": -0.0126443952, "y": 0.7751493, "z": -1.47316957}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": -1.8725, "y": 1.49105871, "z": -1.67773771}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_b9b64351", "position": {"x": -1.92895365, "y": 0.977574646, "z": 0.165200144}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": -0.09697643, "y": 0.910996437, "z": 0.3752191}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": -0.394602776, "y": 0.06890199, "z": 0.612647057}, "rotation": {"x": 0.0, "y": 89.99995, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": -2.135, "y": 1.13467455, "z": -1.76346016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": -1.79193056, "y": 1.04246557, "z": 0.2551492}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": 1.79240572, "y": 0.103541851, "z": 0.311406672}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_41d4caea", "position": {"x": 1.66300225, "y": 0.9794513, "z": 0.4581}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_41d4caea", "position": {"x": 1.78509033, "y": 0.9794513, "z": 0.292338252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": 1.29989243, "y": 0.978749633, "z": -1.53264427}, "rotation": {"x": 2.16695e-05, "y": 1.909873e-05, "z": 9.447489e-06}}, {"objectName": "Knife_b9b64351", "position": {"x": -1.92895365, "y": 0.977574646, "z": 0.435047358}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": -2.203, "y": 0.9527736, "z": 0.4350474}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": -2.0475, "y": 0.780233741, "z": -1.80632138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7922e01e", "position": {"x": -2.06597686, "y": 0.9532663, "z": 0.614945531}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": 0.504299939, "y": 0.108622193, "z": 0.308500022}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": -0.3619999, "y": 0.1192514, "z": 0.4393529}, "rotation": {"x": 0.0, "y": 89.99995, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": 0.8049865, "y": 0.918246448, "z": -1.582726}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": -0.4189759, "y": 1.32959366, "z": -1.809647}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_da74902f", "position": {"x": 1.0071, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": 0.167712927, "y": 0.927299857, "z": 0.292338252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_54f2d061", "position": {"x": -2.203, "y": 1.05651045, "z": 0.5249964}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": -0.975880742, "y": 1.050615, "z": -1.67189026}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": -0.175111651, "y": 1.00254059, "z": -1.82056189}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_42ce5824", "position": {"x": -2.47704625, "y": 0.9485312, "z": 0.435047448}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": -1.79193056, "y": 0.9485312, "z": 0.6149455}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_41d4caea", "position": {"x": -1.07974291, "y": 1.082603, "z": -1.77092588}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_d4975865", "position": {"x": 0.701403737, "y": 0.9195921, "z": -1.64917481}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_483d2614", "position": {"x": -0.923949659, "y": 1.04528785, "z": -1.80393767}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_4b842b85", "position": {"x": 1.022105, "y": 0.9591456, "z": 0.288545281}, "rotation": {"x": 0.0, "y": 219.19072, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": 0.3419488, "y": 0.782450736, "z": -1.64893007}, "rotation": {"x": 1.40334191e-14, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": -2.06597686, "y": 0.9533544, "z": 0.3450983}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": 0.6189993, "y": 0.945, "z": 0.469800025}, "rotation": {"x": 0.0, "y": 180.00032, "z": 0.0}}], "object_toggles": [], "random_seed": 3826798577, "scene_num": 11}, "task_id": "trial_T20190907_164431_737459", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1MKCTVKE7J0ZP_31Z0PCVWUNW4HNPN76F6IH9HJP5T7R", "high_descs": ["Turn right, go forward, then turn left to face the coffee maker.", "Pick up the mug from the coffee maker.", "Turn left, go forward, then turn left to go to the microwave.", "Heat the mug in the microwave.", "Turn left, move forward, then turn left to return to the coffee maker.", "Put the mug at the coffee maker."], "task_desc": "Put a warm mug at the coffee maker.", "votes": [1, 1]}, {"assignment_id": "AUTYWXILTACCR_3LEP4MGT3JHK2AWGYXR7IFDRZT0BD4", "high_descs": ["Turn left to face the counter with the coffee machine on it.", "Pick up the mug from the coffee machine.", "Turn around and walk to the microwave on your right.", "Warm the mug in the microwave, then take it out again.", "Turn around and walk back to the coffee machine.", "Place the mug in the coffee machine."], "task_desc": "Place a warmed mug in a coffee machine.", "votes": [1, 1]}, {"assignment_id": "A3PPLDHC3CG0YN_3R08VXYT7FCW94TH2BKH51GGNI77WD", "high_descs": ["Turn right and move in front of the coffee maker. ", "Pick up the coffee mug from the coffee pot. ", "Turn left and walk to the red microwave. ", "Open the microwave door, put the mug inside of the microwave next to the egg, close the door, heat the mug, take the mug out of the microwave, and close the door. ", "Turn left and walk back to the coffee pot. ", "Put the coffee mug back in the coffee pot. "], "task_desc": "To heat a mug for coffee. ", "votes": [1, 1]}]}}