{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000098.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000099.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000100.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000101.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000102.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000103.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000104.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000105.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000108.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000109.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000110.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000111.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000112.png", "low_idx": 24}, {"high_idx": 1, "image_name": "000000113.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000114.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000115.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000116.png", "low_idx": 25}, {"high_idx": 1, "image_name": "000000117.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000118.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000121.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000122.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000123.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000124.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000125.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000126.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000127.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000130.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000131.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000132.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000133.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000134.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000135.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000205.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000206.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000207.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000208.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000209.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000210.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000211.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000212.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000213.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000214.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000265.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000266.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000267.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000268.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000269.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000270.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000271.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000272.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000273.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000274.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000275.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000276.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000277.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000278.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000279.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000280.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000281.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000282.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000283.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000284.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 57}, {"high_idx": 5, "image_name": "000000340.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000341.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000342.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000343.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000344.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000345.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000346.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000347.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000348.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000349.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000350.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000351.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000352.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000353.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000354.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000355.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000356.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000357.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000358.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000359.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000360.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000361.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000362.png", "low_idx": 60}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-7|0|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-9.53614996, -9.53614996, 0.3066938, 0.3066938, 0.449162484, 0.449162484]], "coordinateReceptacleObjectId": ["Cabinet", [-8.582, -8.582, -0.944, -0.944, 1.610000132, 1.610000132]], "forceVisible": true, "objectId": "Cup|-02.38|+00.11|+00.08"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|3|-30"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-9.53614996, -9.53614996, 0.3066938, 0.3066938, 0.449162484, 0.449162484]], "coordinateReceptacleObjectId": ["Cabinet", [-9.784, -9.784, 11.7, 11.7, 7.79599952, 7.79599952]], "forceVisible": true, "objectId": "Cup|-02.38|+00.11|+00.08", "receptacleObjectId": "Cabinet|-02.45|+01.95|+02.93"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-02.15|+00.40|-00.24"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [86, 177, 197, 221], "mask": [[52886, 112], [53187, 111], [53487, 110], [53788, 109], [54088, 109], [54389, 107], [54689, 107], [54990, 106], [55290, 105], [55591, 104], [55892, 102], [56192, 102], [56493, 101], [56793, 100], [57094, 99], [57394, 98], [57695, 97], [57995, 97], [58296, 95], [58596, 95], [58897, 93], [59197, 93], [59498, 92], [59798, 91], [60099, 90], [60399, 89], [60700, 88], [61000, 88], [61301, 86], [61601, 86], [61902, 85], [62202, 84], [62503, 83], [62803, 82], [63104, 81], [63404, 81], [63705, 79], [64005, 79], [64306, 77], [64606, 77], [64907, 76], [65207, 75], [65508, 74], [65809, 72], [66109, 72]], "point": [141, 198]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-02.38|+00.11|+00.08"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [155, 177, 170, 184], "mask": [[52955, 16], [53255, 16], [53556, 14], [53857, 13], [54158, 11], [54458, 10], [54759, 8], [55060, 6]], "point": [162, 179]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-02.15|+00.40|-00.24"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [67, 177, 197, 300], "mask": [[52886, 112], [53185, 113], [53484, 113], [53784, 113], [54083, 114], [54383, 113], [54683, 113], [54983, 112], [55283, 112], [55583, 112], [55883, 111], [56182, 112], [56482, 111], [56782, 111], [57082, 111], [57382, 110], [57682, 110], [57982, 109], [58281, 110], [58581, 110], [58881, 109], [59181, 109], [59481, 109], [59781, 108], [60081, 108], [60380, 108], [60680, 108], [60980, 108], [61280, 107], [61580, 107], [61880, 106], [62180, 66], [62254, 32], [62479, 62], [62559, 27], [62779, 57], [62864, 21], [63079, 54], [63167, 18], [63379, 53], [63468, 16], [63679, 51], [63770, 14], [63979, 50], [64071, 13], [64279, 48], [64373, 10], [64579, 47], [64674, 9], [64878, 46], [64976, 7], [65178, 45], [65277, 5], [65478, 43], [65579, 3], [65778, 32], [66078, 32], [66378, 32], [66678, 32], [66977, 33], [67277, 33], [67577, 33], [67877, 33], [68177, 33], [68477, 33], [68777, 33], [69076, 33], [69376, 33], [69676, 33], [69976, 33], [70276, 33], [70576, 33], [70876, 33], [71175, 34], [71475, 34], [71775, 34], [72075, 34], [72375, 34], [72675, 34], [72975, 33], [73275, 33], [73574, 34], [73874, 34], [74174, 33], [74474, 33], [74774, 33], [75074, 33], [75374, 33], [75673, 34], [75973, 34], [76273, 35], [76573, 35], [76873, 34], [77173, 34], [77473, 34], [77772, 35], [78072, 35], [78372, 35], [78672, 35], [78972, 35], [79272, 35], [79572, 35], [79871, 36], [80171, 36], [80471, 36], [80771, 35], [81071, 35], [81371, 35], [81671, 35], [81970, 36], [82270, 36], [82570, 36], [82870, 36], [83170, 36], [83470, 36], [83770, 36], [84070, 36], [84369, 37], [84669, 36], [84969, 36], [85269, 36], [85569, 36], [85869, 36], [86169, 36], [86468, 37], [86768, 37], [87068, 37], [87368, 37], [87668, 37], [87968, 37], [88268, 37], [88567, 37], [88867, 37], [89167, 37], [89467, 37], [89767, 37]], "point": [122, 217]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-02.38|+00.11|+00.08", "placeStationary": true, "receptacleObjectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 130], [19960, 77], [20100, 130], [20260, 77], [20400, 130], [20561, 76], [20700, 130], [20861, 76], [21000, 129], [21161, 75], [21300, 129], [21462, 74], [21600, 129], [21762, 74], [21900, 129], [22062, 74], [22200, 129], [22362, 73], [22500, 129], [22662, 73], [22800, 129], [22961, 74], [23100, 129], [23261, 74], [23400, 129], [23561, 73], [23700, 130], [23861, 73], [24000, 130], [24160, 74], [24300, 130], [24460, 74], [24600, 131], [24760, 73], [24900, 131], [25059, 74], [25200, 132], [25359, 74], [25500, 132], [25658, 75], [25800, 133], [25958, 74], [26100, 133], [26257, 75], [26400, 134], [26557, 75], [26700, 134], [26856, 76], [27000, 135], [27156, 75], [27300, 136], [27455, 76], [27600, 137], [27754, 77], [27900, 139], [28053, 78], [28200, 141], [28351, 79], [28500, 143], [28648, 82], [28800, 230], [29100, 230], [29400, 229], [29700, 70], [29774, 155], [30000, 68], [30076, 153], [30300, 67], [30378, 151], [30600, 67], [30678, 150], [30900, 66], [30979, 149], [31200, 66], [31280, 148], [31500, 66], [31580, 148], [31800, 65], [31881, 146], [32100, 65], [32181, 146], [32400, 65], [32481, 146], [32700, 66], [32781, 146], [33000, 66], [33081, 145], [33300, 66], [33381, 145], [33600, 66], [33681, 145], [33900, 67], [33980, 145], [34200, 68], [34280, 145], [34500, 69], [34579, 146], [34800, 70], [34878, 147], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 130], [19960, 77], [20100, 130], [20260, 77], [20400, 88], [20493, 37], [20561, 76], [20700, 83], [20798, 32], [20861, 76], [21000, 80], [21100, 29], [21161, 75], [21300, 78], [21401, 28], [21462, 74], [21600, 77], [21702, 27], [21762, 74], [21900, 76], [22002, 27], [22062, 74], [22200, 75], [22302, 27], [22362, 73], [22500, 75], [22603, 26], [22662, 73], [22800, 75], [22903, 26], [22961, 74], [23100, 75], [23203, 26], [23261, 74], [23400, 75], [23502, 27], [23561, 73], [23700, 75], [23802, 28], [23861, 73], [24000, 76], [24102, 28], [24160, 74], [24300, 76], [24402, 28], [24460, 74], [24600, 77], [24702, 29], [24760, 73], [24900, 78], [25002, 29], [25059, 74], [25200, 78], [25302, 30], [25359, 74], [25500, 79], [25602, 30], [25658, 75], [25800, 79], [25902, 31], [25958, 74], [26100, 80], [26201, 32], [26257, 75], [26400, 80], [26501, 33], [26557, 75], [26700, 81], [26801, 33], [26856, 76], [27000, 81], [27101, 34], [27156, 75], [27300, 82], [27401, 35], [27455, 76], [27600, 82], [27701, 36], [27754, 77], [27900, 83], [28001, 38], [28053, 78], [28200, 83], [28301, 40], [28351, 79], [28500, 84], [28601, 42], [28648, 82], [28800, 84], [28900, 130], [29100, 84], [29200, 130], [29400, 85], [29499, 130], [29700, 70], [29774, 12], [29797, 132], [30000, 68], [30076, 12], [30095, 134], [30300, 67], [30378, 151], [30600, 67], [30678, 150], [30900, 66], [30979, 149], [31200, 66], [31280, 148], [31500, 66], [31580, 148], [31800, 65], [31881, 146], [32100, 65], [32181, 146], [32400, 65], [32481, 146], [32700, 66], [32781, 146], [33000, 66], [33081, 145], [33300, 66], [33381, 145], [33600, 66], [33681, 145], [33900, 67], [33980, 145], [34200, 68], [34280, 145], [34500, 69], [34579, 146], [34800, 70], [34878, 147], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-02.38|+00.11|+00.08"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [75, 69, 102, 101], "mask": [[20488, 5], [20783, 15], [21080, 20], [21378, 23], [21677, 25], [21976, 26], [22275, 27], [22575, 28], [22875, 28], [23175, 28], [23475, 27], [23775, 27], [24076, 26], [24376, 26], [24677, 25], [24978, 24], [25278, 24], [25579, 23], [25879, 23], [26180, 21], [26480, 21], [26781, 20], [27081, 20], [27382, 19], [27682, 19], [27983, 18], [28283, 18], [28584, 17], [28884, 16], [29184, 16], [29485, 14], [29786, 11], [30088, 7]], "point": [88, 84]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 130], [19960, 77], [20100, 130], [20260, 77], [20400, 130], [20561, 76], [20700, 130], [20861, 76], [21000, 129], [21161, 75], [21300, 129], [21462, 74], [21600, 129], [21762, 74], [21900, 129], [22062, 74], [22200, 129], [22362, 73], [22500, 129], [22662, 73], [22800, 129], [22961, 74], [23100, 129], [23261, 74], [23400, 129], [23561, 73], [23700, 130], [23861, 73], [24000, 130], [24160, 74], [24300, 130], [24460, 74], [24600, 131], [24760, 73], [24900, 131], [25059, 74], [25200, 132], [25359, 74], [25500, 132], [25658, 75], [25800, 133], [25958, 74], [26100, 133], [26257, 75], [26400, 134], [26557, 75], [26700, 134], [26856, 76], [27000, 135], [27156, 75], [27300, 136], [27455, 76], [27600, 137], [27754, 77], [27900, 139], [28053, 78], [28200, 141], [28351, 79], [28500, 143], [28648, 82], [28800, 230], [29100, 230], [29400, 229], [29700, 70], [29774, 155], [30000, 68], [30076, 153], [30300, 67], [30378, 151], [30600, 67], [30678, 150], [30900, 66], [30979, 149], [31200, 66], [31280, 148], [31500, 66], [31580, 148], [31800, 65], [31881, 146], [32100, 65], [32181, 146], [32400, 65], [32481, 146], [32700, 66], [32781, 146], [33000, 66], [33081, 145], [33300, 66], [33381, 145], [33600, 66], [33681, 145], [33900, 67], [33980, 145], [34200, 68], [34280, 145], [34500, 69], [34579, 146], [34800, 70], [34878, 147], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-02.45|+01.95|+02.93"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [79, 62, 299, 300], "mask": [[18408, 152], [18707, 153], [19007, 154], [19307, 154], [19607, 154], [19907, 154], [20206, 156], [20506, 156], [20806, 156], [21106, 157], [21406, 157], [21706, 157], [22006, 158], [22306, 158], [22606, 158], [22905, 159], [23205, 160], [23505, 160], [23805, 160], [24105, 161], [24405, 161], [24705, 161], [25005, 162], [25304, 163], [25604, 163], [25904, 163], [26204, 164], [26504, 164], [26804, 164], [27104, 165], [27404, 165], [27704, 165], [28003, 167], [28303, 167], [28603, 167], [28903, 168], [29203, 168], [29503, 168], [29803, 168], [30103, 169], [30402, 170], [30702, 170], [31002, 171], [31302, 171], [31602, 171], [31902, 172], [32202, 172], [32502, 172], [32802, 172], [33101, 174], [33401, 174], [33701, 174], [34001, 175], [34301, 175], [34601, 175], [34901, 176], [35201, 176], [35500, 177], [35800, 178], [36100, 178], [36400, 178], [36700, 178], [37000, 179], [37300, 179], [37600, 179], [37900, 180], [38199, 181], [38499, 181], [38799, 182], [39099, 182], [39399, 182], [39699, 182], [39999, 183], [40299, 183], [40598, 184], [40898, 185], [41198, 185], [41498, 185], [41798, 186], [42098, 186], [42398, 186], [42698, 187], [42997, 188], [43297, 188], [43597, 188], [43897, 189], [44197, 189], [44497, 189], [44797, 190], [45097, 190], [45397, 190], [45696, 192], [45996, 192], [46296, 192], [46596, 192], [46896, 193], [47196, 193], [47496, 193], [47796, 194], [48095, 195], [48395, 195], [48695, 196], [48995, 196], [49295, 196], [49595, 197], [49895, 197], [50195, 197], [50495, 197], [50794, 199], [51094, 199], [51394, 199], [51694, 200], [51994, 200], [52294, 200], [52594, 201], [52894, 201], [53193, 202], [53493, 202], [53793, 203], [54093, 203], [54393, 203], [54693, 204], [54993, 204], [55293, 204], [55593, 205], [55892, 206], [56192, 206], [56492, 207], [56792, 207], [57092, 207], [57392, 207], [57692, 208], [57992, 208], [58291, 209], [58591, 54], [58655, 145], [58891, 48], [58961, 139], [59191, 44], [59265, 135], [59491, 42], [59567, 133], [59791, 40], [59869, 131], [60091, 38], [60171, 129], [60391, 37], [60472, 128], [60691, 35], [60774, 126], [60990, 34], [61076, 124], [61290, 33], [61377, 123], [61590, 32], [61678, 122], [61890, 31], [61979, 121], [62190, 30], [62280, 120], [62490, 29], [62581, 119], [62790, 28], [62882, 118], [63090, 27], [63183, 117], [63389, 28], [63483, 117], [63689, 27], [63784, 116], [63989, 26], [64085, 115], [64289, 25], [64386, 114], [64589, 24], [64687, 113], [64889, 24], [64987, 113], [65189, 24], [65287, 113], [65489, 23], [65588, 112], [65788, 24], [65888, 112], [66088, 24], [66188, 112], [66388, 23], [66489, 111], [66688, 23], [66789, 111], [66988, 23], [67089, 111], [67288, 22], [67390, 110], [67588, 22], [67690, 110], [67888, 22], [67990, 110], [68188, 21], [68291, 109], [68487, 22], [68591, 109], [68787, 22], [68891, 109], [69087, 22], [69191, 109], [69387, 22], [69491, 109], [69687, 22], [69791, 109], [69987, 22], [70091, 109], [70287, 22], [70391, 109], [70587, 22], [70691, 109], [70886, 24], [70990, 110], [71186, 24], [71290, 110], [71486, 24], [71590, 110], [71786, 24], [71890, 110], [72086, 24], [72190, 110], [72386, 24], [72490, 110], [72686, 24], [72790, 110], [72986, 24], [73090, 110], [73286, 24], [73390, 110], [73585, 26], [73689, 111], [73885, 26], [73989, 111], [74185, 26], [74289, 111], [74485, 27], [74588, 112], [74785, 28], [74887, 113], [75085, 28], [75187, 113], [75385, 29], [75486, 114], [75685, 29], [75786, 114], [75984, 31], [76085, 115], [76284, 31], [76385, 115], [76584, 32], [76684, 116], [76884, 33], [76983, 117], [77184, 33], [77283, 117], [77484, 34], [77582, 118], [77784, 34], [77882, 118], [78084, 35], [78181, 119], [78384, 36], [78480, 120], [78683, 38], [78779, 121], [78983, 39], [79078, 122], [79283, 41], [79376, 124], [79583, 42], [79675, 125], [79883, 43], [79974, 126], [80183, 44], [80273, 127], [80483, 45], [80572, 128], [80783, 46], [80871, 129], [81082, 48], [81170, 130], [81382, 49], [81469, 131], [81682, 50], [81768, 132], [81982, 51], [82067, 133], [82282, 52], [82366, 134], [82582, 54], [82664, 136], [82882, 55], [82963, 137], [83182, 57], [83261, 139], [83482, 58], [83560, 140], [83781, 61], [83858, 142], [84081, 65], [84154, 146], [84381, 219], [84681, 219], [84981, 219], [85281, 219], [85581, 219], [85881, 219], [86180, 220], [86480, 220], [86780, 220], [87080, 220], [87380, 220], [87680, 220], [87980, 220], [88280, 220], [88580, 220], [88879, 221], [89179, 221], [89479, 221], [89779, 221]], "point": [189, 180]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-02.38|+00.11|+00.08", "placeStationary": true, "receptacleObjectId": "Cabinet|-02.45|+01.95|+02.93"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [79, 1, 299, 300], "mask": [[284, 16], [584, 16], [884, 16], [1183, 17], [1483, 17], [1782, 18], [2082, 18], [2381, 19], [2681, 19], [2980, 20], [3280, 20], [3579, 21], [3879, 21], [4179, 21], [4478, 22], [4778, 22], [5077, 23], [5377, 23], [5676, 24], [5976, 24], [6275, 25], [6575, 25], [6875, 25], [7174, 26], [7474, 26], [7773, 27], [8073, 27], [8372, 28], [8672, 28], [8971, 29], [9271, 29], [9570, 30], [9870, 30], [10170, 30], [10469, 31], [10769, 31], [11068, 32], [11368, 32], [11667, 33], [11967, 33], [12266, 34], [12566, 34], [12866, 34], [13165, 35], [13465, 35], [13764, 36], [14064, 36], [14363, 37], [14663, 37], [14962, 38], [15262, 38], [15562, 38], [15861, 39], [16161, 39], [16460, 40], [16760, 40], [17059, 41], [17359, 41], [17658, 42], [17958, 42], [18257, 43], [18557, 43], [18858, 42], [19007, 193], [19307, 193], [19607, 193], [19907, 193], [20206, 194], [20506, 194], [20806, 194], [21106, 194], [21406, 194], [21706, 194], [22006, 194], [22306, 194], [22606, 194], [22905, 195], [23205, 195], [23505, 195], [23805, 195], [24105, 195], [24405, 195], [24705, 195], [25005, 195], [25304, 196], [25604, 196], [25904, 196], [26204, 196], [26504, 196], [26804, 196], [27104, 196], [27404, 196], [27704, 196], [28003, 197], [28303, 197], [28603, 197], [28903, 197], [29203, 197], [29503, 197], [29803, 197], [30103, 197], [30402, 198], [30702, 198], [31002, 198], [31302, 198], [31602, 198], [31902, 198], [32202, 198], [32502, 198], [32802, 198], [33101, 199], [33401, 199], [33701, 199], [34001, 199], [34301, 199], [34601, 199], [34901, 199], [35201, 199], [35500, 200], [35800, 200], [36100, 200], [36400, 200], [36700, 200], [37000, 200], [37300, 200], [37600, 200], [37900, 200], [38199, 201], [38499, 201], [38799, 201], [39099, 201], [39399, 201], [39699, 201], [39999, 201], [40299, 201], [40598, 202], [40898, 202], [41198, 202], [41498, 202], [41798, 202], [42098, 202], [42398, 202], [42698, 202], [42997, 203], [43297, 203], [43597, 203], [43897, 203], [44197, 203], [44497, 203], [44797, 203], [45097, 203], [45397, 203], [45696, 204], [45996, 204], [46296, 204], [46596, 204], [46896, 204], [47196, 204], [47496, 204], [47796, 204], [48095, 205], [48395, 205], [48695, 205], [48995, 205], [49295, 205], [49595, 205], [49895, 205], [50195, 205], [50495, 205], [50794, 206], [51094, 206], [51394, 206], [51694, 206], [51994, 206], [52294, 206], [52594, 206], [52894, 206], [53193, 207], [53493, 207], [53793, 207], [54093, 207], [54393, 207], [54693, 207], [54993, 207], [55293, 207], [55593, 207], [55892, 208], [56192, 208], [56492, 208], [56792, 208], [57092, 208], [57392, 208], [57692, 208], [57992, 208], [58291, 209], [58591, 54], [58655, 145], [58891, 48], [58961, 139], [59191, 44], [59265, 135], [59491, 42], [59567, 133], [59791, 40], [59869, 131], [60091, 38], [60171, 129], [60391, 37], [60472, 128], [60691, 35], [60774, 126], [60990, 34], [61076, 124], [61290, 33], [61377, 123], [61590, 32], [61678, 122], [61890, 31], [61979, 121], [62190, 30], [62280, 120], [62490, 29], [62581, 119], [62790, 28], [62882, 118], [63090, 27], [63183, 117], [63389, 28], [63483, 117], [63689, 27], [63784, 116], [63989, 26], [64085, 115], [64289, 25], [64386, 114], [64589, 24], [64687, 113], [64889, 24], [64987, 113], [65189, 24], [65287, 113], [65489, 23], [65588, 112], [65788, 24], [65888, 112], [66088, 24], [66188, 112], [66388, 23], [66489, 111], [66688, 23], [66789, 111], [66988, 23], [67089, 111], [67288, 22], [67390, 110], [67588, 22], [67690, 110], [67888, 22], [67990, 110], [68188, 21], [68291, 109], [68487, 22], [68591, 109], [68787, 22], [68891, 109], [69087, 22], [69191, 109], [69387, 22], [69491, 109], [69687, 22], [69791, 109], [69987, 22], [70091, 109], [70287, 22], [70391, 109], [70587, 22], [70691, 109], [70886, 24], [70990, 110], [71186, 24], [71290, 110], [71486, 24], [71590, 110], [71786, 24], [71890, 110], [72086, 24], [72190, 110], [72386, 24], [72490, 110], [72686, 24], [72790, 110], [72986, 24], [73090, 110], [73286, 24], [73390, 110], [73585, 26], [73689, 111], [73885, 26], [73989, 111], [74185, 26], [74289, 111], [74485, 27], [74588, 112], [74785, 28], [74887, 113], [75085, 28], [75187, 113], [75385, 29], [75486, 114], [75685, 29], [75786, 114], [75984, 31], [76085, 115], [76284, 31], [76385, 115], [76584, 32], [76684, 116], [76884, 33], [76983, 117], [77184, 33], [77283, 117], [77484, 34], [77582, 118], [77784, 34], [77882, 118], [78084, 35], [78181, 119], [78384, 36], [78480, 120], [78683, 38], [78779, 121], [78983, 39], [79078, 122], [79283, 41], [79376, 124], [79583, 42], [79675, 125], [79883, 43], [79974, 126], [80183, 44], [80273, 127], [80483, 45], [80572, 128], [80783, 46], [80871, 129], [81082, 48], [81170, 130], [81382, 49], [81469, 131], [81682, 50], [81768, 132], [81982, 51], [82067, 133], [82282, 52], [82366, 134], [82582, 54], [82664, 136], [82882, 55], [82963, 111], [83093, 7], [83182, 57], [83261, 100], [83396, 4], [83482, 58], [83560, 99], [83696, 4], [83781, 61], [83858, 102], [83996, 4], [84081, 65], [84154, 106], [84296, 4], [84381, 179], [84596, 4], [84681, 179], [84897, 3], [84981, 180], [85197, 3], [85281, 180], [85497, 3], [85581, 180], [85797, 3], [85881, 180], [86097, 3], [86180, 182], [86398, 2], [86480, 182], [86698, 2], [86780, 182], [86998, 2], [87080, 182], [87298, 2], [87380, 182], [87599, 1], [87680, 183], [87899, 1], [87980, 183], [88199, 1], [88280, 183], [88499, 1], [88580, 183], [88799, 1], [88879, 185], [89179, 185], [89479, 185], [89779, 185]], "point": [189, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-02.45|+01.95|+02.93"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [79, 1, 299, 300], "mask": [[284, 16], [584, 16], [884, 16], [1183, 17], [1483, 17], [1782, 18], [2082, 18], [2381, 19], [2681, 19], [2980, 20], [3280, 20], [3579, 21], [3879, 21], [4179, 21], [4478, 22], [4778, 22], [5077, 23], [5377, 23], [5676, 24], [5976, 24], [6275, 25], [6575, 25], [6875, 25], [7174, 26], [7474, 26], [7773, 27], [8073, 27], [8372, 28], [8672, 28], [8971, 29], [9271, 29], [9570, 30], [9870, 30], [10170, 30], [10469, 31], [10769, 31], [11068, 32], [11368, 32], [11667, 33], [11967, 33], [12266, 34], [12566, 34], [12866, 34], [13165, 35], [13465, 35], [13764, 36], [14064, 36], [14363, 37], [14663, 37], [14962, 38], [15262, 38], [15562, 38], [15861, 39], [16161, 39], [16460, 40], [16760, 40], [17059, 41], [17359, 41], [17658, 42], [17958, 42], [18257, 43], [18557, 43], [18858, 42], [19007, 193], [19307, 193], [19607, 193], [19907, 193], [20206, 194], [20506, 194], [20806, 194], [21106, 194], [21406, 194], [21706, 194], [22006, 194], [22306, 194], [22606, 194], [22905, 195], [23205, 195], [23505, 195], [23805, 195], [24105, 195], [24405, 195], [24705, 195], [25005, 195], [25304, 196], [25604, 196], [25904, 196], [26204, 196], [26504, 196], [26804, 196], [27104, 196], [27404, 196], [27704, 196], [28003, 197], [28303, 197], [28603, 197], [28903, 197], [29203, 197], [29503, 197], [29803, 197], [30103, 197], [30402, 198], [30702, 198], [31002, 198], [31302, 198], [31602, 198], [31902, 198], [32202, 198], [32502, 198], [32802, 198], [33101, 199], [33401, 199], [33701, 199], [34001, 199], [34301, 199], [34601, 199], [34901, 199], [35201, 199], [35500, 200], [35800, 200], [36100, 200], [36400, 200], [36700, 200], [37000, 200], [37300, 200], [37600, 200], [37900, 200], [38199, 201], [38499, 201], [38799, 201], [39099, 201], [39399, 201], [39699, 201], [39999, 201], [40299, 201], [40598, 202], [40898, 202], [41198, 202], [41498, 202], [41798, 202], [42098, 202], [42398, 202], [42698, 202], [42997, 203], [43297, 203], [43597, 203], [43897, 203], [44197, 203], [44497, 203], [44797, 203], [45097, 203], [45397, 203], [45696, 204], [45996, 204], [46296, 204], [46596, 204], [46896, 204], [47196, 204], [47496, 204], [47796, 204], [48095, 205], [48395, 205], [48695, 205], [48995, 205], [49295, 205], [49595, 205], [49895, 205], [50195, 205], [50495, 205], [50794, 206], [51094, 206], [51394, 206], [51694, 206], [51994, 206], [52294, 206], [52594, 206], [52894, 206], [53193, 207], [53493, 207], [53793, 207], [54093, 207], [54393, 207], [54693, 207], [54993, 207], [55293, 207], [55593, 207], [55892, 208], [56192, 208], [56492, 208], [56792, 208], [57092, 208], [57392, 208], [57692, 208], [57992, 208], [58291, 209], [58591, 209], [58891, 209], [59191, 209], [59491, 209], [59791, 209], [60091, 209], [60391, 209], [60691, 209], [60990, 210], [61290, 210], [61590, 210], [61890, 210], [62190, 210], [62490, 210], [62790, 210], [63090, 210], [63389, 211], [63689, 211], [63989, 211], [64289, 211], [64589, 211], [64889, 211], [65189, 211], [65489, 211], [65788, 212], [66088, 212], [66388, 212], [66688, 212], [66988, 212], [67288, 212], [67588, 212], [67888, 212], [68188, 212], [68487, 213], [68787, 213], [69087, 213], [69387, 213], [69687, 213], [69987, 213], [70287, 213], [70587, 213], [70886, 214], [71186, 214], [71486, 214], [71786, 214], [72086, 214], [72386, 214], [72686, 214], [72986, 214], [73286, 214], [73585, 215], [73885, 215], [74185, 215], [74485, 215], [74785, 215], [75085, 215], [75385, 215], [75685, 215], [75984, 216], [76284, 216], [76584, 216], [76884, 216], [77184, 216], [77484, 216], [77784, 216], [78084, 216], [78384, 216], [78683, 217], [78983, 217], [79283, 217], [79583, 217], [79883, 217], [80183, 217], [80483, 217], [80783, 217], [81082, 218], [81382, 218], [81682, 218], [81982, 218], [82282, 218], [82582, 218], [82882, 48], [82953, 121], [83093, 7], [83182, 42], [83259, 102], [83396, 4], [83482, 41], [83560, 99], [83696, 4], [83781, 42], [83860, 100], [83996, 4], [84081, 42], [84160, 100], [84296, 4], [84381, 42], [84460, 100], [84596, 4], [84681, 42], [84760, 100], [84897, 3], [84981, 42], [85060, 101], [85197, 3], [85281, 43], [85359, 102], [85497, 3], [85581, 43], [85659, 102], [85797, 3], [85881, 43], [85959, 102], [86097, 3], [86180, 44], [86259, 103], [86398, 2], [86480, 44], [86559, 103], [86698, 2], [86780, 44], [86859, 103], [86998, 2], [87080, 44], [87158, 104], [87298, 2], [87380, 45], [87458, 104], [87599, 1], [87680, 45], [87758, 105], [87899, 1], [87980, 45], [88058, 105], [88199, 1], [88280, 45], [88358, 105], [88499, 1], [88580, 45], [88657, 106], [88799, 1], [88879, 46], [88957, 107], [89179, 47], [89257, 107], [89479, 47], [89557, 107], [89779, 47], [89857, 107]], "point": [189, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan6", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 1.25, "y": 0.9009992, "z": 1.0}, "object_poses": [{"objectName": "DishSponge_1f5d88fd", "position": {"x": -2.538684, "y": 0.9104642, "z": 0.9311123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": 1.34268308, "y": 0.910464168, "z": -0.7264168}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": -0.341296852, "y": 0.9109421, "z": 0.423983335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": -2.289484, "y": 0.9109421, "z": 1.830605}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": -2.62175035, "y": 0.911448538, "z": 1.2309432}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_bc458cba", "position": {"x": 1.6916, "y": 0.9295, "z": -0.3359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": -2.506034, "y": 1.02850568, "z": 2.22795653}, "rotation": {"x": 0.0, "y": 89.99982, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": -2.38403749, "y": 0.112290621, "z": 0.07667345}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": -2.61399865, "y": 0.1763367, "z": -0.79157424}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_7b0d90af", "position": {"x": 1.37260914, "y": 0.923413157, "z": -1.15027642}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": -0.341296852, "y": 0.9362496, "z": 1.76401663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9b863229", "position": {"x": 0.9178922, "y": 0.953229249, "z": -1.17394781}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": -2.538684, "y": 0.912865162, "z": 0.6312814}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": -2.67743039, "y": 1.53996527, "z": 1.9849999}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.57219648, "y": 1.9309833, "z": -1.03440011}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.485795, "y": 1.2976073, "z": -0.6021706}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": 1.68401384, "y": 0.906100035, "z": 0.406000018}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": 0.390452385, "y": 0.9059, "z": -1.36997449}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": 0.0483515263, "y": 0.994053841, "z": 1.76401663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_db7f1139", "position": {"x": 0.4640001, "y": 0.9558, "z": -1.5935}, "rotation": {"x": 0.0, "y": 3.24433968e-05, "z": 0.0}}, {"objectName": "Mug_db7f1139", "position": {"x": -2.524844, "y": 1.53261185, "z": 2.84076881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_adb25946", "position": {"x": 0.0, "y": 0.9, "z": 0.948}, "rotation": {"x": 0.0, "y": 29.9999466, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": -2.22708869, "y": 0.7987526, "z": 0.391211122}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": -2.63572216, "y": 1.077064, "z": 2.48186231}, "rotation": {"x": 0.0, "y": 89.99982, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.560535, "y": 1.53519452, "z": 0.774226665}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": -0.6010624, "y": 0.911448538, "z": 1.094}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_209e2ea9", "position": {"x": -0.7309452, "y": 0.996719658, "z": 1.76401663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_7b0d90af", "position": {"x": -0.341296852, "y": 0.913369656, "z": 1.31733882}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": 1.771809, "y": 0.21585986, "z": 0.706531167}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_0884e440", "position": {"x": 1.6916, "y": 0.9295, "z": 0.0642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_b563bb4b", "position": {"x": 1.11905587, "y": 0.94723314, "z": -0.972783566}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": 1.59833741, "y": 0.906100035, "z": 0.3467031}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -2.45561719, "y": 0.994053841, "z": 1.2309432}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": -2.53202271, "y": 1.29822564, "z": -0.7909757}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": -2.605, "y": 1.539577, "z": 1.26514387}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": -2.25749445, "y": 0.7890956, "z": 0.8093223}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_40552007", "position": {"x": 1.646, "y": 0.289516717, "z": 0.6514689}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": -0.211414054, "y": 0.9109421, "z": 0.6473222}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": -2.289484, "y": 0.905899942, "z": 0.9311123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": 0.301035166, "y": 1.663314, "z": -1.69339836}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_bc458cba", "position": {"x": -2.289484, "y": 0.9146199, "z": 0.6312814}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": 1.42698455, "y": 0.9122294, "z": 0.494945318}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9b863229", "position": {"x": 1.85581267, "y": 0.953229249, "z": -0.642795563}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_db7f1139", "position": {"x": 1.85536671, "y": 0.906100035, "z": 0.3467031}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3746941988, "scene_num": 6}, "task_id": "trial_T20190908_165422_447082", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A20FCMWP43CVIU_3L6L49WXW3ODYNIC0KOIDB3V6FT54F", "high_descs": ["walk to face counter to right of fridge", "pick up black glass from cabinet below counter top", "turn to face microwave", "heat glass in microwave, remove glass from microwave", "gaze upward to face cabinet above microwave", "put glass in cabinet"], "task_desc": "put heated glass away in cabinet", "votes": [1, 1, 1]}, {"assignment_id": "A31681CCEVDIH3_3NKQQ8O391W7JYRKUWXJORPHI9HUD1", "high_descs": ["Turn around, walk left and then hang a right to reach the fridge, stopping at the cabinet on the right of it.", "Take a black cup out of the lower cabinet next to the fridge.", "Turn right and walk to the microwave.", "Put the cup in the microwave on the plate and heat it up, taking it back out.", "Turn to face the cabinet above the microwave.", "Put the black cup in the cabinet."], "task_desc": "Heat up a cup from the cabinet next to the fridge to put it away over the microwave.", "votes": [1, 1, 1]}, {"assignment_id": "AM2KK02JXXW48_3DHE4R9OCZ21B6S9D2OLOTXMCBFG2I", "high_descs": ["Turn around, go around the island to the right, to the counter, right of the fridge. ", "Take the cup out of the lower cabinet, right of the fridge. ", "Bring the cup to the microwave, which is to the right, on the counter.", "Heat the cup in the mike. ", "Bring the heated cup up to the cabinet above the mike. ", "Put the heated cup in the cabinet above the mike."], "task_desc": "Put a heated cup in a cabinet. ", "votes": [1, 0, 1]}]}}