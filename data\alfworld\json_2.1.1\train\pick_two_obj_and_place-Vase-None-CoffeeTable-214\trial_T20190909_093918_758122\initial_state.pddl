
(define (problem plan_trial_T20190909_093918_758122)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__minus_02_dot_40_bar__plus_00_dot_57_bar__plus_04_dot_50 - object
        Box_bar__minus_04_dot_28_bar__plus_00_dot_66_bar__plus_03_dot_23 - object
        CreditCard_bar__minus_00_dot_48_bar__plus_00_dot_28_bar__plus_05_dot_26 - object
        CreditCard_bar__minus_02_dot_53_bar__plus_00_dot_45_bar__plus_04_dot_28 - object
        CreditCard_bar__minus_03_dot_02_bar__plus_00_dot_45_bar__plus_04_dot_06 - object
        Curtains_bar__minus_00_dot_05_bar__plus_02_dot_48_bar__plus_02_dot_63 - object
        Curtains_bar__minus_02_dot_19_bar__plus_02_dot_47_bar__plus_00_dot_04 - object
        Curtains_bar__minus_02_dot_83_bar__plus_02_dot_47_bar__plus_00_dot_04 - object
        FloorLamp_bar__minus_04_dot_51_bar__plus_00_dot_02_bar__plus_00_dot_66 - object
        HousePlant_bar__minus_02_dot_66_bar__plus_00_dot_44_bar__plus_04_dot_17 - object
        KeyChain_bar__minus_00_dot_44_bar__plus_00_dot_62_bar__plus_01_dot_00 - object
        KeyChain_bar__minus_02_dot_19_bar__plus_00_dot_54_bar__plus_00_dot_98 - object
        Lamp_bar__minus_00_dot_45_bar__plus_00_dot_61_bar__plus_00_dot_69 - object
        Laptop_bar__minus_04_dot_34_bar__plus_00_dot_52_bar__plus_02_dot_22 - object
        LightSwitch_bar__minus_05_dot_00_bar__plus_01_dot_25_bar__plus_05_dot_86 - object
        Newspaper_bar__minus_00_dot_56_bar__plus_01_dot_47_bar__plus_05_dot_74 - object
        Newspaper_bar__minus_02_dot_49_bar__plus_00_dot_55_bar__plus_00_dot_98 - object
        Painting_bar__minus_02_dot_67_bar__plus_01_dot_57_bar__plus_07_dot_00 - object
        Pillow_bar__minus_01_dot_58_bar__plus_00_dot_61_bar__plus_00_dot_67 - object
        Plate_bar__minus_00_dot_24_bar__plus_00_dot_29_bar__plus_05_dot_50 - object
        Plate_bar__minus_02_dot_89_bar__plus_00_dot_45_bar__plus_04_dot_28 - object
        Plate_bar__minus_02_dot_91_bar__plus_00_dot_44_bar__plus_03_dot_40 - object
        RemoteControl_bar__minus_00_dot_36_bar__plus_00_dot_62_bar__plus_00_dot_92 - object
        RemoteControl_bar__minus_00_dot_67_bar__plus_00_dot_62_bar__plus_01_dot_00 - object
        RemoteControl_bar__minus_04_dot_05_bar__plus_00_dot_45_bar__plus_06_dot_36 - object
        Statue_bar__minus_00_dot_53_bar__plus_01_dot_45_bar__plus_05_dot_35 - object
        Statue_bar__minus_00_dot_60_bar__plus_01_dot_45_bar__plus_06_dot_79 - object
        Statue_bar__minus_02_dot_65_bar__plus_00_dot_46_bar__plus_03_dot_84 - object
        Television_bar__minus_00_dot_75_bar__plus_01_dot_66_bar__plus_03_dot_94 - object
        Vase_bar__minus_00_dot_40_bar__plus_00_dot_29_bar__plus_06_dot_74 - object
        Vase_bar__minus_00_dot_48_bar__plus_00_dot_28_bar__plus_06_dot_49 - object
        WateringCan_bar__minus_00_dot_24_bar__plus_00_dot_28_bar__plus_05_dot_26 - object
        WateringCan_bar__minus_03_dot_02_bar__plus_00_dot_45_bar__plus_03_dot_61 - object
        Window_bar__minus_00_dot_01_bar__plus_01_dot_57_bar__plus_01_dot_50 - object
        Window_bar__minus_01_dot_37_bar__plus_01_dot_57_bar__plus_00_dot_01 - object
        Window_bar__minus_03_dot_73_bar__plus_01_dot_57_bar__plus_00_dot_01 - object
        ArmChair_bar__minus_04_dot_45_bar__plus_00_dot_00_bar__plus_06_dot_45 - receptacle
        CoffeeTable_bar__minus_02_dot_65_bar__plus_00_dot_02_bar__plus_04_dot_06 - receptacle
        GarbageCan_bar__minus_00_dot_52_bar__plus_00_dot_02_bar__plus_02_dot_82 - receptacle
        Shelf_bar__minus_00_dot_39_bar__plus_01_dot_45_bar__plus_05_dot_95 - receptacle
        Shelf_bar__minus_00_dot_40_bar__plus_00_dot_27_bar__plus_05_dot_96 - receptacle
        SideTable_bar__minus_00_dot_59_bar__plus_00_dot_02_bar__plus_00_dot_84 - receptacle
        Sofa_bar__minus_02_dot_49_bar__plus_00_dot_03_bar__plus_00_dot_73 - receptacle
        Sofa_bar__minus_04_dot_36_bar__plus_00_dot_03_bar__plus_02_dot_93 - receptacle
        loc_bar__minus_14_bar_6_bar_2_bar_0 - location
        loc_bar__minus_6_bar_17_bar_3_bar_60 - location
        loc_bar__minus_5_bar_24_bar_1_bar_15 - location
        loc_bar__minus_5_bar_10_bar_1_bar_60 - location
        loc_bar__minus_11_bar_26_bar_0_bar_15 - location
        loc_bar__minus_12_bar_10_bar_3_bar_45 - location
        loc_bar__minus_11_bar_8_bar_2_bar_45 - location
        loc_bar__minus_6_bar_16_bar_1_bar_0 - location
        loc_bar__minus_4_bar_11_bar_1_bar__minus_30 - location
        loc_bar__minus_13_bar_25_bar_3_bar_60 - location
        loc_bar__minus_18_bar_23_bar_3_bar_45 - location
        loc_bar__minus_3_bar_6_bar_1_bar_15 - location
        loc_bar__minus_14_bar_6_bar_3_bar_60 - location
        loc_bar__minus_4_bar_8_bar_2_bar_60 - location
        loc_bar__minus_5_bar_6_bar_2_bar_0 - location
        loc_bar__minus_9_bar_6_bar_2_bar__minus_30 - location
        loc_bar__minus_6_bar_24_bar_1_bar_45 - location
        loc_bar__minus_11_bar_6_bar_2_bar__minus_30 - location
        loc_bar__minus_5_bar_23_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType Sofa_bar__minus_04_dot_36_bar__plus_00_dot_03_bar__plus_02_dot_93 SofaType)
        (receptacleType Sofa_bar__minus_02_dot_49_bar__plus_00_dot_03_bar__plus_00_dot_73 SofaType)
        (receptacleType GarbageCan_bar__minus_00_dot_52_bar__plus_00_dot_02_bar__plus_02_dot_82 GarbageCanType)
        (receptacleType Shelf_bar__minus_00_dot_39_bar__plus_01_dot_45_bar__plus_05_dot_95 ShelfType)
        (receptacleType CoffeeTable_bar__minus_02_dot_65_bar__plus_00_dot_02_bar__plus_04_dot_06 CoffeeTableType)
        (receptacleType Shelf_bar__minus_00_dot_40_bar__plus_00_dot_27_bar__plus_05_dot_96 ShelfType)
        (receptacleType SideTable_bar__minus_00_dot_59_bar__plus_00_dot_02_bar__plus_00_dot_84 SideTableType)
        (receptacleType ArmChair_bar__minus_04_dot_45_bar__plus_00_dot_00_bar__plus_06_dot_45 ArmChairType)
        (objectType Curtains_bar__minus_02_dot_19_bar__plus_02_dot_47_bar__plus_00_dot_04 CurtainsType)
        (objectType Plate_bar__minus_02_dot_89_bar__plus_00_dot_45_bar__plus_04_dot_28 PlateType)
        (objectType Box_bar__minus_02_dot_40_bar__plus_00_dot_57_bar__plus_04_dot_50 BoxType)
        (objectType HousePlant_bar__minus_02_dot_66_bar__plus_00_dot_44_bar__plus_04_dot_17 HousePlantType)
        (objectType Curtains_bar__minus_00_dot_05_bar__plus_02_dot_48_bar__plus_02_dot_63 CurtainsType)
        (objectType Window_bar__minus_00_dot_01_bar__plus_01_dot_57_bar__plus_01_dot_50 WindowType)
        (objectType KeyChain_bar__minus_02_dot_19_bar__plus_00_dot_54_bar__plus_00_dot_98 KeyChainType)
        (objectType KeyChain_bar__minus_00_dot_44_bar__plus_00_dot_62_bar__plus_01_dot_00 KeyChainType)
        (objectType RemoteControl_bar__minus_00_dot_36_bar__plus_00_dot_62_bar__plus_00_dot_92 RemoteControlType)
        (objectType LightSwitch_bar__minus_05_dot_00_bar__plus_01_dot_25_bar__plus_05_dot_86 LightSwitchType)
        (objectType WateringCan_bar__minus_03_dot_02_bar__plus_00_dot_45_bar__plus_03_dot_61 WateringCanType)
        (objectType Newspaper_bar__minus_02_dot_49_bar__plus_00_dot_55_bar__plus_00_dot_98 NewspaperType)
        (objectType Vase_bar__minus_00_dot_48_bar__plus_00_dot_28_bar__plus_06_dot_49 VaseType)
        (objectType Laptop_bar__minus_04_dot_34_bar__plus_00_dot_52_bar__plus_02_dot_22 LaptopType)
        (objectType FloorLamp_bar__minus_04_dot_51_bar__plus_00_dot_02_bar__plus_00_dot_66 FloorLampType)
        (objectType Statue_bar__minus_00_dot_60_bar__plus_01_dot_45_bar__plus_06_dot_79 StatueType)
        (objectType Television_bar__minus_00_dot_75_bar__plus_01_dot_66_bar__plus_03_dot_94 TelevisionType)
        (objectType WateringCan_bar__minus_00_dot_24_bar__plus_00_dot_28_bar__plus_05_dot_26 WateringCanType)
        (objectType Newspaper_bar__minus_00_dot_56_bar__plus_01_dot_47_bar__plus_05_dot_74 NewspaperType)
        (objectType Statue_bar__minus_00_dot_53_bar__plus_01_dot_45_bar__plus_05_dot_35 StatueType)
        (objectType CreditCard_bar__minus_03_dot_02_bar__plus_00_dot_45_bar__plus_04_dot_06 CreditCardType)
        (objectType Window_bar__minus_01_dot_37_bar__plus_01_dot_57_bar__plus_00_dot_01 WindowType)
        (objectType Plate_bar__minus_00_dot_24_bar__plus_00_dot_29_bar__plus_05_dot_50 PlateType)
        (objectType Pillow_bar__minus_01_dot_58_bar__plus_00_dot_61_bar__plus_00_dot_67 PillowType)
        (objectType RemoteControl_bar__minus_04_dot_05_bar__plus_00_dot_45_bar__plus_06_dot_36 RemoteControlType)
        (objectType Curtains_bar__minus_02_dot_83_bar__plus_02_dot_47_bar__plus_00_dot_04 CurtainsType)
        (objectType CreditCard_bar__minus_00_dot_48_bar__plus_00_dot_28_bar__plus_05_dot_26 CreditCardType)
        (objectType CreditCard_bar__minus_02_dot_53_bar__plus_00_dot_45_bar__plus_04_dot_28 CreditCardType)
        (objectType Window_bar__minus_03_dot_73_bar__plus_01_dot_57_bar__plus_00_dot_01 WindowType)
        (objectType Vase_bar__minus_00_dot_40_bar__plus_00_dot_29_bar__plus_06_dot_74 VaseType)
        (objectType Painting_bar__minus_02_dot_67_bar__plus_01_dot_57_bar__plus_07_dot_00 PaintingType)
        (objectType Box_bar__minus_04_dot_28_bar__plus_00_dot_66_bar__plus_03_dot_23 BoxType)
        (objectType Statue_bar__minus_02_dot_65_bar__plus_00_dot_46_bar__plus_03_dot_84 StatueType)
        (objectType Plate_bar__minus_02_dot_91_bar__plus_00_dot_44_bar__plus_03_dot_40 PlateType)
        (objectType RemoteControl_bar__minus_00_dot_67_bar__plus_00_dot_62_bar__plus_01_dot_00 RemoteControlType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain GarbageCanType NewspaperType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WateringCanType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType VaseType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType PlateType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain CoffeeTableType WateringCanType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PlateType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WateringCanType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PlateType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (pickupable Plate_bar__minus_02_dot_89_bar__plus_00_dot_45_bar__plus_04_dot_28)
        (pickupable Box_bar__minus_02_dot_40_bar__plus_00_dot_57_bar__plus_04_dot_50)
        (pickupable KeyChain_bar__minus_02_dot_19_bar__plus_00_dot_54_bar__plus_00_dot_98)
        (pickupable KeyChain_bar__minus_00_dot_44_bar__plus_00_dot_62_bar__plus_01_dot_00)
        (pickupable RemoteControl_bar__minus_00_dot_36_bar__plus_00_dot_62_bar__plus_00_dot_92)
        (pickupable WateringCan_bar__minus_03_dot_02_bar__plus_00_dot_45_bar__plus_03_dot_61)
        (pickupable Newspaper_bar__minus_02_dot_49_bar__plus_00_dot_55_bar__plus_00_dot_98)
        (pickupable Vase_bar__minus_00_dot_48_bar__plus_00_dot_28_bar__plus_06_dot_49)
        (pickupable Laptop_bar__minus_04_dot_34_bar__plus_00_dot_52_bar__plus_02_dot_22)
        (pickupable Statue_bar__minus_00_dot_60_bar__plus_01_dot_45_bar__plus_06_dot_79)
        (pickupable WateringCan_bar__minus_00_dot_24_bar__plus_00_dot_28_bar__plus_05_dot_26)
        (pickupable Newspaper_bar__minus_00_dot_56_bar__plus_01_dot_47_bar__plus_05_dot_74)
        (pickupable Statue_bar__minus_00_dot_53_bar__plus_01_dot_45_bar__plus_05_dot_35)
        (pickupable CreditCard_bar__minus_03_dot_02_bar__plus_00_dot_45_bar__plus_04_dot_06)
        (pickupable Plate_bar__minus_00_dot_24_bar__plus_00_dot_29_bar__plus_05_dot_50)
        (pickupable Pillow_bar__minus_01_dot_58_bar__plus_00_dot_61_bar__plus_00_dot_67)
        (pickupable RemoteControl_bar__minus_04_dot_05_bar__plus_00_dot_45_bar__plus_06_dot_36)
        (pickupable CreditCard_bar__minus_00_dot_48_bar__plus_00_dot_28_bar__plus_05_dot_26)
        (pickupable CreditCard_bar__minus_02_dot_53_bar__plus_00_dot_45_bar__plus_04_dot_28)
        (pickupable Vase_bar__minus_00_dot_40_bar__plus_00_dot_29_bar__plus_06_dot_74)
        (pickupable Box_bar__minus_04_dot_28_bar__plus_00_dot_66_bar__plus_03_dot_23)
        (pickupable Statue_bar__minus_02_dot_65_bar__plus_00_dot_46_bar__plus_03_dot_84)
        (pickupable Plate_bar__minus_02_dot_91_bar__plus_00_dot_44_bar__plus_03_dot_40)
        (pickupable RemoteControl_bar__minus_00_dot_67_bar__plus_00_dot_62_bar__plus_01_dot_00)
        (isReceptacleObject Plate_bar__minus_02_dot_89_bar__plus_00_dot_45_bar__plus_04_dot_28)
        (isReceptacleObject Box_bar__minus_02_dot_40_bar__plus_00_dot_57_bar__plus_04_dot_50)
        (isReceptacleObject Plate_bar__minus_00_dot_24_bar__plus_00_dot_29_bar__plus_05_dot_50)
        (isReceptacleObject Box_bar__minus_04_dot_28_bar__plus_00_dot_66_bar__plus_03_dot_23)
        (isReceptacleObject Plate_bar__minus_02_dot_91_bar__plus_00_dot_44_bar__plus_03_dot_40)
        
        
        (atLocation agent1 loc_bar__minus_5_bar_23_bar_3_bar_30)
        
        (cleanable Plate_bar__minus_02_dot_89_bar__plus_00_dot_45_bar__plus_04_dot_28)
        (cleanable Plate_bar__minus_00_dot_24_bar__plus_00_dot_29_bar__plus_05_dot_50)
        (cleanable Plate_bar__minus_02_dot_91_bar__plus_00_dot_44_bar__plus_03_dot_40)
        
        (heatable Plate_bar__minus_02_dot_89_bar__plus_00_dot_45_bar__plus_04_dot_28)
        (heatable Plate_bar__minus_00_dot_24_bar__plus_00_dot_29_bar__plus_05_dot_50)
        (heatable Plate_bar__minus_02_dot_91_bar__plus_00_dot_44_bar__plus_03_dot_40)
        (coolable Plate_bar__minus_02_dot_89_bar__plus_00_dot_45_bar__plus_04_dot_28)
        (coolable Plate_bar__minus_00_dot_24_bar__plus_00_dot_29_bar__plus_05_dot_50)
        (coolable Plate_bar__minus_02_dot_91_bar__plus_00_dot_44_bar__plus_03_dot_40)
        
        
        (toggleable FloorLamp_bar__minus_04_dot_51_bar__plus_00_dot_02_bar__plus_00_dot_66)
        
        
        
        
        (inReceptacleObject WateringCan_bar__minus_03_dot_02_bar__plus_00_dot_45_bar__plus_03_dot_61 Plate_bar__minus_02_dot_91_bar__plus_00_dot_44_bar__plus_03_dot_40)
        (inReceptacle RemoteControl_bar__minus_04_dot_05_bar__plus_00_dot_45_bar__plus_06_dot_36 ArmChair_bar__minus_04_dot_45_bar__plus_00_dot_00_bar__plus_06_dot_45)
        (inReceptacle RemoteControl_bar__minus_00_dot_36_bar__plus_00_dot_62_bar__plus_00_dot_92 SideTable_bar__minus_00_dot_59_bar__plus_00_dot_02_bar__plus_00_dot_84)
        (inReceptacle RemoteControl_bar__minus_00_dot_67_bar__plus_00_dot_62_bar__plus_01_dot_00 SideTable_bar__minus_00_dot_59_bar__plus_00_dot_02_bar__plus_00_dot_84)
        (inReceptacle KeyChain_bar__minus_00_dot_44_bar__plus_00_dot_62_bar__plus_01_dot_00 SideTable_bar__minus_00_dot_59_bar__plus_00_dot_02_bar__plus_00_dot_84)
        (inReceptacle Plate_bar__minus_02_dot_89_bar__plus_00_dot_45_bar__plus_04_dot_28 CoffeeTable_bar__minus_02_dot_65_bar__plus_00_dot_02_bar__plus_04_dot_06)
        (inReceptacle Box_bar__minus_02_dot_40_bar__plus_00_dot_57_bar__plus_04_dot_50 CoffeeTable_bar__minus_02_dot_65_bar__plus_00_dot_02_bar__plus_04_dot_06)
        (inReceptacle Statue_bar__minus_02_dot_65_bar__plus_00_dot_46_bar__plus_03_dot_84 CoffeeTable_bar__minus_02_dot_65_bar__plus_00_dot_02_bar__plus_04_dot_06)
        (inReceptacle HousePlant_bar__minus_02_dot_66_bar__plus_00_dot_44_bar__plus_04_dot_17 CoffeeTable_bar__minus_02_dot_65_bar__plus_00_dot_02_bar__plus_04_dot_06)
        (inReceptacle CreditCard_bar__minus_03_dot_02_bar__plus_00_dot_45_bar__plus_04_dot_06 CoffeeTable_bar__minus_02_dot_65_bar__plus_00_dot_02_bar__plus_04_dot_06)
        (inReceptacle CreditCard_bar__minus_02_dot_53_bar__plus_00_dot_45_bar__plus_04_dot_28 CoffeeTable_bar__minus_02_dot_65_bar__plus_00_dot_02_bar__plus_04_dot_06)
        (inReceptacle WateringCan_bar__minus_03_dot_02_bar__plus_00_dot_45_bar__plus_03_dot_61 CoffeeTable_bar__minus_02_dot_65_bar__plus_00_dot_02_bar__plus_04_dot_06)
        (inReceptacle Plate_bar__minus_02_dot_91_bar__plus_00_dot_44_bar__plus_03_dot_40 CoffeeTable_bar__minus_02_dot_65_bar__plus_00_dot_02_bar__plus_04_dot_06)
        (inReceptacle Pillow_bar__minus_01_dot_58_bar__plus_00_dot_61_bar__plus_00_dot_67 Sofa_bar__minus_02_dot_49_bar__plus_00_dot_03_bar__plus_00_dot_73)
        (inReceptacle KeyChain_bar__minus_02_dot_19_bar__plus_00_dot_54_bar__plus_00_dot_98 Sofa_bar__minus_02_dot_49_bar__plus_00_dot_03_bar__plus_00_dot_73)
        (inReceptacle Newspaper_bar__minus_02_dot_49_bar__plus_00_dot_55_bar__plus_00_dot_98 Sofa_bar__minus_02_dot_49_bar__plus_00_dot_03_bar__plus_00_dot_73)
        (inReceptacle Laptop_bar__minus_04_dot_34_bar__plus_00_dot_52_bar__plus_02_dot_22 Sofa_bar__minus_04_dot_36_bar__plus_00_dot_03_bar__plus_02_dot_93)
        (inReceptacle Box_bar__minus_04_dot_28_bar__plus_00_dot_66_bar__plus_03_dot_23 Sofa_bar__minus_04_dot_36_bar__plus_00_dot_03_bar__plus_02_dot_93)
        (inReceptacle Statue_bar__minus_00_dot_60_bar__plus_01_dot_45_bar__plus_06_dot_79 Shelf_bar__minus_00_dot_39_bar__plus_01_dot_45_bar__plus_05_dot_95)
        (inReceptacle Statue_bar__minus_00_dot_53_bar__plus_01_dot_45_bar__plus_05_dot_35 Shelf_bar__minus_00_dot_39_bar__plus_01_dot_45_bar__plus_05_dot_95)
        (inReceptacle Newspaper_bar__minus_00_dot_56_bar__plus_01_dot_47_bar__plus_05_dot_74 Shelf_bar__minus_00_dot_39_bar__plus_01_dot_45_bar__plus_05_dot_95)
        (inReceptacle Vase_bar__minus_00_dot_40_bar__plus_00_dot_29_bar__plus_06_dot_74 Shelf_bar__minus_00_dot_40_bar__plus_00_dot_27_bar__plus_05_dot_96)
        (inReceptacle CreditCard_bar__minus_00_dot_48_bar__plus_00_dot_28_bar__plus_05_dot_26 Shelf_bar__minus_00_dot_40_bar__plus_00_dot_27_bar__plus_05_dot_96)
        (inReceptacle WateringCan_bar__minus_00_dot_24_bar__plus_00_dot_28_bar__plus_05_dot_26 Shelf_bar__minus_00_dot_40_bar__plus_00_dot_27_bar__plus_05_dot_96)
        (inReceptacle Plate_bar__minus_00_dot_24_bar__plus_00_dot_29_bar__plus_05_dot_50 Shelf_bar__minus_00_dot_40_bar__plus_00_dot_27_bar__plus_05_dot_96)
        (inReceptacle Vase_bar__minus_00_dot_48_bar__plus_00_dot_28_bar__plus_06_dot_49 Shelf_bar__minus_00_dot_40_bar__plus_00_dot_27_bar__plus_05_dot_96)
        
        
        (receptacleAtLocation ArmChair_bar__minus_04_dot_45_bar__plus_00_dot_00_bar__plus_06_dot_45 loc_bar__minus_13_bar_25_bar_3_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_02_dot_65_bar__plus_00_dot_02_bar__plus_04_dot_06 loc_bar__minus_6_bar_17_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_00_dot_52_bar__plus_00_dot_02_bar__plus_02_dot_82 loc_bar__minus_5_bar_10_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__minus_00_dot_39_bar__plus_01_dot_45_bar__plus_05_dot_95 loc_bar__minus_5_bar_24_bar_1_bar_15)
        (receptacleAtLocation Shelf_bar__minus_00_dot_40_bar__plus_00_dot_27_bar__plus_05_dot_96 loc_bar__minus_6_bar_24_bar_1_bar_45)
        (receptacleAtLocation SideTable_bar__minus_00_dot_59_bar__plus_00_dot_02_bar__plus_00_dot_84 loc_bar__minus_4_bar_8_bar_2_bar_60)
        (receptacleAtLocation Sofa_bar__minus_02_dot_49_bar__plus_00_dot_03_bar__plus_00_dot_73 loc_bar__minus_11_bar_8_bar_2_bar_45)
        (receptacleAtLocation Sofa_bar__minus_04_dot_36_bar__plus_00_dot_03_bar__plus_02_dot_93 loc_bar__minus_12_bar_10_bar_3_bar_45)
        (objectAtLocation CreditCard_bar__minus_03_dot_02_bar__plus_00_dot_45_bar__plus_04_dot_06 loc_bar__minus_6_bar_17_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__minus_00_dot_36_bar__plus_00_dot_62_bar__plus_00_dot_92 loc_bar__minus_4_bar_8_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_02_dot_19_bar__plus_00_dot_54_bar__plus_00_dot_98 loc_bar__minus_11_bar_8_bar_2_bar_45)
        (objectAtLocation Newspaper_bar__minus_02_dot_49_bar__plus_00_dot_55_bar__plus_00_dot_98 loc_bar__minus_11_bar_8_bar_2_bar_45)
        (objectAtLocation WateringCan_bar__minus_00_dot_24_bar__plus_00_dot_28_bar__plus_05_dot_26 loc_bar__minus_6_bar_24_bar_1_bar_45)
        (objectAtLocation Plate_bar__minus_02_dot_91_bar__plus_00_dot_44_bar__plus_03_dot_40 loc_bar__minus_6_bar_17_bar_3_bar_60)
        (objectAtLocation Box_bar__minus_02_dot_40_bar__plus_00_dot_57_bar__plus_04_dot_50 loc_bar__minus_6_bar_17_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__minus_00_dot_67_bar__plus_00_dot_62_bar__plus_01_dot_00 loc_bar__minus_4_bar_8_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__minus_00_dot_48_bar__plus_00_dot_28_bar__plus_05_dot_26 loc_bar__minus_6_bar_24_bar_1_bar_45)
        (objectAtLocation Plate_bar__minus_00_dot_24_bar__plus_00_dot_29_bar__plus_05_dot_50 loc_bar__minus_6_bar_24_bar_1_bar_45)
        (objectAtLocation Box_bar__minus_04_dot_28_bar__plus_00_dot_66_bar__plus_03_dot_23 loc_bar__minus_12_bar_10_bar_3_bar_45)
        (objectAtLocation Curtains_bar__minus_02_dot_83_bar__plus_02_dot_47_bar__plus_00_dot_04 loc_bar__minus_11_bar_6_bar_2_bar__minus_30)
        (objectAtLocation Curtains_bar__minus_02_dot_19_bar__plus_02_dot_47_bar__plus_00_dot_04 loc_bar__minus_9_bar_6_bar_2_bar__minus_30)
        (objectAtLocation Curtains_bar__minus_00_dot_05_bar__plus_02_dot_48_bar__plus_02_dot_63 loc_bar__minus_4_bar_11_bar_1_bar__minus_30)
        (objectAtLocation Plate_bar__minus_02_dot_89_bar__plus_00_dot_45_bar__plus_04_dot_28 loc_bar__minus_6_bar_17_bar_3_bar_60)
        (objectAtLocation Vase_bar__minus_00_dot_48_bar__plus_00_dot_28_bar__plus_06_dot_49 loc_bar__minus_6_bar_24_bar_1_bar_45)
        (objectAtLocation WateringCan_bar__minus_03_dot_02_bar__plus_00_dot_45_bar__plus_03_dot_61 loc_bar__minus_6_bar_17_bar_3_bar_60)
        (objectAtLocation Newspaper_bar__minus_00_dot_56_bar__plus_01_dot_47_bar__plus_05_dot_74 loc_bar__minus_5_bar_24_bar_1_bar_15)
        (objectAtLocation Vase_bar__minus_00_dot_40_bar__plus_00_dot_29_bar__plus_06_dot_74 loc_bar__minus_6_bar_24_bar_1_bar_45)
        (objectAtLocation KeyChain_bar__minus_00_dot_44_bar__plus_00_dot_62_bar__plus_01_dot_00 loc_bar__minus_4_bar_8_bar_2_bar_60)
        (objectAtLocation Pillow_bar__minus_01_dot_58_bar__plus_00_dot_61_bar__plus_00_dot_67 loc_bar__minus_11_bar_8_bar_2_bar_45)
        (objectAtLocation Television_bar__minus_00_dot_75_bar__plus_01_dot_66_bar__plus_03_dot_94 loc_bar__minus_6_bar_16_bar_1_bar_0)
        (objectAtLocation Statue_bar__minus_02_dot_65_bar__plus_00_dot_46_bar__plus_03_dot_84 loc_bar__minus_6_bar_17_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__minus_04_dot_05_bar__plus_00_dot_45_bar__plus_06_dot_36 loc_bar__minus_13_bar_25_bar_3_bar_60)
        (objectAtLocation Painting_bar__minus_02_dot_67_bar__plus_01_dot_57_bar__plus_07_dot_00 loc_bar__minus_11_bar_26_bar_0_bar_15)
        (objectAtLocation CreditCard_bar__minus_02_dot_53_bar__plus_00_dot_45_bar__plus_04_dot_28 loc_bar__minus_6_bar_17_bar_3_bar_60)
        (objectAtLocation Statue_bar__minus_00_dot_53_bar__plus_01_dot_45_bar__plus_05_dot_35 loc_bar__minus_5_bar_24_bar_1_bar_15)
        (objectAtLocation LightSwitch_bar__minus_05_dot_00_bar__plus_01_dot_25_bar__plus_05_dot_86 loc_bar__minus_18_bar_23_bar_3_bar_45)
        (objectAtLocation Laptop_bar__minus_04_dot_34_bar__plus_00_dot_52_bar__plus_02_dot_22 loc_bar__minus_12_bar_10_bar_3_bar_45)
        (objectAtLocation Window_bar__minus_01_dot_37_bar__plus_01_dot_57_bar__plus_00_dot_01 loc_bar__minus_5_bar_6_bar_2_bar_0)
        (objectAtLocation Window_bar__minus_00_dot_01_bar__plus_01_dot_57_bar__plus_01_dot_50 loc_bar__minus_3_bar_6_bar_1_bar_15)
        (objectAtLocation Window_bar__minus_03_dot_73_bar__plus_01_dot_57_bar__plus_00_dot_01 loc_bar__minus_14_bar_6_bar_2_bar_0)
        (objectAtLocation FloorLamp_bar__minus_04_dot_51_bar__plus_00_dot_02_bar__plus_00_dot_66 loc_bar__minus_14_bar_6_bar_3_bar_60)
        (objectAtLocation HousePlant_bar__minus_02_dot_66_bar__plus_00_dot_44_bar__plus_04_dot_17 loc_bar__minus_6_bar_17_bar_3_bar_60)
        (objectAtLocation Statue_bar__minus_00_dot_60_bar__plus_01_dot_45_bar__plus_06_dot_79 loc_bar__minus_5_bar_24_bar_1_bar_15)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 VaseType)
                                    (receptacleType ?r CoffeeTableType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 VaseType)
                                            (receptacleType ?r CoffeeTableType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            