
(define (problem plan_trial_T20190907_003920_143354)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__plus_01_dot_49_bar__plus_00_dot_48_bar__minus_00_dot_76 - object
        BaseballBat_bar__plus_01_dot_80_bar__plus_00_dot_64_bar__plus_00_dot_43 - object
        Blinds_bar__plus_00_dot_80_bar__plus_02_dot_22_bar__minus_02_dot_48 - object
        Book_bar__plus_01_dot_75_bar__plus_00_dot_69_bar__minus_01_dot_74 - object
        Book_bar__minus_00_dot_29_bar__plus_00_dot_69_bar__minus_01_dot_98 - object
        Book_bar__minus_00_dot_41_bar__plus_00_dot_66_bar__plus_00_dot_56 - object
        Bowl_bar__plus_00_dot_85_bar__plus_00_dot_70_bar__minus_02_dot_13 - object
        Box_bar__minus_00_dot_02_bar__plus_00_dot_24_bar__minus_02_dot_09 - object
        CD_bar__plus_00_dot_27_bar__plus_00_dot_69_bar__minus_02_dot_18 - object
        CD_bar__plus_01_dot_37_bar__plus_00_dot_69_bar__minus_02_dot_17 - object
        CD_bar__plus_01_dot_68_bar__plus_00_dot_05_bar__minus_00_dot_37 - object
        CellPhone_bar__plus_00_dot_16_bar__plus_00_dot_69_bar__minus_02_dot_38 - object
        Chair_bar__plus_00_dot_96_bar__plus_00_dot_00_bar__minus_01_dot_75 - object
        CreditCard_bar__minus_00_dot_40_bar__plus_00_dot_37_bar__plus_00_dot_50 - object
        CreditCard_bar__minus_00_dot_45_bar__plus_00_dot_09_bar__plus_00_dot_50 - object
        CreditCard_bar__minus_00_dot_51_bar__plus_00_dot_37_bar__plus_00_dot_46 - object
        DeskLamp_bar__minus_00_dot_44_bar__plus_00_dot_69_bar__minus_02_dot_27 - object
        HousePlant_bar__minus_00_dot_87_bar__plus_00_dot_00_bar__minus_02_dot_29 - object
        KeyChain_bar__plus_01_dot_53_bar__plus_00_dot_47_bar__minus_00_dot_86 - object
        KeyChain_bar__minus_00_dot_67_bar__plus_00_dot_37_bar__plus_00_dot_46 - object
        Laptop_bar__minus_02_dot_05_bar__plus_00_dot_63_bar__minus_00_dot_47 - object
        LightSwitch_bar__plus_00_dot_63_bar__plus_01_dot_34_bar__plus_00_dot_73 - object
        Mirror_bar__plus_01_dot_30_bar__plus_01_dot_42_bar__plus_00_dot_77 - object
        Pencil_bar__plus_01_dot_03_bar__plus_00_dot_70_bar__minus_02_dot_27 - object
        Pencil_bar__minus_00_dot_49_bar__plus_00_dot_67_bar__plus_00_dot_64 - object
        Pencil_bar__minus_00_dot_63_bar__plus_00_dot_67_bar__plus_00_dot_43 - object
        Pen_bar__plus_00_dot_15_bar__plus_00_dot_70_bar__minus_02_dot_01 - object
        Pen_bar__plus_01_dot_55_bar__plus_00_dot_70_bar__minus_02_dot_03 - object
        Pillow_bar__minus_01_dot_52_bar__plus_00_dot_70_bar__minus_00_dot_47 - object
        Window_bar__plus_00_dot_82_bar__plus_01_dot_50_bar__minus_02_dot_55 - object
        Bed_bar__minus_01_dot_70_bar__plus_00_dot_04_bar__minus_00_dot_45 - receptacle
        Desk_bar__plus_01_dot_49_bar__plus_00_dot_69_bar__minus_01_dot_84 - receptacle
        Drawer_bar__minus_00_dot_56_bar__plus_00_dot_18_bar__plus_00_dot_49 - receptacle
        Drawer_bar__minus_00_dot_56_bar__plus_00_dot_45_bar__plus_00_dot_49 - receptacle
        GarbageCan_bar__plus_01_dot_66_bar__plus_00_dot_00_bar__minus_00_dot_39 - receptacle
        Shelf_bar__plus_01_dot_49_bar__plus_00_dot_47_bar__minus_00_dot_82 - receptacle
        SideTable_bar__minus_00_dot_56_bar__plus_00_dot_45_bar__plus_00_dot_49 - receptacle
        loc_bar_3_bar__minus_2_bar_1_bar_60 - location
        loc_bar_2_bar_1_bar_3_bar_45 - location
        loc_bar_0_bar__minus_5_bar_1_bar_30 - location
        loc_bar_5_bar__minus_1_bar_1_bar_60 - location
        loc_bar_0_bar__minus_2_bar_0_bar_60 - location
        loc_bar_4_bar__minus_3_bar_1_bar_60 - location
        loc_bar__minus_1_bar__minus_1_bar_3_bar_45 - location
        loc_bar_4_bar__minus_4_bar_2_bar_60 - location
        loc_bar_0_bar__minus_6_bar_2_bar_60 - location
        loc_bar_1_bar__minus_6_bar_2_bar__minus_30 - location
        loc_bar_1_bar__minus_6_bar_2_bar_15 - location
        loc_bar__minus_2_bar_0_bar_0_bar_60 - location
        loc_bar_5_bar_1_bar_0_bar_30 - location
        loc_bar_5_bar_1_bar_1_bar_60 - location
        loc_bar_3_bar_1_bar_0_bar_30 - location
        loc_bar_5_bar_1_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType GarbageCan_bar__plus_01_dot_66_bar__plus_00_dot_00_bar__minus_00_dot_39 GarbageCanType)
        (receptacleType SideTable_bar__minus_00_dot_56_bar__plus_00_dot_45_bar__plus_00_dot_49 SideTableType)
        (receptacleType Shelf_bar__plus_01_dot_49_bar__plus_00_dot_47_bar__minus_00_dot_82 ShelfType)
        (receptacleType Desk_bar__plus_01_dot_49_bar__plus_00_dot_69_bar__minus_01_dot_84 DeskType)
        (receptacleType Bed_bar__minus_01_dot_70_bar__plus_00_dot_04_bar__minus_00_dot_45 BedType)
        (receptacleType Drawer_bar__minus_00_dot_56_bar__plus_00_dot_45_bar__plus_00_dot_49 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_56_bar__plus_00_dot_18_bar__plus_00_dot_49 DrawerType)
        (objectType CellPhone_bar__plus_00_dot_16_bar__plus_00_dot_69_bar__minus_02_dot_38 CellPhoneType)
        (objectType Box_bar__minus_00_dot_02_bar__plus_00_dot_24_bar__minus_02_dot_09 BoxType)
        (objectType Window_bar__plus_00_dot_82_bar__plus_01_dot_50_bar__minus_02_dot_55 WindowType)
        (objectType HousePlant_bar__minus_00_dot_87_bar__plus_00_dot_00_bar__minus_02_dot_29 HousePlantType)
        (objectType BaseballBat_bar__plus_01_dot_80_bar__plus_00_dot_64_bar__plus_00_dot_43 BaseballBatType)
        (objectType CD_bar__plus_00_dot_27_bar__plus_00_dot_69_bar__minus_02_dot_18 CDType)
        (objectType Bowl_bar__plus_00_dot_85_bar__plus_00_dot_70_bar__minus_02_dot_13 BowlType)
        (objectType KeyChain_bar__minus_00_dot_67_bar__plus_00_dot_37_bar__plus_00_dot_46 KeyChainType)
        (objectType CreditCard_bar__minus_00_dot_51_bar__plus_00_dot_37_bar__plus_00_dot_46 CreditCardType)
        (objectType CreditCard_bar__minus_00_dot_45_bar__plus_00_dot_09_bar__plus_00_dot_50 CreditCardType)
        (objectType Pen_bar__plus_00_dot_15_bar__plus_00_dot_70_bar__minus_02_dot_01 PenType)
        (objectType Book_bar__minus_00_dot_41_bar__plus_00_dot_66_bar__plus_00_dot_56 BookType)
        (objectType Laptop_bar__minus_02_dot_05_bar__plus_00_dot_63_bar__minus_00_dot_47 LaptopType)
        (objectType Pencil_bar__minus_00_dot_49_bar__plus_00_dot_67_bar__plus_00_dot_64 PencilType)
        (objectType Blinds_bar__plus_00_dot_80_bar__plus_02_dot_22_bar__minus_02_dot_48 BlindsType)
        (objectType Mirror_bar__plus_01_dot_30_bar__plus_01_dot_42_bar__plus_00_dot_77 MirrorType)
        (objectType Chair_bar__plus_00_dot_96_bar__plus_00_dot_00_bar__minus_01_dot_75 ChairType)
        (objectType Book_bar__minus_00_dot_29_bar__plus_00_dot_69_bar__minus_01_dot_98 BookType)
        (objectType Pillow_bar__minus_01_dot_52_bar__plus_00_dot_70_bar__minus_00_dot_47 PillowType)
        (objectType KeyChain_bar__plus_01_dot_53_bar__plus_00_dot_47_bar__minus_00_dot_86 KeyChainType)
        (objectType LightSwitch_bar__plus_00_dot_63_bar__plus_01_dot_34_bar__plus_00_dot_73 LightSwitchType)
        (objectType Pencil_bar__plus_01_dot_03_bar__plus_00_dot_70_bar__minus_02_dot_27 PencilType)
        (objectType DeskLamp_bar__minus_00_dot_44_bar__plus_00_dot_69_bar__minus_02_dot_27 DeskLampType)
        (objectType Book_bar__plus_01_dot_75_bar__plus_00_dot_69_bar__minus_01_dot_74 BookType)
        (objectType CD_bar__plus_01_dot_37_bar__plus_00_dot_69_bar__minus_02_dot_17 CDType)
        (objectType Pencil_bar__minus_00_dot_63_bar__plus_00_dot_67_bar__plus_00_dot_43 PencilType)
        (objectType CreditCard_bar__minus_00_dot_40_bar__plus_00_dot_37_bar__plus_00_dot_50 CreditCardType)
        (objectType AlarmClock_bar__plus_01_dot_49_bar__plus_00_dot_48_bar__minus_00_dot_76 AlarmClockType)
        (objectType Pen_bar__plus_01_dot_55_bar__plus_00_dot_70_bar__minus_02_dot_03 PenType)
        (objectType CD_bar__plus_01_dot_68_bar__plus_00_dot_05_bar__minus_00_dot_37 CDType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType BowlType)
        (canContain SideTableType CDType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType BaseballBatType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DeskType PenType)
        (canContain DeskType BookType)
        (canContain DeskType BowlType)
        (canContain DeskType CDType)
        (canContain DeskType BoxType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType CreditCardType)
        (canContain DeskType LaptopType)
        (canContain DeskType PencilType)
        (canContain DeskType AlarmClockType)
        (canContain BedType BaseballBatType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (pickupable CellPhone_bar__plus_00_dot_16_bar__plus_00_dot_69_bar__minus_02_dot_38)
        (pickupable Box_bar__minus_00_dot_02_bar__plus_00_dot_24_bar__minus_02_dot_09)
        (pickupable BaseballBat_bar__plus_01_dot_80_bar__plus_00_dot_64_bar__plus_00_dot_43)
        (pickupable CD_bar__plus_00_dot_27_bar__plus_00_dot_69_bar__minus_02_dot_18)
        (pickupable Bowl_bar__plus_00_dot_85_bar__plus_00_dot_70_bar__minus_02_dot_13)
        (pickupable KeyChain_bar__minus_00_dot_67_bar__plus_00_dot_37_bar__plus_00_dot_46)
        (pickupable CreditCard_bar__minus_00_dot_51_bar__plus_00_dot_37_bar__plus_00_dot_46)
        (pickupable CreditCard_bar__minus_00_dot_45_bar__plus_00_dot_09_bar__plus_00_dot_50)
        (pickupable Pen_bar__plus_00_dot_15_bar__plus_00_dot_70_bar__minus_02_dot_01)
        (pickupable Book_bar__minus_00_dot_41_bar__plus_00_dot_66_bar__plus_00_dot_56)
        (pickupable Laptop_bar__minus_02_dot_05_bar__plus_00_dot_63_bar__minus_00_dot_47)
        (pickupable Pencil_bar__minus_00_dot_49_bar__plus_00_dot_67_bar__plus_00_dot_64)
        (pickupable Book_bar__minus_00_dot_29_bar__plus_00_dot_69_bar__minus_01_dot_98)
        (pickupable Pillow_bar__minus_01_dot_52_bar__plus_00_dot_70_bar__minus_00_dot_47)
        (pickupable KeyChain_bar__plus_01_dot_53_bar__plus_00_dot_47_bar__minus_00_dot_86)
        (pickupable Pencil_bar__plus_01_dot_03_bar__plus_00_dot_70_bar__minus_02_dot_27)
        (pickupable Book_bar__plus_01_dot_75_bar__plus_00_dot_69_bar__minus_01_dot_74)
        (pickupable CD_bar__plus_01_dot_37_bar__plus_00_dot_69_bar__minus_02_dot_17)
        (pickupable Pencil_bar__minus_00_dot_63_bar__plus_00_dot_67_bar__plus_00_dot_43)
        (pickupable CreditCard_bar__minus_00_dot_40_bar__plus_00_dot_37_bar__plus_00_dot_50)
        (pickupable AlarmClock_bar__plus_01_dot_49_bar__plus_00_dot_48_bar__minus_00_dot_76)
        (pickupable Pen_bar__plus_01_dot_55_bar__plus_00_dot_70_bar__minus_02_dot_03)
        (pickupable CD_bar__plus_01_dot_68_bar__plus_00_dot_05_bar__minus_00_dot_37)
        (isReceptacleObject Box_bar__minus_00_dot_02_bar__plus_00_dot_24_bar__minus_02_dot_09)
        (isReceptacleObject Bowl_bar__plus_00_dot_85_bar__plus_00_dot_70_bar__minus_02_dot_13)
        (openable Drawer_bar__minus_00_dot_56_bar__plus_00_dot_18_bar__plus_00_dot_49)
        
        (atLocation agent1 loc_bar_5_bar_1_bar_3_bar_30)
        
        (cleanable Bowl_bar__plus_00_dot_85_bar__plus_00_dot_70_bar__minus_02_dot_13)
        
        
        (coolable Bowl_bar__plus_00_dot_85_bar__plus_00_dot_70_bar__minus_02_dot_13)
        
        
        (toggleable DeskLamp_bar__minus_00_dot_44_bar__plus_00_dot_69_bar__minus_02_dot_27)
        
        
        
        
        (inReceptacle CreditCard_bar__minus_00_dot_45_bar__plus_00_dot_09_bar__plus_00_dot_50 Drawer_bar__minus_00_dot_56_bar__plus_00_dot_18_bar__plus_00_dot_49)
        (inReceptacle Pencil_bar__minus_00_dot_63_bar__plus_00_dot_67_bar__plus_00_dot_43 SideTable_bar__minus_00_dot_56_bar__plus_00_dot_45_bar__plus_00_dot_49)
        (inReceptacle Book_bar__minus_00_dot_41_bar__plus_00_dot_66_bar__plus_00_dot_56 SideTable_bar__minus_00_dot_56_bar__plus_00_dot_45_bar__plus_00_dot_49)
        (inReceptacle Pencil_bar__minus_00_dot_49_bar__plus_00_dot_67_bar__plus_00_dot_64 SideTable_bar__minus_00_dot_56_bar__plus_00_dot_45_bar__plus_00_dot_49)
        (inReceptacle CreditCard_bar__minus_00_dot_40_bar__plus_00_dot_37_bar__plus_00_dot_50 Drawer_bar__minus_00_dot_56_bar__plus_00_dot_45_bar__plus_00_dot_49)
        (inReceptacle CreditCard_bar__minus_00_dot_51_bar__plus_00_dot_37_bar__plus_00_dot_46 Drawer_bar__minus_00_dot_56_bar__plus_00_dot_45_bar__plus_00_dot_49)
        (inReceptacle KeyChain_bar__minus_00_dot_67_bar__plus_00_dot_37_bar__plus_00_dot_46 Drawer_bar__minus_00_dot_56_bar__plus_00_dot_45_bar__plus_00_dot_49)
        (inReceptacle Laptop_bar__minus_02_dot_05_bar__plus_00_dot_63_bar__minus_00_dot_47 Bed_bar__minus_01_dot_70_bar__plus_00_dot_04_bar__minus_00_dot_45)
        (inReceptacle Pillow_bar__minus_01_dot_52_bar__plus_00_dot_70_bar__minus_00_dot_47 Bed_bar__minus_01_dot_70_bar__plus_00_dot_04_bar__minus_00_dot_45)
        (inReceptacle CellPhone_bar__plus_00_dot_16_bar__plus_00_dot_69_bar__minus_02_dot_38 Desk_bar__plus_01_dot_49_bar__plus_00_dot_69_bar__minus_01_dot_84)
        (inReceptacle HousePlant_bar__minus_00_dot_87_bar__plus_00_dot_00_bar__minus_02_dot_29 Desk_bar__plus_01_dot_49_bar__plus_00_dot_69_bar__minus_01_dot_84)
        (inReceptacle Pen_bar__plus_01_dot_55_bar__plus_00_dot_70_bar__minus_02_dot_03 Desk_bar__plus_01_dot_49_bar__plus_00_dot_69_bar__minus_01_dot_84)
        (inReceptacle Pen_bar__plus_00_dot_15_bar__plus_00_dot_70_bar__minus_02_dot_01 Desk_bar__plus_01_dot_49_bar__plus_00_dot_69_bar__minus_01_dot_84)
        (inReceptacle Book_bar__plus_01_dot_75_bar__plus_00_dot_69_bar__minus_01_dot_74 Desk_bar__plus_01_dot_49_bar__plus_00_dot_69_bar__minus_01_dot_84)
        (inReceptacle Book_bar__minus_00_dot_29_bar__plus_00_dot_69_bar__minus_01_dot_98 Desk_bar__plus_01_dot_49_bar__plus_00_dot_69_bar__minus_01_dot_84)
        (inReceptacle Pencil_bar__plus_01_dot_03_bar__plus_00_dot_70_bar__minus_02_dot_27 Desk_bar__plus_01_dot_49_bar__plus_00_dot_69_bar__minus_01_dot_84)
        (inReceptacle DeskLamp_bar__minus_00_dot_44_bar__plus_00_dot_69_bar__minus_02_dot_27 Desk_bar__plus_01_dot_49_bar__plus_00_dot_69_bar__minus_01_dot_84)
        (inReceptacle CD_bar__plus_00_dot_27_bar__plus_00_dot_69_bar__minus_02_dot_18 Desk_bar__plus_01_dot_49_bar__plus_00_dot_69_bar__minus_01_dot_84)
        (inReceptacle Bowl_bar__plus_00_dot_85_bar__plus_00_dot_70_bar__minus_02_dot_13 Desk_bar__plus_01_dot_49_bar__plus_00_dot_69_bar__minus_01_dot_84)
        (inReceptacle CD_bar__plus_01_dot_37_bar__plus_00_dot_69_bar__minus_02_dot_17 Desk_bar__plus_01_dot_49_bar__plus_00_dot_69_bar__minus_01_dot_84)
        (inReceptacle AlarmClock_bar__plus_01_dot_49_bar__plus_00_dot_48_bar__minus_00_dot_76 Shelf_bar__plus_01_dot_49_bar__plus_00_dot_47_bar__minus_00_dot_82)
        (inReceptacle CD_bar__plus_01_dot_68_bar__plus_00_dot_05_bar__minus_00_dot_37 GarbageCan_bar__plus_01_dot_66_bar__plus_00_dot_00_bar__minus_00_dot_39)
        
        
        (receptacleAtLocation Bed_bar__minus_01_dot_70_bar__plus_00_dot_04_bar__minus_00_dot_45 loc_bar__minus_1_bar__minus_1_bar_3_bar_45)
        (receptacleAtLocation Desk_bar__plus_01_dot_49_bar__plus_00_dot_69_bar__minus_01_dot_84 loc_bar_0_bar__minus_5_bar_1_bar_30)
        (receptacleAtLocation Drawer_bar__minus_00_dot_56_bar__plus_00_dot_18_bar__plus_00_dot_49 loc_bar_0_bar__minus_2_bar_0_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_56_bar__plus_00_dot_45_bar__plus_00_dot_49 loc_bar__minus_2_bar_0_bar_0_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_01_dot_66_bar__plus_00_dot_00_bar__minus_00_dot_39 loc_bar_5_bar__minus_1_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_49_bar__plus_00_dot_47_bar__minus_00_dot_82 loc_bar_3_bar__minus_2_bar_1_bar_60)
        (receptacleAtLocation SideTable_bar__minus_00_dot_56_bar__plus_00_dot_45_bar__plus_00_dot_49 loc_bar_2_bar_1_bar_3_bar_45)
        (objectAtLocation CD_bar__plus_00_dot_27_bar__plus_00_dot_69_bar__minus_02_dot_18 loc_bar_0_bar__minus_5_bar_1_bar_30)
        (objectAtLocation CreditCard_bar__minus_00_dot_51_bar__plus_00_dot_37_bar__plus_00_dot_46 loc_bar__minus_2_bar_0_bar_0_bar_60)
        (objectAtLocation KeyChain_bar__minus_00_dot_67_bar__plus_00_dot_37_bar__plus_00_dot_46 loc_bar__minus_2_bar_0_bar_0_bar_60)
        (objectAtLocation Pencil_bar__minus_00_dot_63_bar__plus_00_dot_67_bar__plus_00_dot_43 loc_bar_2_bar_1_bar_3_bar_45)
        (objectAtLocation Pen_bar__plus_00_dot_15_bar__plus_00_dot_70_bar__minus_02_dot_01 loc_bar_0_bar__minus_5_bar_1_bar_30)
        (objectAtLocation Book_bar__minus_00_dot_41_bar__plus_00_dot_66_bar__plus_00_dot_56 loc_bar_2_bar_1_bar_3_bar_45)
        (objectAtLocation Book_bar__plus_01_dot_75_bar__plus_00_dot_69_bar__minus_01_dot_74 loc_bar_0_bar__minus_5_bar_1_bar_30)
        (objectAtLocation CreditCard_bar__minus_00_dot_45_bar__plus_00_dot_09_bar__plus_00_dot_50 loc_bar_0_bar__minus_2_bar_0_bar_60)
        (objectAtLocation Pencil_bar__minus_00_dot_49_bar__plus_00_dot_67_bar__plus_00_dot_64 loc_bar_2_bar_1_bar_3_bar_45)
        (objectAtLocation CD_bar__plus_01_dot_37_bar__plus_00_dot_69_bar__minus_02_dot_17 loc_bar_0_bar__minus_5_bar_1_bar_30)
        (objectAtLocation Box_bar__minus_00_dot_02_bar__plus_00_dot_24_bar__minus_02_dot_09 loc_bar_0_bar__minus_6_bar_2_bar_60)
        (objectAtLocation Book_bar__minus_00_dot_29_bar__plus_00_dot_69_bar__minus_01_dot_98 loc_bar_0_bar__minus_5_bar_1_bar_30)
        (objectAtLocation Chair_bar__plus_00_dot_96_bar__plus_00_dot_00_bar__minus_01_dot_75 loc_bar_4_bar__minus_4_bar_2_bar_60)
        (objectAtLocation BaseballBat_bar__plus_01_dot_80_bar__plus_00_dot_64_bar__plus_00_dot_43 loc_bar_5_bar_1_bar_1_bar_60)
        (objectAtLocation Pen_bar__plus_01_dot_55_bar__plus_00_dot_70_bar__minus_02_dot_03 loc_bar_0_bar__minus_5_bar_1_bar_30)
        (objectAtLocation Pencil_bar__plus_01_dot_03_bar__plus_00_dot_70_bar__minus_02_dot_27 loc_bar_0_bar__minus_5_bar_1_bar_30)
        (objectAtLocation DeskLamp_bar__minus_00_dot_44_bar__plus_00_dot_69_bar__minus_02_dot_27 loc_bar_0_bar__minus_5_bar_1_bar_30)
        (objectAtLocation LightSwitch_bar__plus_00_dot_63_bar__plus_01_dot_34_bar__plus_00_dot_73 loc_bar_3_bar_1_bar_0_bar_30)
        (objectAtLocation KeyChain_bar__plus_01_dot_53_bar__plus_00_dot_47_bar__minus_00_dot_86 loc_bar_4_bar__minus_3_bar_1_bar_60)
        (objectAtLocation Pillow_bar__minus_01_dot_52_bar__plus_00_dot_70_bar__minus_00_dot_47 loc_bar__minus_1_bar__minus_1_bar_3_bar_45)
        (objectAtLocation CreditCard_bar__minus_00_dot_40_bar__plus_00_dot_37_bar__plus_00_dot_50 loc_bar__minus_2_bar_0_bar_0_bar_60)
        (objectAtLocation CellPhone_bar__plus_00_dot_16_bar__plus_00_dot_69_bar__minus_02_dot_38 loc_bar_0_bar__minus_5_bar_1_bar_30)
        (objectAtLocation Laptop_bar__minus_02_dot_05_bar__plus_00_dot_63_bar__minus_00_dot_47 loc_bar__minus_1_bar__minus_1_bar_3_bar_45)
        (objectAtLocation Mirror_bar__plus_01_dot_30_bar__plus_01_dot_42_bar__plus_00_dot_77 loc_bar_5_bar_1_bar_0_bar_30)
        (objectAtLocation CD_bar__plus_01_dot_68_bar__plus_00_dot_05_bar__minus_00_dot_37 loc_bar_5_bar__minus_1_bar_1_bar_60)
        (objectAtLocation HousePlant_bar__minus_00_dot_87_bar__plus_00_dot_00_bar__minus_02_dot_29 loc_bar_0_bar__minus_5_bar_1_bar_30)
        (objectAtLocation Bowl_bar__plus_00_dot_85_bar__plus_00_dot_70_bar__minus_02_dot_13 loc_bar_0_bar__minus_5_bar_1_bar_30)
        (objectAtLocation AlarmClock_bar__plus_01_dot_49_bar__plus_00_dot_48_bar__minus_00_dot_76 loc_bar_3_bar__minus_2_bar_1_bar_60)
        (objectAtLocation Window_bar__plus_00_dot_82_bar__plus_01_dot_50_bar__minus_02_dot_55 loc_bar_1_bar__minus_6_bar_2_bar_15)
        (objectAtLocation Blinds_bar__plus_00_dot_80_bar__plus_02_dot_22_bar__minus_02_dot_48 loc_bar_1_bar__minus_6_bar_2_bar__minus_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PencilType)
                                    (receptacleType ?r DrawerType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PencilType)
                                            (receptacleType ?r DrawerType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            