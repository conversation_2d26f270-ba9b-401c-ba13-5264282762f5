
(define (problem plan_trial_T20190907_151137_619046)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__plus_01_dot_55_bar__plus_00_dot_55_bar__plus_02_dot_32 - object
        Blinds_bar__minus_00_dot_48_bar__plus_02_dot_79_bar__minus_03_dot_25 - object
        Blinds_bar__minus_01_dot_96_bar__plus_02_dot_79_bar__minus_03_dot_25 - object
        Blinds_bar__minus_03_dot_80_bar__plus_02_dot_51_bar__plus_00_dot_60 - object
        Blinds_bar__minus_03_dot_80_bar__plus_02_dot_51_bar__minus_00_dot_65 - object
        Book_bar__minus_00_dot_54_bar__plus_00_dot_08_bar__minus_02_dot_93 - object
        Book_bar__minus_02_dot_63_bar__plus_00_dot_08_bar__minus_02_dot_90 - object
        CD_bar__minus_02_dot_87_bar__plus_00_dot_08_bar__minus_03_dot_06 - object
        CellPhone_bar__minus_00_dot_37_bar__plus_00_dot_09_bar__minus_02_dot_83 - object
        Chair_bar__minus_02_dot_07_bar__plus_00_dot_04_bar__minus_02_dot_69 - object
        CreditCard_bar__minus_03_dot_26_bar__plus_00_dot_19_bar__plus_03_dot_51 - object
        DeskLamp_bar__plus_01_dot_87_bar__plus_00_dot_55_bar__plus_02_dot_42 - object
        HousePlant_bar__minus_00_dot_20_bar__plus_01_dot_10_bar__minus_03_dot_08 - object
        KeyChain_bar__plus_01_dot_61_bar__plus_00_dot_55_bar__plus_02_dot_44 - object
        Laptop_bar__plus_00_dot_10_bar__plus_00_dot_83_bar__plus_00_dot_98 - object
        Laptop_bar__plus_00_dot_67_bar__plus_00_dot_83_bar__plus_00_dot_98 - object
        LaundryHamperLid_bar__plus_01_dot_83_bar__plus_00_dot_79_bar__plus_03_dot_69 - object
        LightSwitch_bar__minus_00_dot_16_bar__plus_01_dot_28_bar__plus_04_dot_00 - object
        Mirror_bar__minus_01_dot_60_bar__plus_01_dot_45_bar__plus_04_dot_00 - object
        Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91 - object
        Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74 - object
        Pencil_bar__minus_00_dot_19_bar__plus_00_dot_09_bar__minus_02_dot_93 - object
        Pencil_bar__minus_00_dot_28_bar__plus_00_dot_59_bar__minus_02_dot_83 - object
        Pencil_bar__minus_03_dot_40_bar__plus_00_dot_38_bar__minus_02_dot_84 - object
        Pen_bar__minus_01_dot_07_bar__plus_00_dot_59_bar__minus_02_dot_88 - object
        Pillow_bar__plus_01_dot_24_bar__plus_00_dot_94_bar__plus_00_dot_98 - object
        Pillow_bar__plus_01_dot_82_bar__plus_00_dot_87_bar__plus_01_dot_15 - object
        RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_56_bar__plus_02_dot_39 - object
        RemoteControl_bar__minus_01_dot_01_bar__plus_00_dot_19_bar__plus_03_dot_48 - object
        RemoteControl_bar__minus_03_dot_43_bar__plus_00_dot_19_bar__plus_03_dot_31 - object
        TeddyBear_bar__plus_01_dot_87_bar__plus_00_dot_04_bar__minus_00_dot_56 - object
        Television_bar__minus_03_dot_88_bar__plus_01_dot_77_bar__plus_02_dot_12 - object
        Window_bar__minus_00_dot_47_bar__plus_02_dot_15_bar__minus_03_dot_30 - object
        Window_bar__minus_01_dot_95_bar__plus_02_dot_15_bar__minus_03_dot_30 - object
        Window_bar__minus_03_dot_89_bar__plus_01_dot_75_bar__plus_00_dot_58 - object
        Window_bar__minus_03_dot_89_bar__plus_01_dot_75_bar__minus_00_dot_68 - object
        ArmChair_bar__minus_00_dot_94_bar__plus_00_dot_00_bar__plus_03_dot_61 - receptacle
        ArmChair_bar__minus_02_dot_17_bar__plus_00_dot_00_bar__plus_03_dot_63 - receptacle
        ArmChair_bar__minus_03_dot_37_bar__plus_00_dot_00_bar__plus_03_dot_38 - receptacle
        Bed_bar__plus_00_dot_99_bar__plus_00_dot_03_bar__plus_00_dot_67 - receptacle
        Cabinet_bar__plus_02_dot_06_bar__plus_01_dot_50_bar__minus_02_dot_64 - receptacle
        Cabinet_bar__minus_03_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64 - receptacle
        Desk_bar__minus_01_dot_27_bar__plus_01_dot_17_bar__minus_02_dot_99 - receptacle
        Drawer_bar__minus_00_dot_37_bar__plus_00_dot_29_bar__minus_02_dot_85 - receptacle
        Drawer_bar__minus_00_dot_37_bar__plus_00_dot_79_bar__minus_02_dot_85 - receptacle
        Drawer_bar__minus_01_dot_16_bar__plus_00_dot_29_bar__minus_02_dot_85 - receptacle
        Drawer_bar__minus_01_dot_16_bar__plus_00_dot_79_bar__minus_02_dot_85 - receptacle
        GarbageCan_bar__minus_03_dot_34_bar__plus_00_dot_04_bar__minus_02_dot_80 - receptacle
        LaundryHamper_bar__plus_01_dot_83_bar__plus_00_dot_04_bar__plus_03_dot_69 - receptacle
        Safe_bar__plus_01_dot_72_bar__plus_00_dot_05_bar__minus_03_dot_05 - receptacle
        SideTable_bar__plus_01_dot_78_bar__plus_00_dot_02_bar__plus_02_dot_38 - receptacle
        loc_bar__minus_13_bar__minus_9_bar_2_bar_60 - location
        loc_bar__minus_11_bar__minus_8_bar_2_bar_15 - location
        loc_bar__minus_3_bar_10_bar_0_bar_60 - location
        loc_bar__minus_13_bar_2_bar_3_bar__minus_30 - location
        loc_bar__minus_2_bar__minus_9_bar_2_bar__minus_30 - location
        loc_bar__minus_13_bar_11_bar_0_bar_60 - location
        loc_bar__minus_13_bar__minus_3_bar_3_bar__minus_30 - location
        loc_bar__minus_2_bar_3_bar_1_bar_45 - location
        loc_bar__minus_5_bar__minus_9_bar_1_bar_45 - location
        loc_bar_7_bar_12_bar_0_bar_60 - location
        loc_bar__minus_8_bar_10_bar_0_bar_60 - location
        loc_bar_4_bar__minus_8_bar_1_bar_15 - location
        loc_bar_6_bar_13_bar_2_bar_60 - location
        loc_bar__minus_13_bar_2_bar_3_bar_0 - location
        loc_bar__minus_9_bar_12_bar_3_bar_60 - location
        loc_bar__minus_7_bar__minus_9_bar_2_bar_60 - location
        loc_bar__minus_13_bar_8_bar_3_bar_0 - location
        loc_bar__minus_5_bar__minus_9_bar_2_bar_60 - location
        loc_bar__minus_6_bar_13_bar_0_bar_15 - location
        loc_bar__minus_1_bar_14_bar_0_bar_45 - location
        loc_bar__minus_4_bar__minus_9_bar_2_bar_30 - location
        loc_bar_7_bar_12_bar_0_bar_45 - location
        loc_bar__minus_4_bar_12_bar_0_bar_60 - location
        loc_bar__minus_7_bar__minus_9_bar_2_bar__minus_30 - location
        loc_bar__minus_13_bar__minus_3_bar_3_bar_0 - location
        loc_bar_7_bar__minus_8_bar_2_bar_60 - location
        loc_bar_5_bar__minus_8_bar_2_bar_60 - location
        loc_bar_7_bar__minus_4_bar_0_bar_60 - location
        loc_bar__minus_6_bar__minus_9_bar_1_bar_45 - location
        loc_bar__minus_11_bar__minus_8_bar_2_bar_60 - location
        loc_bar__minus_5_bar_11_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType LaundryHamper_bar__plus_01_dot_83_bar__plus_00_dot_04_bar__plus_03_dot_69 LaundryHamperType)
        (receptacleType Drawer_bar__minus_00_dot_37_bar__plus_00_dot_29_bar__minus_02_dot_85 DrawerType)
        (receptacleType Safe_bar__plus_01_dot_72_bar__plus_00_dot_05_bar__minus_03_dot_05 SafeType)
        (receptacleType Bed_bar__plus_00_dot_99_bar__plus_00_dot_03_bar__plus_00_dot_67 BedType)
        (receptacleType Desk_bar__minus_01_dot_27_bar__plus_01_dot_17_bar__minus_02_dot_99 DeskType)
        (receptacleType Drawer_bar__minus_01_dot_16_bar__plus_00_dot_79_bar__minus_02_dot_85 DrawerType)
        (receptacleType Drawer_bar__minus_01_dot_16_bar__plus_00_dot_29_bar__minus_02_dot_85 DrawerType)
        (receptacleType GarbageCan_bar__minus_03_dot_34_bar__plus_00_dot_04_bar__minus_02_dot_80 GarbageCanType)
        (receptacleType Drawer_bar__minus_00_dot_37_bar__plus_00_dot_79_bar__minus_02_dot_85 DrawerType)
        (receptacleType ArmChair_bar__minus_03_dot_37_bar__plus_00_dot_00_bar__plus_03_dot_38 ArmChairType)
        (receptacleType ArmChair_bar__minus_02_dot_17_bar__plus_00_dot_00_bar__plus_03_dot_63 ArmChairType)
        (receptacleType SideTable_bar__plus_01_dot_78_bar__plus_00_dot_02_bar__plus_02_dot_38 SideTableType)
        (receptacleType ArmChair_bar__minus_00_dot_94_bar__plus_00_dot_00_bar__plus_03_dot_61 ArmChairType)
        (receptacleType Cabinet_bar__plus_02_dot_06_bar__plus_01_dot_50_bar__minus_02_dot_64 CabinetType)
        (receptacleType Cabinet_bar__minus_03_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64 CabinetType)
        (objectType LightSwitch_bar__minus_00_dot_16_bar__plus_01_dot_28_bar__plus_04_dot_00 LightSwitchType)
        (objectType Window_bar__minus_01_dot_95_bar__plus_02_dot_15_bar__minus_03_dot_30 WindowType)
        (objectType Pillow_bar__plus_01_dot_82_bar__plus_00_dot_87_bar__plus_01_dot_15 PillowType)
        (objectType Window_bar__minus_00_dot_47_bar__plus_02_dot_15_bar__minus_03_dot_30 WindowType)
        (objectType Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91 MugType)
        (objectType Chair_bar__minus_02_dot_07_bar__plus_00_dot_04_bar__minus_02_dot_69 ChairType)
        (objectType TeddyBear_bar__plus_01_dot_87_bar__plus_00_dot_04_bar__minus_00_dot_56 TeddyBearType)
        (objectType Pen_bar__minus_01_dot_07_bar__plus_00_dot_59_bar__minus_02_dot_88 PenType)
        (objectType CD_bar__minus_02_dot_87_bar__plus_00_dot_08_bar__minus_03_dot_06 CDType)
        (objectType Pencil_bar__minus_03_dot_40_bar__plus_00_dot_38_bar__minus_02_dot_84 PencilType)
        (objectType Pencil_bar__minus_00_dot_19_bar__plus_00_dot_09_bar__minus_02_dot_93 PencilType)
        (objectType Television_bar__minus_03_dot_88_bar__plus_01_dot_77_bar__plus_02_dot_12 TelevisionType)
        (objectType Laptop_bar__plus_00_dot_67_bar__plus_00_dot_83_bar__plus_00_dot_98 LaptopType)
        (objectType Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74 MugType)
        (objectType AlarmClock_bar__plus_01_dot_55_bar__plus_00_dot_55_bar__plus_02_dot_32 AlarmClockType)
        (objectType Blinds_bar__minus_03_dot_80_bar__plus_02_dot_51_bar__minus_00_dot_65 BlindsType)
        (objectType Pencil_bar__minus_00_dot_28_bar__plus_00_dot_59_bar__minus_02_dot_83 PencilType)
        (objectType Book_bar__minus_02_dot_63_bar__plus_00_dot_08_bar__minus_02_dot_90 BookType)
        (objectType CreditCard_bar__minus_03_dot_26_bar__plus_00_dot_19_bar__plus_03_dot_51 CreditCardType)
        (objectType Blinds_bar__minus_03_dot_80_bar__plus_02_dot_51_bar__plus_00_dot_60 BlindsType)
        (objectType Mirror_bar__minus_01_dot_60_bar__plus_01_dot_45_bar__plus_04_dot_00 MirrorType)
        (objectType Laptop_bar__plus_00_dot_10_bar__plus_00_dot_83_bar__plus_00_dot_98 LaptopType)
        (objectType KeyChain_bar__plus_01_dot_61_bar__plus_00_dot_55_bar__plus_02_dot_44 KeyChainType)
        (objectType Window_bar__minus_03_dot_89_bar__plus_01_dot_75_bar__plus_00_dot_58 WindowType)
        (objectType RemoteControl_bar__minus_03_dot_43_bar__plus_00_dot_19_bar__plus_03_dot_31 RemoteControlType)
        (objectType RemoteControl_bar__minus_01_dot_01_bar__plus_00_dot_19_bar__plus_03_dot_48 RemoteControlType)
        (objectType RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_56_bar__plus_02_dot_39 RemoteControlType)
        (objectType Book_bar__minus_00_dot_54_bar__plus_00_dot_08_bar__minus_02_dot_93 BookType)
        (objectType HousePlant_bar__minus_00_dot_20_bar__plus_01_dot_10_bar__minus_03_dot_08 HousePlantType)
        (objectType DeskLamp_bar__plus_01_dot_87_bar__plus_00_dot_55_bar__plus_02_dot_42 DeskLampType)
        (objectType Pillow_bar__plus_01_dot_24_bar__plus_00_dot_94_bar__plus_00_dot_98 PillowType)
        (objectType LaundryHamperLid_bar__plus_01_dot_83_bar__plus_00_dot_79_bar__plus_03_dot_69 LaundryHamperLidType)
        (objectType Window_bar__minus_03_dot_89_bar__plus_01_dot_75_bar__minus_00_dot_68 WindowType)
        (objectType Blinds_bar__minus_01_dot_96_bar__plus_02_dot_79_bar__minus_03_dot_25 BlindsType)
        (objectType Blinds_bar__minus_00_dot_48_bar__plus_02_dot_79_bar__minus_03_dot_25 BlindsType)
        (objectType CellPhone_bar__minus_00_dot_37_bar__plus_00_dot_09_bar__minus_02_dot_83 CellPhoneType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain SafeType CellPhoneType)
        (canContain SafeType KeyChainType)
        (canContain SafeType CreditCardType)
        (canContain SafeType CDType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain DeskType PenType)
        (canContain DeskType BookType)
        (canContain DeskType CDType)
        (canContain DeskType MugType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType CreditCardType)
        (canContain DeskType LaptopType)
        (canContain DeskType PencilType)
        (canContain DeskType RemoteControlType)
        (canContain DeskType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType CellPhoneType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType CellPhoneType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType CDType)
        (canContain SideTableType MugType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType AlarmClockType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType CellPhoneType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain CabinetType BookType)
        (canContain CabinetType CDType)
        (canContain CabinetType MugType)
        (canContain CabinetType BookType)
        (canContain CabinetType CDType)
        (canContain CabinetType MugType)
        (pickupable Pillow_bar__plus_01_dot_82_bar__plus_00_dot_87_bar__plus_01_dot_15)
        (pickupable Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91)
        (pickupable TeddyBear_bar__plus_01_dot_87_bar__plus_00_dot_04_bar__minus_00_dot_56)
        (pickupable Pen_bar__minus_01_dot_07_bar__plus_00_dot_59_bar__minus_02_dot_88)
        (pickupable CD_bar__minus_02_dot_87_bar__plus_00_dot_08_bar__minus_03_dot_06)
        (pickupable Pencil_bar__minus_03_dot_40_bar__plus_00_dot_38_bar__minus_02_dot_84)
        (pickupable Pencil_bar__minus_00_dot_19_bar__plus_00_dot_09_bar__minus_02_dot_93)
        (pickupable Laptop_bar__plus_00_dot_67_bar__plus_00_dot_83_bar__plus_00_dot_98)
        (pickupable Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74)
        (pickupable AlarmClock_bar__plus_01_dot_55_bar__plus_00_dot_55_bar__plus_02_dot_32)
        (pickupable Pencil_bar__minus_00_dot_28_bar__plus_00_dot_59_bar__minus_02_dot_83)
        (pickupable Book_bar__minus_02_dot_63_bar__plus_00_dot_08_bar__minus_02_dot_90)
        (pickupable CreditCard_bar__minus_03_dot_26_bar__plus_00_dot_19_bar__plus_03_dot_51)
        (pickupable Laptop_bar__plus_00_dot_10_bar__plus_00_dot_83_bar__plus_00_dot_98)
        (pickupable KeyChain_bar__plus_01_dot_61_bar__plus_00_dot_55_bar__plus_02_dot_44)
        (pickupable RemoteControl_bar__minus_03_dot_43_bar__plus_00_dot_19_bar__plus_03_dot_31)
        (pickupable RemoteControl_bar__minus_01_dot_01_bar__plus_00_dot_19_bar__plus_03_dot_48)
        (pickupable RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_56_bar__plus_02_dot_39)
        (pickupable Book_bar__minus_00_dot_54_bar__plus_00_dot_08_bar__minus_02_dot_93)
        (pickupable Pillow_bar__plus_01_dot_24_bar__plus_00_dot_94_bar__plus_00_dot_98)
        (pickupable CellPhone_bar__minus_00_dot_37_bar__plus_00_dot_09_bar__minus_02_dot_83)
        (isReceptacleObject Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91)
        (isReceptacleObject Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74)
        (openable Drawer_bar__minus_00_dot_37_bar__plus_00_dot_29_bar__minus_02_dot_85)
        (openable Drawer_bar__minus_01_dot_16_bar__plus_00_dot_79_bar__minus_02_dot_85)
        (openable Drawer_bar__minus_00_dot_37_bar__plus_00_dot_79_bar__minus_02_dot_85)
        (openable Cabinet_bar__plus_02_dot_06_bar__plus_01_dot_50_bar__minus_02_dot_64)
        (openable Cabinet_bar__minus_03_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64)
        
        (atLocation agent1 loc_bar__minus_5_bar_11_bar_3_bar_30)
        
        (cleanable Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91)
        (cleanable Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74)
        
        (heatable Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91)
        (heatable Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74)
        (coolable Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91)
        (coolable Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74)
        
        
        (toggleable DeskLamp_bar__plus_01_dot_87_bar__plus_00_dot_55_bar__plus_02_dot_42)
        
        
        
        
        (inReceptacle Laptop_bar__plus_00_dot_67_bar__plus_00_dot_83_bar__plus_00_dot_98 Bed_bar__plus_00_dot_99_bar__plus_00_dot_03_bar__plus_00_dot_67)
        (inReceptacle Pillow_bar__plus_01_dot_24_bar__plus_00_dot_94_bar__plus_00_dot_98 Bed_bar__plus_00_dot_99_bar__plus_00_dot_03_bar__plus_00_dot_67)
        (inReceptacle Laptop_bar__plus_00_dot_10_bar__plus_00_dot_83_bar__plus_00_dot_98 Bed_bar__plus_00_dot_99_bar__plus_00_dot_03_bar__plus_00_dot_67)
        (inReceptacle Pillow_bar__plus_01_dot_82_bar__plus_00_dot_87_bar__plus_01_dot_15 Bed_bar__plus_00_dot_99_bar__plus_00_dot_03_bar__plus_00_dot_67)
        (inReceptacle CreditCard_bar__minus_03_dot_26_bar__plus_00_dot_19_bar__plus_03_dot_51 ArmChair_bar__minus_03_dot_37_bar__plus_00_dot_00_bar__plus_03_dot_38)
        (inReceptacle Book_bar__minus_02_dot_63_bar__plus_00_dot_08_bar__minus_02_dot_90 Cabinet_bar__minus_03_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64)
        (inReceptacle Pencil_bar__minus_00_dot_28_bar__plus_00_dot_59_bar__minus_02_dot_83 Drawer_bar__minus_00_dot_37_bar__plus_00_dot_79_bar__minus_02_dot_85)
        (inReceptacle DeskLamp_bar__plus_01_dot_87_bar__plus_00_dot_55_bar__plus_02_dot_42 SideTable_bar__plus_01_dot_78_bar__plus_00_dot_02_bar__plus_02_dot_38)
        (inReceptacle RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_56_bar__plus_02_dot_39 SideTable_bar__plus_01_dot_78_bar__plus_00_dot_02_bar__plus_02_dot_38)
        (inReceptacle KeyChain_bar__plus_01_dot_61_bar__plus_00_dot_55_bar__plus_02_dot_44 SideTable_bar__plus_01_dot_78_bar__plus_00_dot_02_bar__plus_02_dot_38)
        (inReceptacle AlarmClock_bar__plus_01_dot_55_bar__plus_00_dot_55_bar__plus_02_dot_32 SideTable_bar__plus_01_dot_78_bar__plus_00_dot_02_bar__plus_02_dot_38)
        (inReceptacle Pen_bar__minus_01_dot_07_bar__plus_00_dot_59_bar__minus_02_dot_88 Drawer_bar__minus_01_dot_16_bar__plus_00_dot_79_bar__minus_02_dot_85)
        (inReceptacle HousePlant_bar__minus_00_dot_20_bar__plus_01_dot_10_bar__minus_03_dot_08 Cabinet_bar__plus_02_dot_06_bar__plus_01_dot_50_bar__minus_02_dot_64)
        (inReceptacle Pencil_bar__minus_00_dot_19_bar__plus_00_dot_09_bar__minus_02_dot_93 Drawer_bar__minus_00_dot_37_bar__plus_00_dot_29_bar__minus_02_dot_85)
        (inReceptacle CellPhone_bar__minus_00_dot_37_bar__plus_00_dot_09_bar__minus_02_dot_83 Drawer_bar__minus_00_dot_37_bar__plus_00_dot_29_bar__minus_02_dot_85)
        (inReceptacle Book_bar__minus_00_dot_54_bar__plus_00_dot_08_bar__minus_02_dot_93 Drawer_bar__minus_00_dot_37_bar__plus_00_dot_29_bar__minus_02_dot_85)
        (inReceptacle HousePlant_bar__minus_00_dot_20_bar__plus_01_dot_10_bar__minus_03_dot_08 Desk_bar__minus_01_dot_27_bar__plus_01_dot_17_bar__minus_02_dot_99)
        (inReceptacle Pencil_bar__minus_03_dot_40_bar__plus_00_dot_38_bar__minus_02_dot_84 GarbageCan_bar__minus_03_dot_34_bar__plus_00_dot_04_bar__minus_02_dot_80)
        
        
        (receptacleAtLocation ArmChair_bar__minus_00_dot_94_bar__plus_00_dot_00_bar__plus_03_dot_61 loc_bar__minus_3_bar_10_bar_0_bar_60)
        (receptacleAtLocation ArmChair_bar__minus_02_dot_17_bar__plus_00_dot_00_bar__plus_03_dot_63 loc_bar__minus_8_bar_10_bar_0_bar_60)
        (receptacleAtLocation ArmChair_bar__minus_03_dot_37_bar__plus_00_dot_00_bar__plus_03_dot_38 loc_bar__minus_9_bar_12_bar_3_bar_60)
        (receptacleAtLocation Bed_bar__plus_00_dot_99_bar__plus_00_dot_03_bar__plus_00_dot_67 loc_bar__minus_2_bar_3_bar_1_bar_45)
        (receptacleAtLocation Cabinet_bar__plus_02_dot_06_bar__plus_01_dot_50_bar__minus_02_dot_64 loc_bar_4_bar__minus_8_bar_1_bar_15)
        (receptacleAtLocation Cabinet_bar__minus_03_dot_00_bar__plus_01_dot_50_bar__minus_02_dot_64 loc_bar__minus_11_bar__minus_8_bar_2_bar_15)
        (receptacleAtLocation Desk_bar__minus_01_dot_27_bar__plus_01_dot_17_bar__minus_02_dot_99 loc_bar__minus_4_bar__minus_9_bar_2_bar_30)
        (receptacleAtLocation Drawer_bar__minus_00_dot_37_bar__plus_00_dot_29_bar__minus_02_dot_85 loc_bar__minus_6_bar__minus_9_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_37_bar__plus_00_dot_79_bar__minus_02_dot_85 loc_bar__minus_5_bar__minus_9_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_16_bar__plus_00_dot_29_bar__minus_02_dot_85 loc_bar__minus_5_bar__minus_9_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_16_bar__plus_00_dot_79_bar__minus_02_dot_85 loc_bar__minus_7_bar__minus_9_bar_2_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_03_dot_34_bar__plus_00_dot_04_bar__minus_02_dot_80 loc_bar__minus_13_bar__minus_9_bar_2_bar_60)
        (receptacleAtLocation LaundryHamper_bar__plus_01_dot_83_bar__plus_00_dot_04_bar__plus_03_dot_69 loc_bar_7_bar_12_bar_0_bar_60)
        (receptacleAtLocation Safe_bar__plus_01_dot_72_bar__plus_00_dot_05_bar__minus_03_dot_05 loc_bar_7_bar__minus_8_bar_2_bar_60)
        (receptacleAtLocation SideTable_bar__plus_01_dot_78_bar__plus_00_dot_02_bar__plus_02_dot_38 loc_bar_6_bar_13_bar_2_bar_60)
        (objectAtLocation Mug_bar__minus_02_dot_93_bar__plus_00_dot_08_bar__minus_02_dot_74 loc_bar__minus_13_bar__minus_9_bar_2_bar_60)
        (objectAtLocation RemoteControl_bar__minus_03_dot_43_bar__plus_00_dot_19_bar__plus_03_dot_31 loc_bar__minus_13_bar_11_bar_0_bar_60)
        (objectAtLocation Laptop_bar__plus_00_dot_10_bar__plus_00_dot_83_bar__plus_00_dot_98 loc_bar__minus_2_bar_3_bar_1_bar_45)
        (objectAtLocation Pencil_bar__minus_03_dot_40_bar__plus_00_dot_38_bar__minus_02_dot_84 loc_bar__minus_13_bar__minus_9_bar_2_bar_60)
        (objectAtLocation Book_bar__minus_00_dot_54_bar__plus_00_dot_08_bar__minus_02_dot_93 loc_bar__minus_6_bar__minus_9_bar_1_bar_45)
        (objectAtLocation RemoteControl_bar__minus_01_dot_01_bar__plus_00_dot_19_bar__plus_03_dot_48 loc_bar__minus_4_bar_12_bar_0_bar_60)
        (objectAtLocation Pencil_bar__minus_00_dot_28_bar__plus_00_dot_59_bar__minus_02_dot_83 loc_bar__minus_5_bar__minus_9_bar_1_bar_45)
        (objectAtLocation Book_bar__minus_02_dot_63_bar__plus_00_dot_08_bar__minus_02_dot_90 loc_bar__minus_11_bar__minus_8_bar_2_bar_15)
        (objectAtLocation LaundryHamperLid_bar__plus_01_dot_83_bar__plus_00_dot_79_bar__plus_03_dot_69 loc_bar_7_bar_12_bar_0_bar_45)
        (objectAtLocation AlarmClock_bar__plus_01_dot_55_bar__plus_00_dot_55_bar__plus_02_dot_32 loc_bar_6_bar_13_bar_2_bar_60)
        (objectAtLocation CellPhone_bar__minus_00_dot_37_bar__plus_00_dot_09_bar__minus_02_dot_83 loc_bar__minus_6_bar__minus_9_bar_1_bar_45)
        (objectAtLocation LightSwitch_bar__minus_00_dot_16_bar__plus_01_dot_28_bar__plus_04_dot_00 loc_bar__minus_1_bar_14_bar_0_bar_45)
        (objectAtLocation Pillow_bar__plus_01_dot_24_bar__plus_00_dot_94_bar__plus_00_dot_98 loc_bar__minus_2_bar_3_bar_1_bar_45)
        (objectAtLocation Pillow_bar__plus_01_dot_82_bar__plus_00_dot_87_bar__plus_01_dot_15 loc_bar__minus_2_bar_3_bar_1_bar_45)
        (objectAtLocation TeddyBear_bar__plus_01_dot_87_bar__plus_00_dot_04_bar__minus_00_dot_56 loc_bar_7_bar__minus_4_bar_0_bar_60)
        (objectAtLocation CreditCard_bar__minus_03_dot_26_bar__plus_00_dot_19_bar__plus_03_dot_51 loc_bar__minus_9_bar_12_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__plus_01_dot_61_bar__plus_00_dot_55_bar__plus_02_dot_44 loc_bar_6_bar_13_bar_2_bar_60)
        (objectAtLocation Pencil_bar__minus_00_dot_19_bar__plus_00_dot_09_bar__minus_02_dot_93 loc_bar__minus_6_bar__minus_9_bar_1_bar_45)
        (objectAtLocation Laptop_bar__plus_00_dot_67_bar__plus_00_dot_83_bar__plus_00_dot_98 loc_bar__minus_2_bar_3_bar_1_bar_45)
        (objectAtLocation HousePlant_bar__minus_00_dot_20_bar__plus_01_dot_10_bar__minus_03_dot_08 loc_bar__minus_4_bar__minus_9_bar_2_bar_30)
        (objectAtLocation DeskLamp_bar__plus_01_dot_87_bar__plus_00_dot_55_bar__plus_02_dot_42 loc_bar_6_bar_13_bar_2_bar_60)
        (objectAtLocation RemoteControl_bar__plus_01_dot_68_bar__plus_00_dot_56_bar__plus_02_dot_39 loc_bar_6_bar_13_bar_2_bar_60)
        (objectAtLocation Mirror_bar__minus_01_dot_60_bar__plus_01_dot_45_bar__plus_04_dot_00 loc_bar__minus_6_bar_13_bar_0_bar_15)
        (objectAtLocation Television_bar__minus_03_dot_88_bar__plus_01_dot_77_bar__plus_02_dot_12 loc_bar__minus_13_bar_8_bar_3_bar_0)
        (objectAtLocation CD_bar__minus_02_dot_87_bar__plus_00_dot_08_bar__minus_03_dot_06 loc_bar__minus_11_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Pen_bar__minus_01_dot_07_bar__plus_00_dot_59_bar__minus_02_dot_88 loc_bar__minus_7_bar__minus_9_bar_2_bar_60)
        (objectAtLocation Window_bar__minus_01_dot_95_bar__plus_02_dot_15_bar__minus_03_dot_30 loc_bar__minus_7_bar__minus_9_bar_2_bar__minus_30)
        (objectAtLocation Window_bar__minus_00_dot_47_bar__plus_02_dot_15_bar__minus_03_dot_30 loc_bar__minus_2_bar__minus_9_bar_2_bar__minus_30)
        (objectAtLocation Window_bar__minus_03_dot_89_bar__plus_01_dot_75_bar__plus_00_dot_58 loc_bar__minus_13_bar_2_bar_3_bar_0)
        (objectAtLocation Window_bar__minus_03_dot_89_bar__plus_01_dot_75_bar__minus_00_dot_68 loc_bar__minus_13_bar__minus_3_bar_3_bar_0)
        (objectAtLocation Chair_bar__minus_02_dot_07_bar__plus_00_dot_04_bar__minus_02_dot_69 loc_bar__minus_7_bar__minus_9_bar_2_bar_60)
        (objectAtLocation Mug_bar__plus_01_dot_32_bar__plus_00_dot_08_bar__minus_02_dot_91 loc_bar_5_bar__minus_8_bar_2_bar_60)
        (objectAtLocation Blinds_bar__minus_00_dot_48_bar__plus_02_dot_79_bar__minus_03_dot_25 loc_bar__minus_2_bar__minus_9_bar_2_bar__minus_30)
        (objectAtLocation Blinds_bar__minus_01_dot_96_bar__plus_02_dot_79_bar__minus_03_dot_25 loc_bar__minus_7_bar__minus_9_bar_2_bar__minus_30)
        (objectAtLocation Blinds_bar__minus_03_dot_80_bar__plus_02_dot_51_bar__plus_00_dot_60 loc_bar__minus_13_bar_2_bar_3_bar__minus_30)
        (objectAtLocation Blinds_bar__minus_03_dot_80_bar__plus_02_dot_51_bar__minus_00_dot_65 loc_bar__minus_13_bar__minus_3_bar_3_bar__minus_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 LaptopType)
                                    (receptacleType ?r DeskType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 LaptopType)
                                            (receptacleType ?r DeskType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            