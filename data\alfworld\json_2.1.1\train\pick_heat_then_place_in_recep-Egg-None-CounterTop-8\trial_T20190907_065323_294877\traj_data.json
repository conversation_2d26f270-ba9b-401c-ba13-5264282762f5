{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 42}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-4|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-1.909022332, -1.909022332, -6.81787252, -6.81787252, 3.76251626, 3.76251626]], "coordinateReceptacleObjectId": ["CounterTop", [4.66, 4.66, -2.6, -2.6, 3.7872, 3.7872]], "forceVisible": true, "objectId": "Egg|-00.48|+00.94|-01.70"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|2|0|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|2|-4|1|30"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-1.909022332, -1.909022332, -6.81787252, -6.81787252, 3.76251626, 3.76251626]], "coordinateReceptacleObjectId": ["CounterTop", [6.0, 6.0, -2.62, -2.62, 4.7908, 4.7908]], "forceVisible": true, "objectId": "Egg|-00.48|+00.94|-01.70", "receptacleObjectId": "CounterTop|+01.50|+01.20|-00.66"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.48|+00.94|-01.70"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [137, 142, 149, 157], "mask": [[42441, 5], [42740, 8], [43039, 10], [43338, 11], [43637, 13], [43937, 13], [44237, 13], [44537, 13], [44837, 13], [45137, 13], [45437, 13], [45738, 12], [46038, 11], [46339, 9], [46640, 7], [46941, 5]], "point": [143, 148]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.48|+00.94|-01.70", "placeStationary": true, "receptacleObjectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 120], [16034, 116], [16200, 114], [16338, 112], [16500, 112], [16639, 110], [16800, 112], [16940, 109], [17100, 112], [17240, 109], [17400, 112], [17540, 109], [17700, 111], [17840, 109], [18000, 111], [18141, 107], [18300, 111], [18441, 107], [18600, 111], [18741, 107], [18900, 111], [19041, 107], [19200, 111], [19341, 107], [19500, 111], [19641, 107], [19800, 111], [19941, 106], [20100, 111], [20241, 106], [20400, 111], [20541, 106], [20700, 111], [20841, 106], [21000, 112], [21141, 106], [21300, 112], [21441, 105], [21600, 112], [21740, 106], [21900, 113], [22040, 106], [22200, 113], [22340, 106], [22500, 113], [22640, 106], [22800, 114], [22939, 107], [23100, 114], [23239, 106], [23400, 115], [23538, 107], [23700, 116], [23838, 107], [24000, 116], [24137, 108], [24300, 117], [24437, 108], [24600, 117], [24737, 107], [24900, 117], [25037, 107], [25200, 117], [25337, 107], [25500, 118], [25637, 107], [25800, 118], [25936, 108], [26100, 118], [26236, 108], [26400, 118], [26536, 107], [26700, 118], [26837, 106], [27000, 118], [27137, 106], [27300, 118], [27437, 106], [27600, 118], [27737, 106], [27900, 117], [28037, 105], [28200, 117], [28338, 104], [28500, 117], [28638, 104], [28800, 117], [28938, 104], [29100, 117], [29238, 104], [29400, 118], [29537, 105], [29700, 119], [29835, 106], [30000, 123], [30131, 110], [30300, 241], [30600, 241], [30900, 241], [31200, 240], [31500, 240], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 120], [16034, 116], [16200, 114], [16338, 112], [16500, 112], [16639, 110], [16800, 112], [16940, 109], [17100, 112], [17240, 109], [17400, 112], [17540, 109], [17700, 111], [17840, 109], [18000, 111], [18141, 107], [18300, 111], [18441, 107], [18600, 111], [18741, 107], [18900, 111], [19041, 107], [19200, 111], [19341, 107], [19500, 111], [19641, 107], [19800, 111], [19941, 106], [20100, 111], [20241, 106], [20400, 111], [20541, 106], [20700, 111], [20841, 106], [21000, 112], [21141, 106], [21300, 112], [21441, 105], [21600, 112], [21740, 106], [21900, 113], [22040, 106], [22200, 113], [22340, 106], [22500, 113], [22640, 106], [22800, 114], [22939, 107], [23100, 114], [23239, 106], [23400, 115], [23538, 107], [23700, 116], [23838, 107], [24000, 116], [24137, 108], [24300, 117], [24437, 108], [24600, 117], [24737, 107], [24900, 117], [25037, 107], [25200, 117], [25337, 107], [25500, 118], [25637, 107], [25800, 118], [25936, 108], [26100, 118], [26236, 18], [26259, 85], [26400, 118], [26536, 17], [26560, 83], [26700, 118], [26837, 15], [26861, 82], [27000, 118], [27137, 14], [27162, 81], [27300, 118], [27437, 14], [27462, 81], [27600, 118], [27737, 13], [27763, 80], [27900, 117], [28037, 13], [28063, 79], [28200, 117], [28338, 11], [28363, 79], [28500, 117], [28638, 11], [28663, 79], [28800, 117], [28938, 11], [28963, 79], [29100, 117], [29238, 11], [29264, 78], [29400, 118], [29537, 12], [29563, 79], [29700, 119], [29835, 14], [29863, 78], [30000, 123], [30131, 19], [30163, 78], [30300, 150], [30463, 78], [30600, 150], [30762, 79], [30900, 151], [31062, 79], [31200, 152], [31361, 79], [31500, 153], [31659, 81], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [5.688, 5.688, 0.092, 0.092, 4.6, 4.6]], "forceVisible": true, "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [29, 24, 252, 135], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9635, 213], [9932, 219], [10229, 224], [10529, 224], [10829, 224], [11129, 224], [11430, 223], [11730, 222], [12030, 222], [12330, 222], [12631, 221], [12931, 221], [13231, 220], [13531, 220], [13831, 220], [14132, 219], [14432, 219], [14732, 219], [15032, 218], [15332, 218], [15633, 217], [15933, 217], [16233, 217], [16533, 216], [16834, 215], [17134, 215], [17434, 215], [17734, 215], [18034, 214], [18335, 213], [18635, 213], [18935, 213], [19235, 213], [19535, 213], [19836, 211], [20136, 211], [20436, 211], [20736, 211], [21037, 210], [21337, 209], [21637, 209], [21937, 209], [22237, 209], [22538, 208], [22838, 208], [23138, 207], [23438, 207], [23738, 207], [24039, 206], [24339, 206], [24639, 205], [24939, 205], [25240, 204], [25540, 204], [25840, 204], [26140, 204], [26440, 203], [26741, 202], [27041, 202], [27341, 202], [27641, 202], [27941, 201], [28242, 200], [28542, 200], [28842, 200], [29142, 200], [29443, 199], [29743, 198], [30043, 198], [30343, 198], [30643, 198], [30944, 197], [31244, 196], [31544, 196], [31844, 196], [32144, 196], [32445, 195], [32745, 195], [33045, 194], [33345, 194], [33646, 193], [33946, 193], [34246, 193], [34546, 192], [34846, 192], [35147, 191], [35447, 191], [35747, 191], [36047, 191], [36347, 190], [36648, 189], [36948, 189], [37248, 189], [37548, 189], [37849, 187], [38149, 187], [38449, 187], [38749, 187], [39049, 187], [39350, 186], [39650, 185], [39950, 185], [40250, 185]], "point": [140, 78]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.48|+00.94|-01.70"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [149, 88, 163, 106], "mask": [[26254, 5], [26553, 7], [26852, 9], [27151, 11], [27451, 11], [27750, 13], [28050, 13], [28349, 14], [28649, 14], [28949, 14], [29249, 15], [29549, 14], [29849, 14], [30150, 13], [30450, 13], [30750, 12], [31051, 11], [31352, 9], [31653, 6]], "point": [156, 96]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.42|+01.15|+00.02"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 24, 252, 209], "mask": [[6963, 159], [7260, 166], [7556, 173], [7852, 180], [8149, 186], [8445, 193], [8741, 200], [9038, 206], [9337, 209], [9622, 226], [9918, 233], [10214, 239], [10510, 243], [10806, 247], [11102, 251], [11400, 253], [11700, 252], [12000, 252], [12300, 252], [12600, 252], [12900, 252], [13200, 251], [13500, 251], [13800, 251], [14100, 251], [14400, 251], [14700, 251], [15000, 250], [15300, 250], [15600, 250], [15900, 120], [16034, 116], [16200, 114], [16338, 112], [16500, 112], [16639, 110], [16800, 112], [16940, 109], [17100, 112], [17240, 109], [17400, 112], [17540, 109], [17700, 111], [17840, 109], [18000, 111], [18141, 107], [18300, 111], [18441, 107], [18600, 111], [18741, 107], [18900, 111], [19041, 107], [19200, 111], [19341, 107], [19500, 111], [19641, 107], [19800, 111], [19941, 106], [20100, 111], [20241, 106], [20400, 111], [20541, 106], [20700, 111], [20841, 106], [21000, 112], [21141, 106], [21300, 112], [21441, 105], [21600, 112], [21740, 106], [21900, 113], [22040, 106], [22200, 113], [22340, 106], [22500, 113], [22640, 106], [22800, 114], [22939, 107], [23100, 114], [23239, 106], [23400, 115], [23538, 107], [23700, 116], [23838, 107], [24000, 116], [24137, 108], [24300, 117], [24437, 108], [24600, 117], [24737, 107], [24900, 117], [25037, 107], [25200, 117], [25337, 107], [25500, 118], [25637, 107], [25800, 118], [25936, 108], [26100, 118], [26236, 108], [26400, 118], [26536, 107], [26700, 118], [26837, 106], [27000, 118], [27137, 106], [27300, 118], [27437, 106], [27600, 118], [27737, 106], [27900, 117], [28037, 105], [28200, 117], [28338, 104], [28500, 117], [28638, 104], [28800, 117], [28938, 104], [29100, 117], [29238, 104], [29400, 118], [29537, 105], [29700, 119], [29835, 106], [30000, 123], [30131, 110], [30300, 241], [30600, 241], [30900, 241], [31200, 240], [31500, 240], [31800, 240], [32100, 240], [32400, 240], [32700, 240], [33000, 239], [33300, 239], [33600, 239], [33900, 239], [34200, 239], [34500, 238], [34800, 238], [35100, 238], [35400, 238], [35700, 238], [36000, 238], [36300, 237], [36600, 237], [36900, 237], [37200, 237], [37500, 237], [37800, 236], [38100, 236], [38400, 236], [38700, 236], [39000, 236], [39300, 236], [39600, 55], [39677, 6], [39798, 37], [39900, 54], [39977, 4], [40098, 37], [40200, 54], [40399, 36], [40500, 53], [40800, 52], [41100, 52], [41400, 51], [41700, 50], [42000, 49], [42300, 49], [42600, 48], [42900, 47], [43200, 47], [43500, 46], [43800, 45], [44100, 44], [44400, 44], [44700, 43], [45000, 42], [45300, 41], [45600, 41], [45900, 40], [46200, 39], [46500, 39], [46800, 38], [47100, 37], [47400, 36], [47700, 36], [48000, 35], [48300, 34], [48600, 34], [48900, 33], [49200, 32], [49500, 31], [49800, 31], [50100, 30], [50400, 29], [50700, 29], [51000, 28], [51300, 27], [51600, 26], [51900, 26], [52200, 25], [52500, 24], [52800, 24], [53100, 23], [53400, 22], [53700, 21], [54000, 21], [54300, 20], [54600, 19], [54900, 19], [55200, 18], [55500, 17], [55800, 16], [56100, 16], [56400, 15], [56700, 14], [57000, 14], [57300, 13], [57600, 12], [57900, 11], [58200, 11], [58500, 10], [58800, 9], [59100, 9], [59400, 8], [59700, 7], [60000, 6], [60300, 6], [60600, 5], [60900, 4], [61200, 4], [61500, 3], [61800, 2], [62100, 1], [62400, 1]], "point": [126, 115]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.48|+00.94|-01.70", "placeStationary": true, "receptacleObjectId": "CounterTop|+01.50|+01.20|-00.66"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 99, 299, 146], "mask": [[29400, 30], [29465, 166], [29648, 83], [29765, 166], [29948, 83], [30065, 166], [30248, 83], [30366, 164], [30548, 83], [30666, 164], [30848, 83], [30966, 164], [31148, 83], [31266, 164], [31448, 83], [31566, 164], [31748, 83], [31866, 164], [32048, 84], [32167, 162], [32347, 85], [32467, 162], [32647, 85], [32767, 162], [32947, 85], [33067, 162], [33247, 86], [33367, 161], [33546, 87], [33667, 161], [33846, 87], [33967, 160], [34147, 51], [34200, 34], [34267, 160], [34447, 49], [34500, 34], [34567, 160], [34747, 48], [34800, 34], [34868, 159], [35047, 47], [35100, 35], [35168, 159], [35346, 47], [35400, 35], [35468, 159], [35646, 47], [35700, 35], [35768, 158], [35946, 48], [36000, 35], [36068, 159], [36246, 48], [36300, 36], [36368, 160], [36546, 48], [36600, 36], [36668, 161], [36845, 50], [36900, 36], [36968, 164], [37144, 52], [37200, 37], [37268, 229], [37500, 37], [37568, 231], [37800, 38], [37868, 270], [38168, 271], [38468, 271], [38768, 271], [39068, 272], [39367, 273], [39667, 274], [39966, 275], [40265, 277], [40564, 279], [40862, 283], [41159, 291], [41452, 2348]], "point": [149, 121]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan8", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -3.0, "y": 0.9009992, "z": -1.0}, "object_poses": [{"objectName": "Pen_73e168e3", "position": {"x": -2.0755434, "y": 0.777266562, "z": -1.56547654}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Ladle_238538e2", "position": {"x": -1.8091464, "y": 0.9548624, "z": -1.44825029}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_238538e2", "position": {"x": 1.40199959, "y": 1.7004267, "z": -1.34299994}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_e75d4607", "position": {"x": -1.0811, "y": 0.949799955, "z": -1.81}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_b308514b", "position": {"x": 1.33563781, "y": 1.16291678, "z": -0.659025431}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_b308514b", "position": {"x": 1.27506137, "y": 1.36818409, "z": 1.72600031}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_6f7c42a7", "position": {"x": -0.6279957, "y": 0.912429333, "z": -1.78968191}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_e08d0eeb", "position": {"x": -0.477255583, "y": 0.940629065, "z": -1.70446813}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_bfcb0336", "position": {"x": 1.44122839, "y": 1.25969732, "z": 0.08376157}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": 1.41487837, "y": 1.16286886, "z": -1.68895817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": -2.07940626, "y": 0.911168754, "z": 0.463499784}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_af290bda", "position": {"x": 0.664081335, "y": 0.9410002, "z": -1.53404093}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_af290bda", "position": {"x": 1.34322, "y": 0.9410003, "z": 0.372564077}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": -1.08240128, "y": 1.85045171, "z": -1.8759867}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_441a8bda", "position": {"x": 1.3884635, "y": 1.76034415, "z": 1.963797}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_441a8bda", "position": {"x": 0.4756838, "y": 0.951417565, "z": -1.61925447}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": -1.12520242, "y": 1.84554827, "z": -1.7659266}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": 0.0494125634, "y": 0.109609306, "z": -1.67887616}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_0f08525d", "position": {"x": 1.17697, "y": 0.980465353, "z": -1.33170938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": -0.0869308859, "y": 0.7778865, "z": -1.70783865}, "rotation": {"x": 1.40334191e-14, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 1.27506256, "y": 1.36246753, "z": 2.10100055}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_b66a32b5", "position": {"x": 1.4264698, "y": 1.40215027, "z": 2.351}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_441a8bda", "position": {"x": 1.381, "y": 0.254656553, "z": 1.10898018}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_b308514b", "position": {"x": -0.95399797, "y": 1.851265, "z": -1.7659266}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_af290bda", "position": {"x": -2.07940626, "y": 0.940200269, "z": 0.291}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_9b9c5535", "position": {"x": 1.40199959, "y": 1.656733, "z": -0.658}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_7aaf3771", "position": {"x": -0.4018854, "y": 0.9118485, "z": -1.53404069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_238538e2", "position": {"x": -1.835049, "y": 0.9548624, "z": 0.3772499}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_1cf505ed", "position": {"x": 1.332814, "y": 1.859023, "z": 2.55745077}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_0f08525d", "position": {"x": -2.20158482, "y": 0.979665339, "z": 0.118500233}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_e75d4607", "position": {"x": -1.431, "y": 0.949799955, "z": -1.5257}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_73e168e3", "position": {"x": -1.835049, "y": 0.9131528, "z": 0.0322503448}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_a2768593", "position": {"x": 1.45449877, "y": 1.1572001, "z": -1.34564734}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_bfcb0336", "position": {"x": -2.07477283, "y": 0.112243295, "z": -1.63276732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_e08d0eeb", "position": {"x": 1.2625916, "y": 0.243868053, "z": 1.05101991}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_1d30af71", "position": {"x": 1.04795253, "y": 0.788162351, "z": -1.23358309}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_a0ece05a", "position": {"x": -2.32376337, "y": 0.911050856, "z": 0.463499784}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_abf5c390", "position": {"x": 0.5638487, "y": 1.654979, "z": -1.839}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_c0048524", "position": {"x": -1.07936192, "y": 0.94527024, "z": -1.52895641}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_c69c5340", "position": {"x": -0.6855, "y": 0.111667871, "z": -1.63276732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_316d55f0", "position": {"x": 0.9673953, "y": 0.9, "z": 0.249277115}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_7cca838f", "position": {"x": 0.664081335, "y": 0.911342, "z": -1.78968167}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1de14ecd", "position": {"x": 1.05215132, "y": 1.65057492, "z": -1.76446271}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b7024f32", "position": {"x": 0.8524788, "y": 0.9108642, "z": -1.61925447}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_9b638b9f", "position": {"x": -2.116709, "y": 0.988641143, "z": -1.53450024}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_b66a32b5", "position": {"x": 0.14961265, "y": 0.9542329, "z": -1.7080245}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_6f7c42a7", "position": {"x": 0.251858473, "y": 0.9206794, "z": -1.60713387}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_49b86ad0", "position": {"x": 0.285940379, "y": 0.91455, "z": -1.75846994}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1740583664, "scene_num": 8}, "task_id": "trial_T20190907_065323_294877", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1MKCTVKE7J0ZP_39U1BHVTDO8MG4O05UCG50X1E9FT3Q", "high_descs": ["Turn around, then go straight, then turn right at the counter to the right of the sink.", "Pick up the egg on the counter, next to the fork.", "Turn around, then go straight, then turn right to go to the microwave.", "Heat the egg in the microwave.", "Turn right, then go straight, then turn left to face the counter.", "Put the egg on the counter, to the left of the salt."], "task_desc": "Put a cooked egg on the counter.", "votes": [1, 1]}, {"assignment_id": "A24RGLS3BRZ60J_3ATPCQ38JBRIHBMFOFEP0P3MZSTAYA", "high_descs": ["Turn to the right, and then the left to find the sink.", "Pick up the egg from behind the fork, on the counter.", "Take the egg and turn around and find the microwave on the right.", "Open the microwave and put the egg inside. Shut the door and turn the microwave on. When finished, open the door, take the egg out and shut the door.", "Take the egg, and move right to where the salt container is on the counter.", "Place the egg on the far back portion of the counter, to the left of the salt."], "task_desc": "Heat an egg and place it on the counter.", "votes": [1, 1]}, {"assignment_id": "A1GVTH5YS3WOK0_3TYCR1GOTF0ZIKNOHS5YNASZZTJLZ7", "high_descs": ["Turn right towards the sink on your right.", "Pick up an egg to the right of the sink.", "Turn around and head towards the microwave on your right.", "Heat up the egg and take it out of the microwave.", "Take a step to the right.", "Place the egg in between the glass vase and salt shaker on the counter."], "task_desc": "Place a warm egg in between the glass vase and salt shaker on the counter.", "votes": [1, 1]}]}}