
(define (problem plan_trial_T20190909_005000_349735)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__plus_00_dot_46_bar__plus_00_dot_81_bar__minus_02_dot_51 - object
        AlarmClock_bar__plus_01_dot_49_bar__plus_00_dot_82_bar__minus_01_dot_67 - object
        Blinds_bar__plus_01_dot_70_bar__plus_02_dot_10_bar__minus_01_dot_58 - object
        Book_bar__plus_00_dot_19_bar__plus_00_dot_81_bar__minus_02_dot_27 - object
        Book_bar__minus_01_dot_23_bar__plus_00_dot_65_bar__minus_02_dot_33 - object
        Bowl_bar__minus_00_dot_67_bar__plus_00_dot_86_bar__minus_02_dot_50 - object
        CD_bar__plus_00_dot_56_bar__plus_00_dot_81_bar__minus_02_dot_44 - object
        CD_bar__minus_01_dot_49_bar__plus_00_dot_65_bar__minus_02_dot_25 - object
        CD_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__minus_02_dot_37 - object
        CellPhone_bar__minus_00_dot_62_bar__plus_00_dot_77_bar__plus_00_dot_85 - object
        Chair_bar__plus_00_dot_22_bar__plus_00_dot_00_bar__minus_02_dot_21 - object
        Chair_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__minus_02_dot_20 - object
        Cloth_bar__plus_01_dot_27_bar__plus_00_dot_00_bar__minus_00_dot_97 - object
        Cloth_bar__plus_01_dot_30_bar__plus_00_dot_00_bar__minus_00_dot_70 - object
        CreditCard_bar__minus_01_dot_67_bar__plus_00_dot_65_bar__minus_02_dot_33 - object
        DeskLamp_bar__plus_00_dot_05_bar__plus_00_dot_81_bar__minus_02_dot_48 - object
        KeyChain_bar__plus_00_dot_04_bar__plus_00_dot_81_bar__minus_02_dot_30 - object
        Laptop_bar__plus_00_dot_24_bar__plus_00_dot_77_bar__plus_01_dot_66 - object
        Laptop_bar__minus_00_dot_62_bar__plus_00_dot_77_bar__plus_01_dot_82 - object
        LaundryHamperLid_bar__minus_01_dot_44_bar__plus_00_dot_48_bar__plus_01_dot_76 - object
        LightSwitch_bar__minus_01_dot_80_bar__plus_01_dot_21_bar__plus_00_dot_68 - object
        Mirror_bar__plus_01_dot_50_bar__plus_01_dot_36_bar__minus_00_dot_54 - object
        Painting_bar__plus_00_dot_49_bar__plus_01_dot_68_bar__minus_02_dot_63 - object
        Pencil_bar__minus_01_dot_32_bar__plus_00_dot_38_bar__minus_02_dot_33 - object
        Pen_bar__plus_01_dot_47_bar__plus_00_dot_83_bar__minus_01_dot_78 - object
        Pen_bar__minus_01_dot_23_bar__plus_00_dot_09_bar__minus_02_dot_29 - object
        Pen_bar__minus_01_dot_58_bar__plus_00_dot_38_bar__minus_02_dot_33 - object
        Pillow_bar__minus_00_dot_05_bar__plus_00_dot_90_bar__plus_01_dot_01 - object
        Poster_bar__minus_01_dot_80_bar__plus_01_dot_69_bar__minus_01_dot_49 - object
        RemoteControl_bar__minus_01_dot_41_bar__plus_00_dot_09_bar__minus_02_dot_36 - object
        RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_09_bar__minus_02_dot_29 - object
        RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_65_bar__minus_02_dot_33 - object
        Television_bar__minus_01_dot_70_bar__plus_01_dot_47_bar__minus_00_dot_90 - object
        TennisRacket_bar__minus_01_dot_70_bar__plus_00_dot_35_bar__plus_01_dot_74 - object
        Window_bar__plus_01_dot_72_bar__plus_01_dot_37_bar__minus_01_dot_60 - object
        Bed_bar__plus_00_dot_27_bar__plus_00_dot_00_bar__plus_01_dot_33 - receptacle
        Desk_bar__minus_00_dot_87_bar__minus_00_dot_01_bar__minus_02_dot_44 - receptacle
        Drawer_bar__minus_01_dot_41_bar__plus_00_dot_20_bar__minus_02_dot_32 - receptacle
        Drawer_bar__minus_01_dot_41_bar__plus_00_dot_48_bar__minus_02_dot_32 - receptacle
        Drawer_bar__minus_01_dot_41_bar__plus_00_dot_73_bar__minus_02_dot_32 - receptacle
        GarbageCan_bar__plus_01_dot_32_bar__plus_00_dot_00_bar__minus_02_dot_47 - receptacle
        LaundryHamper_bar__minus_01_dot_44_bar__plus_00_dot_01_bar__plus_01_dot_77 - receptacle
        Shelf_bar__plus_01_dot_49_bar__plus_00_dot_82_bar__minus_01_dot_48 - receptacle
        Shelf_bar__minus_01_dot_02_bar__plus_01_dot_35_bar__minus_02_dot_54 - receptacle
        SideTable_bar__plus_00_dot_25_bar_00_dot_00_bar__minus_02_dot_38 - receptacle
        loc_bar_4_bar__minus_8_bar_2_bar_60 - location
        loc_bar__minus_4_bar__minus_4_bar_3_bar_15 - location
        loc_bar_4_bar__minus_8_bar_0_bar_60 - location
        loc_bar__minus_3_bar__minus_7_bar_2_bar_60 - location
        loc_bar__minus_4_bar__minus_7_bar_2_bar_15 - location
        loc_bar_4_bar__minus_3_bar_1_bar_60 - location
        loc_bar__minus_5_bar__minus_6_bar_3_bar_0 - location
        loc_bar__minus_1_bar__minus_7_bar_3_bar_45 - location
        loc_bar_1_bar__minus_7_bar_2_bar_60 - location
        loc_bar_2_bar__minus_7_bar_2_bar_0 - location
        loc_bar__minus_5_bar__minus_7_bar_2_bar_60 - location
        loc_bar_4_bar__minus_4_bar_1_bar_60 - location
        loc_bar__minus_1_bar_0_bar_0_bar_45 - location
        loc_bar_4_bar__minus_2_bar_1_bar_30 - location
        loc_bar__minus_3_bar__minus_7_bar_3_bar_60 - location
        loc_bar__minus_5_bar_5_bar_0_bar_60 - location
        loc_bar_4_bar__minus_6_bar_1_bar__minus_30 - location
        loc_bar__minus_5_bar_3_bar_3_bar_45 - location
        loc_bar_4_bar__minus_6_bar_1_bar_15 - location
        loc_bar_3_bar__minus_3_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType Shelf_bar__plus_01_dot_49_bar__plus_00_dot_82_bar__minus_01_dot_48 ShelfType)
        (receptacleType Drawer_bar__minus_01_dot_41_bar__plus_00_dot_48_bar__minus_02_dot_32 DrawerType)
        (receptacleType Shelf_bar__minus_01_dot_02_bar__plus_01_dot_35_bar__minus_02_dot_54 ShelfType)
        (receptacleType Bed_bar__plus_00_dot_27_bar__plus_00_dot_00_bar__plus_01_dot_33 BedType)
        (receptacleType SideTable_bar__plus_00_dot_25_bar_00_dot_00_bar__minus_02_dot_38 SideTableType)
        (receptacleType LaundryHamper_bar__minus_01_dot_44_bar__plus_00_dot_01_bar__plus_01_dot_77 LaundryHamperType)
        (receptacleType Drawer_bar__minus_01_dot_41_bar__plus_00_dot_73_bar__minus_02_dot_32 DrawerType)
        (receptacleType GarbageCan_bar__plus_01_dot_32_bar__plus_00_dot_00_bar__minus_02_dot_47 GarbageCanType)
        (receptacleType Drawer_bar__minus_01_dot_41_bar__plus_00_dot_20_bar__minus_02_dot_32 DrawerType)
        (receptacleType Desk_bar__minus_00_dot_87_bar__minus_00_dot_01_bar__minus_02_dot_44 DeskType)
        (objectType TennisRacket_bar__minus_01_dot_70_bar__plus_00_dot_35_bar__plus_01_dot_74 TennisRacketType)
        (objectType Chair_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__minus_02_dot_20 ChairType)
        (objectType Book_bar__minus_01_dot_23_bar__plus_00_dot_65_bar__minus_02_dot_33 BookType)
        (objectType Laptop_bar__minus_00_dot_62_bar__plus_00_dot_77_bar__plus_01_dot_82 LaptopType)
        (objectType LightSwitch_bar__minus_01_dot_80_bar__plus_01_dot_21_bar__plus_00_dot_68 LightSwitchType)
        (objectType Television_bar__minus_01_dot_70_bar__plus_01_dot_47_bar__minus_00_dot_90 TelevisionType)
        (objectType RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_09_bar__minus_02_dot_29 RemoteControlType)
        (objectType KeyChain_bar__plus_00_dot_04_bar__plus_00_dot_81_bar__minus_02_dot_30 KeyChainType)
        (objectType LaundryHamperLid_bar__minus_01_dot_44_bar__plus_00_dot_48_bar__plus_01_dot_76 LaundryHamperLidType)
        (objectType Painting_bar__plus_00_dot_49_bar__plus_01_dot_68_bar__minus_02_dot_63 PaintingType)
        (objectType Cloth_bar__plus_01_dot_30_bar__plus_00_dot_00_bar__minus_00_dot_70 ClothType)
        (objectType Pen_bar__minus_01_dot_58_bar__plus_00_dot_38_bar__minus_02_dot_33 PenType)
        (objectType RemoteControl_bar__minus_01_dot_41_bar__plus_00_dot_09_bar__minus_02_dot_36 RemoteControlType)
        (objectType Mirror_bar__plus_01_dot_50_bar__plus_01_dot_36_bar__minus_00_dot_54 MirrorType)
        (objectType RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_65_bar__minus_02_dot_33 RemoteControlType)
        (objectType AlarmClock_bar__plus_00_dot_46_bar__plus_00_dot_81_bar__minus_02_dot_51 AlarmClockType)
        (objectType Laptop_bar__plus_00_dot_24_bar__plus_00_dot_77_bar__plus_01_dot_66 LaptopType)
        (objectType Poster_bar__minus_01_dot_80_bar__plus_01_dot_69_bar__minus_01_dot_49 PosterType)
        (objectType Window_bar__plus_01_dot_72_bar__plus_01_dot_37_bar__minus_01_dot_60 WindowType)
        (objectType CellPhone_bar__minus_00_dot_62_bar__plus_00_dot_77_bar__plus_00_dot_85 CellPhoneType)
        (objectType Blinds_bar__plus_01_dot_70_bar__plus_02_dot_10_bar__minus_01_dot_58 BlindsType)
        (objectType Cloth_bar__plus_01_dot_27_bar__plus_00_dot_00_bar__minus_00_dot_97 ClothType)
        (objectType AlarmClock_bar__plus_01_dot_49_bar__plus_00_dot_82_bar__minus_01_dot_67 AlarmClockType)
        (objectType Pillow_bar__minus_00_dot_05_bar__plus_00_dot_90_bar__plus_01_dot_01 PillowType)
        (objectType Pen_bar__minus_01_dot_23_bar__plus_00_dot_09_bar__minus_02_dot_29 PenType)
        (objectType CD_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__minus_02_dot_37 CDType)
        (objectType Pencil_bar__minus_01_dot_32_bar__plus_00_dot_38_bar__minus_02_dot_33 PencilType)
        (objectType Pen_bar__plus_01_dot_47_bar__plus_00_dot_83_bar__minus_01_dot_78 PenType)
        (objectType CreditCard_bar__minus_01_dot_67_bar__plus_00_dot_65_bar__minus_02_dot_33 CreditCardType)
        (objectType DeskLamp_bar__plus_00_dot_05_bar__plus_00_dot_81_bar__minus_02_dot_48 DeskLampType)
        (objectType Book_bar__plus_00_dot_19_bar__plus_00_dot_81_bar__minus_02_dot_27 BookType)
        (objectType CD_bar__minus_01_dot_49_bar__plus_00_dot_65_bar__minus_02_dot_25 CDType)
        (objectType Bowl_bar__minus_00_dot_67_bar__plus_00_dot_86_bar__minus_02_dot_50 BowlType)
        (objectType Chair_bar__plus_00_dot_22_bar__plus_00_dot_00_bar__minus_02_dot_21 ChairType)
        (objectType CD_bar__plus_00_dot_56_bar__plus_00_dot_81_bar__minus_02_dot_44 CDType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType BowlType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType ClothType)
        (canContain ShelfType PencilType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType AlarmClockType)
        (canContain BedType TennisRacketType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType BowlType)
        (canContain SideTableType CDType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType TennisRacketType)
        (canContain SideTableType ClothType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType AlarmClockType)
        (canContain LaundryHamperType ClothType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType ClothType)
        (canContain GarbageCanType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType ClothType)
        (canContain DrawerType PencilType)
        (canContain DrawerType RemoteControlType)
        (canContain DeskType PenType)
        (canContain DeskType BookType)
        (canContain DeskType BowlType)
        (canContain DeskType CDType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType CreditCardType)
        (canContain DeskType TennisRacketType)
        (canContain DeskType ClothType)
        (canContain DeskType LaptopType)
        (canContain DeskType PencilType)
        (canContain DeskType RemoteControlType)
        (canContain DeskType AlarmClockType)
        (pickupable TennisRacket_bar__minus_01_dot_70_bar__plus_00_dot_35_bar__plus_01_dot_74)
        (pickupable Book_bar__minus_01_dot_23_bar__plus_00_dot_65_bar__minus_02_dot_33)
        (pickupable Laptop_bar__minus_00_dot_62_bar__plus_00_dot_77_bar__plus_01_dot_82)
        (pickupable RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_09_bar__minus_02_dot_29)
        (pickupable KeyChain_bar__plus_00_dot_04_bar__plus_00_dot_81_bar__minus_02_dot_30)
        (pickupable Cloth_bar__plus_01_dot_30_bar__plus_00_dot_00_bar__minus_00_dot_70)
        (pickupable Pen_bar__minus_01_dot_58_bar__plus_00_dot_38_bar__minus_02_dot_33)
        (pickupable RemoteControl_bar__minus_01_dot_41_bar__plus_00_dot_09_bar__minus_02_dot_36)
        (pickupable RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_65_bar__minus_02_dot_33)
        (pickupable AlarmClock_bar__plus_00_dot_46_bar__plus_00_dot_81_bar__minus_02_dot_51)
        (pickupable Laptop_bar__plus_00_dot_24_bar__plus_00_dot_77_bar__plus_01_dot_66)
        (pickupable CellPhone_bar__minus_00_dot_62_bar__plus_00_dot_77_bar__plus_00_dot_85)
        (pickupable Cloth_bar__plus_01_dot_27_bar__plus_00_dot_00_bar__minus_00_dot_97)
        (pickupable AlarmClock_bar__plus_01_dot_49_bar__plus_00_dot_82_bar__minus_01_dot_67)
        (pickupable Pillow_bar__minus_00_dot_05_bar__plus_00_dot_90_bar__plus_01_dot_01)
        (pickupable Pen_bar__minus_01_dot_23_bar__plus_00_dot_09_bar__minus_02_dot_29)
        (pickupable CD_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__minus_02_dot_37)
        (pickupable Pencil_bar__minus_01_dot_32_bar__plus_00_dot_38_bar__minus_02_dot_33)
        (pickupable Pen_bar__plus_01_dot_47_bar__plus_00_dot_83_bar__minus_01_dot_78)
        (pickupable CreditCard_bar__minus_01_dot_67_bar__plus_00_dot_65_bar__minus_02_dot_33)
        (pickupable Book_bar__plus_00_dot_19_bar__plus_00_dot_81_bar__minus_02_dot_27)
        (pickupable CD_bar__minus_01_dot_49_bar__plus_00_dot_65_bar__minus_02_dot_25)
        (pickupable Bowl_bar__minus_00_dot_67_bar__plus_00_dot_86_bar__minus_02_dot_50)
        (pickupable CD_bar__plus_00_dot_56_bar__plus_00_dot_81_bar__minus_02_dot_44)
        (isReceptacleObject Bowl_bar__minus_00_dot_67_bar__plus_00_dot_86_bar__minus_02_dot_50)
        (openable Drawer_bar__minus_01_dot_41_bar__plus_00_dot_48_bar__minus_02_dot_32)
        (openable Drawer_bar__minus_01_dot_41_bar__plus_00_dot_73_bar__minus_02_dot_32)
        (openable Drawer_bar__minus_01_dot_41_bar__plus_00_dot_20_bar__minus_02_dot_32)
        
        (atLocation agent1 loc_bar_3_bar__minus_3_bar_3_bar_30)
        
        (cleanable Cloth_bar__plus_01_dot_30_bar__plus_00_dot_00_bar__minus_00_dot_70)
        (cleanable Cloth_bar__plus_01_dot_27_bar__plus_00_dot_00_bar__minus_00_dot_97)
        (cleanable Bowl_bar__minus_00_dot_67_bar__plus_00_dot_86_bar__minus_02_dot_50)
        
        
        (coolable Bowl_bar__minus_00_dot_67_bar__plus_00_dot_86_bar__minus_02_dot_50)
        
        
        (toggleable DeskLamp_bar__plus_00_dot_05_bar__plus_00_dot_81_bar__minus_02_dot_48)
        
        
        
        
        (inReceptacle CD_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__minus_02_dot_37 Desk_bar__minus_00_dot_87_bar__minus_00_dot_01_bar__minus_02_dot_44)
        (inReceptacle Bowl_bar__minus_00_dot_67_bar__plus_00_dot_86_bar__minus_02_dot_50 Desk_bar__minus_00_dot_87_bar__minus_00_dot_01_bar__minus_02_dot_44)
        (inReceptacle DeskLamp_bar__plus_00_dot_05_bar__plus_00_dot_81_bar__minus_02_dot_48 SideTable_bar__plus_00_dot_25_bar_00_dot_00_bar__minus_02_dot_38)
        (inReceptacle Book_bar__plus_00_dot_19_bar__plus_00_dot_81_bar__minus_02_dot_27 SideTable_bar__plus_00_dot_25_bar_00_dot_00_bar__minus_02_dot_38)
        (inReceptacle KeyChain_bar__plus_00_dot_04_bar__plus_00_dot_81_bar__minus_02_dot_30 SideTable_bar__plus_00_dot_25_bar_00_dot_00_bar__minus_02_dot_38)
        (inReceptacle AlarmClock_bar__plus_00_dot_46_bar__plus_00_dot_81_bar__minus_02_dot_51 SideTable_bar__plus_00_dot_25_bar_00_dot_00_bar__minus_02_dot_38)
        (inReceptacle CD_bar__plus_00_dot_56_bar__plus_00_dot_81_bar__minus_02_dot_44 SideTable_bar__plus_00_dot_25_bar_00_dot_00_bar__minus_02_dot_38)
        (inReceptacle Pen_bar__minus_01_dot_58_bar__plus_00_dot_38_bar__minus_02_dot_33 Drawer_bar__minus_01_dot_41_bar__plus_00_dot_48_bar__minus_02_dot_32)
        (inReceptacle Pencil_bar__minus_01_dot_32_bar__plus_00_dot_38_bar__minus_02_dot_33 Drawer_bar__minus_01_dot_41_bar__plus_00_dot_48_bar__minus_02_dot_32)
        (inReceptacle CD_bar__minus_01_dot_49_bar__plus_00_dot_65_bar__minus_02_dot_25 Drawer_bar__minus_01_dot_41_bar__plus_00_dot_73_bar__minus_02_dot_32)
        (inReceptacle RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_65_bar__minus_02_dot_33 Drawer_bar__minus_01_dot_41_bar__plus_00_dot_73_bar__minus_02_dot_32)
        (inReceptacle Book_bar__minus_01_dot_23_bar__plus_00_dot_65_bar__minus_02_dot_33 Drawer_bar__minus_01_dot_41_bar__plus_00_dot_73_bar__minus_02_dot_32)
        (inReceptacle CreditCard_bar__minus_01_dot_67_bar__plus_00_dot_65_bar__minus_02_dot_33 Drawer_bar__minus_01_dot_41_bar__plus_00_dot_73_bar__minus_02_dot_32)
        (inReceptacle RemoteControl_bar__minus_01_dot_41_bar__plus_00_dot_09_bar__minus_02_dot_36 Drawer_bar__minus_01_dot_41_bar__plus_00_dot_20_bar__minus_02_dot_32)
        (inReceptacle RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_09_bar__minus_02_dot_29 Drawer_bar__minus_01_dot_41_bar__plus_00_dot_20_bar__minus_02_dot_32)
        (inReceptacle Pen_bar__minus_01_dot_23_bar__plus_00_dot_09_bar__minus_02_dot_29 Drawer_bar__minus_01_dot_41_bar__plus_00_dot_20_bar__minus_02_dot_32)
        (inReceptacle Laptop_bar__minus_00_dot_62_bar__plus_00_dot_77_bar__plus_01_dot_82 Bed_bar__plus_00_dot_27_bar__plus_00_dot_00_bar__plus_01_dot_33)
        (inReceptacle CellPhone_bar__minus_00_dot_62_bar__plus_00_dot_77_bar__plus_00_dot_85 Bed_bar__plus_00_dot_27_bar__plus_00_dot_00_bar__plus_01_dot_33)
        (inReceptacle Pillow_bar__minus_00_dot_05_bar__plus_00_dot_90_bar__plus_01_dot_01 Bed_bar__plus_00_dot_27_bar__plus_00_dot_00_bar__plus_01_dot_33)
        (inReceptacle Laptop_bar__plus_00_dot_24_bar__plus_00_dot_77_bar__plus_01_dot_66 Bed_bar__plus_00_dot_27_bar__plus_00_dot_00_bar__plus_01_dot_33)
        (inReceptacle Pen_bar__plus_01_dot_47_bar__plus_00_dot_83_bar__minus_01_dot_78 Shelf_bar__plus_01_dot_49_bar__plus_00_dot_82_bar__minus_01_dot_48)
        (inReceptacle AlarmClock_bar__plus_01_dot_49_bar__plus_00_dot_82_bar__minus_01_dot_67 Shelf_bar__plus_01_dot_49_bar__plus_00_dot_82_bar__minus_01_dot_48)
        
        
        (receptacleAtLocation Bed_bar__plus_00_dot_27_bar__plus_00_dot_00_bar__plus_01_dot_33 loc_bar__minus_1_bar_0_bar_0_bar_45)
        (receptacleAtLocation Desk_bar__minus_00_dot_87_bar__minus_00_dot_01_bar__minus_02_dot_44 loc_bar__minus_5_bar__minus_7_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_41_bar__plus_00_dot_20_bar__minus_02_dot_32 loc_bar__minus_1_bar__minus_7_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_41_bar__plus_00_dot_48_bar__minus_02_dot_32 loc_bar__minus_1_bar__minus_7_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_41_bar__plus_00_dot_73_bar__minus_02_dot_32 loc_bar__minus_3_bar__minus_7_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_01_dot_32_bar__plus_00_dot_00_bar__minus_02_dot_47 loc_bar_4_bar__minus_8_bar_2_bar_60)
        (receptacleAtLocation LaundryHamper_bar__minus_01_dot_44_bar__plus_00_dot_01_bar__plus_01_dot_77 loc_bar__minus_5_bar_5_bar_0_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_49_bar__plus_00_dot_82_bar__minus_01_dot_48 loc_bar_4_bar__minus_8_bar_0_bar_60)
        (receptacleAtLocation Shelf_bar__minus_01_dot_02_bar__plus_01_dot_35_bar__minus_02_dot_54 loc_bar__minus_4_bar__minus_7_bar_2_bar_15)
        (receptacleAtLocation SideTable_bar__plus_00_dot_25_bar_00_dot_00_bar__minus_02_dot_38 loc_bar_1_bar__minus_7_bar_2_bar_60)
        (objectAtLocation Pen_bar__plus_01_dot_47_bar__plus_00_dot_83_bar__minus_01_dot_78 loc_bar_4_bar__minus_8_bar_0_bar_60)
        (objectAtLocation RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_65_bar__minus_02_dot_33 loc_bar__minus_3_bar__minus_7_bar_3_bar_60)
        (objectAtLocation AlarmClock_bar__plus_00_dot_46_bar__plus_00_dot_81_bar__minus_02_dot_51 loc_bar_1_bar__minus_7_bar_2_bar_60)
        (objectAtLocation Laptop_bar__plus_00_dot_24_bar__plus_00_dot_77_bar__plus_01_dot_66 loc_bar__minus_1_bar_0_bar_0_bar_45)
        (objectAtLocation Book_bar__plus_00_dot_19_bar__plus_00_dot_81_bar__minus_02_dot_27 loc_bar_1_bar__minus_7_bar_2_bar_60)
        (objectAtLocation RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_09_bar__minus_02_dot_29 loc_bar__minus_1_bar__minus_7_bar_3_bar_45)
        (objectAtLocation Pen_bar__minus_01_dot_58_bar__plus_00_dot_38_bar__minus_02_dot_33 loc_bar__minus_1_bar__minus_7_bar_3_bar_45)
        (objectAtLocation Book_bar__minus_01_dot_23_bar__plus_00_dot_65_bar__minus_02_dot_33 loc_bar__minus_3_bar__minus_7_bar_3_bar_60)
        (objectAtLocation LaundryHamperLid_bar__minus_01_dot_44_bar__plus_00_dot_48_bar__plus_01_dot_76 loc_bar__minus_5_bar_5_bar_0_bar_60)
        (objectAtLocation Chair_bar__minus_00_dot_63_bar__plus_00_dot_00_bar__minus_02_dot_20 loc_bar__minus_3_bar__minus_7_bar_2_bar_60)
        (objectAtLocation Chair_bar__plus_00_dot_22_bar__plus_00_dot_00_bar__minus_02_dot_21 loc_bar_1_bar__minus_7_bar_2_bar_60)
        (objectAtLocation Cloth_bar__plus_01_dot_30_bar__plus_00_dot_00_bar__minus_00_dot_70 loc_bar_4_bar__minus_3_bar_1_bar_60)
        (objectAtLocation Cloth_bar__plus_01_dot_27_bar__plus_00_dot_00_bar__minus_00_dot_97 loc_bar_4_bar__minus_4_bar_1_bar_60)
        (objectAtLocation CellPhone_bar__minus_00_dot_62_bar__plus_00_dot_77_bar__plus_00_dot_85 loc_bar__minus_1_bar_0_bar_0_bar_45)
        (objectAtLocation Television_bar__minus_01_dot_70_bar__plus_01_dot_47_bar__minus_00_dot_90 loc_bar__minus_4_bar__minus_4_bar_3_bar_15)
        (objectAtLocation Pencil_bar__minus_01_dot_32_bar__plus_00_dot_38_bar__minus_02_dot_33 loc_bar__minus_1_bar__minus_7_bar_3_bar_45)
        (objectAtLocation DeskLamp_bar__plus_00_dot_05_bar__plus_00_dot_81_bar__minus_02_dot_48 loc_bar_1_bar__minus_7_bar_2_bar_60)
        (objectAtLocation Laptop_bar__minus_00_dot_62_bar__plus_00_dot_77_bar__plus_01_dot_82 loc_bar__minus_1_bar_0_bar_0_bar_45)
        (objectAtLocation LightSwitch_bar__minus_01_dot_80_bar__plus_01_dot_21_bar__plus_00_dot_68 loc_bar__minus_5_bar_3_bar_3_bar_45)
        (objectAtLocation AlarmClock_bar__plus_01_dot_49_bar__plus_00_dot_82_bar__minus_01_dot_67 loc_bar_4_bar__minus_8_bar_0_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_67_bar__plus_00_dot_65_bar__minus_02_dot_33 loc_bar__minus_3_bar__minus_7_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__plus_00_dot_04_bar__plus_00_dot_81_bar__minus_02_dot_30 loc_bar_1_bar__minus_7_bar_2_bar_60)
        (objectAtLocation TennisRacket_bar__minus_01_dot_70_bar__plus_00_dot_35_bar__plus_01_dot_74 loc_bar__minus_5_bar_5_bar_0_bar_60)
        (objectAtLocation Painting_bar__plus_00_dot_49_bar__plus_01_dot_68_bar__minus_02_dot_63 loc_bar_2_bar__minus_7_bar_2_bar_0)
        (objectAtLocation RemoteControl_bar__minus_01_dot_41_bar__plus_00_dot_09_bar__minus_02_dot_36 loc_bar__minus_1_bar__minus_7_bar_3_bar_45)
        (objectAtLocation Pillow_bar__minus_00_dot_05_bar__plus_00_dot_90_bar__plus_01_dot_01 loc_bar__minus_1_bar_0_bar_0_bar_45)
        (objectAtLocation Poster_bar__minus_01_dot_80_bar__plus_01_dot_69_bar__minus_01_dot_49 loc_bar__minus_5_bar__minus_6_bar_3_bar_0)
        (objectAtLocation Mirror_bar__plus_01_dot_50_bar__plus_01_dot_36_bar__minus_00_dot_54 loc_bar_4_bar__minus_2_bar_1_bar_30)
        (objectAtLocation CD_bar__minus_01_dot_59_bar__plus_00_dot_86_bar__minus_02_dot_37 loc_bar__minus_5_bar__minus_7_bar_2_bar_60)
        (objectAtLocation CD_bar__plus_00_dot_56_bar__plus_00_dot_81_bar__minus_02_dot_44 loc_bar_1_bar__minus_7_bar_2_bar_60)
        (objectAtLocation CD_bar__minus_01_dot_49_bar__plus_00_dot_65_bar__minus_02_dot_25 loc_bar__minus_3_bar__minus_7_bar_3_bar_60)
        (objectAtLocation Pen_bar__minus_01_dot_23_bar__plus_00_dot_09_bar__minus_02_dot_29 loc_bar__minus_1_bar__minus_7_bar_3_bar_45)
        (objectAtLocation Window_bar__plus_01_dot_72_bar__plus_01_dot_37_bar__minus_01_dot_60 loc_bar_4_bar__minus_6_bar_1_bar_15)
        (objectAtLocation Blinds_bar__plus_01_dot_70_bar__plus_02_dot_10_bar__minus_01_dot_58 loc_bar_4_bar__minus_6_bar_1_bar__minus_30)
        (objectAtLocation Bowl_bar__minus_00_dot_67_bar__plus_00_dot_86_bar__minus_02_dot_50 loc_bar__minus_5_bar__minus_7_bar_2_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 LaptopType)
                                    (receptacleType ?r DeskType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 LaptopType)
                                            (receptacleType ?r DeskType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            