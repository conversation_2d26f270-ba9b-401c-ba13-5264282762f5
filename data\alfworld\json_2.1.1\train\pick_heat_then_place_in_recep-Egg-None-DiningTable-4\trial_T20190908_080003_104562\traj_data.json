{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 44}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "DiningTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-3|4|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-3.311030628, -3.311030628, 2.639001368, 2.639001368, 4.62041424, 4.62041424]], "coordinateReceptacleObjectId": ["CounterTop", [-2.06, -2.06, 1.968, 1.968, 4.6416, 4.6416]], "forceVisible": true, "objectId": "Egg|-00.83|+01.16|+00.66"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-6|4|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-1.468, -1.468, 1.72, 1.72, 4.4524064, 4.4524064]], "forceVisible": true, "objectId": "Microwave|-00.37|+01.11|+00.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|12|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-3.311030628, -3.311030628, 2.639001368, 2.639001368, 4.62041424, 4.62041424]], "coordinateReceptacleObjectId": ["DiningTable", [-2.48, -2.48, 9.944, 9.944, 0.0698738544, 0.0698738544]], "forceVisible": true, "objectId": "Egg|-00.83|+01.16|+00.66", "receptacleObjectId": "DiningTable|-00.62|+00.02|+02.49"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.83|+01.16|+00.66"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [175, 99, 199, 126], "mask": [[29584, 8], [29882, 12], [30181, 14], [30480, 16], [30779, 18], [31078, 20], [31377, 21], [31677, 22], [31976, 23], [32276, 23], [32575, 24], [32875, 25], [33175, 25], [33475, 25], [33775, 25], [34075, 25], [34375, 24], [34675, 24], [34975, 24], [35275, 23], [35576, 22], [35876, 21], [36177, 20], [36478, 18], [36779, 16], [37080, 14], [37382, 10], [37685, 3]], "point": [187, 111]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [223, 27, 299, 143], "mask": [[8042, 58], [8331, 69], [8631, 69], [8931, 69], [9231, 69], [9531, 69], [9831, 69], [10130, 70], [10430, 70], [10730, 70], [11030, 70], [11330, 70], [11630, 70], [11930, 70], [12230, 70], [12529, 71], [12829, 71], [13129, 71], [13429, 71], [13729, 71], [14029, 71], [14329, 71], [14628, 72], [14928, 72], [15228, 72], [15528, 72], [15828, 72], [16128, 72], [16428, 72], [16727, 73], [17027, 73], [17327, 73], [17627, 73], [17927, 73], [18227, 73], [18527, 73], [18826, 74], [19126, 74], [19426, 74], [19726, 74], [20026, 74], [20326, 74], [20626, 74], [20925, 75], [21225, 75], [21525, 75], [21825, 75], [22125, 75], [22425, 75], [22725, 75], [23024, 76], [23324, 76], [23624, 76], [23924, 76], [24224, 76], [24524, 76], [24824, 76], [25123, 77], [25423, 77], [25723, 77], [26023, 77], [26323, 77], [26624, 76], [26925, 75], [27225, 75], [27526, 74], [27827, 73], [28128, 72], [28429, 71], [28730, 70], [29030, 70], [29331, 69], [29632, 68], [29933, 67], [30234, 66], [30535, 65], [30835, 65], [31136, 64], [31437, 63], [31738, 62], [32039, 61], [32340, 60], [32641, 59], [32941, 59], [33242, 58], [33543, 57], [33844, 56], [34145, 55], [34446, 54], [34746, 54], [35047, 53], [35348, 52], [35649, 51], [35950, 50], [36251, 49], [36551, 49], [36852, 48], [37153, 47], [37454, 46], [37755, 45], [38056, 44], [38356, 44], [38657, 43], [38958, 42], [39259, 41], [39560, 40], [39861, 39], [40161, 39], [40462, 38], [40763, 37], [41075, 25], [41376, 24], [41683, 8], [41983, 7], [42283, 7], [42583, 7], [42886, 3]], "point": [261, 84]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.83|+01.16|+00.66", "placeStationary": true, "receptacleObjectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [146, 27, 299, 143], "mask": [[7946, 95], [8042, 58], [8246, 154], [8546, 154], [8846, 154], [9146, 154], [9446, 154], [9746, 154], [10046, 154], [10346, 154], [10646, 154], [10946, 154], [11246, 154], [11546, 154], [11846, 154], [12146, 154], [12446, 154], [12746, 154], [13046, 154], [13346, 154], [13646, 154], [13946, 154], [14246, 154], [14546, 154], [14846, 154], [15146, 154], [15446, 154], [15746, 154], [16046, 154], [16346, 154], [16646, 154], [16946, 154], [17246, 154], [17546, 154], [17846, 154], [18146, 154], [18446, 154], [18746, 154], [19046, 154], [19346, 154], [19646, 154], [19946, 154], [20246, 154], [20546, 154], [20846, 154], [21146, 154], [21446, 154], [21746, 154], [22046, 154], [22346, 154], [22646, 154], [22946, 154], [23246, 154], [23546, 154], [23846, 154], [24146, 154], [24446, 154], [24746, 154], [25046, 154], [25346, 154], [25646, 154], [25946, 154], [26246, 154], [26631, 69], [26932, 68], [27233, 67], [27534, 66], [27835, 65], [28136, 64], [28437, 63], [28738, 62], [29039, 61], [29339, 61], [29640, 60], [29941, 59], [30242, 58], [30543, 57], [30844, 56], [31145, 55], [31446, 54], [31747, 53], [32048, 52], [32349, 51], [32650, 50], [32950, 50], [33251, 49], [33552, 48], [33853, 47], [34154, 46], [34454, 46], [34754, 46], [35053, 47], [35353, 47], [35653, 47], [35953, 47], [36253, 47], [36552, 48], [36852, 48], [37153, 47], [37454, 46], [37755, 45], [38056, 44], [38356, 44], [38657, 43], [38958, 42], [39259, 41], [39560, 40], [39861, 39], [40161, 39], [40462, 38], [40763, 37], [41075, 25], [41376, 24], [41683, 8], [41983, 7], [42283, 7], [42583, 7], [42886, 3]], "point": [222, 84]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [146, 27, 299, 143], "mask": [[7946, 95], [8042, 58], [8246, 154], [8546, 154], [8846, 154], [9146, 154], [9446, 154], [9746, 154], [10046, 154], [10346, 154], [10646, 154], [10946, 154], [11246, 154], [11546, 154], [11846, 154], [12146, 154], [12446, 154], [12746, 154], [13046, 154], [13346, 154], [13646, 154], [13946, 154], [14246, 154], [14546, 154], [14846, 154], [15146, 154], [15446, 154], [15746, 154], [16046, 154], [16346, 154], [16646, 154], [16946, 154], [17246, 154], [17546, 154], [17846, 154], [18146, 154], [18446, 154], [18746, 154], [19046, 154], [19346, 154], [19646, 154], [19946, 154], [20246, 154], [20546, 154], [20846, 154], [21146, 106], [21257, 43], [21446, 104], [21558, 42], [21746, 104], [21858, 42], [22046, 103], [22159, 41], [22346, 102], [22459, 41], [22646, 102], [22759, 41], [22946, 102], [23059, 41], [23246, 101], [23359, 41], [23546, 101], [23658, 42], [23846, 101], [23958, 42], [24146, 102], [24258, 42], [24446, 102], [24557, 43], [24746, 103], [24856, 44], [25046, 104], [25155, 45], [25346, 154], [25646, 154], [25946, 154], [26246, 154], [26631, 69], [26932, 68], [27233, 67], [27534, 66], [27835, 65], [28136, 64], [28437, 63], [28738, 62], [29039, 61], [29339, 61], [29640, 60], [29941, 59], [30242, 58], [30543, 57], [30844, 56], [31145, 55], [31446, 54], [31747, 53], [32048, 52], [32349, 51], [32650, 50], [32950, 50], [33251, 49], [33552, 48], [33853, 47], [34154, 46], [34454, 46], [34754, 46], [35053, 47], [35353, 47], [35653, 47], [35953, 47], [36253, 47], [36552, 48], [36852, 48], [37153, 47], [37454, 46], [37755, 45], [38056, 44], [38356, 44], [38657, 43], [38958, 42], [39259, 41], [39560, 40], [39861, 39], [40161, 39], [40462, 38], [40763, 37], [41075, 25], [41376, 24], [41683, 8], [41983, 7], [42283, 7], [42583, 7], [42886, 3]], "point": [222, 84]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-1.468, -1.468, 1.72, 1.72, 4.4524064, 4.4524064]], "forceVisible": true, "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [223, 27, 299, 143], "mask": [[8042, 58], [8331, 69], [8631, 69], [8931, 69], [9231, 69], [9531, 69], [9831, 69], [10130, 70], [10430, 70], [10730, 70], [11030, 70], [11330, 70], [11630, 70], [11930, 70], [12230, 70], [12529, 71], [12829, 71], [13129, 71], [13429, 71], [13729, 71], [14029, 71], [14329, 71], [14628, 72], [14928, 72], [15228, 72], [15528, 72], [15828, 72], [16128, 72], [16428, 72], [16727, 73], [17027, 73], [17327, 73], [17627, 73], [17927, 73], [18227, 73], [18527, 73], [18826, 74], [19126, 74], [19426, 74], [19726, 74], [20026, 74], [20326, 74], [20626, 74], [20925, 75], [21225, 75], [21525, 75], [21825, 75], [22125, 75], [22425, 75], [22725, 75], [23024, 76], [23324, 76], [23624, 76], [23924, 76], [24224, 76], [24524, 76], [24824, 76], [25123, 77], [25423, 77], [25723, 77], [26023, 77], [26323, 77], [26624, 76], [26925, 75], [27225, 75], [27526, 74], [27827, 73], [28128, 72], [28429, 71], [28730, 70], [29030, 70], [29331, 69], [29632, 68], [29933, 67], [30234, 66], [30535, 65], [30835, 65], [31136, 64], [31437, 63], [31738, 62], [32039, 61], [32340, 60], [32641, 59], [32941, 59], [33242, 58], [33543, 57], [33844, 56], [34145, 55], [34446, 54], [34746, 54], [35047, 53], [35348, 52], [35649, 51], [35950, 50], [36251, 49], [36551, 49], [36852, 48], [37153, 47], [37454, 46], [37755, 45], [38056, 44], [38356, 44], [38657, 43], [38958, 42], [39259, 41], [39560, 40], [39861, 39], [40161, 39], [40462, 38], [40763, 37], [41075, 25], [41376, 24], [41683, 8], [41983, 7], [42283, 7], [42583, 7], [42886, 3]], "point": [261, 84]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-1.468, -1.468, 1.72, 1.72, 4.4524064, 4.4524064]], "forceVisible": true, "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [223, 27, 299, 143], "mask": [[8042, 58], [8331, 69], [8631, 69], [8931, 69], [9231, 69], [9531, 69], [9831, 69], [10130, 70], [10430, 70], [10730, 70], [11030, 70], [11330, 70], [11630, 70], [11930, 70], [12230, 70], [12529, 71], [12829, 71], [13129, 71], [13429, 71], [13729, 71], [14029, 71], [14329, 71], [14628, 72], [14928, 72], [15228, 72], [15528, 72], [15828, 72], [16128, 72], [16428, 72], [16727, 73], [17027, 73], [17327, 73], [17627, 73], [17927, 73], [18227, 73], [18527, 73], [18826, 74], [19126, 74], [19426, 74], [19726, 74], [20026, 74], [20326, 74], [20626, 74], [20925, 75], [21225, 75], [21525, 75], [21825, 75], [22125, 75], [22425, 75], [22725, 75], [23024, 76], [23324, 76], [23624, 76], [23924, 76], [24224, 76], [24524, 76], [24824, 76], [25123, 77], [25423, 77], [25723, 77], [26023, 77], [26323, 77], [26624, 76], [26925, 75], [27225, 75], [27526, 74], [27827, 73], [28128, 72], [28429, 71], [28730, 70], [29030, 70], [29331, 69], [29632, 68], [29933, 67], [30234, 66], [30535, 65], [30835, 65], [31136, 64], [31437, 63], [31738, 62], [32039, 61], [32340, 60], [32641, 59], [32941, 59], [33242, 58], [33543, 57], [33844, 56], [34145, 55], [34446, 54], [34746, 54], [35047, 53], [35348, 52], [35649, 51], [35950, 50], [36251, 49], [36551, 49], [36852, 48], [37153, 47], [37454, 46], [37755, 45], [38056, 44], [38356, 44], [38657, 43], [38958, 42], [39259, 41], [39560, 40], [39861, 39], [40161, 39], [40462, 38], [40763, 37], [41075, 25], [41376, 24], [41683, 8], [41983, 7], [42283, 7], [42583, 7], [42886, 3]], "point": [261, 84]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [223, 27, 299, 143], "mask": [[8042, 58], [8331, 69], [8631, 69], [8931, 69], [9231, 69], [9531, 69], [9831, 69], [10130, 70], [10430, 70], [10730, 70], [11030, 70], [11330, 70], [11630, 70], [11930, 70], [12230, 70], [12529, 71], [12829, 71], [13129, 71], [13429, 71], [13729, 71], [14029, 71], [14329, 71], [14628, 72], [14928, 72], [15228, 72], [15528, 72], [15828, 72], [16128, 72], [16428, 72], [16727, 73], [17027, 73], [17327, 73], [17627, 73], [17927, 73], [18227, 73], [18527, 73], [18826, 74], [19126, 74], [19426, 74], [19726, 74], [20026, 74], [20326, 74], [20626, 74], [20925, 75], [21225, 75], [21525, 75], [21825, 75], [22125, 75], [22425, 75], [22725, 75], [23024, 76], [23324, 76], [23624, 76], [23924, 76], [24224, 76], [24524, 76], [24824, 76], [25123, 77], [25423, 77], [25723, 77], [26023, 77], [26323, 77], [26624, 76], [26925, 75], [27225, 75], [27526, 74], [27827, 73], [28128, 72], [28429, 71], [28730, 70], [29030, 70], [29331, 69], [29632, 68], [29933, 67], [30234, 66], [30535, 65], [30835, 65], [31136, 64], [31437, 63], [31738, 62], [32039, 61], [32340, 60], [32641, 59], [32941, 59], [33242, 58], [33543, 57], [33844, 56], [34145, 55], [34446, 54], [34746, 54], [35047, 53], [35348, 52], [35649, 51], [35950, 50], [36251, 49], [36551, 49], [36852, 48], [37153, 47], [37454, 46], [37755, 45], [38056, 44], [38356, 44], [38657, 43], [38958, 42], [39259, 41], [39560, 40], [39861, 39], [40161, 39], [40462, 38], [40763, 37], [41075, 25], [41376, 24], [41683, 8], [41983, 7], [42283, 7], [42583, 7], [42886, 3]], "point": [261, 84]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.83|+01.16|+00.66"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [247, 71, 258, 84], "mask": [[21252, 5], [21550, 8], [21850, 8], [22149, 10], [22448, 11], [22748, 11], [23048, 11], [23347, 12], [23647, 11], [23947, 11], [24248, 10], [24548, 9], [24849, 7], [25150, 5]], "point": [252, 76]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.37|+01.11|+00.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [146, 27, 299, 143], "mask": [[7946, 95], [8042, 58], [8246, 154], [8546, 154], [8846, 154], [9146, 154], [9446, 154], [9746, 154], [10046, 154], [10346, 154], [10646, 154], [10946, 154], [11246, 154], [11546, 154], [11846, 154], [12146, 154], [12446, 154], [12746, 154], [13046, 154], [13346, 154], [13646, 154], [13946, 154], [14246, 154], [14546, 154], [14846, 154], [15146, 154], [15446, 154], [15746, 154], [16046, 154], [16346, 154], [16646, 154], [16946, 154], [17246, 154], [17546, 154], [17846, 154], [18146, 154], [18446, 154], [18746, 154], [19046, 154], [19346, 154], [19646, 154], [19946, 154], [20246, 154], [20546, 154], [20846, 154], [21146, 154], [21446, 154], [21746, 154], [22046, 154], [22346, 154], [22646, 154], [22946, 154], [23246, 154], [23546, 154], [23846, 154], [24146, 154], [24446, 154], [24746, 154], [25046, 154], [25346, 154], [25646, 154], [25946, 154], [26246, 154], [26631, 69], [26932, 68], [27233, 67], [27534, 66], [27835, 65], [28136, 64], [28437, 63], [28738, 62], [29039, 61], [29339, 61], [29640, 60], [29941, 59], [30242, 58], [30543, 57], [30844, 56], [31145, 55], [31446, 54], [31747, 53], [32048, 52], [32349, 51], [32650, 50], [32950, 50], [33251, 49], [33552, 48], [33853, 47], [34154, 46], [34454, 46], [34754, 46], [35053, 47], [35353, 47], [35653, 47], [35953, 47], [36253, 47], [36552, 48], [36852, 48], [37153, 47], [37454, 46], [37755, 45], [38056, 44], [38356, 44], [38657, 43], [38958, 42], [39259, 41], [39560, 40], [39861, 39], [40161, 39], [40462, 38], [40763, 37], [41075, 25], [41376, 24], [41683, 8], [41983, 7], [42283, 7], [42583, 7], [42886, 3]], "point": [222, 84]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.83|+01.16|+00.66", "placeStationary": true, "receptacleObjectId": "DiningTable|-00.62|+00.02|+02.49"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 217], "mask": [[14, 33], [134, 27], [215, 32], [314, 33], [434, 27], [515, 33], [614, 34], [734, 27], [779, 21], [815, 35], [913, 35], [1034, 27], [1078, 22], [1116, 36], [1199, 1], [1213, 35], [1334, 27], [1378, 22], [1416, 39], [1497, 3], [1512, 37], [1634, 27], [1678, 22], [1716, 41], [1795, 5], [1812, 37], [1934, 27], [1977, 24], [2016, 44], [2093, 7], [2111, 38], [2234, 27], [2277, 24], [2316, 48], [2390, 10], [2411, 39], [2533, 28], [2577, 24], [2616, 54], [2685, 15], [2711, 40], [2832, 29], [2876, 25], [2916, 84], [3010, 43], [3132, 168], [3310, 44], [3432, 5], [3439, 161], [3609, 46], [3732, 4], [3740, 160], [3909, 46], [4032, 4], [4040, 160], [4209, 47], [4331, 5], [4341, 159], [4508, 49], [4629, 7], [4641, 159], [4808, 49], [4928, 8], [4942, 158], [5107, 49], [5227, 9], [5242, 158], [5407, 49], [5525, 11], [5542, 158], [5707, 49], [5824, 12], [5843, 157], [6006, 50], [6122, 14], [6143, 157], [6306, 50], [6420, 16], [6443, 157], [6605, 51], [6718, 18], [6743, 157], [6905, 51], [7016, 19], [7043, 157], [7204, 52], [7313, 22], [7343, 157], [7504, 52], [7611, 24], [7642, 158], [7804, 52], [7907, 28], [7942, 158], [8103, 53], [8201, 34], [8242, 158], [8403, 53], [8498, 37], [8542, 158], [8702, 55], [8798, 37], [8842, 158], [9002, 55], [9098, 37], [9141, 159], [9302, 56], [9398, 37], [9441, 68], [9510, 90], [9601, 57], [9698, 37], [9741, 67], [9811, 89], [9901, 58], [9998, 37], [10040, 68], [10111, 149], [10298, 37], [10340, 67], [10411, 150], [10598, 37], [10640, 67], [10711, 150], [10897, 38], [10940, 67], [11012, 150], [11197, 38], [11239, 68], [11312, 151], [11497, 38], [11539, 68], [11612, 152], [11796, 39], [11839, 68], [11912, 153], [12096, 39], [12139, 69], [12213, 153], [12395, 40], [12439, 69], [12513, 154], [12695, 39], [12739, 69], [12813, 156], [12994, 40], [13038, 71], [13113, 158], [13293, 41], [13338, 71], [13414, 158], [13592, 42], [13638, 72], [13714, 160], [13890, 44], [13938, 72], [14014, 162], [14189, 45], [14239, 71], [14314, 165], [14485, 49], [14539, 71], [14615, 218], [14839, 71], [14915, 218], [15139, 72], [15215, 218], [15439, 72], [15516, 217], [15739, 72], [15816, 217], [16039, 73], [16116, 216], [16339, 73], [16416, 216], [16639, 73], [16717, 215], [16939, 73], [17017, 215], [17239, 74], [17317, 215], [17539, 74], [17617, 215], [17839, 74], [17917, 215], [18139, 73], [18217, 215], [18439, 73], [18518, 214], [18739, 73], [18818, 214], [19039, 73], [19118, 148], [19271, 61], [19339, 73], [19418, 146], [19572, 60], [19638, 74], [19719, 144], [19873, 60], [19938, 74], [20019, 143], [20173, 139], [20319, 142], [20473, 139], [20619, 142], [20773, 139], [20920, 70], [20991, 1], [20993, 2], [20996, 2], [20999, 64], [21072, 141], [21220, 70], [21291, 1], [21293, 2], [21296, 2], [21299, 65], [21372, 141], [21520, 70], [21591, 1], [21594, 1], [21596, 2], [21599, 65], [21672, 141], [21820, 70], [21891, 1], [21894, 1], [21896, 2], [21899, 65], [21972, 141], [22121, 69], [22191, 2], [22194, 1], [22197, 1], [22199, 65], [22272, 142], [22421, 69], [22491, 2], [22494, 1], [22497, 1], [22500, 64], [22572, 142], [22721, 69], [22791, 2], [22794, 2], [22797, 1], [22800, 63], [22872, 142], [23021, 69], [23091, 2], [23094, 2], [23097, 2], [23100, 63], [23171, 144], [23321, 69], [23392, 1], [23394, 2], [23397, 2], [23400, 63], [23471, 144], [23622, 68], [23692, 1], [23695, 1], [23698, 1], [23700, 63], [23771, 144], [23922, 68], [23992, 1], [23995, 1], [23998, 1], [24000, 62], [24071, 145], [24222, 68], [24292, 1], [24295, 1], [24298, 1], [24300, 62], [24371, 145], [24522, 68], [24592, 1], [24595, 1], [24598, 64], [24671, 145], [24823, 68], [24892, 2], [24895, 2], [24898, 64], [24971, 145], [25123, 68], [25192, 2], [25196, 1], [25199, 63], [25271, 146], [25423, 68], [25493, 1], [25496, 1], [25499, 63], [25570, 147], [25723, 68], [25800, 62], [25870, 147], [26024, 67], [26100, 62], [26170, 148], [26324, 67], [26400, 62], [26470, 148], [26624, 68], [26700, 62], [26770, 148], [26924, 68], [27000, 62], [27070, 149], [27224, 69], [27300, 62], [27370, 149], [27525, 69], [27600, 62], [27670, 149], [27825, 71], [27900, 62], [27970, 150], [28125, 73], [28200, 62], [28270, 150], [28425, 137], [28569, 151], [28725, 137], [28869, 152], [29025, 134], [29169, 152], [29326, 127], [29469, 152], [29626, 126], [29769, 153], [29926, 126], [30069, 153], [30226, 126], [30369, 154], [30526, 126], [30669, 154], [30826, 126], [30969, 154], [31126, 125], [31268, 156], [31426, 125], [31568, 156], [31726, 125], [31868, 157], [32026, 125], [32168, 283], [32468, 283], [32768, 283], [33068, 283], [33368, 283], [33667, 284], [33967, 284], [34267, 284], [34567, 284], [34867, 284], [35167, 284], [35467, 284], [35767, 284], [36067, 37], [36161, 190], [36366, 38], [36461, 190], [36666, 38], [36761, 190], [36966, 37], [37061, 190], [37266, 37], [37361, 190], [37566, 37], [37661, 190], [37866, 37], [37961, 190], [38166, 37], [38261, 190], [38466, 37], [38561, 190], [38765, 38], [38861, 190], [39065, 38], [39162, 189], [39365, 38], [39462, 189], [39665, 37], [39762, 189], [39965, 37], [40062, 189], [40265, 37], [40362, 189], [40565, 37], [40662, 189], [40864, 38], [40962, 189], [41164, 38], [41262, 189], [41464, 38], [41562, 189], [41764, 38], [41862, 189], [42064, 38], [42162, 189], [42364, 38], [42462, 190], [42663, 38], [42762, 190], [42963, 38], [43062, 190], [43263, 39], [43362, 190], [43563, 39], [43662, 190], [43863, 39], [43962, 190], [44162, 40], [44262, 190], [44462, 41], [44561, 191], [44762, 42], [44860, 192], [45062, 290], [45361, 292], [45661, 292], [45961, 292], [46260, 293], [46560, 293], [46859, 295], [47159, 295], [47458, 296], [47758, 297], [48057, 298], [48357, 298], [48656, 8173], [56835, 267], [57167, 197], [57388, 15], [57500, 131], [57689, 14], [57832, 68], [57989, 15], [58165, 35], [58290, 14], [58497, 3], [58591, 13], [58891, 14], [59192, 13], [59492, 14], [59793, 13], [60093, 14], [60394, 13], [60695, 13], [60995, 13], [61296, 12], [61596, 13], [61897, 12], [62197, 13], [62498, 12], [62799, 12], [63099, 12], [63400, 10], [63700, 10], [64001, 9], [64301, 9], [64602, 8], [64903, 6]], "point": [149, 108]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan4", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -1.5, "y": 0.9009999, "z": 2.5}, "object_poses": [{"objectName": "Potato_4679e4a5", "position": {"x": -3.54231119, "y": 0.142974779, "z": 2.068726}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_4679e4a5", "position": {"x": -1.11113727, "y": 0.962041557, "z": 0.290272}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_f4537974", "position": {"x": -2.51405215, "y": 1.16406763, "z": 0.379}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_130c30e2", "position": {"x": -0.908643544, "y": 1.04780233, "z": 3.04208541}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8393cb4d", "position": {"x": -0.827757657, "y": 1.12494218, "z": 0.47828263}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9dba57b8", "position": {"x": -3.851097, "y": 1.12602949, "z": 0.284664035}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_0271e4d1", "position": {"x": -1.19964325, "y": 0.946556032, "z": 0.290272}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Egg_7b2f7cfb", "position": {"x": -3.59356833, "y": 0.142692849, "z": 1.97881007}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7b1a480e", "position": {"x": -0.168970883, "y": 1.04515457, "z": 2.14499116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7b1a480e", "position": {"x": -0.415176868, "y": 1.22656143, "z": 0.475896239}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ca5fe19d", "position": {"x": -0.662086, "y": 1.04566681, "z": 1.9207176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ca5fe19d", "position": {"x": -1.601334, "y": 0.0478528142, "z": 0.551842153}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e6e9017d", "position": {"x": -0.785364747, "y": 1.07958472, "z": 3.26635885}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e6e9017d", "position": {"x": -0.7158578, "y": 1.15733731, "z": 0.296814859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_1473e57e", "position": {"x": -3.50640631, "y": 0.952445745, "z": 3.061901}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_1473e57e", "position": {"x": -3.5728128, "y": 0.952445745, "z": 2.92009878}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_9496ee5e", "position": {"x": -3.50640655, "y": 1.55805814, "z": 2.84919739}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_9496ee5e", "position": {"x": -0.785364747, "y": 1.04583609, "z": 2.14499116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_e61894bc", "position": {"x": -2.38837767, "y": 1.12120628, "z": 0.284664035}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_e61894bc", "position": {"x": -2.63972664, "y": 1.12120628, "z": 0.09599209}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7c6826ef", "position": {"x": -2.63972664, "y": 1.20227718, "z": 0.567671955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_f6bdaee7", "position": {"x": -0.662086, "y": 1.11561787, "z": 3.26635885}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_f6bdaee7", "position": {"x": -3.79859781, "y": 0.179869235, "z": 2.03875422}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1f60da4f", "position": {"x": -0.785364747, "y": 1.047696, "z": 2.59353828}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_58fe0a5f", "position": {"x": -1.94843364, "y": 0.0954267457, "z": 0.613230169}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_ab8a6692", "position": {"x": -0.415528417, "y": 1.05156243, "z": 2.14499116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_f4537974", "position": {"x": -1.55366719, "y": 0.9707237, "z": 0.498063982}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Potato_4679e4a5", "position": {"x": -0.7158578, "y": 1.15538549, "z": 0.115347147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ca5fe19d", "position": {"x": -0.908643544, "y": 1.04675734, "z": 1.9207176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_d5b8c410", "position": {"x": -2.38837767, "y": 1.21164966, "z": 0.567671955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7c6826ef", "position": {"x": -3.44, "y": 1.36413729, "z": 3.061901}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_0271e4d1", "position": {"x": -3.742703, "y": 1.1399, "z": 0.09599209}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_447fa38c", "position": {"x": -0.266, "y": 1.038, "z": 2.561}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7b2f7cfb", "position": {"x": -0.827757657, "y": 1.15510356, "z": 0.659750342}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7b1a480e", "position": {"x": -1.28814924, "y": 0.9295632, "z": 0.359536}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_e61894bc", "position": {"x": -3.851097, "y": 1.12120628, "z": 0.6620079}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e6e9017d", "position": {"x": -0.662086, "y": 1.08067524, "z": 2.817812}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9496ee5e", "position": {"x": -3.30718732, "y": 1.55805814, "z": 3.13280272}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_4cc43937", "position": {"x": -2.5555687, "y": 0.8983304, "z": 0.645984054}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8393cb4d", "position": {"x": -0.5388072, "y": 1.04718959, "z": 3.04208541}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_f6bdaee7", "position": {"x": -2.13702822, "y": 1.19227993, "z": 0.6620079}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_1473e57e", "position": {"x": -2.13702822, "y": 1.16548562, "z": 0.379}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_130c30e2", "position": {"x": -1.64217317, "y": 0.931120455, "z": 0.567328}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9dba57b8", "position": {"x": -2.04273033, "y": 0.48917672, "z": 0.613230169}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_ff353859", "position": {"x": -1.55366719, "y": 0.92655617, "z": 0.290272}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}], "object_toggles": [], "random_seed": 3403136405, "scene_num": 4}, "task_id": "trial_T20190908_080003_104562", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A36DK84J5YJ942_3GDTJDAPVXS4464M8E60A3AK0JZM8Q", "high_descs": ["move to the counter to the left of the sink", "pick up a egg from the sink", "move to the left of the sink", "heat the egg in the microwave", "move to the table to the left of you", "put the egg on the table"], "task_desc": "Put a heated egg on a table.", "votes": [1, 1, 1]}, {"assignment_id": "AKW57KYG90X61_3Y9N9SS8L1SFZFS3J4C10WFFBJ4D3W", "high_descs": ["turn right and move to the table", "pick up the egg from the table", "turn left to the oven", "cook the egg in the oven and take it out", "turn around and head to the table", "place the egg on the table"], "task_desc": "place a cooked egg on the table", "votes": [1, 1, 1]}, {"assignment_id": "A255A9FFZD8PQW_3YW4XOSQKT256EPAIVO7M346ZZSU12", "high_descs": ["Turn to the right and walk to the counter. Make a slight left then right again to face the counter. ", "Pick up the egg off of the counter. ", "Turn around to the right.", "Open the microwave and put the egg inside. Heat the egg and take it out. ", "Turn to the left and walk forward to the table. Turn to the left and face the table.", "Place the egg on the table. "], "task_desc": "Heat an egg in the microwave. ", "votes": [1, 0, 1]}]}}