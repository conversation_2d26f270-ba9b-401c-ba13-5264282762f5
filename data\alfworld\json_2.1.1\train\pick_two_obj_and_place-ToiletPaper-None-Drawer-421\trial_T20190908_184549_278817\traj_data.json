{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000041.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000042.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000043.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000044.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000045.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000149.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000150.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000151.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000152.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000153.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000157.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000158.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000159.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000211.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000212.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000213.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000214.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000215.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000216.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000217.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000218.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000219.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000221.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 32}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "ToiletPaper", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|7|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-1.158190252, -1.158190252, 5.38130952, 5.38130952, 3.236, 3.236]], "coordinateReceptacleObjectId": ["CounterTop", [-1.4, -1.4, 5.814656, 5.814656, 0.02, 0.02]], "forceVisible": true, "objectId": "ToiletPaper|-00.29|+00.81|+01.35"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|11|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-1.158190252, -1.158190252, 5.38130952, 5.38130952, 3.236, 3.236]], "coordinateReceptacleObjectId": ["Drawer", [-1.2763008, -1.2763008, 12.05185508, 12.05185508, 2.4631992, 2.4631992]], "forceVisible": true, "objectId": "ToiletPaper|-00.29|+00.81|+01.35", "receptacleObjectId": "Drawer|-00.32|+00.62|+03.01"}}, {"discrete_action": {"action": "GotoLocation", "args": ["toilet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-4|13|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-0.754062176, -0.754062176, 15.135988, 15.135988, 3.800782, 3.800782]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-2.4, -2.4, 14.684, 14.684, -0.000692814588, -0.000692814588]], "forceVisible": true, "objectId": "ToiletPaper|-00.19|+00.95|+03.78"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-5|11|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-0.754062176, -0.754062176, 15.135988, 15.135988, 3.800782, 3.800782]], "coordinateReceptacleObjectId": ["Drawer", [-1.2763008, -1.2763008, 12.05185508, 12.05185508, 2.4631992, 2.4631992]], "forceVisible": true, "objectId": "ToiletPaper|-00.19|+00.95|+03.78", "receptacleObjectId": "Drawer|-00.32|+00.62|+03.01"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-00.29|+00.81|+01.35"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [246, 136, 263, 163], "mask": [[40756, 5], [41054, 9], [41354, 9], [41654, 10], [41953, 11], [42253, 11], [42553, 10], [42852, 11], [43152, 10], [43452, 10], [43751, 11], [44051, 10], [44350, 11], [44650, 10], [44950, 10], [45249, 10], [45549, 10], [45849, 10], [46148, 10], [46448, 10], [46747, 10], [47047, 10], [47347, 10], [47646, 10], [47946, 10], [48246, 9], [48547, 8], [48849, 5]], "point": [254, 148]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-00.32|+00.62|+03.01"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [24, 171, 147, 210], "mask": [[51025, 123], [51324, 124], [51625, 123], [51925, 123], [52226, 122], [52526, 122], [52827, 121], [53127, 121], [53428, 120], [53728, 120], [54029, 119], [54329, 119], [54630, 118], [54930, 118], [55231, 117], [55531, 117], [55832, 116], [56133, 115], [56433, 115], [56734, 114], [57034, 114], [57335, 113], [57635, 113], [57936, 112], [58236, 112], [58537, 111], [58837, 111], [59138, 110], [59438, 110], [59739, 109], [60039, 109], [60340, 108], [60640, 108], [60941, 104], [61241, 101], [61542, 99], [61843, 97], [62143, 96], [62444, 94], [62744, 92]], "point": [85, 189]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-00.29|+00.81|+01.35", "placeStationary": true, "receptacleObjectId": "Drawer|-00.32|+00.62|+03.01"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 171, 147, 286], "mask": [[51032, 112], [51331, 113], [51631, 113], [51931, 113], [52230, 114], [52530, 114], [52829, 115], [53129, 115], [53429, 115], [53728, 116], [54028, 116], [54327, 117], [54627, 117], [54926, 118], [55226, 118], [55526, 118], [55825, 119], [56125, 119], [56424, 120], [56724, 120], [57023, 121], [57323, 121], [57623, 121], [57922, 122], [58222, 122], [58521, 123], [58821, 123], [59121, 123], [59420, 124], [59720, 124], [60019, 125], [60319, 125], [60618, 126], [60918, 126], [61218, 124], [61517, 124], [61817, 123], [62116, 123], [62416, 122], [62715, 121], [63015, 121], [63315, 121], [63614, 122], [63914, 121], [64213, 122], [64513, 122], [64812, 123], [65112, 123], [65412, 123], [65711, 124], [66011, 124], [66310, 125], [66610, 125], [66910, 125], [67209, 126], [67509, 126], [67808, 127], [68108, 127], [68407, 129], [68707, 129], [69007, 129], [69306, 130], [69606, 130], [69905, 131], [70205, 131], [70504, 132], [70804, 132], [71104, 132], [71403, 133], [71703, 133], [72002, 134], [72302, 135], [72602, 135], [72901, 136], [73201, 136], [73500, 137], [73800, 137], [74100, 137], [74400, 137], [74700, 137], [75000, 137], [75300, 137], [75600, 137], [75900, 138], [76200, 138], [76500, 138], [76800, 138], [77100, 138], [77400, 138], [77700, 138], [78000, 138], [78300, 138], [78600, 139], [78900, 139], [79200, 140], [79500, 140], [79800, 141], [80100, 141], [80400, 142], [80701, 143], [81002, 144], [81303, 145], [81604, 144], [81905, 143], [82206, 142], [82507, 141], [82808, 140], [83109, 139], [83410, 138], [83711, 137], [84012, 136], [84313, 135], [84615, 133], [84916, 132], [85217, 131], [85518, 130]], "point": [73, 227]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-00.32|+00.62|+03.01"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 171, 147, 286], "mask": [[51032, 112], [51331, 113], [51631, 113], [51931, 113], [52230, 39], [52272, 72], [52530, 37], [52573, 71], [52829, 37], [52874, 70], [53129, 37], [53174, 70], [53429, 37], [53474, 70], [53728, 38], [53775, 69], [54028, 39], [54075, 69], [54327, 40], [54375, 69], [54627, 41], [54676, 68], [54926, 42], [54976, 68], [55226, 42], [55276, 68], [55526, 43], [55577, 67], [55825, 44], [55877, 67], [56125, 44], [56177, 67], [56424, 46], [56478, 66], [56724, 46], [56778, 66], [57023, 47], [57078, 66], [57323, 48], [57378, 66], [57623, 48], [57678, 66], [57922, 50], [57978, 66], [58222, 50], [58277, 67], [58521, 123], [58821, 123], [59121, 123], [59420, 124], [59720, 124], [60019, 125], [60319, 125], [60618, 126], [60918, 126], [61218, 126], [61517, 127], [61817, 127], [62116, 128], [62416, 128], [62715, 129], [63015, 129], [63315, 129], [63614, 130], [63914, 130], [64213, 131], [64513, 131], [64812, 132], [65112, 132], [65412, 132], [65711, 133], [66011, 133], [66310, 134], [66610, 134], [66910, 134], [67209, 135], [67509, 135], [67808, 136], [68108, 136], [68407, 137], [68707, 137], [69007, 137], [69306, 137], [69606, 137], [69905, 138], [70205, 138], [70504, 139], [70804, 139], [71104, 139], [71403, 140], [71703, 140], [72002, 141], [72302, 141], [72602, 141], [72901, 142], [73201, 142], [73500, 143], [73800, 143], [74100, 143], [74400, 143], [74700, 147], [75000, 147], [75300, 147], [75600, 147], [75900, 147], [76200, 147], [76500, 147], [76800, 147], [77100, 147], [77400, 147], [77700, 147], [78000, 147], [78300, 147], [78600, 147], [78900, 147], [79200, 147], [79500, 147], [79800, 147], [80100, 147], [80400, 147], [80701, 146], [81002, 146], [81303, 145], [81604, 144], [81905, 143], [82206, 142], [82507, 141], [82808, 140], [83109, 139], [83410, 138], [83711, 137], [84012, 136], [84313, 135], [84615, 133], [84916, 132], [85217, 131], [85518, 130]], "point": [73, 227]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-00.19|+00.95|+03.78"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [0, 89, 17, 120], "mask": [[26400, 6], [26700, 7], [27000, 7], [27300, 8], [27600, 8], [27900, 8], [28200, 9], [28500, 9], [28800, 10], [29100, 10], [29400, 11], [29700, 11], [30000, 12], [30301, 11], [30601, 11], [30902, 11], [31202, 11], [31503, 11], [31803, 11], [32104, 11], [32404, 11], [32705, 11], [33005, 11], [33306, 11], [33606, 11], [33907, 10], [34207, 11], [34508, 10], [34808, 10], [35109, 9], [35409, 7], [35711, 2]], "point": [8, 103]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-00.32|+00.62|+03.01"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [24, 171, 147, 210], "mask": [[51025, 123], [51324, 124], [51625, 123], [51925, 123], [52226, 122], [52526, 122], [52827, 121], [53127, 121], [53428, 120], [53728, 120], [54029, 119], [54329, 119], [54630, 118], [54930, 118], [55231, 117], [55531, 117], [55832, 116], [56133, 115], [56433, 115], [56734, 114], [57034, 114], [57335, 113], [57635, 113], [57936, 112], [58236, 112], [58537, 111], [58837, 111], [59138, 110], [59438, 110], [59739, 109], [60039, 109], [60340, 108], [60640, 108], [60941, 104], [61241, 101], [61542, 99], [61843, 97], [62143, 96], [62444, 94], [62744, 92]], "point": [85, 189]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-00.19|+00.95|+03.78", "placeStationary": true, "receptacleObjectId": "Drawer|-00.32|+00.62|+03.01"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 171, 147, 286], "mask": [[51032, 112], [51331, 113], [51631, 113], [51931, 113], [52230, 39], [52272, 72], [52530, 37], [52573, 71], [52829, 37], [52874, 70], [53129, 37], [53174, 70], [53429, 37], [53474, 70], [53728, 38], [53775, 69], [54028, 39], [54075, 69], [54327, 40], [54375, 69], [54627, 41], [54676, 68], [54926, 42], [54976, 68], [55226, 42], [55276, 68], [55526, 43], [55577, 67], [55825, 44], [55877, 67], [56125, 44], [56177, 67], [56424, 46], [56478, 66], [56724, 46], [56778, 66], [57023, 47], [57078, 66], [57323, 48], [57378, 66], [57623, 48], [57678, 66], [57922, 50], [57978, 66], [58222, 50], [58277, 67], [58521, 123], [58821, 123], [59121, 123], [59420, 124], [59720, 124], [60019, 125], [60319, 125], [60618, 126], [60918, 126], [61218, 124], [61517, 124], [61817, 123], [62116, 123], [62416, 122], [62715, 121], [63015, 121], [63315, 121], [63614, 122], [63914, 121], [64213, 122], [64513, 122], [64812, 123], [65112, 123], [65412, 123], [65711, 124], [66011, 124], [66310, 125], [66610, 125], [66910, 125], [67209, 126], [67509, 126], [67808, 127], [68108, 127], [68407, 129], [68707, 129], [69007, 129], [69306, 130], [69606, 130], [69905, 131], [70205, 131], [70504, 132], [70804, 132], [71104, 132], [71403, 133], [71703, 133], [72002, 134], [72302, 135], [72602, 135], [72901, 136], [73201, 136], [73500, 137], [73800, 137], [74100, 137], [74400, 137], [74700, 137], [75000, 137], [75300, 137], [75600, 137], [75900, 138], [76200, 138], [76500, 138], [76800, 138], [77100, 138], [77400, 138], [77700, 138], [78000, 138], [78300, 138], [78600, 139], [78900, 139], [79200, 140], [79500, 140], [79800, 141], [80100, 141], [80400, 142], [80701, 143], [81002, 144], [81303, 145], [81604, 144], [81905, 143], [82206, 142], [82507, 141], [82808, 140], [83109, 139], [83410, 138], [83711, 137], [84012, 136], [84313, 135], [84615, 133], [84916, 132], [85217, 131], [85518, 130]], "point": [73, 227]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-00.32|+00.62|+03.01"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 171, 147, 286], "mask": [[51032, 112], [51331, 113], [51631, 113], [51931, 113], [52230, 39], [52272, 20], [52296, 48], [52530, 37], [52573, 18], [52597, 47], [52829, 37], [52874, 16], [52897, 47], [53129, 37], [53174, 16], [53198, 46], [53429, 37], [53474, 16], [53498, 46], [53728, 38], [53775, 15], [53798, 46], [54028, 39], [54075, 16], [54098, 46], [54327, 40], [54375, 16], [54398, 46], [54627, 41], [54676, 15], [54699, 45], [54926, 42], [54976, 15], [54999, 45], [55226, 42], [55276, 16], [55299, 45], [55526, 43], [55577, 15], [55599, 45], [55825, 44], [55877, 15], [55900, 44], [56125, 44], [56177, 15], [56200, 44], [56424, 46], [56478, 15], [56500, 44], [56724, 46], [56778, 15], [56800, 44], [57023, 47], [57078, 15], [57100, 44], [57323, 48], [57378, 15], [57401, 43], [57623, 48], [57678, 16], [57701, 43], [57922, 50], [57978, 16], [58000, 44], [58222, 50], [58277, 18], [58299, 45], [58521, 123], [58821, 123], [59121, 123], [59420, 124], [59720, 124], [60019, 125], [60319, 125], [60618, 126], [60918, 126], [61218, 126], [61517, 127], [61817, 127], [62116, 128], [62416, 128], [62715, 129], [63015, 129], [63315, 129], [63614, 130], [63914, 130], [64213, 131], [64513, 131], [64812, 132], [65112, 132], [65412, 132], [65711, 133], [66011, 133], [66310, 134], [66610, 134], [66910, 134], [67209, 135], [67509, 135], [67808, 136], [68108, 136], [68407, 137], [68707, 137], [69007, 137], [69306, 137], [69606, 137], [69905, 138], [70205, 138], [70504, 139], [70804, 139], [71104, 139], [71403, 140], [71703, 140], [72002, 141], [72302, 141], [72602, 141], [72901, 142], [73201, 142], [73500, 143], [73800, 143], [74100, 143], [74400, 143], [74700, 147], [75000, 147], [75300, 147], [75600, 147], [75900, 147], [76200, 147], [76500, 147], [76800, 147], [77100, 147], [77400, 147], [77700, 147], [78000, 147], [78300, 147], [78600, 147], [78900, 147], [79200, 147], [79500, 147], [79800, 147], [80100, 147], [80400, 147], [80701, 146], [81002, 146], [81303, 145], [81604, 144], [81905, 143], [82206, 142], [82507, 141], [82808, 140], [83109, 139], [83410, 138], [83711, 137], [84012, 136], [84313, 135], [84615, 133], [84916, 132], [85217, 131], [85518, 130]], "point": [73, 227]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan421", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.5, "y": 0.9008256, "z": 1.75}, "object_poses": [{"objectName": "SoapBar_0fd13d7a", "position": {"x": -0.268540174, "y": 0.8284542, "z": 2.753664}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBar_0fd13d7a", "position": {"x": -0.161724091, "y": 0.9532742, "z": 3.65899682}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_862832c1", "position": {"x": -0.363019, "y": 0.8299487, "z": 1.724}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_862832c1", "position": {"x": -0.2465005, "y": 0.5137005, "z": 2.38433743}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_dab0d4d7", "position": {"x": -0.289547563, "y": 0.809, "z": 1.34532738}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_dab0d4d7", "position": {"x": -0.188515544, "y": 0.9501955, "z": 3.783997}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_f6c37fb2", "position": {"x": -0.03691358, "y": 1.658, "z": 3.44300032}, "rotation": {"x": 0.0, "y": 90.0004, "z": 0.0}}, {"objectName": "SoapBar_0fd13d7a", "position": {"x": -2.146, "y": 0.0562489964, "z": 3.81893826}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ToiletPaper_335b3797", "position": {"x": -0.4820853, "y": 0.8090001, "z": 3.201858}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plunger_a5ac7169", "position": {"x": -0.139841557, "y": -0.0006361902, "z": 3.369947}, "rotation": {"x": -0.00118185522, "y": 0.0004360497, "z": 0.000781859}}, {"objectName": "Candle_1814f8bc", "position": {"x": -0.108140647, "y": 0.9532864, "z": 3.84649658}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_5484beaa", "position": {"x": -0.14460443, "y": 0.8046577, "z": 1.32575345}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_dab0d4d7", "position": {"x": -1.1566, "y": 0.897, "z": 3.92599988}, "rotation": {"x": 0.0, "y": 180.000046, "z": 90.0}}, {"objectName": "SprayBottle_55833eba", "position": {"x": -0.3537268, "y": 0.8134041, "z": 1.26032937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Towel_e5bc324c", "position": {"x": -1.106, "y": 1.151, "z": 1.28995311}, "rotation": {"x": 0.0, "y": 1.70754731e-06, "z": 0.0}}, {"objectName": "Cloth_c4a4c4ad", "position": {"x": -0.2253683, "y": 0.812304, "z": 3.201858}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ScrubBrush_274c0193", "position": {"x": -0.346180737, "y": -0.000173203647, "z": 3.34195137}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_862832c1", "position": {"x": -0.2465005, "y": 0.5137007, "z": 1.97894192}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3302789542, "scene_num": 421}, "task_id": "trial_T20190908_184549_278817", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "ARB8SJAWXUWTD_34QN5IT0T2IFMHJDFH8JDSN9RPN08X", "high_descs": ["Go to the sink", "Pick up the empty toilet roll", "Go to the left sink drawers", "Put an empty toilet roll in the drawer", "Go to the toilet", "Pick up the empty toilet roll", "Go to the left sink drawers", "Put an empty toilet roll in the drawer"], "task_desc": "Move two empty toilet rolls to a drawer", "votes": [1, 1]}, {"assignment_id": "A2A028LRDJB7ZB_3BF51CHDTY18FSK6IL2PQ5G8UAFH0Z", "high_descs": ["Turn right go to the right side of the sink counter", "Pick up the empty toilet roll on the right side of the counter", "Turn left then head to the cabinet on your left", "Open the first drawer then put in the empty toilet roll", "Move to your left  move closer to the toilet tank", "Pick up the empty toilet roll on the tank", "Turn right then head same drawer", "Put the empty toilet roll in the drawer beside the other roll"], "task_desc": "Put the two empty toilet roll in the drawer ", "votes": [1, 1]}, {"assignment_id": "A3SFPSMFRSRTB3_3SKEMFQBZ6WHZ7QHJAI1WC2I5MOK8G", "high_descs": ["Turn right, proceed to the right side sink with the green sponge in it.", "Pick up candle from counter on right side of sink, in front of pump dispenser.", "Turn left, proceed to left side sink, turn right to face sink.", "Open top left hand drawer, put candle in drawer in the back, close drawer.", "Turn left then right to move between counter and toilet.", "Pick up candle from back of toilet, left of the pink soap.", "Turn right then left to stand in front of left side sink.  ", "Open top left hand drawer, put candle in drawer in back, to right of other candle, close drawer."], "task_desc": "Move two candles to one drawer.", "votes": [1, 1]}]}}