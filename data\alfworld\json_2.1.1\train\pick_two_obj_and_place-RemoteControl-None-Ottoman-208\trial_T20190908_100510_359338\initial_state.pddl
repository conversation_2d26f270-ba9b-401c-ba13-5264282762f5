
(define (problem plan_trial_T20190908_100510_359338)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__minus_03_dot_22_bar__plus_00_dot_90_bar__minus_03_dot_39 - object
        Chair_bar__minus_02_dot_69_bar__plus_00_dot_00_bar__minus_03_dot_30 - object
        Chair_bar__minus_03_dot_13_bar__plus_00_dot_00_bar__minus_04_dot_42 - object
        Chair_bar__minus_03_dot_18_bar__plus_00_dot_00_bar__minus_02_dot_81 - object
        Chair_bar__minus_03_dot_50_bar__plus_00_dot_00_bar__minus_03_dot_72 - object
        CreditCard_bar__plus_01_dot_85_bar__plus_00_dot_88_bar__plus_00_dot_99 - object
        FloorLamp_bar__plus_01_dot_71_bar__plus_00_dot_00_bar__plus_00_dot_37 - object
        HousePlant_bar__plus_00_dot_51_bar__plus_01_dot_09_bar__plus_02_dot_24 - object
        KeyChain_bar__minus_00_dot_47_bar__plus_00_dot_43_bar__plus_00_dot_87 - object
        KeyChain_bar__minus_00_dot_54_bar__plus_00_dot_39_bar__plus_01_dot_94 - object
        KeyChain_bar__minus_02_dot_25_bar__plus_00_dot_76_bar__plus_00_dot_91 - object
        Laptop_bar__minus_00_dot_68_bar__plus_00_dot_42_bar__plus_01_dot_05 - object
        Laptop_bar__minus_03_dot_35_bar__plus_00_dot_78_bar__minus_04_dot_06 - object
        LightSwitch_bar__minus_02_dot_60_bar__plus_01_dot_26_bar__minus_00_dot_78 - object
        Painting_bar__minus_00_dot_51_bar__plus_01_dot_74_bar__plus_02_dot_40 - object
        Painting_bar__minus_05_dot_00_bar__plus_01_dot_63_bar__minus_03_dot_55 - object
        Pillow_bar__plus_00_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_54 - object
        RemoteControl_bar__plus_01_dot_77_bar__plus_00_dot_88_bar__plus_01_dot_20 - object
        RemoteControl_bar__minus_02_dot_18_bar__plus_00_dot_76_bar__plus_00_dot_61 - object
        RemoteControl_bar__minus_02_dot_25_bar__plus_00_dot_76_bar__plus_00_dot_46 - object
        Statue_bar__plus_01_dot_81_bar__plus_00_dot_88_bar__plus_00_dot_78 - object
        Statue_bar__minus_02_dot_24_bar__plus_00_dot_19_bar__plus_00_dot_83 - object
        Statue_bar__minus_03_dot_04_bar__plus_00_dot_78_bar__minus_03_dot_75 - object
        Television_bar__minus_02_dot_25_bar__plus_01_dot_24_bar__plus_00_dot_76 - object
        Vase_bar__minus_02_dot_85_bar__plus_00_dot_79_bar__minus_02_dot_95 - object
        ArmChair_bar__minus_00_dot_56_bar__plus_00_dot_00_bar__plus_01_dot_95 - receptacle
        DiningTable_bar__minus_03_dot_10_bar__plus_00_dot_00_bar__minus_03_dot_61 - receptacle
        Drawer_bar__plus_01_dot_75_bar__plus_00_dot_78_bar__plus_01_dot_09 - receptacle
        GarbageCan_bar__minus_01_dot_34_bar__plus_00_dot_00_bar__minus_04_dot_83 - receptacle
        Ottoman_bar__minus_00_dot_58_bar__plus_00_dot_01_bar__plus_01_dot_05 - receptacle
        Shelf_bar__minus_00_dot_61_bar__plus_00_dot_88_bar__minus_02_dot_83 - receptacle
        Shelf_bar__minus_00_dot_61_bar__plus_01_dot_32_bar__minus_02_dot_83 - receptacle
        Shelf_bar__minus_02_dot_08_bar__plus_00_dot_17_bar__plus_01_dot_14 - receptacle
        Shelf_bar__minus_02_dot_09_bar__plus_00_dot_17_bar__plus_00_dot_40 - receptacle
        Shelf_bar__minus_02_dot_09_bar__plus_00_dot_41_bar__plus_00_dot_40 - receptacle
        Shelf_bar__minus_02_dot_10_bar__plus_00_dot_47_bar__plus_01_dot_14 - receptacle
        Shelf_bar__minus_02_dot_13_bar__plus_00_dot_56_bar__plus_00_dot_40 - receptacle
        Shelf_bar__minus_02_dot_24_bar__plus_00_dot_17_bar__plus_00_dot_77 - receptacle
        SideTable_bar__plus_01_dot_83_bar__plus_00_dot_00_bar__plus_01_dot_09 - receptacle
        Sofa_bar__plus_00_dot_78_bar__plus_00_dot_00_bar__minus_00_dot_51 - receptacle
        TVStand_bar__minus_02_dot_24_bar__plus_00_dot_00_bar__plus_00_dot_76 - receptacle
        loc_bar__minus_8_bar__minus_3_bar_3_bar_30 - location
        loc_bar__minus_16_bar__minus_15_bar_1_bar_60 - location
        loc_bar__minus_2_bar__minus_3_bar_1_bar_45 - location
        loc_bar__minus_2_bar__minus_8_bar_2_bar_45 - location
        loc_bar__minus_4_bar_2_bar_3_bar_60 - location
        loc_bar_3_bar_6_bar_0_bar_30 - location
        loc_bar_5_bar_4_bar_1_bar_60 - location
        loc_bar_3_bar_2_bar_1_bar_60 - location
        loc_bar__minus_5_bar_8_bar_1_bar_0 - location
        loc_bar__minus_4_bar_2_bar_3_bar_45 - location
        loc_bar__minus_2_bar_2_bar_0_bar_60 - location
        loc_bar__minus_4_bar_0_bar_3_bar_60 - location
        loc_bar__minus_11_bar__minus_10_bar_3_bar_60 - location
        loc_bar__minus_9_bar__minus_17_bar_1_bar_60 - location
        loc_bar__minus_5_bar_3_bar_3_bar_60 - location
        loc_bar__minus_5_bar_8_bar_1_bar_60 - location
        loc_bar__minus_9_bar__minus_14_bar_3_bar_60 - location
        loc_bar__minus_17_bar__minus_14_bar_3_bar_0 - location
        loc_bar__minus_2_bar__minus_8_bar_2_bar_15 - location
        loc_bar__minus_7_bar_7_bar_1_bar_60 - location
        loc_bar__minus_5_bar_3_bar_3_bar_45 - location
        loc_bar_4_bar_4_bar_1_bar_45 - location
        loc_bar__minus_16_bar__minus_18_bar_1_bar_60 - location
        loc_bar_0_bar_1_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType Shelf_bar__minus_02_dot_09_bar__plus_00_dot_41_bar__plus_00_dot_40 ShelfType)
        (receptacleType GarbageCan_bar__minus_01_dot_34_bar__plus_00_dot_00_bar__minus_04_dot_83 GarbageCanType)
        (receptacleType Shelf_bar__minus_02_dot_09_bar__plus_00_dot_17_bar__plus_00_dot_40 ShelfType)
        (receptacleType Drawer_bar__plus_01_dot_75_bar__plus_00_dot_78_bar__plus_01_dot_09 DrawerType)
        (receptacleType DiningTable_bar__minus_03_dot_10_bar__plus_00_dot_00_bar__minus_03_dot_61 DiningTableType)
        (receptacleType ArmChair_bar__minus_00_dot_56_bar__plus_00_dot_00_bar__plus_01_dot_95 ArmChairType)
        (receptacleType Shelf_bar__minus_02_dot_24_bar__plus_00_dot_17_bar__plus_00_dot_77 ShelfType)
        (receptacleType Ottoman_bar__minus_00_dot_58_bar__plus_00_dot_01_bar__plus_01_dot_05 OttomanType)
        (receptacleType Shelf_bar__minus_02_dot_08_bar__plus_00_dot_17_bar__plus_01_dot_14 ShelfType)
        (receptacleType Sofa_bar__plus_00_dot_78_bar__plus_00_dot_00_bar__minus_00_dot_51 SofaType)
        (receptacleType Shelf_bar__minus_02_dot_10_bar__plus_00_dot_47_bar__plus_01_dot_14 ShelfType)
        (receptacleType Shelf_bar__minus_00_dot_61_bar__plus_00_dot_88_bar__minus_02_dot_83 ShelfType)
        (receptacleType Shelf_bar__minus_02_dot_13_bar__plus_00_dot_56_bar__plus_00_dot_40 ShelfType)
        (receptacleType TVStand_bar__minus_02_dot_24_bar__plus_00_dot_00_bar__plus_00_dot_76 TVStandType)
        (receptacleType SideTable_bar__plus_01_dot_83_bar__plus_00_dot_00_bar__plus_01_dot_09 SideTableType)
        (receptacleType Shelf_bar__minus_00_dot_61_bar__plus_01_dot_32_bar__minus_02_dot_83 ShelfType)
        (objectType Chair_bar__minus_03_dot_13_bar__plus_00_dot_00_bar__minus_04_dot_42 ChairType)
        (objectType Vase_bar__minus_02_dot_85_bar__plus_00_dot_79_bar__minus_02_dot_95 VaseType)
        (objectType Statue_bar__minus_03_dot_04_bar__plus_00_dot_78_bar__minus_03_dot_75 StatueType)
        (objectType FloorLamp_bar__plus_01_dot_71_bar__plus_00_dot_00_bar__plus_00_dot_37 FloorLampType)
        (objectType KeyChain_bar__minus_00_dot_54_bar__plus_00_dot_39_bar__plus_01_dot_94 KeyChainType)
        (objectType CreditCard_bar__plus_01_dot_85_bar__plus_00_dot_88_bar__plus_00_dot_99 CreditCardType)
        (objectType Painting_bar__minus_00_dot_51_bar__plus_01_dot_74_bar__plus_02_dot_40 PaintingType)
        (objectType LightSwitch_bar__minus_02_dot_60_bar__plus_01_dot_26_bar__minus_00_dot_78 LightSwitchType)
        (objectType KeyChain_bar__minus_00_dot_47_bar__plus_00_dot_43_bar__plus_00_dot_87 KeyChainType)
        (objectType Painting_bar__minus_05_dot_00_bar__plus_01_dot_63_bar__minus_03_dot_55 PaintingType)
        (objectType Chair_bar__minus_02_dot_69_bar__plus_00_dot_00_bar__minus_03_dot_30 ChairType)
        (objectType Pillow_bar__plus_00_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_54 PillowType)
        (objectType Statue_bar__plus_01_dot_81_bar__plus_00_dot_88_bar__plus_00_dot_78 StatueType)
        (objectType Laptop_bar__minus_03_dot_35_bar__plus_00_dot_78_bar__minus_04_dot_06 LaptopType)
        (objectType RemoteControl_bar__minus_02_dot_18_bar__plus_00_dot_76_bar__plus_00_dot_61 RemoteControlType)
        (objectType RemoteControl_bar__minus_02_dot_25_bar__plus_00_dot_76_bar__plus_00_dot_46 RemoteControlType)
        (objectType RemoteControl_bar__plus_01_dot_77_bar__plus_00_dot_88_bar__plus_01_dot_20 RemoteControlType)
        (objectType Laptop_bar__minus_00_dot_68_bar__plus_00_dot_42_bar__plus_01_dot_05 LaptopType)
        (objectType HousePlant_bar__plus_00_dot_51_bar__plus_01_dot_09_bar__plus_02_dot_24 HousePlantType)
        (objectType Chair_bar__minus_03_dot_18_bar__plus_00_dot_00_bar__minus_02_dot_81 ChairType)
        (objectType KeyChain_bar__minus_02_dot_25_bar__plus_00_dot_76_bar__plus_00_dot_91 KeyChainType)
        (objectType Television_bar__minus_02_dot_25_bar__plus_01_dot_24_bar__plus_00_dot_76 TelevisionType)
        (objectType Box_bar__minus_03_dot_22_bar__plus_00_dot_90_bar__minus_03_dot_39 BoxType)
        (objectType Chair_bar__minus_03_dot_50_bar__plus_00_dot_00_bar__minus_03_dot_72 ChairType)
        (objectType Statue_bar__minus_02_dot_24_bar__plus_00_dot_19_bar__plus_00_dot_83 StatueType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DiningTableType VaseType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType StatueType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain OttomanType BoxType)
        (canContain OttomanType LaptopType)
        (canContain OttomanType PillowType)
        (canContain OttomanType RemoteControlType)
        (canContain OttomanType KeyChainType)
        (canContain OttomanType CreditCardType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType CreditCardType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (pickupable Vase_bar__minus_02_dot_85_bar__plus_00_dot_79_bar__minus_02_dot_95)
        (pickupable Statue_bar__minus_03_dot_04_bar__plus_00_dot_78_bar__minus_03_dot_75)
        (pickupable KeyChain_bar__minus_00_dot_54_bar__plus_00_dot_39_bar__plus_01_dot_94)
        (pickupable CreditCard_bar__plus_01_dot_85_bar__plus_00_dot_88_bar__plus_00_dot_99)
        (pickupable KeyChain_bar__minus_00_dot_47_bar__plus_00_dot_43_bar__plus_00_dot_87)
        (pickupable Pillow_bar__plus_00_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_54)
        (pickupable Statue_bar__plus_01_dot_81_bar__plus_00_dot_88_bar__plus_00_dot_78)
        (pickupable Laptop_bar__minus_03_dot_35_bar__plus_00_dot_78_bar__minus_04_dot_06)
        (pickupable RemoteControl_bar__minus_02_dot_18_bar__plus_00_dot_76_bar__plus_00_dot_61)
        (pickupable RemoteControl_bar__minus_02_dot_25_bar__plus_00_dot_76_bar__plus_00_dot_46)
        (pickupable RemoteControl_bar__plus_01_dot_77_bar__plus_00_dot_88_bar__plus_01_dot_20)
        (pickupable Laptop_bar__minus_00_dot_68_bar__plus_00_dot_42_bar__plus_01_dot_05)
        (pickupable KeyChain_bar__minus_02_dot_25_bar__plus_00_dot_76_bar__plus_00_dot_91)
        (pickupable Box_bar__minus_03_dot_22_bar__plus_00_dot_90_bar__minus_03_dot_39)
        (pickupable Statue_bar__minus_02_dot_24_bar__plus_00_dot_19_bar__plus_00_dot_83)
        (isReceptacleObject Box_bar__minus_03_dot_22_bar__plus_00_dot_90_bar__minus_03_dot_39)
        (openable Drawer_bar__plus_01_dot_75_bar__plus_00_dot_78_bar__plus_01_dot_09)
        
        (atLocation agent1 loc_bar_0_bar_1_bar_3_bar_30)
        
        
        
        
        
        
        
        (toggleable FloorLamp_bar__plus_01_dot_71_bar__plus_00_dot_00_bar__plus_00_dot_37)
        
        
        
        
        (inReceptacle Pillow_bar__plus_00_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_54 Sofa_bar__plus_00_dot_78_bar__plus_00_dot_00_bar__minus_00_dot_51)
        (inReceptacle KeyChain_bar__minus_00_dot_47_bar__plus_00_dot_43_bar__plus_00_dot_87 Ottoman_bar__minus_00_dot_58_bar__plus_00_dot_01_bar__plus_01_dot_05)
        (inReceptacle Laptop_bar__minus_00_dot_68_bar__plus_00_dot_42_bar__plus_01_dot_05 Ottoman_bar__minus_00_dot_58_bar__plus_00_dot_01_bar__plus_01_dot_05)
        (inReceptacle Box_bar__minus_03_dot_22_bar__plus_00_dot_90_bar__minus_03_dot_39 DiningTable_bar__minus_03_dot_10_bar__plus_00_dot_00_bar__minus_03_dot_61)
        (inReceptacle Statue_bar__minus_03_dot_04_bar__plus_00_dot_78_bar__minus_03_dot_75 DiningTable_bar__minus_03_dot_10_bar__plus_00_dot_00_bar__minus_03_dot_61)
        (inReceptacle Vase_bar__minus_02_dot_85_bar__plus_00_dot_79_bar__minus_02_dot_95 DiningTable_bar__minus_03_dot_10_bar__plus_00_dot_00_bar__minus_03_dot_61)
        (inReceptacle Laptop_bar__minus_03_dot_35_bar__plus_00_dot_78_bar__minus_04_dot_06 DiningTable_bar__minus_03_dot_10_bar__plus_00_dot_00_bar__minus_03_dot_61)
        (inReceptacle CreditCard_bar__plus_01_dot_85_bar__plus_00_dot_88_bar__plus_00_dot_99 SideTable_bar__plus_01_dot_83_bar__plus_00_dot_00_bar__plus_01_dot_09)
        (inReceptacle Statue_bar__plus_01_dot_81_bar__plus_00_dot_88_bar__plus_00_dot_78 SideTable_bar__plus_01_dot_83_bar__plus_00_dot_00_bar__plus_01_dot_09)
        (inReceptacle RemoteControl_bar__plus_01_dot_77_bar__plus_00_dot_88_bar__plus_01_dot_20 SideTable_bar__plus_01_dot_83_bar__plus_00_dot_00_bar__plus_01_dot_09)
        (inReceptacle Statue_bar__minus_02_dot_24_bar__plus_00_dot_19_bar__plus_00_dot_83 Shelf_bar__minus_02_dot_24_bar__plus_00_dot_17_bar__plus_00_dot_77)
        (inReceptacle RemoteControl_bar__minus_02_dot_18_bar__plus_00_dot_76_bar__plus_00_dot_61 TVStand_bar__minus_02_dot_24_bar__plus_00_dot_00_bar__plus_00_dot_76)
        (inReceptacle Television_bar__minus_02_dot_25_bar__plus_01_dot_24_bar__plus_00_dot_76 TVStand_bar__minus_02_dot_24_bar__plus_00_dot_00_bar__plus_00_dot_76)
        (inReceptacle RemoteControl_bar__minus_02_dot_25_bar__plus_00_dot_76_bar__plus_00_dot_46 TVStand_bar__minus_02_dot_24_bar__plus_00_dot_00_bar__plus_00_dot_76)
        (inReceptacle KeyChain_bar__minus_02_dot_25_bar__plus_00_dot_76_bar__plus_00_dot_91 TVStand_bar__minus_02_dot_24_bar__plus_00_dot_00_bar__plus_00_dot_76)
        
        
        (receptacleAtLocation ArmChair_bar__minus_00_dot_56_bar__plus_00_dot_00_bar__plus_01_dot_95 loc_bar__minus_7_bar_7_bar_1_bar_60)
        (receptacleAtLocation DiningTable_bar__minus_03_dot_10_bar__plus_00_dot_00_bar__minus_03_dot_61 loc_bar__minus_16_bar__minus_15_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_01_dot_75_bar__plus_00_dot_78_bar__plus_01_dot_09 loc_bar_4_bar_4_bar_1_bar_45)
        (receptacleAtLocation GarbageCan_bar__minus_01_dot_34_bar__plus_00_dot_00_bar__minus_04_dot_83 loc_bar__minus_9_bar__minus_17_bar_1_bar_60)
        (receptacleAtLocation Ottoman_bar__minus_00_dot_58_bar__plus_00_dot_01_bar__plus_01_dot_05 loc_bar__minus_2_bar_2_bar_0_bar_60)
        (receptacleAtLocation Shelf_bar__minus_00_dot_61_bar__plus_00_dot_88_bar__minus_02_dot_83 loc_bar__minus_2_bar__minus_8_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__minus_00_dot_61_bar__plus_01_dot_32_bar__minus_02_dot_83 loc_bar__minus_2_bar__minus_8_bar_2_bar_15)
        (receptacleAtLocation Shelf_bar__minus_02_dot_08_bar__plus_00_dot_17_bar__plus_01_dot_14 loc_bar__minus_4_bar_2_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__minus_02_dot_09_bar__plus_00_dot_17_bar__plus_00_dot_40 loc_bar__minus_4_bar_0_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__minus_02_dot_09_bar__plus_00_dot_41_bar__plus_00_dot_40 loc_bar__minus_5_bar_3_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__minus_02_dot_10_bar__plus_00_dot_47_bar__plus_01_dot_14 loc_bar__minus_5_bar_3_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__minus_02_dot_13_bar__plus_00_dot_56_bar__plus_00_dot_40 loc_bar__minus_5_bar_3_bar_3_bar_45)
        (receptacleAtLocation Shelf_bar__minus_02_dot_24_bar__plus_00_dot_17_bar__plus_00_dot_77 loc_bar__minus_4_bar_2_bar_3_bar_45)
        (receptacleAtLocation SideTable_bar__plus_01_dot_83_bar__plus_00_dot_00_bar__plus_01_dot_09 loc_bar_5_bar_4_bar_1_bar_60)
        (receptacleAtLocation Sofa_bar__plus_00_dot_78_bar__plus_00_dot_00_bar__minus_00_dot_51 loc_bar__minus_2_bar__minus_3_bar_1_bar_45)
        (receptacleAtLocation TVStand_bar__minus_02_dot_24_bar__plus_00_dot_00_bar__plus_00_dot_76 loc_bar__minus_5_bar_3_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__plus_01_dot_77_bar__plus_00_dot_88_bar__plus_01_dot_20 loc_bar_5_bar_4_bar_1_bar_60)
        (objectAtLocation Laptop_bar__minus_00_dot_68_bar__plus_00_dot_42_bar__plus_01_dot_05 loc_bar__minus_2_bar_2_bar_0_bar_60)
        (objectAtLocation KeyChain_bar__minus_00_dot_54_bar__plus_00_dot_39_bar__plus_01_dot_94 loc_bar__minus_5_bar_8_bar_1_bar_60)
        (objectAtLocation RemoteControl_bar__minus_02_dot_25_bar__plus_00_dot_76_bar__plus_00_dot_46 loc_bar__minus_5_bar_3_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__minus_02_dot_25_bar__plus_00_dot_76_bar__plus_00_dot_91 loc_bar__minus_5_bar_3_bar_3_bar_60)
        (objectAtLocation Box_bar__minus_03_dot_22_bar__plus_00_dot_90_bar__minus_03_dot_39 loc_bar__minus_16_bar__minus_15_bar_1_bar_60)
        (objectAtLocation LightSwitch_bar__minus_02_dot_60_bar__plus_01_dot_26_bar__minus_00_dot_78 loc_bar__minus_8_bar__minus_3_bar_3_bar_30)
        (objectAtLocation HousePlant_bar__plus_00_dot_51_bar__plus_01_dot_09_bar__plus_02_dot_24 loc_bar_3_bar_6_bar_0_bar_30)
        (objectAtLocation KeyChain_bar__minus_00_dot_47_bar__plus_00_dot_43_bar__plus_00_dot_87 loc_bar__minus_2_bar_2_bar_0_bar_60)
        (objectAtLocation Laptop_bar__minus_03_dot_35_bar__plus_00_dot_78_bar__minus_04_dot_06 loc_bar__minus_16_bar__minus_15_bar_1_bar_60)
        (objectAtLocation Painting_bar__minus_00_dot_51_bar__plus_01_dot_74_bar__plus_02_dot_40 loc_bar__minus_5_bar_8_bar_1_bar_0)
        (objectAtLocation Statue_bar__minus_03_dot_04_bar__plus_00_dot_78_bar__minus_03_dot_75 loc_bar__minus_16_bar__minus_15_bar_1_bar_60)
        (objectAtLocation Pillow_bar__plus_00_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_54 loc_bar__minus_2_bar__minus_3_bar_1_bar_45)
        (objectAtLocation RemoteControl_bar__minus_02_dot_18_bar__plus_00_dot_76_bar__plus_00_dot_61 loc_bar__minus_5_bar_3_bar_3_bar_60)
        (objectAtLocation FloorLamp_bar__plus_01_dot_71_bar__plus_00_dot_00_bar__plus_00_dot_37 loc_bar_3_bar_2_bar_1_bar_60)
        (objectAtLocation Painting_bar__minus_05_dot_00_bar__plus_01_dot_63_bar__minus_03_dot_55 loc_bar__minus_17_bar__minus_14_bar_3_bar_0)
        (objectAtLocation Statue_bar__plus_01_dot_81_bar__plus_00_dot_88_bar__plus_00_dot_78 loc_bar_5_bar_4_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__plus_01_dot_85_bar__plus_00_dot_88_bar__plus_00_dot_99 loc_bar_5_bar_4_bar_1_bar_60)
        (objectAtLocation Television_bar__minus_02_dot_25_bar__plus_01_dot_24_bar__plus_00_dot_76 loc_bar__minus_5_bar_3_bar_3_bar_60)
        (objectAtLocation Chair_bar__minus_02_dot_69_bar__plus_00_dot_00_bar__minus_03_dot_30 loc_bar__minus_9_bar__minus_14_bar_3_bar_60)
        (objectAtLocation Chair_bar__minus_03_dot_18_bar__plus_00_dot_00_bar__minus_02_dot_81 loc_bar__minus_11_bar__minus_10_bar_3_bar_60)
        (objectAtLocation Chair_bar__minus_03_dot_50_bar__plus_00_dot_00_bar__minus_03_dot_72 loc_bar__minus_16_bar__minus_15_bar_1_bar_60)
        (objectAtLocation Chair_bar__minus_03_dot_13_bar__plus_00_dot_00_bar__minus_04_dot_42 loc_bar__minus_16_bar__minus_18_bar_1_bar_60)
        (objectAtLocation Vase_bar__minus_02_dot_85_bar__plus_00_dot_79_bar__minus_02_dot_95 loc_bar__minus_16_bar__minus_15_bar_1_bar_60)
        (objectAtLocation Statue_bar__minus_02_dot_24_bar__plus_00_dot_19_bar__plus_00_dot_83 loc_bar__minus_4_bar_2_bar_3_bar_45)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 RemoteControlType)
                                    (receptacleType ?r OttomanType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 RemoteControlType)
                                            (receptacleType ?r OttomanType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            