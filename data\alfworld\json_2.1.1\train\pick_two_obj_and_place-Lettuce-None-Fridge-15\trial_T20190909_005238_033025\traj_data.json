{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000299.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000300.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000301.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000302.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000303.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000304.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000305.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000306.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000307.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000308.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000309.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000310.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000311.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000312.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000313.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000314.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000315.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000316.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000317.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000318.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000319.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000320.png", "low_idx": 65}, {"high_idx": 7, "image_name": "000000321.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000322.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000323.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000324.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000325.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000326.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000327.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000328.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000329.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000330.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000331.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000332.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000333.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000334.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000335.png", "low_idx": 66}, {"high_idx": 7, "image_name": "000000336.png", "low_idx": 67}, {"high_idx": 7, "image_name": "000000337.png", "low_idx": 67}, {"high_idx": 7, "image_name": "000000338.png", "low_idx": 67}, {"high_idx": 7, "image_name": "000000339.png", "low_idx": 67}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Lettuce", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-2|2|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-3.979634524, -3.979634524, 0.609986128, 0.609986128, 3.9100188, 3.9100188]], "coordinateReceptacleObjectId": ["DiningTable", [-5.38, -5.38, 1.332, 1.332, 0.0562074184, 0.0562074184]], "forceVisible": true, "objectId": "Lettuce|-00.99|+00.98|+00.15"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|12|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-3.979634524, -3.979634524, 0.609986128, 0.609986128, 3.9100188, 3.9100188]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-1.76, -1.76, 15.988, 15.988, 0.0562069416, 0.0562069416]], "forceVisible": true, "objectId": "Lettuce|-00.99|+00.98|+00.15", "receptacleObjectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-11|11|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-13.08976268, -13.08976268, 11.37704848, 11.37704848, 4.006128, 4.006128]], "coordinateReceptacleObjectId": ["CounterTop", [-13.5272, -13.5272, 13.344, 13.344, 3.804, 3.804]], "forceVisible": true, "objectId": "Lettuce|-03.27|+01.00|+02.84"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-2|12|0|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-13.08976268, -13.08976268, 11.37704848, 11.37704848, 4.006128, 4.006128]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-1.76, -1.76, 15.988, 15.988, 0.0562069416, 0.0562069416]], "forceVisible": true, "objectId": "Lettuce|-03.27|+01.00|+02.84", "receptacleObjectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-00.99|+00.98|+00.15"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [1, 72, 71, 145], "mask": [[21326, 13], [21622, 22], [21920, 27], [22218, 32], [22516, 36], [22814, 40], [23113, 42], [23411, 46], [23710, 48], [24009, 51], [24308, 53], [24606, 55], [24906, 56], [25205, 58], [25505, 59], [25804, 60], [26104, 61], [26404, 61], [26703, 63], [27003, 63], [27302, 64], [27602, 65], [27902, 65], [28201, 67], [28501, 67], [28801, 68], [29101, 68], [29401, 69], [29701, 69], [30001, 69], [30301, 70], [30601, 70], [30901, 70], [31201, 70], [31501, 70], [31801, 70], [32101, 71], [32401, 71], [32701, 71], [33001, 71], [33302, 70], [33602, 70], [33902, 69], [34203, 67], [34503, 67], [34803, 66], [35104, 64], [35404, 64], [35705, 62], [36005, 62], [36306, 60], [36606, 60], [36907, 58], [37207, 57], [37508, 55], [37809, 53], [38109, 52], [38410, 50], [38710, 49], [39011, 47], [39311, 46], [39612, 44], [39912, 42], [40213, 39], [40513, 37], [40814, 34], [41114, 32], [41415, 30], [41716, 27], [42017, 25], [42318, 22], [42619, 19], [42920, 15], [43223, 10]], "point": [36, 107]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 200], "mask": [[0, 24300], [24301, 299], [24602, 298], [24902, 298], [25203, 297], [25504, 296], [25804, 296], [26105, 295], [26406, 294], [26706, 294], [27007, 293], [27308, 292], [27608, 292], [27909, 291], [28210, 290], [28511, 289], [28811, 289], [29112, 288], [29413, 287], [29713, 287], [30014, 286], [30315, 285], [30615, 285], [30916, 284], [31217, 283], [31517, 283], [31818, 282], [32119, 281], [32419, 281], [32720, 280], [33021, 279], [33321, 279], [33622, 278], [33923, 277], [34223, 277], [34524, 276], [34825, 275], [35126, 274], [35426, 274], [35727, 273], [36028, 272], [36328, 272], [36629, 271], [36930, 270], [37230, 270], [37531, 268], [37832, 267], [38132, 266], [38433, 264], [38734, 262], [39034, 261], [39335, 259], [39636, 257], [39936, 257], [40237, 255], [40538, 253], [40838, 252], [41139, 250], [41440, 248], [41741, 246], [42041, 246], [42342, 244], [42643, 242], [42943, 241], [43244, 239], [43545, 237], [43845, 236], [44146, 235], [44447, 233], [44747, 232], [45048, 230], [45349, 228], [45649, 227], [45950, 225], [46251, 224], [46551, 223], [46852, 221], [47153, 219], [47453, 218], [47754, 216], [48055, 214], [48356, 213], [48656, 212], [48957, 210], [49258, 208], [49558, 207], [49859, 205], [50160, 203], [50460, 203], [50761, 201], [51062, 199], [51362, 198], [51663, 196], [51964, 194], [52264, 193], [52565, 192], [52866, 190], [53166, 189], [53467, 187], [53768, 185], [54068, 184], [54369, 182], [54670, 181], [54971, 179], [55271, 72], [55358, 91], [55572, 69], [55660, 88], [55873, 66], [55962, 85], [56173, 64], [56263, 83], [56474, 62], [56564, 81], [56775, 60], [56866, 79], [57075, 58], [57167, 77], [57377, 55], [57468, 74], [57682, 48], [57769, 68], [57988, 41], [58070, 61], [58294, 34], [58371, 54], [58600, 27], [58672, 46], [58907, 18], [58973, 38], [59214, 10], [59274, 31], [59521, 2], [59574, 24], [59875, 13]], "point": [149, 99]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-00.99|+00.98|+00.15", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 57], [97, 57], [177, 181], [397, 58], [476, 182], [696, 61], [774, 185], [995, 64], [1073, 187], [1294, 68], [1369, 192], [1593, 270], [1891, 277], [2186, 16875], [19068, 291], [19371, 286], [19673, 283], [19974, 281], [20275, 279], [20575, 278], [20876, 276], [21177, 275], [21477, 274], [21778, 273], [22078, 273], [22378, 272], [22678, 272], [22979, 271], [23279, 271], [23579, 270], [23879, 270], [24178, 271], [24478, 271], [24778, 271], [25078, 272], [25377, 273], [25677, 273], [25976, 275], [26276, 275], [26575, 277], [26875, 278], [27174, 280], [27473, 282], [27771, 286], [28070, 289], [28367, 2232], [30600, 299], [30900, 298], [31200, 297], [31500, 296], [31800, 296], [32100, 295], [32400, 294], [32700, 293], [33000, 293], [33300, 292], [33600, 291], [33900, 290], [34200, 289], [34500, 289], [34800, 288], [35100, 287], [35400, 286], [35700, 286], [36000, 285], [36300, 284], [36600, 283], [36900, 283], [37200, 282], [37500, 281], [37800, 280], [38100, 280], [38400, 279], [38700, 278], [39000, 277], [39300, 277], [39600, 276], [39900, 275], [40200, 274], [40500, 274], [40800, 273], [41100, 272], [41400, 271], [41700, 271], [42000, 270], [42300, 269], [42600, 268], [42900, 268], [43200, 267], [43500, 266], [43800, 265], [44100, 265], [44400, 264], [44700, 263], [45000, 262], [45300, 262], [45600, 261], [45900, 260], [46200, 259], [46500, 259], [46800, 258], [47100, 257], [47400, 256], [47700, 256], [48000, 255], [48300, 254], [48600, 253], [48900, 253], [49200, 252], [49500, 251], [49800, 250], [50100, 250], [50400, 249], [50700, 248], [51000, 247], [51300, 247], [51600, 246], [51900, 245], [52200, 244], [52500, 244], [52800, 243], [53100, 242], [53400, 241], [53700, 239], [54000, 84], [54300, 84], [54600, 84], [54900, 84], [55200, 84], [55500, 84], [55800, 84], [56100, 83], [56400, 83], [56700, 83], [57000, 83], [57300, 83], [57600, 83], [57900, 83], [58200, 82], [58500, 82], [58800, 83], [59100, 82], [59400, 82], [59700, 82], [60000, 82], [60300, 82], [60600, 82], [60900, 82], [61200, 81], [61500, 81], [61800, 81], [62100, 81], [62400, 81], [62700, 81], [63000, 81], [63300, 80], [63600, 80], [63900, 80], [64200, 80], [64500, 80], [64800, 80], [65100, 80], [65400, 80], [65700, 79], [66000, 79], [66300, 79], [66600, 79], [66900, 79], [67200, 79], [67500, 79], [67800, 78], [68100, 78], [68400, 78], [68700, 78], [69000, 78], [69300, 78], [69600, 78], [69900, 77], [70200, 77], [70500, 77], [70800, 77], [71100, 77], [71400, 77], [71700, 77], [72000, 77], [72300, 76], [72600, 76], [72900, 76], [73200, 76], [73500, 76], [73800, 76], [74100, 76], [74400, 75], [74700, 75], [75000, 75], [75300, 75], [75600, 75], [75900, 75], [76200, 75], [76500, 75], [76800, 74], [77100, 74], [77400, 74], [77700, 74], [78000, 74], [78300, 74], [78600, 74], [78900, 73], [79200, 73], [79500, 73], [79800, 73], [80100, 73], [80400, 73], [80700, 73], [81000, 72], [81300, 72], [81600, 72], [81900, 72], [82200, 72], [82500, 72], [82800, 72], [83100, 72], [83400, 71], [83700, 71], [84000, 71], [84300, 71], [84600, 71], [84900, 71], [85200, 71], [85500, 70], [85800, 70], [86100, 70], [86400, 70], [86700, 70], [87000, 70], [87300, 70], [87600, 69], [87900, 69], [88200, 69], [88500, 69], [88800, 69], [89100, 69], [89400, 69], [89700, 69]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 57], [97, 2], [177, 181], [397, 2], [476, 182], [696, 3], [774, 185], [995, 4], [1073, 187], [1294, 5], [1369, 192], [1593, 6], [1668, 195], [1891, 8], [1968, 200], [2186, 13], [2268, 231], [2567, 233], [2867, 233], [3167, 233], [3467, 233], [3767, 234], [4067, 235], [4367, 236], [4666, 237], [4966, 238], [5266, 239], [5565, 241], [5865, 243], [6165, 244], [6464, 246], [6764, 248], [7063, 250], [7363, 251], [7662, 253], [7961, 255], [8260, 257], [8559, 259], [8859, 261], [9156, 265], [9454, 269], [9752, 273], [10050, 277], [10348, 283], [10646, 289], [10943, 8118], [19068, 291], [19371, 286], [19673, 283], [19974, 281], [20275, 279], [20575, 278], [20876, 276], [21177, 275], [21477, 274], [21778, 273], [22078, 273], [22378, 272], [22678, 272], [22979, 271], [23279, 271], [23579, 270], [23879, 270], [24178, 271], [24478, 271], [24778, 271], [25078, 272], [25377, 273], [25677, 273], [25976, 275], [26276, 275], [26575, 277], [26875, 278], [27174, 280], [27473, 282], [27771, 286], [28070, 289], [28367, 2232], [30600, 299], [30900, 298], [31200, 297], [31500, 296], [31800, 296], [32100, 295], [32400, 294], [32700, 293], [33000, 293], [33300, 292], [33600, 291], [33900, 290], [34200, 289], [34500, 289], [34800, 288], [35100, 287], [35400, 286], [35700, 286], [36000, 285], [36300, 284], [36600, 283], [36900, 283], [37200, 282], [37500, 281], [37800, 280], [38100, 280], [38400, 279], [38700, 278], [39000, 277], [39300, 277], [39600, 276], [39900, 275], [40200, 274], [40500, 274], [40800, 273], [41100, 272], [41400, 271], [41700, 271], [42000, 270], [42300, 269], [42600, 268], [42900, 268], [43200, 267], [43500, 266], [43800, 265], [44100, 265], [44400, 264], [44700, 263], [45000, 262], [45300, 262], [45600, 261], [45900, 260], [46200, 259], [46500, 259], [46800, 258], [47100, 257], [47400, 256], [47700, 256], [48000, 255], [48300, 254], [48600, 253], [48900, 253], [49200, 252], [49500, 251], [49800, 250], [50100, 250], [50400, 249], [50700, 248], [51000, 247], [51300, 247], [51600, 246], [51900, 245], [52200, 244], [52500, 244], [52800, 243], [53100, 242], [53400, 241], [53700, 239], [54000, 84], [54300, 84], [54600, 84], [54900, 84], [55200, 84], [55500, 84], [55800, 84], [56100, 83], [56400, 83], [56700, 83], [57000, 83], [57300, 83], [57600, 83], [57900, 83], [58200, 82], [58500, 82], [58800, 83], [59100, 82], [59400, 82], [59700, 82], [60000, 82], [60300, 82], [60600, 82], [60900, 82], [61200, 81], [61500, 81], [61800, 81], [62100, 81], [62400, 81], [62700, 81], [63000, 81], [63300, 80], [63600, 80], [63900, 80], [64200, 80], [64500, 80], [64800, 80], [65100, 80], [65400, 80], [65700, 79], [66000, 79], [66300, 79], [66600, 79], [66900, 79], [67200, 79], [67500, 79], [67800, 78], [68100, 78], [68400, 78], [68700, 78], [69000, 78], [69300, 78], [69600, 78], [69900, 77], [70200, 77], [70500, 77], [70800, 77], [71100, 77], [71400, 77], [71700, 77], [72000, 77], [72300, 76], [72600, 76], [72900, 76], [73200, 76], [73500, 76], [73800, 76], [74100, 76], [74400, 75], [74700, 75], [75000, 75], [75300, 75], [75600, 75], [75900, 75], [76200, 75], [76500, 75], [76800, 74], [77100, 74], [77400, 74], [77700, 74], [78000, 74], [78300, 74], [78600, 74], [78900, 73], [79200, 73], [79500, 73], [79800, 73], [80100, 73], [80400, 73], [80700, 73], [81000, 72], [81300, 72], [81600, 72], [81900, 72], [82200, 72], [82500, 72], [82800, 72], [83100, 72], [83400, 71], [83700, 71], [84000, 71], [84300, 71], [84600, 71], [84900, 71], [85200, 71], [85500, 70], [85800, 70], [86100, 70], [86400, 70], [86700, 70], [87000, 70], [87300, 70], [87600, 69], [87900, 69], [88200, 69], [88500, 69], [88800, 69], [89100, 69], [89400, 69], [89700, 69]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-03.27|+01.00|+02.84"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [143, 133, 219, 196], "mask": [[39773, 4], [40069, 12], [40367, 18], [40664, 25], [40962, 29], [41260, 33], [41558, 37], [41855, 41], [42153, 45], [42452, 47], [42751, 49], [43050, 51], [43349, 53], [43648, 55], [43948, 56], [44247, 58], [44546, 60], [44846, 61], [45146, 63], [45445, 65], [45745, 66], [46044, 68], [46344, 69], [46644, 70], [46944, 71], [47243, 73], [47543, 74], [47843, 75], [48143, 76], [48443, 76], [48743, 76], [49043, 77], [49343, 77], [49643, 77], [49943, 77], [50243, 77], [50543, 77], [50843, 77], [51143, 77], [51443, 76], [51743, 76], [52044, 74], [52344, 73], [52644, 72], [52945, 70], [53245, 69], [53545, 68], [53846, 65], [54146, 64], [54446, 62], [54747, 60], [55048, 57], [55349, 55], [55650, 53], [55951, 51], [56252, 49], [56553, 46], [56855, 43], [57156, 40], [57457, 38], [57759, 34], [58061, 30], [58364, 25], [58667, 17]], "point": [181, 163]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 201], "mask": [[0, 24300], [24301, 299], [24602, 298], [24902, 298], [25203, 297], [25504, 296], [25804, 296], [26105, 295], [26406, 294], [26706, 294], [27007, 293], [27308, 292], [27608, 292], [27909, 291], [28210, 290], [28511, 289], [28811, 289], [29112, 288], [29413, 287], [29713, 287], [30014, 286], [30315, 285], [30615, 285], [30916, 284], [31217, 283], [31517, 283], [31818, 282], [32119, 281], [32419, 281], [32720, 280], [33021, 279], [33321, 279], [33622, 278], [33923, 277], [34223, 277], [34524, 276], [34825, 275], [35126, 274], [35426, 274], [35727, 273], [36028, 272], [36328, 272], [36629, 271], [36930, 270], [37230, 270], [37531, 268], [37832, 267], [38132, 266], [38433, 264], [38734, 262], [39034, 261], [39335, 259], [39636, 257], [39936, 257], [40237, 255], [40538, 253], [40838, 252], [41139, 250], [41440, 248], [41741, 246], [42041, 246], [42342, 244], [42643, 242], [42943, 241], [43244, 239], [43545, 237], [43845, 236], [44146, 235], [44447, 233], [44747, 232], [45048, 230], [45349, 228], [45649, 227], [45950, 225], [46251, 224], [46551, 223], [46852, 221], [47153, 219], [47453, 218], [47754, 216], [48055, 214], [48356, 213], [48656, 212], [48957, 210], [49258, 208], [49558, 207], [49859, 205], [50160, 203], [50460, 203], [50761, 201], [51062, 199], [51362, 198], [51663, 196], [51964, 194], [52264, 193], [52565, 192], [52866, 190], [53166, 189], [53467, 187], [53768, 185], [54068, 184], [54369, 182], [54670, 181], [54971, 179], [55271, 178], [55572, 176], [55873, 174], [56173, 173], [56474, 171], [56775, 170], [57075, 71], [57155, 89], [57377, 66], [57458, 84], [57682, 59], [57760, 77], [57988, 50], [58062, 69], [58294, 42], [58364, 61], [58600, 34], [58665, 53], [58907, 25], [58967, 44], [59214, 17], [59268, 37], [59521, 9], [59569, 29], [59870, 18], [60171, 4]], "point": [149, 100]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-03.27|+01.00|+02.84", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 57], [97, 2], [177, 181], [397, 2], [476, 182], [696, 3], [774, 185], [995, 4], [1073, 187], [1294, 5], [1369, 192], [1593, 6], [1668, 195], [1891, 8], [1968, 200], [2186, 13], [2268, 231], [2567, 233], [2867, 233], [3167, 233], [3467, 233], [3767, 234], [4067, 235], [4367, 236], [4666, 237], [4966, 238], [5266, 239], [5565, 241], [5865, 243], [6165, 244], [6464, 246], [6764, 248], [7063, 250], [7363, 251], [7662, 253], [7961, 255], [8260, 257], [8559, 259], [8859, 261], [9156, 265], [9454, 269], [9752, 273], [10050, 277], [10348, 283], [10646, 289], [10943, 8118], [19068, 291], [19371, 286], [19673, 283], [19974, 281], [20275, 279], [20575, 278], [20876, 276], [21177, 275], [21477, 274], [21778, 273], [22078, 273], [22378, 272], [22678, 272], [22979, 271], [23279, 271], [23579, 270], [23879, 270], [24178, 271], [24478, 271], [24778, 271], [25078, 272], [25377, 273], [25677, 273], [25976, 275], [26276, 275], [26575, 277], [26875, 278], [27174, 280], [27473, 282], [27771, 286], [28070, 289], [28367, 2232], [30600, 299], [30900, 298], [31200, 297], [31500, 296], [31800, 296], [32100, 295], [32400, 294], [32700, 293], [33000, 293], [33300, 292], [33600, 291], [33900, 290], [34200, 289], [34500, 289], [34800, 288], [35100, 287], [35400, 286], [35700, 286], [36000, 285], [36300, 284], [36600, 283], [36900, 283], [37200, 282], [37500, 281], [37800, 280], [38100, 280], [38400, 279], [38700, 278], [39000, 277], [39300, 277], [39600, 276], [39900, 275], [40200, 274], [40500, 274], [40800, 273], [41100, 272], [41400, 271], [41700, 271], [42000, 270], [42300, 269], [42600, 268], [42900, 268], [43200, 267], [43500, 266], [43800, 265], [44100, 265], [44400, 264], [44700, 263], [45000, 262], [45300, 262], [45600, 261], [45900, 260], [46200, 259], [46500, 259], [46800, 258], [47100, 257], [47400, 256], [47700, 256], [48000, 255], [48300, 254], [48600, 253], [48900, 253], [49200, 252], [49500, 251], [49800, 250], [50100, 250], [50400, 249], [50700, 248], [51000, 247], [51300, 247], [51600, 246], [51900, 245], [52200, 244], [52500, 244], [52800, 243], [53100, 242], [53400, 241], [53700, 239], [54000, 84], [54300, 84], [54600, 84], [54900, 84], [55200, 84], [55500, 84], [55800, 84], [56100, 83], [56400, 83], [56700, 83], [57000, 83], [57300, 83], [57600, 83], [57900, 83], [58200, 82], [58500, 82], [58800, 83], [59100, 82], [59400, 82], [59700, 82], [60000, 82], [60300, 82], [60600, 82], [60900, 82], [61200, 81], [61500, 81], [61800, 81], [62100, 81], [62400, 81], [62700, 81], [63000, 81], [63300, 80], [63600, 80], [63900, 80], [64200, 80], [64500, 80], [64800, 80], [65100, 80], [65400, 80], [65700, 79], [66000, 79], [66300, 79], [66600, 79], [66900, 79], [67200, 79], [67500, 79], [67800, 78], [68100, 78], [68400, 78], [68700, 78], [69000, 78], [69300, 78], [69600, 78], [69900, 77], [70200, 77], [70500, 77], [70800, 77], [71100, 77], [71400, 77], [71700, 77], [72000, 77], [72300, 76], [72600, 76], [72900, 76], [73200, 76], [73500, 76], [73800, 76], [74100, 76], [74400, 75], [74700, 75], [75000, 75], [75300, 75], [75600, 75], [75900, 75], [76200, 75], [76500, 75], [76800, 74], [77100, 74], [77400, 74], [77700, 74], [78000, 74], [78300, 73], [78600, 73], [78900, 73], [79200, 73], [79500, 73], [79800, 73], [80100, 73], [80400, 73], [80700, 73], [81000, 72], [81300, 72], [81600, 72], [81900, 72], [82200, 72], [82500, 72], [82800, 72], [83100, 72], [83400, 71], [83700, 71], [84000, 71], [84300, 71], [84600, 71], [84900, 71], [85200, 71], [85500, 70], [85800, 70], [86100, 70], [86400, 70], [86700, 70], [87000, 70], [87300, 70], [87600, 69], [87900, 69], [88200, 69], [88500, 69], [88800, 69], [89100, 69], [89400, 69], [89700, 69]], "point": [149, 149]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 57], [97, 2], [239, 119], [397, 2], [539, 119], [696, 3], [839, 120], [995, 4], [1138, 122], [1294, 5], [1438, 123], [1593, 6], [1738, 125], [1891, 8], [2038, 130], [2186, 13], [2338, 161], [2567, 1], [2638, 162], [2867, 1], [2937, 163], [3167, 1], [3237, 163], [3467, 1], [3537, 163], [3767, 2], [3837, 164], [4067, 2], [4136, 166], [4367, 3], [4436, 167], [4666, 5], [4736, 167], [4966, 5], [5035, 169], [5266, 6], [5335, 170], [5565, 8], [5635, 171], [5865, 9], [5934, 174], [6165, 11], [6234, 175], [6464, 13], [6533, 177], [6764, 14], [6832, 180], [7063, 17], [7131, 182], [7363, 18], [7431, 183], [7662, 20], [7730, 185], [7961, 22], [8029, 187], [8260, 24], [8328, 189], [8559, 26], [8627, 191], [8859, 27], [8926, 194], [9156, 31], [9224, 197], [9454, 35], [9522, 201], [9752, 38], [9819, 206], [10050, 42], [10117, 210], [10348, 46], [10415, 216], [10646, 52], [10713, 222], [10943, 59], [11010, 8051], [19068, 291], [19371, 286], [19673, 283], [19974, 281], [20275, 279], [20575, 278], [20876, 276], [21177, 275], [21477, 274], [21778, 273], [22078, 273], [22378, 272], [22678, 272], [22979, 271], [23279, 271], [23579, 270], [23879, 270], [24178, 271], [24478, 271], [24778, 271], [25078, 272], [25377, 273], [25677, 273], [25976, 275], [26276, 275], [26575, 277], [26875, 278], [27174, 280], [27473, 282], [27771, 286], [28070, 289], [28367, 2232], [30600, 299], [30900, 298], [31200, 297], [31500, 296], [31800, 296], [32100, 295], [32400, 294], [32700, 293], [33000, 293], [33300, 292], [33600, 291], [33900, 290], [34200, 289], [34500, 289], [34800, 288], [35100, 287], [35400, 286], [35700, 286], [36000, 285], [36300, 284], [36600, 283], [36900, 283], [37200, 282], [37500, 281], [37800, 280], [38100, 280], [38400, 279], [38700, 278], [39000, 277], [39300, 277], [39600, 276], [39900, 275], [40200, 274], [40500, 274], [40800, 273], [41100, 272], [41400, 271], [41700, 271], [42000, 270], [42300, 269], [42600, 268], [42900, 268], [43200, 267], [43500, 266], [43800, 265], [44100, 265], [44400, 264], [44700, 263], [45000, 262], [45300, 262], [45600, 261], [45900, 260], [46200, 259], [46500, 259], [46800, 258], [47100, 257], [47400, 256], [47700, 256], [48000, 255], [48300, 254], [48600, 253], [48900, 253], [49200, 252], [49500, 251], [49800, 250], [50100, 250], [50400, 249], [50700, 248], [51000, 247], [51300, 247], [51600, 246], [51900, 245], [52200, 244], [52500, 244], [52800, 243], [53100, 242], [53400, 241], [53700, 239], [54000, 84], [54300, 84], [54600, 84], [54900, 84], [55200, 84], [55500, 84], [55800, 84], [56100, 83], [56400, 83], [56700, 83], [57000, 83], [57300, 83], [57600, 83], [57900, 83], [58200, 82], [58500, 82], [58800, 83], [59100, 82], [59400, 82], [59700, 82], [60000, 82], [60300, 82], [60600, 82], [60900, 82], [61200, 81], [61500, 81], [61800, 81], [62100, 81], [62400, 81], [62700, 81], [63000, 81], [63300, 80], [63600, 80], [63900, 80], [64200, 80], [64500, 80], [64800, 80], [65100, 80], [65400, 80], [65700, 79], [66000, 79], [66300, 79], [66600, 79], [66900, 79], [67200, 79], [67500, 79], [67800, 78], [68100, 78], [68400, 78], [68700, 78], [69000, 78], [69300, 78], [69600, 78], [69900, 77], [70200, 77], [70500, 77], [70800, 77], [71100, 77], [71400, 77], [71700, 77], [72000, 77], [72300, 76], [72600, 76], [72900, 76], [73200, 76], [73500, 76], [73800, 76], [74100, 76], [74400, 75], [74700, 75], [75000, 75], [75300, 75], [75600, 75], [75900, 75], [76200, 75], [76500, 75], [76800, 74], [77100, 74], [77400, 74], [77700, 74], [78000, 74], [78300, 74], [78600, 74], [78900, 73], [79200, 73], [79500, 73], [79800, 73], [80100, 73], [80400, 73], [80700, 73], [81000, 72], [81300, 72], [81600, 72], [81900, 72], [82200, 72], [82500, 72], [82800, 72], [83100, 72], [83400, 71], [83700, 71], [84000, 71], [84300, 71], [84600, 71], [84900, 71], [85200, 71], [85500, 70], [85800, 70], [86100, 70], [86400, 70], [86700, 70], [87000, 70], [87300, 70], [87600, 69], [87900, 69], [88200, 69], [88500, 69], [88800, 69], [89100, 69], [89400, 69], [89700, 69]], "point": [149, 149]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan15", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -2.25, "y": 0.914953351, "z": 2.25}, "object_poses": [{"objectName": "Pan_7e5e2cad", "position": {"x": -3.1432, "y": 0.9668, "z": 1.9715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_73731673", "position": {"x": -3.47555423, "y": 1.45868576, "z": 1.45450842}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_73731673", "position": {"x": -3.126574, "y": 0.744564831, "z": 1.279815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_657c04b2", "position": {"x": -1.523391, "y": 0.8915147, "z": 0.1524966}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -1.523391, "y": 0.8924014, "z": 0.564659357}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -1.78763235, "y": 0.8924014, "z": 0.04945594}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Kettle_a09ab4be", "position": {"x": -3.4247, "y": 0.9668, "z": 2.3743}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fd79f50b", "position": {"x": -1.523391, "y": 0.9064725, "z": 0.1524966}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_72bf05b8", "position": {"x": -1.25914979, "y": 0.8920212, "z": 0.3585779}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -3.44223332, "y": 1.581462, "z": 2.12050462}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -1.523391, "y": 0.9266359, "z": 0.461618632}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -3.53711653, "y": 1.54608548, "z": 2.17260122}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "Knife_4b738b3b", "position": {"x": -3.03886318, "y": 0.7695433, "z": 1.51067793}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_66cbdca0", "position": {"x": -1.65551162, "y": 0.9601291, "z": 0.564659357}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_66cbdca0", "position": {"x": -0.439998984, "y": 0.7915431, "z": 3.8170855}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "SoapBottle_da34a2c3", "position": {"x": -1.1929, "y": 0.08649755, "z": 3.820289}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_da34a2c3", "position": {"x": -3.49228621, "y": 1.99503958, "z": 2.30522728}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_0e75bb4f", "position": {"x": -2.75952125, "y": 0.07929611, "z": 3.66317081}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_d1c7827d", "position": {"x": -3.18504786, "y": 0.7423218, "z": 1.20286059}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_94931e65", "position": {"x": -3.27244067, "y": 1.001532, "z": 2.84426212}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_94931e65", "position": {"x": -1.25914979, "y": 0.9775047, "z": 0.04945588}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_8bd3d3d2", "position": {"x": -0.440000355, "y": 1.04621, "z": 4.051569}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Apple_8bd3d3d2", "position": {"x": -1.66600478, "y": 0.8495291, "z": 3.7}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -1.65551162, "y": 0.9266359, "z": 0.358577967}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_4b738b3b", "position": {"x": -1.3912704, "y": 0.9150003, "z": 0.3585779}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_72bf05b8", "position": {"x": -0.9949085, "y": 0.8920212, "z": 0.461618453}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_0fed2699", "position": {"x": -0.790438056, "y": 1.06762743, "z": 4.11414528}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Potato_08de48c7", "position": {"x": -0.0895631, "y": 1.30620408, "z": 4.06263256}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Plate_a5e935d4", "position": {"x": -2.410143, "y": 0.08495766, "z": 3.62530613}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_46b03e61", "position": {"x": -3.144, "y": 0.9633135, "z": 2.374}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_a09ab4be", "position": {"x": -3.55922174, "y": 0.916841269, "z": 1.18968439}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -1.12702918, "y": 0.8924014, "z": 0.461618543}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_0e75bb4f", "position": {"x": -0.994908631, "y": 0.895866156, "z": -0.0535848737}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_66cbdca0", "position": {"x": -1.92159271, "y": 0.8582126, "z": 3.662686}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -1.25914979, "y": 0.8912595, "z": 0.4616186}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_fd79f50b", "position": {"x": -3.27244067, "y": 0.9307999, "z": 1.44065785}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_da34a2c3", "position": {"x": -3.27244067, "y": 0.9157029, "z": 1.18968439}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_94931e65", "position": {"x": -0.994908631, "y": 0.9775047, "z": 0.152496532}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_7e5e2cad", "position": {"x": -3.4247, "y": 0.9668, "z": 1.9715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_8bd3d3d2", "position": {"x": -1.71187186, "y": 0.975472867, "z": 4.231027}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_d1c7827d", "position": {"x": -2.022751, "y": 0.9118061, "z": 4.14679575}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_94539d9a", "position": {"x": -1.50461912, "y": 0.9118061, "z": 4.231027}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_657c04b2", "position": {"x": -3.27244067, "y": 0.915542066, "z": 3.336}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_73731673", "position": {"x": -3.463628, "y": 0.915364265, "z": 1.60797334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_58cf1806", "position": {"x": -3.55922174, "y": 0.9169294, "z": 1.60797334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_0af3d2bb", "position": {"x": -3.42458582, "y": 0.945125461, "z": 3.27512622}, "rotation": {"x": 359.749268, "y": 89.87025, "z": 0.007967479}}], "object_toggles": [], "random_seed": 847561754, "scene_num": 15}, "task_id": "trial_T20190909_005238_033025", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A1O3TWBUDONVLO_352YTHGROY4PEG271MPR286LBO8H4W", "high_descs": ["Walk forward to face the left end of the table.", "Pick up the head of lettuce on the table.", "Turn right and walk across the room to face the fridge.", "Place the lettuce in the fridge.", "Turn left and walk forward to face the counter.", "Pick up the lettuce on the counter.", "Turn around and walk forward, then turn left to face the fridge.", "Place the lettuce in the fridge next to the other lettuce."], "task_desc": "To move two heads of lettuce into the fridge.", "votes": [1, 1, 1]}, {"assignment_id": "A20FCMWP43CVIU_3TUI152ZZEELZA4RXUTJDORJQSAQ16", "high_descs": ["walk to face left side of white table", "pick up lettuce from table", "walk to face fridge", "put lettuce inside fridge", "walk to face counter to right of stove", "pick up lettuce from counter", "walk to face fridge", "put lettuce inside fridge"], "task_desc": "put two lettuces inside fridge", "votes": [1, 1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3B3WTRP3DETMWW51ZWIBWCBZ8XS92Q", "high_descs": ["Turn left and walk toward the door, then hang a right and walk up to the white trash bag and turn right to face the white table.", "Pick up the closest head of lettuce off of the table.", "Turn right and walk over to the fridge.", "Open the fridge and put the head of lettuce inside on the top shelf then close the door.", "Turn around and walk forward, then hang a right and walk over to the counter to the right of the oven.", "Pick up the head of lettuce off of the counter.", "Turn around and walk over to the door, then turn left and walk up to the fridge.", "Open the fridge and put the head of lettuce inside on the top shelf, then close the door."], "task_desc": "Move two heads of lettuce into the fridge.", "votes": [0, 1, 1]}]}}