{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000299.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000300.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000301.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000302.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000303.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000304.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000305.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000306.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000307.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000308.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000309.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000310.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000311.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000312.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000313.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000314.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000315.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000316.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000317.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000318.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000319.png", "low_idx": 63}, {"high_idx": 7, "image_name": "000000320.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000321.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000322.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000323.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000324.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000325.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000326.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000327.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000328.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000329.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000330.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000331.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000332.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000333.png", "low_idx": 64}, {"high_idx": 7, "image_name": "000000334.png", "low_idx": 64}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "RemoteControl", "parent_target": "Ottoman", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["armchair"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|3|15|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["remotecontrol"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["RemoteControl", [2.9303452, 2.9303452, 19.53349876, 19.53349876, 1.746419312, 1.746419312]], "coordinateReceptacleObjectId": ["ArmChair", [1.68, 1.68, 20.516, 20.516, -0.0104, -0.0104]], "forceVisible": true, "objectId": "RemoteControl|+00.73|+00.44|+04.88"}}, {"discrete_action": {"action": "GotoLocation", "args": ["ottoman"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|12|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["remotecontrol", "ottoman"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["RemoteControl", [2.9303452, 2.9303452, 19.53349876, 19.53349876, 1.746419312, 1.746419312]], "coordinateReceptacleObjectId": ["Ottoman", [1.976, 1.976, 17.236, 17.236, 0.008, 0.008]], "forceVisible": true, "objectId": "RemoteControl|+00.73|+00.44|+04.88", "receptacleObjectId": "Ottoman|+00.49|+00.00|+04.31"}}, {"discrete_action": {"action": "GotoLocation", "args": ["coffeetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|9|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["remotecontrol"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["RemoteControl", [0.589909912, 0.589909912, 9.21712208, 9.21712208, 2.2188288, 2.2188288]], "coordinateReceptacleObjectId": ["CoffeeTable", [1.624, 1.624, 10.42, 10.42, 0.044, 0.044]], "forceVisible": true, "objectId": "RemoteControl|+00.15|+00.55|+02.30"}}, {"discrete_action": {"action": "GotoLocation", "args": ["ottoman"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|4|12|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["remotecontrol", "ottoman"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["RemoteControl", [0.589909912, 0.589909912, 9.21712208, 9.21712208, 2.2188288, 2.2188288]], "coordinateReceptacleObjectId": ["Ottoman", [1.976, 1.976, 17.236, 17.236, 0.008, 0.008]], "forceVisible": true, "objectId": "RemoteControl|+00.15|+00.55|+02.30", "receptacleObjectId": "Ottoman|+00.49|+00.00|+04.31"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "RemoteControl|+00.73|+00.44|+04.88"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [142, 140, 151, 165], "mask": [[41847, 5], [42142, 10], [42442, 10], [42742, 10], [43042, 10], [43342, 10], [43642, 10], [43942, 10], [44242, 10], [44542, 10], [44842, 10], [45142, 10], [45442, 10], [45742, 10], [46042, 10], [46342, 10], [46642, 10], [46942, 10], [47242, 10], [47542, 10], [47843, 9], [48143, 8], [48443, 8], [48743, 8], [49043, 8], [49343, 6]], "point": [146, 151]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "RemoteControl|+00.73|+00.44|+04.88", "placeStationary": true, "receptacleObjectId": "Ottoman|+00.49|+00.00|+04.31"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 114, 139, 201], "mask": [[33930, 104], [34224, 115], [34524, 115], [34823, 116], [35123, 116], [35422, 77], [35519, 20], [35721, 78], [35819, 20], [36021, 77], [36119, 20], [36320, 78], [36419, 20], [36620, 78], [36719, 20], [36919, 79], [37019, 21], [37218, 80], [37319, 21], [37518, 80], [37618, 22], [37817, 123], [38117, 123], [38416, 124], [38716, 124], [39015, 125], [39315, 125], [39614, 126], [39913, 127], [40213, 126], [40512, 127], [40812, 127], [41111, 128], [41411, 128], [41710, 129], [42010, 129], [42309, 130], [42608, 131], [42908, 131], [43207, 132], [43507, 132], [43806, 133], [44106, 133], [44405, 134], [44704, 135], [45004, 135], [45303, 136], [45603, 136], [45902, 137], [46202, 137], [46501, 138], [46801, 138], [47100, 139], [47400, 139], [47700, 139], [48000, 139], [48300, 139], [48600, 139], [48900, 139], [49200, 139], [49500, 139], [49800, 139], [50100, 139], [50401, 138], [50701, 138], [51002, 137], [51303, 136], [51603, 135], [51904, 134], [52205, 133], [52507, 131], [52808, 130], [53108, 130], [53409, 129], [53710, 128], [54010, 128], [54311, 127], [54611, 127], [54912, 126], [55213, 125], [55513, 125], [55814, 124], [56114, 124], [56415, 123], [56716, 122], [57016, 122], [57317, 121], [57617, 121], [57918, 120], [58219, 119], [58519, 114], [58820, 112], [59120, 111], [59421, 110], [59722, 109], [60023, 108]], "point": [69, 156]}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "RemoteControl|+00.15|+00.55|+02.30"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [110, 185, 160, 199], "mask": [[55311, 37], [55611, 47], [55911, 50], [56210, 51], [56510, 51], [56810, 51], [57110, 51], [57410, 51], [57710, 51], [58010, 51], [58310, 51], [58610, 51], [58910, 49], [59210, 40], [59510, 30]], "point": [135, 191]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "RemoteControl|+00.15|+00.55|+02.30", "placeStationary": true, "receptacleObjectId": "Ottoman|+00.49|+00.00|+04.31"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 114, 139, 201], "mask": [[33930, 104], [34224, 115], [34524, 115], [34823, 116], [35123, 116], [35422, 77], [35519, 20], [35721, 78], [35819, 20], [36021, 77], [36119, 20], [36320, 78], [36419, 20], [36620, 78], [36719, 20], [36919, 79], [37019, 21], [37218, 80], [37319, 21], [37518, 80], [37618, 22], [37817, 123], [38117, 123], [38416, 124], [38716, 124], [39015, 125], [39315, 125], [39614, 126], [39913, 127], [40213, 126], [40512, 127], [40812, 127], [41111, 128], [41411, 128], [41710, 129], [42010, 129], [42309, 130], [42608, 131], [42908, 131], [43207, 132], [43507, 132], [43806, 133], [44106, 133], [44405, 134], [44704, 92], [44820, 19], [45004, 82], [45120, 19], [45303, 83], [45420, 19], [45603, 83], [45720, 19], [45902, 84], [46020, 19], [46202, 83], [46320, 19], [46501, 84], [46620, 19], [46801, 89], [46920, 19], [47100, 139], [47400, 139], [47700, 139], [48000, 139], [48300, 139], [48600, 139], [48900, 139], [49200, 139], [49500, 139], [49800, 139], [50100, 139], [50401, 138], [50701, 138], [51002, 137], [51303, 136], [51603, 135], [51904, 134], [52205, 133], [52507, 131], [52808, 130], [53108, 130], [53409, 129], [53710, 128], [54010, 128], [54311, 127], [54611, 127], [54912, 126], [55213, 125], [55513, 125], [55814, 124], [56114, 124], [56415, 123], [56716, 115], [57016, 114], [57317, 113], [57617, 113], [57918, 112], [58219, 111], [58519, 111], [58820, 110], [59120, 109], [59421, 108], [59722, 107], [60023, 106]], "point": [69, 156]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan203", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -0.25, "y": 0.9070531, "z": 0.75}, "object_poses": [{"objectName": "TissueBox_4bc52e5d", "position": {"x": 0.233651638, "y": 0.5536995, "z": 2.15392065}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "TissueBox_4bc52e5d", "position": {"x": -4.65971136, "y": 0.75641036, "z": -0.626199365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Newspaper_e30ae705", "position": {"x": -1.43798637, "y": 0.456481427, "z": 2.08627272}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Newspaper_e30ae705", "position": {"x": 0.233651638, "y": 0.5584814, "z": 2.605}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Watch_d1050b3a", "position": {"x": 0.161313832, "y": 0.03754517, "z": 2.86376047}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Watch_d1050b3a", "position": {"x": -0.925605655, "y": 0.881287158, "z": 0.140353262}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Vase_218792e3", "position": {"x": -4.65971136, "y": 0.7574515, "z": -0.2554004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_ce678283", "position": {"x": -1.32099986, "y": 0.450210035, "z": 3.03172731}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a3ff949f", "position": {"x": -1.55497289, "y": 0.449996, "z": 1.84990907}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a3ff949f", "position": {"x": 0.6183046, "y": 0.03726845, "z": 2.73438048}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_7a9983d4", "position": {"x": 0.147477478, "y": 0.5547072, "z": 2.30428052}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_7a9983d4", "position": {"x": -1.67195928, "y": 0.4527073, "z": 2.32263637}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_9884a8e2", "position": {"x": 0.492174149, "y": 0.549999952, "z": 2.15392065}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Book_96dcf95a", "position": {"x": -1.55497289, "y": 0.450990736, "z": 3.03172731}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Box_44eb0c7c", "position": {"x": 0.957, "y": 0.286, "z": 6.189}, "rotation": {"x": -1.04069829e-06, "y": 327.9551, "z": -3.62398646e-06}}, {"objectName": "Pillow_d2b7d167", "position": {"x": 0.438, "y": 0.581, "z": 5.162}, "rotation": {"x": 321.968, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_022ec8f3", "position": {"x": -4.295, "y": 0.7560189, "z": -0.008201033}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WateringCan_343d2062", "position": {"x": -0.816115856, "y": 0.005195096, "z": 0.189295188}, "rotation": {"x": -0.00210049818, "y": 0.000354900782, "z": 0.0003145576}}, {"objectName": "TissueBox_4bc52e5d", "position": {"x": -4.295, "y": 0.75641036, "z": -0.2554004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Newspaper_e30ae705", "position": {"x": 0.405999959, "y": 0.5584814, "z": 3.05607939}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a3ff949f", "position": {"x": -1.08702683, "y": 0.4534337, "z": 1.84990907}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_00c9611e", "position": {"x": 0.521161, "y": 0.03810822, "z": 2.863761}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Boots_cd6ed239", "position": {"x": -6.30999327, "y": 0.006700963, "z": 1.25298953}, "rotation": {"x": 0.0228943583, "y": 92.8427, "z": 359.990021}}, {"objectName": "Pencil_8b537f35", "position": {"x": 0.405999959, "y": 0.5552911, "z": 2.605}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_9884a8e2", "position": {"x": -1.087027, "y": 0.448, "z": 2.08627272}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_218792e3", "position": {"x": 0.405999959, "y": 0.5547406, "z": 2.75535965}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_7a9983d4", "position": {"x": 0.7325863, "y": 0.436604828, "z": 4.88337469}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Watch_d1050b3a", "position": {"x": -1.34020758, "y": 0.8799507, "z": 0.101385936}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "CellPhone_ce678283", "position": {"x": 0.7091119, "y": 0.42421, "z": 4.434}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_5aac382d", "position": {"x": -4.044847, "y": 0.748803735, "z": -0.07854534}, "rotation": {"x": -0.002761599, "y": -9.421621e-05, "z": 359.991058}}], "object_toggles": [], "random_seed": 2507073341, "scene_num": 203}, "task_id": "trial_T20190906_182839_298430", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A1ELPYAFO7MANS_3MYYFCXHJ6O3TVLYL8ISGMAHZ7C4G1", "high_descs": ["Turn around and walk straight to the purple chair.", "Take the remote off the chair.", "Take a step back.", "Place the remote on the ottoman in front of the cell phone.", "Turn around hang slightly right to go to the coffee table near the couch.", "Pick the remote up off of the coffee table.", "Go straight back to the purple chair.", "Put the remote control to the left of the first one on the ottoman."], "task_desc": "Place two remote controls on an ottoman.", "votes": [1, 1, 1]}, {"assignment_id": "A24RGLS3BRZ60J_3U4J9857OHS4L1V67KOLIN3UAWO7BL", "high_descs": ["Turn around, and go to the purple chair.", "Pick up the remote from the chair.", "Turn around and then turn back around again.", "Put the remote down on the purple chair.", "Turn to the left and around the coffee table to the left. Sit down on the couch.", "Pick up the remote from the coffee table.", "Stand up with the remote, turn to the right, and then the left and find the purple chair again.", "Place the remote with the other one on the purple chair."], "task_desc": "Move one remote to another location with a second remote.", "votes": [1, 1, 1]}, {"assignment_id": "A2KAGFQU28JY43_31T4R4OBOVXNV6NCLKKGT79DVECC7K", "high_descs": ["Turn around and walk between the couch and the coffee table, then turn right and immediately to your left so that you are in front of the purple chair.", "Pick up the remote controller, from the right side of the purple arm chair. ", "Step over, so that you are on the right side of the ottoman. ", "Place the remote controller on the front, right corner of the ottoman. ", "Turn to your left and go to the end of the table, then go left again to go around the table, and to the end. ", "Pick up the remote controller on the right of the coffee table, between the newspaper and the tissue box. ", "Go around the front of the coffee table with the remote, and back to the purple ottoman. ", "Place the remote controller on the ottoman, to the left of the other remote controller."], "task_desc": "Place two remote's on the ottoman.", "votes": [1, 1]}, {"assignment_id": "A1GVTH5YS3WOK0_3VBEN272MNGMVGN9B9EAEPQOGJTSG7", "high_descs": ["Turn around, walk past the table on your right, turn right to face the chair on your left.", "Pick up the remote on the purple chair.", "Take a step back to look at the ottoman.", "Place the remote in front of the cell phone on the ottoman.", "Turn left towards the long sofa, take a left and walk to the other side of the table.", "Pick up the remote on the table.", "Turn right, take a step forward, turn left, walk past the table, and turn left towards the ottoman.", "Place the tv remote to the left of the other remote on the ottoman."], "task_desc": "Gather the two tv remotes to the ottoman.", "votes": [1, 1]}, {"assignment_id": "AWEPZMK7825JQ_3CFVK00FWO2XUA3DFN8NQCZ3RFLL6Q", "high_descs": ["Turn left 180 degrees and walk towards the purple armchair.", "Pick up the remote from the armchair. ", "Turn around and walk away, then turn back to face the foot rest.", "Place the remote on the purple foot rest.", "Turn left and walk towards the couch, then turn again towards the coffee table.", "Pick the remote up off the coffee table.", "Walk around the table to the purple foot rest.", "Place the remote next to the first remote on the foot rest."], "task_desc": "Put two remotes on the purple foot rest.", "votes": [1, 1]}]}}