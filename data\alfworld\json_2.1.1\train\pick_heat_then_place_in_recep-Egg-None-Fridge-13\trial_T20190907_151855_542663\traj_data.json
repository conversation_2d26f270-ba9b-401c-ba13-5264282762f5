{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 51}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000298.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000299.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000317.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000318.png", "low_idx": 54}, {"high_idx": 5, "image_name": "000000319.png", "low_idx": 54}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-15|14|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-17.832672, -17.832672, 14.19520092, 14.19520092, 3.83735776, 3.83735776]], "coordinateReceptacleObjectId": ["CounterTop", [-17.04, -17.04, 19.7744, 19.7744, 3.836, 3.836]], "forceVisible": true, "objectId": "Egg|-04.46|+00.96|+03.55"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-11|10|2|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|15|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-17.832672, -17.832672, 14.19520092, 14.19520092, 3.83735776, 3.83735776]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-1.298918724, -1.298918724, 14.412, 14.412, -0.00599211456, -0.00599211456]], "forceVisible": true, "objectId": "Egg|-04.46|+00.96|+03.55", "receptacleObjectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-04.46|+00.96|+03.55"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [154, 124, 172, 143], "mask": [[37061, 5], [37359, 9], [37658, 11], [37957, 13], [38256, 15], [38556, 15], [38855, 17], [39155, 17], [39455, 17], [39754, 19], [40054, 19], [40354, 19], [40654, 18], [40955, 17], [41255, 17], [41555, 16], [41856, 15], [42157, 13], [42458, 11], [42760, 7]], "point": [163, 132]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-04.46|+00.96|+03.55", "placeStationary": true, "receptacleObjectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 66], [18415, 148], [18600, 62], [18725, 138], [18900, 62], [19027, 136], [19200, 63], [19327, 136], [19500, 63], [19627, 135], [19800, 63], [19926, 136], [20100, 64], [20226, 136], [20400, 64], [20526, 136], [20700, 65], [20825, 137], [21000, 65], [21125, 137], [21300, 66], [21425, 137], [21600, 66], [21724, 138], [21900, 66], [22024, 138], [22200, 67], [22324, 137], [22500, 68], [22623, 138], [22800, 68], [22923, 138], [23100, 69], [23222, 139], [23400, 70], [23522, 139], [23700, 70], [23821, 140], [24000, 71], [24120, 141], [24300, 72], [24419, 142], [24600, 73], [24719, 142], [24900, 73], [25019, 142], [25200, 74], [25318, 142], [25500, 75], [25617, 143], [25800, 76], [25916, 144], [26100, 78], [26215, 145], [26400, 78], [26515, 145], [26700, 78], [26815, 145], [27000, 79], [27113, 147], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 66], [18415, 148], [18600, 62], [18725, 138], [18900, 62], [19027, 136], [19200, 63], [19327, 16], [19350, 113], [19500, 63], [19627, 15], [19652, 110], [19800, 63], [19926, 15], [19953, 109], [20100, 64], [20226, 14], [20254, 108], [20400, 64], [20526, 13], [20555, 107], [20700, 65], [20825, 13], [20855, 107], [21000, 65], [21125, 13], [21156, 106], [21300, 66], [21425, 12], [21457, 105], [21600, 66], [21724, 13], [21757, 105], [21900, 66], [22024, 12], [22057, 105], [22200, 67], [22324, 12], [22358, 103], [22500, 68], [22623, 13], [22658, 103], [22800, 68], [22923, 13], [22958, 103], [23100, 69], [23222, 14], [23258, 103], [23400, 70], [23522, 14], [23558, 103], [23700, 70], [23821, 15], [23858, 103], [24000, 71], [24120, 16], [24158, 103], [24300, 72], [24419, 17], [24458, 103], [24600, 73], [24719, 17], [24757, 104], [24900, 73], [25019, 18], [25057, 104], [25200, 74], [25318, 19], [25357, 103], [25500, 75], [25617, 21], [25656, 104], [25800, 76], [25916, 22], [25955, 105], [26100, 78], [26215, 24], [26255, 105], [26400, 78], [26515, 25], [26554, 106], [26700, 78], [26815, 26], [26852, 108], [27000, 79], [27113, 30], [27151, 109], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-04.46|+00.96|+03.55"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [136, 65, 157, 91], "mask": [[19343, 7], [19642, 10], [19941, 12], [20240, 14], [20539, 16], [20838, 17], [21138, 18], [21437, 20], [21737, 20], [22036, 21], [22336, 22], [22636, 22], [22936, 22], [23236, 22], [23536, 22], [23836, 22], [24136, 22], [24436, 22], [24736, 21], [25037, 20], [25337, 20], [25638, 18], [25938, 17], [26239, 16], [26540, 14], [26841, 11], [27143, 8]], "point": [146, 77]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 66], [18415, 148], [18600, 62], [18725, 138], [18900, 62], [19027, 136], [19200, 63], [19327, 136], [19500, 63], [19627, 135], [19800, 63], [19926, 136], [20100, 64], [20226, 136], [20400, 64], [20526, 136], [20700, 65], [20825, 137], [21000, 65], [21125, 137], [21300, 66], [21425, 137], [21600, 66], [21724, 138], [21900, 66], [22024, 138], [22200, 67], [22324, 137], [22500, 68], [22623, 138], [22800, 68], [22923, 138], [23100, 69], [23222, 139], [23400, 70], [23522, 139], [23700, 70], [23821, 140], [24000, 71], [24120, 141], [24300, 72], [24419, 142], [24600, 73], [24719, 142], [24900, 73], [25019, 142], [25200, 74], [25318, 142], [25500, 75], [25617, 143], [25800, 76], [25916, 144], [26100, 78], [26215, 145], [26400, 78], [26515, 145], [26700, 78], [26815, 145], [27000, 79], [27113, 147], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 199], "mask": [[0, 18600], [18601, 299], [18901, 299], [19202, 298], [19503, 297], [19803, 297], [20104, 296], [20405, 295], [20705, 295], [21006, 294], [21307, 293], [21607, 293], [21908, 292], [22208, 292], [22509, 291], [22810, 290], [23110, 290], [23411, 289], [23712, 288], [24012, 288], [24313, 287], [24613, 287], [24914, 286], [25215, 285], [25515, 285], [25816, 284], [26117, 283], [26417, 283], [26718, 282], [27018, 282], [27319, 281], [27620, 280], [27920, 280], [28221, 279], [28522, 278], [28822, 278], [29123, 277], [29423, 277], [29724, 276], [30025, 275], [30325, 275], [30626, 274], [30927, 273], [31227, 273], [31528, 272], [31828, 272], [32129, 271], [32430, 270], [32730, 270], [33031, 269], [33332, 268], [33632, 268], [33933, 267], [34234, 266], [34534, 266], [34835, 265], [35135, 265], [35439, 261], [35739, 261], [36040, 260], [36339, 261], [36639, 261], [36939, 261], [37238, 262], [37539, 261], [37839, 261], [38142, 258], [38442, 258], [38743, 257], [39044, 256], [39344, 256], [39645, 255], [39945, 255], [40246, 254], [40547, 253], [40847, 253], [41148, 252], [41449, 251], [41749, 251], [42050, 250], [42350, 250], [42651, 249], [42952, 248], [43252, 248], [43553, 247], [43854, 246], [44154, 246], [44455, 245], [44755, 245], [45056, 244], [45357, 243], [45657, 243], [45958, 242], [46259, 241], [46559, 241], [46860, 240], [47160, 240], [47461, 239], [47762, 237], [48062, 236], [48363, 234], [48664, 232], [48964, 231], [49265, 229], [49565, 228], [49866, 225], [50167, 223], [50467, 222], [50768, 220], [51069, 218], [51369, 217], [51670, 215], [51971, 213], [52271, 212], [52572, 210], [52872, 209], [53173, 207], [53474, 205], [53774, 204], [54075, 202], [54376, 200], [54676, 198], [54977, 196], [55277, 195], [55578, 193], [55879, 191], [56179, 190], [56480, 188], [56781, 186], [57081, 185], [57384, 179], [57684, 11], [57719, 109], [57851, 12], [57984, 11], [58151, 12], [58284, 10], [58451, 12], [58584, 10], [58752, 11], [58885, 9], [59052, 10], [59185, 9], [59352, 10], [59485, 9], [59652, 10]], "point": [149, 99]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-04.46|+00.96|+03.55", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 266], "mask": [[0, 147], [169, 279], [469, 279], [769, 280], [1068, 281], [1368, 282], [1668, 282], [1968, 283], [2267, 285], [2566, 287], [2865, 290], [3163, 32876], [36040, 598], [36639, 299], [36939, 599], [37539, 298], [37839, 298], [38142, 295], [38442, 295], [38743, 293], [39044, 292], [39344, 292], [39645, 291], [39945, 290], [40246, 289], [40547, 288], [40847, 287], [41148, 286], [41449, 285], [41749, 285], [42050, 283], [42350, 283], [42651, 282], [42952, 281], [43252, 280], [43553, 279], [43854, 278], [44154, 278], [44455, 276], [44755, 276], [45056, 275], [45357, 274], [45657, 273], [45958, 272], [46259, 271], [46559, 270], [46860, 269], [47160, 269], [47461, 268], [47762, 237], [48000, 28], [48062, 236], [48300, 28], [48363, 234], [48600, 28], [48664, 232], [48900, 28], [48964, 231], [49200, 27], [49265, 229], [49500, 27], [49565, 228], [49800, 27], [49866, 225], [50100, 27], [50167, 223], [50400, 26], [50467, 222], [50700, 26], [50768, 220], [51000, 26], [51069, 218], [51300, 25], [51369, 217], [51600, 25], [51670, 215], [51900, 25], [51971, 213], [52200, 25], [52271, 212], [52500, 24], [52572, 210], [52800, 24], [52872, 209], [53100, 24], [53173, 207], [53400, 24], [53474, 205], [53700, 23], [53774, 204], [54000, 23], [54075, 202], [54300, 23], [54376, 200], [54600, 23], [54676, 198], [54900, 22], [54977, 196], [55200, 22], [55277, 195], [55500, 22], [55578, 193], [55800, 22], [55879, 191], [56100, 21], [56179, 190], [56400, 21], [56480, 188], [56700, 21], [56781, 186], [57000, 20], [57081, 185], [57300, 20], [57384, 179], [57600, 20], [57684, 11], [57719, 109], [57851, 12], [57900, 20], [57984, 11], [58151, 12], [58200, 19], [58284, 10], [58451, 12], [58500, 19], [58584, 10], [58752, 11], [58800, 19], [58885, 9], [59052, 10], [59100, 19], [59185, 9], [59352, 10], [59400, 18], [59485, 9], [59652, 10], [59700, 18], [60000, 18], [60300, 18], [60600, 17], [60900, 17], [61200, 17], [61500, 17], [61800, 16], [62100, 16], [62400, 16], [62700, 15], [63000, 15], [63300, 15], [63600, 15], [63900, 14], [64200, 14], [64500, 14], [64800, 14], [65100, 13], [65400, 13], [65700, 13], [66000, 13], [66300, 12], [66600, 12], [66900, 12], [67200, 11], [67500, 11], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 10], [69300, 10], [69600, 9], [69900, 9], [70200, 9], [70500, 9], [70800, 8], [71100, 8], [71400, 8], [71700, 8], [72000, 7], [72300, 7], [72600, 7], [72900, 6], [73200, 6], [73500, 6], [73800, 6], [74100, 5], [74400, 5], [74700, 5], [75000, 5], [75300, 4], [75600, 4], [75900, 4], [76200, 4], [76500, 3], [76800, 3], [77100, 3], [77400, 3], [77700, 2], [78000, 2], [78300, 2], [78600, 1], [78900, 1], [79200, 1], [79500, 1]], "point": [149, 132]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 266], "mask": [[0, 110], [121, 26], [169, 240], [422, 26], [469, 239], [723, 25], [769, 239], [1024, 25], [1068, 239], [1324, 25], [1368, 239], [1625, 25], [1668, 238], [1925, 25], [1968, 238], [2226, 25], [2267, 239], [2526, 26], [2566, 239], [2827, 26], [2865, 240], [3127, 28], [3163, 242], [3427, 278], [3727, 278], [4027, 278], [4328, 277], [4628, 278], [4928, 278], [5227, 279], [5527, 280], [5827, 280], [6126, 282], [6426, 282], [6725, 284], [7025, 286], [7324, 288], [7622, 292], [7920, 28119], [36040, 598], [36639, 299], [36939, 599], [37539, 298], [37839, 298], [38142, 295], [38442, 295], [38743, 293], [39044, 292], [39344, 292], [39645, 291], [39945, 290], [40246, 289], [40547, 288], [40847, 287], [41148, 286], [41449, 285], [41749, 285], [42050, 283], [42350, 283], [42651, 282], [42952, 281], [43252, 280], [43553, 279], [43854, 278], [44154, 278], [44455, 276], [44755, 276], [45056, 275], [45357, 274], [45657, 273], [45958, 272], [46259, 271], [46559, 270], [46860, 269], [47160, 269], [47461, 268], [47762, 237], [48000, 28], [48062, 236], [48300, 28], [48363, 234], [48600, 28], [48664, 232], [48900, 28], [48964, 231], [49200, 27], [49265, 229], [49500, 27], [49565, 228], [49800, 27], [49866, 225], [50100, 27], [50167, 223], [50400, 26], [50467, 222], [50700, 26], [50768, 220], [51000, 26], [51069, 218], [51300, 25], [51369, 217], [51600, 25], [51670, 215], [51900, 25], [51971, 213], [52200, 25], [52271, 212], [52500, 24], [52572, 210], [52800, 24], [52872, 209], [53100, 24], [53173, 207], [53400, 24], [53474, 205], [53700, 23], [53774, 204], [54000, 23], [54075, 202], [54300, 23], [54376, 200], [54600, 23], [54676, 198], [54900, 22], [54977, 196], [55200, 22], [55277, 195], [55500, 22], [55578, 193], [55800, 22], [55879, 191], [56100, 21], [56179, 190], [56400, 21], [56480, 188], [56700, 21], [56781, 186], [57000, 20], [57081, 185], [57300, 20], [57384, 179], [57600, 20], [57684, 11], [57719, 109], [57851, 12], [57900, 20], [57984, 11], [58151, 12], [58200, 19], [58284, 10], [58451, 12], [58500, 19], [58584, 10], [58752, 11], [58800, 19], [58885, 9], [59052, 10], [59100, 19], [59185, 9], [59352, 10], [59400, 18], [59485, 9], [59652, 10], [59700, 18], [60000, 18], [60300, 18], [60600, 17], [60900, 17], [61200, 17], [61500, 17], [61800, 16], [62100, 16], [62400, 16], [62700, 15], [63000, 15], [63300, 15], [63600, 15], [63900, 14], [64200, 14], [64500, 14], [64800, 14], [65100, 13], [65400, 13], [65700, 13], [66000, 13], [66300, 12], [66600, 12], [66900, 12], [67200, 11], [67500, 11], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 10], [69300, 10], [69600, 9], [69900, 9], [70200, 9], [70500, 9], [70800, 8], [71100, 8], [71400, 8], [71700, 8], [72000, 7], [72300, 7], [72600, 7], [72900, 6], [73200, 6], [73500, 6], [73800, 6], [74100, 5], [74400, 5], [74700, 5], [75000, 5], [75300, 4], [75600, 4], [75900, 4], [76200, 4], [76500, 3], [76800, 3], [77100, 3], [77400, 3], [77700, 2], [78000, 2], [78300, 2], [78600, 1], [78900, 1], [79200, 1], [79500, 1]], "point": [149, 132]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan13", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -1.5, "y": 0.8995012, "z": 3.25}, "object_poses": [{"objectName": "Pan_94f6c891", "position": {"x": -0.301119149, "y": 1.58046627, "z": 3.28908253}, "rotation": {"x": 0.0, "y": 180.000153, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -4.24220753, "y": 0.9237911, "z": 2.53327823}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -3.25512767, "y": 0.9237911, "z": 5.56610966}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.401854247, "y": 1.10402477, "z": 3.72675037}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -4.12177229, "y": 0.7905213, "z": 3.222413}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -0.488323241, "y": 0.9251421, "z": 5.932334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -0.258274853, "y": 0.9251421, "z": 4.615}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -3.73307371, "y": 0.924695969, "z": 5.33607531}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -4.031869, "y": 0.924048543, "z": 1.64899063}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -0.181592077, "y": 0.92564857, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -4.09417439, "y": 0.756804943, "z": 2.59057426}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -4.19639969, "y": 0.7920865, "z": 3.159811}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Egg_ab4d2337", "position": {"x": -4.458168, "y": 0.95933944, "z": 3.54880023}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -1.777005, "y": 0.9593395, "z": 1.8}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -4.27102757, "y": 0.8267966, "z": 3.22241259}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Plate_53fee662", "position": {"x": -0.334957659, "y": 0.92592597, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_53fee662", "position": {"x": -0.259895, "y": 1.33678281, "z": 3.97424936}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -0.411640465, "y": 0.9548003, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -4.051113, "y": 0.07768291, "z": 4.813544}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -3.784541, "y": 0.06842968, "z": 6.41615725}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -4.1590867, "y": 0.7879045, "z": 3.41021824}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -1.82931209, "y": 1.45018387, "z": 1.63855672}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -4.12837172, "y": 0.07426733, "z": 4.66053}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -0.4098, "y": 0.9556, "z": 6.55700064}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -0.565006, "y": 0.9609395, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -2.02637982, "y": 0.976970434, "z": 1.9510051}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -1.777005, "y": 0.9532003, "z": 1.72449732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.565006, "y": 0.958568037, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -3.25512767, "y": 0.9240485, "z": 5.182719}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -2.58304977, "y": 1.54316556, "z": 1.6662966}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_53fee662", "position": {"x": -0.258274853, "y": 0.92592597, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -1.69388, "y": 1.01892865, "z": 1.9510051}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -4.411006, "y": 1.00305462, "z": 3.86360288}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -0.218674958, "y": 1.74289978, "z": 3.7942822}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -4.10899448, "y": 0.9244287, "z": 4.90006828}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -3.49410057, "y": 0.9384998, "z": 5.182719}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_aa4cec24", "position": {"x": -1.86012983, "y": 1.0277853, "z": 1.87550259}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -3.85912442, "y": 0.9223595, "z": 2.02650476}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -2.109505, "y": 0.92379117, "z": 1.72449732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_94f6c891", "position": {"x": -2.45436978, "y": 0.9335794, "z": 2.0230875}, "rotation": {"x": 0.0, "y": 240.000244, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -2.92400026, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -0.488323241, "y": 0.92140615, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -2.02637982, "y": 0.9232217, "z": 2.02650762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -4.09782028, "y": 0.0767763257, "z": 3.328887}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -1.69388, "y": 0.9235421, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -0.201670259, "y": 2.13415074, "z": 5.36475945}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -0.181592077, "y": 0.9262294, "z": 5.932334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -0.383562922, "y": 1.33095682, "z": 3.47925}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 3014903285, "scene_num": 13}, "task_id": "trial_T20190907_151855_542663", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1GVTH5YS3WOK0_352YTHGROYUHTRG95E0A1MMJHM04HA", "high_descs": ["Turn around and head towards the sink.", "Pick up the egg next to the faucet.", "Turn left and head towards the microwave above the stove.", "Open the microwave, heat up the egg, and then take it back out of the microwave.", "Turn around and head towards the fridge to your right.", "Place the egg inside the fridge and close it."], "task_desc": "Place the warmed egg inside the fridge.", "votes": [1, 1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A36DK84J5YJ942_3GU1KF0O4LITR7Z636I2I9UGD6KPB1", "high_descs": ["Move to the sink to the right of the stove.", "Pick up an egg from the counter.", "Move to the microwave above the stove. ", "Heat up the egg inside the microwave and close the door after doing so.", "Move to the fridge to the left of you.", "Place the egg on the bottom self of the fridge."], "task_desc": "Place a heated egg on the bottom shelf of the fridge.", "votes": [1, 1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A3MLUEOP3CCLXL_326O153BMLFI9D808OTXJNJC2J8DEN", "high_descs": ["Turn around and cross the room to the sink", "Pick up the egg that is on the counter to the right of the faucet", "Turn around and walk to the microwave that is above stove on your right", "Place the egg in the microwave to the right of the bowl then close the microwave and turn it on.  Open the microwave and pick up the egg then close the microwave", "Turn around and walk to the fridge that is on your right", "Open the fridge and place the egg on the bottom shelf then close the fridge"], "task_desc": "Place a cooked egg in a fridge", "votes": [1, 1, 1, 1, 1, 1]}, {"assignment_id": "A24RGLS3BRZ60J_3R3YRB5GRIK1022AEEH0X4X93CCAUM", "high_descs": ["Turn around and go back to the sink.", "Pick up the egg from the counter behind the sink.", "Hold the egg and turn around and go right to the stove.", "Place the egg in the microwave and turn it on. Take the egg out and shut the door.", "Carry the egg and turn around and then right to go to the fridge.", "Open the fridge and place the egg inside. Shut the fridge."], "task_desc": "Heat and chill an egg.", "votes": [1, 1]}]}}