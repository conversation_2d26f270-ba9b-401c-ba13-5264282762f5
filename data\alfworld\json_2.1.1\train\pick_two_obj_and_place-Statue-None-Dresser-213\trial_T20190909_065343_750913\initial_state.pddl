
(define (problem plan_trial_T20190909_065343_750913)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Book_bar__plus_14_dot_14_bar__plus_00_dot_75_bar__plus_01_dot_30 - object
        Book_bar__plus_14_dot_14_bar__plus_00_dot_76_bar__plus_00_dot_52 - object
        Book_bar__plus_14_dot_33_bar__plus_00_dot_75_bar__plus_01_dot_56 - object
        Box_bar__plus_11_dot_43_bar__plus_00_dot_84_bar__plus_00_dot_86 - object
        Box_bar__plus_11_dot_88_bar__plus_00_dot_71_bar__plus_03_dot_30 - object
        Chair_bar__plus_08_dot_16_bar__plus_00_dot_00_bar__plus_01_dot_50 - object
        CreditCard_bar__plus_11_dot_51_bar__plus_00_dot_49_bar__minus_01_dot_07 - object
        CreditCard_bar__plus_14_dot_23_bar__plus_00_dot_75_bar__plus_01_dot_04 - object
        FloorLamp_bar__plus_07_dot_86_bar__plus_00_dot_00_bar__plus_03_dot_40 - object
        HousePlant_bar__plus_11_dot_37_bar__plus_00_dot_48_bar__minus_01_dot_14 - object
        KeyChain_bar__plus_10_dot_26_bar__plus_00_dot_59_bar__plus_00_dot_77 - object
        Laptop_bar__plus_09_dot_40_bar__plus_00_dot_43_bar__plus_03_dot_14 - object
        Laptop_bar__plus_14_dot_24_bar__plus_00_dot_75_bar__plus_02_dot_07 - object
        LightSwitch_bar__plus_12_dot_98_bar__plus_01_dot_40_bar__plus_03_dot_77 - object
        Painting_bar__plus_07_dot_49_bar__plus_01_dot_78_bar__plus_01_dot_12 - object
        Painting_bar__plus_07_dot_49_bar__plus_01_dot_84_bar__plus_02_dot_79 - object
        Painting_bar__plus_07_dot_49_bar__plus_01_dot_84_bar__minus_00_dot_50 - object
        Painting_bar__plus_14_dot_76_bar__plus_01_dot_75_bar__plus_01_dot_13 - object
        Pillow_bar__plus_10_dot_81_bar__plus_00_dot_53_bar__plus_03_dot_16 - object
        Pillow_bar__plus_11_dot_23_bar__plus_00_dot_53_bar__plus_03_dot_16 - object
        RemoteControl_bar__plus_14_dot_62_bar__plus_00_dot_52_bar__minus_00_dot_84 - object
        Statue_bar__plus_10_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_68 - object
        Statue_bar__plus_10_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_86 - object
        Statue_bar__plus_10_dot_65_bar__plus_00_dot_60_bar__plus_01_dot_24 - object
        Statue_bar__plus_10_dot_85_bar__plus_00_dot_60_bar__plus_00_dot_68 - object
        Television_bar__plus_14_dot_45_bar__plus_01_dot_24_bar__plus_00_dot_93 - object
        Watch_bar__plus_14_dot_15_bar__plus_00_dot_55_bar__plus_01_dot_45 - object
        Window_bar__plus_10_dot_23_bar__plus_01_dot_69_bar__minus_01_dot_59 - object
        Window_bar__plus_12_dot_03_bar__plus_01_dot_69_bar__minus_01_dot_60 - object
        ArmChair_bar__plus_08_dot_23_bar__plus_00_dot_00_bar__plus_00_dot_77 - receptacle
        ArmChair_bar__plus_08_dot_23_bar__plus_00_dot_00_bar__minus_00_dot_08 - receptacle
        CoffeeTable_bar__plus_10_dot_82_bar__plus_00_dot_01_bar__plus_00_dot_95 - receptacle
        DiningTable_bar__plus_09_dot_50_bar__minus_00_dot_02_bar__plus_03_dot_27 - receptacle
        Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_00_dot_54 - receptacle
        Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_01_dot_05 - receptacle
        Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_01_dot_56 - receptacle
        Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_02_dot_07 - receptacle
        Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_00_dot_54 - receptacle
        Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_01_dot_05 - receptacle
        Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_01_dot_56 - receptacle
        Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_02_dot_07 - receptacle
        Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_00_dot_54 - receptacle
        Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_01_dot_05 - receptacle
        Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_01_dot_56 - receptacle
        Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_02_dot_07 - receptacle
        Dresser_bar__plus_14_dot_33_bar__plus_00_dot_02_bar__plus_01_dot_32 - receptacle
        GarbageCan_bar__plus_07_dot_79_bar__plus_00_dot_00_bar__minus_01_dot_15 - receptacle
        SideTable_bar__plus_11_dot_38_bar__plus_00_dot_00_bar__minus_01_dot_16 - receptacle
        SideTable_bar__plus_12_dot_31_bar__plus_00_dot_01_bar__minus_01_dot_16 - receptacle
        SideTable_bar__plus_14_dot_62_bar__plus_00_dot_00_bar__minus_00_dot_78 - receptacle
        Sofa_bar__plus_11_dot_45_bar__minus_00_dot_07_bar__plus_03_dot_20 - receptacle
        loc_bar_53_bar_6_bar_1_bar_45 - location
        loc_bar_43_bar_8_bar_2_bar_60 - location
        loc_bar_52_bar_4_bar_1_bar_45 - location
        loc_bar_53_bar_4_bar_1_bar_45 - location
        loc_bar_41_bar__minus_4_bar_2_bar_0 - location
        loc_bar_52_bar_0_bar_1_bar_45 - location
        loc_bar_53_bar_10_bar_1_bar_45 - location
        loc_bar_36_bar_3_bar_3_bar_60 - location
        loc_bar_38_bar_9_bar_0_bar_60 - location
        loc_bar_46_bar_8_bar_0_bar_60 - location
        loc_bar_35_bar_4_bar_3_bar_0 - location
        loc_bar_52_bar_6_bar_1_bar_45 - location
        loc_bar_53_bar_7_bar_1_bar_60 - location
        loc_bar_55_bar__minus_1_bar_1_bar_60 - location
        loc_bar_51_bar__minus_1_bar_2_bar_60 - location
        loc_bar_54_bar_5_bar_1_bar_60 - location
        loc_bar_32_bar_11_bar_3_bar__minus_15 - location
        loc_bar_53_bar_3_bar_1_bar_60 - location
        loc_bar_53_bar_8_bar_1_bar_45 - location
        loc_bar_52_bar_13_bar_0_bar_30 - location
        loc_bar_42_bar__minus_3_bar_1_bar_60 - location
        loc_bar_32_bar__minus_3_bar_3_bar__minus_15 - location
        loc_bar_53_bar_5_bar_1_bar_60 - location
        loc_bar_32_bar_11_bar_0_bar_60 - location
        loc_bar_47_bar__minus_3_bar_2_bar_0 - location
        loc_bar_36_bar_0_bar_3_bar_60 - location
        loc_bar_32_bar__minus_3_bar_2_bar_60 - location
        loc_bar_35_bar_6_bar_3_bar_60 - location
        loc_bar_52_bar_2_bar_1_bar_45 - location
        loc_bar_54_bar_5_bar_1_bar_0 - location
        loc_bar_55_bar_12_bar_1_bar_30 - location
        )
    

(:init


        (receptacleType Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_00_dot_54 DrawerType)
        (receptacleType Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_01_dot_56 DrawerType)
        (receptacleType Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_00_dot_54 DrawerType)
        (receptacleType Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_00_dot_54 DrawerType)
        (receptacleType CoffeeTable_bar__plus_10_dot_82_bar__plus_00_dot_01_bar__plus_00_dot_95 CoffeeTableType)
        (receptacleType SideTable_bar__plus_11_dot_38_bar__plus_00_dot_00_bar__minus_01_dot_16 SideTableType)
        (receptacleType DiningTable_bar__plus_09_dot_50_bar__minus_00_dot_02_bar__plus_03_dot_27 DiningTableType)
        (receptacleType Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_02_dot_07 DrawerType)
        (receptacleType Sofa_bar__plus_11_dot_45_bar__minus_00_dot_07_bar__plus_03_dot_20 SofaType)
        (receptacleType Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_02_dot_07 DrawerType)
        (receptacleType ArmChair_bar__plus_08_dot_23_bar__plus_00_dot_00_bar__plus_00_dot_77 ArmChairType)
        (receptacleType Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_02_dot_07 DrawerType)
        (receptacleType GarbageCan_bar__plus_07_dot_79_bar__plus_00_dot_00_bar__minus_01_dot_15 GarbageCanType)
        (receptacleType SideTable_bar__plus_14_dot_62_bar__plus_00_dot_00_bar__minus_00_dot_78 SideTableType)
        (receptacleType Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_01_dot_05 DrawerType)
        (receptacleType Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_01_dot_05 DrawerType)
        (receptacleType Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_01_dot_56 DrawerType)
        (receptacleType Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_01_dot_56 DrawerType)
        (receptacleType ArmChair_bar__plus_08_dot_23_bar__plus_00_dot_00_bar__minus_00_dot_08 ArmChairType)
        (receptacleType Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_01_dot_05 DrawerType)
        (receptacleType SideTable_bar__plus_12_dot_31_bar__plus_00_dot_01_bar__minus_01_dot_16 SideTableType)
        (receptacleType Dresser_bar__plus_14_dot_33_bar__plus_00_dot_02_bar__plus_01_dot_32 DresserType)
        (objectType Painting_bar__plus_07_dot_49_bar__plus_01_dot_84_bar__minus_00_dot_50 PaintingType)
        (objectType Box_bar__plus_11_dot_88_bar__plus_00_dot_71_bar__plus_03_dot_30 BoxType)
        (objectType KeyChain_bar__plus_10_dot_26_bar__plus_00_dot_59_bar__plus_00_dot_77 KeyChainType)
        (objectType Laptop_bar__plus_14_dot_24_bar__plus_00_dot_75_bar__plus_02_dot_07 LaptopType)
        (objectType RemoteControl_bar__plus_14_dot_62_bar__plus_00_dot_52_bar__minus_00_dot_84 RemoteControlType)
        (objectType FloorLamp_bar__plus_07_dot_86_bar__plus_00_dot_00_bar__plus_03_dot_40 FloorLampType)
        (objectType Watch_bar__plus_14_dot_15_bar__plus_00_dot_55_bar__plus_01_dot_45 WatchType)
        (objectType Chair_bar__plus_08_dot_16_bar__plus_00_dot_00_bar__plus_01_dot_50 ChairType)
        (objectType Box_bar__plus_11_dot_43_bar__plus_00_dot_84_bar__plus_00_dot_86 BoxType)
        (objectType Statue_bar__plus_10_dot_85_bar__plus_00_dot_60_bar__plus_00_dot_68 StatueType)
        (objectType Painting_bar__plus_07_dot_49_bar__plus_01_dot_78_bar__plus_01_dot_12 PaintingType)
        (objectType Statue_bar__plus_10_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_68 StatueType)
        (objectType Television_bar__plus_14_dot_45_bar__plus_01_dot_24_bar__plus_00_dot_93 TelevisionType)
        (objectType Statue_bar__plus_10_dot_65_bar__plus_00_dot_60_bar__plus_01_dot_24 StatueType)
        (objectType LightSwitch_bar__plus_12_dot_98_bar__plus_01_dot_40_bar__plus_03_dot_77 LightSwitchType)
        (objectType HousePlant_bar__plus_11_dot_37_bar__plus_00_dot_48_bar__minus_01_dot_14 HousePlantType)
        (objectType CreditCard_bar__plus_11_dot_51_bar__plus_00_dot_49_bar__minus_01_dot_07 CreditCardType)
        (objectType Statue_bar__plus_10_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_86 StatueType)
        (objectType Book_bar__plus_14_dot_33_bar__plus_00_dot_75_bar__plus_01_dot_56 BookType)
        (objectType Painting_bar__plus_07_dot_49_bar__plus_01_dot_84_bar__plus_02_dot_79 PaintingType)
        (objectType Pillow_bar__plus_10_dot_81_bar__plus_00_dot_53_bar__plus_03_dot_16 PillowType)
        (objectType Window_bar__plus_10_dot_23_bar__plus_01_dot_69_bar__minus_01_dot_59 WindowType)
        (objectType Pillow_bar__plus_11_dot_23_bar__plus_00_dot_53_bar__plus_03_dot_16 PillowType)
        (objectType Painting_bar__plus_14_dot_76_bar__plus_01_dot_75_bar__plus_01_dot_13 PaintingType)
        (objectType Window_bar__plus_12_dot_03_bar__plus_01_dot_69_bar__minus_01_dot_60 WindowType)
        (objectType Book_bar__plus_14_dot_14_bar__plus_00_dot_75_bar__plus_01_dot_30 BookType)
        (objectType CreditCard_bar__plus_14_dot_23_bar__plus_00_dot_75_bar__plus_01_dot_04 CreditCardType)
        (objectType Book_bar__plus_14_dot_14_bar__plus_00_dot_76_bar__plus_00_dot_52 BookType)
        (objectType Laptop_bar__plus_09_dot_40_bar__plus_00_dot_43_bar__plus_03_dot_14 LaptopType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain CoffeeTableType BookType)
        (canContain CoffeeTableType WatchType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain SideTableType BookType)
        (canContain SideTableType WatchType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain DiningTableType BookType)
        (canContain DiningTableType WatchType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType StatueType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType BookType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType CreditCardType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain SideTableType BookType)
        (canContain SideTableType WatchType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain SideTableType BookType)
        (canContain SideTableType WatchType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain DresserType BookType)
        (canContain DresserType WatchType)
        (canContain DresserType BoxType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType LaptopType)
        (canContain DresserType RemoteControlType)
        (canContain DresserType StatueType)
        (pickupable Box_bar__plus_11_dot_88_bar__plus_00_dot_71_bar__plus_03_dot_30)
        (pickupable KeyChain_bar__plus_10_dot_26_bar__plus_00_dot_59_bar__plus_00_dot_77)
        (pickupable Laptop_bar__plus_14_dot_24_bar__plus_00_dot_75_bar__plus_02_dot_07)
        (pickupable RemoteControl_bar__plus_14_dot_62_bar__plus_00_dot_52_bar__minus_00_dot_84)
        (pickupable Watch_bar__plus_14_dot_15_bar__plus_00_dot_55_bar__plus_01_dot_45)
        (pickupable Box_bar__plus_11_dot_43_bar__plus_00_dot_84_bar__plus_00_dot_86)
        (pickupable Statue_bar__plus_10_dot_85_bar__plus_00_dot_60_bar__plus_00_dot_68)
        (pickupable Statue_bar__plus_10_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_68)
        (pickupable Statue_bar__plus_10_dot_65_bar__plus_00_dot_60_bar__plus_01_dot_24)
        (pickupable CreditCard_bar__plus_11_dot_51_bar__plus_00_dot_49_bar__minus_01_dot_07)
        (pickupable Statue_bar__plus_10_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_86)
        (pickupable Book_bar__plus_14_dot_33_bar__plus_00_dot_75_bar__plus_01_dot_56)
        (pickupable Pillow_bar__plus_10_dot_81_bar__plus_00_dot_53_bar__plus_03_dot_16)
        (pickupable Pillow_bar__plus_11_dot_23_bar__plus_00_dot_53_bar__plus_03_dot_16)
        (pickupable Book_bar__plus_14_dot_14_bar__plus_00_dot_75_bar__plus_01_dot_30)
        (pickupable CreditCard_bar__plus_14_dot_23_bar__plus_00_dot_75_bar__plus_01_dot_04)
        (pickupable Book_bar__plus_14_dot_14_bar__plus_00_dot_76_bar__plus_00_dot_52)
        (pickupable Laptop_bar__plus_09_dot_40_bar__plus_00_dot_43_bar__plus_03_dot_14)
        (isReceptacleObject Box_bar__plus_11_dot_88_bar__plus_00_dot_71_bar__plus_03_dot_30)
        (isReceptacleObject Box_bar__plus_11_dot_43_bar__plus_00_dot_84_bar__plus_00_dot_86)
        (openable Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_00_dot_54)
        (openable Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_01_dot_56)
        (openable Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_00_dot_54)
        (openable Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_00_dot_54)
        (openable Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_02_dot_07)
        (openable Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_02_dot_07)
        (openable Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_02_dot_07)
        (openable Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_01_dot_05)
        (openable Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_01_dot_05)
        (openable Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_01_dot_56)
        (openable Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_01_dot_56)
        (openable Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_01_dot_05)
        
        (atLocation agent1 loc_bar_55_bar_12_bar_1_bar_30)
        
        
        
        
        
        
        
        (toggleable FloorLamp_bar__plus_07_dot_86_bar__plus_00_dot_00_bar__plus_03_dot_40)
        
        
        
        
        (inReceptacle Pillow_bar__plus_11_dot_23_bar__plus_00_dot_53_bar__plus_03_dot_16 Sofa_bar__plus_11_dot_45_bar__minus_00_dot_07_bar__plus_03_dot_20)
        (inReceptacle Box_bar__plus_11_dot_88_bar__plus_00_dot_71_bar__plus_03_dot_30 Sofa_bar__plus_11_dot_45_bar__minus_00_dot_07_bar__plus_03_dot_20)
        (inReceptacle Pillow_bar__plus_10_dot_81_bar__plus_00_dot_53_bar__plus_03_dot_16 Sofa_bar__plus_11_dot_45_bar__minus_00_dot_07_bar__plus_03_dot_20)
        (inReceptacle Laptop_bar__plus_09_dot_40_bar__plus_00_dot_43_bar__plus_03_dot_14 DiningTable_bar__plus_09_dot_50_bar__minus_00_dot_02_bar__plus_03_dot_27)
        (inReceptacle RemoteControl_bar__plus_14_dot_62_bar__plus_00_dot_52_bar__minus_00_dot_84 SideTable_bar__plus_14_dot_62_bar__plus_00_dot_00_bar__minus_00_dot_78)
        (inReceptacle Laptop_bar__plus_14_dot_24_bar__plus_00_dot_75_bar__plus_02_dot_07 Dresser_bar__plus_14_dot_33_bar__plus_00_dot_02_bar__plus_01_dot_32)
        (inReceptacle Television_bar__plus_14_dot_45_bar__plus_01_dot_24_bar__plus_00_dot_93 Dresser_bar__plus_14_dot_33_bar__plus_00_dot_02_bar__plus_01_dot_32)
        (inReceptacle Book_bar__plus_14_dot_14_bar__plus_00_dot_75_bar__plus_01_dot_30 Dresser_bar__plus_14_dot_33_bar__plus_00_dot_02_bar__plus_01_dot_32)
        (inReceptacle CreditCard_bar__plus_14_dot_23_bar__plus_00_dot_75_bar__plus_01_dot_04 Dresser_bar__plus_14_dot_33_bar__plus_00_dot_02_bar__plus_01_dot_32)
        (inReceptacle Book_bar__plus_14_dot_14_bar__plus_00_dot_76_bar__plus_00_dot_52 Dresser_bar__plus_14_dot_33_bar__plus_00_dot_02_bar__plus_01_dot_32)
        (inReceptacle Book_bar__plus_14_dot_33_bar__plus_00_dot_75_bar__plus_01_dot_56 Dresser_bar__plus_14_dot_33_bar__plus_00_dot_02_bar__plus_01_dot_32)
        (inReceptacle Watch_bar__plus_14_dot_15_bar__plus_00_dot_55_bar__plus_01_dot_45 Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_01_dot_56)
        (inReceptacle Statue_bar__plus_10_dot_85_bar__plus_00_dot_60_bar__plus_00_dot_68 CoffeeTable_bar__plus_10_dot_82_bar__plus_00_dot_01_bar__plus_00_dot_95)
        (inReceptacle KeyChain_bar__plus_10_dot_26_bar__plus_00_dot_59_bar__plus_00_dot_77 CoffeeTable_bar__plus_10_dot_82_bar__plus_00_dot_01_bar__plus_00_dot_95)
        (inReceptacle Statue_bar__plus_10_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_68 CoffeeTable_bar__plus_10_dot_82_bar__plus_00_dot_01_bar__plus_00_dot_95)
        (inReceptacle Statue_bar__plus_10_dot_65_bar__plus_00_dot_60_bar__plus_01_dot_24 CoffeeTable_bar__plus_10_dot_82_bar__plus_00_dot_01_bar__plus_00_dot_95)
        (inReceptacle Box_bar__plus_11_dot_43_bar__plus_00_dot_84_bar__plus_00_dot_86 CoffeeTable_bar__plus_10_dot_82_bar__plus_00_dot_01_bar__plus_00_dot_95)
        (inReceptacle Statue_bar__plus_10_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_86 CoffeeTable_bar__plus_10_dot_82_bar__plus_00_dot_01_bar__plus_00_dot_95)
        (inReceptacle CreditCard_bar__plus_11_dot_51_bar__plus_00_dot_49_bar__minus_01_dot_07 SideTable_bar__plus_11_dot_38_bar__plus_00_dot_00_bar__minus_01_dot_16)
        (inReceptacle HousePlant_bar__plus_11_dot_37_bar__plus_00_dot_48_bar__minus_01_dot_14 SideTable_bar__plus_11_dot_38_bar__plus_00_dot_00_bar__minus_01_dot_16)
        
        
        (receptacleAtLocation ArmChair_bar__plus_08_dot_23_bar__plus_00_dot_00_bar__plus_00_dot_77 loc_bar_36_bar_3_bar_3_bar_60)
        (receptacleAtLocation ArmChair_bar__plus_08_dot_23_bar__plus_00_dot_00_bar__minus_00_dot_08 loc_bar_36_bar_0_bar_3_bar_60)
        (receptacleAtLocation CoffeeTable_bar__plus_10_dot_82_bar__plus_00_dot_01_bar__plus_00_dot_95 loc_bar_43_bar_8_bar_2_bar_60)
        (receptacleAtLocation DiningTable_bar__plus_09_dot_50_bar__minus_00_dot_02_bar__plus_03_dot_27 loc_bar_38_bar_9_bar_0_bar_60)
        (receptacleAtLocation Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_00_dot_54 loc_bar_52_bar_0_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_01_dot_05 loc_bar_52_bar_2_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_01_dot_56 loc_bar_52_bar_4_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_14_dot_18_bar__plus_00_dot_19_bar__plus_02_dot_07 loc_bar_52_bar_6_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_00_dot_54 loc_bar_53_bar_3_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_01_dot_05 loc_bar_53_bar_3_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_01_dot_56 loc_bar_53_bar_5_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_14_dot_18_bar__plus_00_dot_39_bar__plus_02_dot_07 loc_bar_53_bar_7_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_00_dot_54 loc_bar_53_bar_4_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_01_dot_05 loc_bar_53_bar_6_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_01_dot_56 loc_bar_53_bar_8_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_14_dot_18_bar__plus_00_dot_60_bar__plus_02_dot_07 loc_bar_53_bar_10_bar_1_bar_45)
        (receptacleAtLocation Dresser_bar__plus_14_dot_33_bar__plus_00_dot_02_bar__plus_01_dot_32 loc_bar_54_bar_5_bar_1_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_07_dot_79_bar__plus_00_dot_00_bar__minus_01_dot_15 loc_bar_32_bar__minus_3_bar_2_bar_60)
        (receptacleAtLocation SideTable_bar__plus_11_dot_38_bar__plus_00_dot_00_bar__minus_01_dot_16 loc_bar_42_bar__minus_3_bar_1_bar_60)
        (receptacleAtLocation SideTable_bar__plus_12_dot_31_bar__plus_00_dot_01_bar__minus_01_dot_16 loc_bar_51_bar__minus_1_bar_2_bar_60)
        (receptacleAtLocation SideTable_bar__plus_14_dot_62_bar__plus_00_dot_00_bar__minus_00_dot_78 loc_bar_55_bar__minus_1_bar_1_bar_60)
        (receptacleAtLocation Sofa_bar__plus_11_dot_45_bar__minus_00_dot_07_bar__plus_03_dot_20 loc_bar_46_bar_8_bar_0_bar_60)
        (objectAtLocation Laptop_bar__plus_14_dot_24_bar__plus_00_dot_75_bar__plus_02_dot_07 loc_bar_54_bar_5_bar_1_bar_60)
        (objectAtLocation Statue_bar__plus_10_dot_85_bar__plus_00_dot_60_bar__plus_00_dot_68 loc_bar_43_bar_8_bar_2_bar_60)
        (objectAtLocation Pillow_bar__plus_11_dot_23_bar__plus_00_dot_53_bar__plus_03_dot_16 loc_bar_46_bar_8_bar_0_bar_60)
        (objectAtLocation CreditCard_bar__plus_11_dot_51_bar__plus_00_dot_49_bar__minus_01_dot_07 loc_bar_42_bar__minus_3_bar_1_bar_60)
        (objectAtLocation Box_bar__plus_11_dot_43_bar__plus_00_dot_84_bar__plus_00_dot_86 loc_bar_43_bar_8_bar_2_bar_60)
        (objectAtLocation Book_bar__plus_14_dot_33_bar__plus_00_dot_75_bar__plus_01_dot_56 loc_bar_54_bar_5_bar_1_bar_60)
        (objectAtLocation Book_bar__plus_14_dot_14_bar__plus_00_dot_76_bar__plus_00_dot_52 loc_bar_54_bar_5_bar_1_bar_60)
        (objectAtLocation Statue_bar__plus_10_dot_65_bar__plus_00_dot_60_bar__plus_01_dot_24 loc_bar_43_bar_8_bar_2_bar_60)
        (objectAtLocation Statue_bar__plus_10_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_86 loc_bar_43_bar_8_bar_2_bar_60)
        (objectAtLocation Book_bar__plus_14_dot_14_bar__plus_00_dot_75_bar__plus_01_dot_30 loc_bar_54_bar_5_bar_1_bar_60)
        (objectAtLocation Chair_bar__plus_08_dot_16_bar__plus_00_dot_00_bar__plus_01_dot_50 loc_bar_35_bar_6_bar_3_bar_60)
        (objectAtLocation Box_bar__plus_11_dot_88_bar__plus_00_dot_71_bar__plus_03_dot_30 loc_bar_46_bar_8_bar_0_bar_60)
        (objectAtLocation FloorLamp_bar__plus_07_dot_86_bar__plus_00_dot_00_bar__plus_03_dot_40 loc_bar_32_bar_11_bar_0_bar_60)
        (objectAtLocation CreditCard_bar__plus_14_dot_23_bar__plus_00_dot_75_bar__plus_01_dot_04 loc_bar_54_bar_5_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__plus_10_dot_26_bar__plus_00_dot_59_bar__plus_00_dot_77 loc_bar_43_bar_8_bar_2_bar_60)
        (objectAtLocation Painting_bar__plus_07_dot_49_bar__plus_01_dot_84_bar__minus_00_dot_50 loc_bar_32_bar__minus_3_bar_3_bar__minus_15)
        (objectAtLocation Painting_bar__plus_07_dot_49_bar__plus_01_dot_84_bar__plus_02_dot_79 loc_bar_32_bar_11_bar_3_bar__minus_15)
        (objectAtLocation Television_bar__plus_14_dot_45_bar__plus_01_dot_24_bar__plus_00_dot_93 loc_bar_54_bar_5_bar_1_bar_60)
        (objectAtLocation Pillow_bar__plus_10_dot_81_bar__plus_00_dot_53_bar__plus_03_dot_16 loc_bar_46_bar_8_bar_0_bar_60)
        (objectAtLocation Statue_bar__plus_10_dot_46_bar__plus_00_dot_60_bar__plus_00_dot_68 loc_bar_43_bar_8_bar_2_bar_60)
        (objectAtLocation LightSwitch_bar__plus_12_dot_98_bar__plus_01_dot_40_bar__plus_03_dot_77 loc_bar_52_bar_13_bar_0_bar_30)
        (objectAtLocation Painting_bar__plus_07_dot_49_bar__plus_01_dot_78_bar__plus_01_dot_12 loc_bar_35_bar_4_bar_3_bar_0)
        (objectAtLocation Painting_bar__plus_14_dot_76_bar__plus_01_dot_75_bar__plus_01_dot_13 loc_bar_54_bar_5_bar_1_bar_0)
        (objectAtLocation Laptop_bar__plus_09_dot_40_bar__plus_00_dot_43_bar__plus_03_dot_14 loc_bar_38_bar_9_bar_0_bar_60)
        (objectAtLocation RemoteControl_bar__plus_14_dot_62_bar__plus_00_dot_52_bar__minus_00_dot_84 loc_bar_55_bar__minus_1_bar_1_bar_60)
        (objectAtLocation Watch_bar__plus_14_dot_15_bar__plus_00_dot_55_bar__plus_01_dot_45 loc_bar_53_bar_8_bar_1_bar_45)
        (objectAtLocation HousePlant_bar__plus_11_dot_37_bar__plus_00_dot_48_bar__minus_01_dot_14 loc_bar_42_bar__minus_3_bar_1_bar_60)
        (objectAtLocation Window_bar__plus_12_dot_03_bar__plus_01_dot_69_bar__minus_01_dot_60 loc_bar_47_bar__minus_3_bar_2_bar_0)
        (objectAtLocation Window_bar__plus_10_dot_23_bar__plus_01_dot_69_bar__minus_01_dot_59 loc_bar_41_bar__minus_4_bar_2_bar_0)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 StatueType)
                                    (receptacleType ?r DresserType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 StatueType)
                                            (receptacleType ?r DresserType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            