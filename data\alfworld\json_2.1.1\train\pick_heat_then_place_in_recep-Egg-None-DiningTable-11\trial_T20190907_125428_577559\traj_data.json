{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 41}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "DiningTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-7|-1|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-7.16772224, -7.16772224, 1.380393028, 1.380393028, 3.9495532, 3.9495532]], "coordinateReceptacleObjectId": ["DiningTable", [-8.812, -8.812, 1.792, 1.792, 3.96, 3.96]], "forceVisible": true, "objectId": "Egg|-01.79|+00.99|+00.35"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-4|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-9|-1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-7.16772224, -7.16772224, 1.380393028, 1.380393028, 3.9495532, 3.9495532]], "coordinateReceptacleObjectId": ["DiningTable", [-8.812, -8.812, 1.792, 1.792, 3.96, 3.96]], "forceVisible": true, "objectId": "Egg|-01.79|+00.99|+00.35", "receptacleObjectId": "DiningTable|-02.20|+00.99|+00.45"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.79|+00.99|+00.35"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [127, 139, 146, 160], "mask": [[41533, 8], [41831, 11], [42130, 13], [42430, 14], [42729, 16], [43028, 18], [43328, 18], [43628, 18], [43927, 20], [44227, 20], [44527, 20], [44827, 20], [45127, 20], [45427, 20], [45728, 18], [46028, 18], [46328, 18], [46629, 16], [46930, 14], [47231, 12], [47533, 8], [47836, 2]], "point": [136, 148]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.79|+00.99|+00.35", "placeStationary": true, "receptacleObjectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 271], [28517, 271], [28816, 271], [29115, 272], [29415, 273], [29714, 274], [30013, 275], [30313, 275], [30612, 276], [30911, 277], [31211, 277], [31510, 278], [31809, 279], [32109, 279], [32408, 280], [32708, 280], [33007, 280], [33306, 281], [33606, 281], [33905, 281], [34204, 282], [34504, 282], [34803, 282], [35102, 283], [35402, 282], [35701, 283], [36000, 283], [36300, 283], [36600, 282], [36900, 282], [37200, 281], [37500, 281], [37800, 280], [38100, 280], [38400, 280], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 271], [28517, 271], [28816, 271], [29115, 272], [29415, 273], [29714, 274], [30013, 275], [30313, 275], [30612, 276], [30911, 277], [31211, 277], [31510, 278], [31809, 279], [32109, 130], [32246, 142], [32408, 130], [32547, 141], [32708, 128], [32848, 140], [33007, 129], [33149, 138], [33306, 129], [33450, 137], [33606, 128], [33751, 136], [33905, 129], [34051, 135], [34204, 129], [34352, 134], [34504, 129], [34652, 134], [34803, 130], [34952, 133], [35102, 131], [35252, 133], [35402, 131], [35552, 132], [35701, 132], [35852, 132], [36000, 133], [36152, 131], [36300, 133], [36452, 131], [36600, 133], [36752, 130], [36900, 134], [37051, 131], [37200, 134], [37351, 130], [37500, 135], [37650, 131], [37800, 136], [37949, 131], [38100, 137], [38248, 132], [38400, 139], [38546, 134], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-01.79|+00.99|+00.35"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [133, 108, 151, 129], "mask": [[32239, 7], [32538, 9], [32836, 12], [33136, 13], [33435, 15], [33734, 17], [34034, 17], [34333, 19], [34633, 19], [34933, 19], [35233, 19], [35533, 19], [35833, 19], [36133, 19], [36433, 19], [36733, 19], [37034, 17], [37334, 17], [37635, 15], [37936, 13], [38237, 11], [38539, 7]], "point": [142, 117]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 271], [28517, 271], [28816, 271], [29115, 272], [29415, 273], [29714, 274], [30013, 275], [30313, 275], [30612, 276], [30911, 277], [31211, 277], [31510, 278], [31809, 279], [32109, 279], [32408, 280], [32708, 280], [33007, 280], [33306, 281], [33606, 281], [33905, 281], [34204, 282], [34504, 282], [34803, 282], [35102, 283], [35402, 282], [35701, 283], [36000, 283], [36300, 283], [36600, 282], [36900, 282], [37200, 281], [37500, 281], [37800, 280], [38100, 280], [38400, 280], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-01.79|+00.99|+00.35", "placeStationary": true, "receptacleObjectId": "DiningTable|-02.20|+00.99|+00.45"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 90, 299, 300], "mask": [[26739, 116], [26873, 13], [26900, 46], [27038, 117], [27173, 13], [27200, 46], [27338, 117], [27472, 14], [27500, 20], [27533, 12], [27637, 118], [27772, 14], [27801, 6], [27937, 118], [28072, 14], [28101, 6], [28127, 18], [28236, 119], [28372, 14], [28401, 43], [28535, 120], [28672, 14], [28701, 43], [28835, 120], [28972, 15], [29001, 43], [29134, 121], [29272, 15], [29301, 43], [29434, 121], [29572, 17], [29599, 45], [29733, 122], [29872, 21], [29896, 48], [30033, 121], [30172, 21], [30196, 47], [30332, 62], [30397, 57], [30472, 22], [30496, 47], [30631, 62], [30697, 57], [30772, 22], [30796, 47], [30931, 61], [30997, 57], [31072, 22], [31097, 46], [31230, 62], [31297, 57], [31372, 23], [31397, 46], [31530, 62], [31597, 57], [31671, 24], [31697, 46], [31829, 62], [31897, 57], [31971, 24], [31997, 46], [32129, 62], [32197, 57], [32271, 24], [32297, 46], [32428, 63], [32496, 58], [32571, 25], [32598, 46], [32728, 63], [32796, 58], [32871, 25], [32898, 46], [33027, 64], [33095, 59], [33171, 25], [33198, 46], [33326, 65], [33395, 59], [33471, 25], [33498, 46], [33626, 65], [33695, 59], [33771, 26], [33799, 45], [33925, 65], [33994, 60], [34071, 25], [34100, 44], [34225, 65], [34294, 60], [34371, 25], [34400, 45], [34524, 66], [34594, 60], [34671, 25], [34700, 45], [34824, 66], [34893, 61], [34971, 26], [35000, 46], [35123, 67], [35193, 61], [35271, 26], [35300, 47], [35423, 66], [35492, 62], [35571, 26], [35601, 47], [35699, 1], [35722, 67], [35792, 62], [35870, 27], [35901, 48], [35997, 3], [36021, 67], [36092, 62], [36170, 27], [36201, 49], [36296, 4], [36321, 67], [36392, 62], [36470, 28], [36501, 50], [36594, 6], [36620, 67], [36691, 64], [36770, 28], [36801, 51], [36892, 8], [36920, 67], [36991, 64], [37070, 28], [37102, 53], [37189, 11], [37219, 67], [37291, 65], [37369, 29], [37402, 55], [37487, 13], [37519, 67], [37591, 66], [37668, 30], [37702, 60], [37782, 18], [37818, 67], [37890, 69], [37966, 32], [38002, 98], [38118, 67], [38190, 16], [38244, 55], [38302, 98], [38417, 67], [38490, 15], [38544, 55], [38603, 97], [38716, 68], [38790, 15], [38844, 55], [38903, 97], [39016, 67], [39089, 16], [39144, 18], [39164, 35], [39203, 97], [39315, 68], [39389, 16], [39444, 17], [39465, 34], [39503, 97], [39615, 4], [39679, 4], [39689, 16], [39744, 16], [39765, 35], [39803, 97], [39914, 3], [39980, 2], [39989, 16], [40044, 16], [40065, 135], [40214, 3], [40279, 3], [40288, 16], [40344, 16], [40365, 135], [40513, 3], [40579, 3], [40588, 16], [40644, 16], [40665, 135], [40813, 3], [40879, 2], [40888, 16], [40944, 16], [40965, 135], [41112, 3], [41179, 2], [41188, 16], [41244, 17], [41265, 135], [41411, 4], [41478, 3], [41487, 17], [41544, 17], [41565, 135], [41711, 3], [41778, 3], [41787, 16], [41844, 17], [41865, 135], [42010, 4], [42078, 3], [42087, 17], [42144, 17], [42165, 135], [42310, 3], [42377, 4], [42387, 17], [42444, 17], [42465, 135], [42609, 3], [42677, 5], [42686, 18], [42744, 17], [42765, 135], [42909, 3], [42977, 5], [42986, 18], [43044, 17], [43065, 135], [43208, 3], [43277, 5], [43286, 19], [43343, 19], [43365, 135], [43507, 4], [43576, 6], [43586, 76], [43665, 135], [43807, 3], [43876, 6], [43885, 77], [43965, 135], [44106, 4], [44176, 86], [44265, 116], [44383, 17], [44406, 3], [44475, 87], [44565, 115], [44684, 16], [44705, 4], [44775, 87], [44865, 115], [44985, 15], [45005, 3], [45075, 88], [45165, 115], [45286, 14], [45304, 4], [45375, 88], [45465, 116], [45587, 13], [45604, 3], [45674, 89], [45765, 116], [45887, 13], [45903, 3], [45974, 89], [46065, 117], [46188, 12], [46202, 4], [46274, 89], [46365, 118], [46488, 12], [46502, 3], [46573, 90], [46665, 118], [46789, 11], [46801, 4], [46873, 90], [46965, 119], [47089, 11], [47101, 3], [47173, 90], [47265, 120], [47390, 14], [47473, 90], [47565, 120], [47690, 13], [47772, 91], [47865, 121], [47991, 12], [48072, 91], [48166, 121], [48291, 11], [48372, 90], [48466, 121], [48592, 10], [48671, 91], [48767, 121], [48892, 9], [48971, 90], [49067, 121], [49193, 7], [49271, 90], [49368, 121], [49493, 7], [49571, 89], [49669, 121], [49794, 6], [49870, 90], [49969, 122], [50094, 6], [50170, 90], [50269, 122], [50395, 5], [50470, 90], [50570, 122], [50695, 5], [50769, 91], [50864, 1], [50867, 1], [50870, 123], [50996, 4], [51069, 91], [51161, 2], [51164, 1], [51167, 1], [51170, 124], [51296, 4], [51369, 91], [51461, 2], [51464, 1], [51467, 1], [51470, 124], [51597, 3], [51669, 91], [51762, 1], [51764, 2], [51767, 1], [51770, 125], [51897, 3], [51968, 92], [52062, 1], [52064, 2], [52067, 1], [52070, 126], [52198, 2], [52268, 93], [52362, 1], [52364, 2], [52367, 1], [52370, 126], [52499, 1], [52568, 93], [52662, 1], [52665, 1], [52670, 127], [52799, 1], [52867, 94], [52962, 1], [52972, 125], [53167, 94], [53262, 1], [53273, 125], [53467, 96], [53574, 124], [53766, 97], [53874, 125], [54066, 97], [54174, 125], [54365, 98], [54474, 125], [54600, 162], [54773, 126], [54900, 162], [55074, 125], [55200, 14], [55231, 130], [55375, 37], [55430, 69], [55500, 10], [55534, 127], [55675, 33], [55732, 66], [55800, 6], [55835, 126], [55975, 30], [56034, 64], [56100, 3], [56136, 124], [56275, 27], [56335, 63], [56400, 1], [56437, 123], [56575, 26], [56636, 62], [56738, 122], [56875, 25], [56936, 62], [57038, 122], [57175, 24], [57237, 61], [57338, 121], [57475, 23], [57537, 62], [57638, 121], [57776, 22], [57837, 62], [57938, 121], [58076, 21], [58138, 62], [58238, 121], [58376, 21], [58438, 62], [58538, 121], [58676, 21], [58738, 62], [58838, 120], [58976, 21], [59038, 62], [59137, 121], [59276, 21], [59337, 63], [59437, 121], [59576, 21], [59637, 63], [59736, 122], [59876, 22], [59936, 64], [60035, 123], [60176, 22], [60236, 64], [60334, 123], [60476, 23], [60535, 65], [60633, 124], [60776, 24], [60833, 67], [60931, 126], [61076, 25], [61132, 69], [61229, 129], [61375, 28], [61430, 74], [61526, 132], [61675, 29], [61728, 78], [61824, 135], [61975, 32], [62025, 85], [62121, 138], [62274, 38], [62323, 237], [62574, 288], [62872, 293], [63169, 7777], [70953, 290], [71257, 284], [71559, 280], [71861, 277], [72162, 274], [72464, 136], [72644, 9], [72872, 11], [72944, 10], [73171, 11], [73245, 9], [73471, 10], [73546, 9], [73770, 10], [73846, 10], [74069, 10], [74147, 9], [74368, 11], [74448, 9], [74668, 10], [74748, 9], [74967, 10], [75049, 9], [75266, 10], [75349, 9], [75565, 10], [75650, 9], [75865, 10], [75951, 9], [76164, 10], [76251, 9], [76463, 10], [76552, 9], [76763, 9], [76853, 8], [77062, 9], [77153, 9], [77361, 10], [77454, 8], [77660, 10], [77755, 8], [77960, 9], [78055, 9], [78259, 9], [78356, 8], [78558, 9], [78656, 9], [78857, 10], [78957, 8], [79157, 9], [79258, 8], [79456, 9], [79558, 9], [79755, 9], [79859, 8], [80055, 9], [80160, 8], [80354, 9], [80460, 8], [80653, 9], [80761, 8], [80952, 9], [81062, 7], [81252, 8], [81362, 8], [81551, 9], [81663, 8], [81850, 9], [81963, 8], [82149, 9], [82264, 8], [82449, 8], [82565, 7], [82748, 8], [82865, 8], [83047, 9], [83166, 7], [83346, 9], [83467, 7], [83646, 8], [83767, 8], [83945, 8], [84068, 7], [84244, 8], [84368, 8], [84544, 8], [84669, 7], [84843, 8], [84970, 7], [85142, 8], [85270, 8], [85441, 8], [85571, 7], [85741, 7], [85872, 7], [86040, 8], [86172, 7], [86339, 8], [86473, 7], [86638, 8], [86774, 6], [86938, 7], [87074, 7], [87237, 7], [87375, 7], [87536, 8], [87675, 7], [87836, 7], [87976, 7], [88135, 7], [88277, 7], [88433, 8], [88577, 7], [88733, 8], [88878, 7], [89032, 8], [89178, 8], [89331, 8], [89479, 7], [89631, 8], [89779, 7], [89931, 7]], "point": [149, 194]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan11", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 0.75, "y": 0.9009992, "z": -0.25}, "object_poses": [{"objectName": "Potato_483d2614", "position": {"x": -2.61406946, "y": 0.9820612, "z": 0.165200263}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_483d2614", "position": {"x": -2.06597686, "y": 0.9820612, "z": 0.165200174}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": 0.009415984, "y": 0.9118642, "z": -1.6548}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_d4975865", "position": {"x": -0.3596393, "y": 0.9123421, "z": -1.6548}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_da74902f", "position": {"x": 1.0071, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": -2.203, "y": 0.9527736, "z": 0.3450983}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": 0.3419488, "y": 0.7840159, "z": -1.64893007}, "rotation": {"x": 1.40334191e-14, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": -1.16420984, "y": 0.7747009, "z": -1.53837943}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": -2.06597686, "y": 0.9672249, "z": 0.5249964}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": -1.79193056, "y": 0.9873883, "z": 0.345098257}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": -0.3596393, "y": 0.9474633, "z": -1.82056189}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": -1.8725, "y": 0.779237747, "z": -1.84918261}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": 1.901309, "y": 1.32717073, "z": -1.84639466}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": -2.61406946, "y": 0.9530838, "z": 0.345098317}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": -0.53883034, "y": 0.111744344, "z": -1.50870013}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_b9b64351", "position": {"x": 0.701403737, "y": 0.9448996, "z": -1.64917481}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_b9b64351", "position": {"x": 0.9603606, "y": 0.9448996, "z": -1.64917481}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": 0.856777847, "y": 0.918246448, "z": -1.44982815}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": 1.86777592, "y": 0.107238293, "z": 0.348659247}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": 0.0215998739, "y": 0.105766177, "z": 0.320300043}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": -2.135, "y": 1.4920547, "z": -1.76346016}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SaltShaker_42ce5824", "position": {"x": -2.203, "y": 0.9485312, "z": 0.165200174}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": 1.781162, "y": 1.00254059, "z": -1.82056189}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": 1.658355, "y": 1.00254059, "z": -1.5719192}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": -1.95999992, "y": 1.4877888, "z": -1.80632138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_41d4caea", "position": {"x": -0.329397261, "y": 0.137356848, "z": 0.526}, "rotation": {"x": 0.0, "y": 89.99995, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": -0.175111651, "y": 0.9951729, "z": -1.48903823}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_b9b64351", "position": {"x": 0.0353682637, "y": 0.9376496, "z": 0.292338252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": -1.79193056, "y": 0.9527736, "z": 0.2551492}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": -1.95999992, "y": 1.05123365, "z": -1.72059894}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7922e01e", "position": {"x": -0.05555916, "y": 0.9, "z": 0.643867135}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": 0.564747, "y": 0.9107361, "z": 0.292338252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": -0.09697643, "y": 0.961345851, "z": 0.3752191}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": -0.249364138, "y": 0.109582007, "z": -1.54564261}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": 1.78509033, "y": 0.9131588, "z": 0.3752191}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_da74902f", "position": {"x": 1.361, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": -1.92895377, "y": 0.9672249, "z": 0.704894543}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_54f2d061", "position": {"x": -2.203, "y": 1.05651045, "z": 0.5249964}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": -0.427205354, "y": 0.105368823, "z": 0.5259999}, "rotation": {"x": 0.0, "y": 89.99995, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": -1.79193056, "y": 1.04246557, "z": 0.52499634}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_42ce5824", "position": {"x": 1.903969, "y": 0.9086062, "z": -1.40615737}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": 0.5173435, "y": 0.10499227, "z": -1.54729772}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Apple_41d4caea", "position": {"x": -2.135, "y": 1.11895287, "z": -1.80632138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_d4975865", "position": {"x": -2.47704625, "y": 0.9522671, "z": 0.524996459}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_483d2614", "position": {"x": -0.394602567, "y": 0.100041717, "z": 0.396029323}, "rotation": {"x": 0.0, "y": 89.99995, "z": 0.0}}, {"objectName": "Pan_4b842b85", "position": {"x": 1.022105, "y": 0.9591456, "z": 0.288545281}, "rotation": {"x": 0.0, "y": 219.19072, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": -2.340023, "y": 0.95178926, "z": 0.435047418}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": 0.8049865, "y": 0.9206794, "z": -1.582726}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": 0.3000576, "y": 0.9073, "z": 0.292338252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1707691929, "scene_num": 11}, "task_id": "trial_T20190907_125428_577559", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A320QA9HJFUOZO_37TRT2X24T8X5WW88TDILD844ZTJBU", "high_descs": ["Turn around and walk back to the white table near the door", "Pick up the egg from the table", "Turn around and walk to the microwave on the counter", "Put the egg in the microwave, cook it, and remove it", "Turn around and walk back to the white table", "Put the egg down on the table left of the salt shaker"], "task_desc": "Put the cooked egg on the white table", "votes": [1, 1]}, {"assignment_id": "A3BZVFD82AVLT8_3HFNH7HEMKVAI08WJ5P5JPBNY8UQGS", "high_descs": ["Turn around and head straight, stopping at the white table.", "Pick up the egg that is on top of the fork.", "Turn to the right and go forward then turn right and walk to the microwave. ", "Place the egg in the microwave, then take it out when the microwave shuts off.", "Turn to the right then turn right again, going straight until you reach the table.", "Place the cooked egg on the table next to the salt shaker."], "task_desc": "Place a cooked egg on the table.", "votes": [1, 1]}, {"assignment_id": "A1DMXEJGJY02E1_3PQMUDRV7UNBXTQ8WLKZVZXHMA8IIA", "high_descs": ["Turn around and walk to the table.", "Pick up an egg.", "Turn around and go to the microwave.", "Microwave the egg and then pick it up again.", "Return to the table.", "Put the egg on the table."], "task_desc": "Put a cooked egg on the table.", "votes": [1, 1]}]}}