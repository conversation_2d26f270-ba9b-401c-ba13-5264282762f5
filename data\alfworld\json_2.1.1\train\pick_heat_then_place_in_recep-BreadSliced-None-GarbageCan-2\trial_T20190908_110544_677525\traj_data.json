{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000144.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000145.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000146.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000147.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000148.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000149.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000152.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000153.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000154.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000155.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000156.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000157.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000158.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000159.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000160.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000193.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000194.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000195.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000196.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000197.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000198.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000199.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000200.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000201.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000202.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000203.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000205.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000206.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000207.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000208.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000209.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000210.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000211.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000212.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000213.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000214.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000215.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000216.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000217.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000221.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000222.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000268.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000269.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000270.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000271.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000272.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000273.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000274.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000275.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000276.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000277.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000278.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000279.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000280.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000281.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000282.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000283.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000284.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000285.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000286.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000287.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000288.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000289.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000290.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000291.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000292.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000293.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000294.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000295.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000330.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000331.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000332.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000333.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000335.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000336.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000337.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000338.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000339.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000340.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000341.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000342.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000343.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000344.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000345.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000346.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000347.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000348.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000349.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000350.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000351.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000352.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000353.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000354.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000355.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000356.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000357.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000358.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000359.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000360.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000361.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000362.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000363.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000364.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000365.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000366.png", "low_idx": 67}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|3|3|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [0.001645565032, 0.001645565032, 2.9524016, 2.9524016, 3.7903708, 3.7903708]], "coordinateReceptacleObjectId": ["CounterTop", [-0.06, -0.06, 2.024, 2.024, 3.8444, 3.8444]], "forceVisible": true, "objectId": "Knife|+00.00|+00.95|+00.74"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|3|2|3|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [0.4297392, 0.4297392, 2.024000168, 2.024000168, 4.02471876, 4.02471876]], "forceVisible": true, "objectId": "Bread|+00.11|+01.01|+00.51"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "countertop"]}, "high_idx": 4, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [0.001645565032, 0.001645565032, 2.9524016, 2.9524016, 3.7903708, 3.7903708]], "coordinateReceptacleObjectId": ["CounterTop", [-0.06, -0.06, 2.024, 2.024, 3.8444, 3.8444]], "forceVisible": true, "objectId": "Knife|+00.00|+00.95|+00.74", "receptacleObjectId": "CounterTop|-00.02|+00.96|+00.51"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [0.4297392, 0.4297392, 2.024000168, 2.024000168, 4.02471876, 4.02471876]], "coordinateReceptacleObjectId": ["CounterTop", [-0.06, -0.06, 2.024, 2.024, 3.8444, 3.8444]], "forceVisible": true, "objectId": "Bread|+00.11|+01.01|+00.51|BreadSliced_2"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-5|5|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "garbagecan"]}, "high_idx": 9, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [0.4297392, 0.4297392, 2.024000168, 2.024000168, 4.02471876, 4.02471876]], "coordinateReceptacleObjectId": ["GarbageCan", [-7.184, -7.184, 5.32, 5.32, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|+00.11|+01.01|+00.51|BreadSliced_2", "receptacleObjectId": "GarbageCan|-01.80|+00.00|+01.33"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 10, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|+00.00|+00.95|+00.74"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [150, 100, 163, 154], "mask": [[29862, 1], [30161, 2], [30460, 3], [30759, 4], [31058, 5], [31357, 7], [31657, 7], [31956, 8], [32256, 8], [32556, 8], [32855, 9], [33155, 9], [33454, 9], [33754, 9], [34054, 9], [34354, 9], [34653, 10], [34953, 10], [35253, 10], [35552, 11], [35852, 11], [36152, 11], [36452, 11], [36751, 12], [37051, 11], [37351, 11], [37651, 11], [37951, 11], [38251, 11], [38550, 12], [38852, 10], [39157, 5], [39457, 5], [39756, 6], [40056, 6], [40356, 5], [40656, 5], [40955, 6], [41255, 6], [41555, 6], [41854, 7], [42154, 7], [42454, 7], [42754, 7], [43054, 7], [43354, 7], [43654, 7], [43954, 6], [44254, 6], [44552, 8], [44851, 9], [45151, 9], [45452, 8], [45753, 7], [46054, 4]], "point": [156, 126]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|+00.11|+01.01|+00.51"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [116, 120, 199, 164], "mask": [[35847, 22], [36133, 52], [36427, 62], [36724, 68], [37023, 70], [37321, 74], [37620, 76], [37919, 77], [38218, 79], [38517, 80], [38817, 81], [39117, 81], [39417, 82], [39717, 82], [40017, 82], [40317, 83], [40617, 83], [40917, 83], [41217, 83], [41517, 83], [41817, 83], [42116, 84], [42416, 84], [42716, 84], [43016, 84], [43316, 84], [43616, 83], [43916, 83], [44216, 83], [44516, 82], [44816, 82], [45116, 82], [45416, 82], [45717, 80], [46017, 80], [46317, 80], [46618, 79], [46918, 78], [47218, 78], [47519, 77], [47819, 76], [48119, 76], [48420, 74], [48722, 68], [49028, 7], [49047, 39]], "point": [157, 141]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|+00.00|+00.95|+00.74", "placeStationary": true, "receptacleObjectId": "CounterTop|-00.02|+00.96|+00.51"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 78, 299, 238], "mask": [[23123, 50], [23209, 42], [23252, 87], [23376, 24], [23423, 49], [23511, 37], [23555, 83], [23678, 22], [23722, 50], [23812, 34], [23857, 80], [23979, 21], [24021, 51], [24114, 31], [24158, 79], [24281, 19], [24319, 52], [24415, 30], [24458, 78], [24582, 18], [24618, 53], [24717, 27], [24759, 77], [24884, 16], [24916, 55], [25018, 25], [25060, 75], [25185, 15], [25215, 56], [25320, 23], [25360, 75], [25486, 14], [25513, 58], [25621, 21], [25660, 74], [25787, 13], [25811, 60], [25922, 20], [25961, 73], [26087, 13], [26109, 62], [26223, 19], [26261, 73], [26387, 13], [26406, 65], [26523, 19], [26561, 73], [26687, 13], [26703, 68], [26824, 17], [26861, 73], [26987, 13], [27002, 69], [27124, 17], [27161, 73], [27287, 13], [27301, 30], [27362, 9], [27424, 17], [27462, 72], [27587, 43], [27662, 9], [27724, 17], [27761, 73], [27885, 44], [27963, 9], [28023, 18], [28061, 73], [28184, 45], [28263, 9], [28323, 19], [28361, 73], [28482, 46], [28563, 9], [28622, 20], [28661, 73], [28780, 48], [28863, 10], [28920, 22], [28960, 75], [29079, 48], [29162, 12], [29218, 25], [29260, 75], [29377, 50], [29462, 12], [29516, 27], [29559, 77], [29676, 50], [29762, 13], [29814, 30], [29859, 78], [29974, 52], [30061, 16], [30112, 33], [30158, 79], [30272, 55], [30361, 17], [30409, 37], [30456, 83], [30570, 57], [30660, 20], [30706, 42], [30754, 87], [30867, 60], [30960, 23], [31001, 142], [31163, 283], [31454, 1156], [32614, 294], [32915, 293], [33216, 292], [33516, 292], [33816, 292], [34116, 290], [34415, 291], [34716, 290], [35016, 289], [35316, 289], [35616, 152], [35791, 66], [35865, 40], [35916, 140], [36107, 38], [36184, 20], [36216, 133], [36411, 18], [36489, 15], [36516, 130], [36714, 13], [36791, 13], [36815, 129], [37015, 9], [37093, 10], [37115, 127], [37316, 6], [37394, 9], [37415, 126], [37617, 3], [37695, 8], [37715, 124], [37917, 2], [37996, 6], [38015, 123], [38297, 5], [38315, 122], [38597, 5], [38615, 122], [38898, 3], [38915, 121], [39198, 3], [39215, 121], [39499, 2], [39515, 120], [39799, 2], [39814, 121], [40099, 2], [40114, 120], [40400, 1], [40414, 120], [40700, 3], [40714, 119], [41000, 4], [41013, 120], [41300, 8], [41311, 122], [41600, 132], [41900, 132], [42200, 132], [42500, 132], [42800, 132], [43100, 132], [43400, 132], [43700, 133], [43999, 134], [44299, 134], [44598, 136], [44898, 136], [45198, 136], [45498, 137], [45798, 137], [46097, 138], [46397, 139], [46697, 139], [46917, 1], [46996, 107], [47105, 32], [47217, 1], [47296, 104], [47408, 30], [47517, 1], [47596, 104], [47710, 29], [47816, 3], [47895, 105], [48012, 28], [48115, 4], [48195, 105], [48314, 26], [48414, 7], [48494, 106], [48617, 25], [48711, 10], [48791, 109], [48919, 28], [48956, 10], [49006, 18], [49037, 1], [49043, 1], [49087, 113], [49221, 131], [49356, 144], [49522, 278], [49823, 277], [50123, 277], [50424, 276], [50724, 56], [50784, 216], [51025, 54], [51084, 216], [51324, 55], [51384, 216], [51624, 54], [51684, 216], [51923, 55], [51984, 216], [52223, 55], [52284, 216], [52521, 57], [52584, 216], [52820, 58], [52883, 217], [53118, 59], [53183, 217], [53415, 62], [53483, 220], [53707, 70], [53782, 295], [54082, 295], [54382, 294], [54681, 295], [54981, 295], [55280, 296], [55580, 296], [55880, 295], [56179, 296], [56479, 296], [56779, 296], [57078, 297], [57378, 296], [57678, 296], [57977, 297], [58277, 297], [58576, 298], [58876, 297], [59176, 297], [59475, 298], [59775, 297], [60075, 297], [60374, 298], [60674, 298], [60974, 297], [61274, 297], [61574, 297], [61874, 297], [62174, 296], [62474, 296], [62774, 295], [63074, 295], [63375, 293], [63675, 292], [63976, 290], [64276, 290], [64576, 289], [64876, 289], [65176, 289], [65476, 288], [65776, 288], [66066, 1], [66069, 1], [66072, 1], [66075, 289], [66365, 2], [66369, 1], [66372, 1], [66375, 289], [66665, 2], [66668, 2], [66671, 2], [66674, 289], [66965, 1], [66968, 1], [66971, 2], [66974, 289], [67265, 1], [67268, 1], [67271, 1], [67274, 289], [67564, 2], [67567, 2], [67570, 2], [67573, 289], [67864, 1], [67867, 1], [67870, 1], [67873, 289], [68164, 1], [68166, 2], [68169, 2], [68172, 290], [68463, 2], [68466, 2], [68469, 2], [68472, 289], [68763, 1], [68766, 1], [68769, 1], [68772, 289], [69062, 2], [69065, 2], [69068, 2], [69071, 290], [69362, 2], [69365, 2], [69368, 1], [69371, 292], [69664, 2], [69667, 2], [69670, 290], [69961, 2], [69964, 2], [69967, 2], [69970, 1430]], "point": [149, 164]}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+00.11|+01.01|+00.51|BreadSliced_2"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [144, 121, 150, 164], "mask": [[36146, 4], [36446, 4], [36746, 4], [37046, 4], [37346, 4], [37646, 4], [37946, 4], [38246, 4], [38546, 4], [38846, 5], [39145, 6], [39445, 6], [39745, 6], [40045, 6], [40345, 6], [40645, 6], [40945, 6], [41245, 6], [41545, 6], [41845, 6], [42145, 6], [42445, 6], [42745, 6], [43045, 6], [43345, 6], [43645, 6], [43945, 6], [44245, 6], [44545, 6], [44845, 6], [45145, 6], [45445, 6], [45745, 5], [46045, 5], [46345, 5], [46645, 5], [46945, 5], [47244, 6], [47544, 6], [47844, 6], [48144, 6], [48444, 6], [48744, 6], [49044, 6]], "point": [147, 141]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+00.11|+01.01|+00.51|BreadSliced_2", "placeStationary": true, "receptacleObjectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 216], [15638, 216], [15937, 217], [16237, 217], [16536, 217], [16835, 218], [17134, 219], [17434, 219], [17733, 219], [18032, 220], [18332, 220], [18631, 220], [18930, 221], [19230, 221], [19529, 221], [19828, 222], [20128, 222], [20427, 223], [20726, 223], [21025, 224], [21325, 224], [21624, 224], [21923, 225], [22223, 225], [22522, 226], [22821, 226], [23121, 226], [23420, 227], [23719, 227], [24019, 227], [24318, 228], [24617, 228], [24916, 229], [25216, 229], [25515, 230], [25814, 230], [26114, 230], [26413, 231], [26712, 231], [27012, 231], [27311, 232], [27610, 233], [27910, 232], [28209, 233], [28508, 234], [28807, 234], [29107, 234], [29406, 235], [29705, 236], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 216], [15638, 216], [15937, 217], [16237, 217], [16536, 217], [16835, 218], [17134, 219], [17434, 219], [17733, 219], [18032, 220], [18332, 220], [18631, 220], [18930, 221], [19230, 221], [19529, 128], [19659, 91], [19828, 128], [19962, 88], [20128, 111], [20263, 87], [20427, 110], [20564, 86], [20726, 111], [20865, 84], [21025, 111], [21166, 83], [21325, 111], [21466, 83], [21624, 112], [21767, 81], [21923, 113], [22067, 81], [22223, 113], [22367, 81], [22522, 114], [22668, 80], [22821, 115], [22968, 79], [23121, 115], [23268, 79], [23420, 116], [23568, 79], [23719, 117], [23868, 78], [24019, 117], [24168, 78], [24318, 118], [24468, 78], [24617, 119], [24768, 77], [24916, 120], [25067, 78], [25216, 120], [25367, 78], [25515, 121], [25667, 78], [25814, 122], [25967, 77], [26114, 122], [26267, 77], [26413, 123], [26568, 76], [26712, 124], [26867, 76], [27012, 125], [27167, 76], [27311, 126], [27467, 76], [27610, 127], [27767, 76], [27910, 127], [28067, 75], [28209, 128], [28366, 76], [28508, 129], [28665, 77], [28807, 130], [28965, 76], [29107, 130], [29264, 77], [29406, 133], [29563, 78], [29705, 150], [29861, 80], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 7}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 7}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.732, 7.732, -3.068, -3.068, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 7}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 9, 255, 124], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11160, 190], [11460, 191], [11758, 195], [12057, 197], [12356, 199], [12656, 199], [12955, 201], [13255, 201], [13555, 201], [13855, 201], [14155, 201], [14455, 200], [14756, 199], [15056, 199], [15356, 199], [15656, 198], [15957, 197], [16257, 197], [16557, 196], [16858, 195], [17158, 195], [17458, 195], [17758, 194], [18059, 193], [18359, 193], [18659, 192], [18959, 192], [19260, 191], [19560, 190], [19860, 190], [20160, 190], [20461, 189], [20761, 188], [21061, 188], [21361, 188], [21662, 186], [21962, 186], [22262, 186], [22563, 185], [22863, 184], [23163, 184], [23463, 184], [23764, 182], [24064, 182], [24364, 182], [24664, 181], [24965, 180], [25265, 180], [25565, 180], [25865, 179], [26166, 178], [26466, 178], [26766, 177], [27066, 177], [27367, 176], [27667, 176], [27967, 175], [28267, 175], [28568, 174], [28868, 173], [29168, 173], [29469, 172], [29769, 172], [30069, 171], [30369, 171], [30670, 170], [30970, 169], [31270, 169], [31570, 169], [31871, 167], [32171, 167], [32471, 167], [32771, 167], [33072, 165], [33372, 165], [33672, 165], [33972, 164], [34273, 163], [34573, 163], [34873, 163], [35174, 161], [35474, 161], [35774, 161], [36074, 160], [36375, 159], [36675, 159], [36976, 156]], "point": [155, 65]}}, "high_idx": 7}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+00.11|+01.01|+00.51|BreadSliced_2"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [136, 66, 167, 100], "mask": [[19657, 2], [19956, 6], [20239, 24], [20537, 27], [20837, 28], [21136, 30], [21436, 30], [21736, 31], [22036, 31], [22336, 31], [22636, 32], [22936, 32], [23236, 32], [23536, 32], [23836, 32], [24136, 32], [24436, 32], [24736, 32], [25036, 31], [25336, 31], [25636, 31], [25936, 31], [26236, 31], [26536, 32], [26836, 31], [27137, 30], [27437, 30], [27737, 30], [28037, 30], [28337, 29], [28637, 28], [28937, 28], [29237, 27], [29539, 24], [29855, 6]], "point": [151, 82]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.93|+00.90|-00.77"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 9, 255, 228], "mask": [[2478, 140], [2777, 154], [3076, 156], [3376, 157], [3675, 158], [3975, 159], [4274, 161], [4573, 162], [4873, 163], [5172, 165], [5471, 166], [5771, 167], [6070, 169], [6370, 169], [6669, 171], [6968, 173], [7268, 173], [7567, 175], [7867, 176], [8166, 178], [8465, 179], [8765, 180], [9064, 182], [9364, 182], [9663, 184], [9962, 186], [10262, 186], [10561, 188], [10860, 190], [11149, 201], [11448, 203], [11747, 206], [12046, 208], [12345, 210], [12645, 210], [12944, 212], [13243, 213], [13543, 213], [13842, 214], [14141, 215], [14441, 214], [14740, 215], [15039, 216], [15339, 216], [15638, 216], [15937, 217], [16237, 217], [16536, 217], [16835, 218], [17134, 219], [17434, 219], [17733, 219], [18032, 220], [18332, 220], [18631, 220], [18930, 221], [19230, 221], [19529, 221], [19828, 222], [20128, 222], [20427, 223], [20726, 223], [21025, 224], [21325, 224], [21624, 224], [21923, 225], [22223, 225], [22522, 226], [22821, 226], [23121, 226], [23420, 227], [23719, 227], [24019, 227], [24318, 228], [24617, 228], [24916, 229], [25216, 229], [25515, 230], [25814, 230], [26114, 230], [26413, 231], [26712, 231], [27012, 231], [27311, 232], [27610, 233], [27910, 232], [28209, 233], [28508, 234], [28807, 234], [29107, 234], [29406, 235], [29705, 236], [30005, 235], [30304, 236], [30603, 237], [30903, 236], [31202, 237], [31501, 238], [31801, 237], [32100, 238], [32400, 238], [32700, 238], [33000, 237], [33300, 237], [33600, 237], [33900, 236], [34200, 236], [34500, 236], [34800, 236], [35100, 235], [35400, 235], [35700, 79], [35902, 33], [36000, 79], [36202, 32], [36300, 78], [36502, 32], [36600, 78], [36802, 32], [36900, 78], [37103, 29], [37200, 77], [37500, 77], [37800, 77], [38100, 77], [38400, 76], [38700, 76], [39000, 76], [39300, 75], [39600, 75], [39900, 75], [40200, 74], [40500, 74], [40800, 74], [41100, 73], [41400, 73], [41700, 73], [42000, 73], [42300, 72], [42600, 72], [42900, 72], [43200, 71], [43500, 71], [43800, 71], [44100, 70], [44400, 70], [44700, 70], [45000, 69], [45300, 69], [45600, 69], [45900, 68], [46200, 68], [46500, 68], [46800, 68], [47100, 67], [47400, 67], [47700, 67], [48000, 66], [48300, 66], [48600, 66], [48900, 65], [49200, 65], [49500, 65], [49800, 64], [50100, 64], [50400, 64], [50700, 64], [51000, 63], [51300, 63], [51600, 63], [51900, 62], [52200, 62], [52500, 62], [52800, 61], [53100, 61], [53400, 61], [53700, 60], [54000, 60], [54301, 59], [54601, 59], [54902, 57], [55203, 56], [55503, 56], [55804, 54], [56105, 53], [56405, 53], [56706, 51], [57007, 50], [57307, 50], [57608, 48], [57909, 47], [58209, 47], [58510, 45], [58811, 44], [59111, 44], [59412, 43], [59713, 41], [60013, 41], [60314, 40], [60615, 38], [60915, 38], [61216, 37], [61517, 35], [61817, 35], [62118, 34], [62418, 33], [62719, 32], [63020, 31], [63320, 31], [63621, 29], [63922, 28], [64222, 28], [64522, 27], [64821, 28], [65122, 27], [65423, 25], [65723, 26], [66024, 27], [66325, 26], [66625, 27], [66926, 21], [66948, 3], [67227, 20], [67527, 19], [67828, 18], [68129, 17]], "point": [127, 117]}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+00.11|+01.01|+00.51|BreadSliced_2", "placeStationary": true, "receptacleObjectId": "GarbageCan|-01.80|+00.00|+01.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [96, 129, 239, 217], "mask": [[38516, 100], [38814, 104], [39112, 108], [39410, 112], [39708, 116], [40007, 118], [40306, 120], [40606, 120], [40905, 122], [41204, 124], [41503, 126], [41803, 127], [42102, 128], [42402, 128], [42702, 129], [43002, 129], [43301, 130], [43601, 131], [43901, 131], [44201, 131], [44501, 131], [44801, 131], [45100, 133], [45400, 133], [45700, 133], [46000, 133], [46300, 133], [46600, 134], [46900, 134], [47200, 134], [47500, 134], [47799, 135], [48099, 136], [48399, 136], [48699, 136], [48999, 136], [49299, 136], [49599, 136], [49899, 137], [50198, 138], [50498, 138], [50798, 138], [51098, 138], [51398, 139], [51698, 139], [51998, 139], [52298, 139], [52597, 140], [52897, 141], [53197, 141], [53497, 141], [53797, 141], [54097, 141], [54397, 142], [54697, 142], [54997, 142], [55296, 143], [55596, 143], [55896, 144], [56196, 144], [56496, 144], [56796, 144], [57096, 144], [57396, 144], [57696, 144], [57996, 144], [58296, 144], [58597, 143], [58897, 142], [59198, 141], [59499, 139], [59799, 138], [60100, 137], [60400, 136], [60701, 135], [61002, 133], [61303, 131], [61605, 127], [61907, 123], [62208, 121], [62510, 117], [62812, 113], [63116, 105], [63429, 72], [63732, 66], [64034, 61], [64336, 56], [64639, 49], [64943, 41]], "point": [167, 172]}}, "high_idx": 9}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan2", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -2.25, "y": 0.9009992, "z": 2.25}, "object_poses": [{"objectName": "Potato_5e728867", "position": {"x": 0.214458212, "y": 0.9493317, "z": 0.0417993069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_6cb12d41", "position": {"x": -1.66821682, "y": 0.9094269, "z": -1.61003125}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_6cb12d41", "position": {"x": -0.8440597, "y": 0.9094269, "z": -1.513}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_e1c57c26", "position": {"x": -1.814799, "y": 0.9110642, "z": -0.9641914}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_e1c57c26", "position": {"x": -0.21363543, "y": 0.9251642, "z": 0.0417993069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_c80d57de", "position": {"x": -0.21363543, "y": 0.926148534, "z": -0.190301061}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_de267f63", "position": {"x": -0.261607945, "y": 0.775730669, "z": -1.32494032}, "rotation": {"x": 0.0, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Spatula_de267f63", "position": {"x": 1.55919147, "y": 0.13189052, "z": 0.116144642}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": -1.75051868, "y": 1.47329974, "z": -0.0810167938}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": -1.75051856, "y": 1.680082, "z": -0.175191209}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": -1.753262, "y": 1.08623946, "z": 0.10733138}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": -0.21363543, "y": 0.982472539, "z": 0.506000042}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_4e364eea", "position": {"x": 0.0837812647, "y": 0.7585746, "z": -1.38022017}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": 1.714014, "y": 0.604603946, "z": -0.0927972943}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": 1.96291673, "y": 1.66291428, "z": -1.053475}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "PepperShaker_14add4df", "position": {"x": 0.000411391258, "y": 0.9219062, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": -1.75051856, "y": 1.54979384, "z": 0.20150499}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": 0.1074348, "y": 1.00617969, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": -0.21363543, "y": 1.00230384, "z": 0.9702008}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": -0.21363543, "y": 1.00230384, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": 1.3554858, "y": 0.9554, "z": -1.36239815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": -1.72138691, "y": 0.6164668, "z": 0.107331246}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": -0.08891334, "y": 0.8137338, "z": -1.49077988}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_7ce82db0", "position": {"x": -1.46217752, "y": 0.9150344, "z": -1.61003125}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_4e364eea", "position": {"x": -0.175260633, "y": 0.75871, "z": -1.38022017}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_1bc23cfd", "position": {"x": -0.3059555, "y": 0.118937433, "z": -1.472}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_c80d57de", "position": {"x": 0.321481615, "y": 0.926148534, "z": 0.273899674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_ec6bb035", "position": {"x": 1.66173053, "y": 0.2707531, "z": 0.599400043}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_233c7df0", "position": {"x": 0.000411391258, "y": 0.9266413, "z": 1.20230114}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7f5e18ec", "position": {"x": 1.64025736, "y": 0.9683725, "z": 1.03234315}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d852c2bd", "position": {"x": -1.23707867, "y": 1.67834687, "z": -1.61474991}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_6d10ab8d", "position": {"x": 1.9753, "y": 0.938299954, "z": 0.8192}, "rotation": {"x": 0.0, "y": 180.000214, "z": 0.0}}, {"objectName": "Lettuce_aa2e533f", "position": {"x": -0.320658833, "y": 1.00230384, "z": -0.190301061}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_de267f63", "position": {"x": 1.815539, "y": 0.5948733, "z": 0.5310277}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_487a7950", "position": {"x": 2.02593255, "y": 0.964782655, "z": 0.182397366}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e0496240", "position": {"x": 0.000411391258, "y": 0.9475927, "z": 0.7381004}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_586649ce", "position": {"x": 1.71226215, "y": 0.5417261, "z": 1.41617525}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_14add4df", "position": {"x": 1.09552133, "y": 1.67594481, "z": -1.65244007}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_6cb12d41", "position": {"x": -1.46391475, "y": 0.9094269, "z": -0.9641914}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_4f8c70c0", "position": {"x": 1.71057916, "y": 0.6954429, "z": -0.0159857273}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_48e00ad2", "position": {"x": 0.1074348, "y": 1.00617969, "z": 0.506000042}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_448791c6", "position": {"x": -1.25613821, "y": 0.966667235, "z": -1.31893754}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5e728867", "position": {"x": -1.77133465, "y": 1.05309856, "z": -0.08101666}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_e1c57c26", "position": {"x": 1.76426947, "y": 0.116454892, "z": -0.02060002}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_bfeb74cb", "position": {"x": 1.71401417, "y": 0.121540681, "z": 0.7457101}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_b045e42b", "position": {"x": -1.721387, "y": 0.558184147, "z": -0.0810164362}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_253fa632", "position": {"x": 0.138286144, "y": 0.11122781, "z": -1.53428245}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 703091055, "scene_num": 2}, "task_id": "trial_T20190908_110544_677525", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AO33H4GL9KZX9_31Q0U3WYDS6BTSZC1VKDDXAOLY317W", "high_descs": ["Turn around and go to the kitchen island on the side towards the stove. ", "Pick up the yellow knife on the right of the salt shaker. ", "Move over to the left in front of the bread on top of the island.", "Slice a half of the bread on the right. ", "Put the knife back on the table leaned up against the bread. ", "Pick up one slice of the bread. ", "Turn around and go to the microwave at the end of the counter. ", "Heat the bread in the microwave and take it out. ", "Go around the kitchen island to the trash can on the floor on the right  of the refrigerator.", "Place the heated bread slice in the trash can."], "task_desc": "Throw out a microwaved slice of bread. ", "votes": [1, 1]}, {"assignment_id": "AFU00NU09CFXE_3C5W7UE9CIH24WRSCU79ROBFL3DMXM", "high_descs": ["Turn right, move to the wall, and then turn right again and move to stand facing the kitchen island.", "Pick up the sharp knife next to the salt shaker on the island.", "Move a step to the left to stand in front of the two loaves of bread.", "use the knife to slice the loaf of bread on the right.", "Place the knife in the center of the loaf you just sliced. ", "Pick up the slice of bread next to the knife. ", "Turn left, move to the counter, and turn left again to face the microwave.", "Place the slice of bread on the plate in the microwave and then remove the bread once heated. ", "Turn around and carry the bread slice to where the counter ends, and then turn right again and move to stand in front of the trash bin next to the fridge.", "Place the bread in the trash bin."], "task_desc": "Throw away a heated slice of bread. ", "votes": [1, 1]}, {"assignment_id": "ARB8SJAWXUWTD_3TMFV4NEPB58U7KY8O14KNWDSTI8WL", "high_descs": ["Go to the center kitchen table", "Pick up a knife from the center kitchen table", "Turn and face the loaf of bread", "Cut the loaf of bread", "Put the knife down on the counter", "Take a slice of bread", "Go to the microwave", "Heat the slice of bread in the microwave", "Go to the bin", "Place the slice of bread in the bin"], "task_desc": "Place a heated slice of bread in a bin", "votes": [1, 1]}]}}