{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 58}, {"high_idx": 5, "image_name": "000000320.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000321.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000322.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000323.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000324.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000325.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000326.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000327.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000328.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000329.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000330.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000331.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000332.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000333.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000334.png", "low_idx": 59}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-13|-10|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-14.34726524, -14.34726524, -11.364284, -11.364284, 3.04316616, 3.04316616]], "coordinateReceptacleObjectId": ["DiningTable", [-14.464, -14.464, -13.296, -13.296, 2.673336984, 2.673336984]], "forceVisible": true, "objectId": "Cup|-03.59|+00.76|-02.84"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-8|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-15|-5|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "sidetable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-14.34726524, -14.34726524, -11.364284, -11.364284, 3.04316616, 3.04316616]], "coordinateReceptacleObjectId": ["SideTable", [-16.284, -16.284, -1.236, -1.236, 2.904, 2.904]], "forceVisible": true, "objectId": "Cup|-03.59|+00.76|-02.84", "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-03.59|+00.76|-02.84"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [234, 154, 280, 198], "mask": [[46154, 13], [46450, 19], [46747, 25], [47046, 27], [47345, 29], [47643, 32], [47942, 34], [48241, 36], [48540, 38], [48839, 39], [49138, 41], [49438, 42], [49737, 43], [50037, 43], [50336, 44], [50636, 45], [50935, 46], [51235, 46], [51534, 47], [51834, 47], [52134, 47], [52434, 47], [52734, 47], [53034, 47], [53334, 47], [53634, 47], [53934, 46], [54234, 46], [54534, 46], [54834, 45], [55135, 44], [55435, 43], [55736, 42], [56036, 41], [56337, 39], [56637, 38], [56938, 36], [57239, 34], [57540, 32], [57841, 30], [58142, 28], [58444, 24], [58746, 21], [59048, 17], [59352, 9]], "point": [257, 175]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-03.59|+00.76|-02.84", "placeStationary": true, "receptacleObjectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 32743], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 7937], [7955, 278], [8260, 271], [8562, 268], [8863, 267], [9163, 266], [9464, 265], [9764, 264], [10065, 263], [10365, 263], [10666, 261], [10966, 261], [11267, 259], [11567, 259], [11867, 258], [12168, 257], [12468, 257], [12768, 257], [13069, 255], [13369, 255], [13669, 255], [13970, 254], [14270, 253], [14570, 253], [14870, 253], [15170, 253], [15470, 253], [15770, 253], [16070, 253], [16370, 253], [16670, 253], [16970, 253], [17270, 253], [17570, 253], [17870, 253], [18170, 253], [18470, 253], [18770, 253], [19070, 253], [19370, 253], [19670, 253], [19970, 254], [20270, 254], [20569, 255], [20869, 256], [21169, 256], [21468, 258], [21768, 258], [22067, 259], [22367, 260], [22666, 262], [22965, 264], [23264, 266], [23563, 271], [23860, 8883], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 80]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-03.59|+00.76|-02.84"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [123, 27, 169, 80], "mask": [[7937, 18], [8233, 27], [8531, 31], [8830, 33], [9130, 33], [9429, 35], [9729, 35], [10028, 37], [10328, 37], [10628, 38], [10927, 39], [11227, 40], [11526, 41], [11826, 41], [12125, 43], [12425, 43], [12725, 43], [13025, 44], [13324, 45], [13624, 45], [13924, 46], [14224, 46], [14523, 47], [14823, 47], [15123, 47], [15423, 47], [15723, 47], [16023, 47], [16323, 47], [16623, 47], [16923, 47], [17223, 47], [17523, 47], [17823, 47], [18123, 47], [18423, 47], [18723, 47], [19023, 47], [19323, 47], [19623, 47], [19923, 47], [20224, 46], [20524, 45], [20824, 45], [21125, 44], [21425, 43], [21726, 42], [22026, 41], [22326, 41], [22627, 39], [22928, 37], [23229, 35], [23530, 33], [23834, 26]], "point": [146, 52]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 32743], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-03.59|+00.76|-02.84", "placeStationary": true, "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [21, 115, 139, 251], "mask": [[34251, 25], [34297, 41], [34551, 24], [34598, 40], [34851, 24], [34898, 40], [35150, 25], [35198, 40], [35450, 25], [35498, 40], [35749, 26], [35798, 40], [36049, 27], [36098, 40], [36354, 22], [36398, 40], [36656, 21], [36698, 8], [36710, 28], [36957, 20], [36997, 2], [37010, 28], [37258, 21], [37295, 2], [37310, 28], [37558, 13], [37575, 5], [37593, 3], [37610, 28], [37892, 3], [37909, 29], [38168, 18], [38209, 29], [38459, 36], [38509, 29], [38759, 36], [38808, 30], [39045, 1], [39059, 36], [39101, 37], [39344, 2], [39359, 79], [39644, 2], [39659, 79], [39943, 4], [39959, 79], [40243, 5], [40258, 80], [40542, 7], [40557, 81], [40842, 10], [40855, 83], [41142, 51], [41198, 41], [41441, 51], [41500, 39], [41741, 50], [41801, 38], [42040, 50], [42102, 37], [42340, 50], [42402, 37], [42639, 51], [42703, 15], [42722, 17], [42939, 50], [43003, 14], [43023, 16], [43239, 50], [43303, 13], [43323, 16], [43538, 51], [43604, 13], [43623, 16], [43838, 51], [43904, 13], [43924, 15], [44137, 53], [44204, 12], [44224, 15], [44437, 53], [44503, 13], [44524, 15], [44736, 54], [44803, 13], [44825, 14], [45036, 55], [45103, 13], [45125, 14], [45335, 57], [45402, 14], [45425, 14], [45635, 14], [45654, 39], [45701, 15], [45725, 14], [45935, 13], [45963, 32], [45999, 17], [46026, 13], [46234, 14], [46265, 51], [46326, 13], [46534, 14], [46565, 51], [46626, 13], [46833, 14], [46866, 22], [46893, 23], [46926, 13], [47133, 14], [47166, 3], [47212, 4], [47227, 12], [47432, 14], [47482, 6], [47512, 4], [47527, 12], [47732, 14], [47765, 44], [47811, 5], [47827, 12], [48031, 15], [48064, 52], [48127, 13], [48331, 15], [48364, 52], [48427, 13], [48631, 21], [48663, 54], [48726, 14], [48930, 87], [49025, 15], [49230, 90], [49323, 12], [49529, 101], [49829, 99], [50128, 97], [50428, 23], [50469, 54], [50728, 22], [50810, 11], [51027, 23], [51111, 9], [51327, 55], [51408, 11], [51626, 62], [51702, 16], [51926, 91], [52225, 92], [52525, 91], [52824, 91], [53124, 91], [53424, 90], [53723, 91], [54022, 91], [54322, 91], [54621, 91], [54922, 90], [55223, 88], [55523, 11], [55546, 65], [55824, 11], [55846, 64], [56124, 11], [56145, 64], [56425, 11], [56445, 64], [56725, 11], [56745, 64], [57026, 11], [57044, 64], [57326, 11], [57344, 64], [57627, 11], [57644, 64], [57928, 10], [57943, 64], [58228, 11], [58243, 64], [58529, 10], [58542, 64], [58829, 11], [58842, 64], [59130, 10], [59142, 64], [59430, 75], [59731, 74], [60032, 73], [60332, 72], [60633, 71], [60933, 71], [61234, 70], [61534, 70], [61835, 69], [62136, 9], [62158, 46], [62436, 10], [62457, 46], [62737, 9], [62757, 46], [63037, 10], [63057, 46], [63338, 9], [63356, 47], [63638, 10], [63656, 47], [63939, 10], [63956, 47], [64240, 9], [64256, 47], [64540, 10], [64555, 48], [64841, 9], [64855, 48], [65141, 10], [65155, 48], [65442, 9], [65454, 49], [65742, 10], [65754, 49], [66043, 9], [66054, 49], [66343, 10], [66354, 49], [66644, 59], [66945, 58], [67245, 58], [67546, 57], [67846, 57], [68147, 56], [68447, 56], [68748, 55], [69049, 8], [69349, 9], [69650, 8], [69950, 9], [70251, 8], [70551, 9], [70852, 8], [71153, 8], [71453, 8], [71754, 8], [72054, 8], [72355, 8], [72655, 8], [72956, 8], [73256, 9], [73557, 8], [73858, 8], [74158, 8], [74459, 7], [74759, 7], [75060, 5]], "point": [80, 182]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan28", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.25, "y": 0.900998235, "z": -2.0}, "object_poses": [{"objectName": "Pan_d1250561", "position": {"x": -1.47139764, "y": 0.9403441, "z": -3.75178385}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_d1250561", "position": {"x": -0.3349222, "y": 1.28464341, "z": -0.5296891}, "rotation": {"x": 0.0, "y": 0.000169038554, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -0.471406519, "y": 0.8466891, "z": -3.16372871}, "rotation": {"x": 9.923132e-15, "y": 315.000031, "z": -9.92312e-15}}, {"objectName": "Potato_5e3edff7", "position": {"x": -2.96264076, "y": 0.926596642, "z": -0.6338287}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -4.06235027, "y": 0.6905421, "z": -0.491501927}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -3.89321685, "y": 0.760130465, "z": -3.16021585}, "rotation": {"x": 0.0, "y": 109.125885, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -2.56082487, "y": 0.895750165, "z": -0.462293148}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -3.83992052, "y": 0.7612178, "z": -3.57092285}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -2.761731, "y": 0.909620643, "z": -0.204985365}, "rotation": {"x": 0.0, "y": 0.000327849062, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -4.002282, "y": 0.7054999, "z": -0.430667937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -4.002282, "y": 0.7323631, "z": -0.369833916}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -3.935215, "y": 0.7607916, "z": -2.95915747}, "rotation": {"x": 0.0, "y": 334.125916, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -3.16354656, "y": 0.9760524, "z": -0.376520932}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -2.56082439, "y": 0.9760524, "z": -0.37652427}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.713391, "y": 0.8510728, "z": -3.778327}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "SaltShaker_6f13b4f9", "position": {"x": -0.157691017, "y": 0.937606156, "z": -1.31903028}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -3.565363, "y": 0.890926957, "z": -0.633825362}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -0.394597232, "y": 0.9376062, "z": -1.5381465}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -2.35991573, "y": 0.956032634, "z": -0.119218737}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -2.35401559, "y": 0.161071837, "z": -3.60427237}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -3.423853, "y": 0.7582775, "z": -3.01685834}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Pot_c7418f98", "position": {"x": -0.606, "y": 0.8029107, "z": -3.419}, "rotation": {"x": 0.0, "y": 315.000031, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -0.7214115, "y": 0.8140154, "z": -3.49191141}, "rotation": {"x": 9.923132e-15, "y": 315.000031, "z": -9.92312e-15}}, {"objectName": "Cup_41873c33", "position": {"x": -3.58681631, "y": 0.76079154, "z": -2.841071}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -0.2455739, "y": 1.53947651, "z": -0.87862283}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pan_d1250561", "position": {"x": -0.2221, "y": 0.961999953, "z": -2.243}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -0.212670773, "y": 1.51676917, "z": -3.05265713}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -3.92534733, "y": 0.84152, "z": -3.39901733}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -4.182487, "y": 0.7054999, "z": -0.187332019}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_1b546504", "position": {"x": -3.654337, "y": 0.7857067, "z": -3.68148685}, "rotation": {"x": 0.0, "y": 64.1259, "z": 0.0}}, {"objectName": "SaltShaker_6f13b4f9", "position": {"x": -3.8821454, "y": 0.68760246, "z": -0.4306679}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -2.13991928, "y": 0.9386062, "z": -3.439128}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -2.35991645, "y": 0.894662857, "z": -0.2907565}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -4.002282, "y": 0.7519119, "z": -0.187332019}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -3.565362, "y": 0.894185, "z": -0.462287575}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -3.32981253, "y": 0.79206425, "z": -3.421749}, "rotation": {"x": 0.0, "y": 109.125885, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -4.24255562, "y": 0.7315668, "z": -0.248166}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -0.244763374, "y": 0.943429351, "z": -2.847621}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.56536078, "y": 0.98560524, "z": -0.290749818}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -1.11778057, "y": 0.0767551661, "z": -3.53237867}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -3.183842, "y": 0.758396447, "z": -3.47237}, "rotation": {"x": 0.0, "y": 109.125885, "z": 0.0}}], "object_toggles": [], "random_seed": 382313146, "scene_num": 28}, "task_id": "trial_T20190907_165203_052598", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3F7G1FSFWQPLE_3EJPLAJKEPXHZZKHUOZWYRA299LZ6Q", "high_descs": ["Turn left and walk across the room, then turn left and walk up to the round black table in the corner of the room.", "Pick up the black round glass off of the round black table.", "Turn left and walk over to the oven, then look up at the microwave.", "Open the microwave door and put the glass inside, then close the door and microwave the glass for a couple seconds, after that open the microwave remove the glass and close the door.", "Turn around and walk across the room, then hang a right and walk up to the small black shelving unit in the corner of the room.", "Put the heated black glass on the top shelf of the black shelving unit in between the two eggs."], "task_desc": "Put a heated black glass on a shelving unit.", "votes": [1, 1]}, {"assignment_id": "A24RGLS3BRZ60J_3LUY3GC632H6PVWGQP6MD78KULJP7F", "high_descs": ["Turn to the left and go straight to find the black table. Turn to the left to face it.", "Pick up the glass from the front of the table.", "Hold the glass and turn to the left. to go across to the stove", "Place the glass in the microwave and turn it on. Take the glass out out the microwave and shut the door.", "Take the glass and turn around and go straight across the room. Turn right to face the small black table.", "Place the glass in the middle of the small table."], "task_desc": "Heat a glass and move it to the table.", "votes": [1, 1]}, {"assignment_id": "A3MLUEOP3CCLXL_3X0H8UUIT45IS2D9QEIS0SCCAWWWS1", "high_descs": ["Turn around and walk towards the white door then hand a left and head to the round, black table", "Pick up the black cup that is sitting to the right of the soap bottle", "Turn left and cross the room to the Microwave that is above the stove", "Place the cup into the microwave and close the door, then turn it on.  Open the microwave and pick up the cup then close the door", "Turn around and cross the room, take a right at the white door and stand in front of the black shelf", "Place the cup on the top shelf in the middle of the spatulas and eggs"], "task_desc": "Place a heated cup on a shelf", "votes": [1, 1]}]}}