
(define (problem plan_trial_T20190908_001839_725528)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Boots_bar__minus_05_dot_79_bar__plus_00_dot_01_bar__plus_04_dot_82 - object
        Box_bar__minus_01_dot_62_bar__plus_00_dot_58_bar__plus_07_dot_04 - object
        Box_bar__minus_01_dot_78_bar__plus_00_dot_58_bar__plus_06_dot_74 - object
        Box_bar__minus_02_dot_32_bar__plus_00_dot_91_bar__plus_02_dot_68 - object
        Candle_bar__minus_00_dot_50_bar__plus_00_dot_68_bar__plus_00_dot_29 - object
        Candle_bar__minus_00_dot_56_bar__plus_00_dot_67_bar__plus_00_dot_40 - object
        Candle_bar__minus_00_dot_62_bar__plus_00_dot_67_bar__plus_08_dot_44 - object
        Chair_bar__minus_00_dot_33_bar__plus_00_dot_02_bar__plus_01_dot_00 - object
        Chair_bar__minus_02_dot_35_bar__plus_00_dot_01_bar__plus_02_dot_75 - object
        Chair_bar__minus_02_dot_48_bar__plus_00_dot_01_bar__plus_01_dot_63 - object
        Chair_bar__minus_03_dot_33_bar__plus_00_dot_01_bar__plus_01_dot_70 - object
        Chair_bar__minus_03_dot_33_bar__plus_00_dot_01_bar__plus_02_dot_74 - object
        CreditCard_bar__minus_02_dot_05_bar__plus_00_dot_78_bar__plus_01_dot_75 - object
        CreditCard_bar__minus_03_dot_43_bar__plus_00_dot_78_bar__plus_02_dot_22 - object
        FloorLamp_bar__minus_05_dot_65_bar__plus_00_dot_01_bar__plus_08_dot_53 - object
        HousePlant_bar__minus_03_dot_20_bar__plus_00_dot_45_bar__plus_06_dot_80 - object
        HousePlant_bar__minus_05_dot_36_bar__plus_01_dot_48_bar__plus_05_dot_44 - object
        KeyChain_bar__minus_02_dot_93_bar__plus_00_dot_46_bar__plus_06_dot_14 - object
        KeyChain_bar__minus_03_dot_85_bar__plus_00_dot_33_bar__plus_04_dot_58 - object
        Lamp_bar__minus_00_dot_35_bar__plus_00_dot_68_bar__plus_00_dot_34 - object
        Lamp_bar__minus_00_dot_43_bar__plus_00_dot_68_bar__plus_08_dot_64 - object
        Laptop_bar__minus_03_dot_16_bar__plus_00_dot_78_bar__plus_01_dot_91 - object
        LightSwitch_bar__minus_06_dot_00_bar__plus_01_dot_34_bar__plus_03_dot_43 - object
        Mirror_bar__minus_05_dot_49_bar__plus_01_dot_83_bar__plus_06_dot_51 - object
        Newspaper_bar__minus_01_dot_93_bar__plus_00_dot_45_bar__plus_07_dot_33 - object
        Newspaper_bar__minus_02_dot_88_bar__plus_00_dot_78_bar__plus_02_dot_22 - object
        Painting_bar__minus_05_dot_96_bar__plus_01_dot_53_bar__plus_01_dot_15 - object
        Pillow_bar__minus_01_dot_72_bar__plus_00_dot_52_bar__plus_05_dot_73 - object
        Plate_bar__minus_02_dot_32_bar__plus_00_dot_80_bar__plus_01_dot_91 - object
        Plate_bar__minus_02_dot_81_bar__plus_00_dot_55_bar__plus_08_dot_47 - object
        Plate_bar__minus_03_dot_71_bar__plus_00_dot_80_bar__plus_02_dot_53 - object
        RemoteControl_bar__minus_02_dot_93_bar__plus_00_dot_46_bar__plus_06_dot_97 - object
        RemoteControl_bar__minus_03_dot_23_bar__plus_00_dot_46_bar__plus_06_dot_97 - object
        RemoteControl_bar__minus_03_dot_71_bar__plus_00_dot_78_bar__plus_02_dot_22 - object
        Statue_bar__minus_02_dot_60_bar__plus_00_dot_79_bar__plus_02_dot_68 - object
        Statue_bar__minus_03_dot_23_bar__plus_00_dot_47_bar__plus_05_dot_93 - object
        Statue_bar__minus_03_dot_43_bar__plus_00_dot_88_bar__plus_02_dot_06 - object
        Television_bar__minus_03_dot_11_bar__plus_01_dot_02_bar__plus_08_dot_71 - object
        TissueBox_bar__minus_03_dot_16_bar__plus_00_dot_78_bar__plus_02_dot_68 - object
        TissueBox_bar__minus_03_dot_29_bar__plus_00_dot_54_bar__plus_08_dot_35 - object
        TissueBox_bar__minus_03_dot_71_bar__plus_00_dot_78_bar__plus_01_dot_91 - object
        Watch_bar__minus_03_dot_19_bar__plus_00_dot_53_bar__plus_08_dot_64 - object
        Window_bar__plus_00_dot_16_bar__plus_01_dot_20_bar__plus_01_dot_09 - object
        Window_bar__plus_00_dot_16_bar__plus_01_dot_20_bar__plus_02_dot_99 - object
        Window_bar__plus_00_dot_16_bar__plus_01_dot_20_bar__plus_04_dot_92 - object
        Window_bar__plus_00_dot_16_bar__plus_01_dot_20_bar__plus_06_dot_90 - object
        Window_bar__minus_01_dot_06_bar__plus_01_dot_20_bar__minus_00_dot_16 - object
        Window_bar__minus_02_dot_94_bar__plus_01_dot_20_bar__minus_00_dot_16 - object
        Window_bar__minus_04_dot_86_bar__plus_01_dot_20_bar__minus_00_dot_16 - object
        ArmChair_bar__minus_02_dot_69_bar__plus_00_dot_00_bar__plus_04_dot_62 - receptacle
        ArmChair_bar__minus_03_dot_66_bar__plus_00_dot_00_bar__plus_04_dot_56 - receptacle
        CoffeeTable_bar__minus_03_dot_13_bar__plus_00_dot_02_bar__plus_08_dot_53 - receptacle
        CoffeeTable_bar__minus_03_dot_24_bar__plus_00_dot_01_bar__plus_06_dot_60 - receptacle
        DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23 - receptacle
        GarbageCan_bar__minus_05_dot_75_bar__plus_00_dot_01_bar__plus_00_dot_26 - receptacle
        SideTable_bar__minus_00_dot_40_bar__plus_00_dot_66_bar__plus_00_dot_36 - receptacle
        SideTable_bar__minus_00_dot_53_bar__plus_00_dot_66_bar__plus_08_dot_52 - receptacle
        Sofa_bar__minus_01_dot_74_bar__minus_00_dot_08_bar__plus_06_dot_45 - receptacle
        loc_bar__minus_22_bar_14_bar_3_bar_30 - location
        loc_bar__minus_19_bar_26_bar_3_bar__minus_15 - location
        loc_bar__minus_19_bar_22_bar_3_bar_15 - location
        loc_bar__minus_21_bar_5_bar_3_bar_15 - location
        loc_bar__minus_22_bar_18_bar_0_bar_60 - location
        loc_bar__minus_15_bar_22_bar_2_bar_60 - location
        loc_bar__minus_21_bar_2_bar_3_bar_60 - location
        loc_bar__minus_5_bar_33_bar_1_bar_60 - location
        loc_bar__minus_6_bar_11_bar_3_bar_60 - location
        loc_bar__minus_12_bar_2_bar_2_bar_30 - location
        loc_bar__minus_13_bar_4_bar_0_bar_60 - location
        loc_bar__minus_4_bar_2_bar_2_bar_30 - location
        loc_bar__minus_17_bar_26_bar_1_bar_60 - location
        loc_bar__minus_18_bar_32_bar_1_bar_45 - location
        loc_bar__minus_19_bar_2_bar_2_bar_30 - location
        loc_bar__minus_5_bar_3_bar_1_bar_45 - location
        loc_bar__minus_4_bar_4_bar_1_bar_60 - location
        loc_bar__minus_6_bar_9_bar_3_bar_45 - location
        loc_bar__minus_2_bar_6_bar_1_bar_30 - location
        loc_bar__minus_12_bar_13_bar_2_bar_60 - location
        loc_bar__minus_10_bar_4_bar_0_bar_60 - location
        loc_bar__minus_2_bar_28_bar_1_bar_30 - location
        loc_bar__minus_10_bar_27_bar_1_bar_60 - location
        loc_bar__minus_10_bar_22_bar_2_bar_60 - location
        loc_bar__minus_20_bar_33_bar_3_bar_60 - location
        loc_bar__minus_2_bar_12_bar_1_bar_30 - location
        loc_bar__minus_2_bar_20_bar_1_bar_30 - location
        loc_bar__minus_12_bar_15_bar_1_bar_30 - location
        )
    

(:init


        (receptacleType CoffeeTable_bar__minus_03_dot_13_bar__plus_00_dot_02_bar__plus_08_dot_53 CoffeeTableType)
        (receptacleType DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23 DiningTableType)
        (receptacleType SideTable_bar__minus_00_dot_40_bar__plus_00_dot_66_bar__plus_00_dot_36 SideTableType)
        (receptacleType CoffeeTable_bar__minus_03_dot_24_bar__plus_00_dot_01_bar__plus_06_dot_60 CoffeeTableType)
        (receptacleType ArmChair_bar__minus_02_dot_69_bar__plus_00_dot_00_bar__plus_04_dot_62 ArmChairType)
        (receptacleType SideTable_bar__minus_00_dot_53_bar__plus_00_dot_66_bar__plus_08_dot_52 SideTableType)
        (receptacleType ArmChair_bar__minus_03_dot_66_bar__plus_00_dot_00_bar__plus_04_dot_56 ArmChairType)
        (receptacleType Sofa_bar__minus_01_dot_74_bar__minus_00_dot_08_bar__plus_06_dot_45 SofaType)
        (receptacleType GarbageCan_bar__minus_05_dot_75_bar__plus_00_dot_01_bar__plus_00_dot_26 GarbageCanType)
        (objectType HousePlant_bar__minus_05_dot_36_bar__plus_01_dot_48_bar__plus_05_dot_44 HousePlantType)
        (objectType Box_bar__minus_02_dot_32_bar__plus_00_dot_91_bar__plus_02_dot_68 BoxType)
        (objectType KeyChain_bar__minus_02_dot_93_bar__plus_00_dot_46_bar__plus_06_dot_14 KeyChainType)
        (objectType Pillow_bar__minus_01_dot_72_bar__plus_00_dot_52_bar__plus_05_dot_73 PillowType)
        (objectType Box_bar__minus_01_dot_78_bar__plus_00_dot_58_bar__plus_06_dot_74 BoxType)
        (objectType Painting_bar__minus_05_dot_96_bar__plus_01_dot_53_bar__plus_01_dot_15 PaintingType)
        (objectType Box_bar__minus_01_dot_62_bar__plus_00_dot_58_bar__plus_07_dot_04 BoxType)
        (objectType FloorLamp_bar__minus_05_dot_65_bar__plus_00_dot_01_bar__plus_08_dot_53 FloorLampType)
        (objectType Window_bar__plus_00_dot_16_bar__plus_01_dot_20_bar__plus_06_dot_90 WindowType)
        (objectType Window_bar__plus_00_dot_16_bar__plus_01_dot_20_bar__plus_04_dot_92 WindowType)
        (objectType Statue_bar__minus_02_dot_60_bar__plus_00_dot_79_bar__plus_02_dot_68 StatueType)
        (objectType Chair_bar__minus_02_dot_35_bar__plus_00_dot_01_bar__plus_02_dot_75 ChairType)
        (objectType Television_bar__minus_03_dot_11_bar__plus_01_dot_02_bar__plus_08_dot_71 TelevisionType)
        (objectType TissueBox_bar__minus_03_dot_16_bar__plus_00_dot_78_bar__plus_02_dot_68 TissueBoxType)
        (objectType Candle_bar__minus_00_dot_50_bar__plus_00_dot_68_bar__plus_00_dot_29 CandleType)
        (objectType Window_bar__plus_00_dot_16_bar__plus_01_dot_20_bar__plus_02_dot_99 WindowType)
        (objectType HousePlant_bar__minus_03_dot_20_bar__plus_00_dot_45_bar__plus_06_dot_80 HousePlantType)
        (objectType Chair_bar__minus_02_dot_48_bar__plus_00_dot_01_bar__plus_01_dot_63 ChairType)
        (objectType Window_bar__plus_00_dot_16_bar__plus_01_dot_20_bar__plus_01_dot_09 WindowType)
        (objectType Newspaper_bar__minus_01_dot_93_bar__plus_00_dot_45_bar__plus_07_dot_33 NewspaperType)
        (objectType RemoteControl_bar__minus_03_dot_71_bar__plus_00_dot_78_bar__plus_02_dot_22 RemoteControlType)
        (objectType CreditCard_bar__minus_03_dot_43_bar__plus_00_dot_78_bar__plus_02_dot_22 CreditCardType)
        (objectType Laptop_bar__minus_03_dot_16_bar__plus_00_dot_78_bar__plus_01_dot_91 LaptopType)
        (objectType Chair_bar__minus_03_dot_33_bar__plus_00_dot_01_bar__plus_01_dot_70 ChairType)
        (objectType Newspaper_bar__minus_02_dot_88_bar__plus_00_dot_78_bar__plus_02_dot_22 NewspaperType)
        (objectType Plate_bar__minus_02_dot_81_bar__plus_00_dot_55_bar__plus_08_dot_47 PlateType)
        (objectType Candle_bar__minus_00_dot_62_bar__plus_00_dot_67_bar__plus_08_dot_44 CandleType)
        (objectType LightSwitch_bar__minus_06_dot_00_bar__plus_01_dot_34_bar__plus_03_dot_43 LightSwitchType)
        (objectType Mirror_bar__minus_05_dot_49_bar__plus_01_dot_83_bar__plus_06_dot_51 MirrorType)
        (objectType Statue_bar__minus_03_dot_23_bar__plus_00_dot_47_bar__plus_05_dot_93 StatueType)
        (objectType TissueBox_bar__minus_03_dot_29_bar__plus_00_dot_54_bar__plus_08_dot_35 TissueBoxType)
        (objectType Chair_bar__minus_00_dot_33_bar__plus_00_dot_02_bar__plus_01_dot_00 ChairType)
        (objectType RemoteControl_bar__minus_02_dot_93_bar__plus_00_dot_46_bar__plus_06_dot_97 RemoteControlType)
        (objectType Plate_bar__minus_03_dot_71_bar__plus_00_dot_80_bar__plus_02_dot_53 PlateType)
        (objectType Plate_bar__minus_02_dot_32_bar__plus_00_dot_80_bar__plus_01_dot_91 PlateType)
        (objectType KeyChain_bar__minus_03_dot_85_bar__plus_00_dot_33_bar__plus_04_dot_58 KeyChainType)
        (objectType Watch_bar__minus_03_dot_19_bar__plus_00_dot_53_bar__plus_08_dot_64 WatchType)
        (objectType Candle_bar__minus_00_dot_56_bar__plus_00_dot_67_bar__plus_00_dot_40 CandleType)
        (objectType Window_bar__minus_04_dot_86_bar__plus_01_dot_20_bar__minus_00_dot_16 WindowType)
        (objectType Statue_bar__minus_03_dot_43_bar__plus_00_dot_88_bar__plus_02_dot_06 StatueType)
        (objectType Boots_bar__minus_05_dot_79_bar__plus_00_dot_01_bar__plus_04_dot_82 BootsType)
        (objectType CreditCard_bar__minus_02_dot_05_bar__plus_00_dot_78_bar__plus_01_dot_75 CreditCardType)
        (objectType Window_bar__minus_02_dot_94_bar__plus_01_dot_20_bar__minus_00_dot_16 WindowType)
        (objectType TissueBox_bar__minus_03_dot_71_bar__plus_00_dot_78_bar__plus_01_dot_91 TissueBoxType)
        (objectType Window_bar__minus_01_dot_06_bar__plus_01_dot_20_bar__minus_00_dot_16 WindowType)
        (objectType RemoteControl_bar__minus_03_dot_23_bar__plus_00_dot_46_bar__plus_06_dot_97 RemoteControlType)
        (objectType Chair_bar__minus_03_dot_33_bar__plus_00_dot_01_bar__plus_02_dot_74 ChairType)
        (canContain CoffeeTableType CandleType)
        (canContain CoffeeTableType WatchType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType PlateType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType TissueBoxType)
        (canContain CoffeeTableType StatueType)
        (canContain DiningTableType CandleType)
        (canContain DiningTableType WatchType)
        (canContain DiningTableType NewspaperType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType TissueBoxType)
        (canContain DiningTableType StatueType)
        (canContain SideTableType CandleType)
        (canContain SideTableType WatchType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PlateType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType StatueType)
        (canContain CoffeeTableType CandleType)
        (canContain CoffeeTableType WatchType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType PlateType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType TissueBoxType)
        (canContain CoffeeTableType StatueType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain SideTableType CandleType)
        (canContain SideTableType WatchType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PlateType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType StatueType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain GarbageCanType NewspaperType)
        (canContain GarbageCanType TissueBoxType)
        (pickupable Box_bar__minus_02_dot_32_bar__plus_00_dot_91_bar__plus_02_dot_68)
        (pickupable KeyChain_bar__minus_02_dot_93_bar__plus_00_dot_46_bar__plus_06_dot_14)
        (pickupable Pillow_bar__minus_01_dot_72_bar__plus_00_dot_52_bar__plus_05_dot_73)
        (pickupable Box_bar__minus_01_dot_78_bar__plus_00_dot_58_bar__plus_06_dot_74)
        (pickupable Box_bar__minus_01_dot_62_bar__plus_00_dot_58_bar__plus_07_dot_04)
        (pickupable Statue_bar__minus_02_dot_60_bar__plus_00_dot_79_bar__plus_02_dot_68)
        (pickupable TissueBox_bar__minus_03_dot_16_bar__plus_00_dot_78_bar__plus_02_dot_68)
        (pickupable Candle_bar__minus_00_dot_50_bar__plus_00_dot_68_bar__plus_00_dot_29)
        (pickupable Newspaper_bar__minus_01_dot_93_bar__plus_00_dot_45_bar__plus_07_dot_33)
        (pickupable RemoteControl_bar__minus_03_dot_71_bar__plus_00_dot_78_bar__plus_02_dot_22)
        (pickupable CreditCard_bar__minus_03_dot_43_bar__plus_00_dot_78_bar__plus_02_dot_22)
        (pickupable Laptop_bar__minus_03_dot_16_bar__plus_00_dot_78_bar__plus_01_dot_91)
        (pickupable Newspaper_bar__minus_02_dot_88_bar__plus_00_dot_78_bar__plus_02_dot_22)
        (pickupable Plate_bar__minus_02_dot_81_bar__plus_00_dot_55_bar__plus_08_dot_47)
        (pickupable Candle_bar__minus_00_dot_62_bar__plus_00_dot_67_bar__plus_08_dot_44)
        (pickupable Statue_bar__minus_03_dot_23_bar__plus_00_dot_47_bar__plus_05_dot_93)
        (pickupable TissueBox_bar__minus_03_dot_29_bar__plus_00_dot_54_bar__plus_08_dot_35)
        (pickupable RemoteControl_bar__minus_02_dot_93_bar__plus_00_dot_46_bar__plus_06_dot_97)
        (pickupable Plate_bar__minus_03_dot_71_bar__plus_00_dot_80_bar__plus_02_dot_53)
        (pickupable Plate_bar__minus_02_dot_32_bar__plus_00_dot_80_bar__plus_01_dot_91)
        (pickupable KeyChain_bar__minus_03_dot_85_bar__plus_00_dot_33_bar__plus_04_dot_58)
        (pickupable Watch_bar__minus_03_dot_19_bar__plus_00_dot_53_bar__plus_08_dot_64)
        (pickupable Candle_bar__minus_00_dot_56_bar__plus_00_dot_67_bar__plus_00_dot_40)
        (pickupable Statue_bar__minus_03_dot_43_bar__plus_00_dot_88_bar__plus_02_dot_06)
        (pickupable Boots_bar__minus_05_dot_79_bar__plus_00_dot_01_bar__plus_04_dot_82)
        (pickupable CreditCard_bar__minus_02_dot_05_bar__plus_00_dot_78_bar__plus_01_dot_75)
        (pickupable TissueBox_bar__minus_03_dot_71_bar__plus_00_dot_78_bar__plus_01_dot_91)
        (pickupable RemoteControl_bar__minus_03_dot_23_bar__plus_00_dot_46_bar__plus_06_dot_97)
        (isReceptacleObject Box_bar__minus_02_dot_32_bar__plus_00_dot_91_bar__plus_02_dot_68)
        (isReceptacleObject Box_bar__minus_01_dot_78_bar__plus_00_dot_58_bar__plus_06_dot_74)
        (isReceptacleObject Box_bar__minus_01_dot_62_bar__plus_00_dot_58_bar__plus_07_dot_04)
        (isReceptacleObject Plate_bar__minus_02_dot_81_bar__plus_00_dot_55_bar__plus_08_dot_47)
        (isReceptacleObject Plate_bar__minus_03_dot_71_bar__plus_00_dot_80_bar__plus_02_dot_53)
        (isReceptacleObject Plate_bar__minus_02_dot_32_bar__plus_00_dot_80_bar__plus_01_dot_91)
        
        
        (atLocation agent1 loc_bar__minus_12_bar_15_bar_1_bar_30)
        
        (cleanable Plate_bar__minus_02_dot_81_bar__plus_00_dot_55_bar__plus_08_dot_47)
        (cleanable Plate_bar__minus_03_dot_71_bar__plus_00_dot_80_bar__plus_02_dot_53)
        (cleanable Plate_bar__minus_02_dot_32_bar__plus_00_dot_80_bar__plus_01_dot_91)
        
        (heatable Plate_bar__minus_02_dot_81_bar__plus_00_dot_55_bar__plus_08_dot_47)
        (heatable Plate_bar__minus_03_dot_71_bar__plus_00_dot_80_bar__plus_02_dot_53)
        (heatable Plate_bar__minus_02_dot_32_bar__plus_00_dot_80_bar__plus_01_dot_91)
        (coolable Plate_bar__minus_02_dot_81_bar__plus_00_dot_55_bar__plus_08_dot_47)
        (coolable Plate_bar__minus_03_dot_71_bar__plus_00_dot_80_bar__plus_02_dot_53)
        (coolable Plate_bar__minus_02_dot_32_bar__plus_00_dot_80_bar__plus_01_dot_91)
        
        
        (toggleable FloorLamp_bar__minus_05_dot_65_bar__plus_00_dot_01_bar__plus_08_dot_53)
        
        
        
        
        (inReceptacle Box_bar__minus_02_dot_32_bar__plus_00_dot_91_bar__plus_02_dot_68 DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23)
        (inReceptacle TissueBox_bar__minus_03_dot_16_bar__plus_00_dot_78_bar__plus_02_dot_68 DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23)
        (inReceptacle RemoteControl_bar__minus_03_dot_71_bar__plus_00_dot_78_bar__plus_02_dot_22 DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23)
        (inReceptacle CreditCard_bar__minus_03_dot_43_bar__plus_00_dot_78_bar__plus_02_dot_22 DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23)
        (inReceptacle Laptop_bar__minus_03_dot_16_bar__plus_00_dot_78_bar__plus_01_dot_91 DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23)
        (inReceptacle Statue_bar__minus_03_dot_43_bar__plus_00_dot_88_bar__plus_02_dot_06 DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23)
        (inReceptacle Statue_bar__minus_02_dot_60_bar__plus_00_dot_79_bar__plus_02_dot_68 DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23)
        (inReceptacle Newspaper_bar__minus_02_dot_88_bar__plus_00_dot_78_bar__plus_02_dot_22 DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23)
        (inReceptacle CreditCard_bar__minus_02_dot_05_bar__plus_00_dot_78_bar__plus_01_dot_75 DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23)
        (inReceptacle Plate_bar__minus_03_dot_71_bar__plus_00_dot_80_bar__plus_02_dot_53 DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23)
        (inReceptacle TissueBox_bar__minus_03_dot_71_bar__plus_00_dot_78_bar__plus_01_dot_91 DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23)
        (inReceptacle Plate_bar__minus_02_dot_32_bar__plus_00_dot_80_bar__plus_01_dot_91 DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23)
        (inReceptacle KeyChain_bar__minus_02_dot_93_bar__plus_00_dot_46_bar__plus_06_dot_14 CoffeeTable_bar__minus_03_dot_24_bar__plus_00_dot_01_bar__plus_06_dot_60)
        (inReceptacle Statue_bar__minus_03_dot_23_bar__plus_00_dot_47_bar__plus_05_dot_93 CoffeeTable_bar__minus_03_dot_24_bar__plus_00_dot_01_bar__plus_06_dot_60)
        (inReceptacle RemoteControl_bar__minus_02_dot_93_bar__plus_00_dot_46_bar__plus_06_dot_97 CoffeeTable_bar__minus_03_dot_24_bar__plus_00_dot_01_bar__plus_06_dot_60)
        (inReceptacle HousePlant_bar__minus_03_dot_20_bar__plus_00_dot_45_bar__plus_06_dot_80 CoffeeTable_bar__minus_03_dot_24_bar__plus_00_dot_01_bar__plus_06_dot_60)
        (inReceptacle RemoteControl_bar__minus_03_dot_23_bar__plus_00_dot_46_bar__plus_06_dot_97 CoffeeTable_bar__minus_03_dot_24_bar__plus_00_dot_01_bar__plus_06_dot_60)
        (inReceptacle Box_bar__minus_01_dot_62_bar__plus_00_dot_58_bar__plus_07_dot_04 Sofa_bar__minus_01_dot_74_bar__minus_00_dot_08_bar__plus_06_dot_45)
        (inReceptacle Pillow_bar__minus_01_dot_72_bar__plus_00_dot_52_bar__plus_05_dot_73 Sofa_bar__minus_01_dot_74_bar__minus_00_dot_08_bar__plus_06_dot_45)
        (inReceptacle Box_bar__minus_01_dot_78_bar__plus_00_dot_58_bar__plus_06_dot_74 Sofa_bar__minus_01_dot_74_bar__minus_00_dot_08_bar__plus_06_dot_45)
        (inReceptacle Newspaper_bar__minus_01_dot_93_bar__plus_00_dot_45_bar__plus_07_dot_33 Sofa_bar__minus_01_dot_74_bar__minus_00_dot_08_bar__plus_06_dot_45)
        (inReceptacle KeyChain_bar__minus_03_dot_85_bar__plus_00_dot_33_bar__plus_04_dot_58 ArmChair_bar__minus_03_dot_66_bar__plus_00_dot_00_bar__plus_04_dot_56)
        (inReceptacle Candle_bar__minus_00_dot_62_bar__plus_00_dot_67_bar__plus_08_dot_44 SideTable_bar__minus_00_dot_53_bar__plus_00_dot_66_bar__plus_08_dot_52)
        (inReceptacle TissueBox_bar__minus_03_dot_29_bar__plus_00_dot_54_bar__plus_08_dot_35 CoffeeTable_bar__minus_03_dot_13_bar__plus_00_dot_02_bar__plus_08_dot_53)
        (inReceptacle Television_bar__minus_03_dot_11_bar__plus_01_dot_02_bar__plus_08_dot_71 CoffeeTable_bar__minus_03_dot_13_bar__plus_00_dot_02_bar__plus_08_dot_53)
        (inReceptacle Watch_bar__minus_03_dot_19_bar__plus_00_dot_53_bar__plus_08_dot_64 CoffeeTable_bar__minus_03_dot_13_bar__plus_00_dot_02_bar__plus_08_dot_53)
        (inReceptacle Plate_bar__minus_02_dot_81_bar__plus_00_dot_55_bar__plus_08_dot_47 CoffeeTable_bar__minus_03_dot_13_bar__plus_00_dot_02_bar__plus_08_dot_53)
        (inReceptacle Candle_bar__minus_00_dot_56_bar__plus_00_dot_67_bar__plus_00_dot_40 SideTable_bar__minus_00_dot_40_bar__plus_00_dot_66_bar__plus_00_dot_36)
        (inReceptacle Candle_bar__minus_00_dot_50_bar__plus_00_dot_68_bar__plus_00_dot_29 SideTable_bar__minus_00_dot_40_bar__plus_00_dot_66_bar__plus_00_dot_36)
        
        
        (receptacleAtLocation ArmChair_bar__minus_02_dot_69_bar__plus_00_dot_00_bar__plus_04_dot_62 loc_bar__minus_10_bar_22_bar_2_bar_60)
        (receptacleAtLocation ArmChair_bar__minus_03_dot_66_bar__plus_00_dot_00_bar__plus_04_dot_56 loc_bar__minus_15_bar_22_bar_2_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_03_dot_13_bar__plus_00_dot_02_bar__plus_08_dot_53 loc_bar__minus_18_bar_32_bar_1_bar_45)
        (receptacleAtLocation CoffeeTable_bar__minus_03_dot_24_bar__plus_00_dot_01_bar__plus_06_dot_60 loc_bar__minus_17_bar_26_bar_1_bar_60)
        (receptacleAtLocation DiningTable_bar__minus_02_dot_90_bar__plus_00_dot_01_bar__plus_02_dot_23 loc_bar__minus_6_bar_9_bar_3_bar_45)
        (receptacleAtLocation GarbageCan_bar__minus_05_dot_75_bar__plus_00_dot_01_bar__plus_00_dot_26 loc_bar__minus_21_bar_2_bar_3_bar_60)
        (receptacleAtLocation SideTable_bar__minus_00_dot_40_bar__plus_00_dot_66_bar__plus_00_dot_36 loc_bar__minus_5_bar_3_bar_1_bar_45)
        (receptacleAtLocation SideTable_bar__minus_00_dot_53_bar__plus_00_dot_66_bar__plus_08_dot_52 loc_bar__minus_5_bar_33_bar_1_bar_60)
        (receptacleAtLocation Sofa_bar__minus_01_dot_74_bar__minus_00_dot_08_bar__plus_06_dot_45 loc_bar__minus_10_bar_27_bar_1_bar_60)
        (objectAtLocation Statue_bar__minus_02_dot_60_bar__plus_00_dot_79_bar__plus_02_dot_68 loc_bar__minus_6_bar_9_bar_3_bar_45)
        (objectAtLocation RemoteControl_bar__minus_02_dot_93_bar__plus_00_dot_46_bar__plus_06_dot_97 loc_bar__minus_17_bar_26_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__minus_03_dot_43_bar__plus_00_dot_78_bar__plus_02_dot_22 loc_bar__minus_6_bar_9_bar_3_bar_45)
        (objectAtLocation Newspaper_bar__minus_02_dot_88_bar__plus_00_dot_78_bar__plus_02_dot_22 loc_bar__minus_6_bar_9_bar_3_bar_45)
        (objectAtLocation TissueBox_bar__minus_03_dot_71_bar__plus_00_dot_78_bar__plus_01_dot_91 loc_bar__minus_6_bar_9_bar_3_bar_45)
        (objectAtLocation Plate_bar__minus_03_dot_71_bar__plus_00_dot_80_bar__plus_02_dot_53 loc_bar__minus_6_bar_9_bar_3_bar_45)
        (objectAtLocation KeyChain_bar__minus_03_dot_85_bar__plus_00_dot_33_bar__plus_04_dot_58 loc_bar__minus_15_bar_22_bar_2_bar_60)
        (objectAtLocation Box_bar__minus_02_dot_32_bar__plus_00_dot_91_bar__plus_02_dot_68 loc_bar__minus_6_bar_9_bar_3_bar_45)
        (objectAtLocation Box_bar__minus_01_dot_78_bar__plus_00_dot_58_bar__plus_06_dot_74 loc_bar__minus_10_bar_27_bar_1_bar_60)
        (objectAtLocation RemoteControl_bar__minus_03_dot_71_bar__plus_00_dot_78_bar__plus_02_dot_22 loc_bar__minus_6_bar_9_bar_3_bar_45)
        (objectAtLocation TissueBox_bar__minus_03_dot_29_bar__plus_00_dot_54_bar__plus_08_dot_35 loc_bar__minus_18_bar_32_bar_1_bar_45)
        (objectAtLocation Plate_bar__minus_02_dot_32_bar__plus_00_dot_80_bar__plus_01_dot_91 loc_bar__minus_6_bar_9_bar_3_bar_45)
        (objectAtLocation Chair_bar__minus_00_dot_33_bar__plus_00_dot_02_bar__plus_01_dot_00 loc_bar__minus_4_bar_4_bar_1_bar_60)
        (objectAtLocation Chair_bar__minus_03_dot_33_bar__plus_00_dot_01_bar__plus_01_dot_70 loc_bar__minus_13_bar_4_bar_0_bar_60)
        (objectAtLocation Chair_bar__minus_03_dot_33_bar__plus_00_dot_01_bar__plus_02_dot_74 loc_bar__minus_12_bar_13_bar_2_bar_60)
        (objectAtLocation Chair_bar__minus_02_dot_35_bar__plus_00_dot_01_bar__plus_02_dot_75 loc_bar__minus_6_bar_11_bar_3_bar_60)
        (objectAtLocation Chair_bar__minus_02_dot_48_bar__plus_00_dot_01_bar__plus_01_dot_63 loc_bar__minus_10_bar_4_bar_0_bar_60)
        (objectAtLocation Box_bar__minus_01_dot_62_bar__plus_00_dot_58_bar__plus_07_dot_04 loc_bar__minus_10_bar_27_bar_1_bar_60)
        (objectAtLocation Mirror_bar__minus_05_dot_49_bar__plus_01_dot_83_bar__plus_06_dot_51 loc_bar__minus_19_bar_26_bar_3_bar__minus_15)
        (objectAtLocation FloorLamp_bar__minus_05_dot_65_bar__plus_00_dot_01_bar__plus_08_dot_53 loc_bar__minus_20_bar_33_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__minus_02_dot_93_bar__plus_00_dot_46_bar__plus_06_dot_14 loc_bar__minus_17_bar_26_bar_1_bar_60)
        (objectAtLocation Plate_bar__minus_02_dot_81_bar__plus_00_dot_55_bar__plus_08_dot_47 loc_bar__minus_18_bar_32_bar_1_bar_45)
        (objectAtLocation Candle_bar__minus_00_dot_56_bar__plus_00_dot_67_bar__plus_00_dot_40 loc_bar__minus_5_bar_3_bar_1_bar_45)
        (objectAtLocation Pillow_bar__minus_01_dot_72_bar__plus_00_dot_52_bar__plus_05_dot_73 loc_bar__minus_10_bar_27_bar_1_bar_60)
        (objectAtLocation HousePlant_bar__minus_03_dot_20_bar__plus_00_dot_45_bar__plus_06_dot_80 loc_bar__minus_17_bar_26_bar_1_bar_60)
        (objectAtLocation HousePlant_bar__minus_05_dot_36_bar__plus_01_dot_48_bar__plus_05_dot_44 loc_bar__minus_19_bar_22_bar_3_bar_15)
        (objectAtLocation Candle_bar__minus_00_dot_62_bar__plus_00_dot_67_bar__plus_08_dot_44 loc_bar__minus_5_bar_33_bar_1_bar_60)
        (objectAtLocation TissueBox_bar__minus_03_dot_16_bar__plus_00_dot_78_bar__plus_02_dot_68 loc_bar__minus_6_bar_9_bar_3_bar_45)
        (objectAtLocation Laptop_bar__minus_03_dot_16_bar__plus_00_dot_78_bar__plus_01_dot_91 loc_bar__minus_6_bar_9_bar_3_bar_45)
        (objectAtLocation Television_bar__minus_03_dot_11_bar__plus_01_dot_02_bar__plus_08_dot_71 loc_bar__minus_18_bar_32_bar_1_bar_45)
        (objectAtLocation Newspaper_bar__minus_01_dot_93_bar__plus_00_dot_45_bar__plus_07_dot_33 loc_bar__minus_10_bar_27_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__minus_02_dot_05_bar__plus_00_dot_78_bar__plus_01_dot_75 loc_bar__minus_6_bar_9_bar_3_bar_45)
        (objectAtLocation RemoteControl_bar__minus_03_dot_23_bar__plus_00_dot_46_bar__plus_06_dot_97 loc_bar__minus_17_bar_26_bar_1_bar_60)
        (objectAtLocation Boots_bar__minus_05_dot_79_bar__plus_00_dot_01_bar__plus_04_dot_82 loc_bar__minus_22_bar_18_bar_0_bar_60)
        (objectAtLocation Candle_bar__minus_00_dot_50_bar__plus_00_dot_68_bar__plus_00_dot_29 loc_bar__minus_5_bar_3_bar_1_bar_45)
        (objectAtLocation Statue_bar__minus_03_dot_43_bar__plus_00_dot_88_bar__plus_02_dot_06 loc_bar__minus_6_bar_9_bar_3_bar_45)
        (objectAtLocation LightSwitch_bar__minus_06_dot_00_bar__plus_01_dot_34_bar__plus_03_dot_43 loc_bar__minus_22_bar_14_bar_3_bar_30)
        (objectAtLocation Painting_bar__minus_05_dot_96_bar__plus_01_dot_53_bar__plus_01_dot_15 loc_bar__minus_21_bar_5_bar_3_bar_15)
        (objectAtLocation Watch_bar__minus_03_dot_19_bar__plus_00_dot_53_bar__plus_08_dot_64 loc_bar__minus_18_bar_32_bar_1_bar_45)
        (objectAtLocation Window_bar__plus_00_dot_16_bar__plus_01_dot_20_bar__plus_06_dot_90 loc_bar__minus_2_bar_28_bar_1_bar_30)
        (objectAtLocation Window_bar__minus_04_dot_86_bar__plus_01_dot_20_bar__minus_00_dot_16 loc_bar__minus_19_bar_2_bar_2_bar_30)
        (objectAtLocation Window_bar__plus_00_dot_16_bar__plus_01_dot_20_bar__plus_04_dot_92 loc_bar__minus_2_bar_20_bar_1_bar_30)
        (objectAtLocation Window_bar__plus_00_dot_16_bar__plus_01_dot_20_bar__plus_02_dot_99 loc_bar__minus_2_bar_12_bar_1_bar_30)
        (objectAtLocation Window_bar__plus_00_dot_16_bar__plus_01_dot_20_bar__plus_01_dot_09 loc_bar__minus_2_bar_6_bar_1_bar_30)
        (objectAtLocation Window_bar__minus_02_dot_94_bar__plus_01_dot_20_bar__minus_00_dot_16 loc_bar__minus_12_bar_2_bar_2_bar_30)
        (objectAtLocation Window_bar__minus_01_dot_06_bar__plus_01_dot_20_bar__minus_00_dot_16 loc_bar__minus_4_bar_2_bar_2_bar_30)
        (objectAtLocation Statue_bar__minus_03_dot_23_bar__plus_00_dot_47_bar__plus_05_dot_93 loc_bar__minus_17_bar_26_bar_1_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 NewspaperType)
                                    (receptacleType ?r ArmChairType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 NewspaperType)
                                            (receptacleType ?r ArmChairType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            