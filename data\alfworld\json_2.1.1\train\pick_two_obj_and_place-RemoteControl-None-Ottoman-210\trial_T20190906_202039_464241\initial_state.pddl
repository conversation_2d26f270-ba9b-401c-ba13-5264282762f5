
(define (problem plan_trial_T20190906_202039_464241)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__minus_01_dot_70_bar__plus_00_dot_31_bar__plus_04_dot_30 - object
        Chair_bar__minus_06_dot_02_bar__plus_00_dot_01_bar__plus_00_dot_09 - object
        CreditCard_bar__minus_01_dot_46_bar__plus_00_dot_45_bar__plus_01_dot_51 - object
        Curtains_bar__minus_00_dot_09_bar__plus_02_dot_46_bar__plus_02_dot_11 - object
        Curtains_bar__minus_01_dot_15_bar__plus_01_dot_38_bar__plus_04_dot_53 - object
        DeskLamp_bar__minus_06_dot_77_bar__plus_00_dot_41_bar__minus_00_dot_03 - object
        FloorLamp_bar__minus_00_dot_23_bar__plus_00_dot_00_bar__minus_00_dot_13 - object
        HousePlant_bar__minus_00_dot_29_bar__plus_00_dot_39_bar__plus_02_dot_11 - object
        KeyChain_bar__minus_01_dot_46_bar__plus_00_dot_46_bar__plus_01_dot_93 - object
        Laptop_bar__minus_01_dot_77_bar__plus_00_dot_45_bar__plus_02_dot_03 - object
        Laptop_bar__minus_02_dot_53_bar__plus_00_dot_50_bar__plus_00_dot_30 - object
        LightSwitch_bar__minus_07_dot_00_bar__plus_01_dot_36_bar__plus_02_dot_61 - object
        Newspaper_bar__minus_02_dot_71_bar__plus_01_dot_10_bar__plus_04_dot_23 - object
        Painting_bar__plus_00_dot_00_bar__plus_01_dot_58_bar__plus_04_dot_01 - object
        Painting_bar__minus_03_dot_71_bar__plus_01_dot_72_bar__plus_04_dot_60 - object
        Pillow_bar__minus_01_dot_83_bar__plus_00_dot_60_bar__plus_00_dot_22 - object
        RemoteControl_bar__minus_00_dot_37_bar__plus_00_dot_86_bar__plus_03_dot_90 - object
        RemoteControl_bar__minus_02_dot_35_bar__plus_00_dot_12_bar__plus_04_dot_15 - object
        RemoteControl_bar__minus_03_dot_00_bar__plus_01_dot_10_bar__plus_04_dot_30 - object
        RemoteControl_bar__minus_04_dot_03_bar__plus_00_dot_44_bar__plus_01_dot_40 - object
        Statue_bar__minus_02_dot_12_bar__plus_01_dot_11_bar__plus_04_dot_15 - object
        Television_bar__minus_02_dot_53_bar__plus_01_dot_58_bar__plus_04_dot_40 - object
        Watch_bar__minus_00_dot_33_bar__plus_00_dot_47_bar__plus_04_dot_09 - object
        WateringCan_bar__minus_00_dot_27_bar__plus_00_dot_01_bar__plus_02_dot_37 - object
        Window_bar__minus_00_dot_04_bar__plus_01_dot_71_bar__plus_02_dot_10 - object
        Window_bar__minus_01_dot_16_bar__plus_01_dot_70_bar__plus_04_dot_59 - object
        ArmChair_bar__minus_04_dot_39_bar__plus_00_dot_01_bar__plus_01_dot_40 - receptacle
        CoffeeTable_bar__minus_04_dot_29_bar__plus_00_dot_01_bar__plus_00_dot_41 - receptacle
        CoffeeTable_bar__minus_06_dot_72_bar__plus_00_dot_01_bar__plus_00_dot_00 - receptacle
        Drawer_bar__minus_00_dot_35_bar__plus_00_dot_21_bar__plus_03_dot_79 - receptacle
        Drawer_bar__minus_00_dot_35_bar__plus_00_dot_21_bar__plus_04_dot_23 - receptacle
        Drawer_bar__minus_00_dot_35_bar__plus_00_dot_59_bar__plus_03_dot_79 - receptacle
        Drawer_bar__minus_00_dot_35_bar__plus_00_dot_59_bar__plus_04_dot_23 - receptacle
        Drawer_bar__minus_02_dot_29_bar__plus_00_dot_27_bar__plus_04_dot_17 - receptacle
        Drawer_bar__minus_02_dot_29_bar__plus_00_dot_76_bar__plus_04_dot_17 - receptacle
        Drawer_bar__minus_02_dot_84_bar__plus_00_dot_27_bar__plus_04_dot_17 - receptacle
        Drawer_bar__minus_02_dot_84_bar__plus_00_dot_75_bar__plus_04_dot_17 - receptacle
        Dresser_bar__minus_00_dot_24_bar__plus_00_dot_01_bar__plus_04_dot_01 - receptacle
        Dresser_bar__minus_02_dot_56_bar__plus_00_dot_00_bar__plus_04_dot_30 - receptacle
        GarbageCan_bar__minus_04_dot_12_bar__plus_00_dot_00_bar__minus_00_dot_16 - receptacle
        Ottoman_bar__minus_01_dot_77_bar__plus_00_dot_01_bar__plus_01_dot_81 - receptacle
        Sofa_bar__minus_02_dot_53_bar__plus_00_dot_02_bar__plus_00_dot_26 - receptacle
        loc_bar__minus_3_bar_9_bar_1_bar__minus_30 - location
        loc_bar__minus_3_bar_9_bar_1_bar_60 - location
        loc_bar__minus_6_bar_15_bar_1_bar_45 - location
        loc_bar__minus_25_bar_4_bar_2_bar_60 - location
        loc_bar__minus_21_bar_3_bar_1_bar_60 - location
        loc_bar__minus_2_bar_2_bar_2_bar_60 - location
        loc_bar__minus_4_bar_16_bar_1_bar_60 - location
        loc_bar__minus_5_bar_16_bar_0_bar_0 - location
        loc_bar__minus_19_bar_1_bar_1_bar_60 - location
        loc_bar__minus_15_bar_16_bar_0_bar_0 - location
        loc_bar__minus_6_bar_13_bar_1_bar_45 - location
        loc_bar__minus_5_bar_16_bar_0_bar_30 - location
        loc_bar__minus_5_bar_15_bar_1_bar_45 - location
        loc_bar__minus_3_bar_16_bar_1_bar_0 - location
        loc_bar__minus_14_bar_15_bar_1_bar_60 - location
        loc_bar__minus_16_bar_15_bar_1_bar_45 - location
        loc_bar__minus_13_bar_15_bar_1_bar_60 - location
        loc_bar__minus_12_bar_15_bar_1_bar_45 - location
        loc_bar__minus_12_bar_6_bar_1_bar_60 - location
        loc_bar__minus_3_bar_9_bar_1_bar_0 - location
        loc_bar__minus_7_bar_15_bar_0_bar_60 - location
        loc_bar__minus_5_bar_14_bar_1_bar_45 - location
        loc_bar__minus_11_bar_15_bar_0_bar_60 - location
        loc_bar__minus_11_bar_7_bar_2_bar_45 - location
        loc_bar__minus_13_bar_5_bar_3_bar_60 - location
        loc_bar__minus_26_bar_10_bar_3_bar_30 - location
        loc_bar__minus_25_bar_2_bar_2_bar_60 - location
        loc_bar__minus_12_bar_5_bar_0_bar_30 - location
        )
    

(:init


        (receptacleType Drawer_bar__minus_02_dot_84_bar__plus_00_dot_27_bar__plus_04_dot_17 DrawerType)
        (receptacleType Dresser_bar__minus_02_dot_56_bar__plus_00_dot_00_bar__plus_04_dot_30 DresserType)
        (receptacleType GarbageCan_bar__minus_04_dot_12_bar__plus_00_dot_00_bar__minus_00_dot_16 GarbageCanType)
        (receptacleType Drawer_bar__minus_00_dot_35_bar__plus_00_dot_59_bar__plus_04_dot_23 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_35_bar__plus_00_dot_59_bar__plus_03_dot_79 DrawerType)
        (receptacleType Drawer_bar__minus_02_dot_29_bar__plus_00_dot_27_bar__plus_04_dot_17 DrawerType)
        (receptacleType Sofa_bar__minus_02_dot_53_bar__plus_00_dot_02_bar__plus_00_dot_26 SofaType)
        (receptacleType Drawer_bar__minus_00_dot_35_bar__plus_00_dot_21_bar__plus_04_dot_23 DrawerType)
        (receptacleType CoffeeTable_bar__minus_06_dot_72_bar__plus_00_dot_01_bar__plus_00_dot_00 CoffeeTableType)
        (receptacleType ArmChair_bar__minus_04_dot_39_bar__plus_00_dot_01_bar__plus_01_dot_40 ArmChairType)
        (receptacleType Dresser_bar__minus_00_dot_24_bar__plus_00_dot_01_bar__plus_04_dot_01 DresserType)
        (receptacleType Drawer_bar__minus_02_dot_29_bar__plus_00_dot_76_bar__plus_04_dot_17 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_35_bar__plus_00_dot_21_bar__plus_03_dot_79 DrawerType)
        (receptacleType Drawer_bar__minus_02_dot_84_bar__plus_00_dot_75_bar__plus_04_dot_17 DrawerType)
        (receptacleType Ottoman_bar__minus_01_dot_77_bar__plus_00_dot_01_bar__plus_01_dot_81 OttomanType)
        (receptacleType CoffeeTable_bar__minus_04_dot_29_bar__plus_00_dot_01_bar__plus_00_dot_41 CoffeeTableType)
        (objectType RemoteControl_bar__minus_04_dot_03_bar__plus_00_dot_44_bar__plus_01_dot_40 RemoteControlType)
        (objectType LightSwitch_bar__minus_07_dot_00_bar__plus_01_dot_36_bar__plus_02_dot_61 LightSwitchType)
        (objectType KeyChain_bar__minus_01_dot_46_bar__plus_00_dot_46_bar__plus_01_dot_93 KeyChainType)
        (objectType CreditCard_bar__minus_01_dot_46_bar__plus_00_dot_45_bar__plus_01_dot_51 CreditCardType)
        (objectType HousePlant_bar__minus_00_dot_29_bar__plus_00_dot_39_bar__plus_02_dot_11 HousePlantType)
        (objectType WateringCan_bar__minus_00_dot_27_bar__plus_00_dot_01_bar__plus_02_dot_37 WateringCanType)
        (objectType Box_bar__minus_01_dot_70_bar__plus_00_dot_31_bar__plus_04_dot_30 BoxType)
        (objectType RemoteControl_bar__minus_02_dot_35_bar__plus_00_dot_12_bar__plus_04_dot_15 RemoteControlType)
        (objectType Painting_bar__minus_03_dot_71_bar__plus_01_dot_72_bar__plus_04_dot_60 PaintingType)
        (objectType Window_bar__minus_00_dot_04_bar__plus_01_dot_71_bar__plus_02_dot_10 WindowType)
        (objectType Statue_bar__minus_02_dot_12_bar__plus_01_dot_11_bar__plus_04_dot_15 StatueType)
        (objectType Television_bar__minus_02_dot_53_bar__plus_01_dot_58_bar__plus_04_dot_40 TelevisionType)
        (objectType Curtains_bar__minus_01_dot_15_bar__plus_01_dot_38_bar__plus_04_dot_53 CurtainsType)
        (objectType Window_bar__minus_01_dot_16_bar__plus_01_dot_70_bar__plus_04_dot_59 WindowType)
        (objectType Painting_bar__plus_00_dot_00_bar__plus_01_dot_58_bar__plus_04_dot_01 PaintingType)
        (objectType Curtains_bar__minus_00_dot_09_bar__plus_02_dot_46_bar__plus_02_dot_11 CurtainsType)
        (objectType FloorLamp_bar__minus_00_dot_23_bar__plus_00_dot_00_bar__minus_00_dot_13 FloorLampType)
        (objectType Laptop_bar__minus_01_dot_77_bar__plus_00_dot_45_bar__plus_02_dot_03 LaptopType)
        (objectType Laptop_bar__minus_02_dot_53_bar__plus_00_dot_50_bar__plus_00_dot_30 LaptopType)
        (objectType DeskLamp_bar__minus_06_dot_77_bar__plus_00_dot_41_bar__minus_00_dot_03 DeskLampType)
        (objectType RemoteControl_bar__minus_00_dot_37_bar__plus_00_dot_86_bar__plus_03_dot_90 RemoteControlType)
        (objectType RemoteControl_bar__minus_03_dot_00_bar__plus_01_dot_10_bar__plus_04_dot_30 RemoteControlType)
        (objectType Pillow_bar__minus_01_dot_83_bar__plus_00_dot_60_bar__plus_00_dot_22 PillowType)
        (objectType Chair_bar__minus_06_dot_02_bar__plus_00_dot_01_bar__plus_00_dot_09 ChairType)
        (objectType Watch_bar__minus_00_dot_33_bar__plus_00_dot_47_bar__plus_04_dot_09 WatchType)
        (objectType Newspaper_bar__minus_02_dot_71_bar__plus_01_dot_10_bar__plus_04_dot_23 NewspaperType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DresserType WatchType)
        (canContain DresserType NewspaperType)
        (canContain DresserType BoxType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType LaptopType)
        (canContain DresserType RemoteControlType)
        (canContain DresserType StatueType)
        (canContain DresserType WateringCanType)
        (canContain GarbageCanType NewspaperType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain CoffeeTableType WatchType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain CoffeeTableType WateringCanType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain DresserType WatchType)
        (canContain DresserType NewspaperType)
        (canContain DresserType BoxType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType LaptopType)
        (canContain DresserType RemoteControlType)
        (canContain DresserType StatueType)
        (canContain DresserType WateringCanType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain OttomanType BoxType)
        (canContain OttomanType LaptopType)
        (canContain OttomanType PillowType)
        (canContain OttomanType RemoteControlType)
        (canContain OttomanType KeyChainType)
        (canContain OttomanType NewspaperType)
        (canContain OttomanType CreditCardType)
        (canContain CoffeeTableType WatchType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain CoffeeTableType WateringCanType)
        (pickupable RemoteControl_bar__minus_04_dot_03_bar__plus_00_dot_44_bar__plus_01_dot_40)
        (pickupable KeyChain_bar__minus_01_dot_46_bar__plus_00_dot_46_bar__plus_01_dot_93)
        (pickupable CreditCard_bar__minus_01_dot_46_bar__plus_00_dot_45_bar__plus_01_dot_51)
        (pickupable WateringCan_bar__minus_00_dot_27_bar__plus_00_dot_01_bar__plus_02_dot_37)
        (pickupable Box_bar__minus_01_dot_70_bar__plus_00_dot_31_bar__plus_04_dot_30)
        (pickupable RemoteControl_bar__minus_02_dot_35_bar__plus_00_dot_12_bar__plus_04_dot_15)
        (pickupable Statue_bar__minus_02_dot_12_bar__plus_01_dot_11_bar__plus_04_dot_15)
        (pickupable Laptop_bar__minus_01_dot_77_bar__plus_00_dot_45_bar__plus_02_dot_03)
        (pickupable Laptop_bar__minus_02_dot_53_bar__plus_00_dot_50_bar__plus_00_dot_30)
        (pickupable RemoteControl_bar__minus_00_dot_37_bar__plus_00_dot_86_bar__plus_03_dot_90)
        (pickupable RemoteControl_bar__minus_03_dot_00_bar__plus_01_dot_10_bar__plus_04_dot_30)
        (pickupable Pillow_bar__minus_01_dot_83_bar__plus_00_dot_60_bar__plus_00_dot_22)
        (pickupable Watch_bar__minus_00_dot_33_bar__plus_00_dot_47_bar__plus_04_dot_09)
        (pickupable Newspaper_bar__minus_02_dot_71_bar__plus_01_dot_10_bar__plus_04_dot_23)
        (isReceptacleObject Box_bar__minus_01_dot_70_bar__plus_00_dot_31_bar__plus_04_dot_30)
        (openable Drawer_bar__minus_02_dot_84_bar__plus_00_dot_27_bar__plus_04_dot_17)
        (openable Drawer_bar__minus_00_dot_35_bar__plus_00_dot_59_bar__plus_04_dot_23)
        (openable Drawer_bar__minus_00_dot_35_bar__plus_00_dot_59_bar__plus_03_dot_79)
        (openable Drawer_bar__minus_02_dot_29_bar__plus_00_dot_27_bar__plus_04_dot_17)
        (openable Drawer_bar__minus_00_dot_35_bar__plus_00_dot_21_bar__plus_04_dot_23)
        (openable Drawer_bar__minus_02_dot_29_bar__plus_00_dot_76_bar__plus_04_dot_17)
        (openable Drawer_bar__minus_00_dot_35_bar__plus_00_dot_21_bar__plus_03_dot_79)
        (openable Drawer_bar__minus_02_dot_84_bar__plus_00_dot_75_bar__plus_04_dot_17)
        
        (atLocation agent1 loc_bar__minus_12_bar_5_bar_0_bar_30)
        
        
        
        
        
        
        
        (toggleable FloorLamp_bar__minus_00_dot_23_bar__plus_00_dot_00_bar__minus_00_dot_13)
        (toggleable DeskLamp_bar__minus_06_dot_77_bar__plus_00_dot_41_bar__minus_00_dot_03)
        
        
        
        
        (inReceptacle Watch_bar__minus_00_dot_33_bar__plus_00_dot_47_bar__plus_04_dot_09 Drawer_bar__minus_00_dot_35_bar__plus_00_dot_59_bar__plus_04_dot_23)
        (inReceptacle DeskLamp_bar__minus_06_dot_77_bar__plus_00_dot_41_bar__minus_00_dot_03 CoffeeTable_bar__minus_06_dot_72_bar__plus_00_dot_01_bar__plus_00_dot_00)
        (inReceptacle CreditCard_bar__minus_01_dot_46_bar__plus_00_dot_45_bar__plus_01_dot_51 Ottoman_bar__minus_01_dot_77_bar__plus_00_dot_01_bar__plus_01_dot_81)
        (inReceptacle Laptop_bar__minus_01_dot_77_bar__plus_00_dot_45_bar__plus_02_dot_03 Ottoman_bar__minus_01_dot_77_bar__plus_00_dot_01_bar__plus_01_dot_81)
        (inReceptacle KeyChain_bar__minus_01_dot_46_bar__plus_00_dot_46_bar__plus_01_dot_93 Ottoman_bar__minus_01_dot_77_bar__plus_00_dot_01_bar__plus_01_dot_81)
        (inReceptacle RemoteControl_bar__minus_00_dot_37_bar__plus_00_dot_86_bar__plus_03_dot_90 Dresser_bar__minus_00_dot_24_bar__plus_00_dot_01_bar__plus_04_dot_01)
        (inReceptacle RemoteControl_bar__minus_02_dot_35_bar__plus_00_dot_12_bar__plus_04_dot_15 Drawer_bar__minus_02_dot_29_bar__plus_00_dot_27_bar__plus_04_dot_17)
        (inReceptacle Statue_bar__minus_02_dot_12_bar__plus_01_dot_11_bar__plus_04_dot_15 Dresser_bar__minus_02_dot_56_bar__plus_00_dot_00_bar__plus_04_dot_30)
        (inReceptacle Newspaper_bar__minus_02_dot_71_bar__plus_01_dot_10_bar__plus_04_dot_23 Dresser_bar__minus_02_dot_56_bar__plus_00_dot_00_bar__plus_04_dot_30)
        (inReceptacle Television_bar__minus_02_dot_53_bar__plus_01_dot_58_bar__plus_04_dot_40 Dresser_bar__minus_02_dot_56_bar__plus_00_dot_00_bar__plus_04_dot_30)
        (inReceptacle RemoteControl_bar__minus_03_dot_00_bar__plus_01_dot_10_bar__plus_04_dot_30 Dresser_bar__minus_02_dot_56_bar__plus_00_dot_00_bar__plus_04_dot_30)
        (inReceptacle Laptop_bar__minus_02_dot_53_bar__plus_00_dot_50_bar__plus_00_dot_30 Sofa_bar__minus_02_dot_53_bar__plus_00_dot_02_bar__plus_00_dot_26)
        (inReceptacle Pillow_bar__minus_01_dot_83_bar__plus_00_dot_60_bar__plus_00_dot_22 Sofa_bar__minus_02_dot_53_bar__plus_00_dot_02_bar__plus_00_dot_26)
        (inReceptacle RemoteControl_bar__minus_04_dot_03_bar__plus_00_dot_44_bar__plus_01_dot_40 ArmChair_bar__minus_04_dot_39_bar__plus_00_dot_01_bar__plus_01_dot_40)
        
        
        (receptacleAtLocation ArmChair_bar__minus_04_dot_39_bar__plus_00_dot_01_bar__plus_01_dot_40 loc_bar__minus_13_bar_5_bar_3_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_04_dot_29_bar__plus_00_dot_01_bar__plus_00_dot_41 loc_bar__minus_21_bar_3_bar_1_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_06_dot_72_bar__plus_00_dot_01_bar__plus_00_dot_00 loc_bar__minus_25_bar_4_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_35_bar__plus_00_dot_21_bar__plus_03_dot_79 loc_bar__minus_6_bar_13_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_35_bar__plus_00_dot_21_bar__plus_04_dot_23 loc_bar__minus_6_bar_15_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_35_bar__plus_00_dot_59_bar__plus_03_dot_79 loc_bar__minus_5_bar_14_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_35_bar__plus_00_dot_59_bar__plus_04_dot_23 loc_bar__minus_5_bar_15_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_29_bar__plus_00_dot_27_bar__plus_04_dot_17 loc_bar__minus_13_bar_15_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_02_dot_29_bar__plus_00_dot_76_bar__plus_04_dot_17 loc_bar__minus_12_bar_15_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_84_bar__plus_00_dot_27_bar__plus_04_dot_17 loc_bar__minus_16_bar_15_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_84_bar__plus_00_dot_75_bar__plus_04_dot_17 loc_bar__minus_14_bar_15_bar_1_bar_60)
        (receptacleAtLocation Dresser_bar__minus_00_dot_24_bar__plus_00_dot_01_bar__plus_04_dot_01 loc_bar__minus_4_bar_16_bar_1_bar_60)
        (receptacleAtLocation Dresser_bar__minus_02_dot_56_bar__plus_00_dot_00_bar__plus_04_dot_30 loc_bar__minus_11_bar_15_bar_0_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_04_dot_12_bar__plus_00_dot_00_bar__minus_00_dot_16 loc_bar__minus_19_bar_1_bar_1_bar_60)
        (receptacleAtLocation Ottoman_bar__minus_01_dot_77_bar__plus_00_dot_01_bar__plus_01_dot_81 loc_bar__minus_12_bar_6_bar_1_bar_60)
        (receptacleAtLocation Sofa_bar__minus_02_dot_53_bar__plus_00_dot_02_bar__plus_00_dot_26 loc_bar__minus_11_bar_7_bar_2_bar_45)
        (objectAtLocation RemoteControl_bar__minus_02_dot_35_bar__plus_00_dot_12_bar__plus_04_dot_15 loc_bar__minus_13_bar_15_bar_1_bar_60)
        (objectAtLocation Laptop_bar__minus_02_dot_53_bar__plus_00_dot_50_bar__plus_00_dot_30 loc_bar__minus_11_bar_7_bar_2_bar_45)
        (objectAtLocation RemoteControl_bar__minus_04_dot_03_bar__plus_00_dot_44_bar__plus_01_dot_40 loc_bar__minus_13_bar_5_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__minus_00_dot_37_bar__plus_00_dot_86_bar__plus_03_dot_90 loc_bar__minus_4_bar_16_bar_1_bar_60)
        (objectAtLocation Curtains_bar__minus_00_dot_09_bar__plus_02_dot_46_bar__plus_02_dot_11 loc_bar__minus_3_bar_9_bar_1_bar__minus_30)
        (objectAtLocation Curtains_bar__minus_01_dot_15_bar__plus_01_dot_38_bar__plus_04_dot_53 loc_bar__minus_5_bar_16_bar_0_bar_30)
        (objectAtLocation Chair_bar__minus_06_dot_02_bar__plus_00_dot_01_bar__plus_00_dot_09 loc_bar__minus_25_bar_2_bar_2_bar_60)
        (objectAtLocation Box_bar__minus_01_dot_70_bar__plus_00_dot_31_bar__plus_04_dot_30 loc_bar__minus_7_bar_15_bar_0_bar_60)
        (objectAtLocation LightSwitch_bar__minus_07_dot_00_bar__plus_01_dot_36_bar__plus_02_dot_61 loc_bar__minus_26_bar_10_bar_3_bar_30)
        (objectAtLocation Watch_bar__minus_00_dot_33_bar__plus_00_dot_47_bar__plus_04_dot_09 loc_bar__minus_5_bar_15_bar_1_bar_45)
        (objectAtLocation Pillow_bar__minus_01_dot_83_bar__plus_00_dot_60_bar__plus_00_dot_22 loc_bar__minus_11_bar_7_bar_2_bar_45)
        (objectAtLocation Television_bar__minus_02_dot_53_bar__plus_01_dot_58_bar__plus_04_dot_40 loc_bar__minus_11_bar_15_bar_0_bar_60)
        (objectAtLocation WateringCan_bar__minus_00_dot_27_bar__plus_00_dot_01_bar__plus_02_dot_37 loc_bar__minus_3_bar_9_bar_1_bar_60)
        (objectAtLocation Newspaper_bar__minus_02_dot_71_bar__plus_01_dot_10_bar__plus_04_dot_23 loc_bar__minus_11_bar_15_bar_0_bar_60)
        (objectAtLocation KeyChain_bar__minus_01_dot_46_bar__plus_00_dot_46_bar__plus_01_dot_93 loc_bar__minus_12_bar_6_bar_1_bar_60)
        (objectAtLocation FloorLamp_bar__minus_00_dot_23_bar__plus_00_dot_00_bar__minus_00_dot_13 loc_bar__minus_2_bar_2_bar_2_bar_60)
        (objectAtLocation Painting_bar__plus_00_dot_00_bar__plus_01_dot_58_bar__plus_04_dot_01 loc_bar__minus_3_bar_16_bar_1_bar_0)
        (objectAtLocation Laptop_bar__minus_01_dot_77_bar__plus_00_dot_45_bar__plus_02_dot_03 loc_bar__minus_12_bar_6_bar_1_bar_60)
        (objectAtLocation RemoteControl_bar__minus_03_dot_00_bar__plus_01_dot_10_bar__plus_04_dot_30 loc_bar__minus_11_bar_15_bar_0_bar_60)
        (objectAtLocation DeskLamp_bar__minus_06_dot_77_bar__plus_00_dot_41_bar__minus_00_dot_03 loc_bar__minus_25_bar_4_bar_2_bar_60)
        (objectAtLocation Painting_bar__minus_03_dot_71_bar__plus_01_dot_72_bar__plus_04_dot_60 loc_bar__minus_15_bar_16_bar_0_bar_0)
        (objectAtLocation CreditCard_bar__minus_01_dot_46_bar__plus_00_dot_45_bar__plus_01_dot_51 loc_bar__minus_12_bar_6_bar_1_bar_60)
        (objectAtLocation Window_bar__minus_00_dot_04_bar__plus_01_dot_71_bar__plus_02_dot_10 loc_bar__minus_3_bar_9_bar_1_bar_0)
        (objectAtLocation Window_bar__minus_01_dot_16_bar__plus_01_dot_70_bar__plus_04_dot_59 loc_bar__minus_5_bar_16_bar_0_bar_0)
        (objectAtLocation HousePlant_bar__minus_00_dot_29_bar__plus_00_dot_39_bar__plus_02_dot_11 loc_bar__minus_3_bar_9_bar_1_bar_60)
        (objectAtLocation Statue_bar__minus_02_dot_12_bar__plus_01_dot_11_bar__plus_04_dot_15 loc_bar__minus_11_bar_15_bar_0_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 RemoteControlType)
                                    (receptacleType ?r OttomanType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 RemoteControlType)
                                            (receptacleType ?r OttomanType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            