[EXP] 0: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 1: [success_rate]: 1, [progress_rate]: 0.7666666666666667, [grounding_acc]: 1.0, [score_state]: [(22, 0.7666666666666667), (22, 1.0)]
[EXP] 2: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 3: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 4: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 5: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 6: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 7: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 8: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 9: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 10: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 11: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 12: [success_rate]: 0, [progress_rate]: 0.9666666666666667, [grounding_acc]: 1.0, [score_state]: [(4, 0.16666666666666666), (7, 0.26666666666666666), (11, 0.4), (14, 0.5), (17, 0.6), (20, 0.7), (22, 0.7666666666666667), (24, 0.8333333333333334), (26, 0.9), (28, 0.9666666666666667)]
[EXP] 13: [success_rate]: 1, [progress_rate]: 0.16666666666666666, [grounding_acc]: 1.0, [score_state]: [(4, 0.16666666666666666), (4, 1.0)]
[EXP] 14: [success_rate]: 1, [progress_rate]: 0.26666666666666666, [grounding_acc]: 1.0, [score_state]: [(4, 0.16666666666666666), (7, 0.26666666666666666), (7, 1.0)]
[EXP] 15: [success_rate]: 0, [progress_rate]: 0.7666666666666667, [grounding_acc]: 1.0, [score_state]: [(4, 0.16666666666666666), (8, 0.3), (11, 0.4), (14, 0.5), (16, 0.5666666666666667), (18, 0.6333333333333333), (20, 0.7), (22, 0.7666666666666667)]
[EXP] 16: [success_rate]: 1, [progress_rate]: 0.4, [grounding_acc]: 1.0, [score_state]: [(4, 0.16666666666666666), (8, 0.3), (11, 0.4), (11, 1.0)]
[EXP] 17: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 18: [success_rate]: 1, [progress_rate]: 0.4, [grounding_acc]: 1.0, [score_state]: [(4, 0.16666666666666666), (8, 0.3), (11, 0.4), (11, 1.0)]
[EXP] 19: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 20: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 21: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 22: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 23: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 24: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 25: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 26: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 27: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 28: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 29: [success_rate]: 0, [progress_rate]: 0.9666666666666667, [grounding_acc]: 1.0, [score_state]: [(8, 0.3), (10, 0.36666666666666664), (12, 0.43333333333333335), (13, 0.4666666666666667), (15, 0.5333333333333333), (17, 0.6), (19, 0.6666666666666666), (21, 0.7333333333333333), (23, 0.8), (28, 0.9666666666666667)]
[EXP] 30: [success_rate]: 0, [progress_rate]: 0.5, [grounding_acc]: 1.0, [score_state]: [(6, 0.23333333333333334), (8, 0.3), (10, 0.36666666666666664), (14, 0.5)]
[EXP] 31: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: [(6, 0.23333333333333334), (8, 0.3), (15, 0.5333333333333333), (22, 0.7666666666666667), (29, 1.0)]
[EXP] 32: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: [(5, 0.2), (11, 0.4), (17, 0.6), (23, 0.8), (29, 1.0)]
[EXP] 33: [success_rate]: 0, [progress_rate]: 0.9666666666666667, [grounding_acc]: 1.0, [score_state]: [(6, 0.23333333333333334), (14, 0.5), (21, 0.7333333333333333), (28, 0.9666666666666667)]
[EXP] 34: [success_rate]: 0, [progress_rate]: 0.9666666666666667, [grounding_acc]: 1.0, [score_state]: [(6, 0.23333333333333334), (14, 0.5), (21, 0.7333333333333333), (28, 0.9666666666666667)]
[EXP] 35: [success_rate]: 1, [progress_rate]: 0.5, [grounding_acc]: 1.0, [score_state]: [(5, 0.2), (14, 0.5), (14, 1.0)]
[EXP] 36: [success_rate]: 1, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: [(5, 0.2), (13, 0.4666666666666667), (17, 0.6), (23, 0.8), (29, 1.0)]
[EXP] 37: [success_rate]: 0, [progress_rate]: 0.8666666666666667, [grounding_acc]: 1.0, [score_state]: [(13, 0.4666666666666667), (16, 0.5666666666666667), (19, 0.6666666666666666), (21, 0.7333333333333333), (25, 0.8666666666666667)]
[EXP] 38: [success_rate]: 0, [progress_rate]: 0.8333333333333334, [grounding_acc]: 1.0, [score_state]: [(15, 0.5333333333333333), (24, 0.8333333333333334)]
[EXP] 39: [success_rate]: 0, [progress_rate]: 0.9333333333333333, [grounding_acc]: 1.0, [score_state]: [(5, 0.2), (8, 0.3), (17, 0.6), (19, 0.6666666666666666), (22, 0.7666666666666667), (25, 0.8666666666666667), (27, 0.9333333333333333)]
[EXP] 40: [success_rate]: 0, [progress_rate]: 0.9, [grounding_acc]: 1.0, [score_state]: [(8, 0.3), (10, 0.36666666666666664), (13, 0.4666666666666667), (15, 0.5333333333333333), (17, 0.6), (23, 0.8), (26, 0.9)]
[EXP] 41: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: [(8, 0.3), (12, 0.43333333333333335), (17, 0.6), (23, 0.8), (29, 1.0)]
[EXP] 42: [success_rate]: 0, [progress_rate]: 0.9666666666666667, [grounding_acc]: 1.0, [score_state]: [(5, 0.2), (7, 0.26666666666666666), (16, 0.5666666666666667), (18, 0.6333333333333333), (21, 0.7333333333333333), (23, 0.8), (26, 0.9), (28, 0.9666666666666667)]
[EXP] 43: [success_rate]: 0, [progress_rate]: 0.7333333333333333, [grounding_acc]: 1.0, [score_state]: [(9, 0.3333333333333333), (11, 0.4), (14, 0.5), (16, 0.5666666666666667), (18, 0.6333333333333333), (21, 0.7333333333333333)]
[EXP] 44: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 45: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: [(5, 0.2), (8, 0.3), (17, 0.6), (20, 0.7), (22, 0.7666666666666667), (24, 0.8333333333333334), (27, 0.9333333333333333), (29, 1.0)]
[EXP] 46: [success_rate]: 0, [progress_rate]: 0.9666666666666667, [grounding_acc]: 1.0, [score_state]: [(8, 0.3), (13, 0.4666666666666667), (16, 0.5666666666666667), (25, 0.8666666666666667), (28, 0.9666666666666667)]
[EXP] 47: [success_rate]: 0, [progress_rate]: 0.9, [grounding_acc]: 1.0, [score_state]: [(8, 0.3), (14, 0.5), (20, 0.7), (26, 0.9)]
[EXP] 48: [success_rate]: 1, [progress_rate]: 0.9666666666666667, [grounding_acc]: 1.0, [score_state]: [(10, 0.36666666666666664), (20, 0.7), (28, 0.9666666666666667), (28, 1.0)]
[EXP] 49: [success_rate]: 0, [progress_rate]: 0.9666666666666667, [grounding_acc]: 1.0, [score_state]: [(20, 0.7), (23, 0.8), (28, 0.9666666666666667)]
[EXP] 50: [success_rate]: 1, [progress_rate]: 0.4666666666666667, [grounding_acc]: 1.0, [score_state]: [(13, 0.4666666666666667), (13, 1.0)]
[EXP] 51: [success_rate]: 0, [progress_rate]: 0.5333333333333333, [grounding_acc]: 1.0, [score_state]: [(15, 0.5333333333333333)]
[EXP] 52: [success_rate]: 0, [progress_rate]: 0.7, [grounding_acc]: 1.0, [score_state]: [(17, 0.6), (20, 0.7)]
[EXP] 53: [success_rate]: 0, [progress_rate]: 0.8, [grounding_acc]: 1.0, [score_state]: [(16, 0.5666666666666667), (20, 0.7), (23, 0.8)]
[EXP] 54: [success_rate]: 0, [progress_rate]: 0.8666666666666667, [grounding_acc]: 1.0, [score_state]: [(18, 0.6333333333333333), (22, 0.7666666666666667), (25, 0.8666666666666667)]
[EXP] 55: [success_rate]: 1, [progress_rate]: 0.43333333333333335, [grounding_acc]: 1.0, [score_state]: [(11, 0.4), (12, 0.43333333333333335), (12, 1.0)]
[EXP] 56: [success_rate]: 0, [progress_rate]: 0.5666666666666667, [grounding_acc]: 1.0, [score_state]: [(9, 0.3333333333333333), (16, 0.5666666666666667)]
[EXP] 57: [success_rate]: 0, [progress_rate]: 1.0, [grounding_acc]: 1.0, [score_state]: []
[EXP] 58: [success_rate]: 0, [progress_rate]: 0.8666666666666667, [grounding_acc]: 1.0, [score_state]: [(14, 0.5), (25, 0.8666666666666667)]
[EXP] 59: [success_rate]: 0, [progress_rate]: 0.8666666666666667, [grounding_acc]: 1.0, [score_state]: [(16, 0.5666666666666667), (25, 0.8666666666666667)]
