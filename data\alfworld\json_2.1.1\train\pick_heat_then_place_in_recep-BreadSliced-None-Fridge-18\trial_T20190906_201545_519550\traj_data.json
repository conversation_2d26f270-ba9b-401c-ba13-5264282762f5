{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 54}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000289.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000290.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000291.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 55}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000330.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000331.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000332.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000333.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000335.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000336.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000337.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000338.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000339.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000340.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000341.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000342.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000343.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000344.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000345.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000346.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000347.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000348.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000349.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000350.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000351.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000352.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000353.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000354.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000355.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000356.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000357.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000358.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000359.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000360.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000361.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000362.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000363.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000364.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000365.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000366.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000367.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000368.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000369.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000370.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000371.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000372.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000373.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000374.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000375.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000376.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000377.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000378.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000379.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000380.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000381.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000382.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000383.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000384.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000385.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000386.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000387.png", "low_idx": 69}, {"high_idx": 9, "image_name": "000000388.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000389.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000390.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000391.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000392.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000393.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000394.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000395.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000396.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000397.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000398.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000399.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000400.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000401.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000402.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000403.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000404.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000405.png", "low_idx": 70}, {"high_idx": 9, "image_name": "000000406.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000407.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000408.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000409.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000410.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000411.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000412.png", "low_idx": 72}, {"high_idx": 9, "image_name": "000000413.png", "low_idx": 72}, {"high_idx": 9, "image_name": "000000414.png", "low_idx": 72}, {"high_idx": 9, "image_name": "000000415.png", "low_idx": 72}, {"high_idx": 9, "image_name": "000000416.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000417.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000418.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000419.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000420.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000421.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000422.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000423.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000424.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000425.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000426.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000427.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000428.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000429.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000430.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000431.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000432.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000433.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000434.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000435.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000436.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000437.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000438.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000439.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000440.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000441.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000442.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000443.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000444.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000445.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000446.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000447.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000448.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000449.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000450.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000451.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000452.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000453.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000454.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000455.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000456.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000457.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000458.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000459.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000460.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000461.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000462.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000463.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000464.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000465.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000466.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000467.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000468.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000469.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000470.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000471.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000472.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000473.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000474.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000475.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000476.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000477.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000478.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000479.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000480.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000481.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000482.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000483.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000484.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000485.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000486.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000487.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000488.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000489.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000490.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000491.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000492.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000493.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000494.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000495.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000496.png", "low_idx": 92}, {"high_idx": 10, "image_name": "000000497.png", "low_idx": 92}, {"high_idx": 10, "image_name": "000000498.png", "low_idx": 93}, {"high_idx": 10, "image_name": "000000499.png", "low_idx": 93}, {"high_idx": 10, "image_name": "000000500.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000501.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000502.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000503.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000504.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000505.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000506.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000507.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000508.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000509.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000510.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000511.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000512.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000513.png", "low_idx": 96}, {"high_idx": 10, "image_name": "000000514.png", "low_idx": 96}, {"high_idx": 10, "image_name": "000000515.png", "low_idx": 97}, {"high_idx": 10, "image_name": "000000516.png", "low_idx": 97}, {"high_idx": 10, "image_name": "000000517.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000518.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000519.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000520.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000521.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000522.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000523.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000524.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000525.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000526.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000527.png", "low_idx": 98}, {"high_idx": 11, "image_name": "000000528.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000529.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000530.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000531.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000532.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000533.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000534.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000535.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000536.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000537.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000538.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000539.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000540.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000541.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000542.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000543.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000544.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000545.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000546.png", "low_idx": 100}, {"high_idx": 11, "image_name": "000000547.png", "low_idx": 101}, {"high_idx": 11, "image_name": "000000548.png", "low_idx": 101}, {"high_idx": 11, "image_name": "000000549.png", "low_idx": 101}, {"high_idx": 11, "image_name": "000000550.png", "low_idx": 101}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|3|10|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [5.5492, 5.5492, 10.1014738, 10.1014738, 3.6829376, 3.6829376]], "coordinateReceptacleObjectId": ["SinkBasin", [5.5492, 5.5492, 9.356, 9.356, 3.5209168, 3.5209168]], "forceVisible": true, "objectId": "Knife|+01.39|+00.92|+02.53"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-3|4|2|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-2.7483636, -2.7483636, 1.214434864, 1.214434864, 4.24923944, 4.24923944]], "forceVisible": true, "objectId": "Bread|-00.69|+01.06|+00.30"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|1|6|2|30"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "microwave"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [5.5492, 5.5492, 10.1014738, 10.1014738, 3.6829376, 3.6829376]], "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Knife|+01.39|+00.92|+02.53", "receptacleObjectId": "Microwave|+00.11|+00.98|+00.26"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-3|4|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-2.7483636, -2.7483636, 1.214434864, 1.214434864, 4.24923944, 4.24923944]], "coordinateReceptacleObjectId": ["CounterTop", [5.564, 5.564, 3.588, 3.588, 4.1128, 4.1128]], "forceVisible": true, "objectId": "Bread|-00.69|+01.06|+00.30|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|1|6|2|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|4|22|1|30"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "fridge"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-2.7483636, -2.7483636, 1.214434864, 1.214434864, 4.24923944, 4.24923944]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [8.14, 8.14, 22.504, 22.504, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-00.69|+01.06|+00.30|BreadSliced_1", "receptacleObjectId": "<PERSON><PERSON>|+02.04|+00.00|+05.63"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|+01.39|+00.92|+02.53"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [87, 152, 180, 160], "mask": [[45411, 69], [45693, 88], [45988, 93], [46287, 56], [46347, 34], [46591, 51], [46667, 1], [46672, 8], [46897, 44], [46975, 2], [47206, 35], [47520, 20], [47837, 3]], "point": [133, 155]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-00.69|+01.06|+00.30"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [105, 76, 152, 159], "mask": [[22629, 10], [22924, 17], [23221, 22], [23520, 25], [23819, 27], [24117, 30], [24416, 32], [24715, 34], [25015, 34], [25314, 36], [25613, 37], [25913, 38], [26213, 38], [26512, 39], [26812, 40], [27112, 40], [27412, 40], [27711, 41], [28011, 41], [28311, 42], [28611, 42], [28911, 42], [29210, 43], [29510, 43], [29810, 43], [30110, 43], [30410, 42], [30710, 42], [31009, 43], [31309, 43], [31609, 43], [31909, 43], [32208, 45], [32508, 44], [32808, 44], [33108, 44], [33408, 44], [33707, 45], [34007, 46], [34307, 46], [34607, 46], [34907, 46], [35207, 46], [35506, 47], [35806, 47], [36106, 47], [36406, 47], [36706, 47], [37006, 47], [37306, 47], [37605, 48], [37905, 48], [38205, 48], [38505, 48], [38805, 48], [39105, 48], [39405, 48], [39705, 47], [40005, 47], [40305, 47], [40605, 47], [40905, 47], [41206, 46], [41506, 46], [41806, 46], [42106, 46], [42406, 46], [42706, 46], [43006, 46], [43306, 46], [43607, 45], [43907, 45], [44207, 45], [44507, 45], [44807, 45], [45107, 45], [45407, 45], [45707, 45], [46008, 44], [46308, 44], [46608, 43], [46909, 42], [47212, 37], [47520, 20], [47543, 2]], "point": [128, 116]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|+01.39|+00.92|+02.53", "placeStationary": true, "receptacleObjectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 171], [26788, 172], [27087, 173], [27387, 173], [27686, 173], [27985, 174], [28285, 174], [28584, 175], [28883, 176], [29183, 175], [29482, 176], [29781, 177], [30081, 177], [30380, 177], [30679, 178], [30978, 179], [31278, 179], [31577, 180], [31876, 180], [32176, 180], [32475, 181], [32775, 181], [33075, 180], [33375, 180], [33675, 180], [33975, 180], [34275, 180], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 171], [26788, 172], [27087, 173], [27387, 173], [27686, 173], [27985, 174], [28285, 174], [28584, 175], [28883, 176], [29183, 175], [29482, 176], [29781, 177], [30081, 177], [30380, 177], [30679, 178], [30978, 179], [31278, 179], [31577, 180], [31876, 180], [32176, 115], [32305, 51], [32475, 68], [32606, 50], [32775, 63], [32907, 49], [33075, 61], [33179, 2], [33207, 48], [33375, 63], [33478, 9], [33506, 49], [33675, 67], [33777, 25], [33804, 51], [33975, 73], [34076, 79], [34275, 81], [34376, 79], [34576, 91], [34676, 78], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 5}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.69|+01.06|+00.30|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [110, 88, 151, 107], "mask": [[26229, 1], [26525, 9], [26822, 16], [27120, 21], [27418, 25], [27716, 28], [28015, 13], [28031, 14], [28314, 7], [28339, 8], [28613, 6], [28641, 7], [28912, 5], [28943, 6], [29212, 3], [29244, 5], [29511, 3], [29545, 5], [29811, 2], [29847, 3], [30110, 2], [30148, 3], [30410, 1], [30449, 2], [30710, 1], [30749, 3], [31050, 2], [31351, 1], [31651, 1], [31951, 1]], "point": [131, 93]}}, "high_idx": 7}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.69|+01.06|+00.30|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 171], [26788, 172], [27087, 173], [27387, 173], [27686, 173], [27985, 174], [28285, 174], [28584, 175], [28883, 176], [29183, 175], [29482, 176], [29781, 177], [30081, 177], [30380, 177], [30679, 178], [30978, 179], [31278, 179], [31577, 180], [31876, 180], [32176, 115], [32305, 51], [32475, 68], [32606, 50], [32775, 63], [32907, 49], [33075, 61], [33179, 2], [33207, 48], [33375, 63], [33478, 9], [33506, 49], [33675, 67], [33777, 25], [33804, 51], [33975, 73], [34076, 79], [34275, 81], [34376, 79], [34576, 91], [34676, 78], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 48], [24447, 115], [24693, 47], [24751, 111], [24992, 46], [25053, 108], [25292, 44], [25354, 107], [25591, 44], [25655, 106], [25890, 44], [25956, 105], [26190, 43], [26257, 103], [26489, 43], [26558, 102], [26788, 44], [26858, 102], [27087, 44], [27159, 101], [27387, 44], [27459, 101], [27686, 45], [27760, 99], [27985, 45], [28060, 99], [28285, 45], [28360, 99], [28584, 46], [28660, 99], [28883, 47], [28960, 99], [29183, 47], [29260, 98], [29482, 48], [29560, 98], [29781, 49], [29860, 98], [30081, 49], [30160, 98], [30380, 50], [30460, 97], [30679, 51], [30760, 97], [30978, 52], [31060, 97], [31278, 52], [31360, 97], [31577, 53], [31660, 97], [31876, 55], [31960, 96], [32176, 55], [32260, 31], [32305, 51], [32475, 56], [32606, 50], [32775, 56], [32907, 49], [33075, 56], [33179, 2], [33207, 48], [33375, 56], [33478, 9], [33506, 49], [33675, 56], [33777, 25], [33804, 51], [33975, 57], [34076, 79], [34275, 63], [34339, 17], [34376, 79], [34576, 91], [34676, 78], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.69|+01.06|+00.30|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [130, 82, 159, 115], "mask": [[24442, 5], [24740, 11], [25038, 15], [25336, 18], [25635, 20], [25934, 22], [26233, 24], [26532, 26], [26832, 26], [27131, 28], [27431, 28], [27731, 29], [28030, 30], [28330, 30], [28630, 30], [28930, 30], [29230, 30], [29530, 30], [29830, 30], [30130, 30], [30430, 30], [30730, 30], [31030, 30], [31330, 30], [31630, 30], [31931, 29], [32231, 29], [32531, 12], [32831, 7], [33131, 5], [33431, 7], [33731, 11], [34032, 16], [34338, 1]], "point": [144, 97]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 171], [26788, 172], [27087, 173], [27387, 173], [27686, 173], [27985, 174], [28285, 174], [28584, 175], [28883, 176], [29183, 175], [29482, 176], [29781, 177], [30081, 177], [30380, 177], [30679, 178], [30978, 179], [31278, 179], [31577, 180], [31876, 180], [32176, 115], [32305, 51], [32475, 68], [32606, 50], [32775, 63], [32907, 49], [33075, 61], [33179, 2], [33207, 48], [33375, 63], [33478, 9], [33506, 49], [33675, 67], [33777, 25], [33804, 51], [33975, 73], [34076, 79], [34275, 81], [34376, 79], [34576, 91], [34676, 78], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+02.04|+00.00|+05.63"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 45599], [45600, 299], [45900, 299], [46200, 298], [46500, 298], [46800, 298], [47100, 297], [47400, 297], [47700, 297], [48000, 296], [48300, 296], [48600, 296], [48900, 295], [49200, 295], [49500, 295], [49800, 294], [50100, 294], [50400, 294], [50700, 293], [51000, 293], [51300, 293], [51600, 292], [51900, 292], [52200, 292], [52500, 291], [52800, 291], [53100, 291], [53400, 290], [53700, 290], [54000, 290], [54300, 289], [54600, 289], [54900, 289], [55200, 288], [55500, 288], [55800, 288], [56100, 286], [56400, 286], [56700, 286], [57000, 285], [57300, 285], [57600, 285], [57900, 284], [58200, 284], [58500, 284], [58800, 284], [59100, 284], [59400, 285], [59700, 286], [60000, 286], [60300, 285], [60600, 285], [60900, 285], [61200, 284], [61500, 284], [61800, 284], [62100, 283], [62400, 283], [62700, 281], [63000, 280], [63300, 280], [63600, 280], [63900, 279], [64200, 279], [64500, 279], [64800, 278], [65100, 137], [65261, 117], [65400, 128], [65566, 112], [65700, 126], [65871, 106], [66000, 124], [66174, 103], [66300, 122], [66476, 101], [66600, 120], [66778, 98], [66900, 118], [67080, 96], [67200, 117], [67382, 94], [67500, 116], [67683, 92], [67800, 114], [67985, 90], [68100, 113], [68286, 89], [68400, 112], [68587, 87], [68700, 111], [68889, 85], [69000, 110], [69190, 84], [69300, 109], [69491, 82], [69600, 108], [69792, 81], [69900, 107], [70092, 81], [70200, 107], [70393, 79], [70500, 106], [70694, 78], [70800, 105], [70995, 77], [71100, 105], [71295, 76], [71400, 104], [71596, 75], [71700, 104], [71896, 75], [72000, 103], [72197, 73], [72300, 103], [72497, 73], [72600, 103], [72797, 73], [72900, 103], [73097, 72], [73200, 102], [73397, 72], [73500, 102], [73698, 71], [73800, 102], [73998, 70], [74100, 102], [74298, 70], [74400, 101], [74598, 70], [74700, 101], [74898, 69], [75000, 101], [75199, 68], [75300, 101], [75499, 68], [75600, 101], [75799, 67], [75900, 100], [76099, 67], [76200, 100], [76400, 66], [76500, 100], [76700, 65], [76800, 100], [77000, 65], [77100, 100], [77300, 65], [77400, 100], [77601, 63], [77700, 100], [77901, 63], [78000, 100], [78201, 63], [78300, 100], [78501, 62], [78600, 100], [78800, 63], [78900, 101], [79100, 63], [79200, 101], [79400, 62], [79500, 101], [79699, 63], [79800, 102], [79999, 63], [80100, 102], [80298, 63], [80400, 102], [80598, 63], [80700, 103], [80898, 63], [81000, 103], [81198, 62], [81300, 103], [81498, 62], [81600, 103], [81798, 62], [81900, 103], [82098, 61], [82200, 103], [82397, 62], [82500, 103], [82697, 62], [82800, 103], [82997, 61], [83100, 103], [83297, 61], [83400, 103], [83597, 61], [83700, 104], [83897, 60], [84000, 104], [84197, 60], [84300, 104], [84497, 60], [84600, 104], [84797, 59], [84900, 104], [85097, 59], [85200, 104], [85396, 60], [85500, 104], [85696, 59], [85800, 105], [85996, 59], [86100, 105], [86296, 59], [86400, 105], [86596, 58], [86700, 105], [86895, 59], [87000, 105], [87195, 59], [87300, 106], [87495, 58], [87600, 106], [87795, 58], [87900, 106], [88094, 59], [88200, 107], [88394, 58], [88500, 107], [88693, 59], [88800, 110], [88990, 62], [89100, 119], [89287, 64], [89400, 251], [89700, 251]], "point": [149, 149]}}, "high_idx": 11}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.69|+01.06|+00.30|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|+02.04|+00.00|+05.63"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 57585], [57586, 299], [57887, 297], [58187, 297], [58488, 296], [58789, 295], [59089, 295], [59390, 295], [59691, 295], [59992, 294], [60292, 293], [60593, 292], [60894, 291], [61194, 290], [61495, 289], [61796, 288], [62097, 286], [62397, 286], [62698, 283], [62999, 281], [63299, 281], [63600, 280], [63900, 279], [64200, 279], [64500, 279], [64800, 278], [65100, 137], [65261, 117], [65400, 128], [65566, 112], [65700, 126], [65871, 106], [66000, 124], [66174, 103], [66300, 122], [66476, 101], [66600, 120], [66778, 98], [66900, 118], [67080, 96], [67200, 117], [67382, 94], [67500, 116], [67683, 92], [67800, 114], [67985, 90], [68100, 113], [68286, 89], [68400, 112], [68587, 87], [68700, 111], [68889, 85], [69000, 110], [69190, 84], [69300, 109], [69491, 82], [69600, 108], [69792, 81], [69900, 107], [70092, 81], [70200, 107], [70393, 79], [70500, 106], [70694, 78], [70800, 105], [70995, 77], [71100, 105], [71295, 76], [71400, 104], [71596, 75], [71700, 104], [71896, 75], [72000, 103], [72197, 73], [72300, 103], [72497, 73], [72600, 103], [72797, 73], [72900, 103], [73097, 72], [73200, 102], [73397, 72], [73500, 102], [73698, 71], [73800, 102], [73998, 70], [74100, 102], [74298, 70], [74400, 101], [74598, 70], [74700, 101], [74898, 69], [75000, 101], [75199, 68], [75300, 101], [75499, 68], [75600, 101], [75799, 67], [75900, 100], [76099, 67], [76200, 100], [76400, 66], [76500, 100], [76700, 65], [76800, 100], [77000, 65], [77100, 100], [77300, 65], [77400, 100], [77601, 63], [77700, 100], [77901, 63], [78000, 100], [78201, 63], [78300, 100], [78501, 62], [78600, 100], [78800, 63], [78900, 101], [79100, 63], [79200, 101], [79400, 62], [79500, 101], [79699, 63], [79800, 102], [79999, 63], [80100, 102], [80298, 63], [80400, 102], [80598, 63], [80700, 103], [80898, 63], [81000, 103], [81198, 62], [81300, 103], [81498, 62], [81600, 103], [81798, 62], [81900, 103], [82098, 61], [82200, 103], [82397, 62], [82500, 103], [82697, 62], [82800, 103], [82997, 61], [83100, 103], [83297, 61], [83400, 103], [83597, 61], [83700, 104], [83897, 60], [84000, 104], [84197, 60], [84300, 104], [84497, 60], [84600, 104], [84797, 59], [84900, 104], [85097, 59], [85200, 104], [85396, 60], [85500, 104], [85696, 59], [85800, 105], [85996, 59], [86100, 105], [86296, 59], [86400, 105], [86596, 58], [86700, 105], [86895, 59], [87000, 105], [87195, 59], [87300, 106], [87495, 58], [87600, 106], [87795, 58], [87900, 106], [88094, 59], [88200, 107], [88394, 58], [88500, 107], [88693, 59], [88800, 110], [88990, 62], [89100, 119], [89287, 64], [89400, 251], [89700, 251]], "point": [149, 149]}}, "high_idx": 11}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+02.04|+00.00|+05.63"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 127], [172, 255], [472, 255], [772, 255], [1072, 255], [1372, 255], [1672, 255], [1972, 255], [2272, 255], [2572, 255], [2872, 255], [3172, 255], [3472, 255], [3772, 255], [4072, 255], [4372, 255], [4672, 256], [4972, 256], [5271, 257], [5571, 257], [5871, 257], [6171, 258], [6470, 261], [6765, 50820], [57586, 299], [57887, 297], [58187, 297], [58488, 296], [58789, 295], [59089, 295], [59390, 295], [59691, 295], [59992, 294], [60292, 293], [60593, 292], [60894, 291], [61194, 290], [61495, 289], [61796, 288], [62097, 286], [62397, 286], [62698, 283], [62999, 281], [63299, 281], [63600, 280], [63900, 279], [64200, 279], [64500, 279], [64800, 278], [65100, 278], [65400, 278], [65700, 277], [66000, 277], [66300, 277], [66600, 276], [66900, 276], [67200, 276], [67500, 275], [67800, 275], [68100, 275], [68400, 274], [68700, 274], [69000, 274], [69300, 273], [69600, 273], [69900, 273], [70200, 272], [70500, 272], [70800, 272], [71100, 271], [71400, 271], [71700, 271], [72000, 270], [72300, 270], [72600, 270], [72900, 269], [73200, 269], [73500, 269], [73800, 268], [74100, 268], [74400, 268], [74700, 267], [75000, 267], [75300, 267], [75600, 266], [75900, 266], [76200, 266], [76500, 265], [76800, 265], [77100, 265], [77400, 264], [77700, 264], [78000, 264], [78300, 263], [78600, 263], [78900, 263], [79200, 262], [79500, 262], [79800, 262], [80100, 261], [80400, 261], [80700, 261], [81000, 260], [81300, 260], [81600, 260], [81900, 259], [82200, 259], [82500, 259], [82800, 258], [83100, 258], [83400, 258], [83700, 257], [84000, 257], [84300, 257], [84600, 256], [84900, 256], [85200, 256], [85500, 255], [85800, 255], [86100, 255], [86400, 254], [86700, 254], [87000, 254], [87300, 253], [87600, 253], [87900, 253], [88200, 252], [88500, 252], [88800, 252], [89100, 251], [89400, 251], [89700, 251]], "point": [149, 149]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan18", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 0.5, "y": 0.900999844, "z": 5.0}, "object_poses": [{"objectName": "Pan_08687688", "position": {"x": 1.559894, "y": 0.988599956, "z": 4.2942}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": -0.0037561655, "y": 0.7949763, "z": 6.479039}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": 1.27674, "y": 0.925922036, "z": 2.52536845}, "rotation": {"x": -1.40334183e-14, "y": 180.0, "z": 2.25533157e-21}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": -0.31275335, "y": 0.7596526, "z": 6.354039}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 1.5404948, "y": 2.20666957, "z": 4.1315074}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": -0.5494328, "y": 0.7724465, "z": 0.507678}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": 1.58258653, "y": 0.9927422, "z": 0.4407184}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": -0.4138595, "y": 0.145185351, "z": 0.277464747}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": 0.668040454, "y": 0.773551643, "z": 0.619083345}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": -0.4667135, "y": 0.7612178, "z": 6.60979748}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": 0.9990971, "y": 1.0077, "z": 0.2120077}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": -0.346677959, "y": 0.810111, "z": 6.478116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 0.167910486, "y": 0.7606825, "z": 6.151951}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": 1.3873, "y": 0.9207344, "z": 2.52536845}, "rotation": {"x": 2.27491e-21, "y": 89.99999, "z": 1.40334183e-14}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": -0.284177959, "y": 0.7601505, "z": 6.941612}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 1.238817, "y": 0.140689611, "z": 1.86424649}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 0.0283220261, "y": 0.7550883, "z": 6.01462}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 0.8476807, "y": 0.9877, "z": 0.120407164}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 0.3052411, "y": 0.7550883, "z": 6.666539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 0.0157748461, "y": 0.7550884, "z": 6.74238}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": 1.38469982, "y": 1.043094, "z": 0.8474003}, "rotation": {"x": 0.0, "y": 270.000122, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": 1.22514915, "y": 0.9877001, "z": 1.3502}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 1.49786007, "y": 0.965567231, "z": 2.47406888}, "rotation": {"x": 2.27491e-21, "y": 89.99999, "z": 1.40334183e-14}}, {"objectName": "Apple_a939ee5b", "position": {"x": -0.51090765, "y": 0.8346215, "z": 6.56560326}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Bowl_9f9e1c57", "position": {"x": 1.54393137, "y": 1.57657087, "z": 1.28809607}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b7525b79", "position": {"x": -0.6870909, "y": 1.06230986, "z": 0.303608716}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": 2.154373, "y": 0.130739719, "z": 6.498835}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": 0.923388839, "y": 1.02240038, "z": 0.303608239}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": 1.49322724, "y": 0.993248641, "z": 1.3502}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": -0.224883735, "y": 0.8346215, "z": 6.63313341}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": 1.32, "y": 0.988599956, "z": 3.848}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 1.40633225, "y": 0.141221583, "z": 3.217805}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": -0.09667797, "y": 0.7550883, "z": 6.787113}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": 1.49322653, "y": 1.0811137, "z": 3.41787148}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 1.58568, "y": 2.20716763, "z": 4.21915627}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": 1.34822512, "y": 0.786821842, "z": 3.29175758}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_bd2376ff", "position": {"x": -0.346677959, "y": 0.8643737, "z": 6.1691184}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_73d89a5b", "position": {"x": 1.317894, "y": 0.988599956, "z": 4.2942}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": -0.0037561655, "y": 0.7949763, "z": 6.291539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": -0.487572253, "y": 0.760130465, "z": 6.807433}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 0.08082789, "y": 0.7550884, "z": 6.89582157}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Pan_08687688", "position": {"x": 1.40386784, "y": 0.9908586, "z": 3.13762426}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": 0.300493121, "y": 0.80955404, "z": 6.2845335}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 0.440992653, "y": 1.57915688, "z": 0.207324728}, "rotation": {"x": 0.0, "y": 0.0, "z": -1.70754731e-06}}, {"objectName": "Cup_8e8a26e1", "position": {"x": 0.544847965, "y": 0.9888445, "z": 0.2120077}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": 0.192699984, "y": 0.773074746, "z": 0.5631321}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": 1.412682, "y": 1.572533, "z": 1.47104967}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}], "object_toggles": [], "random_seed": 279504264, "scene_num": 18}, "task_id": "trial_T20190906_201545_519550", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1RLO9LNUJIW5S_382M9COHEKW4QFJMUJJMTNN0NTZEUD", "high_descs": ["Turn around and walk to the sink and then turn to face the left side of the sink.", "Pick up the yellow handled knife in the left sink.", "Turn around and walk a few steps so when you turn you are looking at the right side of a white counter then walk to the counter.", "Cut the loaf of bread into slices.", "Now walk to the microwave that's to the left of the counter you're facing.", "Put the knife on the plate in the microwave and close the door.", "Go back to the loaf of bread you cut up.", "Pick up a slice of bread from the center of the loaf.", "Walk back to the microwave.", "Cook the bread in the microwave then take it out and close the door.", "Turn around and walk to the fridge that's across the room and stand and face it.", "Put the slice of bread on the first shelf towards the right side of the shelf."], "task_desc": "Put a piece of microwaved bread in the fridge.", "votes": [1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3NPFYT4IZFLURX985JKXDX7GUUFXGV", "high_descs": ["turn around and walk forwards until you reach the kitchen sink on the left, turn left to face the sink", "grab the knife out of the left side sink", "turn around and walk forwards a bit, then turn left and walk to the counter at the end of the room", "slice up the bread on the countertop there", "move left a bit to face the microwave", "place the knife inside of the microwave", "move right a bit to face the loaf of bread on the counter", "pick up a slice of the bread off of the counter", "move to the left again a bit, and turn to face the microwave", "place the bread inside of the microwave, turn on the microwave, and pull the bread back out", "turn around and walk to the kitchen table at the end of the room, then turn left and walk to the fridge", "place the bread inside of the fridge"], "task_desc": "place microwaved bread inside of the fridge", "votes": [1, 1]}, {"assignment_id": "A3HL2LL0LEPZT8_3S3AMIZX3XM3CFOMCXN2TZJ9GESCD3", "high_descs": ["Turn around, go to the sink on the left.", "Take the knife from the left sink basin.", "Go to the end of the counter to the right near the white trash bag.", "Cut the bread on the counter into slices.", "Go to the microwave to the left.", "Put the knife in the microwave.", "Go to the end of the counter to the right.", "Take a slice of bread from the counter.", "Go to the microwave to the left.", "Put the bread in the microwave, turn on the microwave, remove the bread", "Turn left, go to the refrigerator on the right by the round table.", "Put the bread in the right shelf of the refrigerator."], "task_desc": "Put a hot slice of bread in the refrigerator.", "votes": [1, 1]}]}}