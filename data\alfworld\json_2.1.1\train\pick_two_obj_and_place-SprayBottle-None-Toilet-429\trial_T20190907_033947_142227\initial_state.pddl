
(define (problem plan_trial_T20190907_033947_142227)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Bathtub_bar__minus_02_dot_12_bar__minus_00_dot_09_bar__minus_03_dot_12 - object
        Candle_bar__plus_00_dot_80_bar__plus_00_dot_82_bar__minus_01_dot_87 - object
        Candle_bar__minus_01_dot_86_bar__plus_00_dot_82_bar__minus_00_dot_17 - object
        Cloth_bar__minus_01_dot_64_bar__plus_00_dot_04_bar__minus_02_dot_97 - object
        Faucet_bar__minus_01_dot_91_bar__plus_00_dot_67_bar__minus_03_dot_86 - object
        Faucet_bar__minus_02_dot_43_bar__plus_00_dot_98_bar__minus_00_dot_66 - object
        HandTowel_bar__minus_02_dot_17_bar__plus_01_dot_50_bar__minus_00_dot_04 - object
        LightSwitch_bar__plus_00_dot_32_bar__plus_01_dot_33_bar__plus_00_dot_00 - object
        Mirror_bar__minus_02_dot_50_bar__plus_01_dot_42_bar__minus_00_dot_70 - object
        Plunger_bar__plus_00_dot_85_bar_00_dot_00_bar__minus_03_dot_91 - object
        ScrubBrush_bar__plus_00_dot_68_bar_00_dot_00_bar__minus_03_dot_91 - object
        ShowerCurtain_bar__minus_01_dot_32_bar__plus_01_dot_78_bar__minus_03_dot_86 - object
        Sink_bar__minus_02_dot_17_bar__plus_00_dot_30_bar__minus_00_dot_66 - object
        SoapBar_bar__plus_00_dot_80_bar__plus_00_dot_82_bar__minus_02_dot_08 - object
        SoapBar_bar__minus_01_dot_97_bar__plus_00_dot_04_bar__minus_02_dot_11 - object
        SoapBottle_bar__plus_00_dot_78_bar__plus_00_dot_82_bar__minus_00_dot_52 - object
        SoapBottle_bar__plus_00_dot_78_bar__plus_00_dot_82_bar__minus_00_dot_72 - object
        SoapBottle_bar__minus_01_dot_45_bar__plus_00_dot_82_bar__minus_00_dot_14 - object
        SprayBottle_bar__minus_01_dot_76_bar__plus_00_dot_82_bar__minus_00_dot_11 - object
        SprayBottle_bar__minus_01_dot_86_bar__plus_00_dot_82_bar__minus_00_dot_22 - object
        TissueBox_bar__plus_00_dot_83_bar__plus_00_dot_81_bar__minus_00_dot_36 - object
        TissueBox_bar__plus_00_dot_85_bar__plus_00_dot_82_bar__minus_01_dot_77 - object
        ToiletPaper_bar__plus_00_dot_41_bar__plus_00_dot_73_bar__minus_03_dot_89 - object
        ToiletPaper_bar__minus_00_dot_56_bar__plus_00_dot_08_bar__minus_03_dot_78 - object
        Towel_bar__minus_01_dot_53_bar__plus_01_dot_69_bar__minus_00_dot_11 - object
        Window_bar__minus_00_dot_54_bar__plus_01_dot_63_bar__minus_04_dot_00 - object
        Bathtub_bar__minus_02_dot_12_bar__minus_00_dot_09_bar__minus_03_dot_12_bar_BathtubBasin - receptacle
        GarbageCan_bar__minus_00_dot_51_bar_00_dot_00_bar__minus_03_dot_76 - receptacle
        HandTowelHolder_bar__minus_02_dot_17_bar__plus_01_dot_60_bar__plus_00_dot_00 - receptacle
        Shelf_bar__plus_00_dot_81_bar__plus_00_dot_81_bar__minus_00_dot_61 - receptacle
        Shelf_bar__plus_00_dot_82_bar__plus_00_dot_81_bar__minus_01_dot_76 - receptacle
        Shelf_bar__minus_01_dot_57_bar__plus_00_dot_81_bar__minus_00_dot_14 - receptacle
        Sink_bar__minus_02_dot_17_bar__plus_00_dot_30_bar__minus_00_dot_66_bar_SinkBasin - receptacle
        ToiletPaperHanger_bar__plus_00_dot_46_bar__plus_00_dot_84_bar__minus_03_dot_98 - receptacle
        Toilet_bar__plus_00_dot_00_bar_00_dot_00_bar__minus_03_dot_44 - receptacle
        TowelHolder_bar__minus_01_dot_53_bar__plus_01_dot_67_bar__plus_00_dot_00 - receptacle
        loc_bar__minus_5_bar__minus_3_bar_0_bar_60 - location
        loc_bar_1_bar__minus_2_bar_0_bar_30 - location
        loc_bar_1_bar__minus_7_bar_1_bar_60 - location
        loc_bar_1_bar__minus_4_bar_1_bar_60 - location
        loc_bar_1_bar__minus_11_bar_2_bar_60 - location
        loc_bar__minus_2_bar__minus_13_bar_2_bar_0 - location
        loc_bar__minus_4_bar__minus_12_bar_3_bar_60 - location
        loc_bar__minus_6_bar__minus_3_bar_0_bar_0 - location
        loc_bar__minus_5_bar__minus_4_bar_2_bar_45 - location
        loc_bar__minus_6_bar__minus_3_bar_3_bar_60 - location
        loc_bar__minus_2_bar__minus_13_bar_2_bar_60 - location
        loc_bar_1_bar__minus_11_bar_2_bar_30 - location
        loc_bar__minus_6_bar__minus_3_bar_3_bar_45 - location
        loc_bar__minus_2_bar__minus_13_bar_1_bar_60 - location
        loc_bar__minus_3_bar__minus_7_bar_1_bar_30 - location
        )
    

(:init


        (receptacleType TowelHolder_bar__minus_01_dot_53_bar__plus_01_dot_67_bar__plus_00_dot_00 TowelHolderType)
        (receptacleType Bathtub_bar__minus_02_dot_12_bar__minus_00_dot_09_bar__minus_03_dot_12_bar_BathtubBasin BathtubBasinType)
        (receptacleType HandTowelHolder_bar__minus_02_dot_17_bar__plus_01_dot_60_bar__plus_00_dot_00 HandTowelHolderType)
        (receptacleType Shelf_bar__plus_00_dot_82_bar__plus_00_dot_81_bar__minus_01_dot_76 ShelfType)
        (receptacleType Sink_bar__minus_02_dot_17_bar__plus_00_dot_30_bar__minus_00_dot_66_bar_SinkBasin SinkBasinType)
        (receptacleType GarbageCan_bar__minus_00_dot_51_bar_00_dot_00_bar__minus_03_dot_76 GarbageCanType)
        (receptacleType ToiletPaperHanger_bar__plus_00_dot_46_bar__plus_00_dot_84_bar__minus_03_dot_98 ToiletPaperHangerType)
        (receptacleType Toilet_bar__plus_00_dot_00_bar_00_dot_00_bar__minus_03_dot_44 ToiletType)
        (receptacleType Shelf_bar__minus_01_dot_57_bar__plus_00_dot_81_bar__minus_00_dot_14 ShelfType)
        (receptacleType Shelf_bar__plus_00_dot_81_bar__plus_00_dot_81_bar__minus_00_dot_61 ShelfType)
        (objectType TissueBox_bar__plus_00_dot_83_bar__plus_00_dot_81_bar__minus_00_dot_36 TissueBoxType)
        (objectType SprayBottle_bar__minus_01_dot_86_bar__plus_00_dot_82_bar__minus_00_dot_22 SprayBottleType)
        (objectType SprayBottle_bar__minus_01_dot_76_bar__plus_00_dot_82_bar__minus_00_dot_11 SprayBottleType)
        (objectType LightSwitch_bar__plus_00_dot_32_bar__plus_01_dot_33_bar__plus_00_dot_00 LightSwitchType)
        (objectType ScrubBrush_bar__plus_00_dot_68_bar_00_dot_00_bar__minus_03_dot_91 ScrubBrushType)
        (objectType Towel_bar__minus_01_dot_53_bar__plus_01_dot_69_bar__minus_00_dot_11 TowelType)
        (objectType Bathtub_bar__minus_02_dot_12_bar__minus_00_dot_09_bar__minus_03_dot_12 BathtubType)
        (objectType SoapBottle_bar__plus_00_dot_78_bar__plus_00_dot_82_bar__minus_00_dot_52 SoapBottleType)
        (objectType SoapBottle_bar__minus_01_dot_45_bar__plus_00_dot_82_bar__minus_00_dot_14 SoapBottleType)
        (objectType ToiletPaper_bar__minus_00_dot_56_bar__plus_00_dot_08_bar__minus_03_dot_78 ToiletPaperType)
        (objectType HandTowel_bar__minus_02_dot_17_bar__plus_01_dot_50_bar__minus_00_dot_04 HandTowelType)
        (objectType SoapBar_bar__minus_01_dot_97_bar__plus_00_dot_04_bar__minus_02_dot_11 SoapBarType)
        (objectType Candle_bar__minus_01_dot_86_bar__plus_00_dot_82_bar__minus_00_dot_17 CandleType)
        (objectType ToiletPaper_bar__plus_00_dot_41_bar__plus_00_dot_73_bar__minus_03_dot_89 ToiletPaperType)
        (objectType Cloth_bar__minus_01_dot_64_bar__plus_00_dot_04_bar__minus_02_dot_97 ClothType)
        (objectType Sink_bar__minus_02_dot_17_bar__plus_00_dot_30_bar__minus_00_dot_66 SinkType)
        (objectType Plunger_bar__plus_00_dot_85_bar_00_dot_00_bar__minus_03_dot_91 PlungerType)
        (objectType Candle_bar__plus_00_dot_80_bar__plus_00_dot_82_bar__minus_01_dot_87 CandleType)
        (objectType Mirror_bar__minus_02_dot_50_bar__plus_01_dot_42_bar__minus_00_dot_70 MirrorType)
        (objectType SoapBottle_bar__plus_00_dot_78_bar__plus_00_dot_82_bar__minus_00_dot_72 SoapBottleType)
        (objectType TissueBox_bar__plus_00_dot_85_bar__plus_00_dot_82_bar__minus_01_dot_77 TissueBoxType)
        (objectType SoapBar_bar__plus_00_dot_80_bar__plus_00_dot_82_bar__minus_02_dot_08 SoapBarType)
        (objectType Window_bar__minus_00_dot_54_bar__plus_01_dot_63_bar__minus_04_dot_00 WindowType)
        (canContain TowelHolderType TowelType)
        (canContain BathtubBasinType ClothType)
        (canContain BathtubBasinType HandTowelType)
        (canContain BathtubBasinType SoapBarType)
        (canContain HandTowelHolderType HandTowelType)
        (canContain ShelfType CandleType)
        (canContain ShelfType SoapBarType)
        (canContain ShelfType SprayBottleType)
        (canContain ShelfType ToiletPaperType)
        (canContain ShelfType SoapBottleType)
        (canContain ShelfType ClothType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType HandTowelType)
        (canContain SinkBasinType SoapBarType)
        (canContain SinkBasinType ClothType)
        (canContain SinkBasinType HandTowelType)
        (canContain GarbageCanType SoapBarType)
        (canContain GarbageCanType SprayBottleType)
        (canContain GarbageCanType ToiletPaperType)
        (canContain GarbageCanType SoapBottleType)
        (canContain GarbageCanType ClothType)
        (canContain GarbageCanType TissueBoxType)
        (canContain GarbageCanType HandTowelType)
        (canContain ToiletPaperHangerType ToiletPaperType)
        (canContain ToiletType SoapBottleType)
        (canContain ToiletType HandTowelType)
        (canContain ToiletType ToiletPaperType)
        (canContain ToiletType ClothType)
        (canContain ToiletType CandleType)
        (canContain ToiletType SoapBarType)
        (canContain ToiletType SprayBottleType)
        (canContain ToiletType TissueBoxType)
        (canContain ShelfType CandleType)
        (canContain ShelfType SoapBarType)
        (canContain ShelfType SprayBottleType)
        (canContain ShelfType ToiletPaperType)
        (canContain ShelfType SoapBottleType)
        (canContain ShelfType ClothType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType HandTowelType)
        (canContain ShelfType CandleType)
        (canContain ShelfType SoapBarType)
        (canContain ShelfType SprayBottleType)
        (canContain ShelfType ToiletPaperType)
        (canContain ShelfType SoapBottleType)
        (canContain ShelfType ClothType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType HandTowelType)
        (pickupable TissueBox_bar__plus_00_dot_83_bar__plus_00_dot_81_bar__minus_00_dot_36)
        (pickupable SprayBottle_bar__minus_01_dot_86_bar__plus_00_dot_82_bar__minus_00_dot_22)
        (pickupable SprayBottle_bar__minus_01_dot_76_bar__plus_00_dot_82_bar__minus_00_dot_11)
        (pickupable ScrubBrush_bar__plus_00_dot_68_bar_00_dot_00_bar__minus_03_dot_91)
        (pickupable Towel_bar__minus_01_dot_53_bar__plus_01_dot_69_bar__minus_00_dot_11)
        (pickupable SoapBottle_bar__plus_00_dot_78_bar__plus_00_dot_82_bar__minus_00_dot_52)
        (pickupable SoapBottle_bar__minus_01_dot_45_bar__plus_00_dot_82_bar__minus_00_dot_14)
        (pickupable ToiletPaper_bar__minus_00_dot_56_bar__plus_00_dot_08_bar__minus_03_dot_78)
        (pickupable HandTowel_bar__minus_02_dot_17_bar__plus_01_dot_50_bar__minus_00_dot_04)
        (pickupable SoapBar_bar__minus_01_dot_97_bar__plus_00_dot_04_bar__minus_02_dot_11)
        (pickupable Candle_bar__minus_01_dot_86_bar__plus_00_dot_82_bar__minus_00_dot_17)
        (pickupable ToiletPaper_bar__plus_00_dot_41_bar__plus_00_dot_73_bar__minus_03_dot_89)
        (pickupable Cloth_bar__minus_01_dot_64_bar__plus_00_dot_04_bar__minus_02_dot_97)
        (pickupable Plunger_bar__plus_00_dot_85_bar_00_dot_00_bar__minus_03_dot_91)
        (pickupable Candle_bar__plus_00_dot_80_bar__plus_00_dot_82_bar__minus_01_dot_87)
        (pickupable SoapBottle_bar__plus_00_dot_78_bar__plus_00_dot_82_bar__minus_00_dot_72)
        (pickupable TissueBox_bar__plus_00_dot_85_bar__plus_00_dot_82_bar__minus_01_dot_77)
        (pickupable SoapBar_bar__plus_00_dot_80_bar__plus_00_dot_82_bar__minus_02_dot_08)
        
        
        
        (atLocation agent1 loc_bar__minus_3_bar__minus_7_bar_1_bar_30)
        
        (cleanable SoapBar_bar__minus_01_dot_97_bar__plus_00_dot_04_bar__minus_02_dot_11)
        (cleanable Cloth_bar__minus_01_dot_64_bar__plus_00_dot_04_bar__minus_02_dot_97)
        (cleanable SoapBar_bar__plus_00_dot_80_bar__plus_00_dot_82_bar__minus_02_dot_08)
        
        
        
        
        
        
        
        
        
        
        (inReceptacle Towel_bar__minus_01_dot_53_bar__plus_01_dot_69_bar__minus_00_dot_11 TowelHolder_bar__minus_01_dot_53_bar__plus_01_dot_67_bar__plus_00_dot_00)
        (inReceptacle TissueBox_bar__plus_00_dot_85_bar__plus_00_dot_82_bar__minus_01_dot_77 Shelf_bar__plus_00_dot_82_bar__plus_00_dot_81_bar__minus_01_dot_76)
        (inReceptacle Candle_bar__plus_00_dot_80_bar__plus_00_dot_82_bar__minus_01_dot_87 Shelf_bar__plus_00_dot_82_bar__plus_00_dot_81_bar__minus_01_dot_76)
        (inReceptacle SoapBar_bar__plus_00_dot_80_bar__plus_00_dot_82_bar__minus_02_dot_08 Shelf_bar__plus_00_dot_82_bar__plus_00_dot_81_bar__minus_01_dot_76)
        (inReceptacle TissueBox_bar__plus_00_dot_83_bar__plus_00_dot_81_bar__minus_00_dot_36 Shelf_bar__plus_00_dot_81_bar__plus_00_dot_81_bar__minus_00_dot_61)
        (inReceptacle SoapBottle_bar__plus_00_dot_78_bar__plus_00_dot_82_bar__minus_00_dot_52 Shelf_bar__plus_00_dot_81_bar__plus_00_dot_81_bar__minus_00_dot_61)
        (inReceptacle SoapBottle_bar__plus_00_dot_78_bar__plus_00_dot_82_bar__minus_00_dot_72 Shelf_bar__plus_00_dot_81_bar__plus_00_dot_81_bar__minus_00_dot_61)
        (inReceptacle Candle_bar__minus_01_dot_86_bar__plus_00_dot_82_bar__minus_00_dot_17 Shelf_bar__minus_01_dot_57_bar__plus_00_dot_81_bar__minus_00_dot_14)
        (inReceptacle SprayBottle_bar__minus_01_dot_76_bar__plus_00_dot_82_bar__minus_00_dot_11 Shelf_bar__minus_01_dot_57_bar__plus_00_dot_81_bar__minus_00_dot_14)
        (inReceptacle SoapBottle_bar__minus_01_dot_45_bar__plus_00_dot_82_bar__minus_00_dot_14 Shelf_bar__minus_01_dot_57_bar__plus_00_dot_81_bar__minus_00_dot_14)
        (inReceptacle SprayBottle_bar__minus_01_dot_86_bar__plus_00_dot_82_bar__minus_00_dot_22 Shelf_bar__minus_01_dot_57_bar__plus_00_dot_81_bar__minus_00_dot_14)
        (inReceptacle HandTowel_bar__minus_02_dot_17_bar__plus_01_dot_50_bar__minus_00_dot_04 HandTowelHolder_bar__minus_02_dot_17_bar__plus_01_dot_60_bar__plus_00_dot_00)
        (inReceptacle Cloth_bar__minus_01_dot_64_bar__plus_00_dot_04_bar__minus_02_dot_97 Bathtub_bar__minus_02_dot_12_bar__minus_00_dot_09_bar__minus_03_dot_12_bar_BathtubBasin)
        (inReceptacle SoapBar_bar__minus_01_dot_97_bar__plus_00_dot_04_bar__minus_02_dot_11 Bathtub_bar__minus_02_dot_12_bar__minus_00_dot_09_bar__minus_03_dot_12_bar_BathtubBasin)
        (inReceptacle ToiletPaper_bar__minus_00_dot_56_bar__plus_00_dot_08_bar__minus_03_dot_78 GarbageCan_bar__minus_00_dot_51_bar_00_dot_00_bar__minus_03_dot_76)
        
        
        (receptacleAtLocation Bathtub_bar__minus_02_dot_12_bar__minus_00_dot_09_bar__minus_03_dot_12_bar_BathtubBasin loc_bar__minus_5_bar__minus_4_bar_2_bar_45)
        (receptacleAtLocation GarbageCan_bar__minus_00_dot_51_bar_00_dot_00_bar__minus_03_dot_76 loc_bar__minus_2_bar__minus_13_bar_2_bar_60)
        (receptacleAtLocation HandTowelHolder_bar__minus_02_dot_17_bar__plus_01_dot_60_bar__plus_00_dot_00 loc_bar__minus_6_bar__minus_3_bar_0_bar_0)
        (receptacleAtLocation Shelf_bar__plus_00_dot_81_bar__plus_00_dot_81_bar__minus_00_dot_61 loc_bar_1_bar__minus_4_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_00_dot_82_bar__plus_00_dot_81_bar__minus_01_dot_76 loc_bar_1_bar__minus_7_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__minus_01_dot_57_bar__plus_00_dot_81_bar__minus_00_dot_14 loc_bar__minus_5_bar__minus_3_bar_0_bar_60)
        (receptacleAtLocation Sink_bar__minus_02_dot_17_bar__plus_00_dot_30_bar__minus_00_dot_66_bar_SinkBasin loc_bar__minus_6_bar__minus_3_bar_3_bar_45)
        (receptacleAtLocation ToiletPaperHanger_bar__plus_00_dot_46_bar__plus_00_dot_84_bar__minus_03_dot_98 loc_bar_1_bar__minus_11_bar_2_bar_30)
        (receptacleAtLocation Toilet_bar__plus_00_dot_00_bar_00_dot_00_bar__minus_03_dot_44 loc_bar__minus_2_bar__minus_13_bar_1_bar_60)
        (receptacleAtLocation TowelHolder_bar__minus_01_dot_53_bar__plus_01_dot_67_bar__plus_00_dot_00 loc_bar__minus_6_bar__minus_3_bar_0_bar_0)
        (objectAtLocation TissueBox_bar__plus_00_dot_83_bar__plus_00_dot_81_bar__minus_00_dot_36 loc_bar_1_bar__minus_4_bar_1_bar_60)
        (objectAtLocation SprayBottle_bar__minus_01_dot_86_bar__plus_00_dot_82_bar__minus_00_dot_22 loc_bar__minus_5_bar__minus_3_bar_0_bar_60)
        (objectAtLocation SoapBar_bar__plus_00_dot_80_bar__plus_00_dot_82_bar__minus_02_dot_08 loc_bar_1_bar__minus_7_bar_1_bar_60)
        (objectAtLocation Candle_bar__plus_00_dot_80_bar__plus_00_dot_82_bar__minus_01_dot_87 loc_bar_1_bar__minus_7_bar_1_bar_60)
        (objectAtLocation SoapBottle_bar__plus_00_dot_78_bar__plus_00_dot_82_bar__minus_00_dot_52 loc_bar_1_bar__minus_4_bar_1_bar_60)
        (objectAtLocation SoapBottle_bar__minus_01_dot_45_bar__plus_00_dot_82_bar__minus_00_dot_14 loc_bar__minus_5_bar__minus_3_bar_0_bar_60)
        (objectAtLocation Sink_bar__minus_02_dot_17_bar__plus_00_dot_30_bar__minus_00_dot_66 loc_bar__minus_6_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Bathtub_bar__minus_02_dot_12_bar__minus_00_dot_09_bar__minus_03_dot_12 loc_bar__minus_4_bar__minus_12_bar_3_bar_60)
        (objectAtLocation Mirror_bar__minus_02_dot_50_bar__plus_01_dot_42_bar__minus_00_dot_70 loc_bar__minus_6_bar__minus_3_bar_3_bar_45)
        (objectAtLocation SoapBottle_bar__plus_00_dot_78_bar__plus_00_dot_82_bar__minus_00_dot_72 loc_bar_1_bar__minus_4_bar_1_bar_60)
        (objectAtLocation Cloth_bar__minus_01_dot_64_bar__plus_00_dot_04_bar__minus_02_dot_97 loc_bar__minus_5_bar__minus_4_bar_2_bar_45)
        (objectAtLocation Candle_bar__minus_01_dot_86_bar__plus_00_dot_82_bar__minus_00_dot_17 loc_bar__minus_5_bar__minus_3_bar_0_bar_60)
        (objectAtLocation HandTowel_bar__minus_02_dot_17_bar__plus_01_dot_50_bar__minus_00_dot_04 loc_bar__minus_6_bar__minus_3_bar_0_bar_0)
        (objectAtLocation Towel_bar__minus_01_dot_53_bar__plus_01_dot_69_bar__minus_00_dot_11 loc_bar__minus_6_bar__minus_3_bar_0_bar_0)
        (objectAtLocation SoapBar_bar__minus_01_dot_97_bar__plus_00_dot_04_bar__minus_02_dot_11 loc_bar__minus_5_bar__minus_4_bar_2_bar_45)
        (objectAtLocation ToiletPaper_bar__minus_00_dot_56_bar__plus_00_dot_08_bar__minus_03_dot_78 loc_bar__minus_2_bar__minus_13_bar_2_bar_60)
        (objectAtLocation Plunger_bar__plus_00_dot_85_bar_00_dot_00_bar__minus_03_dot_91 loc_bar_1_bar__minus_11_bar_2_bar_60)
        (objectAtLocation SprayBottle_bar__minus_01_dot_76_bar__plus_00_dot_82_bar__minus_00_dot_11 loc_bar__minus_5_bar__minus_3_bar_0_bar_60)
        (objectAtLocation TissueBox_bar__plus_00_dot_85_bar__plus_00_dot_82_bar__minus_01_dot_77 loc_bar_1_bar__minus_7_bar_1_bar_60)
        (objectAtLocation ToiletPaper_bar__plus_00_dot_41_bar__plus_00_dot_73_bar__minus_03_dot_89 loc_bar_1_bar__minus_11_bar_2_bar_60)
        (objectAtLocation LightSwitch_bar__plus_00_dot_32_bar__plus_01_dot_33_bar__plus_00_dot_00 loc_bar_1_bar__minus_2_bar_0_bar_30)
        (objectAtLocation Window_bar__minus_00_dot_54_bar__plus_01_dot_63_bar__minus_04_dot_00 loc_bar__minus_2_bar__minus_13_bar_2_bar_0)
        (objectAtLocation ScrubBrush_bar__plus_00_dot_68_bar_00_dot_00_bar__minus_03_dot_91 loc_bar_1_bar__minus_11_bar_2_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 SprayBottleType)
                                    (receptacleType ?r ToiletType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 SprayBottleType)
                                            (receptacleType ?r ToiletType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            