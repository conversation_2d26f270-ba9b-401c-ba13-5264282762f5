{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000100.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000111.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000141.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000143.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000144.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000145.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000148.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000149.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000152.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000153.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000154.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000155.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000158.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000159.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000160.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000197.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000198.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000199.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000200.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000201.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000202.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 8, "image_name": "000000212.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000213.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000216.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000219.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000220.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000221.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000222.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000223.png", "low_idx": 34}, {"high_idx": 8, "image_name": "000000224.png", "low_idx": 34}, {"high_idx": 8, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000229.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000230.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000233.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000240.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000241.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000242.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000243.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000244.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000245.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000246.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000247.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000248.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000249.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000250.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000251.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000252.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000253.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000254.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000255.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000256.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000257.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000258.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000261.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000262.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 42}, {"high_idx": 9, "image_name": "000000268.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000269.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000270.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000271.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000272.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000273.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000274.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000275.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000276.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000277.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000278.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000279.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000280.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000281.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000282.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000283.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000284.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000285.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000286.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000287.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000288.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000289.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000290.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000291.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000292.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000293.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000294.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000295.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000296.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000297.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000298.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000299.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000300.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000301.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000302.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000303.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000304.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000305.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000306.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000307.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000308.png", "low_idx": 46}, {"high_idx": 9, "image_name": "000000309.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000310.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000311.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000312.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000313.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000314.png", "low_idx": 47}, {"high_idx": 9, "image_name": "000000315.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000316.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000317.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000318.png", "low_idx": 48}, {"high_idx": 9, "image_name": "000000319.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000320.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000321.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000322.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000323.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000324.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000325.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000326.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000327.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000328.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000329.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000330.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000331.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000332.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000333.png", "low_idx": 49}, {"high_idx": 9, "image_name": "000000334.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000335.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000336.png", "low_idx": 50}, {"high_idx": 9, "image_name": "000000337.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000338.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000339.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000340.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000341.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000342.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000343.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000344.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000345.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000346.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000347.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000348.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000349.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000350.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000351.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000352.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000353.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000354.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000355.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000356.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000357.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000358.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000359.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000360.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000361.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000362.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000363.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000364.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000365.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000366.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000367.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000368.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000369.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000370.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000371.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000372.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000373.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000374.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000375.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000376.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000377.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000378.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000379.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000380.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000381.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000382.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000383.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000384.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000385.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000386.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000387.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000388.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000389.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000390.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000391.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000392.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000393.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000394.png", "low_idx": 65}, {"high_idx": 10, "image_name": "000000395.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000396.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000397.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000398.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000399.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000400.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000401.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000402.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000403.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000404.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000405.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000406.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000407.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000408.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000409.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000410.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000411.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000412.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000413.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000414.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000415.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000416.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000417.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000418.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000419.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000420.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000421.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000422.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000423.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000424.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000425.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000426.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000427.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000428.png", "low_idx": 73}, {"high_idx": 11, "image_name": "000000429.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000430.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000431.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000432.png", "low_idx": 74}, {"high_idx": 11, "image_name": "000000433.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000434.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000435.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000436.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000437.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000438.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000439.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000440.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000441.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000442.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000443.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000444.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000445.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000446.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000447.png", "low_idx": 75}, {"high_idx": 11, "image_name": "000000448.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000449.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000450.png", "low_idx": 76}, {"high_idx": 11, "image_name": "000000451.png", "low_idx": 76}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|12|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [-0.915936588, -0.915936588, 11.730556, 11.730556, 3.7049564, 3.7049564]], "coordinateReceptacleObjectId": ["DiningTable", [-1.86, -1.86, 12.012, 12.012, 0.024, 0.024]], "forceVisible": true, "objectId": "Knife|-00.23|+00.93|+02.93"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|10|1|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-1.602092, -1.602092, 10.12329864, 10.12329864, 3.8313868, 3.8313868]], "forceVisible": true, "objectId": "Bread|-00.40|+00.96|+02.53"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-8|10|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [-0.915936588, -0.915936588, 11.730556, 11.730556, 3.7049564, 3.7049564]], "coordinateReceptacleObjectId": ["DiningTable", [-11.056, -11.056, 9.424, 9.424, 0.024, 0.024]], "forceVisible": true, "objectId": "Knife|-00.23|+00.93|+02.93", "receptacleObjectId": "DiningTable|-02.76|+00.01|+02.36"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-4|10|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-1.602092, -1.602092, 10.12329864, 10.12329864, 3.8313868, 3.8313868]], "coordinateReceptacleObjectId": ["DiningTable", [-1.86, -1.86, 12.012, 12.012, 0.024, 0.024]], "forceVisible": true, "objectId": "Bread|-00.40|+00.96|+02.53|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-3|5|2|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-1.436, -1.436, 0.896, 0.896, 3.6564356, 3.6564356]], "forceVisible": true, "objectId": "Microwave|-00.36|+00.91|+00.22"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-8|18|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "fridge"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-1.602092, -1.602092, 10.12329864, 10.12329864, 3.8313868, 3.8313868]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-11.208, -11.208, 17.708, 17.708, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-00.40|+00.96|+02.53|BreadSliced_1", "receptacleObjectId": "<PERSON><PERSON>|-02.80|+00.00|+04.43"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|-00.23|+00.93|+02.93"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [169, 115, 185, 174], "mask": [[34372, 3], [34670, 6], [34969, 7], [35269, 7], [35570, 7], [35871, 6], [36172, 5], [36472, 5], [36772, 5], [37072, 6], [37372, 6], [37672, 6], [37972, 6], [38273, 5], [38573, 6], [38873, 6], [39174, 5], [39474, 5], [39774, 6], [40074, 6], [40370, 10], [40669, 11], [40969, 12], [41269, 12], [41569, 12], [41870, 11], [42170, 11], [42470, 12], [42770, 12], [43071, 11], [43371, 11], [43671, 11], [43971, 12], [44272, 11], [44572, 11], [44872, 11], [45173, 10], [45473, 11], [45773, 11], [46074, 10], [46374, 10], [46674, 10], [46974, 11], [47275, 10], [47575, 10], [47876, 9], [48176, 9], [48477, 8], [48777, 9], [49077, 9], [49378, 8], [49679, 7], [49979, 7], [50280, 6], [50580, 6], [50881, 5], [51182, 4], [51483, 3], [51784, 2], [52085, 1]], "point": [177, 143]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-00.40|+00.96|+02.53"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [95, 139, 186, 192], "mask": [[41534, 13], [41828, 25], [42125, 31], [42421, 38], [42716, 46], [43012, 53], [43310, 58], [43608, 62], [43906, 65], [44204, 70], [44503, 72], [44803, 74], [45102, 76], [45401, 78], [45700, 80], [45999, 82], [46298, 84], [46598, 85], [46897, 87], [47197, 88], [47497, 88], [47796, 90], [48096, 90], [48396, 91], [48695, 92], [48995, 92], [49295, 92], [49595, 92], [49895, 92], [50195, 92], [50495, 92], [50795, 92], [51095, 91], [51396, 89], [51696, 88], [51997, 86], [52298, 85], [52599, 84], [52900, 82], [53200, 82], [53501, 80], [53802, 78], [54103, 77], [54405, 73], [54707, 70], [55009, 66], [55311, 63], [55613, 59], [55916, 54], [56218, 49], [56522, 42], [56827, 33], [57132, 24], [57441, 3]], "point": [140, 164]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|-00.23|+00.93|+02.93", "placeStationary": true, "receptacleObjectId": "DiningTable|-02.76|+00.01|+02.36"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 16, 269, 229], "mask": [[4500, 64], [4609, 117], [4800, 65], [4909, 118], [5100, 65], [5209, 118], [5400, 65], [5509, 119], [5700, 65], [5809, 119], [6000, 66], [6109, 119], [6300, 66], [6410, 118], [6600, 67], [6710, 119], [6900, 68], [7010, 119], [7200, 69], [7310, 119], [7500, 71], [7573, 1], [7610, 25], [7636, 93], [7800, 75], [7910, 20], [7940, 90], [8100, 75], [8210, 19], [8242, 88], [8400, 76], [8510, 18], [8543, 87], [8700, 76], [8810, 17], [8844, 86], [9000, 18], [9053, 23], [9110, 16], [9145, 86], [9300, 17], [9353, 24], [9410, 15], [9445, 86], [9600, 17], [9653, 24], [9710, 15], [9746, 85], [9900, 17], [9954, 24], [10009, 16], [10046, 85], [10200, 16], [10256, 22], [10309, 16], [10346, 86], [10500, 16], [10555, 24], [10609, 16], [10646, 86], [10800, 15], [10854, 25], [10909, 16], [10946, 86], [11100, 15], [11154, 26], [11208, 17], [11246, 86], [11400, 15], [11454, 26], [11508, 18], [11546, 87], [11700, 14], [11753, 28], [11807, 19], [11845, 88], [12000, 14], [12053, 29], [12107, 20], [12145, 88], [12300, 14], [12353, 30], [12406, 22], [12444, 89], [12600, 14], [12652, 32], [12705, 25], [12742, 92], [12900, 15], [12952, 33], [13004, 28], [13040, 94], [13200, 15], [13252, 34], [13303, 131], [13500, 16], [13552, 36], [13601, 133], [13811, 21], [13848, 43], [13899, 136], [14112, 20], [14150, 46], [14199, 136], [14413, 19], [14452, 44], [14499, 136], [14713, 19], [14753, 43], [14799, 136], [15014, 19], [15053, 43], [15105, 131], [15314, 19], [15353, 41], [15407, 129], [15615, 18], [15653, 39], [15709, 127], [15916, 18], [15952, 38], [16010, 126], [16216, 18], [16252, 37], [16311, 125], [16517, 17], [16551, 38], [16611, 126], [16817, 17], [16850, 38], [16911, 126], [17118, 17], [17139, 49], [17211, 126], [17418, 70], [17511, 126], [17719, 69], [17811, 127], [18019, 69], [18111, 127], [18320, 68], [18410, 128], [18620, 69], [18709, 129], [18921, 69], [19008, 131], [19222, 27], [19250, 41], [19307, 132], [19522, 26], [19552, 3], [19618, 121], [19823, 24], [19921, 118], [20123, 23], [20223, 117], [20424, 21], [20525, 115], [20724, 20], [20826, 114], [21025, 18], [21127, 113], [21325, 16], [21428, 113], [21626, 14], [21729, 112], [21926, 14], [22030, 111], [22227, 12], [22330, 111], [22528, 12], [22630, 112], [22828, 12], [22930, 112], [23129, 10], [23230, 112], [23429, 9], [23531, 111], [23730, 8], [23831, 112], [24030, 8], [24131, 112], [24331, 7], [24430, 113], [24631, 6], [24730, 113], [24932, 5], [25030, 114], [25232, 5], [25330, 114], [25531, 5], [25630, 114], [25831, 5], [25930, 114], [26131, 5], [26230, 115], [26430, 6], [26530, 115], [26730, 5], [26830, 115], [27030, 5], [27130, 115], [27329, 6], [27430, 116], [27629, 5], [27730, 116], [27929, 5], [28030, 116], [28228, 6], [28330, 47], [28390, 56], [28528, 5], [28630, 37], [28690, 56], [28828, 5], [28930, 30], [28991, 56], [29128, 5], [29230, 20], [29291, 56], [29427, 6], [29530, 4], [29591, 56], [29727, 5], [29830, 3], [29891, 56], [30027, 5], [30129, 4], [30191, 57], [30326, 6], [30429, 2], [30491, 57], [30626, 5], [30790, 58], [30926, 5], [31089, 59], [31225, 6], [31329, 4], [31380, 69], [31525, 5], [31669, 80], [31825, 5], [31960, 89], [32124, 6], [32253, 96], [32424, 5], [32529, 5], [32546, 104], [32724, 4], [32829, 6], [32839, 111], [33024, 3], [33129, 121], [33323, 3], [33429, 121], [33623, 3], [33729, 122], [33923, 3], [34029, 122], [34222, 4], [34329, 122], [34522, 4], [34629, 122], [34822, 5], [34929, 123], [35121, 6], [35229, 123], [35421, 6], [35529, 123], [35700, 26], [35829, 123], [36000, 26], [36128, 125], [36300, 26], [36428, 125], [36600, 26], [36728, 125], [36900, 26], [37028, 125], [37200, 26], [37328, 126], [37500, 26], [37627, 127], [37800, 25], [37927, 127], [38100, 25], [38227, 127], [38400, 25], [38527, 128], [38700, 26], [38826, 129], [39000, 26], [39125, 130], [39300, 27], [39425, 130], [39600, 27], [39725, 131], [39900, 28], [40024, 132], [40200, 29], [40324, 132], [40500, 30], [40624, 132], [40800, 31], [40923, 133], [41100, 33], [41223, 134], [41400, 34], [41522, 135], [41700, 38], [41821, 52], [41880, 77], [42000, 39], [42120, 51], [42181, 76], [42300, 41], [42420, 50], [42482, 76], [42600, 43], [42719, 51], [42783, 75], [42900, 44], [43017, 52], [43083, 75], [43200, 46], [43316, 52], [43383, 75], [43500, 48], [43614, 54], [43684, 75], [43800, 52], [43912, 56], [43984, 75], [44100, 58], [44207, 61], [44284, 75], [44400, 168], [44584, 75], [44700, 168], [44884, 76], [45000, 168], [45184, 76], [45300, 168], [45483, 77], [45600, 168], [45783, 77], [45900, 169], [46082, 79], [46200, 169], [46382, 79], [46500, 170], [46681, 80], [46800, 172], [46979, 82], [47100, 175], [47276, 86], [47400, 262], [47700, 262], [48000, 262], [48300, 263], [48600, 263], [48900, 263], [49200, 263], [49500, 264], [49800, 264], [50100, 264], [50400, 264], [50700, 265], [51000, 265], [51300, 265], [51600, 265], [51900, 266], [52200, 266], [52500, 266], [52800, 266], [53100, 266], [53400, 267], [53700, 267], [54000, 267], [54300, 267], [54600, 268], [54900, 268], [55200, 268], [55500, 268], [55800, 269], [56100, 269], [56400, 269], [56700, 269], [57000, 270], [57300, 270], [57600, 269], [57900, 268], [58211, 11], [58394, 7], [58512, 11], [58694, 7], [58813, 11], [58993, 7], [59114, 11], [59293, 7], [59416, 10], [59592, 7], [59717, 11], [59892, 7], [60018, 11], [60191, 7], [60320, 10], [60491, 7], [60621, 10], [60791, 6], [60922, 10], [61090, 7], [61224, 10], [61390, 6], [61525, 10], [61689, 7], [61826, 10], [61989, 6], [62128, 9], [62289, 6], [62429, 10], [62588, 6], [62730, 10], [62888, 6], [63032, 9], [63187, 6], [63333, 9], [63487, 6], [63634, 9], [63786, 6], [63936, 9], [64086, 6], [64237, 9], [64386, 5], [64538, 9], [64685, 6], [64840, 8], [64985, 5], [65141, 9], [65284, 6], [65442, 9], [65584, 5], [65744, 8], [65884, 5], [66045, 8], [66183, 5], [66346, 8], [66483, 5], [66648, 8], [66782, 6], [66949, 9], [67081, 6], [67250, 9], [67381, 6], [67552, 7], [67681, 5], [67852, 8], [67980, 6], [68153, 6], [68281, 5], [68455, 3], [68581, 4]], "point": [134, 121]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.40|+00.96|+02.53|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [141, 139, 150, 191], "mask": [[41541, 8], [41841, 8], [42141, 8], [42441, 8], [42741, 8], [43041, 8], [43341, 9], [43641, 9], [43941, 9], [44241, 9], [44541, 9], [44841, 9], [45141, 9], [45441, 9], [45741, 9], [46041, 9], [46341, 9], [46641, 9], [46941, 9], [47241, 9], [47541, 9], [47841, 9], [48141, 9], [48441, 9], [48742, 8], [49042, 8], [49342, 8], [49642, 8], [49942, 9], [50242, 9], [50542, 9], [50842, 9], [51142, 9], [51442, 9], [51742, 9], [52042, 9], [52342, 9], [52642, 9], [52942, 9], [53242, 9], [53542, 9], [53842, 9], [54142, 9], [54442, 9], [54742, 9], [55042, 9], [55342, 9], [55642, 9], [55942, 9], [56242, 9], [56542, 9], [56842, 9], [57142, 9]], "point": [145, 164]}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.40|+00.96|+02.53|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 20], [36028, 104], [36300, 17], [36330, 102], [36600, 16], [36631, 101], [36900, 15], [36932, 100], [37200, 14], [37233, 99], [37500, 14], [37534, 98], [37800, 13], [37834, 98], [38100, 13], [38134, 98], [38400, 13], [38434, 98], [38700, 13], [38734, 98], [39000, 13], [39034, 99], [39300, 13], [39334, 99], [39600, 14], [39634, 99], [39900, 14], [39934, 99], [40200, 15], [40234, 99], [40500, 15], [40534, 99], [40800, 16], [40833, 100], [41100, 17], [41133, 100], [41400, 19], [41432, 101], [41700, 20], [41732, 101], [42000, 22], [42031, 102], [42300, 23], [42331, 102], [42600, 25], [42629, 104], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 20], [36028, 104], [36300, 17], [36330, 102], [36600, 16], [36631, 101], [36900, 15], [36932, 100], [37200, 14], [37233, 99], [37500, 14], [37534, 98], [37800, 13], [37834, 98], [38100, 13], [38134, 98], [38400, 13], [38434, 98], [38700, 13], [38734, 98], [39000, 13], [39034, 99], [39300, 13], [39334, 99], [39600, 14], [39634, 99], [39900, 14], [39934, 13], [39961, 72], [40200, 15], [40234, 10], [40263, 70], [40500, 15], [40534, 8], [40565, 68], [40800, 16], [40833, 8], [40866, 67], [41100, 17], [41133, 7], [41168, 65], [41400, 19], [41432, 8], [41471, 62], [41700, 20], [41732, 2], [41735, 2], [41777, 56], [42000, 22], [42031, 1], [42078, 55], [42300, 23], [42379, 54], [42600, 25], [42680, 53], [42900, 28], [42981, 52], [43200, 28], [43281, 52], [43500, 27], [43582, 51], [43800, 27], [43882, 51], [44100, 27], [44182, 51], [44400, 26], [44482, 51], [44700, 26], [44782, 51], [45000, 27], [45081, 51], [45300, 27], [45380, 52], [45600, 29], [45678, 54], [45900, 31], [45972, 60], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-1.436, -1.436, 0.896, 0.896, 3.6564356, 3.6564356]], "forceVisible": true, "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-1.436, -1.436, 0.896, 0.896, 3.6564356, 3.6564356]], "forceVisible": true, "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 132], [36300, 132], [36600, 132], [36900, 132], [37200, 132], [37500, 132], [37800, 132], [38100, 132], [38400, 132], [38700, 132], [39000, 133], [39300, 133], [39600, 133], [39900, 133], [40200, 133], [40500, 133], [40800, 133], [41100, 133], [41400, 133], [41700, 133], [42000, 133], [42300, 133], [42600, 133], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.40|+00.96|+02.53|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [26, 134, 81, 154], "mask": [[39947, 14], [40244, 19], [40542, 23], [40841, 25], [41140, 28], [41440, 31], [41734, 1], [41737, 40], [42032, 46], [42330, 49], [42629, 51], [42928, 53], [43228, 53], [43527, 55], [43827, 55], [44127, 55], [44426, 56], [44726, 56], [45027, 54], [45327, 53], [45629, 49], [45931, 41]], "point": [53, 143]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.36|+00.91|+00.22"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 69, 132, 183], "mask": [[20400, 130], [20700, 130], [21000, 130], [21300, 130], [21600, 130], [21900, 130], [22200, 130], [22500, 130], [22800, 130], [23100, 130], [23400, 130], [23700, 130], [24000, 131], [24300, 131], [24600, 131], [24900, 131], [25200, 131], [25500, 131], [25800, 131], [26100, 131], [26400, 131], [26700, 131], [27000, 131], [27300, 131], [27600, 131], [27900, 131], [28200, 131], [28500, 131], [28800, 131], [29100, 131], [29400, 131], [29700, 131], [30000, 131], [30300, 131], [30600, 131], [30900, 132], [31200, 132], [31500, 132], [31800, 132], [32100, 132], [32400, 132], [32700, 132], [33000, 132], [33300, 132], [33600, 132], [33900, 132], [34200, 132], [34500, 132], [34800, 132], [35100, 132], [35400, 132], [35700, 132], [36000, 20], [36028, 104], [36300, 17], [36330, 102], [36600, 16], [36631, 101], [36900, 15], [36932, 100], [37200, 14], [37233, 99], [37500, 14], [37534, 98], [37800, 13], [37834, 98], [38100, 13], [38134, 98], [38400, 13], [38434, 98], [38700, 13], [38734, 98], [39000, 13], [39034, 99], [39300, 13], [39334, 99], [39600, 14], [39634, 99], [39900, 14], [39934, 99], [40200, 15], [40234, 99], [40500, 15], [40534, 99], [40800, 16], [40833, 100], [41100, 17], [41133, 100], [41400, 19], [41432, 101], [41700, 20], [41732, 101], [42000, 22], [42031, 102], [42300, 23], [42331, 102], [42600, 25], [42629, 104], [42900, 133], [43200, 133], [43500, 133], [43800, 133], [44100, 133], [44400, 133], [44700, 133], [45000, 132], [45300, 132], [45600, 132], [45900, 132], [46200, 132], [46500, 132], [46800, 132], [47100, 132], [47400, 132], [47700, 131], [48000, 131], [48300, 131], [48600, 131], [48900, 131], [49200, 131], [49500, 131], [49800, 131], [50100, 131], [50400, 130], [50700, 130], [51000, 130], [51300, 130], [51600, 130], [51900, 130], [52200, 130], [52500, 130], [52800, 129], [53100, 129], [53400, 129], [53700, 129], [54000, 129], [54300, 129], [54600, 129]], "point": [66, 125]}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.80|+00.00|+04.43"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 220], "mask": [[0, 26399], [26400, 299], [26700, 298], [27000, 298], [27300, 297], [27600, 297], [27900, 296], [28200, 295], [28500, 295], [28800, 293], [29100, 293], [29400, 292], [29700, 292], [30000, 291], [30300, 290], [30600, 290], [30900, 286], [31200, 284], [31500, 283], [31800, 282], [32100, 282], [32400, 281], [32700, 280], [33000, 280], [33300, 279], [33600, 278], [33900, 278], [34200, 277], [34500, 276], [34800, 276], [35100, 275], [35400, 274], [35700, 273], [36000, 273], [36300, 272], [36600, 271], [36900, 271], [37200, 270], [37500, 269], [37800, 269], [38100, 268], [38400, 267], [38700, 267], [39000, 266], [39300, 265], [39600, 265], [39900, 264], [40200, 263], [40500, 263], [40800, 262], [41100, 261], [41400, 260], [41700, 260], [42001, 258], [42302, 256], [42603, 255], [42904, 253], [43204, 252], [43505, 251], [43806, 249], [44107, 247], [44408, 246], [44709, 244], [45010, 242], [45311, 241], [45612, 239], [45913, 237], [46214, 236], [46515, 234], [46816, 232], [47117, 230], [47418, 229], [47719, 227], [48019, 226], [48320, 225], [48621, 223], [48922, 221], [49223, 220], [49524, 218], [49825, 216], [50126, 215], [50427, 213], [50728, 211], [51029, 210], [51330, 208], [51631, 206], [51932, 205], [52233, 203], [52534, 201], [52834, 200], [53135, 199], [53436, 197], [53737, 195], [54038, 194], [54339, 192], [54640, 190], [54941, 189], [55242, 187], [55543, 185], [55844, 184], [56145, 182], [56446, 180], [56747, 179], [57048, 177], [57349, 175], [57649, 175], [57950, 173], [58251, 171], [58552, 169], [58853, 168], [59154, 166], [59455, 164], [59756, 163], [60057, 161], [60358, 159], [60659, 158], [60960, 156], [61261, 154], [61562, 153], [61862, 153], [62163, 152], [62463, 151], [62763, 151], [63063, 150], [63364, 149], [63666, 145], [63970, 136], [64274, 128], [64579, 118], [64885, 106], [65192, 92], [65501, 73], [65812, 52]], "point": [149, 109]}}, "high_idx": 11}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.40|+00.96|+02.53|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.80|+00.00|+04.43"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 14699], [14700, 298], [15000, 298], [15300, 297], [15600, 297], [15900, 296], [16200, 295], [16500, 295], [16800, 294], [17100, 294], [17400, 293], [17700, 292], [18000, 292], [18300, 291], [18600, 291], [18900, 290], [19200, 289], [19500, 289], [19800, 288], [20100, 288], [20400, 287], [20700, 286], [21000, 286], [21300, 285], [21600, 285], [21900, 284], [22200, 284], [22500, 283], [22800, 282], [23100, 282], [23400, 281], [23700, 281], [24000, 280], [24300, 279], [24600, 279], [24900, 278], [25200, 278], [25500, 277], [25800, 276], [26100, 276], [26400, 275], [26700, 275], [27000, 274], [27300, 273], [27600, 273], [27900, 272], [28200, 272], [28500, 271], [28800, 271], [29100, 270], [29400, 269], [29700, 269], [30000, 268], [30300, 268], [30600, 267], [30900, 266], [31200, 266], [31500, 265], [31800, 265], [32100, 264], [32400, 263], [32700, 263], [33000, 262], [33300, 262], [33600, 261], [33900, 260], [34200, 260], [34500, 259], [34800, 259], [35100, 258], [35400, 257], [35701, 256], [36002, 254], [36300, 1], [36303, 253], [36600, 2], [36604, 126], [36733, 2], [36738, 3], [36744, 111], [36900, 3], [36905, 125], [37033, 2], [37038, 3], [37044, 111], [37200, 3], [37205, 120], [37327, 2], [37333, 2], [37338, 3], [37344, 3], [37349, 105], [37500, 4], [37506, 118], [37627, 2], [37633, 2], [37638, 2], [37644, 3], [37650, 103], [37800, 5], [37807, 117], [37927, 2], [37932, 3], [37938, 2], [37944, 3], [37950, 103], [38100, 6], [38108, 116], [38227, 2], [38232, 3], [38238, 2], [38244, 3], [38250, 102], [38400, 7], [38409, 114], [38527, 2], [38532, 3], [38538, 2], [38544, 2], [38550, 102], [38700, 8], [38709, 114], [38827, 2], [38832, 3], [38838, 2], [38844, 2], [38850, 101], [39000, 8], [39010, 240], [39300, 9], [39311, 239], [39600, 10], [39612, 237], [39900, 11], [39913, 236], [40200, 12], [40214, 234], [40500, 13], [40514, 107], [40653, 94], [40800, 13], [40815, 106], [40953, 94], [41100, 14], [41116, 105], [41252, 94], [41400, 15], [41417, 104], [41552, 94], [41700, 16], [41718, 103], [41852, 93], [42000, 17], [42019, 102], [42152, 92], [42300, 18], [42319, 103], [42451, 93], [42600, 18], [42620, 102], [42751, 92], [42900, 19], [42921, 101], [43051, 92], [43200, 20], [43222, 101], [43350, 92], [43500, 21], [43523, 100], [43650, 91], [43800, 22], [43823, 101], [43949, 92], [44100, 23], [44124, 101], [44248, 92], [44400, 23], [44425, 102], [44547, 93], [44700, 24], [44726, 102], [44846, 93], [45000, 25], [45027, 102], [45145, 94], [45300, 26], [45328, 104], [45442, 96], [45600, 27], [45628, 209], [45900, 28], [45929, 208], [46200, 28], [46230, 206], [46500, 29], [46531, 205], [46800, 30], [46832, 203], [47100, 31], [47133, 201], [47400, 32], [47433, 201], [47700, 33], [47734, 199], [48000, 33], [48035, 198], [48300, 34], [48336, 196], [48600, 35], [48637, 194], [48900, 36], [48937, 194], [49200, 37], [49238, 192], [49500, 38], [49539, 191], [49800, 38], [49840, 189], [50100, 39], [50141, 187], [50400, 40], [50442, 186], [50700, 41], [50742, 185], [51000, 42], [51043, 184], [51300, 43], [51344, 182], [51600, 43], [51645, 181], [51900, 44], [51946, 179], [52200, 45], [52247, 177], [52500, 46], [52547, 177], [52800, 47], [52848, 175], [53100, 48], [53149, 174], [53400, 48], [53450, 172], [53700, 49], [53751, 170], [54000, 50], [54051, 170], [54300, 51], [54352, 168], [54600, 52], [54653, 167], [54900, 53], [54954, 165], [55200, 53], [55255, 163], [55500, 54], [55556, 162], [55800, 55], [55856, 161], [56100, 56], [56157, 160], [56400, 57], [56458, 158], [56700, 58], [56759, 156], [57000, 58], [57060, 155], [57300, 59], [57361, 153], [57600, 60], [57661, 153], [57900, 61], [57962, 151], [58200, 62], [58263, 149], [58500, 212], [58800, 211], [59100, 211], [59400, 211], [59700, 211], [60000, 212], [60300, 212], [60600, 212], [60900, 212], [61200, 212], [61500, 212], [61800, 212], [62100, 212], [62400, 213], [62700, 213], [63000, 212], [63300, 63], [63364, 148], [63600, 62], [63666, 145], [63900, 62], [63970, 136], [64200, 62], [64274, 128], [64500, 62], [64579, 118], [64800, 62], [64885, 106], [65100, 61], [65192, 92], [65400, 61], [65501, 73], [65700, 61], [65812, 52], [66000, 61], [66300, 61], [66600, 61], [66900, 60], [67200, 60], [67500, 60], [67800, 60], [68100, 60], [68400, 60], [68700, 59], [69000, 59], [69300, 59], [69600, 59], [69900, 59], [70200, 58], [70500, 58], [70800, 58], [71100, 58], [71400, 58], [71700, 58], [72000, 57], [72300, 57], [72600, 57], [72900, 57], [73200, 57], [73500, 57], [73800, 56], [74100, 56], [74400, 56], [74700, 56], [75000, 56], [75300, 55], [75600, 55], [75900, 55], [76200, 55], [76500, 55], [76800, 55], [77100, 54], [77400, 54], [77700, 54], [78000, 54], [78300, 54], [78600, 53], [78900, 53], [79200, 53], [79500, 53], [79800, 53], [80100, 53], [80400, 52], [80700, 52], [81000, 52], [81300, 52], [81600, 52], [81900, 52], [82200, 51], [82500, 51], [82800, 51], [83100, 51], [83400, 51], [83700, 50], [84000, 50], [84300, 50], [84600, 50], [84900, 50], [85200, 50], [85500, 49], [85800, 49], [86100, 49], [86400, 49], [86700, 49], [87000, 49], [87300, 48], [87600, 48], [87900, 48], [88200, 48], [88500, 48], [88800, 47], [89100, 47], [89400, 47], [89700, 47]], "point": [149, 149]}}, "high_idx": 11}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.80|+00.00|+04.43"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 111], [187, 225], [483, 234], [773, 255], [1029, 13670], [14700, 298], [15000, 298], [15300, 297], [15600, 297], [15900, 296], [16200, 295], [16500, 295], [16800, 294], [17100, 294], [17400, 293], [17700, 292], [18000, 292], [18300, 291], [18600, 291], [18900, 290], [19200, 289], [19500, 289], [19800, 288], [20100, 288], [20400, 287], [20700, 286], [21000, 286], [21300, 285], [21600, 285], [21900, 284], [22200, 284], [22500, 283], [22800, 282], [23100, 282], [23400, 281], [23700, 281], [24000, 280], [24300, 279], [24600, 279], [24900, 278], [25200, 278], [25500, 277], [25800, 276], [26100, 276], [26400, 275], [26700, 275], [27000, 274], [27300, 273], [27600, 273], [27900, 272], [28200, 272], [28500, 271], [28800, 271], [29100, 270], [29400, 269], [29700, 269], [30000, 268], [30300, 268], [30600, 267], [30900, 266], [31200, 266], [31500, 265], [31800, 265], [32100, 264], [32400, 263], [32700, 263], [33000, 262], [33300, 262], [33600, 261], [33900, 260], [34200, 260], [34500, 259], [34800, 259], [35100, 258], [35400, 257], [35701, 256], [36002, 254], [36300, 1], [36303, 253], [36600, 2], [36604, 126], [36733, 2], [36738, 3], [36744, 111], [36900, 3], [36905, 125], [37033, 2], [37038, 3], [37044, 111], [37200, 3], [37205, 120], [37327, 2], [37333, 2], [37338, 3], [37344, 3], [37349, 105], [37500, 4], [37506, 118], [37627, 2], [37633, 2], [37638, 2], [37644, 3], [37650, 103], [37800, 5], [37807, 117], [37927, 2], [37932, 3], [37938, 2], [37944, 3], [37950, 103], [38100, 6], [38108, 116], [38227, 2], [38232, 3], [38238, 2], [38244, 3], [38250, 102], [38400, 7], [38409, 114], [38527, 2], [38532, 3], [38538, 2], [38544, 2], [38550, 102], [38700, 8], [38709, 114], [38827, 2], [38832, 3], [38838, 2], [38844, 2], [38850, 101], [39000, 8], [39010, 240], [39300, 9], [39311, 239], [39600, 10], [39612, 237], [39900, 11], [39913, 236], [40200, 12], [40214, 234], [40500, 13], [40514, 107], [40653, 94], [40800, 13], [40815, 106], [40953, 94], [41100, 14], [41116, 105], [41252, 94], [41400, 15], [41417, 104], [41552, 94], [41700, 16], [41718, 103], [41852, 93], [42000, 17], [42019, 102], [42152, 92], [42300, 18], [42319, 103], [42451, 93], [42600, 18], [42620, 102], [42751, 92], [42900, 19], [42921, 101], [43051, 92], [43200, 20], [43222, 101], [43350, 92], [43500, 21], [43523, 100], [43650, 91], [43800, 22], [43823, 101], [43949, 92], [44100, 23], [44124, 101], [44248, 92], [44400, 23], [44425, 102], [44547, 93], [44700, 24], [44726, 102], [44846, 93], [45000, 25], [45027, 102], [45145, 94], [45300, 26], [45328, 104], [45442, 96], [45600, 27], [45628, 209], [45900, 28], [45929, 208], [46200, 28], [46230, 206], [46500, 29], [46531, 205], [46800, 30], [46832, 203], [47100, 31], [47133, 201], [47400, 32], [47433, 201], [47700, 33], [47734, 199], [48000, 33], [48035, 198], [48300, 34], [48336, 196], [48600, 35], [48637, 194], [48900, 36], [48937, 194], [49200, 37], [49238, 192], [49500, 38], [49539, 191], [49800, 38], [49840, 189], [50100, 39], [50141, 187], [50400, 40], [50442, 186], [50700, 41], [50742, 185], [51000, 42], [51043, 184], [51300, 43], [51344, 182], [51600, 43], [51645, 181], [51900, 44], [51946, 179], [52200, 45], [52247, 177], [52500, 46], [52547, 177], [52800, 47], [52848, 175], [53100, 48], [53149, 174], [53400, 48], [53450, 172], [53700, 49], [53751, 170], [54000, 50], [54051, 170], [54300, 51], [54352, 168], [54600, 52], [54653, 167], [54900, 53], [54954, 165], [55200, 53], [55255, 163], [55500, 54], [55556, 162], [55800, 55], [55856, 161], [56100, 56], [56157, 160], [56400, 57], [56458, 158], [56700, 58], [56759, 156], [57000, 58], [57060, 155], [57300, 59], [57361, 153], [57600, 60], [57661, 153], [57900, 61], [57962, 151], [58200, 62], [58263, 149], [58500, 212], [58800, 211], [59100, 211], [59400, 211], [59700, 211], [60000, 212], [60300, 212], [60600, 212], [60900, 212], [61200, 212], [61500, 212], [61800, 212], [62100, 212], [62400, 213], [62700, 213], [63000, 212], [63300, 63], [63364, 148], [63600, 62], [63666, 145], [63900, 62], [63970, 136], [64200, 62], [64274, 128], [64500, 62], [64579, 118], [64800, 62], [64885, 106], [65100, 61], [65192, 92], [65400, 61], [65501, 73], [65700, 61], [65812, 52], [66000, 61], [66300, 61], [66600, 61], [66900, 60], [67200, 60], [67500, 60], [67800, 60], [68100, 60], [68400, 60], [68700, 59], [69000, 59], [69300, 59], [69600, 59], [69900, 59], [70200, 58], [70500, 58], [70800, 58], [71100, 58], [71400, 58], [71700, 58], [72000, 57], [72300, 57], [72600, 57], [72900, 57], [73200, 57], [73500, 57], [73800, 56], [74100, 56], [74400, 56], [74700, 56], [75000, 56], [75300, 55], [75600, 55], [75900, 55], [76200, 55], [76500, 55], [76800, 55], [77100, 54], [77400, 54], [77700, 54], [78000, 54], [78300, 54], [78600, 53], [78900, 53], [79200, 53], [79500, 53], [79800, 53], [80100, 53], [80400, 52], [80700, 52], [81000, 52], [81300, 52], [81600, 52], [81900, 52], [82200, 51], [82500, 51], [82800, 51], [83100, 51], [83400, 51], [83700, 50], [84000, 50], [84300, 50], [84600, 50], [84900, 50], [85200, 50], [85500, 49], [85800, 49], [86100, 49], [86400, 49], [86700, 49], [87000, 49], [87300, 48], [87600, 48], [87900, 48], [88200, 48], [88500, 48], [88800, 47], [89100, 47], [89400, 47], [89700, 47]], "point": [149, 149]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan26", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.75, "y": 0.901591063, "z": 4.25}, "object_poses": [{"objectName": "Potato_bbca86d2", "position": {"x": -2.89261842, "y": 0.9268664, "z": 2.44185042}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "DishSponge_50b7389e", "position": {"x": -2.89261961, "y": 0.900185, "z": 2.04548883}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "DishSponge_50b7389e", "position": {"x": -2.731272, "y": 0.0537436642, "z": 0.3696859}, "rotation": {"x": 0.0, "y": 26.1012325, "z": 0.0}}, {"objectName": "Spatula_5216d872", "position": {"x": -1.094266, "y": 0.81089437, "z": 0.226540267}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Spatula_5216d872", "position": {"x": -0.572060764, "y": 0.9156206, "z": 2.53082561}, "rotation": {"x": 0.0, "y": 90.00016, "z": 0.0}}, {"objectName": "Fork_80a3685e", "position": {"x": -2.785874, "y": 0.936048567, "z": 3.20964146}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_80a3685e", "position": {"x": -0.5720579, "y": 0.9011693, "z": 3.535362}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Pot_c7fbe144", "position": {"x": -0.5359, "y": 0.928928852, "z": 4.2742}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_05581a9a", "position": {"x": -2.99565959, "y": 0.933984041, "z": 2.17760944}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Egg_05581a9a", "position": {"x": -2.3774147, "y": 0.933984041, "z": 2.5739696}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Cup_c473c604", "position": {"x": -2.789578, "y": 0.9003033, "z": 2.30972958}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Cup_c473c604", "position": {"x": -2.94370174, "y": 0.9351826, "z": 3.30242777}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_d8a5b3b1", "position": {"x": -2.628046, "y": 0.9993129, "z": 3.20964146}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_d8a5b3b1", "position": {"x": -0.572059035, "y": 0.9644336, "z": 3.13354731}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Bowl_9504375a", "position": {"x": -0.5720613, "y": 0.8972413, "z": 2.32991815}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "SaltShaker_38b3b13a", "position": {"x": -1.66258121, "y": 0.658710659, "z": 0.5192609}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c5422882", "position": {"x": -2.4854188, "y": 0.658710659, "z": 0.355198532}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_a7306c85", "position": {"x": -2.77860022, "y": 1.00137031, "z": 3.71400023}, "rotation": {"x": 0.0, "y": 89.99991, "z": 0.0}}, {"objectName": "Apple_5deff24a", "position": {"x": -1.00791872, "y": 0.8522371, "z": 0.3371}, "rotation": {"x": -1.40334191e-14, "y": 180.0, "z": 0.0}}, {"objectName": "Apple_5deff24a", "position": {"x": -0.921571434, "y": 0.8522371, "z": 0.39237985}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_05581a9a", "position": {"x": -1.48582983, "y": 0.9615697, "z": 0.650521755}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d1c1b4b5", "position": {"x": -1.80161786, "y": 1.77788591, "z": 0.2058212}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_80a3685e", "position": {"x": -1.90418983, "y": 0.9287549, "z": 0.6026541}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_4b633365", "position": {"x": -0.400523, "y": 0.9578467, "z": 2.53082466}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Apple_5deff24a", "position": {"x": -0.1911687, "y": 1.07491815, "z": 0.203774}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_8490bc5f", "position": {"x": -2.37741661, "y": 0.980303168, "z": 1.91336668}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Spatula_5216d872", "position": {"x": -1.7709682, "y": 0.943206251, "z": 0.433551341}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_6c866e4b", "position": {"x": -0.228984147, "y": 0.9262391, "z": 2.932639}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Potato_bbca86d2", "position": {"x": -2.54913187, "y": 0.9617457, "z": 3.580786}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_d8a5b3b1", "position": {"x": -2.88892174, "y": 1.25014567, "z": 4.427}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_c473c604", "position": {"x": -2.19144225, "y": 1.77324951, "z": 0.2514462}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_38b3b13a", "position": {"x": -1.8243, "y": 0.658710659, "z": 0.5192609}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c5422882", "position": {"x": -0.1432147, "y": 0.896926939, "z": 3.13354588}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "ButterKnife_19505874", "position": {"x": -1.1806134, "y": 0.7959366, "z": 0.3371}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_57182a42", "position": {"x": -2.58349752, "y": 0.900284231, "z": 2.04548788}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Pan_b3245c00", "position": {"x": -0.250185, "y": 0.926930964, "z": 4.65182543}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_50b7389e", "position": {"x": -0.65782845, "y": 0.900185, "z": 2.93264031}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Pot_c7fbe144", "position": {"x": -2.48045588, "y": 0.9002281, "z": 2.30972862}, "rotation": {"x": 0.0, "y": 180.000153, "z": 0.0}}, {"objectName": "Spoon_11e3f5dc", "position": {"x": -2.583497, "y": 0.901750147, "z": 2.17760825}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Mug_a7306c85", "position": {"x": -2.68653774, "y": 0.895620763, "z": 2.17760849}, "rotation": {"x": 0.0, "y": 0.000163924531, "z": 0.0}}, {"objectName": "Bowl_9504375a", "position": {"x": -2.78504086, "y": 0.2957105, "z": 4.427}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3830125040, "scene_num": 26}, "task_id": "trial_T20190910_220611_482286", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3HL2LL0LEPZT8_379J5II41R79SEIQHDG23MBJ5S9LE7", "high_descs": ["Turn left, go forward, turn left and go forward to the white rectangular table with the toaster.", "Take the large knife from the table.", "Go to the right side of the table where the bread is.", "Cut the bread into slices on the table.", "Turn around, go forward to the white square table.", "Put the knife in the pot on the table.", "Turn around, go forward to the rectangular table.", "Take a slice of bread from the table.", "Turn right, go forward and left to the microwave to the left of the sink.", "Heat up the bread in the microwave. Take the bread from the microwave.", "Turn around, go forward to the wall, turn left, go forward to the fridge.", "Put the bread in the fridge."], "task_desc": "Put a hot slice of bread in a fridge.", "votes": [1, 1]}, {"assignment_id": "AJQGWGESKQT4Y_3NGI5ARFTWW0ZY3CXHEUDJ19LT61P2", "high_descs": ["Go left and then left again to stand in front of the white table.", "Pick the knife up from the table.", "Move to the right and face the right side of the white table.", "Slice the bread on the table.", "Turn around and cross the room to stand in front of the white table to the left of the fridge.", "Put the knife in the pot on the table.", "Turn around and cross the room to face the white table with the bread on it.", "Pick a slice of bread up from the table.", "Go right and face the microwave to the left of the sink.", "Put the bread in the microwave and shut the door and then open the door and pick the bread up again.", "Turn around and cross the room and go left to stand in front of the fridge.", "Put the bread in the fridge and shut the door."], "task_desc": "Put a heated slice of bread in the fridge.", "votes": [1, 1]}, {"assignment_id": "ARB8SJAWXUWTD_3SB5N7Y3O6VUFWPVYFK75547FA6G03", "high_descs": ["Go to the short white table", "Pick up the knife", "Go to the loaf of bread on the table", "Cut the loaf of bread", "Go to the taller white table", "Put the knife in a pot on the tall table", "Go to the short table", "Pick up a slice of bread", "Go to the microwave", "Heat the bread", "Go to the fridge", "Put the bread in the fridge"], "task_desc": "Put a heated slice of bread in the fridge", "votes": [1, 1]}]}}