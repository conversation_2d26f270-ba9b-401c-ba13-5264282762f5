{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000083.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000084.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000085.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000102.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000105.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000106.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000109.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000110.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000111.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000131.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000132.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000133.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000134.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000135.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000136.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000137.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000146.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000147.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000151.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000152.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000153.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000154.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000155.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000158.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 7, "image_name": "000000169.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000171.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000172.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000173.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000174.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000175.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000182.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000183.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000184.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000190.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000191.png", "low_idx": 29}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "SprayBottle", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["toilet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-7|-1|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["spraybottle"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["SprayBottle", [-7.48692848, -7.48692848, 1.350810528, 1.350810528, 4.18223, 4.18223]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-6.44, -6.44, 0.84, 0.84, -0.0021757484, -0.0021757484]], "forceVisible": true, "objectId": "SprayBottle|-01.87|+01.05|+00.34"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-1|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["spraybottle", "cabinet"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["SprayBottle", [-7.48692848, -7.48692848, 1.350810528, 1.350810528, 4.18223, 4.18223]], "coordinateReceptacleObjectId": ["Cabinet", [-8.0804, -8.0804, -5.308848, -5.308848, 1.3603752, 1.3603752]], "forceVisible": true, "objectId": "SprayBottle|-01.87|+01.05|+00.34", "receptacleObjectId": "Cabinet|-02.02|+00.34|-01.33"}}, {"discrete_action": {"action": "GotoLocation", "args": ["toilet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|-1|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["spraybottle"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["SprayBottle", [-7.97706128, -7.97706128, 1.61310208, 1.61310208, 4.18223, 4.18223]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-6.44, -6.44, 0.84, 0.84, -0.0021757484, -0.0021757484]], "forceVisible": true, "objectId": "SprayBottle|-01.99|+01.05|+00.40"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-1|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["spraybottle", "cabinet"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["SprayBottle", [-7.97706128, -7.97706128, 1.61310208, 1.61310208, 4.18223, 4.18223]], "coordinateReceptacleObjectId": ["Cabinet", [-8.0804, -8.0804, -5.308848, -5.308848, 1.3603752, 1.3603752]], "forceVisible": true, "objectId": "SprayBottle|-01.99|+01.05|+00.40", "receptacleObjectId": "Cabinet|-02.02|+00.34|-01.33"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "SprayBottle|-01.87|+01.05|+00.34"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [94, 59, 115, 147], "mask": [[17498, 4], [17797, 7], [18097, 7], [18397, 7], [18696, 7], [18996, 7], [19296, 7], [19595, 8], [19895, 8], [20195, 9], [20494, 10], [20794, 10], [21094, 10], [21394, 10], [21695, 10], [21996, 9], [22297, 8], [22597, 8], [22897, 9], [23197, 9], [23497, 9], [23797, 9], [24097, 10], [24397, 10], [24697, 10], [24997, 10], [25298, 10], [25598, 10], [25898, 10], [26198, 10], [26498, 11], [26799, 10], [27099, 10], [27399, 11], [27699, 11], [27999, 12], [28299, 12], [28600, 11], [28900, 11], [29200, 12], [29500, 12], [29800, 12], [30100, 12], [30399, 13], [30699, 14], [30999, 14], [31299, 14], [31599, 14], [31899, 14], [32199, 14], [32499, 14], [32799, 14], [33099, 15], [33399, 15], [33698, 16], [33998, 16], [34298, 16], [34598, 16], [34898, 16], [35199, 16], [35499, 16], [35799, 16], [36099, 16], [36399, 16], [36699, 16], [36999, 16], [37299, 16], [37599, 16], [37899, 16], [38199, 16], [38500, 16], [38800, 16], [39100, 15], [39400, 15], [39700, 15], [40000, 15], [40301, 14], [40601, 14], [40901, 13], [41201, 13], [41501, 13], [41802, 12], [42102, 12], [42402, 12], [42702, 12], [43003, 10], [43303, 10], [43604, 8], [43906, 4]], "point": [104, 102]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-02.02|+00.34|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [190, 126, 293, 200], "mask": [[37703, 90], [38003, 91], [38303, 90], [38603, 90], [38903, 89], [39203, 89], [39502, 89], [39802, 89], [40106, 84], [40406, 84], [40707, 82], [41007, 82], [41307, 81], [41607, 81], [41907, 80], [42207, 80], [42507, 79], [42807, 79], [43107, 78], [43406, 79], [43700, 84], [44000, 84], [44299, 84], [44599, 84], [44899, 83], [45199, 83], [45499, 82], [45798, 83], [46098, 82], [46398, 82], [46698, 81], [46998, 81], [47297, 81], [47597, 80], [47897, 80], [48197, 79], [48497, 79], [48797, 78], [49096, 79], [49396, 78], [49696, 78], [49996, 77], [50296, 77], [50595, 77], [50895, 77], [51195, 76], [51495, 76], [51795, 75], [52094, 76], [52394, 75], [52694, 75], [52994, 74], [53294, 74], [53594, 73], [53893, 74], [54193, 73], [54493, 73], [54793, 72], [55093, 72], [55392, 72], [55692, 72], [55992, 71], [56292, 71], [56592, 70], [56891, 71], [57191, 70], [57491, 70], [57791, 69], [58091, 69], [58391, 68], [58690, 69], [58990, 68], [59290, 68], [59590, 67], [59891, 66]], "point": [241, 162]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "SprayBottle|-01.87|+01.05|+00.34", "placeStationary": true, "receptacleObjectId": "Cabinet|-02.02|+00.34|-01.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [256, 127, 299, 267], "mask": [[38092, 3], [38392, 3], [38691, 5], [38991, 6], [39290, 7], [39590, 8], [39889, 9], [40189, 10], [40488, 12], [40788, 12], [41087, 13], [41387, 13], [41686, 14], [41986, 14], [42285, 15], [42585, 15], [42884, 16], [43184, 16], [43483, 17], [43783, 17], [44082, 18], [44382, 18], [44681, 19], [44981, 19], [45280, 20], [45580, 20], [45879, 21], [46179, 21], [46478, 22], [46778, 22], [47077, 23], [47377, 23], [47676, 24], [47976, 24], [48275, 25], [48575, 25], [48874, 26], [49174, 26], [49473, 27], [49773, 27], [50072, 28], [50372, 28], [50671, 29], [50971, 29], [51270, 30], [51570, 30], [51869, 31], [52169, 31], [52468, 32], [52768, 32], [53067, 33], [53367, 33], [53666, 34], [53966, 34], [54265, 35], [54565, 35], [54864, 36], [55164, 36], [55463, 37], [55763, 37], [56062, 38], [56362, 38], [56661, 39], [56961, 39], [57260, 40], [57560, 40], [57859, 41], [58159, 41], [58458, 42], [58758, 42], [59057, 43], [59357, 43], [59656, 44], [59956, 44], [60257, 43], [60557, 43], [60857, 43], [61158, 42], [61458, 42], [61758, 42], [62059, 41], [62359, 41], [62659, 41], [62960, 40], [63260, 40], [63560, 40], [63861, 39], [64161, 39], [64461, 39], [64762, 38], [65062, 38], [65362, 38], [65663, 37], [65963, 37], [66264, 36], [66564, 36], [66864, 36], [67165, 35], [67465, 35], [67765, 35], [68066, 34], [68366, 34], [68666, 34], [68967, 33], [69267, 33], [69567, 33], [69868, 32], [70168, 32], [70468, 32], [70769, 31], [71069, 31], [71369, 31], [71670, 30], [71970, 30], [72270, 30], [72571, 29], [72871, 29], [73171, 29], [73472, 28], [73772, 28], [74072, 28], [74373, 26], [74673, 25], [74973, 25], [75274, 23], [75574, 22], [75875, 20], [76175, 19], [76475, 18], [76776, 16], [77076, 15], [77376, 14], [77677, 12], [77977, 11], [78277, 10], [78578, 8], [78878, 8], [79178, 7], [79479, 5], [79779, 4], [80079, 3]], "point": [277, 196]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-02.02|+00.34|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [256, 127, 299, 267], "mask": [[38092, 3], [38392, 3], [38691, 5], [38991, 6], [39290, 7], [39590, 8], [39889, 9], [40189, 10], [40488, 12], [40788, 12], [41087, 13], [41387, 13], [41686, 14], [41986, 14], [42285, 15], [42585, 15], [42884, 16], [43184, 16], [43483, 17], [43783, 17], [44082, 18], [44382, 18], [44681, 19], [44981, 19], [45280, 20], [45580, 20], [45879, 21], [46179, 21], [46478, 22], [46778, 22], [47077, 23], [47377, 23], [47676, 24], [47976, 24], [48275, 25], [48575, 25], [48874, 26], [49174, 26], [49473, 27], [49773, 27], [50072, 28], [50372, 28], [50671, 29], [50971, 29], [51270, 30], [51570, 30], [51869, 31], [52169, 31], [52468, 32], [52768, 32], [53067, 33], [53367, 33], [53666, 34], [53966, 34], [54265, 35], [54565, 35], [54864, 36], [55164, 36], [55463, 37], [55763, 37], [56062, 38], [56362, 38], [56661, 39], [56961, 39], [57260, 40], [57560, 40], [57859, 41], [58159, 41], [58458, 42], [58758, 42], [59057, 43], [59357, 43], [59656, 44], [59956, 44], [60257, 43], [60557, 43], [60857, 43], [61158, 42], [61458, 42], [61758, 42], [62059, 41], [62359, 41], [62659, 41], [62960, 40], [63260, 40], [63560, 40], [63861, 39], [64161, 39], [64461, 39], [64762, 38], [65062, 38], [65362, 38], [65663, 37], [65963, 37], [66264, 36], [66564, 36], [66864, 36], [67165, 35], [67465, 35], [67765, 35], [68066, 34], [68366, 34], [68666, 34], [68967, 33], [69267, 33], [69567, 33], [69868, 32], [70168, 32], [70468, 32], [70769, 31], [71069, 31], [71369, 31], [71670, 30], [71970, 30], [72270, 30], [72571, 29], [72871, 29], [73171, 29], [73472, 28], [73772, 28], [74072, 28], [74373, 26], [74673, 25], [74973, 25], [75274, 23], [75574, 22], [75875, 20], [76175, 19], [76475, 18], [76776, 16], [77076, 15], [77376, 14], [77677, 12], [77977, 11], [78277, 10], [78578, 8], [78878, 8], [79178, 7], [79479, 5], [79779, 4], [80079, 3]], "point": [277, 196]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "SprayBottle|-01.99|+01.05|+00.40"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [48, 47, 79, 132], "mask": [[13854, 4], [14154, 6], [14453, 8], [14752, 9], [15051, 10], [15351, 10], [15650, 11], [15949, 12], [16249, 12], [16549, 12], [16849, 12], [17148, 13], [17448, 14], [17749, 13], [18052, 10], [18353, 9], [18653, 10], [18953, 10], [19253, 10], [19553, 11], [19853, 11], [20154, 11], [20454, 11], [20754, 12], [21054, 12], [21354, 13], [21654, 2], [21657, 10], [21955, 1], [21957, 11], [22257, 11], [22557, 12], [22857, 12], [23158, 11], [23458, 12], [23758, 13], [24058, 13], [24359, 13], [24659, 13], [24960, 13], [25260, 13], [25560, 13], [25860, 14], [26159, 15], [26459, 15], [26759, 16], [27059, 16], [27359, 16], [27658, 17], [27958, 18], [28258, 18], [28558, 18], [28858, 18], [29158, 19], [29458, 19], [29758, 19], [30058, 19], [30358, 20], [30658, 20], [30958, 20], [31258, 20], [31558, 20], [31858, 21], [32159, 20], [32459, 20], [32759, 20], [33059, 21], [33360, 20], [33660, 20], [33960, 20], [34260, 20], [34561, 19], [34861, 19], [35161, 19], [35462, 17], [35762, 17], [36062, 17], [36363, 16], [36663, 15], [36963, 15], [37264, 14], [37564, 13], [37864, 13], [38165, 12], [38465, 11], [38766, 10], [39067, 7], [39368, 4]], "point": [63, 88]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-02.02|+00.34|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [190, 126, 293, 200], "mask": [[37703, 90], [38003, 91], [38303, 90], [38603, 90], [38903, 89], [39203, 89], [39502, 89], [39802, 89], [40106, 84], [40406, 84], [40707, 82], [41007, 82], [41307, 81], [41607, 81], [41907, 80], [42207, 80], [42507, 79], [42807, 79], [43107, 78], [43406, 79], [43700, 84], [44000, 84], [44299, 84], [44599, 84], [44899, 83], [45199, 83], [45499, 82], [45798, 83], [46098, 82], [46398, 82], [46698, 81], [46998, 81], [47297, 81], [47597, 80], [47897, 80], [48197, 79], [48497, 79], [48797, 78], [49096, 79], [49396, 78], [49696, 78], [49996, 77], [50296, 77], [50595, 77], [50895, 77], [51195, 76], [51495, 76], [51795, 75], [52094, 76], [52394, 75], [52694, 75], [52994, 74], [53294, 74], [53594, 73], [53893, 74], [54193, 73], [54493, 73], [54793, 72], [55093, 72], [55392, 72], [55692, 72], [55992, 71], [56292, 71], [56592, 70], [56891, 71], [57191, 70], [57491, 70], [57791, 69], [58091, 69], [58391, 68], [58690, 69], [58990, 68], [59290, 68], [59590, 67], [59891, 66]], "point": [241, 162]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "SprayBottle|-01.99|+01.05|+00.40", "placeStationary": true, "receptacleObjectId": "Cabinet|-02.02|+00.34|-01.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [256, 127, 299, 267], "mask": [[38092, 3], [38392, 3], [38691, 5], [38991, 6], [39290, 7], [39590, 8], [39889, 9], [40189, 10], [40488, 12], [40788, 12], [41087, 13], [41387, 13], [41686, 14], [41986, 14], [42285, 15], [42585, 15], [42884, 16], [43184, 16], [43483, 17], [43783, 17], [44082, 18], [44382, 18], [44681, 19], [44981, 19], [45280, 20], [45580, 20], [45879, 21], [46179, 21], [46478, 22], [46778, 22], [47077, 23], [47377, 23], [47676, 24], [47976, 24], [48275, 25], [48575, 25], [48874, 26], [49174, 26], [49473, 27], [49773, 27], [50072, 28], [50372, 28], [50671, 29], [50971, 29], [51270, 30], [51570, 30], [51869, 31], [52169, 31], [52468, 32], [52768, 32], [53067, 33], [53367, 33], [53666, 34], [53966, 34], [54265, 35], [54565, 35], [54864, 36], [55164, 36], [55463, 37], [55763, 37], [56062, 38], [56362, 38], [56661, 39], [56961, 39], [57260, 40], [57560, 40], [57859, 41], [58159, 41], [58458, 42], [58758, 42], [59057, 43], [59357, 43], [59656, 44], [59956, 44], [60257, 43], [60557, 43], [60857, 43], [61158, 42], [61458, 42], [61758, 42], [62059, 41], [62359, 41], [62659, 41], [62960, 40], [63260, 40], [63560, 40], [63861, 39], [64161, 39], [64461, 39], [64762, 38], [65062, 38], [65362, 38], [65663, 37], [65963, 37], [66264, 36], [66564, 36], [66864, 36], [67165, 35], [67465, 35], [67765, 35], [68066, 34], [68366, 34], [68666, 34], [68967, 33], [69267, 33], [69567, 33], [69868, 32], [70168, 32], [70468, 32], [70769, 31], [71069, 31], [71369, 31], [71670, 30], [71970, 30], [72270, 30], [72571, 29], [72871, 29], [73171, 29], [73472, 28], [73772, 28], [74072, 28], [74373, 26], [74673, 25], [74973, 25], [75274, 23], [75574, 22], [75875, 20], [76175, 19], [76475, 18], [76776, 16], [77076, 15], [77376, 14], [77677, 12], [77977, 11], [78277, 10], [78578, 8], [78878, 8], [79178, 7], [79479, 5], [79779, 4], [80079, 3]], "point": [277, 196]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-02.02|+00.34|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [256, 127, 299, 267], "mask": [[38092, 3], [38392, 3], [38691, 5], [38991, 6], [39290, 7], [39590, 8], [39889, 9], [40189, 10], [40488, 12], [40788, 12], [41087, 13], [41387, 13], [41686, 14], [41986, 14], [42285, 15], [42585, 15], [42884, 16], [43184, 16], [43483, 17], [43783, 17], [44082, 18], [44382, 18], [44681, 19], [44981, 19], [45280, 20], [45580, 20], [45879, 21], [46179, 21], [46478, 22], [46778, 22], [47077, 23], [47377, 23], [47676, 24], [47976, 24], [48275, 25], [48575, 25], [48874, 26], [49174, 26], [49473, 27], [49773, 27], [50072, 28], [50372, 28], [50671, 29], [50971, 29], [51270, 30], [51570, 30], [51869, 31], [52169, 31], [52468, 32], [52768, 32], [53067, 33], [53367, 33], [53666, 34], [53966, 34], [54265, 35], [54565, 35], [54864, 36], [55164, 36], [55463, 37], [55763, 37], [56062, 38], [56362, 38], [56661, 39], [56961, 39], [57260, 40], [57560, 40], [57859, 41], [58159, 41], [58458, 42], [58758, 42], [59057, 43], [59357, 43], [59656, 44], [59956, 44], [60257, 43], [60557, 43], [60857, 43], [61158, 42], [61458, 42], [61758, 42], [62059, 41], [62359, 41], [62659, 41], [62960, 40], [63260, 40], [63560, 40], [63861, 39], [64161, 39], [64461, 39], [64762, 38], [65062, 38], [65362, 38], [65663, 37], [65963, 37], [66264, 36], [66564, 36], [66864, 36], [67165, 35], [67465, 35], [67765, 35], [68066, 34], [68366, 34], [68666, 34], [68967, 33], [69267, 33], [69567, 33], [69868, 32], [70168, 32], [70468, 32], [70769, 31], [71069, 31], [71369, 31], [71670, 30], [71970, 30], [72270, 30], [72571, 29], [72871, 29], [73171, 29], [73472, 28], [73772, 28], [74072, 28], [74373, 26], [74673, 25], [74973, 25], [75274, 23], [75574, 22], [75875, 20], [76175, 19], [76475, 18], [76776, 16], [77076, 15], [77376, 14], [77677, 12], [77977, 11], [78277, 10], [78578, 8], [78878, 8], [79178, 7], [79479, 5], [79779, 4], [80079, 3]], "point": [277, 196]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan407", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -0.25, "y": 0.9004556, "z": -0.75}, "object_poses": [{"objectName": "Cloth_86d3911c", "position": {"x": -1.93300056, "y": 1.04496539, "z": 0.140987486}, "rotation": {"x": 0.0, "y": 90.00046, "z": 0.0}}, {"objectName": "SoapBar_6d80f90c", "position": {"x": -1.63419342, "y": 0.9257054, "z": -1.53448784}, "rotation": {"x": 347.851227, "y": 5.33460067e-08, "z": -2.35103954e-07}}, {"objectName": "SoapBar_6d80f90c", "position": {"x": -0.803343952, "y": 0.016511023, "z": -1.63916969}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_b8f58f82", "position": {"x": -1.969574, "y": 0.9073653, "z": -1.62204278}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_b8f58f82", "position": {"x": -1.969574, "y": 0.9073653, "z": -1.41801715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_68cfa6a9", "position": {"x": -0.121916831, "y": 0.9100139, "z": -1.48463}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_68cfa6a9", "position": {"x": -1.99426532, "y": 1.0455575, "z": 0.40327552}, "rotation": {"x": 0.0, "y": 90.00046, "z": 0.0}}, {"objectName": "SoapBottle_7d19428e", "position": {"x": -1.72573876, "y": 0.0188848972, "z": -1.48640609}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_7d19428e", "position": {"x": 0.0990001, "y": 0.154430568, "z": -1.59868646}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Towel_a3646d85", "position": {"x": 0.89, "y": 1.368, "z": -1.7551}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_42ae589b", "position": {"x": -2.02846885, "y": 1.492063, "z": -1.48263109}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBar_6d80f90c", "position": {"x": -1.60845792, "y": 0.016511023, "z": -1.3845638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_21541b83", "position": {"x": -1.1991, "y": 0.919, "z": 0.680799961}, "rotation": {"x": 0.0, "y": 179.999924, "z": 270.0}}, {"objectName": "ToiletPaper_77ac5aaa", "position": {"x": -0.9792651, "y": 0.0134413242, "z": -1.63916969}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cloth_86d3911c", "position": {"x": 0.9388962, "y": 0.171768725, "z": -1.24873912}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}, {"objectName": "SprayBottle_68cfa6a9", "position": {"x": -1.87173212, "y": 1.0455575, "z": 0.337702632}, "rotation": {"x": 0.0, "y": 90.00046, "z": 0.0}}, {"objectName": "ScrubBrush_f5c2530a", "position": {"x": -1.6482352, "y": -0.0005439371, "z": 0.649161935}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plunger_a7691d94", "position": {"x": -1.88542414, "y": -0.00100692362, "z": 0.6098786}, "rotation": {"x": -0.00118497887, "y": 0.000426067, "z": 0.000782692}}, {"objectName": "Candle_b8f58f82", "position": {"x": -0.5514953, "y": 0.9073653, "z": -1.31413031}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_7d19428e", "position": {"x": -0.8619843, "y": 0.0188848972, "z": -1.48640609}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1842764681, "scene_num": 407}, "task_id": "trial_T20190907_144653_660815", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3T9K2RY06UO14_3FIUS151DYJJ5BEIG9KTPB7LSQVGG7", "high_descs": ["Go to the toilet and turn right.", "Pick up the closest spray bottle on the toilet.", "Turn around and go to the sink on the right.", "Put the spray bottle in the right-hand cabinet under the sink.", "Turn around and go to the toilet.", "Pick up the spray bottle on toilet.", "Turn around and go to the sink on the right.", "Put the spray bottle in the right-hand cabinet under the sink."], "task_desc": "Move the spray bottles from the top of the toilet to the cabinet under the sink", "votes": [1, 1, 1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3DQQ64TANJ2LFAXHYXEY5N71N7PWPB", "high_descs": ["walk ahead to the end of the room and face the toilet to the right", "grab the spray bottle off of the top of the toilet", "turn around and face the bathroom sink cabinets", "place the spray bottle in the cabinet to the bottom right of the bathroom sink cabinets", "turn around and walk to the left side of the toilet on the left again", "grab the second spray bottle up off of the top of the toilet", "turn around and walk to the right side of the bathroom sink cabinets again", "place the spray bottle inside of the bottom right cabinet"], "task_desc": "place two spray bottles inside the bottom right bathroom cabinet", "votes": [1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A1ZE52NWZPN85P_3NL0RFNU0I48VHHHBZ37EE2B9AF4K3", "high_descs": ["Walk forwards and then turn to your right", "Pick up a bottle off of the top of the toilet", "Turn around and face the cabinet", "Open the cabinet door, place the bottle in the cabinet and then close the door", "Go back to facing the toilet", "Pick up another bottle off of the top of the toilet", "Turn around again and face the cabinet", "Open the cabinet door, place the bottle in the cabinet and then close the door"], "task_desc": "Place two bottles in a bathroom cabinet", "votes": [1, 1, 1, 1, 1, 0, 1]}, {"assignment_id": "A2NGMLBFZ3YQP5_33PPO7FECYWUPIAM47JO42H4UI4ID2", "high_descs": ["Walk toward the wall, turn right to face toilet.", "Pick up the spray bottle in the front on the toilet tank.", "Turn right, turn right again, walk to the sinks.", "Put the spray bottle on the left side of the bottom shelf of the cabinet under the right hand sink.", "Walk toward the wall, turn right to face toilet.", "Pick up the spray bottle from the toilet tank.", "Turn right, turn right again, walk to the sinks.", "Put the spray bottle on the bottom shelf of the cabinet under the right-hand sink to the right of the other bottle."], "task_desc": "Put two spray bottles on the bottom shelf of the cabinet under the right-hand sink.", "votes": [1, 1]}]}}