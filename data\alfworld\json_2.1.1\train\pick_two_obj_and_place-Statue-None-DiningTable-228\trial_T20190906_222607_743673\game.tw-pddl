{"pddl_domain": ";; Specification in PDDL of the Alfred domain\n;; Intended to be used with Fast Downward which supports PDDL 2.2 level 1 plus the :action-costs requirement from PDDL 3.1.\n\n(define (domain alfred)\n (:requirements\n    :adl\n    :action-costs\n    :typing\n )\n (:types\n  agent\n  location\n  receptacle\n  object\n  rtype\n  otype\n  )\n\n\n (:predicates\n    (atLocation ?a - agent ?l - location)                     ; true if the agent is at the location\n    (receptacleAtLocation ?r - receptacle ?l - location)      ; true if the receptacle is at the location (constant)\n    (objectAtLocation ?o - object ?l - location)              ; true if the object is at the location\n    (openable ?r - receptacle)                                ; true if a receptacle is openable\n    (opened ?r - receptacle)                                  ; true if a receptacle is opened\n    (inReceptacle ?o - object ?r - receptacle)                ; object ?o is in receptacle ?r\n    (isReceptacleObject ?o - object)                          ; true if the object can have things put inside it\n    (inReceptacleObject ?innerObject - object ?outerObject - object)                ; object ?innerObject is inside object ?outerObject\n    (isReceptacleObjectFull ?o - object)                      ; true if the receptacle object contains something\n    (wasInReceptacle ?o - object ?r - receptacle)             ; object ?o was or is in receptacle ?r now or some time in the past\n    (checked ?r - receptacle)                                 ; whether the receptacle has been looked inside/visited\n    (examined ?l - location)                                  ; TODO\n    (receptacleType ?r - receptacle ?t - rtype)               ; the type of receptacle (Cabinet vs Cabinet|01|2...)\n    (canContain ?rt - rtype ?ot - otype)                      ; true if receptacle can hold object\n    (objectType ?o - object ?t - otype)                       ; the type of object (Apple vs Apple|01|2...)\n    (holds ?a - agent ?o - object)                            ; object ?o is held by agent ?a\n    (holdsAny ?a - agent)                                     ; agent ?a holds an object\n    (holdsAnyReceptacleObject ?a - agent)                        ; agent ?a holds a receptacle object\n    (full ?r - receptacle)                                    ; true if the receptacle has no remaining space\n    (isClean ?o - object)                                     ; true if the object has been clean in sink\n    (cleanable ?o - object)                                   ; true if the object can be placed in a sink\n    (isHot ?o - object)                                       ; true if the object has been heated up\n    (heatable ?o - object)                                    ; true if the object can be heated up in a microwave\n    (isCool ?o - object)                                      ; true if the object has been cooled\n    (coolable ?o - object)                                    ; true if the object can be cooled in the fridge\n    (pickupable ?o - object)                                   ; true if the object can be picked up\n    (moveable ?o - object)                                      ; true if the object can be moved\n    (toggleable ?o - object)                                  ; true if the object can be turned on/off\n    (isOn ?o - object)                                        ; true if the object is on\n    (isToggled ?o - object)                                   ; true if the object has been toggled\n    (sliceable ?o - object)                                   ; true if the object can be sliced\n    (isSliced ?o - object)                                    ; true if the object is sliced\n )\n\n  (:functions\n    (distance ?from ?to)\n    (total-cost) - number\n   )\n\n;; All actions are specified such that the final arguments are the ones used\n;; for performing actions in Unity.\n\n\n(:action look\n    :parameters (?a - agent ?l - location)\n    :precondition\n        (and\n            (atLocation ?a ?l)\n        )\n    :effect\n        (and\n            (checked ?l)\n        )\n)\n\n(:action inventory\n    :parameters (?a - agent)\n    :precondition\n        ()\n    :effect\n        (and\n            (checked ?a)\n        )\n)\n\n(:action examineReceptacle\n    :parameters (?a - agent ?r - receptacle)\n    :precondition\n        (and\n            (exists (?l - location)\n                (and\n                    (atLocation ?a ?l)\n                    (receptacleAtLocation ?r ?l)\n                )\n            )\n        )\n    :effect\n        (and\n            (checked ?r)\n        )\n)\n\n(:action examineObject\n    :parameters (?a - agent ?o - object)\n    :precondition\n        (or\n            ;(exists (?l - location)\n            ;    (and\n            ;        (atLocation ?a ?l)\n            ;        (objectAtLocation ?o ?l)\n            ;    )\n            ;)\n            (exists (?l - location, ?r - receptacle)\n                (and\n                    (atLocation ?a ?l)\n                    (receptacleAtLocation ?r ?l)\n                    ; (objectAtLocation ?o ?l)\n                    (inReceptacle ?o ?r)\n                    (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n              )\n            )\n            (holds ?a ?o)\n        )\n    :effect\n        (and\n            (checked ?o)\n        )\n)\n\n;; agent goes to receptacle\n (:action GotoLocation\n    :parameters (?a - agent ?lStart - location ?lEnd - location ?r - receptacle)\n    :precondition (and\n                    (atLocation ?a ?lStart)\n                    (receptacleAtLocation ?r ?lEnd)\n                    ;(exists (?r - receptacle) (receptacleAtLocation ?r ?lEnd))\n                  )\n    :effect (and\n                (not (atLocation ?a ?lStart))\n                (atLocation ?a ?lEnd)\n                ; (forall (?r - receptacle)\n                ;     (when (and (receptacleAtLocation ?r ?lEnd)\n                ;                (or (not (openable ?r)) (opened ?r)))\n                ;         (checked ?r)\n                ;     )\n                ; )\n                ; (increase (total-cost) (distance ?lStart ?lEnd))\n                (increase (total-cost) 1)\n            )\n )\n\n;; agent opens receptacle\n (:action OpenObject\n    :parameters (?a - agent ?l - location ?r - receptacle)\n    :precondition (and\n            (openable ?r)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (not (opened ?r))\n            )\n    :effect (and\n                (opened ?r)\n                (checked ?r)\n                (increase (total-cost) 1)\n            )\n )\n;; agent closes receptacle\n (:action CloseObject\n    :parameters (?a - agent ?l - location ?r - receptacle)\n    :precondition (and\n            (openable ?r)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (opened ?r)\n            )\n    :effect (and\n                (not (opened ?r))\n                (increase (total-cost) 1)\n            )\n\n )\n\n ;; agent picks up object from a receptacle\n (:action PickupObject\n    :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n    :precondition\n        (and\n            (pickupable ?o)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            ; (objectAtLocation ?o ?l)\n            (inReceptacle ?o ?r)\n            (not (holdsAny ?a))  ; agent's hands are empty.\n            ;(not (holdsAnyReceptacleObject ?a))\n            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n            ;(not (isReceptacleObject ?o))\n        )\n    :effect\n        (and\n            (not (inReceptacle ?o ?r))\n            (holds ?a ?o)\n            (holdsAny ?a)\n            (not (objectAtLocation ?o ?l))\n            ;(not (full ?r))\n            (increase (total-cost) 1)\n        )\n )\n\n\n; ;; agent picks up object from a receptacle\n; (:action PickupObjectFromReceptacleObject\n;    :parameters (?a - agent ?l - location ?o - object ?outerR - object ?r - receptacle)\n;    :precondition\n;        (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (inReceptacle ?o ?r)\n;            (pickupable ?o)\n;            (not (holdsAny ?a))  ; agent's hands are empty.\n;            (not (holdsAnyReceptacleObject ?a))\n;            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n;            (not (isReceptacleObject ?o))\n;            (isReceptacleObject ?outerR)\n;            (inReceptacleObject ?o ?outerR)\n;        )\n;    :effect\n;        (and\n;            (not (inReceptacle ?o ?r))\n;            (holds ?a ?o)\n;            (holdsAny ?a)\n;            (not (objectAtLocation ?o ?l))\n;            (not (full ?r))\n;            (increase (total-cost) 1)\n;\n;            (not (inReceptacleObject ?o ?outerR))\n;            (not (isReceptacleObjectFull ?outerR))\n;        )\n; )\n;\n; ;; agent picks up object from a receptacle\n; (:action PickupEmptyReceptacleObject\n;    :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n;    :precondition\n;        (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            ; (objectAtLocation ?o ?l)\n;            (inReceptacle ?o ?r)\n;            (pickupable ?o)\n;            (not (holdsAny ?a))  ; agent's hands are empty.\n;            (not (holdsAnyReceptacleObject ?a))\n;            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n;            (isReceptacleObject ?o)\n;            (not (isReceptacleObjectFull ?o))\n;        )\n;    :effect\n;        (and\n;            (not (inReceptacle ?o ?r))\n;            (holds ?a ?o)\n;            (holdsAny ?a)\n;            (not (objectAtLocation ?o ?l))\n;            (not (full ?r))\n;            (increase (total-cost) 1)\n;            (holdsAnyReceptacleObject ?a)\n;        )\n; )\n;\n; ;; agent picks up object from a receptacle\n; (:action PickupFullReceptacleObject\n;    :parameters (?a - agent ?l - location ?o - object ?outerR - object ?r - receptacle)\n;    :precondition\n;        (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (inReceptacle ?outerR ?r)\n;            (pickupable ?outerR)\n;            (not (holdsAny ?a))  ; agent's hands are empty.\n;            (not (holdsAnyReceptacleObject ?a))\n;            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n;            (not (isReceptacleObject ?o))\n;            (isReceptacleObject ?outerR)\n;            (inReceptacleObject ?o ?outerR)\n;        )\n;    :effect\n;        (and\n;            (not (inReceptacle ?o ?r))\n;            (not (inReceptacle ?outerR ?r))\n;            (holds ?a ?outerR)\n;            (holdsAny ?a)\n;            (not (objectAtLocation ?o ?l))\n;            (not (objectAtLocation ?outerR ?l))\n;            (not (full ?r))\n;            (increase (total-cost) 1)\n;            (holdsAnyReceptacleObject ?a)\n;        )\n; )\n\n\n;; agent puts down an object\n (:action PutObject\n    :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n    :precondition (and\n            (holds ?a ?o)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (or (not (openable ?r)) (opened ?r))    ; receptacle is opened if it is openable\n            ;(not (full ?r))\n            (objectType ?o ?ot)\n            (receptacleType ?r ?rt)\n            (canContain ?rt ?ot)\n            ;(not (holdsAnyReceptacleObject ?a))\n            )\n    :effect (and\n                (inReceptacle ?o ?r)\n                (objectAtLocation ?o ?l)\n                ;(full ?r)\n                (not (holds ?a ?o))\n                (not (holdsAny ?a))\n                (increase (total-cost) 1)\n            )\n )\n\n;;; agent puts down an object\n; (:action PutObjectInReceptacleObject\n;    :parameters (?a - agent ?l - location ?ot - otype ?o - object ?outerO - object ?outerR - receptacle)\n;    :precondition (and\n;            (atLocation ?a ?l)\n;            (objectAtLocation ?outerO ?l)\n;            (isReceptacleObject ?outerO)\n;            (not (isReceptacleObject ?o))\n;            (objectType ?o ?ot)\n;            (holds ?a ?o)\n;            (not (holdsAnyReceptacleObject ?a))\n;            (inReceptacle ?outerO ?outerR)\n;            (not (isReceptacleObjectFull ?outerO))\n;            )\n;    :effect (and\n;                (inReceptacleObject ?o ?outerO)\n;                (inReceptacle ?o ?outerR)\n;                (not (holds ?a ?o))\n;                (not (holdsAny ?a))\n;                (objectAtLocation ?o ?l)\n;                (isReceptacleObjectFull ?outerO)\n;                (increase (total-cost) 1)\n;            )\n; )\n;\n;;; agent puts down an object\n; (:action PutEmptyReceptacleObjectinReceptacle\n;    :parameters (?a - agent ?l - location ?ot - otype ?o - object ?r - receptacle)\n;    :precondition (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (or (not (openable ?r)) (opened ?r))    ; receptacle is opened if it is openable\n;            (not (full ?r))\n;            (objectType ?o ?ot)\n;            (holds ?a ?o)\n;            (holdsAnyReceptacleObject ?a)\n;            (isReceptacleObject ?o)\n;            (not (isReceptacleObjectFull ?o))\n;            )\n;    :effect (and\n;                (inReceptacle ?o ?r)\n;                (objectAtLocation ?o ?l)\n;                (full ?r)\n;                (not (holds ?a ?o))\n;                (not (holdsAny ?a))\n;                (not (holdsAnyReceptacleObject ?a))\n;                (increase (total-cost) 1)\n;            )\n; )\n;\n;;; agent puts down a receptacle object in a receptacle\n; (:action PutFullReceptacleObjectInReceptacle\n;    :parameters (?a - agent ?l - location ?ot - otype ?innerO - object ?outerO - object ?r - receptacle) ; ?rt - rtype)\n;    :precondition (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (objectType ?outerO ?ot)\n;            (holds ?a ?outerO)\n;            (holdsAnyReceptacleObject ?a)\n;            (isReceptacleObject ?outerO)\n;            (isReceptacleObjectFull ?outerO)\n;            (inReceptacleObject ?innerO ?outerO)\n;            )\n;    :effect (and\n;                (not (holdsAny ?a))\n;                (not (holdsAnyReceptacleObject ?a))\n;                (objectAtLocation ?outerO ?l)\n;                (objectAtLocation ?innerO ?l)\n;                (inReceptacle ?outerO ?r)\n;                (inReceptacle ?innerO ?r)\n;                (not (holds ?a ?outerO))\n;                (increase (total-cost) 1)\n;            )\n; )\n\n;; agent cleans some object\n (:action CleanObject\n    :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n    :precondition (and\n            (cleanable ?o)\n            (or\n                (receptacleType ?r SinkType)\n                (receptacleType ?r SinkBasinType)\n            )\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (holds ?a ?o)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isClean ?o)\n            )\n )\n\n\n;; agent heats-up some object\n (:action HeatObject\n    :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n    :precondition (and\n            (heatable ?o)\n            (or\n                (receptacleType ?r MicrowaveType)\n            )\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (holds ?a ?o)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isHot ?o)\n                (not (isCool ?o))\n            )\n )\n\n;; agent cools some object\n (:action CoolObject\n    :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n    :precondition (and\n            (coolable ?o)\n            (or\n                (receptacleType ?r FridgeType)\n            )\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (holds ?a ?o)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isCool ?o)\n                (not (isHot ?o))\n            )\n )\n\n\n;; agent toggle object\n (:action ToggleObject\n    :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n    :precondition (and\n            (toggleable ?o)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (inReceptacle ?o ?r)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (when (isOn ?o)\n                    (not (isOn ?o)))\n                (when (not (isOn ?o))\n                    (isOn ?o))\n                (isToggled ?o)\n            )\n )\n\n\n;; agent slices some object with a knife\n (:action SliceObject\n    :parameters (?a - agent ?l - location ?co - object ?ko - object)\n    :precondition\n            (and\n                (sliceable ?co)\n                (or\n                    (objectType ?ko KnifeType)\n                    (objectType ?ko ButterKnifeType)\n                )\n                (atLocation ?a ?l)\n                (objectAtLocation ?co ?l)\n                (holds ?a ?ko)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isSliced ?co)\n            )\n )\n\n\n(:action help\n    :parameters (?a - agent)\n    :precondition\n        ()\n    :effect\n        (and\n            (checked ?a)\n        )\n)\n)\n", "grammar": "grammar :: \"\"\"\n    {\n        \"intro\": [\n            {\n                \"rhs\": \"-= Welcome to TextWorld, ALFRED! =-\\n\\n#look.feedback#\\n\\n#task#\"\n            }\n        ],\n\n        \"notImplemented\": [\n            {\n                \"rhs\": \"TODO\"\n            }\n        ],\n\n        \"task\": [\n            {\n                \"rhs\": \"Your task is to: find two statue and put them in diningtable.\"\n            }\n        ],\n\n        \"GotoLocation.feedback\": [\n            {\n                \"rhs\": \"You arrive at {r.name}. #examineReceptacle.feedback#\"\n            }\n        ],\n\n        \"OpenObject.feedback\": [\n            {\n                \"rhs\": \"You open the {r.name}. #examineReceptacle.feedback#\"\n            }\n        ],\n\n        \"CloseObject.feedback\": [\n            {\n                \"rhs\": \"You close the {r.name}.\"\n            }\n        ],\n\n        \"PickupObject.feedback\": [\n            {\n                \"rhs\": \"You pick up the {o.name} from the {r.name}.\"\n            }\n        ],\n\n        \"PickupObjectFromReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PickupObjectFromReceptacleObject: You pick up the {o.name}.\"\n            }\n        ],\n\n        \"PickupEmptyReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PickupEmptyReceptacleObject: You pick up the {o.name}.\"\n            }\n        ],\n\n        \"PickupFullReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PickupFullReceptacleObject: You pick up the {outerr.name}.\"\n            }\n        ],\n\n        \"PutObject.feedback\": [\n            {\n                \"rhs\": \"You move the {o.name} to the {r.name}.\"\n            }\n        ],\n\n        \"PutObjectInReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PutObjectInReceptacleObject: You put the {o.name} in the {outero.name}.\"\n            }\n        ],\n\n        \"PutEmptyReceptacleObjectinReceptacle.feedback\": [\n            {\n                \"rhs\": \"PutEmptyReceptacleObjectinReceptacle: You put the {o.name} in the {r.name}.\"\n            }\n        ],\n\n        \"PutFullReceptacleObjectInReceptacle.feedback\": [\n            {\n                \"rhs\": \"PutFullReceptacleObjectInReceptacle: You put the {outero.name} in the {r.name}.\"\n            }\n        ],\n\n        \"inventory.feedback\": [\n            {\n                \"condition\": \"holdsany(a:agent)\",\n                \"rhs\": \"You are carrying: [{o.indefinite + ' ' + o.name | holds(a:agent, o:object)}].\"\n            },\n            {\n                \"rhs\": \"You are not carrying anything.\"\n            }\n        ],\n\n        \"examineReceptacle.feedback\": [\n            {\n                \"condition\": \"openable(r:receptacle) & opened(r:receptacle)\",\n                \"rhs\": \"The {r.name} is open. In it, you see [{o.indefinite + ' ' + o.name | inreceptacle(o:object, r:receptacle)}].\"\n            },\n            {\n                \"condition\": \"openable(r:receptacle)\",\n                \"rhs\": \"The {r.name} is closed.\"\n            },\n            {\n                \"rhs\": \"On the {r.name}, you see [{o.indefinite + ' ' + o.name | inreceptacle(o:object, r:receptacle)}].\"\n            }\n        ],\n\n        \"examineObject.feedback\": [\n            {\n                \"condition\": \"isreceptacleobject(o:object)\",\n                \"rhs\": \"This is a normal {o.name}. In it, you see [{o2.indefinite + ' ' + o2.name | inreceptacleobject(o2:object, o:object)}].\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & ishot(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a hot and clean sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & iscool(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a cool and clean sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a clean sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"ishot(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a hot sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"iscool(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a cool sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & ishot(o:object)\",\n                \"rhs\": \"This is a hot and clean {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & iscool(o:object)\",\n                \"rhs\": \"This is a cool and clean {o.name}.\"\n            },\n            {\n                \"condition\": \"ishot(o:object)\",\n                \"rhs\": \"This is a hot {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object)\",\n                \"rhs\": \"This is a clean {o.name}.\"\n            },\n            {\n                \"condition\": \"iscool(o:object)\",\n                \"rhs\": \"This is a cold {o.name}.\"\n            },\n            {\n                \"condition\": \"toggleable(o:object) & istoggled(o:object)\",\n                \"rhs\": \"This {o.name} is on.\"\n            },\n            {\n                \"condition\": \"toggleable(o:object) & not_istoggled(o:object)\",\n                \"rhs\": \"This {o.name} is off.\"\n            },\n            {\n                \"condition\": \"sliceable(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a sliced {o.name}.\"\n            },\n            {\n                \"rhs\": \"There's nothing special about {o.name}.\"\n            }\n        ]\n    }\n\"\"\";\n\naction GotoLocation {\n    template :: \"go to [{r.name | receptacleatlocation(r:receptacle, lend:location)}]\";\n    feedback :: \"#GotoLocation.feedback#\";\n}\n\naction OpenObject {\n    template :: \"open {r}\";\n    feedback :: \"#OpenObject.feedback#\";\n}\n\naction CloseObject {\n    template :: \"close {r}\";\n    feedback :: \"#CloseObject.feedback#\";\n}\n\naction PickupObject {\n    template :: \"take {o} from {r}\";\n    feedback :: \"#PickupObject.feedback#\";\n}\n\naction PickupObjectFromReceptacleObject {\n    template :: \"take {o} from {r}\";\n    feedback :: \"#PickupObjectFromReceptacleObject.feedback#\";\n}\n\naction PickupEmptyReceptacleObject {\n    template :: \"take {o} from {r}\";\n    feedback :: \"#PickupEmptyReceptacleObject.feedback#\";\n}\n\naction PickupFullReceptacleObject {\n    template :: \"take {outerr} from {r}\";\n    feedback :: \"#PickupFullReceptacleObject.feedback#\";\n}\n\naction PutObject {\n    template :: \"move {o} to {r}\";\n    feedback :: \"#PutObject.feedback#\";\n}\n\naction PutObjectInReceptacleObject {\n    template :: \"put {o} into {outero}\";\n    feedback :: \"#PutObjectInReceptacleObject.feedback#\";\n}\n\naction PutEmptyReceptacleObjectinReceptacle {\n    template :: \"move {o} to {r}\";\n    feedback :: \"#PutEmptyReceptacleObjectinReceptacle.feedback#\";\n}\n\naction PutFullReceptacleObjectInReceptacle {\n    template :: \"put {outero} in {r}\";\n    feedback :: \"#PutFullReceptacleObjectInReceptacle.feedback#\";\n}\n\naction inventory {\n    template :: \"inventory\";\n    feedback :: \"#inventory.feedback#\";\n}\n\naction examineReceptacle {\n    template :: \"examine {r}\";\n    feedback :: \"#examineReceptacle.feedback#\";\n}\n\naction examineObject {\n    template :: \"examine {o}\";\n    feedback :: \"#examineObject.feedback#\";\n}\n\naction ToggleObject {\n    template :: \"use {o}\";\n    feedback :: \"#toggleObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"toggleObject.feedback\": [\n                {\n                    \"condition\": \"toggleable(o:object) & istoggled(o:object)\",\n                    \"rhs\": \"You turn on the {o.name}.\"\n                },\n                {\n                    \"condition\": \"toggleable(o:object)\",\n                    \"rhs\": \"You turn off the {o.name}.\"\n                },\n                {\n                    \"rhs\": \"You don't see any switch on the {o.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction HeatObject {\n    template :: \"heat {o} with {r}\";\n    feedback :: \"#heatObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"heatObject.feedback\": [\n                {\n                    \"rhs\": \"You heat the {o.name} using the {r.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction CleanObject {\n    template :: \"clean {o} with {r}\";\n    feedback :: \"#cleanObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"cleanObject.feedback\": [\n                {\n                    \"rhs\": \"You clean the {o.name} using the {r.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction CoolObject {\n    template :: \"cool {o} with {r}\";\n    feedback :: \"#coolObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"coolObject.feedback\": [\n                {\n                    \"rhs\": \"You cool the {o.name} using the {r.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction SliceObject {\n    template :: \"slice {co} with {ko}\";\n    feedback :: \"#sliceObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"sliceObject.feedback\": [\n                {\n                    \"rhs\": \"You sliced the {co.name} with the {ko.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction look {\n    template :: \"look\";\n    feedback :: \"#look.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"look.feedback\": [\n                {\n                   \"condition\": \"atlocation(a:agent, l:location) & receptacleatlocation(r:receptacle, l:location)\",\n                   \"rhs\": \"#look-variations#. Next to it, you see #list_objects_on_the_floor#.\"\n                },\n                {\n                    \"rhs\": \"You are in the middle of a room. Looking quickly around you, you see #list_appliances#.\"\n                }\n            ],\n\n            \"look-variations\": [\n                {\n                    \"rhs\": \"You are facing the [{r.name | atlocation(a:agent, l:location) & receptacleatlocation(r:receptacle, l:location)}]\"\n                }\n            ],\n\n            \"list_objects_on_the_floor\": [\n                {\n                    \"condition\": \"atlocation(a:agent, l:location) & objectatlocation(o:object, l:location) & receptacleatlocation(r:receptacle, l:location) & not_inreceptacle(o:object, r:receptacle)\",\n                    \"rhs\": \"[{#overview(o)# | atlocation(a:agent, l:location) & objectatlocation(o:object, l:location) & receptacleatlocation(r:receptacle, l:location) & not_inreceptacle(o:object, r:receptacle)}]\"\n                },\n                {\n                    \"rhs\": \"nothing\"\n                }\n            ],\n\n            \"list_appliances\": [\n                {\n                    \"condition\": \"receptacleatlocation(r:receptacle, l:location)\",\n                    \"rhs\": \"[{#overview(r)# | receptacleatlocation(r:receptacle, l:location)}]\"\n                },\n                {\n                    \"rhs\": \"nothing\"\n                }\n            ],\n\n            \"overview(o, r)\": [\n                {\n                    \"rhs\": \"{o.indefinite} {o.name} (in/on the {r.name})}\"\n                }\n            ],\n\n            \"overview(o)\": [\n                {\n                    \"rhs\": \"{o.indefinite} {o.name}\"\n                }\n            ],\n            \"overview(r)\": [\n                {\n                    \"rhs\": \"{r.indefinite} {r.name or r.id}\"\n                }\n            ],\n            \"overview_with_state(r)\": [\n                {\n                    \"rhs\": \"{r.indefinite} {r.name or r.id}#overview_state(r)#\"\n                }\n            ],\n            \"overview_state(r)\": [\n                {\n                    \"condition\": \"openable(r:receptacle) & opened(r:receptacle)\",\n                    \"rhs\": \" (it is open)\"\n                },\n                {\n                    \"condition\": \"openable(r:receptacle)\",\n                    \"rhs\": \" (it is closed)\"\n                },\n                {\n                    \"rhs\": \"\"\n                }\n            ],\n\n            \"list_empty\": [\n                {\n                    \"rhs\": \"nothing\"\n                }\n            ],\n            \"list_separator\": [\n                {\n                    \"rhs\": \", \"\n                }\n            ],\n            \"list_last_separator\": [\n                {\n                    \"rhs\": \", and \"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction help {\n    template :: \"help\";\n    feedback :: \"\nAvailable commands:\n  look:                             look around your current location\n  inventory:                        check your current inventory\n  go to (receptacle):               move to a receptacle\n  open (receptacle):                open a receptacle\n  close (receptacle):               close a receptacle\n  take (object) from (receptacle):  take an object from a receptacle\n  move (object) to (receptacle):  place an object in or on a receptacle\n  examine (something):              examine a receptacle or an object\n  use (object):                     use an object\n  heat (object) with (receptacle):  heat an object using a receptacle\n  clean (object) with (receptacle): clean an object using a receptacle\n  cool (object) with (receptacle):  cool an object using a receptacle\n  slice (object) with (object):     slice an object using a sharp object\n\";\n}\n", "pddl_problem": "\n(define (problem plan_trial_T20190906_222607_743673)\n(:domain alfred)\n(:objects\nagent1 - agent\n<PERSON><PERSON><PERSON><PERSON> - object\n        HousePlant - object\n        Candle - object\n        SprayBottle - object\n        Bowl - object\n        Window - object\n        CD - object\n        Egg - object\n        Glassbottle - object\n        Sink - object\n        Pillow - object\n        Spoon - object\n        SoapBottle - object\n        TeddyBear - object\n        ButterKnife - object\n        Cup - object\n        Plate - object\n        RemoteControl - object\n        Tomato - object\n        Statue - object\n        HandTowel - object\n        Knife - object\n        StoveKnob - object\n        LightSwitch - object\n        Pen - object\n        Painting - object\n        DishSponge - object\n        Vase - object\n        Mug - object\n        ToiletPaper - object\n        Box - object\n        Mirror - object\n        Ladle - object\n        Fork - object\n        Blinds - object\n        Footstool - object\n        KeyChain - object\n        Cloth - object\n        Laptop - object\n        TissueBox - object\n        PepperShaker - object\n        FloorLamp - object\n        WateringCan - object\n        Apple - object\n        Pan - object\n        PaperTowel - object\n        PaperTowelRoll - object\n        Newspaper - object\n        Television - object\n        Chair - object\n        CellPhone - object\n        CreditCard - object\n        Lettuce - object\n        BasketBall - object\n        Potato - object\n        Curtains - object\n        Boots - object\n        Pencil - object\n        AlarmClock - object\n        ToiletPaperRoll - object\n        Spatula - object\n        Book - object\n        Bread - object\n        SoapBar - object\n        Watch - object\n        DeskLamp - object\n        Kettle - object\n        Pot - object\n        ScrubBrush - object\n        WineBottle - object\n        ShowerDoor - object\n        Bathtub - object\n        LaundryHamperLid - object\n        ShowerGlass - object\n        Poster - object\n        TennisRacket - object\n        BaseballBat - object\n        Towel - object\n        Plunger - object\nSaltShakerType - otype\n        HousePlantType - otype\n        CandleType - otype\n        SprayBottleType - otype\n        BowlType - otype\n        WindowType - otype\n        CDType - otype\n        EggType - otype\n        GlassbottleType - otype\n        SinkType - otype\n        PillowType - otype\n        SpoonType - otype\n        SoapBottleType - otype\n        TeddyBearType - otype\n        ButterKnifeType - otype\n        CupType - otype\n        PlateType - otype\n        RemoteControlType - otype\n        TomatoType - otype\n        StatueType - otype\n        HandTowelType - otype\n        KnifeType - otype\n        StoveKnobType - otype\n        LightSwitchType - otype\n        PenType - otype\n        PaintingType - otype\n        DishSpongeType - otype\n        VaseType - otype\n        MugType - otype\n        ToiletPaperType - otype\n        BoxType - otype\n        MirrorType - otype\n        LadleType - otype\n        ForkType - otype\n        BlindsType - otype\n        FootstoolType - otype\n        KeyChainType - otype\n        ClothType - otype\n        LaptopType - otype\n        TissueBoxType - otype\n        PepperShakerType - otype\n        FloorLampType - otype\n        WateringCanType - otype\n        AppleType - otype\n        PanType - otype\n        PaperTowelType - otype\n        PaperTowelRollType - otype\n        NewspaperType - otype\n        TelevisionType - otype\n        ChairType - otype\n        CellPhoneType - otype\n        CreditCardType - otype\n        LettuceType - otype\n        BasketBallType - otype\n        PotatoType - otype\n        CurtainsType - otype\n        BootsType - otype\n        PencilType - otype\n        AlarmClockType - otype\n        ToiletPaperRollType - otype\n        SpatulaType - otype\n        BookType - otype\n        BreadType - otype\n        SoapBarType - otype\n        WatchType - otype\n        DeskLampType - otype\n        KettleType - otype\n        PotType - otype\n        ScrubBrushType - otype\n        WineBottleType - otype\n        ShowerDoorType - otype\n        BathtubType - otype\n        LaundryHamperLidType - otype\n        ShowerGlassType - otype\n        PosterType - otype\n        TennisRacketType - otype\n        BaseballBatType - otype\n        TowelType - otype\n        PlungerType - otype\nSafeType - rtype\n        DrawerType - rtype\n        CoffeeMachineType - rtype\n        HandTowelHolderType - rtype\n        SinkBasinType - rtype\n        DresserType - rtype\n        LaundryHamperType - rtype\n        TVStandType - rtype\n        BathtubBasinType - rtype\n        CabinetType - rtype\n        FridgeType - rtype\n        DeskType - rtype\n        ToiletType - rtype\n        CartType - rtype\n        SideTableType - rtype\n        SofaType - rtype\n        CoffeeTableType - rtype\n        DiningTableType - rtype\n        CounterTopType - rtype\n        GarbageCanType - rtype\n        ArmChairType - rtype\n        ShelfType - rtype\n        MicrowaveType - rtype\n        ToasterType - rtype\n        BedType - rtype\n        PaintingHangerType - rtype\n        TowelHolderType - rtype\n        ToiletPaperHangerType - rtype\n        StoveBurnerType - rtype\n        OttomanType - rtype\n\n\n        Box_bar__minus_01_dot_26_bar__plus_01_dot_01_bar__plus_01_dot_75 - object\n        Box_bar__minus_01_dot_53_bar__plus_01_dot_01_bar__plus_01_dot_75 - object\n        Chair_bar__minus_00_dot_35_bar__minus_00_dot_01_bar__plus_01_dot_66 - object\n        Chair_bar__minus_00_dot_67_bar__minus_00_dot_01_bar__plus_00_dot_66 - object\n        Chair_bar__minus_01_dot_93_bar__minus_00_dot_01_bar__plus_02_dot_41 - object\n        Chair_bar__minus_05_dot_15_bar__minus_00_dot_01_bar__plus_00_dot_82 - object\n        CreditCard_bar__minus_03_dot_68_bar__plus_00_dot_64_bar__plus_04_dot_69 - object\n        FloorLamp_bar__minus_05_dot_66_bar__minus_00_dot_01_bar__plus_04_dot_68 - object\n        HousePlant_bar__minus_05_dot_71_bar__plus_00_dot_58_bar__plus_03_dot_94 - object\n        KeyChain_bar__minus_02_dot_36_bar__plus_00_dot_63_bar__plus_04_dot_61 - object\n        KeyChain_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_01_dot_90 - object\n        Laptop_bar__minus_03_dot_79_bar__plus_00_dot_64_bar__plus_04_dot_35 - object\n        Laptop_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_19 - object\n        LightSwitch_bar__plus_00_dot_00_bar__plus_01_dot_29_bar__plus_03_dot_38 - object\n        Newspaper_bar__minus_01_dot_19_bar__plus_00_dot_83_bar__plus_01_dot_44 - object\n        Newspaper_bar__minus_03_dot_89_bar__plus_00_dot_65_bar__plus_04_dot_81 - object\n        Newspaper_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_66 - object\n        Painting_bar__minus_01_dot_62_bar__plus_01_dot_57_bar_00_dot_00 - object\n        Painting_bar__minus_04_dot_16_bar__plus_01_dot_66_bar_00_dot_00 - object\n        Pillow_bar__minus_04_dot_16_bar__plus_00_dot_54_bar__plus_00_dot_58 - object\n        Plate_bar__minus_02_dot_69_bar__plus_00_dot_64_bar__plus_04_dot_85 - object\n        RemoteControl_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_02_dot_72 - object\n        RemoteControl_bar__minus_03_dot_98_bar__plus_00_dot_43_bar__plus_02_dot_88 - object\n        Statue_bar__minus_02_dot_69_bar__plus_00_dot_63_bar__plus_04_dot_61 - object\n        Statue_bar__minus_04_dot_06_bar__plus_00_dot_44_bar__plus_02_dot_23 - object\n        Television_bar__minus_03_dot_76_bar__plus_01_dot_13_bar__plus_04_dot_58 - object\n        Vase_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_02_dot_39 - object\n        Watch_bar__minus_01_dot_64_bar__plus_00_dot_82_bar__plus_01_dot_46 - object\n        Watch_bar__minus_03_dot_98_bar__plus_00_dot_43_bar__plus_02_dot_72 - object\n        Watch_bar__minus_04_dot_14_bar__plus_00_dot_43_bar__plus_02_dot_55 - object\n        Window_bar__minus_06_dot_04_bar__plus_01_dot_53_bar__plus_00_dot_86 - object\n        Window_bar__minus_06_dot_04_bar__plus_01_dot_53_bar__plus_04_dot_24 - object\n        Window_bar__minus_06_dot_04_bar__plus_01_dot_55_bar__plus_02_dot_10 - object\n        CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45 - receptacle\n        DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45 - receptacle\n        Drawer_bar__minus_02_dot_56_bar__plus_00_dot_53_bar__plus_04_dot_48 - receptacle\n        GarbageCan_bar__minus_00_dot_22_bar__minus_00_dot_01_bar__plus_04_dot_73 - receptacle\n        SideTable_bar__minus_02_dot_56_bar__minus_00_dot_01_bar__plus_04_dot_74 - receptacle\n        SideTable_bar__minus_03_dot_79_bar__minus_00_dot_01_bar__plus_04_dot_46 - receptacle\n        Sofa_bar__minus_03_dot_66_bar__minus_00_dot_02_bar__plus_00_dot_46 - receptacle\n        Sofa_bar__minus_05_dot_40_bar__minus_00_dot_02_bar__plus_02_dot_43 - receptacle\n        loc_bar__minus_8_bar_12_bar_2_bar_60 - location\n        loc_bar__minus_11_bar_9_bar_3_bar_45 - location\n        loc_bar__minus_19_bar_18_bar_1_bar_60 - location\n        loc_bar__minus_12_bar_15_bar_0_bar_60 - location\n        loc_bar__minus_6_bar_17_bar_3_bar_60 - location\n        loc_bar__minus_2_bar_14_bar_1_bar_30 - location\n        loc_bar__minus_20_bar_18_bar_3_bar_60 - location\n        loc_bar__minus_10_bar_5_bar_2_bar_0 - location\n        loc_bar__minus_3_bar_9_bar_2_bar_45 - location\n        loc_bar__minus_5_bar_17_bar_1_bar_60 - location\n        loc_bar__minus_20_bar_16_bar_3_bar_60 - location\n        loc_bar__minus_19_bar_8_bar_3_bar_0 - location\n        loc_bar__minus_13_bar_7_bar_2_bar_45 - location\n        loc_bar__minus_19_bar_5_bar_3_bar_0 - location\n        loc_bar__minus_18_bar_10_bar_3_bar_60 - location\n        loc_bar__minus_2_bar_9_bar_2_bar_60 - location\n        loc_bar__minus_10_bar_6_bar_1_bar_60 - location\n        loc_bar__minus_17_bar_5_bar_2_bar_0 - location\n        loc_bar__minus_20_bar_17_bar_3_bar_0 - location\n        loc_bar__minus_19_bar_5_bar_2_bar_60 - location\n        loc_bar__minus_15_bar_14_bar_1_bar_30 - location\n        )\n    \n\n(:init\n\n\n        (receptacleType Sofa_bar__minus_05_dot_40_bar__minus_00_dot_02_bar__plus_02_dot_43 SofaType)\n        (receptacleType Drawer_bar__minus_02_dot_56_bar__plus_00_dot_53_bar__plus_04_dot_48 DrawerType)\n        (receptacleType SideTable_bar__minus_03_dot_79_bar__minus_00_dot_01_bar__plus_04_dot_46 SideTableType)\n        (receptacleType Sofa_bar__minus_03_dot_66_bar__minus_00_dot_02_bar__plus_00_dot_46 SofaType)\n        (receptacleType GarbageCan_bar__minus_00_dot_22_bar__minus_00_dot_01_bar__plus_04_dot_73 GarbageCanType)\n        (receptacleType DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45 DiningTableType)\n        (receptacleType SideTable_bar__minus_02_dot_56_bar__minus_00_dot_01_bar__plus_04_dot_74 SideTableType)\n        (receptacleType CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45 CoffeeTableType)\n        (objectType KeyChain_bar__minus_02_dot_36_bar__plus_00_dot_63_bar__plus_04_dot_61 KeyChainType)\n        (objectType Newspaper_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_66 NewspaperType)\n        (objectType RemoteControl_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_02_dot_72 RemoteControlType)\n        (objectType Laptop_bar__minus_03_dot_79_bar__plus_00_dot_64_bar__plus_04_dot_35 LaptopType)\n        (objectType Window_bar__minus_06_dot_04_bar__plus_01_dot_53_bar__plus_04_dot_24 WindowType)\n        (objectType Newspaper_bar__minus_03_dot_89_bar__plus_00_dot_65_bar__plus_04_dot_81 NewspaperType)\n        (objectType Watch_bar__minus_03_dot_98_bar__plus_00_dot_43_bar__plus_02_dot_72 WatchType)\n        (objectType Television_bar__minus_03_dot_76_bar__plus_01_dot_13_bar__plus_04_dot_58 TelevisionType)\n        (objectType Newspaper_bar__minus_01_dot_19_bar__plus_00_dot_83_bar__plus_01_dot_44 NewspaperType)\n        (objectType CreditCard_bar__minus_03_dot_68_bar__plus_00_dot_64_bar__plus_04_dot_69 CreditCardType)\n        (objectType Window_bar__minus_06_dot_04_bar__plus_01_dot_53_bar__plus_00_dot_86 WindowType)\n        (objectType Pillow_bar__minus_04_dot_16_bar__plus_00_dot_54_bar__plus_00_dot_58 PillowType)\n        (objectType Watch_bar__minus_04_dot_14_bar__plus_00_dot_43_bar__plus_02_dot_55 WatchType)\n        (objectType Chair_bar__minus_01_dot_93_bar__minus_00_dot_01_bar__plus_02_dot_41 ChairType)\n        (objectType Box_bar__minus_01_dot_26_bar__plus_01_dot_01_bar__plus_01_dot_75 BoxType)\n        (objectType Plate_bar__minus_02_dot_69_bar__plus_00_dot_64_bar__plus_04_dot_85 PlateType)\n        (objectType Statue_bar__minus_02_dot_69_bar__plus_00_dot_63_bar__plus_04_dot_61 StatueType)\n        (objectType Laptop_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_19 LaptopType)\n        (objectType Vase_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_02_dot_39 VaseType)\n        (objectType FloorLamp_bar__minus_05_dot_66_bar__minus_00_dot_01_bar__plus_04_dot_68 FloorLampType)\n        (objectType Window_bar__minus_06_dot_04_bar__plus_01_dot_55_bar__plus_02_dot_10 WindowType)\n        (objectType KeyChain_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_01_dot_90 KeyChainType)\n        (objectType Chair_bar__minus_05_dot_15_bar__minus_00_dot_01_bar__plus_00_dot_82 ChairType)\n        (objectType LightSwitch_bar__plus_00_dot_00_bar__plus_01_dot_29_bar__plus_03_dot_38 LightSwitchType)\n        (objectType Box_bar__minus_01_dot_53_bar__plus_01_dot_01_bar__plus_01_dot_75 BoxType)\n        (objectType Watch_bar__minus_01_dot_64_bar__plus_00_dot_82_bar__plus_01_dot_46 WatchType)\n        (objectType RemoteControl_bar__minus_03_dot_98_bar__plus_00_dot_43_bar__plus_02_dot_88 RemoteControlType)\n        (objectType Statue_bar__minus_04_dot_06_bar__plus_00_dot_44_bar__plus_02_dot_23 StatueType)\n        (objectType Chair_bar__minus_00_dot_67_bar__minus_00_dot_01_bar__plus_00_dot_66 ChairType)\n        (objectType HousePlant_bar__minus_05_dot_71_bar__plus_00_dot_58_bar__plus_03_dot_94 HousePlantType)\n        (objectType Painting_bar__minus_01_dot_62_bar__plus_01_dot_57_bar_00_dot_00 PaintingType)\n        (objectType Chair_bar__minus_00_dot_35_bar__minus_00_dot_01_bar__plus_01_dot_66 ChairType)\n        (objectType Painting_bar__minus_04_dot_16_bar__plus_01_dot_66_bar_00_dot_00 PaintingType)\n        (canContain SofaType BoxType)\n        (canContain SofaType LaptopType)\n        (canContain SofaType PillowType)\n        (canContain SofaType RemoteControlType)\n        (canContain SofaType KeyChainType)\n        (canContain SofaType NewspaperType)\n        (canContain SofaType CreditCardType)\n        (canContain DrawerType WatchType)\n        (canContain DrawerType NewspaperType)\n        (canContain DrawerType KeyChainType)\n        (canContain DrawerType CreditCardType)\n        (canContain DrawerType RemoteControlType)\n        (canContain SideTableType WatchType)\n        (canContain SideTableType NewspaperType)\n        (canContain SideTableType VaseType)\n        (canContain SideTableType BoxType)\n        (canContain SideTableType KeyChainType)\n        (canContain SideTableType CreditCardType)\n        (canContain SideTableType LaptopType)\n        (canContain SideTableType PlateType)\n        (canContain SideTableType RemoteControlType)\n        (canContain SideTableType StatueType)\n        (canContain SofaType BoxType)\n        (canContain SofaType LaptopType)\n        (canContain SofaType PillowType)\n        (canContain SofaType RemoteControlType)\n        (canContain SofaType KeyChainType)\n        (canContain SofaType NewspaperType)\n        (canContain SofaType CreditCardType)\n        (canContain GarbageCanType NewspaperType)\n        (canContain DiningTableType WatchType)\n        (canContain DiningTableType NewspaperType)\n        (canContain DiningTableType VaseType)\n        (canContain DiningTableType BoxType)\n        (canContain DiningTableType KeyChainType)\n        (canContain DiningTableType CreditCardType)\n        (canContain DiningTableType LaptopType)\n        (canContain DiningTableType PlateType)\n        (canContain DiningTableType RemoteControlType)\n        (canContain DiningTableType StatueType)\n        (canContain SideTableType WatchType)\n        (canContain SideTableType NewspaperType)\n        (canContain SideTableType VaseType)\n        (canContain SideTableType BoxType)\n        (canContain SideTableType KeyChainType)\n        (canContain SideTableType CreditCardType)\n        (canContain SideTableType LaptopType)\n        (canContain SideTableType PlateType)\n        (canContain SideTableType RemoteControlType)\n        (canContain SideTableType StatueType)\n        (canContain CoffeeTableType WatchType)\n        (canContain CoffeeTableType NewspaperType)\n        (canContain CoffeeTableType VaseType)\n        (canContain CoffeeTableType BoxType)\n        (canContain CoffeeTableType KeyChainType)\n        (canContain CoffeeTableType CreditCardType)\n        (canContain CoffeeTableType LaptopType)\n        (canContain CoffeeTableType PlateType)\n        (canContain CoffeeTableType RemoteControlType)\n        (canContain CoffeeTableType StatueType)\n        (pickupable KeyChain_bar__minus_02_dot_36_bar__plus_00_dot_63_bar__plus_04_dot_61)\n        (pickupable Newspaper_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_66)\n        (pickupable RemoteControl_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_02_dot_72)\n        (pickupable Laptop_bar__minus_03_dot_79_bar__plus_00_dot_64_bar__plus_04_dot_35)\n        (pickupable Newspaper_bar__minus_03_dot_89_bar__plus_00_dot_65_bar__plus_04_dot_81)\n        (pickupable Watch_bar__minus_03_dot_98_bar__plus_00_dot_43_bar__plus_02_dot_72)\n        (pickupable Newspaper_bar__minus_01_dot_19_bar__plus_00_dot_83_bar__plus_01_dot_44)\n        (pickupable CreditCard_bar__minus_03_dot_68_bar__plus_00_dot_64_bar__plus_04_dot_69)\n        (pickupable Pillow_bar__minus_04_dot_16_bar__plus_00_dot_54_bar__plus_00_dot_58)\n        (pickupable Watch_bar__minus_04_dot_14_bar__plus_00_dot_43_bar__plus_02_dot_55)\n        (pickupable Box_bar__minus_01_dot_26_bar__plus_01_dot_01_bar__plus_01_dot_75)\n        (pickupable Plate_bar__minus_02_dot_69_bar__plus_00_dot_64_bar__plus_04_dot_85)\n        (pickupable Statue_bar__minus_02_dot_69_bar__plus_00_dot_63_bar__plus_04_dot_61)\n        (pickupable Laptop_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_19)\n        (pickupable Vase_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_02_dot_39)\n        (pickupable KeyChain_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_01_dot_90)\n        (pickupable Box_bar__minus_01_dot_53_bar__plus_01_dot_01_bar__plus_01_dot_75)\n        (pickupable Watch_bar__minus_01_dot_64_bar__plus_00_dot_82_bar__plus_01_dot_46)\n        (pickupable RemoteControl_bar__minus_03_dot_98_bar__plus_00_dot_43_bar__plus_02_dot_88)\n        (pickupable Statue_bar__minus_04_dot_06_bar__plus_00_dot_44_bar__plus_02_dot_23)\n        (isReceptacleObject Box_bar__minus_01_dot_26_bar__plus_01_dot_01_bar__plus_01_dot_75)\n        (isReceptacleObject Plate_bar__minus_02_dot_69_bar__plus_00_dot_64_bar__plus_04_dot_85)\n        (isReceptacleObject Box_bar__minus_01_dot_53_bar__plus_01_dot_01_bar__plus_01_dot_75)\n        (openable Drawer_bar__minus_02_dot_56_bar__plus_00_dot_53_bar__plus_04_dot_48)\n        \n        (atLocation agent1 loc_bar__minus_15_bar_14_bar_1_bar_30)\n        \n        (cleanable Plate_bar__minus_02_dot_69_bar__plus_00_dot_64_bar__plus_04_dot_85)\n        \n        (heatable Plate_bar__minus_02_dot_69_bar__plus_00_dot_64_bar__plus_04_dot_85)\n        (coolable Plate_bar__minus_02_dot_69_bar__plus_00_dot_64_bar__plus_04_dot_85)\n        \n        \n        (toggleable FloorLamp_bar__minus_05_dot_66_bar__minus_00_dot_01_bar__plus_04_dot_68)\n        \n        \n        \n        \n        (inReceptacle Pillow_bar__minus_04_dot_16_bar__plus_00_dot_54_bar__plus_00_dot_58 Sofa_bar__minus_03_dot_66_bar__minus_00_dot_02_bar__plus_00_dot_46)\n        (inReceptacle Watch_bar__minus_01_dot_64_bar__plus_00_dot_82_bar__plus_01_dot_46 DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45)\n        (inReceptacle Newspaper_bar__minus_01_dot_19_bar__plus_00_dot_83_bar__plus_01_dot_44 DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45)\n        (inReceptacle Box_bar__minus_01_dot_26_bar__plus_01_dot_01_bar__plus_01_dot_75 DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45)\n        (inReceptacle Box_bar__minus_01_dot_53_bar__plus_01_dot_01_bar__plus_01_dot_75 DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45)\n        (inReceptacle Laptop_bar__minus_03_dot_79_bar__plus_00_dot_64_bar__plus_04_dot_35 SideTable_bar__minus_03_dot_79_bar__minus_00_dot_01_bar__plus_04_dot_46)\n        (inReceptacle CreditCard_bar__minus_03_dot_68_bar__plus_00_dot_64_bar__plus_04_dot_69 SideTable_bar__minus_03_dot_79_bar__minus_00_dot_01_bar__plus_04_dot_46)\n        (inReceptacle Newspaper_bar__minus_03_dot_89_bar__plus_00_dot_65_bar__plus_04_dot_81 SideTable_bar__minus_03_dot_79_bar__minus_00_dot_01_bar__plus_04_dot_46)\n        (inReceptacle Television_bar__minus_03_dot_76_bar__plus_01_dot_13_bar__plus_04_dot_58 SideTable_bar__minus_03_dot_79_bar__minus_00_dot_01_bar__plus_04_dot_46)\n        (inReceptacle Vase_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_02_dot_39 CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45)\n        (inReceptacle RemoteControl_bar__minus_03_dot_98_bar__plus_00_dot_43_bar__plus_02_dot_88 CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45)\n        (inReceptacle Watch_bar__minus_03_dot_98_bar__plus_00_dot_43_bar__plus_02_dot_72 CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45)\n        (inReceptacle Statue_bar__minus_04_dot_06_bar__plus_00_dot_44_bar__plus_02_dot_23 CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45)\n        (inReceptacle KeyChain_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_01_dot_90 CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45)\n        (inReceptacle RemoteControl_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_02_dot_72 CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45)\n        (inReceptacle Watch_bar__minus_04_dot_14_bar__plus_00_dot_43_bar__plus_02_dot_55 CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45)\n        (inReceptacle Newspaper_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_66 Sofa_bar__minus_05_dot_40_bar__minus_00_dot_02_bar__plus_02_dot_43)\n        (inReceptacle Laptop_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_19 Sofa_bar__minus_05_dot_40_bar__minus_00_dot_02_bar__plus_02_dot_43)\n        (inReceptacle KeyChain_bar__minus_02_dot_36_bar__plus_00_dot_63_bar__plus_04_dot_61 SideTable_bar__minus_02_dot_56_bar__minus_00_dot_01_bar__plus_04_dot_74)\n        (inReceptacle Plate_bar__minus_02_dot_69_bar__plus_00_dot_64_bar__plus_04_dot_85 SideTable_bar__minus_02_dot_56_bar__minus_00_dot_01_bar__plus_04_dot_74)\n        (inReceptacle Statue_bar__minus_02_dot_69_bar__plus_00_dot_63_bar__plus_04_dot_61 SideTable_bar__minus_02_dot_56_bar__minus_00_dot_01_bar__plus_04_dot_74)\n        \n        \n        (receptacleAtLocation CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45 loc_bar__minus_11_bar_9_bar_3_bar_45)\n        (receptacleAtLocation DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45 loc_bar__minus_10_bar_6_bar_1_bar_60)\n        (receptacleAtLocation Drawer_bar__minus_02_dot_56_bar__plus_00_dot_53_bar__plus_04_dot_48 loc_bar__minus_12_bar_15_bar_0_bar_60)\n        (receptacleAtLocation GarbageCan_bar__minus_00_dot_22_bar__minus_00_dot_01_bar__plus_04_dot_73 loc_bar__minus_5_bar_17_bar_1_bar_60)\n        (receptacleAtLocation SideTable_bar__minus_02_dot_56_bar__minus_00_dot_01_bar__plus_04_dot_74 loc_bar__minus_6_bar_17_bar_3_bar_60)\n        (receptacleAtLocation SideTable_bar__minus_03_dot_79_bar__minus_00_dot_01_bar__plus_04_dot_46 loc_bar__minus_19_bar_18_bar_1_bar_60)\n        (receptacleAtLocation Sofa_bar__minus_03_dot_66_bar__minus_00_dot_02_bar__plus_00_dot_46 loc_bar__minus_13_bar_7_bar_2_bar_45)\n        (receptacleAtLocation Sofa_bar__minus_05_dot_40_bar__minus_00_dot_02_bar__plus_02_dot_43 loc_bar__minus_18_bar_10_bar_3_bar_60)\n        (objectAtLocation Watch_bar__minus_01_dot_64_bar__plus_00_dot_82_bar__plus_01_dot_46 loc_bar__minus_10_bar_6_bar_1_bar_60)\n        (objectAtLocation Statue_bar__minus_02_dot_69_bar__plus_00_dot_63_bar__plus_04_dot_61 loc_bar__minus_6_bar_17_bar_3_bar_60)\n        (objectAtLocation Laptop_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_19 loc_bar__minus_18_bar_10_bar_3_bar_60)\n        (objectAtLocation RemoteControl_bar__minus_03_dot_98_bar__plus_00_dot_43_bar__plus_02_dot_88 loc_bar__minus_11_bar_9_bar_3_bar_45)\n        (objectAtLocation Newspaper_bar__minus_03_dot_89_bar__plus_00_dot_65_bar__plus_04_dot_81 loc_bar__minus_19_bar_18_bar_1_bar_60)\n        (objectAtLocation KeyChain_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_01_dot_90 loc_bar__minus_11_bar_9_bar_3_bar_45)\n        (objectAtLocation Box_bar__minus_01_dot_53_bar__plus_01_dot_01_bar__plus_01_dot_75 loc_bar__minus_10_bar_6_bar_1_bar_60)\n        (objectAtLocation Watch_bar__minus_04_dot_14_bar__plus_00_dot_43_bar__plus_02_dot_55 loc_bar__minus_11_bar_9_bar_3_bar_45)\n        (objectAtLocation Newspaper_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_66 loc_bar__minus_18_bar_10_bar_3_bar_60)\n        (objectAtLocation Box_bar__minus_01_dot_26_bar__plus_01_dot_01_bar__plus_01_dot_75 loc_bar__minus_10_bar_6_bar_1_bar_60)\n        (objectAtLocation Chair_bar__minus_01_dot_93_bar__minus_00_dot_01_bar__plus_02_dot_41 loc_bar__minus_8_bar_12_bar_2_bar_60)\n        (objectAtLocation Chair_bar__minus_05_dot_15_bar__minus_00_dot_01_bar__plus_00_dot_82 loc_bar__minus_19_bar_5_bar_2_bar_60)\n        (objectAtLocation Chair_bar__minus_00_dot_35_bar__minus_00_dot_01_bar__plus_01_dot_66 loc_bar__minus_2_bar_9_bar_2_bar_60)\n        (objectAtLocation Chair_bar__minus_00_dot_67_bar__minus_00_dot_01_bar__plus_00_dot_66 loc_bar__minus_3_bar_9_bar_2_bar_45)\n        (objectAtLocation KeyChain_bar__minus_02_dot_36_bar__plus_00_dot_63_bar__plus_04_dot_61 loc_bar__minus_6_bar_17_bar_3_bar_60)\n        (objectAtLocation Television_bar__minus_03_dot_76_bar__plus_01_dot_13_bar__plus_04_dot_58 loc_bar__minus_19_bar_18_bar_1_bar_60)\n        (objectAtLocation Newspaper_bar__minus_01_dot_19_bar__plus_00_dot_83_bar__plus_01_dot_44 loc_bar__minus_10_bar_6_bar_1_bar_60)\n        (objectAtLocation CreditCard_bar__minus_03_dot_68_bar__plus_00_dot_64_bar__plus_04_dot_69 loc_bar__minus_19_bar_18_bar_1_bar_60)\n        (objectAtLocation RemoteControl_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_02_dot_72 loc_bar__minus_11_bar_9_bar_3_bar_45)\n        (objectAtLocation FloorLamp_bar__minus_05_dot_66_bar__minus_00_dot_01_bar__plus_04_dot_68 loc_bar__minus_20_bar_18_bar_3_bar_60)\n        (objectAtLocation HousePlant_bar__minus_05_dot_71_bar__plus_00_dot_58_bar__plus_03_dot_94 loc_bar__minus_20_bar_16_bar_3_bar_60)\n        (objectAtLocation Laptop_bar__minus_03_dot_79_bar__plus_00_dot_64_bar__plus_04_dot_35 loc_bar__minus_19_bar_18_bar_1_bar_60)\n        (objectAtLocation Plate_bar__minus_02_dot_69_bar__plus_00_dot_64_bar__plus_04_dot_85 loc_bar__minus_6_bar_17_bar_3_bar_60)\n        (objectAtLocation LightSwitch_bar__plus_00_dot_00_bar__plus_01_dot_29_bar__plus_03_dot_38 loc_bar__minus_2_bar_14_bar_1_bar_30)\n        (objectAtLocation Pillow_bar__minus_04_dot_16_bar__plus_00_dot_54_bar__plus_00_dot_58 loc_bar__minus_13_bar_7_bar_2_bar_45)\n        (objectAtLocation Statue_bar__minus_04_dot_06_bar__plus_00_dot_44_bar__plus_02_dot_23 loc_bar__minus_11_bar_9_bar_3_bar_45)\n        (objectAtLocation Painting_bar__minus_04_dot_16_bar__plus_01_dot_66_bar_00_dot_00 loc_bar__minus_17_bar_5_bar_2_bar_0)\n        (objectAtLocation Painting_bar__minus_01_dot_62_bar__plus_01_dot_57_bar_00_dot_00 loc_bar__minus_10_bar_5_bar_2_bar_0)\n        (objectAtLocation Vase_bar__minus_03_dot_83_bar__plus_00_dot_43_bar__plus_02_dot_39 loc_bar__minus_11_bar_9_bar_3_bar_45)\n        (objectAtLocation Watch_bar__minus_03_dot_98_bar__plus_00_dot_43_bar__plus_02_dot_72 loc_bar__minus_11_bar_9_bar_3_bar_45)\n        (objectAtLocation Window_bar__minus_06_dot_04_bar__plus_01_dot_55_bar__plus_02_dot_10 loc_bar__minus_19_bar_8_bar_3_bar_0)\n        (objectAtLocation Window_bar__minus_06_dot_04_bar__plus_01_dot_53_bar__plus_00_dot_86 loc_bar__minus_19_bar_5_bar_3_bar_0)\n        (objectAtLocation Window_bar__minus_06_dot_04_bar__plus_01_dot_53_bar__plus_04_dot_24 loc_bar__minus_20_bar_17_bar_3_bar_0)\n        )\n    \n\n                (:goal\n                    (and\n                        (exists (?r - receptacle)\n                            (exists (?o1 - object)\n                                (and\n                                    (objectType ?o1 StatueType)\n                                    (receptacleType ?r DiningTableType)\n                                    (inReceptacle ?o1 ?r)\n                                    (exists (?o2 - object)\n                                        (and\n                                            (not (= ?o1 ?o2))\n                                            (objectType ?o2 StatueType)\n                                            (receptacleType ?r DiningTableType)\n                                            (inReceptacle ?o2 ?r)\n                                        )\n                                    )\n                                )\n                            )\n                        )\n                    )\n                )\n            )\n            ", "solvable": true, "walkthrough": ["go to coffeetable 1", "take statue 1 from coffeetable 1", "go to diningtable 1", "move statue 1 to diningtable 1", "go to sidetable 2", "take statue 2 from sidetable 2", "go to diningtable 1", "move statue 2 to diningtable 1"]}