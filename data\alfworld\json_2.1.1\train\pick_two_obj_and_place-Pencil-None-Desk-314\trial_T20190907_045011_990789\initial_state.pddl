
(define (problem plan_trial_T20190907_045011_990789)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON>haker - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__minus_00_dot_41_bar__plus_00_dot_93_bar__minus_01_dot_25 - object
        AlarmClock_bar__minus_00_dot_41_bar__plus_00_dot_93_bar__minus_01_dot_46 - object
        BasketBall_bar__minus_00_dot_24_bar__plus_00_dot_12_bar__plus_00_dot_17 - object
        Blinds_bar__plus_00_dot_70_bar__plus_01_dot_98_bar__minus_01_dot_87 - object
        Book_bar__plus_02_dot_33_bar__plus_00_dot_84_bar__minus_00_dot_88 - object
        Box_bar__plus_02_dot_15_bar__plus_00_dot_14_bar__plus_00_dot_49 - object
        CD_bar__minus_00_dot_13_bar__plus_00_dot_92_bar__minus_01_dot_03 - object
        CellPhone_bar__minus_00_dot_25_bar__plus_00_dot_09_bar__minus_00_dot_29 - object
        Chair_bar__plus_00_dot_01_bar__plus_00_dot_01_bar__minus_01_dot_24 - object
        CreditCard_bar__plus_02_dot_24_bar__plus_00_dot_84_bar__minus_00_dot_48 - object
        DeskLamp_bar__minus_00_dot_67_bar__plus_00_dot_92_bar__minus_00_dot_06 - object
        KeyChain_bar__plus_02_dot_20_bar__plus_00_dot_46_bar__minus_00_dot_68 - object
        KeyChain_bar__minus_00_dot_51_bar__plus_00_dot_93_bar__minus_00_dot_18 - object
        Laptop_bar__plus_00_dot_17_bar__plus_00_dot_55_bar__plus_01_dot_85 - object
        LightSwitch_bar__plus_00_dot_73_bar__plus_01_dot_20_bar__plus_02_dot_75 - object
        Mirror_bar__plus_02_dot_12_bar__plus_01_dot_28_bar__plus_02_dot_75 - object
        Painting_bar__minus_00_dot_79_bar__plus_01_dot_81_bar__plus_01_dot_02 - object
        Painting_bar__minus_00_dot_79_bar__plus_01_dot_81_bar__minus_00_dot_22 - object
        Pencil_bar__plus_02_dot_14_bar__plus_00_dot_85_bar__minus_00_dot_62 - object
        Pencil_bar__plus_02_dot_24_bar__plus_00_dot_85_bar__minus_00_dot_62 - object
        Pen_bar__minus_00_dot_23_bar__plus_00_dot_93_bar__minus_00_dot_39 - object
        Pen_bar__minus_00_dot_40_bar__plus_00_dot_38_bar__minus_00_dot_29 - object
        Pen_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_25 - object
        Pillow_bar__minus_00_dot_03_bar__plus_00_dot_56_bar__plus_02_dot_46 - object
        Pillow_bar__minus_00_dot_15_bar__plus_00_dot_62_bar__plus_00_dot_97 - object
        Window_bar__plus_00_dot_67_bar__plus_01_dot_36_bar__minus_01_dot_91 - object
        Bed_bar__plus_00_dot_01_bar__minus_00_dot_06_bar__plus_01_dot_61 - receptacle
        Desk_bar__minus_00_dot_38_bar__plus_00_dot_00_bar__minus_00_dot_95 - receptacle
        Drawer_bar__plus_02_dot_16_bar__plus_00_dot_21_bar__minus_00_dot_48 - receptacle
        Drawer_bar__plus_02_dot_16_bar__plus_00_dot_57_bar__minus_00_dot_48 - receptacle
        Drawer_bar__minus_00_dot_10_bar__plus_00_dot_22_bar__minus_00_dot_40 - receptacle
        Drawer_bar__minus_00_dot_10_bar__plus_00_dot_51_bar__minus_00_dot_40 - receptacle
        Drawer_bar__minus_00_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_40 - receptacle
        Dresser_bar__plus_02_dot_16_bar__plus_00_dot_57_bar__minus_00_dot_48 - receptacle
        GarbageCan_bar__plus_02_dot_26_bar__plus_00_dot_00_bar__plus_02_dot_60 - receptacle
        loc_bar_3_bar__minus_5_bar_2_bar__minus_30 - location
        loc_bar_3_bar__minus_2_bar_3_bar_45 - location
        loc_bar_6_bar__minus_2_bar_1_bar_60 - location
        loc_bar_1_bar_0_bar_3_bar_60 - location
        loc_bar_2_bar__minus_5_bar_3_bar_60 - location
        loc_bar_8_bar_9_bar_0_bar_60 - location
        loc_bar_6_bar_2_bar_1_bar_60 - location
        loc_bar_5_bar__minus_5_bar_1_bar_60 - location
        loc_bar_6_bar__minus_5_bar_0_bar_60 - location
        loc_bar_1_bar__minus_1_bar_3_bar__minus_15 - location
        loc_bar_3_bar__minus_5_bar_2_bar_30 - location
        loc_bar_2_bar__minus_5_bar_0_bar_60 - location
        loc_bar_4_bar_9_bar_0_bar_45 - location
        loc_bar_8_bar_9_bar_0_bar_30 - location
        loc_bar_3_bar__minus_5_bar_3_bar_60 - location
        loc_bar_4_bar_4_bar_3_bar_0 - location
        loc_bar_6_bar_6_bar_3_bar_45 - location
        loc_bar_6_bar_6_bar_2_bar_30 - location
        )
    

(:init


        (receptacleType Dresser_bar__plus_02_dot_16_bar__plus_00_dot_57_bar__minus_00_dot_48 DresserType)
        (receptacleType Drawer_bar__minus_00_dot_10_bar__plus_00_dot_51_bar__minus_00_dot_40 DrawerType)
        (receptacleType Drawer_bar__plus_02_dot_16_bar__plus_00_dot_57_bar__minus_00_dot_48 DrawerType)
        (receptacleType Desk_bar__minus_00_dot_38_bar__plus_00_dot_00_bar__minus_00_dot_95 DeskType)
        (receptacleType Drawer_bar__plus_02_dot_16_bar__plus_00_dot_21_bar__minus_00_dot_48 DrawerType)
        (receptacleType GarbageCan_bar__plus_02_dot_26_bar__plus_00_dot_00_bar__plus_02_dot_60 GarbageCanType)
        (receptacleType Drawer_bar__minus_00_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_40 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_10_bar__plus_00_dot_22_bar__minus_00_dot_40 DrawerType)
        (receptacleType Bed_bar__plus_00_dot_01_bar__minus_00_dot_06_bar__plus_01_dot_61 BedType)
        (objectType Pencil_bar__plus_02_dot_14_bar__plus_00_dot_85_bar__minus_00_dot_62 PencilType)
        (objectType Book_bar__plus_02_dot_33_bar__plus_00_dot_84_bar__minus_00_dot_88 BookType)
        (objectType Painting_bar__minus_00_dot_79_bar__plus_01_dot_81_bar__plus_01_dot_02 PaintingType)
        (objectType Pencil_bar__plus_02_dot_24_bar__plus_00_dot_85_bar__minus_00_dot_62 PencilType)
        (objectType AlarmClock_bar__minus_00_dot_41_bar__plus_00_dot_93_bar__minus_01_dot_46 AlarmClockType)
        (objectType Box_bar__plus_02_dot_15_bar__plus_00_dot_14_bar__plus_00_dot_49 BoxType)
        (objectType Blinds_bar__plus_00_dot_70_bar__plus_01_dot_98_bar__minus_01_dot_87 BlindsType)
        (objectType Pillow_bar__minus_00_dot_15_bar__plus_00_dot_62_bar__plus_00_dot_97 PillowType)
        (objectType CellPhone_bar__minus_00_dot_25_bar__plus_00_dot_09_bar__minus_00_dot_29 CellPhoneType)
        (objectType Pen_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_25 PenType)
        (objectType CreditCard_bar__plus_02_dot_24_bar__plus_00_dot_84_bar__minus_00_dot_48 CreditCardType)
        (objectType CD_bar__minus_00_dot_13_bar__plus_00_dot_92_bar__minus_01_dot_03 CDType)
        (objectType BasketBall_bar__minus_00_dot_24_bar__plus_00_dot_12_bar__plus_00_dot_17 BasketBallType)
        (objectType Painting_bar__minus_00_dot_79_bar__plus_01_dot_81_bar__minus_00_dot_22 PaintingType)
        (objectType Mirror_bar__plus_02_dot_12_bar__plus_01_dot_28_bar__plus_02_dot_75 MirrorType)
        (objectType KeyChain_bar__minus_00_dot_51_bar__plus_00_dot_93_bar__minus_00_dot_18 KeyChainType)
        (objectType Window_bar__plus_00_dot_67_bar__plus_01_dot_36_bar__minus_01_dot_91 WindowType)
        (objectType Pen_bar__minus_00_dot_23_bar__plus_00_dot_93_bar__minus_00_dot_39 PenType)
        (objectType DeskLamp_bar__minus_00_dot_67_bar__plus_00_dot_92_bar__minus_00_dot_06 DeskLampType)
        (objectType Pillow_bar__minus_00_dot_03_bar__plus_00_dot_56_bar__plus_02_dot_46 PillowType)
        (objectType KeyChain_bar__plus_02_dot_20_bar__plus_00_dot_46_bar__minus_00_dot_68 KeyChainType)
        (objectType LightSwitch_bar__plus_00_dot_73_bar__plus_01_dot_20_bar__plus_02_dot_75 LightSwitchType)
        (objectType AlarmClock_bar__minus_00_dot_41_bar__plus_00_dot_93_bar__minus_01_dot_25 AlarmClockType)
        (objectType Laptop_bar__plus_00_dot_17_bar__plus_00_dot_55_bar__plus_01_dot_85 LaptopType)
        (objectType Chair_bar__plus_00_dot_01_bar__plus_00_dot_01_bar__minus_01_dot_24 ChairType)
        (objectType Pen_bar__minus_00_dot_40_bar__plus_00_dot_38_bar__minus_00_dot_29 PenType)
        (canContain DresserType PenType)
        (canContain DresserType BookType)
        (canContain DresserType CDType)
        (canContain DresserType BoxType)
        (canContain DresserType CellPhoneType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType BasketBallType)
        (canContain DresserType LaptopType)
        (canContain DresserType PencilType)
        (canContain DresserType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DeskType PenType)
        (canContain DeskType BookType)
        (canContain DeskType CDType)
        (canContain DeskType BoxType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType CreditCardType)
        (canContain DeskType BasketBallType)
        (canContain DeskType LaptopType)
        (canContain DeskType PencilType)
        (canContain DeskType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain BedType BasketBallType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (pickupable Pencil_bar__plus_02_dot_14_bar__plus_00_dot_85_bar__minus_00_dot_62)
        (pickupable Book_bar__plus_02_dot_33_bar__plus_00_dot_84_bar__minus_00_dot_88)
        (pickupable Pencil_bar__plus_02_dot_24_bar__plus_00_dot_85_bar__minus_00_dot_62)
        (pickupable AlarmClock_bar__minus_00_dot_41_bar__plus_00_dot_93_bar__minus_01_dot_46)
        (pickupable Box_bar__plus_02_dot_15_bar__plus_00_dot_14_bar__plus_00_dot_49)
        (pickupable Pillow_bar__minus_00_dot_15_bar__plus_00_dot_62_bar__plus_00_dot_97)
        (pickupable CellPhone_bar__minus_00_dot_25_bar__plus_00_dot_09_bar__minus_00_dot_29)
        (pickupable Pen_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_25)
        (pickupable CreditCard_bar__plus_02_dot_24_bar__plus_00_dot_84_bar__minus_00_dot_48)
        (pickupable CD_bar__minus_00_dot_13_bar__plus_00_dot_92_bar__minus_01_dot_03)
        (pickupable BasketBall_bar__minus_00_dot_24_bar__plus_00_dot_12_bar__plus_00_dot_17)
        (pickupable KeyChain_bar__minus_00_dot_51_bar__plus_00_dot_93_bar__minus_00_dot_18)
        (pickupable Pen_bar__minus_00_dot_23_bar__plus_00_dot_93_bar__minus_00_dot_39)
        (pickupable Pillow_bar__minus_00_dot_03_bar__plus_00_dot_56_bar__plus_02_dot_46)
        (pickupable KeyChain_bar__plus_02_dot_20_bar__plus_00_dot_46_bar__minus_00_dot_68)
        (pickupable AlarmClock_bar__minus_00_dot_41_bar__plus_00_dot_93_bar__minus_01_dot_25)
        (pickupable Laptop_bar__plus_00_dot_17_bar__plus_00_dot_55_bar__plus_01_dot_85)
        (pickupable Pen_bar__minus_00_dot_40_bar__plus_00_dot_38_bar__minus_00_dot_29)
        (isReceptacleObject Box_bar__plus_02_dot_15_bar__plus_00_dot_14_bar__plus_00_dot_49)
        (openable Drawer_bar__minus_00_dot_10_bar__plus_00_dot_51_bar__minus_00_dot_40)
        (openable Drawer_bar__plus_02_dot_16_bar__plus_00_dot_57_bar__minus_00_dot_48)
        (openable Drawer_bar__plus_02_dot_16_bar__plus_00_dot_21_bar__minus_00_dot_48)
        (openable Drawer_bar__minus_00_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_40)
        (openable Drawer_bar__minus_00_dot_10_bar__plus_00_dot_22_bar__minus_00_dot_40)
        
        (atLocation agent1 loc_bar_6_bar_6_bar_2_bar_30)
        
        
        
        
        
        
        
        (toggleable DeskLamp_bar__minus_00_dot_67_bar__plus_00_dot_92_bar__minus_00_dot_06)
        
        
        
        
        (inReceptacle Pencil_bar__plus_02_dot_24_bar__plus_00_dot_85_bar__minus_00_dot_62 Dresser_bar__plus_02_dot_16_bar__plus_00_dot_57_bar__minus_00_dot_48)
        (inReceptacle Pencil_bar__plus_02_dot_14_bar__plus_00_dot_85_bar__minus_00_dot_62 Dresser_bar__plus_02_dot_16_bar__plus_00_dot_57_bar__minus_00_dot_48)
        (inReceptacle Book_bar__plus_02_dot_33_bar__plus_00_dot_84_bar__minus_00_dot_88 Dresser_bar__plus_02_dot_16_bar__plus_00_dot_57_bar__minus_00_dot_48)
        (inReceptacle CreditCard_bar__plus_02_dot_24_bar__plus_00_dot_84_bar__minus_00_dot_48 Dresser_bar__plus_02_dot_16_bar__plus_00_dot_57_bar__minus_00_dot_48)
        (inReceptacle Pen_bar__minus_00_dot_40_bar__plus_00_dot_38_bar__minus_00_dot_29 Drawer_bar__minus_00_dot_10_bar__plus_00_dot_51_bar__minus_00_dot_40)
        (inReceptacle Pillow_bar__minus_00_dot_03_bar__plus_00_dot_56_bar__plus_02_dot_46 Bed_bar__plus_00_dot_01_bar__minus_00_dot_06_bar__plus_01_dot_61)
        (inReceptacle Laptop_bar__plus_00_dot_17_bar__plus_00_dot_55_bar__plus_01_dot_85 Bed_bar__plus_00_dot_01_bar__minus_00_dot_06_bar__plus_01_dot_61)
        (inReceptacle Pillow_bar__minus_00_dot_15_bar__plus_00_dot_62_bar__plus_00_dot_97 Bed_bar__plus_00_dot_01_bar__minus_00_dot_06_bar__plus_01_dot_61)
        (inReceptacle CellPhone_bar__minus_00_dot_25_bar__plus_00_dot_09_bar__minus_00_dot_29 Drawer_bar__minus_00_dot_10_bar__plus_00_dot_22_bar__minus_00_dot_40)
        (inReceptacle Pen_bar__minus_00_dot_23_bar__plus_00_dot_93_bar__minus_00_dot_39 Desk_bar__minus_00_dot_38_bar__plus_00_dot_00_bar__minus_00_dot_95)
        (inReceptacle CD_bar__minus_00_dot_13_bar__plus_00_dot_92_bar__minus_01_dot_03 Desk_bar__minus_00_dot_38_bar__plus_00_dot_00_bar__minus_00_dot_95)
        (inReceptacle DeskLamp_bar__minus_00_dot_67_bar__plus_00_dot_92_bar__minus_00_dot_06 Desk_bar__minus_00_dot_38_bar__plus_00_dot_00_bar__minus_00_dot_95)
        (inReceptacle AlarmClock_bar__minus_00_dot_41_bar__plus_00_dot_93_bar__minus_01_dot_46 Desk_bar__minus_00_dot_38_bar__plus_00_dot_00_bar__minus_00_dot_95)
        (inReceptacle AlarmClock_bar__minus_00_dot_41_bar__plus_00_dot_93_bar__minus_01_dot_25 Desk_bar__minus_00_dot_38_bar__plus_00_dot_00_bar__minus_00_dot_95)
        (inReceptacle KeyChain_bar__minus_00_dot_51_bar__plus_00_dot_93_bar__minus_00_dot_18 Desk_bar__minus_00_dot_38_bar__plus_00_dot_00_bar__minus_00_dot_95)
        (inReceptacle Pen_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_25 Desk_bar__minus_00_dot_38_bar__plus_00_dot_00_bar__minus_00_dot_95)
        (inReceptacle KeyChain_bar__plus_02_dot_20_bar__plus_00_dot_46_bar__minus_00_dot_68 Drawer_bar__plus_02_dot_16_bar__plus_00_dot_57_bar__minus_00_dot_48)
        
        
        (receptacleAtLocation Bed_bar__plus_00_dot_01_bar__minus_00_dot_06_bar__plus_01_dot_61 loc_bar_6_bar_6_bar_3_bar_45)
        (receptacleAtLocation Desk_bar__minus_00_dot_38_bar__plus_00_dot_00_bar__minus_00_dot_95 loc_bar_2_bar__minus_5_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__plus_02_dot_16_bar__plus_00_dot_21_bar__minus_00_dot_48 loc_bar_5_bar__minus_5_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_02_dot_16_bar__plus_00_dot_57_bar__minus_00_dot_48 loc_bar_6_bar__minus_5_bar_0_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_10_bar__plus_00_dot_22_bar__minus_00_dot_40 loc_bar_3_bar__minus_5_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_10_bar__plus_00_dot_51_bar__minus_00_dot_40 loc_bar_2_bar__minus_5_bar_0_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_10_bar__plus_00_dot_76_bar__minus_00_dot_40 loc_bar_3_bar__minus_2_bar_3_bar_45)
        (receptacleAtLocation Dresser_bar__plus_02_dot_16_bar__plus_00_dot_57_bar__minus_00_dot_48 loc_bar_6_bar__minus_2_bar_1_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_02_dot_26_bar__plus_00_dot_00_bar__plus_02_dot_60 loc_bar_8_bar_9_bar_0_bar_60)
        (objectAtLocation AlarmClock_bar__minus_00_dot_41_bar__plus_00_dot_93_bar__minus_01_dot_46 loc_bar_2_bar__minus_5_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__minus_00_dot_51_bar__plus_00_dot_93_bar__minus_00_dot_18 loc_bar_2_bar__minus_5_bar_3_bar_60)
        (objectAtLocation Pencil_bar__plus_02_dot_24_bar__plus_00_dot_85_bar__minus_00_dot_62 loc_bar_6_bar__minus_2_bar_1_bar_60)
        (objectAtLocation Pillow_bar__minus_00_dot_03_bar__plus_00_dot_56_bar__plus_02_dot_46 loc_bar_6_bar_6_bar_3_bar_45)
        (objectAtLocation Pen_bar__minus_00_dot_23_bar__plus_00_dot_93_bar__minus_00_dot_39 loc_bar_2_bar__minus_5_bar_3_bar_60)
        (objectAtLocation Pen_bar__minus_00_dot_60_bar__plus_00_dot_93_bar__minus_01_dot_25 loc_bar_2_bar__minus_5_bar_3_bar_60)
        (objectAtLocation Chair_bar__plus_00_dot_01_bar__plus_00_dot_01_bar__minus_01_dot_24 loc_bar_2_bar__minus_5_bar_3_bar_60)
        (objectAtLocation Box_bar__plus_02_dot_15_bar__plus_00_dot_14_bar__plus_00_dot_49 loc_bar_6_bar_2_bar_1_bar_60)
        (objectAtLocation Book_bar__plus_02_dot_33_bar__plus_00_dot_84_bar__minus_00_dot_88 loc_bar_6_bar__minus_2_bar_1_bar_60)
        (objectAtLocation DeskLamp_bar__minus_00_dot_67_bar__plus_00_dot_92_bar__minus_00_dot_06 loc_bar_2_bar__minus_5_bar_3_bar_60)
        (objectAtLocation BasketBall_bar__minus_00_dot_24_bar__plus_00_dot_12_bar__plus_00_dot_17 loc_bar_1_bar_0_bar_3_bar_60)
        (objectAtLocation Pen_bar__minus_00_dot_40_bar__plus_00_dot_38_bar__minus_00_dot_29 loc_bar_2_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Pillow_bar__minus_00_dot_15_bar__plus_00_dot_62_bar__plus_00_dot_97 loc_bar_6_bar_6_bar_3_bar_45)
        (objectAtLocation Pencil_bar__plus_02_dot_14_bar__plus_00_dot_85_bar__minus_00_dot_62 loc_bar_6_bar__minus_2_bar_1_bar_60)
        (objectAtLocation CellPhone_bar__minus_00_dot_25_bar__plus_00_dot_09_bar__minus_00_dot_29 loc_bar_3_bar__minus_5_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__plus_02_dot_20_bar__plus_00_dot_46_bar__minus_00_dot_68 loc_bar_6_bar__minus_5_bar_0_bar_60)
        (objectAtLocation Painting_bar__minus_00_dot_79_bar__plus_01_dot_81_bar__minus_00_dot_22 loc_bar_1_bar__minus_1_bar_3_bar__minus_15)
        (objectAtLocation Painting_bar__minus_00_dot_79_bar__plus_01_dot_81_bar__plus_01_dot_02 loc_bar_4_bar_4_bar_3_bar_0)
        (objectAtLocation CreditCard_bar__plus_02_dot_24_bar__plus_00_dot_84_bar__minus_00_dot_48 loc_bar_6_bar__minus_2_bar_1_bar_60)
        (objectAtLocation AlarmClock_bar__minus_00_dot_41_bar__plus_00_dot_93_bar__minus_01_dot_25 loc_bar_2_bar__minus_5_bar_3_bar_60)
        (objectAtLocation Mirror_bar__plus_02_dot_12_bar__plus_01_dot_28_bar__plus_02_dot_75 loc_bar_8_bar_9_bar_0_bar_30)
        (objectAtLocation LightSwitch_bar__plus_00_dot_73_bar__plus_01_dot_20_bar__plus_02_dot_75 loc_bar_4_bar_9_bar_0_bar_45)
        (objectAtLocation CD_bar__minus_00_dot_13_bar__plus_00_dot_92_bar__minus_01_dot_03 loc_bar_2_bar__minus_5_bar_3_bar_60)
        (objectAtLocation Laptop_bar__plus_00_dot_17_bar__plus_00_dot_55_bar__plus_01_dot_85 loc_bar_6_bar_6_bar_3_bar_45)
        (objectAtLocation Window_bar__plus_00_dot_67_bar__plus_01_dot_36_bar__minus_01_dot_91 loc_bar_3_bar__minus_5_bar_2_bar_30)
        (objectAtLocation Blinds_bar__plus_00_dot_70_bar__plus_01_dot_98_bar__minus_01_dot_87 loc_bar_3_bar__minus_5_bar_2_bar__minus_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PencilType)
                                    (receptacleType ?r DeskType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PencilType)
                                            (receptacleType ?r DeskType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            