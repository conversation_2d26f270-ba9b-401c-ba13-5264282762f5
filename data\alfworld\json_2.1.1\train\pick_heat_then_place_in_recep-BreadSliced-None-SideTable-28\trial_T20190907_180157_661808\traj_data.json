{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000289.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000290.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000291.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000295.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000330.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000331.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000332.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000333.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000335.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000336.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000337.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000338.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000339.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000340.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000341.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000342.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000343.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000344.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000345.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000346.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000347.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000348.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000349.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000350.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000351.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000352.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000353.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000354.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000355.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000356.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000357.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000358.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000359.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000360.png", "low_idx": 66}, {"high_idx": 8, "image_name": "000000361.png", "low_idx": 67}, {"high_idx": 8, "image_name": "000000362.png", "low_idx": 67}, {"high_idx": 8, "image_name": "000000363.png", "low_idx": 68}, {"high_idx": 8, "image_name": "000000364.png", "low_idx": 68}, {"high_idx": 8, "image_name": "000000365.png", "low_idx": 69}, {"high_idx": 8, "image_name": "000000366.png", "low_idx": 69}, {"high_idx": 8, "image_name": "000000367.png", "low_idx": 70}, {"high_idx": 8, "image_name": "000000368.png", "low_idx": 70}, {"high_idx": 8, "image_name": "000000369.png", "low_idx": 70}, {"high_idx": 8, "image_name": "000000370.png", "low_idx": 70}, {"high_idx": 8, "image_name": "000000371.png", "low_idx": 70}, {"high_idx": 8, "image_name": "000000372.png", "low_idx": 70}, {"high_idx": 8, "image_name": "000000373.png", "low_idx": 70}, {"high_idx": 8, "image_name": "000000374.png", "low_idx": 70}, {"high_idx": 8, "image_name": "000000375.png", "low_idx": 70}, {"high_idx": 8, "image_name": "000000376.png", "low_idx": 70}, {"high_idx": 8, "image_name": "000000377.png", "low_idx": 70}, {"high_idx": 8, "image_name": "000000378.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000379.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000380.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000381.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000382.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000383.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000384.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000385.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000386.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000387.png", "low_idx": 71}, {"high_idx": 8, "image_name": "000000388.png", "low_idx": 71}, {"high_idx": 9, "image_name": "000000389.png", "low_idx": 72}, {"high_idx": 9, "image_name": "000000390.png", "low_idx": 72}, {"high_idx": 9, "image_name": "000000391.png", "low_idx": 72}, {"high_idx": 9, "image_name": "000000392.png", "low_idx": 72}, {"high_idx": 9, "image_name": "000000393.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000394.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000395.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000396.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000397.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000398.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000399.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000400.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000401.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000402.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000403.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000404.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000405.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000406.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000407.png", "low_idx": 73}, {"high_idx": 9, "image_name": "000000408.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000409.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000410.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000411.png", "low_idx": 74}, {"high_idx": 9, "image_name": "000000412.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000413.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000414.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000415.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000416.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000417.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000418.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000419.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000420.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000421.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000422.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000423.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000424.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000425.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000426.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000427.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000428.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000429.png", "low_idx": 75}, {"high_idx": 9, "image_name": "000000430.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000431.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000432.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000433.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000434.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000435.png", "low_idx": 76}, {"high_idx": 9, "image_name": "000000436.png", "low_idx": 77}, {"high_idx": 9, "image_name": "000000437.png", "low_idx": 77}, {"high_idx": 9, "image_name": "000000438.png", "low_idx": 77}, {"high_idx": 9, "image_name": "000000439.png", "low_idx": 77}, {"high_idx": 9, "image_name": "000000440.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000441.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000442.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000443.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000444.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000445.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000446.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000447.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000448.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000449.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000450.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000451.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000452.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000453.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000454.png", "low_idx": 78}, {"high_idx": 9, "image_name": "000000455.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000456.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000457.png", "low_idx": 79}, {"high_idx": 9, "image_name": "000000458.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000459.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000460.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000461.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000462.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000463.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000464.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000465.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000466.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000467.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000468.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000469.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000470.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000471.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000472.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000473.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000474.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000475.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000476.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000477.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000478.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000479.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000480.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000481.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000482.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000483.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000484.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000485.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000486.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000487.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000488.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000489.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000490.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000491.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000492.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000493.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000494.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000495.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000496.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000497.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000498.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000499.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000500.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000501.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000502.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000503.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000504.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000505.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000506.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000507.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000508.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000509.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000510.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000511.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000512.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000513.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000514.png", "low_idx": 89}, {"high_idx": 10, "image_name": "000000515.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000516.png", "low_idx": 90}, {"high_idx": 10, "image_name": "000000517.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000518.png", "low_idx": 91}, {"high_idx": 10, "image_name": "000000519.png", "low_idx": 92}, {"high_idx": 10, "image_name": "000000520.png", "low_idx": 92}, {"high_idx": 10, "image_name": "000000521.png", "low_idx": 93}, {"high_idx": 10, "image_name": "000000522.png", "low_idx": 93}, {"high_idx": 10, "image_name": "000000523.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000524.png", "low_idx": 94}, {"high_idx": 10, "image_name": "000000525.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000526.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000527.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000528.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000529.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000530.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000531.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000532.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000533.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000534.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000535.png", "low_idx": 95}, {"high_idx": 10, "image_name": "000000536.png", "low_idx": 96}, {"high_idx": 10, "image_name": "000000537.png", "low_idx": 96}, {"high_idx": 10, "image_name": "000000538.png", "low_idx": 97}, {"high_idx": 10, "image_name": "000000539.png", "low_idx": 97}, {"high_idx": 10, "image_name": "000000540.png", "low_idx": 98}, {"high_idx": 10, "image_name": "000000541.png", "low_idx": 98}, {"high_idx": 11, "image_name": "000000542.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000543.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000544.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000545.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000546.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000547.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000548.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000549.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000550.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000551.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000552.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000553.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000554.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000555.png", "low_idx": 99}, {"high_idx": 11, "image_name": "000000556.png", "low_idx": 99}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-6|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [-0.630764068, -0.630764068, -5.802, -5.802, 3.7653684, 3.7653684]], "coordinateReceptacleObjectId": ["CounterTop", [-1.308, -1.308, -5.802, -5.802, 3.9072, 3.9072]], "forceVisible": true, "objectId": "ButterKnife|-00.16|+00.94|-01.45"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-14|-4|0|60"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-14.261452, -14.261452, -2.535301448, -2.535301448, 3.9042096, 3.9042096]], "forceVisible": true, "objectId": "Bread|-03.57|+00.98|-00.63"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-15|-5|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "sidetable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [-0.630764068, -0.630764068, -5.802, -5.802, 3.7653684, 3.7653684]], "coordinateReceptacleObjectId": ["SideTable", [-16.284, -16.284, -1.236, -1.236, 2.904, 2.904]], "forceVisible": true, "objectId": "ButterKnife|-00.16|+00.94|-01.45", "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-14|-4|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-14.261452, -14.261452, -2.535301448, -2.535301448, 3.9042096, 3.9042096]], "coordinateReceptacleObjectId": ["DiningTable", [-12.132, -12.132, -1.764, -1.764, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-03.57|+00.98|-00.63|BreadSliced_2"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-8|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-15|-5|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "sidetable"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-14.261452, -14.261452, -2.535301448, -2.535301448, 3.9042096, 3.9042096]], "coordinateReceptacleObjectId": ["SideTable", [-16.284, -16.284, -1.236, -1.236, 2.904, 2.904]], "forceVisible": true, "objectId": "Bread|-03.57|+00.98|-00.63|BreadSliced_2", "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 12, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|-00.16|+00.94|-01.45"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [134, 95, 142, 135], "mask": [[28338, 2], [28638, 3], [28938, 3], [29238, 3], [29537, 5], [29837, 5], [30137, 6], [30437, 6], [30737, 6], [31037, 6], [31337, 5], [31637, 5], [31937, 5], [32237, 5], [32537, 4], [32837, 4], [33137, 4], [33437, 3], [33736, 4], [34036, 4], [34336, 4], [34636, 3], [34936, 3], [35236, 3], [35536, 3], [35836, 3], [36135, 4], [36435, 4], [36735, 4], [37035, 4], [37335, 5], [37634, 6], [37934, 6], [38234, 6], [38534, 6], [38834, 6], [39134, 6], [39434, 6], [39734, 5], [40034, 5], [40334, 4]], "point": [138, 114]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-03.57|+00.98|-00.63"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [56, 119, 191, 179], "mask": [[35468, 1], [35764, 124], [36063, 125], [36362, 127], [36662, 127], [36961, 128], [37261, 128], [37561, 128], [37861, 128], [38161, 128], [38460, 130], [38760, 130], [39060, 130], [39360, 130], [39659, 131], [39959, 131], [40259, 131], [40559, 131], [40859, 131], [41158, 132], [41458, 133], [41758, 133], [42058, 133], [42358, 133], [42657, 134], [42957, 134], [43257, 134], [43557, 134], [43856, 135], [44156, 135], [44456, 136], [44756, 136], [45056, 136], [45356, 136], [45656, 136], [45956, 136], [46256, 136], [46556, 136], [46856, 136], [47156, 135], [47457, 134], [47757, 134], [48057, 134], [48358, 133], [48658, 132], [48959, 131], [49260, 130], [49561, 128], [49861, 128], [50162, 126], [50463, 125], [50763, 125], [51064, 124], [51365, 123], [51665, 122], [51966, 121], [52267, 120], [52568, 118], [52868, 118], [53169, 116], [53470, 114]], "point": [123, 148]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|-00.16|+00.94|-01.45", "placeStationary": true, "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [21, 115, 140, 251], "mask": [[34254, 28], [34293, 14], [34321, 17], [34556, 24], [34595, 12], [34621, 17], [34857, 21], [34896, 12], [34921, 17], [35159, 18], [35196, 11], [35220, 18], [35459, 17], [35496, 8], [35520, 18], [35760, 14], [35797, 6], [35821, 17], [36061, 11], [36097, 5], [36121, 17], [36361, 11], [36397, 4], [36422, 16], [36662, 9], [36674, 2], [36697, 4], [36722, 16], [36962, 9], [36974, 2], [36997, 3], [37023, 15], [37262, 10], [37274, 2], [37298, 2], [37323, 15], [37562, 10], [37575, 2], [37598, 2], [37623, 15], [37862, 10], [37875, 2], [37898, 1], [37923, 15], [38162, 11], [38176, 1], [38198, 1], [38224, 14], [38461, 12], [38498, 2], [38524, 14], [38761, 13], [38798, 2], [38823, 15], [39061, 14], [39099, 1], [39123, 15], [39360, 16], [39399, 2], [39423, 15], [39660, 18], [39699, 2], [39723, 15], [39943, 2], [39959, 20], [39999, 3], [40022, 16], [40243, 3], [40259, 20], [40299, 4], [40321, 17], [40542, 6], [40558, 21], [40599, 5], [40620, 18], [40842, 8], [40856, 24], [40898, 7], [40918, 20], [41142, 38], [41198, 9], [41216, 23], [41441, 39], [41497, 42], [41741, 8], [41757, 24], [41796, 43], [42040, 6], [42059, 24], [42094, 12], [42110, 29], [42340, 5], [42360, 26], [42390, 14], [42412, 27], [42639, 5], [42662, 40], [42713, 26], [42939, 4], [42963, 37], [43016, 23], [43239, 4], [43263, 35], [43317, 22], [43538, 4], [43564, 33], [43618, 21], [43838, 4], [43865, 31], [43918, 21], [44137, 4], [44165, 31], [44219, 20], [44437, 4], [44466, 29], [44520, 19], [44736, 5], [44766, 29], [44820, 19], [45036, 5], [45066, 28], [45120, 19], [45335, 6], [45366, 28], [45421, 18], [45635, 7], [45665, 29], [45721, 18], [45935, 7], [45965, 29], [46021, 18], [46234, 8], [46265, 29], [46321, 18], [46534, 9], [46565, 29], [46621, 18], [46833, 11], [46864, 30], [46920, 19], [47133, 11], [47164, 31], [47220, 19], [47432, 13], [47463, 32], [47520, 19], [47732, 14], [47763, 33], [47819, 20], [48031, 16], [48062, 35], [48119, 21], [48331, 17], [48361, 36], [48418, 22], [48631, 19], [48661, 37], [48717, 23], [48930, 22], [48959, 41], [49015, 25], [49230, 71], [49313, 27], [49529, 76], [49610, 30], [49829, 111], [50128, 112], [50428, 112], [50728, 112], [51027, 113], [51327, 113], [51626, 114], [51926, 114], [52225, 115], [52525, 115], [52824, 116], [53124, 116], [53424, 116], [53723, 117], [54022, 118], [54322, 118], [54621, 119], [54922, 118], [55223, 118], [55523, 11], [55546, 95], [55824, 11], [55846, 95], [56124, 11], [56145, 96], [56425, 11], [56445, 96], [56725, 11], [56745, 92], [57026, 11], [57044, 93], [57326, 11], [57344, 93], [57627, 11], [57644, 93], [57928, 10], [57943, 94], [58228, 11], [58243, 94], [58529, 10], [58542, 95], [58829, 11], [58842, 95], [59130, 10], [59142, 95], [59430, 107], [59731, 106], [60032, 105], [60332, 105], [60633, 104], [60933, 104], [61234, 103], [61534, 102], [61835, 101], [62136, 9], [62158, 78], [62436, 10], [62457, 79], [62737, 9], [62757, 79], [63037, 10], [63057, 79], [63338, 9], [63356, 80], [63638, 10], [63656, 80], [63939, 10], [63956, 80], [64240, 9], [64256, 80], [64540, 10], [64555, 81], [64841, 9], [64855, 82], [65141, 10], [65155, 82], [65442, 9], [65454, 83], [65742, 10], [65754, 83], [66043, 9], [66054, 83], [66343, 10], [66354, 83], [66644, 93], [66945, 92], [67245, 92], [67546, 91], [67846, 91], [68147, 90], [68447, 90], [68748, 89], [69049, 8], [69131, 7], [69349, 9], [69431, 7], [69650, 8], [69731, 7], [69950, 9], [70032, 6], [70251, 8], [70332, 6], [70551, 9], [70632, 6], [70852, 8], [70932, 6], [71153, 8], [71232, 6], [71453, 8], [71532, 6], [71754, 8], [71832, 6], [72054, 8], [72132, 6], [72355, 8], [72432, 6], [72655, 8], [72732, 6], [72956, 8], [73033, 5], [73256, 9], [73333, 5], [73557, 8], [73633, 6], [73858, 8], [73933, 6], [74158, 8], [74233, 6], [74459, 7], [74533, 6], [74759, 7], [74833, 6], [75060, 5], [75133, 6]], "point": [80, 182]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-03.57|+00.98|-00.63|BreadSliced_2"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [142, 120, 152, 179], "mask": [[35843, 9], [36143, 9], [36443, 10], [36743, 10], [37043, 10], [37343, 10], [37643, 10], [37943, 10], [38243, 10], [38543, 10], [38843, 10], [39143, 10], [39443, 10], [39743, 10], [40043, 10], [40343, 10], [40643, 10], [40943, 10], [41243, 10], [41543, 10], [41843, 10], [42143, 10], [42443, 10], [42743, 10], [43043, 10], [43343, 10], [43643, 10], [43943, 10], [44243, 10], [44543, 10], [44843, 10], [45143, 10], [45443, 10], [45743, 10], [46042, 11], [46342, 11], [46642, 11], [46942, 11], [47242, 11], [47543, 10], [47843, 10], [48143, 10], [48443, 10], [48743, 10], [49043, 10], [49343, 10], [49643, 10], [49943, 10], [50243, 10], [50543, 10], [50843, 10], [51143, 10], [51443, 10], [51743, 10], [52043, 10], [52343, 10], [52643, 10], [52943, 9], [53243, 9], [53543, 9]], "point": [147, 148]}}, "high_idx": 7}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-03.57|+00.98|-00.63|BreadSliced_2", "placeStationary": true, "receptacleObjectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 32743], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 62]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 6443], [6448, 289], [6757, 276], [7060, 270], [7363, 265], [7665, 260], [7967, 257], [8268, 255], [8569, 253], [8870, 251], [9171, 249], [9472, 248], [9772, 248], [10072, 248], [10373, 246], [10673, 246], [10973, 246], [11274, 245], [11574, 245], [11874, 245], [12174, 245], [12474, 245], [12774, 245], [13074, 245], [13374, 245], [13674, 245], [13974, 245], [14274, 245], [14574, 245], [14874, 245], [15174, 245], [15474, 245], [15774, 245], [16074, 245], [16374, 245], [16674, 245], [16974, 245], [17274, 245], [17574, 245], [17874, 245], [18174, 245], [18474, 245], [18774, 245], [19074, 245], [19374, 245], [19674, 245], [19974, 245], [20274, 245], [20574, 245], [20874, 245], [21174, 245], [21474, 245], [21774, 245], [22074, 245], [22373, 247], [22673, 247], [22973, 247], [23273, 247], [23572, 249], [23872, 249], [24172, 255], [24466, 8277], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 82]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.8676, -0.8676, -8.22, -8.22, 5.88028048, 5.88028048]], "forceVisible": true, "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [35, 1, 299, 126], "mask": [[35, 265], [335, 265], [635, 265], [935, 265], [1235, 265], [1535, 265], [1835, 265], [2135, 265], [2435, 265], [2736, 264], [3036, 264], [3336, 264], [3636, 264], [3936, 264], [4236, 264], [4536, 264], [4836, 264], [5136, 264], [5436, 264], [5737, 263], [6037, 263], [6337, 263], [6637, 263], [6937, 263], [7237, 263], [7537, 263], [7837, 263], [8137, 263], [8438, 262], [8738, 262], [9038, 262], [9338, 262], [9638, 262], [9938, 262], [10238, 262], [10538, 262], [10838, 262], [11138, 262], [11439, 261], [11739, 261], [12039, 261], [12339, 261], [12639, 261], [12939, 261], [13239, 261], [13539, 261], [13839, 261], [14139, 261], [14440, 260], [14740, 260], [15040, 260], [15340, 260], [15640, 260], [15940, 260], [16240, 260], [16540, 260], [16840, 260], [17141, 259], [17441, 259], [17741, 259], [18041, 259], [18341, 259], [18641, 259], [18941, 259], [19241, 259], [19541, 259], [19841, 259], [20142, 258], [20442, 258], [20742, 258], [21042, 258], [21342, 258], [21642, 258], [21942, 258], [22242, 258], [22542, 258], [22842, 258], [23143, 257], [23443, 257], [23743, 257], [24043, 257], [24343, 257], [24643, 257], [24943, 257], [25243, 257], [25543, 257], [25843, 257], [26144, 256], [26444, 256], [26744, 256], [27044, 256], [27344, 256], [27644, 256], [27944, 256], [28244, 256], [28544, 256], [28845, 255], [29145, 255], [29445, 255], [29745, 255], [30045, 255], [30345, 255], [30645, 255], [30945, 255], [31245, 255], [31545, 255], [31846, 254], [32146, 254], [32447, 253], [32747, 253], [33047, 252], [33347, 252], [33648, 250], [33947, 252], [34247, 252], [34548, 251], [34848, 251], [35148, 250], [35448, 250], [35748, 250], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [167, 62]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-03.57|+00.98|-00.63|BreadSliced_2"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [119, 22, 173, 82], "mask": [[6443, 5], [6737, 20], [7033, 27], [7330, 33], [7628, 37], [7925, 42], [8224, 44], [8523, 46], [8822, 48], [9121, 50], [9420, 52], [9720, 52], [10020, 52], [10320, 53], [10619, 54], [10919, 54], [11219, 55], [11519, 55], [11819, 55], [12119, 55], [12419, 55], [12719, 55], [13019, 55], [13319, 55], [13619, 55], [13919, 55], [14219, 55], [14519, 55], [14819, 55], [15119, 55], [15419, 55], [15719, 55], [16019, 55], [16319, 55], [16619, 55], [16919, 55], [17219, 55], [17519, 55], [17819, 55], [18119, 55], [18419, 55], [18719, 55], [19019, 55], [19319, 55], [19619, 55], [19919, 55], [20219, 55], [20519, 55], [20819, 55], [21119, 55], [21419, 55], [21719, 55], [22019, 55], [22319, 54], [22620, 53], [22920, 53], [23220, 53], [23520, 52], [23821, 51], [24121, 51], [24427, 39]], "point": [146, 51]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.22|+01.47|-02.06"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 126], "mask": [[0, 32743], [32747, 293], [33047, 252], [33300, 36], [33347, 252], [33600, 32], [33648, 250], [33900, 29], [33947, 252], [34200, 25], [34247, 252], [34500, 21], [34548, 251], [34800, 18], [34848, 251], [35100, 14], [35148, 250], [35400, 10], [35448, 250], [35700, 7], [35748, 250], [36000, 3], [36048, 250], [36348, 250], [36648, 250], [36948, 250], [37248, 249], [37550, 246]], "point": [149, 62]}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-03.57|+00.98|-00.63|BreadSliced_2", "placeStationary": true, "receptacleObjectId": "SideTable|-04.07|+00.73|-00.31"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [21, 115, 140, 251], "mask": [[34254, 28], [34293, 14], [34321, 17], [34556, 24], [34595, 12], [34621, 17], [34857, 21], [34896, 12], [34921, 17], [35159, 18], [35196, 11], [35220, 18], [35459, 17], [35496, 8], [35520, 18], [35760, 14], [35797, 6], [35821, 17], [36061, 11], [36097, 5], [36121, 17], [36361, 11], [36397, 4], [36422, 16], [36662, 9], [36674, 2], [36697, 4], [36722, 16], [36962, 9], [36974, 2], [36997, 3], [37023, 15], [37262, 10], [37274, 2], [37298, 2], [37323, 15], [37562, 10], [37575, 2], [37598, 2], [37623, 15], [37862, 10], [37875, 2], [37898, 1], [37923, 15], [38162, 11], [38176, 1], [38198, 1], [38224, 14], [38461, 12], [38498, 2], [38524, 14], [38761, 13], [38798, 2], [38823, 15], [39061, 14], [39099, 1], [39123, 15], [39360, 16], [39399, 2], [39423, 15], [39660, 18], [39699, 2], [39723, 15], [39943, 2], [39959, 20], [39999, 3], [40022, 16], [40243, 3], [40259, 20], [40299, 4], [40321, 17], [40542, 6], [40558, 21], [40599, 5], [40620, 18], [40842, 8], [40856, 24], [40898, 7], [40918, 20], [41142, 38], [41198, 9], [41216, 23], [41441, 39], [41497, 16], [41515, 24], [41741, 8], [41757, 24], [41796, 18], [41815, 24], [42040, 6], [42059, 24], [42094, 10], [42110, 4], [42115, 24], [42340, 5], [42360, 26], [42390, 13], [42411, 3], [42415, 24], [42639, 5], [42662, 39], [42716, 23], [42939, 4], [42963, 35], [43016, 23], [43239, 4], [43263, 34], [43317, 22], [43538, 4], [43564, 32], [43617, 22], [43838, 4], [43865, 31], [43918, 21], [44137, 4], [44165, 30], [44219, 20], [44437, 4], [44466, 29], [44519, 20], [44736, 5], [44766, 28], [44820, 19], [45036, 5], [45066, 28], [45120, 19], [45335, 6], [45366, 28], [45420, 19], [45635, 7], [45665, 28], [45720, 19], [45935, 7], [45965, 28], [46020, 19], [46234, 8], [46265, 29], [46320, 19], [46534, 9], [46565, 29], [46620, 19], [46833, 11], [46864, 30], [46920, 19], [47133, 11], [47164, 31], [47220, 19], [47432, 13], [47463, 32], [47519, 20], [47732, 14], [47763, 33], [47819, 20], [48031, 16], [48062, 34], [48119, 21], [48331, 17], [48361, 36], [48417, 1], [48419, 21], [48631, 19], [48661, 37], [48716, 2], [48720, 20], [48930, 22], [48959, 41], [49013, 5], [49020, 20], [49230, 72], [49311, 7], [49320, 20], [49529, 90], [49620, 20], [49829, 90], [49920, 20], [50128, 91], [50221, 19], [50428, 91], [50521, 19], [50728, 92], [50821, 19], [51027, 93], [51121, 19], [51327, 93], [51421, 19], [51626, 114], [51926, 114], [52225, 115], [52525, 115], [52824, 116], [53124, 116], [53424, 116], [53723, 117], [54022, 118], [54322, 118], [54621, 119], [54922, 118], [55223, 118], [55523, 11], [55546, 95], [55824, 11], [55846, 95], [56124, 11], [56145, 96], [56425, 11], [56445, 96], [56725, 11], [56745, 92], [57026, 11], [57044, 93], [57326, 11], [57344, 93], [57627, 11], [57644, 93], [57928, 10], [57943, 94], [58228, 11], [58243, 94], [58529, 10], [58542, 95], [58829, 11], [58842, 95], [59130, 10], [59142, 95], [59430, 107], [59731, 106], [60032, 105], [60332, 105], [60633, 104], [60933, 104], [61234, 103], [61534, 102], [61835, 101], [62136, 9], [62158, 78], [62436, 10], [62457, 79], [62737, 9], [62757, 79], [63037, 10], [63057, 79], [63338, 9], [63356, 80], [63638, 10], [63656, 80], [63939, 10], [63956, 78], [64240, 9], [64256, 74], [64540, 10], [64555, 71], [64841, 9], [64855, 68], [65141, 10], [65155, 64], [65442, 9], [65454, 63], [65742, 10], [65754, 62], [66043, 9], [66054, 60], [66343, 10], [66354, 59], [66644, 67], [66945, 64], [67245, 63], [67546, 62], [67846, 61], [68147, 60], [68447, 59], [68748, 58], [69049, 8], [69349, 9], [69650, 8], [69950, 9], [70251, 8], [70551, 9], [70852, 8], [71153, 8], [71453, 8], [71754, 8], [72054, 8], [72355, 8], [72655, 8], [72956, 8], [73256, 9], [73557, 8], [73858, 8], [74158, 8], [74459, 7], [74759, 7], [75060, 5]], "point": [80, 182]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan28", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -4.0, "y": 0.900998235, "z": -2.5}, "object_poses": [{"objectName": "Pan_d1250561", "position": {"x": -0.2221, "y": 0.961999953, "z": -2.243}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -3.94221377, "y": 0.7232722, "z": -0.126497984}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -2.56082344, "y": 0.894185, "z": -0.204986483}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -0.157691017, "y": 0.9413421, "z": -1.4505}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -1.24855721, "y": 0.9423421, "z": -3.59545565}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_c7418f98", "position": {"x": -3.56536078, "y": 0.8955984, "z": -0.290749818}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -1.69423819, "y": 0.942848563, "z": -3.439128}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -3.16354752, "y": 0.8951693, "z": -0.5480586}, "rotation": {"x": 0.0, "y": 0.000327849062, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -3.36445427, "y": 0.895750165, "z": -0.462288678}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -0.5436009, "y": 0.7520972, "z": -2.84604383}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -3.3731637, "y": 0.775088251, "z": -3.54511356}, "rotation": {"x": 0.0, "y": 64.1259, "z": 0.0}}, {"objectName": "Spatula_1d69618b", "position": {"x": -0.7401416, "y": 0.8284667, "z": -3.588819}, "rotation": {"x": 9.92312057e-15, "y": 45.0000343, "z": 9.923131e-15}}, {"objectName": "Cup_41873c33", "position": {"x": -3.163548, "y": 0.895324, "z": -0.633827567}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -0.465482, "y": 0.09629309, "z": -1.48973584}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -0.175364524, "y": 1.95197284, "z": -1.05575228}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_1b546504", "position": {"x": -0.244763374, "y": 0.9679183, "z": -2.847621}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_1b546504", "position": {"x": -1.69423819, "y": 0.967918336, "z": -3.75178385}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -3.16354561, "y": 0.892809868, "z": -0.20498313}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -0.17396453, "y": 1.50082469, "z": -1.48968232}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -3.94221377, "y": 0.741266131, "z": -0.430667937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -3.78633642, "y": 0.758396447, "z": -3.331595}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -2.76173329, "y": 0.892928839, "z": -0.633829832}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -3.565363, "y": 0.9760524, "z": -0.633825362}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -0.278669566, "y": 1.93525827, "z": -1.73901224}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -2.56082487, "y": 0.890926957, "z": -0.462293148}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -4.06235027, "y": 0.686296344, "z": -0.24816598}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -0.397045672, "y": 1.48371041, "z": -0.994933665}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -4.182487, "y": 0.7527082, "z": -0.4306679}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_4eadea0d", "position": {"x": -4.24255562, "y": 0.7519119, "z": -0.248166}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0407be4c", "position": {"x": -4.091633, "y": 0.7582775, "z": -3.41020513}, "rotation": {"x": 0.0, "y": 244.125916, "z": 0.0}}, {"objectName": "Pot_c7418f98", "position": {"x": -0.4374, "y": 0.961999953, "z": -1.8457}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Fork_938fe393", "position": {"x": -0.4751878, "y": 0.752463937, "z": -2.68573141}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_41873c33", "position": {"x": -3.30574131, "y": 0.7607916, "z": -3.68412447}, "rotation": {"x": 0.0, "y": 334.125916, "z": 0.0}}, {"objectName": "Tomato_b1760e7a", "position": {"x": -3.94221377, "y": 0.741266131, "z": -0.248166}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_d1250561", "position": {"x": -0.217, "y": 0.958, "z": -2.215}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "Plate_ba5a8c1e", "position": {"x": -3.77244115, "y": 0.774220169, "z": -3.75784922}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Bread_4d72833d", "position": {"x": -2.984, "y": 0.956136167, "z": -0.311999947}, "rotation": {"x": -6.909952e-07, "y": 50.59269, "z": -9.587563e-06}}, {"objectName": "Spatula_1d69618b", "position": {"x": -3.46871614, "y": 0.775088251, "z": -2.88202667}, "rotation": {"x": 0.0, "y": 199.125946, "z": 0.0}}, {"objectName": "Knife_1b546504", "position": {"x": -0.509394348, "y": 0.7765861, "z": -2.68573141}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_6f13b4f9", "position": {"x": -0.318482876, "y": 0.938606143, "z": -2.93781018}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "PepperShaker_8ae9f292", "position": {"x": -0.185307026, "y": 0.938606143, "z": -3.012313}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "ButterKnife_e676ac70", "position": {"x": -0.7214115, "y": 0.8135089, "z": -3.49191141}, "rotation": {"x": 9.923132e-15, "y": 315.000031, "z": -9.92312e-15}}, {"objectName": "Apple_4eadea0d", "position": {"x": -0.3349219, "y": 1.34801114, "z": -0.413378119}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "DishSponge_7e2c9e01", "position": {"x": -0.09655203, "y": 1.50220168, "z": -2.70088148}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5e3edff7", "position": {"x": -2.56082582, "y": 0.926596642, "z": -0.6338309}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Egg_571582a1", "position": {"x": -0.4323179, "y": 0.8545337, "z": -3.202818}, "rotation": {"x": 9.923132e-15, "y": 315.000031, "z": -9.92312e-15}}, {"objectName": "Spoon_7dcfa4cd", "position": {"x": -0.157691017, "y": 0.9424294, "z": -1.49432325}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_ec37c659", "position": {"x": -3.6655252, "y": 0.851072848, "z": -3.515201}, "rotation": {"x": 0.0, "y": 334.125916, "z": 0.0}}, {"objectName": "Mug_f3c7368a", "position": {"x": -2.35991621, "y": 0.8896208, "z": -0.204987615}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Bowl_1f51c27f", "position": {"x": -0.252379239, "y": 1.28490734, "z": -0.646000266}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 778677235, "scene_num": 28}, "task_id": "trial_T20190907_180157_661808", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2M7VEYAS1405L_354P56DE9NK32NC8I6AG8CDVH997S4", "high_descs": ["turn around and cross the room to the counter between the stove and refrigerator", "take the knife from the counter", "turn around and cross the room to the door, then turn to the right and head to the rectangular table with the loaves of bread", "cut the loaf of bread this located along the long edge of the table", "turn around to the left, and head very slightly away from the rectangular table before continuing your rotation to the left until you face the table once more", "place the knife on the tomato located on the front right corner of the top of the brown shelf between the rectangular table and the wall", "move back closer to the sliced loaf of bread", "take a slice of bread", "turn around and cross the room to the round table before turning left and cross the room to the stove. Then, look up at the microwave", "open the microwave, insert the bread, and close the microwave. Heat the bread in the microwave for 5 seconds, then open the microwave, remove the bread, and close the microwave door. Look back down to your normal elevation", "turn around and cross the room to the door, then turn right and go to the brown shelf where you left the knife", "put the bread on the center front of the brown shelf, making sure to knock the knife to the floor in the process"], "task_desc": "Place a warm piece of bread on the top of the brown shelf", "votes": [1, 1]}, {"assignment_id": "A2KAGFQU28JY43_3MB8LZR5BIA4TPO2HR2O0V4MV1NKLZ", "high_descs": ["Turn to your left and walk across the kitchen to the small counter between the refrigerator and the stove. ", "Pick up the butter knife on the counter, to the left of the spoon. ", "Turn around and walk across the kitchen, stopping and turning to your right at the loaf of bread on the white table to your right. ", "Cut the loaf of bread on the white table, near the edge into slices. ", "Step over to your left so that you are in front of the small black shelf to the left of the white table. ", "Place the knife on the small black shelf, between the front, right tomato and the white table. ", "Move back over to your right so that you are back in front of the loaf of bread, on the white table. ", "Pick up one of the center slices of bread previously cut. ", "Turn to your right and walk across the kitchen to the microwave, above the stove. ", "Place the slice of bread in the microwave above the stove, heat it, then remove it from the microwave. ", "Turn around and walk across the kitchen, back to the small black shelf on your right, to the left of the white table. ", "Place the slice of bread on the front of the small, black shelf, between the front two tomato's. "], "task_desc": "Put a warm slice of bread on the small, black shelf. ", "votes": [1, 1]}, {"assignment_id": "AFU00NU09CFXE_3WT783CTPEYAXIQ2Y1WWY9V9EV0CBL", "high_descs": ["Move to stand at the counter space between the fridge and stove.", "Pick up the butter knife from the counter. ", "Carry the butter knife to the white folding table next to the small black table in the corner.", "Slice the loaf of bread closest to the black table. ", "Move a bit closer to the black table while still holding the butter knife.", "Place the knife in the tomato closest to the white table. ", "Move back over to stand in front of the sliced loaf of bread.", "Pick up a slice of bread from the center of the loaf. ", "Carry the slice of bread to stand in front of the stove. ", "Open the door to the microwave, which is above the stove, heat the bread, and then remove. ", "Carry the warm bread to stand where the black and white tables meet.", "Place the slice of bread on the black table while knocking the butter knife to the floor."], "task_desc": "Heat a piece of sliced bread to put on the black table.", "votes": [1, 1]}]}}