{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000149.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000150.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000151.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000152.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000153.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000156.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000158.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000159.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000289.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000290.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000291.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000292.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000293.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000294.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000295.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 42}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|-5|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [4.777956, 4.777956, -7.71740152, -7.71740152, 3.7393108, 3.7393108]], "coordinateReceptacleObjectId": ["CounterTop", [4.64, 4.64, -8.056, -8.056, 3.7872, 3.7872]], "forceVisible": true, "objectId": "Knife|+01.19|+00.93|-01.93"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [5.67714168, 5.67714168, -7.71740152, -7.71740152, 3.973039864, 3.973039864]], "forceVisible": true, "objectId": "Bread|+01.42|+00.99|-01.93"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "countertop"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [4.777956, 4.777956, -7.71740152, -7.71740152, 3.7393108, 3.7393108]], "coordinateReceptacleObjectId": ["CounterTop", [4.64, 4.64, -8.056, -8.056, 3.7872, 3.7872]], "forceVisible": true, "objectId": "Knife|+01.19|+00.93|-01.93", "receptacleObjectId": "CounterTop|+01.16|+00.95|-02.01"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 4, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [5.67714168, 5.67714168, -7.71740152, -7.71740152, 3.973039864, 3.973039864]], "coordinateReceptacleObjectId": ["CounterTop", [4.64, 4.64, -8.056, -8.056, 3.7872, 3.7872]], "forceVisible": true, "objectId": "Bread|+01.42|+00.99|-01.93|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 5, "planner_action": {"action": "GotoLocation", "location": "loc|4|-5|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 6, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.308, 7.308, -5.416, -5.416, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.83|+00.90|-01.35"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 7, "planner_action": {"action": "GotoLocation", "location": "loc|5|-2|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "fridge"]}, "high_idx": 8, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [5.67714168, 5.67714168, -7.71740152, -7.71740152, 3.973039864, 3.973039864]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [7.9, 7.9, -2.148, -2.148, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|+01.42|+00.99|-01.93|BreadSliced_1", "receptacleObjectId": "<PERSON><PERSON>|+01.98|+00.00|-00.54"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 9, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|+01.19|+00.93|-01.93"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [61, 150, 151, 161], "mask": [[44801, 1], [45066, 3], [45101, 13], [45363, 8], [45400, 24], [45662, 24], [45700, 32], [45961, 34], [45999, 40], [46261, 86], [46561, 91], [46868, 83], [47188, 62], [47504, 45], [47816, 31], [48129, 15]], "point": [106, 154]}}, "high_idx": 1}, {"api_action": {"action": "SliceObject", "objectId": "Bread|+01.42|+00.99|-01.93"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [0, 99, 61, 184], "mask": [[29439, 2], [29728, 19], [30026, 25], [30324, 29], [30623, 31], [30922, 33], [31220, 35], [31519, 36], [31818, 38], [32117, 40], [32416, 42], [32715, 44], [33015, 44], [33314, 45], [33613, 47], [33912, 48], [34212, 48], [34511, 50], [34810, 51], [35110, 51], [35409, 52], [35708, 54], [36008, 54], [36307, 55], [36607, 55], [36906, 56], [37205, 57], [37505, 57], [37804, 57], [38104, 57], [38403, 57], [38703, 57], [39002, 58], [39302, 57], [39601, 58], [39901, 58], [40200, 58], [40500, 58], [40800, 57], [41100, 57], [41400, 57], [41700, 56], [42000, 56], [42300, 55], [42600, 55], [42900, 55], [43200, 54], [43500, 54], [43800, 54], [44100, 53], [44400, 53], [44700, 52], [45000, 52], [45300, 52], [45600, 51], [45900, 51], [46200, 51], [46500, 50], [46800, 50], [47100, 50], [47400, 49], [47700, 49], [48000, 48], [48300, 48], [48600, 48], [48900, 47], [49200, 47], [49500, 47], [49800, 46], [50100, 46], [50400, 45], [50700, 45], [51000, 45], [51300, 44], [51600, 44], [51900, 44], [52200, 43], [52500, 43], [52800, 42], [53101, 40], [53402, 38], [53703, 36], [54004, 34], [54305, 32], [54606, 28], [54912, 16]], "point": [30, 140]}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|+01.19|+00.93|-01.93", "placeStationary": true, "receptacleObjectId": "CounterTop|+01.16|+00.95|-02.01"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 90, 299, 268], "mask": [[26700, 165], [26923, 8], [27000, 165], [27223, 8], [27300, 165], [27524, 6], [27600, 165], [27824, 6], [27900, 165], [28124, 6], [28200, 165], [28425, 5], [28500, 165], [28725, 4], [28800, 31], [28846, 119], [29026, 3], [29100, 29], [29151, 114], [29326, 3], [29400, 27], [29454, 111], [29626, 3], [29700, 25], [29755, 110], [29927, 2], [30000, 25], [30056, 109], [30227, 2], [30300, 24], [30357, 108], [30527, 3], [30600, 22], [30657, 108], [30828, 2], [30900, 21], [30957, 108], [31128, 3], [31200, 20], [31258, 107], [31429, 2], [31500, 19], [31559, 106], [31729, 2], [31800, 18], [31860, 105], [32029, 3], [32100, 17], [32160, 105], [32330, 2], [32400, 17], [32461, 104], [32630, 2], [32700, 16], [32761, 104], [32930, 3], [33000, 15], [33061, 104], [33230, 3], [33300, 14], [33362, 103], [33531, 3], [33600, 13], [33662, 103], [33831, 3], [33900, 13], [33962, 103], [34131, 3], [34200, 12], [34262, 103], [34431, 4], [34500, 12], [34563, 103], [34731, 4], [34800, 11], [34863, 103], [35031, 4], [35100, 10], [35163, 103], [35331, 5], [35400, 10], [35463, 103], [35630, 6], [35700, 9], [35763, 103], [35930, 7], [36000, 8], [36063, 103], [36230, 7], [36300, 8], [36363, 103], [36530, 7], [36600, 7], [36663, 103], [36829, 9], [36900, 7], [36963, 103], [37129, 9], [37200, 6], [37262, 104], [37429, 10], [37500, 6], [37562, 104], [37729, 10], [37800, 5], [37862, 104], [38028, 11], [38100, 5], [38161, 105], [38328, 12], [38400, 4], [38461, 105], [38628, 12], [38700, 4], [38761, 106], [38927, 13], [39000, 3], [39060, 107], [39227, 14], [39300, 2], [39360, 107], [39527, 14], [39600, 2], [39659, 108], [39827, 15], [39900, 1], [39959, 108], [40126, 16], [40200, 1], [40259, 108], [40426, 17], [40558, 109], [40726, 16], [40858, 109], [41026, 17], [41157, 110], [41325, 18], [41457, 110], [41625, 18], [41757, 110], [41925, 19], [42056, 111], [42224, 20], [42356, 111], [42524, 21], [42655, 112], [42824, 21], [42955, 112], [43123, 22], [43255, 113], [43423, 23], [43554, 114], [43722, 24], [43854, 114], [44021, 25], [44154, 114], [44320, 27], [44453, 115], [44619, 28], [44753, 115], [44919, 29], [45053, 116], [45219, 29], [45352, 117], [45518, 30], [45652, 118], [45818, 31], [45951, 120], [46117, 32], [46251, 123], [46415, 34], [46551, 199], [46850, 200], [47150, 201], [47450, 201], [47749, 202], [48049, 203], [48348, 204], [48648, 204], [48948, 205], [49247, 206], [49547, 207], [49847, 207], [50146, 208], [50446, 209], [50746, 209], [51045, 210], [51345, 211], [51644, 212], [51944, 213], [52244, 213], [52543, 214], [52842, 216], [53100, 1], [53142, 216], [53400, 2], [53441, 217], [53700, 3], [53740, 219], [54000, 4], [54039, 220], [54300, 5], [54338, 222], [54586, 19], [54637, 272], [54931, 7494], [62700, 24], [63000, 23], [63300, 23], [63600, 23], [63900, 22], [64200, 22], [64500, 21], [64800, 21], [65100, 21], [65400, 20], [65700, 20], [66000, 19], [66300, 19], [66600, 19], [66900, 18], [67200, 18], [67500, 17], [67800, 17], [68100, 17], [68400, 16], [68700, 16], [69000, 15], [69300, 15], [69600, 15], [69900, 14], [70200, 14], [70500, 13], [70800, 13], [71100, 13], [71400, 12], [71700, 12], [72000, 11], [72300, 11], [72600, 11], [72900, 10], [73200, 10], [73500, 9], [73800, 9], [74100, 9], [74400, 8], [74700, 8], [75000, 8], [75300, 7], [75600, 7], [75900, 6], [76200, 6], [76500, 6], [76800, 5], [77100, 5], [77400, 4], [77700, 4], [78000, 4], [78300, 3], [78600, 3], [78900, 2], [79200, 2], [79500, 2], [79800, 1], [80100, 1]], "point": [149, 178]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+01.42|+00.99|-01.93|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [10, 111, 55, 146], "mask": [[33020, 11], [33317, 18], [33614, 25], [33913, 28], [34212, 31], [34512, 32], [34811, 3], [34829, 17], [35110, 2], [35134, 12], [35410, 1], [35437, 10], [35739, 8], [36041, 6], [36342, 6], [36643, 6], [36944, 6], [37244, 6], [37544, 7], [37845, 6], [38146, 6], [38447, 5], [38747, 5], [39048, 5], [39348, 5], [39649, 4], [39949, 5], [40250, 4], [40550, 4], [40850, 5], [41151, 4], [41451, 4], [41751, 4], [42052, 4], [42352, 4], [42652, 3], [42953, 2], [43253, 2], [43553, 1]], "point": [34, 117]}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 5}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [78, 19, 297, 144], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16281, 210], [16581, 212], [16880, 214], [17180, 215], [17479, 217], [17779, 218], [18078, 219], [18378, 220], [18678, 220], [18978, 220], [19278, 220], [19578, 220], [19878, 220], [20178, 220], [20478, 220], [20778, 220], [21078, 220], [21378, 220], [21678, 220], [21978, 220], [22278, 220], [22578, 220], [22878, 219], [23178, 219], [23478, 218], [23779, 217], [24079, 216], [24379, 216], [24679, 216], [24979, 215], [25280, 214], [25580, 213], [25880, 213], [26180, 212], [26480, 212], [26781, 210], [27081, 210], [27381, 210], [27681, 209], [27982, 208], [28282, 207], [28582, 207], [28882, 206], [29182, 206], [29483, 204], [29783, 204], [30083, 204], [30383, 203], [30684, 202], [30984, 201], [31284, 201], [31584, 200], [31884, 200], [32185, 198], [32485, 198], [32785, 198], [33085, 197], [33385, 197], [33686, 195], [33986, 195], [34286, 194], [34586, 194], [34887, 192], [35187, 192], [35487, 192], [35787, 191], [36087, 191], [36388, 189], [36688, 189], [36988, 188], [37288, 188], [37588, 188], [37889, 186], [38189, 186], [38489, 185], [38789, 185], [39090, 183], [39390, 183], [39690, 182], [39990, 182], [40290, 182], [40591, 180], [40891, 180], [41191, 179], [41491, 179], [41792, 177], [42092, 177], [42392, 176], [42692, 176], [42992, 175]], "point": [187, 80]}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+01.42|+00.99|-01.93|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 19, 297, 268], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16267, 224], [16567, 226], [16866, 228], [17165, 230], [17465, 231], [17764, 233], [18064, 233], [18363, 235], [18662, 236], [18962, 236], [19261, 237], [19561, 237], [19860, 238], [20160, 238], [20459, 239], [20759, 114], [20882, 116], [21058, 111], [21185, 113], [21358, 110], [21487, 111], [21657, 110], [21789, 109], [21957, 109], [22090, 108], [22256, 110], [22390, 108], [22555, 111], [22691, 107], [22855, 111], [22994, 103], [23154, 112], [23295, 102], [23454, 112], [23590, 2], [23595, 101], [23753, 113], [23890, 3], [23895, 101], [24053, 113], [24190, 3], [24196, 99], [24352, 114], [24490, 3], [24495, 100], [24652, 114], [24790, 3], [24795, 100], [24951, 115], [25089, 4], [25095, 99], [25251, 115], [25389, 3], [25395, 99], [25550, 116], [25689, 3], [25694, 99], [25850, 116], [25989, 3], [25994, 99], [26149, 117], [26289, 2], [26294, 98], [26449, 117], [26588, 3], [26593, 99], [26748, 118], [26888, 2], [26892, 99], [27048, 118], [27188, 1], [27192, 99], [27347, 119], [27488, 1], [27491, 100], [27647, 119], [27791, 99], [27946, 120], [28090, 100], [28246, 120], [28389, 100], [28545, 121], [28687, 102], [28845, 121], [28987, 101], [29144, 122], [29287, 101], [29444, 122], [29587, 100], [29743, 123], [29887, 100], [30043, 123], [30186, 101], [30342, 125], [30486, 100], [30642, 126], [30785, 101], [30941, 128], [31084, 101], [31241, 130], [31382, 103], [31540, 244], [31840, 244], [32139, 244], [32439, 244], [32738, 245], [33038, 244], [33337, 245], [33637, 244], [33936, 245], [34236, 244], [34535, 245], [34835, 244], [35134, 245], [35434, 245], [35733, 245], [36033, 245], [36332, 245], [36632, 245], [36931, 245], [37230, 246], [37530, 246], [37829, 246], [38129, 246], [38428, 246], [38728, 246], [39027, 246], [39327, 246], [39626, 246], [39926, 246], [40225, 247], [40525, 246], [40824, 247], [41124, 74], [41329, 41], [41423, 75], [41630, 40], [41723, 75], [41930, 39], [42022, 75], [42230, 39], [42322, 75], [42531, 37], [42621, 76], [42831, 37], [42921, 76], [43132, 35], [43220, 77], [43520, 76], [43819, 77], [44119, 77], [44418, 78], [44718, 77], [45017, 78], [45317, 78], [45616, 79], [45916, 79], [46215, 79], [46515, 79], [46814, 80], [47114, 80], [47413, 81], [47713, 80], [48012, 81], [48312, 81], [48611, 82], [48911, 82], [49210, 82], [49510, 82], [49809, 83], [50109, 83], [50408, 83], [50708, 83], [51007, 84], [51307, 84], [51606, 85], [51905, 85], [52205, 85], [52504, 86], [52804, 86], [53103, 87], [53403, 86], [53702, 87], [54002, 87], [54301, 88], [54601, 88], [54900, 88], [55200, 88], [55500, 88], [55800, 88], [56100, 87], [56400, 87], [56700, 87], [57000, 87], [57300, 87], [57600, 86], [57900, 86], [58200, 86], [58500, 86], [58800, 86], [59100, 85], [59400, 85], [59700, 85], [60000, 85], [60300, 85], [60600, 84], [60900, 84], [61200, 84], [61500, 84], [61800, 83], [62100, 83], [62400, 83], [62700, 83], [63000, 83], [63300, 82], [63600, 82], [63900, 82], [64200, 82], [64501, 81], [64801, 80], [65102, 79], [65403, 78], [65704, 77], [66005, 76], [66305, 75], [66606, 74], [66907, 73], [67208, 72], [67508, 71], [67809, 70], [68110, 69], [68411, 68], [68711, 68], [69012, 66], [69313, 65], [69614, 64], [69915, 63], [70215, 63], [70516, 61], [70817, 60], [71118, 59], [71418, 59], [71719, 58], [72020, 56], [72321, 55], [72621, 55], [72922, 54], [73223, 52], [73524, 51], [73824, 51], [74125, 50], [74426, 49], [74727, 47], [75028, 46], [75328, 46], [75629, 45], [75930, 44], [76231, 42], [76531, 42], [76832, 41], [77133, 40], [77434, 39], [77734, 38], [78035, 40], [78336, 41], [78637, 41], [78938, 33], [78972, 6], [79238, 33], [79273, 5], [79540, 31], [79574, 2], [79844, 27], [80148, 22]], "point": [148, 136]}}, "high_idx": 6}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 297, 268], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16267, 224], [16567, 226], [16866, 228], [17165, 230], [17465, 231], [17764, 233], [18064, 233], [18363, 235], [18662, 236], [18962, 236], [19261, 237], [19561, 237], [19860, 238], [20160, 238], [20459, 239], [20759, 114], [20882, 116], [21058, 111], [21185, 113], [21358, 87], [21452, 16], [21487, 111], [21657, 83], [21757, 10], [21789, 109], [21957, 81], [22060, 6], [22090, 108], [22256, 80], [22362, 4], [22390, 108], [22555, 80], [22664, 2], [22691, 107], [22855, 78], [22965, 1], [22994, 103], [23154, 79], [23295, 102], [23454, 78], [23590, 2], [23595, 101], [23753, 78], [23890, 3], [23895, 101], [24053, 78], [24190, 3], [24196, 99], [24352, 78], [24490, 3], [24495, 100], [24652, 78], [24790, 3], [24795, 100], [24951, 79], [25089, 4], [25095, 99], [25251, 79], [25389, 3], [25395, 99], [25550, 79], [25689, 3], [25694, 99], [25850, 79], [25989, 3], [25994, 99], [26149, 80], [26289, 2], [26294, 98], [26449, 79], [26588, 3], [26593, 99], [26748, 80], [26888, 2], [26892, 99], [27048, 80], [27188, 1], [27192, 99], [27347, 81], [27488, 1], [27491, 100], [27647, 81], [27791, 99], [27946, 82], [28090, 100], [28246, 82], [28389, 100], [28545, 83], [28687, 102], [28845, 83], [28987, 101], [29144, 84], [29287, 101], [29444, 84], [29587, 100], [29743, 85], [29887, 100], [30043, 85], [30186, 101], [30342, 86], [30486, 100], [30642, 86], [30785, 101], [30941, 87], [31084, 101], [31241, 87], [31369, 2], [31382, 103], [31540, 88], [31669, 115], [31840, 89], [31969, 115], [32139, 90], [32269, 114], [32439, 90], [32568, 115], [32738, 91], [32868, 115], [33038, 91], [33168, 114], [33337, 92], [33468, 114], [33637, 92], [33768, 113], [33936, 94], [34067, 114], [34236, 95], [34367, 113], [34535, 245], [34835, 244], [35134, 245], [35434, 245], [35733, 245], [36033, 245], [36332, 245], [36632, 245], [36931, 245], [37230, 246], [37530, 246], [37829, 246], [38129, 246], [38428, 246], [38728, 246], [39027, 246], [39327, 246], [39626, 246], [39926, 246], [40225, 247], [40525, 246], [40824, 247], [41124, 74], [41329, 41], [41423, 75], [41630, 40], [41723, 75], [41930, 39], [42022, 75], [42230, 39], [42322, 75], [42531, 37], [42621, 76], [42831, 37], [42921, 76], [43132, 35], [43220, 77], [43520, 76], [43819, 77], [44119, 77], [44418, 78], [44718, 77], [45017, 78], [45317, 78], [45616, 79], [45916, 79], [46215, 79], [46515, 79], [46814, 80], [47114, 80], [47413, 81], [47713, 80], [48012, 81], [48312, 81], [48611, 82], [48911, 82], [49210, 82], [49510, 82], [49809, 83], [50109, 83], [50408, 83], [50708, 83], [51007, 84], [51307, 84], [51606, 85], [51905, 85], [52205, 85], [52504, 86], [52804, 86], [53103, 87], [53403, 86], [53702, 87], [54002, 87], [54301, 88], [54601, 88], [54900, 88], [55200, 88], [55500, 88], [55800, 88], [56100, 87], [56400, 87], [56700, 87], [57000, 87], [57300, 87], [57600, 86], [57900, 86], [58200, 86], [58500, 86], [58800, 86], [59100, 85], [59400, 85], [59700, 85], [60000, 85], [60300, 85], [60600, 84], [60900, 84], [61200, 84], [61500, 84], [61800, 83], [62100, 83], [62400, 83], [62700, 83], [63000, 83], [63300, 82], [63600, 82], [63900, 82], [64200, 82], [64501, 81], [64801, 80], [65102, 79], [65403, 78], [65704, 77], [66005, 76], [66305, 75], [66606, 74], [66907, 73], [67208, 72], [67508, 71], [67809, 70], [68110, 69], [68411, 68], [68711, 68], [69012, 66], [69313, 65], [69614, 64], [69915, 63], [70215, 63], [70516, 61], [70817, 60], [71118, 59], [71418, 59], [71719, 58], [72020, 56], [72321, 55], [72621, 55], [72922, 54], [73223, 52], [73524, 51], [73824, 51], [74125, 50], [74426, 49], [74727, 47], [75028, 46], [75328, 46], [75629, 45], [75930, 44], [76231, 42], [76531, 42], [76832, 41], [77133, 40], [77434, 39], [77734, 38], [78035, 40], [78336, 41], [78637, 41], [78938, 33], [78972, 6], [79238, 33], [79273, 5], [79540, 31], [79574, 2], [79844, 27], [80148, 22]], "point": [148, 136]}}, "high_idx": 6}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.308, 7.308, -5.416, -5.416, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [78, 19, 297, 144], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16281, 210], [16581, 212], [16880, 214], [17180, 215], [17479, 217], [17779, 218], [18078, 219], [18378, 220], [18678, 220], [18978, 220], [19278, 220], [19578, 220], [19878, 220], [20178, 220], [20478, 220], [20778, 220], [21078, 220], [21378, 220], [21678, 220], [21978, 220], [22278, 220], [22578, 220], [22878, 219], [23178, 219], [23478, 218], [23779, 217], [24079, 216], [24379, 216], [24679, 216], [24979, 215], [25280, 214], [25580, 213], [25880, 213], [26180, 212], [26480, 212], [26781, 210], [27081, 210], [27381, 210], [27681, 209], [27982, 208], [28282, 207], [28582, 207], [28882, 206], [29182, 206], [29483, 204], [29783, 204], [30083, 204], [30383, 203], [30684, 202], [30984, 201], [31284, 201], [31584, 200], [31884, 200], [32185, 198], [32485, 198], [32785, 198], [33085, 197], [33385, 197], [33686, 195], [33986, 195], [34286, 194], [34586, 194], [34887, 192], [35187, 192], [35487, 192], [35787, 191], [36087, 191], [36388, 189], [36688, 189], [36988, 188], [37288, 188], [37588, 188], [37889, 186], [38189, 186], [38489, 185], [38789, 185], [39090, 183], [39390, 183], [39690, 182], [39990, 182], [40290, 182], [40591, 180], [40891, 180], [41191, 179], [41491, 179], [41792, 177], [42092, 177], [42392, 176], [42692, 176], [42992, 175]], "point": [187, 80]}}, "high_idx": 6}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.308, 7.308, -5.416, -5.416, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [78, 19, 297, 144], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16281, 210], [16581, 212], [16880, 214], [17180, 215], [17479, 217], [17779, 218], [18078, 219], [18378, 220], [18678, 220], [18978, 220], [19278, 220], [19578, 220], [19878, 220], [20178, 220], [20478, 220], [20778, 220], [21078, 220], [21378, 220], [21678, 220], [21978, 220], [22278, 220], [22578, 220], [22878, 219], [23178, 219], [23478, 218], [23779, 217], [24079, 216], [24379, 216], [24679, 216], [24979, 215], [25280, 214], [25580, 213], [25880, 213], [26180, 212], [26480, 212], [26781, 210], [27081, 210], [27381, 210], [27681, 209], [27982, 208], [28282, 207], [28582, 207], [28882, 206], [29182, 206], [29483, 204], [29783, 204], [30083, 204], [30383, 203], [30684, 202], [30984, 201], [31284, 201], [31584, 200], [31884, 200], [32185, 198], [32485, 198], [32785, 198], [33085, 197], [33385, 197], [33686, 195], [33986, 195], [34286, 194], [34586, 194], [34887, 192], [35187, 192], [35487, 192], [35787, 191], [36087, 191], [36388, 189], [36688, 189], [36988, 188], [37288, 188], [37588, 188], [37889, 186], [38189, 186], [38489, 185], [38789, 185], [39090, 183], [39390, 183], [39690, 182], [39990, 182], [40290, 182], [40591, 180], [40891, 180], [41191, 179], [41491, 179], [41792, 177], [42092, 177], [42392, 176], [42692, 176], [42992, 175]], "point": [187, 80]}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [78, 19, 297, 144], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16281, 210], [16581, 212], [16880, 214], [17180, 215], [17479, 217], [17779, 218], [18078, 219], [18378, 220], [18678, 220], [18978, 220], [19278, 220], [19578, 220], [19878, 220], [20178, 220], [20478, 220], [20778, 220], [21078, 220], [21378, 220], [21678, 220], [21978, 220], [22278, 220], [22578, 220], [22878, 219], [23178, 219], [23478, 218], [23779, 217], [24079, 216], [24379, 216], [24679, 216], [24979, 215], [25280, 214], [25580, 213], [25880, 213], [26180, 212], [26480, 212], [26781, 210], [27081, 210], [27381, 210], [27681, 209], [27982, 208], [28282, 207], [28582, 207], [28882, 206], [29182, 206], [29483, 204], [29783, 204], [30083, 204], [30383, 203], [30684, 202], [30984, 201], [31284, 201], [31584, 200], [31884, 200], [32185, 198], [32485, 198], [32785, 198], [33085, 197], [33385, 197], [33686, 195], [33986, 195], [34286, 194], [34586, 194], [34887, 192], [35187, 192], [35487, 192], [35787, 191], [36087, 191], [36388, 189], [36688, 189], [36988, 188], [37288, 188], [37588, 188], [37889, 186], [38189, 186], [38489, 185], [38789, 185], [39090, 183], [39390, 183], [39690, 182], [39990, 182], [40290, 182], [40591, 180], [40891, 180], [41191, 179], [41491, 179], [41792, 177], [42092, 177], [42392, 176], [42692, 176], [42992, 175]], "point": [187, 80]}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+01.42|+00.99|-01.93|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [128, 72, 168, 115], "mask": [[21445, 7], [21740, 17], [22038, 22], [22336, 26], [22635, 29], [22933, 32], [23233, 33], [23532, 35], [23831, 36], [24131, 37], [24430, 38], [24730, 38], [25030, 38], [25330, 38], [25629, 39], [25929, 40], [26229, 40], [26528, 41], [26828, 41], [27128, 41], [27428, 41], [27728, 41], [28028, 41], [28328, 41], [28628, 41], [28928, 41], [29228, 41], [29528, 41], [29828, 41], [30128, 41], [30428, 41], [30728, 41], [31028, 41], [31328, 41], [31628, 41], [31929, 40], [32229, 40], [32529, 39], [32829, 39], [33129, 39], [33429, 39], [33729, 39], [34030, 37], [34331, 36]], "point": [148, 92]}}, "high_idx": 6}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 297, 268], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16267, 224], [16567, 226], [16866, 228], [17165, 230], [17465, 231], [17764, 233], [18064, 233], [18363, 235], [18662, 236], [18962, 236], [19261, 237], [19561, 237], [19860, 238], [20160, 238], [20459, 239], [20759, 114], [20882, 116], [21058, 111], [21185, 113], [21358, 110], [21487, 111], [21657, 110], [21789, 109], [21957, 109], [22090, 108], [22256, 110], [22390, 108], [22555, 111], [22691, 107], [22855, 111], [22994, 103], [23154, 112], [23295, 102], [23454, 112], [23590, 2], [23595, 101], [23753, 113], [23890, 3], [23895, 101], [24053, 113], [24190, 3], [24196, 99], [24352, 114], [24490, 3], [24495, 100], [24652, 114], [24790, 3], [24795, 100], [24951, 115], [25089, 4], [25095, 99], [25251, 115], [25389, 3], [25395, 99], [25550, 116], [25689, 3], [25694, 99], [25850, 116], [25989, 3], [25994, 99], [26149, 117], [26289, 2], [26294, 98], [26449, 117], [26588, 3], [26593, 99], [26748, 118], [26888, 2], [26892, 99], [27048, 118], [27188, 1], [27192, 99], [27347, 119], [27488, 1], [27491, 100], [27647, 119], [27791, 99], [27946, 120], [28090, 100], [28246, 120], [28389, 100], [28545, 121], [28687, 102], [28845, 121], [28987, 101], [29144, 122], [29287, 101], [29444, 122], [29587, 100], [29743, 123], [29887, 100], [30043, 123], [30186, 101], [30342, 125], [30486, 100], [30642, 126], [30785, 101], [30941, 128], [31084, 101], [31241, 130], [31382, 103], [31540, 244], [31840, 244], [32139, 244], [32439, 244], [32738, 245], [33038, 244], [33337, 245], [33637, 244], [33936, 245], [34236, 244], [34535, 245], [34835, 244], [35134, 245], [35434, 245], [35733, 245], [36033, 245], [36332, 245], [36632, 245], [36931, 245], [37230, 246], [37530, 246], [37829, 246], [38129, 246], [38428, 246], [38728, 246], [39027, 246], [39327, 246], [39626, 246], [39926, 246], [40225, 247], [40525, 246], [40824, 247], [41124, 74], [41329, 41], [41423, 75], [41630, 40], [41723, 75], [41930, 39], [42022, 75], [42230, 39], [42322, 75], [42531, 37], [42621, 76], [42831, 37], [42921, 76], [43132, 35], [43220, 77], [43520, 76], [43819, 77], [44119, 77], [44418, 78], [44718, 77], [45017, 78], [45317, 78], [45616, 79], [45916, 79], [46215, 79], [46515, 79], [46814, 80], [47114, 80], [47413, 81], [47713, 80], [48012, 81], [48312, 81], [48611, 82], [48911, 82], [49210, 82], [49510, 82], [49809, 83], [50109, 83], [50408, 83], [50708, 83], [51007, 84], [51307, 84], [51606, 85], [51905, 85], [52205, 85], [52504, 86], [52804, 86], [53103, 87], [53403, 86], [53702, 87], [54002, 87], [54301, 88], [54601, 88], [54900, 88], [55200, 88], [55500, 88], [55800, 88], [56100, 87], [56400, 87], [56700, 87], [57000, 87], [57300, 87], [57600, 86], [57900, 86], [58200, 86], [58500, 86], [58800, 86], [59100, 85], [59400, 85], [59700, 85], [60000, 85], [60300, 85], [60600, 84], [60900, 84], [61200, 84], [61500, 84], [61800, 83], [62100, 83], [62400, 83], [62700, 83], [63000, 83], [63300, 82], [63600, 82], [63900, 82], [64200, 82], [64501, 81], [64801, 80], [65102, 79], [65403, 78], [65704, 77], [66005, 76], [66305, 75], [66606, 74], [66907, 73], [67208, 72], [67508, 71], [67809, 70], [68110, 69], [68411, 68], [68711, 68], [69012, 66], [69313, 65], [69614, 64], [69915, 63], [70215, 63], [70516, 61], [70817, 60], [71118, 59], [71418, 59], [71719, 58], [72020, 56], [72321, 55], [72621, 55], [72922, 54], [73223, 52], [73524, 51], [73824, 51], [74125, 50], [74426, 49], [74727, 47], [75028, 46], [75328, 46], [75629, 45], [75930, 44], [76231, 42], [76531, 42], [76832, 41], [77133, 40], [77434, 39], [77734, 38], [78035, 40], [78336, 41], [78637, 41], [78938, 33], [78972, 6], [79238, 33], [79273, 5], [79540, 31], [79574, 2], [79844, 27], [80148, 22]], "point": [148, 136]}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 7}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 7}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+01.98|+00.00|-00.54"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 236], "mask": [[0, 41700], [41701, 299], [42002, 298], [42303, 297], [42604, 296], [42905, 295], [43206, 294], [43507, 293], [43807, 293], [44108, 292], [44409, 291], [44710, 290], [45011, 289], [45312, 288], [45613, 287], [45914, 286], [46215, 285], [46516, 284], [46817, 283], [47118, 282], [47419, 281], [47720, 280], [48020, 280], [48321, 279], [48622, 278], [48923, 277], [49224, 276], [49525, 275], [49826, 274], [50127, 273], [50428, 272], [50729, 271], [51030, 270], [51331, 269], [51632, 268], [51933, 266], [52233, 265], [52534, 263], [52835, 261], [53136, 258], [53437, 256], [53738, 254], [54039, 252], [54340, 250], [54641, 247], [54942, 245], [55243, 243], [55544, 241], [55845, 239], [56146, 237], [56446, 235], [56747, 233], [57048, 231], [57349, 229], [57650, 227], [57951, 225], [58252, 222], [58553, 220], [58854, 218], [59155, 216], [59456, 214], [59757, 211], [60058, 209], [60359, 207], [60659, 206], [60960, 204], [61261, 202], [61562, 199], [61863, 197], [62164, 195], [62465, 193], [62766, 191], [63067, 188], [63368, 186], [63669, 184], [63970, 181], [64271, 176], [64572, 174], [64872, 173], [65173, 170], [65474, 168], [65775, 166], [66076, 164], [66377, 162], [66678, 160], [66979, 158], [67280, 61], [67363, 73], [67581, 49], [67672, 62], [67882, 42], [67975, 58], [68183, 36], [68278, 54], [68484, 32], [68581, 50], [68785, 28], [68884, 46], [69089, 22], [69186, 40], [69394, 14], [69488, 32], [69700, 7], [69789, 26], [70005, 1], [70090, 19], [70391, 12], [70693, 4]], "point": [149, 117]}}, "high_idx": 8}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+01.42|+00.99|-01.93|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|+01.98|+00.00|-00.54"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 145], [193, 251], [494, 248], [796, 245], [1097, 243], [1398, 241], [1699, 239], [2000, 237], [2301, 236], [2602, 234], [2903, 233], [3203, 232], [3504, 231], [3804, 231], [4104, 231], [4405, 230], [4705, 230], [5005, 230], [5305, 230], [5605, 230], [5905, 230], [6205, 231], [6504, 232], [6804, 232], [7104, 232], [7403, 234], [7703, 234], [8002, 235], [8302, 235], [8602, 236], [8901, 237], [9201, 238], [9500, 239], [9800, 239], [10099, 241], [10399, 241], [10698, 243], [10998, 243], [11297, 244], [11597, 245], [11896, 247], [12195, 249], [12495, 250], [12794, 252], [13092, 255], [13391, 258], [13690, 260], [13988, 263], [14287, 266], [14585, 270], [14883, 274], [15181, 280], [15477, 11030], [26518, 73], [26642, 165], [26817, 74], [26943, 164], [27117, 74], [27243, 165], [27416, 75], [27543, 165], [27716, 75], [27843, 166], [28015, 76], [28143, 167], [28314, 78], [28443, 249], [28743, 249], [29043, 249], [29343, 249], [29643, 249], [29942, 251], [30242, 251], [30541, 252], [30841, 253], [31140, 254], [31439, 256], [31738, 257], [32037, 259], [32337, 260], [32636, 262], [32935, 264], [33233, 267], [33532, 270], [33831, 272], [34130, 275], [34428, 280], [34726, 286], [35022, 7877], [42900, 298], [43200, 297], [43500, 296], [43800, 295], [44100, 294], [44400, 293], [44700, 292], [45000, 291], [45300, 291], [45600, 290], [45900, 289], [46200, 288], [46500, 287], [46800, 286], [47100, 285], [47400, 284], [47700, 283], [48000, 282], [48300, 281], [48600, 280], [48900, 279], [49200, 278], [49500, 277], [49800, 276], [50100, 275], [50400, 274], [50700, 273], [51000, 273], [51300, 272], [51600, 271], [51900, 270], [52200, 269], [52500, 268], [52800, 267], [53100, 266], [53400, 265], [53700, 264], [54000, 263], [54300, 262], [54600, 261], [54900, 260], [55200, 259], [55500, 258], [55800, 257], [56100, 256], [56400, 256], [56700, 255], [57000, 254], [57300, 253], [57600, 252], [57900, 251], [58200, 250], [58500, 249], [58800, 248], [59100, 247], [59400, 246], [59700, 245], [60000, 244], [60300, 243], [60600, 242], [60900, 241], [61200, 240], [61500, 239], [61800, 238], [62100, 238], [62400, 237], [62700, 236], [63000, 235], [63300, 234], [63600, 233], [63900, 232], [64200, 231], [64500, 230], [64800, 229], [65100, 227], [65400, 92], [65700, 93], [66000, 93], [66300, 93], [66600, 93], [66900, 93], [67200, 93], [67500, 92], [67800, 92], [68100, 92], [68400, 92], [68700, 92], [69000, 92], [69300, 92], [69600, 92], [69900, 92], [70200, 92], [70500, 91], [70800, 91], [71100, 91], [71400, 91], [71700, 91], [72000, 91], [72300, 91], [72600, 91], [72900, 91], [73200, 90], [73500, 90], [73800, 90], [74100, 90], [74400, 90], [74700, 90], [75000, 90], [75300, 90], [75600, 90], [75900, 89], [76200, 89], [76500, 89], [76800, 89], [77100, 89], [77400, 89], [77700, 89], [78000, 89], [78300, 89], [78600, 88], [78900, 88], [79200, 88], [79500, 88], [79800, 88], [80100, 88], [80400, 88], [80700, 88], [81000, 88], [81300, 87], [81600, 87], [81900, 87], [82200, 87], [82500, 87], [82800, 87], [83100, 87], [83400, 87], [83700, 87], [84000, 86], [84300, 86], [84600, 86], [84900, 86], [85200, 86], [85500, 86], [85800, 86], [86100, 86], [86400, 86], [86700, 85], [87000, 85], [87300, 85], [87600, 85], [87900, 85], [88200, 85], [88500, 85], [88800, 85], [89100, 85], [89400, 85], [89700, 84]], "point": [149, 149]}}, "high_idx": 8}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+01.98|+00.00|-00.54"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 145], [193, 251], [494, 248], [796, 245], [1097, 243], [1398, 241], [1699, 239], [2000, 237], [2301, 236], [2602, 234], [2903, 233], [3203, 232], [3504, 231], [3804, 231], [4104, 231], [4405, 230], [4705, 230], [5005, 230], [5305, 230], [5605, 230], [5905, 230], [6205, 231], [6504, 232], [6804, 232], [7104, 232], [7403, 234], [7703, 234], [8002, 235], [8302, 235], [8602, 236], [8901, 237], [9201, 238], [9500, 239], [9800, 239], [10099, 241], [10399, 241], [10698, 243], [10998, 243], [11297, 244], [11597, 245], [11896, 247], [12195, 249], [12495, 250], [12794, 252], [13092, 255], [13391, 258], [13690, 260], [13988, 263], [14287, 266], [14585, 270], [14883, 274], [15181, 280], [15477, 11030], [26518, 73], [26642, 165], [26817, 74], [26943, 164], [27117, 74], [27243, 165], [27416, 75], [27543, 165], [27716, 75], [27843, 166], [28015, 76], [28143, 167], [28314, 78], [28443, 187], [28643, 49], [28743, 183], [28948, 44], [29043, 180], [29251, 41], [29343, 178], [29553, 39], [29643, 177], [29855, 37], [29942, 178], [30156, 37], [30242, 177], [30456, 37], [30541, 177], [30757, 36], [30841, 177], [31057, 37], [31140, 178], [31357, 37], [31439, 179], [31657, 38], [31738, 179], [31958, 37], [32037, 180], [32258, 38], [32337, 180], [32558, 39], [32636, 180], [32858, 40], [32935, 181], [33158, 41], [33233, 183], [33458, 42], [33532, 184], [33758, 44], [33831, 185], [34058, 45], [34130, 186], [34358, 47], [34428, 188], [34658, 50], [34726, 190], [34958, 54], [35022, 194], [35258, 258], [35558, 259], [35858, 259], [36158, 259], [36458, 259], [36758, 259], [37058, 260], [37358, 260], [37658, 260], [37958, 260], [38257, 262], [38557, 262], [38857, 265], [39154, 3745], [42900, 298], [43200, 297], [43500, 296], [43800, 295], [44100, 294], [44400, 293], [44700, 292], [45000, 291], [45300, 291], [45600, 290], [45900, 289], [46200, 288], [46500, 287], [46800, 286], [47100, 285], [47400, 284], [47700, 283], [48000, 282], [48300, 281], [48600, 280], [48900, 279], [49200, 278], [49500, 277], [49800, 276], [50100, 275], [50400, 274], [50700, 273], [51000, 273], [51300, 272], [51600, 271], [51900, 270], [52200, 269], [52500, 268], [52800, 267], [53100, 266], [53400, 265], [53700, 264], [54000, 263], [54300, 262], [54600, 261], [54900, 260], [55200, 259], [55500, 258], [55800, 257], [56100, 256], [56400, 256], [56700, 255], [57000, 254], [57300, 253], [57600, 252], [57900, 251], [58200, 250], [58500, 249], [58800, 248], [59100, 247], [59400, 246], [59700, 245], [60000, 244], [60300, 243], [60600, 242], [60900, 241], [61200, 240], [61500, 239], [61800, 238], [62100, 238], [62400, 237], [62700, 236], [63000, 235], [63300, 234], [63600, 233], [63900, 232], [64200, 231], [64500, 230], [64800, 229], [65100, 227], [65400, 92], [65700, 93], [66000, 93], [66300, 93], [66600, 93], [66900, 93], [67200, 93], [67500, 92], [67800, 92], [68100, 92], [68400, 92], [68700, 92], [69000, 92], [69300, 92], [69600, 92], [69900, 92], [70200, 92], [70500, 91], [70800, 91], [71100, 91], [71400, 91], [71700, 91], [72000, 91], [72300, 91], [72600, 91], [72900, 91], [73200, 90], [73500, 90], [73800, 90], [74100, 90], [74400, 90], [74700, 90], [75000, 90], [75300, 90], [75600, 90], [75900, 89], [76200, 89], [76500, 89], [76800, 89], [77100, 89], [77400, 89], [77700, 89], [78000, 89], [78300, 89], [78600, 88], [78900, 88], [79200, 88], [79500, 88], [79800, 88], [80100, 88], [80400, 88], [80700, 88], [81000, 88], [81300, 87], [81600, 87], [81900, 87], [82200, 87], [82500, 87], [82800, 87], [83100, 87], [83400, 87], [83700, 87], [84000, 86], [84300, 86], [84600, 86], [84900, 86], [85200, 86], [85500, 86], [85800, 86], [86100, 86], [86400, 86], [86700, 85], [87000, 85], [87300, 85], [87600, 85], [87900, 85], [88200, 85], [88500, 85], [88800, 85], [89100, 85], [89400, 85], [89700, 84]], "point": [149, 149]}}, "high_idx": 8}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan5", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 1.5, "y": 0.9009992, "z": 1.25}, "object_poses": [{"objectName": "Statue_bb70ba4e", "position": {"x": -1.32211292, "y": 0.9183639, "z": -1.46934056}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Kettle_7c4fce22", "position": {"x": 2.79846668, "y": 0.5530998, "z": 0.09032591}, "rotation": {"x": 3.621837e-06, "y": 270.000336, "z": 3.62185733e-06}}, {"objectName": "Kettle_7c4fce22", "position": {"x": -0.420080632, "y": 0.914541245, "z": 0.294503123}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spatula_ef52a4ea", "position": {"x": -0.2527125, "y": 0.711996, "z": 0.09828753}, "rotation": {"x": 7.0167104e-15, "y": 90.0, "z": -7.01670955e-15}}, {"objectName": "Fork_66b6c857", "position": {"x": -1.32211292, "y": 0.9118485, "z": -1.8069706}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_80376f38", "position": {"x": -0.997205555, "y": 0.9003024, "z": -0.04844135}, "rotation": {"x": 0.280805469, "y": -0.00198041555, "z": 359.882721}}, {"objectName": "Spoon_80376f38", "position": {"x": 0.0129125118, "y": 0.698125541, "z": 0.139693767}, "rotation": {"x": 7.01670955e-15, "y": 180.0, "z": 7.01670955e-15}}, {"objectName": "Knife_84756f6d", "position": {"x": -0.849135458, "y": 0.9370277, "z": 0.08309689}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_84756f6d", "position": {"x": 1.194489, "y": 0.9348277, "z": -1.92935038}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_99ef625f", "position": {"x": 1.74612522, "y": 0.11698544, "z": -1.1363461}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_99ef625f", "position": {"x": 2.491201, "y": 0.8799294, "z": 0.08706522}, "rotation": {"x": 3.415104e-06, "y": 180.0003, "z": -3.4150853e-06}}, {"objectName": "Bowl_4de763d6", "position": {"x": -0.290060669, "y": 0.7604714, "z": -2.06327963}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_4de763d6", "position": {"x": 1.89104843, "y": 0.8142541, "z": -0.7374977}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SaltShaker_52c85e6d", "position": {"x": 3.00296783, "y": 0.8761374, "z": 0.168566868}, "rotation": {"x": 3.415104e-06, "y": 180.0003, "z": -3.4150853e-06}}, {"objectName": "Bread_0d0ef808", "position": {"x": 1.41928542, "y": 0.993259966, "z": -1.92935038}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_9396b7e6", "position": {"x": 2.00055718, "y": 0.8936437, "z": -0.3650822}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_3526507b", "position": {"x": -0.375432163, "y": 1.18008864, "z": 0.662104}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_14c38bc8", "position": {"x": -1.2899, "y": 0.957, "z": -0.756}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Knife_84756f6d", "position": {"x": -0.5988306, "y": 1.15502787, "z": 0.618002}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_4de763d6", "position": {"x": 1.89104807, "y": 1.09590423, "z": -0.5512897}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Fork_66b6c857", "position": {"x": -0.319118738, "y": 0.696219742, "z": 0.139693767}, "rotation": {"x": 7.0167104e-15, "y": 90.0, "z": -7.01670955e-15}}, {"objectName": "Cup_11735285", "position": {"x": 0.1015501, "y": 0.750666142, "z": -1.9382143}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_43faf0e4", "position": {"x": -0.679746747, "y": 0.7313538, "z": 0.190700009}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_3526507b", "position": {"x": -0.117366061, "y": 0.811944664, "z": -1.8974402}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7c4fce22", "position": {"x": -1.0561, "y": 0.956400037, "z": -1.127}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_777c51a0", "position": {"x": 2.695908, "y": 0.881990552, "z": 0.141400471}, "rotation": {"x": 3.415104e-06, "y": 180.0003, "z": -3.4150853e-06}}, {"objectName": "Potato_8a9da01b", "position": {"x": -1.16388893, "y": 0.9396353, "z": -0.4157109}, "rotation": {"x": 358.240936, "y": 1.975984e-05, "z": -0.001809607}}, {"objectName": "Pot_b40d1b1e", "position": {"x": -1.089, "y": 0.9, "z": 0.235}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_0d0ef808", "position": {"x": -1.14827466, "y": 0.981639266, "z": -0.184534878}, "rotation": {"x": 0.1432648, "y": 0.347935349, "z": 0.176838547}}, {"objectName": "Plate_c0097cec", "position": {"x": 0.02274999, "y": 0.1146639, "z": 0.163000017}, "rotation": {"x": 7.01670955e-15, "y": 180.0, "z": 7.01670955e-15}}, {"objectName": "Spatula_ef52a4ea", "position": {"x": -1.018377, "y": 0.9262998, "z": -1.92959273}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_e7f3845e", "position": {"x": 2.59106636, "y": 0.656343937, "z": 0.0385534}, "rotation": {"x": 3.415104e-06, "y": 180.0003, "z": -3.4150853e-06}}, {"objectName": "Vase_f4e7dd3c", "position": {"x": 1.52884161, "y": 1.65980649, "z": -2.20033884}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_c00db6cc", "position": {"x": 0.00897419453, "y": 0.9471739, "z": 0.294503123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_99ef625f", "position": {"x": -0.61200124, "y": 0.112473369, "z": 0.260728955}, "rotation": {"x": 0.0, "y": 179.999741, "z": 0.0}}, {"objectName": "SaltShaker_52c85e6d", "position": {"x": -0.725746751, "y": 0.6883444, "z": -2.02354431}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_9396b7e6", "position": {"x": 1.8910476, "y": 1.5042938, "z": -0.7374961}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "PepperShaker_b0cc376f", "position": {"x": -0.623526633, "y": 0.69072, "z": -1.97936285}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_1a7bf448", "position": {"x": -0.375432163, "y": 1.13154221, "z": 0.441594}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_42dba209", "position": {"x": 1.924, "y": 0.06268937, "z": 0.166980162}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_3478a6e4", "position": {"x": -1.02899992, "y": 0.133775234, "z": -0.158944741}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_80376f38", "position": {"x": -0.117366061, "y": 0.764485538, "z": -2.06327963}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_74fc855a", "position": {"x": 2.00055718, "y": 1.47734952, "z": -0.458186746}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_8066794b", "position": {"x": 1.84610152, "y": 1.01045275, "z": -1.35028613}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Statue_bb70ba4e", "position": {"x": -1.33056784, "y": 0.920563936, "z": -0.353479475}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2299346848, "scene_num": 5}, "task_id": "trial_T20190908_180811_086871", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2A028LRDJB7ZB_3EFVCAY5L60MONKZ1PIPVX25Q3AJ8E", "high_descs": ["Turn right,make steps then turn left head to the counter beside the toaster", "Pick up the knife on the counter", "Slice the bread on the counter", "Drop the knife on the counter beside the toaster", "Pick up the slice of bread", "Turn left then head to the microwave", "Open the microwave put in and out the sliced bread then close the microwave", "Turn left and head to the fridge", "Open the fridge and put in the sliced bread then close the fridge"], "task_desc": "Put the heated sliced bread in the fridge", "votes": [1, 1]}, {"assignment_id": "A3C81THYYSBGVD_3PZDLQMM0WCOUUC4BGS2Y2C7EJZC2G", "high_descs": ["Turn right and face the counter with the toaster in front of you.", "Take the knife from the counter in front of you.", "Slice the bread on the counter in front of you.", "Place the knife on the counter in front of you.", "Take a slice of bread from the counter in front of you.", "Turn left and face the microwave in front of you.", "Heat up the bread in the microwave in front of you then take it out.", "Turn left then face the fridge on your right.", "Place the slice of bread in the fridge then close it."], "task_desc": "Place a warm piece of bread in the fridge.", "votes": [1, 1]}, {"assignment_id": "A2A4UAFZ5LW71K_3Z7EFSHGNC5PVAF1Z5DVOS63RTTCXM", "high_descs": ["turn right, walk towards fridge, turn right, take a step, turn left, walk to the counter", "pick up the knife that is on the counter", "slice the bread that is on the counter", "put the knife back on the counter", "pick up a piece of bread that is on the counter", "turn left to face the microwave", "open microwave, put bread in, close it, turn it on, open and take the bread out, close it", "turn left, take a few steps, turn right to face the fridge", "open fridge, put bread on the shelf, close fridge"], "task_desc": "warm up a sliced piece of bread to put in the fridge", "votes": [1, 1]}]}}