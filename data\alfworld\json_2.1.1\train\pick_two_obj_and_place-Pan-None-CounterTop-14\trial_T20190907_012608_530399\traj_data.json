{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000161.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000162.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000163.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000165.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000191.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000192.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000193.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000194.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000195.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000197.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000198.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000199.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000200.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000203.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000205.png", "low_idx": 31}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Pan", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["stoveburner"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|-1|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["pan"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pan", [6.1732, 6.1732, -1.2812, -1.2812, 3.6392, 3.6392]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", [6.1732, 6.1732, -1.2812, -1.2812, 3.628, 3.628]], "forceVisible": true, "objectId": "Pan|+01.54|+00.91|-00.32"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["pan", "countertop"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pan", [6.1732, 6.1732, -1.2812, -1.2812, 3.6392, 3.6392]], "coordinateReceptacleObjectId": ["CounterTop", [3.956, 3.956, 4.068, 4.068, 3.7844, 3.7844]], "forceVisible": true, "objectId": "Pan|+01.54|+00.91|-00.32", "receptacleObjectId": "CounterTop|+00.99|+00.95|+01.02"}}, {"discrete_action": {"action": "GotoLocation", "args": ["stoveburner"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|4|-1|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["pan"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pan", [7.228, 7.228, -1.2812, -1.2812, 3.6392, 3.6392]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", [7.228, 7.228, -1.2812, -1.2812, 3.628, 3.628]], "forceVisible": true, "objectId": "Pan|+01.81|+00.91|-00.32"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|4|1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["pan", "countertop"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pan", [7.228, 7.228, -1.2812, -1.2812, 3.6392, 3.6392]], "coordinateReceptacleObjectId": ["CounterTop", [3.956, 3.956, 4.068, 4.068, 3.7844, 3.7844]], "forceVisible": true, "objectId": "Pan|+01.81|+00.91|-00.32", "receptacleObjectId": "CounterTop|+00.99|+00.95|+01.02"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Pan|+01.54|+00.91|-00.32"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [133, 73, 211, 177], "mask": [[21766, 9], [22060, 22], [22356, 30], [22653, 36], [22951, 40], [23249, 44], [23547, 48], [23846, 51], [24144, 54], [24443, 57], [24742, 59], [25041, 61], [25340, 63], [25639, 65], [25938, 67], [26237, 69], [26537, 70], [26836, 71], [27136, 72], [27435, 74], [27735, 74], [28034, 76], [28334, 76], [28634, 77], [28933, 78], [29233, 78], [29533, 78], [29833, 79], [30133, 79], [30433, 79], [30733, 79], [31033, 79], [31333, 79], [31633, 79], [31933, 79], [32233, 79], [32534, 78], [32834, 78], [33134, 77], [33435, 76], [33735, 76], [34035, 76], [34336, 74], [34637, 73], [34937, 72], [35238, 71], [35538, 70], [35839, 68], [36139, 67], [36440, 66], [36741, 64], [37042, 62], [37343, 60], [37644, 58], [37945, 56], [38246, 54], [38547, 51], [38849, 48], [39150, 46], [39452, 42], [39754, 38], [40056, 34], [40359, 29], [40663, 21], [40968, 10], [41271, 7], [41571, 7], [41871, 7], [42172, 6], [42472, 6], [42772, 6], [43072, 6], [43371, 8], [43671, 8], [43971, 8], [44272, 7], [44572, 7], [44872, 7], [45172, 7], [45472, 7], [45772, 7], [46072, 7], [46372, 7], [46672, 7], [46972, 7], [47272, 8], [47572, 8], [47872, 8], [48172, 8], [48472, 8], [48772, 8], [49072, 8], [49372, 8], [49672, 8], [49972, 8], [50272, 8], [50572, 8], [50872, 9], [51172, 4], [51177, 4], [51472, 4], [51477, 4], [51772, 3], [51778, 3], [52072, 9], [52372, 9], [52673, 7], [52976, 2]], "point": [172, 124]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pan|+01.54|+00.91|-00.32", "placeStationary": true, "receptacleObjectId": "CounterTop|+00.99|+00.95|+01.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 89, 299, 273], "mask": [[26400, 26], [26443, 283], [26743, 283], [27043, 283], [27344, 282], [27644, 283], [27944, 283], [28245, 282], [28545, 283], [28844, 284], [29143, 286], [29442, 288], [29739, 559], [30300, 297], [30600, 271], [30874, 23], [30900, 249], [31188, 9], [31200, 143], [31353, 95], [31475, 6], [31500, 142], [31658, 91], [31754, 43], [31800, 142], [31958, 284], [32258, 284], [32558, 284], [32857, 285], [33151, 291], [33451, 291], [33751, 291], [34051, 290], [34353, 286], [34654, 284], [34955, 282], [35256, 281], [35557, 279], [35857, 278], [36158, 277], [36458, 277], [36758, 277], [37059, 275], [37359, 275], [37659, 275], [37959, 275], [38259, 275], [38559, 275], [38859, 276], [39159, 276], [39459, 276], [39759, 276], [40059, 276], [40359, 276], [40659, 276], [40959, 276], [41259, 276], [41559, 276], [41859, 276], [42159, 276], [42459, 55], [42516, 219], [42759, 54], [42817, 218], [43059, 54], [43118, 217], [43358, 55], [43418, 217], [43658, 55], [43718, 217], [43958, 55], [44019, 216], [44258, 56], [44319, 217], [44558, 56], [44619, 217], [44858, 57], [44919, 217], [45158, 57], [45219, 217], [45458, 57], [45520, 216], [45758, 58], [45820, 216], [46058, 58], [46120, 216], [46358, 58], [46420, 216], [46658, 59], [46721, 216], [46957, 60], [47021, 216], [47257, 61], [47321, 217], [47556, 62], [47621, 217], [47856, 63], [47921, 218], [48155, 64], [48222, 218], [48454, 65], [48522, 219], [48753, 67], [48822, 220], [49052, 68], [49122, 223], [49349, 71], [49423, 298], [49723, 298], [50023, 298], [50323, 299], [50624, 298], [50924, 298], [51224, 225], [51451, 71], [51524, 214], [51762, 60], [51825, 207], [52068, 54], [52125, 203], [52372, 49], [52426, 199], [52675, 46], [52727, 194], [52979, 41], [53027, 191], [53282, 38], [53328, 188], [53584, 36], [53629, 185], [53886, 34], [53929, 183], [54188, 32], [54230, 180], [54490, 30], [54530, 178], [54792, 29], [54822, 1], [54825, 1], [54828, 1], [54830, 176], [55094, 27], [55123, 1], [55125, 1], [55128, 1], [55131, 173], [55396, 26], [55423, 1], [55426, 1], [55428, 2], [55431, 172], [55697, 25], [55724, 1], [55726, 1], [55729, 1], [55731, 170], [55999, 24], [56024, 1], [56026, 2], [56029, 1], [56032, 168], [56300, 23], [56324, 2], [56327, 1], [56329, 2], [56332, 166], [56602, 22], [56625, 1], [56627, 2], [56630, 1], [56632, 165], [56903, 21], [56925, 2], [56928, 1], [56930, 2], [56933, 163], [57204, 21], [57226, 1], [57228, 2], [57231, 1], [57233, 161], [57506, 187], [57807, 184], [58109, 181], [58410, 179], [58711, 178], [59011, 177], [59312, 175], [59613, 173], [59914, 171], [60215, 169], [60516, 167], [60817, 165], [61118, 163], [61419, 161], [61720, 159], [62021, 157], [62322, 156], [62622, 155], [62923, 153], [63224, 152], [63524, 151], [63825, 149], [64126, 147], [64427, 146], [64727, 73], [65077, 23], [65378, 22], [65678, 22], [65979, 21], [66279, 21], [66579, 21], [66880, 20], [67180, 20], [67480, 20], [67781, 19], [68081, 19], [68382, 18], [68682, 18], [68982, 18], [69283, 17], [69583, 17], [69884, 16], [70184, 16], [70484, 16], [70785, 15], [71085, 15], [71386, 14], [71686, 14], [71986, 14], [72287, 13], [72587, 13], [72887, 13], [73188, 12], [73488, 12], [73789, 11], [74089, 11], [74389, 11], [74690, 10], [74990, 10], [75291, 9], [75591, 9], [75891, 9], [76192, 8], [76492, 8], [76793, 7], [77093, 7], [77393, 7], [77694, 6], [77994, 6], [78295, 5], [78595, 5], [78895, 5], [79196, 4], [79496, 4], [79796, 4], [80097, 3], [80397, 3], [80698, 2], [80998, 2], [81298, 2], [81599, 1], [81899, 1]], "point": [148, 171]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Pan|+01.81|+00.91|-00.32"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [101, 101, 199, 144], "mask": [[30159, 15], [30454, 25], [30751, 32], [31049, 36], [31346, 41], [31645, 44], [31943, 48], [32242, 50], [32541, 52], [32840, 55], [33139, 57], [33438, 58], [33738, 59], [34037, 61], [34337, 62], [34602, 33], [34637, 62], [34901, 98], [35201, 99], [35501, 99], [35834, 66], [36135, 65], [36436, 64], [36736, 64], [37037, 63], [37337, 63], [37638, 61], [37938, 61], [38238, 60], [38539, 59], [38839, 58], [39140, 57], [39440, 56], [39741, 55], [40042, 53], [40343, 51], [40644, 49], [40945, 47], [41246, 45], [41548, 42], [41850, 38], [42152, 34], [42454, 30], [42757, 24], [43062, 14]], "point": [150, 121]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pan|+01.81|+00.91|-00.32", "placeStationary": true, "receptacleObjectId": "CounterTop|+00.99|+00.95|+01.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 89, 299, 273], "mask": [[26400, 26], [26443, 283], [26743, 283], [27043, 283], [27344, 282], [27644, 283], [27944, 283], [28245, 282], [28545, 283], [28844, 284], [29143, 286], [29442, 288], [29739, 559], [30300, 297], [30600, 271], [30874, 23], [30900, 249], [31188, 9], [31200, 143], [31353, 95], [31475, 6], [31500, 142], [31658, 91], [31754, 43], [31800, 142], [31958, 284], [32258, 284], [32558, 284], [32857, 285], [33151, 291], [33451, 291], [33751, 291], [34051, 290], [34353, 286], [34654, 284], [34955, 282], [35256, 281], [35557, 279], [35857, 278], [36158, 277], [36458, 277], [36758, 277], [37059, 275], [37359, 275], [37659, 275], [37959, 275], [38259, 275], [38559, 275], [38859, 276], [39159, 276], [39459, 276], [39759, 276], [40059, 276], [40359, 276], [40659, 209], [40884, 51], [40959, 204], [41189, 46], [41259, 201], [41491, 44], [41559, 198], [41794, 41], [41859, 195], [42096, 39], [42159, 193], [42397, 38], [42459, 55], [42516, 134], [42699, 36], [42759, 54], [42817, 131], [43000, 35], [43059, 54], [43118, 128], [43301, 34], [43358, 55], [43418, 127], [43602, 33], [43658, 55], [43718, 125], [43903, 32], [43958, 55], [44019, 123], [44204, 31], [44258, 56], [44319, 122], [44504, 32], [44558, 56], [44619, 121], [44805, 31], [44858, 57], [44919, 119], [45105, 31], [45158, 57], [45219, 118], [45406, 30], [45458, 57], [45520, 117], [45706, 30], [45758, 58], [45820, 116], [46006, 30], [46058, 58], [46120, 115], [46307, 29], [46358, 58], [46420, 114], [46607, 29], [46658, 59], [46721, 112], [46907, 30], [46957, 60], [47021, 112], [47207, 30], [47257, 61], [47321, 79], [47430, 2], [47507, 31], [47556, 62], [47621, 79], [47807, 31], [47856, 63], [47921, 79], [48107, 32], [48155, 64], [48222, 78], [48407, 33], [48454, 65], [48522, 78], [48618, 9], [48707, 34], [48753, 67], [48822, 106], [49006, 36], [49052, 68], [49122, 107], [49306, 39], [49349, 71], [49423, 106], [49606, 115], [49723, 106], [49906, 115], [50023, 106], [50206, 115], [50323, 106], [50505, 117], [50624, 105], [50805, 117], [50924, 105], [51105, 117], [51224, 105], [51404, 118], [51524, 105], [51704, 118], [51825, 104], [52003, 119], [52125, 104], [52303, 118], [52426, 104], [52602, 119], [52727, 103], [52902, 118], [53027, 103], [53201, 45], [53254, 66], [53328, 102], [53500, 35], [53565, 55], [53629, 102], [53799, 31], [53870, 50], [53929, 103], [54098, 28], [54174, 46], [54230, 102], [54398, 24], [54478, 42], [54530, 103], [54697, 22], [54781, 40], [54822, 1], [54825, 1], [54828, 1], [54830, 104], [54995, 22], [55083, 38], [55123, 1], [55125, 1], [55128, 1], [55131, 104], [55294, 20], [55386, 36], [55423, 1], [55426, 1], [55428, 2], [55431, 105], [55593, 19], [55688, 34], [55724, 1], [55726, 1], [55729, 1], [55731, 106], [55892, 17], [55991, 32], [56024, 1], [56026, 2], [56029, 1], [56032, 106], [56190, 17], [56293, 30], [56324, 2], [56327, 1], [56329, 2], [56332, 108], [56489, 17], [56594, 30], [56625, 1], [56627, 2], [56630, 1], [56632, 109], [56787, 17], [56896, 28], [56925, 2], [56928, 1], [56930, 2], [56933, 109], [57085, 17], [57198, 27], [57226, 1], [57228, 2], [57231, 1], [57233, 111], [57383, 18], [57499, 147], [57680, 19], [57801, 147], [57978, 19], [58103, 149], [58274, 22], [58404, 152], [58569, 25], [58706, 187], [59007, 185], [59308, 183], [59609, 181], [59910, 178], [60212, 175], [60513, 173], [60814, 171], [61115, 169], [61416, 167], [61717, 165], [62018, 163], [62319, 161], [62620, 159], [62921, 158], [63221, 157], [63522, 155], [63823, 153], [64124, 151], [64425, 149], [64726, 74], [65077, 23], [65378, 22], [65678, 22], [65979, 21], [66279, 21], [66579, 21], [66880, 20], [67180, 20], [67480, 20], [67781, 19], [68081, 19], [68382, 18], [68682, 18], [68982, 18], [69283, 17], [69583, 17], [69884, 16], [70184, 16], [70484, 16], [70785, 15], [71085, 15], [71386, 14], [71686, 14], [71986, 14], [72287, 13], [72587, 13], [72887, 13], [73188, 12], [73488, 12], [73789, 11], [74089, 11], [74389, 11], [74690, 10], [74990, 10], [75291, 9], [75591, 9], [75891, 9], [76192, 8], [76492, 8], [76793, 7], [77093, 7], [77393, 7], [77694, 6], [77994, 6], [78295, 5], [78595, 5], [78895, 5], [79196, 4], [79496, 4], [79796, 4], [80097, 3], [80397, 3], [80698, 2], [80998, 2], [81298, 2], [81599, 1], [81899, 1]], "point": [149, 176]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan14", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -0.5, "y": 0.9009992, "z": 1.25}, "object_poses": [{"objectName": "Pan_7013969f", "position": {"x": 1.5433, "y": 0.9098, "z": -0.3203}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ButterKnife_38537d23", "position": {"x": 0.5539353, "y": 0.911242068, "z": -1.352079}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ButterKnife_38537d23", "position": {"x": 1.83962345, "y": 0.910642, "z": 0.518974066}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Fork_192d20c7", "position": {"x": 0.988999963, "y": 0.911148548, "z": 1.00428188}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Fork_192d20c7", "position": {"x": 1.24529254, "y": 0.911148548, "z": 0.8276421}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 1.5015831, "y": 0.9255999, "z": 1.18092394}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Cup_5ecc05ec", "position": {"x": 1.87625933, "y": 1.63142717, "z": -0.6827678}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_e8bf9352", "position": {"x": -0.790139437, "y": 0.4621566, "z": -1.47692657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_e8bf9352", "position": {"x": -0.7415557, "y": 1.09320486, "z": -1.59446669}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_06a240e2", "position": {"x": 0.455365479, "y": 0.9436372, "z": -1.263759}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "SoapBottle_fd471042", "position": {"x": 0.98900044, "y": 0.9087641, "z": 0.9159616}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_fd471042", "position": {"x": 1.87778664, "y": 1.65207028, "z": -0.497722775}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_817a54e2", "position": {"x": 1.76208282, "y": 0.9744553, "z": 0.5708326}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 1.45192122, "y": 0.905599952, "z": 0.363396764}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 0.8403779, "y": 0.906199932, "z": -1.23038375}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 0.4764157, "y": 0.9056, "z": 1.26924109}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_5b80d804", "position": {"x": 1.83586168, "y": 1.07275975, "z": -0.6760018}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Plate_e8bf9352", "position": {"x": 1.80571866, "y": 1.63482726, "z": -0.9011388}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_fd471042", "position": {"x": 1.75787473, "y": 0.9087641, "z": 1.26924491}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_5ecc05ec", "position": {"x": 1.7711072, "y": 1.01373744, "z": -0.8880987}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Egg_85efe221", "position": {"x": -0.8873074, "y": 0.4956351, "z": -1.67017055}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_192d20c7", "position": {"x": 1.656339, "y": 0.911748469, "z": -1.63161}, "rotation": {"x": 0.0, "y": 269.999817, "z": 0.0}}, {"objectName": "Ladle_4b0a72cf", "position": {"x": 1.170325, "y": 0.8047956, "z": -1.5995}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_5b80d804", "position": {"x": -0.790139735, "y": 1.44619751, "z": -1.62949562}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 1.93587351, "y": 0.906199932, "z": -1.37455559}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pan_7013969f", "position": {"x": 1.807, "y": 0.9098, "z": -0.3203}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Tomato_817a54e2", "position": {"x": 1.91716433, "y": 0.9744553, "z": 0.415256858}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 1.86599064, "y": 0.9261998, "z": -1.717294}, "rotation": {"x": 0.0, "y": -0.000160509444, "z": 0.0}}, {"objectName": "Potato_6d0d5c3c", "position": {"x": 1.03216934, "y": 0.800844848, "z": -1.71005964}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_06a240e2", "position": {"x": 1.7578752, "y": 0.9430372, "z": 1.0926044}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ButterKnife_38537d23", "position": {"x": 0.296, "y": 0.9, "z": -1.296}, "rotation": {"x": 0.0, "y": 14.5689316, "z": 0.0}}, {"objectName": "Bread_8f8a19bf", "position": {"x": 1.615, "y": 0.977039754, "z": 0.761}, "rotation": {"x": 9.785828e-06, "y": 50.5255928, "z": -2.43427385e-05}}, {"objectName": "Lettuce_e61d6e1d", "position": {"x": 1.79610646, "y": 1.01113224, "z": -1.545925}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 0.258226335, "y": 0.9062, "z": -1.26375961}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_bb5e3511", "position": {"x": 1.5345701, "y": 0.9084982, "z": 0.05875536}, "rotation": {"x": 0.0, "y": 195.000244, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 1.77, "y": 0.06976977, "z": 1.54046369}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_89592edf", "position": {"x": 0.894013643, "y": 0.7618923, "z": -1.65477979}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Bowl_7266264e", "position": {"x": 1.80805576, "y": 1.63811362, "z": 0.375051945}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f9995e03", "position": {"x": 1.706354, "y": 1.01045275, "z": -0.67600137}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 1589538299, "scene_num": 14}, "task_id": "trial_T20190907_012608_530399", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "AUTYWXILTACCR_34MAJL3QP742ZJ9AQTB8XSL9XRC34F", "high_descs": ["Walk to your right, face the fridge, turn left, and walk to the stove.", "Pick up the pan from the front right burner of the stove.", "Turn to your left and walk to the counter.", "Place the pan on the counter to the left of the bottle.", "Turn around, then turn to the left to face the stove.", "Pick up the pan from the back right burner on the stove.", "Walk back to the counter to your left.", "Place the pan on the right of the counter, behind the fork."], "task_desc": "Place two pans on a counter.", "votes": [1, 1]}, {"assignment_id": "A3A0VPWPASCO9J_354GIDR5ZENPJ3SCO56NJRXPD0H000", "high_descs": ["Walk across the room to the stove on the left.", "Pick up the frying pan on the front right burner of the stove.", "Turn left and walk to the counter with the bottle of lotion.", "Set the frying pan down on the counter to the left of the bottle of lotion.", "Turn around and walk back to the stove.", "Pick up the frying pan on the back right corner of the stove.", "Turn left and walk to the counter with the bottle of lotion.", "Set the frying pan down on the counter behind the fork."], "task_desc": "Put the two frying pans on the counter with the bottle of lotion.", "votes": [1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_358UUM7WR2KR18CV3HABI28ZU787RX", "high_descs": ["turn right and walk over to the fridge ahead, then hang a left and walk over to the stove directly ahead", "grab the sauce pan off of the front right burner", "turn left and walk to the kitchen counter directly ahead", "place the sauce pan down on the kitchen counter", "turn around and walk back to the stove on the left", "grab the sauce pan on the back right burner off of the stove", "turn left and walk to the kitchen counter ahead", "place the sauce pan down on the kitchen counter behind the fork"], "task_desc": "place the two sauce pans from the stove on the kitchen counter", "votes": [1, 1]}]}}