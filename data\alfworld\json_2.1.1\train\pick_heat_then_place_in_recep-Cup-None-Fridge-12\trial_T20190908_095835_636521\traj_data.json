{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000296.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000298.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000299.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 49}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-1|4|3|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-4.83762, -4.83762, 3.684, 3.684, 3.7459128, 3.7459128]], "coordinateReceptacleObjectId": ["CounterTop", [-3.888, -3.888, 3.684, 3.684, 3.8936, 3.8936]], "forceVisible": true, "objectId": "Cup|-01.21|+00.94|+00.92"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|0|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-4.83762, -4.83762, 3.684, 3.684, 3.7459128, 3.7459128]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-4.004, -4.004, 0.476, 0.476, 0.0, 0.0]], "forceVisible": true, "objectId": "Cup|-01.21|+00.94|+00.92", "receptacleObjectId": "<PERSON><PERSON>|-01.00|+00.00|+00.12"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.21|+00.94|+00.92"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [118, 127, 142, 171], "mask": [[37930, 3], [38226, 11], [38522, 19], [38819, 24], [39119, 24], [39419, 24], [39718, 25], [40018, 25], [40318, 25], [40618, 25], [40918, 25], [41218, 25], [41518, 25], [41819, 24], [42119, 23], [42419, 23], [42719, 23], [43019, 23], [43320, 22], [43620, 22], [43920, 22], [44220, 22], [44520, 22], [44821, 21], [45121, 20], [45421, 20], [45721, 20], [46021, 20], [46322, 19], [46622, 19], [46922, 19], [47222, 19], [47522, 19], [47823, 18], [48123, 17], [48423, 17], [48723, 17], [49023, 17], [49323, 17], [49624, 16], [49924, 3], [49931, 9], [50224, 1], [50232, 8], [50524, 1], [50532, 8], [50832, 6], [51132, 3]], "point": [130, 148]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.21|+00.94|+00.92", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 75], [127, 134], [300, 75], [427, 134], [600, 75], [727, 134], [900, 75], [1027, 133], [1200, 76], [1327, 133], [1500, 76], [1627, 133], [1800, 76], [1927, 133], [2100, 76], [2227, 133], [2400, 76], [2527, 133], [2700, 76], [2826, 134], [3000, 77], [3126, 134], [3300, 77], [3426, 134], [3600, 77], [3726, 134], [3900, 77], [4026, 133], [4200, 77], [4326, 133], [4500, 77], [4626, 133], [4800, 78], [4926, 133], [5100, 78], [5226, 133], [5400, 78], [5526, 133], [5700, 78], [5826, 133], [6000, 78], [6126, 133], [6300, 79], [6425, 134], [6600, 79], [6725, 134], [6900, 79], [7025, 133], [7200, 79], [7325, 133], [7500, 79], [7625, 133], [7800, 79], [7925, 133], [8100, 80], [8225, 133], [8400, 80], [8525, 133], [8700, 80], [8825, 133], [9000, 80], [9125, 133], [9300, 80], [9425, 133], [9600, 80], [9725, 133], [9900, 81], [10024, 133], [10200, 81], [10324, 133], [10500, 81], [10624, 133], [10800, 81], [10924, 133], [11100, 81], [11224, 133], [11400, 81], [11524, 133], [11700, 82], [11824, 133], [12000, 82], [12124, 133], [12300, 82], [12424, 133], [12600, 82], [12724, 133], [12900, 82], [13024, 132], [13200, 82], [13324, 132], [13500, 83], [13623, 133], [13800, 83], [13923, 133], [14100, 83], [14223, 133], [14400, 83], [14523, 133], [14700, 83], [14823, 133], [15000, 83], [15123, 133], [15300, 84], [15423, 133], [15600, 84], [15723, 133], [15900, 84], [16023, 132], [16200, 84], [16323, 132], [16500, 84], [16623, 132], [16800, 84], [16923, 132], [17100, 85], [17222, 133], [17400, 85], [17522, 133], [17700, 85], [17822, 133], [18000, 85], [18122, 133], [18300, 85], [18422, 133], [18600, 86], [18722, 133], [18900, 86], [19022, 132], [19200, 86], [19322, 132], [19500, 86], [19622, 132], [19800, 86], [19922, 132], [20100, 86], [20222, 132], [20400, 87], [20522, 132], [20700, 87], [20821, 133], [21000, 87], [21121, 133], [21300, 92], [21419, 135], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.21|+00.94|+00.92"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [75, 1, 126, 72], "mask": [[75, 52], [375, 52], [675, 52], [975, 52], [1276, 51], [1576, 51], [1876, 51], [2176, 51], [2476, 51], [2776, 50], [3077, 49], [3377, 49], [3677, 49], [3977, 49], [4277, 49], [4577, 49], [4878, 48], [5178, 48], [5478, 48], [5778, 48], [6078, 48], [6379, 46], [6679, 46], [6979, 46], [7279, 46], [7579, 46], [7879, 46], [8180, 45], [8480, 45], [8780, 45], [9080, 45], [9380, 45], [9680, 45], [9981, 43], [10281, 43], [10581, 43], [10881, 43], [11181, 43], [11481, 43], [11782, 42], [12082, 42], [12382, 42], [12682, 42], [12982, 42], [13282, 42], [13583, 40], [13883, 40], [14183, 40], [14483, 40], [14783, 40], [15083, 40], [15384, 39], [15684, 39], [15984, 39], [16284, 39], [16584, 39], [16884, 39], [17185, 37], [17485, 37], [17785, 37], [18085, 37], [18385, 37], [18686, 36], [18986, 36], [19286, 36], [19586, 36], [19886, 36], [20186, 36], [20487, 35], [20787, 34], [21087, 34], [21392, 27]], "point": [100, 35]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-01.00|+00.00|+00.12"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 224], "mask": [[0, 19200], [19201, 299], [19501, 299], [19802, 298], [20102, 298], [20403, 297], [20704, 296], [21004, 296], [21305, 295], [21606, 294], [21906, 294], [22207, 293], [22508, 292], [22808, 292], [23109, 291], [23409, 291], [23710, 290], [24011, 289], [24311, 289], [24612, 288], [24913, 287], [25213, 287], [25514, 286], [25815, 285], [26116, 284], [26416, 284], [26716, 284], [27017, 283], [27318, 282], [27618, 282], [27919, 281], [28219, 281], [28520, 280], [28821, 279], [29121, 279], [29422, 278], [29723, 277], [30023, 277], [30324, 276], [30625, 275], [30925, 275], [31226, 274], [31526, 274], [31827, 273], [32128, 272], [32428, 272], [32729, 271], [33030, 270], [33330, 270], [33631, 269], [33932, 268], [34232, 268], [34533, 267], [34833, 267], [35134, 266], [35435, 265], [35735, 265], [36036, 264], [36337, 263], [36637, 263], [36938, 262], [37239, 261], [37539, 261], [37840, 260], [38140, 260], [38441, 259], [38742, 258], [39042, 258], [39343, 257], [39644, 256], [39944, 256], [40245, 255], [40545, 255], [40846, 254], [41147, 253], [41447, 83], [41570, 130], [41748, 78], [41874, 126], [42049, 74], [42177, 123], [42349, 71], [42480, 120], [42650, 67], [42783, 117], [42951, 63], [43086, 114], [43251, 60], [43389, 111], [43552, 56], [43692, 108], [43852, 55], [43993, 107], [44153, 54], [44293, 107], [44454, 52], [44594, 106], [44754, 52], [44894, 106], [45055, 52], [45193, 107], [45356, 51], [45493, 107], [45656, 51], [45793, 107], [45957, 50], [46093, 107], [46258, 49], [46393, 107], [46558, 49], [46693, 107], [46859, 49], [46992, 108], [47159, 49], [47292, 108], [47460, 48], [47592, 108], [47761, 47], [47892, 108], [48061, 47], [48192, 108], [48362, 46], [48492, 108], [48663, 46], [48791, 109], [48963, 46], [49091, 109], [49264, 45], [49391, 109], [49565, 44], [49691, 109], [49865, 44], [49991, 109], [50166, 43], [50291, 109], [50466, 44], [50590, 110], [50767, 43], [50890, 110], [51068, 42], [51190, 110], [51368, 42], [51490, 110], [51669, 41], [51790, 110], [51970, 40], [52090, 109], [52270, 41], [52389, 109], [52571, 40], [52689, 108], [52872, 39], [52989, 107], [53172, 39], [53289, 105], [53473, 38], [53589, 104], [53773, 38], [53889, 103], [54074, 38], [54188, 103], [54375, 37], [54488, 102], [54675, 37], [54788, 101], [54976, 36], [55088, 99], [55277, 35], [55388, 98], [55577, 35], [55688, 97], [55878, 35], [55987, 97], [56179, 34], [56287, 96], [56479, 34], [56587, 94], [56780, 33], [56887, 93], [57080, 33], [57187, 92], [57381, 32], [57487, 91], [57682, 32], [57786, 91], [57982, 32], [58086, 90], [58283, 31], [58386, 88], [58584, 30], [58686, 87], [58884, 30], [58986, 86], [59185, 29], [59286, 85], [59486, 29], [59585, 85], [59786, 29], [59885, 83], [60087, 28], [60185, 82], [60387, 28], [60485, 81], [60688, 27], [60785, 80], [60989, 26], [61085, 79], [61289, 26], [61385, 78], [61590, 26], [61684, 77], [61891, 25], [61984, 76], [62191, 25], [62284, 75], [62492, 24], [62584, 74], [62793, 23], [62884, 73], [63093, 23], [63184, 71], [63394, 23], [63483, 71], [63694, 23], [63783, 70], [63995, 22], [64083, 69], [64296, 21], [64383, 68], [64596, 21], [64683, 67], [64897, 20], [64983, 65], [65198, 20], [65282, 65], [65498, 20], [65582, 64], [65799, 19], [65882, 63], [66099, 19], [66182, 62], [66400, 18], [66482, 60], [66701, 17], [66782, 59], [67002, 17], [67081, 59]], "point": [149, 111]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.21|+00.94|+00.92", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-01.00|+00.00|+00.12"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 2100], [2101, 299], [2401, 299], [2702, 298], [3002, 298], [3303, 297], [3603, 297], [3904, 296], [4205, 295], [4505, 295], [4806, 294], [5106, 294], [5407, 293], [5707, 293], [6008, 292], [6308, 292], [6609, 291], [6909, 291], [7210, 290], [7510, 290], [7811, 289], [8111, 289], [8412, 288], [8712, 288], [9013, 287], [9313, 287], [9614, 286], [9914, 286], [10215, 285], [10515, 285], [10816, 284], [11116, 284], [11417, 283], [11717, 283], [12018, 282], [12318, 282], [12619, 281], [12919, 281], [13220, 280], [13520, 280], [13821, 279], [14121, 279], [14422, 278], [14722, 278], [15023, 277], [15323, 277], [15624, 276], [15925, 275], [16226, 274], [16526, 274], [16826, 274], [17126, 274], [17427, 273], [17727, 273], [18028, 272], [18328, 272], [18629, 271], [18929, 271], [19230, 270], [19531, 269], [19831, 269], [20132, 268], [20432, 268], [20733, 267], [21033, 267], [21334, 266], [21634, 266], [21935, 265], [22235, 265], [22536, 264], [22836, 264], [23137, 263], [23437, 263], [23738, 262], [24038, 262], [24339, 261], [24639, 261], [24940, 260], [25240, 260], [25541, 259], [25841, 259], [26142, 258], [26442, 258], [26743, 257], [27043, 257], [27344, 256], [27644, 256], [27945, 255], [28245, 255], [28546, 254], [28846, 254], [29147, 253], [29447, 253], [29748, 252], [30048, 252], [30349, 251], [30649, 251], [30950, 250], [31250, 250], [31551, 249], [31851, 249], [32152, 248], [32452, 248], [32753, 247], [33053, 130], [33220, 80], [33354, 128], [33520, 80], [33654, 128], [33821, 79], [33955, 127], [34121, 79], [34255, 127], [34421, 79], [34556, 126], [34721, 79], [34856, 126], [35021, 79], [35157, 125], [35321, 79], [35458, 124], [35621, 79], [35758, 124], [35920, 80], [36059, 123], [36220, 80], [36359, 123], [36520, 80], [36660, 122], [36820, 80], [36960, 122], [37119, 81], [37261, 121], [37419, 81], [37561, 121], [37719, 81], [37862, 120], [38019, 81], [38162, 120], [38319, 81], [38463, 119], [38619, 81], [38763, 120], [38918, 82], [39064, 119], [39218, 82], [39364, 119], [39518, 82], [39665, 119], [39817, 83], [39965, 119], [40116, 84], [40266, 119], [40416, 84], [40566, 119], [40715, 85], [40867, 119], [41014, 86], [41167, 119], [41314, 86], [41468, 62], [41570, 17], [41613, 87], [41768, 58], [41874, 14], [41912, 88], [42069, 54], [42177, 12], [42212, 88], [42369, 51], [42480, 10], [42511, 89], [42670, 47], [42783, 8], [42811, 89], [42970, 44], [43086, 6], [43110, 90], [43271, 40], [43389, 4], [43409, 91], [43571, 37], [43692, 2], [43708, 92], [43872, 35], [43993, 3], [44007, 93], [44172, 35], [44293, 5], [44305, 95], [44473, 33], [44594, 106], [44773, 33], [44894, 106], [45074, 33], [45193, 107], [45374, 33], [45493, 107], [45675, 32], [45793, 107], [45975, 32], [46093, 107], [46276, 31], [46393, 107], [46576, 31], [46693, 107], [46877, 31], [46992, 108], [47177, 31], [47292, 108], [47478, 30], [47592, 108], [47778, 30], [47892, 108], [48079, 29], [48192, 108], [48379, 29], [48492, 108], [48680, 29], [48791, 109], [48980, 29], [49091, 109], [49281, 28], [49391, 109], [49581, 28], [49691, 109], [49882, 27], [49991, 109], [50182, 27], [50291, 109], [50483, 27], [50590, 110], [50783, 27], [50890, 110], [51084, 26], [51190, 110], [51385, 25], [51490, 110], [51685, 25], [51790, 110], [51986, 24], [52090, 110], [52286, 25], [52389, 111], [52587, 24], [52689, 111], [52887, 24], [52989, 111], [53188, 23], [53289, 111], [53488, 23], [53589, 111], [53789, 22], [53889, 111], [54089, 23], [54188, 112], [54390, 22], [54488, 112], [54690, 22], [54788, 112], [54991, 21], [55088, 71], [55160, 40], [55291, 21], [55388, 112], [55592, 20], [55688, 112], [55892, 21], [55987, 113], [56193, 20], [56287, 113], [56493, 20], [56587, 113], [56794, 19], [56887, 113], [57094, 19], [57187, 113], [57395, 18], [57487, 113], [57695, 19], [57786, 114], [57996, 18], [58086, 114], [58296, 18], [58386, 114], [58597, 17], [58686, 114], [58897, 17], [58986, 114], [59198, 16], [59286, 114], [59498, 17], [59585, 115], [59799, 16], [59885, 115], [60099, 16], [60185, 115], [60400, 15], [60485, 115], [60700, 15], [60785, 115], [61001, 14], [61085, 115], [61301, 14], [61385, 115], [61602, 14], [61684, 116], [61903, 13], [61984, 53], [62038, 62], [62209, 7], [62304, 26], [62337, 63], [62637, 63], [62938, 62], [63238, 62], [63538, 62], [63838, 62], [64138, 62], [64438, 62], [64739, 61], [65039, 61], [65339, 61], [65639, 61], [65939, 61], [66239, 61], [66540, 60], [66840, 60], [67140, 60], [67440, 60], [67740, 60], [68040, 60], [68341, 59], [68641, 59], [68941, 59], [69241, 59], [69541, 59], [69842, 58], [70142, 58], [70442, 58], [70742, 58], [71042, 58], [71342, 58], [71643, 57], [71943, 57], [72243, 57], [72543, 57], [72843, 57], [73143, 57], [73444, 56], [73744, 56], [74044, 56], [74344, 56], [74644, 56], [74944, 56], [75245, 55], [75545, 55], [75845, 55], [76145, 55], [76445, 55], [76745, 55], [77046, 54], [77346, 54], [77646, 54], [77946, 54], [78246, 54], [78546, 54], [78847, 53], [79147, 53], [79447, 53], [79747, 53], [80047, 53], [80348, 52], [80648, 52], [80948, 52], [81248, 52], [81548, 52], [81848, 52], [82149, 51], [82449, 51], [82749, 51], [83049, 51], [83349, 51], [83649, 51], [83950, 50], [84250, 50], [84550, 50], [84850, 50], [85150, 50], [85450, 50], [85751, 49], [86051, 49], [86351, 49], [86651, 49], [86951, 49], [87251, 49], [87552, 48], [87852, 48], [88152, 48], [88452, 48], [88752, 48], [89053, 47], [89353, 47], [89653, 47], [89953, 47]], "point": [149, 137]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-01.00|+00.00|+00.12"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 2100], [2101, 299], [2401, 299], [2702, 298], [3002, 298], [3303, 297], [3603, 297], [3904, 296], [4205, 295], [4505, 295], [4806, 294], [5106, 294], [5407, 293], [5707, 293], [6008, 292], [6308, 292], [6609, 291], [6909, 291], [7210, 290], [7510, 290], [7811, 289], [8111, 289], [8412, 288], [8712, 288], [9013, 287], [9313, 287], [9614, 286], [9914, 286], [10215, 285], [10515, 285], [10816, 284], [11116, 284], [11417, 283], [11717, 283], [12018, 282], [12318, 282], [12619, 281], [12919, 281], [13220, 280], [13520, 280], [13821, 279], [14121, 279], [14422, 278], [14722, 278], [15023, 277], [15323, 277], [15624, 276], [15925, 275], [16226, 274], [16526, 274], [16826, 274], [17126, 274], [17427, 273], [17727, 273], [18028, 272], [18328, 272], [18629, 271], [18929, 271], [19230, 270], [19531, 269], [19831, 269], [20132, 268], [20432, 268], [20733, 267], [21033, 267], [21334, 266], [21634, 266], [21935, 265], [22235, 265], [22536, 264], [22836, 264], [23137, 263], [23437, 263], [23738, 262], [24038, 262], [24339, 261], [24639, 261], [24940, 260], [25240, 260], [25541, 259], [25841, 259], [26142, 258], [26442, 258], [26743, 257], [27043, 257], [27344, 256], [27644, 256], [27945, 255], [28245, 255], [28546, 254], [28846, 254], [29147, 253], [29447, 253], [29748, 252], [30048, 252], [30349, 251], [30649, 251], [30950, 250], [31250, 250], [31551, 249], [31851, 249], [32152, 248], [32452, 248], [32753, 247], [33053, 59], [33134, 49], [33220, 80], [33354, 57], [33435, 47], [33520, 80], [33654, 56], [33735, 47], [33821, 79], [33955, 55], [34036, 46], [34121, 79], [34255, 54], [34336, 46], [34421, 79], [34556, 53], [34636, 46], [34721, 79], [34856, 53], [34936, 46], [35021, 79], [35157, 52], [35236, 46], [35321, 79], [35458, 52], [35536, 46], [35621, 79], [35758, 52], [35836, 46], [35920, 80], [36059, 52], [36136, 46], [36220, 80], [36359, 52], [36436, 46], [36520, 80], [36660, 51], [36736, 46], [36820, 80], [36960, 52], [37036, 46], [37119, 81], [37261, 51], [37335, 47], [37419, 81], [37561, 52], [37635, 47], [37719, 81], [37862, 51], [37935, 47], [38019, 81], [38162, 51], [38235, 47], [38319, 81], [38463, 51], [38535, 47], [38619, 81], [38763, 51], [38835, 48], [38918, 82], [39064, 51], [39135, 48], [39218, 82], [39364, 51], [39435, 48], [39518, 82], [39665, 51], [39735, 49], [39817, 83], [39965, 51], [40034, 50], [40116, 84], [40266, 50], [40334, 51], [40416, 84], [40566, 51], [40634, 51], [40715, 85], [40867, 50], [40934, 52], [41014, 86], [41167, 51], [41234, 52], [41314, 86], [41468, 50], [41534, 53], [41613, 87], [41768, 51], [41833, 55], [41912, 88], [42069, 50], [42132, 57], [42212, 88], [42369, 51], [42432, 58], [42511, 89], [42670, 50], [42731, 60], [42811, 89], [42970, 52], [43030, 62], [43110, 90], [43271, 122], [43409, 91], [43571, 123], [43708, 92], [43872, 124], [44007, 93], [44172, 126], [44305, 95], [44473, 227], [44773, 227], [45074, 226], [45374, 226], [45675, 225], [45975, 225], [46276, 224], [46576, 224], [46877, 223], [47177, 223], [47478, 222], [47778, 222], [48079, 221], [48379, 221], [48680, 220], [48980, 220], [49281, 219], [49581, 219], [49882, 218], [50182, 218], [50483, 217], [50783, 217], [51084, 216], [51385, 215], [51685, 215], [51986, 214], [52286, 214], [52587, 213], [52887, 213], [53188, 212], [53488, 212], [53789, 211], [54089, 211], [54390, 210], [54690, 210], [54991, 168], [55160, 40], [55291, 209], [55592, 208], [55892, 208], [56193, 207], [56493, 207], [56794, 206], [57094, 206], [57395, 205], [57695, 205], [57996, 204], [58296, 204], [58597, 203], [58897, 203], [59198, 202], [59498, 202], [59799, 201], [60099, 201], [60400, 200], [60700, 200], [61001, 199], [61301, 199], [61602, 198], [61903, 134], [62038, 62], [62209, 25], [62304, 26], [62337, 63], [62637, 63], [62938, 62], [63238, 62], [63538, 62], [63838, 62], [64138, 62], [64438, 62], [64739, 61], [65039, 61], [65339, 61], [65639, 61], [65939, 61], [66239, 61], [66540, 60], [66840, 60], [67140, 60], [67440, 60], [67740, 60], [68040, 60], [68341, 59], [68641, 59], [68941, 59], [69241, 59], [69541, 59], [69842, 58], [70142, 58], [70442, 58], [70742, 58], [71042, 58], [71342, 58], [71643, 57], [71943, 57], [72243, 57], [72543, 57], [72843, 57], [73143, 57], [73444, 56], [73744, 56], [74044, 56], [74344, 56], [74644, 56], [74944, 56], [75245, 55], [75545, 55], [75845, 55], [76145, 55], [76445, 55], [76745, 55], [77046, 54], [77346, 54], [77646, 54], [77946, 54], [78246, 54], [78546, 54], [78847, 53], [79147, 53], [79447, 53], [79747, 53], [80047, 53], [80348, 52], [80648, 52], [80948, 52], [81248, 52], [81548, 52], [81848, 52], [82149, 51], [82449, 51], [82749, 51], [83049, 51], [83349, 51], [83649, 51], [83950, 50], [84250, 50], [84550, 50], [84850, 50], [85150, 50], [85450, 50], [85751, 49], [86051, 49], [86351, 49], [86651, 49], [86951, 49], [87251, 49], [87552, 48], [87852, 48], [88152, 48], [88452, 48], [88752, 48], [89053, 47], [89353, 47], [89653, 47], [89953, 47]], "point": [149, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan12", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": 1.0, "y": 0.9009999, "z": 1.5}, "object_poses": [{"objectName": "Potato_22312ae0", "position": {"x": -0.7937801, "y": 0.9705572, "z": 0.9986881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": -1.01830947, "y": 1.35187662, "z": 0.195651144}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -0.689003944, "y": 0.7466093, "z": 2.31313133}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_d40bfead", "position": {"x": -1.12628, "y": 0.938448548, "z": 0.921}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_d40bfead", "position": {"x": -0.7937801, "y": 0.938448548, "z": 0.921}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": 1.40962565, "y": 0.9458269, "z": 1.63136768}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.827398658, "y": 0.7479664, "z": 2.3811}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": 1.44873142, "y": 0.761567056, "z": 1.05253744}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.8657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.5506295, "y": 0.9798608, "z": 1.58957624}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.59296227, "y": 0.936478138, "z": 0.8063456}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.58572066, "y": 1.94713616, "z": 1.78360128}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.50362825, "y": 0.9432757, "z": 1.71495044}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -0.791773558, "y": 0.08874029, "z": 0.8450084}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.896596, "y": 0.7685599, "z": 1.05093741}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -1.12628, "y": 0.9342062, "z": 1.0763762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -1.043155, "y": 0.9342062, "z": 0.921}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": 1.31033671, "y": 0.7428734, "z": 0.9166}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": 1.7485894, "y": 0.987130344, "z": 1.673176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": -0.937683463, "y": 0.6562203, "z": 0.195651367}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.352982, "y": 0.08154476, "z": 0.7074625}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.9230004, "y": 0.990678251, "z": 2.26549959}, "rotation": {"x": -3.14625759e-05, "y": 302.470337, "z": 4.54345864e-05}}, {"objectName": "Fork_d40bfead", "position": {"x": -0.689004064, "y": 0.747115731, "z": 0.847031236}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -1.01830876, "y": 1.56053746, "z": -0.03430283}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": 1.38253057, "y": 0.09737432, "z": 0.171664327}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.4704}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": 1.76781189, "y": 0.9528998, "z": 1.35170221}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.793779969, "y": 0.9598927, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -1.043155, "y": 0.9342062, "z": 1.0763762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": 1.48997676, "y": 0.119435936, "z": 2.879911}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.04122186, "y": 1.49741375, "z": 2.2165885}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": 1.3622911, "y": 0.9379421, "z": 2.11864662}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": -1.211005, "y": 0.9705572, "z": 2.159162}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -0.804073453, "y": 0.0866650939, "z": 2.456895}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": 1.45544767, "y": 0.100557625, "z": -0.0303022265}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.50362837, "y": 0.9798608, "z": 1.7985332}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -1.209405, "y": 0.9364782, "z": 0.921}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_9f7abbea", "position": {"x": 1.3781, "y": 1.73753989, "z": 0.3929}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": 1.40962577, "y": 0.9458269, "z": 1.79853344}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": 1.42227757, "y": 0.8384428, "z": 1.39808726}, "rotation": {"x": -1.40334208e-14, "y": 9.659347e-06, "z": 4.25675352e-22}}], "object_toggles": [], "random_seed": 2171996738, "scene_num": 12}, "task_id": "trial_T20190908_095835_636521", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2KAGFQU28JY43_3FIUS151DYTRQ00GCH9AQXRNMPNGGU", "high_descs": ["Turn to your right and go to the counter top in front of you, between the stove and the refrigerator. ", "Pick up the glass cup on the back of the counter, behind the salt. ", "Turn around and go to the microwave, above the sink. ", "Place the glass in the microwave, heat it up, and remove it from the microwave. ", "Turn around and go across the kitchen, to the left, to the refrigerator. ", "Place the cup on the second shelf, to the left of the lettuce, in the refrigerator. "], "task_desc": "Put a warm cup in the refrigerator. ", "votes": [1, 1, 1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3PWWM24LHVPMIRMRU4CXWGGOOMT283", "high_descs": ["turn right and walk over to the kitchen counter ahead", "grab the clear cup off of the kitchen counter", "turn around and walk over to the sink up ahead", "place the cup inside of the microwave above the kitchen sink, microwave it, then take it back out", "turn right and walk forwards a bit, then turn right and walk over to the fridge up ahead at the end of the room", "place the cup inside of the fridge"], "task_desc": "place a microwaved cup inside of the fridge", "votes": [1, 1, 1, 1]}, {"assignment_id": "A2871R3LEPWMMK_36AHBNMV1U3O07BP4XU8RCVUE5DDYJ", "high_descs": ["Take a right and walk to the counter to the left of the stove.", "Pick up the gray glass from the back of the counter.", "Turn around and walk to the sink.", "Put the glass inside the microwave above the sink, heat it, remove it and close the door.", "Turn around and walk to the black fridge.", "Put the heated glass inside the fridge on the middle shelf to the left of the lettuce and close to the door."], "task_desc": "Place a heated glass in a fridge.", "votes": [1, 1]}]}}