{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 39}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["coffeemachine"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|-6|1|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [3.863998652, 3.863998652, -6.13999796, -6.13999796, 5.4855976, 5.4855976]], "coordinateReceptacleObjectId": ["CoffeeMachine", [4.52, 4.52, -5.704, -5.704, 5.24159764, 5.24159764]], "forceVisible": true, "objectId": "Mug|+00.97|+01.37|-01.53"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|3|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [3.863998652, 3.863998652, -6.13999796, -6.13999796, 5.4855976, 5.4855976]], "coordinateReceptacleObjectId": ["Cabinet", [-5.84518432, -5.84518432, 1.8804, 1.8804, 3.100034236, 3.100034236]], "forceVisible": true, "objectId": "Mug|+00.97|+01.37|-01.53", "receptacleObjectId": "Cabinet|-01.46|+00.78|+00.47"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+00.97|+01.37|-01.53"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [146, 95, 178, 128], "mask": [[28352, 14], [28649, 20], [28947, 24], [29246, 26], [29546, 26], [29846, 31], [30146, 32], [30446, 26], [30475, 3], [30746, 26], [30776, 3], [31046, 26], [31076, 3], [31346, 26], [31376, 3], [31646, 26], [31676, 3], [31946, 26], [31976, 2], [32246, 26], [32276, 2], [32546, 26], [32576, 2], [32846, 26], [32875, 3], [33146, 26], [33175, 3], [33446, 26], [33474, 3], [33746, 26], [33774, 3], [34046, 26], [34073, 3], [34346, 29], [34646, 28], [34946, 27], [35246, 25], [35546, 25], [35846, 25], [36146, 25], [36446, 25], [36746, 25], [37047, 24], [37348, 22], [37649, 20], [37951, 16], [38255, 8]], "point": [162, 110]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+00.97|+01.37|-01.53", "placeStationary": true, "receptacleObjectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 72, 216, 245], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51602, 205], [51903, 204], [52203, 203], [52504, 39], [52673, 33], [52804, 38], [52973, 33], [53104, 37], [53273, 33], [53405, 36], [53573, 33], [53705, 35], [53874, 32], [54005, 35], [54174, 31], [54306, 33], [54606, 32], [54907, 31], [55207, 30], [55507, 30], [55808, 28], [56108, 27], [56408, 27], [56709, 25], [57009, 25], [57309, 24], [57609, 23], [57909, 23], [58209, 22], [58508, 22], [58807, 23], [59100, 1], [59106, 23], [59400, 1], [59404, 25], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 23], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1], [73200, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 245], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 132], [35536, 78], [35700, 126], [35842, 72], [36000, 123], [36144, 70], [36300, 122], [36446, 67], [36600, 120], [36747, 66], [36900, 120], [37047, 66], [37200, 120], [37351, 62], [37500, 120], [37653, 60], [37800, 120], [37953, 60], [38100, 120], [38247, 3], [38254, 59], [38400, 120], [38547, 4], [38554, 59], [38700, 120], [38847, 5], [38854, 58], [39000, 120], [39147, 5], [39154, 58], [39300, 120], [39447, 5], [39454, 58], [39600, 120], [39747, 5], [39754, 58], [39900, 120], [40047, 4], [40054, 58], [40200, 120], [40347, 4], [40354, 58], [40500, 120], [40647, 4], [40653, 59], [40800, 120], [40947, 4], [40953, 59], [41100, 120], [41247, 3], [41253, 58], [41400, 121], [41547, 2], [41552, 59], [41700, 121], [41847, 1], [41852, 59], [42000, 121], [42151, 60], [42300, 121], [42450, 61], [42600, 121], [42749, 62], [42900, 121], [43047, 64], [43200, 121], [43347, 63], [43500, 121], [43647, 63], [43800, 121], [43947, 63], [44100, 121], [44247, 63], [44400, 121], [44547, 63], [44700, 121], [44847, 63], [45000, 122], [45146, 64], [45300, 123], [45445, 65], [45600, 124], [45744, 65], [45900, 126], [46041, 68], [46200, 129], [46338, 71], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51602, 205], [51903, 204], [52203, 203], [52504, 39], [52673, 33], [52804, 38], [52973, 33], [53104, 37], [53273, 33], [53405, 36], [53573, 33], [53705, 35], [53874, 32], [54005, 35], [54174, 31], [54306, 33], [54606, 32], [54907, 31], [55207, 30], [55507, 30], [55808, 28], [56108, 27], [56408, 27], [56709, 25], [57009, 25], [57309, 24], [57609, 23], [57909, 23], [58209, 22], [58508, 22], [58807, 23], [59100, 1], [59106, 23], [59400, 1], [59404, 25], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 23], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1], [73200, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 168]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+00.97|+01.37|-01.53"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [120, 119, 153, 155], "mask": [[35532, 4], [35826, 16], [36123, 21], [36422, 24], [36720, 27], [37020, 27], [37320, 31], [37620, 33], [37920, 33], [38220, 27], [38250, 4], [38520, 27], [38551, 3], [38820, 27], [38852, 2], [39120, 27], [39152, 2], [39420, 27], [39452, 2], [39720, 27], [39752, 2], [40020, 27], [40051, 3], [40320, 27], [40351, 3], [40620, 27], [40651, 2], [40920, 27], [40951, 2], [41220, 27], [41250, 3], [41521, 26], [41549, 3], [41821, 26], [41848, 4], [42121, 30], [42421, 29], [42721, 28], [43021, 26], [43321, 26], [43621, 26], [43921, 26], [44221, 26], [44521, 26], [44821, 26], [45122, 24], [45423, 22], [45724, 20], [46026, 15], [46329, 9]], "point": [136, 136]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 245], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51602, 205], [51903, 204], [52203, 203], [52504, 39], [52673, 33], [52804, 38], [52973, 33], [53104, 37], [53273, 33], [53405, 36], [53573, 33], [53705, 35], [53874, 32], [54005, 35], [54174, 31], [54306, 33], [54606, 32], [54907, 31], [55207, 30], [55507, 30], [55808, 28], [56108, 27], [56408, 27], [56709, 25], [57009, 25], [57309, 24], [57609, 23], [57909, 23], [58209, 22], [58508, 22], [58807, 23], [59100, 1], [59106, 23], [59400, 1], [59404, 25], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 23], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 12], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1], [73200, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-01.46|+00.78|+00.47"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [174, 109, 299, 199], "mask": [[32582, 1], [32882, 10], [33182, 25], [33482, 40], [33782, 57], [34081, 119], [34381, 119], [34681, 119], [34981, 119], [35281, 119], [35581, 119], [35881, 119], [36181, 119], [36481, 119], [36780, 120], [37080, 120], [37380, 120], [37680, 120], [37980, 120], [38280, 120], [38580, 120], [38880, 120], [39180, 120], [39480, 120], [39779, 121], [40079, 121], [40379, 121], [40679, 121], [40979, 121], [41279, 121], [41579, 121], [41879, 121], [42179, 121], [42478, 122], [42778, 122], [43078, 122], [43378, 122], [43678, 122], [43978, 122], [44278, 122], [44578, 122], [44878, 122], [45177, 122], [45477, 122], [45777, 121], [46077, 121], [46377, 120], [46677, 119], [46977, 119], [47277, 118], [47577, 118], [47877, 117], [48176, 118], [48476, 117], [48776, 116], [49076, 116], [49376, 115], [49676, 115], [49976, 114], [50276, 114], [50576, 113], [50875, 113], [51175, 113], [51475, 112], [51775, 112], [52075, 111], [52375, 111], [52675, 110], [52975, 109], [53275, 109], [53575, 108], [53874, 109], [54174, 108], [54474, 108], [54774, 107], [55074, 106], [55374, 106], [55674, 105], [55974, 105], [56274, 104], [56575, 102], [56877, 100], [57178, 98], [57480, 96], [57781, 94], [58082, 92], [58383, 91], [58684, 89], [58985, 88], [59285, 87], [59586, 86]], "point": [236, 153]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+00.97|+01.37|-01.53", "placeStationary": true, "receptacleObjectId": "Cabinet|-01.46|+00.78|+00.47"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [173, 109, 299, 300], "mask": [[32581, 2], [32881, 11], [33181, 26], [33481, 41], [33780, 59], [34080, 120], [34380, 120], [34680, 120], [34980, 120], [35280, 120], [35580, 120], [35880, 120], [36180, 120], [36480, 120], [36779, 121], [37079, 121], [37379, 121], [37679, 121], [37979, 121], [38279, 121], [38579, 121], [38879, 121], [39179, 121], [39479, 121], [39778, 122], [40078, 51], [40136, 64], [40378, 50], [40431, 3], [40437, 63], [40678, 50], [40729, 7], [40738, 62], [40978, 49], [41029, 1], [41036, 1], [41038, 62], [41278, 49], [41341, 59], [41578, 47], [41643, 57], [41878, 45], [41945, 55], [42178, 44], [42247, 53], [42477, 45], [42548, 52], [42777, 44], [42849, 51], [43077, 43], [43150, 49], [43377, 42], [43451, 48], [43677, 41], [43751, 47], [43977, 41], [44052, 46], [44277, 40], [44352, 45], [44577, 40], [44653, 44], [44877, 39], [44953, 43], [45177, 39], [45253, 42], [45476, 40], [45553, 42], [45776, 39], [45852, 42], [46076, 39], [46153, 41], [46376, 39], [46453, 40], [46676, 39], [46753, 40], [46976, 39], [47053, 39], [47276, 39], [47353, 38], [47576, 39], [47653, 38], [47876, 40], [47953, 37], [48176, 40], [48252, 38], [48475, 41], [48552, 37], [48775, 42], [48852, 37], [49075, 42], [49151, 37], [49375, 43], [49451, 36], [49675, 44], [49750, 37], [49975, 45], [50049, 37], [50275, 46], [50349, 37], [50575, 47], [50648, 37], [50875, 48], [50946, 39], [51175, 50], [51245, 39], [51474, 53], [51543, 41], [51774, 57], [51840, 43], [52074, 108], [52374, 108], [52674, 107], [52974, 107], [53274, 106], [53574, 106], [53874, 105], [54173, 105], [54473, 105], [54773, 104], [55073, 104], [55373, 103], [55673, 103], [55973, 102], [56273, 102], [56575, 99], [56877, 96], [57178, 95], [57480, 92], [57781, 91], [58082, 89], [58383, 88], [58684, 86], [58985, 19], [59285, 19], [59586, 18], [59887, 17], [60187, 18], [60488, 17], [60788, 17], [61089, 16], [61389, 16], [61689, 17], [61990, 16], [62290, 16], [62590, 16], [62890, 16], [63190, 16], [63490, 17], [63790, 17], [64090, 17], [64389, 18], [64689, 18], [64989, 18], [65289, 19], [65589, 19], [65889, 19], [66189, 19], [66489, 19], [66789, 20], [67089, 20], [67388, 21], [67688, 21], [67988, 21], [68288, 21], [68588, 22], [68888, 22], [69188, 22], [69488, 21], [69788, 21], [70088, 21], [70387, 21], [70687, 21], [70987, 21], [71287, 20], [71587, 20], [71887, 20], [72187, 19], [72487, 19], [72787, 19], [73086, 19], [73386, 19], [73686, 19], [73986, 18], [74286, 18], [74586, 18], [74886, 17], [75186, 17], [75486, 17], [75786, 16], [76085, 17], [76385, 17], [76685, 16], [76985, 16], [77285, 16], [77585, 15], [77885, 15], [78185, 15], [78485, 14], [78785, 14], [79084, 15], [79384, 14], [79684, 14], [79984, 14], [80284, 13], [80583, 14], [80883, 14], [81183, 13], [81482, 14], [81782, 14], [82082, 13], [82381, 14], [82680, 15], [82980, 14], [83279, 15], [83578, 16], [83877, 16], [84177, 16], [84477, 16], [84777, 15], [85077, 15], [85377, 15], [85677, 14], [85977, 14], [86276, 15], [86576, 14], [86876, 14], [87176, 14], [87476, 13], [87776, 13], [88076, 13], [88376, 12], [88678, 10], [88979, 9], [89279, 8], [89579, 8], [89879, 8]], "point": [236, 195]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-01.46|+00.78|+00.47"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [172, 109, 299, 300], "mask": [[32581, 2], [32881, 11], [33181, 26], [33481, 41], [33780, 59], [34080, 120], [34380, 120], [34680, 120], [34980, 120], [35280, 120], [35580, 120], [35880, 120], [36180, 120], [36480, 120], [36779, 121], [37079, 121], [37379, 121], [37679, 121], [37979, 121], [38279, 121], [38579, 121], [38879, 121], [39179, 121], [39479, 121], [39778, 122], [40078, 51], [40136, 64], [40378, 50], [40431, 3], [40437, 63], [40678, 50], [40729, 7], [40738, 62], [40978, 49], [41029, 1], [41036, 1], [41038, 62], [41278, 49], [41341, 59], [41578, 47], [41643, 57], [41878, 45], [41945, 55], [42178, 44], [42247, 53], [42477, 45], [42548, 52], [42777, 44], [42849, 51], [43077, 43], [43150, 49], [43377, 42], [43451, 48], [43677, 41], [43751, 47], [43977, 41], [44052, 46], [44277, 40], [44352, 45], [44577, 40], [44653, 44], [44877, 39], [44953, 43], [45177, 39], [45253, 42], [45476, 40], [45553, 42], [45776, 39], [45852, 42], [46076, 39], [46153, 41], [46376, 39], [46453, 40], [46676, 39], [46753, 40], [46976, 39], [47053, 39], [47276, 39], [47353, 38], [47576, 39], [47653, 38], [47876, 40], [47953, 37], [48176, 40], [48252, 38], [48475, 41], [48552, 37], [48775, 42], [48852, 37], [49075, 42], [49151, 37], [49375, 43], [49451, 36], [49675, 44], [49750, 37], [49975, 45], [50049, 37], [50275, 46], [50349, 37], [50575, 47], [50648, 37], [50875, 48], [50946, 39], [51175, 50], [51245, 39], [51474, 53], [51543, 41], [51774, 57], [51840, 43], [52074, 108], [52374, 108], [52674, 107], [52974, 50], [53027, 54], [53274, 50], [53328, 52], [53574, 49], [53631, 49], [53874, 48], [53933, 46], [54173, 48], [54234, 44], [54473, 48], [54535, 43], [54773, 47], [54836, 41], [55073, 47], [55136, 41], [55373, 47], [55436, 40], [55673, 46], [55736, 40], [55973, 46], [56036, 39], [56273, 46], [56336, 39], [56573, 46], [56635, 39], [56873, 45], [56935, 38], [57172, 46], [57234, 39], [57472, 46], [57534, 38], [57772, 46], [57834, 38], [58072, 46], [58133, 38], [58372, 47], [58433, 38], [58672, 47], [58732, 38], [58972, 32], [59272, 32], [59572, 32], [59872, 32], [60172, 33], [60472, 33], [60772, 33], [61072, 33], [61372, 33], [61672, 34], [61972, 34], [62272, 34], [62572, 34], [62872, 34], [63172, 34], [63473, 34], [63773, 34], [64073, 34], [64373, 34], [64673, 34], [64973, 34], [65273, 35], [65573, 35], [65873, 35], [66173, 35], [66473, 35], [66773, 36], [67073, 36], [67373, 36], [67674, 35], [67974, 35], [68274, 35], [68574, 36], [68874, 36], [69174, 36], [69474, 35], [69774, 35], [70074, 35], [70374, 34], [70674, 34], [70974, 34], [71274, 33], [71574, 33], [71875, 32], [72175, 31], [72475, 31], [72775, 31], [73075, 30], [73375, 30], [73675, 30], [73975, 29], [74275, 29], [74575, 29], [74875, 28], [75175, 28], [75475, 28], [75775, 27], [76075, 27], [76376, 26], [76676, 25], [76976, 25], [77276, 25], [77576, 24], [77876, 24], [78176, 24], [78476, 23], [78776, 23], [79076, 23], [79376, 22], [79676, 22], [79976, 22], [80276, 21], [80577, 20], [80877, 20], [81177, 19], [81477, 19], [81777, 19], [82077, 18], [82377, 18], [82677, 18], [82977, 17], [83277, 17], [83577, 17], [83877, 16], [84177, 16], [84477, 16], [84777, 15], [85077, 15], [85377, 15], [85677, 14], [85977, 14], [86276, 15], [86576, 14], [86876, 14], [87176, 14], [87476, 13], [87776, 13], [88076, 13], [88376, 12], [88678, 10], [88979, 9], [89279, 8], [89579, 8], [89879, 8]], "point": [235, 195]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan3", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -0.25, "y": 1.12401652, "z": -1.25}, "object_poses": [{"objectName": "Pan_9d168802", "position": {"x": -0.6309, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": -1.73021924, "y": 0.419585526, "z": -1.2177}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": -1.3086729, "y": 1.32256436, "z": -3.42523718}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -2.179573, "y": 1.32392883, "z": -1.68147337}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.3086729, "y": 1.32392883, "z": -3.203495}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -0.2394, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -1.665155, "y": 0.344855428, "z": -2.26237869}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -1.53080976, "y": 1.32412946, "z": 2.069365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -1.68797278, "y": 0.421150625, "z": -0.561244547}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.73758006, "y": 1.343986, "z": -1.11794019}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.648636, "y": 1.343986, "z": -1.25536227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.82631838, "y": 1.09187412, "z": -0.405415058}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.95454407, "y": 1.09187412, "z": -0.633444369}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": -1.81848335, "y": 0.338814139, "z": 1.11066759}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": -1.75051689, "y": 0.338814139, "z": 1.52389443}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": -1.53080976, "y": 1.39739525, "z": 1.094923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.965999663, "y": 1.3713994, "z": -1.53499949}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": 1.056315, "y": 1.80610383, "z": 2.11270857}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.9200685, "y": 1.37188375, "z": 1.4197371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": -1.51363051, "y": 1.38091612, "z": 0.6621988}, "rotation": {"x": -3.61547645e-05, "y": -7.996309e-06, "z": 3.595849e-05}}, {"objectName": "Pot_930b560d", "position": {"x": -1.817123, "y": 0.350247264, "z": 0.6130607}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": 0.206582308, "y": 1.35270047, "z": -3.12197924}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": 0.970999956, "y": 1.52282739, "z": 1.78658581}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": -2.04982066, "y": 1.36275363, "z": -1.68147337}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": 0.8613707, "y": 1.20799184, "z": 1.2225256}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.91546834, "y": 1.33600378, "z": -1.04922915}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": -1.95454407, "y": 1.04844725, "z": -0.4510209}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -2.04982138, "y": 1.32404137, "z": 1.094923}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.6575036, "y": 0.321009457, "z": 2.316678}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_9d168802", "position": {"x": 0.5634017, "y": 1.32260954, "z": -3.01110744}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": 1.19326913, "y": 1.2028, "z": 1.25445545}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": 0.824245334, "y": 0.3459003, "z": -2.37100124}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.38003659, "y": 1.39912164, "z": -2.9817524}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.3086729, "y": 1.33799994, "z": -3.31436586}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_ddd73f57", "position": {"x": -1.38003659, "y": 1.42728543, "z": -2.76001024}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": -1.79031563, "y": 1.35692108, "z": -2.72122478}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -2.04982138, "y": 1.323614, "z": 0.770108938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": 0.824245334, "y": 0.339971542, "z": -1.75116086}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": 0.8577638, "y": 0.4601643, "z": 0.710978568}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -2.04982066, "y": 1.32412946, "z": -3.24110079}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.61049628, "y": 0.32906872, "z": 2.193}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.5068307, "y": 1.318, "z": -3.164938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3416245209, "scene_num": 3}, "task_id": "trial_T20190909_014747_191766", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A31681CCEVDIH3_3MYYFCXHJ6YBEK7WHG79H8UKWRS4GN", "high_descs": ["Turn left to face the coffee machine.", "Pick up the white mug from the coffee machine.", "Make a left to walk towards the microwave.", "Heat up the mug in the microwave, removing it again.", "Turn around and walk towards the sink.", "Put the white mug underneath the sink in the cabinet to the right of the cabinet directly below it."], "task_desc": "Heat up a mug to put it away in a cabinet.", "votes": [1, 1]}, {"assignment_id": "A1CPNA5L0J834N_3V5Q80FXI0I0HUGLINH15Z8D88G327", "high_descs": ["look down and then turn left towards the counter", "look at the coffee maker and grab a cup", "turn left with the cup in your hand walk forward then turn right at the microwave", "open up the microwave and then place cup inside and close the door turn on microwave then pull the cup back out close the door ", "holding the cup turn left and then turn right towards the sink", "open the cabinet and place cup inside on the shelf and then close the cabinet "], "task_desc": "heat up a mug and place it in a cabinet", "votes": [1, 1]}, {"assignment_id": "A13OOAT2ORKH6V_3WZ36BJEV67Z4OEBGVG3O5FFWDNTBX", "high_descs": ["Turn left to face the coffee maker on the counter.", "Grab the cup on the coffee maker that's on the counter.", "Turn left and walk to the microwave to your right that's on the stand.", "Place the cup inside the microwave, warm it and remove it.", "Turn right and walk to the cabinet under the sink to your right.", "Place the warm cup in the cabinet underneath the sink."], "task_desc": "Place a warm cup in the cabinet underneath the sink.", "votes": [1, 1]}]}}