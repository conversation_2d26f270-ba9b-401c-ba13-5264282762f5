{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000317.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000318.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000319.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000320.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000321.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000322.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000323.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000324.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000325.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000326.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000327.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000328.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000329.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000330.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000331.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000332.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000333.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000334.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000335.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000336.png", "low_idx": 40}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|10|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [5.14160156, 5.14160156, 10.00541304, 10.00541304, 3.7316, 3.7316]], "coordinateReceptacleObjectId": ["CounterTop", [6.02, 6.02, 9.24, 9.24, 3.8936, 3.8936]], "forceVisible": true, "objectId": "Mug|+01.29|+00.93|+02.50"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|1|6|1|-15"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [5.14160156, 5.14160156, 10.00541304, 10.00541304, 3.7316, 3.7316]], "coordinateReceptacleObjectId": ["Cabinet", [6.00313236, 6.00313236, 8.32905484, 8.32905484, 8.45383264, 8.45383264]], "forceVisible": true, "objectId": "Mug|+01.29|+00.93|+02.50", "receptacleObjectId": "Cabinet|+01.50|+02.11|+02.08"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+01.29|+00.93|+02.50"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [131, 146, 167, 192], "mask": [[43645, 9], [43942, 15], [44240, 19], [44538, 23], [44837, 25], [45136, 27], [45435, 29], [45734, 31], [46033, 33], [46333, 33], [46632, 35], [46932, 35], [47231, 36], [47531, 37], [47831, 37], [48131, 37], [48431, 37], [48731, 37], [49031, 37], [49331, 37], [49631, 37], [49931, 36], [50232, 35], [50532, 35], [50833, 33], [51133, 33], [51433, 33], [51733, 33], [52033, 32], [52334, 31], [52634, 31], [52934, 31], [53234, 31], [53534, 31], [53835, 29], [54135, 29], [54435, 29], [54735, 29], [55036, 27], [55336, 27], [55637, 25], [55938, 23], [56238, 23], [56539, 21], [56841, 17], [57142, 15], [57445, 9]], "point": [149, 168]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+01.29|+00.93|+02.50", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 89], [4908, 151], [5100, 83], [5215, 144], [5400, 81], [5519, 140], [5700, 80], [5822, 137], [6000, 80], [6123, 136], [6300, 80], [6424, 135], [6600, 80], [6724, 135], [6900, 80], [7028, 130], [7200, 80], [7330, 128], [7500, 81], [7631, 127], [7800, 81], [7923, 4], [7932, 126], [8100, 81], [8223, 5], [8233, 125], [8400, 81], [8523, 6], [8533, 125], [8700, 81], [8823, 7], [8833, 125], [9000, 81], [9123, 7], [9134, 124], [9300, 82], [9423, 7], [9434, 124], [9600, 82], [9723, 6], [9733, 125], [9900, 82], [10023, 6], [10033, 124], [10200, 82], [10323, 6], [10333, 124], [10500, 82], [10623, 6], [10633, 124], [10800, 82], [10923, 5], [10933, 124], [11100, 82], [11223, 5], [11232, 125], [11400, 82], [11523, 5], [11532, 125], [11700, 82], [11823, 4], [11832, 125], [12000, 83], [12123, 4], [12131, 126], [12300, 83], [12423, 4], [12431, 126], [12600, 83], [12723, 4], [12731, 126], [12900, 83], [13023, 3], [13030, 126], [13200, 83], [13323, 3], [13330, 126], [13500, 83], [13623, 3], [13630, 126], [13800, 83], [13923, 2], [13930, 126], [14100, 83], [14223, 2], [14229, 127], [14400, 83], [14523, 2], [14529, 127], [14700, 83], [14823, 1], [14829, 127], [15000, 83], [15123, 1], [15128, 128], [15300, 84], [15428, 128], [15600, 84], [15728, 128], [15900, 84], [16027, 128], [16200, 84], [16327, 128], [16500, 84], [16626, 129], [16800, 84], [16926, 129], [17100, 84], [17225, 130], [17400, 84], [17525, 130], [17700, 84], [17823, 132], [18000, 84], [18123, 132], [18300, 84], [18423, 132], [18600, 84], [18723, 132], [18900, 85], [19023, 131], [19200, 85], [19323, 131], [19500, 85], [19623, 131], [19800, 85], [19923, 131], [20100, 85], [20223, 131], [20400, 85], [20523, 131], [20700, 85], [20823, 131], [21000, 85], [21123, 131], [21300, 85], [21423, 131], [21600, 86], [21722, 132], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+01.29|+00.93|+02.50"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [80, 17, 133, 73], "mask": [[4889, 19], [5183, 32], [5481, 38], [5780, 42], [6080, 43], [6380, 44], [6680, 44], [6980, 48], [7280, 50], [7581, 50], [7881, 42], [7927, 5], [8181, 42], [8228, 5], [8481, 42], [8529, 4], [8781, 42], [8830, 3], [9081, 42], [9130, 4], [9382, 41], [9430, 4], [9682, 41], [9729, 4], [9982, 41], [10029, 4], [10282, 41], [10329, 4], [10582, 41], [10629, 4], [10882, 41], [10928, 5], [11182, 41], [11228, 4], [11482, 41], [11528, 4], [11782, 41], [11827, 5], [12083, 40], [12127, 4], [12383, 40], [12427, 4], [12683, 40], [12727, 4], [12983, 40], [13026, 4], [13283, 40], [13326, 4], [13583, 40], [13626, 4], [13883, 40], [13925, 5], [14183, 40], [14225, 4], [14483, 40], [14525, 4], [14783, 40], [14824, 5], [15083, 40], [15124, 4], [15384, 44], [15684, 44], [15984, 43], [16284, 43], [16584, 42], [16884, 42], [17184, 41], [17484, 41], [17784, 39], [18084, 39], [18384, 39], [18684, 39], [18985, 38], [19285, 38], [19585, 38], [19885, 38], [20185, 38], [20485, 38], [20785, 38], [21085, 38], [21385, 38], [21686, 36]], "point": [106, 44]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+01.50|+02.11|+02.08"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [33, 81, 119, 147], "mask": [[24041, 79], [24341, 79], [24641, 79], [24941, 79], [25240, 80], [25540, 80], [25840, 80], [26140, 80], [26440, 80], [26740, 80], [27040, 80], [27340, 80], [27639, 81], [27939, 81], [28239, 81], [28539, 81], [28839, 81], [29139, 81], [29439, 81], [29739, 81], [30038, 81], [30338, 81], [30638, 81], [30938, 81], [31238, 81], [31538, 81], [31838, 81], [32138, 81], [32438, 81], [32737, 82], [33037, 82], [33337, 82], [33637, 82], [33937, 82], [34237, 82], [34537, 82], [34837, 82], [35136, 83], [35436, 83], [35736, 83], [36036, 83], [36336, 83], [36636, 83], [36936, 83], [37236, 83], [37535, 84], [37835, 84], [38135, 84], [38435, 84], [38735, 84], [39035, 83], [39335, 83], [39635, 83], [39934, 84], [40234, 84], [40534, 84], [40834, 84], [41134, 84], [41434, 84], [41734, 84], [42034, 84], [42334, 84], [42633, 85], [42933, 85], [43233, 85], [43533, 85], [43834, 83]], "point": [76, 113]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+01.29|+00.93|+02.50", "placeStationary": true, "receptacleObjectId": "Cabinet|+01.50|+02.11|+02.08"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 27, 114, 146], "mask": [[7800, 1], [8100, 1], [8400, 2], [8700, 3], [9000, 4], [9300, 5], [9600, 5], [9900, 6], [10200, 7], [10500, 8], [10800, 8], [11100, 9], [11400, 10], [11700, 11], [12000, 12], [12300, 12], [12600, 13], [12900, 14], [13200, 15], [13500, 15], [13800, 16], [14100, 17], [14400, 18], [14700, 19], [15000, 19], [15300, 20], [15600, 21], [15900, 22], [16200, 22], [16500, 23], [16800, 24], [17100, 25], [17400, 25], [17700, 26], [18000, 27], [18300, 28], [18600, 29], [18900, 29], [19200, 30], [19500, 31], [19800, 32], [20100, 32], [20400, 33], [20700, 34], [21000, 35], [21300, 36], [21600, 36], [21900, 37], [22200, 38], [22500, 39], [22800, 39], [23100, 40], [23400, 41], [23700, 42], [24000, 42], [24300, 43], [24600, 42], [24900, 42], [24945, 70], [25200, 42], [25245, 70], [25500, 42], [25545, 70], [25800, 42], [25845, 70], [26100, 42], [26145, 70], [26400, 42], [26444, 71], [26700, 42], [26744, 71], [27000, 42], [27044, 71], [27300, 41], [27344, 71], [27600, 41], [27644, 71], [27900, 41], [27944, 71], [28200, 41], [28244, 71], [28500, 41], [28544, 71], [28800, 41], [28843, 72], [29100, 41], [29143, 72], [29400, 41], [29443, 72], [29700, 40], [29743, 71], [30000, 40], [30043, 71], [30300, 40], [30343, 71], [30600, 40], [30643, 71], [30900, 40], [30943, 71], [31200, 40], [31243, 71], [31500, 40], [31542, 72], [31800, 40], [31843, 71], [32100, 40], [32400, 39], [32700, 39], [33000, 39], [33042, 72], [33300, 39], [33342, 72], [33600, 39], [33642, 72], [33900, 39], [33941, 73], [34200, 39], [34241, 73], [34500, 39], [34541, 73], [34800, 38], [34841, 73], [35100, 38], [35141, 73], [35400, 38], [35441, 73], [35700, 38], [35741, 73], [36000, 38], [36041, 73], [36300, 38], [36341, 73], [36600, 38], [36640, 74], [36900, 38], [36940, 74], [37200, 37], [37240, 74], [37500, 37], [37540, 73], [37800, 37], [37840, 73], [38101, 36], [38140, 73], [38403, 34], [38440, 73], [38704, 33], [38740, 73], [39006, 31], [39040, 73], [39308, 29], [39339, 74], [39609, 28], [39639, 74], [39911, 25], [39939, 74], [40213, 23], [40239, 74], [40514, 22], [40539, 74], [40816, 20], [40839, 74], [41117, 19], [41139, 74], [41419, 17], [41439, 74], [41721, 15], [41738, 75], [42022, 14], [42038, 75], [42324, 11], [42338, 75], [42625, 10], [42638, 75], [42927, 8], [42938, 75], [43229, 6], [43238, 75], [43530, 5]], "point": [57, 85]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+01.50|+02.11|+02.08"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 27, 114, 146], "mask": [[7800, 1], [8100, 1], [8400, 2], [8700, 3], [9000, 4], [9300, 5], [9600, 5], [9900, 6], [10200, 7], [10500, 8], [10800, 8], [11100, 9], [11400, 10], [11700, 11], [12000, 12], [12300, 12], [12600, 13], [12900, 14], [13200, 15], [13500, 15], [13800, 16], [14100, 17], [14400, 18], [14700, 19], [15000, 19], [15300, 20], [15600, 21], [15900, 22], [16200, 22], [16500, 23], [16800, 24], [17100, 25], [17400, 25], [17700, 26], [18000, 27], [18300, 28], [18600, 29], [18900, 29], [19200, 30], [19500, 31], [19800, 32], [20100, 32], [20400, 33], [20700, 34], [21000, 35], [21300, 36], [21600, 36], [21900, 37], [22200, 38], [22500, 39], [22800, 39], [23100, 40], [23400, 41], [23700, 42], [24000, 42], [24300, 43], [24600, 42], [24900, 42], [24945, 70], [25200, 42], [25245, 70], [25500, 42], [25545, 70], [25800, 42], [25845, 70], [26100, 42], [26145, 70], [26400, 42], [26444, 71], [26700, 42], [26744, 71], [27000, 42], [27044, 71], [27300, 41], [27344, 71], [27600, 41], [27644, 71], [27900, 41], [27944, 71], [28200, 41], [28244, 71], [28500, 41], [28544, 71], [28800, 41], [28843, 72], [29100, 41], [29143, 72], [29400, 41], [29443, 72], [29700, 40], [29743, 71], [30000, 40], [30043, 71], [30300, 40], [30343, 71], [30600, 40], [30643, 71], [30900, 40], [30943, 71], [31200, 40], [31243, 71], [31500, 40], [31542, 72], [31800, 40], [31843, 71], [32100, 40], [32400, 39], [32700, 39], [33000, 39], [33042, 72], [33300, 39], [33342, 72], [33600, 39], [33642, 72], [33900, 39], [33941, 73], [34200, 39], [34241, 73], [34500, 39], [34541, 73], [34800, 38], [34841, 73], [35100, 38], [35141, 73], [35400, 38], [35441, 73], [35700, 38], [35741, 73], [36000, 38], [36041, 73], [36300, 38], [36341, 73], [36600, 38], [36640, 74], [36900, 38], [36940, 74], [37200, 37], [37240, 74], [37500, 37], [37540, 73], [37800, 37], [37840, 73], [38101, 36], [38140, 62], [38209, 4], [38403, 34], [38440, 59], [38704, 33], [38740, 58], [39006, 31], [39040, 57], [39308, 29], [39339, 56], [39609, 28], [39639, 55], [39696, 2], [39911, 25], [39939, 55], [39996, 2], [40213, 23], [40239, 55], [40296, 2], [40514, 22], [40539, 56], [40596, 2], [40816, 20], [40839, 56], [40897, 1], [41117, 19], [41139, 56], [41197, 1], [41419, 17], [41439, 57], [41497, 1], [41721, 15], [41738, 58], [42022, 14], [42038, 58], [42324, 11], [42338, 59], [42625, 10], [42638, 59], [42927, 8], [42938, 60], [43229, 6], [43238, 60], [43530, 5]], "point": [57, 85]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan12", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 0.75, "y": 0.9009999, "z": 2.0}, "object_poses": [{"objectName": "Pan_9f7abbea", "position": {"x": -1.109, "y": 0.9307289, "z": 1.4704}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": 1.42227757, "y": 0.8761, "z": 1.3413173}, "rotation": {"x": 1.40334191e-14, "y": 180.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": 1.36262453, "y": 0.9773547, "z": 1.756742}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": 1.51607227, "y": 0.9374642, "z": 0.9402925}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": -1.043155, "y": 0.9379421, "z": 0.9986881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_d40bfead", "position": {"x": 1.456627, "y": 0.945246041, "z": 1.84032464}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Fork_d40bfead", "position": {"x": 1.34493542, "y": 0.747115731, "z": 2.39195585}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.876905, "y": 0.9390294, "z": 1.0763762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.50362825, "y": 0.9798608, "z": 1.673159}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.38527226, "y": 1.07142246, "z": 0.478009224}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.39171886, "y": 0.08154476, "z": 1.059025}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -0.877188742, "y": 1.31421936, "z": 0.348954171}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -1.01283777, "y": 0.8101194, "z": 0.348953784}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -1.11238956, "y": 1.31421936, "z": -0.0343031}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": 1.39087415, "y": 0.119435936, "z": 2.91251349}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": -1.12628, "y": 1.02830076, "z": 0.921}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": -1.04122126, "y": 1.50041461, "z": 2.5538}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.9230003, "y": 0.990678668, "z": 2.26549983}, "rotation": {"x": -6.287087e-05, "y": 302.4704, "z": 5.009416e-05}}, {"objectName": "Fork_d40bfead", "position": {"x": -0.689004064, "y": 0.747115731, "z": 0.9829687}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -0.988836169, "y": 0.876337349, "z": -0.110954061}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": 1.710189, "y": 1.49748969, "z": 2.24829674}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -0.818, "y": 0.927, "z": 0.816}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": 1.275738, "y": 0.7618369, "z": 0.780662537}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.8965961, "y": 0.7688297, "z": 1.05093741}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": -0.677153468, "y": 0.08057672, "z": 0.7746959}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": -0.899039, "y": 0.615049839, "z": 0.0423488319}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": 1.41413271, "y": 0.7428734, "z": 2.595862}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": 1.3765, "y": 0.736837, "z": 2.2341}, "rotation": {"x": 0.0, "y": 29.9998684, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": -1.01283681, "y": 0.8477766, "z": 0.04234852}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -1.07811213, "y": 1.50646961, "z": 0.887962461}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": -0.79279995, "y": 0.7461314, "z": 0.847031236}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.55604517, "y": 0.105368823, "z": 2.945116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.38253057, "y": 0.0995715857, "z": -0.131285489}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_9f7abbea", "position": {"x": -1.109, "y": 0.9307289, "z": 1.8657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": 1.36262441, "y": 0.9458269, "z": 1.67315912}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": 1.28540039, "y": 0.9329, "z": 2.50135326}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 450209002, "scene_num": 12}, "task_id": "trial_T20190909_032940_018377", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A31681CCEVDIH3_33PPUNGG3BWI674ZIKRBA8L0ZVHZR1", "high_descs": ["Make a left to face the coffee machine.", "Pick up the black mug from the counter.", "Walk right towards the microwave.", "Heat up the mug in the microwave and remove it again.", "Turn to face the cabinets above the microwave.", "Set the mug on the bottom shelf of the left cabinet above the microwave."], "task_desc": "Heat up a black mug and put it away over the microwave.", "votes": [1, 1, 1]}, {"assignment_id": "AM2KK02JXXW48_3ITXP059PZA477Z8FNOUCO262ZESJO", "high_descs": ["Move to the mug on the counter, to your left.", "Pick up the mug on the counter.", "Bring the mug to the microwave, above the sink to your right.", "Heat the mug in the microwave. ", "Bring the heated mug to the the left cabinet above the microwave. ", "Put the heated mug in the the left cabinet above the microwave. "], "task_desc": "Put a heated mug in the the left cabinet above the microwave. ", "votes": [1, 1, 1]}, {"assignment_id": "A24RGLS3BRZ60J_34QN5IT0T2IFMHJDFH8JDSN9RWP08D", "high_descs": ["Take a few steps left and look at the coffee machine.", "Pick up the cup from the counter.", "Carry the cup and take a few steps right, and look up the microwave.", "Open the microwave and place the cup inside. Shut the door then open it again. Take out the cup and shut the door.", "Hold the cup and look above the microwave.", "Open the top left cabinet and place the cup inside. Shut the door."], "task_desc": "Heat a cup and place it in the cabinet.", "votes": [1, 0, 1]}]}}