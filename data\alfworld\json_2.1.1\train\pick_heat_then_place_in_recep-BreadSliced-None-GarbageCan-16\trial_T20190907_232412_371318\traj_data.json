{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 37}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 49}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 50}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 51}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 52}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 66}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 66}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 68}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 68}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000299.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000300.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000301.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000302.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000303.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000304.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000305.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000306.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000307.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000308.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000309.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000310.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000311.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000312.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000313.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000314.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000315.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000316.png", "low_idx": 71}, {"high_idx": 7, "image_name": "000000317.png", "low_idx": 72}, {"high_idx": 7, "image_name": "000000318.png", "low_idx": 72}, {"high_idx": 7, "image_name": "000000319.png", "low_idx": 72}, {"high_idx": 7, "image_name": "000000320.png", "low_idx": 72}, {"high_idx": 7, "image_name": "000000321.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000322.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000323.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000324.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000325.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000326.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000327.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000328.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000329.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000330.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000331.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000332.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000333.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000334.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000335.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000336.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000337.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000338.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000339.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000340.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000341.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000342.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000343.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000344.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000345.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000346.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000347.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000348.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000349.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000350.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000351.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000352.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000353.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000354.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000355.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000356.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000357.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000358.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000359.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000360.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000361.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000362.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000363.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000364.png", "low_idx": 77}, {"high_idx": 7, "image_name": "000000365.png", "low_idx": 77}, {"high_idx": 7, "image_name": "000000366.png", "low_idx": 77}, {"high_idx": 7, "image_name": "000000367.png", "low_idx": 77}, {"high_idx": 7, "image_name": "000000368.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000369.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000370.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000371.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000372.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000373.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000374.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000375.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000376.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000377.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000378.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000379.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000380.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000381.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000382.png", "low_idx": 78}, {"high_idx": 7, "image_name": "000000383.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000384.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000385.png", "low_idx": 79}, {"high_idx": 7, "image_name": "000000386.png", "low_idx": 79}, {"high_idx": 8, "image_name": "000000387.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000388.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000389.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000390.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000391.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000392.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000393.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000394.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000395.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000396.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000397.png", "low_idx": 80}, {"high_idx": 8, "image_name": "000000398.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000399.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000400.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000401.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000402.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000403.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000404.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000405.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000406.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000407.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000408.png", "low_idx": 81}, {"high_idx": 8, "image_name": "000000409.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000410.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000411.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000412.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000413.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000414.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000415.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000416.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000417.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000418.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000419.png", "low_idx": 82}, {"high_idx": 8, "image_name": "000000420.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000421.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000422.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000423.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000424.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000425.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000426.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000427.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000428.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000429.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000430.png", "low_idx": 83}, {"high_idx": 8, "image_name": "000000431.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000432.png", "low_idx": 84}, {"high_idx": 8, "image_name": "000000433.png", "low_idx": 85}, {"high_idx": 8, "image_name": "000000434.png", "low_idx": 85}, {"high_idx": 8, "image_name": "000000435.png", "low_idx": 86}, {"high_idx": 8, "image_name": "000000436.png", "low_idx": 86}, {"high_idx": 8, "image_name": "000000437.png", "low_idx": 87}, {"high_idx": 8, "image_name": "000000438.png", "low_idx": 87}, {"high_idx": 8, "image_name": "000000439.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000440.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000441.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000442.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000443.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000444.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000445.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000446.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000447.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000448.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000449.png", "low_idx": 88}, {"high_idx": 8, "image_name": "000000450.png", "low_idx": 89}, {"high_idx": 8, "image_name": "000000451.png", "low_idx": 89}, {"high_idx": 8, "image_name": "000000452.png", "low_idx": 90}, {"high_idx": 8, "image_name": "000000453.png", "low_idx": 90}, {"high_idx": 8, "image_name": "000000454.png", "low_idx": 91}, {"high_idx": 8, "image_name": "000000455.png", "low_idx": 91}, {"high_idx": 8, "image_name": "000000456.png", "low_idx": 92}, {"high_idx": 8, "image_name": "000000457.png", "low_idx": 92}, {"high_idx": 8, "image_name": "000000458.png", "low_idx": 93}, {"high_idx": 8, "image_name": "000000459.png", "low_idx": 93}, {"high_idx": 8, "image_name": "000000460.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000461.png", "low_idx": 94}, {"high_idx": 8, "image_name": "000000462.png", "low_idx": 95}, {"high_idx": 8, "image_name": "000000463.png", "low_idx": 95}, {"high_idx": 8, "image_name": "000000464.png", "low_idx": 96}, {"high_idx": 8, "image_name": "000000465.png", "low_idx": 96}, {"high_idx": 8, "image_name": "000000466.png", "low_idx": 97}, {"high_idx": 8, "image_name": "000000467.png", "low_idx": 97}, {"high_idx": 8, "image_name": "000000468.png", "low_idx": 98}, {"high_idx": 8, "image_name": "000000469.png", "low_idx": 98}, {"high_idx": 8, "image_name": "000000470.png", "low_idx": 99}, {"high_idx": 8, "image_name": "000000471.png", "low_idx": 99}, {"high_idx": 8, "image_name": "000000472.png", "low_idx": 100}, {"high_idx": 8, "image_name": "000000473.png", "low_idx": 100}, {"high_idx": 8, "image_name": "000000474.png", "low_idx": 101}, {"high_idx": 8, "image_name": "000000475.png", "low_idx": 101}, {"high_idx": 8, "image_name": "000000476.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000477.png", "low_idx": 102}, {"high_idx": 8, "image_name": "000000478.png", "low_idx": 103}, {"high_idx": 8, "image_name": "000000479.png", "low_idx": 103}, {"high_idx": 8, "image_name": "000000480.png", "low_idx": 104}, {"high_idx": 8, "image_name": "000000481.png", "low_idx": 104}, {"high_idx": 8, "image_name": "000000482.png", "low_idx": 105}, {"high_idx": 8, "image_name": "000000483.png", "low_idx": 105}, {"high_idx": 8, "image_name": "000000484.png", "low_idx": 106}, {"high_idx": 8, "image_name": "000000485.png", "low_idx": 106}, {"high_idx": 8, "image_name": "000000486.png", "low_idx": 106}, {"high_idx": 8, "image_name": "000000487.png", "low_idx": 106}, {"high_idx": 8, "image_name": "000000488.png", "low_idx": 106}, {"high_idx": 8, "image_name": "000000489.png", "low_idx": 106}, {"high_idx": 8, "image_name": "000000490.png", "low_idx": 106}, {"high_idx": 8, "image_name": "000000491.png", "low_idx": 106}, {"high_idx": 8, "image_name": "000000492.png", "low_idx": 106}, {"high_idx": 8, "image_name": "000000493.png", "low_idx": 106}, {"high_idx": 8, "image_name": "000000494.png", "low_idx": 106}, {"high_idx": 8, "image_name": "000000495.png", "low_idx": 107}, {"high_idx": 8, "image_name": "000000496.png", "low_idx": 107}, {"high_idx": 8, "image_name": "000000497.png", "low_idx": 108}, {"high_idx": 8, "image_name": "000000498.png", "low_idx": 108}, {"high_idx": 8, "image_name": "000000499.png", "low_idx": 109}, {"high_idx": 8, "image_name": "000000500.png", "low_idx": 109}, {"high_idx": 8, "image_name": "000000501.png", "low_idx": 110}, {"high_idx": 8, "image_name": "000000502.png", "low_idx": 110}, {"high_idx": 8, "image_name": "000000503.png", "low_idx": 111}, {"high_idx": 8, "image_name": "000000504.png", "low_idx": 111}, {"high_idx": 8, "image_name": "000000505.png", "low_idx": 112}, {"high_idx": 8, "image_name": "000000506.png", "low_idx": 112}, {"high_idx": 8, "image_name": "000000507.png", "low_idx": 113}, {"high_idx": 8, "image_name": "000000508.png", "low_idx": 113}, {"high_idx": 8, "image_name": "000000509.png", "low_idx": 114}, {"high_idx": 8, "image_name": "000000510.png", "low_idx": 114}, {"high_idx": 8, "image_name": "000000511.png", "low_idx": 115}, {"high_idx": 8, "image_name": "000000512.png", "low_idx": 115}, {"high_idx": 8, "image_name": "000000513.png", "low_idx": 115}, {"high_idx": 8, "image_name": "000000514.png", "low_idx": 115}, {"high_idx": 8, "image_name": "000000515.png", "low_idx": 115}, {"high_idx": 8, "image_name": "000000516.png", "low_idx": 115}, {"high_idx": 8, "image_name": "000000517.png", "low_idx": 115}, {"high_idx": 8, "image_name": "000000518.png", "low_idx": 115}, {"high_idx": 8, "image_name": "000000519.png", "low_idx": 115}, {"high_idx": 8, "image_name": "000000520.png", "low_idx": 115}, {"high_idx": 8, "image_name": "000000521.png", "low_idx": 115}, {"high_idx": 8, "image_name": "000000522.png", "low_idx": 116}, {"high_idx": 8, "image_name": "000000523.png", "low_idx": 116}, {"high_idx": 8, "image_name": "000000524.png", "low_idx": 116}, {"high_idx": 8, "image_name": "000000525.png", "low_idx": 116}, {"high_idx": 8, "image_name": "000000526.png", "low_idx": 116}, {"high_idx": 8, "image_name": "000000527.png", "low_idx": 116}, {"high_idx": 8, "image_name": "000000528.png", "low_idx": 116}, {"high_idx": 8, "image_name": "000000529.png", "low_idx": 116}, {"high_idx": 8, "image_name": "000000530.png", "low_idx": 116}, {"high_idx": 8, "image_name": "000000531.png", "low_idx": 116}, {"high_idx": 8, "image_name": "000000532.png", "low_idx": 116}, {"high_idx": 9, "image_name": "000000533.png", "low_idx": 117}, {"high_idx": 9, "image_name": "000000534.png", "low_idx": 117}, {"high_idx": 9, "image_name": "000000535.png", "low_idx": 117}, {"high_idx": 9, "image_name": "000000536.png", "low_idx": 117}, {"high_idx": 9, "image_name": "000000537.png", "low_idx": 117}, {"high_idx": 9, "image_name": "000000538.png", "low_idx": 117}, {"high_idx": 9, "image_name": "000000539.png", "low_idx": 117}, {"high_idx": 9, "image_name": "000000540.png", "low_idx": 117}, {"high_idx": 9, "image_name": "000000541.png", "low_idx": 117}, {"high_idx": 9, "image_name": "000000542.png", "low_idx": 117}, {"high_idx": 9, "image_name": "000000543.png", "low_idx": 117}, {"high_idx": 9, "image_name": "000000544.png", "low_idx": 117}, {"high_idx": 9, "image_name": "000000545.png", "low_idx": 117}, {"high_idx": 9, "image_name": "000000546.png", "low_idx": 117}, {"high_idx": 9, "image_name": "000000547.png", "low_idx": 117}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|6|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [-2.857599736, -2.857599736, 5.97013952, 5.97013952, 3.266392948, 3.266392948]], "coordinateReceptacleObjectId": ["SinkBasin", [-2.8576, -2.8576, 5.3932, 5.3932, 3.224, 3.224]], "forceVisible": true, "objectId": "ButterKnife|-00.71|+00.82|+01.49"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|10|12|0|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [10.39774992, 10.39774992, 15.88, 15.88, 3.330284, 3.330284]], "forceVisible": true, "objectId": "Bread|+02.60|+00.83|+03.97"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "diningtable"]}, "high_idx": 4, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [-2.857599736, -2.857599736, 5.97013952, 5.97013952, 3.266392948, 3.266392948]], "coordinateReceptacleObjectId": ["DiningTable", [9.788, 9.788, 15.904, 15.904, 2.673336984, 2.673336984]], "forceVisible": true, "objectId": "ButterKnife|-00.71|+00.82|+01.49", "receptacleObjectId": "DiningTable|+02.45|+00.67|+03.98"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [10.39774992, 10.39774992, 15.88, 15.88, 3.330284, 3.330284]], "coordinateReceptacleObjectId": ["DiningTable", [9.788, 9.788, 15.904, 15.904, 2.673336984, 2.673336984]], "forceVisible": true, "objectId": "Bread|+02.60|+00.83|+03.97|BreadSliced_8"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|8|-4|2|0"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.758, 7.758, -7.3052, -7.3052, 6.982419, 6.982419]], "forceVisible": true, "objectId": "Microwave|+01.94|+01.75|-01.83"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-4|13|2|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "garbagecan"]}, "high_idx": 9, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [10.39774992, 10.39774992, 15.88, 15.88, 3.330284, 3.330284]], "coordinateReceptacleObjectId": ["GarbageCan", [-3.805050612, -3.805050612, 11.30460928, 11.30460928, -0.0039073228, -0.0039073228]], "forceVisible": true, "objectId": "Bread|+02.60|+00.83|+03.97|BreadSliced_8", "receptacleObjectId": "GarbageCan|-00.95|00.00|+02.83"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 10, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|-00.71|+00.82|+01.49"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [114, 155, 180, 159], "mask": [[46325, 13], [46619, 26], [46666, 13], [46915, 66], [47214, 67], [47515, 1], [47555, 26]], "point": [147, 156]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|+02.60|+00.83|+03.97"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [153, 94, 191, 148], "mask": [[28064, 14], [28360, 22], [28657, 26], [28956, 28], [29255, 30], [29554, 32], [29854, 32], [30154, 33], [30454, 33], [30753, 34], [31053, 34], [31353, 34], [31653, 35], [31953, 35], [32253, 35], [32553, 35], [32853, 36], [33153, 36], [33454, 35], [33754, 35], [34054, 35], [34354, 35], [34654, 36], [34954, 36], [35254, 36], [35554, 36], [35854, 36], [36153, 37], [36453, 38], [36753, 38], [37053, 38], [37353, 38], [37654, 37], [37954, 38], [38254, 38], [38554, 38], [38854, 38], [39154, 38], [39454, 38], [39754, 38], [40054, 37], [40354, 37], [40654, 37], [40954, 37], [41254, 37], [41554, 37], [41854, 37], [42154, 37], [42454, 37], [42754, 37], [43054, 36], [43355, 35], [43655, 35], [43956, 34], [44256, 32]], "point": [172, 120]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|-00.71|+00.82|+01.49", "placeStationary": true, "receptacleObjectId": "DiningTable|+02.45|+00.67|+03.98"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 60, 299, 300], "mask": [[17835, 13], [18126, 21], [18161, 4], [18426, 21], [18461, 16], [18704, 5], [18725, 22], [18761, 18], [18998, 12], [19025, 22], [19061, 18], [19085, 1], [19292, 18], [19325, 22], [19361, 18], [19385, 6], [19588, 22], [19625, 22], [19661, 17], [19685, 10], [19885, 26], [19925, 22], [19961, 16], [19986, 14], [20181, 30], [20225, 23], [20261, 14], [20288, 16], [20478, 33], [20525, 23], [20560, 14], [20589, 18], [20774, 38], [20824, 24], [20860, 13], [20889, 21], [21071, 41], [21124, 24], [21160, 13], [21189, 24], [21368, 44], [21424, 25], [21459, 13], [21490, 25], [21664, 49], [21724, 25], [21759, 13], [21790, 28], [21962, 51], [22024, 26], [22058, 14], [22090, 31], [22260, 54], [22323, 27], [22358, 14], [22390, 33], [22557, 59], [22620, 32], [22656, 16], [22690, 36], [22855, 117], [22990, 39], [23153, 119], [23290, 41], [23451, 121], [23590, 43], [23749, 123], [23890, 45], [24046, 126], [24190, 47], [24344, 128], [24490, 48], [24646, 126], [24790, 16], [24810, 30], [24949, 121], [25090, 15], [25111, 31], [25251, 115], [25391, 14], [25411, 33], [25552, 112], [25691, 14], [25711, 35], [25853, 109], [25992, 12], [26010, 37], [26154, 107], [26292, 12], [26311, 38], [26455, 104], [26592, 12], [26611, 40], [26756, 101], [26892, 11], [26911, 42], [27056, 100], [27192, 11], [27211, 43], [27357, 98], [27493, 10], [27511, 45], [27657, 97], [27793, 10], [27811, 46], [27957, 97], [28093, 9], [28111, 48], [28258, 96], [28393, 9], [28411, 49], [28558, 96], [28693, 9], [28711, 50], [28858, 96], [28993, 8], [29010, 52], [29158, 97], [29292, 9], [29310, 53], [29458, 96], [29592, 9], [29610, 54], [29757, 97], [29892, 9], [29910, 56], [30057, 97], [30191, 9], [30210, 57], [30357, 97], [30490, 11], [30510, 58], [30657, 96], [30788, 14], [30809, 60], [30957, 80], [31043, 10], [31087, 18], [31107, 63], [31257, 78], [31345, 8], [31387, 84], [31556, 78], [31646, 7], [31688, 84], [31856, 77], [31947, 6], [31988, 84], [32156, 77], [32247, 6], [32288, 85], [32456, 76], [32548, 5], [32588, 86], [32756, 76], [32848, 5], [32888, 87], [33056, 76], [33147, 6], [33189, 87], [33356, 76], [33447, 7], [33489, 87], [33656, 77], [33739, 2], [33747, 7], [33789, 88], [33955, 78], [34038, 5], [34047, 7], [34089, 88], [34255, 79], [34337, 6], [34346, 8], [34389, 89], [34555, 79], [34637, 5], [34645, 9], [34690, 89], [34855, 81], [34944, 10], [34990, 89], [35154, 84], [35242, 12], [35290, 90], [35454, 85], [35541, 13], [35590, 90], [35754, 51], [35815, 24], [35841, 13], [35890, 91], [36053, 48], [36119, 20], [36141, 13], [36190, 91], [36353, 45], [36421, 18], [36441, 13], [36491, 91], [36652, 44], [36723, 16], [36741, 13], [36791, 92], [36952, 43], [37024, 15], [37041, 12], [37091, 92], [37251, 42], [37326, 13], [37341, 12], [37391, 93], [37551, 41], [37626, 12], [37641, 12], [37691, 93], [37850, 41], [37927, 11], [37941, 12], [37991, 94], [38149, 41], [38227, 11], [38241, 13], [38292, 93], [38448, 41], [38528, 10], [38541, 13], [38592, 94], [38747, 42], [38828, 10], [38841, 13], [38892, 95], [39046, 43], [39128, 10], [39141, 13], [39192, 95], [39345, 43], [39428, 10], [39441, 13], [39492, 96], [39644, 44], [39728, 10], [39741, 13], [39792, 96], [39942, 46], [40028, 10], [40041, 13], [40092, 97], [40202, 6], [40241, 47], [40329, 9], [40341, 13], [40392, 97], [40502, 9], [40538, 50], [40629, 9], [40641, 13], [40692, 98], [40803, 7], [40836, 52], [40929, 9], [40941, 13], [40992, 99], [41132, 56], [41229, 9], [41241, 13], [41292, 99], [41418, 6], [41429, 60], [41529, 9], [41541, 13], [41592, 100], [41716, 8], [41729, 60], [41829, 9], [41841, 13], [41892, 100], [42014, 9], [42028, 61], [42129, 9], [42141, 13], [42192, 101], [42300, 2], [42309, 14], [42327, 62], [42429, 9], [42441, 13], [42492, 101], [42600, 22], [42627, 63], [42729, 9], [42740, 14], [42792, 102], [42900, 22], [42926, 64], [43029, 9], [43040, 14], [43092, 102], [43200, 21], [43225, 65], [43329, 9], [43340, 14], [43392, 103], [43500, 21], [43524, 66], [43629, 9], [43640, 14], [43691, 104], [43800, 20], [43823, 68], [43929, 9], [43940, 14], [43991, 105], [44100, 20], [44123, 68], [44229, 9], [44240, 15], [44291, 105], [44400, 19], [44422, 69], [44528, 10], [44540, 15], [44591, 106], [44700, 19], [44722, 69], [44828, 27], [44891, 106], [45000, 18], [45021, 71], [45127, 28], [45191, 107], [45300, 18], [45320, 72], [45426, 30], [45491, 107], [45600, 17], [45620, 73], [45725, 31], [45789, 110], [45900, 16], [45919, 74], [46024, 38], [46085, 114], [46200, 16], [46219, 75], [46323, 192], [46518, 78], [46622, 192], [46818, 79], [46920, 194], [47118, 81], [47217, 196], [47417, 86], [47514, 103], [47625, 88], [47717, 196], [47928, 84], [48016, 194], [48230, 81], [48316, 192], [48531, 80], [48616, 190], [48832, 78], [48915, 190], [49133, 77], [49215, 188], [49434, 75], [49514, 188], [49736, 73], [49814, 186], [50037, 71], [50113, 186], [50338, 70], [50413, 185], [50639, 69], [50712, 185], [50940, 68], [51011, 185], [51241, 254], [51542, 252], [51843, 251], [52144, 249], [52445, 81], [52532, 161], [52746, 78], [52834, 159], [53047, 76], [53136, 157], [53348, 74], [53437, 155], [53649, 72], [53738, 154], [53950, 71], [54039, 152], [54251, 69], [54339, 152], [54552, 68], [54640, 151], [54854, 66], [54940, 150], [55155, 65], [55241, 149], [55456, 64], [55541, 149], [55757, 63], [55841, 149], [56058, 62], [56141, 149], [56359, 61], [56441, 150], [56660, 61], [56741, 151], [56961, 60], [57040, 153], [57262, 60], [57340, 154], [57563, 59], [57639, 155], [57865, 58], [57938, 157], [58166, 58], [58237, 159], [58467, 59], [58536, 161], [58768, 61], [58833, 164], [59069, 229], [59370, 229], [59671, 229], [59973, 228], [60274, 227], [60575, 190], [60773, 29], [60875, 188], [61076, 27], [61176, 186], [61379, 25], [61477, 184], [61681, 24], [61778, 182], [61983, 23], [62078, 21], [62100, 160], [62284, 23], [62379, 20], [62400, 160], [62586, 21], [62679, 19], [62700, 160], [62887, 21], [62980, 18], [63000, 159], [63189, 20], [63280, 17], [63300, 159], [63490, 20], [63580, 16], [63600, 159], [63791, 20], [63880, 16], [63900, 159], [64092, 20], [64180, 15], [64200, 160], [64393, 20], [64480, 15], [64500, 160], [64694, 20], [64780, 14], [64800, 160], [64995, 19], [65080, 14], [65100, 160], [65296, 19], [65379, 14], [65400, 160], [65597, 19], [65679, 14], [65700, 160], [65898, 19], [65978, 14], [66000, 161], [66199, 18], [66278, 14], [66300, 161], [66501, 17], [66578, 13], [66600, 161], [66802, 17], [66877, 14], [66900, 162], [67103, 17], [67177, 13], [67200, 162], [67404, 17], [67476, 14], [67500, 162], [67705, 17], [67776, 13], [67800, 163], [68005, 18], [68075, 14], [68100, 163], [68306, 17], [68374, 14], [68400, 163], [68606, 18], [68673, 14], [68700, 164], [68907, 18], [68971, 16], [69000, 164], [69207, 19], [69270, 16], [69300, 165], [69508, 19], [69568, 18], [69600, 166], [69808, 20], [69867, 18], [69900, 166], [70109, 20], [70165, 20], [70200, 167], [70409, 21], [70464, 20], [70500, 167], [70710, 21], [70762, 22], [70800, 168], [71010, 22], [71060, 23], [71100, 168], [71310, 22], [71359, 24], [71400, 169], [71610, 23], [71657, 25], [71700, 169], [71910, 24], [71955, 26], [72000, 170], [72210, 25], [72254, 26], [72300, 170], [72510, 26], [72552, 27], [72600, 171], [72810, 27], [72851, 27], [72900, 172], [73109, 29], [73149, 28], [73200, 173], [73409, 31], [73447, 29], [73500, 174], [73708, 33], [73745, 30], [73800, 174], [74008, 34], [74043, 30], [74100, 175], [74307, 65], [74400, 176], [74606, 65], [74700, 177], [74906, 64], [75000, 177], [75205, 64], [75300, 74], [75383, 95], [75504, 64], [75600, 66], [75702, 76], [75803, 63], [75901, 62], [76011, 68], [76102, 63], [76202, 59], [76324, 55], [76401, 63], [76503, 56], [76649, 2], [76704, 59], [76803, 56], [77041, 21], [77104, 55], [77342, 19], [77405, 53], [77643, 16], [77706, 52], [77944, 14], [78007, 51], [78244, 13], [78309, 49], [78544, 12], [78610, 48], [78844, 11], [78912, 47], [79020, 18], [79143, 11], [79214, 47], [79307, 39], [79441, 11], [79515, 48], [79597, 57], [79738, 13], [79817, 143], [80034, 16], [80119, 146], [80331, 18], [80420, 150], [80627, 21], [80722, 153], [80923, 24], [81024, 158], [81218, 27], [81325, 169], [81514, 30], [81627, 216], [81929, 213], [82230, 210], [82532, 206], [82834, 201], [83136, 197], [83437, 193], [83739, 189], [84041, 184], [84342, 181], [84644, 176], [84946, 172], [85247, 168], [85549, 164], [85851, 159], [86152, 156], [86454, 151], [86757, 146], [87062, 138], [87367, 131], [87671, 124], [87976, 117], [88280, 110], [88585, 103], [88890, 95], [89194, 83], [89499, 66], [89804, 49]], "point": [149, 179]}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+02.60|+00.83|+03.97|BreadSliced_8"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [156, 135, 190, 154], "mask": [[40366, 16], [40662, 24], [40960, 28], [41259, 30], [41558, 32], [41858, 32], [42158, 32], [42458, 32], [42758, 32], [43057, 33], [43356, 34], [43656, 35], [43956, 35], [44256, 35], [44556, 35], [44856, 35], [45156, 35], [45456, 35], [45756, 33], [46062, 23]], "point": [173, 143]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.94|+01.75|-01.83"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [48, 1, 296, 88], "mask": [[48, 248], [348, 248], [648, 248], [948, 248], [1248, 248], [1548, 248], [1848, 248], [2148, 248], [2448, 248], [2748, 248], [3048, 248], [3348, 248], [3648, 248], [3948, 248], [4248, 248], [4548, 248], [4848, 248], [5148, 248], [5448, 248], [5748, 248], [6048, 248], [6348, 248], [6648, 248], [6948, 248], [7248, 248], [7548, 248], [7848, 248], [8148, 248], [8448, 248], [8748, 248], [9048, 248], [9348, 248], [9648, 248], [9948, 248], [10248, 248], [10548, 248], [10848, 248], [11148, 248], [11448, 248], [11748, 248], [12048, 248], [12348, 248], [12648, 248], [12948, 248], [13248, 248], [13548, 248], [13848, 248], [14148, 248], [14448, 248], [14748, 248], [15048, 248], [15348, 248], [15648, 248], [15948, 248], [16248, 248], [16548, 248], [16848, 248], [17148, 248], [17448, 248], [17748, 248], [18048, 248], [18348, 248], [18648, 248], [18948, 248], [19248, 248], [19548, 248], [19848, 248], [20148, 248], [20448, 248], [20748, 248], [21048, 248], [21348, 248], [21648, 248], [21948, 248], [22248, 248], [22548, 249], [22848, 249], [23148, 249], [23448, 249], [23749, 246], [24050, 243], [24352, 239], [24653, 236], [24955, 232], [25256, 229], [25558, 225], [25859, 222], [26161, 218]], "point": [172, 43]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+02.60|+00.83|+03.97|BreadSliced_8", "placeStationary": true, "receptacleObjectId": "Microwave|+01.94|+01.75|-01.83"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 296, 88], "mask": [[0, 296], [300, 296], [600, 296], [900, 296], [1200, 296], [1500, 296], [1800, 296], [2100, 296], [2400, 296], [2700, 296], [3000, 296], [3300, 296], [3600, 296], [3900, 296], [4200, 296], [4500, 296], [4800, 296], [5100, 296], [5400, 296], [5700, 296], [6000, 296], [6300, 296], [6600, 296], [6900, 296], [7200, 296], [7500, 296], [7800, 296], [8100, 296], [8400, 296], [8700, 296], [9000, 296], [9300, 296], [9600, 296], [9900, 296], [10201, 295], [10502, 294], [10803, 293], [11105, 291], [11406, 290], [11707, 289], [12008, 288], [12310, 286], [12611, 285], [12912, 284], [13214, 282], [13515, 281], [13816, 280], [14118, 278], [14419, 277], [14720, 276], [15021, 275], [15323, 273], [15624, 272], [15925, 271], [16227, 269], [16528, 268], [16829, 267], [17130, 266], [17432, 264], [17733, 263], [18034, 262], [18336, 260], [18637, 259], [18938, 258], [19239, 257], [19541, 255], [19842, 254], [20143, 253], [20445, 251], [20746, 250], [21047, 249], [21348, 248], [21648, 248], [21948, 248], [22248, 248], [22548, 249], [22848, 249], [23148, 249], [23448, 249], [23749, 246], [24050, 243], [24352, 239], [24653, 236], [24955, 232], [25256, 229], [25558, 225], [25859, 222], [26161, 218]], "point": [148, 43]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.94|+01.75|-01.83"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 296, 88], "mask": [[0, 133], [148, 148], [300, 132], [450, 146], [600, 131], [751, 1], [760, 136], [900, 130], [1061, 135], [1200, 130], [1363, 133], [1500, 130], [1664, 132], [1800, 130], [1965, 131], [2100, 130], [2266, 130], [2400, 130], [2566, 130], [2700, 130], [2867, 129], [3000, 130], [3167, 129], [3300, 130], [3468, 128], [3600, 130], [3768, 128], [3900, 130], [4068, 128], [4200, 130], [4369, 127], [4500, 130], [4669, 127], [4800, 130], [4969, 127], [5100, 130], [5269, 127], [5400, 130], [5569, 127], [5700, 130], [5870, 126], [6000, 130], [6170, 126], [6300, 130], [6470, 126], [6600, 130], [6770, 126], [6900, 130], [7070, 126], [7200, 130], [7370, 126], [7500, 130], [7670, 126], [7800, 130], [7970, 126], [8100, 130], [8270, 126], [8400, 130], [8570, 126], [8700, 130], [8870, 126], [9000, 130], [9170, 126], [9300, 130], [9470, 126], [9600, 130], [9770, 126], [9900, 130], [10070, 126], [10201, 129], [10370, 126], [10502, 128], [10670, 126], [10803, 127], [10970, 126], [11105, 125], [11269, 127], [11406, 124], [11569, 127], [11707, 123], [11868, 128], [12008, 122], [12168, 128], [12310, 120], [12468, 128], [12611, 119], [12767, 129], [12912, 118], [13067, 129], [13214, 116], [13367, 129], [13515, 115], [13666, 130], [13816, 114], [13966, 130], [14118, 112], [14265, 131], [14419, 111], [14564, 132], [14720, 110], [14862, 134], [15021, 109], [15160, 136], [15323, 107], [15451, 145], [15624, 106], [15748, 148], [15925, 109], [16043, 153], [16227, 269], [16528, 268], [16829, 267], [17130, 266], [17432, 264], [17733, 263], [18034, 262], [18336, 260], [18637, 259], [18938, 258], [19239, 257], [19541, 255], [19842, 254], [20143, 253], [20445, 251], [20746, 250], [21047, 249], [21348, 248], [21648, 248], [21948, 248], [22248, 248], [22548, 249], [22848, 249], [23148, 249], [23448, 249], [23749, 246], [24050, 243], [24352, 239], [24653, 236], [24955, 232], [25256, 229], [25558, 225], [25859, 222], [26161, 218]], "point": [151, 51]}}, "high_idx": 7}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.758, 7.758, -7.3052, -7.3052, 6.982419, 6.982419]], "forceVisible": true, "objectId": "Microwave|+01.94|+01.75|-01.83"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [48, 1, 296, 88], "mask": [[48, 248], [348, 248], [648, 248], [948, 248], [1248, 248], [1548, 248], [1848, 248], [2148, 248], [2448, 248], [2748, 248], [3048, 248], [3348, 248], [3648, 248], [3948, 248], [4248, 248], [4548, 248], [4848, 248], [5148, 248], [5448, 248], [5748, 248], [6048, 248], [6348, 248], [6648, 248], [6948, 248], [7248, 248], [7548, 248], [7848, 248], [8148, 248], [8448, 248], [8748, 248], [9048, 248], [9348, 248], [9648, 248], [9948, 248], [10248, 248], [10548, 248], [10848, 248], [11148, 248], [11448, 248], [11748, 248], [12048, 248], [12348, 248], [12648, 248], [12948, 248], [13248, 248], [13548, 248], [13848, 248], [14148, 248], [14448, 248], [14748, 248], [15048, 248], [15348, 248], [15648, 248], [15948, 248], [16248, 248], [16548, 248], [16848, 248], [17148, 248], [17448, 248], [17748, 248], [18048, 248], [18348, 248], [18648, 248], [18948, 248], [19248, 248], [19548, 248], [19848, 248], [20148, 248], [20448, 248], [20748, 248], [21048, 248], [21348, 248], [21648, 248], [21948, 248], [22248, 248], [22548, 249], [22848, 249], [23148, 249], [23448, 249], [23749, 246], [24050, 243], [24352, 239], [24653, 236], [24955, 232], [25256, 229], [25558, 225], [25859, 222], [26161, 218]], "point": [172, 43]}}, "high_idx": 7}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.758, 7.758, -7.3052, -7.3052, 6.982419, 6.982419]], "forceVisible": true, "objectId": "Microwave|+01.94|+01.75|-01.83"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [48, 1, 296, 88], "mask": [[48, 248], [348, 248], [648, 248], [948, 248], [1248, 248], [1548, 248], [1848, 248], [2148, 248], [2448, 248], [2748, 248], [3048, 248], [3348, 248], [3648, 248], [3948, 248], [4248, 248], [4548, 248], [4848, 248], [5148, 248], [5448, 248], [5748, 248], [6048, 248], [6348, 248], [6648, 248], [6948, 248], [7248, 248], [7548, 248], [7848, 248], [8148, 248], [8448, 248], [8748, 248], [9048, 248], [9348, 248], [9648, 248], [9948, 248], [10248, 248], [10548, 248], [10848, 248], [11148, 248], [11448, 248], [11748, 248], [12048, 248], [12348, 248], [12648, 248], [12948, 248], [13248, 248], [13548, 248], [13848, 248], [14148, 248], [14448, 248], [14748, 248], [15048, 248], [15348, 248], [15648, 248], [15948, 248], [16248, 248], [16548, 248], [16848, 248], [17148, 248], [17448, 248], [17748, 248], [18048, 248], [18348, 248], [18648, 248], [18948, 248], [19248, 248], [19548, 248], [19848, 248], [20148, 248], [20448, 248], [20748, 248], [21048, 248], [21348, 248], [21648, 248], [21948, 248], [22248, 248], [22548, 249], [22848, 249], [23148, 249], [23448, 249], [23749, 246], [24050, 243], [24352, 239], [24653, 236], [24955, 232], [25256, 229], [25558, 225], [25859, 222], [26161, 218]], "point": [172, 43]}}, "high_idx": 7}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.94|+01.75|-01.83"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [48, 1, 296, 88], "mask": [[48, 248], [348, 248], [648, 248], [948, 248], [1248, 248], [1548, 248], [1848, 248], [2148, 248], [2448, 248], [2748, 248], [3048, 248], [3348, 248], [3648, 248], [3948, 248], [4248, 248], [4548, 248], [4848, 248], [5148, 248], [5448, 248], [5748, 248], [6048, 248], [6348, 248], [6648, 248], [6948, 248], [7248, 248], [7548, 248], [7848, 248], [8148, 248], [8448, 248], [8748, 248], [9048, 248], [9348, 248], [9648, 248], [9948, 248], [10248, 248], [10548, 248], [10848, 248], [11148, 248], [11448, 248], [11748, 248], [12048, 248], [12348, 248], [12648, 248], [12948, 248], [13248, 248], [13548, 248], [13848, 248], [14148, 248], [14448, 248], [14748, 248], [15048, 248], [15348, 248], [15648, 248], [15948, 248], [16248, 248], [16548, 248], [16848, 248], [17148, 248], [17448, 248], [17748, 248], [18048, 248], [18348, 248], [18648, 248], [18948, 248], [19248, 248], [19548, 248], [19848, 248], [20148, 248], [20448, 248], [20748, 248], [21048, 248], [21348, 248], [21648, 248], [21948, 248], [22248, 248], [22548, 249], [22848, 249], [23148, 249], [23448, 249], [23749, 246], [24050, 243], [24352, 239], [24653, 236], [24955, 232], [25256, 229], [25558, 225], [25859, 222], [26161, 218]], "point": [172, 43]}}, "high_idx": 7}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+02.60|+00.83|+03.97|BreadSliced_8"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [130, 1, 169, 54], "mask": [[133, 15], [432, 18], [731, 20], [752, 8], [1030, 31], [1330, 33], [1630, 34], [1930, 35], [2230, 36], [2530, 36], [2830, 37], [3130, 37], [3430, 38], [3730, 38], [4030, 38], [4330, 39], [4630, 39], [4930, 39], [5230, 39], [5530, 39], [5830, 40], [6130, 40], [6430, 40], [6730, 40], [7030, 40], [7330, 40], [7630, 40], [7930, 40], [8230, 40], [8530, 40], [8830, 40], [9130, 40], [9430, 40], [9730, 40], [10030, 40], [10330, 40], [10630, 40], [10930, 40], [11230, 39], [11530, 39], [11830, 38], [12130, 38], [12430, 38], [12730, 37], [13030, 37], [13330, 37], [13630, 36], [13930, 36], [14230, 35], [14530, 34], [14830, 32], [15130, 30], [15430, 21], [15730, 18], [16034, 9]], "point": [149, 26]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.94|+01.75|-01.83"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 296, 88], "mask": [[0, 296], [300, 296], [600, 296], [900, 296], [1200, 296], [1500, 296], [1800, 296], [2100, 296], [2400, 296], [2700, 296], [3000, 296], [3300, 296], [3600, 296], [3900, 296], [4200, 296], [4500, 296], [4800, 296], [5100, 296], [5400, 296], [5700, 296], [6000, 296], [6300, 296], [6600, 296], [6900, 296], [7200, 296], [7500, 296], [7800, 296], [8100, 296], [8400, 296], [8700, 296], [9000, 296], [9300, 296], [9600, 296], [9900, 296], [10201, 295], [10502, 294], [10803, 293], [11105, 291], [11406, 290], [11707, 289], [12008, 288], [12310, 286], [12611, 285], [12912, 284], [13214, 282], [13515, 281], [13816, 280], [14118, 278], [14419, 277], [14720, 276], [15021, 275], [15323, 273], [15624, 272], [15925, 271], [16227, 269], [16528, 268], [16829, 267], [17130, 266], [17432, 264], [17733, 263], [18034, 262], [18336, 260], [18637, 259], [18938, 258], [19239, 257], [19541, 255], [19842, 254], [20143, 253], [20445, 251], [20746, 250], [21047, 249], [21348, 248], [21648, 248], [21948, 248], [22248, 248], [22548, 249], [22848, 249], [23148, 249], [23448, 249], [23749, 246], [24050, 243], [24352, 239], [24653, 236], [24955, 232], [25256, 229], [25558, 225], [25859, 222], [26161, 218]], "point": [148, 43]}}, "high_idx": 7}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+02.60|+00.83|+03.97|BreadSliced_8", "placeStationary": true, "receptacleObjectId": "GarbageCan|-00.95|00.00|+02.83"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [85, 144, 190, 233], "mask": [[43000, 78], [43296, 86], [43594, 90], [43893, 92], [44192, 93], [44492, 94], [44791, 96], [45091, 96], [45390, 98], [45690, 98], [45990, 98], [46290, 98], [46590, 98], [46890, 98], [47190, 98], [47489, 99], [47789, 100], [48089, 100], [48389, 100], [48689, 100], [48989, 100], [49289, 100], [49588, 101], [49888, 101], [50188, 101], [50488, 101], [50788, 101], [51088, 102], [51388, 102], [51687, 103], [51987, 103], [52287, 103], [52587, 103], [52887, 103], [53187, 103], [53487, 103], [53786, 104], [54086, 104], [54386, 105], [54686, 105], [54986, 105], [55286, 105], [55586, 105], [55885, 106], [56185, 106], [56485, 106], [56785, 106], [57085, 106], [57385, 106], [57685, 106], [57985, 106], [58285, 106], [58585, 106], [58885, 106], [59185, 106], [59485, 106], [59785, 106], [60085, 106], [60385, 106], [60685, 106], [60985, 105], [61286, 45], [61337, 53], [61586, 43], [61639, 51], [61886, 42], [61941, 49], [62186, 41], [62242, 47], [62486, 40], [62543, 46], [62787, 38], [62844, 45], [63087, 38], [63145, 43], [63387, 38], [63445, 43], [63687, 37], [63746, 42], [63988, 36], [64046, 41], [64289, 35], [64346, 41], [64590, 34], [64646, 40], [64890, 34], [64946, 39], [65191, 33], [65246, 38], [65492, 33], [65546, 37], [65793, 32], [65863, 19], [66094, 31], [66169, 12], [66395, 30], [66473, 7], [66695, 28], [66776, 4], [66996, 24], [67297, 19], [67598, 16], [67899, 14], [68201, 10], [68503, 7], [68803, 6], [69104, 4], [69405, 2], [69705, 2]], "point": [137, 187]}}, "high_idx": 9}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan16", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -1.0, "y": 0.900999665, "z": 3.5}, "object_poses": [{"objectName": "Potato_a189bfca", "position": {"x": 1.2305876, "y": 1.06887722, "z": -1.56086969}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Ladle_8232b75a", "position": {"x": 2.79063821, "y": 1.066041, "z": -1.45686913}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_8232b75a", "position": {"x": 2.57816267, "y": 1.066041, "z": 0.907042}, "rotation": {"x": 0.0, "y": 136.999985, "z": 0.0}}, {"objectName": "ButterKnife_98253f2a", "position": {"x": 2.7057817, "y": 0.800667346, "z": -0.6997441}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_98253f2a", "position": {"x": 1.92600012, "y": 0.7645124, "z": 3.821562}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "WineBottle_ca02bbc4", "position": {"x": -0.454400539, "y": 0.0500369072, "z": 0.7862968}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_105a13ed", "position": {"x": -0.987978458, "y": 0.786932051, "z": -0.73892504}, "rotation": {"x": 0.0, "y": 89.99984, "z": 0.0}}, {"objectName": "Glassbottle_105a13ed", "position": {"x": 2.6077, "y": 1.78161144, "z": -1.91136026}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_267fd2ad", "position": {"x": 1.836, "y": 1.0276562, "z": -1.84}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_267fd2ad", "position": {"x": 2.116, "y": 1.0276562, "z": -1.84}, "rotation": {"x": 0.0, "y": 270.0001, "z": 0.0}}, {"objectName": "Spatula_87bb090b", "position": {"x": 2.875057, "y": 1.04187334, "z": -0.541442335}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_8af67485", "position": {"x": -0.6696348, "y": 1.02804852, "z": 1.94372618}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_8af67485", "position": {"x": -0.6611951, "y": 0.8028446, "z": 0.777434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_6cf51d94", "position": {"x": 1.35014987, "y": 1.07088161, "z": -1.8810637}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Egg_6cf51d94", "position": {"x": 2.06137443, "y": 0.8084784, "z": 3.58037424}, "rotation": {"x": 0.0, "y": 224.999969, "z": 0.0}}, {"objectName": "Cup_7fa88b29", "position": {"x": 2.30256248, "y": 0.76306, "z": 4.495}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1895c9f9", "position": {"x": -1.013737, "y": 1.4426043, "z": -0.7389251}, "rotation": {"x": 0.0, "y": 89.99984, "z": 0.0}}, {"objectName": "Tomato_6be5e93d", "position": {"x": -0.9161765, "y": 0.130966753, "z": 2.84268427}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_6be5e93d", "position": {"x": -0.5775822, "y": 0.870248, "z": 1.4148221}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_a47378fd", "position": {"x": -1.04572344, "y": 1.0261718, "z": 1.94372618}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_b3ba1f80", "position": {"x": 2.70621943, "y": 1.02530766, "z": -0.541442335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_26395c85", "position": {"x": 1.29764175, "y": 0.787037134, "z": -1.5463053}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_7275394e", "position": {"x": 2.79078341, "y": 0.832571, "z": 3.52991724}, "rotation": {"x": 0.0, "y": 329.9999, "z": 0.0}}, {"objectName": "Bread_7275394e", "position": {"x": 2.59943748, "y": 0.832571, "z": 3.97}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_2bc5be8e", "position": {"x": 2.62599969, "y": 0.822627544, "z": 4.19265652}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "Apple_dc079739", "position": {"x": -0.6459911, "y": 0.86690855, "z": 1.1816839}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_dc079739", "position": {"x": -0.851217866, "y": 0.86690855, "z": 1.33710945}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_c7f7c9bb", "position": {"x": 2.04230833, "y": 1.017125, "z": 1.77973437}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_105a13ed", "position": {"x": 2.70621943, "y": 1.02759016, "z": 0.06884211}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_2539b087", "position": {"x": -0.6696348, "y": 1.05284965, "z": 2.189}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_a47378fd", "position": {"x": 2.67365623, "y": 0.7631421, "z": 4.32}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_6cf51d94", "position": {"x": -0.851217866, "y": 0.8605643, "z": 1.10397124}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_8af67485", "position": {"x": 2.475812, "y": 1.027422, "z": 1.648444}, "rotation": {"x": 0.0, "y": 136.999985, "z": 0.0}}, {"objectName": "Potato_a189bfca", "position": {"x": -0.9474034, "y": 0.8282191, "z": -0.184049949}, "rotation": {"x": 0.0, "y": 89.99984, "z": 0.0}}, {"objectName": "Ladle_8232b75a", "position": {"x": 2.451, "y": 0.803637862, "z": 3.97}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_6be5e93d", "position": {"x": 1.35014892, "y": 1.08056533, "z": -1.56086946}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_267fd2ad", "position": {"x": 1.92599988, "y": 0.76753366, "z": 3.96999955}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "WineBottle_ca02bbc4", "position": {"x": 2.79063821, "y": 1.02780211, "z": -1.151727}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1895c9f9", "position": {"x": 2.37079453, "y": 1.02736247, "z": 1.55051351}, "rotation": {"x": 0.0, "y": 136.999985, "z": 0.0}}, {"objectName": "Spatula_87bb090b", "position": {"x": 2.53742075, "y": 0.814982653, "z": -0.792470157}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_758d0bf4", "position": {"x": 2.95947552, "y": 1.13115883, "z": -1.45686913}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Lettuce_2bc5be8e", "position": {"x": 2.59795761, "y": 0.822627544, "z": 3.41858864}, "rotation": {"x": 0.0, "y": 329.9999, "z": 0.0}}, {"objectName": "Apple_dc079739", "position": {"x": 2.52521873, "y": 0.814822733, "z": 4.495}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_c2d3cb7a", "position": {"x": 2.87414265, "y": 0.796931446, "z": -0.607018054}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_26395c85", "position": {"x": 2.80099964, "y": 0.7607765, "z": 4.19265652}, "rotation": {"x": 0.0, "y": 269.999939, "z": 0.0}}, {"objectName": "ButterKnife_98253f2a", "position": {"x": -0.714399934, "y": 0.816598237, "z": 1.49253488}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_7275394e", "position": {"x": -0.968222141, "y": 1.510216, "z": -0.295025021}, "rotation": {"x": 0.0, "y": 89.99984, "z": 0.0}}, {"objectName": "Cup_7fa88b29", "position": {"x": -0.4189102, "y": 1.02608967, "z": 0.600607157}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_00fca5a2", "position": {"x": -0.8035485, "y": 0.0457288623, "z": 1.95434976}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_b3ba1f80", "position": {"x": 2.32112479, "y": 0.7629045, "z": 3.84012485}, "rotation": {"x": 0.0, "y": 224.999969, "z": 0.0}}, {"objectName": "Spoon_5dd10cb9", "position": {"x": -1.17108631, "y": 1.0286293, "z": 2.55691051}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_a04e993b", "position": {"x": -0.8038, "y": 1.07065928, "z": 0.646000445}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 124562843, "scene_num": 16}, "task_id": "trial_T20190907_232412_371318", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3HL2LL0LEPZT8_35L9RVQFCRZGAPAOKKRSEQKDGGIHU2", "high_descs": ["Turn right, go forward a step, turn right, go forward, turn right at the sink.", "Take the knife from the sink.", "Turn right, go past the end of the counter, turn right, go towards the wall, turn left at the round white table. Go to the table.", "Cut the bread to the right of the large spoon into slices.", "Put the knife on the table to the left of the lettuce.", "Take a slice of bread from the table.", "Turn left, go forward, after the end of the counter on the left turn left, go forward and to the left to the oven.", "Heat the bread in the microwave above the oven. Take the bread from the microwave.", "Turn right, go forward, at the end of the counter on the left turn right, go forward, turn left after passing the end of the counter on the left, go forward to the wall, turn left to face the bin.", "Put the bread in the bin."], "task_desc": "Put a hot slice of bread in a bin.", "votes": [1, 1, 1]}, {"assignment_id": "A3SOZYQZWJOOYS_3QBD8R3Z240RL82W9RV0LVJKTP14OJ", "high_descs": ["Turn around and head towards the sink, then turn and face the sink.", "Pick up knife out of sink.", "Turn around and head towards the wall, then turn to left and face the table.", "Cut the top loaf of bread into slices.", "Put knife down on table.", "Pick up a slice of bread.", "Turn to your left and head towards the counters, then turn left and head towards the stove and face the microwave.", "Open the microwave, put the slice of bread in the microwave, close door, cook the bread for a bit, then take out the bread and close the door.", "Turn around and head towards the wall, then turn left and head towards the wall, then turn left again and face the trash can.", "Place the slice of bread in the trash can."], "task_desc": "Place a cooked slice of bread in the trash.", "votes": [1, 1, 1]}, {"assignment_id": "A2871R3LEPWMMK_3LKC68YZ3DK3UALGJX1JGN2BDIZOWO", "high_descs": ["Turn right, hang a right at the edge of the counter and face the sink on the right.", "Pick up the spoon in the sink.", "Turn right, hang a right at the edge of the counter and turn left at the wall to face the white table.", "Slice the loaf of bread to the right of the spoon on the table.", "Place the knife on the head of lettuce to the left of the loaf of bread.", "Pick up a slice of bread from the loaf to the right of the spoon.", "Turn left, hang a left around the angle of the counter, walk to the counter and walk to the right to reach the stove.", "Put the bread inside the microwave above the stove in the middle, heat it for a few seconds, remove it and close the door.", "Turn right, head right at the end of the counter, turn left at the white table and turn left to face the gray container on the floor.", "Put the bread inside the gray container behind the tomato."], "task_desc": "Place a heated slice of bread in a container.", "votes": [1, 1, 0]}]}}