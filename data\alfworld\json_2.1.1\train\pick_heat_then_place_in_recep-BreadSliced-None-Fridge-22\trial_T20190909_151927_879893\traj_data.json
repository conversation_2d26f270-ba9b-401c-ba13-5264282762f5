{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000025.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000026.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000027.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000028.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000029.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000030.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000031.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000038.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000039.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000040.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000041.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000042.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000045.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000046.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000095.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000098.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000099.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000100.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000101.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000102.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000103.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000104.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000106.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000108.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000125.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000128.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000129.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000130.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000131.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000132.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000133.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000134.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000135.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000136.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000137.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000138.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000139.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000140.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000141.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000142.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000143.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000144.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000145.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000146.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000147.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000148.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000149.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000150.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000151.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000152.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000153.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000154.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000155.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000156.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000157.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000158.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000159.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000160.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000170.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000171.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000172.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000173.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000174.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000175.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000176.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000177.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000178.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000179.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000180.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000181.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000182.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000183.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000184.png", "low_idx": 32}, {"high_idx": 8, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000187.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000188.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000189.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000190.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000191.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000192.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000193.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000194.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000195.png", "low_idx": 33}, {"high_idx": 8, "image_name": "000000196.png", "low_idx": 34}, {"high_idx": 8, "image_name": "000000197.png", "low_idx": 34}, {"high_idx": 8, "image_name": "000000198.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000199.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000202.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000203.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000204.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000205.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000206.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000207.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000209.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000210.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000211.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000212.png", "low_idx": 37}, {"high_idx": 9, "image_name": "000000213.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000214.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000215.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000216.png", "low_idx": 38}, {"high_idx": 9, "image_name": "000000217.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000218.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000219.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000220.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000221.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000222.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000223.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000224.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000225.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000226.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000227.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000228.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000229.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000230.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000231.png", "low_idx": 39}, {"high_idx": 9, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 9, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 9, "image_name": "000000234.png", "low_idx": 40}, {"high_idx": 9, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 9, "image_name": "000000236.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000237.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000238.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000239.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000240.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000241.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000242.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000243.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000244.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000245.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000246.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000247.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000248.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000249.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000250.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000251.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000252.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000253.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000254.png", "low_idx": 42}, {"high_idx": 9, "image_name": "000000255.png", "low_idx": 42}, {"high_idx": 9, "image_name": "000000256.png", "low_idx": 42}, {"high_idx": 9, "image_name": "000000257.png", "low_idx": 42}, {"high_idx": 9, "image_name": "000000258.png", "low_idx": 42}, {"high_idx": 9, "image_name": "000000259.png", "low_idx": 42}, {"high_idx": 9, "image_name": "000000260.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000261.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000262.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000263.png", "low_idx": 43}, {"high_idx": 9, "image_name": "000000264.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000265.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000266.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000267.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000268.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000269.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000270.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000271.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000272.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000273.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000274.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000275.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000276.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000277.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000278.png", "low_idx": 44}, {"high_idx": 9, "image_name": "000000279.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000280.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000281.png", "low_idx": 45}, {"high_idx": 9, "image_name": "000000282.png", "low_idx": 45}, {"high_idx": 10, "image_name": "000000283.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000284.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000285.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000286.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000287.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000288.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000289.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000290.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000291.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000292.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000293.png", "low_idx": 46}, {"high_idx": 10, "image_name": "000000294.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000295.png", "low_idx": 47}, {"high_idx": 10, "image_name": "000000296.png", "low_idx": 48}, {"high_idx": 10, "image_name": "000000297.png", "low_idx": 48}, {"high_idx": 10, "image_name": "000000298.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000299.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000300.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000301.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000302.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000303.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000304.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000305.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000306.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000307.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000308.png", "low_idx": 49}, {"high_idx": 10, "image_name": "000000309.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000310.png", "low_idx": 50}, {"high_idx": 10, "image_name": "000000311.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000312.png", "low_idx": 51}, {"high_idx": 10, "image_name": "000000313.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000314.png", "low_idx": 52}, {"high_idx": 10, "image_name": "000000315.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000316.png", "low_idx": 53}, {"high_idx": 10, "image_name": "000000317.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000318.png", "low_idx": 54}, {"high_idx": 10, "image_name": "000000319.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000320.png", "low_idx": 55}, {"high_idx": 10, "image_name": "000000321.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000322.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000323.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000324.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000325.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000326.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000327.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000328.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000329.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000330.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000331.png", "low_idx": 56}, {"high_idx": 10, "image_name": "000000332.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000333.png", "low_idx": 57}, {"high_idx": 10, "image_name": "000000334.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000335.png", "low_idx": 58}, {"high_idx": 10, "image_name": "000000336.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000337.png", "low_idx": 59}, {"high_idx": 10, "image_name": "000000338.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000339.png", "low_idx": 60}, {"high_idx": 10, "image_name": "000000340.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000341.png", "low_idx": 61}, {"high_idx": 10, "image_name": "000000342.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000343.png", "low_idx": 62}, {"high_idx": 10, "image_name": "000000344.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000345.png", "low_idx": 63}, {"high_idx": 10, "image_name": "000000346.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000347.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000348.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000349.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000350.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000351.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000352.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000353.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000354.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000355.png", "low_idx": 64}, {"high_idx": 10, "image_name": "000000356.png", "low_idx": 64}, {"high_idx": 11, "image_name": "000000357.png", "low_idx": 65}, {"high_idx": 11, "image_name": "000000358.png", "low_idx": 65}, {"high_idx": 11, "image_name": "000000359.png", "low_idx": 65}, {"high_idx": 11, "image_name": "000000360.png", "low_idx": 65}, {"high_idx": 11, "image_name": "000000361.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000362.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000363.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000364.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000365.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000366.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000367.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000368.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000369.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000370.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000371.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000372.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000373.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000374.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000375.png", "low_idx": 66}, {"high_idx": 11, "image_name": "000000376.png", "low_idx": 67}, {"high_idx": 11, "image_name": "000000377.png", "low_idx": 67}, {"high_idx": 11, "image_name": "000000378.png", "low_idx": 67}, {"high_idx": 11, "image_name": "000000379.png", "low_idx": 67}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-8|-1|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [-11.08222, -11.08222, -0.589657784, -0.589657784, 3.6453684, 3.6453684]], "coordinateReceptacleObjectId": ["CounterTop", [-10.7976, -10.7976, 0.4244, 0.4244, 3.7872, 3.7872]], "forceVisible": true, "objectId": "ButterKnife|-02.77|+00.91|-00.15"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-2|2|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-1.606400488, -1.606400488, -5.16599752, -5.16599752, 3.7932192, 3.7932192]], "forceVisible": true, "objectId": "Bread|-00.40|+00.95|-01.29"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-6|-2|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "sinkbasin"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [-11.08222, -11.08222, -0.589657784, -0.589657784, 3.6453684, 3.6453684]], "coordinateReceptacleObjectId": ["SinkBasin", [-5.3792, -5.3792, -5.0, -5.0, 3.356, 3.356]], "forceVisible": true, "objectId": "ButterKnife|-02.77|+00.91|-00.15", "receptacleObjectId": "Sink|-01.33|+00.92|-01.23|SinkBasin"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-2|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-1.606400488, -1.606400488, -5.16599752, -5.16599752, 3.7932192, 3.7932192]], "coordinateReceptacleObjectId": ["CounterTop", [0.284, 0.284, -4.796, -4.796, 3.7872, 3.7872]], "forceVisible": true, "objectId": "Bread|-00.40|+00.95|-01.29|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|1|-2|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-8|4|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "fridge"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-1.606400488, -1.606400488, -5.16599752, -5.16599752, 3.7932192, 3.7932192]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-11.424, -11.424, 3.408, 3.408, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-00.40|+00.95|-01.29|BreadSliced_1", "receptacleObjectId": "<PERSON><PERSON>|-02.86|+00.00|+00.85"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|-02.77|+00.91|-00.15"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [172, 111, 179, 156], "mask": [[33172, 4], [33472, 5], [33772, 5], [34072, 5], [34372, 5], [34672, 5], [34972, 5], [35272, 5], [35573, 4], [35873, 4], [36173, 4], [36473, 4], [36774, 3], [37074, 3], [37374, 3], [37674, 3], [37974, 3], [38274, 3], [38574, 3], [38874, 3], [39174, 4], [39474, 4], [39774, 4], [40074, 3], [40373, 4], [40673, 4], [40973, 4], [41273, 3], [41573, 3], [41873, 3], [42173, 3], [42473, 3], [42773, 2], [43073, 2], [43373, 2], [43673, 3], [43973, 4], [44274, 4], [44574, 5], [44874, 6], [45175, 5], [45475, 5], [45776, 4], [46076, 4], [46377, 3], [46677, 3]], "point": [175, 132]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-00.40|+00.95|-01.29"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [92, 100, 157, 150], "mask": [[29817, 16], [30113, 25], [30411, 30], [30709, 34], [31007, 38], [31306, 40], [31604, 44], [31903, 46], [32202, 48], [32500, 51], [32799, 52], [33099, 53], [33398, 55], [33697, 57], [33996, 58], [34296, 59], [34595, 60], [34895, 61], [35194, 62], [35494, 62], [35793, 64], [36093, 64], [36393, 64], [36692, 65], [36992, 66], [37292, 66], [37592, 66], [37892, 66], [38192, 66], [38492, 66], [38792, 66], [39092, 65], [39393, 64], [39693, 64], [39993, 63], [40294, 62], [40594, 61], [40895, 60], [41195, 59], [41496, 57], [41796, 57], [42097, 55], [42398, 53], [42699, 51], [43000, 48], [43301, 45], [43603, 41], [43906, 36], [44209, 30], [44512, 23], [44821, 5]], "point": [124, 124]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|-02.77|+00.91|-00.15", "placeStationary": true, "receptacleObjectId": "Sink|-01.33|+00.92|-01.23|SinkBasin"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [7, 120, 197, 186], "mask": [[35752, 64], [35840, 31], [36046, 70], [36140, 37], [36343, 73], [36440, 39], [36640, 76], [36739, 43], [36938, 78], [37039, 45], [37237, 80], [37339, 46], [37535, 82], [37638, 48], [37834, 84], [37938, 49], [38132, 86], [38238, 51], [38431, 88], [38537, 52], [38730, 90], [38837, 53], [39029, 10], [39055, 65], [39136, 54], [39329, 9], [39355, 66], [39435, 56], [39628, 9], [39656, 66], [39734, 57], [39927, 10], [39956, 69], [40032, 59], [40227, 9], [40257, 71], [40330, 62], [40526, 10], [40556, 72], [40630, 62], [40826, 9], [40856, 72], [40930, 62], [41125, 10], [41156, 72], [41230, 62], [41425, 10], [41455, 71], [41535, 57], [41724, 10], [41755, 68], [41837, 55], [42024, 10], [42054, 68], [42139, 54], [42323, 10], [42354, 67], [42439, 54], [42623, 10], [42654, 66], [42740, 53], [42922, 10], [42953, 66], [43041, 52], [43222, 10], [43253, 66], [43341, 52], [43521, 10], [43553, 65], [43641, 52], [43821, 10], [43852, 67], [43941, 53], [44120, 10], [44152, 67], [44240, 54], [44420, 10], [44451, 68], [44540, 54], [44719, 10], [44751, 69], [44839, 55], [45018, 11], [45051, 70], [45138, 56], [45318, 11], [45350, 69], [45420, 2], [45437, 57], [45617, 11], [45650, 74], [45735, 60], [45917, 11], [45949, 79], [46030, 65], [46216, 12], [46249, 146], [46516, 13], [46549, 146], [46815, 14], [46848, 147], [47115, 15], [47148, 148], [47414, 182], [47714, 182], [48013, 183], [48313, 183], [48612, 184], [48912, 185], [49211, 186], [49511, 186], [49810, 187], [50110, 187], [50409, 188], [50709, 189], [51009, 189], [51308, 189], [51608, 189], [51908, 189], [52208, 189], [52507, 190], [52807, 190], [53107, 190], [53407, 189], [53707, 189], [54008, 187], [54308, 186], [54609, 185], [54909, 184], [55209, 183], [55510, 181]], "point": [102, 152]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.40|+00.95|-01.29|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [92, 110, 157, 129], "mask": [[32814, 19], [33110, 29], [33404, 39], [33702, 43], [34000, 47], [34298, 51], [34597, 19], [34627, 23], [34896, 15], [34934, 18], [35195, 9], [35242, 11], [35494, 8], [35544, 10], [35794, 6], [35846, 9], [36093, 5], [36148, 8], [36393, 4], [36450, 6], [36692, 4], [36751, 6], [36992, 3], [37052, 5], [37292, 2], [37353, 5], [37592, 2], [37654, 4], [37892, 1], [37955, 3], [38192, 1], [38256, 2], [38556, 2]], "point": [124, 114]}}, "high_idx": 7}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.40|+00.95|-01.29|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 130], [19965, 82], [20108, 131], [20265, 82], [20407, 132], [20566, 81], [20707, 132], [20865, 81], [21006, 133], [21165, 81], [21305, 135], [21465, 81], [21604, 136], [21765, 80], [21903, 137], [22065, 80], [22203, 137], [22365, 80], [22502, 138], [22664, 81], [22801, 140], [22964, 80], [23100, 141], [23264, 80], [23400, 141], [23563, 81], [23700, 142], [23863, 80], [24000, 142], [24163, 80], [24300, 143], [24462, 81], [24600, 143], [24762, 81], [24900, 144], [25061, 81], [25200, 145], [25360, 82], [25500, 146], [25659, 83], [25800, 147], [25957, 84], [26100, 149], [26256, 85], [26400, 151], [26553, 88], [26700, 151], [26853, 88], [27000, 151], [27153, 87], [27300, 151], [27453, 87], [27600, 151], [27753, 87], [27900, 151], [28053, 87], [28200, 151], [28353, 86], [28500, 151], [28653, 86], [28800, 151], [28953, 86], [29100, 146], [29258, 80], [29400, 144], [29561, 77], [29700, 142], [29862, 76], [30000, 141], [30163, 75], [30300, 141], [30464, 73], [30600, 140], [30764, 73], [30900, 140], [31064, 73], [31200, 140], [31364, 72], [31500, 141], [31664, 72], [31800, 141], [31963, 73], [32100, 142], [32262, 74], [32400, 143], [32561, 74], [32700, 146], [32859, 76], [33000, 150], [33154, 81], [33300, 234], [33600, 234], [33900, 234], [34200, 234], [34500, 233], [34800, 233], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 130], [19965, 82], [20108, 131], [20265, 82], [20407, 132], [20566, 81], [20707, 132], [20865, 81], [21006, 133], [21165, 81], [21305, 135], [21465, 81], [21604, 136], [21765, 80], [21903, 137], [22065, 80], [22203, 137], [22365, 80], [22502, 138], [22664, 81], [22801, 140], [22964, 80], [23100, 141], [23264, 80], [23400, 141], [23563, 81], [23700, 142], [23863, 80], [24000, 142], [24163, 80], [24300, 143], [24462, 81], [24600, 143], [24762, 81], [24900, 144], [25061, 81], [25200, 145], [25360, 82], [25500, 146], [25659, 83], [25800, 147], [25957, 84], [26100, 149], [26256, 85], [26400, 151], [26553, 88], [26700, 130], [26844, 7], [26853, 88], [27000, 125], [27149, 2], [27153, 87], [27300, 120], [27455, 85], [27600, 115], [27758, 82], [27900, 113], [28060, 80], [28200, 111], [28362, 77], [28500, 110], [28664, 75], [28800, 108], [28965, 74], [29100, 107], [29267, 71], [29400, 106], [29568, 70], [29700, 105], [29869, 69], [30000, 104], [30170, 68], [30300, 104], [30470, 67], [30600, 103], [30771, 66], [30900, 102], [31072, 65], [31200, 102], [31372, 64], [31500, 102], [31673, 63], [31800, 102], [31973, 63], [32100, 102], [32274, 62], [32400, 102], [32574, 61], [32700, 102], [32874, 61], [33000, 102], [33174, 61], [33300, 102], [33474, 60], [33600, 102], [33774, 60], [33900, 102], [34073, 61], [34200, 103], [34373, 61], [34500, 103], [34673, 60], [34800, 104], [34972, 61], [35100, 106], [35270, 63], [35400, 111], [35565, 67], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.40|+00.95|-01.29|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [102, 90, 173, 119], "mask": [[26830, 14], [27125, 24], [27420, 35], [27715, 43], [28013, 47], [28311, 51], [28610, 54], [28908, 57], [29207, 60], [29506, 62], [29805, 64], [30104, 66], [30404, 66], [30703, 68], [31002, 70], [31302, 70], [31602, 71], [31902, 71], [32202, 72], [32502, 72], [32802, 72], [33102, 72], [33402, 72], [33702, 72], [34002, 71], [34303, 70], [34603, 70], [34904, 68], [35206, 64], [35511, 54]], "point": [137, 103]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 130], [19965, 82], [20108, 131], [20265, 82], [20407, 132], [20566, 81], [20707, 132], [20865, 81], [21006, 133], [21165, 81], [21305, 135], [21465, 81], [21604, 136], [21765, 80], [21903, 137], [22065, 80], [22203, 137], [22365, 80], [22502, 138], [22664, 81], [22801, 140], [22964, 80], [23100, 141], [23264, 80], [23400, 141], [23563, 81], [23700, 142], [23863, 80], [24000, 142], [24163, 80], [24300, 143], [24462, 81], [24600, 143], [24762, 81], [24900, 144], [25061, 81], [25200, 145], [25360, 82], [25500, 146], [25659, 83], [25800, 147], [25957, 84], [26100, 149], [26256, 85], [26400, 151], [26553, 88], [26700, 151], [26853, 88], [27000, 151], [27153, 87], [27300, 151], [27453, 87], [27600, 151], [27753, 87], [27900, 151], [28053, 87], [28200, 151], [28353, 86], [28500, 151], [28653, 86], [28800, 151], [28953, 86], [29100, 146], [29258, 80], [29400, 144], [29561, 77], [29700, 142], [29862, 76], [30000, 141], [30163, 75], [30300, 141], [30464, 73], [30600, 140], [30764, 73], [30900, 140], [31064, 73], [31200, 140], [31364, 72], [31500, 141], [31664, 72], [31800, 141], [31963, 73], [32100, 142], [32262, 74], [32400, 143], [32561, 74], [32700, 146], [32859, 76], [33000, 150], [33154, 81], [33300, 234], [33600, 234], [33900, 234], [34200, 234], [34500, 233], [34800, 233], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 9}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 10}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.86|+00.00|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 218], "mask": [[0, 2099], [2100, 299], [2400, 298], [2700, 296], [3000, 297], [3300, 297], [3600, 296], [3900, 296], [4200, 295], [4500, 295], [4800, 294], [5100, 294], [5400, 293], [5700, 293], [6000, 292], [6300, 292], [6600, 291], [6900, 291], [7200, 290], [7500, 290], [7800, 289], [8100, 288], [8400, 288], [8700, 287], [9000, 287], [9300, 286], [9600, 286], [9900, 285], [10200, 285], [10500, 284], [10800, 284], [11100, 283], [11400, 283], [11700, 282], [12000, 282], [12300, 281], [12600, 281], [12900, 280], [13200, 280], [13500, 279], [13800, 279], [14100, 278], [14400, 278], [14700, 277], [15000, 277], [15300, 276], [15600, 276], [15900, 275], [16200, 275], [16500, 274], [16800, 274], [17100, 273], [17400, 273], [17700, 272], [18000, 272], [18300, 271], [18600, 271], [18900, 270], [19200, 270], [19500, 269], [19800, 269], [20100, 268], [20400, 268], [20700, 267], [21000, 267], [21300, 266], [21600, 266], [21900, 265], [22200, 265], [22500, 264], [22800, 264], [23100, 263], [23400, 263], [23700, 262], [24000, 262], [24300, 261], [24600, 261], [24900, 260], [25200, 260], [25500, 259], [25800, 259], [26100, 258], [26400, 257], [26700, 257], [27000, 256], [27300, 256], [27600, 255], [27900, 255], [28200, 254], [28500, 254], [28800, 253], [29100, 253], [29400, 252], [29700, 252], [30000, 251], [30300, 251], [30600, 250], [30900, 250], [31200, 249], [31500, 249], [31800, 248], [32100, 248], [32400, 247], [32700, 247], [33000, 246], [33300, 246], [33600, 245], [33900, 245], [34200, 244], [34500, 244], [34800, 243], [35100, 243], [35400, 242], [35700, 242], [36000, 241], [36300, 241], [36600, 240], [36900, 240], [37200, 239], [37500, 239], [37800, 238], [38100, 238], [38400, 237], [38700, 237], [39000, 236], [39300, 236], [39600, 235], [39900, 235], [40200, 234], [40500, 234], [40800, 233], [41100, 233], [41400, 232], [41700, 232], [42000, 231], [42300, 231], [42600, 230], [42900, 230], [43200, 229], [43500, 229], [43800, 228], [44100, 228], [44400, 227], [44700, 226], [45000, 226], [45300, 225], [45600, 225], [45900, 224], [46200, 224], [46500, 223], [46800, 223], [47100, 222], [47400, 222], [47700, 221], [48000, 221], [48300, 220], [48600, 220], [48900, 219], [49200, 219], [49500, 218], [49800, 218], [50100, 217], [50401, 216], [50702, 214], [51003, 213], [51305, 210], [51606, 209], [51907, 207], [52208, 206], [52509, 204], [52810, 203], [53111, 201], [53413, 199], [53714, 197], [54015, 196], [54316, 194], [54617, 193], [54918, 191], [55219, 190], [55520, 188], [55822, 186], [56123, 184], [56424, 183], [56725, 181], [57026, 180], [57327, 178], [57628, 177], [57929, 175], [58231, 173], [58532, 171], [58833, 170], [59136, 166], [59437, 165], [59738, 163], [60039, 162], [60340, 160], [60641, 159], [60942, 157], [61244, 155], [61545, 153], [61846, 152], [62147, 150], [62448, 149], [62749, 147], [63050, 145], [63351, 144], [63652, 142], [63954, 140], [64255, 138], [64556, 137], [64857, 135], [65158, 134]], "point": [149, 108]}}, "high_idx": 11}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.40|+00.95|-01.29|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-02.86|+00.00|+00.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 12650], [12784, 3], [12788, 162], [13084, 3], [13088, 5944], [19048, 283], [19350, 279], [19652, 276], [19954, 272], [20256, 269], [20558, 266], [20859, 265], [21159, 264], [21460, 262], [21761, 261], [22061, 260], [22362, 259], [22663, 258], [22963, 257], [23264, 256], [23564, 256], [23865, 254], [24166, 253], [24466, 253], [24767, 252], [25067, 252], [25367, 252], [25668, 251], [25968, 251], [26269, 250], [26569, 250], [26869, 251], [27169, 251], [27470, 251], [27770, 251], [28070, 251], [28370, 252], [28670, 252], [28970, 253], [29270, 253], [29569, 255], [29868, 257], [30168, 257], [30467, 260], [30765, 263], [31063, 266], [31361, 269], [31659, 273], [31957, 278], [32255, 282], [32553, 287], [32851, 293], [33147, 3453], [36601, 299], [36902, 298], [37202, 298], [37503, 297], [37804, 296], [38105, 295], [38406, 294], [38707, 107], [38833, 167], [39007, 105], [39135, 165], [39308, 102], [39437, 163], [39609, 100], [39738, 162], [39910, 98], [40038, 162], [40211, 97], [40339, 161], [40512, 95], [40639, 161], [40813, 94], [40939, 161], [41113, 94], [41239, 161], [41414, 93], [41539, 161], [41715, 92], [41839, 161], [42016, 90], [42139, 161], [42317, 89], [42439, 161], [42618, 88], [42739, 161], [42918, 88], [43039, 161], [43219, 87], [43339, 161], [43520, 86], [43639, 161], [43821, 85], [43939, 161], [44122, 84], [44239, 161], [44423, 83], [44539, 161], [44723, 83], [44839, 161], [45024, 82], [45139, 161], [45325, 81], [45439, 161], [45626, 81], [45738, 162], [45927, 80], [46037, 163], [46228, 80], [46337, 163], [46529, 80], [46635, 165], [46829, 82], [46933, 167], [47130, 270], [47431, 269], [47732, 268], [48033, 267], [48334, 266], [48634, 266], [48935, 265], [49236, 264], [49537, 263], [49838, 262], [50139, 261], [50439, 261], [50740, 260], [51041, 259], [51342, 258], [51643, 257], [51944, 256], [52244, 256], [52545, 255], [52846, 254], [53147, 253], [53448, 252], [53749, 251], [54050, 250], [54350, 250], [54651, 249], [54952, 248], [55253, 247], [55554, 246], [55855, 245], [56155, 245], [56456, 244], [56757, 243], [57058, 242], [57359, 241], [57660, 240], [57960, 240], [58262, 238], [58566, 120], [58690, 110], [58990, 110], [59290, 110], [59590, 110], [59890, 110], [60190, 110], [60490, 110], [60790, 110], [61091, 109], [61391, 109], [61691, 109], [61991, 109], [62291, 109], [62591, 109], [62891, 109], [63191, 109], [63491, 109], [63791, 109], [64091, 109], [64391, 109], [64692, 108], [64992, 108], [65292, 108], [65592, 108], [65892, 108], [66192, 108], [66492, 108], [66792, 108], [67092, 108], [67392, 108], [67692, 108], [67992, 108], [68292, 108], [68593, 107], [68893, 107], [69193, 107], [69493, 107], [69793, 107], [70093, 107], [70393, 107], [70693, 107], [70999, 101], [71303, 97], [71607, 93], [71911, 89], [72214, 86], [72516, 84], [72817, 83], [73119, 81], [73421, 79], [73722, 78], [74024, 76], [74325, 75], [74625, 75], [74926, 74], [75227, 73], [75528, 72], [75828, 72], [76129, 71], [76429, 71], [76730, 70], [77030, 70], [77331, 69], [77631, 69], [77932, 68], [78232, 68], [78532, 68], [78833, 67], [79133, 67], [79433, 67], [79734, 66], [80034, 66], [80335, 65], [80635, 65], [80935, 65], [81236, 64], [81536, 64], [81836, 64], [82137, 63], [82437, 63], [82737, 63], [83037, 63], [83337, 63], [83637, 63], [83936, 64], [84234, 66], [84533, 67], [84830, 70], [85124, 76], [85397, 103], [85697, 103], [85997, 103], [86297, 103], [86597, 103], [86897, 103], [87198, 102], [87498, 102], [87798, 102], [88098, 102], [88398, 102], [88698, 102], [88998, 102], [89298, 102], [89598, 102], [89898, 102]], "point": [149, 149]}}, "high_idx": 11}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.86|+00.00|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 12650], [12784, 3], [12788, 162], [13084, 3], [13088, 5944], [19048, 283], [19350, 279], [19652, 276], [19954, 272], [20256, 269], [20558, 266], [20859, 265], [21159, 264], [21460, 262], [21761, 261], [22061, 260], [22362, 259], [22663, 258], [22963, 257], [23264, 256], [23564, 256], [23865, 254], [24166, 253], [24466, 253], [24767, 252], [25067, 252], [25367, 252], [25668, 180], [25872, 47], [25968, 175], [26179, 40], [26269, 170], [26483, 36], [26569, 168], [26786, 33], [26869, 166], [27088, 32], [27169, 165], [27390, 30], [27470, 163], [27692, 29], [27770, 162], [27994, 27], [28070, 161], [28295, 26], [28370, 161], [28596, 26], [28670, 161], [28898, 24], [28970, 160], [29199, 24], [29270, 160], [29500, 23], [29569, 161], [29800, 24], [29868, 162], [30101, 24], [30168, 162], [30401, 24], [30467, 163], [30701, 26], [30765, 165], [31001, 27], [31063, 167], [31301, 28], [31361, 169], [31600, 30], [31659, 171], [31900, 32], [31957, 174], [32200, 35], [32255, 176], [32499, 38], [32553, 180], [32799, 41], [32851, 184], [33098, 46], [33147, 3453], [36601, 299], [36902, 298], [37202, 298], [37503, 297], [37804, 296], [38105, 295], [38406, 294], [38707, 107], [38833, 167], [39007, 105], [39135, 165], [39308, 102], [39437, 163], [39609, 100], [39738, 162], [39910, 98], [40038, 162], [40211, 97], [40339, 161], [40512, 95], [40639, 161], [40813, 94], [40939, 161], [41113, 94], [41239, 161], [41414, 93], [41539, 161], [41715, 92], [41839, 161], [42016, 90], [42139, 161], [42317, 89], [42439, 161], [42618, 88], [42739, 161], [42918, 88], [43039, 161], [43219, 87], [43339, 161], [43520, 86], [43639, 161], [43821, 85], [43939, 161], [44122, 84], [44239, 161], [44423, 83], [44539, 161], [44723, 83], [44839, 161], [45024, 82], [45139, 161], [45325, 81], [45439, 161], [45626, 81], [45738, 162], [45927, 80], [46037, 163], [46228, 80], [46337, 163], [46529, 80], [46635, 165], [46829, 82], [46933, 167], [47130, 270], [47431, 269], [47732, 268], [48033, 267], [48334, 266], [48634, 266], [48935, 265], [49236, 264], [49537, 263], [49838, 262], [50139, 261], [50439, 261], [50740, 260], [51041, 259], [51342, 258], [51643, 257], [51944, 256], [52244, 256], [52545, 255], [52846, 254], [53147, 253], [53448, 252], [53749, 251], [54050, 250], [54350, 250], [54651, 249], [54952, 248], [55253, 247], [55554, 246], [55855, 245], [56155, 245], [56456, 244], [56757, 243], [57058, 242], [57359, 241], [57660, 240], [57960, 240], [58262, 238], [58566, 120], [58690, 110], [58990, 110], [59290, 110], [59590, 110], [59890, 110], [60190, 110], [60490, 110], [60790, 110], [61091, 109], [61391, 109], [61691, 109], [61991, 109], [62291, 109], [62591, 109], [62891, 109], [63191, 109], [63491, 109], [63791, 109], [64091, 109], [64391, 109], [64692, 108], [64992, 108], [65292, 108], [65592, 108], [65892, 108], [66192, 108], [66492, 108], [66792, 108], [67092, 108], [67392, 108], [67692, 108], [67992, 108], [68292, 108], [68593, 107], [68893, 107], [69193, 107], [69493, 107], [69793, 107], [70093, 107], [70393, 107], [70693, 107], [70993, 107], [71293, 107], [71593, 107], [71893, 107], [72194, 106], [72494, 106], [72794, 106], [73094, 106], [73394, 106], [73694, 106], [73994, 106], [74294, 106], [74594, 106], [74894, 106], [75194, 106], [75494, 106], [75794, 106], [76095, 105], [76395, 105], [76695, 105], [76995, 105], [77295, 105], [77595, 105], [77895, 105], [78195, 105], [78495, 105], [78795, 105], [79095, 105], [79395, 105], [79696, 104], [79996, 104], [80296, 104], [80596, 104], [80896, 104], [81196, 104], [81496, 104], [81796, 104], [82096, 104], [82396, 104], [82696, 104], [82996, 104], [83297, 103], [83597, 103], [83897, 103], [84197, 103], [84497, 103], [84797, 103], [85097, 103], [85397, 103], [85697, 103], [85997, 103], [86297, 103], [86597, 103], [86897, 103], [87198, 102], [87498, 102], [87798, 102], [88098, 102], [88398, 102], [88698, 102], [88998, 102], [89298, 102], [89598, 102], [89898, 102]], "point": [149, 149]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan22", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -0.5, "y": 0.9009992, "z": -0.25}, "object_poses": [{"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "DishSponge_9171dd8d", "position": {"x": 0.28485465, "y": 1.11126435, "z": 1.88852167}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Pot_86f597c8", "position": {"x": 0.548222065, "y": 1.11294746, "z": 1.47979164}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Fork_b9d165af", "position": {"x": -2.84395027, "y": 0.9118485, "z": -0.9486538}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_46773746", "position": {"x": -1.41871345, "y": 0.850295067, "z": -1.25}, "rotation": {"x": 1.40334183e-14, "y": 3.208973e-43, "z": 2.89269767e-27}}, {"objectName": "Cup_46773746", "position": {"x": 0.241928414, "y": 1.00790739, "z": -1.31486058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ad96aa4f", "position": {"x": -2.74032116, "y": 0.380319774, "z": 0.8520002}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Egg_e02c5e35", "position": {"x": -2.61988449, "y": 1.5090251, "z": 1.09428811}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_f346af22", "position": {"x": 0.759770453, "y": 1.11295617, "z": 0.6525471}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Bowl_b7a8ccec", "position": {"x": -0.09281999, "y": 1.11073792, "z": 1.5108484}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Lettuce_e2bf19ed", "position": {"x": -2.68010283, "y": 1.55306363, "z": 0.932762861}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_e2bf19ed", "position": {"x": -2.8607583, "y": 1.55306363, "z": 0.690474868}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": -2.698607, "y": 1.79136467, "z": -0.448218465}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": -1.98216009, "y": 0.112172782, "z": -0.981952548}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_304c8d90", "position": {"x": -2.59, "y": 0.9, "z": -1.147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a99f93a6", "position": {"x": 0.284412235, "y": 1.15439582, "z": 1.58049023}, "rotation": {"x": 359.99234, "y": 2.19712561e-07, "z": -0.00426209671}}, {"objectName": "Fork_b9d165af", "position": {"x": -2.02628255, "y": 0.747497737, "z": -0.966086149}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_1cf3b23c", "position": {"x": -2.62011266, "y": 0.922509432, "z": -0.724260747}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_35b71b93", "position": {"x": 0.997390866, "y": 0.9, "z": -0.982281566}, "rotation": {"x": 0.0, "y": 334.941345, "z": 0.0}}, {"objectName": "Bread_81a93477", "position": {"x": -0.401600122, "y": 0.9483048, "z": -1.29149938}, "rotation": {"x": -6.960003e-05, "y": 5.980759e-05, "z": -6.60807855e-05}}, {"objectName": "Plate_ad96aa4f", "position": {"x": -2.81003428, "y": 1.6666342, "z": 0.03346309}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_f346af22", "position": {"x": 0.394298255, "y": 1.11295617, "z": 1.32586813}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Spatula_3b50a8bf", "position": {"x": -2.55216455, "y": 0.922, "z": 0.0104080439}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e2bf19ed", "position": {"x": -2.6608, "y": 0.8744, "z": 0.9831}, "rotation": {"x": 31.0465031, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_4f98d325", "position": {"x": -2.695, "y": 0.9, "z": -0.132}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9ffe4e75", "position": {"x": 0.203787982, "y": 1.15205479, "z": 1.05698335}, "rotation": {"x": 359.98233, "y": 0.00772271724, "z": 0.233231589}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": -2.10609436, "y": 1.65877759, "z": -1.43540239}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_dc49bd4c", "position": {"x": -2.770555, "y": 0.9113421, "z": -0.147414446}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_86f597c8", "position": {"x": 0.920218468, "y": 0.91254735, "z": -1.36589742}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "DishSponge_9171dd8d", "position": {"x": -1.07332408, "y": 0.851295948, "z": -1.24999988}, "rotation": {"x": 1.40334183e-14, "y": 3.208973e-43, "z": 2.89269767e-27}}, {"objectName": "Egg_e02c5e35", "position": {"x": -2.52118, "y": 0.9464633, "z": -0.0629096553}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_4b9198b3", "position": {"x": -0.157054365, "y": 0.9, "z": -1.01503634}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_1fa5d324", "position": {"x": 0.0605277121, "y": 1.1069684, "z": 1.58733857}, "rotation": {"x": 270.0, "y": 209.997833, "z": 0.0}}, {"objectName": "Cup_46773746", "position": {"x": -2.920977, "y": 1.67194128, "z": 0.7712372}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_f88244ed", "position": {"x": 0.372600079, "y": 1.65404749, "z": -1.35359979}, "rotation": {"x": 359.6101, "y": -5.922173e-05, "z": 0.00736820651}}, {"objectName": "Bowl_b7a8ccec", "position": {"x": 0.688191056, "y": 0.9103378, "z": -1.03210258}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 908671265, "scene_num": 22}, "task_id": "trial_T20190909_151927_879893", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A18X8ZAS0QDLKJ_32AT8R96GOQ50YO4VE3RI4RFPCZSU4", "high_descs": ["Walk towards the stove.", "Pick up the knife by the salt shaker.", "Turn around and walk towards the dishwasher, then turn right.", "Cut the loaf of bread into slices.", "Turn right. walk towards the sink. ", "Place the knife into the sink.", "Turn left, walk towards the loaf of bread.", "Pick up a slice of bread.", "Turn left and walk towards the microwave.", "Place the slice of bread inside of the microwave then turn it on, when its done remove it.", "Turn around and walk towards the round table, turn left, then walk towards the refrigerator.", "Open the refrigerator and place the bread on the shelf next to the lettuce."], "task_desc": "Place a slice of warm bread inside of the refrigerator. ", "votes": [1, 1, 1, 1, 1, 1]}, {"assignment_id": "A1S1K7134S2VUC_38YMOXR4MXGD54FCJ1FPZNGKS2KW6Q", "high_descs": ["Move to the counter that is left of the stove.", "Pick up the knife that is left of the stove.", "Turn left and move to the counter in front of the bread.", "use the knife to slice the bread.", "Turn right and move to the counter in front of the sink.", "Place the knife into the sink.", "Turn right and move to the counter in front of the bread.", "Pick up a slice of bread from the counter.", "Turn left, move to the counter in front of the microwave.", "Place the bread slice into the microwave, cook it, then pick it back up.", "Turn right and go to the refrigerator.", "Place the microwaved bread slice on to the top shelf of the refrigerator."], "task_desc": "Microwave a slice of bread, put it into the refrigerator.", "votes": [1, 1, 1, 1, 1, 1, 1, 0]}, {"assignment_id": "A1DMXEJGJY02E1_3SKEMFQBZ6M9EI4JN2TKVQIGAHA8KI", "high_descs": ["Walk forward to the stove.", "Pick up the knife on the counter to the right of the stove.", "Turn around and walk to the counter to the left of the sink.", "Slice the loaf of bread on the counter.", "Move to the right to stand at the sink.", "Place the knife in the sink.", "Move back to the left.", "Pick up a slice of bread.", "Go to the right to the microwave.", "Put the bread in the microwave, heat it, and take it out again.", "Turn around and go to the fridge on the left.", "Put the bread in the fridge."], "task_desc": "Put a microwaved slice of bread in the fridge.", "votes": [1, 1, 1, 1, 1, 1]}, {"assignment_id": "A1O3TWBUDONVLO_3XC1O3LBOV33W8EPB0GG1MRIEB0LTM", "high_descs": ["Walk forward to face the counter beside the stove.", "Pick up the knife on the counter.", "Turn around and walk forward and then turn right to face the sink.", "Cut the loaf of bread on the counter.", "Step to the right to face the sink.", "Place the knife in the sink.", "Step to the left to face the counter.", "Pick up a slice of bread from the counter.", "Step to the left to face the microwave.", "Heat the bread in the microwave and then remove it.", "Turn to the right and walk forward to face the fridge.", "Place the bread in the fridge to the left of the lettuce."], "task_desc": "Cut a loaf of bread as well as put it in the fridge.", "votes": [1, 1]}, {"assignment_id": "A1O3TWBUDONVLO_3UJ1CZ6IZK6OFJYPUDVJASZZQYX5SW", "high_descs": ["Walk straight ahead and stand in front of the stove.", "Pick up the knife laying on the counter.", "Turn around and walk halfway across the room and turn right to face the counter.", "Cut the bread on the counter into slices.", "Turn to the right, take a step, and turn to face the sink.", "Put the knife in the sink.", "Turn to the left, take a step, and then turn to face the counter with bread on it.", "Pick up a slice of bread.", "Turn to the left, take a step, and then turn to the right to face the microwave.", "Open the microwave, place the bread inside, heat the bread, and then open the door and remove the bread.", "Turn to the right, take a step, then turn right again and walk to the counter, then take a left and walk across the room to face cabinet.", "Open the cabinet and place the bread on the shelf and then close the cabinet."], "task_desc": "To cook a slice of bread and store it for later.", "votes": [1, 1]}]}}