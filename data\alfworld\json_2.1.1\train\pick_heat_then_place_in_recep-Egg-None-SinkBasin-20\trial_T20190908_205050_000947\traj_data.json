{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 31}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "SinkBasin", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|-5|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [0.4772318, 0.4772318, -6.90294264, -6.90294264, 3.868214, 3.868214]], "coordinateReceptacleObjectId": ["CounterTop", [0.932, 0.932, -8.008, -8.008, 3.796, 3.796]], "forceVisible": true, "objectId": "Egg|+00.12|+00.97|-01.73"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-5|3|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.936, -4.936, -6.724, -6.724, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.23|+00.90|-01.68"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-5|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "sinkbasin"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [0.4772318, 0.4772318, -6.90294264, -6.90294264, 3.868214, 3.868214]], "coordinateReceptacleObjectId": ["SinkBasin", [-0.5932, -0.5932, -8.1056, -8.1056, 3.0156, 3.0156]], "forceVisible": true, "objectId": "Egg|+00.12|+00.97|-01.73", "receptacleObjectId": "Sink|-00.11|+00.89|-02.01|SinkBasin"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+00.12|+00.97|-01.73"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [96, 101, 123, 131], "mask": [[30105, 7], [30403, 11], [30702, 14], [31001, 16], [31300, 18], [31599, 20], [31899, 21], [32198, 23], [32498, 23], [32797, 25], [33097, 25], [33397, 26], [33697, 26], [33997, 26], [34296, 27], [34596, 27], [34896, 27], [35197, 27], [35497, 27], [35797, 26], [36097, 26], [36398, 25], [36698, 25], [36999, 23], [37299, 23], [37600, 21], [37901, 20], [38202, 18], [38503, 16], [38804, 14], [39106, 10]], "point": [109, 115]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+00.12|+00.97|-01.73", "placeStationary": true, "receptacleObjectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 85, 133, 185], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54087, 31], [54388, 30], [54688, 29], [54988, 29], [55288, 29]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 85, 133, 185], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 50], [39057, 77], [39300, 49], [39359, 75], [39600, 48], [39660, 74], [39900, 47], [39961, 73], [40200, 47], [40262, 72], [40500, 46], [40563, 71], [40800, 46], [40863, 71], [41100, 45], [41164, 70], [41400, 45], [41464, 70], [41700, 45], [41765, 69], [42000, 45], [42065, 69], [42300, 45], [42365, 69], [42600, 45], [42666, 68], [42900, 45], [42966, 68], [43200, 45], [43266, 68], [43500, 45], [43566, 68], [43800, 46], [43866, 68], [44100, 46], [44165, 69], [44400, 46], [44465, 69], [44700, 47], [44765, 69], [45000, 47], [45064, 70], [45300, 48], [45364, 70], [45600, 49], [45663, 71], [45900, 50], [45962, 72], [46200, 52], [46261, 73], [46500, 55], [46558, 75], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54087, 31], [54388, 30], [54688, 29], [54988, 29], [55288, 29]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.936, -4.936, -6.724, -6.724, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.936, -4.936, -6.724, -6.724, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 85, 133, 186], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54000, 118], [54300, 118], [54600, 117], [54900, 117], [55200, 117], [55577, 5]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+00.12|+00.97|-01.73"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [45, 131, 65, 156], "mask": [[39050, 7], [39349, 10], [39648, 12], [39947, 14], [40247, 15], [40546, 17], [40846, 17], [41145, 19], [41445, 19], [41745, 20], [42045, 20], [42345, 20], [42645, 21], [42945, 21], [43245, 21], [43545, 21], [43846, 20], [44146, 19], [44446, 19], [44747, 18], [45047, 17], [45348, 16], [45649, 14], [45950, 12], [46252, 9], [46555, 3]], "point": [55, 142]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.23|+00.90|-01.68"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 85, 133, 185], "mask": [[25200, 120], [25321, 11], [25500, 132], [25800, 132], [26100, 132], [26400, 132], [26700, 133], [27000, 133], [27300, 133], [27600, 133], [27900, 133], [28200, 133], [28500, 133], [28800, 133], [29100, 133], [29400, 133], [29700, 133], [30000, 133], [30300, 133], [30600, 133], [30900, 133], [31200, 133], [31500, 133], [31800, 133], [32100, 133], [32400, 133], [32700, 133], [33000, 133], [33300, 133], [33600, 133], [33900, 133], [34200, 133], [34500, 133], [34800, 133], [35100, 133], [35400, 134], [35700, 134], [36000, 134], [36300, 134], [36600, 134], [36900, 134], [37200, 134], [37500, 134], [37800, 134], [38100, 134], [38400, 134], [38700, 134], [39000, 134], [39300, 134], [39600, 134], [39900, 134], [40200, 134], [40500, 134], [40800, 134], [41100, 134], [41400, 134], [41700, 134], [42000, 134], [42300, 134], [42600, 134], [42900, 134], [43200, 134], [43500, 134], [43800, 134], [44100, 134], [44400, 134], [44700, 134], [45000, 134], [45300, 134], [45600, 134], [45900, 134], [46200, 134], [46500, 133], [46800, 133], [47100, 133], [47400, 133], [47700, 133], [48000, 133], [48300, 133], [48600, 133], [48900, 133], [49200, 133], [49500, 132], [49800, 132], [50100, 132], [50400, 132], [50700, 132], [51000, 132], [51300, 132], [51600, 132], [51900, 132], [52200, 131], [52500, 131], [52800, 131], [53100, 131], [53400, 120], [53700, 119], [54087, 31], [54388, 30], [54688, 29], [54988, 29], [55288, 29]], "point": [66, 134]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+00.12|+00.97|-01.73", "placeStationary": true, "receptacleObjectId": "Sink|-00.11|+00.89|-02.01|SinkBasin"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [54, 131, 194, 169], "mask": [[39077, 102], [39374, 108], [39672, 112], [39970, 116], [40268, 119], [40567, 41], [40611, 77], [40866, 42], [40911, 78], [41165, 43], [41211, 79], [41465, 43], [41511, 79], [41764, 44], [41811, 80], [42063, 44], [42110, 81], [42363, 44], [42410, 81], [42663, 44], [42710, 81], [42963, 44], [43010, 81], [43262, 45], [43310, 81], [43562, 44], [43610, 82], [43862, 44], [43909, 83], [44161, 45], [44209, 83], [44461, 45], [44509, 83], [44761, 45], [44809, 83], [45060, 45], [45109, 83], [45360, 46], [45408, 85], [45660, 46], [45708, 85], [45959, 47], [46008, 85], [46259, 47], [46307, 86], [46559, 46], [46607, 86], [46858, 47], [46907, 86], [47158, 47], [47207, 87], [47458, 47], [47507, 18], [47532, 62], [47757, 48], [47807, 14], [47835, 59], [48057, 48], [48107, 13], [48142, 52], [48357, 47], [48407, 13], [48467, 27], [48656, 48], [48706, 14], [48735, 12], [48769, 25], [48956, 48], [49006, 15], [49034, 18], [49069, 25], [49255, 49], [49306, 17], [49333, 26], [49368, 27], [49555, 49], [49606, 20], [49630, 65], [49855, 49], [49906, 89], [50154, 6], [50162, 41], [50206, 89], [50454, 4], [50465, 38], [50506, 86]], "point": [124, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan20", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -0.75, "y": 0.9009992, "z": 0.5}, "object_poses": [{"objectName": "Potato_4e602a34", "position": {"x": -1.61054039, "y": 1.5339905, "z": -0.4959929}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Potato_4e602a34", "position": {"x": 0.689599156, "y": 0.942806, "z": -1.75133562}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_bd8f96dd", "position": {"x": 1.52849984, "y": 1.70435667, "z": -2.162786}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_c9c37476", "position": {"x": 0.180688992, "y": 0.930208743, "z": 0.534036636}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_620f4eaa", "position": {"x": 2.00973034, "y": 0.914428651, "z": -1.446528}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_620f4eaa", "position": {"x": -0.160943776, "y": 0.9315732, "z": 0.076635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_33e0bb4d", "position": {"x": -0.06476809, "y": 0.779456, "z": -2.07131481}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Spoon_a85d92ad", "position": {"x": 0.06681141, "y": 0.9317739, "z": 1.44883978}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_a1e5049a", "position": {"x": 1.82878232, "y": 0.9140485, "z": -1.329551}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_5f9ea4fe", "position": {"x": -1.55301237, "y": 1.183903, "z": -0.5994965}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_5f9ea4fe", "position": {"x": -0.160943776, "y": 0.927151, "z": 0.534036636}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d1e14b30", "position": {"x": 0.408444166, "y": 0.9340559, "z": 0.534036636}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_416e93ae", "position": {"x": 0.294566572, "y": 0.9537251, "z": 1.44883978}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_416e93ae", "position": {"x": 0.06681141, "y": 0.9500131, "z": 0.30533576}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_de47916c", "position": {"x": -0.0470661819, "y": 0.9787365, "z": 0.30533576}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_de47916c", "position": {"x": 0.457472026, "y": 0.965304, "z": -2.16910982}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6ea32579", "position": {"x": -0.0470661819, "y": 0.9232387, "z": 1.220139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_6ea32579", "position": {"x": -0.8244117, "y": 0.909806132, "z": -2.16755772}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b4d69cc9", "position": {"x": 1.850235, "y": 0.9586454, "z": -2.002}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_0e2bb95a", "position": {"x": 0.180688992, "y": 0.9219325, "z": 1.44883978}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_792121da", "position": {"x": 0.06681141, "y": 1.00156987, "z": 0.076635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_4e602a34", "position": {"x": -1.61053967, "y": 1.5339905, "z": -0.7030003}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_980e4fc9", "position": {"x": -1.27728152, "y": 1.6562798, "z": -1.60499954}, "rotation": {"x": -1.40334208e-14, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_11e2985b", "position": {"x": 1.73830819, "y": 0.9121536, "z": -1.21257389}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_a1e5049a", "position": {"x": 0.522321761, "y": 0.927481055, "z": 0.99143815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_bd8f96dd", "position": {"x": 1.57290637, "y": 0.164887309, "z": -1.266475}, "rotation": {"x": -7.062251e-30, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_b4d69cc9", "position": {"x": 0.399768561, "y": 0.9645044, "z": 0.11100021}, "rotation": {"x": 0.00258629257, "y": 0.009981024, "z": 1.60186684}}, {"objectName": "Lettuce_aebda124", "position": {"x": -1.429, "y": 0.9252, "z": -0.542}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "WineBottle_620f4eaa", "position": {"x": 2.00973034, "y": 0.914428651, "z": -1.09559679}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_33e0bb4d", "position": {"x": 0.408444166, "y": 0.9456444, "z": 1.220139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d1e14b30", "position": {"x": 0.180688992, "y": 0.9340559, "z": 1.220139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_725f4a28", "position": {"x": 0.00282304, "y": 0.909806132, "z": -1.72573566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_d7116d9f", "position": {"x": 1.5675, "y": 0.948799968, "z": -0.7576}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_5f9ea4fe", "position": {"x": -1.55301142, "y": 0.8596686, "z": -1.01351285}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_6ea32579", "position": {"x": 0.180688992, "y": 0.9232387, "z": 0.7627374}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_bc4f8581", "position": {"x": 1.82878232, "y": 0.9131094, "z": -1.56350493}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8db17fc4", "position": {"x": 0.06681141, "y": 0.9306866, "z": 0.99143815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_de47916c", "position": {"x": -1.43811178, "y": 1.23548853, "z": -0.806503534}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "DishSponge_c9c37476", "position": {"x": 1.67023325, "y": 0.552622855, "z": 2.4829}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_8bcb7893", "position": {"x": 0.11930795, "y": 0.9670535, "z": -1.72573566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_a85d92ad", "position": {"x": -0.210802972, "y": 0.765585542, "z": -1.98148525}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_416e93ae", "position": {"x": -0.0470661819, "y": 0.9500131, "z": 0.7627374}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_0e2bb95a", "position": {"x": 1.26109993, "y": 0.951999962, "z": -1.93409991}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1370683240, "scene_num": 20}, "task_id": "trial_T20190908_205050_000947", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A320QA9HJFUOZO_3P529IW9K1C1YSIMCO08L4WJHVCFLB", "high_descs": ["Turn around and walk forward to the kitchen sink", "Pick up the egg from the counter just by the sink", "Turn right and walk to the microwave", "Heat the egg in the microwave then remove it", "Turn left and face the kitchen sink", "Put the egg in the sink"], "task_desc": "Put the cooked egg in the kitchen sink", "votes": [1, 1]}, {"assignment_id": "A13OOAT2ORKH6V_3SKRO2GZ74IZO76WKP9MH2FARLKK18", "high_descs": ["Look down, turn around and walk to the sink.", "Grab the egg sitting to the front of the sink.", "Turn right and walk to the microwave.", "Place the egg on the plate inside the microwave, cook the egg and remove it.", "Holding the egg, look down, and turn left to face the sink.", "Place the egg inside the sink."], "task_desc": "Place a cooked egg inside the sink.", "votes": [1, 1]}, {"assignment_id": "A20FCMWP43CVIU_3TDXMTX3CEL3PYH5QS0RZIRE7SD6IJ", "high_descs": ["walk to face sink", "pick up white egg on ledge in front of sink", "walk to face microwave", "cook egg in microwave, remove egg from microwave", "walk to face sink", "place egg into sink basin"], "task_desc": "put cooked egg into sink", "votes": [1, 1]}]}}