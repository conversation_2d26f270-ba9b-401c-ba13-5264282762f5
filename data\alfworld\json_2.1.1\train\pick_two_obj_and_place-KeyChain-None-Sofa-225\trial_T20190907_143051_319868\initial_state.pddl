
(define (problem plan_trial_T20190907_143051_319868)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__minus_04_dot_58_bar__plus_00_dot_52_bar__plus_02_dot_15 - object
        Chair_bar__minus_04_dot_56_bar_00_dot_00_bar__plus_03_dot_87 - object
        CreditCard_bar__minus_00_dot_46_bar__plus_00_dot_49_bar__plus_02_dot_41 - object
        DeskLamp_bar__minus_04_dot_66_bar__plus_00_dot_51_bar__plus_03_dot_32 - object
        FloorLamp_bar__minus_04_dot_64_bar__plus_00_dot_01_bar__plus_00_dot_36 - object
        HousePlant_bar__minus_03_dot_13_bar__plus_01_dot_50_bar__plus_00_dot_12 - object
        KeyChain_bar__minus_00_dot_10_bar__plus_00_dot_76_bar__plus_02_dot_23 - object
        KeyChain_bar__minus_01_dot_82_bar__plus_00_dot_20_bar__plus_02_dot_53 - object
        KeyChain_bar__minus_04_dot_57_bar__plus_00_dot_51_bar__plus_03_dot_43 - object
        Laptop_bar__minus_00_dot_34_bar__plus_00_dot_76_bar__plus_02_dot_38 - object
        LightSwitch_bar__minus_03_dot_23_bar__plus_01_dot_34_bar__plus_05_dot_00 - object
        Newspaper_bar__minus_00_dot_25_bar__plus_00_dot_76_bar__plus_02_dot_98 - object
        Newspaper_bar__minus_00_dot_50_bar__plus_00_dot_18_bar__plus_03_dot_10 - object
        Newspaper_bar__minus_00_dot_78_bar__plus_00_dot_38_bar__plus_00_dot_71 - object
        Painting_bar__minus_02_dot_51_bar__plus_01_dot_82_bar__plus_00_dot_11 - object
        Pillow_bar__minus_01_dot_38_bar__plus_00_dot_48_bar__plus_04_dot_52 - object
        Pillow_bar__minus_02_dot_87_bar__plus_00_dot_48_bar__plus_04_dot_49 - object
        RemoteControl_bar__minus_00_dot_33_bar__plus_00_dot_77_bar__plus_03_dot_13 - object
        RemoteControl_bar__minus_01_dot_82_bar__plus_00_dot_20_bar__plus_02_dot_87 - object
        RemoteControl_bar__minus_02_dot_05_bar__plus_00_dot_20_bar__plus_02_dot_53 - object
        Statue_bar__minus_00_dot_20_bar__plus_00_dot_19_bar__plus_02_dot_68 - object
        Statue_bar__minus_00_dot_43_bar__plus_00_dot_50_bar__plus_02_dot_31 - object
        Television_bar__plus_00_dot_00_bar__plus_01_dot_38_bar__plus_02_dot_66 - object
        TissueBox_bar__minus_00_dot_47_bar__plus_00_dot_18_bar__plus_02_dot_36 - object
        TissueBox_bar__minus_02_dot_12_bar__plus_00_dot_20_bar__plus_02_dot_99 - object
        Vase_bar__minus_02_dot_88_bar__plus_01_dot_52_bar__plus_00_dot_14 - object
        Watch_bar__minus_01_dot_97_bar__plus_00_dot_20_bar__plus_02_dot_99 - object
        Watch_bar__minus_04_dot_52_bar__plus_00_dot_51_bar__plus_03_dot_34 - object
        Watch_bar__minus_04_dot_70_bar__plus_00_dot_51_bar__plus_03_dot_48 - object
        Window_bar__minus_01_dot_01_bar__plus_01_dot_39_bar__plus_00_dot_03 - object
        Window_bar__minus_03_dot_90_bar__plus_01_dot_39_bar__plus_00_dot_03 - object
        ArmChair_bar__minus_00_dot_88_bar__minus_00_dot_03_bar__plus_00_dot_57 - receptacle
        CoffeeTable_bar__minus_01_dot_97_bar__plus_00_dot_00_bar__plus_02_dot_62 - receptacle
        Drawer_bar__minus_04_dot_43_bar__plus_00_dot_47_bar__plus_03_dot_34 - receptacle
        GarbageCan_bar__minus_00_dot_23_bar__plus_00_dot_01_bar__plus_04_dot_78 - receptacle
        Shelf_bar__minus_00_dot_34_bar__plus_00_dot_18_bar__plus_02_dot_67 - receptacle
        Shelf_bar__minus_00_dot_46_bar__plus_00_dot_17_bar__plus_03_dot_04 - receptacle
        Shelf_bar__minus_00_dot_46_bar__plus_00_dot_42_bar__plus_03_dot_04 - receptacle
        Shelf_bar__minus_00_dot_46_bar__plus_00_dot_48_bar__plus_02_dot_30 - receptacle
        Shelf_bar__minus_00_dot_46_bar__plus_00_dot_57_bar__plus_03_dot_04 - receptacle
        Shelf_bar__minus_00_dot_47_bar__plus_00_dot_18_bar__plus_02_dot_30 - receptacle
        SideTable_bar__minus_04_dot_61_bar__plus_00_dot_01_bar__plus_03_dot_35 - receptacle
        Sofa_bar__minus_02_dot_13_bar_00_dot_00_bar__plus_04_dot_56 - receptacle
        Sofa_bar__minus_04_dot_57_bar_00_dot_00_bar__plus_01_dot_90 - receptacle
        TVStand_bar__minus_00_dot_34_bar__plus_00_dot_01_bar__plus_02_dot_68 - receptacle
        loc_bar__minus_15_bar_17_bar_2_bar_60 - location
        loc_bar__minus_4_bar_5_bar_2_bar_15 - location
        loc_bar__minus_6_bar_8_bar_1_bar_45 - location
        loc_bar__minus_4_bar_11_bar_1_bar_15 - location
        loc_bar__minus_4_bar_7_bar_2_bar_60 - location
        loc_bar__minus_16_bar_15_bar_3_bar_60 - location
        loc_bar__minus_16_bar_2_bar_2_bar_30 - location
        loc_bar__minus_17_bar_17_bar_2_bar_60 - location
        loc_bar__minus_15_bar_18_bar_1_bar_30 - location
        loc_bar__minus_6_bar_14_bar_1_bar_60 - location
        loc_bar__minus_10_bar_13_bar_0_bar_45 - location
        loc_bar__minus_16_bar_2_bar_3_bar_60 - location
        loc_bar__minus_12_bar_3_bar_2_bar_15 - location
        loc_bar__minus_4_bar_11_bar_3_bar_60 - location
        loc_bar__minus_13_bar_8_bar_3_bar_45 - location
        loc_bar__minus_2_bar_17_bar_0_bar_60 - location
        loc_bar__minus_10_bar_3_bar_2_bar__minus_15 - location
        loc_bar__minus_6_bar_14_bar_1_bar_45 - location
        loc_bar__minus_13_bar_3_bar_2_bar_15 - location
        loc_bar__minus_5_bar_11_bar_1_bar_60 - location
        loc_bar__minus_5_bar_14_bar_1_bar_60 - location
        loc_bar__minus_6_bar_7_bar_1_bar_60 - location
        loc_bar__minus_15_bar_18_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType ArmChair_bar__minus_00_dot_88_bar__minus_00_dot_03_bar__plus_00_dot_57 ArmChairType)
        (receptacleType Sofa_bar__minus_02_dot_13_bar_00_dot_00_bar__plus_04_dot_56 SofaType)
        (receptacleType Drawer_bar__minus_04_dot_43_bar__plus_00_dot_47_bar__plus_03_dot_34 DrawerType)
        (receptacleType Shelf_bar__minus_00_dot_46_bar__plus_00_dot_17_bar__plus_03_dot_04 ShelfType)
        (receptacleType Shelf_bar__minus_00_dot_46_bar__plus_00_dot_57_bar__plus_03_dot_04 ShelfType)
        (receptacleType Shelf_bar__minus_00_dot_46_bar__plus_00_dot_48_bar__plus_02_dot_30 ShelfType)
        (receptacleType CoffeeTable_bar__minus_01_dot_97_bar__plus_00_dot_00_bar__plus_02_dot_62 CoffeeTableType)
        (receptacleType Shelf_bar__minus_00_dot_34_bar__plus_00_dot_18_bar__plus_02_dot_67 ShelfType)
        (receptacleType Shelf_bar__minus_00_dot_46_bar__plus_00_dot_42_bar__plus_03_dot_04 ShelfType)
        (receptacleType Shelf_bar__minus_00_dot_47_bar__plus_00_dot_18_bar__plus_02_dot_30 ShelfType)
        (receptacleType GarbageCan_bar__minus_00_dot_23_bar__plus_00_dot_01_bar__plus_04_dot_78 GarbageCanType)
        (receptacleType TVStand_bar__minus_00_dot_34_bar__plus_00_dot_01_bar__plus_02_dot_68 TVStandType)
        (receptacleType Sofa_bar__minus_04_dot_57_bar_00_dot_00_bar__plus_01_dot_90 SofaType)
        (receptacleType SideTable_bar__minus_04_dot_61_bar__plus_00_dot_01_bar__plus_03_dot_35 SideTableType)
        (objectType Pillow_bar__minus_01_dot_38_bar__plus_00_dot_48_bar__plus_04_dot_52 PillowType)
        (objectType Painting_bar__minus_02_dot_51_bar__plus_01_dot_82_bar__plus_00_dot_11 PaintingType)
        (objectType Window_bar__minus_03_dot_90_bar__plus_01_dot_39_bar__plus_00_dot_03 WindowType)
        (objectType KeyChain_bar__minus_04_dot_57_bar__plus_00_dot_51_bar__plus_03_dot_43 KeyChainType)
        (objectType Statue_bar__minus_00_dot_20_bar__plus_00_dot_19_bar__plus_02_dot_68 StatueType)
        (objectType LightSwitch_bar__minus_03_dot_23_bar__plus_01_dot_34_bar__plus_05_dot_00 LightSwitchType)
        (objectType Statue_bar__minus_00_dot_43_bar__plus_00_dot_50_bar__plus_02_dot_31 StatueType)
        (objectType KeyChain_bar__minus_01_dot_82_bar__plus_00_dot_20_bar__plus_02_dot_53 KeyChainType)
        (objectType TissueBox_bar__minus_00_dot_47_bar__plus_00_dot_18_bar__plus_02_dot_36 TissueBoxType)
        (objectType CreditCard_bar__minus_00_dot_46_bar__plus_00_dot_49_bar__plus_02_dot_41 CreditCardType)
        (objectType Newspaper_bar__minus_00_dot_50_bar__plus_00_dot_18_bar__plus_03_dot_10 NewspaperType)
        (objectType TissueBox_bar__minus_02_dot_12_bar__plus_00_dot_20_bar__plus_02_dot_99 TissueBoxType)
        (objectType DeskLamp_bar__minus_04_dot_66_bar__plus_00_dot_51_bar__plus_03_dot_32 DeskLampType)
        (objectType Television_bar__plus_00_dot_00_bar__plus_01_dot_38_bar__plus_02_dot_66 TelevisionType)
        (objectType FloorLamp_bar__minus_04_dot_64_bar__plus_00_dot_01_bar__plus_00_dot_36 FloorLampType)
        (objectType Vase_bar__minus_02_dot_88_bar__plus_01_dot_52_bar__plus_00_dot_14 VaseType)
        (objectType HousePlant_bar__minus_03_dot_13_bar__plus_01_dot_50_bar__plus_00_dot_12 HousePlantType)
        (objectType RemoteControl_bar__minus_02_dot_05_bar__plus_00_dot_20_bar__plus_02_dot_53 RemoteControlType)
        (objectType Watch_bar__minus_04_dot_70_bar__plus_00_dot_51_bar__plus_03_dot_48 WatchType)
        (objectType Chair_bar__minus_04_dot_56_bar_00_dot_00_bar__plus_03_dot_87 ChairType)
        (objectType Newspaper_bar__minus_00_dot_25_bar__plus_00_dot_76_bar__plus_02_dot_98 NewspaperType)
        (objectType Pillow_bar__minus_02_dot_87_bar__plus_00_dot_48_bar__plus_04_dot_49 PillowType)
        (objectType Window_bar__minus_01_dot_01_bar__plus_01_dot_39_bar__plus_00_dot_03 WindowType)
        (objectType Box_bar__minus_04_dot_58_bar__plus_00_dot_52_bar__plus_02_dot_15 BoxType)
        (objectType RemoteControl_bar__minus_01_dot_82_bar__plus_00_dot_20_bar__plus_02_dot_87 RemoteControlType)
        (objectType Laptop_bar__minus_00_dot_34_bar__plus_00_dot_76_bar__plus_02_dot_38 LaptopType)
        (objectType KeyChain_bar__minus_00_dot_10_bar__plus_00_dot_76_bar__plus_02_dot_23 KeyChainType)
        (objectType Newspaper_bar__minus_00_dot_78_bar__plus_00_dot_38_bar__plus_00_dot_71 NewspaperType)
        (objectType RemoteControl_bar__minus_00_dot_33_bar__plus_00_dot_77_bar__plus_03_dot_13 RemoteControlType)
        (objectType Watch_bar__minus_01_dot_97_bar__plus_00_dot_20_bar__plus_02_dot_99 WatchType)
        (objectType Watch_bar__minus_04_dot_52_bar__plus_00_dot_51_bar__plus_03_dot_34 WatchType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType TissueBoxType)
        (canContain ShelfType WatchType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WatchType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WatchType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType StatueType)
        (canContain CoffeeTableType WatchType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType VaseType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType TissueBoxType)
        (canContain CoffeeTableType StatueType)
        (canContain ShelfType WatchType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WatchType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType StatueType)
        (canContain ShelfType WatchType)
        (canContain ShelfType NewspaperType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType StatueType)
        (canContain GarbageCanType NewspaperType)
        (canContain GarbageCanType TissueBoxType)
        (canContain TVStandType TissueBoxType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain SideTableType WatchType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType StatueType)
        (pickupable Pillow_bar__minus_01_dot_38_bar__plus_00_dot_48_bar__plus_04_dot_52)
        (pickupable KeyChain_bar__minus_04_dot_57_bar__plus_00_dot_51_bar__plus_03_dot_43)
        (pickupable Statue_bar__minus_00_dot_20_bar__plus_00_dot_19_bar__plus_02_dot_68)
        (pickupable Statue_bar__minus_00_dot_43_bar__plus_00_dot_50_bar__plus_02_dot_31)
        (pickupable KeyChain_bar__minus_01_dot_82_bar__plus_00_dot_20_bar__plus_02_dot_53)
        (pickupable TissueBox_bar__minus_00_dot_47_bar__plus_00_dot_18_bar__plus_02_dot_36)
        (pickupable CreditCard_bar__minus_00_dot_46_bar__plus_00_dot_49_bar__plus_02_dot_41)
        (pickupable Newspaper_bar__minus_00_dot_50_bar__plus_00_dot_18_bar__plus_03_dot_10)
        (pickupable TissueBox_bar__minus_02_dot_12_bar__plus_00_dot_20_bar__plus_02_dot_99)
        (pickupable Vase_bar__minus_02_dot_88_bar__plus_01_dot_52_bar__plus_00_dot_14)
        (pickupable RemoteControl_bar__minus_02_dot_05_bar__plus_00_dot_20_bar__plus_02_dot_53)
        (pickupable Watch_bar__minus_04_dot_70_bar__plus_00_dot_51_bar__plus_03_dot_48)
        (pickupable Newspaper_bar__minus_00_dot_25_bar__plus_00_dot_76_bar__plus_02_dot_98)
        (pickupable Pillow_bar__minus_02_dot_87_bar__plus_00_dot_48_bar__plus_04_dot_49)
        (pickupable Box_bar__minus_04_dot_58_bar__plus_00_dot_52_bar__plus_02_dot_15)
        (pickupable RemoteControl_bar__minus_01_dot_82_bar__plus_00_dot_20_bar__plus_02_dot_87)
        (pickupable Laptop_bar__minus_00_dot_34_bar__plus_00_dot_76_bar__plus_02_dot_38)
        (pickupable KeyChain_bar__minus_00_dot_10_bar__plus_00_dot_76_bar__plus_02_dot_23)
        (pickupable Newspaper_bar__minus_00_dot_78_bar__plus_00_dot_38_bar__plus_00_dot_71)
        (pickupable RemoteControl_bar__minus_00_dot_33_bar__plus_00_dot_77_bar__plus_03_dot_13)
        (pickupable Watch_bar__minus_01_dot_97_bar__plus_00_dot_20_bar__plus_02_dot_99)
        (pickupable Watch_bar__minus_04_dot_52_bar__plus_00_dot_51_bar__plus_03_dot_34)
        (isReceptacleObject Box_bar__minus_04_dot_58_bar__plus_00_dot_52_bar__plus_02_dot_15)
        (openable Drawer_bar__minus_04_dot_43_bar__plus_00_dot_47_bar__plus_03_dot_34)
        
        (atLocation agent1 loc_bar__minus_15_bar_18_bar_3_bar_30)
        
        
        
        
        
        
        
        (toggleable DeskLamp_bar__minus_04_dot_66_bar__plus_00_dot_51_bar__plus_03_dot_32)
        (toggleable FloorLamp_bar__minus_04_dot_64_bar__plus_00_dot_01_bar__plus_00_dot_36)
        
        
        
        
        (inReceptacle Newspaper_bar__minus_00_dot_50_bar__plus_00_dot_18_bar__plus_03_dot_10 Shelf_bar__minus_00_dot_46_bar__plus_00_dot_17_bar__plus_03_dot_04)
        (inReceptacle Watch_bar__minus_04_dot_70_bar__plus_00_dot_51_bar__plus_03_dot_48 SideTable_bar__minus_04_dot_61_bar__plus_00_dot_01_bar__plus_03_dot_35)
        (inReceptacle Watch_bar__minus_04_dot_52_bar__plus_00_dot_51_bar__plus_03_dot_34 SideTable_bar__minus_04_dot_61_bar__plus_00_dot_01_bar__plus_03_dot_35)
        (inReceptacle DeskLamp_bar__minus_04_dot_66_bar__plus_00_dot_51_bar__plus_03_dot_32 SideTable_bar__minus_04_dot_61_bar__plus_00_dot_01_bar__plus_03_dot_35)
        (inReceptacle KeyChain_bar__minus_04_dot_57_bar__plus_00_dot_51_bar__plus_03_dot_43 SideTable_bar__minus_04_dot_61_bar__plus_00_dot_01_bar__plus_03_dot_35)
        (inReceptacle Statue_bar__minus_00_dot_20_bar__plus_00_dot_19_bar__plus_02_dot_68 Shelf_bar__minus_00_dot_34_bar__plus_00_dot_18_bar__plus_02_dot_67)
        (inReceptacle Pillow_bar__minus_01_dot_38_bar__plus_00_dot_48_bar__plus_04_dot_52 Sofa_bar__minus_02_dot_13_bar_00_dot_00_bar__plus_04_dot_56)
        (inReceptacle Pillow_bar__minus_02_dot_87_bar__plus_00_dot_48_bar__plus_04_dot_49 Sofa_bar__minus_02_dot_13_bar_00_dot_00_bar__plus_04_dot_56)
        (inReceptacle CreditCard_bar__minus_00_dot_46_bar__plus_00_dot_49_bar__plus_02_dot_41 Shelf_bar__minus_00_dot_46_bar__plus_00_dot_48_bar__plus_02_dot_30)
        (inReceptacle Statue_bar__minus_00_dot_43_bar__plus_00_dot_50_bar__plus_02_dot_31 Shelf_bar__minus_00_dot_46_bar__plus_00_dot_48_bar__plus_02_dot_30)
        (inReceptacle RemoteControl_bar__minus_01_dot_82_bar__plus_00_dot_20_bar__plus_02_dot_87 CoffeeTable_bar__minus_01_dot_97_bar__plus_00_dot_00_bar__plus_02_dot_62)
        (inReceptacle RemoteControl_bar__minus_02_dot_05_bar__plus_00_dot_20_bar__plus_02_dot_53 CoffeeTable_bar__minus_01_dot_97_bar__plus_00_dot_00_bar__plus_02_dot_62)
        (inReceptacle TissueBox_bar__minus_02_dot_12_bar__plus_00_dot_20_bar__plus_02_dot_99 CoffeeTable_bar__minus_01_dot_97_bar__plus_00_dot_00_bar__plus_02_dot_62)
        (inReceptacle Watch_bar__minus_01_dot_97_bar__plus_00_dot_20_bar__plus_02_dot_99 CoffeeTable_bar__minus_01_dot_97_bar__plus_00_dot_00_bar__plus_02_dot_62)
        (inReceptacle KeyChain_bar__minus_01_dot_82_bar__plus_00_dot_20_bar__plus_02_dot_53 CoffeeTable_bar__minus_01_dot_97_bar__plus_00_dot_00_bar__plus_02_dot_62)
        (inReceptacle TissueBox_bar__minus_00_dot_47_bar__plus_00_dot_18_bar__plus_02_dot_36 Shelf_bar__minus_00_dot_47_bar__plus_00_dot_18_bar__plus_02_dot_30)
        (inReceptacle Box_bar__minus_04_dot_58_bar__plus_00_dot_52_bar__plus_02_dot_15 Sofa_bar__minus_04_dot_57_bar_00_dot_00_bar__plus_01_dot_90)
        (inReceptacle Newspaper_bar__minus_00_dot_78_bar__plus_00_dot_38_bar__plus_00_dot_71 ArmChair_bar__minus_00_dot_88_bar__minus_00_dot_03_bar__plus_00_dot_57)
        (inReceptacle KeyChain_bar__minus_00_dot_10_bar__plus_00_dot_76_bar__plus_02_dot_23 TVStand_bar__minus_00_dot_34_bar__plus_00_dot_01_bar__plus_02_dot_68)
        (inReceptacle Laptop_bar__minus_00_dot_34_bar__plus_00_dot_76_bar__plus_02_dot_38 TVStand_bar__minus_00_dot_34_bar__plus_00_dot_01_bar__plus_02_dot_68)
        (inReceptacle Newspaper_bar__minus_00_dot_25_bar__plus_00_dot_76_bar__plus_02_dot_98 TVStand_bar__minus_00_dot_34_bar__plus_00_dot_01_bar__plus_02_dot_68)
        (inReceptacle RemoteControl_bar__minus_00_dot_33_bar__plus_00_dot_77_bar__plus_03_dot_13 TVStand_bar__minus_00_dot_34_bar__plus_00_dot_01_bar__plus_02_dot_68)
        
        
        (receptacleAtLocation ArmChair_bar__minus_00_dot_88_bar__minus_00_dot_03_bar__plus_00_dot_57 loc_bar__minus_4_bar_7_bar_2_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_01_dot_97_bar__plus_00_dot_00_bar__plus_02_dot_62 loc_bar__minus_4_bar_11_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__minus_04_dot_43_bar__plus_00_dot_47_bar__plus_03_dot_34 loc_bar__minus_15_bar_17_bar_2_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_00_dot_23_bar__plus_00_dot_01_bar__plus_04_dot_78 loc_bar__minus_2_bar_17_bar_0_bar_60)
        (receptacleAtLocation Shelf_bar__minus_00_dot_34_bar__plus_00_dot_18_bar__plus_02_dot_67 loc_bar__minus_6_bar_8_bar_1_bar_45)
        (receptacleAtLocation Shelf_bar__minus_00_dot_46_bar__plus_00_dot_17_bar__plus_03_dot_04 loc_bar__minus_6_bar_14_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__minus_00_dot_46_bar__plus_00_dot_42_bar__plus_03_dot_04 loc_bar__minus_6_bar_14_bar_1_bar_45)
        (receptacleAtLocation Shelf_bar__minus_00_dot_46_bar__plus_00_dot_48_bar__plus_02_dot_30 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__minus_00_dot_46_bar__plus_00_dot_57_bar__plus_03_dot_04 loc_bar__minus_5_bar_14_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__minus_00_dot_47_bar__plus_00_dot_18_bar__plus_02_dot_30 loc_bar__minus_6_bar_7_bar_1_bar_60)
        (receptacleAtLocation SideTable_bar__minus_04_dot_61_bar__plus_00_dot_01_bar__plus_03_dot_35 loc_bar__minus_17_bar_17_bar_2_bar_60)
        (receptacleAtLocation Sofa_bar__minus_02_dot_13_bar_00_dot_00_bar__plus_04_dot_56 loc_bar__minus_10_bar_13_bar_0_bar_45)
        (receptacleAtLocation Sofa_bar__minus_04_dot_57_bar_00_dot_00_bar__plus_01_dot_90 loc_bar__minus_13_bar_8_bar_3_bar_45)
        (receptacleAtLocation TVStand_bar__minus_00_dot_34_bar__plus_00_dot_01_bar__plus_02_dot_68 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (objectAtLocation Newspaper_bar__minus_00_dot_25_bar__plus_00_dot_76_bar__plus_02_dot_98 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (objectAtLocation Watch_bar__minus_04_dot_52_bar__plus_00_dot_51_bar__plus_03_dot_34 loc_bar__minus_17_bar_17_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_04_dot_57_bar__plus_00_dot_51_bar__plus_03_dot_43 loc_bar__minus_17_bar_17_bar_2_bar_60)
        (objectAtLocation Pillow_bar__minus_01_dot_38_bar__plus_00_dot_48_bar__plus_04_dot_52 loc_bar__minus_10_bar_13_bar_0_bar_45)
        (objectAtLocation RemoteControl_bar__minus_01_dot_82_bar__plus_00_dot_20_bar__plus_02_dot_87 loc_bar__minus_4_bar_11_bar_3_bar_60)
        (objectAtLocation TissueBox_bar__minus_02_dot_12_bar__plus_00_dot_20_bar__plus_02_dot_99 loc_bar__minus_4_bar_11_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__minus_02_dot_05_bar__plus_00_dot_20_bar__plus_02_dot_53 loc_bar__minus_4_bar_11_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__minus_01_dot_82_bar__plus_00_dot_20_bar__plus_02_dot_53 loc_bar__minus_4_bar_11_bar_3_bar_60)
        (objectAtLocation Newspaper_bar__minus_00_dot_50_bar__plus_00_dot_18_bar__plus_03_dot_10 loc_bar__minus_6_bar_14_bar_1_bar_60)
        (objectAtLocation Watch_bar__minus_04_dot_70_bar__plus_00_dot_51_bar__plus_03_dot_48 loc_bar__minus_17_bar_17_bar_2_bar_60)
        (objectAtLocation Chair_bar__minus_04_dot_56_bar_00_dot_00_bar__plus_03_dot_87 loc_bar__minus_16_bar_15_bar_3_bar_60)
        (objectAtLocation Box_bar__minus_04_dot_58_bar__plus_00_dot_52_bar__plus_02_dot_15 loc_bar__minus_13_bar_8_bar_3_bar_45)
        (objectAtLocation HousePlant_bar__minus_03_dot_13_bar__plus_01_dot_50_bar__plus_00_dot_12 loc_bar__minus_13_bar_3_bar_2_bar_15)
        (objectAtLocation CreditCard_bar__minus_00_dot_46_bar__plus_00_dot_49_bar__plus_02_dot_41 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (objectAtLocation Television_bar__plus_00_dot_00_bar__plus_01_dot_38_bar__plus_02_dot_66 loc_bar__minus_4_bar_11_bar_1_bar_15)
        (objectAtLocation DeskLamp_bar__minus_04_dot_66_bar__plus_00_dot_51_bar__plus_03_dot_32 loc_bar__minus_17_bar_17_bar_2_bar_60)
        (objectAtLocation TissueBox_bar__minus_00_dot_47_bar__plus_00_dot_18_bar__plus_02_dot_36 loc_bar__minus_6_bar_7_bar_1_bar_60)
        (objectAtLocation Laptop_bar__minus_00_dot_34_bar__plus_00_dot_76_bar__plus_02_dot_38 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (objectAtLocation Vase_bar__minus_02_dot_88_bar__plus_01_dot_52_bar__plus_00_dot_14 loc_bar__minus_12_bar_3_bar_2_bar_15)
        (objectAtLocation RemoteControl_bar__minus_00_dot_33_bar__plus_00_dot_77_bar__plus_03_dot_13 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (objectAtLocation Painting_bar__minus_02_dot_51_bar__plus_01_dot_82_bar__plus_00_dot_11 loc_bar__minus_10_bar_3_bar_2_bar__minus_15)
        (objectAtLocation Pillow_bar__minus_02_dot_87_bar__plus_00_dot_48_bar__plus_04_dot_49 loc_bar__minus_10_bar_13_bar_0_bar_45)
        (objectAtLocation FloorLamp_bar__minus_04_dot_64_bar__plus_00_dot_01_bar__plus_00_dot_36 loc_bar__minus_16_bar_2_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__minus_00_dot_10_bar__plus_00_dot_76_bar__plus_02_dot_23 loc_bar__minus_5_bar_11_bar_1_bar_60)
        (objectAtLocation Watch_bar__minus_01_dot_97_bar__plus_00_dot_20_bar__plus_02_dot_99 loc_bar__minus_4_bar_11_bar_3_bar_60)
        (objectAtLocation LightSwitch_bar__minus_03_dot_23_bar__plus_01_dot_34_bar__plus_05_dot_00 loc_bar__minus_15_bar_18_bar_1_bar_30)
        (objectAtLocation Newspaper_bar__minus_00_dot_78_bar__plus_00_dot_38_bar__plus_00_dot_71 loc_bar__minus_4_bar_7_bar_2_bar_60)
        (objectAtLocation Window_bar__minus_01_dot_01_bar__plus_01_dot_39_bar__plus_00_dot_03 loc_bar__minus_4_bar_5_bar_2_bar_15)
        (objectAtLocation Window_bar__minus_03_dot_90_bar__plus_01_dot_39_bar__plus_00_dot_03 loc_bar__minus_16_bar_2_bar_2_bar_30)
        (objectAtLocation Statue_bar__minus_00_dot_20_bar__plus_00_dot_19_bar__plus_02_dot_68 loc_bar__minus_6_bar_8_bar_1_bar_45)
        (objectAtLocation Statue_bar__minus_00_dot_43_bar__plus_00_dot_50_bar__plus_02_dot_31 loc_bar__minus_5_bar_11_bar_1_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 KeyChainType)
                                    (receptacleType ?r SofaType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 KeyChainType)
                                            (receptacleType ?r SofaType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            