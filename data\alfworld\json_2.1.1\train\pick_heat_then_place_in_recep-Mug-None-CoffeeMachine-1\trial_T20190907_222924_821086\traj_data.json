{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000340.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000341.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000342.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000343.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000344.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000345.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000346.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000347.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000348.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000349.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000350.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000351.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000352.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000353.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000354.png", "low_idx": 60}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "CoffeeMachine", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|1|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [-1.894742728, -1.894742728, 1.007095336, 1.007095336, 4.4264, 4.4264]], "coordinateReceptacleObjectId": ["CounterTop", [-0.316, -0.316, -0.004, -0.004, 4.5884, 4.5884]], "forceVisible": true, "objectId": "Mug|-00.47|+01.11|+00.25"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-7|2|0"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.948, -0.948, -10.1004, -10.1004, 6.756, 6.756]], "forceVisible": true, "objectId": "Microwave|-00.24|+01.69|-02.53"}}, {"discrete_action": {"action": "GotoLocation", "args": ["coffeemachine"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-1|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "coffeemachine"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [-1.894742728, -1.894742728, 1.007095336, 1.007095336, 4.4264, 4.4264]], "coordinateReceptacleObjectId": ["CoffeeMachine", [-7.988, -7.988, -0.676, -0.676, 3.6, 3.6]], "forceVisible": true, "objectId": "Mug|-00.47|+01.11|+00.25", "receptacleObjectId": "CoffeeMachine|-02.00|+00.90|-00.17"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-00.47|+01.11|+00.25"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [129, 95, 169, 149], "mask": [[28348, 2], [28641, 16], [28938, 23], [29236, 27], [29534, 30], [29833, 33], [30132, 35], [30431, 37], [30730, 39], [31030, 39], [31329, 40], [31629, 41], [31929, 41], [32229, 41], [32529, 41], [32829, 40], [33129, 40], [33429, 40], [33729, 40], [34029, 40], [34329, 40], [34630, 39], [34930, 39], [35230, 39], [35530, 39], [35830, 39], [36130, 39], [36430, 39], [36730, 39], [37030, 39], [37330, 39], [37630, 38], [37930, 38], [38230, 38], [38530, 38], [38830, 38], [39131, 37], [39431, 37], [39731, 37], [40031, 37], [40331, 37], [40631, 37], [40931, 37], [41231, 37], [41531, 36], [41831, 36], [42132, 35], [42432, 34], [42733, 33], [43034, 31], [43335, 29], [43636, 26], [43938, 23], [44240, 19], [44543, 12]], "point": [149, 121]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-00.47|+01.11|+00.25", "placeStationary": true, "receptacleObjectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 53], [13260, 107], [13500, 50], [13566, 101], [13800, 48], [13869, 98], [14100, 46], [14171, 96], [14400, 45], [14472, 95], [14700, 44], [14773, 94], [15000, 44], [15074, 93], [15300, 44], [15375, 92], [15600, 44], [15676, 91], [15900, 43], [15976, 91], [16200, 43], [16276, 91], [16500, 43], [16577, 90], [16800, 43], [16877, 90], [17100, 43], [17177, 90], [17400, 44], [17477, 89], [17700, 44], [17777, 89], [18000, 44], [18077, 89], [18300, 44], [18377, 89], [18600, 44], [18677, 89], [18900, 45], [18976, 90], [19200, 45], [19276, 90], [19500, 45], [19576, 90], [19800, 45], [19876, 90], [20100, 46], [20175, 91], [20400, 46], [20475, 91], [20700, 46], [20775, 91], [21000, 47], [21074, 92], [21300, 47], [21374, 92], [21600, 48], [21673, 93], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 1], [9312, 155], [9618, 149], [9922, 145], [10225, 142], [10528, 139], [10830, 137], [11131, 136], [11433, 134], [11734, 133], [12035, 132], [12335, 132], [12635, 132], [12935, 132], [13235, 18], [13260, 107], [13535, 15], [13566, 101], [13835, 13], [13869, 98], [14135, 11], [14171, 96], [14435, 10], [14472, 95], [14735, 9], [14773, 94], [15035, 9], [15074, 93], [15335, 9], [15375, 92], [15635, 9], [15676, 91], [15935, 8], [15976, 91], [16235, 8], [16276, 91], [16535, 8], [16577, 90], [16835, 8], [16877, 90], [17135, 8], [17177, 90], [17435, 9], [17477, 89], [17735, 9], [17777, 89], [18035, 9], [18077, 89], [18335, 9], [18377, 89], [18635, 9], [18677, 89], [18935, 10], [18976, 90], [19235, 10], [19276, 90], [19535, 10], [19576, 90], [19835, 10], [19876, 90], [20135, 11], [20175, 91], [20435, 11], [20475, 91], [20735, 11], [20775, 91], [21035, 12], [21074, 92], [21335, 12], [21374, 92], [21635, 13], [21673, 93], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.948, -0.948, -10.1004, -10.1004, 6.756, 6.756]], "forceVisible": true, "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.948, -0.948, -10.1004, -10.1004, 6.756, 6.756]], "forceVisible": true, "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 167], [13500, 167], [13800, 167], [14100, 167], [14400, 167], [14700, 167], [15000, 167], [15300, 167], [15600, 167], [15900, 167], [16200, 167], [16500, 167], [16800, 167], [17100, 167], [17400, 166], [17700, 166], [18000, 166], [18300, 166], [18600, 166], [18900, 166], [19200, 166], [19500, 166], [19800, 166], [20100, 166], [20400, 166], [20700, 166], [21000, 166], [21300, 166], [21600, 166], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-00.47|+01.11|+00.25"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [0, 32, 34, 73], "mask": [[9301, 11], [9600, 18], [9900, 22], [10200, 25], [10500, 28], [10800, 30], [11100, 31], [11400, 33], [11700, 34], [12000, 35], [12300, 35], [12600, 35], [12900, 35], [13200, 35], [13500, 35], [13800, 35], [14100, 35], [14400, 35], [14700, 35], [15000, 35], [15300, 35], [15600, 35], [15900, 35], [16200, 35], [16500, 35], [16800, 35], [17100, 35], [17400, 35], [17700, 35], [18000, 35], [18300, 35], [18600, 35], [18900, 35], [19200, 35], [19500, 35], [19800, 35], [20100, 35], [20400, 35], [20700, 35], [21000, 35], [21300, 35], [21600, 35]], "point": [17, 51]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.24|+01.69|-02.53"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 166, 102], "mask": [[0, 167], [300, 167], [600, 167], [900, 167], [1200, 167], [1500, 167], [1800, 167], [2100, 167], [2400, 167], [2700, 167], [3000, 167], [3300, 167], [3600, 167], [3900, 167], [4200, 167], [4500, 167], [4800, 167], [5100, 167], [5400, 167], [5700, 167], [6000, 167], [6300, 167], [6600, 167], [6900, 167], [7200, 167], [7500, 167], [7800, 167], [8100, 167], [8400, 167], [8700, 167], [9000, 167], [9300, 167], [9600, 167], [9900, 167], [10200, 167], [10500, 167], [10800, 167], [11100, 167], [11400, 167], [11700, 167], [12000, 167], [12300, 167], [12600, 167], [12900, 167], [13200, 53], [13260, 107], [13500, 50], [13566, 101], [13800, 48], [13869, 98], [14100, 46], [14171, 96], [14400, 45], [14472, 95], [14700, 44], [14773, 94], [15000, 44], [15074, 93], [15300, 44], [15375, 92], [15600, 44], [15676, 91], [15900, 43], [15976, 91], [16200, 43], [16276, 91], [16500, 43], [16577, 90], [16800, 43], [16877, 90], [17100, 43], [17177, 90], [17400, 44], [17477, 89], [17700, 44], [17777, 89], [18000, 44], [18077, 89], [18300, 44], [18377, 89], [18600, 44], [18677, 89], [18900, 45], [18976, 90], [19200, 45], [19276, 90], [19500, 45], [19576, 90], [19800, 45], [19876, 90], [20100, 46], [20175, 91], [20400, 46], [20475, 91], [20700, 46], [20775, 91], [21000, 47], [21074, 92], [21300, 47], [21374, 92], [21600, 48], [21673, 93], [21900, 166], [22200, 166], [22500, 166], [22800, 166], [23100, 166], [23400, 166], [23700, 166], [24000, 165], [24300, 165], [24600, 165], [24900, 165], [25200, 165], [25500, 165], [25800, 165], [26100, 165], [26400, 165], [26700, 166], [27000, 166], [27300, 166], [27600, 166], [27900, 166], [28200, 166], [28500, 165], [28800, 165], [29100, 165], [29400, 164], [29700, 164], [30000, 164], [30300, 164]], "point": [83, 50]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-00.47|+01.11|+00.25", "placeStationary": true, "receptacleObjectId": "CoffeeMachine|-02.00|+00.90|-00.17"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [125, 5, 241, 217], "mask": [[1333, 88], [1631, 92], [1930, 94], [2230, 95], [2530, 95], [2830, 96], [3130, 97], [3430, 97], [3729, 99], [4029, 100], [4329, 100], [4629, 101], [4929, 102], [5229, 102], [5528, 104], [5828, 104], [6128, 105], [6428, 106], [6728, 106], [7028, 107], [7327, 109], [7627, 109], [7927, 110], [8227, 111], [8527, 111], [8827, 112], [9126, 113], [9426, 114], [9726, 115], [10026, 115], [10326, 115], [10626, 116], [10925, 117], [11225, 117], [11526, 116], [11826, 116], [12126, 116], [12426, 115], [12726, 115], [13026, 115], [13326, 115], [13626, 114], [13926, 114], [14226, 114], [14526, 114], [14826, 113], [15126, 113], [15426, 113], [15726, 113], [16027, 111], [16327, 111], [16627, 111], [16927, 111], [17227, 110], [17527, 110], [17827, 110], [18127, 110], [18427, 109], [18727, 109], [19027, 109], [19327, 109], [19627, 108], [19927, 108], [20227, 108], [20527, 108], [20828, 106], [21128, 106], [21428, 106], [21728, 106], [22028, 105], [22328, 105], [22628, 105], [22928, 105], [23228, 104], [23528, 104], [23828, 104], [24128, 104], [24428, 103], [24728, 103], [25028, 103], [25329, 102], [25629, 101], [25929, 101], [26229, 101], [26529, 101], [26828, 101], [27128, 101], [27428, 101], [27728, 101], [28028, 101], [28328, 101], [28628, 101], [28928, 101], [29229, 100], [29529, 99], [29830, 97], [30130, 97], [30430, 96], [30730, 96], [31030, 96], [31330, 96], [31630, 95], [31930, 95], [32230, 95], [32530, 95], [32830, 94], [33130, 94], [33430, 94], [33730, 94], [34030, 93], [34331, 92], [34631, 92], [34931, 92], [35231, 91], [35531, 91], [35831, 91], [36131, 91], [36431, 90], [36731, 90], [37031, 90], [37331, 90], [37631, 89], [37931, 89], [38231, 89], [38531, 89], [38832, 87], [39132, 87], [39432, 87], [39732, 87], [40032, 86], [40332, 86], [40632, 86], [40932, 86], [41232, 85], [41532, 85], [41832, 85], [42132, 85], [42432, 84], [42732, 84], [43032, 84], [43333, 83], [43633, 82], [43933, 82], [44233, 82], [44533, 81], [44833, 82], [45133, 82], [45433, 82], [45733, 83], [46032, 84], [46332, 84], [46632, 84], [46932, 85], [47232, 85], [47532, 85], [47832, 85], [48132, 86], [48432, 86], [48732, 86], [49032, 86], [49332, 87], [49632, 87], [49932, 87], [50232, 87], [50531, 89], [50831, 89], [51131, 89], [51431, 89], [51731, 90], [52031, 90], [52331, 90], [52631, 90], [52931, 91], [53231, 91], [53531, 91], [53831, 91], [54131, 92], [54431, 92], [54731, 92], [55030, 93], [55330, 94], [55630, 1], [55669, 55], [55976, 48], [56281, 43], [56583, 42], [56884, 41], [57184, 41], [57484, 41], [57784, 42], [58084, 42], [58384, 42], [58684, 42], [58984, 43], [59284, 43], [59584, 43], [59884, 43], [60184, 44], [60484, 44], [60784, 44], [61084, 44], [61384, 45], [61684, 45], [61984, 45], [62284, 45], [62584, 44], [62884, 44], [63184, 44], [63484, 43], [63784, 43], [64084, 42], [64384, 42], [64696, 30], [64996, 29]], "point": [183, 110]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan1", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 0.75, "y": 0.9009995, "z": -1.75}, "object_poses": [{"objectName": "Pan_e8d2711b", "position": {"x": 0.355485976, "y": 1.11120951, "z": -0.5065479}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Potato_dc7b7f7e", "position": {"x": -1.935, "y": 0.0784983039, "z": 2.17252541}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_dc7b7f7e", "position": {"x": 0.969296634, "y": 0.9411854, "z": -2.637633}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_30762d8a", "position": {"x": -1.935, "y": 0.0831761956, "z": 1.98115826}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_811f3b02", "position": {"x": -2.01865983, "y": 0.8088245, "z": -1.67392874}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_811f3b02", "position": {"x": 0.8404269, "y": 0.949768364, "z": -2.71444964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": 0.0790954158, "y": 1.11258078, "z": -0.5065479}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": -1.39226007, "y": 0.137981415, "z": -2.55404758}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_816f44ce", "position": {"x": -1.56235909, "y": 0.907706141, "z": -0.9361435}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": -0.225462511, "y": 1.84107757, "z": -2.5463}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": -2.01865983, "y": 0.8203338, "z": -1.32853913}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_c9d70d0f", "position": {"x": -1.77484679, "y": 0.9367496, "z": -2.25621462}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_89238ee6", "position": {"x": -1.58601356, "y": 0.9119485, "z": -2.32982731}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cd066502", "position": {"x": -1.79754007, "y": 0.7708855, "z": -1.32853913}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": 1.47836733, "y": 0.5480585, "z": -2.4593513}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Mug_b5db8038", "position": {"x": -2.08159924, "y": 1.497975, "z": 0.841334343}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": -0.473685682, "y": 1.1066, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7ed44c70", "position": {"x": -0.0361, "y": 0.950499952, "z": -2.3722}, "rotation": {"x": 0.0, "y": 270.000061, "z": 0.0}}, {"objectName": "Kettle_7ed44c70", "position": {"x": 0.355485976, "y": 1.11264133, "z": -0.7593218}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": -0.197295129, "y": 1.11231673, "z": 0.5045478}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": -1.80525851, "y": 0.137228727, "z": -1.74019194}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": 0.324948162, "y": 0.9116288, "z": -2.71444964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": -0.197295129, "y": 1.11116433, "z": -0.7593218}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9960ee75", "position": {"x": -2.0260036, "y": 1.30585468, "z": 0.9250779}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Book_f5814e4b", "position": {"x": 0.155, "y": 1.1, "z": 0.617}, "rotation": {"x": 0.0, "y": 315.826447, "z": 0.0}}, {"objectName": "Glassbottle_c5dfb511", "position": {"x": -1.65551662, "y": 0.137016714, "z": -1.389955}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_c9d70d0f", "position": {"x": 1.09816682, "y": 0.93604964, "z": -2.63763356}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_35d530e8", "position": {"x": 0.711557269, "y": 0.9831652, "z": -2.33036685}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_89238ee6", "position": {"x": -0.8480768, "y": 0.9119485, "z": -2.32982731}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_dc7b7f7e", "position": {"x": -1.98200166, "y": 1.33652139, "z": 1.26004636}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_0fe5b61f", "position": {"x": 1.89316654, "y": 0.554039359, "z": -2.66644835}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Kettle_7ed44c70", "position": {"x": 0.582687557, "y": 0.9117413, "z": -2.637633}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_e8d2711b", "position": {"x": -0.4652, "y": 0.950499952, "z": -2.576}, "rotation": {"x": 0.0, "y": 90.00001, "z": 0.0}}, {"objectName": "Plate_ba789e3c", "position": {"x": 0.563592434, "y": 0.13756156, "z": -2.387}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_811f3b02", "position": {"x": -1.90809989, "y": 0.8088245, "z": -1.41488659}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Vase_d1ae33eb", "position": {"x": -0.473685682, "y": 1.11375952, "z": -0.5065479}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_30762d8a", "position": {"x": -2.03855252, "y": 0.575676143, "z": 1.00317287}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "CreditCard_01a9d33b", "position": {"x": 1.0065074, "y": 0.773219943, "z": -2.37714672}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_1a4cbefa", "position": {"x": -0.059099853, "y": 1.1103971, "z": -0.00100004673}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_c21bc5e7", "position": {"x": 0.2172907, "y": 1.1125288, "z": 0.251773834}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_9e34d9fc", "position": {"x": -1.219, "y": 0.9, "z": -2.356}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_1e920a81", "position": {"x": 1.0065074, "y": 0.791223764, "z": -2.29119}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_2c38299b", "position": {"x": -1.58601356, "y": 1.01568532, "z": -2.25301242}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_77fad61b", "position": {"x": -1.00811148, "y": 0.13488245, "z": -2.40271449}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_5ffb3206", "position": {"x": -1.71521759, "y": 0.127596617, "z": 0.158000946}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_9960ee75", "position": {"x": -1.51231253, "y": 1.654938, "z": -2.64124084}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b714915a", "position": {"x": -0.197295129, "y": 1.10790622, "z": -0.253773928}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_816f44ce", "position": {"x": -0.473685682, "y": 1.10790622, "z": -0.7593218}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e166a32f", "position": {"x": -1.80697548, "y": 0.973761737, "z": -0.942994237}, "rotation": {"x": 359.9812, "y": 256.7714, "z": 0.0007696777}}, {"objectName": "ButterKnife_f63cc56c", "position": {"x": -1.96337986, "y": 0.7697982, "z": -1.50123394}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_a0b666be", "position": {"x": -0.059099853, "y": 1.16217768, "z": -0.253773928}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_56e11318", "position": {"x": 1.5820663, "y": 0.5526228, "z": -2.66644645}, "rotation": {"x": -3.41504619e-06, "y": 180.00032, "z": -1.70754829e-05}}, {"objectName": "Spoon_cd066502", "position": {"x": -1.79754019, "y": 0.7708855, "z": -1.76027608}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_b5db8038", "position": {"x": -2.12060976, "y": 0.793513, "z": 1.00317216}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1490291456, "scene_num": 1}, "task_id": "trial_T20190907_222924_821086", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A320QA9HJFUOZO_34HJIJKLP8DMBSBTA3HEGNDSPHSV44", "high_descs": ["Turn around and walk through the kitchen, turning right to face the kitchen island opposite the sink", "Pick up the white mug from the kitchen island", "Turn right and walk over to the microwave above the oven", "Heat the mug in the microwave then remove it", "Turn right and walk over to the coffee maker on the counter", "Put the mug in the coffee maker"], "task_desc": "Put the heated mug in the coffee maker", "votes": [1, 1]}, {"assignment_id": "AJQGWGESKQT4Y_3EFE17QCRFM0VCIKVFWSZP2PVINSHL", "high_descs": ["Turn around and cross the room and then turn right and turn right again to face the mug.", "Pick the white mug up from the counter.", "Go right and then left to turn to the right to face the microwave above the stove.", "Put the mug in the microwave and close the door, turn it on, and then open the door and take the mug out and close the door again.", "Go right and then right again and then turn left to face the coffee maker.", "Put the mug under the coffee maker on the counter."], "task_desc": "Put a warmed mug under a coffee maker.", "votes": [1, 1]}, {"assignment_id": "A1NWXL4QO30M8U_3M23Y66PO5OKYNY3ZW5O2YDZM5C6S5", "high_descs": ["Turn around and walk across the room to the sink, then turn right and take two steps, then turn right and face the counter. ", "Pick up the white coffee cup on the counter. ", "Turn right and walk across the room, then turn left and take two steps, then turn right and walk to the stove. ", "Look up and open the microwave, put the coffee cup inside and close the door, turn on the microwave for two seconds, then open the microwave and pick up the coffee cup.", "Turn right and walk across the room, then turn right and take two steps, then turn left and face the coffee maker. ", "Put the coffee cup down on the coffee maker. "], "task_desc": "To heat up the coffee cup.", "votes": [1, 1]}]}}