{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000155.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000156.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000157.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000158.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000159.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000160.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000162.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000163.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000289.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000290.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000291.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000293.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000294.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000295.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000330.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000331.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000332.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000333.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 62}, {"high_idx": 8, "image_name": "000000335.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000336.png", "low_idx": 63}, {"high_idx": 8, "image_name": "000000337.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000338.png", "low_idx": 64}, {"high_idx": 8, "image_name": "000000339.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000340.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000341.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000342.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000343.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000344.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000345.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000346.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000347.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000348.png", "low_idx": 65}, {"high_idx": 8, "image_name": "000000349.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000350.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000351.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000352.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000353.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000354.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000355.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000356.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000357.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000358.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000359.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000360.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000361.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000362.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000363.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000364.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000365.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000366.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000367.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000368.png", "low_idx": 67}, {"high_idx": 9, "image_name": "000000369.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000370.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000371.png", "low_idx": 68}, {"high_idx": 9, "image_name": "000000372.png", "low_idx": 68}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-6|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [-1.029323816, -1.029323816, -6.22003556, -6.22003556, 3.7937488, 3.7937488]], "coordinateReceptacleObjectId": ["CounterTop", [-1.076, -1.076, -4.3592, -4.3592, 3.806, 3.806]], "forceVisible": true, "objectId": "Knife|-00.26|+00.95|-01.56"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-11|-6|0|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-11.01242064, -11.01242064, -3.1418892, -3.1418892, 3.0185678, 3.0185678]], "forceVisible": true, "objectId": "Bread|-02.75|+00.75|-00.79"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "diningtable"]}, "high_idx": 4, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [-1.029323816, -1.029323816, -6.22003556, -6.22003556, 3.7937488, 3.7937488]], "coordinateReceptacleObjectId": ["DiningTable", [-11.476, -11.476, -2.024, -2.024, 2.708, 2.708]], "forceVisible": true, "objectId": "Knife|-00.26|+00.95|-01.56", "receptacleObjectId": "DiningTable|-02.87|+00.68|-00.51"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-11.01242064, -11.01242064, -3.1418892, -3.1418892, 3.0185678, 3.0185678]], "coordinateReceptacleObjectId": ["DiningTable", [-11.476, -11.476, -2.024, -2.024, 2.708, 2.708]], "forceVisible": true, "objectId": "Bread|-02.75|+00.75|-00.79|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-10|-9|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "fridge"]}, "high_idx": 9, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-11.01242064, -11.01242064, -3.1418892, -3.1418892, 3.0185678, 3.0185678]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-12.75556944, -12.75556944, -8.74, -8.74, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-02.75|+00.75|-00.79|BreadSliced_1", "receptacleObjectId": "<PERSON><PERSON>|-03.19|+00.00|-02.19"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 10, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|-00.26|+00.95|-01.56"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [162, 112, 166, 164], "mask": [[33463, 1], [33762, 2], [34062, 3], [34362, 3], [34662, 3], [34962, 3], [35262, 3], [35562, 3], [35862, 3], [36162, 4], [36462, 4], [36762, 4], [37062, 4], [37363, 3], [37663, 3], [37963, 3], [38263, 3], [38563, 3], [38863, 3], [39163, 3], [39463, 3], [39763, 3], [40063, 3], [40363, 2], [40663, 2], [40964, 1], [41264, 2], [41564, 2], [41864, 2], [42164, 2], [42464, 2], [42764, 2], [43064, 2], [43365, 1], [43665, 1], [43965, 1], [44265, 1], [44565, 1], [44865, 1], [45165, 1], [45465, 1], [45765, 1], [46065, 2], [46365, 2], [46665, 2], [46966, 1], [47266, 1], [47566, 1], [47866, 1], [48166, 1], [48466, 1], [48766, 1], [49066, 1]], "point": [164, 137]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-02.75|+00.75|-00.79"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [117, 147, 185, 202], "mask": [[43925, 13], [44223, 19], [44522, 23], [44820, 27], [45120, 29], [45419, 33], [45718, 35], [46018, 37], [46317, 39], [46617, 41], [46917, 42], [47217, 44], [47517, 45], [47818, 46], [48118, 47], [48418, 48], [48719, 49], [49019, 50], [49320, 50], [49620, 51], [49921, 51], [50221, 52], [50522, 52], [50823, 52], [51123, 53], [51424, 53], [51725, 53], [52026, 52], [52327, 52], [52628, 52], [52929, 52], [53230, 51], [53532, 50], [53833, 49], [54134, 49], [54435, 48], [54737, 47], [55038, 46], [55339, 46], [55641, 44], [55942, 43], [56243, 42], [56544, 42], [56846, 40], [57147, 39], [57448, 38], [57749, 37], [58051, 34], [58352, 33], [58654, 30], [58956, 27], [59258, 24], [59560, 21], [59862, 18], [60166, 12], [60472, 4]], "point": [151, 173]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|-00.26|+00.95|-01.56", "placeStationary": true, "receptacleObjectId": "DiningTable|-02.87|+00.68|-00.51"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 75, 252, 256], "mask": [[22318, 30], [22618, 40], [22918, 45], [23218, 50], [23518, 55], [23819, 58], [24119, 50], [24173, 7], [24419, 49], [24475, 8], [24719, 47], [24778, 8], [25020, 45], [25077, 11], [25320, 44], [25376, 15], [25620, 43], [25675, 19], [25921, 43], [25973, 23], [26221, 43], [26272, 27], [26522, 40], [26565, 3], [26571, 30], [26823, 37], [26863, 40], [27125, 33], [27161, 44], [27427, 29], [27459, 47], [27655, 2], [27729, 25], [27756, 52], [27953, 4], [28030, 21], [28054, 56], [28251, 6], [28332, 18], [28353, 58], [28549, 9], [28634, 14], [28652, 12], [28667, 46], [28847, 11], [28935, 12], [28950, 13], [28968, 47], [29145, 14], [29237, 8], [29249, 13], [29269, 47], [29443, 17], [29539, 4], [29547, 15], [29569, 49], [29742, 19], [29763, 1], [29841, 1], [29845, 16], [29870, 50], [30040, 25], [30144, 17], [30170, 51], [30339, 27], [30444, 17], [30470, 52], [30638, 30], [30746, 15], [30770, 53], [30937, 32], [31047, 14], [31070, 35], [31106, 18], [31235, 35], [31348, 13], [31370, 34], [31406, 19], [31534, 38], [31648, 13], [31669, 35], [31706, 1], [31708, 18], [31833, 40], [31948, 14], [31969, 35], [32005, 2], [32008, 19], [32132, 43], [32248, 14], [32268, 35], [32305, 1], [32308, 20], [32430, 46], [32548, 16], [32566, 37], [32605, 1], [32608, 21], [32729, 48], [32848, 55], [32905, 1], [32907, 24], [33028, 49], [33148, 55], [33209, 23], [33327, 51], [33448, 55], [33509, 24], [33625, 54], [33747, 57], [33809, 25], [33924, 56], [34046, 60], [34108, 27], [34223, 58], [34344, 63], [34408, 28], [34522, 60], [34642, 65], [34708, 29], [34821, 62], [34941, 66], [35009, 28], [35120, 65], [35239, 68], [35309, 29], [35419, 67], [35536, 72], [35609, 30], [35719, 68], [35834, 74], [35909, 30], [36018, 71], [36133, 75], [36210, 30], [36317, 64], [36389, 1], [36431, 77], [36510, 30], [36616, 59], [36729, 79], [36810, 31], [36915, 55], [37027, 81], [37111, 31], [37215, 52], [37326, 82], [37411, 31], [37514, 50], [37624, 84], [37711, 32], [37813, 49], [37922, 86], [38012, 31], [38112, 48], [38197, 1], [38222, 87], [38312, 32], [38411, 47], [38498, 1], [38521, 88], [38612, 33], [38711, 45], [38798, 3], [38819, 90], [38913, 32], [39010, 43], [39098, 4], [39117, 92], [39213, 33], [39309, 43], [39398, 5], [39416, 64], [39487, 22], [39513, 33], [39608, 42], [39697, 8], [39714, 62], [39792, 18], [39813, 34], [39908, 40], [39997, 9], [40012, 61], [40095, 53], [40207, 40], [40296, 11], [40311, 60], [40398, 50], [40506, 39], [40596, 73], [40700, 48], [40806, 38], [40895, 73], [41001, 48], [41106, 36], [41194, 73], [41302, 47], [41405, 36], [41493, 73], [41604, 45], [41705, 34], [41792, 73], [41904, 45], [42004, 34], [42091, 74], [42205, 44], [42304, 33], [42390, 74], [42506, 44], [42604, 32], [42689, 75], [42807, 43], [42903, 32], [42987, 34], [43033, 31], [43107, 43], [43203, 31], [43286, 33], [43336, 28], [43407, 43], [43502, 31], [43584, 33], [43639, 25], [43708, 42], [43802, 30], [43883, 33], [43942, 22], [44008, 43], [44102, 29], [44181, 34], [44243, 21], [44308, 43], [44401, 29], [44480, 34], [44546, 18], [44608, 43], [44701, 29], [44778, 36], [44848, 17], [44908, 43], [45001, 28], [45076, 37], [45149, 16], [45208, 44], [45300, 28], [45374, 39], [45451, 15], [45507, 45], [45600, 28], [45673, 40], [45753, 13], [45807, 45], [45900, 27], [45971, 42], [46054, 13], [46107, 45], [46200, 27], [46270, 43], [46355, 13], [46406, 46], [46500, 27], [46568, 45], [46657, 12], [46705, 48], [46800, 26], [46866, 47], [46959, 11], [47004, 49], [47100, 26], [47164, 50], [47260, 11], [47303, 50], [47400, 26], [47463, 51], [47561, 12], [47602, 51], [47700, 26], [47761, 54], [47863, 12], [47900, 52], [48000, 27], [48059, 56], [48164, 14], [48198, 54], [48300, 27], [48357, 59], [48466, 15], [48495, 57], [48600, 27], [48655, 61], [48767, 85], [48900, 28], [48952, 55], [49011, 6], [49068, 84], [49200, 29], [49249, 57], [49311, 6], [49369, 83], [49500, 30], [49545, 59], [49609, 9], [49671, 80], [49800, 32], [49840, 63], [49908, 11], [49972, 79], [50100, 101], [50206, 14], [50273, 78], [50400, 99], [50505, 15], [50574, 77], [50700, 98], [50803, 18], [50875, 76], [51000, 96], [51101, 21], [51176, 74], [51300, 95], [51400, 23], [51476, 74], [51600, 93], [51698, 26], [51777, 73], [51900, 91], [51997, 28], [52078, 72], [52200, 90], [52295, 31], [52379, 71], [52500, 89], [52593, 35], [52680, 70], [52800, 88], [52892, 37], [52980, 69], [53100, 87], [53189, 41], [53281, 68], [53400, 85], [53488, 43], [53582, 67], [53700, 84], [53786, 46], [53882, 67], [54000, 82], [54085, 48], [54183, 66], [54300, 81], [54384, 51], [54483, 65], [54600, 79], [54682, 54], [54784, 64], [54900, 78], [54981, 56], [55084, 64], [55200, 77], [55280, 59], [55384, 63], [55500, 75], [55578, 62], [55685, 62], [55800, 74], [55877, 64], [55985, 61], [56100, 72], [56176, 67], [56285, 61], [56401, 70], [56474, 70], [56585, 60], [56701, 59], [56765, 5], [56773, 72], [56886, 59], [57001, 57], [57072, 74], [57186, 58], [57302, 54], [57370, 78], [57486, 57], [57602, 52], [57669, 80], [57786, 57], [57903, 50], [57970, 80], [58085, 57], [58203, 48], [58270, 82], [58385, 56], [58504, 45], [58570, 83], [58684, 57], [58804, 43], [58870, 85], [58983, 57], [59105, 41], [59170, 87], [59283, 57], [59405, 41], [59469, 90], [59581, 58], [59705, 42], [59767, 94], [59880, 58], [60006, 42], [60066, 98], [60179, 59], [60306, 43], [60364, 105], [60477, 60], [60607, 43], [60662, 174], [60907, 44], [60960, 176], [61208, 44], [61258, 177], [61508, 44], [61556, 179], [61809, 225], [62109, 224], [62410, 223], [62710, 222], [63011, 220], [63312, 219], [63612, 218], [63913, 216], [64215, 213], [64516, 210], [64817, 208], [65118, 206], [65419, 204], [65720, 201], [66021, 199], [66322, 197], [66623, 195], [66924, 192], [67225, 190], [67526, 188], [67827, 186], [68128, 183], [68429, 181], [68730, 179], [69031, 177], [69333, 174], [69634, 171], [69935, 169], [70236, 167], [70537, 164], [70839, 160], [71142, 154], [71444, 150], [71746, 146], [72048, 141], [72350, 137], [72653, 131], [72955, 127], [73257, 123], [73559, 118], [73862, 113], [74164, 108], [74466, 104], [74768, 99], [75071, 94], [75373, 90], [75680, 75], [75988, 59], [76295, 44], [76604, 28]], "point": [119, 167]}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-02.75|+00.75|-00.79|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [131, 155, 159, 183], "mask": [[46352, 3], [46651, 6], [46949, 10], [47247, 13], [47546, 12], [47844, 12], [48143, 11], [48442, 10], [48740, 11], [49039, 11], [49338, 10], [49637, 10], [49936, 9], [50235, 9], [50534, 9], [50833, 9], [51132, 9], [51432, 8], [51731, 8], [52031, 7], [52331, 7], [52631, 6], [52931, 6], [53231, 5], [53531, 5], [53832, 4], [54133, 3], [54435, 1], [54736, 1]], "point": [144, 166]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-02.75|+00.75|-00.79|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 68], [19966, 134], [20192, 67], [20268, 132], [20492, 66], [20569, 131], [20791, 66], [20870, 130], [21091, 65], [21170, 130], [21391, 65], [21471, 129], [21690, 65], [21771, 129], [21990, 65], [22072, 128], [22290, 65], [22372, 128], [22589, 65], [22673, 127], [22889, 65], [22973, 127], [23189, 65], [23274, 125], [23488, 66], [23574, 125], [23788, 65], [23874, 124], [24088, 65], [24175, 123], [24387, 66], [24475, 123], [24687, 66], [24775, 122], [24987, 65], [25075, 122], [25286, 66], [25375, 121], [25586, 66], [25675, 121], [25886, 66], [25976, 119], [26185, 67], [26276, 119], [26485, 68], [26576, 118], [26785, 68], [26876, 118], [27084, 70], [27175, 118], [27384, 70], [27475, 118], [27684, 71], [27775, 118], [27984, 72], [28075, 117], [28283, 73], [28375, 117], [28583, 74], [28674, 117], [28883, 75], [28973, 118], [29182, 78], [29272, 118], [29482, 79], [29571, 119], [29782, 207], [30081, 208], [30381, 208], [30681, 207], [30980, 208], [31280, 207], [31580, 207], [31879, 207], [32179, 207], [32479, 206], [32778, 207], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44466, 50], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 37], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 68], [19966, 134], [20192, 67], [20268, 132], [20492, 66], [20569, 131], [20791, 66], [20870, 130], [21091, 65], [21170, 130], [21391, 65], [21471, 129], [21690, 65], [21771, 129], [21990, 65], [22072, 128], [22290, 65], [22372, 128], [22589, 65], [22673, 127], [22889, 65], [22973, 127], [23189, 65], [23274, 125], [23488, 66], [23574, 125], [23788, 65], [23875, 123], [24088, 65], [24175, 123], [24387, 66], [24475, 123], [24687, 66], [24776, 121], [24987, 65], [25076, 121], [25286, 66], [25376, 120], [25586, 66], [25676, 120], [25886, 66], [25977, 118], [26185, 67], [26277, 118], [26485, 68], [26577, 117], [26785, 68], [26877, 117], [27084, 70], [27177, 116], [27384, 70], [27476, 117], [27684, 70], [27776, 117], [27984, 70], [28076, 116], [28283, 71], [28376, 116], [28583, 71], [28676, 115], [28883, 71], [28976, 115], [29182, 72], [29275, 115], [29482, 72], [29575, 115], [29782, 72], [29875, 114], [30081, 73], [30175, 114], [30381, 73], [30475, 114], [30681, 73], [30775, 113], [30980, 74], [31075, 113], [31280, 74], [31374, 113], [31580, 74], [31674, 113], [31879, 75], [31973, 113], [32179, 75], [32273, 113], [32479, 75], [32572, 113], [32778, 76], [32872, 113], [33078, 76], [33172, 112], [33378, 77], [33471, 113], [33677, 79], [33770, 114], [33977, 80], [34068, 115], [34277, 83], [34365, 118], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44466, 50], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 37], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 7}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 7}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-0.576, -0.576, -3.632, -3.632, 3.615618228, 3.615618228]], "forceVisible": true, "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 7}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [95, 46, 299, 136], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17796, 204], [18096, 204], [18396, 204], [18696, 204], [18996, 204], [19296, 204], [19597, 203], [19897, 203], [20197, 203], [20497, 203], [20797, 203], [21097, 203], [21397, 203], [21698, 202], [21998, 202], [22298, 202], [22598, 202], [22898, 202], [23198, 201], [23499, 200], [23799, 199], [24099, 199], [24399, 199], [24699, 198], [24999, 198], [25300, 196], [25600, 196], [25900, 195], [26200, 195], [26500, 194], [26800, 194], [27100, 193], [27401, 192], [27701, 192], [28001, 191], [28301, 191], [28601, 190], [28901, 190], [29202, 188], [29502, 188], [29802, 187], [30102, 187], [30402, 187], [30702, 186], [31002, 186], [31303, 184], [31603, 184], [31903, 183], [32203, 183], [32503, 182], [32803, 182], [33104, 180], [33404, 180], [33704, 180], [34004, 179], [34304, 179], [34604, 178], [34904, 178], [35205, 176], [35505, 176], [35805, 175], [36105, 175], [36405, 175], [36705, 174], [37006, 173], [37306, 172], [37606, 172], [37906, 171], [38206, 171], [38515, 158], [38815, 158], [39115, 158], [39415, 158], [39715, 158], [40015, 157], [40315, 157], [40616, 155]], "point": [197, 90]}}, "high_idx": 7}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-02.75|+00.75|-00.79|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [154, 73, 176, 115], "mask": [[21760, 7], [22058, 12], [22356, 15], [22655, 17], [22954, 19], [23254, 20], [23554, 20], [23854, 21], [24154, 21], [24454, 21], [24754, 22], [25054, 22], [25354, 22], [25654, 22], [25954, 23], [26254, 23], [26554, 23], [26854, 23], [27154, 23], [27454, 22], [27754, 22], [28054, 22], [28354, 22], [28654, 22], [28954, 22], [29254, 21], [29554, 21], [29854, 21], [30154, 21], [30454, 21], [30754, 21], [31054, 21], [31354, 20], [31654, 20], [31954, 19], [32254, 19], [32554, 18], [32854, 18], [33154, 18], [33455, 16], [33756, 14], [34057, 11], [34360, 5]], "point": [165, 93]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.14|+00.90|-00.91"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [59, 46, 299, 242], "mask": [[13597, 203], [13896, 204], [14196, 204], [14496, 204], [14795, 205], [15095, 205], [15395, 205], [15695, 205], [15995, 205], [16295, 205], [16595, 205], [16895, 205], [17195, 205], [17495, 205], [17795, 205], [18094, 206], [18394, 206], [18694, 206], [18993, 207], [19293, 207], [19593, 207], [19892, 68], [19966, 134], [20192, 67], [20268, 132], [20492, 66], [20569, 131], [20791, 66], [20870, 130], [21091, 65], [21170, 130], [21391, 65], [21471, 129], [21690, 65], [21771, 129], [21990, 65], [22072, 128], [22290, 65], [22372, 128], [22589, 65], [22673, 127], [22889, 65], [22973, 127], [23189, 65], [23274, 125], [23488, 66], [23574, 125], [23788, 65], [23874, 124], [24088, 65], [24175, 123], [24387, 66], [24475, 123], [24687, 66], [24775, 122], [24987, 65], [25075, 122], [25286, 66], [25375, 121], [25586, 66], [25675, 121], [25886, 66], [25976, 119], [26185, 67], [26276, 119], [26485, 68], [26576, 118], [26785, 68], [26876, 118], [27084, 70], [27175, 118], [27384, 70], [27475, 118], [27684, 71], [27775, 118], [27984, 72], [28075, 117], [28283, 73], [28375, 117], [28583, 74], [28674, 117], [28883, 75], [28973, 118], [29182, 78], [29272, 118], [29482, 79], [29571, 119], [29782, 207], [30081, 208], [30381, 208], [30681, 207], [30980, 208], [31280, 207], [31580, 207], [31879, 207], [32179, 207], [32479, 206], [32778, 207], [33078, 206], [33378, 206], [33677, 207], [33977, 206], [34277, 206], [34576, 206], [34876, 206], [35176, 205], [35475, 206], [35775, 205], [36075, 205], [36374, 206], [36674, 205], [36974, 205], [37273, 205], [37573, 205], [37873, 204], [38172, 205], [38472, 201], [38772, 201], [39071, 48], [39239, 34], [39371, 48], [39539, 34], [39671, 48], [39840, 33], [39970, 48], [40140, 32], [40270, 48], [40440, 32], [40570, 48], [40741, 30], [40869, 49], [41169, 49], [41469, 49], [41768, 50], [42068, 49], [42368, 49], [42667, 50], [42967, 50], [43267, 50], [43566, 51], [43866, 51], [44166, 51], [44466, 50], [44765, 51], [45065, 51], [45365, 51], [45664, 52], [45964, 52], [46264, 52], [46563, 53], [46863, 52], [47163, 52], [47462, 53], [47762, 53], [48062, 53], [48361, 54], [48661, 54], [48961, 53], [49260, 54], [49560, 54], [49860, 54], [50159, 55], [50460, 54], [50760, 54], [51060, 54], [51361, 52], [51661, 52], [51961, 52], [52262, 51], [52563, 50], [52864, 49], [53166, 47], [53468, 45], [53769, 43], [54071, 41], [54372, 40], [54674, 38], [54974, 38], [55275, 37], [55575, 37], [55875, 37], [56176, 35], [56476, 35], [56776, 35], [57077, 34], [57377, 34], [57677, 34], [57978, 33], [58278, 32], [58578, 32], [58879, 31], [59179, 31], [59479, 31], [59780, 30], [60080, 30], [60380, 30], [60681, 28], [60981, 28], [61281, 28], [61582, 27], [61882, 27], [62182, 27], [62483, 26], [62783, 26], [63083, 25], [63384, 24], [63684, 24], [63984, 24], [64285, 23], [64585, 23], [64885, 23], [65186, 21], [65486, 21], [65786, 21], [66087, 20], [66387, 20], [66687, 20], [66988, 19], [67288, 19], [67587, 19], [67887, 19], [68187, 19], [68487, 19], [68787, 19], [69086, 20], [69386, 20], [69686, 20], [69986, 22], [70286, 25], [70586, 26], [70885, 20], [70908, 4], [71185, 20], [71208, 5], [71486, 19], [71509, 3], [71786, 19], [72087, 17], [72387, 17]], "point": [179, 129]}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-03.19|+00.00|-02.19"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 236], "mask": [[0, 48300], [48301, 299], [48602, 298], [48903, 297], [49204, 296], [49505, 295], [49806, 294], [50107, 293], [50409, 291], [50710, 290], [51011, 289], [51312, 288], [51613, 287], [51914, 286], [52215, 285], [52516, 284], [52817, 283], [53118, 282], [53419, 281], [53720, 280], [54021, 279], [54323, 277], [54624, 276], [54925, 275], [55226, 274], [55527, 273], [55828, 272], [56129, 271], [56430, 270], [56731, 269], [57032, 268], [57333, 267], [57634, 266], [57935, 265], [58237, 263], [58538, 262], [58839, 261], [59140, 258], [59441, 256], [59742, 253], [60043, 251], [60344, 248], [60645, 246], [60946, 244], [61247, 241], [61548, 239], [61849, 236], [62151, 233], [62452, 230], [62753, 228], [63054, 225], [63355, 223], [63656, 221], [63957, 218], [64258, 216], [64559, 213], [64860, 211], [65161, 208], [65462, 206], [65763, 203], [66064, 201], [66366, 198], [66667, 195], [66968, 193], [67269, 190], [67570, 188], [67871, 185], [68172, 183], [68473, 180], [68774, 178], [69075, 176], [69376, 173], [69677, 171], [69978, 168], [70280, 165], [70581, 162]], "point": [149, 117]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-02.75|+00.75|-00.79|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-03.19|+00.00|-02.19"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 2605], [2616, 286], [2919, 281], [3222, 277], [3523, 275], [3824, 274], [4125, 272], [4426, 271], [4726, 271], [5027, 270], [5329, 268], [5631, 266], [5932, 265], [6226, 3], [6232, 265], [6526, 3], [6532, 265], [6826, 3], [6832, 265], [7125, 3], [7132, 265], [7425, 3], [7431, 265], [7725, 2], [7731, 265], [8024, 2], [8030, 266], [8324, 2], [8329, 267], [8624, 1], [8629, 267], [8923, 2], [8928, 268], [9223, 1], [9227, 269], [9527, 268], [9826, 269], [10125, 270], [10425, 270], [10724, 271], [11023, 272], [11322, 273], [11621, 273], [11920, 274], [12220, 274], [12520, 274], [12819, 275], [13119, 275], [13419, 275], [13718, 276], [14018, 277], [14318, 277], [14617, 279], [14917, 280], [15216, 283], [15515, 286], [15813, 293], [16108, 605], [16717, 293], [17020, 288], [17322, 284], [17624, 281], [17925, 279], [18226, 277], [18526, 276], [18827, 275], [19127, 274], [19428, 273], [19728, 272], [20028, 272], [20328, 272], [20628, 272], [20928, 272], [21228, 272], [21528, 272], [21828, 273], [22127, 274], [22427, 275], [22726, 276], [23026, 277], [23325, 279], [23624, 281], [23922, 285], [24221, 288], [24519, 13465], [37992, 292], [38310, 273], [38631, 252], [38931, 252], [39231, 252], [39532, 252], [39832, 252], [40132, 252], [40432, 252], [40732, 252], [41032, 253], [41331, 255], [41631, 255], [41931, 256], [42230, 257], [42530, 258], [42829, 260], [43129, 261], [43428, 263], [43727, 266], [44026, 268], [44325, 271], [44623, 275], [44922, 279], [45219, 285], [45516, 21323], [66840, 299], [67140, 299], [67440, 299], [67740, 300], [68041, 299], [68341, 299], [68641, 299], [68941, 299], [69241, 299], [69542, 299], [69842, 296], [70142, 295], [70442, 295], [70742, 143], [71042, 143], [71343, 142], [71643, 142], [71943, 142], [72243, 142], [72543, 141], [72843, 141], [73144, 140], [73444, 140], [73744, 140], [74044, 140], [74344, 140], [74644, 140], [74945, 138], [75245, 138], [75545, 138], [75845, 138], [76145, 138], [76445, 138], [76746, 137], [77046, 137], [77346, 136], [77646, 136], [77946, 136], [78246, 136], [78547, 135], [78847, 135], [79147, 135], [79447, 135], [79747, 135], [80048, 133], [80348, 133], [80648, 133], [80948, 133], [81248, 133], [81548, 133], [81849, 132], [82149, 132], [82449, 131], [82749, 131], [83049, 131], [83349, 131], [83650, 130], [83950, 130], [84250, 130], [84550, 130], [84850, 129], [85150, 129], [85451, 128], [85751, 126], [86051, 124], [86351, 124], [86651, 124], [86951, 124], [87252, 122], [87552, 48], [87607, 67], [87852, 48], [87926, 48], [88152, 48], [88245, 28], [88452, 48], [88753, 47], [89053, 47], [89353, 47], [89653, 47], [89953, 47]], "point": [149, 149]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-03.19|+00.00|-02.19"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 2605], [2616, 286], [2919, 281], [3222, 277], [3523, 275], [3824, 274], [4125, 272], [4426, 271], [4726, 271], [5027, 270], [5329, 268], [5631, 266], [5932, 265], [6226, 3], [6232, 265], [6526, 3], [6532, 265], [6826, 3], [6832, 265], [7125, 3], [7132, 265], [7425, 3], [7431, 265], [7725, 2], [7731, 265], [8024, 2], [8030, 266], [8324, 2], [8329, 267], [8624, 1], [8629, 267], [8923, 2], [8928, 268], [9223, 1], [9227, 269], [9527, 268], [9826, 269], [10125, 270], [10425, 270], [10724, 271], [11023, 272], [11322, 273], [11621, 273], [11920, 274], [12220, 274], [12520, 274], [12819, 275], [13119, 275], [13419, 275], [13718, 276], [14018, 277], [14318, 277], [14617, 279], [14917, 280], [15216, 283], [15515, 286], [15813, 293], [16108, 605], [16717, 293], [17020, 288], [17322, 284], [17624, 281], [17925, 279], [18226, 277], [18526, 276], [18827, 275], [19127, 274], [19428, 273], [19728, 272], [20028, 272], [20328, 272], [20628, 272], [20928, 272], [21228, 272], [21528, 272], [21828, 273], [22127, 274], [22427, 275], [22726, 276], [23026, 277], [23325, 279], [23624, 281], [23922, 278], [24201, 6], [24221, 264], [24519, 260], [24819, 256], [25120, 252], [25421, 250], [25722, 248], [26023, 246], [26324, 244], [26624, 244], [26925, 242], [27225, 242], [27525, 242], [27825, 242], [28126, 241], [28426, 241], [28726, 241], [29026, 241], [29326, 241], [29626, 241], [29926, 241], [30225, 242], [30525, 243], [30824, 244], [31123, 246], [31422, 248], [31721, 250], [32019, 257], [32316, 5668], [37992, 292], [38310, 273], [38631, 252], [38931, 252], [39231, 252], [39532, 252], [39832, 252], [40132, 252], [40432, 252], [40732, 252], [41032, 253], [41331, 255], [41631, 255], [41931, 256], [42230, 257], [42530, 258], [42829, 260], [43129, 261], [43428, 263], [43727, 266], [44026, 268], [44325, 271], [44623, 275], [44922, 279], [45219, 285], [45516, 21323], [66840, 299], [67140, 299], [67440, 299], [67740, 300], [68041, 299], [68341, 299], [68641, 299], [68941, 299], [69241, 299], [69542, 299], [69842, 296], [70142, 295], [70442, 295], [70742, 143], [71042, 143], [71343, 142], [71643, 142], [71943, 142], [72243, 142], [72543, 141], [72843, 141], [73144, 140], [73444, 140], [73744, 140], [74044, 140], [74344, 140], [74644, 140], [74945, 138], [75245, 138], [75545, 138], [75845, 138], [76145, 138], [76445, 138], [76746, 137], [77046, 137], [77346, 136], [77646, 136], [77946, 136], [78246, 136], [78547, 135], [78847, 135], [79147, 135], [79447, 135], [79747, 135], [80048, 133], [80348, 133], [80648, 133], [80948, 133], [81248, 133], [81548, 133], [81849, 132], [82149, 132], [82449, 131], [82749, 131], [83049, 131], [83349, 131], [83650, 130], [83950, 130], [84250, 130], [84550, 130], [84850, 129], [85150, 129], [85451, 128], [85751, 126], [86051, 124], [86351, 124], [86651, 124], [86951, 124], [87252, 122], [87552, 48], [87607, 67], [87852, 48], [87926, 48], [88152, 48], [88245, 28], [88452, 48], [88753, 47], [89053, 47], [89353, 47], [89653, 47], [89953, 47]], "point": [149, 149]}}, "high_idx": 9}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan19", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -2.5, "y": 0.9016907, "z": -3.0}, "object_poses": [{"objectName": "ButterKnife_a7531534", "position": {"x": -0.41940096, "y": 0.91604203, "z": -0.542100668}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -2.97849512, "y": 0.7287701, "z": -0.7854718}, "rotation": {"x": 0.0, "y": 315.000031, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -2.75310469, "y": 0.7287701, "z": -0.206528127}, "rotation": {"x": 0.0, "y": 135.000046, "z": 0.0}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -2.43923759, "y": 0.717515945, "z": -0.371000051}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_b0ce4484", "position": {"x": -2.66471648, "y": 0.752868235, "z": -0.29491657}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Egg_b0ce4484", "position": {"x": -1.56911922, "y": 0.8266152, "z": -3.83288431}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_410beacd", "position": {"x": -0.09370521, "y": 1.29474783, "z": -0.6200673}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -3.13095522, "y": 0.6393903, "z": -2.002616}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -2.581425, "y": 0.715117753, "z": -0.621000051}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_46bffc5f", "position": {"x": -0.257330954, "y": 0.9484372, "z": -1.55500889}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -0.41940093, "y": 0.9155963, "z": -1.61635566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_697b561f", "position": {"x": -0.4135693, "y": 0.07809031, "z": -0.9157784}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_697b561f", "position": {"x": -0.411939174, "y": 0.0796982646, "z": -3.348648}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -0.412048727, "y": 0.7431938, "z": -2.8030715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -0.5006436, "y": 0.7431938, "z": -1.17647409}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -0.500435948, "y": 0.9123061, "z": -0.524823248}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_77e17ce3", "position": {"x": -2.75310516, "y": 0.75464195, "z": -0.7854723}, "rotation": {"x": 0.0, "y": 135.000092, "z": 0.0}}, {"objectName": "Bread_77e17ce3", "position": {"x": -3.30916429, "y": 0.954571664, "z": -2.92600584}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -3.23444986, "y": 1.00850379, "z": -2.06418562}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -3.07920742, "y": 1.05614364, "z": -2.06418514}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_fd5c5c80", "position": {"x": -0.1653, "y": 0.9759, "z": -2.0309}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Egg_b0ce4484", "position": {"x": -1.70727491, "y": 0.826456964, "z": -3.7472}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_410beacd", "position": {"x": -1.00880063, "y": 1.29323149, "z": -3.88892531}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_77e17ce3", "position": {"x": -3.15527177, "y": 0.75464195, "z": -0.6086949}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Fork_1cd5bb8d", "position": {"x": -0.5006436, "y": 0.7474362, "z": -2.8583312}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_4966d0cc", "position": {"x": -0.6509867, "y": 0.91207397, "z": -3.95224237}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Potato_2b74ee3a", "position": {"x": -0.127360374, "y": 1.05657792, "z": -0.8022114}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_908150ce", "position": {"x": -3.14090919, "y": 1.0015142, "z": -3.22723937}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_f34461c5", "position": {"x": -0.284036428, "y": 0.129380241, "z": -0.0300000049}, "rotation": {"x": 0.0, "y": 269.999969, "z": 0.0}}, {"objectName": "Apple_1634f71e", "position": {"x": -0.8549319, "y": 0.9563398, "z": -3.804}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_dd4bbd23", "position": {"x": -3.31319165, "y": 1.29174423, "z": -2.88153863}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fca8a224", "position": {"x": -3.47741938, "y": 0.9286998, "z": -3.12682819}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_2ce80e75", "position": {"x": -0.471111983, "y": 0.7431938, "z": -1.56380391}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_46bffc5f", "position": {"x": -0.3529855, "y": 0.7793249, "z": -0.912980556}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_5fac94a5", "position": {"x": -0.0684893, "y": 1.29072857, "z": -1.428961}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a7531534", "position": {"x": -2.80841613, "y": 0.913742065, "z": -3.87812114}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_bdd88192", "position": {"x": -0.41940093, "y": 0.914150357, "z": -1.3709687}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_78f219bc", "position": {"x": -0.489008784, "y": 0.08052623, "z": -3.08187246}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_94d2125c", "position": {"x": -1.91450834, "y": 0.7916855, "z": -3.704358}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f4b8ca2f", "position": {"x": -1.05887651, "y": 0.9086999, "z": -3.72987914}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_697b561f", "position": {"x": -3.07920742, "y": 1.3965019, "z": -2.002615}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 22738058, "scene_num": 19}, "task_id": "trial_T20190908_070259_699724", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1MKCTVKE7J0ZP_3IFS6Q0HJL00R7T41XEDZ134V7UISK", "high_descs": ["Turn around, go forward, then turn right to face the counter between the stove and the microwave.", "Pick up the knife on the counter, to the left of the soap.", "Turn around, go forward, then turn right to face the table.", "Slice the loaf of bread on the table, to the left of the plate.", "Put the knife down on the table, next to the sliced loaf of bread.", "Pick up a slice of bread from the table.", "Turn right, move forward, turn left and go toward the door, then turn right to go to the microwave.", "Heat the slice of bread in the microwave. ", "Turn right, go forward, then turn right and go to the fridge.", "Put the slice of bread in the fridge, in front of the apple."], "task_desc": "Put a warm slice of bread in the fridge.", "votes": [1, 1, 1]}, {"assignment_id": "A3PPLDHC3CG0YN_3ZQIG0FLQHXYKUUDK9NXBFWJ9KGWV0", "high_descs": ["Turn around and walk to the counter to the left of the stove. ", "Pick up the knife to the right of the plate. ", "Turn around and walk to the table to the right of the fridge. ", "Slice the loaf of bread to the left of the plate on the table. ", "Put the knife down on the table in front of the loaf of bread. ", "Pick up a piece of bread from the sliced loaf on the table. ", "Turn right and walk to the microwave. ", "Put the bread in the microwave, heat up the bread, take the bread out of the microwave. ", "Turn around and walk to the fridge. ", "Place the bread inside of the fridge in front of the red apple. "], "task_desc": "To heat a slice of bread and put it in the fridge. ", "votes": [1, 1, 1]}, {"assignment_id": "AKW57KYG90X61_3LQ8PUHQFO9B1YFOQ32EVNW2MTHIHX", "high_descs": ["move left to the oven table", "pick up a knife ", "turn around to the table", "slice the bread on the table", "drop the knife on the table", "pick up a slice from the bread ", "turn around and head for the microwave", "warm the bread in the oven", "turn around and head for  the fridge", "place the slice in the fridge"], "task_desc": "place a warm bread slice in the fridge", "votes": [1, 1, 0]}]}}