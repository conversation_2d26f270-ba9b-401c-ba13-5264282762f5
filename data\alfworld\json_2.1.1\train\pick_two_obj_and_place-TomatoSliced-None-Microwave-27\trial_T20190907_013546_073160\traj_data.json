{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000219.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000221.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000222.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 35}, {"high_idx": 8, "image_name": "000000240.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000241.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000242.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000243.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000244.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000245.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000246.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000247.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000248.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000249.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000250.png", "low_idx": 36}, {"high_idx": 8, "image_name": "000000251.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000252.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000253.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000254.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000255.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000256.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000257.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000258.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000259.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000260.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000261.png", "low_idx": 37}, {"high_idx": 8, "image_name": "000000262.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000268.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000269.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000270.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000271.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000272.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000273.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000274.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000275.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000276.png", "low_idx": 40}, {"high_idx": 9, "image_name": "000000277.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000278.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000279.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000280.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000281.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000282.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000283.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000284.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000285.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000286.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000287.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000288.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000289.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000290.png", "low_idx": 41}, {"high_idx": 9, "image_name": "000000291.png", "low_idx": 41}, {"high_idx": 10, "image_name": "000000292.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000293.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000294.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000295.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000296.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000297.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000298.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000299.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000300.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000301.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000302.png", "low_idx": 42}, {"high_idx": 10, "image_name": "000000303.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000304.png", "low_idx": 43}, {"high_idx": 10, "image_name": "000000305.png", "low_idx": 44}, {"high_idx": 10, "image_name": "000000306.png", "low_idx": 44}, {"high_idx": 11, "image_name": "000000307.png", "low_idx": 45}, {"high_idx": 11, "image_name": "000000308.png", "low_idx": 45}, {"high_idx": 11, "image_name": "000000309.png", "low_idx": 45}, {"high_idx": 11, "image_name": "000000310.png", "low_idx": 45}, {"high_idx": 11, "image_name": "000000311.png", "low_idx": 46}, {"high_idx": 11, "image_name": "000000312.png", "low_idx": 46}, {"high_idx": 11, "image_name": "000000313.png", "low_idx": 46}, {"high_idx": 11, "image_name": "000000314.png", "low_idx": 46}, {"high_idx": 11, "image_name": "000000315.png", "low_idx": 46}, {"high_idx": 11, "image_name": "000000316.png", "low_idx": 46}, {"high_idx": 11, "image_name": "000000317.png", "low_idx": 46}, {"high_idx": 11, "image_name": "000000318.png", "low_idx": 46}, {"high_idx": 11, "image_name": "000000319.png", "low_idx": 46}, {"high_idx": 11, "image_name": "000000320.png", "low_idx": 46}, {"high_idx": 12, "image_name": "000000321.png", "low_idx": 47}, {"high_idx": 12, "image_name": "000000322.png", "low_idx": 47}, {"high_idx": 12, "image_name": "000000323.png", "low_idx": 47}, {"high_idx": 12, "image_name": "000000324.png", "low_idx": 47}, {"high_idx": 12, "image_name": "000000325.png", "low_idx": 47}, {"high_idx": 12, "image_name": "000000326.png", "low_idx": 47}, {"high_idx": 12, "image_name": "000000327.png", "low_idx": 47}, {"high_idx": 12, "image_name": "000000328.png", "low_idx": 47}, {"high_idx": 12, "image_name": "000000329.png", "low_idx": 47}, {"high_idx": 12, "image_name": "000000330.png", "low_idx": 47}, {"high_idx": 12, "image_name": "000000331.png", "low_idx": 48}, {"high_idx": 12, "image_name": "000000332.png", "low_idx": 48}, {"high_idx": 12, "image_name": "000000333.png", "low_idx": 48}, {"high_idx": 12, "image_name": "000000334.png", "low_idx": 48}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Tomato", "parent_target": "Microwave", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|2|3|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["tomato"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Tomato", [0.134391904, 0.134391904, 2.853743076, 2.853743076, 3.349036, 3.349036]], "coordinateReceptacleObjectId": ["DiningTable", [-0.848358096, -0.848358096, 3.524, 3.524, 0.0886555464, 0.0886555464]], "forceVisible": true, "objectId": "Tomato|+00.03|+00.84|+00.71"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|2|8|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tomato", "microwave"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Tomato", [0.134391904, 0.134391904, 2.853743076, 2.853743076, 3.349036, 3.349036]], "coordinateReceptacleObjectId": ["Microwave", [-1.244, -1.244, 8.312, 8.312, 3.7060904, 3.7060904]], "forceVisible": true, "objectId": "Tomato|+00.03|+00.84|+00.71", "receptacleObjectId": "Microwave|-00.31|+00.93|+02.08"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|6|8|0|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["tomato"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Tomato", [6.69233036, 6.69233036, 11.593462, 11.593462, 3.9359688, 3.9359688]], "coordinateReceptacleObjectId": ["CounterTop", [4.264, 4.264, 10.6656, 10.6656, 3.8928, 3.8928]], "forceVisible": true, "objectId": "Tomato|+01.67|+00.98|+02.90"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|2|8|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tomato", "microwave"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Tomato", [6.69233036, 6.69233036, 11.593462, 11.593462, 3.9359688, 3.9359688]], "coordinateReceptacleObjectId": ["Microwave", [-1.244, -1.244, 8.312, 8.312, 3.7060904, 3.7060904]], "forceVisible": true, "objectId": "Tomato|+01.67|+00.98|+02.90", "receptacleObjectId": "Microwave|-00.31|+00.93|+02.08"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|4|8|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 9, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [4.47237492, 4.47237492, 10.78111936, 10.78111936, 3.208413364, 3.208413364]], "coordinateReceptacleObjectId": ["SinkBasin", [3.616, 3.616, 10.56, 10.56, 3.1394172, 3.1394172]], "forceVisible": true, "objectId": "ButterKnife|+01.12|+00.80|+02.70"}}, {"discrete_action": {"action": "GotoLocation", "args": ["tomato"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|2|8|3|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["tomato"]}, "high_idx": 11, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Tomato", [0.134391904, 0.134391904, 2.853743076, 2.853743076, 3.349036, 3.349036]], "forceVisible": true, "objectId": "Tomato|+00.03|+00.84|+00.71"}}, {"discrete_action": {"action": "SliceObject", "args": ["tomato"]}, "high_idx": 12, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Tomato", [6.69233036, 6.69233036, 11.593462, 11.593462, 3.9359688, 3.9359688]], "forceVisible": true, "objectId": "Tomato|+01.67|+00.98|+02.90"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 13, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Tomato|+00.03|+00.84|+00.71"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [120, 126, 157, 158], "mask": [[37636, 6], [37933, 13], [38230, 1], [38232, 16], [38530, 19], [38827, 23], [39125, 26], [39424, 27], [39723, 30], [40022, 32], [40322, 34], [40622, 34], [40921, 35], [41221, 36], [41520, 37], [41820, 37], [42120, 38], [42420, 38], [42720, 38], [43021, 37], [43321, 37], [43621, 36], [43921, 36], [44222, 34], [44523, 33], [44824, 31], [45124, 30], [45425, 28], [45726, 25], [46027, 23], [46328, 21], [46630, 17], [46932, 12], [47236, 5]], "point": [138, 141]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Tomato|+00.03|+00.84|+00.71", "placeStationary": true, "receptacleObjectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 272], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14156, 243], [14455, 243], [14754, 244], [15054, 244], [15353, 244], [15653, 244], [15952, 244], [16251, 245], [16551, 245], [16850, 245], [17150, 245], [17449, 246], [17748, 246], [18048, 246], [18347, 246], [18647, 246], [18946, 246], [19245, 247], [19545, 247], [19844, 247], [20144, 247], [20443, 247], [20742, 248], [21042, 248], [21341, 248], [21641, 248], [21940, 248], [22239, 249], [22539, 249], [22838, 249], [23138, 249], [23437, 249], [23736, 250], [24036, 249], [24335, 250], [24635, 250], [24934, 250], [25233, 251], [25533, 250], [25832, 251], [26132, 251], [26431, 251], [26730, 252], [27030, 251], [27329, 252], [27629, 252], [27928, 252], [28227, 253], [28527, 252], [28826, 253], [29126, 253], [29425, 253], [29724, 254], [30024, 253], [30323, 254], [30623, 253], [30922, 254], [31221, 255], [31521, 254], [31820, 255], [32120, 254], [32419, 255], [32718, 256], [33018, 255], [33317, 256], [33617, 255], [33916, 256], [34215, 257], [34515, 256], [34814, 257], [35114, 256], [35413, 257], [35712, 257], [36012, 257], [36311, 258], [36611, 257], [36910, 258], [37209, 258], [37509, 258], [37808, 259], [38108, 258], [38407, 259], [38706, 259], [39006, 259], [39305, 260], [39605, 259], [39904, 260], [40203, 260], [40503, 260], [40802, 260], [41102, 260], [41401, 261], [41700, 261], [42000, 261], [42300, 261], [42600, 260], [42900, 260], [43200, 259], [43500, 259], [43800, 259], [44100, 86], [44187, 171], [44400, 85], [44487, 171], [44700, 85], [45000, 85], [45300, 85], [45600, 84], [45900, 84], [46200, 84], [46500, 84], [46800, 83], [47100, 83], [47400, 83], [47700, 83], [48000, 82], [48300, 82], [48600, 82], [48900, 82], [49200, 81], [49500, 81], [49800, 81], [50100, 81], [50400, 80], [50700, 80], [51000, 80], [51300, 80], [51600, 79], [51900, 79], [52200, 79], [52500, 79], [52800, 78], [53100, 78], [53400, 78], [53700, 78], [54000, 77], [54300, 77], [54600, 77], [54900, 77], [55200, 76], [55500, 76], [55800, 76], [56100, 76], [56400, 75], [56700, 75], [57000, 75], [57300, 75], [57600, 74], [57900, 74], [58200, 74], [58500, 74], [58800, 73], [59100, 73], [59400, 73], [59700, 73], [60000, 72], [60300, 72], [60600, 72], [60900, 72], [61200, 71], [61500, 71], [61800, 71], [62100, 71], [62400, 70], [62700, 70], [63000, 70], [63300, 70], [63600, 69], [63900, 69], [64200, 69], [64500, 69], [64800, 68], [65100, 68], [65400, 68], [65700, 68], [66000, 67], [66300, 67], [66600, 67], [66900, 67], [67200, 66], [67500, 66], [67800, 66], [68100, 66], [68400, 65], [68700, 65], [69000, 65], [69300, 65], [69600, 64], [69900, 64], [70200, 64], [70500, 64], [70800, 63], [71100, 63], [71400, 63], [71700, 63], [72000, 62], [72300, 62], [72600, 62], [72900, 62], [73200, 61], [73500, 61], [73800, 61], [74101, 60], [74402, 58], [74703, 57], [75004, 56], [75305, 55], [75606, 53], [75907, 52], [76208, 51], [76509, 50], [76809, 49], [77110, 48], [77411, 12], [77425, 33], [77712, 10], [77726, 32], [78013, 8], [78027, 30], [78314, 6], [78327, 30], [78615, 4], [78628, 29], [78929, 31], [79230, 32], [79531, 32], [79832, 24], [79857, 6], [80132, 24], [80158, 4], [80433, 22], [80734, 21], [81035, 20], [81336, 18]], "point": [149, 135]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 272], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14156, 243], [14455, 243], [14754, 244], [15054, 244], [15353, 244], [15653, 244], [15952, 244], [16251, 245], [16551, 245], [16850, 245], [17150, 245], [17449, 246], [17748, 246], [18048, 246], [18347, 246], [18647, 246], [18946, 246], [19245, 247], [19545, 247], [19844, 247], [20144, 247], [20443, 247], [20742, 248], [21042, 248], [21341, 248], [21641, 248], [21940, 248], [22239, 249], [22539, 249], [22838, 249], [23138, 114], [23255, 132], [23437, 113], [23558, 128], [23736, 113], [23861, 125], [24036, 111], [24162, 123], [24335, 109], [24463, 122], [24635, 108], [24763, 122], [24934, 109], [25064, 120], [25233, 109], [25366, 118], [25533, 108], [25667, 116], [25832, 108], [25968, 115], [26132, 108], [26269, 114], [26431, 109], [26570, 112], [26730, 109], [26870, 112], [27030, 109], [27170, 111], [27329, 109], [27470, 111], [27629, 109], [27771, 110], [27928, 110], [28070, 110], [28227, 111], [28370, 110], [28527, 111], [28670, 109], [28826, 112], [28970, 109], [29126, 112], [29270, 109], [29425, 113], [29569, 109], [29724, 115], [29869, 109], [30024, 115], [30169, 108], [30323, 117], [30468, 109], [30623, 117], [30768, 108], [30922, 119], [31067, 109], [31221, 120], [31367, 109], [31521, 121], [31666, 109], [31820, 123], [31965, 110], [32120, 124], [32264, 110], [32419, 126], [32563, 111], [32718, 128], [32862, 112], [33018, 129], [33160, 113], [33317, 131], [33459, 114], [33617, 135], [33757, 115], [33916, 256], [34215, 257], [34515, 256], [34814, 257], [35114, 256], [35413, 257], [35712, 257], [36012, 257], [36311, 258], [36611, 257], [36910, 258], [37209, 258], [37509, 258], [37808, 259], [38108, 258], [38407, 259], [38706, 259], [39006, 259], [39305, 260], [39605, 259], [39904, 260], [40203, 260], [40503, 260], [40802, 260], [41102, 260], [41401, 261], [41700, 261], [42000, 261], [42300, 261], [42600, 260], [42900, 260], [43200, 259], [43500, 259], [43800, 259], [44100, 86], [44187, 171], [44400, 85], [44487, 171], [44700, 85], [45000, 85], [45300, 85], [45600, 84], [45900, 84], [46200, 84], [46500, 84], [46800, 83], [47100, 83], [47400, 83], [47700, 83], [48000, 82], [48300, 82], [48600, 82], [48900, 82], [49200, 81], [49500, 81], [49800, 81], [50100, 81], [50400, 80], [50700, 80], [51000, 80], [51300, 80], [51600, 79], [51900, 79], [52200, 79], [52500, 79], [52800, 78], [53100, 78], [53400, 78], [53700, 78], [54000, 77], [54300, 77], [54600, 77], [54900, 77], [55200, 76], [55500, 76], [55800, 76], [56100, 76], [56400, 75], [56700, 75], [57000, 75], [57300, 75], [57600, 74], [57900, 74], [58200, 74], [58500, 74], [58800, 73], [59100, 73], [59400, 73], [59700, 73], [60000, 72], [60300, 72], [60600, 72], [60900, 72], [61200, 71], [61500, 71], [61800, 71], [62100, 71], [62400, 70], [62700, 70], [63000, 70], [63300, 70], [63600, 69], [63900, 69], [64200, 69], [64500, 69], [64800, 68], [65100, 68], [65400, 68], [65700, 68], [66000, 67], [66300, 67], [66600, 67], [66900, 67], [67200, 66], [67500, 66], [67800, 66], [68100, 66], [68400, 65], [68700, 65], [69000, 65], [69300, 65], [69600, 64], [69900, 64], [70200, 64], [70500, 64], [70800, 63], [71100, 63], [71400, 63], [71700, 63], [72000, 62], [72300, 62], [72600, 62], [72900, 62], [73200, 61], [73500, 61], [73800, 61], [74101, 60], [74402, 58], [74703, 57], [75004, 56], [75305, 55], [75606, 53], [75907, 52], [76208, 51], [76509, 50], [76809, 49], [77110, 48], [77411, 12], [77425, 33], [77712, 10], [77726, 32], [78013, 8], [78027, 30], [78314, 6], [78327, 30], [78615, 4], [78628, 29], [78929, 31], [79230, 32], [79531, 32], [79832, 24], [79857, 6], [80132, 24], [80158, 4], [80433, 22], [80734, 21], [81035, 20], [81336, 18]], "point": [149, 135]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Tomato|+01.67|+00.98|+02.90"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [178, 154, 204, 181], "mask": [[46090, 2], [46094, 1], [46388, 6], [46687, 8], [46696, 1], [46984, 15], [47283, 18], [47582, 20], [47881, 21], [48180, 23], [48480, 24], [48779, 25], [49079, 25], [49379, 26], [49678, 27], [49978, 27], [50278, 27], [50579, 26], [50879, 26], [51179, 26], [51479, 26], [51780, 24], [52080, 24], [52381, 23], [52682, 21], [52983, 19], [53283, 18], [53584, 16], [53886, 12], [54188, 8]], "point": [191, 166]}}, "high_idx": 5}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Tomato|+01.67|+00.98|+02.90", "placeStationary": true, "receptacleObjectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 272], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14156, 243], [14455, 243], [14754, 244], [15054, 244], [15353, 244], [15653, 244], [15952, 244], [16251, 245], [16551, 245], [16850, 245], [17150, 245], [17449, 246], [17748, 246], [18048, 246], [18347, 246], [18647, 246], [18946, 246], [19245, 247], [19545, 247], [19844, 247], [20144, 247], [20443, 247], [20742, 248], [21042, 248], [21341, 248], [21641, 248], [21940, 248], [22239, 249], [22539, 249], [22838, 249], [23138, 114], [23255, 132], [23437, 113], [23558, 128], [23736, 113], [23861, 125], [24036, 111], [24162, 123], [24335, 109], [24463, 122], [24635, 108], [24763, 122], [24934, 109], [25064, 120], [25233, 109], [25366, 118], [25533, 108], [25667, 116], [25832, 108], [25968, 115], [26132, 108], [26269, 114], [26431, 109], [26570, 112], [26730, 109], [26870, 112], [27030, 109], [27170, 111], [27329, 109], [27470, 111], [27629, 109], [27771, 110], [27928, 110], [28070, 110], [28227, 111], [28370, 110], [28527, 111], [28670, 109], [28826, 112], [28970, 109], [29126, 112], [29270, 109], [29425, 113], [29569, 109], [29724, 115], [29869, 109], [30024, 115], [30169, 108], [30323, 117], [30468, 109], [30623, 117], [30768, 108], [30922, 119], [31067, 109], [31221, 120], [31367, 109], [31521, 121], [31666, 109], [31820, 123], [31965, 110], [32120, 124], [32264, 110], [32419, 126], [32563, 111], [32718, 128], [32862, 112], [33018, 129], [33160, 113], [33317, 131], [33459, 114], [33617, 135], [33757, 115], [33916, 256], [34215, 257], [34515, 256], [34814, 257], [35114, 256], [35413, 257], [35712, 257], [36012, 257], [36311, 258], [36611, 257], [36910, 258], [37209, 258], [37509, 258], [37808, 259], [38108, 258], [38407, 259], [38706, 259], [39006, 259], [39305, 260], [39605, 259], [39904, 260], [40203, 260], [40503, 260], [40802, 260], [41102, 260], [41401, 261], [41700, 261], [42000, 261], [42300, 261], [42600, 260], [42900, 260], [43200, 259], [43500, 259], [43800, 259], [44100, 86], [44187, 171], [44400, 85], [44487, 171], [44700, 85], [45000, 85], [45300, 85], [45600, 84], [45900, 84], [46200, 84], [46500, 84], [46800, 83], [47100, 83], [47400, 83], [47700, 83], [48000, 82], [48300, 82], [48600, 82], [48900, 82], [49200, 81], [49500, 81], [49800, 81], [50100, 81], [50400, 80], [50700, 80], [51000, 80], [51300, 80], [51600, 79], [51900, 79], [52200, 79], [52500, 79], [52800, 78], [53100, 78], [53400, 78], [53700, 78], [54000, 77], [54300, 77], [54600, 77], [54900, 77], [55200, 76], [55500, 76], [55800, 76], [56100, 76], [56400, 75], [56700, 75], [57000, 75], [57300, 75], [57600, 74], [57900, 74], [58200, 74], [58500, 74], [58800, 73], [59100, 73], [59400, 73], [59700, 73], [60000, 72], [60300, 72], [60600, 72], [60900, 72], [61200, 71], [61500, 71], [61800, 71], [62100, 71], [62400, 70], [62700, 70], [63000, 70], [63300, 70], [63600, 69], [63900, 69], [64200, 69], [64500, 69], [64800, 68], [65100, 68], [65400, 68], [65700, 68], [66000, 67], [66300, 67], [66600, 67], [66900, 67], [67200, 66], [67500, 66], [67800, 66], [68100, 66], [68400, 65], [68700, 65], [69000, 65], [69300, 65], [69600, 64], [69900, 64], [70200, 64], [70500, 64], [70800, 63], [71100, 63], [71400, 63], [71700, 63], [72000, 62], [72300, 62], [72600, 62], [72900, 62], [73200, 61], [73500, 61], [73800, 61], [74101, 60], [74402, 58], [74703, 57], [75004, 56], [75305, 55], [75606, 53], [75907, 52], [76208, 51], [76509, 50], [76809, 49], [77110, 48], [77411, 12], [77425, 33], [77712, 10], [77726, 32], [78013, 8], [78027, 30], [78314, 6], [78327, 30], [78615, 4], [78628, 29], [78929, 31], [79230, 32], [79531, 32], [79832, 24], [79857, 6], [80132, 24], [80158, 4], [80433, 22], [80734, 21], [81035, 20], [81336, 18]], "point": [149, 135]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 272], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14156, 243], [14455, 243], [14754, 244], [15054, 244], [15353, 244], [15653, 244], [15952, 244], [16251, 245], [16551, 245], [16850, 245], [17150, 245], [17449, 246], [17748, 246], [18048, 246], [18347, 246], [18647, 246], [18946, 77], [19025, 167], [19245, 76], [19329, 163], [19545, 75], [19631, 161], [19844, 73], [19932, 159], [20144, 71], [20233, 158], [20443, 71], [20534, 156], [20742, 71], [20835, 155], [21042, 71], [21136, 154], [21341, 71], [21437, 152], [21641, 71], [21738, 151], [21940, 71], [22039, 149], [22239, 72], [22339, 149], [22539, 71], [22639, 149], [22838, 72], [22940, 147], [23138, 72], [23240, 12], [23255, 132], [23437, 73], [23540, 10], [23558, 128], [23736, 74], [23840, 9], [23861, 125], [24036, 74], [24140, 7], [24162, 123], [24335, 75], [24439, 5], [24463, 122], [24635, 75], [24739, 4], [24763, 122], [24934, 76], [25039, 4], [25064, 120], [25233, 78], [25339, 3], [25366, 118], [25533, 78], [25638, 3], [25667, 116], [25832, 80], [25938, 2], [25968, 115], [26132, 80], [26237, 3], [26269, 114], [26431, 82], [26537, 3], [26570, 112], [26730, 83], [26836, 3], [26870, 112], [27030, 84], [27135, 4], [27170, 111], [27329, 86], [27434, 4], [27470, 111], [27629, 87], [27733, 5], [27771, 110], [27928, 90], [28032, 6], [28070, 110], [28227, 92], [28330, 8], [28370, 110], [28527, 94], [28628, 10], [28670, 109], [28826, 112], [28970, 109], [29126, 112], [29270, 109], [29425, 113], [29569, 109], [29724, 115], [29869, 109], [30024, 115], [30169, 108], [30323, 117], [30468, 109], [30623, 117], [30768, 108], [30922, 119], [31067, 109], [31221, 120], [31367, 109], [31521, 121], [31666, 109], [31820, 123], [31965, 110], [32120, 124], [32264, 110], [32419, 126], [32563, 111], [32718, 128], [32862, 112], [33018, 129], [33160, 113], [33317, 131], [33459, 114], [33617, 135], [33757, 115], [33916, 256], [34215, 257], [34515, 256], [34814, 257], [35114, 256], [35413, 257], [35712, 257], [36012, 257], [36311, 258], [36611, 257], [36910, 258], [37209, 258], [37509, 258], [37808, 259], [38108, 258], [38407, 259], [38706, 259], [39006, 259], [39305, 260], [39605, 259], [39904, 260], [40203, 260], [40503, 260], [40802, 260], [41102, 260], [41401, 261], [41700, 261], [42000, 261], [42300, 261], [42600, 260], [42900, 260], [43200, 259], [43500, 259], [43800, 259], [44100, 86], [44187, 171], [44400, 85], [44487, 171], [44700, 85], [45000, 85], [45300, 85], [45600, 84], [45900, 84], [46200, 84], [46500, 84], [46800, 83], [47100, 83], [47400, 83], [47700, 83], [48000, 82], [48300, 82], [48600, 82], [48900, 82], [49200, 81], [49500, 81], [49800, 81], [50100, 81], [50400, 80], [50700, 80], [51000, 80], [51300, 80], [51600, 79], [51900, 79], [52200, 79], [52500, 79], [52800, 78], [53100, 78], [53400, 78], [53700, 78], [54000, 77], [54300, 77], [54600, 77], [54900, 77], [55200, 76], [55500, 76], [55800, 76], [56100, 76], [56400, 75], [56700, 75], [57000, 75], [57300, 75], [57600, 74], [57900, 74], [58200, 74], [58500, 74], [58800, 73], [59100, 73], [59400, 73], [59700, 73], [60000, 72], [60300, 72], [60600, 72], [60900, 72], [61200, 71], [61500, 71], [61800, 71], [62100, 71], [62400, 70], [62700, 70], [63000, 70], [63300, 70], [63600, 69], [63900, 69], [64200, 69], [64500, 69], [64800, 68], [65100, 68], [65400, 68], [65700, 68], [66000, 67], [66300, 67], [66600, 67], [66900, 67], [67200, 66], [67500, 66], [67800, 66], [68100, 66], [68400, 65], [68700, 65], [69000, 65], [69300, 65], [69600, 64], [69900, 64], [70200, 64], [70500, 64], [70800, 63], [71100, 63], [71400, 63], [71700, 63], [72000, 62], [72300, 62], [72600, 62], [72900, 62], [73200, 61], [73500, 61], [73800, 61], [74101, 60], [74402, 58], [74703, 57], [75004, 56], [75305, 55], [75606, 53], [75907, 52], [76208, 51], [76509, 50], [76809, 49], [77110, 48], [77411, 12], [77425, 33], [77712, 10], [77726, 32], [78013, 8], [78027, 30], [78314, 6], [78327, 30], [78615, 4], [78628, 29], [78929, 31], [79230, 32], [79531, 32], [79832, 24], [79857, 6], [80132, 24], [80158, 4], [80433, 22], [80734, 21], [81035, 20], [81336, 18]], "point": [149, 135]}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|+01.12|+00.80|+02.70"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [146, 163, 213, 167], "mask": [[48746, 19], [48767, 7], [49046, 20], [49067, 47], [49347, 19], [49368, 46], [49683, 28], [49989, 17]], "point": [179, 164]}}, "high_idx": 9}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 11}, {"api_action": {"action": "SliceObject", "objectId": "Tomato|+00.03|+00.84|+00.71"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [138, 78, 170, 113], "mask": [[23252, 3], [23550, 8], [23849, 12], [24147, 15], [24444, 19], [24743, 20], [25043, 21], [25342, 24], [25641, 26], [25940, 28], [26240, 29], [26540, 30], [26839, 31], [27139, 31], [27438, 32], [27738, 33], [28038, 32], [28338, 32], [28638, 32], [28938, 32], [29238, 32], [29538, 31], [29839, 30], [30139, 30], [30440, 28], [30740, 28], [31041, 26], [31341, 26], [31642, 24], [31943, 22], [32244, 20], [32545, 18], [32846, 16], [33147, 13], [33448, 11], [33752, 5]], "point": [154, 94]}}, "high_idx": 11}, {"api_action": {"action": "SliceObject", "objectId": "Tomato|+01.67|+00.98|+02.90"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [110, 64, 139, 96], "mask": [[19023, 2], [19321, 8], [19620, 11], [19917, 15], [20215, 18], [20514, 20], [20813, 22], [21113, 23], [21412, 25], [21712, 26], [22011, 28], [22311, 28], [22610, 29], [22910, 30], [23210, 30], [23510, 30], [23810, 30], [24110, 30], [24410, 29], [24710, 29], [25010, 29], [25311, 28], [25611, 27], [25912, 26], [26212, 25], [26513, 24], [26813, 23], [27114, 21], [27415, 19], [27716, 17], [28018, 14], [28319, 11], [28621, 7]], "point": [124, 79]}}, "high_idx": 12}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 272], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14156, 243], [14455, 243], [14754, 244], [15054, 244], [15353, 244], [15653, 244], [15952, 244], [16251, 245], [16551, 245], [16850, 245], [17150, 245], [17449, 246], [17748, 246], [18048, 246], [18347, 246], [18647, 246], [18946, 246], [19245, 247], [19545, 77], [19628, 164], [19844, 77], [19931, 160], [20144, 75], [20232, 159], [20443, 73], [20533, 157], [20742, 73], [20834, 156], [21042, 72], [21134, 156], [21341, 72], [21436, 153], [21641, 71], [21737, 152], [21940, 72], [22038, 150], [22239, 72], [22339, 149], [22539, 72], [22639, 149], [22838, 73], [22939, 148], [23138, 72], [23240, 147], [23437, 73], [23540, 146], [23736, 74], [23840, 10], [23859, 127], [24036, 74], [24140, 9], [24161, 124], [24335, 75], [24440, 7], [24462, 123], [24635, 75], [24740, 4], [24763, 122], [24934, 76], [25039, 5], [25063, 121], [25233, 78], [25339, 4], [25364, 120], [25533, 78], [25639, 3], [25666, 117], [25832, 79], [25938, 3], [25967, 116], [26132, 80], [26238, 3], [26268, 115], [26431, 81], [26537, 3], [26569, 113], [26730, 83], [26837, 3], [26869, 113], [27030, 83], [27136, 3], [27170, 111], [27329, 85], [27435, 4], [27470, 111], [27629, 86], [27735, 4], [27770, 111], [27928, 88], [28034, 4], [28070, 110], [28227, 90], [28332, 6], [28370, 110], [28527, 92], [28630, 8], [28670, 109], [28826, 94], [28929, 9], [28970, 109], [29126, 98], [29225, 13], [29270, 109], [29425, 113], [29570, 108], [29724, 114], [29869, 109], [30024, 115], [30169, 108], [30323, 116], [30469, 108], [30623, 116], [30769, 107], [30922, 118], [31068, 108], [31221, 120], [31367, 109], [31521, 120], [31667, 108], [31820, 122], [31966, 109], [32120, 123], [32265, 109], [32419, 124], [32564, 110], [32718, 126], [32863, 111], [33018, 127], [33162, 111], [33317, 130], [33461, 112], [33617, 131], [33759, 113], [33916, 135], [34057, 115], [34215, 257], [34515, 256], [34814, 257], [35114, 256], [35413, 257], [35712, 257], [36012, 257], [36311, 258], [36611, 257], [36910, 258], [37209, 258], [37509, 258], [37808, 259], [38108, 258], [38407, 259], [38706, 259], [39006, 259], [39305, 260], [39605, 259], [39904, 260], [40203, 260], [40503, 260], [40802, 260], [41102, 260], [41401, 261], [41700, 261], [42000, 261], [42300, 261], [42600, 260], [42900, 260], [43200, 259], [43500, 259], [43800, 259], [44100, 86], [44187, 171], [44400, 85], [44487, 171], [44700, 85], [45000, 85], [45300, 85], [45600, 84], [45900, 84], [46200, 84], [46500, 84], [46800, 83], [47100, 83], [47400, 83], [47700, 83], [48000, 82], [48300, 82], [48600, 82], [48900, 82], [49200, 81], [49500, 81], [49800, 81], [50100, 81], [50400, 80], [50700, 80], [51000, 80], [51300, 80], [51600, 79], [51900, 79], [52200, 79], [52500, 79], [52800, 78], [53100, 78], [53400, 78], [53700, 78], [54000, 77], [54300, 77], [54600, 77], [54900, 77], [55200, 76], [55500, 76], [55800, 76], [56100, 76], [56400, 75], [56700, 75], [57000, 75], [57300, 75], [57600, 74], [57900, 74], [58200, 74], [58500, 74], [58800, 73], [59100, 73], [59400, 73], [59700, 73], [60000, 72], [60300, 72], [60600, 72], [60900, 72], [61200, 71], [61500, 71], [61800, 71], [62100, 71], [62400, 70], [62700, 70], [63000, 70], [63300, 70], [63600, 69], [63900, 69], [64200, 69], [64500, 69], [64800, 68], [65100, 68], [65400, 68], [65700, 68], [66000, 67], [66300, 67], [66600, 67], [66900, 67], [67200, 66], [67500, 66], [67800, 66], [68100, 66], [68400, 65], [68700, 65], [69000, 65], [69300, 65], [69600, 64], [69900, 64], [70200, 64], [70500, 64], [70800, 63], [71100, 63], [71400, 63], [71700, 63], [72000, 62], [72300, 62], [72600, 62], [72900, 62], [73200, 61], [73500, 61], [73800, 61], [74101, 60], [74402, 58], [74703, 57], [75004, 56], [75305, 55], [75606, 53], [75907, 52], [76208, 51], [76509, 50], [76809, 49], [77110, 48], [77411, 12], [77425, 33], [77712, 10], [77726, 32], [78013, 8], [78027, 30], [78314, 6], [78327, 30], [78615, 4], [78628, 29], [78929, 30], [79230, 31], [79531, 32], [79832, 24], [79857, 6], [80132, 24], [80158, 4], [80433, 22], [80734, 21], [81035, 20], [81336, 18]], "point": [149, 135]}}, "high_idx": 12}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan27", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": 1.25, "y": 0.9010001, "z": 0.75}, "object_poses": [{"objectName": "Potato_d7da2d9d", "position": {"x": -0.425777048, "y": 0.831192434, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_d7da2d9d", "position": {"x": 2.09635258, "y": 0.378844738, "z": -0.188374981}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "WineBottle_2a35bd39", "position": {"x": 2.07424426, "y": 0.9386287, "z": 1.9649893}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_2a35bd39", "position": {"x": 2.28230524, "y": 1.5022769, "z": 1.96310914}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a2d86e36", "position": {"x": -0.196089536, "y": 0.7910058, "z": 0.3771075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_db580e26", "position": {"x": 0.735489666, "y": 0.810389459, "z": 2.75055981}, "rotation": {"x": 0.0, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Spatula_db580e26", "position": {"x": 1.06522775, "y": 0.810388446, "z": 2.75056028}, "rotation": {"x": 0.0, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Spoon_9b26f0a9", "position": {"x": 1.9069, "y": 0.747938633, "z": 0.5148143}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_3133524b", "position": {"x": -0.349214524, "y": 0.79151535, "z": 0.3771075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_343518b0", "position": {"x": 0.6297573, "y": 0.8376902, "z": 2.75055981}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_540cf8b1", "position": {"x": -0.0429645181, "y": 0.8165851, "z": 0.713435769}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_540cf8b1", "position": {"x": 0.03359799, "y": 0.816582, "z": 1.049764}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_e54ab207", "position": {"x": 1.84060025, "y": 0.9390831, "z": 0.657804251}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_fac53a9c", "position": {"x": 1.67308259, "y": 0.9839922, "z": 2.8983655}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": 2.223756, "y": 1.3202486, "z": -0.1883754}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": 0.7083289, "y": 0.07819581, "z": 2.52336979}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8e528c3b", "position": {"x": -0.425777048, "y": 0.7859638, "z": 0.713435769}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": -0.4254055, "y": 0.9326999, "z": 2.42546678}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": 2.12710762, "y": 1.49853539, "z": 0.7250124}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_598ee640", "position": {"x": 2.12791038, "y": 1.58099759, "z": -0.188374937}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Lettuce_598ee640", "position": {"x": 2.12213445, "y": 0.664197564, "z": -0.0977499038}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_92427322", "position": {"x": 2.12213445, "y": 0.6524415, "z": -0.278999984}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_343518b0", "position": {"x": -0.3961984, "y": 0.980000556, "z": 2.68805}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_598ee640", "position": {"x": -0.196089536, "y": 0.8527509, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_3133524b", "position": {"x": -0.196089536, "y": 0.791512251, "z": 1.38609231}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_e54ab207", "position": {"x": 2.08882618, "y": 1.50431848, "z": 0.448988259}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Ladle_158e5727", "position": {"x": 0.060256362, "y": 0.9820624, "z": 2.61821938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_741f0242", "position": {"x": 2.20534277, "y": 1.99332058, "z": 1.05008936}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_4be1a058", "position": {"x": -0.199893564, "y": 0.823938251, "z": 0.813959062}, "rotation": {"x": 0.0162763428, "y": 0.0151294908, "z": 359.887421}}, {"objectName": "SaltShaker_8e528c3b", "position": {"x": -0.0429645181, "y": 0.7859668, "z": 1.049764}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_d7da2d9d", "position": {"x": 2.07127213, "y": 0.978528738, "z": 0.3613872}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_f15c1655", "position": {"x": 2.1402, "y": 0.9351, "z": 1.5045}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "WineBottle_2a35bd39", "position": {"x": 1.36938751, "y": 0.08233911, "z": 2.601572}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_db580e26", "position": {"x": -0.06825626, "y": 0.7617931, "z": 2.19738054}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_540cf8b1", "position": {"x": 1.81528163, "y": 0.7713503, "z": 1.99161363}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_fac53a9c", "position": {"x": 0.033597976, "y": 0.837259, "z": 0.713435769}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_92427322", "position": {"x": 2.112262, "y": 1.37144148, "z": -0.4602501}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_a2d86e36", "position": {"x": 1.11809373, "y": 0.802103341, "z": 2.69527984}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_a2dda372", "position": {"x": 1.85448289, "y": 0.9379, "z": 1.497}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": -0.349214524, "y": 0.785963655, "z": 0.713435769}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7054e289", "position": {"x": 2.00076771, "y": 1.32172918, "z": -0.0977503359}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_6b8b37c4", "position": {"x": -0.196089536, "y": 0.79052794, "z": 1.049764}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_922e45ca", "position": {"x": 1.92592835, "y": 0.0863831043, "z": 2.12878227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9b26f0a9", "position": {"x": 1.91749084, "y": 0.9394294, "z": 0.3613872}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_5c2a3ea2", "position": {"x": -0.196089536, "y": 0.7859637, "z": 0.545271635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": 2.02334642, "y": 0.33540082, "z": -0.5508752}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}], "object_toggles": [], "random_seed": 395917373, "scene_num": 27}, "task_id": "trial_T20190907_013546_073160", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3TWYQENN82FJI_3W8CV64QJ5GIQXMLM9W1IUXZ2RHH9B", "high_descs": ["Turn to your right and go to the white kitchen table.", "Pick up the only tomato on the table.", "Move to your right and go to the microwave.", "Open the microwave, put the tomato in, and close the door.", "Turn to your right and go to the counter just to the right of the sink", "Pick up the tomato that is to the right of the sink.", "Turn to your left and go to the microwave.", "Open the microwave, put the tomato in, and close the door.", "Turn to your right and go to the sink.", "Pick up the butter knife from the right part of the sink.", "Turn to your left and go to the microwave.", "Open the microwave door, use the knife to cut the tops off of both tomatoes, and close the door."], "task_desc": "Put two tomatoes with the tops cut off into the microwave.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A1O3TWBUDONVLO_3M0NZ3JDP4FLGVK3P5PR6LW2DOXZ5H", "high_descs": ["Turn right and walk forward to face the table.", "Pick up the tomato on the table.", "Turn to the right and walk forward to face the microwave.", "Place the tomato in the microwave and close it.", "Turn around and walk across the room then turn left to face the counter.", "Pick up the tomato on the counter.", "Turn left and walk across the room to face the microwave.", "Place the tomato in the microwave beside the other tomato.", "Turn to the right to face the sink.", "Pick up the knife in the right side of the sink.", "Turn left and walk forward to face the microwave.", "Cut the two tomatoes into slices and shut the microwave."], "task_desc": "Place two tomatoes in the microwave, as well as cut them into slices.", "votes": [1, 1]}, {"assignment_id": "AT3C00TKZK13L_3TAYZSBPLOPWGMF2CUTLCLWN5ZDS2A", "high_descs": ["Walk towards the table", "Grab a tomato from the table", "Walk towards the microwave", "Put the tomato inside the microwave", "Walk to the right towards the sink", "Grab the other tomato", "Walk towards the microwave", "Grab the cooked tomato", "Walk towards the sink ", "Grab the knife inside the sink", "Walk with the knife towards the microwave", "Open the microwave door"], "task_desc": "Grab both tomatoes, cook them in the microwave grab the knife from the sink", "votes": [1, 0, 1, 0, 1]}]}}