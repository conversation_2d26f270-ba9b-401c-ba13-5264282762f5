
(define (problem plan_trial_T20190907_201910_219917)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__plus_00_dot_29_bar__plus_00_dot_71_bar__plus_01_dot_05 - object
        Blinds_bar__plus_02_dot_30_bar__plus_02_dot_53_bar__minus_02_dot_89 - object
        Book_bar__plus_00_dot_97_bar__plus_00_dot_60_bar__plus_00_dot_72 - object
        Book_bar__plus_01_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_43 - object
        Book_bar__plus_02_dot_96_bar__plus_00_dot_77_bar__minus_01_dot_50 - object
        CD_bar__plus_00_dot_93_bar__plus_00_dot_44_bar__minus_02_dot_35 - object
        CellPhone_bar__plus_00_dot_67_bar__plus_00_dot_44_bar__minus_02_dot_07 - object
        CellPhone_bar__plus_03_dot_08_bar__plus_00_dot_78_bar__minus_01_dot_77 - object
        Chair_bar__plus_03_dot_23_bar_00_dot_00_bar__minus_01_dot_87 - object
        CreditCard_bar__plus_00_dot_33_bar__plus_00_dot_62_bar__plus_01_dot_45 - object
        KeyChain_bar__plus_00_dot_35_bar__plus_00_dot_62_bar__plus_01_dot_22 - object
        KeyChain_bar__plus_00_dot_89_bar__plus_00_dot_43_bar__minus_01_dot_94 - object
        Lamp_bar__plus_00_dot_35_bar__plus_00_dot_70_bar__minus_00_dot_98 - object
        Lamp_bar__plus_03_dot_68_bar__plus_00_dot_77_bar__minus_01_dot_39 - object
        Laptop_bar__plus_03_dot_45_bar__plus_00_dot_77_bar__minus_01_dot_67 - object
        LightSwitch_bar__plus_01_dot_81_bar__plus_01_dot_17_bar__plus_01_dot_70 - object
        Mirror_bar__plus_01_dot_08_bar__plus_01_dot_23_bar__plus_01_dot_70 - object
        Painting_bar__plus_00_dot_10_bar__plus_01_dot_43_bar__minus_02_dot_05 - object
        Pencil_bar__plus_00_dot_36_bar__plus_00_dot_71_bar__plus_01_dot_30 - object
        Pencil_bar__plus_02_dot_78_bar__plus_00_dot_78_bar__minus_01_dot_68 - object
        Pen_bar__plus_03_dot_55_bar__plus_00_dot_78_bar__minus_01_dot_32 - object
        Pen_bar__plus_03_dot_70_bar__plus_00_dot_79_bar__minus_01_dot_59 - object
        Pillow_bar__plus_00_dot_46_bar__plus_00_dot_64_bar__plus_00_dot_41 - object
        Pillow_bar__plus_01_dot_17_bar__plus_00_dot_68_bar__plus_00_dot_15 - object
        TissueBox_bar__plus_00_dot_23_bar__plus_00_dot_71_bar__plus_01_dot_46 - object
        TissueBox_bar__plus_03_dot_70_bar__plus_00_dot_78_bar__minus_01_dot_86 - object
        Window_bar__plus_02_dot_27_bar__plus_01_dot_55_bar__minus_02_dot_92 - object
        ArmChair_bar__plus_00_dot_70_bar__plus_00_dot_01_bar__minus_02_dot_27 - receptacle
        Bed_bar__plus_00_dot_94_bar__plus_00_dot_00_bar__plus_00_dot_15 - receptacle
        DiningTable_bar__plus_03_dot_24_bar__minus_00_dot_01_bar__minus_01_dot_59 - receptacle
        Drawer_bar__plus_00_dot_34_bar__plus_00_dot_62_bar__plus_01_dot_30 - receptacle
        Drawer_bar__plus_00_dot_34_bar__plus_00_dot_62_bar__minus_01_dot_02 - receptacle
        GarbageCan_bar__plus_03_dot_64_bar__plus_00_dot_00_bar__minus_00_dot_98 - receptacle
        SideTable_bar__plus_00_dot_28_bar__plus_00_dot_00_bar__plus_01_dot_30 - receptacle
        SideTable_bar__plus_00_dot_28_bar__plus_00_dot_00_bar__minus_01_dot_02 - receptacle
        loc_bar_4_bar__minus_4_bar_3_bar_60 - location
        loc_bar_7_bar_5_bar_3_bar_30 - location
        loc_bar_11_bar_1_bar_3_bar_45 - location
        loc_bar_3_bar_5_bar_0_bar_45 - location
        loc_bar_5_bar__minus_7_bar_3_bar_60 - location
        loc_bar_9_bar__minus_9_bar_2_bar__minus_30 - location
        loc_bar_9_bar__minus_9_bar_2_bar_15 - location
        loc_bar_11_bar__minus_9_bar_1_bar_60 - location
        loc_bar_13_bar__minus_3_bar_1_bar_60 - location
        loc_bar_7_bar__minus_8_bar_3_bar_60 - location
        loc_bar_3_bar_5_bar_3_bar_60 - location
        loc_bar_6_bar__minus_8_bar_3_bar_15 - location
        loc_bar_7_bar_5_bar_0_bar_45 - location
        loc_bar_13_bar__minus_3_bar_2_bar_60 - location
        loc_bar_11_bar_4_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType Drawer_bar__plus_00_dot_34_bar__plus_00_dot_62_bar__minus_01_dot_02 DrawerType)
        (receptacleType Drawer_bar__plus_00_dot_34_bar__plus_00_dot_62_bar__plus_01_dot_30 DrawerType)
        (receptacleType Bed_bar__plus_00_dot_94_bar__plus_00_dot_00_bar__plus_00_dot_15 BedType)
        (receptacleType SideTable_bar__plus_00_dot_28_bar__plus_00_dot_00_bar__plus_01_dot_30 SideTableType)
        (receptacleType ArmChair_bar__plus_00_dot_70_bar__plus_00_dot_01_bar__minus_02_dot_27 ArmChairType)
        (receptacleType GarbageCan_bar__plus_03_dot_64_bar__plus_00_dot_00_bar__minus_00_dot_98 GarbageCanType)
        (receptacleType DiningTable_bar__plus_03_dot_24_bar__minus_00_dot_01_bar__minus_01_dot_59 DiningTableType)
        (receptacleType SideTable_bar__plus_00_dot_28_bar__plus_00_dot_00_bar__minus_01_dot_02 SideTableType)
        (objectType Pillow_bar__plus_00_dot_46_bar__plus_00_dot_64_bar__plus_00_dot_41 PillowType)
        (objectType Pencil_bar__plus_02_dot_78_bar__plus_00_dot_78_bar__minus_01_dot_68 PencilType)
        (objectType Pen_bar__plus_03_dot_70_bar__plus_00_dot_79_bar__minus_01_dot_59 PenType)
        (objectType LightSwitch_bar__plus_01_dot_81_bar__plus_01_dot_17_bar__plus_01_dot_70 LightSwitchType)
        (objectType CD_bar__plus_00_dot_93_bar__plus_00_dot_44_bar__minus_02_dot_35 CDType)
        (objectType Laptop_bar__plus_03_dot_45_bar__plus_00_dot_77_bar__minus_01_dot_67 LaptopType)
        (objectType Book_bar__plus_01_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_43 BookType)
        (objectType CellPhone_bar__plus_03_dot_08_bar__plus_00_dot_78_bar__minus_01_dot_77 CellPhoneType)
        (objectType Mirror_bar__plus_01_dot_08_bar__plus_01_dot_23_bar__plus_01_dot_70 MirrorType)
        (objectType Chair_bar__plus_03_dot_23_bar_00_dot_00_bar__minus_01_dot_87 ChairType)
        (objectType TissueBox_bar__plus_03_dot_70_bar__plus_00_dot_78_bar__minus_01_dot_86 TissueBoxType)
        (objectType Pillow_bar__plus_01_dot_17_bar__plus_00_dot_68_bar__plus_00_dot_15 PillowType)
        (objectType TissueBox_bar__plus_00_dot_23_bar__plus_00_dot_71_bar__plus_01_dot_46 TissueBoxType)
        (objectType AlarmClock_bar__plus_00_dot_29_bar__plus_00_dot_71_bar__plus_01_dot_05 AlarmClockType)
        (objectType Pencil_bar__plus_00_dot_36_bar__plus_00_dot_71_bar__plus_01_dot_30 PencilType)
        (objectType Book_bar__plus_02_dot_96_bar__plus_00_dot_77_bar__minus_01_dot_50 BookType)
        (objectType CellPhone_bar__plus_00_dot_67_bar__plus_00_dot_44_bar__minus_02_dot_07 CellPhoneType)
        (objectType Window_bar__plus_02_dot_27_bar__plus_01_dot_55_bar__minus_02_dot_92 WindowType)
        (objectType Book_bar__plus_00_dot_97_bar__plus_00_dot_60_bar__plus_00_dot_72 BookType)
        (objectType KeyChain_bar__plus_00_dot_89_bar__plus_00_dot_43_bar__minus_01_dot_94 KeyChainType)
        (objectType Blinds_bar__plus_02_dot_30_bar__plus_02_dot_53_bar__minus_02_dot_89 BlindsType)
        (objectType Painting_bar__plus_00_dot_10_bar__plus_01_dot_43_bar__minus_02_dot_05 PaintingType)
        (objectType CreditCard_bar__plus_00_dot_33_bar__plus_00_dot_62_bar__plus_01_dot_45 CreditCardType)
        (objectType KeyChain_bar__plus_00_dot_35_bar__plus_00_dot_62_bar__plus_01_dot_22 KeyChainType)
        (objectType Pen_bar__plus_03_dot_55_bar__plus_00_dot_78_bar__minus_01_dot_32 PenType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType TissueBoxType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType CDType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType AlarmClockType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType CellPhoneType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain GarbageCanType TissueBoxType)
        (canContain DiningTableType PenType)
        (canContain DiningTableType BookType)
        (canContain DiningTableType CDType)
        (canContain DiningTableType CellPhoneType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType PencilType)
        (canContain DiningTableType TissueBoxType)
        (canContain DiningTableType AlarmClockType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType CDType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType AlarmClockType)
        (pickupable Pillow_bar__plus_00_dot_46_bar__plus_00_dot_64_bar__plus_00_dot_41)
        (pickupable Pencil_bar__plus_02_dot_78_bar__plus_00_dot_78_bar__minus_01_dot_68)
        (pickupable Pen_bar__plus_03_dot_70_bar__plus_00_dot_79_bar__minus_01_dot_59)
        (pickupable CD_bar__plus_00_dot_93_bar__plus_00_dot_44_bar__minus_02_dot_35)
        (pickupable Laptop_bar__plus_03_dot_45_bar__plus_00_dot_77_bar__minus_01_dot_67)
        (pickupable Book_bar__plus_01_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_43)
        (pickupable CellPhone_bar__plus_03_dot_08_bar__plus_00_dot_78_bar__minus_01_dot_77)
        (pickupable TissueBox_bar__plus_03_dot_70_bar__plus_00_dot_78_bar__minus_01_dot_86)
        (pickupable Pillow_bar__plus_01_dot_17_bar__plus_00_dot_68_bar__plus_00_dot_15)
        (pickupable TissueBox_bar__plus_00_dot_23_bar__plus_00_dot_71_bar__plus_01_dot_46)
        (pickupable AlarmClock_bar__plus_00_dot_29_bar__plus_00_dot_71_bar__plus_01_dot_05)
        (pickupable Pencil_bar__plus_00_dot_36_bar__plus_00_dot_71_bar__plus_01_dot_30)
        (pickupable Book_bar__plus_02_dot_96_bar__plus_00_dot_77_bar__minus_01_dot_50)
        (pickupable CellPhone_bar__plus_00_dot_67_bar__plus_00_dot_44_bar__minus_02_dot_07)
        (pickupable Book_bar__plus_00_dot_97_bar__plus_00_dot_60_bar__plus_00_dot_72)
        (pickupable KeyChain_bar__plus_00_dot_89_bar__plus_00_dot_43_bar__minus_01_dot_94)
        (pickupable CreditCard_bar__plus_00_dot_33_bar__plus_00_dot_62_bar__plus_01_dot_45)
        (pickupable KeyChain_bar__plus_00_dot_35_bar__plus_00_dot_62_bar__plus_01_dot_22)
        (pickupable Pen_bar__plus_03_dot_55_bar__plus_00_dot_78_bar__minus_01_dot_32)
        
        (openable Drawer_bar__plus_00_dot_34_bar__plus_00_dot_62_bar__minus_01_dot_02)
        (openable Drawer_bar__plus_00_dot_34_bar__plus_00_dot_62_bar__plus_01_dot_30)
        
        (atLocation agent1 loc_bar_11_bar_4_bar_3_bar_30)
        
        
        
        
        
        
        
        
        
        
        
        
        (inReceptacle Book_bar__plus_01_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_43 Bed_bar__plus_00_dot_94_bar__plus_00_dot_00_bar__plus_00_dot_15)
        (inReceptacle Book_bar__plus_00_dot_97_bar__plus_00_dot_60_bar__plus_00_dot_72 Bed_bar__plus_00_dot_94_bar__plus_00_dot_00_bar__plus_00_dot_15)
        (inReceptacle Pillow_bar__plus_00_dot_46_bar__plus_00_dot_64_bar__plus_00_dot_41 Bed_bar__plus_00_dot_94_bar__plus_00_dot_00_bar__plus_00_dot_15)
        (inReceptacle Pillow_bar__plus_01_dot_17_bar__plus_00_dot_68_bar__plus_00_dot_15 Bed_bar__plus_00_dot_94_bar__plus_00_dot_00_bar__plus_00_dot_15)
        (inReceptacle CellPhone_bar__plus_00_dot_67_bar__plus_00_dot_44_bar__minus_02_dot_07 ArmChair_bar__plus_00_dot_70_bar__plus_00_dot_01_bar__minus_02_dot_27)
        (inReceptacle CD_bar__plus_00_dot_93_bar__plus_00_dot_44_bar__minus_02_dot_35 ArmChair_bar__plus_00_dot_70_bar__plus_00_dot_01_bar__minus_02_dot_27)
        (inReceptacle Pencil_bar__plus_02_dot_78_bar__plus_00_dot_78_bar__minus_01_dot_68 DiningTable_bar__plus_03_dot_24_bar__minus_00_dot_01_bar__minus_01_dot_59)
        (inReceptacle Book_bar__plus_02_dot_96_bar__plus_00_dot_77_bar__minus_01_dot_50 DiningTable_bar__plus_03_dot_24_bar__minus_00_dot_01_bar__minus_01_dot_59)
        (inReceptacle TissueBox_bar__plus_03_dot_70_bar__plus_00_dot_78_bar__minus_01_dot_86 DiningTable_bar__plus_03_dot_24_bar__minus_00_dot_01_bar__minus_01_dot_59)
        (inReceptacle Pen_bar__plus_03_dot_70_bar__plus_00_dot_79_bar__minus_01_dot_59 DiningTable_bar__plus_03_dot_24_bar__minus_00_dot_01_bar__minus_01_dot_59)
        (inReceptacle Laptop_bar__plus_03_dot_45_bar__plus_00_dot_77_bar__minus_01_dot_67 DiningTable_bar__plus_03_dot_24_bar__minus_00_dot_01_bar__minus_01_dot_59)
        (inReceptacle CellPhone_bar__plus_03_dot_08_bar__plus_00_dot_78_bar__minus_01_dot_77 DiningTable_bar__plus_03_dot_24_bar__minus_00_dot_01_bar__minus_01_dot_59)
        (inReceptacle Pen_bar__plus_03_dot_55_bar__plus_00_dot_78_bar__minus_01_dot_32 DiningTable_bar__plus_03_dot_24_bar__minus_00_dot_01_bar__minus_01_dot_59)
        (inReceptacle TissueBox_bar__plus_00_dot_23_bar__plus_00_dot_71_bar__plus_01_dot_46 SideTable_bar__plus_00_dot_28_bar__plus_00_dot_00_bar__plus_01_dot_30)
        (inReceptacle AlarmClock_bar__plus_00_dot_29_bar__plus_00_dot_71_bar__plus_01_dot_05 SideTable_bar__plus_00_dot_28_bar__plus_00_dot_00_bar__plus_01_dot_30)
        (inReceptacle Pencil_bar__plus_00_dot_36_bar__plus_00_dot_71_bar__plus_01_dot_30 SideTable_bar__plus_00_dot_28_bar__plus_00_dot_00_bar__plus_01_dot_30)
        (inReceptacle KeyChain_bar__plus_00_dot_35_bar__plus_00_dot_62_bar__plus_01_dot_22 Drawer_bar__plus_00_dot_34_bar__plus_00_dot_62_bar__plus_01_dot_30)
        (inReceptacle CreditCard_bar__plus_00_dot_33_bar__plus_00_dot_62_bar__plus_01_dot_45 Drawer_bar__plus_00_dot_34_bar__plus_00_dot_62_bar__plus_01_dot_30)
        
        
        (receptacleAtLocation ArmChair_bar__plus_00_dot_70_bar__plus_00_dot_01_bar__minus_02_dot_27 loc_bar_7_bar__minus_8_bar_3_bar_60)
        (receptacleAtLocation Bed_bar__plus_00_dot_94_bar__plus_00_dot_00_bar__plus_00_dot_15 loc_bar_11_bar_1_bar_3_bar_45)
        (receptacleAtLocation DiningTable_bar__plus_03_dot_24_bar__minus_00_dot_01_bar__minus_01_dot_59 loc_bar_13_bar__minus_3_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_34_bar__plus_00_dot_62_bar__plus_01_dot_30 loc_bar_7_bar_5_bar_3_bar_30)
        (receptacleAtLocation Drawer_bar__plus_00_dot_34_bar__plus_00_dot_62_bar__minus_01_dot_02 loc_bar_4_bar__minus_4_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_03_dot_64_bar__plus_00_dot_00_bar__minus_00_dot_98 loc_bar_13_bar__minus_3_bar_1_bar_60)
        (receptacleAtLocation SideTable_bar__plus_00_dot_28_bar__plus_00_dot_00_bar__plus_01_dot_30 loc_bar_3_bar_5_bar_3_bar_60)
        (receptacleAtLocation SideTable_bar__plus_00_dot_28_bar__plus_00_dot_00_bar__minus_01_dot_02 loc_bar_4_bar__minus_4_bar_3_bar_60)
        (objectAtLocation CellPhone_bar__plus_00_dot_67_bar__plus_00_dot_44_bar__minus_02_dot_07 loc_bar_7_bar__minus_8_bar_3_bar_60)
        (objectAtLocation TissueBox_bar__plus_00_dot_23_bar__plus_00_dot_71_bar__plus_01_dot_46 loc_bar_3_bar_5_bar_3_bar_60)
        (objectAtLocation Pencil_bar__plus_00_dot_36_bar__plus_00_dot_71_bar__plus_01_dot_30 loc_bar_3_bar_5_bar_3_bar_60)
        (objectAtLocation Pen_bar__plus_03_dot_55_bar__plus_00_dot_78_bar__minus_01_dot_32 loc_bar_13_bar__minus_3_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__plus_00_dot_89_bar__plus_00_dot_43_bar__minus_01_dot_94 loc_bar_5_bar__minus_7_bar_3_bar_60)
        (objectAtLocation Book_bar__plus_02_dot_96_bar__plus_00_dot_77_bar__minus_01_dot_50 loc_bar_13_bar__minus_3_bar_2_bar_60)
        (objectAtLocation Book_bar__plus_01_dot_58_bar__plus_00_dot_60_bar__minus_00_dot_43 loc_bar_11_bar_1_bar_3_bar_45)
        (objectAtLocation Mirror_bar__plus_01_dot_08_bar__plus_01_dot_23_bar__plus_01_dot_70 loc_bar_3_bar_5_bar_0_bar_45)
        (objectAtLocation Chair_bar__plus_03_dot_23_bar_00_dot_00_bar__minus_01_dot_87 loc_bar_11_bar__minus_9_bar_1_bar_60)
        (objectAtLocation Book_bar__plus_00_dot_97_bar__plus_00_dot_60_bar__plus_00_dot_72 loc_bar_11_bar_1_bar_3_bar_45)
        (objectAtLocation KeyChain_bar__plus_00_dot_35_bar__plus_00_dot_62_bar__plus_01_dot_22 loc_bar_7_bar_5_bar_3_bar_30)
        (objectAtLocation Pillow_bar__plus_00_dot_46_bar__plus_00_dot_64_bar__plus_00_dot_41 loc_bar_11_bar_1_bar_3_bar_45)
        (objectAtLocation Pillow_bar__plus_01_dot_17_bar__plus_00_dot_68_bar__plus_00_dot_15 loc_bar_11_bar_1_bar_3_bar_45)
        (objectAtLocation CreditCard_bar__plus_00_dot_33_bar__plus_00_dot_62_bar__plus_01_dot_45 loc_bar_7_bar_5_bar_3_bar_30)
        (objectAtLocation Pen_bar__plus_03_dot_70_bar__plus_00_dot_79_bar__minus_01_dot_59 loc_bar_13_bar__minus_3_bar_2_bar_60)
        (objectAtLocation Pencil_bar__plus_02_dot_78_bar__plus_00_dot_78_bar__minus_01_dot_68 loc_bar_13_bar__minus_3_bar_2_bar_60)
        (objectAtLocation TissueBox_bar__plus_03_dot_70_bar__plus_00_dot_78_bar__minus_01_dot_86 loc_bar_13_bar__minus_3_bar_2_bar_60)
        (objectAtLocation Laptop_bar__plus_03_dot_45_bar__plus_00_dot_77_bar__minus_01_dot_67 loc_bar_13_bar__minus_3_bar_2_bar_60)
        (objectAtLocation CellPhone_bar__plus_03_dot_08_bar__plus_00_dot_78_bar__minus_01_dot_77 loc_bar_13_bar__minus_3_bar_2_bar_60)
        (objectAtLocation AlarmClock_bar__plus_00_dot_29_bar__plus_00_dot_71_bar__plus_01_dot_05 loc_bar_3_bar_5_bar_3_bar_60)
        (objectAtLocation Painting_bar__plus_00_dot_10_bar__plus_01_dot_43_bar__minus_02_dot_05 loc_bar_6_bar__minus_8_bar_3_bar_15)
        (objectAtLocation CD_bar__plus_00_dot_93_bar__plus_00_dot_44_bar__minus_02_dot_35 loc_bar_7_bar__minus_8_bar_3_bar_60)
        (objectAtLocation Window_bar__plus_02_dot_27_bar__plus_01_dot_55_bar__minus_02_dot_92 loc_bar_9_bar__minus_9_bar_2_bar_15)
        (objectAtLocation LightSwitch_bar__plus_01_dot_81_bar__plus_01_dot_17_bar__plus_01_dot_70 loc_bar_7_bar_5_bar_0_bar_45)
        (objectAtLocation Blinds_bar__plus_02_dot_30_bar__plus_02_dot_53_bar__minus_02_dot_89 loc_bar_9_bar__minus_9_bar_2_bar__minus_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PenType)
                                    (receptacleType ?r GarbageCanType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PenType)
                                            (receptacleType ?r GarbageCanType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            