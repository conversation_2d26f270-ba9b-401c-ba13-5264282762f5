{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000209.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000210.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 32}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|1|5|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [3.4454828, 3.4454828, 4.95396232, 4.95396232, 4.82079028, 4.82079028]], "coordinateReceptacleObjectId": ["SideTable", [4.088, 4.088, 3.4844, 3.4844, 4.9732, 4.9732]], "forceVisible": true, "objectId": "Cup|+00.86|+01.21|+01.24"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|3|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [3.4454828, 3.4454828, 4.95396232, 4.95396232, 4.82079028, 4.82079028]], "coordinateReceptacleObjectId": ["Cabinet", [-5.84518432, -5.84518432, 1.8804, 1.8804, 3.100034236, 3.100034236]], "forceVisible": true, "objectId": "Cup|+00.86|+01.21|+01.24", "receptacleObjectId": "Cabinet|-01.46|+00.78|+00.47"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.86|+01.21|+01.24"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [135, 101, 172, 153], "mask": [[30149, 9], [30445, 18], [30743, 22], [31041, 26], [31339, 30], [31638, 32], [31937, 34], [32236, 36], [32536, 36], [32835, 38], [33135, 38], [33435, 38], [33735, 38], [34035, 38], [34335, 38], [34635, 38], [34935, 38], [35235, 38], [35535, 37], [35836, 36], [36136, 36], [36437, 34], [36737, 34], [37038, 32], [37339, 30], [37639, 30], [37940, 28], [38240, 27], [38541, 26], [38841, 26], [39141, 25], [39442, 24], [39742, 24], [40042, 23], [40342, 23], [40642, 23], [40942, 23], [41242, 23], [41542, 23], [41842, 23], [42142, 23], [42442, 23], [42742, 23], [43042, 23], [43342, 23], [43642, 23], [43943, 21], [44243, 21], [44544, 19], [44845, 17], [45146, 15], [45447, 13], [45748, 2], [45757, 2]], "point": [153, 126]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 106], [50560, 47], [50734, 101], [50865, 42], [51034, 98], [51168, 39], [51335, 93], [51472, 35], [51635, 91], [51774, 33], [51935, 89], [52076, 31], [52235, 87], [52378, 28], [52536, 85], [52679, 27], [52836, 83], [52981, 25], [53136, 82], [53282, 24], [53437, 79], [53584, 22], [53737, 78], [53885, 21], [54037, 77]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.86|+01.21|+01.24", "placeStationary": true, "receptacleObjectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 140], [50560, 47], [50700, 135], [50865, 42], [51000, 132], [51168, 39], [51300, 128], [51472, 35], [51600, 126], [51774, 33], [51900, 124], [52076, 31], [52200, 122], [52378, 28], [52500, 43], [52679, 27], [52800, 42], [52981, 25], [53100, 41], [53282, 24], [53400, 41], [53584, 22], [53700, 40], [53885, 21], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 126], [32542, 73], [32700, 123], [32844, 71], [33000, 120], [33146, 69], [33300, 119], [33448, 67], [33600, 118], [33748, 67], [33900, 117], [34049, 66], [34200, 117], [34349, 65], [34500, 117], [34649, 65], [34800, 117], [34949, 65], [35100, 118], [35249, 65], [35400, 118], [35549, 65], [35700, 118], [35848, 66], [36000, 118], [36148, 66], [36300, 119], [36447, 66], [36600, 119], [36747, 66], [36900, 120], [37046, 67], [37200, 120], [37346, 67], [37500, 121], [37646, 67], [37800, 121], [37945, 68], [38100, 122], [38245, 68], [38400, 122], [38545, 68], [38700, 122], [38844, 68], [39000, 123], [39144, 68], [39300, 123], [39444, 68], [39600, 123], [39744, 68], [39900, 124], [40044, 68], [40200, 124], [40344, 68], [40500, 124], [40644, 68], [40800, 124], [40944, 68], [41100, 124], [41244, 67], [41400, 124], [41543, 68], [41700, 124], [41844, 67], [42000, 124], [42144, 67], [42300, 124], [42444, 67], [42600, 124], [42744, 67], [42900, 124], [43044, 67], [43200, 124], [43344, 66], [43500, 124], [43644, 66], [43800, 124], [43944, 66], [44100, 125], [44243, 67], [44400, 125], [44543, 67], [44700, 125], [44843, 67], [45000, 131], [45136, 74], [45300, 132], [45436, 74], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28220, 194], [28518, 197], [28817, 198], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.86|+01.21|+01.24"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [117, 109, 148, 152], "mask": [[32526, 16], [32823, 21], [33120, 26], [33419, 29], [33718, 30], [34017, 32], [34317, 32], [34617, 32], [34917, 32], [35218, 31], [35518, 31], [35818, 30], [36118, 30], [36419, 28], [36719, 28], [37020, 26], [37320, 26], [37621, 25], [37921, 24], [38222, 23], [38522, 23], [38822, 22], [39123, 21], [39423, 21], [39723, 21], [40024, 20], [40324, 20], [40624, 20], [40924, 20], [41224, 20], [41524, 19], [41824, 20], [42124, 20], [42424, 20], [42724, 20], [43024, 20], [43324, 20], [43624, 20], [43924, 20], [44225, 18], [44525, 18], [44825, 18], [45131, 5], [45432, 4]], "point": [132, 129]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23144, 158], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25533, 174], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 210], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 138], [47562, 47], [47700, 133], [47867, 41], [48000, 129], [48171, 37], [48300, 127], [48473, 35], [48600, 124], [48776, 32], [48900, 122], [49078, 30], [49200, 120], [49380, 28], [49500, 118], [49682, 26], [49800, 117], [49983, 25], [50100, 115], [50285, 22], [50400, 114], [50586, 21], [50700, 112], [50888, 19], [51000, 111], [51189, 18], [51300, 110], [51490, 17], [51600, 109], [51791, 16], [51900, 108], [52092, 15], [52200, 108], [52392, 14], [52500, 43], [52693, 13], [52800, 42], [52994, 12], [53100, 41], [53294, 12], [53400, 41], [53595, 11], [53700, 40], [53896, 10], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 34], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 26], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 15], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 4], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-01.46|+00.78|+00.47"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [176, 109, 299, 199], "mask": [[32582, 2], [32882, 11], [33182, 25], [33482, 41], [33782, 58], [34081, 119], [34381, 119], [34681, 119], [34981, 119], [35281, 119], [35581, 119], [35881, 119], [36181, 119], [36481, 119], [36780, 120], [37080, 120], [37380, 120], [37680, 120], [37980, 120], [38280, 120], [38580, 120], [38880, 120], [39180, 120], [39480, 120], [39779, 121], [40079, 121], [40379, 121], [40679, 121], [40979, 121], [41279, 121], [41579, 121], [41879, 121], [42179, 121], [42478, 122], [42778, 122], [43078, 122], [43378, 122], [43678, 122], [43978, 122], [44278, 122], [44578, 122], [44878, 122], [45177, 122], [45477, 122], [45777, 121], [46077, 121], [46377, 120], [46677, 119], [46977, 119], [47277, 118], [47577, 118], [47877, 117], [48176, 118], [48476, 117], [48776, 116], [49078, 114], [49380, 111], [49682, 109], [49983, 107], [50285, 105], [50586, 103], [50888, 100], [51189, 99], [51490, 97], [51791, 96], [52092, 94], [52392, 94], [52693, 92], [52994, 90], [53294, 90], [53595, 88], [53896, 87], [54196, 86], [54497, 85], [54797, 84], [55097, 83], [55397, 83], [55697, 82], [55997, 82], [56297, 81], [56597, 80], [56897, 80], [57197, 79], [57497, 79], [57797, 78], [58096, 78], [58396, 78], [58696, 77], [58996, 77], [59296, 76], [59596, 76]], "point": [237, 153]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.86|+01.21|+01.24", "placeStationary": true, "receptacleObjectId": "Cabinet|-01.46|+00.78|+00.47"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [175, 109, 299, 300], "mask": [[32581, 3], [32881, 12], [33181, 26], [33481, 42], [33780, 60], [34080, 120], [34380, 120], [34680, 120], [34980, 120], [35280, 120], [35580, 120], [35880, 120], [36180, 120], [36480, 120], [36779, 121], [37079, 121], [37379, 121], [37679, 121], [37979, 121], [38279, 121], [38579, 121], [38879, 121], [39179, 121], [39479, 121], [39778, 63], [39844, 56], [40078, 61], [40148, 52], [40378, 60], [40450, 50], [40678, 59], [40751, 49], [40978, 59], [41052, 48], [41278, 59], [41352, 48], [41578, 59], [41652, 48], [41878, 59], [41952, 48], [42178, 59], [42252, 48], [42477, 60], [42551, 49], [42777, 60], [42851, 49], [43077, 60], [43150, 49], [43377, 60], [43448, 51], [43677, 60], [43747, 51], [43977, 59], [44047, 51], [44277, 59], [44346, 51], [44577, 59], [44646, 51], [44877, 58], [44946, 50], [45177, 59], [45246, 49], [45476, 61], [45545, 50], [45776, 62], [45844, 50], [46076, 118], [46376, 117], [46676, 117], [46976, 116], [47276, 115], [47576, 115], [47876, 114], [48176, 114], [48475, 114], [48776, 113], [49078, 110], [49380, 107], [49682, 105], [49983, 103], [50285, 101], [50586, 99], [50888, 97], [51189, 95], [51490, 94], [51791, 92], [52092, 90], [52392, 90], [52693, 88], [52994, 87], [53294, 86], [53595, 85], [53896, 83], [54196, 82], [54497, 81], [54797, 80], [55097, 80], [55397, 79], [55697, 79], [55997, 78], [56297, 78], [56597, 77], [56897, 76], [57197, 76], [57497, 75], [57797, 75], [58096, 75], [58396, 75], [58696, 74], [58996, 8], [59296, 8], [59596, 8], [59896, 8], [60195, 10], [60495, 10], [60795, 10], [61095, 10], [61395, 10], [61694, 12], [61994, 12], [62293, 13], [62593, 13], [62893, 13], [63192, 14], [63492, 15], [63791, 16], [64090, 17], [64390, 17], [64689, 18], [64988, 19], [65287, 21], [65586, 22], [65885, 23], [66184, 24], [66483, 25], [66783, 26], [67082, 27], [67381, 28], [67681, 28], [67980, 29], [68280, 29], [68579, 31], [68879, 31], [69179, 31], [69478, 31], [69778, 31], [70078, 31], [70377, 31], [70677, 31], [70977, 31], [71277, 30], [71576, 31], [71876, 31], [72176, 30], [72476, 30], [72776, 30], [73076, 29], [73377, 28], [73677, 28], [73977, 27], [74277, 27], [74577, 27], [74877, 26], [75177, 26], [75477, 26], [75777, 25], [76077, 25], [76377, 25], [76677, 24], [76977, 24], [77276, 25], [77576, 24], [77876, 24], [78176, 24], [78476, 23], [78776, 23], [79076, 23], [79376, 22], [79676, 22], [79976, 22], [80276, 21], [80577, 20], [80877, 20], [81177, 19], [81477, 19], [81777, 19], [82077, 18], [82377, 18], [82677, 18], [82977, 17], [83277, 17], [83577, 17], [83877, 16], [84177, 16], [84477, 16], [84777, 15], [85077, 15], [85377, 15], [85677, 14], [85977, 14], [86276, 15], [86576, 14], [86876, 14], [87176, 14], [87476, 13], [87776, 13], [88076, 13], [88376, 12], [88678, 10], [88979, 9], [89279, 8], [89579, 8], [89879, 8]], "point": [237, 195]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-01.46|+00.78|+00.47"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [172, 109, 299, 300], "mask": [[32581, 3], [32881, 12], [33181, 26], [33481, 42], [33780, 60], [34080, 120], [34380, 120], [34680, 120], [34980, 120], [35280, 120], [35580, 120], [35880, 120], [36180, 120], [36480, 120], [36779, 121], [37079, 121], [37379, 121], [37679, 121], [37979, 121], [38279, 121], [38579, 121], [38879, 121], [39179, 121], [39479, 121], [39778, 63], [39844, 56], [40078, 61], [40148, 52], [40378, 60], [40450, 50], [40678, 59], [40751, 49], [40978, 59], [41052, 48], [41278, 59], [41352, 48], [41578, 59], [41652, 48], [41878, 59], [41952, 48], [42178, 59], [42252, 48], [42477, 60], [42551, 49], [42777, 60], [42851, 49], [43077, 60], [43150, 49], [43377, 60], [43448, 51], [43677, 60], [43747, 51], [43977, 59], [44047, 51], [44277, 59], [44346, 51], [44577, 59], [44646, 51], [44877, 58], [44946, 50], [45177, 59], [45246, 49], [45476, 61], [45545, 50], [45776, 62], [45844, 50], [46076, 118], [46376, 117], [46676, 117], [46976, 116], [47276, 115], [47576, 115], [47876, 114], [48176, 114], [48475, 114], [48775, 114], [49075, 113], [49375, 112], [49675, 49], [49727, 60], [49975, 46], [50031, 55], [50275, 45], [50333, 53], [50575, 44], [50634, 51], [50875, 44], [50935, 50], [51175, 44], [51235, 49], [51474, 45], [51536, 48], [51774, 45], [51836, 47], [52074, 45], [52136, 46], [52374, 45], [52436, 46], [52674, 45], [52736, 45], [52974, 46], [53035, 46], [53274, 46], [53335, 45], [53574, 46], [53634, 46], [53874, 45], [53932, 47], [54173, 46], [54231, 47], [54473, 45], [54530, 48], [54773, 45], [54830, 47], [55073, 45], [55130, 47], [55373, 45], [55429, 47], [55673, 46], [55729, 47], [55973, 47], [56028, 47], [56273, 48], [56328, 47], [56573, 101], [56873, 100], [57172, 101], [57472, 100], [57772, 100], [58072, 99], [58372, 99], [58672, 98], [58972, 32], [59272, 32], [59572, 32], [59872, 32], [60172, 33], [60472, 33], [60772, 33], [61072, 33], [61372, 33], [61672, 34], [61972, 34], [62272, 34], [62572, 34], [62872, 34], [63172, 34], [63473, 34], [63773, 34], [64073, 34], [64373, 34], [64673, 34], [64973, 34], [65273, 35], [65573, 35], [65873, 35], [66173, 35], [66473, 35], [66773, 36], [67073, 36], [67373, 36], [67674, 35], [67974, 35], [68274, 35], [68574, 36], [68874, 36], [69174, 36], [69474, 35], [69774, 35], [70074, 35], [70374, 34], [70674, 34], [70974, 34], [71274, 33], [71574, 33], [71875, 32], [72175, 31], [72475, 31], [72775, 31], [73075, 30], [73375, 30], [73675, 30], [73975, 29], [74275, 29], [74575, 29], [74875, 28], [75175, 28], [75475, 28], [75775, 27], [76075, 27], [76376, 26], [76676, 25], [76976, 25], [77276, 25], [77576, 24], [77876, 24], [78176, 24], [78476, 23], [78776, 23], [79076, 23], [79376, 22], [79676, 22], [79976, 22], [80276, 21], [80577, 20], [80877, 20], [81177, 19], [81477, 19], [81777, 19], [82077, 18], [82377, 18], [82677, 18], [82977, 17], [83277, 17], [83577, 17], [83877, 16], [84177, 16], [84477, 16], [84777, 15], [85077, 15], [85377, 15], [85677, 14], [85977, 14], [86276, 15], [86576, 14], [86876, 14], [87176, 14], [87476, 13], [87776, 13], [88076, 13], [88376, 12], [88678, 10], [88979, 9], [89279, 8], [89579, 8], [89879, 8]], "point": [235, 195]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan3", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 0.25, "y": 1.12401652, "z": 0.25}, "object_poses": [{"objectName": "Pan_9d168802", "position": {"x": -1.9200685, "y": 1.32260954, "z": 0.445294976}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": -1.16594541, "y": 1.36275363, "z": -3.31436586}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.363688, "y": 1.32392883, "z": -0.3328594}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -0.125, "y": 1.3324, "z": -2.973}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -1.88644969, "y": 0.344855428, "z": 1.21397424}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": -1.95454407, "y": 1.05159819, "z": -0.587838531}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.73021924, "y": 0.437571168, "z": -0.561244547}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": 0.900508642, "y": 0.351404667, "z": -1.85446763}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.2394, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -2.04982138, "y": 1.36382449, "z": 2.069365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": 0.818656, "y": 1.56534374, "z": 1.78658581}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": 0.900508642, "y": 0.34236908, "z": -2.1643877}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": -1.95251179, "y": 0.341211677, "z": 0.110898912}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": 0.8613707, "y": 1.20519757, "z": 1.23849058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.53081059, "y": 1.35270047, "z": -3.24110079}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.73758006, "y": 1.35868657, "z": -1.18665123}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.68100739, "y": 0.280376852, "z": 2.23422623}, "rotation": {"x": 0.0, "y": 6.83018925e-06, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": -1.61458409, "y": 0.33881402, "z": 1.62720108}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": 1.19326913, "y": 1.25668371, "z": 1.23849058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.6980927, "y": 1.09993339, "z": -0.496626765}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": -1.363688, "y": 1.39739525, "z": -1.50393641}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.749, "y": 1.3324, "z": -2.973}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.79031563, "y": 1.35270047, "z": -3.24110079}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -2.09335637, "y": 1.33261383, "z": -1.04922915}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": 1.285346, "y": 1.36275363, "z": -3.164938}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.5869925, "y": 0.280376852, "z": 2.110548}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": 0.9818125, "y": 0.9538326, "z": 1.019027}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": -1.95251179, "y": 0.341211677, "z": 0.7134931}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -1.691, "y": 1.31039894, "z": -3.156}, "rotation": {"x": 0.0, "y": 150.0004, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": 0.9202187, "y": 1.56534374, "z": 1.84843886}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_9d168802", "position": {"x": -1.92006814, "y": 1.32260954, "z": -2.20134926}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": -1.817123, "y": 0.338814139, "z": 0.211331308}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.68255043, "y": 0.3447429, "z": 1.11066759}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": 0.349309921, "y": 1.39912164, "z": -2.78936434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.5189867, "y": 0.437571168, "z": -1.348922}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_ddd73f57", "position": {"x": 0.6347654, "y": 1.42728543, "z": -2.90023613}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": -1.76220548, "y": 1.08497071, "z": -0.633444369}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -1.78949094, "y": 0.344428062, "z": -2.162692}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": -1.4514004, "y": 1.31800008, "z": -3.09262371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": -1.74942863, "y": 0.343378425, "z": 0.110898912}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -1.9200685, "y": 1.32412946, "z": 1.4197371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.16594541, "y": 1.37188375, "z": -3.203495}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.965999663, "y": 1.3713994, "z": -1.53499949}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}], "object_toggles": [], "random_seed": 2817796482, "scene_num": 3}, "task_id": "trial_T20190908_232857_744331", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1SX8IVV82M0LW_3COPXFW7XE325ZPY5RKCIYX8U0CKPW", "high_descs": ["Walk to the edge of the dresser on your left.", "Grab the green cup from the dresser.", "Walk to the microwave on the dresser.", "Put the cup in the microwave, then grab it.", "Walk to the kitchen counter behind you.", "Put the cup away in the bottom right cabinet."], "task_desc": "Heat a cup and put it away in a cabinet.", "votes": [1, 1]}, {"assignment_id": "A3PPLDHC3CG0YN_3IFS6Q0HJLA8CWF2X53U0NN6PG0IS5", "high_descs": ["Walk to the counter to the left of the microwave. ", "Pick up the green mug in front of the apple. ", "Move to the front of the microwave on the right. ", "Open the microwave door, place the mug inside of the microwave, close the door, heat the mug, open the door, take the mug out, close the door. ", "Turn around and face the cabinet below the counter with the pan. ", "Open the cabinet door, place the mug inside of the cabinet, close the door. "], "task_desc": "To heat the mug and place it in the cabinet. ", "votes": [1, 1]}, {"assignment_id": "ASP32QB28TEZU_3U8YCDAGXS7LSLJ91BPJZJ89LYU0QZ", "high_descs": ["Turn left, walk to fridge", "Get green cup from counter", "Turn right, move to front of microwave", "Put cup in microwave, cook, get cup from microwave", "Turn right, walk towards sink", "Put cup in the bottom cupboard to the right of sink"], "task_desc": "<PERSON> green cup in microwave, put cup in cupboard.", "votes": [1, 1]}]}}