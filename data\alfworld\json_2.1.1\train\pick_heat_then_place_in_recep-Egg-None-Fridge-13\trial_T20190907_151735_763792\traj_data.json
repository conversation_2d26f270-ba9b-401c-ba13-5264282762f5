{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 44}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 45}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 63}, {"high_idx": 5, "image_name": "000000321.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000322.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000323.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000324.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000325.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000326.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000327.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000328.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000329.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000330.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000331.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000332.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000333.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000334.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000335.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000336.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000337.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000338.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000339.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000340.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000341.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000342.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000343.png", "low_idx": 66}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|18|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-0.419637144, -0.419637144, 18.46, 18.46, 3.843758, 3.843758]], "coordinateReceptacleObjectId": ["CounterTop", [-1.384, -1.384, 22.412, 22.412, 3.8424, 3.8424]], "forceVisible": true, "objectId": "Egg|-00.10|+00.96|+04.62"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-11|10|2|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|15|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "fridge"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-0.419637144, -0.419637144, 18.46, 18.46, 3.843758, 3.843758]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-1.298918724, -1.298918724, 14.412, 14.412, -0.00599211456, -0.00599211456]], "forceVisible": true, "objectId": "Egg|-00.10|+00.96|+04.62", "receptacleObjectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.10|+00.96|+04.62"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [114, 95, 129, 112], "mask": [[28320, 4], [28618, 8], [28917, 10], [29216, 12], [29515, 14], [29815, 14], [30115, 15], [30414, 16], [30714, 16], [31014, 16], [31314, 16], [31614, 16], [31915, 15], [32215, 15], [32516, 13], [32816, 12], [33117, 10], [33419, 7]], "point": [121, 102]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.10|+00.96|+04.62", "placeStationary": true, "receptacleObjectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 147], [16648, 116], [16800, 147], [16948, 115], [17100, 140], [17244, 3], [17248, 115], [17400, 138], [17556, 107], [17700, 136], [17858, 105], [18000, 136], [18159, 104], [18300, 66], [18415, 20], [18459, 104], [18600, 62], [18725, 10], [18759, 104], [18900, 62], [19027, 7], [19060, 103], [19200, 63], [19327, 7], [19360, 103], [19500, 63], [19627, 7], [19660, 102], [19800, 63], [19926, 8], [19960, 102], [20100, 64], [20226, 8], [20260, 102], [20400, 64], [20526, 9], [20560, 102], [20700, 65], [20825, 10], [20860, 102], [21000, 65], [21125, 10], [21160, 102], [21300, 66], [21425, 10], [21460, 102], [21600, 66], [21724, 11], [21759, 103], [21900, 66], [22024, 11], [22059, 103], [22200, 67], [22324, 12], [22359, 102], [22500, 68], [22623, 13], [22659, 102], [22800, 68], [22923, 13], [22959, 102], [23100, 69], [23222, 14], [23258, 103], [23400, 70], [23522, 14], [23558, 103], [23700, 70], [23821, 15], [23858, 103], [24000, 71], [24120, 16], [24158, 103], [24300, 72], [24419, 17], [24458, 103], [24600, 73], [24719, 17], [24758, 103], [24900, 73], [25019, 17], [25058, 103], [25200, 74], [25318, 19], [25358, 102], [25500, 75], [25617, 20], [25658, 102], [25800, 76], [25916, 21], [25957, 103], [26100, 78], [26215, 23], [26256, 104], [26400, 78], [26515, 24], [26556, 104], [26700, 78], [26815, 25], [26855, 105], [27000, 79], [27113, 147], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [133, 65]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 147], [16648, 116], [16800, 147], [16948, 115], [17100, 140], [17244, 3], [17248, 115], [17400, 138], [17556, 107], [17700, 136], [17858, 105], [18000, 136], [18159, 104], [18300, 66], [18415, 20], [18459, 104], [18600, 62], [18725, 10], [18759, 104], [18900, 62], [19027, 7], [19060, 103], [19200, 63], [19327, 7], [19360, 103], [19500, 63], [19627, 7], [19660, 102], [19800, 63], [19926, 8], [19960, 102], [20100, 64], [20226, 8], [20260, 102], [20400, 64], [20526, 9], [20560, 102], [20700, 65], [20825, 10], [20860, 102], [21000, 65], [21125, 10], [21160, 102], [21300, 66], [21425, 10], [21460, 102], [21600, 66], [21724, 11], [21759, 103], [21900, 66], [22024, 11], [22059, 103], [22200, 67], [22324, 12], [22359, 102], [22500, 68], [22623, 13], [22659, 102], [22800, 68], [22923, 13], [22959, 102], [23100, 69], [23222, 14], [23258, 103], [23400, 70], [23522, 14], [23558, 103], [23700, 70], [23821, 15], [23858, 103], [24000, 71], [24120, 16], [24158, 103], [24300, 72], [24419, 17], [24458, 103], [24600, 73], [24719, 17], [24758, 103], [24900, 73], [25019, 17], [25058, 103], [25200, 74], [25318, 19], [25358, 102], [25500, 75], [25617, 20], [25658, 102], [25800, 76], [25916, 21], [25957, 103], [26100, 78], [26215, 23], [26256, 104], [26400, 78], [26515, 24], [26556, 104], [26700, 78], [26815, 25], [26855, 105], [27000, 79], [27113, 30], [27151, 109], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [133, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.776, -10.776, 6.75624992, 6.75624992, 5.72338344, 5.72338344]], "forceVisible": true, "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 264], [16800, 263], [17100, 263], [17400, 263], [17700, 263], [18000, 263], [18300, 263], [18600, 263], [18900, 263], [19200, 263], [19500, 262], [19800, 262], [20100, 262], [20400, 262], [20700, 262], [21000, 262], [21300, 262], [21600, 262], [21900, 262], [22200, 261], [22500, 261], [22800, 261], [23100, 261], [23400, 261], [23700, 261], [24000, 261], [24300, 261], [24600, 261], [24900, 261], [25200, 260], [25500, 260], [25800, 260], [26100, 260], [26400, 260], [26700, 260], [27000, 260], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28501, 258], [28801, 258], [29101, 258], [29401, 258], [29701, 258], [30001, 258], [30301, 258], [30602, 257], [30902, 256], [31202, 256], [31502, 256], [31802, 256], [32102, 256], [32402, 256], [32703, 255], [33003, 255], [33303, 255], [33603, 254], [33903, 254], [34203, 254], [34503, 254], [34804, 253], [35104, 253], [35405, 251], [35704, 253], [36004, 253], [36304, 253], [36604, 253], [36946, 8], [37118, 9], [37246, 9], [37418, 9], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [134, 65]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.10|+00.96|+04.62"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [136, 65, 157, 91], "mask": [[19343, 7], [19642, 10], [19941, 12], [20240, 14], [20539, 16], [20838, 17], [21138, 18], [21437, 20], [21737, 20], [22036, 21], [22336, 22], [22636, 22], [22936, 22], [23236, 22], [23536, 22], [23836, 22], [24136, 22], [24436, 22], [24736, 21], [25037, 20], [25337, 20], [25638, 18], [25938, 17], [26239, 16], [26540, 14], [26841, 11], [27143, 8]], "point": [146, 77]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.69|+01.43|+01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 268, 131], "mask": [[0, 269], [300, 269], [600, 269], [900, 269], [1200, 269], [1500, 269], [1800, 269], [2100, 269], [2400, 269], [2700, 268], [3000, 268], [3300, 268], [3600, 268], [3900, 268], [4200, 268], [4500, 268], [4800, 268], [5100, 268], [5400, 267], [5700, 267], [6000, 267], [6300, 267], [6600, 267], [6900, 267], [7200, 267], [7500, 267], [7800, 267], [8100, 267], [8400, 266], [8700, 266], [9000, 266], [9300, 266], [9600, 266], [9900, 266], [10200, 266], [10500, 266], [10800, 266], [11100, 265], [11400, 265], [11700, 265], [12000, 265], [12300, 265], [12600, 265], [12900, 265], [13200, 265], [13500, 265], [13800, 264], [14100, 264], [14400, 264], [14700, 264], [15000, 264], [15300, 264], [15600, 264], [15900, 264], [16200, 264], [16500, 147], [16648, 116], [16800, 147], [16948, 115], [17100, 140], [17244, 3], [17248, 115], [17400, 138], [17556, 107], [17700, 136], [17858, 105], [18000, 136], [18159, 104], [18300, 66], [18415, 20], [18459, 104], [18600, 62], [18725, 10], [18759, 104], [18900, 62], [19027, 7], [19060, 103], [19200, 63], [19327, 7], [19360, 103], [19500, 63], [19627, 7], [19660, 102], [19800, 63], [19926, 8], [19960, 102], [20100, 64], [20226, 8], [20260, 102], [20400, 64], [20526, 9], [20560, 102], [20700, 65], [20825, 10], [20860, 102], [21000, 65], [21125, 10], [21160, 102], [21300, 66], [21425, 10], [21460, 102], [21600, 66], [21724, 11], [21759, 103], [21900, 66], [22024, 11], [22059, 103], [22200, 67], [22324, 12], [22359, 102], [22500, 68], [22623, 13], [22659, 102], [22800, 68], [22923, 13], [22959, 102], [23100, 69], [23222, 14], [23258, 103], [23400, 70], [23522, 14], [23558, 103], [23700, 70], [23821, 15], [23858, 103], [24000, 71], [24120, 16], [24158, 103], [24300, 72], [24419, 17], [24458, 103], [24600, 73], [24719, 17], [24758, 103], [24900, 73], [25019, 17], [25058, 103], [25200, 74], [25318, 19], [25358, 102], [25500, 75], [25617, 20], [25658, 102], [25800, 76], [25916, 21], [25957, 103], [26100, 78], [26215, 23], [26256, 104], [26400, 78], [26515, 24], [26556, 104], [26700, 78], [26815, 25], [26855, 105], [27000, 79], [27113, 147], [27300, 260], [27600, 260], [27900, 259], [28200, 259], [28500, 259], [28800, 259], [29100, 259], [29400, 259], [29700, 259], [30000, 259], [30300, 259], [30600, 259], [30900, 258], [31200, 258], [31500, 258], [31800, 258], [32100, 258], [32400, 258], [32700, 258], [33000, 258], [33300, 258], [33600, 257], [33900, 257], [34200, 257], [34500, 257], [34800, 257], [35100, 257], [35400, 256], [35700, 257], [36000, 257], [36300, 257], [36600, 257], [36900, 8], [36946, 8], [37118, 9], [37200, 5], [37246, 9], [37418, 9], [37500, 2], [37546, 9], [37718, 9], [37846, 9], [38018, 9], [38146, 9], [38318, 8], [38446, 9], [38618, 8], [38746, 9], [38918, 8], [39047, 8], [39218, 8]], "point": [133, 65]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 199], "mask": [[0, 18600], [18601, 299], [18901, 299], [19202, 298], [19503, 297], [19803, 297], [20104, 296], [20405, 295], [20705, 295], [21006, 294], [21307, 293], [21607, 293], [21908, 292], [22208, 292], [22509, 291], [22810, 290], [23110, 290], [23411, 289], [23712, 288], [24012, 288], [24313, 287], [24613, 287], [24914, 286], [25215, 285], [25515, 285], [25816, 284], [26117, 283], [26417, 283], [26718, 282], [27018, 282], [27319, 281], [27620, 280], [27920, 280], [28221, 279], [28522, 278], [28822, 278], [29123, 277], [29423, 277], [29724, 276], [30025, 275], [30325, 275], [30626, 274], [30927, 273], [31227, 273], [31528, 272], [31828, 272], [32129, 271], [32430, 270], [32730, 270], [33031, 269], [33332, 268], [33632, 268], [33933, 267], [34234, 266], [34534, 266], [34835, 265], [35135, 265], [35439, 261], [35739, 261], [36040, 260], [36339, 261], [36639, 261], [36939, 261], [37238, 262], [37539, 261], [37839, 261], [38142, 258], [38442, 258], [38743, 257], [39044, 256], [39344, 256], [39645, 255], [39945, 255], [40246, 254], [40547, 253], [40847, 253], [41148, 252], [41449, 251], [41749, 251], [42050, 250], [42350, 250], [42651, 249], [42952, 248], [43252, 248], [43553, 247], [43854, 246], [44154, 246], [44455, 245], [44755, 245], [45056, 244], [45357, 243], [45657, 243], [45958, 242], [46259, 241], [46559, 241], [46860, 240], [47160, 240], [47461, 239], [47762, 237], [48062, 236], [48363, 234], [48664, 232], [48964, 231], [49265, 229], [49565, 228], [49866, 225], [50167, 223], [50467, 222], [50768, 220], [51069, 218], [51369, 217], [51670, 215], [51971, 213], [52271, 212], [52572, 210], [52872, 209], [53173, 207], [53474, 205], [53774, 204], [54075, 202], [54376, 200], [54676, 198], [54977, 196], [55277, 195], [55578, 193], [55879, 191], [56179, 190], [56480, 188], [56781, 186], [57081, 185], [57384, 179], [57684, 11], [57719, 109], [57851, 12], [57984, 11], [58151, 12], [58284, 10], [58451, 12], [58584, 10], [58752, 11], [58885, 9], [59052, 10], [59185, 9], [59352, 10], [59485, 9], [59652, 10]], "point": [149, 99]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.10|+00.96|+04.62", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 266], "mask": [[0, 94], [139, 34], [216, 178], [439, 35], [515, 179], [739, 35], [814, 180], [1039, 36], [1113, 181], [1339, 37], [1412, 182], [1639, 38], [1711, 183], [1939, 39], [2010, 184], [2239, 41], [2308, 186], [2539, 43], [2607, 188], [2839, 44], [2905, 190], [3139, 46], [3203, 192], [3439, 52], [3497, 198], [3739, 256], [4039, 257], [4338, 258], [4638, 258], [4938, 258], [5238, 259], [5538, 259], [5838, 260], [6138, 260], [6437, 262], [6736, 263], [7035, 265], [7334, 267], [7633, 270], [7931, 276], [8226, 27813], [36040, 598], [36639, 299], [36939, 599], [37539, 298], [37839, 298], [38142, 295], [38442, 295], [38743, 293], [39044, 292], [39344, 292], [39645, 291], [39945, 290], [40246, 289], [40547, 288], [40847, 287], [41148, 286], [41449, 285], [41749, 285], [42050, 283], [42350, 283], [42651, 282], [42952, 281], [43252, 280], [43553, 279], [43854, 278], [44154, 278], [44455, 276], [44755, 276], [45056, 275], [45357, 274], [45657, 273], [45958, 272], [46259, 271], [46559, 270], [46860, 269], [47160, 269], [47461, 268], [47762, 237], [48000, 28], [48062, 236], [48300, 28], [48363, 234], [48600, 28], [48664, 232], [48900, 28], [48964, 231], [49200, 27], [49265, 229], [49500, 27], [49565, 228], [49800, 27], [49866, 225], [50100, 27], [50167, 223], [50400, 26], [50467, 222], [50700, 26], [50768, 220], [51000, 26], [51069, 218], [51300, 25], [51369, 217], [51600, 25], [51670, 215], [51900, 25], [51971, 213], [52200, 25], [52271, 212], [52500, 24], [52572, 210], [52800, 24], [52872, 209], [53100, 24], [53173, 207], [53400, 24], [53474, 205], [53700, 23], [53774, 204], [54000, 23], [54075, 202], [54300, 23], [54376, 200], [54600, 23], [54676, 198], [54900, 22], [54977, 196], [55200, 22], [55277, 195], [55500, 22], [55578, 193], [55800, 22], [55879, 191], [56100, 21], [56179, 190], [56400, 21], [56480, 188], [56700, 21], [56781, 186], [57000, 20], [57081, 185], [57300, 20], [57384, 179], [57600, 20], [57684, 11], [57719, 109], [57851, 12], [57900, 20], [57984, 11], [58151, 12], [58200, 19], [58284, 10], [58451, 12], [58500, 19], [58584, 10], [58752, 11], [58800, 19], [58885, 9], [59052, 10], [59100, 19], [59185, 9], [59352, 10], [59400, 18], [59485, 9], [59652, 10], [59700, 18], [60000, 18], [60300, 18], [60600, 17], [60900, 17], [61200, 17], [61500, 17], [61800, 16], [62100, 16], [62400, 16], [62700, 15], [63000, 15], [63300, 15], [63600, 15], [63900, 14], [64200, 14], [64500, 14], [64800, 14], [65100, 13], [65400, 13], [65700, 13], [66000, 13], [66300, 12], [66600, 12], [66900, 12], [67200, 11], [67500, 11], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 10], [69300, 10], [69600, 9], [69900, 9], [70200, 9], [70500, 9], [70800, 8], [71100, 8], [71400, 8], [71700, 8], [72000, 7], [72300, 7], [72600, 7], [72900, 6], [73200, 6], [73500, 6], [73800, 6], [74100, 5], [74400, 5], [74700, 5], [75000, 5], [75300, 4], [75600, 4], [75900, 4], [76200, 4], [76500, 3], [76800, 3], [77100, 3], [77400, 3], [77700, 2], [78000, 2], [78300, 2], [78600, 1], [78900, 1], [79200, 1], [79500, 1]], "point": [149, 132]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.32|00.00|+03.60"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 266], "mask": [[0, 94], [139, 14], [163, 10], [216, 178], [439, 13], [464, 10], [515, 179], [739, 12], [765, 9], [814, 180], [1039, 11], [1066, 9], [1113, 181], [1339, 10], [1366, 10], [1412, 182], [1639, 10], [1667, 10], [1711, 183], [1939, 9], [1967, 11], [2010, 184], [2239, 9], [2268, 12], [2308, 186], [2539, 8], [2568, 14], [2607, 188], [2839, 8], [2868, 15], [2905, 190], [3139, 8], [3169, 16], [3203, 192], [3439, 8], [3469, 22], [3497, 198], [3739, 8], [3769, 226], [4039, 8], [4069, 227], [4338, 9], [4369, 227], [4638, 9], [4669, 227], [4938, 9], [4969, 227], [5238, 9], [5268, 229], [5538, 9], [5568, 229], [5838, 10], [5868, 230], [6138, 10], [6167, 231], [6437, 12], [6467, 232], [6736, 13], [6766, 233], [7035, 15], [7065, 235], [7334, 17], [7364, 237], [7633, 19], [7663, 240], [7931, 23], [7961, 246], [8226, 27813], [36040, 598], [36639, 299], [36939, 599], [37539, 298], [37839, 298], [38142, 295], [38442, 295], [38743, 293], [39044, 292], [39344, 292], [39645, 291], [39945, 290], [40246, 289], [40547, 288], [40847, 287], [41148, 286], [41449, 285], [41749, 285], [42050, 283], [42350, 283], [42651, 282], [42952, 281], [43252, 280], [43553, 279], [43854, 278], [44154, 278], [44455, 276], [44755, 276], [45056, 275], [45357, 274], [45657, 273], [45958, 272], [46259, 271], [46559, 270], [46860, 269], [47160, 269], [47461, 268], [47762, 237], [48000, 28], [48062, 236], [48300, 28], [48363, 234], [48600, 28], [48664, 232], [48900, 28], [48964, 231], [49200, 27], [49265, 229], [49500, 27], [49565, 228], [49800, 27], [49866, 225], [50100, 27], [50167, 223], [50400, 26], [50467, 222], [50700, 26], [50768, 220], [51000, 26], [51069, 218], [51300, 25], [51369, 217], [51600, 25], [51670, 215], [51900, 25], [51971, 213], [52200, 25], [52271, 212], [52500, 24], [52572, 210], [52800, 24], [52872, 209], [53100, 24], [53173, 207], [53400, 24], [53474, 205], [53700, 23], [53774, 204], [54000, 23], [54075, 202], [54300, 23], [54376, 200], [54600, 23], [54676, 198], [54900, 22], [54977, 196], [55200, 22], [55277, 195], [55500, 22], [55578, 193], [55800, 22], [55879, 191], [56100, 21], [56179, 190], [56400, 21], [56480, 188], [56700, 21], [56781, 186], [57000, 20], [57081, 185], [57300, 20], [57384, 179], [57600, 20], [57684, 11], [57719, 109], [57851, 12], [57900, 20], [57984, 11], [58151, 12], [58200, 19], [58284, 10], [58451, 12], [58500, 19], [58584, 10], [58752, 11], [58800, 19], [58885, 9], [59052, 10], [59100, 19], [59185, 9], [59352, 10], [59400, 18], [59485, 9], [59652, 10], [59700, 18], [60000, 18], [60300, 18], [60600, 17], [60900, 17], [61200, 17], [61500, 17], [61800, 16], [62100, 16], [62400, 16], [62700, 15], [63000, 15], [63300, 15], [63600, 15], [63900, 14], [64200, 14], [64500, 14], [64800, 14], [65100, 13], [65400, 13], [65700, 13], [66000, 13], [66300, 12], [66600, 12], [66900, 12], [67200, 11], [67500, 11], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 10], [69300, 10], [69600, 9], [69900, 9], [70200, 9], [70500, 9], [70800, 8], [71100, 8], [71400, 8], [71700, 8], [72000, 7], [72300, 7], [72600, 7], [72900, 6], [73200, 6], [73500, 6], [73800, 6], [74100, 5], [74400, 5], [74700, 5], [75000, 5], [75300, 4], [75600, 4], [75900, 4], [76200, 4], [76500, 3], [76800, 3], [77100, 3], [77400, 3], [77700, 2], [78000, 2], [78300, 2], [78600, 1], [78900, 1], [79200, 1], [79500, 1]], "point": [149, 132]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan13", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -2.75, "y": 0.8995012, "z": 2.5}, "object_poses": [{"objectName": "Pan_94f6c891", "position": {"x": -2.469, "y": 0.9303, "z": 1.712}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -0.4734755, "y": 0.747098565, "z": 5.99718952}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -4.125074, "y": 0.747251868, "z": 4.58567047}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -3.37461424, "y": 0.95696795, "z": 5.41275358}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.488323241, "y": 0.958568037, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -4.18449736, "y": 0.923064232, "z": 4.90006828}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -0.334957659, "y": 0.924664259, "z": 5.603}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -1.86012983, "y": 0.924428642, "z": 2.02650762}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -0.488323241, "y": 0.9262294, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -0.5030999, "y": 0.7479368, "z": 6.48951054}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -3.34089136, "y": 0.938499868, "z": 1.95100212}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -3.68638015, "y": 0.938499868, "z": 2.02650476}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -4.19640064, "y": 0.7898166, "z": 3.41021824}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Cup_ba2c4270", "position": {"x": -4.10899448, "y": 0.9223595, "z": 3.86360288}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -0.104909286, "y": 0.9609395, "z": 4.615}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_53fee662", "position": {"x": -4.19639969, "y": 0.791783035, "z": 3.159811}, "rotation": {"x": -1.44077641e-20, "y": 269.999939, "z": -1.40334191e-14}}, {"objectName": "Plate_53fee662", "position": {"x": -0.3423413, "y": 1.5816828, "z": 3.28908253}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -0.181592077, "y": 0.9548003, "z": 5.932334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -0.301118731, "y": 1.68280423, "z": 3.97536564}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -2.58304977, "y": 1.54316556, "z": 1.6662966}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -4.201876, "y": 0.0813307762, "z": 2.776416}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -4.210116, "y": 0.07393843, "z": 2.16533613}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -1.94325483, "y": 1.01892865, "z": 1.87550259}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -0.454979658, "y": 1.16598535, "z": 3.60300016}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -0.301118255, "y": 1.41551137, "z": 3.60299969}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -2.74077916, "y": 1.59968865, "z": 1.60112977}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -0.383563668, "y": 1.3894273, "z": 3.23175}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Egg_ab4d2337", "position": {"x": -3.712, "y": 0.104547471, "z": 6.45731449}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_d89eec12", "position": {"x": -3.712, "y": 0.122178406, "z": 6.33384275}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_52085e6b", "position": {"x": -0.181592077, "y": 0.9548003, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_9933ef2d", "position": {"x": -0.38356328, "y": 1.36942482, "z": 3.3555}, "rotation": {"x": 0.0, "y": 0.000169038554, "z": 0.0}}, {"objectName": "Fork_1dc44411", "position": {"x": -4.19639969, "y": 0.791505635, "z": 3.28501463}, "rotation": {"x": 1.40334191e-14, "y": -6.27857444e-05, "z": -1.46596551e-20}}, {"objectName": "Bowl_f227e8eb", "position": {"x": -0.3423393, "y": 1.33290422, "z": 3.97424984}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Plate_53fee662", "position": {"x": -0.181592077, "y": 0.92592597, "z": 4.944333}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_67328aa7", "position": {"x": -1.69388, "y": 1.01892865, "z": 1.8}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b10200c3", "position": {"x": -0.4018539, "y": 1.15011132, "z": 3.85050058}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Tomato_9c9ac9d5", "position": {"x": -4.158591, "y": 0.972936451, "z": 4.393535}, "rotation": {"x": 359.536163, "y": 359.8225, "z": 359.659}}, {"objectName": "WineBottle_eafa1419", "position": {"x": -1.94325483, "y": 0.924428642, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_ebd9a920", "position": {"x": -2.109505, "y": 0.938499868, "z": 1.6489948}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_aa4cec24", "position": {"x": -0.488323241, "y": 1.02938533, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_ba2c4270", "position": {"x": -0.565006, "y": 0.923959553, "z": 6.26166725}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pencil_282a316d", "position": {"x": -3.25512767, "y": 0.9237911, "z": 5.56610966}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_94f6c891", "position": {"x": -2.92400026, "y": 0.9303, "z": 2.0129}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_04c898c4", "position": {"x": -0.396172553, "y": 0.912319362, "z": 5.93382072}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_c6eeef1e", "position": {"x": -0.411640465, "y": 0.92140615, "z": 5.27366638}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_56566bc4", "position": {"x": -4.09782028, "y": 0.08019191, "z": 3.55607533}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_ddab876f", "position": {"x": -1.905484, "y": 1.44954264, "z": 1.60590935}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8b572f1d", "position": {"x": -0.488323241, "y": 0.9251421, "z": 4.615}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a23d8e54", "position": {"x": -0.5327245, "y": 0.7463716, "z": 5.84161043}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_8612b3fc", "position": {"x": -4.10899448, "y": 0.9246294, "z": 4.640952}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_19433bed", "position": {"x": -0.4098, "y": 0.9556, "z": 6.55700064}, "rotation": {"x": 0.0, "y": 270.000153, "z": 0.0}}], "object_toggles": [], "random_seed": 4159290585, "scene_num": 13}, "task_id": "trial_T20190907_151735_763792", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1MKCTVKE7J0ZP_37U1UTWH9Y3V1MHEDM2HW24U6A48RE", "high_descs": ["Turn right, then go forward, then turn right to go toward the counter next to the fridge.", "Pick up the egg on the back of the counter.", "Turn around, then turn left, to go to the microwave above the stove.", "Cook the egg in the microwave.", "Turn around and go straight, then turn right to go to the fridge.", "Put the egg in the fridge."], "task_desc": "Put a cooked egg in the fridge.", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3B837J3LDRDDK6WNRK8MDT4VI57SRW", "high_descs": ["Turn right and begin across the room, then hang a right and walk up to the bit of counter to the left of the fridge.", "Pick up the egg that is closest to the fridge on the counter.", "Turn around and walk over to the stove then look up at the microwave.", "Open the microwave, place the egg inside in front of the apple and close the door, turn on the microwave, after a couple seconds remove the egg and close the microwave door.", "Turn around and take a few steps forward, then turn right ad walk up to the fridge.", "Open the fridge and place the heated egg inside on the bottom shelf."], "task_desc": "Put a heated egg in the fridge.", "votes": [1, 1]}, {"assignment_id": "A3A0VPWPASCO9J_3DPNQGW4LOW1W2WS9V964VXT82964O", "high_descs": ["Go to the counter to the left of the fridge.", "Pick up the egg on the counter.", "Walk across the kitchen to the microwave.", "Cook the egg in the microwave.", "Take the cooked egg to the fridge.", "Put the cooked egg in the fridge."], "task_desc": "Put a cooked egg in the fridge.", "votes": [1, 1]}]}}