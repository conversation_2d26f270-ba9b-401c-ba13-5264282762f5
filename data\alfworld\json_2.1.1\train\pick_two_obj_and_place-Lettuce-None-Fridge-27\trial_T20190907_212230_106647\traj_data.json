{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000197.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000198.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000199.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000200.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000203.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 44}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Lettuce", "parent_target": "<PERSON><PERSON>", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|2|2|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-0.1718580724, -0.1718580724, 2.18108654, 2.18108654, 3.4109912, 3.4109912]], "coordinateReceptacleObjectId": ["DiningTable", [-0.848358096, -0.848358096, 3.524, 3.524, 0.0886555464, 0.0886555464]], "forceVisible": true, "objectId": "Lettuce|-00.04|+00.85|+00.55"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|5|-1|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-0.1718580724, -0.1718580724, 2.18108654, 2.18108654, 3.4109912, 3.4109912]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [8.416, 8.416, -1.116, -1.116, 0.0, 0.0]], "forceVisible": true, "objectId": "Lettuce|-00.04|+00.85|+00.55", "receptacleObjectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|2|2|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["lettuce"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Lettuce", [-0.1718580724, -0.1718580724, 1.50843, 1.50843, 3.4110036, 3.4110036]], "coordinateReceptacleObjectId": ["DiningTable", [-0.848358096, -0.848358096, 3.524, 3.524, 0.0886555464, 0.0886555464]], "forceVisible": true, "objectId": "Lettuce|-00.04|+00.85|+00.38"}}, {"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|5|-1|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["lettuce", "fridge"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Lettuce", [-0.1718580724, -0.1718580724, 1.50843, 1.50843, 3.4110036, 3.4110036]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [8.416, 8.416, -1.116, -1.116, 0.0, 0.0]], "forceVisible": true, "objectId": "Lettuce|-00.04|+00.85|+00.38", "receptacleObjectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-00.04|+00.85|+00.55"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [136, 100, 199, 136], "mask": [[29877, 1], [30168, 14], [30455, 31], [30752, 36], [31050, 39], [31348, 42], [31647, 45], [31946, 47], [32245, 49], [32544, 52], [32843, 53], [33142, 55], [33440, 57], [33740, 58], [34039, 59], [34338, 61], [34638, 61], [34937, 62], [35237, 63], [35537, 63], [35837, 63], [36136, 63], [36436, 63], [36736, 63], [37036, 62], [37336, 62], [37636, 61], [37938, 59], [38239, 57], [38542, 54], [38844, 50], [39147, 45], [39449, 42], [39752, 37], [40055, 32], [40357, 19], [40661, 11]], "point": [167, 117]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 210], "mask": [[0, 36900], [36901, 299], [37202, 298], [37503, 297], [37804, 296], [38105, 295], [38405, 295], [38706, 294], [39007, 293], [39308, 292], [39609, 291], [39910, 290], [40211, 289], [40511, 289], [40812, 288], [41113, 287], [41414, 286], [41715, 284], [42016, 282], [42316, 281], [42617, 279], [42918, 277], [43219, 275], [43520, 274], [43821, 272], [44121, 271], [44422, 269], [44723, 267], [45024, 265], [45325, 263], [45626, 261], [45927, 259], [46227, 258], [46528, 256], [46829, 254], [47130, 252], [47431, 250], [47732, 248], [48032, 248], [48333, 246], [48634, 244], [48935, 242], [49236, 240], [49537, 238], [49838, 236], [50138, 235], [50439, 233], [50740, 231], [51041, 229], [51342, 227], [51643, 225], [51943, 224], [52244, 223], [52545, 221], [52846, 219], [53147, 217], [53448, 215], [53749, 213], [54049, 212], [54350, 210], [54651, 208], [54952, 206], [55253, 204], [55554, 202], [55854, 201], [56155, 199], [56456, 197], [56757, 196], [57058, 194], [57359, 192], [57660, 80], [57751, 99], [57960, 78], [58053, 96], [58261, 76], [58355, 93], [58562, 73], [58656, 91], [58863, 71], [58957, 89], [59164, 70], [59258, 87], [59465, 68], [59559, 85], [59765, 68], [59860, 83], [60066, 66], [60161, 81], [60367, 65], [60462, 79], [60668, 63], [60763, 77], [60969, 61], [61064, 75], [61270, 60], [61365, 74], [61570, 59], [61666, 72], [61871, 58], [61967, 70], [62172, 56], [62267, 69], [62473, 55], [62568, 67], [62774, 53], [62869, 65]], "point": [149, 104]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-00.04|+00.85|+00.55", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 21300], [21301, 299], [21602, 298], [21902, 298], [22203, 297], [22504, 296], [22804, 296], [23105, 295], [23406, 294], [23706, 294], [24007, 293], [24308, 292], [24608, 292], [24909, 291], [25210, 290], [25510, 290], [25811, 289], [26111, 289], [26412, 288], [26713, 287], [27013, 287], [27314, 286], [27615, 285], [27915, 285], [28216, 284], [28517, 283], [28817, 283], [29118, 282], [29419, 281], [29719, 281], [30020, 280], [30321, 279], [30621, 279], [30922, 278], [31223, 277], [31523, 277], [31824, 276], [32125, 275], [32425, 275], [32726, 274], [33026, 274], [33327, 273], [33628, 272], [33928, 272], [34229, 271], [34530, 270], [34830, 270], [35131, 269], [35432, 268], [35732, 268], [36033, 267], [36334, 266], [36634, 266], [36935, 265], [37236, 264], [37536, 264], [37837, 263], [38138, 262], [38438, 262], [38739, 261], [39040, 260], [39340, 260], [39641, 259], [39941, 259], [40242, 258], [40543, 257], [40843, 257], [41144, 256], [41445, 255], [41745, 255], [42046, 254], [42347, 253], [42647, 253], [42948, 252], [43249, 251], [43549, 251], [43850, 250], [44151, 249], [44451, 249], [44752, 248], [45053, 247], [45353, 247], [45654, 246], [45955, 245], [46255, 245], [46556, 244], [46856, 244], [47157, 243], [47458, 242], [47758, 242], [48059, 241], [48360, 240], [48660, 240], [48961, 239], [49262, 238], [49562, 238], [49863, 237], [50164, 236], [50464, 236], [50765, 235], [51066, 234], [51366, 234], [51667, 233], [51968, 232], [52268, 232], [52569, 231], [52869, 231], [53170, 230], [53471, 229], [53771, 229], [54072, 228], [54373, 227], [54673, 227], [54974, 226], [55275, 225], [55575, 225], [55876, 224], [56177, 223], [56479, 221], [56930, 70], [57230, 70], [57531, 69], [57831, 69], [58131, 69], [58431, 69], [58731, 69], [59031, 69], [59332, 68], [59632, 68], [59932, 68], [60232, 68], [60532, 68], [60832, 68], [61133, 67], [61433, 67], [61733, 67], [62033, 67], [62333, 67], [62633, 67], [62934, 66], [63234, 66], [63534, 66], [63834, 66], [64134, 66], [64434, 66], [64735, 65], [65035, 65], [65335, 65], [65635, 65], [65935, 65], [66235, 65], [66536, 64], [66836, 64], [67136, 64], [67436, 64], [67736, 64], [68036, 64], [68336, 64], [68637, 63], [68937, 63], [69237, 63], [69537, 63], [69837, 63], [70137, 63], [70438, 62], [70738, 62], [71038, 62], [71338, 62], [71638, 62], [71938, 62], [72239, 61], [72539, 61], [72839, 61], [73139, 61], [73439, 61], [73739, 61], [74040, 60], [74340, 60], [74640, 60], [74940, 60], [75240, 60], [75540, 60], [75841, 59], [76141, 59], [76441, 59], [76741, 59], [77041, 59], [77341, 59], [77642, 58], [77942, 58], [78242, 58], [78542, 58], [78842, 58], [79142, 58], [79443, 57], [79743, 57], [80043, 57], [80343, 57], [80643, 57], [80943, 57], [81244, 56], [81544, 56], [81844, 56], [82144, 56], [82444, 56], [82744, 56], [83045, 55], [83345, 55], [83645, 55], [83945, 55], [84245, 55], [84545, 55], [84846, 54], [85146, 54], [85446, 54], [85746, 54], [86046, 54], [86346, 54], [86646, 54], [86947, 53], [87247, 53], [87547, 53], [87847, 53], [88147, 53], [88447, 53], [88748, 52], [89048, 52], [89348, 52], [89648, 52], [89948, 52]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 21300], [21301, 299], [21602, 142], [21768, 132], [21902, 141], [22068, 132], [22203, 140], [22368, 132], [22504, 139], [22669, 131], [22804, 139], [22969, 131], [23105, 138], [23269, 131], [23406, 137], [23569, 131], [23706, 137], [23869, 131], [24007, 136], [24169, 131], [24308, 135], [24470, 130], [24608, 135], [24770, 130], [24909, 134], [25070, 130], [25210, 133], [25369, 131], [25510, 133], [25669, 131], [25811, 132], [25969, 131], [26111, 132], [26269, 131], [26412, 131], [26568, 132], [26713, 130], [26868, 132], [27013, 130], [27168, 132], [27314, 130], [27467, 133], [27615, 129], [27767, 133], [27915, 130], [28067, 133], [28216, 129], [28366, 134], [28517, 129], [28666, 134], [28817, 130], [28966, 134], [29118, 130], [29265, 135], [29419, 130], [29564, 136], [29719, 132], [29864, 136], [30020, 131], [30163, 137], [30321, 131], [30463, 137], [30621, 132], [30762, 138], [30922, 132], [31061, 139], [31223, 133], [31360, 140], [31523, 277], [31824, 276], [32125, 275], [32425, 275], [32726, 274], [33026, 274], [33327, 273], [33628, 272], [33928, 272], [34229, 271], [34530, 270], [34830, 270], [35131, 269], [35432, 268], [35732, 268], [36033, 267], [36334, 266], [36634, 266], [36935, 265], [37236, 264], [37536, 264], [37837, 263], [38138, 262], [38438, 262], [38739, 261], [39040, 260], [39340, 260], [39641, 259], [39941, 259], [40242, 258], [40543, 257], [40843, 257], [41144, 256], [41445, 255], [41745, 255], [42046, 254], [42347, 253], [42647, 253], [42948, 252], [43249, 251], [43549, 251], [43850, 250], [44151, 249], [44451, 249], [44752, 248], [45053, 247], [45353, 247], [45654, 246], [45955, 245], [46255, 245], [46556, 244], [46856, 244], [47157, 243], [47458, 242], [47758, 242], [48059, 241], [48360, 240], [48660, 240], [48961, 239], [49262, 238], [49562, 238], [49863, 237], [50164, 236], [50464, 236], [50765, 235], [51066, 234], [51366, 234], [51667, 233], [51968, 232], [52268, 232], [52569, 231], [52869, 231], [53170, 230], [53471, 229], [53771, 229], [54072, 228], [54373, 227], [54673, 227], [54974, 226], [55275, 225], [55575, 225], [55876, 224], [56177, 223], [56479, 221], [56930, 70], [57230, 70], [57531, 69], [57831, 69], [58131, 69], [58431, 69], [58731, 69], [59031, 69], [59332, 68], [59632, 68], [59932, 68], [60232, 68], [60532, 68], [60832, 68], [61133, 67], [61433, 67], [61733, 67], [62033, 67], [62333, 67], [62633, 67], [62934, 66], [63234, 66], [63534, 66], [63834, 66], [64134, 66], [64434, 66], [64735, 65], [65035, 65], [65335, 65], [65635, 65], [65935, 65], [66235, 65], [66536, 64], [66836, 64], [67136, 64], [67436, 64], [67736, 64], [68036, 64], [68336, 64], [68637, 63], [68937, 63], [69237, 63], [69537, 63], [69837, 63], [70137, 63], [70438, 62], [70738, 62], [71038, 62], [71338, 62], [71638, 62], [71938, 62], [72239, 61], [72539, 61], [72839, 61], [73139, 61], [73439, 61], [73739, 61], [74040, 60], [74340, 60], [74640, 60], [74940, 60], [75240, 60], [75540, 60], [75841, 59], [76141, 59], [76441, 59], [76741, 59], [77041, 59], [77341, 59], [77642, 58], [77942, 58], [78242, 58], [78542, 58], [78842, 58], [79142, 58], [79443, 57], [79743, 57], [80043, 57], [80343, 57], [80643, 57], [80943, 57], [81244, 56], [81544, 56], [81844, 56], [82144, 56], [82444, 56], [82744, 56], [83045, 55], [83345, 55], [83645, 55], [83945, 55], [84245, 55], [84545, 55], [84846, 54], [85146, 54], [85446, 54], [85746, 54], [86046, 54], [86346, 54], [86646, 54], [86947, 53], [87247, 53], [87547, 53], [87847, 53], [88147, 53], [88447, 53], [88748, 52], [89048, 52], [89348, 52], [89648, 52], [89948, 52]], "point": [149, 149]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Lettuce|-00.04|+00.85|+00.38"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [77, 100, 143, 137], "mask": [[29805, 5], [30097, 23], [30393, 29], [30689, 36], [30988, 40], [31286, 44], [31584, 48], [31883, 51], [32181, 54], [32481, 56], [32780, 58], [33080, 60], [33379, 62], [33679, 64], [33979, 65], [34278, 66], [34578, 66], [34878, 66], [35177, 67], [35478, 66], [35778, 66], [36079, 65], [36379, 64], [36680, 62], [36980, 60], [37280, 59], [37581, 56], [37882, 52], [38182, 51], [38484, 48], [38785, 46], [39086, 44], [39387, 41], [39688, 38], [39989, 35], [40291, 31], [40595, 25], [40898, 17]], "point": [110, 117]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 210], "mask": [[0, 36900], [36901, 299], [37202, 298], [37503, 297], [37804, 296], [38105, 295], [38405, 295], [38706, 294], [39007, 293], [39308, 292], [39609, 291], [39910, 290], [40211, 289], [40511, 289], [40812, 288], [41113, 287], [41414, 286], [41715, 284], [42016, 282], [42316, 281], [42617, 279], [42918, 277], [43219, 275], [43520, 274], [43821, 272], [44121, 271], [44422, 269], [44723, 267], [45024, 265], [45325, 263], [45626, 261], [45927, 259], [46227, 258], [46528, 256], [46829, 254], [47130, 252], [47431, 250], [47732, 248], [48032, 248], [48333, 246], [48634, 244], [48935, 242], [49236, 240], [49537, 238], [49838, 236], [50138, 235], [50439, 233], [50740, 231], [51041, 229], [51342, 227], [51643, 225], [51943, 224], [52244, 223], [52545, 221], [52846, 219], [53147, 217], [53448, 215], [53749, 213], [54049, 212], [54350, 210], [54651, 208], [54952, 206], [55253, 204], [55554, 202], [55854, 201], [56155, 199], [56456, 197], [56757, 196], [57058, 194], [57359, 192], [57660, 80], [57751, 99], [57960, 78], [58053, 96], [58261, 76], [58355, 93], [58562, 73], [58656, 91], [58863, 71], [58957, 89], [59164, 70], [59258, 87], [59465, 68], [59559, 85], [59765, 68], [59860, 83], [60066, 66], [60161, 81], [60367, 65], [60462, 79], [60668, 63], [60763, 77], [60969, 61], [61064, 75], [61270, 60], [61365, 74], [61570, 59], [61666, 72], [61871, 58], [61967, 70], [62172, 56], [62267, 69], [62473, 55], [62568, 67], [62774, 53], [62869, 65]], "point": [149, 104]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Lettuce|-00.04|+00.85|+00.38", "placeStationary": true, "receptacleObjectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 21300], [21301, 299], [21602, 142], [21768, 132], [21902, 141], [22068, 132], [22203, 140], [22368, 132], [22504, 139], [22669, 131], [22804, 139], [22969, 131], [23105, 138], [23269, 131], [23406, 137], [23569, 131], [23706, 137], [23869, 131], [24007, 136], [24169, 131], [24308, 135], [24470, 130], [24608, 135], [24770, 130], [24909, 134], [25070, 130], [25210, 133], [25369, 131], [25510, 133], [25669, 131], [25811, 132], [25969, 131], [26111, 132], [26269, 131], [26412, 131], [26568, 132], [26713, 130], [26868, 132], [27013, 130], [27168, 132], [27314, 130], [27467, 133], [27615, 129], [27767, 133], [27915, 130], [28067, 133], [28216, 129], [28366, 134], [28517, 129], [28666, 134], [28817, 130], [28966, 134], [29118, 130], [29265, 135], [29419, 130], [29564, 136], [29719, 132], [29864, 136], [30020, 131], [30163, 137], [30321, 131], [30463, 137], [30621, 132], [30762, 138], [30922, 132], [31061, 139], [31223, 133], [31360, 140], [31523, 277], [31824, 276], [32125, 275], [32425, 275], [32726, 274], [33026, 274], [33327, 273], [33628, 272], [33928, 272], [34229, 271], [34530, 270], [34830, 270], [35131, 269], [35432, 268], [35732, 268], [36033, 267], [36334, 266], [36634, 266], [36935, 265], [37236, 264], [37536, 264], [37837, 263], [38138, 262], [38438, 262], [38739, 261], [39040, 260], [39340, 260], [39641, 259], [39941, 259], [40242, 258], [40543, 257], [40843, 257], [41144, 256], [41445, 255], [41745, 255], [42046, 254], [42347, 253], [42647, 253], [42948, 252], [43249, 251], [43549, 251], [43850, 250], [44151, 249], [44451, 249], [44752, 248], [45053, 247], [45353, 247], [45654, 246], [45955, 245], [46255, 245], [46556, 244], [46856, 244], [47157, 243], [47458, 242], [47758, 242], [48059, 241], [48360, 240], [48660, 240], [48961, 239], [49262, 238], [49562, 238], [49863, 237], [50164, 236], [50464, 236], [50765, 235], [51066, 234], [51366, 234], [51667, 233], [51968, 232], [52268, 232], [52569, 231], [52869, 231], [53170, 230], [53471, 229], [53771, 229], [54072, 228], [54373, 227], [54673, 227], [54974, 226], [55275, 225], [55575, 225], [55876, 224], [56177, 223], [56479, 221], [56930, 70], [57230, 70], [57531, 69], [57831, 69], [58131, 69], [58431, 69], [58731, 69], [59031, 69], [59332, 68], [59632, 68], [59932, 68], [60232, 68], [60532, 68], [60832, 68], [61133, 67], [61433, 67], [61733, 67], [62033, 67], [62333, 67], [62633, 67], [62934, 66], [63234, 66], [63534, 66], [63834, 66], [64134, 66], [64434, 66], [64735, 65], [65035, 65], [65335, 65], [65635, 65], [65935, 65], [66235, 65], [66536, 64], [66836, 64], [67136, 64], [67436, 64], [67736, 64], [68036, 64], [68336, 64], [68637, 63], [68937, 63], [69237, 63], [69537, 63], [69837, 63], [70137, 63], [70438, 62], [70738, 62], [71038, 62], [71338, 62], [71638, 62], [71938, 62], [72239, 61], [72539, 61], [72839, 61], [73139, 61], [73439, 61], [73739, 61], [74040, 60], [74340, 60], [74640, 60], [74940, 60], [75240, 60], [75540, 60], [75841, 59], [76141, 59], [76441, 59], [76741, 59], [77041, 59], [77341, 59], [77642, 58], [77942, 58], [78242, 58], [78542, 58], [78842, 58], [79142, 58], [79443, 57], [79743, 57], [80043, 57], [80343, 57], [80643, 57], [80943, 57], [81244, 56], [81544, 56], [81844, 56], [82144, 56], [82444, 56], [82744, 56], [83045, 55], [83345, 55], [83645, 55], [83945, 55], [84245, 55], [84545, 55], [84846, 54], [85146, 54], [85446, 54], [85746, 54], [86046, 54], [86346, 54], [86646, 54], [86947, 53], [87247, 53], [87547, 53], [87847, 53], [88147, 53], [88447, 53], [88748, 52], [89048, 52], [89348, 52], [89648, 52], [89948, 52]], "point": [149, 149]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 21300], [21301, 299], [21602, 104], [21730, 14], [21768, 132], [21902, 103], [22030, 13], [22068, 132], [22203, 102], [22330, 13], [22368, 132], [22504, 101], [22630, 13], [22669, 131], [22804, 101], [22930, 13], [22969, 131], [23105, 100], [23230, 13], [23269, 131], [23406, 99], [23530, 13], [23569, 131], [23706, 99], [23831, 12], [23869, 131], [24007, 97], [24131, 12], [24169, 131], [24308, 96], [24432, 11], [24470, 130], [24608, 96], [24731, 12], [24770, 130], [24909, 95], [25031, 12], [25070, 130], [25210, 94], [25331, 12], [25369, 131], [25510, 94], [25631, 12], [25669, 131], [25811, 93], [25931, 12], [25969, 131], [26111, 93], [26230, 13], [26269, 131], [26412, 92], [26530, 13], [26568, 132], [26713, 91], [26830, 13], [26868, 132], [27013, 92], [27130, 13], [27168, 132], [27314, 91], [27429, 15], [27467, 133], [27615, 91], [27729, 15], [27767, 133], [27915, 91], [28028, 17], [28067, 133], [28216, 91], [28328, 17], [28366, 134], [28517, 91], [28627, 19], [28666, 134], [28817, 91], [28927, 20], [28966, 134], [29118, 91], [29226, 22], [29265, 135], [29419, 91], [29525, 24], [29564, 136], [29719, 92], [29825, 26], [29864, 136], [30020, 92], [30124, 27], [30163, 137], [30321, 92], [30423, 29], [30463, 137], [30621, 92], [30722, 31], [30762, 138], [30922, 92], [31022, 32], [31061, 139], [31223, 93], [31321, 35], [31360, 140], [31523, 277], [31824, 276], [32125, 275], [32425, 275], [32726, 274], [33026, 274], [33327, 273], [33628, 272], [33928, 272], [34229, 271], [34530, 270], [34830, 270], [35131, 269], [35432, 268], [35732, 268], [36033, 267], [36334, 266], [36634, 266], [36935, 265], [37236, 264], [37536, 264], [37837, 263], [38138, 262], [38438, 262], [38739, 261], [39040, 260], [39340, 260], [39641, 259], [39941, 259], [40242, 258], [40543, 257], [40843, 257], [41144, 256], [41445, 255], [41745, 255], [42046, 254], [42347, 253], [42647, 253], [42948, 252], [43249, 251], [43549, 251], [43850, 250], [44151, 249], [44451, 249], [44752, 248], [45053, 247], [45353, 247], [45654, 246], [45955, 245], [46255, 245], [46556, 244], [46856, 244], [47157, 243], [47458, 242], [47758, 242], [48059, 241], [48360, 240], [48660, 240], [48961, 239], [49262, 238], [49562, 238], [49863, 237], [50164, 236], [50464, 236], [50765, 235], [51066, 234], [51366, 234], [51667, 233], [51968, 232], [52268, 232], [52569, 231], [52869, 231], [53170, 230], [53471, 229], [53771, 229], [54072, 228], [54373, 227], [54673, 227], [54974, 226], [55275, 225], [55575, 225], [55876, 224], [56177, 223], [56479, 221], [56930, 70], [57230, 70], [57531, 69], [57831, 69], [58131, 69], [58431, 69], [58731, 69], [59031, 69], [59332, 68], [59632, 68], [59932, 68], [60232, 68], [60532, 68], [60832, 68], [61133, 67], [61433, 67], [61733, 67], [62033, 67], [62333, 67], [62633, 67], [62934, 66], [63234, 66], [63534, 66], [63834, 66], [64134, 66], [64434, 66], [64735, 65], [65035, 65], [65335, 65], [65635, 65], [65935, 65], [66235, 65], [66536, 64], [66836, 64], [67136, 64], [67436, 64], [67736, 64], [68036, 64], [68336, 64], [68637, 63], [68937, 63], [69237, 63], [69537, 63], [69837, 63], [70137, 63], [70438, 62], [70738, 62], [71038, 62], [71338, 62], [71638, 62], [71938, 62], [72239, 61], [72539, 61], [72839, 61], [73139, 61], [73439, 61], [73739, 61], [74040, 60], [74340, 60], [74640, 60], [74940, 60], [75240, 60], [75540, 60], [75841, 59], [76141, 59], [76441, 59], [76741, 59], [77041, 59], [77341, 59], [77642, 58], [77942, 58], [78242, 58], [78542, 58], [78842, 58], [79142, 58], [79443, 57], [79743, 57], [80043, 57], [80343, 57], [80643, 57], [80943, 57], [81244, 56], [81544, 56], [81844, 56], [82144, 56], [82444, 56], [82744, 56], [83045, 55], [83345, 55], [83645, 55], [83945, 55], [84245, 55], [84545, 55], [84846, 54], [85146, 54], [85446, 54], [85746, 54], [86046, 54], [86346, 54], [86646, 54], [86947, 53], [87247, 53], [87547, 53], [87847, 53], [88147, 53], [88447, 53], [88748, 52], [89048, 52], [89348, 52], [89648, 52], [89948, 52]], "point": [149, 149]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan27", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": 1.25, "y": 0.9010001, "z": 0.75}, "object_poses": [{"objectName": "DishSponge_6b8b37c4", "position": {"x": 2.24362421, "y": 1.94852245, "z": 1.51597953}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a2d86e36", "position": {"x": -0.349214524, "y": 0.782299, "z": 0.881598532}, "rotation": {"x": 359.993134, "y": -3.675994e-05, "z": -0.000190332968}}, {"objectName": "Spatula_db580e26", "position": {"x": 1.97660708, "y": 0.7607302, "z": 0.4460751}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_9b26f0a9", "position": {"x": -0.119527027, "y": 0.7920962, "z": 1.38609231}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9b26f0a9", "position": {"x": 1.95469666, "y": 0.7479364, "z": 2.06035233}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_343518b0", "position": {"x": 1.84060025, "y": 0.9806006, "z": 0.435491472}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7054e289", "position": {"x": 2.223756, "y": 1.32172918, "z": -0.550875545}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_7054e289", "position": {"x": -0.261131257, "y": 1.03401828, "z": 2.0681653}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Knife_540cf8b1", "position": {"x": 1.80233955, "y": 0.7724276, "z": 0.7210319}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_fac53a9c", "position": {"x": 2.05651474, "y": 1.369756, "z": -0.460250616}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": 2.20604277, "y": 1.49813533, "z": 2.337342}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": 2.30194378, "y": 0.933299959, "z": 0.3613872}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_598ee640", "position": {"x": -0.0429645181, "y": 0.8527478, "z": 0.545271635}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_5c2a3ea2", "position": {"x": 2.12791038, "y": 1.51421344, "z": -0.09774989}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_343518b0", "position": {"x": -0.2948274, "y": 1.07805347, "z": 1.90751112}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_598ee640", "position": {"x": -0.0429645181, "y": 0.8527509, "z": 0.3771075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_3133524b", "position": {"x": 0.00205624476, "y": 0.746872, "z": 2.1294117}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_e54ab207", "position": {"x": 2.12977934, "y": 1.50213361, "z": 2.46997452}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_158e5727", "position": {"x": 1.55612528, "y": 0.982062459, "z": 2.74372172}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Ladle_741f0242", "position": {"x": -0.09656369, "y": 0.9428194, "z": 2.54614043}, "rotation": {"x": 6.888048, "y": 44.3553123, "z": 340.409424}}, {"objectName": "Bread_4be1a058", "position": {"x": -0.200649559, "y": 0.8241675, "z": 0.8145511}, "rotation": {"x": 359.951416, "y": 359.971741, "z": 0.08558819}}, {"objectName": "SaltShaker_8e528c3b", "position": {"x": -0.425777048, "y": 0.7859668, "z": 0.713435769}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_d7da2d9d", "position": {"x": 2.00076771, "y": 1.36164212, "z": -0.09774995}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_f15c1655", "position": {"x": 2.1402, "y": 0.9351, "z": 1.5045}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "WineBottle_2a35bd39", "position": {"x": -0.04197693, "y": 0.9386287, "z": 2.39919853}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_db580e26", "position": {"x": -0.0429645181, "y": 0.8059667, "z": 0.8815999}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_540cf8b1", "position": {"x": 1.9069, "y": 0.7713487, "z": 0.7210319}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_fac53a9c", "position": {"x": -0.0429645181, "y": 0.837259, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_92427322", "position": {"x": 2.23158026, "y": 1.56924152, "z": -0.278999954}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_a2d86e36", "position": {"x": -0.119527027, "y": 0.7910058, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_a2dda372", "position": {"x": 1.85448289, "y": 0.9379, "z": 1.497}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": 2.12710762, "y": 1.49853539, "z": 0.7550118}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7054e289", "position": {"x": -0.07871573, "y": 0.07956445, "z": 2.265489}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_6b8b37c4", "position": {"x": -0.272034049, "y": 0.9372642, "z": 2.42546678}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_922e45ca", "position": {"x": -0.3120002, "y": 0.781068146, "z": 0.4090001}, "rotation": {"x": -0.0017192763, "y": 0.000235514744, "z": 0.000684290659}}, {"objectName": "Spoon_9b26f0a9", "position": {"x": 0.033597976, "y": 0.7920962, "z": 0.713435769}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_5c2a3ea2", "position": {"x": 2.24195147, "y": 1.49853539, "z": 0.7850112}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": 1.15610623, "y": 0.0781952143, "z": 2.562471}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 4202985854, "scene_num": 27}, "task_id": "trial_T20190907_212230_106647", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A36DK84J5YJ942_3KMS4QQVK57ITXE6VGZQ5IZ2Y0TKFC", "high_descs": ["Move to the table to the right of the bin", "Pick up lettuce from the table ", "Move to the fridge behind you", "Place the lettuce inside and close the door ", "Move to the table to the right of the bin", "Pick up lettuce from the table ", "Move to the fridge behind you", "Place the lettuce inside and close the door "], "task_desc": "Put two things of lettuce in the fridge.", "votes": [1, 1]}, {"assignment_id": "A1WJU1IQ3UTRC6_3SNVL38CI79BQLUW4E9PXCEZ1N2KC2", "high_descs": ["Turn right and walk over to the table.", "Pick up the lettuce leaf on the right.", "Turn around and walk to the fridge.", "Open the fridge and put the lettuce leaf on the second shelf and close the fridge.", "Turn around and walk back to the table.", "Pick up the last lettuce leaf from the table.", "Turn around and walk to the fridge.", "Open the fridge and put the lettuce leaf on the second shelf and close the fridge."], "task_desc": "The robot should pick up the lettuce leaves and put them in the fridge.", "votes": [1, 1]}, {"assignment_id": "A2KAGFQU28JY43_3W92K5RLWXY47BFTO80OLFJ6P58V5E", "high_descs": ["Turn around and walk to the table across from you. ", "Pick up the lettuce closest to the bread, on the table. ", "Turn around and go to the refrigerator, in the corner, across from you. ", "Place the lettuce in the refrigerator, on the second shelf. ", "Turn around and walk back to the table across from you. ", "Pick up the other head of lettuce, beside the plate, on the table. ", "Turn around and walk back to the refrigerator across from you. ", "Place the head of lettuce in the refrigerator to the left of the other head of lettuce. "], "task_desc": "Put two heads of lettuce in the refrigerator, on the second shelf. ", "votes": [1, 1]}]}}