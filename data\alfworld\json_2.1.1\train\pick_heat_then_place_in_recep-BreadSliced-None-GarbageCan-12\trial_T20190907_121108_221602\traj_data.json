{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000132.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000133.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000134.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000135.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000136.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000137.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000139.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000140.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000141.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000142.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000143.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000144.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000145.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000146.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000147.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000148.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000151.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000152.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000153.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000154.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000155.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000156.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000157.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000158.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000159.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000160.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000207.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000208.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000209.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000210.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000211.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000212.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000213.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000214.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000215.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000216.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000217.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000218.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000219.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000221.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000222.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 47}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000254.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000255.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000277.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000278.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000279.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000280.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000281.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000282.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000283.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000284.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000285.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000286.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000287.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000288.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000289.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000290.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000291.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000292.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000293.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000294.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000295.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000315.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000316.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000317.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000318.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000319.png", "low_idx": 59}, {"high_idx": 8, "image_name": "000000320.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000321.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000322.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000323.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000324.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000325.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000326.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000327.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000328.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000329.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000330.png", "low_idx": 60}, {"high_idx": 8, "image_name": "000000331.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000332.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000333.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000334.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000335.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000336.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000337.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000338.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000339.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000340.png", "low_idx": 61}, {"high_idx": 8, "image_name": "000000341.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000342.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000343.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000344.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000345.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000346.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000347.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000348.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000349.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000350.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000351.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000352.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000353.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000354.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000355.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000356.png", "low_idx": 62}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [6.9943576, 6.9943576, 6.2640724, 6.2640724, 3.839570524, 3.839570524]], "coordinateReceptacleObjectId": ["CounterTop", [6.02, 6.02, 9.24, 9.24, 3.8936, 3.8936]], "forceVisible": true, "objectId": "Knife|+01.75|+00.96|+01.57"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-1|9|3|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-3.6920008, -3.6920008, 9.06199836, 9.06199836, 3.962714672, 3.962714672]], "forceVisible": true, "objectId": "Bread|-00.92|+00.99|+02.27"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "countertop"]}, "high_idx": 4, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [6.9943576, 6.9943576, 6.2640724, 6.2640724, 3.839570524, 3.839570524]], "coordinateReceptacleObjectId": ["CounterTop", [-3.8944, -3.8944, 9.5456, 9.5456, 3.8936, 3.8936]], "forceVisible": true, "objectId": "Knife|+01.75|+00.96|+01.57", "receptacleObjectId": "CounterTop|-00.97|+00.97|+02.39"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-3.6920008, -3.6920008, 9.06199836, 9.06199836, 3.962714672, 3.962714672]], "coordinateReceptacleObjectId": ["CounterTop", [-3.8944, -3.8944, 9.5456, 9.5456, 3.8936, 3.8936]], "forceVisible": true, "objectId": "Bread|-00.92|+00.99|+02.27|BreadSliced_4"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|4|6|1|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|4|11|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "garbagecan"]}, "high_idx": 9, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-3.6920008, -3.6920008, 9.06199836, 9.06199836, 3.962714672, 3.962714672]], "coordinateReceptacleObjectId": ["GarbageCan", [5.80777024, 5.80777024, 11.65005396, 11.65005396, 0.0, 0.0]], "forceVisible": true, "objectId": "Bread|-00.92|+00.99|+02.27|BreadSliced_4", "receptacleObjectId": "GarbageCan|+01.45|+00.00|+02.91"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|+01.75|+00.96|+01.57"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [105, 121, 165, 130], "mask": [[36105, 27], [36134, 30], [36406, 59], [36707, 59], [37007, 59], [37307, 25], [37333, 32], [37607, 24], [37644, 4], [37658, 6], [37907, 24], [37959, 3], [38207, 24], [38507, 24], [38816, 14]], "point": [135, 124]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-00.92|+00.99|+02.27"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [115, 101, 189, 178], "mask": [[30165, 4], [30462, 11], [30761, 14], [31056, 21], [31355, 23], [31653, 26], [31952, 29], [32252, 32], [32551, 33], [32850, 35], [33149, 36], [33448, 38], [33746, 40], [34045, 41], [34344, 42], [34642, 44], [34941, 46], [35241, 47], [35540, 48], [35839, 49], [36138, 50], [36437, 52], [36736, 53], [37035, 54], [37333, 56], [37633, 56], [37932, 57], [38231, 59], [38531, 59], [38830, 59], [39128, 61], [39427, 62], [39726, 62], [40024, 63], [40324, 63], [40623, 63], [40922, 64], [41222, 63], [41521, 63], [41820, 63], [42120, 62], [42419, 63], [42719, 62], [43019, 61], [43318, 61], [43619, 59], [43918, 58], [44218, 58], [44517, 59], [44817, 58], [45116, 59], [45416, 58], [45716, 57], [46015, 58], [46315, 57], [46615, 56], [46915, 55], [47215, 54], [47515, 54], [47815, 53], [48115, 51], [48415, 49], [48716, 48], [49016, 47], [49317, 45], [49617, 45], [49918, 43], [50219, 42], [50521, 39], [50822, 37], [51124, 35], [51426, 32], [51727, 30], [52030, 25], [52333, 21], [52635, 18], [52938, 13], [53240, 6]], "point": [152, 138]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|+01.75|+00.96|+01.57", "placeStationary": true, "receptacleObjectId": "CounterTop|-00.97|+00.97|+02.39"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [92, 90, 299, 216], "mask": [[26838, 12], [26878, 24], [27139, 11], [27178, 24], [27439, 11], [27478, 24], [27740, 10], [27778, 24], [28040, 10], [28078, 23], [28340, 11], [28378, 23], [28640, 11], [28677, 24], [28941, 10], [28977, 24], [29241, 10], [29277, 24], [29541, 10], [29577, 24], [29841, 10], [29877, 24], [30141, 11], [30177, 24], [30412, 2], [30441, 12], [30476, 25], [30712, 2], [30740, 14], [30776, 26], [31012, 2], [31040, 16], [31077, 26], [31312, 3], [31340, 14], [31379, 24], [31612, 3], [31640, 13], [31680, 23], [31911, 4], [31939, 13], [31983, 21], [32211, 5], [32239, 13], [32284, 20], [32511, 6], [32538, 13], [32585, 19], [32811, 7], [32837, 13], [32885, 19], [33111, 8], [33137, 12], [33186, 19], [33411, 9], [33435, 13], [33486, 19], [33710, 11], [33734, 12], [33786, 19], [34010, 13], [34032, 13], [34086, 19], [34310, 34], [34386, 20], [34610, 32], [34686, 20], [34910, 32], [34987, 19], [35210, 31], [35288, 18], [35509, 31], [35588, 19], [35809, 30], [35889, 18], [36109, 29], [36189, 18], [36409, 28], [36489, 18], [36709, 26], [36789, 19], [37008, 26], [37089, 19], [37308, 26], [37390, 18], [37608, 24], [37690, 18], [37908, 24], [37990, 19], [38208, 23], [38290, 19], [38508, 22], [38590, 19], [38807, 23], [38890, 19], [39107, 22], [39189, 21], [39407, 20], [39489, 21], [39707, 18], [39788, 22], [40007, 17], [40088, 22], [40306, 17], [40387, 24], [40606, 16], [40687, 24], [40906, 15], [40986, 25], [41206, 15], [41285, 26], [41506, 14], [41584, 28], [41806, 13], [41883, 29], [42105, 14], [42183, 29], [42405, 13], [42482, 30], [42705, 13], [42781, 32], [43005, 12], [43080, 33], [43305, 12], [43379, 34], [43605, 11], [43678, 35], [43904, 12], [43977, 37], [44204, 12], [44276, 38], [44504, 12], [44576, 38], [44804, 11], [44875, 39], [45104, 11], [45175, 40], [45403, 11], [45474, 41], [45703, 11], [45773, 42], [46003, 11], [46073, 42], [46303, 10], [46372, 43], [46603, 10], [46671, 45], [46903, 10], [46970, 46], [47202, 10], [47270, 46], [47502, 10], [47569, 47], [47802, 10], [47868, 49], [47978, 1], [48102, 10], [48167, 50], [48278, 1], [48402, 10], [48466, 51], [48577, 3], [48702, 10], [48764, 54], [48877, 3], [49001, 12], [49063, 55], [49177, 4], [49301, 12], [49362, 55], [49418, 1], [49476, 5], [49601, 12], [49662, 55], [49775, 6], [49901, 13], [49961, 56], [50074, 8], [50201, 13], [50260, 57], [50324, 42], [50374, 8], [50500, 15], [50560, 57], [50624, 42], [50673, 10], [50800, 15], [50859, 58], [50923, 44], [50973, 10], [51100, 17], [51159, 60], [51222, 46], [51272, 12], [51400, 18], [51458, 126], [51700, 20], [51757, 128], [52000, 21], [52056, 129], [52299, 24], [52355, 131], [52599, 25], [52655, 131], [52899, 28], [52953, 134], [53199, 31], [53252, 135], [53499, 33], [53551, 137], [53798, 37], [53849, 139], [54098, 39], [54145, 144], [54398, 42], [54441, 148], [54698, 192], [54998, 192], [55298, 50], [55358, 133], [55597, 47], [55661, 130], [55897, 45], [55963, 129], [56197, 44], [56271, 121], [56497, 44], [56607, 85], [56797, 44], [56866, 2], [56909, 84], [57097, 45], [57163, 16], [57210, 83], [57396, 47], [57462, 22], [57510, 84], [57696, 48], [57761, 29], [57809, 85], [57996, 50], [58060, 39], [58108, 87], [58296, 52], [58358, 137], [58596, 57], [58654, 142], [58895, 201], [59195, 202], [59495, 202], [59795, 203], [60095, 203], [60395, 204], [60694, 205], [60994, 206], [61294, 206], [61594, 206], [61894, 206], [62193, 207], [62493, 207], [62793, 207], [63093, 207], [63393, 207], [63693, 207], [63992, 208], [64292, 208], [64592, 208]], "point": [195, 152]}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.92|+00.99|+02.27|BreadSliced_4"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [112, 138, 154, 182], "mask": [[41221, 6], [41520, 10], [41819, 13], [42119, 15], [42418, 18], [42718, 19], [43017, 21], [43317, 22], [43616, 26], [43916, 28], [44216, 29], [44516, 30], [44815, 32], [45115, 33], [45414, 35], [45714, 35], [46014, 36], [46313, 37], [46613, 37], [46913, 37], [47212, 38], [47512, 38], [47812, 39], [48112, 40], [48412, 40], [48712, 41], [49013, 40], [49313, 40], [49613, 41], [49914, 40], [50214, 40], [50515, 39], [50815, 39], [51117, 38], [51418, 37], [51720, 35], [52021, 34], [52323, 32], [52624, 31], [52927, 26], [53230, 22], [53532, 19], [53835, 14], [54137, 8], [54440, 1]], "point": [133, 159]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.92|+00.99|+02.27|BreadSliced_4", "placeStationary": true, "receptacleObjectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 63], [103, 2], [113, 148], [300, 63], [414, 147], [600, 63], [716, 145], [900, 63], [1019, 141], [1200, 63], [1319, 141], [1500, 64], [1620, 140], [1800, 64], [1921, 139], [2100, 64], [2221, 139], [2400, 64], [2522, 138], [2700, 64], [2823, 137], [3000, 64], [3123, 137], [3300, 64], [3424, 136], [3600, 64], [3724, 136], [3900, 64], [4025, 134], [4200, 64], [4325, 134], [4500, 64], [4625, 134], [4800, 64], [4925, 134], [5100, 64], [5225, 134], [5400, 64], [5525, 134], [5700, 64], [5825, 134], [6000, 64], [6125, 134], [6300, 64], [6425, 134], [6600, 65], [6725, 134], [6900, 65], [7025, 133], [7200, 65], [7326, 132], [7500, 65], [7626, 132], [7800, 65], [7927, 131], [8100, 65], [8227, 131], [8400, 65], [8528, 130], [8700, 65], [8828, 130], [9000, 65], [9129, 129], [9300, 65], [9429, 129], [9600, 65], [9729, 129], [9900, 66], [10029, 128], [10200, 66], [10329, 128], [10500, 66], [10630, 127], [10800, 66], [10930, 127], [11100, 66], [11230, 127], [11400, 66], [11530, 127], [11700, 66], [11830, 127], [12000, 66], [12130, 127], [12300, 66], [12430, 127], [12600, 66], [12729, 128], [12900, 66], [13029, 127], [13200, 66], [13329, 127], [13500, 66], [13629, 127], [13800, 67], [13929, 127], [14100, 67], [14229, 127], [14400, 67], [14529, 127], [14700, 67], [14829, 127], [15000, 67], [15128, 128], [15300, 67], [15428, 128], [15600, 67], [15728, 128], [15900, 67], [16028, 127], [16200, 67], [16327, 128], [16500, 67], [16627, 128], [16800, 67], [16926, 129], [17100, 67], [17226, 129], [17400, 68], [17525, 130], [17700, 68], [17825, 130], [18000, 68], [18124, 131], [18300, 68], [18423, 132], [18600, 68], [18722, 133], [18900, 68], [19021, 133], [19200, 68], [19319, 135], [19500, 68], [19617, 137], [19800, 69], [19915, 139], [20100, 69], [20212, 142], [20400, 69], [20506, 148], [20700, 69], [20802, 152], [21000, 69], [21101, 153], [21300, 69], [21400, 154], [21600, 70], [21698, 156], [21900, 71], [21994, 159], [22200, 72], [22290, 163], [22500, 73], [22584, 169], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 7}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 7}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [6.528, 6.528, 6.584, 6.584, 5.92407848, 5.92407848]], "forceVisible": true, "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 7}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 7}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-00.92|+00.99|+02.27|BreadSliced_4"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [63, 1, 129, 76], "mask": [[63, 40], [105, 8], [363, 51], [663, 53], [963, 56], [1263, 56], [1564, 56], [1864, 57], [2164, 57], [2464, 58], [2764, 59], [3064, 59], [3364, 60], [3664, 60], [3964, 61], [4264, 61], [4564, 61], [4864, 61], [5164, 61], [5464, 61], [5764, 61], [6064, 61], [6364, 61], [6665, 60], [6965, 60], [7265, 61], [7565, 61], [7865, 62], [8165, 62], [8465, 63], [8765, 63], [9065, 64], [9365, 64], [9665, 64], [9966, 63], [10266, 63], [10566, 64], [10866, 64], [11166, 64], [11466, 64], [11766, 64], [12066, 64], [12366, 64], [12666, 63], [12966, 63], [13266, 63], [13566, 63], [13867, 62], [14167, 62], [14467, 62], [14767, 62], [15067, 61], [15367, 61], [15667, 61], [15967, 61], [16267, 60], [16567, 60], [16867, 59], [17167, 59], [17468, 57], [17768, 57], [18068, 56], [18368, 55], [18668, 54], [18968, 53], [19268, 51], [19568, 49], [19869, 46], [20169, 43], [20469, 37], [20769, 33], [21069, 32], [21369, 31], [21670, 28], [21971, 23], [22272, 18], [22573, 11]], "point": [96, 37]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.63|+01.48|+01.65"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 260, 125], "mask": [[0, 261], [300, 261], [600, 261], [900, 260], [1200, 260], [1500, 260], [1800, 260], [2100, 260], [2400, 260], [2700, 260], [3000, 260], [3300, 260], [3600, 260], [3900, 259], [4200, 259], [4500, 259], [4800, 259], [5100, 259], [5400, 259], [5700, 259], [6000, 259], [6300, 259], [6600, 259], [6900, 258], [7200, 258], [7500, 258], [7800, 258], [8100, 258], [8400, 258], [8700, 258], [9000, 258], [9300, 258], [9600, 258], [9900, 257], [10200, 257], [10500, 257], [10800, 257], [11100, 257], [11400, 257], [11700, 257], [12000, 257], [12300, 257], [12600, 257], [12900, 256], [13200, 256], [13500, 256], [13800, 256], [14100, 256], [14400, 256], [14700, 256], [15000, 256], [15300, 256], [15600, 256], [15900, 255], [16200, 255], [16500, 255], [16800, 255], [17100, 255], [17400, 255], [17700, 255], [18000, 255], [18300, 255], [18600, 255], [18900, 254], [19200, 254], [19500, 254], [19800, 254], [20100, 254], [20400, 254], [20700, 254], [21000, 254], [21300, 254], [21600, 254], [21900, 253], [22200, 253], [22500, 253], [22800, 253], [23100, 253], [23400, 253], [23700, 253], [24000, 253], [24300, 253], [24600, 253], [24900, 253], [25200, 252], [25500, 252], [25800, 252], [26100, 252], [26400, 252], [26700, 252], [27000, 252], [27300, 252], [27600, 252], [27900, 252], [28200, 251], [28500, 251], [28800, 251], [29100, 251], [29400, 251], [29700, 251], [30000, 251], [30300, 251], [30600, 251], [30900, 251], [31200, 250], [31500, 250], [31800, 250], [32100, 249], [32400, 250], [32700, 250], [33000, 250], [33300, 250], [33600, 250], [33900, 227], [34400, 11], [34700, 11], [35000, 11], [35300, 11], [35600, 11], [35900, 11], [36200, 10], [36500, 10], [36800, 10], [37100, 10], [37402, 7]], "point": [130, 62]}}, "high_idx": 7}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 8}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-00.92|+00.99|+02.27|BreadSliced_4", "placeStationary": true, "receptacleObjectId": "GarbageCan|+01.45|+00.00|+02.91"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [64, 137, 150, 241], "mask": [[40886, 55], [41182, 62], [41479, 69], [41778, 70], [42077, 72], [42376, 74], [42675, 75], [42975, 76], [43275, 76], [43575, 76], [43874, 77], [44174, 77], [44474, 77], [44774, 77], [45074, 77], [45374, 77], [45673, 78], [45973, 78], [46273, 78], [46573, 78], [46873, 78], [47173, 78], [47472, 79], [47772, 79], [48072, 79], [48372, 79], [48672, 79], [48971, 80], [49271, 80], [49571, 80], [49871, 80], [50171, 80], [50471, 80], [50770, 81], [51070, 81], [51370, 81], [51670, 81], [51970, 81], [52270, 81], [52569, 82], [52869, 82], [53169, 82], [53469, 82], [53769, 82], [54069, 82], [54368, 83], [54668, 83], [54968, 83], [55268, 60], [55335, 16], [55568, 59], [55638, 13], [55868, 58], [55939, 12], [56167, 59], [56240, 11], [56467, 59], [56540, 11], [56767, 58], [56840, 11], [57067, 58], [57140, 11], [57367, 58], [57440, 11], [57667, 58], [57740, 11], [57966, 59], [58040, 11], [58266, 38], [58309, 16], [58340, 11], [58566, 38], [58610, 15], [58640, 11], [58866, 38], [58910, 15], [58940, 11], [59166, 38], [59211, 14], [59240, 11], [59466, 38], [59511, 15], [59540, 11], [59765, 39], [59811, 15], [59839, 12], [60065, 39], [60110, 17], [60138, 13], [60365, 39], [60410, 18], [60436, 15], [60665, 40], [60708, 22], [60733, 18], [60965, 86], [61265, 86], [61564, 87], [61864, 87], [62164, 87], [62464, 87], [62764, 87], [63064, 87], [63365, 85], [63665, 85], [63965, 84], [64266, 69], [64566, 63], [64868, 58], [65170, 54], [65473, 49], [65775, 45], [66077, 42], [66379, 39], [66682, 35], [66985, 30], [67287, 28], [67589, 25], [67891, 23], [68193, 20], [68494, 19], [68795, 17], [69095, 17], [69396, 15], [69697, 14], [69998, 13], [70298, 13], [70599, 12], [70900, 11], [71200, 10], [71501, 9], [71801, 9], [72104, 6]], "point": [107, 188]}}, "high_idx": 9}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan12", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -0.25, "y": 0.9009999, "z": -1.25}, "object_poses": [{"objectName": "Potato_22312ae0", "position": {"x": -1.065349, "y": 1.35187662, "z": -0.110954285}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": 1.63003159, "y": 1.500673, "z": 2.27798438}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": 1.62400174, "y": 1.94812238, "z": 1.41047323}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": -0.827398658, "y": 0.761567056, "z": 2.24516249}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.8657}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.36262441, "y": 0.9798608, "z": 1.63136768}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": -1.15446556, "y": 1.82730782, "z": -0.127863228}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": 1.7485894, "y": 0.959892631, "z": 1.5660181}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": 1.690922, "y": 0.959892631, "z": 1.673176}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -0.9648363, "y": 0.876337349, "z": 0.0423486531}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": -1.211005, "y": 0.937205851, "z": 2.310654}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -1.15446556, "y": 1.82503581, "z": 0.0106455684}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": 1.28540039, "y": 0.9342062, "z": 2.59703}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": 1.48997676, "y": 0.119435996, "z": 2.84730816}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": -0.988837, "y": 0.8643498, "z": 0.195651233}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_f3700d17", "position": {"x": -1.12628, "y": 1.02830076, "z": 0.921}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_09f78d94", "position": {"x": 1.50362837, "y": 0.944003344, "z": 1.7985332}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bread_20fd7fed", "position": {"x": -0.9230002, "y": 0.990678668, "z": 2.26549959}, "rotation": {"x": -6.535081e-05, "y": 302.470367, "z": 7.51836e-05}}, {"objectName": "Fork_d40bfead", "position": {"x": 1.78703427, "y": 0.9384485, "z": 1.35170221}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_2b7ef926", "position": {"x": -1.12788, "y": 0.999118, "z": 2.159162}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_01d32d7f", "position": {"x": -0.946484864, "y": 0.19658269, "z": -0.0445037037}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_b82a625f", "position": {"x": -1.109, "y": 0.9307289, "z": 1.4704}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_733b0828", "position": {"x": 1.275738, "y": 0.7618369, "z": 0.780662537}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_8aa9254c", "position": {"x": -0.793779969, "y": 0.9598927, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_00786e88", "position": {"x": 1.379534, "y": 0.7428734, "z": 0.848631263}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_d0e3153d", "position": {"x": -1.12628, "y": 0.9871304, "z": 0.765623748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_2858d553", "position": {"x": -0.7937801, "y": 0.9342062, "z": 1.15406442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_6bdc3897", "position": {"x": 1.51607227, "y": 0.937942, "z": 0.87331903}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_22312ae0", "position": {"x": 1.43918169, "y": 0.9705572, "z": 2.11864662}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c49d2ab8", "position": {"x": -1.0766772, "y": 1.83225048, "z": 0.3563524}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a0f18af8", "position": {"x": 1.34043908, "y": 1.7463212, "z": 0.1684567}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_83ce1479", "position": {"x": 1.523011, "y": 0.105368823, "z": 3.01032162}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_0e044937", "position": {"x": 1.5506295, "y": 0.9432757, "z": 1.673159}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pan_9f7abbea", "position": {"x": 1.3781, "y": 1.73753989, "z": 0.3929}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_5fe99c37", "position": {"x": -0.7122551, "y": 0.9390294, "z": 2.310654}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_c6f8cd8f", "position": {"x": -1.16145754, "y": 1.49610877, "z": 2.494425}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2747297006, "scene_num": 12}, "task_id": "trial_T20190907_121108_221602", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3HL2LL0LEPZT8_31IBVUNM9VGW988IUKDX7RAJE1EVFT", "high_descs": ["Go forward, turn right at the oven, go forward to the sink.", "Take the large knife that is closes to the wall.", "Turn around, go forward and to the right to the counter to the right of the oven.", "Cut the bread on the counter into slices.", "Put the knife on the spoon on the counter.", "Take a slice of bread from the counter.", "Turn around, go forward and to the right to the sink.", "Heat the bread in the microwave above the sink. Take the bread from the oven.", "Turn left, go forward to the wall, turn right to face the silver bin.", "Put the bread slice in the bin."], "task_desc": "Put a heated slice of bred in a bin.", "votes": [1, 1, 1]}, {"assignment_id": "A4CHLWPHZIP7Y_34Q075JO10UPQSDUKOANXWGZJV201P", "high_descs": ["Go straight toward the wall, turn right, and go to the sink.", "Pick up a knife from behind the sink.", "Turn left, go to the end of the counter, turn left, and go to the counter with the bread.", "Slice the bread on the counter.", "Put the knife on the counter near the bread.", "Take a slice of bread from the counter.", "Turn around and go to the microwave over the sink.", "Open the microwave, put the bread in the microwave, close the microwave, run the microwave for two seconds, open the microwave, take out the bread, and close the microwave.", "Turn left, go to the trash can, and turn right.", "Put the bread in the trash can."], "task_desc": "Place a slice of warmed bread in the trash can.", "votes": [1, 1, 1]}, {"assignment_id": "A17TKHT8FEVH0R_3E7TUJ2EGF31EHHJ0DDBR9I1BPTD9K", "high_descs": ["Turn right to the sink", "Grab a knife that's behind the sink", "Turn around and head to the counter to the right of the oven", "Cut the loaf of bread into slices", "Place the knife down on the counter", "Grab a slice of bread", "Turn around and head to the microwave", "Heat the bread in the microwave and then take it out", "Turn left and head to the trash can", "Throw the bread into the trash can"], "task_desc": "Throwing sliced bread into the trash can", "votes": [1, 1]}, {"assignment_id": "A3HL2LL0LEPZT8_3NJM2BJS4ZNC1CRCH6EXTT0YVRQCP1", "high_descs": ["Go straight, turn right at the sink, go straight to the sink.", "Take the large knife closest to the wall.", "Turn around, go to the counter to the right of the oven.", "Cut the bread on the counter into slices.", "Put the knife on the counter on top of the spoon.", "Take a piece of bread from the counter.", "Turn around, go back to the sink.", "Heat the bread in the microwave above the sink. Take the bread from the microwave.", "Turn left, go to the wall, turn right to face the garbage can.", "Put the bread in the garbage can."], "task_desc": "Put a heated slice of the bread in a garbage can.", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3WZ36BJEV6XRJZSDKNRMNJVD29WBTJ", "high_descs": ["<PERSON><PERSON> walking across the room, then hang a right and walk up to the kitchen sink.", "Pick up the furthest large metal knife with a golden handle from behind the sink.", "Turn left and begin walking over to the wall, then hang a left and walk up to the counter to the right of the oven.", "Slice up the loaf of bread on the counter.", "Place the knife on the counter in front of you right above the middle of the handle of the spoon.", "Pick up the end slice of bread off of the counter.", "Turn left and begin walking forward, then turn left again and walk over to the sink, look up at the microwave.", "Open the microwave and place the slice of bread inside, then close the door and turn on the microwave, after a couple seconds take out the slice of bread and close the microwave door.", "Turn left and walk across the room, then turn right to face the small grey bin in between the counter and the wall.", "Place the heated slice of bread inside the small grey bin"], "task_desc": "Put a heated slice of bread in a grey bin.", "votes": [1, 1]}, {"assignment_id": "A1ZE52NWZPN85P_3EA3QWIZ4LC1676JQXJ396ZQX86TII", "high_descs": ["Walk straight and then turn to your right so you are facing the sink", "Pick up a knife from behind the sink", "Move to your left and then turn around so you are facing the loaf of bread", "Use the knife to slice the bread", "Put the knife down on the kitchen counter", "Pick up a slice of bread off of the kitchen counter", "Turn around again so you are facing the sink again", "Open the microwave, put the bread in and cook it for a few seconds before taking it out and closing the door.", "Move left and then turn to your right so you are facing the trash can", "Drop the slice of bread into the trash can"], "task_desc": "Slice, microwave and throw away some bread", "votes": [1, 1, 0]}]}}