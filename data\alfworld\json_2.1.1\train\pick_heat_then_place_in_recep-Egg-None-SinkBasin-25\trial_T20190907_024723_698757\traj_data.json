{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000211.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000212.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 27}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "SinkBasin", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-8|4|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-9.03525352, -9.03525352, 2.001453876, 2.001453876, 3.3915132, 3.3915132]], "coordinateReceptacleObjectId": ["CounterTop", [-6.232, -6.232, 1.112, 1.112, 3.4364, 3.4364]], "forceVisible": true, "objectId": "Egg|-02.26|+00.85|+00.50"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-8|5|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-11.18, -11.18, 5.396, 5.396, 3.2488436, 3.2488436]], "forceVisible": true, "objectId": "Microwave|-02.80|+00.81|+01.35"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-6|5|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "sinkbasin"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-9.03525352, -9.03525352, 2.001453876, 2.001453876, 3.3915132, 3.3915132]], "coordinateReceptacleObjectId": ["SinkBasin", [-6.9823656, -6.9823656, 1.183695672, 1.183695672, 2.748186588, 2.748186588]], "forceVisible": true, "objectId": "Egg|-02.26|+00.85|+00.50", "receptacleObjectId": "Sink|-01.84|+00.73|+00.34|SinkBasin"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-02.26|+00.85|+00.50"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [219, 125, 232, 138], "mask": [[37424, 5], [37722, 8], [38021, 10], [38320, 12], [38620, 12], [38919, 14], [39219, 14], [39519, 14], [39819, 13], [40119, 13], [40419, 13], [40720, 11], [41021, 9], [41322, 6]], "point": [225, 130]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-02.26|+00.85|+00.50", "placeStationary": true, "receptacleObjectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [2, 34, 295, 294], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25870, 224], [26170, 223], [26469, 224], [26768, 224], [27068, 224], [27367, 224], [27666, 225], [27965, 226], [28265, 225], [28564, 226], [28864, 225], [29163, 226], [29463, 226], [29763, 226], [30062, 78], [30149, 140], [30362, 75], [30452, 137], [30661, 74], [30754, 134], [30961, 73], [31055, 133], [31260, 73], [31357, 131], [31560, 72], [31658, 130], [31860, 71], [31959, 129], [32159, 72], [32259, 129], [32459, 71], [32560, 128], [32758, 72], [32860, 128], [33058, 71], [33161, 126], [33358, 71], [33461, 126], [33657, 72], [33761, 125], [33957, 72], [34061, 125], [34256, 73], [34362, 123], [34556, 73], [34662, 123], [34855, 74], [34962, 123], [35155, 74], [35262, 122], [35455, 74], [35562, 122], [35754, 75], [35861, 122], [36054, 75], [36161, 122], [36353, 77], [36461, 121], [36653, 77], [36760, 122], [36953, 77], [37060, 121], [37252, 79], [37360, 121], [37552, 79], [37659, 121], [37851, 81], [37958, 122], [38151, 81], [38258, 121], [38450, 83], [38557, 122], [38750, 84], [38856, 123], [39050, 84], [39156, 122], [39349, 86], [39455, 123], [39649, 87], [39754, 123], [39948, 89], [40053, 124], [40248, 90], [40352, 124], [40548, 92], [40650, 126], [40847, 95], [40948, 127], [41147, 228], [41446, 228], [41746, 228], [42045, 229], [42345, 228], [42645, 228], [42944, 228], [43244, 228], [43543, 228], [43843, 228], [44143, 227], [44442, 228], [44742, 227], [45041, 228], [45341, 227], [45640, 228], [45940, 228], [46240, 227], [46539, 228], [46839, 227], [47138, 228], [47438, 227], [47738, 227], [48037, 227], [48337, 227], [48636, 227], [48936, 227], [49235, 228], [49535, 227], [49835, 227], [50134, 227], [50434, 224], [50733, 224], [51033, 224], [51333, 223], [51632, 64], [51697, 159], [51932, 64], [51997, 158], [52231, 65], [52531, 65], [52830, 66], [53130, 66], [53430, 65], [53729, 66], [54029, 66], [54328, 67], [54628, 67], [54927, 67], [55227, 67], [55527, 67], [55826, 68], [56126, 68], [56425, 68], [56725, 68], [57025, 68], [57324, 69], [57624, 69], [57923, 70], [58223, 69], [58522, 70], [58822, 70], [59122, 70], [59421, 71], [59721, 70], [60020, 71], [60320, 71], [60620, 71], [60919, 72], [61219, 71], [61518, 72], [61818, 72], [62117, 73], [62417, 73], [62717, 72], [63016, 73], [63316, 73], [63615, 74], [63915, 74], [64215, 74], [64514, 74], [64814, 74], [65113, 75], [65413, 75], [65712, 76], [66012, 75], [66312, 75], [66611, 76], [66911, 76], [67210, 77], [67510, 76], [67810, 76], [68109, 77], [68409, 77], [68708, 78], [69008, 77], [69307, 78], [69607, 78], [69907, 78], [70206, 79], [70506, 79], [70805, 79], [71105, 79], [71405, 79], [71704, 80], [72004, 80], [72303, 80], [72603, 80], [72902, 81], [73202, 81], [73503, 80], [73804, 78], [74105, 77], [74405, 77], [74706, 76], [75007, 75], [75308, 74], [75609, 72], [75910, 71], [76211, 70], [76512, 69], [76813, 68], [77113, 67], [77414, 66], [77715, 65], [78016, 64], [78317, 63], [78618, 61], [78919, 60], [79220, 59], [79521, 58], [79822, 57], [80122, 56], [80423, 55], [80724, 54], [81025, 53], [81326, 52], [81627, 51], [81928, 49], [82229, 48], [82530, 47], [82831, 46], [83131, 46], [83432, 44], [83733, 43], [84034, 42], [84335, 41], [84636, 40], [84937, 38], [85238, 37], [85539, 36], [85839, 36], [86140, 38], [86441, 39], [86742, 39], [87043, 31], [87076, 5], [87345, 29], [87377, 2], [87649, 25], [87953, 20]], "point": [148, 163]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [2, 34, 295, 294], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25870, 224], [26170, 223], [26469, 224], [26768, 224], [27068, 224], [27367, 224], [27666, 225], [27965, 226], [28265, 225], [28564, 226], [28864, 225], [29163, 226], [29463, 226], [29763, 226], [30062, 78], [30149, 140], [30362, 75], [30452, 137], [30661, 74], [30754, 134], [30961, 73], [31055, 133], [31260, 73], [31357, 131], [31560, 72], [31658, 130], [31860, 71], [31959, 129], [32159, 72], [32259, 129], [32459, 71], [32560, 128], [32758, 72], [32860, 128], [33058, 71], [33161, 126], [33358, 71], [33461, 126], [33657, 72], [33761, 125], [33957, 72], [34061, 125], [34256, 73], [34362, 123], [34556, 73], [34662, 123], [34855, 74], [34962, 123], [35155, 74], [35262, 122], [35455, 74], [35562, 122], [35754, 75], [35861, 122], [36054, 75], [36161, 122], [36353, 77], [36461, 121], [36653, 77], [36760, 122], [36953, 77], [37060, 121], [37252, 79], [37360, 121], [37552, 79], [37659, 121], [37851, 81], [37958, 122], [38151, 81], [38258, 15], [38276, 103], [38450, 83], [38557, 14], [38578, 101], [38750, 84], [38856, 14], [38878, 101], [39050, 84], [39156, 13], [39179, 99], [39349, 86], [39455, 14], [39480, 98], [39649, 87], [39754, 14], [39780, 97], [39948, 89], [40053, 15], [40080, 97], [40248, 90], [40352, 16], [40380, 96], [40548, 92], [40650, 18], [40680, 96], [40847, 95], [40948, 20], [40980, 95], [41147, 121], [41279, 96], [41446, 123], [41579, 95], [41746, 124], [41878, 96], [42045, 126], [42177, 97], [42345, 228], [42645, 228], [42944, 228], [43244, 228], [43543, 228], [43843, 228], [44143, 227], [44442, 228], [44742, 227], [45041, 228], [45341, 227], [45640, 228], [45940, 228], [46240, 227], [46539, 228], [46839, 227], [47138, 228], [47438, 227], [47738, 227], [48037, 227], [48337, 227], [48636, 227], [48936, 227], [49235, 228], [49535, 227], [49835, 227], [50134, 227], [50434, 224], [50733, 224], [51033, 224], [51333, 223], [51632, 64], [51697, 159], [51932, 64], [51997, 158], [52231, 65], [52531, 65], [52830, 66], [53130, 66], [53430, 65], [53729, 66], [54029, 66], [54328, 67], [54628, 67], [54927, 67], [55227, 67], [55527, 67], [55826, 68], [56126, 68], [56425, 68], [56725, 68], [57025, 68], [57324, 69], [57624, 69], [57923, 70], [58223, 69], [58522, 70], [58822, 70], [59122, 70], [59421, 71], [59721, 70], [60020, 71], [60320, 71], [60620, 71], [60919, 72], [61219, 71], [61518, 72], [61818, 72], [62117, 73], [62417, 73], [62717, 72], [63016, 73], [63316, 73], [63615, 74], [63915, 74], [64215, 74], [64514, 74], [64814, 74], [65113, 75], [65413, 75], [65712, 76], [66012, 75], [66312, 75], [66611, 76], [66911, 76], [67210, 77], [67510, 76], [67810, 76], [68109, 77], [68409, 77], [68708, 78], [69008, 77], [69307, 78], [69607, 78], [69907, 78], [70206, 79], [70506, 79], [70805, 79], [71105, 79], [71405, 79], [71704, 80], [72004, 80], [72303, 80], [72603, 80], [72902, 81], [73202, 81], [73503, 80], [73804, 78], [74105, 77], [74405, 77], [74706, 76], [75007, 75], [75308, 74], [75609, 72], [75910, 71], [76211, 70], [76512, 69], [76813, 68], [77113, 67], [77414, 66], [77715, 65], [78016, 64], [78317, 63], [78618, 61], [78919, 60], [79220, 59], [79521, 58], [79822, 57], [80122, 56], [80423, 55], [80724, 54], [81025, 53], [81326, 52], [81627, 51], [81928, 49], [82229, 48], [82530, 47], [82831, 46], [83131, 46], [83432, 44], [83733, 43], [84034, 42], [84335, 41], [84636, 40], [84937, 38], [85238, 37], [85539, 36], [85839, 36], [86140, 38], [86441, 39], [86742, 39], [87043, 31], [87076, 5], [87345, 29], [87377, 2], [87649, 25], [87953, 20]], "point": [148, 163]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-11.18, -11.18, 5.396, 5.396, 3.2488436, 3.2488436]], "forceVisible": true, "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-11.18, -11.18, 5.396, 5.396, 3.2488436, 3.2488436]], "forceVisible": true, "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [76, 34, 295, 174], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25877, 217], [26177, 216], [26478, 215], [26778, 214], [27078, 214], [27378, 213], [27679, 212], [27979, 212], [28279, 211], [28579, 211], [28879, 210], [29179, 210], [29479, 210], [29779, 210], [30079, 210], [30379, 210], [30679, 209], [30979, 209], [31279, 209], [31579, 209], [31879, 209], [32179, 209], [32479, 209], [32779, 209], [33079, 208], [33380, 207], [33680, 206], [33980, 206], [34280, 205], [34581, 204], [34881, 204], [35181, 203], [35481, 203], [35781, 202], [36082, 201], [36382, 200], [36682, 200], [36982, 199], [37283, 198], [37583, 197], [37883, 197], [38183, 196], [38484, 195], [38784, 195], [39084, 194], [39384, 194], [39685, 192], [39985, 192], [40285, 191], [40585, 191], [40885, 190], [41186, 189], [41486, 188], [41786, 188], [42086, 188], [42387, 186], [42687, 186], [42987, 185], [43287, 185], [43588, 183], [43888, 183], [44188, 182], [44488, 182], [44789, 180], [45089, 180], [45389, 179], [45689, 179], [45989, 179], [46290, 177], [46590, 177], [46890, 176], [47190, 176], [47491, 174], [47791, 174], [48091, 173], [48391, 173], [48692, 171], [48992, 171], [49292, 171], [49592, 170], [49893, 169], [50193, 168], [50496, 162], [50796, 161], [51096, 161], [51396, 160], [51697, 159], [51997, 158]], "point": [185, 103]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-02.26|+00.85|+00.50"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [168, 128, 179, 141], "mask": [[38273, 3], [38571, 7], [38870, 8], [39169, 10], [39469, 11], [39768, 12], [40068, 12], [40368, 12], [40668, 12], [40968, 12], [41268, 11], [41569, 10], [41870, 8], [42171, 6]], "point": [173, 133]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.80|+00.81|+01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [2, 34, 295, 294], "mask": [[9994, 166], [10294, 167], [10594, 167], [10893, 169], [11193, 170], [11492, 172], [11792, 173], [12092, 173], [12391, 175], [12691, 176], [12990, 178], [13290, 178], [13590, 179], [13889, 181], [14189, 182], [14489, 182], [14788, 184], [15088, 185], [15387, 187], [15687, 187], [15987, 188], [16286, 190], [16586, 191], [16885, 193], [17185, 193], [17485, 194], [17784, 196], [18084, 197], [18383, 198], [18683, 199], [18983, 200], [19282, 202], [19582, 202], [19882, 203], [20181, 205], [20481, 206], [20780, 208], [21080, 208], [21380, 209], [21679, 211], [21979, 212], [22278, 213], [22578, 214], [22878, 215], [23177, 217], [23477, 217], [23776, 219], [24076, 220], [24376, 220], [24676, 219], [24977, 218], [25277, 218], [25577, 217], [25870, 224], [26170, 223], [26469, 224], [26768, 224], [27068, 224], [27367, 224], [27666, 225], [27965, 226], [28265, 225], [28564, 226], [28864, 225], [29163, 226], [29463, 226], [29763, 226], [30062, 78], [30149, 140], [30362, 75], [30452, 137], [30661, 74], [30754, 134], [30961, 73], [31055, 133], [31260, 73], [31357, 131], [31560, 72], [31658, 130], [31860, 71], [31959, 129], [32159, 72], [32259, 129], [32459, 71], [32560, 128], [32758, 72], [32860, 128], [33058, 71], [33161, 126], [33358, 71], [33461, 126], [33657, 72], [33761, 125], [33957, 72], [34061, 125], [34256, 73], [34362, 123], [34556, 73], [34662, 123], [34855, 74], [34962, 123], [35155, 74], [35262, 122], [35455, 74], [35562, 122], [35754, 75], [35861, 122], [36054, 75], [36161, 122], [36353, 77], [36461, 121], [36653, 77], [36760, 122], [36953, 77], [37060, 121], [37252, 79], [37360, 121], [37552, 79], [37659, 121], [37851, 81], [37958, 122], [38151, 81], [38258, 121], [38450, 83], [38557, 122], [38750, 84], [38856, 123], [39050, 84], [39156, 122], [39349, 86], [39455, 123], [39649, 87], [39754, 123], [39948, 89], [40053, 124], [40248, 90], [40352, 124], [40548, 92], [40650, 126], [40847, 95], [40948, 127], [41147, 228], [41446, 228], [41746, 228], [42045, 229], [42345, 228], [42645, 228], [42944, 228], [43244, 228], [43543, 228], [43843, 228], [44143, 227], [44442, 228], [44742, 227], [45041, 228], [45341, 227], [45640, 228], [45940, 228], [46240, 227], [46539, 228], [46839, 227], [47138, 228], [47438, 227], [47738, 227], [48037, 227], [48337, 227], [48636, 227], [48936, 227], [49235, 228], [49535, 227], [49835, 227], [50134, 227], [50434, 224], [50733, 224], [51033, 224], [51333, 223], [51632, 64], [51697, 159], [51932, 64], [51997, 158], [52231, 65], [52531, 65], [52830, 66], [53130, 66], [53430, 65], [53729, 66], [54029, 66], [54328, 67], [54628, 67], [54927, 67], [55227, 67], [55527, 67], [55826, 68], [56126, 68], [56425, 68], [56725, 68], [57025, 68], [57324, 69], [57624, 69], [57923, 70], [58223, 69], [58522, 70], [58822, 70], [59122, 70], [59421, 71], [59721, 70], [60020, 71], [60320, 71], [60620, 71], [60919, 72], [61219, 71], [61518, 72], [61818, 72], [62117, 73], [62417, 73], [62717, 72], [63016, 73], [63316, 73], [63615, 74], [63915, 74], [64215, 74], [64514, 74], [64814, 74], [65113, 75], [65413, 75], [65712, 76], [66012, 75], [66312, 75], [66611, 76], [66911, 76], [67210, 77], [67510, 76], [67810, 76], [68109, 77], [68409, 77], [68708, 78], [69008, 77], [69307, 78], [69607, 78], [69907, 78], [70206, 79], [70506, 79], [70805, 79], [71105, 79], [71405, 79], [71704, 80], [72004, 80], [72303, 80], [72603, 80], [72902, 81], [73202, 81], [73503, 80], [73804, 78], [74105, 77], [74405, 77], [74706, 76], [75007, 75], [75308, 74], [75609, 72], [75910, 71], [76211, 70], [76512, 69], [76813, 68], [77113, 67], [77414, 66], [77715, 65], [78016, 64], [78317, 63], [78618, 61], [78919, 60], [79220, 59], [79521, 58], [79822, 57], [80122, 56], [80423, 55], [80724, 54], [81025, 53], [81326, 52], [81627, 51], [81928, 49], [82229, 48], [82530, 47], [82831, 46], [83131, 46], [83432, 44], [83733, 43], [84034, 42], [84335, 41], [84636, 40], [84937, 38], [85238, 37], [85539, 36], [85839, 36], [86140, 38], [86441, 39], [86742, 39], [87043, 31], [87076, 5], [87345, 29], [87377, 2], [87649, 25], [87953, 20]], "point": [148, 163]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-02.26|+00.85|+00.50", "placeStationary": true, "receptacleObjectId": "Sink|-01.84|+00.73|+00.34|SinkBasin"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [120, 121, 275, 149], "mask": [[36130, 33], [36196, 4], [36236, 20], [36427, 38], [36493, 7], [36536, 23], [36726, 41], [36792, 7], [36836, 25], [37025, 43], [37091, 8], [37135, 27], [37324, 45], [37390, 9], [37435, 28], [37623, 46], [37690, 9], [37734, 30], [37923, 47], [37990, 9], [38033, 32], [38223, 47], [38290, 9], [38332, 34], [38523, 47], [38590, 9], [38631, 35], [38822, 48], [38890, 9], [38931, 35], [39122, 48], [39191, 7], [39230, 37], [39422, 48], [39491, 7], [39530, 37], [39722, 48], [39791, 7], [39829, 39], [40022, 48], [40091, 7], [40128, 40], [40322, 48], [40391, 8], [40426, 43], [40622, 48], [40692, 7], [40725, 44], [40922, 49], [40992, 8], [41024, 42], [41222, 49], [41292, 9], [41302, 1], [41323, 35], [41521, 50], [41592, 20], [41620, 27], [41821, 50], [41892, 34], [41948, 23], [42121, 50], [42192, 34], [42232, 2], [42244, 28], [42421, 50], [42493, 33], [42530, 5], [42542, 30], [42721, 50], [42794, 33], [42829, 5], [42841, 32], [43021, 50], [43095, 38], [43139, 34], [43321, 50], [43397, 77], [43621, 50], [43699, 75], [43920, 51], [44001, 74], [44220, 51], [44304, 71], [44522, 45], [44608, 68]], "point": [197, 134]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan25", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -2.0, "y": 0.9009992, "z": 2.25}, "object_poses": [{"objectName": "Pan_e0764688", "position": {"x": -0.2442, "y": 0.8297, "z": 1.684}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_3943cbe8", "position": {"x": -2.54089117, "y": 1.89342344, "z": 2.42067075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_d1baac51", "position": {"x": -0.169301391, "y": 0.823642, "z": 1.21489143}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_2c8fe53a", "position": {"x": -1.96787512, "y": 0.7030673, "z": 0.295923918}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_df0734fa", "position": {"x": -2.65868163, "y": 0.8247293, "z": 0.544074237}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_68224885", "position": {"x": -0.262604654, "y": 0.08077991, "z": 2.60685635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_298dd93a", "position": {"x": -2.957396, "y": 0.8492183, "z": 0.1297577}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_3a4a4d0d", "position": {"x": -2.375244, "y": 0.8213809, "z": 0.278}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_4c742e05", "position": {"x": -2.78456974, "y": 1.43164122, "z": 0.2932866}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "SaltShaker_69e12ad1", "position": {"x": -2.8592093, "y": 1.4294852, "z": 1.5882057}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_69e12ad1", "position": {"x": -1.63772511, "y": 0.1203081, "z": 0.323648632}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b2f1312c", "position": {"x": -2.84096551, "y": 0.9075473, "z": 0.20387885}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_57da2f86", "position": {"x": -2.726793, "y": 0.8198504, "z": 2.22170019}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -2.74435759, "y": 0.9944068, "z": 1.23169577}, "rotation": {"x": 0.0, "y": 269.999878, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -0.362648815, "y": 0.129467249, "z": 2.67462254}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_0dd786e0", "position": {"x": -2.7820003, "y": 1.5185833, "z": 2.309645}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Fork_2c8fe53a", "position": {"x": -1.21490157, "y": 0.813989162, "z": 0.498001218}, "rotation": {"x": -0.0005676719, "y": -0.000455468951, "z": 359.7126}}, {"objectName": "Ladle_3943cbe8", "position": {"x": -2.54089117, "y": 1.89342153, "z": 1.97887111}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_e0764688", "position": {"x": -0.2442, "y": 0.8297, "z": 2.0755}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "SoapBottle_3a4a4d0d", "position": {"x": -2.60917473, "y": 0.11931026, "z": 1.55042481}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_ccf3345d", "position": {"x": -0.531748533, "y": 0.8226794, "z": 2.09645438}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_2ee69558", "position": {"x": -2.66940522, "y": 1.18147969, "z": 2.37310648}, "rotation": {"x": 359.939575, "y": 0.00730837928, "z": 0.37767002}}, {"objectName": "Potato_a3c14b3a", "position": {"x": -2.67158461, "y": 1.182501, "z": 2.04581046}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_69e12ad1", "position": {"x": -0.47795558, "y": 0.8185999, "z": 1.32607329}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_29dab58d", "position": {"x": -2.58694315, "y": 0.8134342, "z": 0.309928238}, "rotation": {"x": -0.005284878, "y": -0.000626178342, "z": 0.0007639088}}, {"objectName": "Spatula_77ce7117", "position": {"x": -0.341, "y": 0.696, "z": 1.194}, "rotation": {"x": 0.0, "y": 45.0000267, "z": 0.0}}, {"objectName": "Knife_298dd93a", "position": {"x": -1.7095356, "y": 0.849218249, "z": 0.5084426}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_68224885", "position": {"x": -2.25881338, "y": 0.8478783, "z": 0.500363469}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_61785f6c", "position": {"x": -2.83720732, "y": 1.144056, "z": 2.133755}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ButterKnife_d1baac51", "position": {"x": -2.4939487, "y": 0.812210739, "z": 0.476511866}, "rotation": {"x": 0.0, "y": 50.7348976, "z": 0.0}}, {"objectName": "Lettuce_b2f1312c", "position": {"x": -1.8365, "y": 0.7415, "z": 0.238453716}, "rotation": {"x": 0.0, "y": 55.91092, "z": 0.0}}, {"objectName": "PepperShaker_407d7c3b", "position": {"x": -2.35832071, "y": 1.43102276, "z": 0.142315656}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_eb848da8", "position": {"x": -2.50179482, "y": 1.84862339, "z": 1.8231262}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_db4f73f8", "position": {"x": -0.979999542, "y": 0.855475545, "z": 0.249999925}, "rotation": {"x": -1.38762762e-05, "y": 29.9999161, "z": 4.06328145e-05}}, {"objectName": "Spoon_df0734fa", "position": {"x": -2.59749985, "y": 0.693495333, "z": 1.54731381}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_57da2f86", "position": {"x": -2.78199935, "y": 1.44061768, "z": 1.957865}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_4c742e05", "position": {"x": -2.05799365, "y": 0.121914148, "z": 0.401696056}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1931193691, "scene_num": 25}, "task_id": "trial_T20190907_024723_698757", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AD0NVUGLDYDYN_3137ONMDKJMLLXIRAEU5P8M9ELGEG9", "high_descs": ["Turn right, walk to the sink", "Grab the egg by the sink", "Turn around, walk to the microwave", "Open the microwave, put the egg in, close the microwave, turn on the microwave, open the microwave, take the egg out, close the microwave", "Turn around, walk to the sink", "Put the egg in the sink on the left"], "task_desc": "Cook the egg in the microwave, put the cooked egg in the sink on the left", "votes": [1, 1]}, {"assignment_id": "AM2KK02JXXW48_3COPXFW7XETUKA309JVVHCD6Z90KP4", "high_descs": ["Move to the sink.", "Pick up the brown egg on the counter right of the sink. ", "Bring the egg to the microwave.", "Heat the egg in the microwave.", "Bring the egg to the sink.", "Put the egg in the sink on the left. "], "task_desc": "Put a heated egg in the sink on the left. ", "votes": [1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3LYA37P8IT4SGG2QQEDUMS8HI6LBKC", "high_descs": ["Turn right and go to the sink.", "Pick up the egg on the counter, to the right of the sink.", "Turn around, then go left to the microwave.", "Heat the egg in the microwave.", "Turn around, then go right to the sink.", "Place the egg in the left sink basin."], "task_desc": "Put a cooked egg in the sink.", "votes": [1, 1]}]}}