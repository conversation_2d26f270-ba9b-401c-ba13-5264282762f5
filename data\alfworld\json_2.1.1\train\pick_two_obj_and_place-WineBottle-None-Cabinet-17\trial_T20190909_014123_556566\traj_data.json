{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 8}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000245.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000246.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000247.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000248.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000249.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000250.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000251.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000252.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000253.png", "low_idx": 40}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "WineBottle", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-2|4|3|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["winebottle"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["WineBottle", [-5.51214124, -5.51214124, 4.370749, 4.370749, 3.008345364, 3.008345364]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-5.204, -5.204, 3.952, 3.952, 0.032000004, 0.032000004]], "forceVisible": true, "objectId": "WineBottle|-01.38|+00.75|+01.09"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|0|5|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["winebottle", "cabinet"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["WineBottle", [-5.51214124, -5.51214124, 4.370749, 4.370749, 3.008345364, 3.008345364]], "coordinateReceptacleObjectId": ["Cabinet", [3.955002548, 3.955002548, 2.326399804, 2.326399804, 1.610000132, 1.610000132]], "forceVisible": true, "objectId": "WineBottle|-01.38|+00.75|+01.09", "receptacleObjectId": "Cabinet|+00.99|+00.40|+00.58"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|10|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["winebottle"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["WineBottle", [-3.67952776, -3.67952776, 9.836, 9.836, 3.00222, 3.00222]], "coordinateReceptacleObjectId": ["DiningTable", [-4.8448, -4.8448, 9.836, 9.836, 3.1376, 3.1376]], "forceVisible": true, "objectId": "WineBottle|-00.92|+00.75|+02.46"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|0|5|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["winebottle", "cabinet"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["WineBottle", [-3.67952776, -3.67952776, 9.836, 9.836, 3.00222, 3.00222]], "coordinateReceptacleObjectId": ["Cabinet", [3.955002548, 3.955002548, 2.326399804, 2.326399804, 1.610000132, 1.610000132]], "forceVisible": true, "objectId": "WineBottle|-00.92|+00.75|+02.46", "receptacleObjectId": "Cabinet|+00.99|+00.40|+00.58"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 81899], [81900, 298], [82200, 297], [82500, 296], [82800, 294], [83100, 293], [83401, 291], [83703, 288], [84004, 286], [84305, 284], [84606, 282], [84907, 280], [85208, 278], [85509, 276], [85811, 273], [86112, 271], [86413, 268], [86714, 266], [87015, 264], [87316, 262], [87617, 260], [87919, 257], [88220, 255], [88521, 253], [88822, 251], [89123, 249], [89424, 247], [89725, 244]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "WineBottle|-01.38|+00.75|+01.09"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [161, 145, 178, 150], "mask": [[43361, 18], [43662, 17], [43962, 16], [44264, 13], [44565, 10], [44870, 1]], "point": [169, 146]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-01.30|+00.01|+00.99"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 1602], [1606, 295], [1907, 289], [2197, 4], [2207, 4], [2212, 282], [2496, 5], [2506, 5], [2513, 280], [2796, 4], [2806, 4], [2815, 276], [3095, 5], [3106, 4], [3116, 275], [3395, 4], [3405, 5], [3416, 274], [3694, 5], [3705, 4], [3715, 274], [3994, 5], [4004, 5], [4015, 274], [4293, 5], [4304, 5], [4315, 4], [4320, 268], [4593, 5], [4604, 4], [4615, 4], [4620, 268], [4893, 4], [4903, 5], [4914, 5], [4921, 267], [5192, 5], [5203, 5], [5214, 5], [5222, 266], [5492, 4], [5503, 4], [5514, 5], [5522, 918], [6460, 276], [6764, 269], [7067, 264], [7369, 260], [7671, 216], [7923, 5], [7972, 215], [8223, 3], [8274, 213], [8523, 3], [8574, 214], [8823, 2], [8875, 213], [9123, 2], [9175, 214], [9423, 1], [9476, 213], [9723, 1], [9776, 214], [10023, 1], [10076, 215], [10323, 1], [10376, 215], [10623, 1], [10676, 216], [10922, 2], [10976, 217], [11222, 3], [11275, 219], [11521, 4], [11575, 220], [11820, 6], [11874, 222], [12120, 6], [12174, 223], [12419, 7], [12474, 225], [12718, 8], [12774, 226], [13017, 9], [13074, 227], [13316, 10], [13374, 231], [13612, 14], [13674, 252], [13974, 252], [14274, 252], [14574, 252], [14874, 252], [15174, 252], [15474, 252], [15774, 252], [16074, 252], [16374, 252], [16674, 252], [16974, 252], [17274, 252], [17574, 252], [17874, 253], [18173, 254], [18473, 254], [18773, 254], [19073, 254], [19373, 254], [19673, 254], [19973, 254], [20273, 254], [20573, 254], [20873, 254], [21173, 254], [21473, 254], [21773, 254], [22073, 254], [22373, 254], [22673, 254], [22973, 254], [23273, 254], [23573, 254], [23873, 255], [24172, 256], [24472, 256], [24772, 256], [25072, 256], [25372, 256], [25672, 256], [25972, 256], [26272, 256], [26572, 256], [26872, 256], [27172, 256], [27472, 256], [27772, 256], [28072, 256], [28372, 256], [28672, 256], [28972, 256], [29272, 256], [29572, 256], [29872, 257], [30171, 258], [30471, 258], [30771, 258], [31071, 258], [31371, 258], [31671, 258], [31971, 258], [32271, 258], [32571, 258], [32871, 258], [33171, 258], [33471, 258], [33771, 258], [34071, 258], [34371, 258], [34671, 258], [34971, 258], [35271, 258], [35571, 258], [35871, 259], [36170, 260], [36470, 260], [36770, 260], [37070, 260], [37370, 260], [37670, 259], [37971, 258], [38271, 257], [38572, 256], [38872, 255], [39173, 253], [39474, 251], [39775, 250], [40075, 249], [40376, 247], [40677, 245], [40978, 243], [41279, 242], [41579, 241], [41880, 239], [42181, 237], [42482, 235], [42783, 234], [43083, 233], [43384, 231], [43685, 229], [43986, 227], [44287, 226], [44587, 225], [44888, 223], [45189, 221], [45490, 220], [45790, 219], [46091, 217], [46392, 216], [46692, 215], [46993, 214], [47293, 213], [47594, 212], [47894, 212], [48194, 211], [48495, 210], [48795, 210], [49095, 209], [49396, 208], [49696, 208], [49996, 208], [50296, 208], [50596, 207], [50897, 206], [51197, 206], [51497, 206], [51797, 206], [52097, 206], [52397, 206], [52697, 206], [52997, 206], [53297, 206], [53597, 206], [53897, 206], [54197, 206], [54497, 206], [54797, 206], [55097, 206], [55397, 206], [55697, 206], [55997, 206], [56297, 206], [56597, 206], [56897, 207], [57196, 208], [57496, 208], [57796, 208], [58096, 208], [58396, 209], [58695, 210], [58995, 210], [59295, 210], [59595, 211], [59894, 212], [60194, 212], [60494, 212], [60794, 212], [61094, 213], [61393, 214], [61693, 214], [61993, 214], [62293, 214], [62593, 215], [62892, 216], [63192, 216], [63492, 216], [63792, 216], [64092, 217], [64391, 218], [64691, 218], [64991, 218], [65291, 219], [65590, 110], [65701, 109], [65890, 110], [66001, 109], [66190, 110], [66302, 108], [66490, 110], [66603, 107], [66790, 110], [66904, 79], [66986, 25], [67089, 111], [67204, 77], [67289, 22], [67389, 111], [67505, 74], [67590, 21], [67689, 111], [67806, 72], [67891, 20], [67989, 111], [68107, 71], [68192, 19], [68289, 111], [68408, 69], [68492, 20], [68588, 112], [68708, 69], [68793, 19], [68888, 112], [69009, 68], [69093, 19], [69188, 112], [69310, 66], [69393, 19], [69488, 112], [69611, 65], [69693, 19], [69788, 112], [69912, 64], [69993, 20], [70087, 113], [70212, 64], [70293, 20], [70387, 113], [70513, 63], [70593, 20], [70687, 113], [70814, 63], [70892, 21], [70987, 113], [71115, 63], [71192, 22], [71286, 114], [71415, 64], [71491, 23], [71586, 114], [71716, 64], [71790, 24], [71886, 114], [72017, 66], [72088, 26], [72186, 114], [72318, 96], [72486, 114], [72619, 96], [72785, 115], [72919, 96], [73085, 115], [73220, 95], [73385, 115], [73521, 94], [73685, 115], [73822, 93], [73985, 115], [74123, 93], [74284, 116], [74423, 93], [74584, 116], [74724, 92], [74884, 116], [75025, 91], [75184, 116], [75326, 91], [75483, 117], [75626, 91], [75783, 117], [75927, 90], [76083, 117], [76228, 89], [76383, 117], [76529, 88], [76683, 117], [76830, 88], [76982, 118], [77130, 88], [77282, 118], [77431, 87], [77582, 118], [77732, 86], [77882, 118], [78033, 85], [78182, 118], [78334, 85], [78481, 119], [78634, 85], [78781, 119], [78935, 84], [79081, 119], [79236, 83], [79381, 119], [79537, 82], [79681, 119], [79837, 83], [79980, 120], [80138, 82], [80280, 120], [80439, 81], [80580, 120], [80740, 81], [80879, 121], [81041, 80], [81179, 121], [81341, 80], [81479, 121], [81642, 80], [81778, 122], [81943, 79], [82078, 122], [82244, 78], [82378, 122], [82545, 78], [82677, 123], [82845, 79], [82976, 124], [83146, 78], [83276, 124], [83447, 78], [83575, 125], [83748, 78], [83874, 126], [84048, 78], [84174, 126], [84349, 78], [84473, 127], [84650, 78], [84772, 128], [84951, 79], [85070, 130], [85252, 79], [85369, 131], [85556, 76], [85668, 72], [85743, 57], [86043, 57], [86344, 56], [86644, 56], [86944, 56], [87244, 56], [87544, 56], [87845, 55], [88145, 55], [88445, 55], [88745, 55], [89046, 54], [89346, 54], [89646, 54], [89946, 54]], "point": [124, 133]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+00.99|+00.40|+00.58"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [205, 135, 281, 203], "mask": [[40422, 59], [40721, 60], [41021, 61], [41321, 60], [41621, 60], [41920, 60], [42220, 60], [42520, 59], [42820, 59], [43119, 59], [43419, 59], [43719, 58], [44019, 58], [44318, 59], [44618, 58], [44918, 58], [45218, 57], [45517, 58], [45817, 57], [46117, 57], [46417, 56], [46716, 57], [47016, 56], [47316, 56], [47615, 56], [47915, 56], [48215, 55], [48515, 55], [48814, 55], [49114, 55], [49414, 54], [49714, 54], [50013, 54], [50313, 54], [50613, 53], [50913, 53], [51212, 53], [51512, 53], [51812, 52], [52112, 52], [52411, 52], [52711, 52], [53011, 52], [53311, 51], [53610, 52], [53910, 51], [54210, 51], [54509, 51], [54809, 51], [55109, 50], [55409, 50], [55708, 50], [56008, 50], [56308, 49], [56608, 49], [56907, 49], [57207, 49], [57507, 48], [57807, 48], [58106, 48], [58406, 48], [58706, 47], [59006, 47], [59305, 47], [59605, 47], [59905, 46], [60205, 46], [60505, 45], [60805, 45]], "point": [243, 168]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "WineBottle|-01.38|+00.75|+01.09", "placeStationary": true, "receptacleObjectId": "Cabinet|+00.99|+00.40|+00.58"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [205, 135, 299, 245], "mask": [[40423, 58], [40722, 61], [41022, 62], [41322, 62], [41622, 63], [41921, 64], [42221, 65], [42521, 65], [42820, 67], [43120, 67], [43420, 68], [43720, 68], [44019, 70], [44319, 70], [44619, 71], [44919, 72], [45218, 73], [45518, 74], [45818, 74], [46118, 75], [46417, 76], [46717, 77], [47017, 77], [47317, 78], [47616, 79], [47916, 80], [48216, 81], [48515, 82], [48815, 83], [49115, 83], [49415, 84], [49714, 85], [50014, 86], [50314, 86], [50614, 86], [50913, 87], [51213, 87], [51513, 87], [51813, 87], [52112, 88], [52412, 88], [52712, 88], [53012, 88], [53311, 89], [53611, 89], [53911, 89], [54210, 90], [54510, 90], [54810, 90], [55110, 90], [55409, 91], [55709, 91], [56009, 91], [56309, 91], [56608, 92], [56908, 92], [57208, 92], [57508, 92], [57807, 93], [58107, 93], [58407, 93], [58706, 93], [59006, 92], [59306, 92], [59606, 91], [59905, 91], [60248, 48], [60548, 47], [60848, 46], [61148, 45], [61449, 44], [61749, 43], [62049, 42], [62350, 41], [62650, 40], [62950, 39], [63251, 38], [63551, 37], [63851, 36], [64152, 34], [64452, 34], [64752, 33], [65053, 31], [65353, 31], [65653, 30], [65953, 29], [66254, 28], [66554, 27], [66854, 26], [67155, 25], [67455, 24], [67755, 23], [68056, 21], [68356, 21], [68656, 20], [68957, 18], [69257, 18], [69557, 17], [69858, 15], [70158, 15], [70458, 14], [70758, 13], [71059, 11], [71359, 11], [71659, 10], [71960, 8], [72260, 8], [72560, 7], [72861, 5], [73161, 5], [73461, 4]], "point": [252, 189]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+00.99|+00.40|+00.58"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [205, 135, 299, 245], "mask": [[40423, 58], [40722, 61], [41022, 62], [41322, 62], [41622, 63], [41921, 64], [42221, 65], [42521, 65], [42820, 67], [43120, 67], [43420, 68], [43720, 68], [44019, 70], [44319, 70], [44619, 71], [44919, 72], [45218, 73], [45518, 74], [45818, 74], [46118, 75], [46417, 76], [46717, 3], [46725, 69], [47017, 3], [47025, 69], [47317, 3], [47325, 70], [47616, 4], [47625, 70], [47916, 3], [47924, 72], [48216, 3], [48224, 73], [48515, 4], [48524, 73], [48815, 3], [48824, 74], [49115, 3], [49123, 75], [49415, 1], [49423, 76], [49724, 75], [50024, 76], [50325, 75], [50625, 75], [50926, 74], [51226, 74], [51526, 74], [51826, 74], [52125, 75], [52425, 75], [52725, 75], [53024, 76], [53324, 76], [53624, 76], [53923, 77], [54223, 77], [54523, 77], [54822, 78], [55122, 78], [55422, 78], [55722, 78], [56021, 79], [56321, 79], [56621, 79], [56920, 80], [57220, 80], [57508, 1], [57519, 81], [57807, 4], [57818, 82], [58107, 6], [58116, 84], [58407, 93], [58706, 93], [59006, 92], [59306, 92], [59606, 91], [59905, 91], [60248, 48], [60548, 47], [60848, 46], [61148, 45], [61449, 44], [61749, 43], [62049, 42], [62350, 41], [62650, 40], [62950, 39], [63251, 38], [63551, 37], [63851, 36], [64152, 34], [64452, 34], [64752, 33], [65053, 31], [65353, 31], [65653, 30], [65953, 29], [66254, 28], [66554, 27], [66854, 26], [67155, 25], [67455, 24], [67755, 23], [68056, 21], [68356, 21], [68656, 20], [68957, 18], [69257, 18], [69557, 17], [69858, 15], [70158, 15], [70458, 14], [70758, 13], [71059, 11], [71359, 11], [71659, 10], [71960, 8], [72260, 8], [72560, 7], [72861, 5], [73161, 5], [73461, 4]], "point": [252, 189]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "WineBottle|-00.92|+00.75|+02.46"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [121, 103, 150, 176], "mask": [[30731, 6], [31029, 9], [31329, 10], [31628, 12], [31928, 12], [32228, 12], [32528, 12], [32828, 12], [33128, 12], [33428, 12], [33728, 12], [34029, 11], [34329, 11], [34629, 11], [34929, 11], [35229, 11], [35529, 11], [35829, 11], [36129, 12], [36429, 12], [36729, 14], [37027, 18], [37326, 20], [37625, 22], [37924, 24], [38223, 26], [38523, 26], [38822, 28], [39122, 28], [39422, 28], [39722, 29], [40021, 30], [40321, 30], [40621, 30], [40921, 30], [41221, 30], [41521, 30], [41821, 30], [42122, 29], [42422, 29], [42722, 29], [43022, 29], [43322, 29], [43622, 29], [43923, 28], [44223, 28], [44523, 28], [44823, 28], [45123, 28], [45424, 27], [45724, 27], [46024, 27], [46324, 27], [46624, 27], [46924, 27], [47225, 26], [47525, 26], [47825, 26], [48125, 26], [48425, 26], [48725, 26], [49026, 25], [49326, 25], [49626, 25], [49926, 25], [50226, 24], [50527, 23], [50827, 22], [51128, 21], [51429, 19], [51730, 17], [52031, 15], [52333, 11], [52636, 5]], "point": [135, 138]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+00.99|+00.40|+00.58"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [205, 135, 281, 203], "mask": [[40422, 59], [40721, 60], [41021, 61], [41321, 60], [41621, 60], [41920, 60], [42220, 60], [42520, 59], [42820, 59], [43119, 59], [43419, 59], [43719, 58], [44019, 58], [44318, 59], [44618, 58], [44918, 58], [45218, 57], [45517, 58], [45817, 57], [46117, 57], [46417, 56], [46716, 57], [47016, 56], [47316, 56], [47615, 56], [47915, 56], [48215, 55], [48515, 55], [48814, 55], [49114, 55], [49414, 54], [49714, 54], [50013, 54], [50313, 54], [50613, 53], [50913, 53], [51212, 53], [51512, 53], [51812, 52], [52112, 52], [52411, 52], [52711, 52], [53011, 52], [53311, 51], [53610, 52], [53910, 51], [54210, 51], [54509, 51], [54809, 51], [55109, 50], [55409, 50], [55708, 50], [56008, 50], [56308, 49], [56608, 49], [56907, 49], [57207, 49], [57507, 48], [57807, 48], [58106, 48], [58406, 48], [58706, 47], [59006, 47], [59305, 47], [59605, 47], [59905, 46], [60205, 46], [60505, 45], [60805, 45]], "point": [243, 168]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "WineBottle|-00.92|+00.75|+02.46", "placeStationary": true, "receptacleObjectId": "Cabinet|+00.99|+00.40|+00.58"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [205, 135, 299, 245], "mask": [[40423, 58], [40722, 61], [41022, 62], [41322, 62], [41622, 63], [41921, 64], [42221, 65], [42521, 65], [42820, 67], [43120, 67], [43420, 68], [43720, 68], [44019, 70], [44319, 70], [44619, 71], [44919, 72], [45218, 73], [45518, 74], [45818, 74], [46118, 75], [46417, 76], [46717, 3], [46725, 69], [47017, 3], [47025, 69], [47317, 3], [47325, 70], [47616, 4], [47625, 70], [47916, 3], [47924, 72], [48216, 3], [48224, 73], [48515, 4], [48524, 73], [48815, 3], [48824, 74], [49115, 3], [49123, 75], [49415, 1], [49423, 76], [49724, 75], [50024, 76], [50325, 75], [50625, 75], [50926, 74], [51226, 74], [51526, 74], [51826, 74], [52125, 75], [52425, 75], [52725, 75], [53024, 76], [53324, 76], [53624, 76], [53923, 77], [54223, 77], [54523, 77], [54822, 78], [55122, 78], [55422, 78], [55722, 78], [56021, 79], [56321, 79], [56621, 79], [56920, 80], [57220, 80], [57508, 1], [57519, 81], [57807, 4], [57818, 82], [58107, 6], [58116, 84], [58407, 93], [58706, 93], [59006, 92], [59306, 92], [59606, 91], [59905, 91], [60248, 48], [60548, 47], [60848, 46], [61148, 45], [61449, 44], [61749, 43], [62049, 42], [62350, 41], [62650, 40], [62950, 39], [63251, 38], [63551, 37], [63851, 36], [64152, 34], [64452, 34], [64752, 33], [65053, 31], [65353, 31], [65653, 30], [65953, 29], [66254, 28], [66554, 27], [66854, 26], [67155, 25], [67455, 24], [67755, 23], [68056, 21], [68356, 21], [68656, 20], [68957, 18], [69257, 18], [69557, 17], [69858, 15], [70158, 15], [70458, 14], [70758, 13], [71059, 11], [71359, 11], [71659, 10], [71960, 8], [72260, 8], [72560, 7], [72861, 5], [73161, 5], [73461, 4]], "point": [252, 189]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+00.99|+00.40|+00.58"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [205, 135, 299, 245], "mask": [[40423, 58], [40722, 61], [41022, 62], [41322, 62], [41622, 63], [41921, 64], [42221, 65], [42521, 65], [42820, 67], [43120, 67], [43420, 68], [43720, 68], [44019, 70], [44319, 70], [44619, 71], [44919, 72], [45218, 73], [45518, 74], [45818, 74], [46118, 75], [46417, 76], [46717, 3], [46725, 14], [46743, 51], [47017, 3], [47025, 14], [47044, 50], [47317, 3], [47325, 13], [47344, 51], [47616, 4], [47625, 13], [47643, 52], [47916, 3], [47924, 14], [47943, 53], [48216, 3], [48224, 13], [48243, 54], [48515, 4], [48524, 13], [48542, 55], [48815, 3], [48824, 12], [48842, 56], [49115, 3], [49123, 13], [49142, 56], [49415, 1], [49423, 10], [49441, 58], [49724, 7], [49742, 57], [50024, 7], [50042, 58], [50325, 5], [50343, 57], [50625, 4], [50643, 57], [50926, 3], [50943, 57], [51226, 3], [51244, 56], [51526, 2], [51544, 56], [51826, 2], [51843, 57], [52125, 3], [52143, 57], [52425, 2], [52443, 57], [52725, 2], [52742, 58], [53024, 3], [53042, 58], [53324, 2], [53342, 58], [53624, 2], [53641, 59], [53923, 3], [53941, 59], [54223, 2], [54240, 60], [54523, 2], [54540, 60], [54822, 3], [54840, 60], [55122, 2], [55139, 61], [55422, 2], [55439, 61], [55722, 2], [55738, 62], [56021, 3], [56038, 62], [56321, 3], [56338, 62], [56621, 3], [56637, 63], [56920, 4], [56937, 63], [57220, 5], [57236, 64], [57508, 1], [57519, 7], [57536, 64], [57807, 4], [57818, 9], [57835, 65], [58107, 6], [58116, 14], [58133, 67], [58407, 93], [58706, 93], [59006, 92], [59306, 92], [59606, 91], [59905, 91], [60248, 48], [60548, 47], [60848, 46], [61148, 45], [61449, 44], [61749, 43], [62049, 42], [62350, 41], [62650, 40], [62950, 39], [63251, 38], [63551, 37], [63851, 36], [64152, 34], [64452, 34], [64752, 33], [65053, 31], [65353, 31], [65653, 30], [65953, 29], [66254, 28], [66554, 27], [66854, 26], [67155, 25], [67455, 24], [67755, 23], [68056, 21], [68356, 21], [68656, 20], [68957, 18], [69257, 18], [69557, 17], [69858, 15], [70158, 15], [70458, 14], [70758, 13], [71059, 11], [71359, 11], [71659, 10], [71960, 8], [72260, 8], [72560, 7], [72861, 5], [73161, 5], [73461, 4]], "point": [252, 189]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan17", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -0.5, "y": 0.908999562, "z": 0.5}, "object_poses": [{"objectName": "SprayBottle_4f8d0eee", "position": {"x": 1.20097, "y": 0.8857041, "z": 2.80043745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": -1.19785714, "y": 0.7938309, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": -1.383174, "y": 0.7484642, "z": 2.273428}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": 1.36534023, "y": 0.9115421, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": 1.03472006, "y": 0.8863421, "z": 2.692625}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": -0.91988194, "y": 0.750555, "z": 2.459}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": -1.37803531, "y": 0.752086341, "z": 1.09268725}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 1.36534023, "y": 0.9126294, "z": 0.4229527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": -1.18451083, "y": 0.777128, "z": -0.205843419}, "rotation": {"x": 0.0, "y": 89.9996643, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": -1.19785714, "y": 0.764626145, "z": 2.459}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": -0.503703535, "y": 0.9270999, "z": -0.5894883}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.802, "y": 0.9393, "z": -0.47308147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -0.0203592032, "y": 0.792722642, "z": -0.7306608}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": -1.383174, "y": 0.750511765, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 0.209448546, "y": 0.117524266, "z": -0.5338761}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -1.16246963, "y": 0.3912896, "z": 0.9880004}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -1.20685482, "y": 1.68668628, "z": 0.8832667}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": -0.91988194, "y": 0.7502286, "z": 3.015717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": 1.20740271, "y": 0.9128286, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": 1.13115919, "y": 0.115498424, "z": -0.05235876}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": -1.01254034, "y": 0.813041151, "z": 2.08785558}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -1.13565314, "y": 0.9723039, "z": -0.732326269}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -1.29051554, "y": 0.809103966, "z": 2.83014464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": 1.36722, "y": 0.9497149, "z": 2.36918736}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": -0.293590665, "y": 0.117355466, "z": -0.5777503}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": 1.444309, "y": 0.9120486, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": -1.357, "y": 0.7396263, "z": 3.024}, "rotation": {"x": 0.0, "y": 240.000229, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -1.3209753, "y": 1.47098637, "z": 1.09268737}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 1.20740271, "y": 0.9123855, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": 0.951595068, "y": 0.8840117, "z": 2.80043745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": 1.03472006, "y": 0.957268536, "z": 2.36918736}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.5029, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": 1.24557734, "y": 0.0875803, "z": 1.409267}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": 1.20097, "y": 0.8872287, "z": 2.15356255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": -0.6586952, "y": 0.9270999, "z": -0.669}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": -1.01254034, "y": 0.7480669, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -1.13076985, "y": 0.40176475, "z": 0.673938}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": -1.29628491, "y": 1.01470053, "z": -0.109642744}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": -1.01254034, "y": 0.771618962, "z": 3.015717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -1.42715728, "y": 1.04961908, "z": -0.00556433573}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": 1.30640435, "y": 0.113363981, "z": 0.07148978}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": -1.10519874, "y": 0.7452062, "z": 2.83014464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": -1.20685482, "y": 1.33328843, "z": 0.883312762}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": 1.128434, "y": 0.9115421, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": 1.128434, "y": 0.911064267, "z": 0.4229527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_a0065d6c", "position": {"x": -1.332, "y": 0.9, "z": -0.699}, "rotation": {"x": 0.0, "y": 345.0, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 1.03472006, "y": 0.8874294, "z": 2.477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": -1.383174, "y": 0.749030352, "z": 2.08785558}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": 1.0494653, "y": 0.906500041, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1429924808, "scene_num": 17}, "task_id": "trial_T20190909_014123_556566", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3HL2LL0LEPZT8_32RIADZISVVE4PGNCG1Q6MDNKZB4SQ", "high_descs": ["Turn around, go forward, turn left to the fridge.", "Take the wine bottle from the fridge.", "Turn around, go forward a step away from the counter.", "Put the wine in the left side of the bottom cabinet second from the red bin.", "Turn left, go forward, turn left to the white table, go to the table.", "Take the wine bottle from the table.", "Turn left, go forward, at the fridge turn left, go forward a step away from the counter.", "Put the wine in the bottom cabinet second from the red bin to the right of the first bottle."], "task_desc": "Put two wine bottles in a cabinet.", "votes": [1, 1, 1]}, {"assignment_id": "A20FCMWP43CVIU_3DR23U6WE85XBRQVQMITPDL0Z79ETL", "high_descs": ["walk to face fridge", "remove wine bottle from fridge", "turn to face cabinets under toaster", "put bottle in cabinet under toaster", "walk to face white table", "pick up wine bottle from table", "walk to face cabinets under toaster", "put bottle in cabinet under toaster"], "task_desc": "put two wine bottles in cabinet", "votes": [1, 1, 1]}, {"assignment_id": "A1E0WK5W1BFPWR_3CP1TO84PWS3V93PW65UKW6XM1552R", "high_descs": ["Turn around and move to the fridge on the left. ", "Open the fridge and take out the wine bottle.", "Turn around and take the wine bottle to the counter directly in front of you.", "Place the wine bottle into the second cupboard from the left, below the counter.", "Turn to the left and move towards the white table on the left. ", "Pick up the open wine bottle on the white table.", "Turn to the left and walk to the counter on the left.", "Place the wine bottle into the second cupboard from the left, below the counter."], "task_desc": "Organize all the wine bottles into the cupboard.", "votes": [0, 1, 1]}]}}