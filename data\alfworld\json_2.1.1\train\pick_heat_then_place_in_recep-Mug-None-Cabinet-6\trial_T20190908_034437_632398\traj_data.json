{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 46}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 47}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 48}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 49}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 50}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 59}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000296.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000297.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000298.png", "low_idx": 60}, {"high_idx": 5, "image_name": "000000299.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000300.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000301.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000302.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000303.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000304.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 61}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 62}, {"high_idx": 5, "image_name": "000000317.png", "low_idx": 62}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [7.10094928, 7.10094928, -3.59138584, -3.59138584, 3.623599768, 3.623599768]], "coordinateReceptacleObjectId": ["CounterTop", [1.896, 1.896, -6.5, -6.5, 3.7856, 3.7856]], "forceVisible": true, "objectId": "Mug|+01.78|+00.91|-00.90"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|3|-30"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [7.10094928, 7.10094928, -3.59138584, -3.59138584, 3.623599768, 3.623599768]], "coordinateReceptacleObjectId": ["Cabinet", [-9.784, -9.784, 11.7, 11.7, 7.79599952, 7.79599952]], "forceVisible": true, "objectId": "Mug|+01.78|+00.91|-00.90", "receptacleObjectId": "Cabinet|-02.45|+01.95|+02.93"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+01.78|+00.91|-00.90"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [177, 105, 201, 139], "mask": [[31385, 8], [31682, 15], [31980, 19], [32279, 21], [32579, 22], [32878, 24], [33178, 24], [33478, 24], [33778, 24], [34078, 24], [34378, 24], [34678, 24], [34978, 24], [35278, 23], [35578, 23], [35878, 23], [36178, 23], [36478, 23], [36778, 22], [37078, 22], [37378, 22], [37678, 22], [37978, 21], [38277, 22], [38577, 22], [38877, 22], [39177, 21], [39477, 21], [39778, 20], [40078, 20], [40379, 18], [40679, 18], [40981, 15], [41283, 11], [41586, 5]], "point": [189, 121]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+01.78|+00.91|-00.90", "placeStationary": true, "receptacleObjectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 92], [22296, 139], [22500, 90], [22598, 137], [22800, 89], [22899, 136], [23100, 88], [23200, 135], [23400, 87], [23501, 133], [23700, 86], [23801, 133], [24000, 86], [24102, 132], [24300, 85], [24402, 132], [24600, 84], [24702, 131], [24900, 84], [25002, 131], [25200, 84], [25302, 131], [25500, 84], [25602, 131], [25800, 84], [25902, 130], [26100, 84], [26201, 131], [26400, 84], [26501, 131], [26700, 84], [26800, 132], [27000, 84], [27100, 131], [27300, 85], [27399, 132], [27600, 85], [27698, 133], [27900, 86], [27997, 134], [28200, 87], [28296, 134], [28500, 88], [28595, 135], [28800, 91], [28892, 138], [29100, 230], [29400, 229], [29700, 229], [30000, 229], [30300, 229], [30600, 228], [30900, 228], [31200, 228], [31500, 228], [31800, 227], [32100, 227], [32400, 227], [32700, 227], [33000, 226], [33300, 226], [33600, 226], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 92], [22296, 139], [22500, 90], [22598, 137], [22800, 89], [22899, 40], [22951, 84], [23100, 88], [23200, 36], [23254, 81], [23400, 87], [23501, 33], [23555, 79], [23700, 86], [23801, 32], [23856, 78], [24000, 86], [24102, 30], [24157, 77], [24300, 85], [24402, 30], [24458, 76], [24600, 84], [24702, 30], [24758, 75], [24900, 84], [25002, 30], [25061, 72], [25200, 84], [25302, 30], [25362, 71], [25500, 84], [25602, 30], [25663, 70], [25800, 84], [25902, 30], [25958, 2], [25963, 69], [26100, 84], [26201, 31], [26257, 4], [26264, 68], [26400, 84], [26501, 31], [26557, 4], [26564, 68], [26700, 84], [26800, 32], [26857, 4], [26863, 69], [27000, 84], [27100, 32], [27157, 4], [27163, 68], [27300, 85], [27399, 34], [27457, 4], [27463, 68], [27600, 85], [27698, 35], [27757, 4], [27763, 68], [27900, 86], [27997, 36], [28057, 3], [28063, 68], [28200, 87], [28296, 37], [28357, 3], [28362, 68], [28500, 88], [28595, 38], [28657, 3], [28662, 68], [28800, 91], [28892, 41], [28957, 2], [28961, 69], [29100, 133], [29257, 2], [29261, 69], [29400, 133], [29557, 1], [29561, 68], [29700, 133], [29860, 69], [30000, 133], [30159, 70], [30300, 134], [30458, 71], [30600, 134], [30757, 71], [30900, 134], [31056, 72], [31200, 134], [31356, 72], [31500, 134], [31656, 72], [31800, 134], [31956, 71], [32100, 134], [32256, 71], [32400, 135], [32556, 71], [32700, 135], [32855, 72], [33000, 136], [33154, 72], [33300, 137], [33453, 73], [33600, 139], [33751, 75], [33900, 143], [34046, 79], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|+01.78|+00.91|-00.90"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [132, 77, 163, 114], "mask": [[22939, 12], [23236, 18], [23534, 21], [23833, 23], [24132, 25], [24432, 26], [24732, 26], [25032, 29], [25332, 30], [25632, 31], [25932, 26], [25960, 3], [26232, 25], [26261, 3], [26532, 25], [26561, 3], [26832, 25], [26861, 2], [27132, 25], [27161, 2], [27433, 24], [27461, 2], [27733, 24], [27761, 2], [28033, 24], [28060, 3], [28333, 24], [28360, 2], [28633, 24], [28660, 2], [28933, 24], [28959, 2], [29233, 24], [29259, 2], [29533, 24], [29558, 3], [29833, 27], [30133, 26], [30434, 24], [30734, 23], [31034, 22], [31334, 22], [31634, 22], [31934, 22], [32234, 22], [32535, 21], [32835, 20], [33136, 18], [33437, 16], [33739, 12], [34043, 3]], "point": [147, 94]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 92], [22296, 139], [22500, 90], [22598, 137], [22800, 89], [22899, 136], [23100, 88], [23200, 135], [23400, 87], [23501, 133], [23700, 86], [23801, 133], [24000, 86], [24102, 132], [24300, 85], [24402, 132], [24600, 84], [24702, 131], [24900, 84], [25002, 131], [25200, 84], [25302, 131], [25500, 84], [25602, 131], [25800, 84], [25902, 130], [26100, 84], [26201, 131], [26400, 84], [26501, 131], [26700, 84], [26800, 132], [27000, 84], [27100, 131], [27300, 85], [27399, 132], [27600, 85], [27698, 133], [27900, 86], [27997, 134], [28200, 87], [28296, 134], [28500, 88], [28595, 135], [28800, 91], [28892, 138], [29100, 230], [29400, 229], [29700, 229], [30000, 229], [30300, 229], [30600, 228], [30900, 228], [31200, 228], [31500, 228], [31800, 227], [32100, 227], [32400, 227], [32700, 227], [33000, 226], [33300, 226], [33600, 226], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-02.45|+01.95|+02.93"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [79, 62, 299, 300], "mask": [[18408, 152], [18707, 153], [19007, 154], [19307, 154], [19607, 154], [19907, 154], [20206, 156], [20506, 156], [20806, 156], [21106, 157], [21406, 157], [21706, 157], [22006, 158], [22306, 158], [22606, 158], [22905, 159], [23205, 160], [23505, 160], [23805, 160], [24105, 161], [24405, 161], [24705, 161], [25005, 162], [25304, 163], [25604, 163], [25904, 163], [26204, 164], [26504, 164], [26804, 164], [27104, 165], [27404, 165], [27704, 165], [28003, 167], [28303, 167], [28603, 167], [28903, 168], [29203, 168], [29503, 168], [29803, 168], [30103, 169], [30402, 170], [30702, 170], [31002, 171], [31302, 171], [31602, 171], [31902, 172], [32202, 172], [32502, 172], [32802, 172], [33101, 174], [33401, 174], [33701, 174], [34001, 175], [34301, 175], [34601, 175], [34901, 176], [35201, 176], [35500, 177], [35800, 178], [36100, 178], [36400, 178], [36700, 178], [37000, 179], [37300, 179], [37600, 179], [37900, 180], [38199, 181], [38499, 181], [38799, 182], [39099, 182], [39399, 182], [39699, 182], [39999, 183], [40299, 183], [40598, 184], [40898, 185], [41198, 185], [41498, 185], [41798, 186], [42098, 186], [42398, 186], [42698, 187], [42997, 188], [43297, 188], [43597, 188], [43897, 189], [44197, 189], [44497, 189], [44797, 190], [45097, 190], [45397, 190], [45696, 192], [45996, 192], [46296, 192], [46596, 192], [46896, 193], [47196, 193], [47496, 193], [47796, 194], [48095, 195], [48395, 195], [48695, 196], [48995, 196], [49295, 196], [49595, 197], [49895, 197], [50195, 197], [50495, 197], [50794, 199], [51094, 199], [51394, 199], [51694, 200], [51994, 200], [52294, 200], [52594, 201], [52894, 201], [53193, 202], [53493, 202], [53793, 203], [54093, 203], [54393, 203], [54693, 204], [54993, 204], [55293, 204], [55593, 205], [55892, 206], [56192, 206], [56492, 207], [56792, 207], [57092, 207], [57392, 207], [57692, 208], [57992, 50], [58058, 142], [58291, 47], [58362, 138], [58591, 45], [58664, 136], [58891, 42], [58967, 133], [59191, 40], [59269, 131], [59491, 38], [59571, 129], [59791, 37], [59872, 128], [60091, 35], [60174, 126], [60391, 34], [60475, 125], [60691, 33], [60776, 124], [60990, 33], [61077, 123], [61290, 32], [61378, 122], [61590, 31], [61679, 121], [61890, 30], [61980, 120], [62190, 29], [62281, 119], [62490, 28], [62582, 118], [62790, 28], [62882, 118], [63090, 27], [63183, 117], [63389, 28], [63483, 117], [63689, 27], [63784, 116], [63989, 26], [64085, 115], [64289, 26], [64385, 115], [64589, 25], [64686, 114], [64889, 25], [64986, 114], [65189, 25], [65286, 114], [65489, 25], [65586, 114], [65788, 25], [65887, 113], [66088, 25], [66187, 113], [66388, 25], [66487, 113], [66688, 19], [66787, 113], [66988, 16], [67088, 112], [67288, 14], [67388, 112], [67588, 12], [67688, 112], [67888, 11], [67988, 112], [68188, 11], [68288, 112], [68487, 12], [68588, 112], [68787, 11], [68888, 112], [69087, 11], [69188, 112], [69387, 10], [69487, 113], [69687, 10], [69705, 8], [69787, 113], [69987, 10], [70005, 8], [70087, 113], [70287, 10], [70305, 8], [70387, 113], [70587, 11], [70606, 7], [70687, 113], [70886, 12], [70906, 8], [70986, 114], [71186, 12], [71207, 7], [71286, 114], [71486, 13], [71508, 6], [71586, 114], [71786, 13], [71809, 5], [71886, 114], [72086, 14], [72110, 5], [72185, 115], [72386, 15], [72411, 4], [72485, 115], [72686, 15], [72712, 3], [72785, 115], [72986, 16], [73013, 3], [73084, 116], [73286, 17], [73314, 2], [73384, 116], [73585, 19], [73684, 116], [73885, 20], [73983, 117], [74185, 21], [74283, 117], [74485, 22], [74583, 117], [74785, 23], [74882, 118], [75085, 24], [75182, 118], [75385, 25], [75482, 118], [75685, 26], [75781, 119], [75984, 29], [76081, 119], [76284, 30], [76381, 119], [76584, 33], [76680, 120], [76884, 36], [76980, 120], [77184, 36], [77280, 120], [77484, 36], [77580, 120], [77784, 37], [77879, 121], [78084, 37], [78179, 121], [78384, 37], [78479, 121], [78683, 39], [78778, 122], [78983, 39], [79078, 122], [79283, 39], [79378, 122], [79583, 40], [79677, 123], [79883, 40], [79977, 123], [80183, 40], [80277, 123], [80483, 41], [80576, 124], [80783, 41], [80876, 124], [81082, 42], [81176, 124], [81382, 43], [81475, 125], [81682, 43], [81775, 125], [81982, 43], [82075, 125], [82282, 44], [82374, 126], [82582, 45], [82673, 127], [82882, 45], [82973, 127], [83182, 46], [83272, 128], [83482, 47], [83571, 129], [83781, 49], [83870, 130], [84081, 50], [84169, 131], [84381, 51], [84468, 132], [84681, 52], [84767, 133], [84981, 53], [85066, 134], [85281, 55], [85364, 136], [85581, 57], [85662, 138], [85881, 59], [85960, 140], [86180, 62], [86258, 142], [86480, 69], [86551, 149], [86780, 220], [87080, 220], [87380, 220], [87680, 220], [87980, 220], [88280, 220], [88580, 220], [88879, 221], [89179, 221], [89479, 221], [89779, 221]], "point": [189, 180]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|+01.78|+00.91|-00.90", "placeStationary": true, "receptacleObjectId": "Cabinet|-02.45|+01.95|+02.93"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [79, 1, 299, 300], "mask": [[284, 16], [584, 16], [884, 16], [1183, 17], [1483, 17], [1782, 18], [2082, 18], [2381, 19], [2681, 19], [2980, 20], [3280, 20], [3579, 21], [3879, 21], [4179, 21], [4478, 22], [4778, 22], [5077, 23], [5377, 23], [5676, 24], [5976, 24], [6275, 25], [6575, 25], [6875, 25], [7174, 26], [7474, 26], [7773, 27], [8073, 27], [8372, 28], [8672, 28], [8971, 29], [9271, 29], [9570, 30], [9870, 30], [10170, 30], [10469, 31], [10769, 31], [11068, 32], [11368, 32], [11667, 33], [11967, 33], [12266, 34], [12566, 34], [12866, 34], [13165, 35], [13465, 35], [13764, 36], [14064, 36], [14363, 37], [14663, 37], [14962, 38], [15262, 38], [15562, 38], [15861, 39], [16161, 39], [16460, 40], [16760, 40], [17059, 41], [17359, 41], [17658, 42], [17958, 42], [18257, 43], [18557, 43], [18858, 42], [19007, 193], [19307, 193], [19607, 193], [19907, 193], [20206, 194], [20506, 194], [20806, 194], [21106, 194], [21406, 194], [21706, 194], [22006, 194], [22306, 194], [22606, 194], [22905, 195], [23205, 195], [23505, 195], [23805, 195], [24105, 195], [24405, 195], [24705, 195], [25005, 195], [25304, 196], [25604, 196], [25904, 196], [26204, 196], [26504, 196], [26804, 196], [27104, 196], [27404, 196], [27704, 196], [28003, 197], [28303, 197], [28603, 197], [28903, 197], [29203, 197], [29503, 197], [29803, 197], [30103, 197], [30402, 198], [30702, 198], [31002, 198], [31302, 198], [31602, 198], [31902, 198], [32202, 198], [32502, 198], [32802, 198], [33101, 199], [33401, 199], [33701, 199], [34001, 199], [34301, 199], [34601, 199], [34901, 199], [35201, 199], [35500, 200], [35800, 200], [36100, 200], [36400, 200], [36700, 200], [37000, 200], [37300, 200], [37600, 200], [37900, 200], [38199, 201], [38499, 201], [38799, 201], [39099, 201], [39399, 201], [39699, 201], [39999, 201], [40299, 201], [40598, 202], [40898, 202], [41198, 202], [41498, 202], [41798, 202], [42098, 202], [42398, 202], [42698, 202], [42997, 203], [43297, 203], [43597, 203], [43897, 203], [44197, 203], [44497, 203], [44797, 203], [45097, 203], [45397, 203], [45696, 204], [45996, 204], [46296, 204], [46596, 204], [46896, 204], [47196, 204], [47496, 204], [47796, 204], [48095, 205], [48395, 205], [48695, 205], [48995, 205], [49295, 205], [49595, 205], [49895, 205], [50195, 205], [50495, 205], [50794, 206], [51094, 206], [51394, 206], [51694, 206], [51994, 206], [52294, 206], [52594, 206], [52894, 206], [53193, 207], [53493, 207], [53793, 207], [54093, 207], [54393, 207], [54693, 207], [54993, 207], [55293, 207], [55593, 207], [55892, 208], [56192, 208], [56492, 208], [56792, 208], [57092, 208], [57392, 208], [57692, 208], [57992, 50], [58058, 142], [58291, 47], [58362, 138], [58591, 45], [58664, 136], [58891, 42], [58967, 133], [59191, 40], [59269, 131], [59491, 38], [59571, 129], [59791, 37], [59872, 128], [60091, 35], [60174, 126], [60391, 34], [60475, 125], [60691, 33], [60776, 124], [60990, 33], [61077, 123], [61290, 32], [61378, 122], [61590, 31], [61679, 121], [61890, 30], [61980, 120], [62190, 29], [62281, 119], [62490, 28], [62582, 118], [62790, 28], [62882, 118], [63090, 27], [63183, 117], [63389, 28], [63483, 117], [63689, 27], [63784, 116], [63989, 26], [64085, 115], [64289, 26], [64385, 115], [64589, 25], [64686, 114], [64889, 25], [64986, 114], [65189, 25], [65286, 114], [65489, 25], [65586, 114], [65788, 25], [65887, 113], [66088, 25], [66187, 113], [66388, 25], [66487, 113], [66688, 19], [66787, 113], [66988, 16], [67088, 112], [67288, 14], [67388, 112], [67588, 12], [67688, 112], [67888, 11], [67988, 112], [68188, 11], [68288, 112], [68487, 12], [68588, 112], [68787, 11], [68888, 112], [69087, 11], [69188, 112], [69387, 10], [69487, 113], [69687, 10], [69705, 8], [69787, 113], [69987, 10], [70005, 8], [70087, 113], [70287, 10], [70305, 8], [70387, 113], [70587, 11], [70606, 7], [70687, 113], [70886, 12], [70906, 8], [70986, 114], [71186, 12], [71207, 7], [71286, 114], [71486, 13], [71508, 6], [71586, 114], [71786, 13], [71809, 5], [71886, 114], [72086, 14], [72110, 5], [72185, 115], [72386, 15], [72411, 4], [72485, 115], [72686, 15], [72712, 3], [72785, 54], [72843, 57], [72986, 16], [73013, 3], [73084, 54], [73143, 57], [73286, 17], [73314, 2], [73384, 54], [73446, 54], [73585, 19], [73684, 50], [73749, 51], [73885, 20], [73983, 50], [74051, 49], [74185, 21], [74283, 48], [74352, 48], [74485, 22], [74583, 48], [74653, 47], [74785, 23], [74882, 48], [74954, 46], [75085, 24], [75182, 48], [75254, 46], [75385, 25], [75482, 48], [75555, 45], [75685, 26], [75781, 49], [75855, 45], [75984, 29], [76081, 49], [76155, 45], [76284, 30], [76381, 49], [76454, 46], [76584, 33], [76680, 51], [76754, 46], [76884, 36], [76980, 52], [77052, 48], [77184, 36], [77280, 53], [77353, 47], [77484, 36], [77580, 53], [77653, 47], [77784, 37], [77879, 55], [77953, 47], [78084, 37], [78179, 55], [78253, 47], [78384, 37], [78479, 55], [78554, 46], [78683, 39], [78778, 56], [78854, 46], [78983, 39], [79078, 56], [79154, 46], [79283, 39], [79378, 56], [79454, 46], [79583, 40], [79677, 57], [79754, 46], [79883, 40], [79977, 58], [80055, 45], [80183, 40], [80277, 58], [80355, 45], [80483, 41], [80576, 59], [80655, 45], [80783, 41], [80876, 59], [80955, 45], [81082, 42], [81176, 59], [81255, 45], [81382, 43], [81475, 60], [81556, 44], [81682, 43], [81775, 60], [81856, 44], [81982, 43], [82075, 60], [82156, 44], [82282, 44], [82374, 61], [82456, 44], [82582, 45], [82673, 62], [82757, 43], [82882, 45], [82973, 62], [83057, 43], [83182, 46], [83272, 63], [83357, 43], [83482, 30], [83520, 9], [83571, 64], [83658, 42], [83781, 20], [83870, 65], [83958, 42], [84081, 20], [84169, 67], [84258, 42], [84381, 20], [84468, 68], [84559, 41], [84681, 20], [84767, 69], [84859, 41], [84981, 20], [85066, 70], [85159, 41], [85281, 20], [85334, 2], [85364, 72], [85460, 40], [85581, 20], [85634, 4], [85662, 74], [85760, 40], [85881, 20], [85934, 6], [85960, 76], [86060, 40], [86180, 21], [86233, 9], [86258, 78], [86361, 39], [86480, 21], [86533, 16], [86551, 85], [86661, 39], [86780, 21], [86833, 103], [86961, 39], [87080, 22], [87133, 103], [87261, 39], [87380, 22], [87432, 104], [87561, 39], [87680, 22], [87732, 104], [87862, 38], [87980, 22], [88032, 104], [88162, 38], [88280, 22], [88332, 105], [88462, 38], [88580, 22], [88632, 105], [88762, 38], [88879, 23], [88931, 106], [89062, 38], [89179, 23], [89231, 106], [89362, 38], [89479, 24], [89531, 106], [89663, 37], [89779, 24], [89831, 106], [89963, 37]], "point": [189, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-02.45|+01.95|+02.93"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [79, 1, 299, 300], "mask": [[284, 16], [584, 16], [884, 16], [1183, 17], [1483, 17], [1782, 18], [2082, 18], [2381, 19], [2681, 19], [2980, 20], [3280, 20], [3579, 21], [3879, 21], [4179, 21], [4478, 22], [4778, 22], [5077, 23], [5377, 23], [5676, 24], [5976, 24], [6275, 25], [6575, 25], [6875, 25], [7174, 26], [7474, 26], [7773, 27], [8073, 27], [8372, 28], [8672, 28], [8971, 29], [9271, 29], [9570, 30], [9870, 30], [10170, 30], [10469, 31], [10769, 31], [11068, 32], [11368, 32], [11667, 33], [11967, 33], [12266, 34], [12566, 34], [12866, 34], [13165, 35], [13465, 35], [13764, 36], [14064, 36], [14363, 37], [14663, 37], [14962, 38], [15262, 38], [15562, 38], [15861, 39], [16161, 39], [16460, 40], [16760, 40], [17059, 41], [17359, 41], [17658, 42], [17958, 42], [18257, 43], [18557, 43], [18858, 42], [19007, 193], [19307, 193], [19607, 193], [19907, 193], [20206, 194], [20506, 194], [20806, 194], [21106, 194], [21406, 194], [21706, 194], [22006, 194], [22306, 194], [22606, 194], [22905, 195], [23205, 195], [23505, 195], [23805, 195], [24105, 195], [24405, 195], [24705, 195], [25005, 195], [25304, 196], [25604, 196], [25904, 196], [26204, 196], [26504, 196], [26804, 196], [27104, 196], [27404, 196], [27704, 196], [28003, 197], [28303, 197], [28603, 197], [28903, 197], [29203, 197], [29503, 197], [29803, 197], [30103, 197], [30402, 198], [30702, 198], [31002, 198], [31302, 198], [31602, 198], [31902, 198], [32202, 198], [32502, 198], [32802, 198], [33101, 199], [33401, 199], [33701, 199], [34001, 199], [34301, 199], [34601, 199], [34901, 199], [35201, 199], [35500, 200], [35800, 200], [36100, 200], [36400, 200], [36700, 200], [37000, 200], [37300, 200], [37600, 200], [37900, 200], [38199, 201], [38499, 201], [38799, 201], [39099, 201], [39399, 201], [39699, 201], [39999, 201], [40299, 201], [40598, 202], [40898, 202], [41198, 202], [41498, 202], [41798, 202], [42098, 202], [42398, 202], [42698, 202], [42997, 203], [43297, 203], [43597, 203], [43897, 203], [44197, 203], [44497, 203], [44797, 203], [45097, 203], [45397, 203], [45696, 204], [45996, 204], [46296, 204], [46596, 204], [46896, 204], [47196, 204], [47496, 204], [47796, 204], [48095, 205], [48395, 205], [48695, 205], [48995, 205], [49295, 205], [49595, 205], [49895, 205], [50195, 205], [50495, 205], [50794, 206], [51094, 206], [51394, 206], [51694, 206], [51994, 206], [52294, 206], [52594, 206], [52894, 206], [53193, 207], [53493, 207], [53793, 207], [54093, 207], [54393, 207], [54693, 207], [54993, 207], [55293, 207], [55593, 207], [55892, 208], [56192, 208], [56492, 208], [56792, 208], [57092, 208], [57392, 208], [57692, 208], [57992, 208], [58291, 209], [58591, 209], [58891, 209], [59191, 209], [59491, 209], [59791, 209], [60091, 209], [60391, 209], [60691, 209], [60990, 210], [61290, 210], [61590, 210], [61890, 210], [62190, 210], [62490, 210], [62790, 210], [63090, 210], [63389, 211], [63689, 211], [63989, 211], [64289, 211], [64589, 211], [64889, 211], [65189, 211], [65489, 211], [65788, 212], [66088, 212], [66388, 212], [66688, 212], [66988, 212], [67288, 212], [67588, 212], [67888, 212], [68188, 212], [68487, 213], [68787, 213], [69087, 213], [69387, 213], [69687, 213], [69987, 213], [70287, 213], [70587, 213], [70886, 214], [71186, 214], [71486, 214], [71786, 214], [72086, 214], [72386, 214], [72686, 153], [72843, 57], [72986, 152], [73143, 57], [73286, 152], [73446, 54], [73585, 149], [73749, 51], [73885, 148], [74051, 49], [74185, 146], [74352, 48], [74485, 146], [74653, 47], [74785, 145], [74954, 46], [75085, 145], [75254, 46], [75385, 145], [75555, 45], [75685, 145], [75855, 45], [75984, 146], [76155, 45], [76284, 146], [76454, 46], [76584, 147], [76754, 46], [76884, 148], [77052, 48], [77184, 149], [77353, 47], [77484, 149], [77653, 47], [77784, 150], [77953, 47], [78084, 150], [78253, 47], [78384, 150], [78554, 46], [78683, 151], [78854, 46], [78983, 151], [79154, 46], [79283, 151], [79454, 46], [79583, 151], [79754, 46], [79883, 152], [80055, 45], [80183, 152], [80355, 45], [80483, 152], [80655, 45], [80783, 152], [80955, 45], [81082, 153], [81255, 45], [81382, 153], [81556, 44], [81682, 153], [81856, 44], [81982, 75], [82082, 53], [82156, 44], [82282, 70], [82385, 50], [82456, 44], [82582, 70], [82685, 50], [82757, 43], [82882, 70], [82985, 50], [83057, 43], [83182, 70], [83285, 50], [83357, 43], [83482, 30], [83520, 32], [83585, 50], [83658, 42], [83781, 20], [83834, 18], [83885, 50], [83958, 42], [84081, 20], [84135, 17], [84185, 51], [84258, 42], [84381, 20], [84435, 17], [84485, 51], [84559, 41], [84681, 20], [84735, 17], [84785, 51], [84859, 41], [84981, 20], [85034, 18], [85085, 51], [85159, 41], [85281, 20], [85334, 18], [85385, 51], [85460, 40], [85581, 20], [85634, 18], [85685, 51], [85760, 40], [85881, 20], [85934, 19], [85985, 51], [86060, 40], [86180, 21], [86233, 20], [86285, 51], [86361, 39], [86480, 21], [86533, 20], [86585, 51], [86661, 39], [86780, 21], [86833, 20], [86885, 51], [86961, 39], [87080, 22], [87133, 20], [87186, 50], [87261, 39], [87380, 22], [87432, 21], [87486, 50], [87561, 39], [87680, 22], [87732, 21], [87786, 50], [87862, 38], [87980, 22], [88032, 21], [88086, 50], [88162, 38], [88280, 22], [88332, 21], [88386, 51], [88462, 38], [88580, 22], [88632, 21], [88686, 51], [88762, 38], [88879, 23], [88931, 22], [88986, 51], [89062, 38], [89179, 23], [89231, 22], [89286, 51], [89362, 38], [89479, 24], [89531, 22], [89586, 51], [89663, 37], [89779, 24], [89831, 22], [89886, 51], [89963, 37]], "point": [189, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan6", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -1.5, "y": 0.9009992, "z": 0.5}, "object_poses": [{"objectName": "Potato_b563bb4b", "position": {"x": -2.63572145, "y": 1.051786, "z": 2.27873731}, "rotation": {"x": 0.0, "y": 89.99982, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": -2.2879, "y": 0.773659945, "z": 0.07037759}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": -2.538684, "y": 0.9109421, "z": 0.03161955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_0884e440", "position": {"x": 1.4114, "y": 0.9295, "z": 0.0642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": -0.6010624, "y": 0.9120294, "z": 1.54067779}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": -2.289484, "y": 0.9120294, "z": 0.331450462}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": -2.538684, "y": 0.925899863, "z": 1.53077412}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": 1.57097375, "y": 0.9258998, "z": -0.868744552}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": 1.512661, "y": 0.9089127, "z": 0.435648441}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": -2.485795, "y": 1.29822564, "z": -0.6021706}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": 1.41873252, "y": 0.94397974, "z": -1.03414083}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": -2.538684, "y": 0.9362496, "z": 1.2309432}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": -2.57219648, "y": 1.93575406, "z": -1.15002513}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": -2.31330013, "y": 0.116943181, "z": 1.26794994}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": 0.5169825, "y": 1.6609441, "z": -1.72989917}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": -0.341296852, "y": 0.9059, "z": 0.6473222}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": 0.0483515263, "y": 0.9059, "z": 1.76401663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -0.341296852, "y": 0.994053841, "z": 0.870661139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -0.341296852, "y": 0.994053841, "z": 1.76401663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": -2.53202271, "y": 1.15082419, "z": -0.5077681}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_adb25946", "position": {"x": 0.0, "y": 0.9, "z": 0.948}, "rotation": {"x": 0.0, "y": 29.9999466, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": -2.196683, "y": 0.7987526, "z": 1.32221115}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": -2.466795, "y": 0.6563896, "z": -1.05196643}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.538684, "y": 0.9080944, "z": 0.9311123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": 1.34268308, "y": 0.9114485, "z": -0.6846062}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_209e2ea9", "position": {"x": -2.289484, "y": 0.996719658, "z": 0.6312814}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_7b0d90af", "position": {"x": 1.7355001, "y": 1.66621935, "z": -0.407017648}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": 1.47762811, "y": 0.9522195, "z": -1.25529552}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "Kettle_0884e440", "position": {"x": 1.6916, "y": 0.9295, "z": 0.0642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_b563bb4b", "position": {"x": -2.466795, "y": 0.6311115, "z": -0.6036891}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": -2.564922, "y": 1.53261185, "z": 2.76811242}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -0.211414054, "y": 0.994053841, "z": 1.54067779}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": -2.605, "y": 1.53542459, "z": 2.40483141}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": 1.68793631, "y": 0.187196583, "z": 0.679}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": 1.2564733, "y": 0.9346558, "z": -1.19639921}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "PaperTowelRoll_40552007", "position": {"x": 1.56212735, "y": 0.289516717, "z": 0.7340623}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": -2.289484, "y": 0.9109421, "z": 0.03161955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": -2.38490152, "y": 0.109977961, "z": 1.52732491}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": -2.2879, "y": 0.7729672, "z": -0.121177867}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_bc458cba", "position": {"x": 1.4123, "y": 0.9295, "z": -0.3359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": -2.289484, "y": 0.9120294, "z": 0.03161955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9b863229", "position": {"x": -0.7309452, "y": 0.9532293, "z": 1.76401663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_db7f1139", "position": {"x": 1.77523732, "y": 0.905899942, "z": -0.89784646}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 581999852, "scene_num": 6}, "task_id": "trial_T20190908_034437_632398", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1P0XSCJ9XAV74_3CFJTT4SXW7E899CDUG24A788GMI7C", "high_descs": ["Turn right and walk a few steps in front of the fridge. Turn left and walk towards the counter.", "Walk closer to the counter and take the mug from the counter.", "Turn around take a step and go to the right. Walk straight ahead to the wall. Turn left and go towards the microwave.", "Open the microwave and place the mug on the right side and shut the door. After a few seconds take the mug back out.", "Raise the mug up to the cabinet above the microwave.", "Open the cabinet and place the mug inside."], "task_desc": "To take a mug from the microwave to put it away in the cabinet.", "votes": [1, 1]}, {"assignment_id": "A1K5ILJMG439M5_3J2UYBXQQOTKXEERQUM4SS7GRC106I", "high_descs": ["Go around the kitchen island to face the counter between the stove and the sink.", "Pick up the coffee mug from behind the spatula.", "Bring the coffee mug across the room to the microwave.", "Open the microwave and put the coffee mug inside, heat it up, then take it out again.", "Look up at the cabinet directly above the microwave.", "Open the cabinet, put the coffee mug to the left of the salt shaker, and then close the cabinet."], "task_desc": "Put a warm mug away.", "votes": [1, 1]}, {"assignment_id": "AD0NVUGLDYDYN_3OVHNO1VE9IGK8ZW4B7A4XKUQ4MDZS", "high_descs": ["turn right,  turn left, walk to the sink", "grab the cup by the stove", "turn around, walk around the island to the microwave", "put the cup in the microwave, heat the cup, take the cup out", "look up to the cabinet above", "open the cabinet, put the cup in"], "task_desc": "heat the cup in the microwave, put the cup in the cabinet above", "votes": [1, 1]}]}}