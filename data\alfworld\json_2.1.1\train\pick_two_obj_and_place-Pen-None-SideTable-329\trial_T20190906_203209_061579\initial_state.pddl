
(define (problem plan_trial_T20190906_203209_061579)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__minus_01_dot_24_bar__plus_00_dot_68_bar__minus_02_dot_43 - object
        BaseballBat_bar__plus_00_dot_42_bar__plus_00_dot_67_bar__plus_01_dot_04 - object
        Blinds_bar__minus_01_dot_46_bar__plus_02_dot_16_bar__minus_00_dot_05 - object
        Blinds_bar__minus_01_dot_46_bar__plus_02_dot_16_bar__minus_01_dot_34 - object
        Book_bar__minus_00_dot_59_bar__plus_00_dot_62_bar__plus_00_dot_83 - object
        Box_bar__plus_02_dot_45_bar__plus_00_dot_22_bar__minus_00_dot_04 - object
        CD_bar__plus_00_dot_95_bar__plus_00_dot_37_bar__minus_02_dot_38 - object
        CD_bar__minus_01_dot_09_bar__plus_00_dot_68_bar__minus_02_dot_55 - object
        CellPhone_bar__minus_00_dot_48_bar__plus_00_dot_56_bar__minus_01_dot_66 - object
        CellPhone_bar__minus_00_dot_74_bar__plus_00_dot_62_bar__plus_00_dot_76 - object
        CellPhone_bar__minus_01_dot_20_bar__plus_00_dot_67_bar__minus_02_dot_55 - object
        CreditCard_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_83 - object
        CreditCard_bar__minus_00_dot_74_bar__plus_00_dot_62_bar__plus_00_dot_83 - object
        KeyChain_bar__plus_01_dot_13_bar__plus_00_dot_38_bar__minus_02_dot_42 - object
        KeyChain_bar__plus_01_dot_17_bar__plus_00_dot_68_bar__minus_02_dot_42 - object
        KeyChain_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_68 - object
        Lamp_bar__plus_00_dot_92_bar__plus_00_dot_67_bar__minus_02_dot_46 - object
        Laptop_bar__minus_00_dot_12_bar__plus_00_dot_55_bar__minus_01_dot_21 - object
        Laptop_bar__minus_00_dot_30_bar__plus_00_dot_55_bar__minus_01_dot_88 - object
        LightSwitch_bar__plus_00_dot_33_bar__plus_01_dot_26_bar__plus_01_dot_15 - object
        Mirror_bar__plus_02_dot_64_bar__plus_01_dot_35_bar__plus_00_dot_39 - object
        Mug_bar__plus_00_dot_19_bar__plus_00_dot_62_bar__plus_00_dot_99 - object
        Painting_bar__plus_00_dot_00_bar__plus_01_dot_56_bar__minus_02_dot_69 - object
        Pencil_bar__minus_00_dot_28_bar__plus_00_dot_62_bar__plus_00_dot_68 - object
        Pencil_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_99 - object
        Pencil_bar__minus_00_dot_59_bar__plus_00_dot_62_bar__plus_00_dot_60 - object
        Pen_bar__plus_00_dot_03_bar__plus_00_dot_62_bar__plus_00_dot_91 - object
        Pen_bar__minus_00_dot_28_bar__plus_00_dot_63_bar__plus_00_dot_83 - object
        Pillow_bar__plus_00_dot_19_bar__plus_00_dot_61_bar__minus_02_dot_31 - object
        Window_bar__minus_01_dot_56_bar__plus_01_dot_42_bar__minus_00_dot_06 - object
        Window_bar__minus_01_dot_56_bar__plus_01_dot_42_bar__minus_01_dot_36 - object
        Bed_bar__minus_00_dot_12_bar__plus_00_dot_00_bar__minus_01_dot_70 - receptacle
        DiningTable_bar__minus_00_dot_28_bar__minus_00_dot_01_bar__plus_00_dot_83 - receptacle
        Drawer_bar__plus_01_dot_00_bar__plus_00_dot_17_bar__minus_02_dot_39 - receptacle
        Drawer_bar__plus_01_dot_00_bar__plus_00_dot_46_bar__minus_02_dot_39 - receptacle
        Drawer_bar__minus_01_dot_20_bar__plus_00_dot_17_bar__minus_02_dot_39 - receptacle
        Drawer_bar__minus_01_dot_20_bar__plus_00_dot_46_bar__minus_02_dot_39 - receptacle
        GarbageCan_bar__plus_02_dot_47_bar__plus_00_dot_00_bar__plus_00_dot_97 - receptacle
        SideTable_bar__plus_01_dot_00_bar__plus_00_dot_01_bar__minus_02_dot_47 - receptacle
        SideTable_bar__minus_01_dot_20_bar__plus_00_dot_01_bar__minus_02_dot_47 - receptacle
        loc_bar_8_bar__minus_8_bar_3_bar_60 - location
        loc_bar__minus_1_bar__minus_2_bar_2_bar_60 - location
        loc_bar__minus_2_bar__minus_2_bar_0_bar_45 - location
        loc_bar_8_bar_0_bar_1_bar_60 - location
        loc_bar__minus_4_bar__minus_2_bar_2_bar_15 - location
        loc_bar__minus_4_bar_0_bar_3_bar__minus_30 - location
        loc_bar__minus_4_bar_0_bar_3_bar_30 - location
        loc_bar_5_bar__minus_5_bar_2_bar_45 - location
        loc_bar_2_bar_1_bar_0_bar_45 - location
        loc_bar_4_bar__minus_8_bar_3_bar_0 - location
        loc_bar_5_bar__minus_6_bar_2_bar_60 - location
        loc_bar_8_bar_2_bar_0_bar_60 - location
        loc_bar__minus_4_bar__minus_2_bar_2_bar_45 - location
        loc_bar__minus_4_bar__minus_2_bar_2_bar__minus_30 - location
        loc_bar_8_bar_2_bar_1_bar_30 - location
        loc_bar__minus_4_bar__minus_2_bar_2_bar_30 - location
        loc_bar_1_bar_1_bar_0_bar_30 - location
        loc_bar_9_bar__minus_8_bar_0_bar_30 - location
        )
    

(:init


        (receptacleType SideTable_bar__plus_01_dot_00_bar__plus_00_dot_01_bar__minus_02_dot_47 SideTableType)
        (receptacleType Bed_bar__minus_00_dot_12_bar__plus_00_dot_00_bar__minus_01_dot_70 BedType)
        (receptacleType Drawer_bar__plus_01_dot_00_bar__plus_00_dot_17_bar__minus_02_dot_39 DrawerType)
        (receptacleType SideTable_bar__minus_01_dot_20_bar__plus_00_dot_01_bar__minus_02_dot_47 SideTableType)
        (receptacleType Drawer_bar__minus_01_dot_20_bar__plus_00_dot_46_bar__minus_02_dot_39 DrawerType)
        (receptacleType DiningTable_bar__minus_00_dot_28_bar__minus_00_dot_01_bar__plus_00_dot_83 DiningTableType)
        (receptacleType GarbageCan_bar__plus_02_dot_47_bar__plus_00_dot_00_bar__plus_00_dot_97 GarbageCanType)
        (receptacleType Drawer_bar__minus_01_dot_20_bar__plus_00_dot_17_bar__minus_02_dot_39 DrawerType)
        (receptacleType Drawer_bar__plus_01_dot_00_bar__plus_00_dot_46_bar__minus_02_dot_39 DrawerType)
        (objectType Book_bar__minus_00_dot_59_bar__plus_00_dot_62_bar__plus_00_dot_83 BookType)
        (objectType Painting_bar__plus_00_dot_00_bar__plus_01_dot_56_bar__minus_02_dot_69 PaintingType)
        (objectType KeyChain_bar__plus_01_dot_13_bar__plus_00_dot_38_bar__minus_02_dot_42 KeyChainType)
        (objectType Pillow_bar__plus_00_dot_19_bar__plus_00_dot_61_bar__minus_02_dot_31 PillowType)
        (objectType KeyChain_bar__plus_01_dot_17_bar__plus_00_dot_68_bar__minus_02_dot_42 KeyChainType)
        (objectType LightSwitch_bar__plus_00_dot_33_bar__plus_01_dot_26_bar__plus_01_dot_15 LightSwitchType)
        (objectType Mirror_bar__plus_02_dot_64_bar__plus_01_dot_35_bar__plus_00_dot_39 MirrorType)
        (objectType CD_bar__plus_00_dot_95_bar__plus_00_dot_37_bar__minus_02_dot_38 CDType)
        (objectType Pen_bar__plus_00_dot_03_bar__plus_00_dot_62_bar__plus_00_dot_91 PenType)
        (objectType Laptop_bar__minus_00_dot_12_bar__plus_00_dot_55_bar__minus_01_dot_21 LaptopType)
        (objectType CellPhone_bar__minus_00_dot_74_bar__plus_00_dot_62_bar__plus_00_dot_76 CellPhoneType)
        (objectType Blinds_bar__minus_01_dot_46_bar__plus_02_dot_16_bar__minus_01_dot_34 BlindsType)
        (objectType Pencil_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_99 PencilType)
        (objectType Mug_bar__plus_00_dot_19_bar__plus_00_dot_62_bar__plus_00_dot_99 MugType)
        (objectType Window_bar__minus_01_dot_56_bar__plus_01_dot_42_bar__minus_00_dot_06 WindowType)
        (objectType CreditCard_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_83 CreditCardType)
        (objectType Pencil_bar__minus_00_dot_59_bar__plus_00_dot_62_bar__plus_00_dot_60 PencilType)
        (objectType CellPhone_bar__minus_00_dot_48_bar__plus_00_dot_56_bar__minus_01_dot_66 CellPhoneType)
        (objectType KeyChain_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_68 KeyChainType)
        (objectType AlarmClock_bar__minus_01_dot_24_bar__plus_00_dot_68_bar__minus_02_dot_43 AlarmClockType)
        (objectType Window_bar__minus_01_dot_56_bar__plus_01_dot_42_bar__minus_01_dot_36 WindowType)
        (objectType CellPhone_bar__minus_01_dot_20_bar__plus_00_dot_67_bar__minus_02_dot_55 CellPhoneType)
        (objectType CD_bar__minus_01_dot_09_bar__plus_00_dot_68_bar__minus_02_dot_55 CDType)
        (objectType Laptop_bar__minus_00_dot_30_bar__plus_00_dot_55_bar__minus_01_dot_88 LaptopType)
        (objectType Blinds_bar__minus_01_dot_46_bar__plus_02_dot_16_bar__minus_00_dot_05 BlindsType)
        (objectType Pen_bar__minus_00_dot_28_bar__plus_00_dot_63_bar__plus_00_dot_83 PenType)
        (objectType BaseballBat_bar__plus_00_dot_42_bar__plus_00_dot_67_bar__plus_01_dot_04 BaseballBatType)
        (objectType Box_bar__plus_02_dot_45_bar__plus_00_dot_22_bar__minus_00_dot_04 BoxType)
        (objectType CreditCard_bar__minus_00_dot_74_bar__plus_00_dot_62_bar__plus_00_dot_83 CreditCardType)
        (objectType Pencil_bar__minus_00_dot_28_bar__plus_00_dot_62_bar__plus_00_dot_68 PencilType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType CDType)
        (canContain SideTableType MugType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType BaseballBatType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType AlarmClockType)
        (canContain BedType BaseballBatType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType CDType)
        (canContain SideTableType MugType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType BaseballBatType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DiningTableType PenType)
        (canContain DiningTableType BookType)
        (canContain DiningTableType CDType)
        (canContain DiningTableType MugType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType CellPhoneType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType BaseballBatType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType PencilType)
        (canContain DiningTableType AlarmClockType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (pickupable Book_bar__minus_00_dot_59_bar__plus_00_dot_62_bar__plus_00_dot_83)
        (pickupable KeyChain_bar__plus_01_dot_13_bar__plus_00_dot_38_bar__minus_02_dot_42)
        (pickupable Pillow_bar__plus_00_dot_19_bar__plus_00_dot_61_bar__minus_02_dot_31)
        (pickupable KeyChain_bar__plus_01_dot_17_bar__plus_00_dot_68_bar__minus_02_dot_42)
        (pickupable CD_bar__plus_00_dot_95_bar__plus_00_dot_37_bar__minus_02_dot_38)
        (pickupable Pen_bar__plus_00_dot_03_bar__plus_00_dot_62_bar__plus_00_dot_91)
        (pickupable Laptop_bar__minus_00_dot_12_bar__plus_00_dot_55_bar__minus_01_dot_21)
        (pickupable CellPhone_bar__minus_00_dot_74_bar__plus_00_dot_62_bar__plus_00_dot_76)
        (pickupable Pencil_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_99)
        (pickupable Mug_bar__plus_00_dot_19_bar__plus_00_dot_62_bar__plus_00_dot_99)
        (pickupable CreditCard_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_83)
        (pickupable Pencil_bar__minus_00_dot_59_bar__plus_00_dot_62_bar__plus_00_dot_60)
        (pickupable CellPhone_bar__minus_00_dot_48_bar__plus_00_dot_56_bar__minus_01_dot_66)
        (pickupable KeyChain_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_68)
        (pickupable AlarmClock_bar__minus_01_dot_24_bar__plus_00_dot_68_bar__minus_02_dot_43)
        (pickupable CellPhone_bar__minus_01_dot_20_bar__plus_00_dot_67_bar__minus_02_dot_55)
        (pickupable CD_bar__minus_01_dot_09_bar__plus_00_dot_68_bar__minus_02_dot_55)
        (pickupable Laptop_bar__minus_00_dot_30_bar__plus_00_dot_55_bar__minus_01_dot_88)
        (pickupable Pen_bar__minus_00_dot_28_bar__plus_00_dot_63_bar__plus_00_dot_83)
        (pickupable BaseballBat_bar__plus_00_dot_42_bar__plus_00_dot_67_bar__plus_01_dot_04)
        (pickupable Box_bar__plus_02_dot_45_bar__plus_00_dot_22_bar__minus_00_dot_04)
        (pickupable CreditCard_bar__minus_00_dot_74_bar__plus_00_dot_62_bar__plus_00_dot_83)
        (pickupable Pencil_bar__minus_00_dot_28_bar__plus_00_dot_62_bar__plus_00_dot_68)
        (isReceptacleObject Mug_bar__plus_00_dot_19_bar__plus_00_dot_62_bar__plus_00_dot_99)
        (isReceptacleObject Box_bar__plus_02_dot_45_bar__plus_00_dot_22_bar__minus_00_dot_04)
        (openable Drawer_bar__plus_01_dot_00_bar__plus_00_dot_17_bar__minus_02_dot_39)
        (openable Drawer_bar__plus_01_dot_00_bar__plus_00_dot_46_bar__minus_02_dot_39)
        
        (atLocation agent1 loc_bar_9_bar__minus_8_bar_0_bar_30)
        
        (cleanable Mug_bar__plus_00_dot_19_bar__plus_00_dot_62_bar__plus_00_dot_99)
        
        (heatable Mug_bar__plus_00_dot_19_bar__plus_00_dot_62_bar__plus_00_dot_99)
        (coolable Mug_bar__plus_00_dot_19_bar__plus_00_dot_62_bar__plus_00_dot_99)
        
        
        
        
        
        
        
        (inReceptacle Laptop_bar__minus_00_dot_30_bar__plus_00_dot_55_bar__minus_01_dot_88 Bed_bar__minus_00_dot_12_bar__plus_00_dot_00_bar__minus_01_dot_70)
        (inReceptacle Laptop_bar__minus_00_dot_12_bar__plus_00_dot_55_bar__minus_01_dot_21 Bed_bar__minus_00_dot_12_bar__plus_00_dot_00_bar__minus_01_dot_70)
        (inReceptacle Pillow_bar__plus_00_dot_19_bar__plus_00_dot_61_bar__minus_02_dot_31 Bed_bar__minus_00_dot_12_bar__plus_00_dot_00_bar__minus_01_dot_70)
        (inReceptacle CellPhone_bar__minus_00_dot_48_bar__plus_00_dot_56_bar__minus_01_dot_66 Bed_bar__minus_00_dot_12_bar__plus_00_dot_00_bar__minus_01_dot_70)
        (inReceptacle CreditCard_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_83 DiningTable_bar__minus_00_dot_28_bar__minus_00_dot_01_bar__plus_00_dot_83)
        (inReceptacle Book_bar__minus_00_dot_59_bar__plus_00_dot_62_bar__plus_00_dot_83 DiningTable_bar__minus_00_dot_28_bar__minus_00_dot_01_bar__plus_00_dot_83)
        (inReceptacle Pencil_bar__minus_00_dot_59_bar__plus_00_dot_62_bar__plus_00_dot_60 DiningTable_bar__minus_00_dot_28_bar__minus_00_dot_01_bar__plus_00_dot_83)
        (inReceptacle Pen_bar__minus_00_dot_28_bar__plus_00_dot_63_bar__plus_00_dot_83 DiningTable_bar__minus_00_dot_28_bar__minus_00_dot_01_bar__plus_00_dot_83)
        (inReceptacle CellPhone_bar__minus_00_dot_74_bar__plus_00_dot_62_bar__plus_00_dot_76 DiningTable_bar__minus_00_dot_28_bar__minus_00_dot_01_bar__plus_00_dot_83)
        (inReceptacle CreditCard_bar__minus_00_dot_74_bar__plus_00_dot_62_bar__plus_00_dot_83 DiningTable_bar__minus_00_dot_28_bar__minus_00_dot_01_bar__plus_00_dot_83)
        (inReceptacle Pencil_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_99 DiningTable_bar__minus_00_dot_28_bar__minus_00_dot_01_bar__plus_00_dot_83)
        (inReceptacle Mug_bar__plus_00_dot_19_bar__plus_00_dot_62_bar__plus_00_dot_99 DiningTable_bar__minus_00_dot_28_bar__minus_00_dot_01_bar__plus_00_dot_83)
        (inReceptacle Pencil_bar__minus_00_dot_28_bar__plus_00_dot_62_bar__plus_00_dot_68 DiningTable_bar__minus_00_dot_28_bar__minus_00_dot_01_bar__plus_00_dot_83)
        (inReceptacle KeyChain_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_68 DiningTable_bar__minus_00_dot_28_bar__minus_00_dot_01_bar__plus_00_dot_83)
        (inReceptacle Pen_bar__plus_00_dot_03_bar__plus_00_dot_62_bar__plus_00_dot_91 DiningTable_bar__minus_00_dot_28_bar__minus_00_dot_01_bar__plus_00_dot_83)
        (inReceptacle CD_bar__plus_00_dot_95_bar__plus_00_dot_37_bar__minus_02_dot_38 Drawer_bar__plus_01_dot_00_bar__plus_00_dot_46_bar__minus_02_dot_39)
        (inReceptacle KeyChain_bar__plus_01_dot_13_bar__plus_00_dot_38_bar__minus_02_dot_42 Drawer_bar__plus_01_dot_00_bar__plus_00_dot_46_bar__minus_02_dot_39)
        (inReceptacle CellPhone_bar__minus_01_dot_20_bar__plus_00_dot_67_bar__minus_02_dot_55 SideTable_bar__minus_01_dot_20_bar__plus_00_dot_01_bar__minus_02_dot_47)
        (inReceptacle CD_bar__minus_01_dot_09_bar__plus_00_dot_68_bar__minus_02_dot_55 SideTable_bar__minus_01_dot_20_bar__plus_00_dot_01_bar__minus_02_dot_47)
        (inReceptacle KeyChain_bar__plus_01_dot_17_bar__plus_00_dot_68_bar__minus_02_dot_42 SideTable_bar__plus_01_dot_00_bar__plus_00_dot_01_bar__minus_02_dot_47)
        
        
        (receptacleAtLocation Bed_bar__minus_00_dot_12_bar__plus_00_dot_00_bar__minus_01_dot_70 loc_bar__minus_1_bar__minus_2_bar_2_bar_60)
        (receptacleAtLocation DiningTable_bar__minus_00_dot_28_bar__minus_00_dot_01_bar__plus_00_dot_83 loc_bar__minus_2_bar__minus_2_bar_0_bar_45)
        (receptacleAtLocation Drawer_bar__plus_01_dot_00_bar__plus_00_dot_17_bar__minus_02_dot_39 loc_bar_5_bar__minus_5_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__plus_01_dot_00_bar__plus_00_dot_46_bar__minus_02_dot_39 loc_bar_5_bar__minus_6_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_01_dot_20_bar__plus_00_dot_17_bar__minus_02_dot_39 loc_bar__minus_4_bar__minus_2_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__minus_01_dot_20_bar__plus_00_dot_46_bar__minus_02_dot_39 loc_bar__minus_4_bar__minus_2_bar_2_bar_30)
        (receptacleAtLocation GarbageCan_bar__plus_02_dot_47_bar__plus_00_dot_00_bar__plus_00_dot_97 loc_bar_8_bar_2_bar_0_bar_60)
        (receptacleAtLocation SideTable_bar__plus_01_dot_00_bar__plus_00_dot_01_bar__minus_02_dot_47 loc_bar_8_bar__minus_8_bar_3_bar_60)
        (receptacleAtLocation SideTable_bar__minus_01_dot_20_bar__plus_00_dot_01_bar__minus_02_dot_47 loc_bar__minus_4_bar__minus_2_bar_2_bar_45)
        (objectAtLocation CD_bar__minus_01_dot_09_bar__plus_00_dot_68_bar__minus_02_dot_55 loc_bar__minus_4_bar__minus_2_bar_2_bar_45)
        (objectAtLocation Laptop_bar__minus_00_dot_12_bar__plus_00_dot_55_bar__minus_01_dot_21 loc_bar__minus_1_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Pen_bar__plus_00_dot_03_bar__plus_00_dot_62_bar__plus_00_dot_91 loc_bar__minus_2_bar__minus_2_bar_0_bar_45)
        (objectAtLocation CreditCard_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_83 loc_bar__minus_2_bar__minus_2_bar_0_bar_45)
        (objectAtLocation CellPhone_bar__minus_00_dot_74_bar__plus_00_dot_62_bar__plus_00_dot_76 loc_bar__minus_2_bar__minus_2_bar_0_bar_45)
        (objectAtLocation Pencil_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_99 loc_bar__minus_2_bar__minus_2_bar_0_bar_45)
        (objectAtLocation KeyChain_bar__plus_01_dot_17_bar__plus_00_dot_68_bar__minus_02_dot_42 loc_bar_8_bar__minus_8_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__minus_00_dot_43_bar__plus_00_dot_62_bar__plus_00_dot_68 loc_bar__minus_2_bar__minus_2_bar_0_bar_45)
        (objectAtLocation Pencil_bar__minus_00_dot_59_bar__plus_00_dot_62_bar__plus_00_dot_60 loc_bar__minus_2_bar__minus_2_bar_0_bar_45)
        (objectAtLocation CellPhone_bar__minus_01_dot_20_bar__plus_00_dot_67_bar__minus_02_dot_55 loc_bar__minus_4_bar__minus_2_bar_2_bar_45)
        (objectAtLocation Mirror_bar__plus_02_dot_64_bar__plus_01_dot_35_bar__plus_00_dot_39 loc_bar_8_bar_2_bar_1_bar_30)
        (objectAtLocation Box_bar__plus_02_dot_45_bar__plus_00_dot_22_bar__minus_00_dot_04 loc_bar_8_bar_0_bar_1_bar_60)
        (objectAtLocation Book_bar__minus_00_dot_59_bar__plus_00_dot_62_bar__plus_00_dot_83 loc_bar__minus_2_bar__minus_2_bar_0_bar_45)
        (objectAtLocation KeyChain_bar__plus_01_dot_13_bar__plus_00_dot_38_bar__minus_02_dot_42 loc_bar_5_bar__minus_6_bar_2_bar_60)
        (objectAtLocation Pillow_bar__plus_00_dot_19_bar__plus_00_dot_61_bar__minus_02_dot_31 loc_bar__minus_1_bar__minus_2_bar_2_bar_60)
        (objectAtLocation BaseballBat_bar__plus_00_dot_42_bar__plus_00_dot_67_bar__plus_01_dot_04 loc_bar_2_bar_1_bar_0_bar_45)
        (objectAtLocation Pencil_bar__minus_00_dot_28_bar__plus_00_dot_62_bar__plus_00_dot_68 loc_bar__minus_2_bar__minus_2_bar_0_bar_45)
        (objectAtLocation CellPhone_bar__minus_00_dot_48_bar__plus_00_dot_56_bar__minus_01_dot_66 loc_bar__minus_1_bar__minus_2_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__minus_00_dot_74_bar__plus_00_dot_62_bar__plus_00_dot_83 loc_bar__minus_2_bar__minus_2_bar_0_bar_45)
        (objectAtLocation LightSwitch_bar__plus_00_dot_33_bar__plus_01_dot_26_bar__plus_01_dot_15 loc_bar_1_bar_1_bar_0_bar_30)
        (objectAtLocation Pen_bar__minus_00_dot_28_bar__plus_00_dot_63_bar__plus_00_dot_83 loc_bar__minus_2_bar__minus_2_bar_0_bar_45)
        (objectAtLocation Laptop_bar__minus_00_dot_30_bar__plus_00_dot_55_bar__minus_01_dot_88 loc_bar__minus_1_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Painting_bar__plus_00_dot_00_bar__plus_01_dot_56_bar__minus_02_dot_69 loc_bar_4_bar__minus_8_bar_3_bar_0)
        (objectAtLocation AlarmClock_bar__minus_01_dot_24_bar__plus_00_dot_68_bar__minus_02_dot_43 loc_bar__minus_4_bar__minus_2_bar_2_bar_30)
        (objectAtLocation CD_bar__plus_00_dot_95_bar__plus_00_dot_37_bar__minus_02_dot_38 loc_bar_5_bar__minus_6_bar_2_bar_60)
        (objectAtLocation Window_bar__minus_01_dot_56_bar__plus_01_dot_42_bar__minus_01_dot_36 loc_bar__minus_4_bar__minus_2_bar_2_bar_15)
        (objectAtLocation Window_bar__minus_01_dot_56_bar__plus_01_dot_42_bar__minus_00_dot_06 loc_bar__minus_4_bar_0_bar_3_bar_30)
        (objectAtLocation Mug_bar__plus_00_dot_19_bar__plus_00_dot_62_bar__plus_00_dot_99 loc_bar__minus_2_bar__minus_2_bar_0_bar_45)
        (objectAtLocation Blinds_bar__minus_01_dot_46_bar__plus_02_dot_16_bar__minus_01_dot_34 loc_bar__minus_4_bar__minus_2_bar_2_bar__minus_30)
        (objectAtLocation Blinds_bar__minus_01_dot_46_bar__plus_02_dot_16_bar__minus_00_dot_05 loc_bar__minus_4_bar_0_bar_3_bar__minus_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PenType)
                                    (receptacleType ?r SideTableType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PenType)
                                            (receptacleType ?r SideTableType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            