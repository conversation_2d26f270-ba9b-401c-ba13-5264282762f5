{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 52}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000245.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000246.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000247.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000248.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000249.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000250.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000251.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000252.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 53}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 53}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 54}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000276.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000277.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000278.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000279.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000280.png", "low_idx": 55}, {"high_idx": 6, "image_name": "000000281.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000282.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000283.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000284.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000285.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000286.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000287.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000288.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000289.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000290.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000291.png", "low_idx": 56}, {"high_idx": 6, "image_name": "000000292.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000293.png", "low_idx": 57}, {"high_idx": 6, "image_name": "000000294.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000295.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000296.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000297.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000298.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000299.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000300.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000301.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000302.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000303.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000304.png", "low_idx": 58}, {"high_idx": 6, "image_name": "000000305.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000306.png", "low_idx": 59}, {"high_idx": 6, "image_name": "000000307.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000308.png", "low_idx": 60}, {"high_idx": 6, "image_name": "000000309.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000310.png", "low_idx": 61}, {"high_idx": 6, "image_name": "000000311.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000312.png", "low_idx": 62}, {"high_idx": 6, "image_name": "000000313.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000314.png", "low_idx": 63}, {"high_idx": 6, "image_name": "000000315.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000316.png", "low_idx": 64}, {"high_idx": 6, "image_name": "000000317.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000318.png", "low_idx": 65}, {"high_idx": 6, "image_name": "000000319.png", "low_idx": 66}, {"high_idx": 6, "image_name": "000000320.png", "low_idx": 66}, {"high_idx": 6, "image_name": "000000321.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000322.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000323.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000324.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000325.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000326.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000327.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000328.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000329.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000330.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000331.png", "low_idx": 67}, {"high_idx": 6, "image_name": "000000332.png", "low_idx": 68}, {"high_idx": 6, "image_name": "000000333.png", "low_idx": 68}, {"high_idx": 6, "image_name": "000000334.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000335.png", "low_idx": 69}, {"high_idx": 6, "image_name": "000000336.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000337.png", "low_idx": 70}, {"high_idx": 6, "image_name": "000000338.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000339.png", "low_idx": 71}, {"high_idx": 6, "image_name": "000000340.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000341.png", "low_idx": 72}, {"high_idx": 6, "image_name": "000000342.png", "low_idx": 73}, {"high_idx": 6, "image_name": "000000343.png", "low_idx": 73}, {"high_idx": 7, "image_name": "000000344.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000345.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000346.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000347.png", "low_idx": 74}, {"high_idx": 7, "image_name": "000000348.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000349.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000350.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000351.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000352.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000353.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000354.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000355.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000356.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000357.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000358.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000359.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000360.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000361.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000362.png", "low_idx": 75}, {"high_idx": 7, "image_name": "000000363.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000364.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000365.png", "low_idx": 76}, {"high_idx": 7, "image_name": "000000366.png", "low_idx": 76}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Knife", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-1|22|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [-1.331300736, -1.331300736, 24.868016, 24.868016, 3.159154652, 3.159154652]], "coordinateReceptacleObjectId": ["DiningTable", [-0.624711872, -0.624711872, 25.896156, 25.896156, 2.673336984, 2.673336984]], "forceVisible": true, "objectId": "Knife|-00.33|+00.79|+06.22"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|20|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [-1.331300736, -1.331300736, 24.868016, 24.868016, 3.159154652, 3.159154652]], "coordinateReceptacleObjectId": ["Drawer", [5.1224, 5.1224, 18.874292, 18.874292, 3.2341672, 3.2341672]], "forceVisible": true, "objectId": "Knife|-00.33|+00.79|+06.22", "receptacleObjectId": "Drawer|+01.28|+00.81|+04.72"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-4|28|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [-1.950289012, -1.950289012, 27.229732, 27.229732, 3.159154652, 3.159154652]], "coordinateReceptacleObjectId": ["DiningTable", [-0.624711872, -0.624711872, 25.896156, 25.896156, 2.673336984, 2.673336984]], "forceVisible": true, "objectId": "Knife|-00.49|+00.79|+06.81"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|1|20|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [-1.950289012, -1.950289012, 27.229732, 27.229732, 3.159154652, 3.159154652]], "coordinateReceptacleObjectId": ["Drawer", [5.1224, 5.1224, 18.874292, 18.874292, 3.2341672, 3.2341672]], "forceVisible": true, "objectId": "Knife|-00.49|+00.79|+06.81", "receptacleObjectId": "Drawer|+01.28|+00.81|+04.72"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|-00.33|+00.79|+06.22"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [91, 146, 152, 190], "mask": [[43650, 3], [43948, 5], [44247, 5], [44546, 6], [44844, 7], [45143, 7], [45441, 6], [45740, 5], [46039, 5], [46337, 6], [46636, 5], [46934, 6], [47233, 5], [47532, 4], [47830, 5], [48129, 4], [48427, 4], [48726, 4], [49025, 5], [49323, 6], [49624, 5], [49925, 3], [50226, 2], [50526, 2], [52906, 1], [53205, 2], [53503, 5], [53802, 6], [54101, 7], [54399, 8], [54698, 8], [54997, 7], [55296, 6], [55595, 6], [55894, 5], [56193, 4], [56492, 3], [56791, 2]], "point": [123, 164]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+01.28|+00.81|+04.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [175, 123, 263, 156], "mask": [[36778, 85], [37078, 86], [37378, 86], [37678, 85], [37978, 85], [38278, 84], [38578, 84], [38877, 85], [39177, 84], [39477, 84], [39777, 83], [40077, 83], [40377, 83], [40677, 82], [40977, 82], [41277, 81], [41577, 81], [41876, 82], [42176, 81], [42476, 81], [42776, 80], [43076, 80], [43376, 80], [43676, 79], [43976, 79], [44276, 78], [44576, 78], [44876, 78], [45175, 78], [45475, 78], [45775, 77], [46075, 77], [46375, 77], [46675, 76]], "point": [219, 138]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|-00.33|+00.79|+06.22", "placeStationary": true, "receptacleObjectId": "Drawer|+01.28|+00.81|+04.72"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [178, 126, 299, 248], "mask": [[37680, 79], [37979, 81], [38279, 81], [38579, 82], [38879, 82], [39179, 83], [39479, 83], [39779, 84], [40079, 84], [40379, 85], [40679, 85], [40978, 87], [41278, 87], [41578, 87], [41878, 88], [42178, 88], [42478, 89], [42778, 89], [43078, 90], [43378, 90], [43678, 91], [43978, 91], [44278, 92], [44578, 92], [44878, 93], [45178, 93], [45478, 94], [45778, 94], [46078, 94], [46378, 95], [46679, 94], [46979, 95], [47279, 95], [47579, 96], [47879, 96], [48179, 97], [48479, 97], [48779, 98], [49079, 98], [49380, 98], [49680, 98], [49980, 99], [50280, 99], [50580, 99], [50880, 100], [51180, 100], [51480, 101], [51780, 101], [52081, 101], [52381, 101], [52681, 102], [52981, 102], [53281, 103], [53581, 103], [53881, 104], [54181, 104], [54481, 104], [54781, 105], [55082, 104], [55382, 105], [55682, 105], [55982, 106], [56282, 106], [56582, 107], [56882, 107], [57182, 108], [57482, 108], [57783, 108], [58083, 108], [58383, 109], [58683, 109], [58983, 109], [59283, 110], [59583, 110], [59883, 111], [60183, 111], [60484, 111], [60784, 111], [61084, 112], [61384, 112], [61684, 113], [61984, 113], [62284, 114], [62584, 114], [62884, 115], [63184, 115], [63485, 114], [63785, 115], [64085, 115], [64385, 115], [64685, 115], [64985, 115], [65285, 115], [65585, 115], [65885, 115], [66186, 114], [66486, 114], [66786, 114], [67086, 114], [67386, 114], [67686, 114], [67986, 114], [68286, 114], [68586, 114], [68887, 113], [69187, 113], [69486, 114], [69786, 113], [70086, 112], [70386, 111], [70686, 110], [70985, 110], [71285, 109], [71585, 109], [71885, 108], [72185, 107], [72484, 107], [72784, 106], [73084, 105], [73384, 105], [73684, 104], [73984, 103], [74284, 102]], "point": [238, 186]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+01.28|+00.81|+04.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [178, 126, 299, 248], "mask": [[37680, 79], [37979, 81], [38279, 81], [38579, 82], [38879, 82], [39179, 83], [39479, 83], [39779, 84], [40079, 84], [40379, 85], [40679, 85], [40978, 87], [41278, 87], [41578, 87], [41878, 88], [42178, 88], [42478, 89], [42778, 89], [43078, 11], [43091, 77], [43378, 11], [43391, 77], [43678, 11], [43692, 77], [43978, 11], [43992, 77], [44278, 11], [44292, 78], [44578, 11], [44592, 78], [44878, 11], [44892, 79], [45178, 12], [45193, 78], [45478, 12], [45493, 79], [45778, 12], [45793, 79], [46078, 12], [46093, 79], [46378, 12], [46393, 80], [46679, 11], [46693, 80], [46979, 12], [46993, 81], [47279, 12], [47294, 80], [47579, 12], [47594, 81], [47879, 12], [47894, 81], [48179, 12], [48194, 82], [48479, 13], [48494, 82], [48779, 13], [48794, 83], [49079, 13], [49094, 83], [49380, 12], [49394, 84], [49680, 13], [49694, 84], [49980, 13], [49994, 85], [50280, 13], [50295, 84], [50580, 13], [50595, 84], [50880, 13], [50895, 85], [51180, 13], [51195, 85], [51480, 13], [51495, 86], [51780, 13], [51795, 86], [52081, 12], [52095, 87], [52381, 12], [52396, 86], [52681, 12], [52696, 87], [52981, 12], [52996, 87], [53281, 12], [53296, 88], [53581, 13], [53596, 88], [53881, 13], [53896, 89], [54181, 13], [54197, 88], [54481, 13], [54497, 88], [54781, 13], [54797, 89], [55082, 12], [55097, 89], [55382, 13], [55397, 90], [55682, 13], [55697, 90], [55982, 13], [55998, 90], [56282, 13], [56298, 90], [56582, 13], [56598, 91], [56882, 14], [56898, 91], [57182, 14], [57198, 92], [57482, 14], [57498, 92], [57783, 13], [57799, 92], [58083, 13], [58099, 92], [58383, 14], [58399, 93], [58683, 14], [58699, 93], [58983, 14], [58999, 93], [59283, 14], [59299, 94], [59583, 14], [59599, 94], [59883, 15], [59900, 94], [60183, 15], [60200, 94], [60484, 14], [60500, 95], [60784, 14], [60800, 95], [61084, 14], [61100, 96], [61384, 15], [61400, 96], [61684, 15], [61700, 97], [61984, 15], [62000, 97], [62284, 15], [62300, 98], [62584, 114], [62884, 16], [62901, 98], [63184, 16], [63201, 98], [63485, 15], [63501, 98], [63785, 115], [64085, 115], [64385, 115], [64685, 115], [64985, 115], [65285, 115], [65585, 115], [65885, 115], [66186, 114], [66486, 114], [66786, 114], [67086, 114], [67386, 114], [67686, 114], [67986, 114], [68286, 114], [68586, 114], [68887, 113], [69187, 113], [69486, 114], [69786, 113], [70086, 112], [70386, 111], [70686, 110], [70985, 110], [71285, 109], [71585, 109], [71885, 108], [72185, 107], [72484, 107], [72784, 106], [73084, 105], [73384, 105], [73684, 104], [73984, 103], [74284, 102]], "point": [238, 186]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Knife|-00.49|+00.79|+06.81"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [164, 107, 233, 160], "mask": [[31964, 1], [32264, 3], [32565, 4], [32866, 5], [33167, 5], [33468, 5], [33769, 6], [34070, 6], [34371, 7], [34673, 6], [34974, 6], [35275, 7], [35576, 7], [35877, 8], [36178, 8], [36479, 8], [36780, 9], [37082, 8], [37383, 8], [37684, 9], [37985, 9], [38286, 9], [38587, 10], [38889, 9], [39190, 9], [39491, 10], [39792, 10], [40093, 10], [40395, 10], [40696, 10], [40997, 4], [41002, 5], [41298, 3], [41305, 4], [41599, 1], [41606, 4], [41907, 4], [42208, 5], [42509, 5], [42810, 5], [43111, 6], [43413, 5], [43714, 6], [44015, 6], [44316, 6], [44618, 6], [44919, 6], [45220, 6], [45522, 6], [45823, 6], [46124, 6], [46425, 7], [46727, 6], [47028, 6], [47328, 6], [47629, 5], [47931, 3]], "point": [198, 132]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|+01.28|+00.81|+04.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [175, 123, 263, 156], "mask": [[36778, 85], [37078, 86], [37378, 86], [37678, 85], [37978, 85], [38278, 84], [38578, 84], [38877, 85], [39177, 84], [39477, 84], [39777, 83], [40077, 83], [40377, 83], [40677, 82], [40977, 82], [41277, 81], [41577, 81], [41876, 82], [42176, 81], [42476, 81], [42776, 80], [43076, 80], [43376, 80], [43676, 79], [43976, 79], [44276, 78], [44576, 78], [44876, 78], [45175, 78], [45475, 78], [45775, 77], [46075, 77], [46375, 77], [46675, 76]], "point": [219, 138]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|-00.49|+00.79|+06.81", "placeStationary": true, "receptacleObjectId": "Drawer|+01.28|+00.81|+04.72"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [178, 126, 299, 248], "mask": [[37680, 79], [37979, 81], [38279, 81], [38579, 82], [38879, 82], [39179, 83], [39479, 83], [39779, 84], [40079, 84], [40379, 85], [40679, 85], [40978, 87], [41278, 87], [41578, 87], [41878, 88], [42178, 88], [42478, 89], [42778, 89], [43078, 11], [43091, 77], [43378, 11], [43391, 77], [43678, 11], [43692, 77], [43978, 11], [43992, 77], [44278, 11], [44292, 78], [44578, 11], [44592, 78], [44878, 11], [44892, 79], [45178, 12], [45193, 78], [45478, 12], [45493, 79], [45778, 12], [45793, 79], [46078, 12], [46093, 79], [46378, 12], [46393, 80], [46679, 11], [46693, 80], [46979, 12], [46993, 81], [47279, 12], [47294, 80], [47579, 12], [47594, 81], [47879, 12], [47894, 81], [48179, 12], [48194, 82], [48479, 13], [48494, 82], [48779, 13], [48794, 83], [49079, 13], [49094, 83], [49380, 12], [49394, 84], [49680, 13], [49694, 84], [49980, 13], [49994, 85], [50280, 13], [50295, 84], [50580, 13], [50595, 84], [50880, 13], [50895, 85], [51180, 13], [51195, 85], [51480, 13], [51495, 86], [51780, 13], [51795, 86], [52081, 12], [52095, 87], [52381, 12], [52396, 86], [52681, 12], [52696, 87], [52981, 12], [52996, 87], [53281, 12], [53296, 88], [53581, 13], [53596, 88], [53881, 13], [53896, 89], [54181, 13], [54197, 88], [54481, 13], [54497, 88], [54781, 13], [54797, 89], [55082, 12], [55097, 89], [55382, 13], [55397, 90], [55682, 13], [55697, 90], [55982, 13], [55998, 90], [56282, 13], [56298, 90], [56582, 13], [56598, 91], [56882, 14], [56898, 91], [57182, 14], [57198, 92], [57482, 14], [57498, 92], [57783, 13], [57799, 92], [58083, 13], [58099, 92], [58383, 14], [58399, 93], [58683, 14], [58699, 93], [58983, 14], [58999, 93], [59283, 14], [59299, 94], [59583, 14], [59599, 94], [59883, 15], [59900, 94], [60183, 15], [60200, 94], [60484, 14], [60500, 95], [60784, 14], [60800, 95], [61084, 14], [61100, 96], [61384, 15], [61400, 96], [61684, 15], [61700, 97], [61984, 15], [62000, 97], [62284, 15], [62300, 98], [62584, 114], [62884, 16], [62901, 98], [63184, 16], [63201, 98], [63485, 15], [63501, 98], [63785, 115], [64085, 115], [64385, 115], [64685, 115], [64985, 115], [65285, 115], [65585, 115], [65885, 115], [66186, 114], [66486, 114], [66786, 114], [67086, 114], [67386, 114], [67686, 114], [67986, 114], [68286, 114], [68586, 114], [68887, 113], [69187, 113], [69486, 114], [69786, 113], [70086, 112], [70386, 111], [70686, 110], [70985, 110], [71285, 109], [71585, 109], [71885, 108], [72185, 107], [72484, 107], [72784, 106], [73084, 105], [73384, 105], [73684, 104], [73984, 103], [74284, 102]], "point": [238, 186]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|+01.28|+00.81|+04.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [178, 126, 299, 248], "mask": [[37680, 79], [37979, 81], [38279, 81], [38579, 82], [38879, 82], [39179, 83], [39479, 83], [39779, 84], [40079, 84], [40379, 85], [40679, 85], [40978, 87], [41278, 87], [41578, 87], [41878, 88], [42178, 88], [42478, 89], [42778, 89], [43078, 11], [43091, 7], [43099, 69], [43378, 11], [43391, 6], [43400, 68], [43678, 11], [43692, 5], [43700, 69], [43978, 11], [43992, 5], [44000, 69], [44278, 11], [44292, 6], [44301, 69], [44578, 11], [44592, 6], [44601, 69], [44878, 11], [44892, 6], [44901, 70], [45178, 12], [45193, 5], [45201, 70], [45478, 12], [45493, 5], [45501, 71], [45778, 12], [45793, 6], [45802, 70], [46078, 12], [46093, 6], [46102, 70], [46378, 12], [46393, 6], [46402, 71], [46679, 11], [46693, 6], [46702, 71], [46979, 12], [46993, 7], [47002, 72], [47279, 12], [47294, 6], [47303, 71], [47579, 12], [47594, 6], [47603, 72], [47879, 12], [47894, 6], [47903, 72], [48179, 12], [48194, 6], [48203, 73], [48479, 13], [48494, 7], [48503, 73], [48779, 13], [48794, 7], [48803, 74], [49079, 13], [49094, 7], [49104, 73], [49380, 12], [49394, 7], [49404, 74], [49680, 13], [49694, 8], [49704, 74], [49980, 13], [49994, 9], [50004, 75], [50280, 13], [50295, 7], [50304, 75], [50580, 13], [50595, 7], [50604, 75], [50880, 13], [50895, 8], [50904, 76], [51180, 13], [51195, 8], [51205, 75], [51480, 13], [51495, 7], [51505, 76], [51780, 13], [51795, 7], [51805, 76], [52081, 12], [52095, 7], [52105, 77], [52381, 12], [52396, 6], [52405, 77], [52681, 12], [52696, 6], [52705, 78], [52981, 12], [52996, 6], [53006, 77], [53281, 12], [53296, 7], [53306, 78], [53581, 13], [53596, 7], [53606, 78], [53881, 13], [53896, 7], [53906, 79], [54181, 13], [54197, 6], [54206, 79], [54481, 13], [54497, 6], [54507, 78], [54781, 13], [54797, 7], [54807, 79], [55082, 12], [55097, 7], [55107, 79], [55382, 13], [55397, 7], [55407, 80], [55682, 13], [55697, 7], [55707, 80], [55982, 13], [55998, 7], [56008, 80], [56282, 13], [56298, 7], [56308, 80], [56582, 13], [56598, 7], [56608, 81], [56882, 14], [56898, 7], [56908, 81], [57182, 14], [57198, 8], [57208, 82], [57482, 14], [57498, 8], [57509, 81], [57783, 13], [57799, 7], [57809, 82], [58083, 13], [58099, 7], [58109, 82], [58383, 14], [58399, 7], [58409, 83], [58683, 14], [58699, 8], [58709, 83], [58983, 14], [58999, 8], [59009, 83], [59283, 14], [59299, 8], [59310, 83], [59583, 14], [59599, 8], [59610, 83], [59883, 15], [59900, 8], [59910, 84], [60183, 15], [60200, 8], [60210, 84], [60484, 14], [60500, 8], [60510, 85], [60784, 14], [60800, 8], [60810, 85], [61084, 14], [61100, 9], [61111, 85], [61384, 15], [61400, 9], [61411, 85], [61684, 15], [61700, 9], [61711, 86], [61984, 15], [62000, 9], [62011, 86], [62284, 15], [62300, 10], [62311, 87], [62584, 26], [62611, 87], [62884, 16], [62901, 9], [62911, 88], [63184, 16], [63201, 98], [63485, 15], [63501, 98], [63785, 115], [64085, 115], [64385, 115], [64685, 115], [64985, 115], [65285, 115], [65585, 115], [65885, 115], [66186, 114], [66486, 114], [66786, 114], [67086, 114], [67386, 114], [67686, 114], [67986, 114], [68286, 114], [68586, 114], [68887, 113], [69187, 113], [69486, 114], [69786, 113], [70086, 112], [70386, 111], [70686, 110], [70985, 110], [71285, 109], [71585, 109], [71885, 108], [72185, 107], [72484, 107], [72784, 106], [73084, 105], [73384, 105], [73684, 104], [73984, 103], [74284, 102]], "point": [238, 186]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan18", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 0.5, "y": 0.900999844, "z": 1.75}, "object_poses": [{"objectName": "Pan_08687688", "position": {"x": 2.05195951, "y": 1.5137676, "z": 5.626}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": -0.6870909, "y": 1.027588, "z": 0.486808717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 1.33202, "y": 0.8960006, "z": 2.13703632}, "rotation": {"x": 2.27491e-21, "y": 89.99999, "z": 1.40334183e-14}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 0.192551464, "y": 0.7596526, "z": 6.56560326}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": -0.5356747, "y": 0.9927421, "z": 0.395208716}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": 0.169216156, "y": 0.760130465, "z": 6.807433}, "rotation": {"x": 0.0, "y": 315.000031, "z": 0.0}}, {"objectName": "Pot_73d89a5b", "position": {"x": 1.32, "y": 0.988599956, "z": 3.848}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": -0.6172804, "y": 0.7725101, "z": 0.395155728}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": 0.6205562, "y": 0.9932486, "z": 0.486809283}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": -0.467251956, "y": 0.7606369, "z": 6.416539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": 1.96668923, "y": 1.56563163, "z": 5.876}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": 1.33202, "y": 0.9463248, "z": 2.52536821}, "rotation": {"x": 2.27491e-21, "y": 89.99999, "z": 1.40334183e-14}}, {"objectName": "Cup_8e8a26e1", "position": {"x": 1.13578975, "y": 0.9888446, "z": 0.6680888}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 0.9584641, "y": 1.58018672, "z": 0.251273155}, "rotation": {"x": 0.0, "y": 0.0, "z": -1.70754731e-06}}, {"objectName": "Knife_94d26365", "position": {"x": -0.332825184, "y": 0.789788663, "z": 6.217004}, "rotation": {"x": 0.0, "y": 315.000031, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": -0.3842585, "y": 1.04216564, "z": 0.6700087}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": 2.05555582, "y": 0.130182654, "z": 6.46908}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 1.238817, "y": 0.140689611, "z": 2.08601737}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 1.40633225, "y": 0.140689611, "z": 2.72012758}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": -0.275385082, "y": 0.139144063, "z": 0.429923683}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 1.58568, "y": 2.20210528, "z": 4.175332}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 1.40031552, "y": 0.136815369, "z": 4.608868}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": 1.49322724, "y": 1.0811137, "z": 0.213347912}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": 0.3052411, "y": 0.848502, "z": 6.604039}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": 0.06465548, "y": 1.09214664, "z": 0.310429215}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": -0.0037561655, "y": 0.8346215, "z": 6.479039}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": 1.22514915, "y": 1.0672332, "z": 1.57757056}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9f9e1c57", "position": {"x": -0.1349523, "y": 1.57863045, "z": 0.251073122}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_b7525b79", "position": {"x": -0.221677959, "y": 0.829698145, "z": 6.787113}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": 1.49786007, "y": 0.9410567, "z": 2.52536845}, "rotation": {"x": 2.27491e-21, "y": 89.99999, "z": 1.40334183e-14}}, {"objectName": "Knife_94d26365", "position": {"x": -0.487572253, "y": 0.789788663, "z": 6.807433}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": 0.80350095, "y": 0.7729708, "z": 0.395142823}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": -0.308550477, "y": 1.0672332, "z": 0.303608716}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": 1.317894, "y": 0.988599956, "z": 4.2942}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": -0.5523339, "y": 0.1447382, "z": 0.506153166}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": -0.346677959, "y": 0.7550883, "z": 6.63261461}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": -0.45996666, "y": 1.0811137, "z": 0.486808717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 0.6045078, "y": 0.1437946, "z": 0.5763414}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": 0.10285759, "y": 0.775088251, "z": 6.30539227}, "rotation": {"x": 0.0, "y": 224.999985, "z": 0.0}}, {"objectName": "PaperTowelRoll_bd2376ff", "position": {"x": -0.353683978, "y": 0.864373744, "z": 6.019368}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Pot_73d89a5b", "position": {"x": 1.559894, "y": 0.988599956, "z": 4.2942}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": 0.150742471, "y": 0.7949763, "z": 6.416539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": 0.9233889, "y": 0.9927421, "z": 0.2120077}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 1.40217876, "y": 2.20210528, "z": 3.933576}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_08687688", "position": {"x": 1.562, "y": 0.988599956, "z": 3.848}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": 1.44258, "y": 0.9400979, "z": 2.085737}, "rotation": {"x": 2.27491e-21, "y": 89.99999, "z": 1.40334183e-14}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 0.124673754, "y": 0.7715096, "z": 0.618839443}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_8e8a26e1", "position": {"x": 0.125022, "y": 0.7562329, "z": 6.85162735}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": -0.09667797, "y": 0.7612178, "z": 6.63261461}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": 1.40217876, "y": 2.20210552, "z": 3.74781227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2339092532, "scene_num": 18}, "task_id": "trial_T20190907_065059_144926", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A1GVTH5YS3WOK0_3JMSRU9HQLB43U8MZ9TRP9PJ2RJVED", "high_descs": ["Turn left and walk towards the black circle table.", "Pick up the yellow knife from the table.", "Turn to the right and walk towards the drawer below the toaster.", "Open the drawer and place the knife in it.", "Turn around and walk towards the black circle table.", "Pick up the other yellow knife on the table.", "Turn to the right and walk towards the drawer below the toaster.", "Open the drawer and place the knife in it."], "task_desc": "Place the two yellow knives from the table into the drawer below the toaster.", "votes": [1, 1, 1, 1]}, {"assignment_id": "A1S1K7134S2VUC_3QJOXOW4XM8LD7EDLHS02D66B6NMEJ", "high_descs": ["Turn left, move to the table, near the chair.", "Pick up the knife, near the paper towel, from the table", "Turn right, move to the counter between the stove and refrigerator.", "Place the knife into the drawer left of the stove.", "Turn left, move to the table, opposite the chair.", "Pick up the knife from the table.", "Turn left, move to the counter between the stove and refrigerator.", "Place the knife into the drawer left of the stove."], "task_desc": "Place knives into a drawer.", "votes": [1, 1, 1, 1]}, {"assignment_id": "A1OWHPMKE7YAGL_3Y9N9SS8L1SFZFS3J4C10WFFB333DP", "high_descs": ["Back away from the counter and go to the left to the dining table.", "Pick up the big knife that is on the table behind the paper towels.", "Turn to the right and go to the drawer between the fridge and stove. ", "Open the drawer, put the knife inside, close the drawer. ", "Turn around and go to the wall, go right to the dining table.", "Pick up the knife that is on the table by the bread. ", "Turn around and go to the left, then go left to the drawer between the fridge and stove. ", "Open the drawer, put the knife inside, close the drawer, "], "task_desc": "Put two knives in a drawer. ", "votes": [1, 1, 1, 1]}, {"assignment_id": "A2A028LRDJB7ZB_3PXX5PX6L0FM43T4EN05SE5LELWBAL", "high_descs": ["Turn to your left walk few steps, then turn right head to the table", "Pick up the knife beside the tissue on the table", "Turn to your right, then turn left head to the cabinet beside the stove", "Pull the drawer and put in the knife, close the cabinet drawer", "Turn around to your left, walk straight then head to the table", "Pick up the knife on the left side of the table", "Turn around to your left walk few step and head back to the drawer beside the stove", "Pull the drawer, then put the knife on the right side of the other knife"], "task_desc": "Put two knives on the cabinet drawer", "votes": [1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3UN61F00HZ6CE4YCFOVEKIU34TX5RL", "high_descs": ["Round, then turn right to approach the round table.", "Pick up the knife on the table, behind a paper towel.", "Turn around, then turn left to face the counter with the toaster on it.", "Put the knife in the drawer beneath the toaster.", "Turn around, then turn left, then turn right again to approach the table.", "Pick up a knife on the table, next to the bread.", "Turn around and return to the counter with the toaster on it.", "Put the knife in the drawer, beneath the toaster."], "task_desc": "Put two knives in a drawer.", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_33FOTY3KEP29KK8AW437SXI0SKJ1C3", "high_descs": ["Turn around and walk towards the wall, then hang a right and walk over to the round black table.", "Pick up the large kitchen knife that is closest to the paper towel roll on the round black table.", "Turn around and walk forward, then turn left ad walk over to the toaster.", "Open the drawer below where the toaster is on the counter and put the large knife inside on the left side, then close the drawer.", "Turn around and walk towards the wall, then turn right and walk up to the wall, then turn right and walk up to the round black table.", "Pick up the large kitchen knife off of the round black table.", "Turn around and take a step forward, then turn left and walk across the room, then hang a left and walk up to the toaster.", "Open the drawer below the counter that the toaster is on and put the large knife inside to the right of the knife that is already in there."], "task_desc": "Move two knives into a drawer.", "votes": [1, 1]}]}}