
(define (problem plan_trial_T20190908_094147_637106)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__plus_01_dot_49_bar__plus_01_dot_14_bar__minus_01_dot_61 - object
        AlarmClock_bar__plus_01_dot_69_bar__plus_00_dot_75_bar__minus_01_dot_36 - object
        AlarmClock_bar__plus_02_dot_34_bar__plus_00_dot_75_bar__minus_01_dot_43 - object
        BasketBall_bar__plus_01_dot_14_bar__plus_00_dot_12_bar__minus_01_dot_66 - object
        Book_bar__plus_02_dot_06_bar__plus_00_dot_74_bar__minus_01_dot_39 - object
        Book_bar__minus_00_dot_31_bar__plus_00_dot_51_bar__minus_00_dot_39 - object
        Book_bar__minus_01_dot_31_bar__plus_00_dot_51_bar__minus_01_dot_12 - object
        CD_bar__plus_00_dot_36_bar__plus_00_dot_65_bar__minus_01_dot_66 - object
        CD_bar__plus_02_dot_51_bar__plus_00_dot_75_bar__minus_01_dot_32 - object
        CellPhone_bar__plus_02_dot_17_bar__plus_01_dot_39_bar__minus_01_dot_66 - object
        CellPhone_bar__plus_02_dot_49_bar__plus_01_dot_39_bar__minus_01_dot_66 - object
        CellPhone_bar__minus_00_dot_64_bar__plus_00_dot_51_bar__minus_00_dot_39 - object
        Chair_bar__plus_02_dot_16_bar__plus_00_dot_02_bar__minus_01_dot_13 - object
        CreditCard_bar__plus_00_dot_05_bar__plus_00_dot_38_bar__minus_01_dot_66 - object
        CreditCard_bar__plus_00_dot_36_bar__plus_00_dot_65_bar__minus_01_dot_78 - object
        CreditCard_bar__plus_01_dot_85_bar__plus_00_dot_75_bar__minus_01_dot_47 - object
        KeyChain_bar__plus_00_dot_08_bar__plus_00_dot_65_bar__minus_01_dot_66 - object
        KeyChain_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_47 - object
        Lamp_bar__plus_00_dot_00_bar__plus_00_dot_64_bar__minus_01_dot_77 - object
        Laptop_bar__minus_00_dot_48_bar__plus_00_dot_51_bar__minus_00_dot_87 - object
        Laptop_bar__minus_00_dot_98_bar__plus_00_dot_51_bar__minus_00_dot_87 - object
        LightSwitch_bar__minus_00_dot_61_bar__plus_01_dot_25_bar__plus_01_dot_44 - object
        Mirror_bar__plus_02_dot_82_bar__plus_01_dot_76_bar__minus_00_dot_07 - object
        Pencil_bar__plus_02_dot_35_bar__plus_00_dot_78_bar__minus_01_dot_61 - object
        Pen_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_74 - object
        Pen_bar__plus_01_dot_52_bar__plus_00_dot_56_bar__minus_01_dot_30 - object
        Pen_bar__plus_01_dot_66_bar__plus_00_dot_78_bar__minus_01_dot_68 - object
        Pillow_bar__minus_01_dot_15_bar__plus_00_dot_54_bar__minus_01_dot_61 - object
        Window_bar__minus_01_dot_68_bar__plus_01_dot_40_bar__plus_00_dot_37 - object
        Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92 - receptacle
        Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58 - receptacle
        Drawer_bar__plus_00_dot_15_bar__plus_00_dot_20_bar__minus_01_dot_64 - receptacle
        Drawer_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64 - receptacle
        GarbageCan_bar__plus_02_dot_66_bar__plus_00_dot_00_bar__plus_01_dot_28 - receptacle
        Shelf_bar__plus_01_dot_56_bar__plus_00_dot_06_bar__minus_01_dot_34 - receptacle
        Shelf_bar__plus_01_dot_56_bar__plus_00_dot_30_bar__minus_01_dot_34 - receptacle
        Shelf_bar__plus_01_dot_56_bar__plus_00_dot_54_bar__minus_01_dot_34 - receptacle
        Shelf_bar__plus_01_dot_60_bar__plus_00_dot_77_bar__minus_01_dot_68 - receptacle
        Shelf_bar__plus_01_dot_60_bar__plus_01_dot_13_bar__minus_01_dot_68 - receptacle
        Shelf_bar__plus_01_dot_99_bar__plus_01_dot_38_bar__minus_01_dot_70 - receptacle
        Shelf_bar__plus_02_dot_24_bar__plus_00_dot_77_bar__minus_01_dot_68 - receptacle
        Shelf_bar__plus_02_dot_24_bar__plus_01_dot_13_bar__minus_01_dot_68 - receptacle
        SideTable_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64 - receptacle
        loc_bar_6_bar__minus_4_bar_2_bar_30 - location
        loc_bar_2_bar__minus_3_bar_2_bar_60 - location
        loc_bar_9_bar__minus_2_bar_2_bar_30 - location
        loc_bar_4_bar__minus_1_bar_2_bar_60 - location
        loc_bar_6_bar__minus_4_bar_2_bar_15 - location
        loc_bar_9_bar_4_bar_1_bar_60 - location
        loc_bar_2_bar__minus_5_bar_1_bar_45 - location
        loc_bar_6_bar__minus_4_bar_2_bar_45 - location
        loc_bar_9_bar_0_bar_1_bar__minus_15 - location
        loc_bar_3_bar__minus_3_bar_3_bar_45 - location
        loc_bar_4_bar__minus_5_bar_2_bar_60 - location
        loc_bar_2_bar__minus_4_bar_2_bar_60 - location
        loc_bar__minus_2_bar_4_bar_0_bar_45 - location
        loc_bar_9_bar__minus_2_bar_2_bar_60 - location
        loc_bar__minus_4_bar_2_bar_3_bar_15 - location
        loc_bar_5_bar__minus_3_bar_3_bar_60 - location
        loc_bar_8_bar__minus_2_bar_2_bar_60 - location
        loc_bar_3_bar__minus_1_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType Shelf_bar__plus_01_dot_60_bar__plus_00_dot_77_bar__minus_01_dot_68 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_99_bar__plus_01_dot_38_bar__minus_01_dot_70 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_60_bar__plus_01_dot_13_bar__minus_01_dot_68 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_56_bar__plus_00_dot_54_bar__minus_01_dot_34 ShelfType)
        (receptacleType Drawer_bar__plus_00_dot_15_bar__plus_00_dot_20_bar__minus_01_dot_64 DrawerType)
        (receptacleType Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92 BedType)
        (receptacleType Shelf_bar__plus_02_dot_24_bar__plus_00_dot_77_bar__minus_01_dot_68 ShelfType)
        (receptacleType Shelf_bar__plus_02_dot_24_bar__plus_01_dot_13_bar__minus_01_dot_68 ShelfType)
        (receptacleType GarbageCan_bar__plus_02_dot_66_bar__plus_00_dot_00_bar__plus_01_dot_28 GarbageCanType)
        (receptacleType SideTable_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64 SideTableType)
        (receptacleType Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58 DeskType)
        (receptacleType Shelf_bar__plus_01_dot_56_bar__plus_00_dot_30_bar__minus_01_dot_34 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_56_bar__plus_00_dot_06_bar__minus_01_dot_34 ShelfType)
        (receptacleType Drawer_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64 DrawerType)
        (objectType AlarmClock_bar__plus_02_dot_34_bar__plus_00_dot_75_bar__minus_01_dot_43 AlarmClockType)
        (objectType CellPhone_bar__plus_02_dot_49_bar__plus_01_dot_39_bar__minus_01_dot_66 CellPhoneType)
        (objectType Laptop_bar__minus_00_dot_98_bar__plus_00_dot_51_bar__minus_00_dot_87 LaptopType)
        (objectType CreditCard_bar__plus_01_dot_85_bar__plus_00_dot_75_bar__minus_01_dot_47 CreditCardType)
        (objectType Book_bar__minus_01_dot_31_bar__plus_00_dot_51_bar__minus_01_dot_12 BookType)
        (objectType CellPhone_bar__plus_02_dot_17_bar__plus_01_dot_39_bar__minus_01_dot_66 CellPhoneType)
        (objectType Mirror_bar__plus_02_dot_82_bar__plus_01_dot_76_bar__minus_00_dot_07 MirrorType)
        (objectType BasketBall_bar__plus_01_dot_14_bar__plus_00_dot_12_bar__minus_01_dot_66 BasketBallType)
        (objectType Book_bar__minus_00_dot_31_bar__plus_00_dot_51_bar__minus_00_dot_39 BookType)
        (objectType Chair_bar__plus_02_dot_16_bar__plus_00_dot_02_bar__minus_01_dot_13 ChairType)
        (objectType Pillow_bar__minus_01_dot_15_bar__plus_00_dot_54_bar__minus_01_dot_61 PillowType)
        (objectType CreditCard_bar__plus_00_dot_36_bar__plus_00_dot_65_bar__minus_01_dot_78 CreditCardType)
        (objectType CD_bar__plus_02_dot_51_bar__plus_00_dot_75_bar__minus_01_dot_32 CDType)
        (objectType Pencil_bar__plus_02_dot_35_bar__plus_00_dot_78_bar__minus_01_dot_61 PencilType)
        (objectType LightSwitch_bar__minus_00_dot_61_bar__plus_01_dot_25_bar__plus_01_dot_44 LightSwitchType)
        (objectType CD_bar__plus_00_dot_36_bar__plus_00_dot_65_bar__minus_01_dot_66 CDType)
        (objectType KeyChain_bar__plus_00_dot_08_bar__plus_00_dot_65_bar__minus_01_dot_66 KeyChainType)
        (objectType Window_bar__minus_01_dot_68_bar__plus_01_dot_40_bar__plus_00_dot_37 WindowType)
        (objectType Pen_bar__plus_01_dot_66_bar__plus_00_dot_78_bar__minus_01_dot_68 PenType)
        (objectType KeyChain_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_47 KeyChainType)
        (objectType Pen_bar__plus_01_dot_52_bar__plus_00_dot_56_bar__minus_01_dot_30 PenType)
        (objectType Laptop_bar__minus_00_dot_48_bar__plus_00_dot_51_bar__minus_00_dot_87 LaptopType)
        (objectType AlarmClock_bar__plus_01_dot_49_bar__plus_01_dot_14_bar__minus_01_dot_61 AlarmClockType)
        (objectType Pen_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_74 PenType)
        (objectType Book_bar__plus_02_dot_06_bar__plus_00_dot_74_bar__minus_01_dot_39 BookType)
        (objectType CreditCard_bar__plus_00_dot_05_bar__plus_00_dot_38_bar__minus_01_dot_66 CreditCardType)
        (objectType CellPhone_bar__minus_00_dot_64_bar__plus_00_dot_51_bar__minus_00_dot_39 CellPhoneType)
        (objectType AlarmClock_bar__plus_01_dot_69_bar__plus_00_dot_75_bar__minus_01_dot_36 AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain BedType BasketBallType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType CDType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType BasketBallType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType AlarmClockType)
        (canContain DeskType PenType)
        (canContain DeskType BookType)
        (canContain DeskType CDType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType CreditCardType)
        (canContain DeskType BasketBallType)
        (canContain DeskType LaptopType)
        (canContain DeskType PencilType)
        (canContain DeskType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (pickupable AlarmClock_bar__plus_02_dot_34_bar__plus_00_dot_75_bar__minus_01_dot_43)
        (pickupable CellPhone_bar__plus_02_dot_49_bar__plus_01_dot_39_bar__minus_01_dot_66)
        (pickupable Laptop_bar__minus_00_dot_98_bar__plus_00_dot_51_bar__minus_00_dot_87)
        (pickupable CreditCard_bar__plus_01_dot_85_bar__plus_00_dot_75_bar__minus_01_dot_47)
        (pickupable Book_bar__minus_01_dot_31_bar__plus_00_dot_51_bar__minus_01_dot_12)
        (pickupable CellPhone_bar__plus_02_dot_17_bar__plus_01_dot_39_bar__minus_01_dot_66)
        (pickupable BasketBall_bar__plus_01_dot_14_bar__plus_00_dot_12_bar__minus_01_dot_66)
        (pickupable Book_bar__minus_00_dot_31_bar__plus_00_dot_51_bar__minus_00_dot_39)
        (pickupable Pillow_bar__minus_01_dot_15_bar__plus_00_dot_54_bar__minus_01_dot_61)
        (pickupable CreditCard_bar__plus_00_dot_36_bar__plus_00_dot_65_bar__minus_01_dot_78)
        (pickupable CD_bar__plus_02_dot_51_bar__plus_00_dot_75_bar__minus_01_dot_32)
        (pickupable Pencil_bar__plus_02_dot_35_bar__plus_00_dot_78_bar__minus_01_dot_61)
        (pickupable CD_bar__plus_00_dot_36_bar__plus_00_dot_65_bar__minus_01_dot_66)
        (pickupable KeyChain_bar__plus_00_dot_08_bar__plus_00_dot_65_bar__minus_01_dot_66)
        (pickupable Pen_bar__plus_01_dot_66_bar__plus_00_dot_78_bar__minus_01_dot_68)
        (pickupable KeyChain_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_47)
        (pickupable Pen_bar__plus_01_dot_52_bar__plus_00_dot_56_bar__minus_01_dot_30)
        (pickupable Laptop_bar__minus_00_dot_48_bar__plus_00_dot_51_bar__minus_00_dot_87)
        (pickupable AlarmClock_bar__plus_01_dot_49_bar__plus_01_dot_14_bar__minus_01_dot_61)
        (pickupable Pen_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_74)
        (pickupable Book_bar__plus_02_dot_06_bar__plus_00_dot_74_bar__minus_01_dot_39)
        (pickupable CreditCard_bar__plus_00_dot_05_bar__plus_00_dot_38_bar__minus_01_dot_66)
        (pickupable CellPhone_bar__minus_00_dot_64_bar__plus_00_dot_51_bar__minus_00_dot_39)
        (pickupable AlarmClock_bar__plus_01_dot_69_bar__plus_00_dot_75_bar__minus_01_dot_36)
        
        (openable Drawer_bar__plus_00_dot_15_bar__plus_00_dot_20_bar__minus_01_dot_64)
        (openable Drawer_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64)
        
        (atLocation agent1 loc_bar_3_bar__minus_1_bar_3_bar_30)
        
        
        
        
        
        
        
        
        
        
        
        
        (inReceptacle Pen_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_74 SideTable_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64)
        (inReceptacle CreditCard_bar__plus_00_dot_36_bar__plus_00_dot_65_bar__minus_01_dot_78 SideTable_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64)
        (inReceptacle CD_bar__plus_00_dot_36_bar__plus_00_dot_65_bar__minus_01_dot_66 SideTable_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64)
        (inReceptacle KeyChain_bar__plus_00_dot_08_bar__plus_00_dot_65_bar__minus_01_dot_66 SideTable_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64)
        (inReceptacle CreditCard_bar__plus_00_dot_05_bar__plus_00_dot_38_bar__minus_01_dot_66 Drawer_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64)
        (inReceptacle CD_bar__plus_02_dot_51_bar__plus_00_dot_75_bar__minus_01_dot_32 Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58)
        (inReceptacle AlarmClock_bar__plus_02_dot_34_bar__plus_00_dot_75_bar__minus_01_dot_43 Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58)
        (inReceptacle AlarmClock_bar__plus_01_dot_69_bar__plus_00_dot_75_bar__minus_01_dot_36 Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58)
        (inReceptacle KeyChain_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_47 Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58)
        (inReceptacle CreditCard_bar__plus_01_dot_85_bar__plus_00_dot_75_bar__minus_01_dot_47 Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58)
        (inReceptacle Book_bar__plus_02_dot_06_bar__plus_00_dot_74_bar__minus_01_dot_39 Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58)
        (inReceptacle Laptop_bar__minus_00_dot_98_bar__plus_00_dot_51_bar__minus_00_dot_87 Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92)
        (inReceptacle Book_bar__minus_01_dot_31_bar__plus_00_dot_51_bar__minus_01_dot_12 Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92)
        (inReceptacle CellPhone_bar__minus_00_dot_64_bar__plus_00_dot_51_bar__minus_00_dot_39 Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92)
        (inReceptacle Laptop_bar__minus_00_dot_48_bar__plus_00_dot_51_bar__minus_00_dot_87 Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92)
        (inReceptacle Book_bar__minus_00_dot_31_bar__plus_00_dot_51_bar__minus_00_dot_39 Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92)
        (inReceptacle Pillow_bar__minus_01_dot_15_bar__plus_00_dot_54_bar__minus_01_dot_61 Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92)
        (inReceptacle Pen_bar__plus_01_dot_52_bar__plus_00_dot_56_bar__minus_01_dot_30 Shelf_bar__plus_01_dot_56_bar__plus_00_dot_54_bar__minus_01_dot_34)
        (inReceptacle Pen_bar__plus_01_dot_66_bar__plus_00_dot_78_bar__minus_01_dot_68 Shelf_bar__plus_01_dot_60_bar__plus_00_dot_77_bar__minus_01_dot_68)
        (inReceptacle Pencil_bar__plus_02_dot_35_bar__plus_00_dot_78_bar__minus_01_dot_61 Shelf_bar__plus_02_dot_24_bar__plus_00_dot_77_bar__minus_01_dot_68)
        (inReceptacle CellPhone_bar__plus_02_dot_49_bar__plus_01_dot_39_bar__minus_01_dot_66 Shelf_bar__plus_01_dot_99_bar__plus_01_dot_38_bar__minus_01_dot_70)
        (inReceptacle CellPhone_bar__plus_02_dot_17_bar__plus_01_dot_39_bar__minus_01_dot_66 Shelf_bar__plus_01_dot_99_bar__plus_01_dot_38_bar__minus_01_dot_70)
        (inReceptacle AlarmClock_bar__plus_01_dot_49_bar__plus_01_dot_14_bar__minus_01_dot_61 Shelf_bar__plus_01_dot_60_bar__plus_01_dot_13_bar__minus_01_dot_68)
        
        
        (receptacleAtLocation Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (receptacleAtLocation Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58 loc_bar_2_bar__minus_5_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_00_dot_15_bar__plus_00_dot_20_bar__minus_01_dot_64 loc_bar_5_bar__minus_3_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64 loc_bar_2_bar__minus_3_bar_2_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_02_dot_66_bar__plus_00_dot_00_bar__plus_01_dot_28 loc_bar_9_bar_4_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_56_bar__plus_00_dot_06_bar__minus_01_dot_34 loc_bar_4_bar__minus_1_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_56_bar__plus_00_dot_30_bar__minus_01_dot_34 loc_bar_8_bar__minus_2_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_56_bar__plus_00_dot_54_bar__minus_01_dot_34 loc_bar_8_bar__minus_2_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_60_bar__plus_00_dot_77_bar__minus_01_dot_68 loc_bar_6_bar__minus_4_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_60_bar__plus_01_dot_13_bar__minus_01_dot_68 loc_bar_6_bar__minus_4_bar_2_bar_30)
        (receptacleAtLocation Shelf_bar__plus_01_dot_99_bar__plus_01_dot_38_bar__minus_01_dot_70 loc_bar_6_bar__minus_4_bar_2_bar_15)
        (receptacleAtLocation Shelf_bar__plus_02_dot_24_bar__plus_00_dot_77_bar__minus_01_dot_68 loc_bar_9_bar__minus_2_bar_2_bar_30)
        (receptacleAtLocation Shelf_bar__plus_02_dot_24_bar__plus_01_dot_13_bar__minus_01_dot_68 loc_bar_9_bar__minus_2_bar_2_bar_30)
        (receptacleAtLocation SideTable_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64 loc_bar_2_bar__minus_4_bar_2_bar_60)
        (objectAtLocation AlarmClock_bar__plus_01_dot_69_bar__plus_00_dot_75_bar__minus_01_dot_36 loc_bar_2_bar__minus_5_bar_1_bar_45)
        (objectAtLocation CD_bar__plus_00_dot_36_bar__plus_00_dot_65_bar__minus_01_dot_66 loc_bar_2_bar__minus_4_bar_2_bar_60)
        (objectAtLocation CellPhone_bar__plus_02_dot_49_bar__plus_01_dot_39_bar__minus_01_dot_66 loc_bar_6_bar__minus_4_bar_2_bar_15)
        (objectAtLocation CreditCard_bar__plus_01_dot_85_bar__plus_00_dot_75_bar__minus_01_dot_47 loc_bar_2_bar__minus_5_bar_1_bar_45)
        (objectAtLocation KeyChain_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_47 loc_bar_2_bar__minus_5_bar_1_bar_45)
        (objectAtLocation Pen_bar__plus_01_dot_66_bar__plus_00_dot_78_bar__minus_01_dot_68 loc_bar_6_bar__minus_4_bar_2_bar_45)
        (objectAtLocation Laptop_bar__minus_00_dot_98_bar__plus_00_dot_51_bar__minus_00_dot_87 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation Book_bar__plus_02_dot_06_bar__plus_00_dot_74_bar__minus_01_dot_39 loc_bar_2_bar__minus_5_bar_1_bar_45)
        (objectAtLocation AlarmClock_bar__plus_02_dot_34_bar__plus_00_dot_75_bar__minus_01_dot_43 loc_bar_2_bar__minus_5_bar_1_bar_45)
        (objectAtLocation Book_bar__minus_01_dot_31_bar__plus_00_dot_51_bar__minus_01_dot_12 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation CreditCard_bar__plus_00_dot_36_bar__plus_00_dot_65_bar__minus_01_dot_78 loc_bar_2_bar__minus_4_bar_2_bar_60)
        (objectAtLocation Pen_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_74 loc_bar_2_bar__minus_4_bar_2_bar_60)
        (objectAtLocation CellPhone_bar__minus_00_dot_64_bar__plus_00_dot_51_bar__minus_00_dot_39 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation Chair_bar__plus_02_dot_16_bar__plus_00_dot_02_bar__minus_01_dot_13 loc_bar_9_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Book_bar__minus_00_dot_31_bar__plus_00_dot_51_bar__minus_00_dot_39 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation Pencil_bar__plus_02_dot_35_bar__plus_00_dot_78_bar__minus_01_dot_61 loc_bar_9_bar__minus_2_bar_2_bar_30)
        (objectAtLocation BasketBall_bar__plus_01_dot_14_bar__plus_00_dot_12_bar__minus_01_dot_66 loc_bar_4_bar__minus_5_bar_2_bar_60)
        (objectAtLocation Laptop_bar__minus_00_dot_48_bar__plus_00_dot_51_bar__minus_00_dot_87 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation Pen_bar__plus_01_dot_52_bar__plus_00_dot_56_bar__minus_01_dot_30 loc_bar_8_bar__minus_2_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__plus_00_dot_08_bar__plus_00_dot_65_bar__minus_01_dot_66 loc_bar_2_bar__minus_4_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__plus_00_dot_05_bar__plus_00_dot_38_bar__minus_01_dot_66 loc_bar_2_bar__minus_3_bar_2_bar_60)
        (objectAtLocation CellPhone_bar__plus_02_dot_17_bar__plus_01_dot_39_bar__minus_01_dot_66 loc_bar_6_bar__minus_4_bar_2_bar_15)
        (objectAtLocation Mirror_bar__plus_02_dot_82_bar__plus_01_dot_76_bar__minus_00_dot_07 loc_bar_9_bar_0_bar_1_bar__minus_15)
        (objectAtLocation CD_bar__plus_02_dot_51_bar__plus_00_dot_75_bar__minus_01_dot_32 loc_bar_2_bar__minus_5_bar_1_bar_45)
        (objectAtLocation Window_bar__minus_01_dot_68_bar__plus_01_dot_40_bar__plus_00_dot_37 loc_bar__minus_4_bar_2_bar_3_bar_15)
        (objectAtLocation AlarmClock_bar__plus_01_dot_49_bar__plus_01_dot_14_bar__minus_01_dot_61 loc_bar_6_bar__minus_4_bar_2_bar_30)
        (objectAtLocation Pillow_bar__minus_01_dot_15_bar__plus_00_dot_54_bar__minus_01_dot_61 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation LightSwitch_bar__minus_00_dot_61_bar__plus_01_dot_25_bar__plus_01_dot_44 loc_bar__minus_2_bar_4_bar_0_bar_45)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PenType)
                                    (receptacleType ?r DeskType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PenType)
                                            (receptacleType ?r DeskType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            