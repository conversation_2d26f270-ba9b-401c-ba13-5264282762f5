{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000266.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 50}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|6|-1|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [8.4885378, 8.4885378, -1.115999936, -1.115999936, 2.402715444, 2.402715444]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [8.416, 8.416, -1.116, -1.116, 0.0, 0.0]], "forceVisible": true, "objectId": "Cup|+02.12|+00.60|-00.28"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|2|8|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-1.244, -1.244, 8.312, 8.312, 3.7060904, 3.7060904]], "forceVisible": true, "objectId": "Microwave|-00.31|+00.93|+02.08"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|3|7|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [8.4885378, 8.4885378, -1.115999936, -1.115999936, 2.402715444, 2.402715444]], "coordinateReceptacleObjectId": ["Cabinet", [7.04371356, 7.04371356, 9.38795376, 9.38795376, 1.555381416, 1.555381416]], "forceVisible": true, "objectId": "Cup|+02.12|+00.60|-00.28", "receptacleObjectId": "Cabinet|+01.76|+00.39|+02.35"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 253], "mask": [[0, 64500], [64501, 299], [64803, 297], [65105, 295], [65407, 293], [65708, 292], [66010, 290], [66312, 288], [66614, 286], [66915, 283], [67217, 280], [67519, 276], [67821, 272], [68122, 269], [68424, 265], [68726, 261], [69028, 257], [69329, 254], [69631, 250], [69933, 246], [70235, 242], [70537, 238], [70838, 235], [71140, 231], [71442, 227], [71744, 223], [72045, 220], [72347, 216], [72649, 213], [72951, 209], [73252, 206], [73554, 202], [73856, 198], [74158, 194], [74459, 191], [74761, 187], [75063, 183], [75365, 179], [75667, 175]], "point": [149, 126]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+02.12|+00.60|-00.28"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [145, 130, 168, 147], "mask": [[38845, 24], [39146, 23], [39446, 22], [39747, 20], [40048, 19], [40348, 18], [40647, 19], [40947, 19], [41247, 20], [41546, 21], [41847, 20], [42147, 19], [42447, 19], [42748, 17], [43048, 17], [43349, 15], [43651, 11], [43953, 8]], "point": [156, 137]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+02.10|+00.00|-00.28"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 38889], [38903, 287], [39202, 289], [39501, 292], [39800, 9100], [48901, 299], [49202, 298], [49503, 297], [49804, 296], [50105, 295], [50406, 294], [50707, 293], [51008, 292], [51310, 290], [51611, 136], [51764, 136], [51912, 135], [52064, 136], [52213, 134], [52364, 136], [52514, 133], [52664, 136], [52815, 132], [52964, 136], [53116, 132], [53263, 137], [53417, 131], [53563, 137], [53718, 131], [53862, 138], [54019, 133], [54160, 140], [54320, 135], [54458, 142], [54622, 278], [54923, 277], [55224, 276], [55525, 275], [55826, 274], [56127, 273], [56428, 272], [56729, 271], [57030, 270], [57331, 269], [57632, 268], [57934, 113], [58053, 147], [58235, 107], [58358, 142], [58536, 101], [58663, 137], [58837, 95], [58968, 132], [59138, 92], [59270, 130], [59439, 90], [59571, 129], [59740, 87], [59873, 127], [60041, 85], [60174, 126], [60342, 82], [60476, 124], [60643, 79], [60778, 122], [60945, 76], [61079, 121], [61246, 73], [61381, 119], [61547, 71], [61682, 118], [61848, 68], [61984, 116], [62149, 67], [62284, 116], [62450, 65], [62585, 115], [62751, 63], [62886, 114], [63052, 61], [63187, 113], [63353, 60], [63487, 113], [63654, 58], [63788, 112], [63955, 56], [64089, 111], [64257, 53], [64390, 110], [64558, 52], [64690, 110], [64859, 50], [64991, 109], [65160, 48], [65292, 108], [65461, 46], [65593, 107], [65762, 45], [65893, 107], [66063, 43], [66194, 106], [66364, 41], [66495, 105], [66665, 40], [66795, 105], [66966, 39], [67095, 105], [67267, 37], [67396, 104], [67569, 35], [67696, 104], [67870, 34], [67996, 104], [68171, 32], [68297, 103], [68474, 29], [68597, 39], [68637, 63], [68937, 63], [69237, 63], [69537, 63], [69837, 63], [70137, 63], [70438, 62], [70738, 62], [71038, 62], [71338, 62], [71638, 62], [71938, 62], [72239, 61], [72539, 61], [72839, 61], [73139, 61], [73439, 61], [73739, 61], [74040, 60], [74340, 60], [74640, 60], [74940, 60], [75240, 60], [75540, 60], [75841, 59], [76141, 59], [76441, 59], [76741, 59], [77041, 59], [77341, 59], [77642, 58], [77942, 58], [78242, 58], [78542, 58], [78842, 58], [79142, 58], [79443, 57], [79743, 57], [80043, 57], [80343, 57], [80643, 57], [80943, 57], [81244, 56], [81544, 56], [81844, 56], [82144, 56], [82444, 56], [82744, 56], [83045, 55], [83345, 55], [83645, 55], [83945, 55], [84245, 55], [84545, 55], [84846, 54], [85146, 54], [85446, 54], [85746, 54], [86046, 54], [86346, 54], [86646, 54], [86947, 53], [87247, 53], [87547, 53], [87847, 53], [88147, 53], [88447, 53], [88748, 52], [89048, 52], [89348, 52], [89648, 52], [89948, 52]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+02.12|+00.60|-00.28", "placeStationary": true, "receptacleObjectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 272], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14156, 243], [14455, 243], [14754, 244], [15054, 244], [15353, 244], [15653, 244], [15952, 244], [16251, 245], [16551, 245], [16850, 245], [17150, 245], [17449, 246], [17748, 246], [18048, 118], [18169, 125], [18347, 112], [18477, 116], [18647, 108], [18781, 112], [18946, 107], [19083, 109], [19245, 106], [19385, 107], [19545, 105], [19687, 105], [19844, 105], [19988, 103], [20144, 104], [20289, 102], [20443, 104], [20590, 100], [20742, 105], [20890, 100], [21042, 105], [21191, 99], [21341, 106], [21491, 98], [21641, 106], [21791, 98], [21940, 107], [22091, 97], [22239, 109], [22391, 97], [22539, 109], [22690, 98], [22838, 111], [22989, 98], [23138, 112], [23289, 98], [23437, 113], [23587, 99], [23736, 114], [23887, 99], [24036, 115], [24187, 98], [24335, 116], [24487, 98], [24635, 116], [24786, 99], [24934, 117], [25086, 98], [25233, 119], [25385, 99], [25533, 120], [25685, 98], [25832, 121], [25984, 99], [26132, 122], [26283, 100], [26431, 123], [26583, 99], [26730, 125], [26882, 100], [27030, 126], [27181, 100], [27329, 128], [27480, 101], [27629, 128], [27779, 102], [27928, 129], [28079, 101], [28227, 131], [28378, 102], [28527, 133], [28677, 102], [28826, 136], [28974, 105], [29126, 253], [29425, 253], [29724, 254], [30024, 253], [30323, 254], [30623, 253], [30922, 254], [31221, 255], [31521, 254], [31820, 255], [32120, 254], [32419, 255], [32718, 256], [33018, 255], [33317, 256], [33617, 255], [33916, 256], [34215, 257], [34515, 256], [34814, 257], [35114, 256], [35413, 257], [35712, 257], [36012, 257], [36311, 258], [36611, 257], [36910, 258], [37209, 258], [37509, 258], [37808, 259], [38108, 258], [38407, 259], [38706, 259], [39006, 259], [39305, 260], [39605, 259], [39904, 260], [40203, 260], [40503, 260], [40802, 260], [41102, 260], [41401, 261], [41700, 261], [42000, 261], [42300, 261], [42600, 260], [42900, 260], [43200, 259], [43500, 259], [43800, 259], [44100, 86], [44187, 171], [44400, 85], [44487, 171], [44700, 85], [45000, 85], [45300, 85], [45600, 84], [45900, 84], [46200, 84], [46500, 84], [46800, 83], [47100, 83], [47400, 83], [47700, 83], [48000, 82], [48300, 82], [48600, 82], [48900, 82], [49200, 81], [49500, 81], [49800, 81], [50100, 81], [50400, 80], [50700, 80], [51000, 80], [51300, 80], [51600, 79], [51900, 79], [52200, 79], [52500, 79], [52800, 78], [53100, 78], [53400, 78], [53700, 78], [54000, 77], [54300, 77], [54600, 77], [54900, 77], [55200, 76], [55500, 76], [55800, 76], [56100, 76], [56400, 75], [56700, 75], [57000, 75], [57300, 75], [57600, 74], [57900, 74], [58200, 74], [58500, 74], [58800, 73], [59100, 73], [59400, 73], [59700, 73], [60000, 72], [60300, 72], [60600, 72], [60900, 72], [61200, 71], [61500, 71], [61800, 71], [62100, 71], [62400, 70], [62700, 70], [63000, 70], [63300, 70], [63600, 69], [63900, 69], [64200, 69], [64500, 69], [64800, 68], [65100, 68], [65400, 68], [65700, 68], [66000, 67], [66300, 67], [66600, 67], [66900, 67], [67200, 66], [67500, 66], [67800, 66], [68100, 66], [68400, 65], [68700, 65], [69000, 65], [69300, 65], [69600, 64], [69900, 64], [70200, 64], [70500, 64], [70800, 63], [71100, 63], [71400, 63], [71700, 63], [72000, 62], [72300, 62], [72600, 62], [72900, 62], [73200, 61], [73500, 61], [73800, 61], [74101, 60], [74402, 58], [74703, 57], [75004, 56], [75305, 55], [75606, 53], [75907, 52], [76208, 51], [76509, 50], [76809, 49], [77110, 48], [77411, 12], [77425, 33], [77712, 10], [77726, 32], [78013, 8], [78027, 30], [78314, 6], [78327, 30], [78615, 4], [78628, 29], [78929, 31], [79230, 32], [79531, 32], [79832, 24], [79857, 6], [80132, 24], [80158, 4], [80433, 22], [80734, 21], [81035, 20], [81336, 18]], "point": [149, 135]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 272], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14156, 243], [14455, 243], [14754, 244], [15054, 244], [15353, 244], [15653, 244], [15952, 244], [16251, 245], [16551, 245], [16850, 245], [17150, 245], [17449, 246], [17748, 246], [18048, 63], [18131, 35], [18169, 125], [18347, 62], [18432, 27], [18477, 116], [18647, 61], [18733, 22], [18781, 112], [18946, 61], [19034, 19], [19083, 109], [19245, 62], [19334, 17], [19385, 107], [19545, 61], [19635, 15], [19687, 105], [19844, 62], [19935, 14], [19988, 103], [20144, 62], [20236, 12], [20289, 102], [20443, 62], [20536, 11], [20590, 100], [20742, 63], [20836, 11], [20890, 100], [21042, 63], [21137, 10], [21191, 99], [21341, 64], [21437, 10], [21491, 98], [21641, 64], [21737, 10], [21791, 98], [21940, 65], [22037, 10], [22091, 97], [22239, 66], [22338, 10], [22391, 97], [22539, 66], [22638, 10], [22690, 98], [22838, 67], [22938, 11], [22989, 98], [23138, 67], [23238, 12], [23289, 98], [23437, 68], [23538, 12], [23587, 99], [23736, 69], [23838, 12], [23887, 99], [24036, 69], [24138, 13], [24187, 98], [24335, 71], [24438, 13], [24487, 98], [24635, 71], [24738, 13], [24786, 99], [24934, 72], [25037, 14], [25086, 98], [25233, 73], [25337, 15], [25385, 99], [25533, 74], [25637, 16], [25685, 98], [25832, 75], [25937, 16], [25984, 99], [26132, 75], [26236, 18], [26283, 100], [26431, 77], [26536, 18], [26583, 99], [26730, 79], [26835, 20], [26882, 100], [27030, 79], [27134, 22], [27181, 100], [27329, 81], [27434, 23], [27480, 101], [27629, 82], [27733, 24], [27779, 102], [27928, 85], [28031, 26], [28079, 101], [28227, 87], [28330, 28], [28378, 102], [28527, 89], [28627, 33], [28677, 102], [28826, 94], [28926, 36], [28974, 105], [29126, 95], [29226, 153], [29425, 96], [29526, 152], [29724, 95], [29831, 147], [30024, 92], [30133, 144], [30323, 92], [30434, 143], [30623, 90], [30735, 141], [30922, 90], [31036, 140], [31221, 91], [31336, 140], [31521, 90], [31637, 138], [31820, 91], [31937, 138], [32120, 91], [32236, 138], [32419, 92], [32536, 138], [32718, 94], [32835, 139], [33018, 95], [33134, 139], [33317, 97], [33433, 140], [33617, 99], [33730, 142], [33916, 103], [34027, 145], [34215, 257], [34515, 256], [34814, 257], [35114, 256], [35413, 257], [35712, 257], [36012, 257], [36311, 258], [36611, 257], [36910, 258], [37209, 258], [37509, 258], [37808, 259], [38108, 258], [38407, 259], [38706, 259], [39006, 259], [39305, 260], [39605, 259], [39904, 260], [40203, 260], [40503, 260], [40802, 260], [41102, 260], [41401, 261], [41700, 261], [42000, 261], [42300, 261], [42600, 260], [42900, 260], [43200, 259], [43500, 259], [43800, 259], [44100, 86], [44187, 171], [44400, 85], [44487, 171], [44700, 85], [45000, 85], [45300, 85], [45600, 84], [45900, 84], [46200, 84], [46500, 84], [46800, 83], [47100, 83], [47400, 83], [47700, 83], [48000, 82], [48300, 82], [48600, 82], [48900, 82], [49200, 81], [49500, 81], [49800, 81], [50100, 81], [50400, 80], [50700, 80], [51000, 80], [51300, 80], [51600, 79], [51900, 79], [52200, 79], [52500, 79], [52800, 78], [53100, 78], [53400, 78], [53700, 78], [54000, 77], [54300, 77], [54600, 77], [54900, 77], [55200, 76], [55500, 76], [55800, 76], [56100, 76], [56400, 75], [56700, 75], [57000, 75], [57300, 75], [57600, 74], [57900, 74], [58200, 74], [58500, 74], [58800, 73], [59100, 73], [59400, 73], [59700, 73], [60000, 72], [60300, 72], [60600, 72], [60900, 72], [61200, 71], [61500, 71], [61800, 71], [62100, 71], [62400, 70], [62700, 70], [63000, 70], [63300, 70], [63600, 69], [63900, 69], [64200, 69], [64500, 69], [64800, 68], [65100, 68], [65400, 68], [65700, 68], [66000, 67], [66300, 67], [66600, 67], [66900, 67], [67200, 66], [67500, 66], [67800, 66], [68100, 66], [68400, 65], [68700, 65], [69000, 65], [69300, 65], [69600, 64], [69900, 64], [70200, 64], [70500, 64], [70800, 63], [71100, 63], [71400, 63], [71700, 63], [72000, 62], [72300, 62], [72600, 62], [72900, 62], [73200, 61], [73500, 61], [73800, 61], [74101, 60], [74402, 58], [74703, 57], [75004, 56], [75305, 55], [75606, 53], [75907, 52], [76208, 51], [76509, 50], [76809, 49], [77110, 48], [77411, 12], [77425, 33], [77712, 10], [77726, 32], [78013, 8], [78027, 30], [78314, 6], [78327, 30], [78615, 4], [78628, 29], [78929, 31], [79230, 32], [79531, 32], [79832, 24], [79857, 6], [80132, 24], [80158, 4], [80433, 22], [80734, 21], [81035, 20], [81336, 18]], "point": [149, 135]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-1.244, -1.244, 8.312, 8.312, 3.7060904, 3.7060904]], "forceVisible": true, "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-1.244, -1.244, 8.312, 8.312, 3.7060904, 3.7060904]], "forceVisible": true, "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+02.12|+00.60|-00.28"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [105, 61, 137, 114], "mask": [[18111, 20], [18409, 23], [18708, 25], [19007, 27], [19307, 27], [19606, 29], [19906, 29], [20206, 30], [20505, 31], [20805, 31], [21105, 32], [21405, 32], [21705, 32], [22005, 32], [22305, 33], [22605, 33], [22905, 33], [23205, 33], [23505, 33], [23805, 33], [24105, 33], [24406, 32], [24706, 32], [25006, 31], [25306, 31], [25607, 30], [25907, 30], [26207, 29], [26508, 28], [26809, 26], [27109, 25], [27410, 24], [27711, 22], [28013, 18], [28314, 16], [28616, 11], [28920, 6], [29221, 5], [29521, 5], [29819, 12], [30116, 17], [30415, 19], [30713, 22], [31012, 24], [31312, 24], [31611, 26], [31911, 26], [32211, 25], [32511, 25], [32812, 23], [33113, 21], [33414, 19], [33716, 14], [34019, 8]], "point": [121, 86]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 272], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14156, 243], [14455, 243], [14754, 244], [15054, 244], [15353, 244], [15653, 244], [15952, 244], [16251, 245], [16551, 245], [16850, 245], [17150, 245], [17449, 246], [17748, 246], [18048, 118], [18169, 125], [18347, 112], [18477, 116], [18647, 108], [18781, 112], [18946, 107], [19083, 109], [19245, 106], [19385, 107], [19545, 105], [19687, 105], [19844, 105], [19988, 103], [20144, 104], [20289, 102], [20443, 104], [20590, 100], [20742, 105], [20890, 100], [21042, 105], [21191, 99], [21341, 106], [21491, 98], [21641, 106], [21791, 98], [21940, 107], [22091, 97], [22239, 109], [22391, 97], [22539, 109], [22690, 98], [22838, 111], [22989, 98], [23138, 112], [23289, 98], [23437, 113], [23587, 99], [23736, 114], [23887, 99], [24036, 115], [24187, 98], [24335, 116], [24487, 98], [24635, 116], [24786, 99], [24934, 117], [25086, 98], [25233, 119], [25385, 99], [25533, 120], [25685, 98], [25832, 121], [25984, 99], [26132, 122], [26283, 100], [26431, 123], [26583, 99], [26730, 125], [26882, 100], [27030, 126], [27181, 100], [27329, 128], [27480, 101], [27629, 128], [27779, 102], [27928, 129], [28079, 101], [28227, 131], [28378, 102], [28527, 133], [28677, 102], [28826, 136], [28974, 105], [29126, 253], [29425, 253], [29724, 254], [30024, 253], [30323, 254], [30623, 253], [30922, 254], [31221, 255], [31521, 254], [31820, 255], [32120, 254], [32419, 255], [32718, 256], [33018, 255], [33317, 256], [33617, 255], [33916, 256], [34215, 257], [34515, 256], [34814, 257], [35114, 256], [35413, 257], [35712, 257], [36012, 257], [36311, 258], [36611, 257], [36910, 258], [37209, 258], [37509, 258], [37808, 259], [38108, 258], [38407, 259], [38706, 259], [39006, 259], [39305, 260], [39605, 259], [39904, 260], [40203, 260], [40503, 260], [40802, 260], [41102, 260], [41401, 261], [41700, 261], [42000, 261], [42300, 261], [42600, 260], [42900, 260], [43200, 259], [43500, 259], [43800, 259], [44100, 86], [44187, 171], [44400, 85], [44487, 171], [44700, 85], [45000, 85], [45300, 85], [45600, 84], [45900, 84], [46200, 84], [46500, 84], [46800, 83], [47100, 83], [47400, 83], [47700, 83], [48000, 82], [48300, 82], [48600, 82], [48900, 82], [49200, 81], [49500, 81], [49800, 81], [50100, 81], [50400, 80], [50700, 80], [51000, 80], [51300, 80], [51600, 79], [51900, 79], [52200, 79], [52500, 79], [52800, 78], [53100, 78], [53400, 78], [53700, 78], [54000, 77], [54300, 77], [54600, 77], [54900, 77], [55200, 76], [55500, 76], [55800, 76], [56100, 76], [56400, 75], [56700, 75], [57000, 75], [57300, 75], [57600, 74], [57900, 74], [58200, 74], [58500, 74], [58800, 73], [59100, 73], [59400, 73], [59700, 73], [60000, 72], [60300, 72], [60600, 72], [60900, 72], [61200, 71], [61500, 71], [61800, 71], [62100, 71], [62400, 70], [62700, 70], [63000, 70], [63300, 70], [63600, 69], [63900, 69], [64200, 69], [64500, 69], [64800, 68], [65100, 68], [65400, 68], [65700, 68], [66000, 67], [66300, 67], [66600, 67], [66900, 67], [67200, 66], [67500, 66], [67800, 66], [68100, 66], [68400, 65], [68700, 65], [69000, 65], [69300, 65], [69600, 64], [69900, 64], [70200, 64], [70500, 64], [70800, 63], [71100, 63], [71400, 63], [71700, 63], [72000, 62], [72300, 62], [72600, 62], [72900, 62], [73200, 61], [73500, 61], [73800, 61], [74101, 60], [74402, 58], [74703, 57], [75004, 56], [75305, 55], [75606, 53], [75907, 52], [76208, 51], [76509, 50], [76809, 49], [77110, 48], [77411, 12], [77425, 33], [77712, 10], [77726, 32], [78013, 8], [78027, 30], [78314, 6], [78327, 30], [78615, 4], [78628, 29], [78929, 31], [79230, 32], [79531, 32], [79832, 24], [79857, 6], [80132, 24], [80158, 4], [80433, 22], [80734, 21], [81035, 20], [81336, 18]], "point": [149, 135]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+01.76|+00.39|+02.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [33, 130, 145, 203], "mask": [[38733, 112], [39033, 112], [39333, 113], [39633, 113], [39934, 112], [40234, 112], [40534, 112], [40835, 111], [41135, 111], [41436, 110], [41736, 110], [42037, 109], [42337, 109], [42637, 109], [42938, 108], [43238, 108], [43539, 107], [43839, 107], [44139, 107], [44440, 106], [44740, 106], [45041, 105], [45341, 105], [45642, 104], [45942, 104], [46242, 104], [46543, 103], [46843, 103], [47144, 102], [47444, 102], [47745, 101], [48045, 101], [48345, 101], [48646, 100], [48946, 100], [49247, 99], [49547, 99], [49847, 99], [50148, 92], [50448, 87], [50749, 84], [51049, 82], [51350, 79], [51650, 77], [51950, 75], [52251, 72], [52551, 71], [52852, 69], [53152, 68], [53453, 66], [53753, 65], [54053, 64], [54354, 62], [54654, 61], [54955, 59], [55255, 59], [55555, 58], [55856, 56], [56156, 56], [56457, 54], [56757, 54], [57058, 52], [57358, 52], [57658, 51], [57959, 50], [58259, 49], [58560, 48], [58860, 48], [59161, 46], [59461, 46], [59761, 46], [60062, 44], [60362, 44], [60663, 42]], "point": [89, 165]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+02.12|+00.60|-00.28", "placeStationary": true, "receptacleObjectId": "Cabinet|+01.76|+00.39|+02.35"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [21, 130, 144, 289], "mask": [[38732, 2], [39030, 114], [39330, 5], [39336, 108], [39630, 114], [39929, 7], [39937, 107], [40229, 115], [40529, 115], [40829, 8], [40838, 107], [41129, 116], [41429, 9], [41439, 106], [41729, 116], [42029, 10], [42040, 105], [42329, 10], [42340, 105], [42629, 116], [42929, 11], [42941, 104], [43228, 117], [43528, 13], [43542, 103], [43828, 117], [44128, 117], [44428, 14], [44443, 102], [44728, 117], [45028, 15], [45044, 101], [45328, 117], [45628, 117], [45928, 16], [45945, 100], [46228, 117], [46527, 18], [46546, 99], [46827, 118], [47127, 118], [47427, 19], [47447, 98], [47727, 118], [48027, 20], [48048, 97], [48327, 118], [48627, 21], [48649, 96], [48927, 118], [49227, 118], [49527, 22], [49550, 95], [49826, 119], [50126, 24], [50151, 89], [50426, 109], [50726, 107], [51026, 25], [51052, 79], [51326, 103], [51626, 26], [51653, 74], [51926, 99], [52226, 97], [52526, 27], [52554, 68], [52826, 95], [53125, 29], [53155, 65], [53425, 94], [53725, 30], [53756, 62], [54025, 92], [54325, 91], [54625, 31], [54657, 58], [54925, 89], [55225, 32], [55258, 56], [55525, 88], [55825, 87], [56125, 33], [56159, 53], [56424, 87], [56724, 35], [56760, 51], [57024, 86], [57324, 86], [57624, 36], [57661, 48], [57924, 85], [58224, 37], [58262, 46], [58524, 84], [58824, 38], [58863, 45], [59124, 83], [59424, 83], [59723, 40], [59764, 43], [60023, 41], [60323, 41], [60623, 41], [60923, 41], [61223, 41], [61523, 41], [61823, 41], [62123, 42], [62423, 42], [62723, 42], [63022, 43], [63322, 43], [63622, 43], [63922, 43], [64222, 43], [64522, 43], [64822, 43], [65122, 43], [65422, 43], [65722, 43], [66022, 43], [66321, 44], [66621, 44], [66921, 44], [67221, 44], [67521, 44], [67821, 44], [68121, 44], [68421, 44], [68721, 44], [69021, 44], [69322, 43], [69622, 44], [69923, 43], [70224, 42], [70525, 41], [70825, 41], [71126, 40], [71427, 39], [71728, 38], [72028, 38], [72329, 37], [72630, 36], [72930, 36], [73231, 35], [73532, 34], [73833, 33], [74133, 33], [74434, 32], [74735, 31], [75035, 31], [75336, 30], [75637, 29], [75938, 28], [76238, 28], [76539, 27], [76840, 27], [77140, 27], [77441, 26], [77742, 25], [78043, 24], [78343, 24], [78644, 23], [78945, 22], [79245, 22], [79546, 21], [79847, 20], [80148, 19], [80448, 19], [80749, 18], [81050, 17], [81351, 16], [81651, 16], [81952, 15], [82253, 14], [82553, 14], [82854, 13], [83155, 12], [83456, 11], [83756, 11], [84057, 11], [84358, 10], [84658, 10], [84959, 9], [85260, 8], [85561, 7], [85861, 7], [86162, 6], [86463, 5]], "point": [82, 199]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+01.76|+00.39|+02.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [21, 130, 145, 289], "mask": [[38732, 2], [39030, 114], [39330, 5], [39336, 108], [39630, 114], [39929, 7], [39937, 107], [40229, 115], [40529, 115], [40829, 8], [40838, 107], [41129, 116], [41429, 9], [41439, 106], [41729, 116], [42029, 10], [42040, 105], [42329, 10], [42340, 105], [42629, 116], [42929, 11], [42941, 104], [43228, 117], [43528, 13], [43542, 103], [43828, 117], [44128, 117], [44428, 14], [44443, 102], [44728, 117], [45028, 15], [45044, 101], [45328, 117], [45628, 117], [45928, 16], [45945, 100], [46228, 117], [46527, 18], [46546, 99], [46827, 118], [47127, 118], [47427, 19], [47447, 98], [47727, 118], [48027, 20], [48048, 97], [48327, 118], [48627, 21], [48649, 96], [48927, 118], [49227, 118], [49527, 22], [49550, 95], [49826, 119], [50126, 24], [50151, 94], [50426, 119], [50726, 119], [51026, 25], [51052, 93], [51326, 75], [51408, 37], [51626, 26], [51653, 46], [51709, 36], [51926, 72], [52010, 35], [52226, 72], [52311, 34], [52526, 27], [52554, 43], [52611, 34], [52826, 71], [52912, 33], [53125, 29], [53155, 41], [53212, 33], [53425, 71], [53512, 33], [53725, 30], [53756, 40], [53812, 33], [54025, 71], [54112, 33], [54325, 72], [54412, 33], [54625, 31], [54657, 40], [54712, 33], [54925, 72], [55012, 33], [55225, 32], [55258, 40], [55311, 34], [55525, 73], [55611, 34], [55825, 74], [55912, 34], [56125, 33], [56159, 42], [56213, 33], [56424, 76], [56513, 33], [56724, 35], [56760, 40], [56813, 33], [57024, 76], [57113, 33], [57324, 76], [57412, 34], [57624, 36], [57661, 40], [57711, 35], [57924, 78], [58010, 36], [58224, 37], [58262, 41], [58308, 38], [58524, 122], [58824, 38], [58863, 83], [59124, 122], [59424, 122], [59723, 40], [59764, 82], [60023, 41], [60323, 41], [60623, 41], [60923, 41], [61223, 41], [61523, 41], [61823, 41], [62123, 42], [62423, 42], [62723, 42], [63022, 43], [63322, 43], [63622, 43], [63922, 43], [64222, 43], [64522, 43], [64822, 43], [65122, 43], [65422, 43], [65722, 43], [66022, 43], [66321, 44], [66621, 44], [66921, 44], [67221, 44], [67521, 44], [67821, 44], [68121, 44], [68421, 44], [68721, 44], [69021, 44], [69322, 43], [69622, 44], [69923, 43], [70224, 42], [70525, 41], [70825, 41], [71126, 40], [71427, 39], [71728, 38], [72028, 38], [72329, 37], [72630, 36], [72930, 36], [73231, 35], [73532, 34], [73833, 33], [74133, 33], [74434, 32], [74735, 31], [75035, 31], [75336, 30], [75637, 29], [75938, 28], [76238, 28], [76539, 27], [76840, 27], [77140, 27], [77441, 26], [77742, 25], [78043, 24], [78343, 24], [78644, 23], [78945, 22], [79245, 22], [79546, 21], [79847, 20], [80148, 19], [80448, 19], [80749, 18], [81050, 17], [81351, 16], [81651, 16], [81952, 15], [82253, 14], [82553, 14], [82854, 13], [83155, 12], [83456, 11], [83756, 11], [84057, 11], [84358, 10], [84658, 10], [84959, 9], [85260, 8], [85561, 7], [85861, 7], [86162, 6], [86463, 5]], "point": [83, 199]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan27", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": 0.5, "y": 0.9010001, "z": 1.75}, "object_poses": [{"objectName": "Potato_d7da2d9d", "position": {"x": 2.00076771, "y": 1.36164212, "z": -0.09774995}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "WineBottle_2a35bd39", "position": {"x": -0.27265203, "y": 0.7918924, "z": 1.38609231}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_6b8b37c4", "position": {"x": -0.0429645181, "y": 0.790531039, "z": 1.049764}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a2d86e36", "position": {"x": -0.272034049, "y": 0.937742054, "z": 2.53054}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_a2dda372", "position": {"x": 1.29829383, "y": 0.08108717, "z": 2.52336955}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_9b26f0a9", "position": {"x": -0.1191458, "y": 0.781269, "z": 0.54523927}, "rotation": {"x": 359.722931, "y": 0.000182074858, "z": 359.872375}}, {"objectName": "Fork_3133524b", "position": {"x": 0.00205624476, "y": 0.746872, "z": 1.99347425}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_3133524b", "position": {"x": -0.349214524, "y": 0.79151535, "z": 1.049764}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_343518b0", "position": {"x": 0.033597976, "y": 0.8332674, "z": 0.8815999}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7054e289", "position": {"x": 2.223756, "y": 1.32172918, "z": -0.0977503359}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_7054e289", "position": {"x": 2.13735771, "y": 0.8591789, "z": -0.188374951}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_7054e289", "position": {"x": 2.11626387, "y": 0.8591789, "z": -0.007124841}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_540cf8b1", "position": {"x": -0.03310001, "y": 0.7719418, "z": 1.92550552}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_e54ab207", "position": {"x": -0.295327067, "y": 0.038132526, "z": -0.7693271}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_fac53a9c", "position": {"x": -0.0429645181, "y": 0.8372559, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_fac53a9c", "position": {"x": 0.060256362, "y": 0.983992159, "z": 2.82771134}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": -0.362221032, "y": 1.0325377, "z": 2.068165}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": 2.16706157, "y": 1.945743, "z": 1.6589483}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8e528c3b", "position": {"x": 2.1481626, "y": 0.9333, "z": 0.3613872}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": 2.22505331, "y": 0.933299959, "z": 0.8060128}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_598ee640", "position": {"x": 0.033597976, "y": 0.8527509, "z": 0.713435769}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_598ee640", "position": {"x": -0.27265203, "y": 0.8527478, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_5c2a3ea2", "position": {"x": 2.244174, "y": 1.49634814, "z": 2.10403252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_92427322", "position": {"x": 2.0598495, "y": 0.38864404, "z": -0.279}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_92427322", "position": {"x": 0.033597976, "y": 0.840994835, "z": 0.545271635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_343518b0", "position": {"x": -0.118662715, "y": 0.980000556, "z": 2.4780035}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_598ee640", "position": {"x": 2.168009, "y": 1.38319755, "z": -0.369625032}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Fork_3133524b", "position": {"x": 0.6826235, "y": 0.795938134, "z": 2.58472013}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "SoapBottle_e54ab207", "position": {"x": 2.31851387, "y": 1.50431848, "z": 0.448988259}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Ladle_158e5727", "position": {"x": 0.060256362, "y": 0.9820624, "z": 2.61821938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_741f0242", "position": {"x": 1.85013545, "y": 0.791169465, "z": 1.92287481}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_4be1a058", "position": {"x": -0.199893579, "y": 0.8239382, "z": 0.813959}, "rotation": {"x": 0.016275553, "y": 0.0151319923, "z": 359.887421}}, {"objectName": "SaltShaker_8e528c3b", "position": {"x": 0.4950477, "y": 0.07641101, "z": 2.48426819}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_d7da2d9d", "position": {"x": -0.0429645181, "y": 0.831192434, "z": 1.38609231}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_f15c1655", "position": {"x": 2.1402, "y": 0.9351, "z": 1.5045}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "WineBottle_2a35bd39", "position": {"x": -0.425777048, "y": 0.7918924, "z": 0.713435769}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_db580e26", "position": {"x": 1.99756, "y": 0.95269984, "z": 1.9649893}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_540cf8b1", "position": {"x": 0.033597976, "y": 0.8165851, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_fac53a9c", "position": {"x": 2.20738077, "y": 0.648705661, "z": -0.46025005}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_92427322", "position": {"x": 1.17096, "y": 0.8454166, "z": 2.52944}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a2d86e36", "position": {"x": -0.119527027, "y": 0.7910089, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_a2dda372", "position": {"x": 1.85448289, "y": 0.9379, "z": 1.497}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": -0.119527027, "y": 0.785966754, "z": 0.3771075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7054e289", "position": {"x": 2.12213445, "y": 0.600678861, "z": -0.278999984}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_6b8b37c4", "position": {"x": 1.84060025, "y": 0.937864244, "z": 0.3613872}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_922e45ca", "position": {"x": -0.3120002, "y": 0.7810681, "z": 0.4090001}, "rotation": {"x": -0.0017186678, "y": 0.000224735908, "z": 0.000681249949}}, {"objectName": "Spoon_9b26f0a9", "position": {"x": 1.22382593, "y": 0.796517968, "z": 2.58472013}, "rotation": {"x": -1.40334191e-14, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_5c2a3ea2", "position": {"x": 2.28230643, "y": 1.49635053, "z": 2.3815527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": 2.07607532, "y": 1.51599824, "z": -0.278999984}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1809377900, "scene_num": 27}, "task_id": "trial_T20190908_123907_725964", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2KAGFQU28JY43_386CSBG1O2DG6W5O30508KI0YZDQ6J", "high_descs": ["Go across the room and turn left at the wall, then go to the refrigerator ahead of you.", "Pull out the wine glass from the middle of the top shelf in front of you. ", "Turn to your left and go to the sink across from you, then turn right and go to the microwave, on the counter, across from you. ", "Place the wine glass in the microwave, heat it up, then remove it from the microwave. ", "Turn around and go to the bottom cabinet, to the left of the stove. ", "Place the glass in the middle, front of the bottom cabinet, to the left of the stove. "], "task_desc": "Place a warm glass in the cabinet. ", "votes": [1, 1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3GFK2QRXXC8P7P1WAQU183MMQ49W51", "high_descs": ["Walk to the wall then turn left and walk to the fridge.", "Open the fridge and get the wine glass that's in there before closing the door.", "Turn left and take a step to your left then walk to the sink then turn left and walk to the microwave.", "Warm the wine glass in the microwave then take it back out and close the door.", "Turn around and take a small step to your right then a small step forward.", "Open the cabinet to the left of the stove under the drawer and put the wine glass in there before closing the cabinet door again."], "task_desc": "Put a warm wine glass in the cabinet.", "votes": [1, 1, 1]}, {"assignment_id": "A1OWHPMKE7YAGL_30LB5CDZNF1U6DK0O78WQCKDQYN0ZV", "high_descs": ["Head across the room, turn to the left and go to the fridge. ", "Remove the wine glass from the fridge.", "Turn to the left, go across the room to the sink, turn to the left and go to the microwave.", "Put the wine glass inside the microwave and turn it on, remove the wine glass once it is hot.", "Turn around and go to the cabinet across the room to the left of the stove.", "Put the wine glass in the bottom cabinet to the left of the stove."], "task_desc": "Put a hot wine glass in a cabinet.", "votes": [1, 0, 1]}]}}