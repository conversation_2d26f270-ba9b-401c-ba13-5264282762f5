{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 21}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 22}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 50}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 50}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-9|-4|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-8.8899994, -8.8899994, -6.53950548, -6.53950548, 3.263859748, 3.263859748]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-8.54, -8.54, -6.764, -6.764, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|-02.22|+00.82|-01.63"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-4|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-3|2|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "garbagecan"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-8.8899994, -8.8899994, -6.53950548, -6.53950548, 3.263859748, 3.263859748]], "coordinateReceptacleObjectId": ["GarbageCan", [-1.448, -1.448, 2.156, 2.156, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|-02.22|+00.82|-01.63", "receptacleObjectId": "GarbageCan|-00.36|+00.00|+00.54"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.14|+00.00|-01.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 235], "mask": [[0, 26999], [27000, 298], [27300, 298], [27600, 297], [27900, 296], [28200, 295], [28500, 295], [28800, 294], [29100, 293], [29400, 293], [29700, 292], [30000, 291], [30300, 291], [30600, 290], [30900, 289], [31200, 288], [31500, 288], [31800, 287], [32100, 286], [32400, 286], [32700, 285], [33000, 284], [33300, 283], [33600, 283], [33900, 282], [34200, 281], [34500, 281], [34800, 280], [35100, 279], [35400, 278], [35700, 278], [36000, 277], [36300, 276], [36600, 276], [36900, 275], [37200, 274], [37500, 274], [37800, 273], [38100, 272], [38400, 271], [38700, 271], [39000, 270], [39300, 269], [39600, 269], [39900, 268], [40200, 267], [40500, 266], [40800, 266], [41100, 265], [41400, 264], [41700, 264], [42000, 263], [42300, 262], [42600, 261], [42900, 261], [43200, 260], [43500, 259], [43800, 259], [44100, 258], [44400, 257], [44700, 257], [45000, 256], [45300, 255], [45600, 254], [45900, 254], [46200, 253], [46500, 252], [46800, 252], [47100, 251], [47400, 250], [47700, 249], [48000, 249], [48300, 248], [48600, 247], [48900, 247], [49200, 246], [49500, 245], [49800, 244], [50100, 244], [50400, 243], [50700, 242], [51000, 242], [51300, 241], [51600, 240], [51900, 240], [52200, 239], [52500, 238], [52801, 236], [53102, 235], [53403, 233], [53704, 231], [54006, 229], [54307, 227], [54608, 225], [54909, 223], [55210, 222], [55512, 219], [55813, 217], [56114, 216], [56415, 214], [56716, 212], [57018, 210], [57319, 208], [57620, 206], [57921, 204], [58222, 203], [58524, 200], [58825, 198], [59126, 197], [59427, 195], [59729, 192], [60030, 190], [60331, 189], [60632, 187], [60933, 185], [61235, 180], [61536, 178], [61837, 177], [62138, 175], [62439, 173], [62741, 171], [63042, 169], [63343, 167], [63644, 166], [63945, 165], [64246, 164], [64547, 162], [64847, 162], [65148, 161], [65450, 159], [65751, 157], [66052, 155], [66354, 153], [66655, 151], [66956, 149], [67257, 147], [67561, 140], [67864, 133], [68167, 127], [68470, 121], [68774, 113], [69080, 101], [69385, 91], [69692, 76], [70000, 61], [70310, 41]], "point": [149, 117]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-02.22|+00.82|-01.63"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [134, 96, 150, 114], "mask": [[28641, 3], [28939, 8], [29237, 11], [29537, 12], [29836, 13], [30135, 15], [30435, 15], [30735, 16], [31034, 17], [31334, 17], [31634, 17], [31934, 17], [32235, 16], [32535, 16], [32835, 15], [33136, 14], [33437, 12], [33738, 10], [34040, 6]], "point": [142, 104]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.14|+00.00|-01.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 694], [729, 265], [1029, 265], [1329, 265], [1629, 265], [1929, 265], [2229, 266], [2529, 266], [2829, 266], [3129, 267], [3428, 268], [3728, 268], [4028, 268], [4328, 269], [4628, 269], [4928, 270], [5227, 271], [5527, 272], [5827, 272], [6127, 273], [6426, 275], [6726, 275], [7026, 276], [7325, 278], [7625, 278], [7924, 281], [8223, 284], [8521, 288], [8819, 293], [9117, 2282], [11400, 299], [11700, 298], [12000, 298], [12300, 297], [12600, 297], [12900, 296], [13200, 295], [13500, 295], [13800, 294], [14100, 294], [14400, 293], [14700, 293], [15000, 292], [15300, 291], [15600, 291], [15900, 290], [16200, 290], [16500, 289], [16800, 289], [17100, 288], [17400, 287], [17700, 287], [18000, 286], [18300, 286], [18600, 285], [18900, 285], [19200, 284], [19500, 283], [19800, 283], [20100, 282], [20400, 282], [20700, 281], [21000, 281], [21300, 280], [21600, 280], [21900, 279], [22200, 278], [22500, 278], [22800, 277], [23100, 277], [23400, 276], [23700, 276], [24000, 275], [24300, 274], [24600, 274], [24900, 273], [25200, 273], [25500, 272], [25800, 272], [26100, 271], [26400, 270], [26700, 270], [27000, 269], [27300, 269], [27600, 268], [27900, 268], [28200, 267], [28500, 266], [28800, 266], [29100, 265], [29400, 265], [29700, 264], [30000, 105], [30106, 3], [30113, 151], [30300, 102], [30406, 3], [30413, 3], [30417, 146], [30600, 98], [30699, 3], [30706, 3], [30713, 3], [30720, 142], [30900, 97], [30999, 3], [31006, 3], [31013, 3], [31020, 142], [31200, 95], [31299, 3], [31306, 3], [31313, 3], [31320, 141], [31500, 95], [31599, 3], [31606, 3], [31613, 3], [31620, 3], [31624, 137], [31800, 95], [31899, 3], [31906, 3], [31913, 3], [31920, 2], [31925, 135], [32100, 95], [32199, 3], [32206, 3], [32213, 3], [32220, 2], [32226, 134], [32400, 91], [32492, 3], [32499, 2], [32506, 2], [32513, 3], [32519, 3], [32526, 133], [32700, 90], [32792, 3], [32798, 3], [32805, 3], [32813, 3], [32819, 3], [32826, 132], [33000, 89], [33092, 3], [33098, 3], [33105, 3], [33113, 2], [33119, 3], [33126, 132], [33300, 89], [33391, 3], [33398, 3], [33405, 3], [33412, 3], [33419, 3], [33426, 131], [33600, 88], [33691, 3], [33698, 3], [33705, 3], [33712, 3], [33719, 3], [33726, 131], [33900, 88], [33991, 3], [33998, 3], [34005, 3], [34012, 3], [34019, 3], [34026, 130], [34200, 88], [34291, 3], [34298, 3], [34305, 3], [34312, 3], [34319, 3], [34326, 130], [34500, 87], [34591, 3], [34598, 3], [34605, 3], [34612, 3], [34619, 3], [34626, 129], [34800, 87], [34891, 3], [34898, 3], [34905, 3], [34912, 3], [34919, 3], [34926, 128], [35100, 87], [35191, 3], [35197, 3], [35205, 3], [35212, 3], [35219, 3], [35226, 128], [35400, 87], [35490, 3], [35497, 3], [35504, 3], [35512, 3], [35519, 3], [35526, 127], [35700, 253], [36000, 252], [36300, 252], [36600, 251], [36900, 250], [37200, 89], [37326, 124], [37500, 90], [37625, 124], [37800, 90], [37925, 124], [38100, 91], [38224, 124], [38400, 92], [38523, 125], [38700, 93], [38822, 125], [39000, 94], [39121, 125], [39300, 95], [39419, 127], [39600, 97], [39718, 127], [39900, 99], [40015, 130], [40200, 102], [40312, 132], [40500, 244], [40800, 243], [41100, 243], [41400, 242], [41700, 241], [42000, 241], [42300, 240], [42600, 240], [42900, 239], [43200, 239], [43500, 238], [43800, 237], [44100, 237], [44400, 236], [44700, 236], [45000, 235], [45300, 235], [45600, 234], [45900, 233], [46200, 233], [46500, 232], [46800, 232], [47100, 231], [47400, 231], [47700, 230], [48000, 229], [48300, 229], [48600, 228], [48900, 228], [49200, 227], [49500, 227], [49800, 226], [50100, 225], [50400, 225], [50700, 224], [51000, 224], [51300, 223], [51600, 223], [51900, 222], [52200, 221], [52500, 221], [52800, 220], [53100, 220], [53400, 219], [53700, 219], [54000, 218], [54300, 217], [54600, 217], [54900, 216], [55200, 216], [55500, 215], [55800, 215], [56100, 214], [56400, 213], [56700, 213], [57000, 212], [57300, 212], [57600, 211], [57900, 211], [58200, 210], [58500, 209], [58800, 209], [59100, 208], [59400, 208], [59700, 52], [59753, 154], [60000, 51], [60053, 154], [60300, 51], [60353, 154], [60600, 51], [60652, 155], [60900, 51], [60952, 156], [61200, 50], [61252, 156], [61500, 50], [61552, 156], [61800, 50], [61852, 156], [62100, 50], [62151, 157], [62400, 50], [62451, 157], [62700, 49], [62751, 157], [63000, 49], [63051, 157], [63300, 49], [63351, 157], [63600, 49], [63651, 158], [63900, 49], [63950, 159], [64200, 48], [64250, 159], [64500, 48], [64550, 159], [64800, 48], [64850, 159], [65100, 48], [65150, 159], [65400, 48], [65450, 159], [65700, 47], [65751, 157], [66000, 47], [66052, 155], [66300, 47], [66354, 153], [66600, 47], [66655, 151], [66900, 47], [66956, 149], [67200, 46], [67257, 147], [67500, 46], [67561, 140], [67800, 46], [67864, 133], [68100, 46], [68167, 127], [68400, 46], [68470, 121], [68700, 45], [68774, 113], [69000, 45], [69080, 101], [69300, 45], [69385, 91], [69600, 45], [69692, 76], [69900, 45], [70000, 61], [70200, 44], [70310, 41], [70500, 44], [70800, 44], [71100, 44], [71400, 43], [71700, 43], [72000, 43], [72300, 43], [72600, 43], [72900, 42], [73200, 42], [73500, 42], [73800, 42], [74100, 42], [74400, 41], [74700, 41], [75000, 41], [75300, 41], [75600, 41], [75900, 40], [76200, 40], [76500, 40], [76800, 40], [77100, 40], [77400, 39], [77700, 39], [78000, 39], [78300, 39], [78600, 39], [78900, 38], [79200, 38], [79500, 38], [79800, 38], [80100, 37], [80400, 37], [80700, 37], [81000, 37], [81300, 37], [81600, 36], [81900, 36], [82200, 36], [82500, 36], [82800, 36], [83100, 35], [83400, 35], [83700, 35], [84000, 35], [84300, 35], [84600, 34], [84900, 34], [85200, 34], [85500, 34], [85800, 34], [86100, 33], [86400, 33], [86700, 33], [87000, 33], [87300, 33], [87600, 32], [87900, 32], [88200, 32], [88500, 32], [88800, 32], [89100, 31], [89400, 31], [89700, 31]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-02.22|+00.82|-01.63", "placeStationary": true, "receptacleObjectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 271], [28517, 271], [28816, 271], [29115, 272], [29415, 273], [29714, 274], [30013, 275], [30313, 275], [30612, 276], [30911, 277], [31211, 277], [31510, 278], [31809, 279], [32109, 279], [32408, 280], [32708, 280], [33007, 280], [33306, 281], [33606, 281], [33905, 281], [34204, 282], [34504, 282], [34803, 282], [35102, 283], [35402, 282], [35701, 283], [36000, 283], [36300, 283], [36600, 282], [36900, 282], [37200, 281], [37500, 281], [37800, 280], [38100, 280], [38400, 280], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 271], [28517, 271], [28816, 271], [29115, 272], [29415, 273], [29714, 274], [30013, 275], [30313, 275], [30612, 276], [30911, 277], [31211, 277], [31510, 278], [31809, 279], [32109, 130], [32246, 142], [32408, 130], [32547, 141], [32708, 128], [32848, 140], [33007, 129], [33149, 138], [33306, 129], [33450, 137], [33606, 128], [33751, 136], [33905, 129], [34051, 135], [34204, 129], [34352, 134], [34504, 129], [34652, 134], [34803, 130], [34952, 133], [35102, 131], [35252, 133], [35402, 131], [35552, 132], [35701, 132], [35852, 132], [36000, 133], [36152, 131], [36300, 133], [36452, 131], [36600, 133], [36752, 130], [36900, 134], [37051, 131], [37200, 134], [37351, 130], [37500, 135], [37650, 131], [37800, 136], [37949, 131], [38100, 137], [38248, 132], [38400, 139], [38546, 134], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-02.22|+00.82|-01.63"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [133, 108, 151, 129], "mask": [[32239, 7], [32538, 9], [32836, 12], [33136, 13], [33435, 15], [33734, 17], [34034, 17], [34333, 19], [34633, 19], [34933, 19], [35233, 19], [35533, 19], [35833, 19], [36133, 19], [36433, 19], [36733, 19], [37034, 17], [37334, 17], [37635, 15], [37936, 13], [38237, 11], [38539, 7]], "point": [142, 117]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 269], [25822, 270], [26122, 269], [26421, 270], [26720, 270], [27020, 270], [27319, 271], [27619, 270], [27918, 271], [28217, 271], [28517, 271], [28816, 271], [29115, 272], [29415, 273], [29714, 274], [30013, 275], [30313, 275], [30612, 276], [30911, 277], [31211, 277], [31510, 278], [31809, 279], [32109, 279], [32408, 280], [32708, 280], [33007, 280], [33306, 281], [33606, 281], [33905, 281], [34204, 282], [34504, 282], [34803, 282], [35102, 283], [35402, 282], [35701, 283], [36000, 283], [36300, 283], [36600, 282], [36900, 282], [37200, 281], [37500, 281], [37800, 280], [38100, 280], [38400, 280], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-02.22|+00.82|-01.63", "placeStationary": true, "receptacleObjectId": "GarbageCan|-00.36|+00.00|+00.54"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [76, 153, 202, 244], "mask": [[45695, 92], [45991, 100], [46289, 104], [46588, 106], [46887, 108], [47187, 108], [47486, 110], [47785, 111], [48085, 112], [48384, 113], [48684, 113], [48984, 114], [49284, 114], [49584, 114], [49884, 114], [50183, 115], [50483, 115], [50783, 115], [51083, 115], [51383, 115], [51683, 115], [51983, 116], [52282, 117], [52582, 117], [52882, 117], [53182, 117], [53482, 117], [53782, 117], [54082, 117], [54381, 118], [54681, 118], [54981, 119], [55281, 119], [55581, 119], [55881, 119], [56181, 119], [56480, 120], [56780, 120], [57080, 120], [57380, 120], [57680, 120], [57980, 121], [58280, 121], [58579, 122], [58879, 122], [59179, 122], [59479, 122], [59779, 122], [60079, 122], [60379, 122], [60678, 124], [60978, 124], [61278, 124], [61578, 124], [61878, 124], [62178, 124], [62478, 124], [62777, 125], [63077, 125], [63377, 125], [63677, 126], [63977, 126], [64277, 126], [64577, 126], [64876, 127], [65176, 127], [65476, 127], [65776, 127], [66077, 125], [66377, 125], [66677, 125], [66978, 123], [67278, 123], [67578, 123], [67879, 121], [68179, 121], [68481, 118], [68783, 114], [69085, 110], [69387, 107], [69687, 107], [69988, 105], [70291, 100], [70593, 96], [70895, 51], [70953, 34], [71198, 45], [71257, 28], [71500, 41], [71559, 24], [71808, 31], [71861, 15], [72111, 27], [72162, 12], [72413, 23], [72464, 9], [72714, 21], [72765, 7], [73017, 17], [73066, 3]], "point": [139, 197]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan11", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 1.25, "y": 0.9009992, "z": -0.75}, "object_poses": [{"objectName": "Kettle_7922e01e", "position": {"x": -0.175111651, "y": 0.9133413, "z": -1.48903823}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_483d2614", "position": {"x": -2.340023, "y": 0.9820612, "z": 0.7048946}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_483d2614", "position": {"x": 0.3000576, "y": 0.942136168, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": 1.78509033, "y": 0.9118642, "z": 0.4581}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": -2.47704625, "y": 0.95178926, "z": 0.614945531}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "ButterKnife_d4975865", "position": {"x": -2.340023, "y": 0.9522671, "z": 0.435047418}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_da74902f", "position": {"x": 1.0071, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": -1.92895365, "y": 0.9527736, "z": 0.614945531}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": 0.3419488, "y": 0.783435047, "z": -1.64893007}, "rotation": {"x": 0.0, "y": 180.0, "z": -1.40334225e-14}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": -2.340023, "y": 0.9672249, "z": 0.165200233}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": 0.8049864, "y": 0.9547133, "z": -1.71562362}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": 0.9603605, "y": 0.9547133, "z": -1.84852147}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": 1.84613431, "y": 0.9131588, "z": 0.62386173}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_b9b64351", "position": {"x": 0.0353682637, "y": 0.9376496, "z": 0.5409809}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": -0.09697643, "y": 0.961345851, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": 0.445706218, "y": 0.109618187, "z": 0.270902365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": -2.0475, "y": 0.5107337, "z": -1.76346016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": 0.6800812, "y": 0.106492281, "z": 0.270902365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": 0.3000576, "y": 0.9086062, "z": 0.292338252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": -2.203, "y": 1.04246557, "z": 0.255149245}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": -0.175111651, "y": 1.00254059, "z": -1.82056189}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": 0.6189993, "y": 0.945, "z": 0.469800025}, "rotation": {"x": 0.0, "y": 180.00032, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": 0.649612367, "y": 0.91455, "z": -1.582726}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_41d4caea", "position": {"x": -2.61406946, "y": 1.0193764, "z": 0.435047448}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": 1.29989243, "y": 0.978749633, "z": -1.53264427}, "rotation": {"x": -8.506719e-06, "y": 4.352369e-06, "z": -2.14586034e-05}}, {"objectName": "Knife_b9b64351", "position": {"x": 1.658355, "y": 0.9376496, "z": -1.48903823}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": -2.47704625, "y": 0.9527736, "z": 0.345098346}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": 1.82812929, "y": 1.32816672, "z": -1.77289879}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Kettle_7922e01e", "position": {"x": -0.4519031, "y": 0.9133413, "z": -1.6548}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": 0.377126157, "y": 0.7748355, "z": -1.7455647}, "rotation": {"x": 1.40334191e-14, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": 1.9071784, "y": 0.961345851, "z": 0.292338252}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": 1.903969, "y": 0.910996437, "z": -1.48903823}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": -1.79193056, "y": 0.9530838, "z": 0.435047358}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_da74902f", "position": {"x": 1.361, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": 1.658355, "y": 0.927299857, "z": -1.73768091}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_54f2d061", "position": {"x": -2.61406946, "y": 1.05651045, "z": 0.165200263}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": -2.22249985, "y": 0.815964937, "z": -1.63487637}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": 0.851057053, "y": 1.0014, "z": -1.53786421}, "rotation": {"x": 0.0, "y": 231.4903, "z": 0.0}}, {"objectName": "SaltShaker_42ce5824", "position": {"x": 1.658355, "y": 0.9086062, "z": -1.82056189}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": -1.92895365, "y": 0.9485312, "z": 0.2551492}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_41d4caea", "position": {"x": -2.135, "y": 1.11895287, "z": -1.76346016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_d4975865", "position": {"x": -2.06597686, "y": 0.9522671, "z": 0.4350474}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_483d2614", "position": {"x": -1.79193056, "y": 0.9820612, "z": 0.2551492}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_4b842b85", "position": {"x": 1.022105, "y": 0.9591456, "z": 0.288545281}, "rotation": {"x": 0.0, "y": 219.19072, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": -2.61406946, "y": 0.95178926, "z": 0.614945531}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": -1.79193056, "y": 0.9533544, "z": 0.6149455}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": 0.6496123, "y": 0.91455, "z": -1.71562362}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1337681359, "scene_num": 11}, "task_id": "trial_T20190908_220027_082582", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AM2KK02JXXW48_3HRMW88U19HU76LH9II2IAO4BY3M06", "high_descs": ["Turn around, move to the fridge across the room on the left. ", "Take the egg out of the fridge. ", "Bring the egg to the microwave left of the fridge. ", "Heat the egg in the microwave. ", "Bring the heated egg to the green recycling been behind you.", "Put the heated egg in the recycling bin."], "task_desc": "Put a heated egg in the recycling bin. ", "votes": [1, 1]}, {"assignment_id": "A255A9FFZD8PQW_3OUYGIZWRAP0S9IU1YAUZEIZG4R0PV", "high_descs": ["Turn around and walk to the fridge. ", "Take out an egg from the fridge. ", "Turn left then right and face the microwave.", "Heat the egg in the microwave and take it out. ", "Turn around completely then turn to the right to face a green bin.", "Place the egg in the green bin."], "task_desc": "Heat an egg and place in a green bin. ", "votes": [1, 1]}, {"assignment_id": "A2KAGFQU28JY43_3YJ6NA41JE77UFJGN6K0HJXVFCMJP8", "high_descs": ["Turn around and go to the refrigerator at the end of the kitchen, on the right. ", "Remove the egg from the second shelf of the refrigerator. ", "Turn to your left and go to the microwave on the counter, to your right, just past the refrigerator. ", "Place the egg in the microwave, heat it up, then remove the egg from the microwave. ", "Turn around and go to the green recycle bin on the floor, next to the kitchen counter. ", "Put the egg in the green recycle bin. "], "task_desc": "Put a warm egg in the recycle bin. ", "votes": [1, 1]}]}}