{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 12}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 39}, {"high_idx": 5, "image_name": "000000244.png", "low_idx": 39}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "<PERSON>g", "parent_target": "CoffeeMachine", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-2|-4|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["mug"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["<PERSON>g", [-1.773451448, -1.773451448, -6.03480052, -6.03480052, 0.42354226, 0.42354226]], "coordinateReceptacleObjectId": ["Cabinet", [-1.586, -1.586, -5.40400028, -5.40400028, 1.610000132, 1.610000132]], "forceVisible": true, "objectId": "Mug|-00.44|+00.11|-01.51"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-4|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["mug"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}}, {"discrete_action": {"action": "GotoLocation", "args": ["coffeemachine"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|2|-1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["mug", "coffeemachine"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["<PERSON>g", [-1.773451448, -1.773451448, -6.03480052, -6.03480052, 0.42354226, 0.42354226]], "coordinateReceptacleObjectId": ["CoffeeMachine", [2.476, 2.476, 2.4, 2.4, 3.6, 3.6]], "forceVisible": true, "objectId": "Mug|-00.44|+00.11|-01.51", "receptacleObjectId": "CoffeeMachine|+00.62|+00.90|+00.60"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.40|+00.40|-01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [121, 186, 228, 229], "mask": [[55621, 107], [55921, 108], [56221, 108], [56521, 108], [56821, 108], [57121, 107], [57422, 105], [57722, 105], [58022, 104], [58322, 103], [58623, 101], [58923, 101], [59223, 100], [59523, 99], [59824, 98], [60124, 97], [60424, 96], [60724, 95], [61025, 94], [61325, 93], [61625, 92], [61925, 92], [62226, 90], [62526, 89], [62826, 88], [63127, 87], [63427, 86], [63727, 85], [64027, 85], [64328, 83], [64628, 82], [64928, 81], [65228, 81], [65529, 79], [65829, 78], [66129, 78], [66429, 77], [66730, 75], [67030, 74], [67330, 74], [67630, 73], [67931, 71], [68231, 71], [68531, 70]], "point": [174, 206]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-00.44|+00.11|-01.51"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [132, 188, 150, 207], "mask": [[56237, 6], [56535, 10], [56834, 12], [57133, 14], [57433, 14], [57732, 16], [58032, 16], [58332, 19], [58632, 19], [58932, 15], [58949, 2], [59232, 18], [59533, 16], [59833, 15], [60133, 14], [60434, 13], [60734, 13], [61034, 12], [61335, 11], [61636, 8], [61938, 5]], "point": [141, 196]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.40|+00.40|-01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [121, 186, 248, 300], "mask": [[55621, 106], [55922, 108], [56222, 108], [56522, 109], [56822, 109], [57123, 108], [57423, 108], [57723, 108], [58023, 108], [58324, 108], [58624, 108], [58924, 108], [59224, 108], [59525, 107], [59825, 107], [60125, 108], [60425, 108], [60726, 107], [61026, 107], [61326, 47], [61376, 57], [61626, 46], [61677, 56], [61927, 45], [61978, 56], [62227, 44], [62278, 56], [62527, 21], [62552, 18], [62578, 56], [62827, 13], [62860, 10], [62878, 56], [63128, 10], [63162, 7], [63178, 56], [63428, 7], [63465, 4], [63478, 56], [63728, 5], [63767, 2], [63778, 57], [64028, 3], [64078, 57], [64377, 58], [64677, 58], [64975, 60], [65275, 60], [65576, 60], [65877, 59], [66178, 58], [66479, 57], [66780, 56], [67080, 56], [67381, 56], [67682, 55], [67999, 38], [68298, 39], [68598, 39], [68899, 38], [69199, 39], [69499, 39], [69799, 39], [70099, 39], [70399, 39], [70699, 39], [70999, 40], [71299, 40], [71599, 40], [71899, 40], [72200, 39], [72500, 39], [72800, 40], [73100, 40], [73400, 40], [73700, 40], [74000, 40], [74300, 40], [74600, 41], [74900, 41], [75200, 41], [75501, 40], [75801, 40], [76101, 40], [76401, 41], [76701, 41], [77001, 41], [77301, 41], [77601, 41], [77901, 41], [78201, 42], [78501, 42], [78802, 41], [79102, 41], [79402, 41], [79702, 41], [80002, 41], [80302, 42], [80602, 42], [80902, 42], [81202, 42], [81502, 42], [81802, 42], [82103, 42], [82403, 42], [82703, 42], [83003, 42], [83303, 42], [83603, 42], [83903, 43], [84203, 43], [84503, 43], [84803, 43], [85103, 43], [85404, 42], [85704, 43], [86004, 43], [86304, 43], [86604, 43], [86904, 43], [87204, 43], [87504, 44], [87804, 44], [88104, 44], [88405, 43], [88705, 43], [89005, 43], [89305, 44], [89605, 44], [89905, 44]], "point": [199, 239]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-00.44|+00.11|-01.51", "placeStationary": true, "receptacleObjectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 89], [25645, 147], [25822, 87], [25948, 144], [26122, 85], [26249, 142], [26421, 85], [26551, 140], [26720, 84], [26852, 138], [27020, 83], [27153, 137], [27319, 83], [27454, 136], [27619, 82], [27755, 134], [27918, 82], [28055, 134], [28217, 83], [28356, 132], [28517, 82], [28656, 132], [28816, 83], [28956, 131], [29115, 83], [29256, 131], [29415, 83], [29556, 132], [29714, 84], [29856, 132], [30013, 85], [30156, 132], [30313, 85], [30456, 132], [30612, 86], [30756, 132], [30911, 87], [31056, 132], [31211, 88], [31356, 132], [31510, 89], [31655, 133], [31809, 90], [31955, 133], [32109, 91], [32255, 133], [32408, 92], [32554, 134], [32708, 93], [32854, 134], [33007, 94], [33153, 134], [33306, 96], [33452, 135], [33606, 96], [33751, 136], [33905, 98], [34050, 136], [34204, 100], [34349, 137], [34504, 101], [34648, 138], [34803, 104], [34947, 138], [35102, 106], [35246, 139], [35402, 108], [35544, 140], [35701, 110], [35843, 141], [36000, 113], [36141, 142], [36300, 115], [36439, 144], [36600, 118], [36737, 145], [36900, 122], [37033, 149], [37200, 281], [37500, 281], [37800, 280], [38100, 280], [38400, 280], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 89], [25645, 147], [25822, 87], [25948, 144], [26122, 85], [26249, 142], [26421, 85], [26551, 140], [26720, 84], [26852, 138], [27020, 83], [27153, 137], [27319, 83], [27454, 136], [27619, 82], [27755, 134], [27918, 82], [28055, 134], [28217, 83], [28356, 15], [28379, 109], [28517, 82], [28656, 11], [28683, 105], [28816, 83], [28956, 9], [28985, 102], [29115, 83], [29256, 8], [29287, 100], [29415, 83], [29556, 7], [29588, 100], [29714, 84], [29856, 6], [29889, 99], [30013, 85], [30156, 6], [30190, 98], [30313, 85], [30456, 6], [30490, 98], [30612, 86], [30756, 5], [30790, 98], [30911, 87], [31056, 5], [31094, 94], [31211, 88], [31356, 6], [31395, 93], [31510, 89], [31655, 7], [31690, 3], [31696, 92], [31809, 90], [31955, 7], [31990, 4], [31996, 92], [32109, 91], [32255, 7], [32289, 4], [32296, 92], [32408, 92], [32554, 8], [32589, 4], [32596, 92], [32708, 93], [32854, 8], [32889, 3], [32895, 93], [33007, 94], [33153, 9], [33189, 3], [33195, 92], [33306, 96], [33452, 10], [33488, 3], [33494, 93], [33606, 96], [33751, 11], [33788, 2], [33794, 93], [33905, 98], [34050, 12], [34088, 2], [34093, 93], [34204, 100], [34349, 13], [34388, 1], [34393, 93], [34504, 101], [34648, 14], [34688, 1], [34692, 94], [34803, 104], [34947, 15], [34991, 94], [35102, 106], [35246, 16], [35291, 94], [35402, 108], [35544, 18], [35590, 94], [35701, 110], [35843, 19], [35889, 95], [36000, 113], [36141, 21], [36189, 94], [36300, 115], [36439, 23], [36487, 96], [36600, 118], [36737, 25], [36787, 95], [36900, 122], [37033, 29], [37086, 96], [37200, 162], [37386, 95], [37500, 162], [37686, 95], [37800, 162], [37986, 94], [38100, 162], [38286, 94], [38400, 162], [38586, 94], [38700, 163], [38885, 94], [39000, 163], [39185, 94], [39300, 164], [39484, 94], [39600, 165], [39783, 95], [39900, 167], [40082, 95], [40200, 169], [40380, 97], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Mug|-00.44|+00.11|-01.51"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [161, 95, 195, 135], "mask": [[28371, 8], [28667, 16], [28965, 20], [29264, 23], [29563, 25], [29862, 27], [30162, 28], [30462, 28], [30761, 29], [31061, 33], [31362, 33], [31662, 28], [31693, 3], [31962, 28], [31994, 2], [32262, 27], [32293, 3], [32562, 27], [32593, 3], [32862, 27], [32892, 3], [33162, 27], [33192, 3], [33462, 26], [33491, 3], [33762, 26], [33790, 4], [34062, 26], [34090, 3], [34362, 26], [34389, 4], [34662, 26], [34689, 3], [34962, 29], [35262, 29], [35562, 28], [35862, 27], [36162, 27], [36462, 25], [36762, 25], [37062, 24], [37362, 24], [37662, 24], [37962, 24], [38262, 24], [38562, 24], [38863, 22], [39163, 22], [39464, 20], [39765, 18], [40067, 15], [40369, 11]], "point": [178, 114]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 89], [25645, 147], [25822, 87], [25948, 144], [26122, 85], [26249, 142], [26421, 85], [26551, 140], [26720, 84], [26852, 138], [27020, 83], [27153, 137], [27319, 83], [27454, 136], [27619, 82], [27755, 134], [27918, 82], [28055, 134], [28217, 83], [28356, 132], [28517, 82], [28656, 132], [28816, 83], [28956, 131], [29115, 83], [29256, 131], [29415, 83], [29556, 132], [29714, 84], [29856, 132], [30013, 85], [30156, 132], [30313, 85], [30456, 132], [30612, 86], [30756, 132], [30911, 87], [31056, 132], [31211, 88], [31356, 132], [31510, 89], [31655, 133], [31809, 90], [31955, 133], [32109, 91], [32255, 133], [32408, 92], [32554, 134], [32708, 93], [32854, 134], [33007, 94], [33153, 134], [33306, 96], [33452, 135], [33606, 96], [33751, 136], [33905, 98], [34050, 136], [34204, 100], [34349, 137], [34504, 101], [34648, 138], [34803, 104], [34947, 138], [35102, 106], [35246, 139], [35402, 108], [35544, 140], [35701, 110], [35843, 141], [36000, 113], [36141, 142], [36300, 115], [36439, 144], [36600, 118], [36737, 145], [36900, 122], [37033, 149], [37200, 281], [37500, 281], [37800, 280], [38100, 280], [38400, 280], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Mug|-00.44|+00.11|-01.51", "placeStationary": true, "receptacleObjectId": "CoffeeMachine|+00.62|+00.90|+00.60"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [124, 1, 255, 162], "mask": [[160, 51], [457, 58], [756, 60], [1055, 63], [1355, 64], [1655, 64], [1948, 74], [2244, 82], [2542, 87], [2839, 93], [3137, 97], [3436, 100], [3734, 104], [4033, 106], [4332, 108], [4632, 109], [4931, 111], [5230, 113], [5529, 115], [5829, 115], [6128, 116], [6428, 117], [6728, 117], [7028, 118], [7327, 119], [7627, 120], [7927, 120], [8227, 121], [8527, 121], [8826, 122], [9126, 123], [9426, 123], [9726, 124], [10026, 124], [10326, 124], [10625, 126], [10925, 126], [11225, 127], [11525, 127], [11825, 127], [12125, 127], [12425, 128], [12725, 128], [13025, 128], [13325, 128], [13625, 129], [13925, 129], [14225, 129], [14524, 130], [14824, 131], [15124, 131], [15425, 130], [15725, 130], [16025, 130], [16325, 130], [16625, 131], [16925, 131], [17225, 131], [17525, 131], [17825, 131], [18125, 131], [18425, 131], [18725, 131], [19025, 131], [19325, 130], [19625, 130], [19925, 130], [20225, 129], [20525, 129], [20825, 129], [21125, 128], [21426, 127], [21726, 127], [22026, 127], [22326, 126], [22627, 124], [22927, 123], [23227, 123], [23527, 123], [23827, 123], [24127, 122], [24427, 122], [24727, 122], [25027, 121], [25328, 120], [25628, 120], [25928, 119], [26228, 119], [26528, 119], [26828, 118], [27128, 118], [27428, 118], [27728, 118], [28028, 117], [28328, 117], [28628, 117], [28928, 116], [29228, 116], [29529, 115], [29829, 114], [30129, 114], [30429, 114], [30729, 114], [31029, 113], [31329, 113], [31629, 113], [31929, 112], [32229, 112], [32529, 112], [32829, 111], [33129, 111], [33429, 111], [33730, 110], [34030, 109], [34330, 109], [34630, 109], [34930, 108], [35230, 108], [35530, 108], [35830, 107], [36130, 107], [36430, 107], [36730, 107], [37030, 106], [37330, 106], [37630, 106], [37930, 105], [38230, 105], [38530, 105], [38830, 105], [39130, 105], [39430, 105], [39730, 105], [40030, 104], [40330, 104], [40630, 104], [40930, 103], [41230, 103], [41531, 9], [41541, 92], [41831, 7], [41841, 91], [42131, 5], [42141, 83], [42227, 5], [42432, 1], [42441, 83], [42529, 2], [42742, 82], [43042, 81], [43342, 81], [43643, 80], [43943, 80], [44244, 79], [44544, 78], [44844, 78], [45145, 77], [45445, 76], [45746, 75], [46047, 73], [46348, 72], [46649, 70], [46949, 70], [47250, 68], [47551, 66], [47855, 59], [48160, 49], [48469, 32]], "point": [189, 80]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan11", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -2.25, "y": 0.9009992, "z": -0.75}, "object_poses": [{"objectName": "ButterKnife_d4975865", "position": {"x": 0.7014037, "y": 0.9195921, "z": -1.71562362}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pot_da74902f", "position": {"x": 1.0071, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": -2.47704625, "y": 0.9527736, "z": 0.345098346}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": 0.167712927, "y": 0.9134294, "z": 0.3752191}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": -1.92895365, "y": 0.9672249, "z": 0.5249964}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": 1.53552556, "y": 0.907632232, "z": -1.4891752}, "rotation": {"x": 359.527649, "y": 0.0002675901, "z": 0.07703085}}, {"objectName": "Egg_7c2e9408", "position": {"x": -2.203, "y": 0.9873883, "z": 0.165200174}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": -0.2967947, "y": 0.105368823, "z": 0.655970633}, "rotation": {"x": 0.0, "y": 89.99995, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": -2.135, "y": 0.50973773, "z": -1.84918261}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": -0.267375469, "y": 0.910996437, "z": -1.73768091}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": 0.7531951, "y": 0.918246448, "z": -1.582726}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": -0.3946026, "y": 0.1192514, "z": 0.43935287}, "rotation": {"x": 0.0, "y": 89.99995, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": 0.432402283, "y": 0.961345851, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": 1.83009076, "y": 0.107973933, "z": 0.311406672}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": 1.658355, "y": 0.9117321, "z": -1.73768091}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": -0.634297848, "y": 0.107191741, "z": -1.434815}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": 1.66300225, "y": 0.9086062, "z": 0.706742644}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": -0.09697643, "y": 0.9951729, "z": 0.4581}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": 1.84613431, "y": 0.9951729, "z": 0.3752191}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": -1.79193056, "y": 1.04246557, "z": 0.2551492}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": -2.06597686, "y": 1.04246557, "z": 0.3450983}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": -0.443362862, "y": 0.105885565, "z": -1.50870013}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_41d4caea", "position": {"x": 0.3419488, "y": 0.8500378, "z": -1.64893007}, "rotation": {"x": 1.40334191e-14, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": 1.30535078, "y": 0.978711069, "z": -1.53005636}, "rotation": {"x": 359.994232, "y": 359.245148, "z": 0.0170560535}}, {"objectName": "Knife_b9b64351", "position": {"x": -2.06597686, "y": 0.977574646, "z": 0.4350474}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": 1.781162, "y": 0.912848532, "z": -1.82056189}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": -0.923949659, "y": 1.01488376, "z": -1.737914}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7922e01e", "position": {"x": 0.27113992, "y": 0.109727383, "z": -1.54729772}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": 0.9085692, "y": 0.9179861, "z": -1.44982815}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": -2.31, "y": 1.10084748, "z": -1.67773771}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": -2.340023, "y": 0.9509215, "z": 0.255149245}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": 1.5354104, "y": 1.32959354, "z": -1.80964673}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_da74902f", "position": {"x": 1.361, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": 0.167712927, "y": 0.927299857, "z": 0.62386173}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_54f2d061", "position": {"x": -0.296794474, "y": 0.174490839, "z": 0.3960294}, "rotation": {"x": 0.0, "y": 179.999954, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": -2.61406946, "y": 0.9873883, "z": 0.524996459}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": -1.79193056, "y": 1.04246557, "z": 0.6149455}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_42ce5824", "position": {"x": 0.6800812, "y": 0.106492281, "z": 0.270902365}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": 0.167712927, "y": 0.9086062, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_41d4caea", "position": {"x": -2.3974998, "y": 0.578452945, "z": -1.80632138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_d4975865", "position": {"x": -2.06597686, "y": 0.9522671, "z": 0.614945531}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_483d2614", "position": {"x": -2.61406946, "y": 0.9820612, "z": 0.345098317}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_4b842b85", "position": {"x": 1.022105, "y": 0.9591456, "z": 0.288545281}, "rotation": {"x": 0.0, "y": 219.19072, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": -0.1963186, "y": 0.7723391, "z": -1.5058}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": 0.8049865, "y": 0.9206794, "z": -1.64917481}, "rotation": {"x": 0.0, "y": 6.83018925e-06, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": 1.72404635, "y": 0.9073, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 59348318, "scene_num": 11}, "task_id": "trial_T20190907_164357_593878", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1MKCTVKE7J0ZP_3300DTYQT5YCYMC5DAZHJMQBL6EEQQ", "high_descs": ["Turn left, go straight, then turn right to face the counter with the microwave.", "Open the cabinet under and to the left of the microwave; take out the mug.", "Go to the microwave to your right.", "Heat up the mug in the microwave.", "Turn left, go forward, then turn left to go to the coffee maker.", "Put the mug at the coffee maker."], "task_desc": "Put a warm mug at the coffee maker.", "votes": [1, 1]}, {"assignment_id": "AD0NVUGLDYDYN_35LDD5557DLOZ76NH33308JBV2AKMQ", "high_descs": ["turn left, walk to the left side of the microwave", "open the cabinet under the microwave, take the cup out of it, close the cabinet", "walk to the right a little", "open the microwave, heat the cup with it, take the cup", "turn around to the coffee machine", "put the cup under the coffee machine on the counter"], "task_desc": "heat the cup from the cabinet in the microwave, put the cup under the coffee machine", "votes": [1, 1]}, {"assignment_id": "AJQGWGESKQT4Y_3U4J9857OHS4L1V67KOLIN3VDJCB7S", "high_descs": ["Go left and the turn left to face the counter in front of the bottle.", "Open the cabinet below and to the left and take out the mug.", "Go to the right and stand facing the microwave.", "Put the mug in the microwave and turn it on and then take the mug out and shut the door.", "Go left and then turn left again to face the coffee maker on the counter.", "Put the mug under the coffee maker on the counter."], "task_desc": "Put a warmed mug under the coffee maker.", "votes": [1, 1]}]}}