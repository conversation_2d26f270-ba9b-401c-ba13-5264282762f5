{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 1, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000022.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000023.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000024.png", "low_idx": 1}, {"high_idx": 1, "image_name": "000000025.png", "low_idx": 1}, {"high_idx": 2, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 2, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 2, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 2, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 2, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 2, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 2, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 2, "image_name": "000000033.png", "low_idx": 2}, {"high_idx": 2, "image_name": "000000034.png", "low_idx": 2}, {"high_idx": 2, "image_name": "000000035.png", "low_idx": 2}, {"high_idx": 2, "image_name": "000000036.png", "low_idx": 2}, {"high_idx": 2, "image_name": "000000037.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000038.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000039.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000040.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000041.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000042.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000043.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000044.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000045.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000046.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 3}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 3, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000071.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000072.png", "low_idx": 10}, {"high_idx": 3, "image_name": "000000073.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000074.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000075.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000076.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000077.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000078.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000079.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000082.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000083.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000084.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000085.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000086.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 4, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000095.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000096.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000097.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000098.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000099.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000100.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000101.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000102.png", "low_idx": 13}, {"high_idx": 4, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000106.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000107.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000108.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000109.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000110.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000111.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 14}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 15}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 19}, {"high_idx": 5, "image_name": "000000133.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000134.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000135.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000136.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000137.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000138.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000139.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000140.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000141.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000142.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000143.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000144.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000145.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000146.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 6, "image_name": "000000148.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000149.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000150.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000151.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000152.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000153.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000154.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000155.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000156.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000157.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000158.png", "low_idx": 21}, {"high_idx": 6, "image_name": "000000159.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 22}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 24}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 7, "image_name": "000000189.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000192.png", "low_idx": 28}, {"high_idx": 7, "image_name": "000000193.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000194.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000195.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000199.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000200.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000201.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000202.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000203.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000204.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000205.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000206.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000207.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000209.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000210.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000211.png", "low_idx": 30}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Ladle", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-5|11|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["ladle"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Ladle", [-3.141458988, -3.141458988, 11.271248, 11.271248, 4.34962224, 4.34962224]], "coordinateReceptacleObjectId": ["DiningTable", [-2.48, -2.48, 9.944, 9.944, 0.0698738544, 0.0698738544]], "forceVisible": true, "objectId": "Ladle|-00.79|+01.09|+02.82"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-6|7|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["ladle", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Ladle", [-3.141458988, -3.141458988, 11.271248, 11.271248, 4.34962224, 4.34962224]], "coordinateReceptacleObjectId": ["Drawer", [-8.172122, -8.172122, 2.3791896, 2.3791896, 0.864, 0.864]], "forceVisible": true, "objectId": "Ladle|-00.79|+01.09|+02.82", "receptacleObjectId": "Drawer|-02.04|+00.22|+00.59"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|10|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["ladle"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Ladle", [-2.1552288, -2.1552288, 10.37415312, 10.37415312, 4.34526, 4.34526]], "coordinateReceptacleObjectId": ["DiningTable", [-2.48, -2.48, 9.944, 9.944, 0.0698738544, 0.0698738544]], "forceVisible": true, "objectId": "Ladle|-00.54|+01.09|+02.59"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-6|7|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["ladle", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Ladle", [-2.1552288, -2.1552288, 10.37415312, 10.37415312, 4.34526, 4.34526]], "coordinateReceptacleObjectId": ["Drawer", [-8.172122, -8.172122, 2.3791896, 2.3791896, 0.864, 0.864]], "forceVisible": true, "objectId": "Ladle|-00.54|+01.09|+02.59", "receptacleObjectId": "Drawer|-02.04|+00.22|+00.59"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Ladle|-00.79|+01.09|+02.82"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [108, 133, 141, 215], "mask": [[39727, 3], [40026, 4], [40326, 4], [40626, 4], [40926, 4], [41226, 4], [41526, 4], [41826, 4], [42126, 4], [42426, 4], [42726, 4], [43026, 4], [43326, 4], [43625, 5], [43925, 5], [44225, 5], [44525, 5], [44825, 4], [45125, 4], [45425, 4], [45725, 4], [46025, 4], [46325, 4], [46625, 4], [46925, 4], [47224, 5], [47524, 5], [47824, 5], [48124, 5], [48424, 5], [48724, 5], [49024, 5], [49324, 5], [49624, 4], [49924, 4], [50224, 4], [50524, 4], [50823, 5], [51123, 5], [51423, 5], [51723, 5], [52023, 5], [52323, 5], [52623, 5], [52923, 5], [53223, 4], [53523, 4], [53823, 4], [54120, 7], [54417, 13], [54715, 17], [55014, 20], [55312, 23], [55611, 25], [55911, 26], [56210, 28], [56509, 30], [56809, 30], [57109, 31], [57408, 32], [57708, 33], [58008, 33], [58308, 33], [58608, 33], [58908, 33], [59208, 34], [59508, 33], [59808, 33], [60108, 33], [60408, 33], [60709, 32], [61009, 31], [61310, 30], [61610, 29], [61911, 27], [62211, 27], [62512, 25], [62813, 23], [63114, 21], [63415, 19], [63717, 16], [64019, 11], [64322, 3]], "point": [124, 173]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-02.04|+00.22|+00.59"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [198, 166, 278, 203], "mask": [[49706, 72], [50005, 74], [50305, 74], [50605, 73], [50905, 73], [51204, 73], [51504, 73], [51804, 72], [52104, 72], [52404, 71], [52703, 71], [53003, 71], [53303, 70], [53603, 70], [53902, 70], [54202, 70], [54502, 69], [54802, 69], [55102, 68], [55401, 69], [55701, 68], [56001, 68], [56301, 67], [56600, 68], [56900, 67], [57200, 66], [57500, 66], [57799, 66], [58099, 66], [58399, 65], [58699, 65], [58999, 64], [59298, 65], [59598, 64], [59898, 64], [60198, 63], [60498, 63], [60798, 62]], "point": [238, 183]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Ladle|-00.79|+01.09|+02.82", "placeStationary": true, "receptacleObjectId": "Drawer|-02.04|+00.22|+00.59"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [200, 166, 296, 240], "mask": [[49707, 66], [50007, 66], [50307, 67], [50606, 68], [50906, 69], [51206, 69], [51506, 70], [51806, 70], [52105, 72], [52405, 72], [52705, 72], [53005, 73], [53304, 74], [53604, 75], [53904, 75], [54204, 76], [54503, 77], [54803, 78], [55103, 78], [55403, 78], [55702, 80], [56002, 80], [56302, 81], [56602, 81], [56902, 82], [57201, 83], [57501, 84], [57801, 84], [58101, 84], [58400, 86], [58700, 86], [59000, 87], [59300, 87], [59600, 88], [59900, 88], [60201, 88], [60501, 88], [60801, 89], [61101, 95], [61401, 95], [61701, 96], [62002, 95], [62302, 94], [62602, 93], [62902, 92], [63202, 92], [63502, 91], [63803, 89], [64103, 89], [64403, 88], [64703, 87], [65003, 86], [65303, 86], [65604, 84], [65904, 83], [66204, 83], [66504, 82], [66804, 81], [67104, 80], [67404, 80], [67705, 78], [68005, 77], [68305, 76], [68605, 76], [68905, 75], [69205, 74], [69505, 74], [69804, 74], [70104, 73], [70404, 72], [70704, 72], [71003, 72], [71303, 71], [71603, 71], [71903, 70]], "point": [248, 202]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-02.04|+00.22|+00.59"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [200, 166, 296, 240], "mask": [[49707, 66], [50007, 66], [50307, 67], [50606, 68], [50906, 69], [51206, 69], [51506, 70], [51806, 70], [52105, 72], [52405, 72], [52705, 72], [53005, 73], [53304, 74], [53604, 75], [53904, 75], [54204, 76], [54503, 77], [54803, 78], [55103, 78], [55403, 78], [55702, 80], [56002, 80], [56302, 81], [56602, 81], [56902, 82], [57201, 83], [57501, 38], [57543, 42], [57801, 37], [57844, 41], [58101, 37], [58145, 40], [58400, 37], [58446, 40], [58700, 37], [58746, 40], [59000, 37], [59046, 41], [59300, 37], [59346, 41], [59600, 37], [59646, 42], [59900, 37], [59946, 42], [60201, 36], [60246, 43], [60501, 36], [60546, 43], [60801, 36], [60845, 45], [61101, 95], [61401, 95], [61701, 96], [62002, 95], [62302, 94], [62602, 93], [62902, 92], [63202, 92], [63502, 91], [63803, 89], [64103, 89], [64403, 88], [64703, 87], [65003, 86], [65303, 86], [65604, 84], [65904, 83], [66204, 83], [66504, 82], [66804, 81], [67104, 80], [67404, 80], [67705, 78], [68005, 77], [68305, 76], [68605, 76], [68905, 75], [69205, 74], [69505, 74], [69804, 74], [70104, 73], [70404, 72], [70704, 72], [71003, 72], [71303, 71], [71603, 71], [71903, 70]], "point": [248, 202]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Ladle|-00.54|+01.09|+02.59"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [106, 81, 132, 143], "mask": [[24123, 3], [24423, 3], [24722, 4], [25022, 4], [25322, 4], [25622, 4], [25922, 3], [26222, 3], [26522, 3], [26822, 3], [27122, 3], [27421, 4], [27721, 4], [28021, 4], [28321, 4], [28621, 4], [28921, 3], [29221, 3], [29521, 3], [29820, 4], [30120, 4], [30420, 4], [30720, 4], [31020, 4], [31320, 4], [31620, 3], [31920, 3], [32219, 4], [32519, 4], [32819, 4], [33119, 4], [33419, 4], [33719, 4], [34019, 4], [34319, 4], [34619, 3], [34919, 3], [35219, 3], [35517, 6], [35814, 12], [36112, 16], [36411, 18], [36710, 21], [37009, 22], [37308, 24], [37607, 25], [37907, 25], [38207, 25], [38506, 27], [38806, 27], [39106, 26], [39406, 26], [39706, 26], [40007, 25], [40307, 24], [40607, 24], [40908, 22], [41208, 22], [41509, 20], [41810, 18], [42111, 16], [42413, 12], [42715, 8]], "point": [119, 111]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-02.04|+00.22|+00.59"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [198, 166, 278, 203], "mask": [[49706, 72], [50005, 74], [50305, 74], [50605, 73], [50905, 73], [51204, 73], [51504, 73], [51804, 72], [52104, 72], [52404, 71], [52703, 71], [53003, 71], [53303, 70], [53603, 70], [53902, 70], [54202, 70], [54502, 69], [54802, 69], [55102, 68], [55401, 69], [55701, 68], [56001, 68], [56301, 67], [56600, 68], [56900, 67], [57200, 66], [57500, 66], [57799, 66], [58099, 66], [58399, 65], [58699, 65], [58999, 64], [59298, 65], [59598, 64], [59898, 64], [60198, 63], [60498, 63], [60798, 62]], "point": [238, 183]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Ladle|-00.54|+01.09|+02.59", "placeStationary": true, "receptacleObjectId": "Drawer|-02.04|+00.22|+00.59"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [200, 166, 296, 240], "mask": [[49707, 66], [50007, 66], [50307, 67], [50606, 68], [50906, 69], [51206, 69], [51506, 70], [51806, 70], [52105, 72], [52405, 72], [52705, 72], [53005, 73], [53304, 74], [53604, 75], [53904, 75], [54204, 76], [54503, 77], [54803, 78], [55103, 78], [55403, 78], [55702, 80], [56002, 80], [56302, 81], [56602, 81], [56902, 82], [57201, 83], [57501, 38], [57543, 42], [57801, 37], [57844, 41], [58101, 37], [58145, 40], [58400, 37], [58446, 40], [58700, 37], [58746, 40], [59000, 37], [59046, 41], [59300, 37], [59346, 41], [59600, 37], [59646, 42], [59900, 37], [59946, 42], [60201, 36], [60246, 43], [60501, 36], [60546, 43], [60801, 36], [60845, 45], [61101, 95], [61401, 95], [61701, 96], [62002, 95], [62302, 94], [62602, 93], [62902, 92], [63202, 92], [63502, 91], [63803, 89], [64103, 89], [64403, 88], [64703, 87], [65003, 86], [65303, 86], [65604, 84], [65904, 83], [66204, 83], [66504, 82], [66804, 81], [67104, 80], [67404, 80], [67705, 78], [68005, 77], [68305, 76], [68605, 76], [68905, 75], [69205, 74], [69505, 74], [69804, 74], [70104, 73], [70404, 72], [70704, 72], [71003, 72], [71303, 71], [71603, 71], [71903, 70]], "point": [248, 202]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-02.04|+00.22|+00.59"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [200, 166, 296, 240], "mask": [[49707, 66], [50007, 66], [50307, 67], [50606, 68], [50906, 69], [51206, 69], [51506, 70], [51806, 70], [52105, 72], [52405, 72], [52705, 72], [53005, 73], [53304, 74], [53604, 75], [53904, 75], [54204, 76], [54503, 33], [54539, 41], [54803, 33], [54841, 40], [55103, 32], [55142, 39], [55403, 32], [55442, 39], [55702, 33], [55743, 39], [56002, 32], [56043, 39], [56302, 32], [56343, 40], [56602, 32], [56643, 40], [56902, 32], [56943, 41], [57201, 33], [57243, 41], [57501, 33], [57543, 42], [57801, 33], [57844, 41], [58101, 34], [58145, 40], [58400, 34], [58446, 40], [58700, 34], [58735, 1], [58746, 40], [59000, 33], [59035, 2], [59046, 41], [59300, 33], [59335, 2], [59346, 41], [59600, 32], [59634, 3], [59646, 42], [59900, 32], [59934, 3], [59946, 42], [60201, 30], [60233, 4], [60246, 43], [60501, 30], [60533, 4], [60546, 43], [60801, 29], [60832, 5], [60845, 45], [61101, 95], [61401, 95], [61701, 96], [62002, 95], [62302, 94], [62602, 93], [62902, 92], [63202, 92], [63502, 91], [63803, 89], [64103, 89], [64403, 88], [64703, 87], [65003, 86], [65303, 86], [65604, 84], [65904, 83], [66204, 83], [66504, 82], [66804, 81], [67104, 80], [67404, 80], [67705, 78], [68005, 77], [68305, 76], [68605, 76], [68905, 75], [69205, 74], [69505, 74], [69804, 74], [70104, 73], [70404, 72], [70704, 72], [71003, 72], [71303, 71], [71603, 71], [71903, 70]], "point": [248, 202]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan4", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -1.25, "y": 0.9009999, "z": 2.75}, "object_poses": [{"objectName": "Pan_447fa38c", "position": {"x": -3.50640655, "y": 0.9114697, "z": 2.92009878}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Potato_4679e4a5", "position": {"x": -1.64217317, "y": 0.962041557, "z": 0.567328}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_f4537974", "position": {"x": -0.5388072, "y": 1.086315, "z": 2.59353828}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_130c30e2", "position": {"x": -1.11659849, "y": 0.04900515, "z": 0.49469927}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8393cb4d", "position": {"x": -0.662086, "y": 1.04828012, "z": 3.26635885}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_ab8a6692", "position": {"x": -3.00049949, "y": 1.1332, "z": 0.480000019}, "rotation": {"x": 0.0, "y": 270.000732, "z": 0.0}}, {"objectName": "Spoon_9dba57b8", "position": {"x": -2.54504824, "y": 0.48917672, "z": 0.613230169}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spatula_0271e4d1", "position": {"x": -0.785364747, "y": 1.06323791, "z": 2.59353828}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_0271e4d1", "position": {"x": -0.5388072, "y": 1.06214738, "z": 3.04208541}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7b1a480e", "position": {"x": -1.55366719, "y": 0.9295632, "z": 0.498063982}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e6e9017d", "position": {"x": -1.4651612, "y": 0.9639934, "z": 0.4288}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e6e9017d", "position": {"x": -2.262703, "y": 1.15733731, "z": 0.190328062}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_58fe0a5f", "position": {"x": -2.13702822, "y": 1.12543952, "z": 0.6620079}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_1473e57e", "position": {"x": -3.44000077, "y": 1.60308588, "z": 3.13280153}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_9496ee5e", "position": {"x": -0.785364747, "y": 1.04474556, "z": 3.26635885}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_e61894bc", "position": {"x": -1.640668, "y": 0.0456396937, "z": 0.438600659}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_4cc43937", "position": {"x": -1.601334, "y": 0.0456396937, "z": 0.665083647}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_4cc43937", "position": {"x": -0.168970883, "y": 1.04454422, "z": 2.14499116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7c6826ef", "position": {"x": -2.262703, "y": 1.20227718, "z": 0.567671955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ff353859", "position": {"x": -0.5388072, "y": 1.043238, "z": 3.26635885}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_1f60da4f", "position": {"x": -1.88567913, "y": 1.12544858, "z": 0.473335981}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_58fe0a5f", "position": {"x": -0.5388072, "y": 1.04768693, "z": 2.14499116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_ab8a6692", "position": {"x": -3.475, "y": 1.1332, "z": 0.48}, "rotation": {"x": 0.0, "y": 90.00039, "z": 0.0}}, {"objectName": "Ladle_f4537974", "position": {"x": -0.785364747, "y": 1.08740556, "z": 2.817812}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_4679e4a5", "position": {"x": -3.696083, "y": 0.142974779, "z": 2.00878215}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_ca5fe19d", "position": {"x": -1.1936096, "y": 0.921509564, "z": 0.403257042}, "rotation": {"x": -0.00563099235, "y": -0.00100996275, "z": 359.9816}}, {"objectName": "Bread_d5b8c410", "position": {"x": -2.63972664, "y": 1.21164966, "z": 0.567671955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7c6826ef", "position": {"x": -0.785364747, "y": 1.12561512, "z": 2.14499116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_0271e4d1", "position": {"x": -2.51405215, "y": 1.1399, "z": 0.190328062}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_447fa38c", "position": {"x": -0.266, "y": 1.038, "z": 2.561}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7b2f7cfb", "position": {"x": -0.203308851, "y": 1.25875783, "z": 0.3773778}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7b1a480e", "position": {"x": -3.24800014, "y": 0.7260677, "z": 3.20370531}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_e61894bc", "position": {"x": -2.35645461, "y": 0.4843535, "z": 0.646863}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_e6e9017d", "position": {"x": -0.662086, "y": 1.08067524, "z": 1.9207176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9496ee5e", "position": {"x": -3.44, "y": 1.55805814, "z": 2.84919739}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_4cc43937", "position": {"x": -2.5093, "y": 0.8983304, "z": 0.645984054}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8393cb4d", "position": {"x": -0.415528417, "y": 1.04718959, "z": 2.14499116}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_f6bdaee7", "position": {"x": -2.01135373, "y": 1.19227993, "z": 0.567671955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_1473e57e", "position": {"x": -0.908643544, "y": 1.087733, "z": 1.9207176}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_130c30e2", "position": {"x": -0.827757657, "y": 1.12446439, "z": 0.296814859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_9dba57b8", "position": {"x": -0.7158578, "y": 1.12602937, "z": 0.659750342}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_ff353859", "position": {"x": -0.3778987, "y": 1.09474707, "z": 3.19400048}, "rotation": {"x": 1.027709e-05, "y": 270.000366, "z": 3.658575e-05}}], "object_toggles": [], "random_seed": 3800022313, "scene_num": 4}, "task_id": "trial_T20190908_154812_240120", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "ABR580YP0ST7X_3BEFOD78W9KB6U3ISJ54UDEHS584MJ", "high_descs": ["Look down at counter", "Pick up spoon closest to you on counter", "Turn right and walk to counter with sink", "Open bottom right drawer and put spoon inside.  Close drawer", "Turn around and walk back to counter, turn right", "Pick up other spoon on counter", "Turn right and walk to counter with sink", "Open bottom right drawer and put spoon inside.  Close drawer"], "task_desc": "Place two spoons into bottom drawer near sink.", "votes": [1, 1, 1]}, {"assignment_id": "A11IIS435RPN2O_3LRKMWOKB8812NNE3UCA90DSNU2Z25", "high_descs": ["stand in front of the brown table.", "Pick up the Dish spoon in front of the toaster.", "Turn Around walk one step then turn left and go towards sink.", "Open the third drawer of the cabinet then Put the dish spoon in that drawer and close the drawer.", "Turn around go straight and turn right to the brown table.", "Pick up the Dish Spoon in front of the pan.", "Turn Around walk one step then turn left and go towards sink.", "Open the third drawer of the cabinet then Put the dish spoon in that drawer and close the drawer."], "task_desc": "Put the Dish Spoons in the drawer.", "votes": [1, 1, 1]}, {"assignment_id": "A6U2C66WQ7QQN_3LUY3GC632REAKIEMXV3ETSMOCVP76", "high_descs": ["Take the spoon from the table", "Head to the right, open the drawer", "Turn around, head to the table", "head to the table", "Take the spoon from the table", "Head to the right", "Open the drawer on the bottom", "put the spoon in the drawer, close the drawer"], "task_desc": "Put two spoons from the table in the bottom drawer of the counter", "votes": [1, 0, 1]}]}}