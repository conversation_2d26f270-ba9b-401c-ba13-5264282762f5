{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000096.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000097.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000098.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000099.png", "low_idx": 27}, {"high_idx": 0, "image_name": "000000100.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 28}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000108.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000110.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000111.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000112.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000113.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000114.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000115.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000116.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000117.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000118.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000119.png", "low_idx": 29}, {"high_idx": 1, "image_name": "000000120.png", "low_idx": 30}, {"high_idx": 1, "image_name": "000000121.png", "low_idx": 30}, {"high_idx": 1, "image_name": "000000122.png", "low_idx": 30}, {"high_idx": 1, "image_name": "000000123.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 36}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 37}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 38}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 39}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 40}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 41}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 42}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 43}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 44}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 45}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 46}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 47}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 48}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 49}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000205.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000206.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000207.png", "low_idx": 50}, {"high_idx": 2, "image_name": "000000208.png", "low_idx": 51}, {"high_idx": 2, "image_name": "000000209.png", "low_idx": 51}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 52}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 53}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 54}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 55}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 56}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 57}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 57}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 57}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 57}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000265.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000266.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000267.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000268.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000269.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000270.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000271.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000272.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000273.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000274.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000275.png", "low_idx": 58}, {"high_idx": 3, "image_name": "000000276.png", "low_idx": 59}, {"high_idx": 3, "image_name": "000000277.png", "low_idx": 59}, {"high_idx": 3, "image_name": "000000278.png", "low_idx": 59}, {"high_idx": 3, "image_name": "000000279.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 64}, {"high_idx": 5, "image_name": "000000335.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000336.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000337.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000338.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000339.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000340.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000341.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000342.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000343.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000344.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000345.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000346.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000347.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000348.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000349.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000350.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000351.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000352.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000353.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000354.png", "low_idx": 67}, {"high_idx": 5, "image_name": "000000355.png", "low_idx": 67}, {"high_idx": 5, "image_name": "000000356.png", "low_idx": 67}, {"high_idx": 5, "image_name": "000000357.png", "low_idx": 67}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-7|-4|3|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-9.94318, -9.94318, -3.9191232, -3.9191232, 4.34810256, 4.34810256]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-9.90399932, -9.90399932, -3.132, -3.132, 0.0, 0.0]], "forceVisible": true, "objectId": "Cup|-02.49|+01.09|-00.98"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-7|10|3|-30"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-9.94318, -9.94318, -3.9191232, -3.9191232, 4.34810256, 4.34810256]], "coordinateReceptacleObjectId": ["Cabinet", [-9.784, -9.784, 11.7, 11.7, 7.79599952, 7.79599952]], "forceVisible": true, "objectId": "Cup|-02.49|+01.09|-00.98", "receptacleObjectId": "Cabinet|-02.45|+01.95|+02.93"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 46200], [46201, 299], [46501, 299], [46801, 299], [47102, 298], [47402, 298], [47702, 298], [48003, 297], [48303, 297], [48603, 297], [48904, 296], [49204, 296], [49504, 296], [49805, 295], [50105, 295], [50405, 295], [50706, 294], [51006, 294], [51306, 294], [51607, 293], [51907, 293], [52207, 293], [52508, 292], [52808, 292], [53108, 292], [53409, 291], [53709, 291], [54009, 291], [54310, 290], [54610, 290], [54910, 290], [55211, 289], [55511, 289], [55811, 289], [56112, 288], [56412, 288], [56712, 288], [57013, 287], [57313, 287], [57613, 287], [57914, 286], [58214, 286], [58514, 286], [58815, 285], [59115, 285], [59415, 285], [59716, 284], [60016, 284], [60316, 284], [60617, 283], [60917, 283], [61217, 283], [61518, 282], [61818, 282], [62118, 282], [62419, 281], [62719, 281], [63019, 281], [63320, 280], [63620, 280], [63920, 280], [64221, 279], [64521, 279], [64821, 279], [65122, 278], [65422, 278], [65722, 278], [66023, 277], [66323, 277], [66623, 277], [66924, 276], [67224, 276], [67524, 276], [67825, 275], [68125, 275], [68425, 275], [68726, 274], [69026, 274], [69326, 274], [69627, 273], [69927, 273], [70227, 273], [70528, 272], [70828, 272], [71128, 272], [71429, 271], [71729, 271], [72030, 270], [72330, 270], [72630, 270], [72931, 269], [73231, 269], [73531, 269], [73832, 268], [74132, 268], [74432, 268], [74733, 267], [75033, 267], [75333, 267], [75634, 266], [75934, 266], [76234, 266], [76535, 265], [76835, 265], [77135, 265], [77436, 264], [77736, 264], [78036, 264], [78337, 263], [78637, 263], [78937, 263], [79238, 262], [79538, 262], [79838, 262], [80139, 261], [80439, 261], [80739, 261], [81038, 262], [81338, 262], [81638, 262], [81937, 263], [82237, 263], [82537, 263], [82837, 263], [83137, 263], [83437, 263], [83738, 262], [84038, 262], [84338, 262], [84638, 262], [84939, 261], [85239, 261], [85539, 261], [85839, 261], [86140, 260], [86440, 260], [86740, 260], [87041, 259], [87341, 259], [87641, 259], [87942, 258], [88242, 258], [88542, 258], [88843, 257], [89148, 252], [89449, 251], [89749, 251]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-02.49|+01.09|-00.98"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [141, 137, 171, 171], "mask": [[40954, 7], [43641, 31], [43941, 30], [44242, 29], [44542, 29], [44842, 28], [45143, 27], [45443, 26], [45743, 26], [46043, 26], [46344, 25], [46644, 24], [46944, 24], [47245, 23], [47545, 22], [47845, 22], [48145, 22], [48446, 20], [48746, 20], [49046, 20], [49346, 20], [49647, 18], [49947, 18], [50248, 17], [50548, 16], [50850, 13], [51152, 8]], "point": [156, 153]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-02.48|+00.00|-00.78"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 19706], [19724, 280], [20028, 275], [20331, 272], [20632, 271], [20932, 271], [21232, 271], [21532, 270], [21832, 270], [22131, 271], [22431, 271], [22731, 271], [23031, 271], [23331, 271], [23630, 272], [23930, 272], [24230, 272], [24530, 272], [24829, 273], [25129, 273], [25429, 273], [25729, 273], [26029, 273], [26328, 274], [26628, 273], [26928, 273], [27228, 273], [27528, 273], [27827, 274], [28127, 274], [28427, 274], [28727, 274], [29027, 274], [29326, 275], [29626, 275], [29926, 276], [30226, 277], [30525, 281], [30824, 285], [31121, 12947], [44071, 295], [44373, 291], [44675, 288], [44976, 286], [45277, 285], [45578, 283], [45879, 282], [46179, 282], [46480, 280], [46781, 279], [47081, 279], [47381, 279], [47681, 279], [47982, 279], [48282, 279], [48582, 280], [48882, 280], [49182, 281], [49483, 281], [49783, 281], [50083, 282], [50383, 283], [50682, 285], [50981, 288], [51279, 292], [51577, 4972], [56551, 290], [56859, 277], [57164, 270], [57466, 265], [57769, 260], [58071, 255], [58374, 251], [58675, 248], [58977, 245], [59278, 243], [59579, 241], [59880, 239], [60181, 237], [60482, 235], [60783, 233], [61084, 231], [61385, 230], [61685, 229], [61986, 228], [62286, 227], [62587, 226], [62887, 225], [63188, 224], [63488, 223], [63789, 222], [64089, 222], [64389, 222], [64689, 223], [64988, 224], [65288, 224], [65588, 224], [65888, 224], [66188, 224], [66488, 224], [66788, 224], [67088, 224], [67388, 224], [67688, 225], [67987, 226], [68287, 226], [68587, 226], [68887, 227], [69186, 228], [69486, 229], [69785, 231], [70084, 232], [70384, 233], [70683, 235], [70982, 236], [71282, 237], [71581, 239], [71880, 240], [72180, 241], [72479, 242], [72779, 243], [73078, 244], [73378, 245], [73677, 246], [73977, 246], [74277, 247], [74576, 248], [74876, 249], [75175, 250], [75475, 251], [75774, 252], [76074, 252], [76374, 253], [76673, 254], [76973, 255], [77272, 256], [77572, 256], [77872, 257], [78171, 258], [78471, 258], [78771, 258], [79071, 258], [79371, 259], [79670, 261], [79969, 262], [80269, 263], [80568, 265], [80867, 266], [81167, 267], [81466, 269], [81765, 272], [82063, 276], [82361, 279], [82660, 282], [82958, 290], [83252, 37], [83317, 272], [83617, 273], [83917, 273], [84217, 273], [84517, 273], [84817, 273], [85117, 273], [85417, 274], [85717, 274], [86017, 274], [86316, 275], [86616, 276], [86916, 277], [87216, 277], [87515, 279], [87814, 281], [88114, 282], [88413, 284], [88712, 287], [89011, 291], [89308, 692]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-02.49|+01.09|-00.98", "placeStationary": true, "receptacleObjectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 235], [23400, 234], [23700, 234], [24000, 234], [24300, 234], [24600, 233], [24900, 233], [25200, 233], [25500, 233], [25800, 232], [26100, 232], [26400, 232], [26700, 232], [27000, 231], [27300, 231], [27600, 231], [27900, 231], [28200, 230], [28500, 230], [28800, 230], [29100, 230], [29400, 229], [29700, 229], [30000, 229], [30300, 229], [30600, 228], [30900, 228], [31200, 228], [31500, 228], [31800, 227], [32100, 227], [32400, 227], [32700, 227], [33000, 226], [33300, 226], [33600, 226], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 141], [23249, 86], [23400, 137], [23553, 81], [23700, 135], [23855, 79], [24000, 133], [24156, 78], [24300, 132], [24458, 76], [24600, 131], [24758, 75], [24900, 131], [25059, 74], [25200, 130], [25359, 74], [25500, 130], [25659, 74], [25800, 130], [25959, 73], [26100, 130], [26259, 73], [26400, 131], [26559, 73], [26700, 131], [26858, 74], [27000, 132], [27158, 73], [27300, 132], [27458, 73], [27600, 132], [27757, 74], [27900, 133], [28057, 74], [28200, 133], [28357, 73], [28500, 133], [28657, 73], [28800, 134], [28956, 74], [29100, 134], [29256, 74], [29400, 134], [29556, 73], [29700, 135], [29855, 74], [30000, 135], [30155, 74], [30300, 135], [30455, 74], [30600, 135], [30755, 73], [30900, 136], [31054, 74], [31200, 136], [31354, 74], [31500, 136], [31654, 74], [31800, 137], [31953, 74], [32100, 137], [32253, 74], [32400, 138], [32552, 75], [32700, 139], [32851, 76], [33000, 141], [33149, 77], [33300, 226], [33600, 226], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-10.3232, -10.3232, 9.764, 9.764, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [20, 18, 238, 142], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15924, 211], [16224, 212], [16524, 213], [16823, 214], [17122, 216], [17422, 216], [17721, 218], [18020, 219], [18320, 219], [18620, 218], [18920, 218], [19220, 218], [19520, 218], [19821, 216], [20121, 216], [20422, 215], [20722, 215], [21022, 214], [21323, 213], [21623, 213], [21923, 213], [22224, 211], [22524, 211], [22825, 210], [23125, 210], [23425, 209], [23726, 208], [24026, 208], [24326, 208], [24627, 206], [24927, 206], [25228, 205], [25528, 205], [25828, 204], [26129, 203], [26429, 203], [26729, 203], [27030, 201], [27330, 201], [27631, 200], [27931, 200], [28231, 199], [28532, 198], [28832, 198], [29132, 198], [29433, 196], [29733, 196], [30034, 195], [30334, 195], [30634, 194], [30935, 193], [31235, 193], [31535, 193], [31836, 191], [32136, 191], [32437, 190], [32737, 190], [33037, 189], [33338, 188], [33638, 188], [33938, 187], [34239, 186], [34539, 186], [34840, 185], [35140, 184], [35440, 184], [35741, 183], [36041, 183], [36341, 182], [36642, 181], [36942, 181], [37243, 180], [37543, 179], [37843, 179], [38144, 178], [38444, 178], [38744, 177], [39045, 176], [39345, 176], [39646, 175], [39946, 174], [40246, 174], [40547, 173], [40847, 173], [41147, 172], [41448, 171], [41748, 171], [42049, 170], [42350, 135], [42486, 32]], "point": [129, 79]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-02.49|+01.09|-00.98"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [130, 78, 158, 111], "mask": [[23241, 8], [23537, 16], [23835, 20], [24133, 23], [24432, 26], [24731, 27], [25031, 28], [25330, 29], [25630, 29], [25930, 29], [26230, 29], [26531, 28], [26831, 27], [27132, 26], [27432, 26], [27732, 25], [28033, 24], [28333, 24], [28633, 24], [28934, 22], [29234, 22], [29534, 22], [29835, 20], [30135, 20], [30435, 20], [30735, 20], [31036, 18], [31336, 18], [31636, 18], [31937, 16], [32237, 16], [32538, 14], [32839, 12], [33141, 8]], "point": [144, 93]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.58|+00.90|+02.44"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 18, 238, 268], "mask": [[5152, 139], [5450, 166], [5750, 167], [6049, 168], [6348, 170], [6647, 171], [6946, 173], [7246, 174], [7545, 175], [7844, 177], [8143, 178], [8443, 179], [8742, 180], [9041, 182], [9340, 183], [9640, 184], [9939, 185], [10238, 187], [10537, 188], [10836, 190], [11136, 190], [11435, 192], [11734, 193], [12033, 195], [12333, 195], [12632, 197], [12931, 198], [13230, 200], [13529, 201], [13829, 202], [14128, 203], [14427, 205], [14726, 206], [15026, 207], [15325, 208], [15624, 210], [15911, 224], [16210, 226], [16509, 228], [16808, 229], [17108, 230], [17407, 231], [17706, 233], [18005, 234], [18304, 235], [18603, 235], [18903, 235], [19202, 236], [19501, 237], [19800, 237], [20100, 237], [20400, 237], [20700, 237], [21000, 236], [21300, 236], [21600, 236], [21900, 236], [22200, 235], [22500, 235], [22800, 235], [23100, 235], [23400, 234], [23700, 234], [24000, 234], [24300, 234], [24600, 233], [24900, 233], [25200, 233], [25500, 233], [25800, 232], [26100, 232], [26400, 232], [26700, 232], [27000, 231], [27300, 231], [27600, 231], [27900, 231], [28200, 230], [28500, 230], [28800, 230], [29100, 230], [29400, 229], [29700, 229], [30000, 229], [30300, 229], [30600, 228], [30900, 228], [31200, 228], [31500, 228], [31800, 227], [32100, 227], [32400, 227], [32700, 227], [33000, 226], [33300, 226], [33600, 226], [33900, 225], [34200, 225], [34500, 225], [34800, 225], [35100, 224], [35400, 224], [35700, 224], [36000, 224], [36300, 223], [36600, 223], [36900, 223], [37200, 223], [37500, 222], [37800, 222], [38100, 222], [38400, 222], [38700, 221], [39000, 221], [39300, 221], [39600, 221], [39900, 220], [40200, 220], [40500, 220], [40800, 53], [40985, 35], [41100, 53], [41285, 34], [41400, 52], [41585, 34], [41700, 52], [41886, 33], [42000, 52], [42186, 33], [42300, 51], [42486, 32], [42600, 51], [42900, 51], [43200, 50], [43500, 50], [43800, 49], [44100, 49], [44400, 49], [44700, 48], [45000, 48], [45300, 47], [45600, 47], [45900, 47], [46200, 46], [46500, 46], [46800, 45], [47100, 45], [47400, 45], [47700, 44], [48000, 44], [48300, 43], [48600, 43], [48900, 43], [49200, 42], [49500, 42], [49800, 41], [50100, 41], [50400, 41], [50700, 40], [51000, 40], [51300, 40], [51600, 39], [51900, 39], [52200, 38], [52500, 38], [52800, 38], [53100, 37], [53400, 37], [53700, 36], [54000, 36], [54300, 36], [54600, 35], [54900, 35], [55200, 34], [55500, 34], [55800, 34], [56100, 33], [56400, 33], [56700, 32], [57000, 32], [57300, 32], [57600, 31], [57900, 31], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 29], [60000, 28], [60300, 28], [60600, 27], [60900, 27], [61200, 27], [61500, 26], [61800, 26], [62100, 25], [62400, 25], [62700, 25], [63000, 24], [63300, 24], [63600, 23], [63900, 23], [64200, 23], [64500, 22], [64800, 22], [65100, 21], [65400, 21], [65700, 21], [66000, 20], [66300, 20], [66600, 20], [66900, 19], [67200, 19], [67500, 18], [67800, 18], [68100, 18], [68400, 17], [68700, 17], [69000, 16], [69300, 16], [69600, 16], [69900, 15], [70200, 15], [70500, 14], [70800, 14], [71100, 14], [71400, 13], [71700, 13], [72000, 12], [72300, 12], [72600, 12], [72900, 11], [73200, 11], [73500, 11], [73800, 10], [74100, 10], [74400, 9], [74700, 9], [75000, 9], [75300, 8], [75600, 8], [75900, 7], [76200, 7], [76500, 7], [76800, 6], [77100, 6], [77400, 5], [77700, 5], [78000, 7], [78300, 8], [78600, 8], [78900, 3], [78904, 3], [79200, 3], [79500, 3], [79800, 2], [80100, 2]], "point": [119, 135]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-02.45|+01.95|+02.93"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [79, 62, 299, 300], "mask": [[18408, 152], [18707, 153], [19007, 154], [19307, 154], [19607, 154], [19907, 154], [20206, 156], [20506, 156], [20806, 156], [21106, 157], [21406, 157], [21706, 157], [22006, 158], [22306, 158], [22606, 158], [22905, 159], [23205, 160], [23505, 160], [23805, 160], [24105, 161], [24405, 161], [24705, 161], [25005, 162], [25304, 163], [25604, 163], [25904, 163], [26204, 164], [26504, 164], [26804, 164], [27104, 165], [27404, 165], [27704, 165], [28003, 167], [28303, 167], [28603, 167], [28903, 168], [29203, 168], [29503, 168], [29803, 168], [30103, 169], [30402, 170], [30702, 170], [31002, 171], [31302, 171], [31602, 171], [31902, 172], [32202, 172], [32502, 172], [32802, 172], [33101, 174], [33401, 174], [33701, 174], [34001, 175], [34301, 175], [34601, 175], [34901, 176], [35201, 176], [35500, 177], [35800, 178], [36100, 178], [36400, 178], [36700, 178], [37000, 179], [37300, 179], [37600, 179], [37900, 180], [38199, 181], [38499, 181], [38799, 182], [39099, 182], [39399, 182], [39699, 182], [39999, 183], [40299, 183], [40598, 184], [40898, 185], [41198, 185], [41498, 185], [41798, 186], [42098, 186], [42398, 186], [42698, 187], [42997, 188], [43297, 188], [43597, 188], [43897, 189], [44197, 189], [44497, 189], [44797, 190], [45097, 190], [45397, 190], [45696, 192], [45996, 192], [46296, 192], [46596, 192], [46896, 193], [47196, 193], [47496, 193], [47796, 194], [48095, 195], [48395, 195], [48695, 196], [48995, 196], [49295, 196], [49595, 197], [49895, 197], [50195, 197], [50495, 197], [50794, 199], [51094, 199], [51394, 199], [51694, 200], [51994, 200], [52294, 200], [52594, 201], [52894, 201], [53193, 202], [53493, 202], [53793, 203], [54093, 203], [54393, 203], [54693, 204], [54993, 204], [55293, 204], [55593, 205], [55892, 206], [56192, 206], [56492, 207], [56792, 207], [57092, 207], [57392, 207], [57692, 208], [57992, 208], [58291, 209], [58591, 54], [58655, 145], [58891, 48], [58961, 139], [59191, 44], [59265, 135], [59491, 42], [59567, 133], [59791, 40], [59869, 131], [60091, 38], [60171, 129], [60391, 37], [60472, 128], [60691, 35], [60774, 126], [60990, 34], [61076, 124], [61290, 33], [61377, 123], [61590, 32], [61678, 122], [61890, 31], [61979, 121], [62190, 30], [62280, 120], [62490, 29], [62581, 119], [62790, 28], [62882, 118], [63090, 27], [63183, 117], [63389, 28], [63483, 117], [63689, 27], [63784, 116], [63989, 26], [64085, 115], [64289, 25], [64386, 114], [64589, 24], [64687, 113], [64889, 24], [64987, 113], [65189, 24], [65287, 113], [65489, 23], [65588, 112], [65788, 24], [65888, 112], [66088, 24], [66188, 112], [66388, 23], [66489, 111], [66688, 23], [66789, 111], [66988, 23], [67089, 111], [67288, 22], [67390, 110], [67588, 22], [67690, 110], [67888, 22], [67990, 110], [68188, 21], [68291, 109], [68487, 22], [68591, 109], [68787, 22], [68891, 109], [69087, 22], [69191, 109], [69387, 22], [69491, 109], [69687, 22], [69791, 109], [69987, 22], [70091, 109], [70287, 22], [70391, 109], [70587, 22], [70691, 109], [70886, 24], [70990, 110], [71186, 24], [71290, 110], [71486, 24], [71590, 110], [71786, 24], [71890, 110], [72086, 24], [72190, 110], [72386, 24], [72490, 110], [72686, 24], [72790, 110], [72986, 24], [73090, 110], [73286, 24], [73390, 110], [73585, 26], [73689, 111], [73885, 26], [73989, 111], [74185, 26], [74289, 111], [74485, 27], [74588, 112], [74785, 28], [74887, 113], [75085, 28], [75187, 113], [75385, 29], [75486, 114], [75685, 29], [75786, 114], [75984, 31], [76085, 115], [76284, 31], [76385, 115], [76584, 32], [76684, 116], [76884, 33], [76983, 117], [77184, 33], [77283, 117], [77484, 34], [77582, 118], [77784, 34], [77882, 118], [78084, 35], [78181, 119], [78384, 36], [78480, 120], [78683, 38], [78779, 121], [78983, 39], [79078, 122], [79283, 41], [79376, 124], [79583, 42], [79675, 125], [79883, 43], [79974, 126], [80183, 44], [80273, 127], [80483, 45], [80572, 128], [80783, 46], [80871, 129], [81082, 48], [81170, 130], [81382, 49], [81469, 131], [81682, 50], [81768, 132], [81982, 51], [82067, 133], [82282, 52], [82366, 134], [82582, 54], [82664, 136], [82882, 55], [82963, 137], [83182, 57], [83261, 139], [83482, 58], [83560, 140], [83781, 61], [83858, 142], [84081, 65], [84154, 146], [84381, 219], [84681, 219], [84981, 219], [85281, 219], [85581, 219], [85881, 219], [86180, 220], [86480, 220], [86780, 220], [87080, 220], [87380, 220], [87680, 220], [87980, 220], [88280, 220], [88580, 220], [88879, 221], [89179, 221], [89479, 221], [89779, 221]], "point": [189, 180]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-02.49|+01.09|-00.98", "placeStationary": true, "receptacleObjectId": "Cabinet|-02.45|+01.95|+02.93"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [79, 1, 299, 300], "mask": [[284, 16], [584, 16], [884, 16], [1183, 17], [1483, 17], [1782, 18], [2082, 18], [2381, 19], [2681, 19], [2980, 20], [3280, 20], [3579, 21], [3879, 21], [4179, 21], [4478, 22], [4778, 22], [5077, 23], [5377, 23], [5676, 24], [5976, 24], [6275, 25], [6575, 25], [6875, 25], [7174, 26], [7474, 26], [7773, 27], [8073, 27], [8372, 28], [8672, 28], [8971, 29], [9271, 29], [9570, 30], [9870, 30], [10170, 30], [10469, 31], [10769, 31], [11068, 32], [11368, 32], [11667, 33], [11967, 33], [12266, 34], [12566, 34], [12866, 34], [13165, 35], [13465, 35], [13764, 36], [14064, 36], [14363, 37], [14663, 37], [14962, 38], [15262, 38], [15562, 38], [15861, 39], [16161, 39], [16460, 40], [16760, 40], [17059, 41], [17359, 41], [17658, 42], [17958, 42], [18257, 43], [18557, 43], [18858, 42], [19007, 193], [19307, 193], [19607, 193], [19907, 193], [20206, 194], [20506, 194], [20806, 194], [21106, 194], [21406, 194], [21706, 194], [22006, 194], [22306, 194], [22606, 194], [22905, 195], [23205, 195], [23505, 195], [23805, 195], [24105, 195], [24405, 195], [24705, 195], [25005, 195], [25304, 196], [25604, 196], [25904, 196], [26204, 196], [26504, 196], [26804, 196], [27104, 196], [27404, 196], [27704, 196], [28003, 197], [28303, 197], [28603, 197], [28903, 197], [29203, 197], [29503, 197], [29803, 197], [30103, 197], [30402, 198], [30702, 198], [31002, 198], [31302, 198], [31602, 198], [31902, 198], [32202, 198], [32502, 198], [32802, 198], [33101, 199], [33401, 199], [33701, 199], [34001, 199], [34301, 199], [34601, 199], [34901, 199], [35201, 199], [35500, 200], [35800, 200], [36100, 200], [36400, 200], [36700, 200], [37000, 200], [37300, 200], [37600, 200], [37900, 200], [38199, 201], [38499, 201], [38799, 201], [39099, 201], [39399, 201], [39699, 201], [39999, 201], [40299, 201], [40598, 202], [40898, 202], [41198, 202], [41498, 202], [41798, 202], [42098, 202], [42398, 202], [42698, 202], [42997, 203], [43297, 203], [43597, 203], [43897, 203], [44197, 203], [44497, 203], [44797, 203], [45097, 203], [45397, 203], [45696, 204], [45996, 204], [46296, 204], [46596, 204], [46896, 204], [47196, 204], [47496, 204], [47796, 204], [48095, 205], [48395, 205], [48695, 205], [48995, 205], [49295, 205], [49595, 205], [49895, 205], [50195, 205], [50495, 205], [50794, 206], [51094, 206], [51394, 206], [51694, 206], [51994, 206], [52294, 206], [52594, 206], [52894, 206], [53193, 207], [53493, 207], [53793, 207], [54093, 207], [54393, 207], [54693, 207], [54993, 207], [55293, 207], [55593, 207], [55892, 208], [56192, 208], [56492, 208], [56792, 208], [57092, 208], [57392, 208], [57692, 208], [57992, 208], [58291, 209], [58591, 54], [58655, 145], [58891, 48], [58961, 139], [59191, 44], [59265, 135], [59491, 42], [59567, 133], [59791, 40], [59869, 131], [60091, 38], [60171, 129], [60391, 37], [60472, 128], [60691, 35], [60774, 126], [60990, 34], [61076, 124], [61290, 33], [61377, 123], [61590, 32], [61678, 122], [61890, 31], [61979, 121], [62190, 30], [62280, 120], [62490, 29], [62581, 119], [62790, 28], [62882, 118], [63090, 27], [63183, 117], [63389, 28], [63483, 117], [63689, 27], [63784, 116], [63989, 26], [64085, 115], [64289, 25], [64386, 114], [64589, 24], [64687, 113], [64889, 24], [64987, 113], [65189, 24], [65287, 113], [65489, 23], [65588, 112], [65788, 24], [65888, 112], [66088, 24], [66188, 112], [66388, 23], [66489, 111], [66688, 23], [66789, 111], [66988, 23], [67089, 111], [67288, 22], [67390, 110], [67588, 22], [67690, 110], [67888, 22], [67990, 110], [68188, 21], [68291, 109], [68487, 22], [68591, 109], [68787, 22], [68891, 109], [69087, 22], [69191, 109], [69387, 22], [69491, 109], [69687, 22], [69791, 109], [69987, 22], [70091, 109], [70287, 22], [70391, 109], [70587, 22], [70691, 109], [70886, 24], [70990, 110], [71186, 24], [71290, 110], [71486, 24], [71590, 110], [71786, 24], [71890, 110], [72086, 24], [72190, 110], [72386, 24], [72490, 110], [72686, 24], [72790, 110], [72986, 24], [73090, 110], [73286, 24], [73390, 110], [73585, 26], [73689, 111], [73885, 26], [73989, 111], [74185, 26], [74289, 111], [74485, 27], [74588, 112], [74785, 28], [74887, 113], [75085, 28], [75187, 113], [75385, 29], [75486, 114], [75685, 29], [75786, 114], [75984, 31], [76085, 115], [76284, 31], [76385, 115], [76584, 32], [76684, 116], [76884, 33], [76983, 117], [77184, 33], [77283, 117], [77484, 34], [77582, 118], [77784, 34], [77882, 118], [78084, 35], [78181, 119], [78384, 36], [78480, 120], [78683, 38], [78779, 121], [78983, 39], [79078, 122], [79283, 41], [79376, 124], [79583, 42], [79675, 125], [79883, 43], [79974, 126], [80183, 44], [80273, 127], [80483, 45], [80572, 128], [80783, 46], [80871, 129], [81082, 47], [81170, 130], [81382, 45], [81469, 131], [81682, 43], [81768, 132], [81982, 41], [82067, 133], [82282, 40], [82366, 134], [82582, 39], [82664, 136], [82882, 38], [82964, 136], [83182, 37], [83265, 135], [83482, 35], [83566, 134], [83781, 35], [83867, 133], [84081, 35], [84168, 132], [84381, 34], [84469, 131], [84681, 33], [84770, 130], [84981, 32], [85070, 130], [85281, 32], [85371, 129], [85581, 31], [85672, 128], [85881, 30], [85972, 128], [86180, 30], [86273, 127], [86480, 30], [86574, 126], [86780, 30], [86874, 126], [87080, 29], [87174, 126], [87380, 29], [87475, 125], [87680, 28], [87775, 125], [87980, 28], [88076, 124], [88280, 27], [88376, 124], [88580, 27], [88676, 124], [88879, 28], [88977, 123], [89179, 27], [89277, 123], [89479, 27], [89577, 123], [89779, 27], [89877, 123]], "point": [189, 149]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-02.45|+01.95|+02.93"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [79, 1, 299, 300], "mask": [[284, 16], [584, 16], [884, 16], [1183, 17], [1483, 17], [1782, 18], [2082, 18], [2381, 19], [2681, 19], [2980, 20], [3280, 20], [3579, 21], [3879, 21], [4179, 21], [4478, 22], [4778, 22], [5077, 23], [5377, 23], [5676, 24], [5976, 24], [6275, 25], [6575, 25], [6875, 25], [7174, 26], [7474, 26], [7773, 27], [8073, 27], [8372, 28], [8672, 28], [8971, 29], [9271, 29], [9570, 30], [9870, 30], [10170, 30], [10469, 31], [10769, 31], [11068, 32], [11368, 32], [11667, 33], [11967, 33], [12266, 34], [12566, 34], [12866, 34], [13165, 35], [13465, 35], [13764, 36], [14064, 36], [14363, 37], [14663, 37], [14962, 38], [15262, 38], [15562, 38], [15861, 39], [16161, 39], [16460, 40], [16760, 40], [17059, 41], [17359, 41], [17658, 42], [17958, 42], [18257, 43], [18557, 43], [18858, 42], [19007, 193], [19307, 193], [19607, 193], [19907, 193], [20206, 194], [20506, 194], [20806, 194], [21106, 194], [21406, 194], [21706, 194], [22006, 194], [22306, 194], [22606, 194], [22905, 195], [23205, 195], [23505, 195], [23805, 195], [24105, 195], [24405, 195], [24705, 195], [25005, 195], [25304, 196], [25604, 196], [25904, 196], [26204, 196], [26504, 196], [26804, 196], [27104, 196], [27404, 196], [27704, 196], [28003, 197], [28303, 197], [28603, 197], [28903, 197], [29203, 197], [29503, 197], [29803, 197], [30103, 197], [30402, 198], [30702, 198], [31002, 198], [31302, 198], [31602, 198], [31902, 198], [32202, 198], [32502, 198], [32802, 198], [33101, 199], [33401, 199], [33701, 199], [34001, 199], [34301, 199], [34601, 199], [34901, 199], [35201, 199], [35500, 200], [35800, 200], [36100, 200], [36400, 200], [36700, 200], [37000, 200], [37300, 200], [37600, 200], [37900, 200], [38199, 201], [38499, 201], [38799, 201], [39099, 201], [39399, 201], [39699, 201], [39999, 201], [40299, 201], [40598, 202], [40898, 202], [41198, 202], [41498, 202], [41798, 202], [42098, 202], [42398, 202], [42698, 202], [42997, 203], [43297, 203], [43597, 203], [43897, 203], [44197, 203], [44497, 203], [44797, 203], [45097, 203], [45397, 203], [45696, 204], [45996, 204], [46296, 204], [46596, 204], [46896, 204], [47196, 204], [47496, 204], [47796, 204], [48095, 205], [48395, 205], [48695, 205], [48995, 205], [49295, 205], [49595, 205], [49895, 205], [50195, 205], [50495, 205], [50794, 206], [51094, 206], [51394, 206], [51694, 206], [51994, 206], [52294, 206], [52594, 206], [52894, 206], [53193, 207], [53493, 207], [53793, 207], [54093, 207], [54393, 207], [54693, 207], [54993, 207], [55293, 207], [55593, 207], [55892, 208], [56192, 208], [56492, 208], [56792, 208], [57092, 208], [57392, 208], [57692, 208], [57992, 208], [58291, 209], [58591, 209], [58891, 209], [59191, 209], [59491, 209], [59791, 209], [60091, 209], [60391, 209], [60691, 209], [60990, 210], [61290, 210], [61590, 210], [61890, 210], [62190, 210], [62490, 210], [62790, 210], [63090, 210], [63389, 211], [63689, 211], [63989, 211], [64289, 211], [64589, 211], [64889, 211], [65189, 211], [65489, 211], [65788, 212], [66088, 212], [66388, 212], [66688, 212], [66988, 212], [67288, 212], [67588, 212], [67888, 212], [68188, 212], [68487, 213], [68787, 213], [69087, 213], [69387, 213], [69687, 213], [69987, 213], [70287, 213], [70587, 213], [70886, 214], [71186, 214], [71486, 214], [71786, 214], [72086, 214], [72386, 214], [72686, 214], [72986, 214], [73286, 55], [73344, 156], [73585, 54], [73646, 154], [73885, 53], [73946, 154], [74185, 53], [74246, 154], [74485, 53], [74546, 154], [74785, 53], [74846, 154], [75085, 53], [75146, 154], [75385, 53], [75446, 154], [75685, 53], [75746, 154], [75984, 54], [76046, 154], [76284, 53], [76346, 154], [76584, 53], [76646, 154], [76884, 54], [76946, 154], [77184, 55], [77246, 154], [77484, 55], [77546, 154], [77784, 55], [77846, 154], [78084, 55], [78146, 154], [78384, 55], [78446, 154], [78683, 56], [78746, 154], [78983, 56], [79046, 154], [79283, 56], [79346, 154], [79583, 56], [79646, 154], [79883, 56], [79946, 154], [80183, 56], [80246, 154], [80483, 51], [80550, 150], [80783, 49], [80853, 147], [81082, 47], [81155, 145], [81382, 45], [81457, 143], [81682, 43], [81759, 141], [81982, 41], [82060, 140], [82282, 40], [82362, 138], [82582, 39], [82663, 137], [82882, 38], [82964, 51], [83038, 62], [83182, 37], [83265, 40], [83342, 58], [83482, 35], [83566, 38], [83642, 58], [83781, 35], [83867, 37], [83943, 57], [84081, 35], [84168, 37], [84243, 57], [84381, 34], [84469, 36], [84543, 57], [84681, 33], [84770, 35], [84843, 57], [84981, 32], [85070, 35], [85142, 58], [85281, 32], [85371, 35], [85442, 58], [85581, 31], [85672, 34], [85742, 58], [85881, 30], [85972, 34], [86042, 58], [86180, 30], [86273, 34], [86342, 58], [86480, 30], [86574, 33], [86642, 58], [86780, 30], [86874, 33], [86942, 58], [87080, 29], [87174, 34], [87242, 58], [87380, 29], [87475, 33], [87542, 58], [87680, 28], [87775, 33], [87842, 58], [87980, 28], [88076, 32], [88141, 59], [88280, 27], [88376, 33], [88441, 59], [88580, 27], [88676, 33], [88741, 59], [88879, 28], [88977, 32], [89041, 59], [89179, 27], [89277, 33], [89341, 59], [89479, 27], [89577, 33], [89641, 59], [89779, 27], [89877, 33], [89941, 59]], "point": [189, 149]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan6", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 0.75, "y": 0.9009992, "z": 2.25}, "object_poses": [{"objectName": "Pan_adb25946", "position": {"x": -2.538684, "y": 0.910509467, "z": 1.2309432}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_b563bb4b", "position": {"x": -2.485795, "y": 1.1255461, "z": -0.6021706}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": -0.7309452, "y": 0.9104642, "z": 0.423983335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": 1.51903379, "y": 0.910942, "z": -0.8168047}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "Kettle_0884e440", "position": {"x": -2.6053, "y": 1.93959117, "z": -0.6343242}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_0884e440", "position": {"x": -2.564922, "y": 1.53865314, "z": 2.47748756}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": 1.37260914, "y": 0.9220729, "z": -1.15027642}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": -2.20641732, "y": 0.925899863, "z": 1.830605}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_bc458cba", "position": {"x": 1.4123, "y": 0.9295, "z": -0.3359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_bc458cba", "position": {"x": 1.6916, "y": 0.9295, "z": 0.0642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": 1.646, "y": 0.21585986, "z": 0.59640646}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": -2.538684, "y": 0.9362496, "z": 1.53077412}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": 1.68401384, "y": 0.936449647, "z": 0.494945318}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": 1.512661, "y": 0.9130652, "z": 0.435648441}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": -0.211414054, "y": 0.912865162, "z": 1.76401663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.31330013, "y": 0.112172425, "z": 0.437999874}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": -2.68515635, "y": 1.53261185, "z": 1.26514387}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": -0.08153126, "y": 0.905899942, "z": 1.094}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -0.341296852, "y": 0.994053841, "z": 0.423983335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -2.62175035, "y": 0.994053841, "z": 0.6312814}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_209e2ea9", "position": {"x": -0.341296852, "y": 0.996719658, "z": 1.54067779}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": -0.471179664, "y": 0.9725113, "z": 0.870661139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_adb25946", "position": {"x": 0.0, "y": 0.9, "z": 0.948}, "rotation": {"x": 0.0, "y": 29.9999466, "z": 0.0}}, {"objectName": "Knife_f4ae1c7a", "position": {"x": -2.2879, "y": 0.7987526, "z": 0.391211122}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Apple_57ff6e2f", "position": {"x": 1.56212735, "y": 0.246842712, "z": 0.706531167}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_988570b5", "position": {"x": -2.45561719, "y": 0.9080944, "z": 1.53077412}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_850ba9bc", "position": {"x": -0.341296852, "y": 0.911448538, "z": 0.870661139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_209e2ea9", "position": {"x": 0.0483515263, "y": 0.996719658, "z": 1.76401663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_7b0d90af", "position": {"x": -0.6010624, "y": 0.913369656, "z": 1.76401663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_2802642b", "position": {"x": -0.08153126, "y": 0.94152844, "z": 0.423983335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_0884e440", "position": {"x": 0.8544904, "y": 0.900000036, "z": -1.790359}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_b563bb4b", "position": {"x": -2.51302266, "y": 0.6311115, "z": -0.693344533}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_81e9b3dd", "position": {"x": 0.19524762, "y": 0.9059, "z": -1.625}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6f8b6e53", "position": {"x": -0.341296852, "y": 0.994053841, "z": 1.31733882}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_b0ad5ef5", "position": {"x": -2.485795, "y": 1.08702564, "z": -0.9797808}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_b57df01c", "position": {"x": 1.68793631, "y": 0.187196583, "z": 0.706531167}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_3e5dadd0", "position": {"x": -0.471179664, "y": 0.925899863, "z": 1.094}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_40552007", "position": {"x": -0.08153126, "y": 1.01518536, "z": 0.6473222}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f05a14b3", "position": {"x": 1.31852293, "y": 0.9209856, "z": -1.20436263}, "rotation": {"x": 0.0, "y": 225.000031, "z": 0.0}}, {"objectName": "PepperShaker_e9198ae5", "position": {"x": -2.31330013, "y": 0.109977961, "z": 1.216075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_1f5d88fd", "position": {"x": -2.37539816, "y": 1.58334851, "z": -1.77707756}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_bc458cba", "position": {"x": -2.289484, "y": 0.9146199, "z": 0.6312814}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_0b8e1a97", "position": {"x": -0.7309452, "y": 0.9120294, "z": 0.870661139}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9b863229", "position": {"x": -2.37433982, "y": 0.6371076, "z": -0.783}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_db7f1139", "position": {"x": -2.57825041, "y": 1.2954129, "z": -0.7909757}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}], "object_toggles": [], "random_seed": 947069630, "scene_num": 6}, "task_id": "trial_T20190908_165525_475387", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3F7G1FSFWQPLE_358010RM5HKLUJL1QM80EKUSCY6XVZ", "high_descs": ["Turn around and walk across the room to the counter, then hang a right and walk over to the fridge.", "Open the fridge and remove the grey cup from the fridge, then close the door.", "Walk over to the microwave on the counter to your right.", "Put the grey cup in the microwave and turn it on, after a couple seconds open the microwave and take out the now heated grey cup then close the door.", "Look up at the upper cabinets above the microwave.", "Open the rightmost cabinet door and put the heated grey cup inside to the right of the kettle, then close the door."], "task_desc": "Put a heated grey cup in an upper cabinet.", "votes": [1, 1]}, {"assignment_id": "A1G1OO4IWM8EP5_3TK8OJTYM4CGLAJA4VYJK78PAZ1VPD", "high_descs": ["Turn around from the wall and start walking to the counter across the room, turn right at the counter and walk to the refrigerator.  ", "Open the freezer door, grab a cup, and shut the freezer door.", "Carry the cup and turn right and walk to the wall.  Turn left and face the microwave.", "Open the microwave door, put the cup in the microwave, turn on the microwave, open the door and take out cup.", "Open the cabinet above the microwave.", "Put the cup in the cabinet and close the door."], "task_desc": "Heat up a cup from the freezer and put it in a cabinet.", "votes": [1, 1]}, {"assignment_id": "A1MKCTVKE7J0ZP_3WOKGM4L747I796TMYXFAED96XCO0F", "high_descs": ["Turn around and go across the room, then turn right and go forward to the fridge.", "Open the fridge and pick up the black cup from inside it.", "Turn right, go to the wall, then turn left to face the microwave.", "Open the microwave, put the cup inside and heat it, then take it out.", "Look up to the cabinet above the microwave. ", "Open the cabinet and put the cup inside, then close it."], "task_desc": "Put a warm cup in the cabinet.", "votes": [1, 1]}]}}