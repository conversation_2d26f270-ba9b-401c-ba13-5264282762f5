{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000253.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000254.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000255.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000256.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000266.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 48}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-1|-5|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-1.160242676, -1.160242676, -8.25311852, -8.25311852, 3.188120128, 3.188120128]], "coordinateReceptacleObjectId": ["SinkBasin", [-0.5192, -0.5192, -8.032, -8.032, 3.0112, 3.0112]], "forceVisible": true, "objectId": "Egg|-00.29|+00.80|-02.06"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|-5|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.308, 7.308, -5.416, -5.416, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.83|+00.90|-01.35"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|6|1|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "garbagecan"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-1.160242676, -1.160242676, -8.25311852, -8.25311852, 3.188120128, 3.188120128]], "coordinateReceptacleObjectId": ["GarbageCan", [7.696, 7.696, 0.552, 0.552, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|-00.29|+00.80|-02.06", "receptacleObjectId": "GarbageCan|+01.92|+00.00|+00.14"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.29|+00.80|-02.06"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [152, 138, 165, 152], "mask": [[41256, 7], [41555, 9], [41854, 11], [42153, 12], [42453, 13], [42753, 13], [43052, 14], [43352, 14], [43652, 14], [43952, 14], [44253, 13], [44553, 12], [44854, 11], [45155, 9], [45456, 6]], "point": [158, 144]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [78, 19, 297, 144], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16281, 210], [16581, 212], [16880, 214], [17180, 215], [17479, 217], [17779, 218], [18078, 219], [18378, 220], [18678, 220], [18978, 220], [19278, 220], [19578, 220], [19878, 220], [20178, 220], [20478, 220], [20778, 220], [21078, 220], [21378, 220], [21678, 220], [21978, 220], [22278, 220], [22578, 220], [22878, 219], [23178, 219], [23478, 218], [23779, 217], [24079, 216], [24379, 216], [24679, 216], [24979, 215], [25280, 214], [25580, 213], [25880, 213], [26180, 212], [26480, 212], [26781, 210], [27081, 210], [27381, 210], [27681, 209], [27982, 208], [28282, 207], [28582, 207], [28882, 206], [29182, 206], [29483, 204], [29783, 204], [30083, 204], [30383, 203], [30684, 202], [30984, 201], [31284, 201], [31584, 200], [31884, 200], [32185, 198], [32485, 198], [32785, 198], [33085, 197], [33385, 197], [33686, 195], [33986, 195], [34286, 194], [34586, 194], [34887, 192], [35187, 192], [35487, 192], [35787, 191], [36087, 191], [36388, 189], [36688, 189], [36988, 188], [37288, 188], [37588, 188], [37889, 186], [38189, 186], [38489, 185], [38789, 185], [39090, 183], [39390, 183], [39690, 182], [39990, 182], [40290, 182], [40591, 180], [40891, 180], [41191, 179], [41491, 179], [41792, 177], [42092, 177], [42392, 176], [42692, 176], [42992, 175]], "point": [187, 80]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.29|+00.80|-02.06", "placeStationary": true, "receptacleObjectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 19, 297, 268], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16267, 224], [16567, 226], [16866, 228], [17165, 230], [17465, 231], [17764, 233], [18064, 233], [18363, 235], [18662, 236], [18962, 236], [19261, 237], [19561, 237], [19860, 238], [20160, 238], [20459, 239], [20759, 239], [21058, 240], [21358, 240], [21657, 241], [21957, 86], [22054, 144], [22256, 82], [22360, 138], [22555, 80], [22663, 135], [22855, 77], [22965, 132], [23154, 76], [23267, 130], [23454, 74], [23569, 127], [23753, 74], [23870, 126], [24053, 73], [24171, 124], [24352, 73], [24472, 123], [24652, 72], [24773, 122], [24951, 73], [25074, 120], [25251, 73], [25374, 120], [25550, 73], [25674, 119], [25850, 73], [25974, 119], [26149, 74], [26274, 118], [26449, 74], [26574, 118], [26748, 75], [26874, 117], [27048, 76], [27174, 117], [27347, 77], [27473, 118], [27647, 77], [27773, 117], [27946, 78], [28073, 117], [28246, 79], [28373, 116], [28545, 80], [28672, 117], [28845, 81], [28972, 116], [29144, 82], [29271, 117], [29444, 82], [29571, 116], [29743, 84], [29870, 117], [30043, 84], [30170, 117], [30342, 86], [30470, 116], [30642, 86], [30769, 117], [30941, 88], [31068, 117], [31241, 89], [31367, 118], [31540, 91], [31666, 118], [31840, 93], [31965, 119], [32139, 95], [32263, 120], [32439, 96], [32562, 121], [32738, 99], [32860, 123], [33038, 102], [33157, 125], [33337, 108], [33453, 129], [33637, 244], [33936, 245], [34236, 244], [34535, 245], [34835, 244], [35134, 245], [35434, 245], [35733, 245], [36033, 245], [36332, 245], [36632, 245], [36931, 245], [37230, 246], [37530, 246], [37829, 246], [38129, 246], [38428, 246], [38728, 246], [39027, 246], [39327, 246], [39626, 246], [39926, 246], [40225, 247], [40525, 246], [40824, 247], [41124, 74], [41329, 41], [41423, 75], [41630, 40], [41723, 75], [41930, 39], [42022, 75], [42230, 39], [42322, 75], [42531, 37], [42621, 76], [42831, 37], [42921, 76], [43132, 35], [43220, 77], [43520, 76], [43819, 77], [44119, 77], [44418, 78], [44718, 77], [45017, 78], [45317, 78], [45616, 79], [45916, 79], [46215, 79], [46515, 79], [46814, 80], [47114, 80], [47413, 81], [47713, 80], [48012, 81], [48312, 81], [48611, 82], [48911, 82], [49210, 82], [49510, 82], [49809, 83], [50109, 83], [50408, 83], [50708, 83], [51007, 84], [51307, 84], [51606, 85], [51905, 85], [52205, 85], [52504, 86], [52804, 86], [53103, 87], [53403, 86], [53702, 87], [54002, 87], [54301, 88], [54601, 88], [54900, 88], [55200, 88], [55500, 88], [55800, 88], [56100, 87], [56400, 87], [56700, 87], [57000, 87], [57300, 87], [57600, 86], [57900, 86], [58200, 86], [58500, 86], [58800, 86], [59100, 85], [59400, 85], [59700, 85], [60000, 85], [60300, 85], [60600, 84], [60900, 84], [61200, 84], [61500, 84], [61800, 83], [62100, 83], [62400, 83], [62700, 83], [63000, 83], [63300, 82], [63600, 82], [63900, 82], [64200, 82], [64501, 81], [64801, 80], [65102, 79], [65403, 78], [65704, 77], [66005, 76], [66305, 75], [66606, 74], [66907, 73], [67208, 72], [67508, 71], [67809, 70], [68110, 69], [68411, 68], [68711, 68], [69012, 66], [69313, 65], [69614, 64], [69915, 63], [70215, 63], [70516, 61], [70817, 60], [71118, 59], [71418, 59], [71719, 58], [72020, 56], [72321, 55], [72621, 55], [72922, 54], [73223, 52], [73524, 51], [73824, 51], [74125, 50], [74426, 49], [74727, 47], [75028, 46], [75328, 46], [75629, 45], [75930, 44], [76231, 42], [76531, 42], [76832, 41], [77133, 40], [77434, 39], [77734, 38], [78035, 40], [78336, 41], [78637, 41], [78938, 33], [78972, 6], [79238, 33], [79273, 5], [79540, 31], [79574, 2], [79844, 27], [80148, 22]], "point": [148, 136]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 297, 268], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16267, 224], [16567, 226], [16866, 228], [17165, 230], [17465, 231], [17764, 233], [18064, 233], [18363, 235], [18662, 236], [18962, 236], [19261, 237], [19561, 237], [19860, 238], [20160, 238], [20459, 239], [20759, 239], [21058, 240], [21358, 240], [21657, 241], [21957, 86], [22054, 144], [22256, 82], [22360, 138], [22555, 80], [22663, 135], [22855, 77], [22965, 132], [23154, 76], [23267, 130], [23454, 74], [23569, 127], [23753, 74], [23870, 126], [24053, 73], [24171, 124], [24352, 73], [24472, 123], [24652, 72], [24773, 122], [24951, 73], [25074, 120], [25251, 73], [25374, 120], [25550, 73], [25674, 119], [25850, 73], [25974, 119], [26149, 74], [26274, 118], [26449, 74], [26574, 118], [26748, 75], [26874, 18], [26896, 95], [27048, 76], [27174, 16], [27198, 93], [27347, 77], [27473, 15], [27499, 92], [27647, 77], [27773, 14], [27799, 91], [27946, 78], [28073, 14], [28100, 90], [28246, 79], [28373, 13], [28401, 88], [28545, 80], [28672, 13], [28701, 88], [28845, 81], [28972, 13], [29001, 87], [29144, 82], [29271, 14], [29301, 87], [29444, 82], [29571, 13], [29602, 85], [29743, 84], [29870, 14], [29902, 85], [30043, 84], [30170, 14], [30201, 86], [30342, 86], [30470, 14], [30501, 85], [30642, 86], [30769, 15], [30801, 85], [30941, 88], [31068, 17], [31101, 84], [31241, 89], [31367, 18], [31400, 85], [31540, 91], [31666, 20], [31700, 84], [31840, 93], [31965, 21], [31999, 85], [32139, 95], [32263, 24], [32298, 85], [32439, 96], [32562, 27], [32596, 87], [32738, 99], [32860, 123], [33038, 102], [33157, 125], [33337, 108], [33453, 129], [33637, 244], [33936, 245], [34236, 244], [34535, 245], [34835, 244], [35134, 245], [35434, 245], [35733, 245], [36033, 245], [36332, 245], [36632, 245], [36931, 245], [37230, 246], [37530, 246], [37829, 246], [38129, 246], [38428, 246], [38728, 246], [39027, 246], [39327, 246], [39626, 246], [39926, 246], [40225, 247], [40525, 246], [40824, 247], [41124, 74], [41329, 41], [41423, 75], [41630, 40], [41723, 75], [41930, 39], [42022, 75], [42230, 39], [42322, 75], [42531, 37], [42621, 76], [42831, 37], [42921, 76], [43132, 35], [43220, 77], [43520, 76], [43819, 77], [44119, 77], [44418, 78], [44718, 77], [45017, 78], [45317, 78], [45616, 79], [45916, 79], [46215, 79], [46515, 79], [46814, 80], [47114, 80], [47413, 81], [47713, 80], [48012, 81], [48312, 81], [48611, 82], [48911, 82], [49210, 82], [49510, 82], [49809, 83], [50109, 83], [50408, 83], [50708, 83], [51007, 84], [51307, 84], [51606, 85], [51905, 85], [52205, 85], [52504, 86], [52804, 86], [53103, 87], [53403, 86], [53702, 87], [54002, 87], [54301, 88], [54601, 88], [54900, 88], [55200, 88], [55500, 88], [55800, 88], [56100, 87], [56400, 87], [56700, 87], [57000, 87], [57300, 87], [57600, 86], [57900, 86], [58200, 86], [58500, 86], [58800, 86], [59100, 85], [59400, 85], [59700, 85], [60000, 85], [60300, 85], [60600, 84], [60900, 84], [61200, 84], [61500, 84], [61800, 83], [62100, 83], [62400, 83], [62700, 83], [63000, 83], [63300, 82], [63600, 82], [63900, 82], [64200, 82], [64501, 81], [64801, 80], [65102, 79], [65403, 78], [65704, 77], [66005, 76], [66305, 75], [66606, 74], [66907, 73], [67208, 72], [67508, 71], [67809, 70], [68110, 69], [68411, 68], [68711, 68], [69012, 66], [69313, 65], [69614, 64], [69915, 63], [70215, 63], [70516, 61], [70817, 60], [71118, 59], [71418, 59], [71719, 58], [72020, 56], [72321, 55], [72621, 55], [72922, 54], [73223, 52], [73524, 51], [73824, 51], [74125, 50], [74426, 49], [74727, 47], [75028, 46], [75328, 46], [75629, 45], [75930, 44], [76231, 42], [76531, 42], [76832, 41], [77133, 40], [77434, 39], [77734, 38], [78035, 40], [78336, 41], [78637, 41], [78938, 33], [78972, 6], [79238, 33], [79273, 5], [79540, 31], [79574, 2], [79844, 27], [80148, 22]], "point": [148, 136]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.308, 7.308, -5.416, -5.416, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [78, 19, 297, 144], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16281, 210], [16581, 212], [16880, 214], [17180, 215], [17479, 217], [17779, 218], [18078, 219], [18378, 220], [18678, 220], [18978, 220], [19278, 220], [19578, 220], [19878, 220], [20178, 220], [20478, 220], [20778, 220], [21078, 220], [21378, 220], [21678, 220], [21978, 220], [22278, 220], [22578, 220], [22878, 219], [23178, 219], [23478, 218], [23779, 217], [24079, 216], [24379, 216], [24679, 216], [24979, 215], [25280, 214], [25580, 213], [25880, 213], [26180, 212], [26480, 212], [26781, 210], [27081, 210], [27381, 210], [27681, 209], [27982, 208], [28282, 207], [28582, 207], [28882, 206], [29182, 206], [29483, 204], [29783, 204], [30083, 204], [30383, 203], [30684, 202], [30984, 201], [31284, 201], [31584, 200], [31884, 200], [32185, 198], [32485, 198], [32785, 198], [33085, 197], [33385, 197], [33686, 195], [33986, 195], [34286, 194], [34586, 194], [34887, 192], [35187, 192], [35487, 192], [35787, 191], [36087, 191], [36388, 189], [36688, 189], [36988, 188], [37288, 188], [37588, 188], [37889, 186], [38189, 186], [38489, 185], [38789, 185], [39090, 183], [39390, 183], [39690, 182], [39990, 182], [40290, 182], [40591, 180], [40891, 180], [41191, 179], [41491, 179], [41792, 177], [42092, 177], [42392, 176], [42692, 176], [42992, 175]], "point": [187, 80]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.308, 7.308, -5.416, -5.416, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [78, 19, 297, 144], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16281, 210], [16581, 212], [16880, 214], [17180, 215], [17479, 217], [17779, 218], [18078, 219], [18378, 220], [18678, 220], [18978, 220], [19278, 220], [19578, 220], [19878, 220], [20178, 220], [20478, 220], [20778, 220], [21078, 220], [21378, 220], [21678, 220], [21978, 220], [22278, 220], [22578, 220], [22878, 219], [23178, 219], [23478, 218], [23779, 217], [24079, 216], [24379, 216], [24679, 216], [24979, 215], [25280, 214], [25580, 213], [25880, 213], [26180, 212], [26480, 212], [26781, 210], [27081, 210], [27381, 210], [27681, 209], [27982, 208], [28282, 207], [28582, 207], [28882, 206], [29182, 206], [29483, 204], [29783, 204], [30083, 204], [30383, 203], [30684, 202], [30984, 201], [31284, 201], [31584, 200], [31884, 200], [32185, 198], [32485, 198], [32785, 198], [33085, 197], [33385, 197], [33686, 195], [33986, 195], [34286, 194], [34586, 194], [34887, 192], [35187, 192], [35487, 192], [35787, 191], [36087, 191], [36388, 189], [36688, 189], [36988, 188], [37288, 188], [37588, 188], [37889, 186], [38189, 186], [38489, 185], [38789, 185], [39090, 183], [39390, 183], [39690, 182], [39990, 182], [40290, 182], [40591, 180], [40891, 180], [41191, 179], [41491, 179], [41792, 177], [42092, 177], [42392, 176], [42692, 176], [42992, 175]], "point": [187, 80]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [78, 19, 297, 144], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16281, 210], [16581, 212], [16880, 214], [17180, 215], [17479, 217], [17779, 218], [18078, 219], [18378, 220], [18678, 220], [18978, 220], [19278, 220], [19578, 220], [19878, 220], [20178, 220], [20478, 220], [20778, 220], [21078, 220], [21378, 220], [21678, 220], [21978, 220], [22278, 220], [22578, 220], [22878, 219], [23178, 219], [23478, 218], [23779, 217], [24079, 216], [24379, 216], [24679, 216], [24979, 215], [25280, 214], [25580, 213], [25880, 213], [26180, 212], [26480, 212], [26781, 210], [27081, 210], [27381, 210], [27681, 209], [27982, 208], [28282, 207], [28582, 207], [28882, 206], [29182, 206], [29483, 204], [29783, 204], [30083, 204], [30383, 203], [30684, 202], [30984, 201], [31284, 201], [31584, 200], [31884, 200], [32185, 198], [32485, 198], [32785, 198], [33085, 197], [33385, 197], [33686, 195], [33986, 195], [34286, 194], [34586, 194], [34887, 192], [35187, 192], [35487, 192], [35787, 191], [36087, 191], [36388, 189], [36688, 189], [36988, 188], [37288, 188], [37588, 188], [37889, 186], [38189, 186], [38489, 185], [38789, 185], [39090, 183], [39390, 183], [39690, 182], [39990, 182], [40290, 182], [40591, 180], [40891, 180], [41191, 179], [41491, 179], [41792, 177], [42092, 177], [42392, 176], [42692, 176], [42992, 175]], "point": [187, 80]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.29|+00.80|-02.06"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [184, 90, 201, 109], "mask": [[26892, 4], [27190, 8], [27488, 11], [27787, 12], [28087, 13], [28386, 15], [28685, 16], [28985, 16], [29285, 16], [29584, 18], [29884, 18], [30184, 17], [30484, 17], [30784, 17], [31085, 16], [31385, 15], [31686, 14], [31986, 13], [32287, 11], [32589, 7]], "point": [192, 98]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.83|+00.90|-01.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 297, 268], "mask": [[5496, 165], [5795, 167], [6095, 168], [6394, 170], [6694, 171], [6993, 173], [7293, 174], [7593, 174], [7892, 176], [8192, 177], [8491, 179], [8791, 180], [9091, 181], [9390, 183], [9690, 184], [9989, 185], [10289, 186], [10588, 188], [10888, 189], [11188, 190], [11487, 192], [11787, 193], [12086, 195], [12386, 195], [12685, 197], [12985, 198], [13285, 199], [13584, 201], [13884, 202], [14183, 204], [14483, 204], [14782, 206], [15082, 207], [15382, 208], [15681, 210], [15981, 210], [16267, 224], [16567, 226], [16866, 228], [17165, 230], [17465, 231], [17764, 233], [18064, 233], [18363, 235], [18662, 236], [18962, 236], [19261, 237], [19561, 237], [19860, 238], [20160, 238], [20459, 239], [20759, 239], [21058, 240], [21358, 240], [21657, 241], [21957, 86], [22054, 144], [22256, 82], [22360, 138], [22555, 80], [22663, 135], [22855, 77], [22965, 132], [23154, 76], [23267, 130], [23454, 74], [23569, 127], [23753, 74], [23870, 126], [24053, 73], [24171, 124], [24352, 73], [24472, 123], [24652, 72], [24773, 122], [24951, 73], [25074, 120], [25251, 73], [25374, 120], [25550, 73], [25674, 119], [25850, 73], [25974, 119], [26149, 74], [26274, 118], [26449, 74], [26574, 118], [26748, 75], [26874, 117], [27048, 76], [27174, 117], [27347, 77], [27473, 118], [27647, 77], [27773, 117], [27946, 78], [28073, 117], [28246, 79], [28373, 116], [28545, 80], [28672, 117], [28845, 81], [28972, 116], [29144, 82], [29271, 117], [29444, 82], [29571, 116], [29743, 84], [29870, 117], [30043, 84], [30170, 117], [30342, 86], [30470, 116], [30642, 86], [30769, 117], [30941, 88], [31068, 117], [31241, 89], [31367, 118], [31540, 91], [31666, 118], [31840, 93], [31965, 119], [32139, 95], [32263, 120], [32439, 96], [32562, 121], [32738, 99], [32860, 123], [33038, 102], [33157, 125], [33337, 108], [33453, 129], [33637, 244], [33936, 245], [34236, 244], [34535, 245], [34835, 244], [35134, 245], [35434, 245], [35733, 245], [36033, 245], [36332, 245], [36632, 245], [36931, 245], [37230, 246], [37530, 246], [37829, 246], [38129, 246], [38428, 246], [38728, 246], [39027, 246], [39327, 246], [39626, 246], [39926, 246], [40225, 247], [40525, 246], [40824, 247], [41124, 74], [41329, 41], [41423, 75], [41630, 40], [41723, 75], [41930, 39], [42022, 75], [42230, 39], [42322, 75], [42531, 37], [42621, 76], [42831, 37], [42921, 76], [43132, 35], [43220, 77], [43520, 76], [43819, 77], [44119, 77], [44418, 78], [44718, 77], [45017, 78], [45317, 78], [45616, 79], [45916, 79], [46215, 79], [46515, 79], [46814, 80], [47114, 80], [47413, 81], [47713, 80], [48012, 81], [48312, 81], [48611, 82], [48911, 82], [49210, 82], [49510, 82], [49809, 83], [50109, 83], [50408, 83], [50708, 83], [51007, 84], [51307, 84], [51606, 85], [51905, 85], [52205, 85], [52504, 86], [52804, 86], [53103, 87], [53403, 86], [53702, 87], [54002, 87], [54301, 88], [54601, 88], [54900, 88], [55200, 88], [55500, 88], [55800, 88], [56100, 87], [56400, 87], [56700, 87], [57000, 87], [57300, 87], [57600, 86], [57900, 86], [58200, 86], [58500, 86], [58800, 86], [59100, 85], [59400, 85], [59700, 85], [60000, 85], [60300, 85], [60600, 84], [60900, 84], [61200, 84], [61500, 84], [61800, 83], [62100, 83], [62400, 83], [62700, 83], [63000, 83], [63300, 82], [63600, 82], [63900, 82], [64200, 82], [64501, 81], [64801, 80], [65102, 79], [65403, 78], [65704, 77], [66005, 76], [66305, 75], [66606, 74], [66907, 73], [67208, 72], [67508, 71], [67809, 70], [68110, 69], [68411, 68], [68711, 68], [69012, 66], [69313, 65], [69614, 64], [69915, 63], [70215, 63], [70516, 61], [70817, 60], [71118, 59], [71418, 59], [71719, 58], [72020, 56], [72321, 55], [72621, 55], [72922, 54], [73223, 52], [73524, 51], [73824, 51], [74125, 50], [74426, 49], [74727, 47], [75028, 46], [75328, 46], [75629, 45], [75930, 44], [76231, 42], [76531, 42], [76832, 41], [77133, 40], [77434, 39], [77734, 38], [78035, 40], [78336, 41], [78637, 41], [78938, 33], [78972, 6], [79238, 33], [79273, 5], [79540, 31], [79574, 2], [79844, 27], [80148, 22]], "point": [148, 136]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.29|+00.80|-02.06", "placeStationary": true, "receptacleObjectId": "GarbageCan|+01.92|+00.00|+00.14"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [136, 148, 216, 245], "mask": [[44275, 17], [44549, 49], [44847, 53], [45144, 59], [45443, 61], [45742, 63], [46041, 65], [46340, 67], [46639, 69], [46939, 69], [47239, 70], [47539, 70], [47839, 70], [48138, 72], [48438, 72], [48738, 72], [49038, 72], [49338, 72], [49638, 72], [49938, 72], [50238, 73], [50538, 73], [50838, 73], [51138, 73], [51438, 73], [51738, 73], [52037, 74], [52337, 74], [52637, 75], [52937, 75], [53237, 75], [53537, 75], [53837, 75], [54137, 75], [54437, 75], [54737, 76], [55037, 76], [55337, 76], [55637, 76], [55937, 76], [56237, 76], [56537, 76], [56837, 77], [57137, 77], [57437, 77], [57737, 77], [58037, 77], [58337, 77], [58637, 77], [58937, 77], [59237, 78], [59537, 78], [59837, 78], [60137, 78], [60437, 78], [60737, 78], [61037, 78], [61337, 78], [61637, 79], [61937, 79], [62237, 79], [62537, 79], [62837, 79], [63136, 80], [63436, 80], [63736, 80], [64036, 80], [64336, 81], [64636, 38], [64681, 36], [64936, 37], [64982, 35], [65236, 36], [65283, 34], [65536, 35], [65584, 33], [65836, 34], [65884, 33], [66136, 34], [66185, 32], [66436, 33], [66485, 32], [66736, 32], [66785, 32], [67037, 31], [67085, 32], [67337, 30], [67385, 32], [67637, 30], [67684, 33], [67937, 30], [67985, 32], [68237, 30], [68284, 32], [68538, 28], [68584, 32], [68838, 77], [69139, 76], [69440, 74], [69741, 73], [70041, 72], [70342, 70], [70644, 66], [70946, 63], [71253, 54], [71557, 47], [71859, 35], [72161, 30], [72462, 27], [72763, 24], [73064, 21], [73365, 18]], "point": [176, 195]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan5", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 0.25, "y": 0.9009992, "z": 1.25}, "object_poses": [{"objectName": "Pan_14c38bc8", "position": {"x": -1.0561, "y": 0.956400037, "z": -1.127}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_8a9da01b", "position": {"x": 0.0713647, "y": 1.16198552, "z": 0.529798}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_43faf0e4", "position": {"x": -1.23985851, "y": 0.9482399, "z": -1.722563}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_43faf0e4", "position": {"x": -1.38696766, "y": 1.69949, "z": 0.231900036}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_42dba209", "position": {"x": -0.7776127, "y": 0.9108642, "z": -1.76077831}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7c4fce22", "position": {"x": -1.35631454, "y": 1.65946925, "z": -1.60041583}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_b40d1b1e", "position": {"x": -1.0561, "y": 0.956400037, "z": -0.672}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Spatula_ef52a4ea", "position": {"x": -0.822229, "y": 1.1465, "z": 0.618002}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spoon_80376f38", "position": {"x": 0.055328548, "y": 0.764485538, "z": -1.95272}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_c00db6cc", "position": {"x": -0.290060669, "y": 0.797030032, "z": -2.06327963}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_c00db6cc", "position": {"x": 1.83629322, "y": 1.46146286, "z": -0.7374959}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Plate_c0097cec", "position": {"x": -1.02899992, "y": 0.134518445, "z": -0.20487237}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_84756f6d", "position": {"x": -0.6576, "y": 0.7179415, "z": -1.9351815}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_99ef625f", "position": {"x": 0.6211755, "y": 1.66077912, "z": -2.1614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_4de763d6", "position": {"x": 1.05714178, "y": 1.65506327, "z": -2.1634}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b0cc376f", "position": {"x": -0.6116, "y": 0.6883444, "z": 0.05815587}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "PepperShaker_b0cc376f", "position": {"x": -0.0491249859, "y": 0.112527966, "z": 0.245646521}, "rotation": {"x": 7.01670955e-15, "y": 180.0, "z": 7.01670955e-15}}, {"objectName": "Lettuce_9396b7e6", "position": {"x": -0.420080632, "y": 0.990004838, "z": 0.135948449}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_8066794b", "position": {"x": 1.94580364, "y": 0.812138855, "z": -0.27197808}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pan_14c38bc8", "position": {"x": 1.743, "y": 0.9, "z": -1.969}, "rotation": {"x": 0.0, "y": 308.846832, "z": 0.0}}, {"objectName": "Knife_84756f6d", "position": {"x": -0.376407981, "y": 0.786883831, "z": -2.06327963}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Bowl_4de763d6", "position": {"x": 1.81365991, "y": 1.012568, "z": -1.24526358}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Fork_66b6c857", "position": {"x": -0.05349374, "y": 0.6975447, "z": 0.181100011}, "rotation": {"x": 7.01670955e-15, "y": 180.0, "z": 7.01670955e-15}}, {"objectName": "Cup_11735285", "position": {"x": 0.1015501, "y": 0.750666142, "z": -1.9382143}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_43faf0e4", "position": {"x": -0.152033746, "y": 1.1684401, "z": 0.706205964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_3526507b", "position": {"x": -0.203713372, "y": 0.811944664, "z": -2.008}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_7c4fce22", "position": {"x": 1.52884161, "y": 1.65447712, "z": -2.12806129}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Vase_777c51a0", "position": {"x": -0.849135458, "y": 0.9156595, "z": 0.294503123}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_8a9da01b", "position": {"x": -1.27819037, "y": 0.9439854, "z": 0.135948449}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_b40d1b1e", "position": {"x": -1.2899, "y": 0.957, "z": -1.02}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_0d0ef808", "position": {"x": -1.148, "y": 0.9816431, "z": -0.193000033}, "rotation": {"x": 1.917763e-06, "y": 2.92166624e-06, "z": -1.0441222e-05}}, {"objectName": "Plate_c0097cec", "position": {"x": -1.312, "y": 1.659261, "z": -0.353388548}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_ef52a4ea", "position": {"x": -0.117366061, "y": 0.778356, "z": -2.1185596}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "PaperTowelRoll_e7f3845e", "position": {"x": 1.64408183, "y": 1.0155853, "z": -2.26794887}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_f4e7dd3c", "position": {"x": -0.205553234, "y": 0.9198706, "z": 0.135948449}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_c00db6cc", "position": {"x": 2.11006451, "y": 0.8509986, "z": -0.271978438}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBottle_99ef625f", "position": {"x": 1.84506106, "y": 0.06322349, "z": 0.109019831}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_52c85e6d", "position": {"x": -0.5988306, "y": 1.12780631, "z": 0.441594}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_9396b7e6", "position": {"x": 1.194489, "y": 0.98780483, "z": -1.92935038}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b0cc376f", "position": {"x": -1.2020483, "y": 1.65885615, "z": -0.111849934}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_1a7bf448", "position": {"x": -0.822229, "y": 1.13154221, "z": 0.706205964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_42dba209", "position": {"x": -0.99721235, "y": 0.9130642, "z": -0.231475621}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_3478a6e4", "position": {"x": -0.612001, "y": 0.110073924, "z": 0.218014091}, "rotation": {"x": 0.0, "y": 179.999741, "z": 0.0}}, {"objectName": "Spoon_80376f38", "position": {"x": -0.679746747, "y": 0.6955432, "z": 0.146518633}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_74fc855a", "position": {"x": 1.89104939, "y": 0.8668852, "z": -0.458185673}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_8066794b", "position": {"x": 1.94580269, "y": 1.422789, "z": -0.4581866}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Statue_bb70ba4e", "position": {"x": 2.593555, "y": 0.886895061, "z": 0.195737422}, "rotation": {"x": 3.415104e-06, "y": 180.0003, "z": -3.4150853e-06}}], "object_toggles": [], "random_seed": 2746394187, "scene_num": 5}, "task_id": "trial_T20190906_190505_376018", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AM2KK02JXXW48_30ZX6P7VFBC3HJWCP0K7KK509AC2J4", "high_descs": ["Move to the sink.", "Pick up the egg in the sink.", "Bring the egg to the microwave. ", "Heat the egg in the microwave. ", "Bring the egg to the trash can.", "Put the egg in the trash can. "], "task_desc": "Put a heated egg in the trash can. ", "votes": [1, 1]}, {"assignment_id": "A23HZ18KTCK2DA_3KWTYT0873K1B63ZA4YIY6L4X9BL5T", "high_descs": ["Turn to your right and go to the sink.", "Pick the egg up that is in the sink.", "With the egg go to the microwave and open the microwave door.", "Put the egg in the microwave oven and shut the door.  Turn the microwave on.  When finished open the door and take the egg out of the microwave.  Shut the microwave door.", "Go to the trash can that is on the left side of the fridge.", "Put the egg in the trash can to the left and behind the object that is in there."], "task_desc": "Warm up an egg to throw it away.", "votes": [1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3WJEQKOXABJLR5BCCQ7GM2SCRHGA1R", "high_descs": ["turn right and head over to the kitchen sink at the right corner of the room", "grab the egg out of the sink", "turn left and head over to the microwave straight ahead", "place the egg inside of the microwave, microwave it, then take it back out", "move to the left a bit and face the garbage bin next to the fridge on the left", "place the egg inside of the garbage bin"], "task_desc": "place a microwaved egg inside the garbage bin", "votes": [1, 1]}]}}