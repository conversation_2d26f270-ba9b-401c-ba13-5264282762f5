{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 4}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 4}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 5}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 29}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000256.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000257.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000258.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000259.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000260.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000261.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000262.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000263.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000264.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000265.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 41}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "TissueBox", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-12|1|3|30"}}, {"discrete_action": {"action": "PickupObject", "args": ["tissuebox"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["TissueBox", [-15.36109256, -15.36109256, 0.515050532, 0.515050532, 4.26689624, 4.26689624]], "coordinateReceptacleObjectId": ["DiningTable", [-15.764, -15.764, 1.028, 1.028, 0.0, 0.0]], "forceVisible": true, "objectId": "TissueBox|-03.84|+01.07|+00.13"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|0|1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tissuebox", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["TissueBox", [-15.36109256, -15.36109256, 0.515050532, 0.515050532, 4.26689624, 4.26689624]], "coordinateReceptacleObjectId": ["Drawer", [-0.632600308, -0.632600308, 4.13040064, 4.13040064, 2.931000232, 2.931000232]], "forceVisible": true, "objectId": "TissueBox|-03.84|+01.07|+00.13", "receptacleObjectId": "Drawer|-00.16|+00.73|+01.03"}}, {"discrete_action": {"action": "GotoLocation", "args": ["shelf"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|3|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["tissuebox"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["TissueBox", [-0.700199844, -0.700199844, 4.808, 4.808, 0.517510892, 0.517510892]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-2.504, -2.504, 4.808, 4.808, 0.476, 0.476]], "forceVisible": true, "objectId": "TissueBox|-00.18|+00.13|+01.20"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|0|1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tissuebox", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["TissueBox", [-0.700199844, -0.700199844, 4.808, 4.808, 0.517510892, 0.517510892]], "coordinateReceptacleObjectId": ["Drawer", [-0.632600308, -0.632600308, 4.13040064, 4.13040064, 2.931000232, 2.931000232]], "forceVisible": true, "objectId": "TissueBox|-00.18|+00.13|+01.20", "receptacleObjectId": "Drawer|-00.16|+00.73|+01.03"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "TissueBox|-03.84|+01.07|+00.13"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [89, 117, 142, 162], "mask": [[34913, 5], [35213, 7], [35511, 10], [35806, 17], [36105, 22], [36404, 24], [36702, 28], [37003, 27], [37303, 27], [37603, 27], [37903, 27], [38203, 26], [38504, 25], [38804, 24], [39104, 24], [39394, 48], [39693, 49], [39993, 49], [40293, 49], [40592, 50], [40892, 51], [41191, 52], [41491, 52], [41790, 53], [42090, 53], [42390, 53], [42689, 54], [42989, 54], [43289, 54], [43589, 54], [43889, 54], [44189, 54], [44490, 53], [44790, 53], [45090, 52], [45390, 52], [45690, 52], [45990, 52], [46290, 52], [46590, 52], [46891, 51], [47191, 51], [47491, 51], [47791, 51], [48091, 51], [48391, 51]], "point": [115, 138]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-00.16|+00.73|+01.03"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [16, 136, 208, 171], "mask": [[40516, 193], [40816, 193], [41117, 191], [41417, 191], [41718, 190], [42018, 190], [42319, 189], [42619, 188], [42920, 187], [43220, 187], [43521, 186], [43821, 185], [44122, 184], [44422, 184], [44723, 183], [45023, 183], [45324, 181], [45624, 181], [45925, 180], [46225, 180], [46525, 180], [46826, 178], [47126, 178], [47427, 177], [47727, 177], [48028, 175], [48328, 175], [48629, 174], [48929, 174], [49230, 173], [49530, 172], [49831, 120], [49956, 46], [50131, 113], [50259, 43], [50432, 111], [50561, 41], [50732, 109], [50862, 40], [51033, 106], [51163, 38]], "point": [112, 152]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "TissueBox|-03.84|+01.07|+00.13", "placeStationary": true, "receptacleObjectId": "Drawer|-00.16|+00.73|+01.03"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 136, 219, 218], "mask": [[40516, 193], [40815, 194], [41115, 194], [41414, 196], [41714, 196], [42013, 197], [42312, 198], [42612, 199], [42911, 200], [43211, 200], [43510, 201], [43810, 201], [44109, 203], [44409, 203], [44708, 204], [45008, 204], [45307, 206], [45606, 207], [45906, 207], [46205, 208], [46505, 209], [46804, 210], [47104, 210], [47403, 211], [47703, 212], [48002, 213], [48302, 213], [48601, 214], [48900, 216], [49200, 216], [49500, 216], [49800, 151], [49956, 60], [50100, 144], [50259, 57], [50400, 143], [50561, 56], [50700, 141], [50862, 55], [51000, 139], [51163, 54], [51300, 137], [51464, 53], [51600, 135], [51767, 51], [51900, 133], [52070, 48], [52200, 130], [52374, 44], [52500, 127], [52678, 40], [52800, 124], [52980, 39], [53100, 122], [53282, 37], [53400, 120], [53583, 36], [53700, 118], [53888, 31], [54000, 117], [54188, 32], [54300, 113], [54487, 33], [54600, 110], [54787, 33], [54900, 111], [55087, 33], [55200, 111], [55387, 33], [55500, 112], [55687, 33], [55800, 112], [55987, 32], [56100, 112], [56287, 32], [56400, 113], [56586, 33], [56700, 113], [56886, 32], [57000, 113], [57186, 32], [57300, 113], [57486, 32], [57600, 113], [57785, 33], [57900, 113], [58085, 32], [58200, 113], [58385, 32], [58500, 114], [58685, 32], [58800, 88], [59012, 4], [59100, 88], [59312, 4], [59400, 87], [59613, 3], [59701, 86], [59913, 2], [60002, 85], [60213, 2], [60302, 84], [60514, 1], [60603, 83], [60904, 82], [61204, 81], [61505, 80], [61806, 79], [62107, 77], [62407, 77], [62708, 76], [63009, 74], [63309, 74], [63610, 73], [63911, 72], [64212, 70], [64512, 70], [64813, 69], [65114, 67]], "point": [109, 176]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-00.16|+00.73|+01.03"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 136, 219, 218], "mask": [[40516, 147], [40681, 28], [40815, 148], [40982, 27], [41115, 148], [41282, 27], [41414, 149], [41582, 28], [41714, 149], [41882, 28], [42013, 150], [42182, 28], [42312, 151], [42482, 28], [42612, 151], [42783, 28], [42911, 152], [43083, 28], [43211, 152], [43383, 28], [43510, 143], [43654, 9], [43683, 28], [43810, 142], [43956, 6], [43983, 28], [44109, 142], [44257, 5], [44283, 29], [44409, 141], [44558, 4], [44583, 29], [44708, 142], [44858, 4], [44883, 29], [45008, 142], [45159, 3], [45184, 28], [45307, 142], [45460, 2], [45484, 29], [45606, 142], [45760, 2], [45783, 30], [45906, 141], [46061, 1], [46083, 30], [46205, 141], [46383, 30], [46505, 140], [46683, 31], [46804, 141], [46983, 31], [47104, 142], [47283, 31], [47403, 145], [47583, 31], [47703, 145], [47883, 32], [48002, 146], [48182, 33], [48302, 146], [48482, 33], [48601, 147], [48782, 33], [48900, 151], [49082, 34], [49200, 154], [49382, 34], [49500, 162], [49682, 34], [49800, 162], [49982, 34], [50100, 162], [50282, 34], [50400, 162], [50581, 36], [50700, 162], [50881, 36], [51000, 162], [51181, 36], [51300, 162], [51481, 36], [51600, 162], [51781, 37], [51900, 162], [52081, 37], [52200, 162], [52381, 37], [52500, 162], [52680, 38], [52800, 162], [52980, 39], [53100, 162], [53280, 39], [53400, 162], [53580, 39], [53700, 219], [54000, 220], [54300, 220], [54600, 220], [54900, 220], [55200, 220], [55500, 220], [55800, 219], [56100, 219], [56400, 219], [56700, 218], [57000, 218], [57300, 218], [57600, 218], [57900, 217], [58200, 217], [58500, 217], [58800, 216], [59100, 216], [59400, 216], [59701, 214], [60002, 213], [60302, 213], [60603, 211], [60904, 210], [61204, 210], [61505, 208], [61806, 207], [62107, 206], [62407, 206], [62708, 204], [63009, 203], [63309, 203], [63610, 201], [63911, 200], [64212, 199], [64512, 198], [64813, 197], [65114, 196]], "point": [109, 176]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "TissueBox|-00.18|+00.13|+01.20"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [147, 209, 180, 217], "mask": [[62547, 34], [62847, 34], [63147, 34], [63447, 34], [63747, 34], [64047, 34], [64347, 33], [64647, 33], [64947, 33]], "point": [163, 212]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-00.16|+00.73|+01.03"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [16, 136, 208, 171], "mask": [[40516, 193], [40816, 193], [41117, 191], [41417, 191], [41718, 190], [42018, 190], [42319, 189], [42619, 188], [42920, 187], [43220, 187], [43521, 186], [43821, 185], [44122, 184], [44422, 184], [44723, 183], [45023, 183], [45324, 181], [45624, 181], [45925, 180], [46225, 180], [46525, 180], [46826, 178], [47126, 178], [47427, 177], [47727, 177], [48028, 175], [48328, 175], [48629, 174], [48929, 174], [49230, 173], [49530, 172], [49831, 171], [50131, 171], [50432, 170], [50732, 170], [51033, 168]], "point": [112, 152]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "TissueBox|-00.18|+00.13|+01.20", "placeStationary": true, "receptacleObjectId": "Drawer|-00.16|+00.73|+01.03"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 136, 219, 218], "mask": [[40516, 147], [40681, 28], [40815, 148], [40982, 27], [41115, 148], [41282, 27], [41414, 149], [41582, 28], [41714, 149], [41882, 28], [42013, 150], [42182, 28], [42312, 151], [42482, 28], [42612, 151], [42783, 28], [42911, 152], [43083, 28], [43211, 152], [43383, 28], [43510, 143], [43654, 9], [43683, 28], [43810, 142], [43956, 6], [43983, 28], [44109, 142], [44257, 5], [44283, 29], [44409, 141], [44558, 4], [44583, 29], [44708, 142], [44858, 4], [44883, 29], [45008, 142], [45159, 3], [45184, 28], [45307, 142], [45460, 2], [45484, 29], [45606, 142], [45760, 2], [45783, 30], [45906, 141], [46061, 1], [46083, 30], [46205, 141], [46383, 30], [46505, 140], [46683, 31], [46804, 141], [46983, 31], [47104, 142], [47283, 31], [47403, 145], [47583, 31], [47703, 145], [47883, 32], [48002, 146], [48182, 33], [48302, 146], [48482, 33], [48601, 147], [48782, 33], [48900, 151], [49082, 34], [49200, 154], [49382, 34], [49500, 162], [49682, 34], [49800, 162], [49982, 34], [50100, 162], [50282, 34], [50400, 162], [50581, 36], [50700, 162], [50881, 36], [51000, 162], [51181, 36], [51300, 162], [51481, 36], [51600, 162], [51781, 37], [51900, 162], [52081, 37], [52200, 162], [52381, 37], [52500, 162], [52680, 38], [52800, 162], [52980, 39], [53100, 162], [53280, 39], [53400, 162], [53580, 39], [53700, 219], [54000, 220], [54300, 220], [54600, 220], [54900, 220], [55200, 220], [55500, 220], [55800, 219], [56100, 219], [56400, 219], [56700, 218], [57000, 218], [57300, 218], [57600, 218], [57900, 217], [58200, 217], [58500, 217], [58800, 216], [59100, 216], [59400, 216], [59701, 214], [60002, 213], [60302, 213], [60603, 211], [60904, 210], [61204, 210], [61505, 208], [61806, 207], [62107, 206], [62407, 206], [62708, 204], [63009, 203], [63309, 71], [63610, 69], [63911, 68], [64212, 67], [64512, 67], [64813, 66], [65114, 65]], "point": [109, 176]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-00.16|+00.73|+01.03"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 136, 219, 218], "mask": [[40516, 147], [40681, 28], [40815, 148], [40982, 27], [41115, 148], [41282, 27], [41414, 149], [41582, 28], [41714, 149], [41882, 28], [42013, 150], [42182, 28], [42312, 151], [42482, 28], [42612, 151], [42783, 28], [42911, 152], [43083, 28], [43211, 152], [43383, 28], [43510, 143], [43654, 9], [43683, 28], [43810, 142], [43956, 6], [43983, 28], [44109, 142], [44257, 5], [44283, 29], [44409, 141], [44558, 4], [44583, 29], [44708, 142], [44858, 4], [44883, 29], [45008, 142], [45159, 3], [45184, 28], [45307, 142], [45460, 2], [45484, 29], [45606, 142], [45760, 2], [45783, 30], [45906, 141], [46061, 1], [46084, 29], [46205, 141], [46384, 29], [46505, 140], [46684, 30], [46804, 141], [46984, 30], [47104, 142], [47285, 29], [47403, 145], [47585, 29], [47703, 145], [47885, 30], [48002, 146], [48185, 30], [48302, 146], [48485, 30], [48601, 147], [48785, 30], [48900, 151], [49085, 31], [49200, 153], [49385, 31], [49500, 152], [49657, 5], [49686, 30], [49800, 151], [49958, 4], [49986, 30], [50100, 150], [50258, 4], [50286, 30], [50400, 150], [50559, 3], [50586, 31], [50700, 149], [50860, 2], [50886, 31], [51000, 148], [51160, 2], [51186, 31], [51300, 147], [51461, 1], [51486, 31], [51600, 146], [51786, 32], [51900, 145], [52086, 32], [52200, 145], [52386, 32], [52500, 145], [52686, 32], [52800, 146], [52986, 33], [53100, 148], [53285, 34], [53400, 148], [53585, 34], [53700, 148], [53885, 34], [54000, 148], [54185, 35], [54300, 220], [54600, 220], [54900, 220], [55200, 220], [55500, 220], [55800, 219], [56100, 219], [56400, 219], [56700, 218], [57000, 218], [57300, 218], [57600, 218], [57900, 217], [58200, 217], [58500, 217], [58800, 216], [59100, 216], [59400, 216], [59701, 214], [60002, 213], [60302, 213], [60603, 211], [60904, 210], [61204, 210], [61505, 208], [61806, 207], [62107, 206], [62407, 206], [62708, 204], [63009, 203], [63309, 203], [63610, 201], [63911, 200], [64212, 199], [64512, 198], [64813, 197], [65114, 196]], "point": [109, 176]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan216", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -3.0, "y": 0.9009992, "z": 0.5}, "object_poses": [{"objectName": "TissueBox_50e690f5", "position": {"x": -0.175049961, "y": 0.129377723, "z": 1.202}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a56cd598", "position": {"x": 0.0352813, "y": 0.127674222, "z": 1.0931344}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a56cd598", "position": {"x": -1.22670615, "y": 0.127674222, "z": 1.202}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Box_6fa08eb0", "position": {"x": -3.78054142, "y": 1.305, "z": 0.457275361}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_462d92e0", "position": {"x": -0.97179544, "y": 0.114890076, "z": 1.19714463}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "TissueBox_50e690f5", "position": {"x": -3.84027314, "y": 1.06672406, "z": 0.128762633}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Newspaper_932fe9f5", "position": {"x": 0.967799544, "y": 0.6563504, "z": 1.0432601}, "rotation": {"x": 0.0, "y": -1.70754731e-06, "z": 0.0}}, {"objectName": "KeyChain_5b568144", "position": {"x": 1.5765, "y": 0.461668432, "z": -1.40827429}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_1d8d3594", "position": {"x": 1.74530458, "y": 0.665370643, "z": -1.41366136}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_b9ed9b88", "position": {"x": -1.29, "y": 0.891890049, "z": 1.219}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "RemoteControl_5b8bcfa9", "position": {"x": -4.165264, "y": 1.06409776, "z": 0.4493562}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pillow_22286f86", "position": {"x": -1.82799935, "y": 0.585, "z": -1.25104988}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a56cd598", "position": {"x": 1.36502755, "y": 0.456994, "z": 1.06819963}, "rotation": {"x": 0.0, "y": -1.70754731e-06, "z": 0.0}}, {"objectName": "Laptop_fbe5dbee", "position": {"x": 0.434110135, "y": 0.541742444, "z": -1.1877985}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_6ea54dcc", "position": {"x": -0.5166645, "y": 0.114890076, "z": 1.20104086}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_d6a7b63c", "position": {"x": 0.09891027, "y": 1.00041425, "z": 1.23403287}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1613678983, "scene_num": 216}, "task_id": "trial_T20190908_150448_341154", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A1HKHM4NVAO98H_3KYQYYSHYYYC6T7NYALAPLXS0I6ODL", "high_descs": ["turn right to face the round table there", "grab the tissue box off of the table", "turn around and walk over to the first brown cabinet on the left side wall of the room", "place the tissue box inside of the cabinet drawer on the right side", "move forwards to the cabinet a bit and look downwards", "grab the tissue box from the bottom shelf of the cabinet there", "turn to back up a bit and face the cabinet again", "place the tissue box in the same right side cabinet drawer you put the other tissue box in"], "task_desc": "place the tissue boxes in the right side drawer of the cabinet ", "votes": [1, 1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3VNXK88KKF9VTN33N62MYH8A5QQ9VT", "high_descs": ["Take a step forward then turn right to face the round table.", "Pick up the box of tissues off of the table.", "Turn around and walk across the room, hang a left to face the wooden dresser below the television.", "Open the rightmost drawer of the wooden dresser and put the box of tissues inside, then close the drawer.", "Turn left and walk forward, then turn right and walk up to the wooden dresser.", "Pick up the box of tissues that are on the bottom shelf of the wooden dresser.", "Turn around and walk towards the couch, then turn left and walk forward, turn left again to face the wooden dresser below the television.", "Open the rightmost drawer of the wooden dresser and put the box of tissues inside, then close the drawer."], "task_desc": "Move two boxes of tissues into a drawer.", "votes": [1, 1, 1]}, {"assignment_id": "A11LSO6D7BMY99_3LRKMWOKB8812NNE3UCA90DSNVDZ2I", "high_descs": ["turn to your right to the table with the white table cloth", "pick up the of tissues", "take the tissues to the far end of the wooden table that is against the wall with a vase on it", "place the tissue box in the right hand drawer", "pick up the tissue box that is on the bottom shelf of the same table", "raise it up to the top of the table", "open the drawer that the other tissue box is in ", "place this tissue box next to the other one"], "task_desc": "put two boxes of tissues in a drawer", "votes": [1, 1, 0]}]}}