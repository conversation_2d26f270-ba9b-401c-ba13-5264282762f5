{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 19}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 32}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000242.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000243.png", "low_idx": 34}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-2|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-4.47874116, -4.47874116, -4.9645624, -4.9645624, 3.3667964, 3.3667964]], "coordinateReceptacleObjectId": ["SinkBasin", [-5.3792, -5.3792, -5.0, -5.0, 3.356, 3.356]], "forceVisible": true, "objectId": "Cup|-01.12|+00.84|-01.24"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|-2|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|1|-2|2|-30"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-4.47874116, -4.47874116, -4.9645624, -4.9645624, 3.3667964, 3.3667964]], "coordinateReceptacleObjectId": ["Cabinet", [0.940000056, 0.940000056, -4.79, -4.79, 8.04, 8.04]], "forceVisible": true, "objectId": "Cup|-01.12|+00.84|-01.24", "receptacleObjectId": "Cabinet|+00.24|+02.01|-01.20"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.12|+00.84|-01.24"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [169, 111, 194, 157], "mask": [[33179, 7], [33476, 13], [33774, 17], [34073, 20], [34372, 22], [34672, 22], [34972, 23], [35272, 23], [35571, 24], [35871, 24], [36171, 24], [36471, 24], [36771, 24], [37071, 24], [37371, 24], [37671, 23], [37971, 23], [38271, 23], [38572, 22], [38872, 21], [39172, 21], [39473, 19], [39773, 18], [40074, 16], [40375, 14], [40676, 12], [40978, 9], [41280, 2], [41580, 2], [41880, 2], [42180, 2], [42474, 10], [42772, 14], [43071, 17], [43370, 19], [43669, 21], [43969, 21], [44269, 22], [44569, 22], [44869, 22], [45169, 22], [45470, 21], [45770, 20], [46071, 18], [46373, 15], [46675, 11], [46978, 5]], "point": [181, 133]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.12|+00.84|-01.24", "placeStationary": true, "receptacleObjectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 89], [19924, 21], [19959, 88], [20108, 89], [20224, 18], [20262, 85], [20407, 90], [20524, 17], [20563, 84], [20707, 90], [20824, 16], [20864, 82], [21006, 91], [21124, 15], [21165, 81], [21305, 93], [21424, 15], [21466, 80], [21604, 94], [21724, 15], [21770, 75], [21903, 95], [22024, 15], [22071, 74], [22203, 95], [22324, 15], [22372, 73], [22502, 96], [22624, 15], [22666, 2], [22672, 73], [22801, 97], [22925, 14], [22966, 3], [22972, 72], [23100, 99], [23225, 14], [23265, 5], [23272, 72], [23400, 99], [23525, 14], [23565, 5], [23572, 72], [23700, 99], [23825, 14], [23865, 5], [23872, 71], [24000, 99], [24125, 14], [24165, 5], [24172, 71], [24300, 99], [24425, 14], [24465, 4], [24472, 71], [24600, 99], [24725, 14], [24765, 4], [24772, 71], [24900, 99], [25025, 14], [25065, 4], [25071, 71], [25200, 100], [25325, 14], [25365, 3], [25371, 71], [25500, 100], [25625, 14], [25665, 2], [25671, 71], [25800, 100], [25925, 14], [25965, 1], [25970, 71], [26100, 100], [26225, 14], [26270, 71], [26400, 100], [26525, 14], [26569, 72], [26700, 100], [26825, 14], [26868, 73], [27000, 101], [27126, 13], [27166, 74], [27300, 101], [27425, 14], [27465, 75], [27600, 101], [27725, 14], [27765, 75], [27900, 101], [28024, 15], [28065, 75], [28200, 102], [28323, 16], [28365, 74], [28500, 103], [28622, 17], [28665, 74], [28800, 105], [28920, 20], [28964, 75], [29100, 108], [29216, 24], [29264, 74], [29400, 120], [29524, 17], [29563, 75], [29700, 118], [29826, 16], [29862, 76], [30000, 117], [30127, 16], [30161, 77], [30300, 117], [30428, 17], [30459, 78], [30600, 116], [30728, 21], [30756, 81], [30900, 116], [31029, 108], [31200, 115], [31329, 107], [31500, 115], [31630, 106], [31800, 115], [31930, 106], [32100, 115], [32230, 106], [32400, 115], [32530, 105], [32700, 115], [32830, 105], [33000, 115], [33130, 105], [33300, 115], [33430, 104], [33600, 116], [33729, 105], [33900, 116], [34029, 105], [34200, 117], [34328, 106], [34500, 118], [34627, 106], [34800, 119], [34926, 107], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 89], [19924, 18], [19963, 84], [20108, 89], [20224, 17], [20264, 83], [20407, 90], [20524, 16], [20565, 82], [20707, 90], [20824, 16], [20865, 81], [21006, 91], [21124, 15], [21165, 81], [21305, 93], [21424, 15], [21466, 80], [21604, 94], [21724, 15], [21770, 75], [21903, 95], [22024, 15], [22071, 74], [22203, 95], [22324, 15], [22372, 73], [22502, 96], [22624, 15], [22666, 2], [22672, 73], [22801, 97], [22925, 14], [22966, 4], [22972, 72], [23100, 99], [23225, 14], [23266, 4], [23272, 72], [23400, 99], [23525, 14], [23565, 5], [23572, 72], [23700, 99], [23825, 14], [23865, 5], [23872, 71], [24000, 99], [24125, 14], [24165, 5], [24172, 71], [24300, 99], [24425, 14], [24465, 4], [24472, 71], [24600, 99], [24725, 14], [24765, 4], [24772, 71], [24900, 99], [25025, 14], [25065, 4], [25071, 71], [25200, 100], [25325, 14], [25365, 3], [25371, 71], [25500, 100], [25625, 14], [25665, 2], [25671, 71], [25800, 100], [25925, 14], [25965, 1], [25970, 71], [26100, 100], [26225, 14], [26270, 71], [26400, 100], [26525, 14], [26569, 72], [26700, 100], [26825, 14], [26868, 73], [27000, 101], [27126, 13], [27166, 74], [27300, 101], [27425, 14], [27465, 75], [27600, 101], [27725, 14], [27765, 75], [27900, 101], [28024, 15], [28065, 75], [28200, 102], [28323, 16], [28365, 74], [28500, 103], [28622, 17], [28665, 74], [28800, 105], [28920, 20], [28964, 75], [29100, 108], [29216, 24], [29264, 74], [29400, 120], [29524, 17], [29563, 75], [29700, 118], [29826, 16], [29862, 76], [30000, 117], [30127, 16], [30161, 77], [30300, 117], [30428, 17], [30459, 78], [30600, 116], [30728, 21], [30756, 81], [30900, 116], [31029, 16], [31059, 78], [31200, 115], [31329, 14], [31361, 75], [31500, 115], [31630, 12], [31662, 74], [31800, 115], [31930, 11], [31963, 73], [32100, 115], [32230, 11], [32264, 72], [32400, 115], [32530, 10], [32564, 71], [32700, 115], [32830, 10], [32865, 70], [33000, 115], [33130, 10], [33164, 71], [33300, 115], [33430, 10], [33464, 70], [33600, 116], [33729, 12], [33764, 70], [33900, 116], [34029, 13], [34063, 71], [34200, 117], [34328, 15], [34362, 72], [34500, 118], [34627, 18], [34660, 73], [34800, 119], [34926, 21], [34957, 76], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [1.132, 1.132, -5.3248, -5.3248, 3.6003664, 3.6003664]], "forceVisible": true, "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [21, 4, 252, 150], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15324, 227], [15624, 226], [15925, 225], [16225, 225], [16526, 224], [16826, 223], [17126, 223], [17427, 222], [17727, 222], [18027, 222], [18327, 222], [18627, 221], [18927, 221], [19228, 220], [19528, 219], [19828, 219], [20129, 218], [20429, 218], [20729, 217], [21030, 216], [21330, 216], [21630, 215], [21931, 214], [22231, 214], [22531, 214], [22832, 212], [23132, 212], [23433, 211], [23733, 210], [24033, 210], [24334, 209], [24634, 209], [24934, 208], [25235, 207], [25535, 207], [25835, 206], [26136, 205], [26436, 205], [26736, 205], [27037, 203], [27337, 203], [27637, 203], [27938, 202], [28238, 201], [28539, 200], [28839, 200], [29139, 199], [29440, 198], [29740, 198], [30040, 198], [30341, 196], [30641, 196], [30941, 196], [31242, 194], [31542, 194], [31842, 194], [32143, 193], [32443, 192], [32744, 191], [33044, 191], [33344, 190], [33645, 189], [33945, 189], [34245, 189], [34546, 187], [34846, 187], [35146, 187], [35447, 185], [35747, 185], [36047, 185], [36348, 184], [36648, 183], [36949, 182], [37249, 182], [37549, 181], [37850, 180], [38150, 180], [38450, 180], [38751, 178], [39051, 178], [39351, 178], [39652, 176], [39952, 176], [40252, 176], [40553, 175], [40853, 174], [41153, 174], [41454, 173], [41754, 172], [42055, 171], [42355, 170], [42656, 170], [42957, 168], [43257, 168], [43557, 168], [43858, 166], [44158, 166], [44458, 166], [44759, 165]], "point": [136, 76]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-01.12|+00.84|-01.24"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [139, 67, 165, 117], "mask": [[19942, 21], [20241, 23], [20540, 25], [20840, 25], [21139, 26], [21439, 27], [21739, 27], [22039, 27], [22339, 27], [22639, 27], [22939, 27], [23239, 27], [23539, 26], [23840, 25], [24140, 25], [24440, 25], [24740, 24], [25041, 23], [25341, 23], [25641, 22], [25942, 21], [26242, 20], [26543, 19], [26843, 18], [27144, 16], [27445, 14], [27747, 11], [28049, 7], [28351, 2], [28651, 2], [28951, 2], [29251, 2], [29551, 2], [29851, 2], [30151, 2], [30451, 2], [30749, 7], [31045, 14], [31343, 18], [31642, 20], [31941, 22], [32241, 23], [32540, 24], [32840, 25], [33140, 24], [33440, 24], [33741, 23], [34042, 21], [34343, 19], [34645, 15], [34947, 10]], "point": [152, 91]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.28|+00.90|-01.33"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 4, 252, 267], "mask": [[954, 172], [1254, 173], [1553, 175], [1852, 176], [2151, 178], [2450, 180], [2749, 181], [3049, 182], [3348, 184], [3647, 185], [3946, 187], [4245, 189], [4544, 190], [4844, 191], [5143, 193], [5442, 195], [5741, 196], [6040, 198], [6339, 200], [6638, 201], [6938, 202], [7237, 204], [7536, 205], [7835, 207], [8134, 209], [8433, 210], [8733, 211], [9032, 213], [9331, 214], [9630, 216], [9929, 218], [10228, 219], [10528, 220], [10827, 222], [11126, 223], [11425, 225], [11724, 227], [12023, 228], [12322, 230], [12622, 231], [12921, 232], [13222, 231], [13522, 230], [13822, 230], [14123, 229], [14423, 229], [14723, 228], [15024, 227], [15322, 229], [15620, 230], [15919, 231], [16219, 231], [16518, 232], [16817, 232], [17116, 233], [17415, 234], [17715, 234], [18014, 235], [18313, 236], [18612, 236], [18911, 237], [19211, 237], [19510, 237], [19809, 89], [19924, 21], [19959, 88], [20108, 89], [20224, 19], [20262, 85], [20407, 90], [20524, 17], [20563, 84], [20707, 90], [20824, 16], [20864, 82], [21006, 91], [21124, 15], [21165, 81], [21305, 93], [21424, 15], [21466, 80], [21604, 94], [21724, 15], [21770, 75], [21903, 95], [22024, 15], [22071, 74], [22203, 95], [22324, 15], [22372, 73], [22502, 96], [22624, 15], [22666, 2], [22672, 73], [22801, 97], [22925, 14], [22966, 4], [22972, 72], [23100, 99], [23225, 14], [23266, 4], [23272, 72], [23400, 99], [23525, 14], [23565, 5], [23572, 72], [23700, 99], [23825, 14], [23865, 5], [23872, 71], [24000, 99], [24125, 14], [24165, 5], [24172, 71], [24300, 99], [24425, 14], [24465, 4], [24472, 71], [24600, 99], [24725, 14], [24765, 4], [24772, 71], [24900, 99], [25025, 14], [25065, 4], [25071, 71], [25200, 100], [25325, 14], [25365, 3], [25371, 71], [25500, 100], [25625, 14], [25665, 2], [25671, 71], [25800, 100], [25925, 14], [25965, 1], [25970, 71], [26100, 100], [26225, 14], [26270, 71], [26400, 100], [26525, 14], [26569, 72], [26700, 100], [26825, 14], [26868, 73], [27000, 101], [27126, 13], [27166, 74], [27300, 101], [27425, 14], [27465, 75], [27600, 101], [27725, 14], [27765, 75], [27900, 101], [28024, 15], [28065, 75], [28200, 102], [28323, 16], [28365, 74], [28500, 103], [28622, 17], [28665, 74], [28800, 105], [28920, 20], [28964, 75], [29100, 108], [29216, 24], [29264, 74], [29400, 120], [29524, 17], [29563, 75], [29700, 118], [29826, 16], [29862, 76], [30000, 117], [30127, 16], [30161, 77], [30300, 117], [30428, 17], [30459, 78], [30600, 116], [30728, 21], [30756, 81], [30900, 116], [31029, 108], [31200, 115], [31329, 107], [31500, 115], [31630, 106], [31800, 115], [31930, 106], [32100, 115], [32230, 106], [32400, 115], [32530, 105], [32700, 115], [32830, 105], [33000, 115], [33130, 105], [33300, 115], [33430, 104], [33600, 116], [33729, 105], [33900, 116], [34029, 105], [34200, 117], [34328, 106], [34500, 118], [34627, 106], [34800, 119], [34926, 107], [35100, 233], [35400, 232], [35700, 232], [36000, 232], [36300, 232], [36600, 231], [36900, 231], [37200, 231], [37500, 230], [37800, 230], [38100, 230], [38400, 230], [38700, 229], [39000, 229], [39300, 229], [39600, 228], [39900, 228], [40200, 228], [40500, 228], [40800, 227], [41100, 227], [41400, 227], [41700, 226], [42000, 226], [42300, 225], [42600, 226], [42900, 225], [43200, 225], [43500, 225], [43800, 56], [43858, 166], [44100, 56], [44158, 166], [44400, 56], [44458, 166], [44700, 55], [44759, 165], [45000, 55], [45300, 54], [45600, 54], [45900, 54], [46200, 53], [46500, 53], [46800, 53], [47100, 52], [47400, 52], [47700, 52], [48000, 51], [48300, 51], [48600, 50], [48900, 50], [49200, 50], [49500, 49], [49800, 49], [50100, 49], [50400, 48], [50700, 48], [51000, 48], [51300, 47], [51600, 47], [51900, 46], [52200, 46], [52500, 46], [52800, 45], [53100, 45], [53400, 45], [53700, 44], [54000, 44], [54300, 43], [54600, 43], [54900, 43], [55200, 42], [55500, 42], [55800, 42], [56100, 41], [56400, 41], [56700, 41], [57000, 40], [57300, 40], [57600, 39], [57900, 39], [58200, 39], [58500, 38], [58800, 38], [59100, 38], [59400, 37], [59700, 37], [60000, 37], [60300, 36], [60600, 36], [60900, 35], [61200, 35], [61500, 35], [61800, 34], [62100, 34], [62400, 34], [62700, 33], [63000, 33], [63300, 33], [63600, 32], [63900, 32], [64200, 31], [64500, 31], [64800, 31], [65100, 30], [65400, 30], [65700, 30], [66000, 29], [66300, 29], [66600, 28], [66900, 28], [67200, 28], [67500, 27], [67800, 27], [68100, 27], [68400, 26], [68700, 26], [69000, 26], [69300, 25], [69600, 25], [69900, 24], [70200, 24], [70500, 24], [70800, 23], [71100, 23], [71400, 23], [71700, 22], [72000, 22], [72300, 22], [72600, 21], [72900, 21], [73200, 20], [73500, 20], [73800, 20], [74100, 19], [74400, 19], [74700, 19], [75000, 18], [75300, 18], [75600, 18], [75900, 17], [76200, 17], [76500, 16], [76800, 16], [77100, 16], [77400, 15], [77700, 17], [78000, 18], [78300, 19], [78600, 19], [78900, 14], [78915, 2], [79200, 13], [79500, 13], [79800, 12]], "point": [126, 134]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+00.24|+02.01|-01.20"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 64, 154, 264], "mask": [[18949, 105], [19248, 106], [19548, 106], [19848, 106], [20148, 106], [20447, 107], [20747, 107], [21047, 107], [21347, 107], [21646, 108], [21946, 108], [22246, 108], [22545, 109], [22845, 109], [23145, 109], [23445, 109], [23744, 110], [24044, 110], [24344, 110], [24643, 111], [24943, 111], [25243, 111], [25543, 111], [25842, 112], [26142, 112], [26442, 112], [26741, 113], [27041, 113], [27341, 113], [27641, 113], [27940, 114], [28240, 114], [28540, 114], [28840, 114], [29139, 115], [29439, 115], [29739, 115], [30038, 116], [30338, 116], [30638, 116], [30938, 116], [31237, 117], [31537, 117], [31837, 117], [32136, 118], [32436, 118], [32736, 118], [33036, 118], [33335, 119], [33635, 119], [33935, 119], [34235, 119], [34534, 120], [34834, 120], [35134, 120], [35433, 121], [35733, 121], [36033, 122], [36333, 122], [36632, 123], [36932, 123], [37232, 123], [37531, 124], [37831, 124], [38131, 124], [38431, 124], [38730, 125], [39030, 125], [39330, 125], [39630, 125], [39929, 126], [40229, 126], [40529, 126], [40828, 127], [41128, 127], [41428, 127], [41728, 127], [42027, 128], [42327, 128], [42627, 128], [42926, 129], [43226, 129], [43526, 129], [43826, 129], [44125, 130], [44425, 130], [44725, 130], [45025, 130], [45324, 131], [45624, 131], [45924, 131], [46223, 132], [46523, 132], [46823, 132], [47123, 132], [47422, 133], [47722, 133], [48022, 133], [48321, 134], [48621, 134], [48921, 134], [49221, 134], [49520, 128], [49652, 3], [49820, 122], [50120, 116], [50419, 115], [50719, 113], [51019, 110], [51319, 108], [51618, 107], [51918, 105], [52218, 104], [52518, 103], [52817, 103], [53117, 102], [53417, 102], [53716, 102], [54016, 101], [54316, 100], [54616, 99], [54915, 99], [55215, 98], [55515, 98], [55814, 99], [56114, 98], [56414, 98], [56714, 98], [57013, 98], [57313, 98], [57613, 97], [57913, 97], [58212, 98], [58512, 97], [58812, 97], [59111, 98], [59411, 98], [59711, 98], [60011, 98], [60310, 99], [60610, 99], [60910, 99], [61209, 100], [61509, 100], [61809, 100], [62109, 100], [62408, 101], [62708, 101], [63008, 101], [63308, 101], [63607, 102], [63907, 102], [64207, 103], [64506, 104], [64806, 104], [65106, 104], [65406, 104], [65705, 105], [66005, 105], [66305, 106], [66604, 107], [66904, 108], [67204, 108], [67504, 108], [67803, 110], [68103, 110], [68403, 111], [68702, 112], [69002, 113], [69302, 113], [69602, 114], [69901, 116], [70201, 116], [70501, 117], [70801, 118], [71100, 119], [71400, 120], [71700, 120], [72000, 121], [72300, 122], [72600, 123], [72900, 124], [73200, 124], [73500, 123], [73800, 123], [74100, 122], [74400, 122], [74700, 122], [75000, 121], [75300, 121], [75600, 121], [75900, 120], [76200, 120], [76500, 120], [76800, 120], [77100, 119], [77400, 119], [77700, 119], [78000, 119], [78300, 119], [78600, 119], [78900, 119]], "point": [77, 163]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-01.12|+00.84|-01.24", "placeStationary": true, "receptacleObjectId": "Cabinet|+00.24|+02.01|-01.20"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 173, 264], "mask": [[151, 5], [451, 5], [751, 5], [1051, 5], [1351, 6], [1651, 6], [1951, 6], [2251, 6], [2551, 6], [2851, 6], [3151, 6], [3451, 6], [3751, 6], [4051, 6], [4351, 6], [4651, 6], [4951, 6], [5251, 6], [5551, 6], [5851, 6], [6151, 6], [6451, 6], [6751, 6], [7051, 6], [7351, 6], [7651, 6], [7951, 6], [8251, 6], [8551, 6], [8851, 6], [9151, 6], [9451, 6], [9751, 6], [10051, 6], [10351, 6], [10651, 6], [10951, 6], [11251, 6], [11551, 6], [11851, 6], [12151, 6], [12451, 6], [12751, 6], [13051, 6], [13351, 6], [13651, 6], [13951, 6], [14251, 6], [14551, 6], [14851, 6], [15151, 6], [15451, 7], [15751, 7], [16051, 7], [16351, 7], [16651, 7], [16951, 7], [17251, 7], [17551, 7], [17851, 7], [18151, 7], [18451, 7], [18751, 7], [19051, 7], [19351, 7], [19651, 7], [19850, 108], [20149, 109], [20449, 109], [20749, 109], [21049, 109], [21348, 110], [21648, 110], [21948, 110], [22248, 110], [22547, 111], [22847, 111], [23147, 111], [23446, 112], [23746, 112], [24046, 112], [24346, 112], [24645, 113], [24945, 113], [25245, 113], [25545, 113], [25844, 114], [26144, 114], [26444, 114], [26743, 115], [27043, 115], [27343, 115], [27643, 115], [27942, 116], [28242, 116], [28542, 116], [28842, 116], [29141, 117], [29441, 118], [29741, 118], [30040, 119], [30340, 119], [30640, 119], [30940, 119], [31239, 120], [31539, 120], [31839, 120], [32139, 120], [32438, 121], [32738, 121], [33038, 121], [33337, 122], [33637, 122], [33937, 122], [34237, 122], [34536, 123], [34836, 123], [35136, 123], [35436, 123], [35735, 124], [36035, 124], [36335, 124], [36634, 125], [36934, 125], [37234, 125], [37534, 125], [37833, 126], [38133, 126], [38433, 126], [38732, 127], [39032, 127], [39332, 127], [39632, 127], [39931, 128], [40231, 128], [40531, 128], [40831, 128], [41130, 129], [41430, 129], [41730, 129], [42029, 130], [42329, 130], [42629, 130], [42929, 130], [43228, 131], [43528, 132], [43828, 132], [44128, 132], [44427, 133], [44727, 133], [45027, 133], [45326, 134], [45626, 134], [45926, 134], [46226, 134], [46525, 135], [46825, 135], [47125, 135], [47425, 135], [47724, 136], [48024, 136], [48324, 136], [48623, 137], [48923, 137], [49223, 137], [49523, 125], [49652, 8], [49822, 120], [49958, 2], [50122, 114], [50422, 112], [50722, 110], [51021, 108], [51321, 106], [51621, 104], [51920, 103], [52220, 102], [52520, 101], [52820, 100], [53119, 100], [53419, 100], [53719, 99], [54019, 98], [54318, 98], [54618, 97], [54752, 4], [54918, 96], [55052, 8], [55217, 96], [55352, 8], [55517, 96], [55652, 8], [55817, 96], [55952, 8], [56117, 95], [56252, 8], [56416, 96], [56552, 8], [56716, 96], [56852, 8], [57016, 95], [57152, 8], [57316, 95], [57452, 9], [57615, 95], [57752, 11], [57915, 95], [58052, 13], [58215, 95], [58352, 14], [58514, 95], [58652, 16], [58814, 95], [58952, 18], [59114, 95], [59252, 20], [59414, 95], [59552, 21], [59713, 96], [59852, 21], [60013, 96], [60152, 21], [60313, 96], [60452, 21], [60613, 96], [60752, 21], [60912, 97], [61052, 21], [61212, 97], [61352, 21], [61512, 97], [61652, 21], [61811, 98], [61952, 21], [62111, 98], [62252, 21], [62411, 98], [62552, 21], [62711, 98], [62852, 21], [63010, 99], [63152, 22], [63310, 99], [63452, 22], [63610, 99], [63752, 22], [63910, 99], [64052, 22], [64209, 101], [64352, 22], [64509, 101], [64652, 22], [64809, 101], [64952, 21], [65108, 102], [65252, 21], [65408, 102], [65552, 21], [65708, 102], [65852, 21], [66008, 102], [66152, 20], [66307, 104], [66452, 19], [66607, 104], [66752, 17], [66907, 105], [67052, 16], [67207, 105], [67352, 14], [67506, 106], [67652, 12], [67806, 107], [67952, 10], [68106, 107], [68252, 8], [68405, 109], [68705, 109], [69005, 110], [69305, 110], [69604, 112], [69904, 113], [70204, 113], [70503, 115], [70803, 116], [71103, 116], [71403, 117], [71702, 118], [72002, 119], [72302, 120], [72602, 121], [72901, 123], [73201, 123], [73501, 122], [73800, 123], [74100, 122], [74400, 122], [74700, 122], [75000, 121], [75300, 121], [75600, 121], [75900, 120], [76200, 120], [76500, 120], [76800, 120], [77100, 119], [77400, 119], [77700, 119], [78000, 119], [78300, 119], [78600, 119], [78900, 119]], "point": [86, 131]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+00.24|+02.01|-01.20"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 173, 264], "mask": [[151, 5], [451, 5], [751, 5], [1051, 5], [1351, 6], [1651, 6], [1951, 6], [2251, 6], [2551, 6], [2851, 6], [3151, 6], [3451, 6], [3751, 6], [4051, 6], [4351, 6], [4651, 6], [4951, 6], [5251, 6], [5551, 6], [5851, 6], [6151, 6], [6451, 6], [6751, 6], [7051, 6], [7351, 6], [7651, 6], [7951, 6], [8251, 6], [8551, 6], [8851, 6], [9151, 6], [9451, 6], [9751, 6], [10051, 6], [10351, 6], [10651, 6], [10951, 6], [11251, 6], [11551, 6], [11851, 6], [12151, 6], [12451, 6], [12751, 6], [13051, 6], [13351, 6], [13651, 6], [13951, 6], [14251, 6], [14551, 6], [14851, 6], [15151, 6], [15451, 7], [15751, 7], [16051, 7], [16351, 7], [16651, 7], [16951, 7], [17251, 7], [17551, 7], [17851, 7], [18151, 7], [18451, 7], [18751, 7], [19051, 7], [19351, 7], [19651, 7], [19850, 108], [20149, 109], [20449, 109], [20749, 109], [21049, 109], [21348, 110], [21648, 110], [21948, 110], [22248, 110], [22547, 111], [22847, 111], [23147, 111], [23446, 112], [23746, 112], [24046, 112], [24346, 112], [24645, 113], [24945, 113], [25245, 113], [25545, 113], [25844, 114], [26144, 114], [26444, 114], [26743, 115], [27043, 115], [27343, 115], [27643, 115], [27942, 116], [28242, 116], [28542, 116], [28842, 116], [29141, 117], [29441, 118], [29741, 118], [30040, 119], [30340, 119], [30640, 119], [30940, 119], [31239, 120], [31539, 120], [31839, 120], [32139, 120], [32438, 121], [32738, 121], [33038, 121], [33337, 122], [33637, 122], [33937, 122], [34237, 122], [34536, 123], [34836, 123], [35136, 123], [35436, 123], [35735, 124], [36035, 124], [36335, 124], [36634, 125], [36934, 125], [37234, 125], [37534, 125], [37833, 126], [38133, 126], [38433, 126], [38732, 127], [39032, 127], [39332, 127], [39632, 127], [39931, 128], [40231, 128], [40531, 128], [40831, 128], [41130, 129], [41430, 129], [41730, 129], [42029, 130], [42329, 130], [42629, 130], [42929, 130], [43228, 131], [43528, 132], [43828, 132], [44128, 132], [44427, 133], [44727, 133], [45027, 133], [45326, 134], [45626, 134], [45926, 134], [46226, 134], [46525, 135], [46825, 135], [47125, 135], [47425, 135], [47724, 136], [48024, 136], [48324, 136], [48623, 137], [48923, 137], [49223, 137], [49523, 137], [49822, 138], [50122, 138], [50422, 138], [50722, 138], [51021, 139], [51321, 139], [51621, 139], [51920, 140], [52220, 140], [52520, 140], [52820, 140], [53119, 141], [53419, 141], [53719, 141], [54019, 141], [54318, 142], [54618, 142], [54918, 142], [55217, 143], [55517, 143], [55817, 143], [56117, 143], [56416, 144], [56716, 144], [57016, 144], [57316, 145], [57615, 148], [57915, 150], [58215, 151], [58514, 154], [58814, 156], [59114, 158], [59414, 159], [59713, 160], [60013, 160], [60313, 160], [60613, 160], [60912, 161], [61212, 102], [61328, 45], [61512, 99], [61632, 41], [61811, 98], [61934, 39], [62111, 98], [62235, 38], [62411, 97], [62536, 37], [62711, 97], [62836, 37], [63010, 98], [63136, 38], [63310, 98], [63436, 38], [63610, 98], [63736, 38], [63910, 97], [64036, 38], [64209, 98], [64336, 38], [64509, 98], [64636, 38], [64809, 98], [64936, 37], [65108, 99], [65236, 37], [65408, 99], [65536, 37], [65708, 99], [65836, 37], [66008, 99], [66136, 36], [66307, 100], [66435, 37], [66607, 101], [66735, 37], [66907, 101], [67035, 36], [67207, 101], [67335, 36], [67506, 102], [67634, 37], [67806, 102], [67934, 37], [68106, 103], [68233, 37], [68405, 104], [68533, 37], [68705, 105], [68832, 38], [69005, 105], [69132, 37], [69305, 105], [69431, 38], [69604, 107], [69731, 38], [69904, 108], [70030, 39], [70204, 109], [70329, 39], [70503, 111], [70628, 40], [70803, 113], [70926, 42], [71103, 115], [71224, 44], [71403, 116], [71522, 45], [71702, 117], [71822, 45], [72002, 117], [72122, 45], [72302, 117], [72422, 44], [72602, 117], [72722, 44], [72901, 118], [73022, 44], [73201, 118], [73322, 44], [73501, 118], [73622, 43], [73800, 119], [73921, 44], [74100, 119], [74221, 44], [74400, 119], [74521, 43], [74700, 118], [74821, 43], [75000, 118], [75121, 43], [75300, 118], [75421, 43], [75600, 118], [75721, 42], [75900, 118], [76021, 42], [76200, 118], [76321, 42], [76500, 118], [76621, 41], [76800, 118], [76921, 41], [77100, 118], [77221, 41], [77400, 118], [77521, 41], [77700, 118], [77821, 40], [78000, 118], [78121, 40], [78300, 116], [78421, 38], [78600, 104], [78732, 24], [78900, 103], [79035, 20]], "point": [86, 131]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan22", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.5, "y": 0.9009992, "z": 1.0}, "object_poses": [{"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "DishSponge_9171dd8d", "position": {"x": -0.344864368, "y": 0.910864234, "z": -1.36589718}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_9171dd8d", "position": {"x": -2.54045773, "y": 0.9108642, "z": -1.36589742}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_4b9198b3", "position": {"x": -2.58522987, "y": 0.7477182, "z": 0.163269192}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_4b9198b3", "position": {"x": 0.6108486, "y": 0.912429333, "z": -1.28244877}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_b9d165af", "position": {"x": -1.91011548, "y": 0.747497737, "z": -1.094794}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_ad96aa4f", "position": {"x": 0.997561, "y": 0.9131638, "z": -1.199}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_e02c5e35", "position": {"x": -2.699162, "y": 0.07124245, "z": 1.57196748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_e02c5e35", "position": {"x": -2.75755763, "y": 0.07124245, "z": 1.57196748}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9ffe4e75", "position": {"x": 0.548222065, "y": 1.161149, "z": 1.47979164}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "Bowl_b7a8ccec", "position": {"x": -2.8607583, "y": 0.3774938, "z": 0.690474033}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Bowl_b7a8ccec", "position": {"x": -1.56990516, "y": 0.116327584, "z": -1.06876659}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": -1.1153841, "y": 0.113595843, "z": -1.06876659}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": -0.06304765, "y": 1.10800624, "z": 1.86829472}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "SaltShaker_4f98d325", "position": {"x": -0.6579682, "y": 1.65877759, "z": -1.32230663}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f88244ed", "position": {"x": 0.241928414, "y": 1.004228, "z": -1.38111067}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f88244ed", "position": {"x": -2.438055, "y": 0.9063001, "z": -0.0629096553}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_304c8d90", "position": {"x": -2.59, "y": 0.9, "z": -1.147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a99f93a6", "position": {"x": 0.284412235, "y": 1.15439582, "z": 1.58049023}, "rotation": {"x": 359.99234, "y": 2.19712561e-07, "z": -0.00426209671}}, {"objectName": "Fork_b9d165af", "position": {"x": -1.50506079, "y": 0.8522292, "z": -1.30527985}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334183e-14}}, {"objectName": "Kettle_1cf3b23c", "position": {"x": -2.62011266, "y": 0.922509432, "z": -0.724260747}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_35b71b93", "position": {"x": 0.997390866, "y": 0.9, "z": -0.982281566}, "rotation": {"x": 0.0, "y": 334.941345, "z": 0.0}}, {"objectName": "Bread_81a93477", "position": {"x": -2.74032116, "y": 1.528507, "z": 0.932762742}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Plate_ad96aa4f", "position": {"x": -2.2369647, "y": 0.9131638, "z": -1.199}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_f346af22", "position": {"x": -0.748, "y": 0.8999999, "z": -1.291}, "rotation": {"x": 0.0, "y": 315.000031, "z": 0.0}}, {"objectName": "Spatula_3b50a8bf", "position": {"x": -2.55216455, "y": 0.922, "z": 0.0104080439}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_e2bf19ed", "position": {"x": -2.6608, "y": 0.8744, "z": 0.9831}, "rotation": {"x": 31.0465031, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_4f98d325", "position": {"x": -0.9981702, "y": 0.113595843, "z": -0.994823635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_9ffe4e75", "position": {"x": -2.7283597, "y": 0.0870659351, "z": 1.43180835}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_f59054f9", "position": {"x": -0.146045625, "y": 1.10800624, "z": 1.41300416}, "rotation": {"x": 0.0, "y": 315.0001, "z": 0.0}}, {"objectName": "ButterKnife_dc49bd4c", "position": {"x": -2.22248077, "y": 0.9, "z": -0.997339845}, "rotation": {"x": 0.0, "y": 323.977722, "z": 0.0}}, {"objectName": "Pot_86f597c8", "position": {"x": -2.823, "y": 0.9287, "z": -0.3964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5ea35e17", "position": {"x": -0.598039, "y": 0.9335, "z": -1.119659}, "rotation": {"x": 0.0, "y": 47.53002, "z": 0.0}}, {"objectName": "DishSponge_9171dd8d", "position": {"x": -1.41871345, "y": 0.8511799, "z": -1.25}, "rotation": {"x": 1.40334183e-14, "y": 3.208973e-43, "z": 2.89269767e-27}}, {"objectName": "Egg_e02c5e35", "position": {"x": 0.3466089, "y": 1.04439116, "z": -1.2486105}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_4b9198b3", "position": {"x": 0.920218468, "y": 0.912429333, "z": -0.9486538}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_1fa5d324", "position": {"x": 0.0605277, "y": 1.1069684, "z": 1.58733857}, "rotation": {"x": 270.019775, "y": 209.997864, "z": 0.0}}, {"objectName": "Cup_46773746", "position": {"x": -1.11968529, "y": 0.8416991, "z": -1.2411406}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_f88244ed", "position": {"x": 0.398949146, "y": 1.00045967, "z": -1.41423571}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_b7a8ccec", "position": {"x": -2.86075854, "y": 0.818859637, "z": 0.7712374}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 2084051908, "scene_num": 22}, "task_id": "trial_T20190907_173342_454704", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AJQGWGESKQT4Y_317HQ483IA93BU1ZT1XR07TY8OZINH", "high_descs": ["Go forward and stand in front of the sink.", "Take the wine glass out of the sink.", "Go to the left and stand in front of the microwave.", "Put the mug into the microwave and shut the door and then open it and take the mug out.", "Look up to the cabinets above the microwave.", "Open the cabinet to the left above the microwave and put the glass in and shut the door."], "task_desc": "Put a warmed glass in the cupboard.", "votes": [1, 1]}, {"assignment_id": "A3PPLDHC3CG0YN_33PPUNGG3BMALII1MC2U9M1Y5VIZR5", "high_descs": ["Walk to the sink straight ahead. ", "Pick up the wine glass to the left of the sponge. ", "Move to the counter space to the left of the dishwasher in front of the microwave. ", "Place the glass in the microwave, heat up the glass, take the glass out of the microwave and close the door. ", "Look up at the cabinets above the microwave. ", "Open the door to the cabinet on the left, place the glass inside the cabinet, and close the door. "], "task_desc": "To heat a glass and put in the cabinet. ", "votes": [1, 1]}, {"assignment_id": "A3PGS1Q1XAY79I_3YMU66OBIQPX579T0F2BABJM7Y0GHE", "high_descs": ["Walk to the left side of the sink.", "Pick up the brown goblet in the sink.", "Turn to your left and walk to the microwave oven.", "Open the microwave door and place the goblet on the plate in the microwave to the right of the egg, close the microwave door, turn the oven on and when it is done cooking, open the microwave door and pick up the goblet.", "Look up to the cupboard above the microwave oven and open the door of the left cupboard.", "Place the goblet at the right front of the cupboard and close the cupboard door."], "task_desc": "Move a goblet on the shelf to the microwave oven then into the cupboard", "votes": [1, 1]}]}}