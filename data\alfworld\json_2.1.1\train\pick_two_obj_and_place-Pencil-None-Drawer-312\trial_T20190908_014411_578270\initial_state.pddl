
(define (problem plan_trial_T20190908_014411_578270)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__plus_01_dot_47_bar__plus_00_dot_31_bar__minus_01_dot_34 - object
        AlarmClock_bar__plus_01_dot_56_bar__plus_00_dot_07_bar__minus_01_dot_34 - object
        AlarmClock_bar__plus_02_dot_35_bar__plus_00_dot_78_bar__minus_01_dot_72 - object
        BasketBall_bar__plus_01_dot_14_bar__plus_00_dot_12_bar__minus_01_dot_66 - object
        Book_bar__minus_00_dot_31_bar__plus_00_dot_51_bar__minus_00_dot_14 - object
        CD_bar__plus_01_dot_69_bar__plus_00_dot_75_bar__minus_01_dot_43 - object
        CellPhone_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_36 - object
        Chair_bar__plus_02_dot_16_bar__plus_00_dot_02_bar__minus_01_dot_13 - object
        CreditCard_bar__plus_00_dot_10_bar__plus_00_dot_12_bar__minus_01_dot_68 - object
        CreditCard_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_59 - object
        KeyChain_bar__plus_00_dot_15_bar__plus_00_dot_65_bar__minus_01_dot_82 - object
        KeyChain_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_70 - object
        KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_38_bar__minus_01_dot_61 - object
        Lamp_bar__plus_00_dot_00_bar__plus_00_dot_64_bar__minus_01_dot_77 - object
        Laptop_bar__minus_00_dot_79_bar__plus_00_dot_50_bar__minus_00_dot_30 - object
        LightSwitch_bar__minus_00_dot_61_bar__plus_01_dot_25_bar__plus_01_dot_44 - object
        Mirror_bar__plus_02_dot_82_bar__plus_01_dot_76_bar__minus_00_dot_07 - object
        Pencil_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_28 - object
        Pencil_bar__plus_01_dot_85_bar__plus_00_dot_75_bar__minus_01_dot_36 - object
        Pencil_bar__plus_02_dot_51_bar__plus_00_dot_75_bar__minus_01_dot_36 - object
        Pen_bar__plus_01_dot_52_bar__plus_00_dot_56_bar__minus_01_dot_34 - object
        Pen_bar__plus_02_dot_66_bar__plus_00_dot_05_bar__plus_01_dot_28 - object
        Pillow_bar__minus_00_dot_48_bar__plus_00_dot_59_bar__minus_00_dot_87 - object
        Pillow_bar__minus_00_dot_64_bar__plus_00_dot_59_bar__minus_01_dot_60 - object
        Window_bar__minus_01_dot_68_bar__plus_01_dot_40_bar__plus_00_dot_37 - object
        Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92 - receptacle
        Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58 - receptacle
        Drawer_bar__plus_00_dot_15_bar__plus_00_dot_20_bar__minus_01_dot_64 - receptacle
        Drawer_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64 - receptacle
        GarbageCan_bar__plus_02_dot_66_bar__plus_00_dot_00_bar__plus_01_dot_28 - receptacle
        Shelf_bar__plus_01_dot_56_bar__plus_00_dot_06_bar__minus_01_dot_34 - receptacle
        Shelf_bar__plus_01_dot_56_bar__plus_00_dot_30_bar__minus_01_dot_34 - receptacle
        Shelf_bar__plus_01_dot_56_bar__plus_00_dot_54_bar__minus_01_dot_34 - receptacle
        Shelf_bar__plus_01_dot_60_bar__plus_00_dot_77_bar__minus_01_dot_68 - receptacle
        Shelf_bar__plus_01_dot_60_bar__plus_01_dot_13_bar__minus_01_dot_68 - receptacle
        Shelf_bar__plus_01_dot_99_bar__plus_01_dot_38_bar__minus_01_dot_70 - receptacle
        Shelf_bar__plus_02_dot_24_bar__plus_00_dot_77_bar__minus_01_dot_68 - receptacle
        Shelf_bar__plus_02_dot_24_bar__plus_01_dot_13_bar__minus_01_dot_68 - receptacle
        SideTable_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64 - receptacle
        loc_bar_6_bar__minus_4_bar_2_bar_30 - location
        loc_bar_2_bar__minus_3_bar_2_bar_60 - location
        loc_bar_9_bar__minus_2_bar_2_bar_30 - location
        loc_bar_4_bar__minus_1_bar_2_bar_60 - location
        loc_bar_6_bar__minus_4_bar_2_bar_15 - location
        loc_bar_9_bar_4_bar_1_bar_60 - location
        loc_bar_2_bar__minus_5_bar_1_bar_45 - location
        loc_bar_6_bar__minus_4_bar_2_bar_45 - location
        loc_bar_9_bar_0_bar_1_bar__minus_15 - location
        loc_bar_3_bar__minus_3_bar_3_bar_45 - location
        loc_bar_4_bar__minus_5_bar_2_bar_60 - location
        loc_bar_2_bar__minus_4_bar_2_bar_60 - location
        loc_bar__minus_2_bar_4_bar_0_bar_45 - location
        loc_bar_9_bar__minus_2_bar_2_bar_60 - location
        loc_bar__minus_4_bar_2_bar_3_bar_15 - location
        loc_bar_5_bar__minus_3_bar_3_bar_60 - location
        loc_bar_8_bar__minus_2_bar_2_bar_60 - location
        loc_bar_5_bar_3_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType Shelf_bar__plus_01_dot_60_bar__plus_00_dot_77_bar__minus_01_dot_68 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_99_bar__plus_01_dot_38_bar__minus_01_dot_70 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_60_bar__plus_01_dot_13_bar__minus_01_dot_68 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_56_bar__plus_00_dot_54_bar__minus_01_dot_34 ShelfType)
        (receptacleType Drawer_bar__plus_00_dot_15_bar__plus_00_dot_20_bar__minus_01_dot_64 DrawerType)
        (receptacleType Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92 BedType)
        (receptacleType Shelf_bar__plus_02_dot_24_bar__plus_00_dot_77_bar__minus_01_dot_68 ShelfType)
        (receptacleType Shelf_bar__plus_02_dot_24_bar__plus_01_dot_13_bar__minus_01_dot_68 ShelfType)
        (receptacleType GarbageCan_bar__plus_02_dot_66_bar__plus_00_dot_00_bar__plus_01_dot_28 GarbageCanType)
        (receptacleType SideTable_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64 SideTableType)
        (receptacleType Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58 DeskType)
        (receptacleType Shelf_bar__plus_01_dot_56_bar__plus_00_dot_30_bar__minus_01_dot_34 ShelfType)
        (receptacleType Shelf_bar__plus_01_dot_56_bar__plus_00_dot_06_bar__minus_01_dot_34 ShelfType)
        (receptacleType Drawer_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64 DrawerType)
        (objectType AlarmClock_bar__plus_01_dot_56_bar__plus_00_dot_07_bar__minus_01_dot_34 AlarmClockType)
        (objectType Pen_bar__plus_01_dot_52_bar__plus_00_dot_56_bar__minus_01_dot_34 PenType)
        (objectType Pen_bar__plus_02_dot_66_bar__plus_00_dot_05_bar__plus_01_dot_28 PenType)
        (objectType CD_bar__plus_01_dot_69_bar__plus_00_dot_75_bar__minus_01_dot_43 CDType)
        (objectType Mirror_bar__plus_02_dot_82_bar__plus_01_dot_76_bar__minus_00_dot_07 MirrorType)
        (objectType BasketBall_bar__plus_01_dot_14_bar__plus_00_dot_12_bar__minus_01_dot_66 BasketBallType)
        (objectType KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_38_bar__minus_01_dot_61 KeyChainType)
        (objectType AlarmClock_bar__plus_02_dot_35_bar__plus_00_dot_78_bar__minus_01_dot_72 AlarmClockType)
        (objectType Chair_bar__plus_02_dot_16_bar__plus_00_dot_02_bar__minus_01_dot_13 ChairType)
        (objectType Pillow_bar__minus_00_dot_48_bar__plus_00_dot_59_bar__minus_00_dot_87 PillowType)
        (objectType KeyChain_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_70 KeyChainType)
        (objectType Pencil_bar__plus_01_dot_85_bar__plus_00_dot_75_bar__minus_01_dot_36 PencilType)
        (objectType Book_bar__minus_00_dot_31_bar__plus_00_dot_51_bar__minus_00_dot_14 BookType)
        (objectType LightSwitch_bar__minus_00_dot_61_bar__plus_01_dot_25_bar__plus_01_dot_44 LightSwitchType)
        (objectType KeyChain_bar__plus_00_dot_15_bar__plus_00_dot_65_bar__minus_01_dot_82 KeyChainType)
        (objectType CellPhone_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_36 CellPhoneType)
        (objectType Window_bar__minus_01_dot_68_bar__plus_01_dot_40_bar__plus_00_dot_37 WindowType)
        (objectType Pencil_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_28 PencilType)
        (objectType Pillow_bar__minus_00_dot_64_bar__plus_00_dot_59_bar__minus_01_dot_60 PillowType)
        (objectType Pencil_bar__plus_02_dot_51_bar__plus_00_dot_75_bar__minus_01_dot_36 PencilType)
        (objectType CreditCard_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_59 CreditCardType)
        (objectType CreditCard_bar__plus_00_dot_10_bar__plus_00_dot_12_bar__minus_01_dot_68 CreditCardType)
        (objectType AlarmClock_bar__plus_01_dot_47_bar__plus_00_dot_31_bar__minus_01_dot_34 AlarmClockType)
        (objectType Laptop_bar__minus_00_dot_79_bar__plus_00_dot_50_bar__minus_00_dot_30 LaptopType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain BedType BasketBallType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType CDType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType BasketBallType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType AlarmClockType)
        (canContain DeskType PenType)
        (canContain DeskType BookType)
        (canContain DeskType CDType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType CreditCardType)
        (canContain DeskType BasketBallType)
        (canContain DeskType LaptopType)
        (canContain DeskType PencilType)
        (canContain DeskType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (pickupable AlarmClock_bar__plus_01_dot_56_bar__plus_00_dot_07_bar__minus_01_dot_34)
        (pickupable Pen_bar__plus_01_dot_52_bar__plus_00_dot_56_bar__minus_01_dot_34)
        (pickupable Pen_bar__plus_02_dot_66_bar__plus_00_dot_05_bar__plus_01_dot_28)
        (pickupable CD_bar__plus_01_dot_69_bar__plus_00_dot_75_bar__minus_01_dot_43)
        (pickupable BasketBall_bar__plus_01_dot_14_bar__plus_00_dot_12_bar__minus_01_dot_66)
        (pickupable KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_38_bar__minus_01_dot_61)
        (pickupable AlarmClock_bar__plus_02_dot_35_bar__plus_00_dot_78_bar__minus_01_dot_72)
        (pickupable Pillow_bar__minus_00_dot_48_bar__plus_00_dot_59_bar__minus_00_dot_87)
        (pickupable KeyChain_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_70)
        (pickupable Pencil_bar__plus_01_dot_85_bar__plus_00_dot_75_bar__minus_01_dot_36)
        (pickupable Book_bar__minus_00_dot_31_bar__plus_00_dot_51_bar__minus_00_dot_14)
        (pickupable KeyChain_bar__plus_00_dot_15_bar__plus_00_dot_65_bar__minus_01_dot_82)
        (pickupable CellPhone_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_36)
        (pickupable Pencil_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_28)
        (pickupable Pillow_bar__minus_00_dot_64_bar__plus_00_dot_59_bar__minus_01_dot_60)
        (pickupable Pencil_bar__plus_02_dot_51_bar__plus_00_dot_75_bar__minus_01_dot_36)
        (pickupable CreditCard_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_59)
        (pickupable CreditCard_bar__plus_00_dot_10_bar__plus_00_dot_12_bar__minus_01_dot_68)
        (pickupable AlarmClock_bar__plus_01_dot_47_bar__plus_00_dot_31_bar__minus_01_dot_34)
        (pickupable Laptop_bar__minus_00_dot_79_bar__plus_00_dot_50_bar__minus_00_dot_30)
        
        (openable Drawer_bar__plus_00_dot_15_bar__plus_00_dot_20_bar__minus_01_dot_64)
        (openable Drawer_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64)
        
        (atLocation agent1 loc_bar_5_bar_3_bar_3_bar_30)
        
        
        
        
        
        
        
        
        
        
        
        
        (inReceptacle KeyChain_bar__plus_00_dot_15_bar__plus_00_dot_65_bar__minus_01_dot_82 SideTable_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64)
        (inReceptacle KeyChain_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_70 SideTable_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64)
        (inReceptacle CreditCard_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_59 SideTable_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64)
        (inReceptacle KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_38_bar__minus_01_dot_61 Drawer_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64)
        (inReceptacle CreditCard_bar__plus_00_dot_10_bar__plus_00_dot_12_bar__minus_01_dot_68 Drawer_bar__plus_00_dot_15_bar__plus_00_dot_20_bar__minus_01_dot_64)
        (inReceptacle CellPhone_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_36 Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58)
        (inReceptacle Pencil_bar__plus_02_dot_51_bar__plus_00_dot_75_bar__minus_01_dot_36 Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58)
        (inReceptacle Pencil_bar__plus_01_dot_85_bar__plus_00_dot_75_bar__minus_01_dot_36 Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58)
        (inReceptacle CD_bar__plus_01_dot_69_bar__plus_00_dot_75_bar__minus_01_dot_43 Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58)
        (inReceptacle Pencil_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_28 Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58)
        (inReceptacle Pillow_bar__minus_00_dot_48_bar__plus_00_dot_59_bar__minus_00_dot_87 Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92)
        (inReceptacle Laptop_bar__minus_00_dot_79_bar__plus_00_dot_50_bar__minus_00_dot_30 Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92)
        (inReceptacle Pillow_bar__minus_00_dot_64_bar__plus_00_dot_59_bar__minus_01_dot_60 Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92)
        (inReceptacle Book_bar__minus_00_dot_31_bar__plus_00_dot_51_bar__minus_00_dot_14 Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92)
        (inReceptacle Pen_bar__plus_02_dot_66_bar__plus_00_dot_05_bar__plus_01_dot_28 GarbageCan_bar__plus_02_dot_66_bar__plus_00_dot_00_bar__plus_01_dot_28)
        (inReceptacle AlarmClock_bar__plus_01_dot_47_bar__plus_00_dot_31_bar__minus_01_dot_34 Shelf_bar__plus_01_dot_56_bar__plus_00_dot_30_bar__minus_01_dot_34)
        (inReceptacle Pen_bar__plus_01_dot_52_bar__plus_00_dot_56_bar__minus_01_dot_34 Shelf_bar__plus_01_dot_56_bar__plus_00_dot_54_bar__minus_01_dot_34)
        (inReceptacle AlarmClock_bar__plus_01_dot_56_bar__plus_00_dot_07_bar__minus_01_dot_34 Shelf_bar__plus_01_dot_56_bar__plus_00_dot_06_bar__minus_01_dot_34)
        (inReceptacle AlarmClock_bar__plus_02_dot_35_bar__plus_00_dot_78_bar__minus_01_dot_72 Shelf_bar__plus_02_dot_24_bar__plus_00_dot_77_bar__minus_01_dot_68)
        
        
        (receptacleAtLocation Bed_bar__minus_00_dot_81_bar__minus_00_dot_01_bar__minus_00_dot_92 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (receptacleAtLocation Desk_bar__plus_02_dot_24_bar_00_dot_00_bar__minus_01_dot_58 loc_bar_2_bar__minus_5_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_00_dot_15_bar__plus_00_dot_20_bar__minus_01_dot_64 loc_bar_5_bar__minus_3_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64 loc_bar_2_bar__minus_3_bar_2_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_02_dot_66_bar__plus_00_dot_00_bar__plus_01_dot_28 loc_bar_9_bar_4_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_56_bar__plus_00_dot_06_bar__minus_01_dot_34 loc_bar_4_bar__minus_1_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_56_bar__plus_00_dot_30_bar__minus_01_dot_34 loc_bar_8_bar__minus_2_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_56_bar__plus_00_dot_54_bar__minus_01_dot_34 loc_bar_8_bar__minus_2_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_60_bar__plus_00_dot_77_bar__minus_01_dot_68 loc_bar_6_bar__minus_4_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__plus_01_dot_60_bar__plus_01_dot_13_bar__minus_01_dot_68 loc_bar_6_bar__minus_4_bar_2_bar_30)
        (receptacleAtLocation Shelf_bar__plus_01_dot_99_bar__plus_01_dot_38_bar__minus_01_dot_70 loc_bar_6_bar__minus_4_bar_2_bar_15)
        (receptacleAtLocation Shelf_bar__plus_02_dot_24_bar__plus_00_dot_77_bar__minus_01_dot_68 loc_bar_9_bar__minus_2_bar_2_bar_30)
        (receptacleAtLocation Shelf_bar__plus_02_dot_24_bar__plus_01_dot_13_bar__minus_01_dot_68 loc_bar_9_bar__minus_2_bar_2_bar_30)
        (receptacleAtLocation SideTable_bar__plus_00_dot_15_bar__plus_00_dot_45_bar__minus_01_dot_64 loc_bar_2_bar__minus_4_bar_2_bar_60)
        (objectAtLocation Pillow_bar__minus_00_dot_48_bar__plus_00_dot_59_bar__minus_00_dot_87 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation AlarmClock_bar__plus_01_dot_56_bar__plus_00_dot_07_bar__minus_01_dot_34 loc_bar_4_bar__minus_1_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__plus_00_dot_10_bar__plus_00_dot_12_bar__minus_01_dot_68 loc_bar_5_bar__minus_3_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_70 loc_bar_2_bar__minus_4_bar_2_bar_60)
        (objectAtLocation Pen_bar__plus_02_dot_66_bar__plus_00_dot_05_bar__plus_01_dot_28 loc_bar_9_bar_4_bar_1_bar_60)
        (objectAtLocation Pencil_bar__plus_02_dot_51_bar__plus_00_dot_75_bar__minus_01_dot_36 loc_bar_2_bar__minus_5_bar_1_bar_45)
        (objectAtLocation AlarmClock_bar__plus_01_dot_47_bar__plus_00_dot_31_bar__minus_01_dot_34 loc_bar_8_bar__minus_2_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__plus_00_dot_15_bar__plus_00_dot_65_bar__minus_01_dot_82 loc_bar_2_bar__minus_4_bar_2_bar_60)
        (objectAtLocation Pencil_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_28 loc_bar_2_bar__minus_5_bar_1_bar_45)
        (objectAtLocation Chair_bar__plus_02_dot_16_bar__plus_00_dot_02_bar__minus_01_dot_13 loc_bar_9_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Book_bar__minus_00_dot_31_bar__plus_00_dot_51_bar__minus_00_dot_14 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation Pencil_bar__plus_01_dot_85_bar__plus_00_dot_75_bar__minus_01_dot_36 loc_bar_2_bar__minus_5_bar_1_bar_45)
        (objectAtLocation BasketBall_bar__plus_01_dot_14_bar__plus_00_dot_12_bar__minus_01_dot_66 loc_bar_4_bar__minus_5_bar_2_bar_60)
        (objectAtLocation Laptop_bar__minus_00_dot_79_bar__plus_00_dot_50_bar__minus_00_dot_30 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation Pen_bar__plus_01_dot_52_bar__plus_00_dot_56_bar__minus_01_dot_34 loc_bar_8_bar__minus_2_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__plus_00_dot_25_bar__plus_00_dot_38_bar__minus_01_dot_61 loc_bar_2_bar__minus_3_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__plus_00_dot_22_bar__plus_00_dot_65_bar__minus_01_dot_59 loc_bar_2_bar__minus_4_bar_2_bar_60)
        (objectAtLocation CellPhone_bar__plus_01_dot_52_bar__plus_00_dot_75_bar__minus_01_dot_36 loc_bar_2_bar__minus_5_bar_1_bar_45)
        (objectAtLocation Mirror_bar__plus_02_dot_82_bar__plus_01_dot_76_bar__minus_00_dot_07 loc_bar_9_bar_0_bar_1_bar__minus_15)
        (objectAtLocation CD_bar__plus_01_dot_69_bar__plus_00_dot_75_bar__minus_01_dot_43 loc_bar_2_bar__minus_5_bar_1_bar_45)
        (objectAtLocation Window_bar__minus_01_dot_68_bar__plus_01_dot_40_bar__plus_00_dot_37 loc_bar__minus_4_bar_2_bar_3_bar_15)
        (objectAtLocation AlarmClock_bar__plus_02_dot_35_bar__plus_00_dot_78_bar__minus_01_dot_72 loc_bar_9_bar__minus_2_bar_2_bar_30)
        (objectAtLocation Pillow_bar__minus_00_dot_64_bar__plus_00_dot_59_bar__minus_01_dot_60 loc_bar_3_bar__minus_3_bar_3_bar_45)
        (objectAtLocation LightSwitch_bar__minus_00_dot_61_bar__plus_01_dot_25_bar__plus_01_dot_44 loc_bar__minus_2_bar_4_bar_0_bar_45)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 PencilType)
                                    (receptacleType ?r DrawerType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 PencilType)
                                            (receptacleType ?r DrawerType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            