
(define (problem plan_trial_T20190907_225306_342000)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__minus_01_dot_59_bar__plus_00_dot_50_bar__plus_02_dot_28 - object
        CellPhone_bar__minus_01_dot_91_bar__plus_00_dot_32_bar__plus_02_dot_66 - object
        CellPhone_bar__minus_02_dot_48_bar__plus_00_dot_53_bar__plus_02_dot_98 - object
        Chair_bar__minus_00_dot_14_bar__plus_00_dot_02_bar__plus_06_dot_41 - object
        Chair_bar__minus_03_dot_16_bar__plus_00_dot_02_bar__plus_00_dot_87 - object
        Chair_bar__minus_05_dot_18_bar__plus_00_dot_00_bar__plus_05_dot_29 - object
        Chair_bar__minus_05_dot_19_bar__plus_00_dot_00_bar__plus_04_dot_68 - object
        Chair_bar__minus_05_dot_58_bar__plus_00_dot_00_bar__plus_04_dot_10 - object
        Chair_bar__minus_05_dot_63_bar__plus_00_dot_00_bar__plus_05_dot_75 - object
        Chair_bar__minus_06_dot_04_bar__plus_00_dot_00_bar__plus_05_dot_24 - object
        Chair_bar__minus_06_dot_05_bar__plus_00_dot_00_bar__plus_04_dot_56 - object
        CreditCard_bar__minus_03_dot_69_bar__plus_00_dot_55_bar__plus_01_dot_91 - object
        Curtains_bar__minus_01_dot_05_bar__plus_03_dot_50_bar__plus_02_dot_88 - object
        DeskLamp_bar__plus_00_dot_89_bar__plus_00_dot_54_bar__plus_06_dot_82 - object
        FloorLamp_bar__minus_03_dot_49_bar_00_dot_00_bar__plus_00_dot_37 - object
        HousePlant_bar__minus_03_dot_58_bar__plus_00_dot_54_bar__plus_01_dot_64 - object
        KeyChain_bar__plus_00_dot_68_bar__plus_00_dot_54_bar__plus_03_dot_32 - object
        KeyChain_bar__minus_01_dot_99_bar__plus_00_dot_31_bar__plus_02_dot_56 - object
        KeyChain_bar__minus_02_dot_31_bar__plus_00_dot_53_bar__plus_02_dot_85 - object
        Laptop_bar__minus_01_dot_30_bar__plus_00_dot_31_bar__plus_02_dot_66 - object
        Laptop_bar__minus_05_dot_46_bar__plus_00_dot_78_bar__plus_05_dot_25 - object
        LightSwitch_bar__minus_05_dot_23_bar__plus_01_dot_39_bar__plus_07_dot_03 - object
        Newspaper_bar__plus_00_dot_91_bar__plus_00_dot_98_bar__plus_03_dot_51 - object
        Newspaper_bar__minus_03_dot_31_bar__plus_00_dot_55_bar__plus_01_dot_91 - object
        Painting_bar__minus_03_dot_81_bar__plus_01_dot_81_bar__plus_01_dot_01 - object
        Pillow_bar__minus_02_dot_72_bar__plus_00_dot_61_bar__plus_02_dot_77 - object
        Plate_bar__plus_00_dot_54_bar__plus_00_dot_55_bar__plus_06_dot_47 - object
        RemoteControl_bar__minus_01_dot_46_bar__plus_00_dot_31_bar__plus_02_dot_46 - object
        RemoteControl_bar__minus_01_dot_98_bar__plus_00_dot_31_bar__plus_02_dot_38 - object
        RemoteControl_bar__minus_05_dot_64_bar__plus_00_dot_78_bar__plus_04_dot_91 - object
        Statue_bar__plus_00_dot_91_bar__plus_00_dot_99_bar__plus_04_dot_34 - object
        Television_bar__plus_00_dot_83_bar__plus_01_dot_47_bar__plus_03_dot_73 - object
        WateringCan_bar__plus_00_dot_83_bar_00_dot_00_bar__plus_02_dot_34 - object
        Window_bar__minus_01_dot_99_bar__plus_01_dot_65_bar__minus_00_dot_01 - object
        Window_bar__minus_04_dot_91_bar__plus_00_dot_99_bar__plus_02_dot_02 - object
        Window_bar__minus_05_dot_80_bar__plus_01_dot_07_bar__plus_01_dot_87 - object
        CoffeeTable_bar__minus_01_dot_55_bar__plus_00_dot_02_bar__plus_02_dot_53 - receptacle
        DiningTable_bar__minus_05_dot_64_bar_00_dot_00_bar__plus_04_dot_91 - receptacle
        Drawer_bar__plus_00_dot_74_bar__plus_00_dot_23_bar__plus_03_dot_32 - receptacle
        Drawer_bar__plus_00_dot_74_bar__plus_00_dot_23_bar__plus_04_dot_10 - receptacle
        Drawer_bar__plus_00_dot_74_bar__plus_00_dot_67_bar__plus_03_dot_32 - receptacle
        Drawer_bar__plus_00_dot_74_bar__plus_00_dot_67_bar__plus_04_dot_10 - receptacle
        Dresser_bar__plus_00_dot_85_bar__plus_00_dot_00_bar__plus_03_dot_71 - receptacle
        GarbageCan_bar__minus_07_dot_10_bar_00_dot_00_bar__plus_02_dot_29 - receptacle
        SideTable_bar__plus_00_dot_73_bar__plus_00_dot_00_bar__plus_06_dot_64 - receptacle
        SideTable_bar__minus_03_dot_41_bar__plus_00_dot_00_bar__plus_01_dot_64 - receptacle
        Sofa_bar__minus_02_dot_47_bar_00_dot_00_bar__plus_03_dot_28 - receptacle
        loc_bar__minus_19_bar_18_bar_3_bar_60 - location
        loc_bar_3_bar_20_bar_2_bar_45 - location
        loc_bar_0_bar_9_bar_0_bar_60 - location
        loc_bar__minus_26_bar_13_bar_2_bar_60 - location
        loc_bar_2_bar_9_bar_1_bar_60 - location
        loc_bar_0_bar_12_bar_0_bar_45 - location
        loc_bar__minus_26_bar_21_bar_1_bar_60 - location
        loc_bar__minus_20_bar_10_bar_2_bar_60 - location
        loc_bar__minus_4_bar_15_bar_3_bar_45 - location
        loc_bar__minus_23_bar_25_bar_2_bar_60 - location
        loc_bar__minus_23_bar_10_bar_2_bar_45 - location
        loc_bar__minus_10_bar_3_bar_3_bar_60 - location
        loc_bar__minus_26_bar_20_bar_1_bar_60 - location
        loc_bar__minus_2_bar_9_bar_3_bar_60 - location
        loc_bar_3_bar_23_bar_0_bar_60 - location
        loc_bar__minus_8_bar_2_bar_2_bar_0 - location
        loc_bar__minus_10_bar_2_bar_3_bar_60 - location
        loc_bar__minus_1_bar_23_bar_0_bar_60 - location
        loc_bar__minus_19_bar_21_bar_3_bar_60 - location
        loc_bar__minus_10_bar_4_bar_3_bar_0 - location
        loc_bar__minus_3_bar_12_bar_3_bar__minus_30 - location
        loc_bar_0_bar_16_bar_1_bar_60 - location
        loc_bar__minus_21_bar_26_bar_0_bar_30 - location
        loc_bar__minus_10_bar_8_bar_3_bar_60 - location
        loc_bar_0_bar_13_bar_1_bar_60 - location
        loc_bar__minus_22_bar_14_bar_0_bar_60 - location
        loc_bar__minus_26_bar_18_bar_1_bar_60 - location
        loc_bar__minus_4_bar_6_bar_1_bar_30 - location
        )
    

(:init


        (receptacleType Drawer_bar__plus_00_dot_74_bar__plus_00_dot_23_bar__plus_03_dot_32 DrawerType)
        (receptacleType Dresser_bar__plus_00_dot_85_bar__plus_00_dot_00_bar__plus_03_dot_71 DresserType)
        (receptacleType Drawer_bar__plus_00_dot_74_bar__plus_00_dot_23_bar__plus_04_dot_10 DrawerType)
        (receptacleType DiningTable_bar__minus_05_dot_64_bar_00_dot_00_bar__plus_04_dot_91 DiningTableType)
        (receptacleType SideTable_bar__minus_03_dot_41_bar__plus_00_dot_00_bar__plus_01_dot_64 SideTableType)
        (receptacleType GarbageCan_bar__minus_07_dot_10_bar_00_dot_00_bar__plus_02_dot_29 GarbageCanType)
        (receptacleType CoffeeTable_bar__minus_01_dot_55_bar__plus_00_dot_02_bar__plus_02_dot_53 CoffeeTableType)
        (receptacleType Drawer_bar__plus_00_dot_74_bar__plus_00_dot_67_bar__plus_04_dot_10 DrawerType)
        (receptacleType Sofa_bar__minus_02_dot_47_bar_00_dot_00_bar__plus_03_dot_28 SofaType)
        (receptacleType Drawer_bar__plus_00_dot_74_bar__plus_00_dot_67_bar__plus_03_dot_32 DrawerType)
        (receptacleType SideTable_bar__plus_00_dot_73_bar__plus_00_dot_00_bar__plus_06_dot_64 SideTableType)
        (objectType WateringCan_bar__plus_00_dot_83_bar_00_dot_00_bar__plus_02_dot_34 WateringCanType)
        (objectType Chair_bar__minus_05_dot_18_bar__plus_00_dot_00_bar__plus_05_dot_29 ChairType)
        (objectType Window_bar__minus_01_dot_99_bar__plus_01_dot_65_bar__minus_00_dot_01 WindowType)
        (objectType KeyChain_bar__minus_02_dot_31_bar__plus_00_dot_53_bar__plus_02_dot_85 KeyChainType)
        (objectType Chair_bar__minus_06_dot_04_bar__plus_00_dot_00_bar__plus_05_dot_24 ChairType)
        (objectType Newspaper_bar__minus_03_dot_31_bar__plus_00_dot_55_bar__plus_01_dot_91 NewspaperType)
        (objectType FloorLamp_bar__minus_03_dot_49_bar_00_dot_00_bar__plus_00_dot_37 FloorLampType)
        (objectType Chair_bar__minus_00_dot_14_bar__plus_00_dot_02_bar__plus_06_dot_41 ChairType)
        (objectType Statue_bar__plus_00_dot_91_bar__plus_00_dot_99_bar__plus_04_dot_34 StatueType)
        (objectType KeyChain_bar__minus_01_dot_99_bar__plus_00_dot_31_bar__plus_02_dot_56 KeyChainType)
        (objectType Window_bar__minus_04_dot_91_bar__plus_00_dot_99_bar__plus_02_dot_02 WindowType)
        (objectType Chair_bar__minus_05_dot_19_bar__plus_00_dot_00_bar__plus_04_dot_68 ChairType)
        (objectType HousePlant_bar__minus_03_dot_58_bar__plus_00_dot_54_bar__plus_01_dot_64 HousePlantType)
        (objectType Chair_bar__minus_05_dot_63_bar__plus_00_dot_00_bar__plus_05_dot_75 ChairType)
        (objectType Curtains_bar__minus_01_dot_05_bar__plus_03_dot_50_bar__plus_02_dot_88 CurtainsType)
        (objectType Chair_bar__minus_06_dot_05_bar__plus_00_dot_00_bar__plus_04_dot_56 ChairType)
        (objectType Box_bar__minus_01_dot_59_bar__plus_00_dot_50_bar__plus_02_dot_28 BoxType)
        (objectType Pillow_bar__minus_02_dot_72_bar__plus_00_dot_61_bar__plus_02_dot_77 PillowType)
        (objectType LightSwitch_bar__minus_05_dot_23_bar__plus_01_dot_39_bar__plus_07_dot_03 LightSwitchType)
        (objectType Painting_bar__minus_03_dot_81_bar__plus_01_dot_81_bar__plus_01_dot_01 PaintingType)
        (objectType CellPhone_bar__minus_02_dot_48_bar__plus_00_dot_53_bar__plus_02_dot_98 CellPhoneType)
        (objectType RemoteControl_bar__minus_01_dot_98_bar__plus_00_dot_31_bar__plus_02_dot_38 RemoteControlType)
        (objectType Television_bar__plus_00_dot_83_bar__plus_01_dot_47_bar__plus_03_dot_73 TelevisionType)
        (objectType RemoteControl_bar__minus_05_dot_64_bar__plus_00_dot_78_bar__plus_04_dot_91 RemoteControlType)
        (objectType Plate_bar__plus_00_dot_54_bar__plus_00_dot_55_bar__plus_06_dot_47 PlateType)
        (objectType CellPhone_bar__minus_01_dot_91_bar__plus_00_dot_32_bar__plus_02_dot_66 CellPhoneType)
        (objectType DeskLamp_bar__plus_00_dot_89_bar__plus_00_dot_54_bar__plus_06_dot_82 DeskLampType)
        (objectType CreditCard_bar__minus_03_dot_69_bar__plus_00_dot_55_bar__plus_01_dot_91 CreditCardType)
        (objectType Chair_bar__minus_03_dot_16_bar__plus_00_dot_02_bar__plus_00_dot_87 ChairType)
        (objectType KeyChain_bar__plus_00_dot_68_bar__plus_00_dot_54_bar__plus_03_dot_32 KeyChainType)
        (objectType Laptop_bar__minus_05_dot_46_bar__plus_00_dot_78_bar__plus_05_dot_25 LaptopType)
        (objectType Window_bar__minus_05_dot_80_bar__plus_01_dot_07_bar__plus_01_dot_87 WindowType)
        (objectType Newspaper_bar__plus_00_dot_91_bar__plus_00_dot_98_bar__plus_03_dot_51 NewspaperType)
        (objectType Laptop_bar__minus_01_dot_30_bar__plus_00_dot_31_bar__plus_02_dot_66 LaptopType)
        (objectType Chair_bar__minus_05_dot_58_bar__plus_00_dot_00_bar__plus_04_dot_10 ChairType)
        (objectType RemoteControl_bar__minus_01_dot_46_bar__plus_00_dot_31_bar__plus_02_dot_46 RemoteControlType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DresserType NewspaperType)
        (canContain DresserType BoxType)
        (canContain DresserType CellPhoneType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType LaptopType)
        (canContain DresserType PlateType)
        (canContain DresserType RemoteControlType)
        (canContain DresserType StatueType)
        (canContain DresserType WateringCanType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DiningTableType NewspaperType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType CellPhoneType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType StatueType)
        (canContain DiningTableType WateringCanType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PlateType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain GarbageCanType NewspaperType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType CellPhoneType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType PlateType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain CoffeeTableType WateringCanType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType CellPhoneType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PlateType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (pickupable WateringCan_bar__plus_00_dot_83_bar_00_dot_00_bar__plus_02_dot_34)
        (pickupable KeyChain_bar__minus_02_dot_31_bar__plus_00_dot_53_bar__plus_02_dot_85)
        (pickupable Newspaper_bar__minus_03_dot_31_bar__plus_00_dot_55_bar__plus_01_dot_91)
        (pickupable Statue_bar__plus_00_dot_91_bar__plus_00_dot_99_bar__plus_04_dot_34)
        (pickupable KeyChain_bar__minus_01_dot_99_bar__plus_00_dot_31_bar__plus_02_dot_56)
        (pickupable Box_bar__minus_01_dot_59_bar__plus_00_dot_50_bar__plus_02_dot_28)
        (pickupable Pillow_bar__minus_02_dot_72_bar__plus_00_dot_61_bar__plus_02_dot_77)
        (pickupable CellPhone_bar__minus_02_dot_48_bar__plus_00_dot_53_bar__plus_02_dot_98)
        (pickupable RemoteControl_bar__minus_01_dot_98_bar__plus_00_dot_31_bar__plus_02_dot_38)
        (pickupable RemoteControl_bar__minus_05_dot_64_bar__plus_00_dot_78_bar__plus_04_dot_91)
        (pickupable Plate_bar__plus_00_dot_54_bar__plus_00_dot_55_bar__plus_06_dot_47)
        (pickupable CellPhone_bar__minus_01_dot_91_bar__plus_00_dot_32_bar__plus_02_dot_66)
        (pickupable CreditCard_bar__minus_03_dot_69_bar__plus_00_dot_55_bar__plus_01_dot_91)
        (pickupable KeyChain_bar__plus_00_dot_68_bar__plus_00_dot_54_bar__plus_03_dot_32)
        (pickupable Laptop_bar__minus_05_dot_46_bar__plus_00_dot_78_bar__plus_05_dot_25)
        (pickupable Newspaper_bar__plus_00_dot_91_bar__plus_00_dot_98_bar__plus_03_dot_51)
        (pickupable Laptop_bar__minus_01_dot_30_bar__plus_00_dot_31_bar__plus_02_dot_66)
        (pickupable RemoteControl_bar__minus_01_dot_46_bar__plus_00_dot_31_bar__plus_02_dot_46)
        (isReceptacleObject Box_bar__minus_01_dot_59_bar__plus_00_dot_50_bar__plus_02_dot_28)
        (isReceptacleObject Plate_bar__plus_00_dot_54_bar__plus_00_dot_55_bar__plus_06_dot_47)
        (openable Drawer_bar__plus_00_dot_74_bar__plus_00_dot_23_bar__plus_03_dot_32)
        (openable Drawer_bar__plus_00_dot_74_bar__plus_00_dot_23_bar__plus_04_dot_10)
        (openable Drawer_bar__plus_00_dot_74_bar__plus_00_dot_67_bar__plus_04_dot_10)
        (openable Drawer_bar__plus_00_dot_74_bar__plus_00_dot_67_bar__plus_03_dot_32)
        
        (atLocation agent1 loc_bar__minus_4_bar_6_bar_1_bar_30)
        
        (cleanable Plate_bar__plus_00_dot_54_bar__plus_00_dot_55_bar__plus_06_dot_47)
        
        (heatable Plate_bar__plus_00_dot_54_bar__plus_00_dot_55_bar__plus_06_dot_47)
        (coolable Plate_bar__plus_00_dot_54_bar__plus_00_dot_55_bar__plus_06_dot_47)
        
        
        (toggleable FloorLamp_bar__minus_03_dot_49_bar_00_dot_00_bar__plus_00_dot_37)
        (toggleable DeskLamp_bar__plus_00_dot_89_bar__plus_00_dot_54_bar__plus_06_dot_82)
        
        
        
        
        (inReceptacle Newspaper_bar__plus_00_dot_91_bar__plus_00_dot_98_bar__plus_03_dot_51 Dresser_bar__plus_00_dot_85_bar__plus_00_dot_00_bar__plus_03_dot_71)
        (inReceptacle Television_bar__plus_00_dot_83_bar__plus_01_dot_47_bar__plus_03_dot_73 Dresser_bar__plus_00_dot_85_bar__plus_00_dot_00_bar__plus_03_dot_71)
        (inReceptacle Statue_bar__plus_00_dot_91_bar__plus_00_dot_99_bar__plus_04_dot_34 Dresser_bar__plus_00_dot_85_bar__plus_00_dot_00_bar__plus_03_dot_71)
        (inReceptacle KeyChain_bar__minus_02_dot_31_bar__plus_00_dot_53_bar__plus_02_dot_85 Sofa_bar__minus_02_dot_47_bar_00_dot_00_bar__plus_03_dot_28)
        (inReceptacle Pillow_bar__minus_02_dot_72_bar__plus_00_dot_61_bar__plus_02_dot_77 Sofa_bar__minus_02_dot_47_bar_00_dot_00_bar__plus_03_dot_28)
        (inReceptacle CellPhone_bar__minus_02_dot_48_bar__plus_00_dot_53_bar__plus_02_dot_98 Sofa_bar__minus_02_dot_47_bar_00_dot_00_bar__plus_03_dot_28)
        (inReceptacle Newspaper_bar__minus_03_dot_31_bar__plus_00_dot_55_bar__plus_01_dot_91 SideTable_bar__minus_03_dot_41_bar__plus_00_dot_00_bar__plus_01_dot_64)
        (inReceptacle HousePlant_bar__minus_03_dot_58_bar__plus_00_dot_54_bar__plus_01_dot_64 SideTable_bar__minus_03_dot_41_bar__plus_00_dot_00_bar__plus_01_dot_64)
        (inReceptacle CreditCard_bar__minus_03_dot_69_bar__plus_00_dot_55_bar__plus_01_dot_91 SideTable_bar__minus_03_dot_41_bar__plus_00_dot_00_bar__plus_01_dot_64)
        (inReceptacle Laptop_bar__minus_05_dot_46_bar__plus_00_dot_78_bar__plus_05_dot_25 DiningTable_bar__minus_05_dot_64_bar_00_dot_00_bar__plus_04_dot_91)
        (inReceptacle RemoteControl_bar__minus_05_dot_64_bar__plus_00_dot_78_bar__plus_04_dot_91 DiningTable_bar__minus_05_dot_64_bar_00_dot_00_bar__plus_04_dot_91)
        (inReceptacle KeyChain_bar__plus_00_dot_68_bar__plus_00_dot_54_bar__plus_03_dot_32 Drawer_bar__plus_00_dot_74_bar__plus_00_dot_67_bar__plus_03_dot_32)
        (inReceptacle Box_bar__minus_01_dot_59_bar__plus_00_dot_50_bar__plus_02_dot_28 CoffeeTable_bar__minus_01_dot_55_bar__plus_00_dot_02_bar__plus_02_dot_53)
        (inReceptacle CellPhone_bar__minus_01_dot_91_bar__plus_00_dot_32_bar__plus_02_dot_66 CoffeeTable_bar__minus_01_dot_55_bar__plus_00_dot_02_bar__plus_02_dot_53)
        (inReceptacle Laptop_bar__minus_01_dot_30_bar__plus_00_dot_31_bar__plus_02_dot_66 CoffeeTable_bar__minus_01_dot_55_bar__plus_00_dot_02_bar__plus_02_dot_53)
        (inReceptacle KeyChain_bar__minus_01_dot_99_bar__plus_00_dot_31_bar__plus_02_dot_56 CoffeeTable_bar__minus_01_dot_55_bar__plus_00_dot_02_bar__plus_02_dot_53)
        (inReceptacle RemoteControl_bar__minus_01_dot_98_bar__plus_00_dot_31_bar__plus_02_dot_38 CoffeeTable_bar__minus_01_dot_55_bar__plus_00_dot_02_bar__plus_02_dot_53)
        (inReceptacle RemoteControl_bar__minus_01_dot_46_bar__plus_00_dot_31_bar__plus_02_dot_46 CoffeeTable_bar__minus_01_dot_55_bar__plus_00_dot_02_bar__plus_02_dot_53)
        (inReceptacle DeskLamp_bar__plus_00_dot_89_bar__plus_00_dot_54_bar__plus_06_dot_82 SideTable_bar__plus_00_dot_73_bar__plus_00_dot_00_bar__plus_06_dot_64)
        (inReceptacle Plate_bar__plus_00_dot_54_bar__plus_00_dot_55_bar__plus_06_dot_47 SideTable_bar__plus_00_dot_73_bar__plus_00_dot_00_bar__plus_06_dot_64)
        
        
        (receptacleAtLocation CoffeeTable_bar__minus_01_dot_55_bar__plus_00_dot_02_bar__plus_02_dot_53 loc_bar__minus_2_bar_9_bar_3_bar_60)
        (receptacleAtLocation DiningTable_bar__minus_05_dot_64_bar_00_dot_00_bar__plus_04_dot_91 loc_bar__minus_26_bar_20_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_74_bar__plus_00_dot_23_bar__plus_03_dot_32 loc_bar_0_bar_9_bar_0_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_74_bar__plus_00_dot_23_bar__plus_04_dot_10 loc_bar_0_bar_12_bar_0_bar_45)
        (receptacleAtLocation Drawer_bar__plus_00_dot_74_bar__plus_00_dot_67_bar__plus_03_dot_32 loc_bar_0_bar_13_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_74_bar__plus_00_dot_67_bar__plus_04_dot_10 loc_bar_0_bar_16_bar_1_bar_60)
        (receptacleAtLocation Dresser_bar__plus_00_dot_85_bar__plus_00_dot_00_bar__plus_03_dot_71 loc_bar_3_bar_20_bar_2_bar_45)
        (receptacleAtLocation GarbageCan_bar__minus_07_dot_10_bar_00_dot_00_bar__plus_02_dot_29 loc_bar__minus_26_bar_13_bar_2_bar_60)
        (receptacleAtLocation SideTable_bar__plus_00_dot_73_bar__plus_00_dot_00_bar__plus_06_dot_64 loc_bar_3_bar_23_bar_0_bar_60)
        (receptacleAtLocation SideTable_bar__minus_03_dot_41_bar__plus_00_dot_00_bar__plus_01_dot_64 loc_bar__minus_10_bar_8_bar_3_bar_60)
        (receptacleAtLocation Sofa_bar__minus_02_dot_47_bar_00_dot_00_bar__plus_03_dot_28 loc_bar__minus_4_bar_15_bar_3_bar_45)
        (objectAtLocation Laptop_bar__minus_05_dot_46_bar__plus_00_dot_78_bar__plus_05_dot_25 loc_bar__minus_26_bar_20_bar_1_bar_60)
        (objectAtLocation RemoteControl_bar__minus_01_dot_46_bar__plus_00_dot_31_bar__plus_02_dot_46 loc_bar__minus_2_bar_9_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__minus_02_dot_31_bar__plus_00_dot_53_bar__plus_02_dot_85 loc_bar__minus_4_bar_15_bar_3_bar_45)
        (objectAtLocation Newspaper_bar__plus_00_dot_91_bar__plus_00_dot_98_bar__plus_03_dot_51 loc_bar_3_bar_20_bar_2_bar_45)
        (objectAtLocation CellPhone_bar__minus_01_dot_91_bar__plus_00_dot_32_bar__plus_02_dot_66 loc_bar__minus_2_bar_9_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__minus_05_dot_64_bar__plus_00_dot_78_bar__plus_04_dot_91 loc_bar__minus_26_bar_20_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__plus_00_dot_68_bar__plus_00_dot_54_bar__plus_03_dot_32 loc_bar_0_bar_13_bar_1_bar_60)
        (objectAtLocation Chair_bar__minus_06_dot_04_bar__plus_00_dot_00_bar__plus_05_dot_24 loc_bar__minus_26_bar_21_bar_1_bar_60)
        (objectAtLocation Chair_bar__minus_05_dot_58_bar__plus_00_dot_00_bar__plus_04_dot_10 loc_bar__minus_22_bar_14_bar_0_bar_60)
        (objectAtLocation Chair_bar__minus_00_dot_14_bar__plus_00_dot_02_bar__plus_06_dot_41 loc_bar__minus_1_bar_23_bar_0_bar_60)
        (objectAtLocation Chair_bar__minus_05_dot_18_bar__plus_00_dot_00_bar__plus_05_dot_29 loc_bar__minus_19_bar_21_bar_3_bar_60)
        (objectAtLocation Chair_bar__minus_05_dot_63_bar__plus_00_dot_00_bar__plus_05_dot_75 loc_bar__minus_23_bar_25_bar_2_bar_60)
        (objectAtLocation Chair_bar__minus_03_dot_16_bar__plus_00_dot_02_bar__plus_00_dot_87 loc_bar__minus_10_bar_3_bar_3_bar_60)
        (objectAtLocation Chair_bar__minus_05_dot_19_bar__plus_00_dot_00_bar__plus_04_dot_68 loc_bar__minus_19_bar_18_bar_3_bar_60)
        (objectAtLocation Box_bar__minus_01_dot_59_bar__plus_00_dot_50_bar__plus_02_dot_28 loc_bar__minus_2_bar_9_bar_3_bar_60)
        (objectAtLocation Chair_bar__minus_06_dot_05_bar__plus_00_dot_00_bar__plus_04_dot_56 loc_bar__minus_26_bar_18_bar_1_bar_60)
        (objectAtLocation Curtains_bar__minus_01_dot_05_bar__plus_03_dot_50_bar__plus_02_dot_88 loc_bar__minus_3_bar_12_bar_3_bar__minus_30)
        (objectAtLocation CellPhone_bar__minus_02_dot_48_bar__plus_00_dot_53_bar__plus_02_dot_98 loc_bar__minus_4_bar_15_bar_3_bar_45)
        (objectAtLocation HousePlant_bar__minus_03_dot_58_bar__plus_00_dot_54_bar__plus_01_dot_64 loc_bar__minus_10_bar_8_bar_3_bar_60)
        (objectAtLocation Television_bar__plus_00_dot_83_bar__plus_01_dot_47_bar__plus_03_dot_73 loc_bar_3_bar_20_bar_2_bar_45)
        (objectAtLocation WateringCan_bar__plus_00_dot_83_bar_00_dot_00_bar__plus_02_dot_34 loc_bar_2_bar_9_bar_1_bar_60)
        (objectAtLocation Newspaper_bar__minus_03_dot_31_bar__plus_00_dot_55_bar__plus_01_dot_91 loc_bar__minus_10_bar_8_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__minus_01_dot_99_bar__plus_00_dot_31_bar__plus_02_dot_56 loc_bar__minus_2_bar_9_bar_3_bar_60)
        (objectAtLocation Plate_bar__plus_00_dot_54_bar__plus_00_dot_55_bar__plus_06_dot_47 loc_bar_3_bar_23_bar_0_bar_60)
        (objectAtLocation Pillow_bar__minus_02_dot_72_bar__plus_00_dot_61_bar__plus_02_dot_77 loc_bar__minus_4_bar_15_bar_3_bar_45)
        (objectAtLocation Painting_bar__minus_03_dot_81_bar__plus_01_dot_81_bar__plus_01_dot_01 loc_bar__minus_10_bar_4_bar_3_bar_0)
        (objectAtLocation DeskLamp_bar__plus_00_dot_89_bar__plus_00_dot_54_bar__plus_06_dot_82 loc_bar_3_bar_23_bar_0_bar_60)
        (objectAtLocation RemoteControl_bar__minus_01_dot_98_bar__plus_00_dot_31_bar__plus_02_dot_38 loc_bar__minus_2_bar_9_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__minus_03_dot_69_bar__plus_00_dot_55_bar__plus_01_dot_91 loc_bar__minus_10_bar_8_bar_3_bar_60)
        (objectAtLocation FloorLamp_bar__minus_03_dot_49_bar_00_dot_00_bar__plus_00_dot_37 loc_bar__minus_10_bar_2_bar_3_bar_60)
        (objectAtLocation LightSwitch_bar__minus_05_dot_23_bar__plus_01_dot_39_bar__plus_07_dot_03 loc_bar__minus_21_bar_26_bar_0_bar_30)
        (objectAtLocation Window_bar__minus_01_dot_99_bar__plus_01_dot_65_bar__minus_00_dot_01 loc_bar__minus_8_bar_2_bar_2_bar_0)
        (objectAtLocation Window_bar__minus_04_dot_91_bar__plus_00_dot_99_bar__plus_02_dot_02 loc_bar__minus_20_bar_10_bar_2_bar_60)
        (objectAtLocation Window_bar__minus_05_dot_80_bar__plus_01_dot_07_bar__plus_01_dot_87 loc_bar__minus_23_bar_10_bar_2_bar_45)
        (objectAtLocation Laptop_bar__minus_01_dot_30_bar__plus_00_dot_31_bar__plus_02_dot_66 loc_bar__minus_2_bar_9_bar_3_bar_60)
        (objectAtLocation Statue_bar__plus_00_dot_91_bar__plus_00_dot_99_bar__plus_04_dot_34 loc_bar_3_bar_20_bar_2_bar_45)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 NewspaperType)
                                    (receptacleType ?r GarbageCanType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 NewspaperType)
                                            (receptacleType ?r GarbageCanType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            