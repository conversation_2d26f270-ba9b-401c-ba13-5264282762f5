{"pddl_domain": ";; Specification in PDDL of the Alfred domain\n;; Intended to be used with Fast Downward which supports PDDL 2.2 level 1 plus the :action-costs requirement from PDDL 3.1.\n\n(define (domain alfred)\n (:requirements\n    :adl\n    :action-costs\n    :typing\n )\n (:types\n  agent\n  location\n  receptacle\n  object\n  rtype\n  otype\n  )\n\n\n (:predicates\n    (atLocation ?a - agent ?l - location)                     ; true if the agent is at the location\n    (receptacleAtLocation ?r - receptacle ?l - location)      ; true if the receptacle is at the location (constant)\n    (objectAtLocation ?o - object ?l - location)              ; true if the object is at the location\n    (openable ?r - receptacle)                                ; true if a receptacle is openable\n    (opened ?r - receptacle)                                  ; true if a receptacle is opened\n    (inReceptacle ?o - object ?r - receptacle)                ; object ?o is in receptacle ?r\n    (isReceptacleObject ?o - object)                          ; true if the object can have things put inside it\n    (inReceptacleObject ?innerObject - object ?outerObject - object)                ; object ?innerObject is inside object ?outerObject\n    (isReceptacleObjectFull ?o - object)                      ; true if the receptacle object contains something\n    (wasInReceptacle ?o - object ?r - receptacle)             ; object ?o was or is in receptacle ?r now or some time in the past\n    (checked ?r - receptacle)                                 ; whether the receptacle has been looked inside/visited\n    (examined ?l - location)                                  ; TODO\n    (receptacleType ?r - receptacle ?t - rtype)               ; the type of receptacle (Cabinet vs Cabinet|01|2...)\n    (canContain ?rt - rtype ?ot - otype)                      ; true if receptacle can hold object\n    (objectType ?o - object ?t - otype)                       ; the type of object (Apple vs Apple|01|2...)\n    (holds ?a - agent ?o - object)                            ; object ?o is held by agent ?a\n    (holdsAny ?a - agent)                                     ; agent ?a holds an object\n    (holdsAnyReceptacleObject ?a - agent)                        ; agent ?a holds a receptacle object\n    (full ?r - receptacle)                                    ; true if the receptacle has no remaining space\n    (isClean ?o - object)                                     ; true if the object has been clean in sink\n    (cleanable ?o - object)                                   ; true if the object can be placed in a sink\n    (isHot ?o - object)                                       ; true if the object has been heated up\n    (heatable ?o - object)                                    ; true if the object can be heated up in a microwave\n    (isCool ?o - object)                                      ; true if the object has been cooled\n    (coolable ?o - object)                                    ; true if the object can be cooled in the fridge\n    (pickupable ?o - object)                                   ; true if the object can be picked up\n    (moveable ?o - object)                                      ; true if the object can be moved\n    (toggleable ?o - object)                                  ; true if the object can be turned on/off\n    (isOn ?o - object)                                        ; true if the object is on\n    (isToggled ?o - object)                                   ; true if the object has been toggled\n    (sliceable ?o - object)                                   ; true if the object can be sliced\n    (isSliced ?o - object)                                    ; true if the object is sliced\n )\n\n  (:functions\n    (distance ?from ?to)\n    (total-cost) - number\n   )\n\n;; All actions are specified such that the final arguments are the ones used\n;; for performing actions in Unity.\n\n\n(:action look\n    :parameters (?a - agent ?l - location)\n    :precondition\n        (and\n            (atLocation ?a ?l)\n        )\n    :effect\n        (and\n            (checked ?l)\n        )\n)\n\n(:action inventory\n    :parameters (?a - agent)\n    :precondition\n        ()\n    :effect\n        (and\n            (checked ?a)\n        )\n)\n\n(:action examineReceptacle\n    :parameters (?a - agent ?r - receptacle)\n    :precondition\n        (and\n            (exists (?l - location)\n                (and\n                    (atLocation ?a ?l)\n                    (receptacleAtLocation ?r ?l)\n                )\n            )\n        )\n    :effect\n        (and\n            (checked ?r)\n        )\n)\n\n(:action examineObject\n    :parameters (?a - agent ?o - object)\n    :precondition\n        (or\n            ;(exists (?l - location)\n            ;    (and\n            ;        (atLocation ?a ?l)\n            ;        (objectAtLocation ?o ?l)\n            ;    )\n            ;)\n            (exists (?l - location, ?r - receptacle)\n                (and\n                    (atLocation ?a ?l)\n                    (receptacleAtLocation ?r ?l)\n                    ; (objectAtLocation ?o ?l)\n                    (inReceptacle ?o ?r)\n                    (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n              )\n            )\n            (holds ?a ?o)\n        )\n    :effect\n        (and\n            (checked ?o)\n        )\n)\n\n;; agent goes to receptacle\n (:action GotoLocation\n    :parameters (?a - agent ?lStart - location ?lEnd - location ?r - receptacle)\n    :precondition (and\n                    (atLocation ?a ?lStart)\n                    (receptacleAtLocation ?r ?lEnd)\n                    ;(exists (?r - receptacle) (receptacleAtLocation ?r ?lEnd))\n                  )\n    :effect (and\n                (not (atLocation ?a ?lStart))\n                (atLocation ?a ?lEnd)\n                ; (forall (?r - receptacle)\n                ;     (when (and (receptacleAtLocation ?r ?lEnd)\n                ;                (or (not (openable ?r)) (opened ?r)))\n                ;         (checked ?r)\n                ;     )\n                ; )\n                ; (increase (total-cost) (distance ?lStart ?lEnd))\n                (increase (total-cost) 1)\n            )\n )\n\n;; agent opens receptacle\n (:action OpenObject\n    :parameters (?a - agent ?l - location ?r - receptacle)\n    :precondition (and\n            (openable ?r)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (not (opened ?r))\n            )\n    :effect (and\n                (opened ?r)\n                (checked ?r)\n                (increase (total-cost) 1)\n            )\n )\n;; agent closes receptacle\n (:action CloseObject\n    :parameters (?a - agent ?l - location ?r - receptacle)\n    :precondition (and\n            (openable ?r)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (opened ?r)\n            )\n    :effect (and\n                (not (opened ?r))\n                (increase (total-cost) 1)\n            )\n\n )\n\n ;; agent picks up object from a receptacle\n (:action PickupObject\n    :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n    :precondition\n        (and\n            (pickupable ?o)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            ; (objectAtLocation ?o ?l)\n            (inReceptacle ?o ?r)\n            (not (holdsAny ?a))  ; agent's hands are empty.\n            ;(not (holdsAnyReceptacleObject ?a))\n            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n            ;(not (isReceptacleObject ?o))\n        )\n    :effect\n        (and\n            (not (inReceptacle ?o ?r))\n            (holds ?a ?o)\n            (holdsAny ?a)\n            (not (objectAtLocation ?o ?l))\n            ;(not (full ?r))\n            (increase (total-cost) 1)\n        )\n )\n\n\n; ;; agent picks up object from a receptacle\n; (:action PickupObjectFromReceptacleObject\n;    :parameters (?a - agent ?l - location ?o - object ?outerR - object ?r - receptacle)\n;    :precondition\n;        (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (inReceptacle ?o ?r)\n;            (pickupable ?o)\n;            (not (holdsAny ?a))  ; agent's hands are empty.\n;            (not (holdsAnyReceptacleObject ?a))\n;            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n;            (not (isReceptacleObject ?o))\n;            (isReceptacleObject ?outerR)\n;            (inReceptacleObject ?o ?outerR)\n;        )\n;    :effect\n;        (and\n;            (not (inReceptacle ?o ?r))\n;            (holds ?a ?o)\n;            (holdsAny ?a)\n;            (not (objectAtLocation ?o ?l))\n;            (not (full ?r))\n;            (increase (total-cost) 1)\n;\n;            (not (inReceptacleObject ?o ?outerR))\n;            (not (isReceptacleObjectFull ?outerR))\n;        )\n; )\n;\n; ;; agent picks up object from a receptacle\n; (:action PickupEmptyReceptacleObject\n;    :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n;    :precondition\n;        (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            ; (objectAtLocation ?o ?l)\n;            (inReceptacle ?o ?r)\n;            (pickupable ?o)\n;            (not (holdsAny ?a))  ; agent's hands are empty.\n;            (not (holdsAnyReceptacleObject ?a))\n;            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n;            (isReceptacleObject ?o)\n;            (not (isReceptacleObjectFull ?o))\n;        )\n;    :effect\n;        (and\n;            (not (inReceptacle ?o ?r))\n;            (holds ?a ?o)\n;            (holdsAny ?a)\n;            (not (objectAtLocation ?o ?l))\n;            (not (full ?r))\n;            (increase (total-cost) 1)\n;            (holdsAnyReceptacleObject ?a)\n;        )\n; )\n;\n; ;; agent picks up object from a receptacle\n; (:action PickupFullReceptacleObject\n;    :parameters (?a - agent ?l - location ?o - object ?outerR - object ?r - receptacle)\n;    :precondition\n;        (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (inReceptacle ?outerR ?r)\n;            (pickupable ?outerR)\n;            (not (holdsAny ?a))  ; agent's hands are empty.\n;            (not (holdsAnyReceptacleObject ?a))\n;            (or (not (openable ?r)) (opened ?r))  ; receptacle is opened if it is openable.\n;            (not (isReceptacleObject ?o))\n;            (isReceptacleObject ?outerR)\n;            (inReceptacleObject ?o ?outerR)\n;        )\n;    :effect\n;        (and\n;            (not (inReceptacle ?o ?r))\n;            (not (inReceptacle ?outerR ?r))\n;            (holds ?a ?outerR)\n;            (holdsAny ?a)\n;            (not (objectAtLocation ?o ?l))\n;            (not (objectAtLocation ?outerR ?l))\n;            (not (full ?r))\n;            (increase (total-cost) 1)\n;            (holdsAnyReceptacleObject ?a)\n;        )\n; )\n\n\n;; agent puts down an object\n (:action PutObject\n    :parameters (?a - agent ?l - location ?o - object ?r - receptacle ?ot - otype ?rt - rtype)\n    :precondition (and\n            (holds ?a ?o)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (or (not (openable ?r)) (opened ?r))    ; receptacle is opened if it is openable\n            ;(not (full ?r))\n            (objectType ?o ?ot)\n            (receptacleType ?r ?rt)\n            (canContain ?rt ?ot)\n            ;(not (holdsAnyReceptacleObject ?a))\n            )\n    :effect (and\n                (inReceptacle ?o ?r)\n                (objectAtLocation ?o ?l)\n                ;(full ?r)\n                (not (holds ?a ?o))\n                (not (holdsAny ?a))\n                (increase (total-cost) 1)\n            )\n )\n\n;;; agent puts down an object\n; (:action PutObjectInReceptacleObject\n;    :parameters (?a - agent ?l - location ?ot - otype ?o - object ?outerO - object ?outerR - receptacle)\n;    :precondition (and\n;            (atLocation ?a ?l)\n;            (objectAtLocation ?outerO ?l)\n;            (isReceptacleObject ?outerO)\n;            (not (isReceptacleObject ?o))\n;            (objectType ?o ?ot)\n;            (holds ?a ?o)\n;            (not (holdsAnyReceptacleObject ?a))\n;            (inReceptacle ?outerO ?outerR)\n;            (not (isReceptacleObjectFull ?outerO))\n;            )\n;    :effect (and\n;                (inReceptacleObject ?o ?outerO)\n;                (inReceptacle ?o ?outerR)\n;                (not (holds ?a ?o))\n;                (not (holdsAny ?a))\n;                (objectAtLocation ?o ?l)\n;                (isReceptacleObjectFull ?outerO)\n;                (increase (total-cost) 1)\n;            )\n; )\n;\n;;; agent puts down an object\n; (:action PutEmptyReceptacleObjectinReceptacle\n;    :parameters (?a - agent ?l - location ?ot - otype ?o - object ?r - receptacle)\n;    :precondition (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (or (not (openable ?r)) (opened ?r))    ; receptacle is opened if it is openable\n;            (not (full ?r))\n;            (objectType ?o ?ot)\n;            (holds ?a ?o)\n;            (holdsAnyReceptacleObject ?a)\n;            (isReceptacleObject ?o)\n;            (not (isReceptacleObjectFull ?o))\n;            )\n;    :effect (and\n;                (inReceptacle ?o ?r)\n;                (objectAtLocation ?o ?l)\n;                (full ?r)\n;                (not (holds ?a ?o))\n;                (not (holdsAny ?a))\n;                (not (holdsAnyReceptacleObject ?a))\n;                (increase (total-cost) 1)\n;            )\n; )\n;\n;;; agent puts down a receptacle object in a receptacle\n; (:action PutFullReceptacleObjectInReceptacle\n;    :parameters (?a - agent ?l - location ?ot - otype ?innerO - object ?outerO - object ?r - receptacle) ; ?rt - rtype)\n;    :precondition (and\n;            (atLocation ?a ?l)\n;            (receptacleAtLocation ?r ?l)\n;            (objectType ?outerO ?ot)\n;            (holds ?a ?outerO)\n;            (holdsAnyReceptacleObject ?a)\n;            (isReceptacleObject ?outerO)\n;            (isReceptacleObjectFull ?outerO)\n;            (inReceptacleObject ?innerO ?outerO)\n;            )\n;    :effect (and\n;                (not (holdsAny ?a))\n;                (not (holdsAnyReceptacleObject ?a))\n;                (objectAtLocation ?outerO ?l)\n;                (objectAtLocation ?innerO ?l)\n;                (inReceptacle ?outerO ?r)\n;                (inReceptacle ?innerO ?r)\n;                (not (holds ?a ?outerO))\n;                (increase (total-cost) 1)\n;            )\n; )\n\n;; agent cleans some object\n (:action CleanObject\n    :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n    :precondition (and\n            (cleanable ?o)\n            (or\n                (receptacleType ?r SinkType)\n                (receptacleType ?r SinkBasinType)\n            )\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (holds ?a ?o)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isClean ?o)\n            )\n )\n\n\n;; agent heats-up some object\n (:action HeatObject\n    :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n    :precondition (and\n            (heatable ?o)\n            (or\n                (receptacleType ?r MicrowaveType)\n            )\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (holds ?a ?o)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isHot ?o)\n                (not (isCool ?o))\n            )\n )\n\n;; agent cools some object\n (:action CoolObject\n    :parameters (?a - agent ?l - location ?r - receptacle ?o - object)\n    :precondition (and\n            (coolable ?o)\n            (or\n                (receptacleType ?r FridgeType)\n            )\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (holds ?a ?o)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isCool ?o)\n                (not (isHot ?o))\n            )\n )\n\n\n;; agent toggle object\n (:action ToggleObject\n    :parameters (?a - agent ?l - location ?o - object ?r - receptacle)\n    :precondition (and\n            (toggleable ?o)\n            (atLocation ?a ?l)\n            (receptacleAtLocation ?r ?l)\n            (inReceptacle ?o ?r)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (when (isOn ?o)\n                    (not (isOn ?o)))\n                (when (not (isOn ?o))\n                    (isOn ?o))\n                (isToggled ?o)\n            )\n )\n\n\n;; agent slices some object with a knife\n (:action SliceObject\n    :parameters (?a - agent ?l - location ?co - object ?ko - object)\n    :precondition\n            (and\n                (sliceable ?co)\n                (or\n                    (objectType ?ko KnifeType)\n                    (objectType ?ko ButterKnifeType)\n                )\n                (atLocation ?a ?l)\n                (objectAtLocation ?co ?l)\n                (holds ?a ?ko)\n            )\n    :effect (and\n                (increase (total-cost) 5)\n                (isSliced ?co)\n            )\n )\n\n\n(:action help\n    :parameters (?a - agent)\n    :precondition\n        ()\n    :effect\n        (and\n            (checked ?a)\n        )\n)\n)\n", "grammar": "grammar :: \"\"\"\n    {\n        \"intro\": [\n            {\n                \"rhs\": \"-= Welcome to TextWorld, ALFRED! =-\\n\\n#look.feedback#\\n\\n#task#\"\n            }\n        ],\n\n        \"notImplemented\": [\n            {\n                \"rhs\": \"TODO\"\n            }\n        ],\n\n        \"task\": [\n            {\n                \"rhs\": \"Your task is to: heat some cup and put it in countertop.\"\n            }\n        ],\n\n        \"GotoLocation.feedback\": [\n            {\n                \"rhs\": \"You arrive at {r.name}. #examineReceptacle.feedback#\"\n            }\n        ],\n\n        \"OpenObject.feedback\": [\n            {\n                \"rhs\": \"You open the {r.name}. #examineReceptacle.feedback#\"\n            }\n        ],\n\n        \"CloseObject.feedback\": [\n            {\n                \"rhs\": \"You close the {r.name}.\"\n            }\n        ],\n\n        \"PickupObject.feedback\": [\n            {\n                \"rhs\": \"You pick up the {o.name} from the {r.name}.\"\n            }\n        ],\n\n        \"PickupObjectFromReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PickupObjectFromReceptacleObject: You pick up the {o.name}.\"\n            }\n        ],\n\n        \"PickupEmptyReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PickupEmptyReceptacleObject: You pick up the {o.name}.\"\n            }\n        ],\n\n        \"PickupFullReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PickupFullReceptacleObject: You pick up the {outerr.name}.\"\n            }\n        ],\n\n        \"PutObject.feedback\": [\n            {\n                \"rhs\": \"You move the {o.name} to the {r.name}.\"\n            }\n        ],\n\n        \"PutObjectInReceptacleObject.feedback\": [\n            {\n                \"rhs\": \"PutObjectInReceptacleObject: You put the {o.name} in the {outero.name}.\"\n            }\n        ],\n\n        \"PutEmptyReceptacleObjectinReceptacle.feedback\": [\n            {\n                \"rhs\": \"PutEmptyReceptacleObjectinReceptacle: You put the {o.name} in the {r.name}.\"\n            }\n        ],\n\n        \"PutFullReceptacleObjectInReceptacle.feedback\": [\n            {\n                \"rhs\": \"PutFullReceptacleObjectInReceptacle: You put the {outero.name} in the {r.name}.\"\n            }\n        ],\n\n        \"inventory.feedback\": [\n            {\n                \"condition\": \"holdsany(a:agent)\",\n                \"rhs\": \"You are carrying: [{o.indefinite + ' ' + o.name | holds(a:agent, o:object)}].\"\n            },\n            {\n                \"rhs\": \"You are not carrying anything.\"\n            }\n        ],\n\n        \"examineReceptacle.feedback\": [\n            {\n                \"condition\": \"openable(r:receptacle) & opened(r:receptacle)\",\n                \"rhs\": \"The {r.name} is open. In it, you see [{o.indefinite + ' ' + o.name | inreceptacle(o:object, r:receptacle)}].\"\n            },\n            {\n                \"condition\": \"openable(r:receptacle)\",\n                \"rhs\": \"The {r.name} is closed.\"\n            },\n            {\n                \"rhs\": \"On the {r.name}, you see [{o.indefinite + ' ' + o.name | inreceptacle(o:object, r:receptacle)}].\"\n            }\n        ],\n\n        \"examineObject.feedback\": [\n            {\n                \"condition\": \"isreceptacleobject(o:object)\",\n                \"rhs\": \"This is a normal {o.name}. In it, you see [{o2.indefinite + ' ' + o2.name | inreceptacleobject(o2:object, o:object)}].\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & ishot(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a hot and clean sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & iscool(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a cool and clean sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a clean sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"ishot(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a hot sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"iscool(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a cool sliced {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & ishot(o:object)\",\n                \"rhs\": \"This is a hot and clean {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object) & iscool(o:object)\",\n                \"rhs\": \"This is a cool and clean {o.name}.\"\n            },\n            {\n                \"condition\": \"ishot(o:object)\",\n                \"rhs\": \"This is a hot {o.name}.\"\n            },\n            {\n                \"condition\": \"isclean(o:object)\",\n                \"rhs\": \"This is a clean {o.name}.\"\n            },\n            {\n                \"condition\": \"iscool(o:object)\",\n                \"rhs\": \"This is a cold {o.name}.\"\n            },\n            {\n                \"condition\": \"toggleable(o:object) & istoggled(o:object)\",\n                \"rhs\": \"This {o.name} is on.\"\n            },\n            {\n                \"condition\": \"toggleable(o:object) & not_istoggled(o:object)\",\n                \"rhs\": \"This {o.name} is off.\"\n            },\n            {\n                \"condition\": \"sliceable(o:object) & issliced(o:object)\",\n                \"rhs\": \"This is a sliced {o.name}.\"\n            },\n            {\n                \"rhs\": \"There's nothing special about {o.name}.\"\n            }\n        ]\n    }\n\"\"\";\n\naction GotoLocation {\n    template :: \"go to [{r.name | receptacleatlocation(r:receptacle, lend:location)}]\";\n    feedback :: \"#GotoLocation.feedback#\";\n}\n\naction OpenObject {\n    template :: \"open {r}\";\n    feedback :: \"#OpenObject.feedback#\";\n}\n\naction CloseObject {\n    template :: \"close {r}\";\n    feedback :: \"#CloseObject.feedback#\";\n}\n\naction PickupObject {\n    template :: \"take {o} from {r}\";\n    feedback :: \"#PickupObject.feedback#\";\n}\n\naction PickupObjectFromReceptacleObject {\n    template :: \"take {o} from {r}\";\n    feedback :: \"#PickupObjectFromReceptacleObject.feedback#\";\n}\n\naction PickupEmptyReceptacleObject {\n    template :: \"take {o} from {r}\";\n    feedback :: \"#PickupEmptyReceptacleObject.feedback#\";\n}\n\naction PickupFullReceptacleObject {\n    template :: \"take {outerr} from {r}\";\n    feedback :: \"#PickupFullReceptacleObject.feedback#\";\n}\n\naction PutObject {\n    template :: \"move {o} to {r}\";\n    feedback :: \"#PutObject.feedback#\";\n}\n\naction PutObjectInReceptacleObject {\n    template :: \"put {o} into {outero}\";\n    feedback :: \"#PutObjectInReceptacleObject.feedback#\";\n}\n\naction PutEmptyReceptacleObjectinReceptacle {\n    template :: \"move {o} to {r}\";\n    feedback :: \"#PutEmptyReceptacleObjectinReceptacle.feedback#\";\n}\n\naction PutFullReceptacleObjectInReceptacle {\n    template :: \"put {outero} in {r}\";\n    feedback :: \"#PutFullReceptacleObjectInReceptacle.feedback#\";\n}\n\naction inventory {\n    template :: \"inventory\";\n    feedback :: \"#inventory.feedback#\";\n}\n\naction examineReceptacle {\n    template :: \"examine {r}\";\n    feedback :: \"#examineReceptacle.feedback#\";\n}\n\naction examineObject {\n    template :: \"examine {o}\";\n    feedback :: \"#examineObject.feedback#\";\n}\n\naction ToggleObject {\n    template :: \"use {o}\";\n    feedback :: \"#toggleObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"toggleObject.feedback\": [\n                {\n                    \"condition\": \"toggleable(o:object) & istoggled(o:object)\",\n                    \"rhs\": \"You turn on the {o.name}.\"\n                },\n                {\n                    \"condition\": \"toggleable(o:object)\",\n                    \"rhs\": \"You turn off the {o.name}.\"\n                },\n                {\n                    \"rhs\": \"You don't see any switch on the {o.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction HeatObject {\n    template :: \"heat {o} with {r}\";\n    feedback :: \"#heatObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"heatObject.feedback\": [\n                {\n                    \"rhs\": \"You heat the {o.name} using the {r.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction CleanObject {\n    template :: \"clean {o} with {r}\";\n    feedback :: \"#cleanObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"cleanObject.feedback\": [\n                {\n                    \"rhs\": \"You clean the {o.name} using the {r.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction CoolObject {\n    template :: \"cool {o} with {r}\";\n    feedback :: \"#coolObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"coolObject.feedback\": [\n                {\n                    \"rhs\": \"You cool the {o.name} using the {r.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction SliceObject {\n    template :: \"slice {co} with {ko}\";\n    feedback :: \"#sliceObject.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"sliceObject.feedback\": [\n                {\n                    \"rhs\": \"You sliced the {co.name} with the {ko.name}.\"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction look {\n    template :: \"look\";\n    feedback :: \"#look.feedback#\";\n\n    grammar :: \"\"\"\n        {\n            \"look.feedback\": [\n                {\n                   \"condition\": \"atlocation(a:agent, l:location) & receptacleatlocation(r:receptacle, l:location)\",\n                   \"rhs\": \"#look-variations#. Next to it, you see #list_objects_on_the_floor#.\"\n                },\n                {\n                    \"rhs\": \"You are in the middle of a room. Looking quickly around you, you see #list_appliances#.\"\n                }\n            ],\n\n            \"look-variations\": [\n                {\n                    \"rhs\": \"You are facing the [{r.name | atlocation(a:agent, l:location) & receptacleatlocation(r:receptacle, l:location)}]\"\n                }\n            ],\n\n            \"list_objects_on_the_floor\": [\n                {\n                    \"condition\": \"atlocation(a:agent, l:location) & objectatlocation(o:object, l:location) & receptacleatlocation(r:receptacle, l:location) & not_inreceptacle(o:object, r:receptacle)\",\n                    \"rhs\": \"[{#overview(o)# | atlocation(a:agent, l:location) & objectatlocation(o:object, l:location) & receptacleatlocation(r:receptacle, l:location) & not_inreceptacle(o:object, r:receptacle)}]\"\n                },\n                {\n                    \"rhs\": \"nothing\"\n                }\n            ],\n\n            \"list_appliances\": [\n                {\n                    \"condition\": \"receptacleatlocation(r:receptacle, l:location)\",\n                    \"rhs\": \"[{#overview(r)# | receptacleatlocation(r:receptacle, l:location)}]\"\n                },\n                {\n                    \"rhs\": \"nothing\"\n                }\n            ],\n\n            \"overview(o, r)\": [\n                {\n                    \"rhs\": \"{o.indefinite} {o.name} (in/on the {r.name})}\"\n                }\n            ],\n\n            \"overview(o)\": [\n                {\n                    \"rhs\": \"{o.indefinite} {o.name}\"\n                }\n            ],\n            \"overview(r)\": [\n                {\n                    \"rhs\": \"{r.indefinite} {r.name or r.id}\"\n                }\n            ],\n            \"overview_with_state(r)\": [\n                {\n                    \"rhs\": \"{r.indefinite} {r.name or r.id}#overview_state(r)#\"\n                }\n            ],\n            \"overview_state(r)\": [\n                {\n                    \"condition\": \"openable(r:receptacle) & opened(r:receptacle)\",\n                    \"rhs\": \" (it is open)\"\n                },\n                {\n                    \"condition\": \"openable(r:receptacle)\",\n                    \"rhs\": \" (it is closed)\"\n                },\n                {\n                    \"rhs\": \"\"\n                }\n            ],\n\n            \"list_empty\": [\n                {\n                    \"rhs\": \"nothing\"\n                }\n            ],\n            \"list_separator\": [\n                {\n                    \"rhs\": \", \"\n                }\n            ],\n            \"list_last_separator\": [\n                {\n                    \"rhs\": \", and \"\n                }\n            ]\n        }\n    \"\"\";\n}\n\naction help {\n    template :: \"help\";\n    feedback :: \"\nAvailable commands:\n  look:                             look around your current location\n  inventory:                        check your current inventory\n  go to (receptacle):               move to a receptacle\n  open (receptacle):                open a receptacle\n  close (receptacle):               close a receptacle\n  take (object) from (receptacle):  take an object from a receptacle\n  move (object) to (receptacle):  place an object in or on a receptacle\n  examine (something):              examine a receptacle or an object\n  use (object):                     use an object\n  heat (object) with (receptacle):  heat an object using a receptacle\n  clean (object) with (receptacle): clean an object using a receptacle\n  cool (object) with (receptacle):  cool an object using a receptacle\n  slice (object) with (object):     slice an object using a sharp object\n\";\n}\n", "pddl_problem": "\n(define (problem plan_trial_T20190907_013610_766116)\n(:domain alfred)\n(:objects\nagent1 - agent\n<PERSON><PERSON><PERSON><PERSON> - object\n        HousePlant - object\n        Candle - object\n        SprayBottle - object\n        Bowl - object\n        Window - object\n        CD - object\n        Egg - object\n        Glassbottle - object\n        Sink - object\n        Pillow - object\n        Spoon - object\n        SoapBottle - object\n        TeddyBear - object\n        ButterKnife - object\n        Cup - object\n        Plate - object\n        RemoteControl - object\n        Tomato - object\n        Statue - object\n        HandTowel - object\n        Knife - object\n        StoveKnob - object\n        LightSwitch - object\n        Pen - object\n        Painting - object\n        DishSponge - object\n        Vase - object\n        Mug - object\n        ToiletPaper - object\n        Box - object\n        Mirror - object\n        Ladle - object\n        Fork - object\n        Blinds - object\n        Footstool - object\n        KeyChain - object\n        Cloth - object\n        Laptop - object\n        TissueBox - object\n        PepperShaker - object\n        FloorLamp - object\n        WateringCan - object\n        Apple - object\n        Pan - object\n        PaperTowel - object\n        PaperTowelRoll - object\n        Newspaper - object\n        Television - object\n        Chair - object\n        CellPhone - object\n        CreditCard - object\n        Lettuce - object\n        BasketBall - object\n        Potato - object\n        Curtains - object\n        Boots - object\n        Pencil - object\n        AlarmClock - object\n        ToiletPaperRoll - object\n        Spatula - object\n        Book - object\n        Bread - object\n        SoapBar - object\n        Watch - object\n        DeskLamp - object\n        Kettle - object\n        Pot - object\n        ScrubBrush - object\n        WineBottle - object\n        ShowerDoor - object\n        Bathtub - object\n        LaundryHamperLid - object\n        ShowerGlass - object\n        Poster - object\n        TennisRacket - object\n        BaseballBat - object\n        Towel - object\n        Plunger - object\nSaltShakerType - otype\n        HousePlantType - otype\n        CandleType - otype\n        SprayBottleType - otype\n        BowlType - otype\n        WindowType - otype\n        CDType - otype\n        EggType - otype\n        GlassbottleType - otype\n        SinkType - otype\n        PillowType - otype\n        SpoonType - otype\n        SoapBottleType - otype\n        TeddyBearType - otype\n        ButterKnifeType - otype\n        CupType - otype\n        PlateType - otype\n        RemoteControlType - otype\n        TomatoType - otype\n        StatueType - otype\n        HandTowelType - otype\n        KnifeType - otype\n        StoveKnobType - otype\n        LightSwitchType - otype\n        PenType - otype\n        PaintingType - otype\n        DishSpongeType - otype\n        VaseType - otype\n        MugType - otype\n        ToiletPaperType - otype\n        BoxType - otype\n        MirrorType - otype\n        LadleType - otype\n        ForkType - otype\n        BlindsType - otype\n        FootstoolType - otype\n        KeyChainType - otype\n        ClothType - otype\n        LaptopType - otype\n        TissueBoxType - otype\n        PepperShakerType - otype\n        FloorLampType - otype\n        WateringCanType - otype\n        AppleType - otype\n        PanType - otype\n        PaperTowelType - otype\n        PaperTowelRollType - otype\n        NewspaperType - otype\n        TelevisionType - otype\n        ChairType - otype\n        CellPhoneType - otype\n        CreditCardType - otype\n        LettuceType - otype\n        BasketBallType - otype\n        PotatoType - otype\n        CurtainsType - otype\n        BootsType - otype\n        PencilType - otype\n        AlarmClockType - otype\n        ToiletPaperRollType - otype\n        SpatulaType - otype\n        BookType - otype\n        BreadType - otype\n        SoapBarType - otype\n        WatchType - otype\n        DeskLampType - otype\n        KettleType - otype\n        PotType - otype\n        ScrubBrushType - otype\n        WineBottleType - otype\n        ShowerDoorType - otype\n        BathtubType - otype\n        LaundryHamperLidType - otype\n        ShowerGlassType - otype\n        PosterType - otype\n        TennisRacketType - otype\n        BaseballBatType - otype\n        TowelType - otype\n        PlungerType - otype\nSafeType - rtype\n        DrawerType - rtype\n        CoffeeMachineType - rtype\n        HandTowelHolderType - rtype\n        SinkBasinType - rtype\n        DresserType - rtype\n        LaundryHamperType - rtype\n        TVStandType - rtype\n        BathtubBasinType - rtype\n        CabinetType - rtype\n        FridgeType - rtype\n        DeskType - rtype\n        ToiletType - rtype\n        CartType - rtype\n        SideTableType - rtype\n        SofaType - rtype\n        CoffeeTableType - rtype\n        DiningTableType - rtype\n        CounterTopType - rtype\n        GarbageCanType - rtype\n        ArmChairType - rtype\n        ShelfType - rtype\n        MicrowaveType - rtype\n        ToasterType - rtype\n        BedType - rtype\n        PaintingHangerType - rtype\n        TowelHolderType - rtype\n        ToiletPaperHangerType - rtype\n        StoveBurnerType - rtype\n        OttomanType - rtype\n\n\n        Apple_bar__minus_03_dot_64_bar__plus_00_dot_12_bar__plus_06_dot_29 - object\n        Apple_bar__minus_03_dot_73_bar__plus_00_dot_98_bar__plus_05_dot_34 - object\n        Apple_bar__minus_03_dot_75_bar__plus_00_dot_12_bar__plus_06_dot_46 - object\n        Bowl_bar__minus_00_dot_26_bar__plus_00_dot_92_bar__plus_04_dot_62 - object\n        Bread_bar__minus_00_dot_30_bar__plus_01_dot_15_bar__plus_03_dot_60 - object\n        Bread_bar__minus_03_dot_86_bar__plus_01_dot_00_bar__plus_01_dot_80 - object\n        ButterKnife_bar__minus_04_dot_13_bar__plus_00_dot_76_bar__plus_02_dot_32 - object\n        ButterKnife_bar__minus_04_dot_18_bar__plus_00_dot_92_bar__plus_04_dot_90 - object\n        Cup_bar__minus_00_dot_35_bar__plus_01_dot_07_bar__plus_03_dot_97 - object\n        Cup_bar__minus_00_dot_57_bar__plus_00_dot_92_bar__plus_06_dot_26 - object\n        Cup_bar__minus_03_dot_81_bar__plus_01_dot_46_bar__plus_05_dot_51 - object\n        Curtains_bar__minus_04_dot_52_bar__plus_02_dot_25_bar__plus_03_dot_39 - object\n        DishSponge_bar__minus_00_dot_50_bar__plus_00_dot_75_bar__plus_06_dot_49 - object\n        DishSponge_bar__minus_02_dot_11_bar__plus_00_dot_92_bar__plus_01_dot_65 - object\n        Egg_bar__minus_03_dot_68_bar__plus_00_dot_10_bar__plus_06_dot_38 - object\n        Egg_bar__minus_04_dot_08_bar__plus_00_dot_83_bar__plus_03_dot_35 - object\n        Egg_bar__minus_04_dot_16_bar__plus_00_dot_83_bar__plus_03_dot_41 - object\n        Faucet_bar__minus_04_dot_44_bar__plus_00_dot_91_bar__plus_03_dot_29 - object\n        Fork_bar__minus_00_dot_26_bar__plus_00_dot_93_bar__plus_05_dot_60 - object\n        Fork_bar__minus_03_dot_73_bar__plus_00_dot_92_bar__plus_05_dot_49 - object\n        Fork_bar__minus_04_dot_52_bar__plus_00_dot_92_bar__plus_03_dot_20 - object\n        Knife_bar__minus_00_dot_26_bar__plus_00_dot_95_bar__plus_04_dot_94 - object\n        Knife_bar__minus_00_dot_41_bar__plus_00_dot_95_bar__plus_05_dot_60 - object\n        Knife_bar__minus_04_dot_26_bar__plus_00_dot_95_bar__plus_03_dot_86 - object\n        Lettuce_bar__minus_00_dot_18_bar__plus_01_dot_02_bar__plus_04_dot_94 - object\n        Lettuce_bar__minus_00_dot_30_bar__plus_01_dot_17_bar__plus_03_dot_23 - object\n        Lettuce_bar__minus_01_dot_78_bar__plus_01_dot_02_bar__plus_01_dot_72 - object\n        LightSwitch_bar__minus_01_dot_59_bar__plus_01_dot_17_bar__plus_01_dot_51 - object\n        Mug_bar__minus_00_dot_41_bar__plus_00_dot_96_bar__plus_06_dot_56 - object\n        Pan_bar__minus_02_dot_47_bar__plus_00_dot_93_bar__plus_01_dot_71 - object\n        Pan_bar__minus_02_dot_92_bar__plus_00_dot_93_bar__plus_01_dot_71 - object\n        Pan_bar__minus_04_dot_41_bar__plus_00_dot_92_bar__plus_05_dot_42 - object\n        PaperTowelRoll_bar__minus_03_dot_78_bar__plus_00_dot_17_bar__plus_06_dot_29 - object\n        Pencil_bar__minus_01_dot_78_bar__plus_00_dot_92_bar__plus_01_dot_80 - object\n        Pencil_bar__minus_03_dot_48_bar__plus_00_dot_75_bar__plus_05_dot_16 - object\n        PepperShaker_bar__minus_04_dot_31_bar__plus_00_dot_92_bar__plus_02_dot_30 - object\n        Plate_bar__minus_00_dot_30_bar__plus_01_dot_58_bar__plus_03_dot_47 - object\n        Plate_bar__minus_01_dot_94_bar__plus_00_dot_92_bar__plus_01_dot_95 - object\n        Potato_bar__minus_00_dot_22_bar__plus_01_dot_72_bar__plus_03_dot_67 - object\n        Potato_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__plus_01_dot_95 - object\n        Potato_bar__minus_04_dot_18_bar__plus_00_dot_96_bar__plus_03_dot_86 - object\n        Pot_bar__minus_00_dot_40_bar__plus_00_dot_91_bar__plus_05_dot_93 - object\n        SaltShaker_bar__minus_00_dot_28_bar__plus_02_dot_13_bar__plus_06_dot_60 - object\n        SaltShaker_bar__minus_03_dot_47_bar__plus_00_dot_74_bar__plus_01_dot_96 - object\n        Sink_bar__minus_04_dot_19_bar__plus_00_dot_92_bar__plus_03_dot_28 - object\n        SoapBottle_bar__minus_03_dot_86_bar__plus_00_dot_92_bar__plus_01_dot_57 - object\n        Spatula_bar__minus_01_dot_61_bar__plus_00_dot_94_bar__plus_01_dot_72 - object\n        Spoon_bar__minus_01_dot_86_bar__plus_00_dot_92_bar__plus_01_dot_65 - object\n        Spoon_bar__minus_03_dot_26_bar__plus_00_dot_92_bar__plus_05_dot_41 - object\n        Spoon_bar__minus_04_dot_11_bar__plus_00_dot_92_bar__plus_04_dot_38 - object\n        StoveKnob_bar__minus_02_dot_35_bar__plus_01_dot_10_bar__plus_01_dot_56 - object\n        StoveKnob_bar__minus_02_dot_51_bar__plus_01_dot_10_bar__plus_01_dot_56 - object\n        StoveKnob_bar__minus_02_dot_79_bar__plus_01_dot_10_bar__plus_01_dot_56 - object\n        StoveKnob_bar__minus_02_dot_97_bar__plus_01_dot_10_bar__plus_01_dot_56 - object\n        Tomato_bar__minus_03_dot_49_bar__plus_00_dot_98_bar__plus_05_dot_49 - object\n        Tomato_bar__minus_04_dot_03_bar__plus_00_dot_98_bar__plus_02_dot_03 - object\n        Tomato_bar__minus_04_dot_16_bar__plus_00_dot_85_bar__plus_03_dot_22 - object\n        Window_bar__minus_04_dot_87_bar__plus_01_dot_63_bar__plus_03_dot_31 - object\n        WineBottle_bar__minus_03_dot_34_bar__plus_00_dot_92_bar__plus_02_dot_03 - object\n        Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_03_dot_01 - receptacle\n        Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_04_dot_21 - receptacle\n        Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_04_dot_31 - receptacle\n        Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_05_dot_51 - receptacle\n        Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_05_dot_61 - receptacle\n        Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_06_dot_81 - receptacle\n        Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_04_dot_30 - receptacle\n        Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_05_dot_58 - receptacle\n        Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_05_dot_60 - receptacle\n        Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_06_dot_88 - receptacle\n        Cabinet_bar__minus_01_dot_54_bar__plus_00_dot_39_bar__plus_02_dot_09 - receptacle\n        Cabinet_bar__minus_01_dot_58_bar__plus_01_dot_93_bar__plus_01_dot_78 - receptacle\n        Cabinet_bar__minus_03_dot_04_bar__plus_00_dot_39_bar__plus_05_dot_03 - receptacle\n        Cabinet_bar__minus_03_dot_04_bar__plus_01_dot_94_bar__plus_05_dot_32 - receptacle\n        Cabinet_bar__minus_03_dot_19_bar__plus_00_dot_39_bar__plus_02_dot_09 - receptacle\n        Cabinet_bar__minus_03_dot_20_bar__plus_01_dot_93_bar__plus_01_dot_78 - receptacle\n        Cabinet_bar__minus_03_dot_64_bar__plus_00_dot_39_bar__plus_05_dot_03 - receptacle\n        Cabinet_bar__minus_03_dot_67_bar__plus_00_dot_39_bar__plus_05_dot_03 - receptacle\n        Cabinet_bar__minus_03_dot_92_bar__plus_00_dot_40_bar__plus_02_dot_09 - receptacle\n        Cabinet_bar__minus_03_dot_96_bar__plus_00_dot_39_bar__plus_02_dot_12 - receptacle\n        Cabinet_bar__minus_03_dot_96_bar__plus_00_dot_39_bar__plus_02_dot_88 - receptacle\n        Cabinet_bar__minus_03_dot_96_bar__plus_00_dot_39_bar__plus_04_dot_34 - receptacle\n        Cabinet_bar__minus_03_dot_97_bar__plus_00_dot_39_bar__plus_02_dot_89 - receptacle\n        Cabinet_bar__minus_03_dot_97_bar__plus_00_dot_39_bar__plus_03_dot_66 - receptacle\n        Cabinet_bar__minus_04_dot_11_bar__plus_01_dot_94_bar__plus_05_dot_32 - receptacle\n        Cabinet_bar__minus_04_dot_28_bar__plus_01_dot_93_bar__plus_02_dot_23 - receptacle\n        CoffeeMachine_bar__minus_00_dot_28_bar__plus_00_dot_91_bar__plus_06_dot_56 - receptacle\n        CounterTop_bar__minus_00_dot_35_bar__plus_00_dot_96_bar__plus_05_dot_60 - receptacle\n        CounterTop_bar__minus_01_dot_87_bar__plus_00_dot_96_bar__plus_01_dot_80 - receptacle\n        CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94 - receptacle\n        Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_04_dot_62 - receptacle\n        Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_05_dot_27 - receptacle\n        Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_05_dot_92 - receptacle\n        Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_06_dot_57 - receptacle\n        Drawer_bar__minus_01_dot_86_bar__plus_00_dot_78_bar__plus_01_dot_96 - receptacle\n        Drawer_bar__minus_03_dot_19_bar__plus_00_dot_78_bar__plus_05_dot_16 - receptacle\n        Drawer_bar__minus_03_dot_42_bar__plus_00_dot_78_bar__plus_01_dot_96 - receptacle\n        Drawer_bar__minus_03_dot_51_bar__plus_00_dot_78_bar__plus_05_dot_16 - receptacle\n        Drawer_bar__minus_03_dot_80_bar__plus_00_dot_78_bar__plus_01_dot_96 - receptacle\n        Drawer_bar__minus_03_dot_80_bar__plus_00_dot_78_bar__plus_05_dot_16 - receptacle\n        Drawer_bar__minus_04_dot_09_bar__plus_00_dot_78_bar__plus_04_dot_66 - receptacle\n        Drawer_bar__minus_04_dot_13_bar__plus_00_dot_80_bar__plus_02_dot_50 - receptacle\n        Fridge_bar__minus_00_dot_32_bar_00_dot_00_bar__plus_03_dot_60 - receptacle\n        GarbageCan_bar__minus_03_dot_71_bar_00_dot_00_bar__plus_06_dot_38 - receptacle\n        Microwave_bar__minus_02_dot_69_bar__plus_01_dot_43_bar__plus_01_dot_69 - receptacle\n        Sink_bar__minus_04_dot_19_bar__plus_00_dot_92_bar__plus_03_dot_28_bar_SinkBasin - receptacle\n        StoveBurner_bar__minus_02_dot_47_bar__plus_00_dot_92_bar__plus_01_dot_71 - receptacle\n        StoveBurner_bar__minus_02_dot_47_bar__plus_00_dot_92_bar__plus_02_dot_01 - receptacle\n        StoveBurner_bar__minus_02_dot_92_bar__plus_00_dot_92_bar__plus_01_dot_71 - receptacle\n        StoveBurner_bar__minus_02_dot_92_bar__plus_00_dot_92_bar__plus_02_dot_01 - receptacle\n        Toaster_bar__minus_03_dot_58_bar__plus_00_dot_91_bar__plus_01_dot_76 - receptacle\n        loc_bar__minus_4_bar_25_bar_1_bar__minus_30 - location\n        loc_bar__minus_13_bar_10_bar_2_bar__minus_15 - location\n        loc_bar__minus_6_bar_20_bar_1_bar_60 - location\n        loc_bar__minus_6_bar_17_bar_1_bar_60 - location\n        loc_bar__minus_4_bar_23_bar_0_bar_45 - location\n        loc_bar__minus_15_bar_14_bar_3_bar_60 - location\n        loc_bar__minus_5_bar_15_bar_1_bar_60 - location\n        loc_bar__minus_12_bar_12_bar_2_bar_60 - location\n        loc_bar__minus_12_bar_10_bar_2_bar_30 - location\n        loc_bar__minus_4_bar_21_bar_2_bar_60 - location\n        loc_bar__minus_2_bar_10_bar_0_bar__minus_30 - location\n        loc_bar__minus_15_bar_13_bar_3_bar_0 - location\n        loc_bar__minus_13_bar_11_bar_3_bar_45 - location\n        loc_bar__minus_12_bar_13_bar_3_bar_45 - location\n        loc_bar__minus_13_bar_16_bar_0_bar_45 - location\n        loc_bar__minus_15_bar_14_bar_3_bar__minus_30 - location\n        loc_bar__minus_14_bar_10_bar_3_bar__minus_15 - location\n        loc_bar__minus_15_bar_19_bar_0_bar__minus_30 - location\n        loc_bar__minus_4_bar_20_bar_1_bar_45 - location\n        loc_bar__minus_11_bar_10_bar_2_bar_30 - location\n        loc_bar__minus_14_bar_10_bar_2_bar_60 - location\n        loc_bar__minus_4_bar_21_bar_0_bar_60 - location\n        loc_bar__minus_10_bar_10_bar_2_bar_45 - location\n        loc_bar__minus_12_bar_13_bar_3_bar_60 - location\n        loc_bar__minus_8_bar_10_bar_2_bar_45 - location\n        loc_bar__minus_12_bar_10_bar_3_bar_60 - location\n        loc_bar__minus_10_bar_25_bar_3_bar_60 - location\n        loc_bar__minus_6_bar_25_bar_1_bar_60 - location\n        loc_bar__minus_13_bar_19_bar_0_bar__minus_30 - location\n        loc_bar__minus_13_bar_12_bar_2_bar_60 - location\n        loc_bar__minus_4_bar_6_bar_3_bar_45 - location\n        loc_bar__minus_12_bar_17_bar_0_bar_60 - location\n        loc_bar__minus_5_bar_9_bar_3_bar_60 - location\n        loc_bar__minus_5_bar_14_bar_1_bar_0 - location\n        loc_bar__minus_11_bar_10_bar_2_bar_15 - location\n        loc_bar__minus_4_bar_7_bar_3_bar__minus_30 - location\n        loc_bar__minus_12_bar_10_bar_2_bar_45 - location\n        loc_bar__minus_15_bar_19_bar_0_bar_60 - location\n        loc_bar__minus_11_bar_17_bar_0_bar_60 - location\n        loc_bar__minus_4_bar_25_bar_1_bar_45 - location\n        loc_bar__minus_5_bar_17_bar_1_bar__minus_30 - location\n        loc_bar__minus_4_bar_18_bar_1_bar__minus_30 - location\n        loc_bar__minus_10_bar_10_bar_2_bar_30 - location\n        loc_bar__minus_12_bar_12_bar_3_bar_45 - location\n        loc_bar__minus_15_bar_19_bar_3_bar_60 - location\n        loc_bar__minus_6_bar_22_bar_1_bar_60 - location\n        loc_bar__minus_15_bar_14_bar_0_bar_30 - location\n        loc_bar__minus_10_bar_10_bar_2_bar_60 - location\n        loc_bar__minus_4_bar_22_bar_1_bar__minus_30 - location\n        loc_bar__minus_12_bar_17_bar_3_bar_60 - location\n        loc_bar__minus_14_bar_19_bar_0_bar_60 - location\n        loc_bar__minus_4_bar_24_bar_2_bar_45 - location\n        loc_bar__minus_14_bar_10_bar_2_bar_45 - location\n        loc_bar__minus_9_bar_10_bar_2_bar_30 - location\n        loc_bar__minus_15_bar_13_bar_3_bar_60 - location\n        loc_bar__minus_9_bar_12_bar_2_bar_60 - location\n        loc_bar__minus_15_bar_19_bar_1_bar_60 - location\n        loc_bar__minus_12_bar_10_bar_2_bar_60 - location\n        loc_bar__minus_4_bar_7_bar_0_bar_30 - location\n        )\n    \n\n(:init\n\n\n        (receptacleType Cabinet_bar__minus_03_dot_92_bar__plus_00_dot_40_bar__plus_02_dot_09 CabinetType)\n        (receptacleType Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_05_dot_92 DrawerType)\n        (receptacleType Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_06_dot_88 CabinetType)\n        (receptacleType CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94 CounterTopType)\n        (receptacleType Microwave_bar__minus_02_dot_69_bar__plus_01_dot_43_bar__plus_01_dot_69 MicrowaveType)\n        (receptacleType Sink_bar__minus_04_dot_19_bar__plus_00_dot_92_bar__plus_03_dot_28_bar_SinkBasin SinkBasinType)\n        (receptacleType Cabinet_bar__minus_01_dot_58_bar__plus_01_dot_93_bar__plus_01_dot_78 CabinetType)\n        (receptacleType StoveBurner_bar__minus_02_dot_47_bar__plus_00_dot_92_bar__plus_02_dot_01 StoveBurnerType)\n        (receptacleType Cabinet_bar__minus_03_dot_64_bar__plus_00_dot_39_bar__plus_05_dot_03 CabinetType)\n        (receptacleType Cabinet_bar__minus_03_dot_97_bar__plus_00_dot_39_bar__plus_02_dot_89 CabinetType)\n        (receptacleType Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_06_dot_81 CabinetType)\n        (receptacleType Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_05_dot_58 CabinetType)\n        (receptacleType StoveBurner_bar__minus_02_dot_92_bar__plus_00_dot_92_bar__plus_01_dot_71 StoveBurnerType)\n        (receptacleType Cabinet_bar__minus_03_dot_96_bar__plus_00_dot_39_bar__plus_02_dot_12 CabinetType)\n        (receptacleType Drawer_bar__minus_03_dot_80_bar__plus_00_dot_78_bar__plus_05_dot_16 DrawerType)\n        (receptacleType Fridge_bar__minus_00_dot_32_bar_00_dot_00_bar__plus_03_dot_60 FridgeType)\n        (receptacleType Cabinet_bar__minus_04_dot_11_bar__plus_01_dot_94_bar__plus_05_dot_32 CabinetType)\n        (receptacleType CoffeeMachine_bar__minus_00_dot_28_bar__plus_00_dot_91_bar__plus_06_dot_56 CoffeeMachineType)\n        (receptacleType Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_03_dot_01 CabinetType)\n        (receptacleType Drawer_bar__minus_03_dot_42_bar__plus_00_dot_78_bar__plus_01_dot_96 DrawerType)\n        (receptacleType StoveBurner_bar__minus_02_dot_47_bar__plus_00_dot_92_bar__plus_01_dot_71 StoveBurnerType)\n        (receptacleType StoveBurner_bar__minus_02_dot_92_bar__plus_00_dot_92_bar__plus_02_dot_01 StoveBurnerType)\n        (receptacleType Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_05_dot_27 DrawerType)\n        (receptacleType Cabinet_bar__minus_03_dot_97_bar__plus_00_dot_39_bar__plus_03_dot_66 CabinetType)\n        (receptacleType Toaster_bar__minus_03_dot_58_bar__plus_00_dot_91_bar__plus_01_dot_76 ToasterType)\n        (receptacleType Drawer_bar__minus_01_dot_86_bar__plus_00_dot_78_bar__plus_01_dot_96 DrawerType)\n        (receptacleType Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_05_dot_51 CabinetType)\n        (receptacleType Cabinet_bar__minus_03_dot_04_bar__plus_00_dot_39_bar__plus_05_dot_03 CabinetType)\n        (receptacleType Drawer_bar__minus_03_dot_80_bar__plus_00_dot_78_bar__plus_01_dot_96 DrawerType)\n        (receptacleType Cabinet_bar__minus_03_dot_96_bar__plus_00_dot_39_bar__plus_02_dot_88 CabinetType)\n        (receptacleType CounterTop_bar__minus_01_dot_87_bar__plus_00_dot_96_bar__plus_01_dot_80 CounterTopType)\n        (receptacleType Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_05_dot_61 CabinetType)\n        (receptacleType CounterTop_bar__minus_00_dot_35_bar__plus_00_dot_96_bar__plus_05_dot_60 CounterTopType)\n        (receptacleType Cabinet_bar__minus_01_dot_54_bar__plus_00_dot_39_bar__plus_02_dot_09 CabinetType)\n        (receptacleType Cabinet_bar__minus_03_dot_20_bar__plus_01_dot_93_bar__plus_01_dot_78 CabinetType)\n        (receptacleType Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_05_dot_60 CabinetType)\n        (receptacleType Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_04_dot_31 CabinetType)\n        (receptacleType Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_06_dot_57 DrawerType)\n        (receptacleType Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_04_dot_62 DrawerType)\n        (receptacleType Drawer_bar__minus_04_dot_13_bar__plus_00_dot_80_bar__plus_02_dot_50 DrawerType)\n        (receptacleType Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_04_dot_30 CabinetType)\n        (receptacleType Cabinet_bar__minus_04_dot_28_bar__plus_01_dot_93_bar__plus_02_dot_23 CabinetType)\n        (receptacleType GarbageCan_bar__minus_03_dot_71_bar_00_dot_00_bar__plus_06_dot_38 GarbageCanType)\n        (receptacleType Drawer_bar__minus_04_dot_09_bar__plus_00_dot_78_bar__plus_04_dot_66 DrawerType)\n        (receptacleType Drawer_bar__minus_03_dot_51_bar__plus_00_dot_78_bar__plus_05_dot_16 DrawerType)\n        (receptacleType Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_04_dot_21 CabinetType)\n        (receptacleType Cabinet_bar__minus_03_dot_19_bar__plus_00_dot_39_bar__plus_02_dot_09 CabinetType)\n        (receptacleType Cabinet_bar__minus_03_dot_04_bar__plus_01_dot_94_bar__plus_05_dot_32 CabinetType)\n        (receptacleType Drawer_bar__minus_03_dot_19_bar__plus_00_dot_78_bar__plus_05_dot_16 DrawerType)\n        (receptacleType Cabinet_bar__minus_03_dot_67_bar__plus_00_dot_39_bar__plus_05_dot_03 CabinetType)\n        (receptacleType Cabinet_bar__minus_03_dot_96_bar__plus_00_dot_39_bar__plus_04_dot_34 CabinetType)\n        (objectType Bowl_bar__minus_00_dot_26_bar__plus_00_dot_92_bar__plus_04_dot_62 BowlType)\n        (objectType Potato_bar__minus_00_dot_22_bar__plus_01_dot_72_bar__plus_03_dot_67 PotatoType)\n        (objectType Apple_bar__minus_03_dot_73_bar__plus_00_dot_98_bar__plus_05_dot_34 AppleType)\n        (objectType Spoon_bar__minus_04_dot_11_bar__plus_00_dot_92_bar__plus_04_dot_38 SpoonType)\n        (objectType Window_bar__minus_04_dot_87_bar__plus_01_dot_63_bar__plus_03_dot_31 WindowType)\n        (objectType Tomato_bar__minus_04_dot_03_bar__plus_00_dot_98_bar__plus_02_dot_03 TomatoType)\n        (objectType StoveKnob_bar__minus_02_dot_51_bar__plus_01_dot_10_bar__plus_01_dot_56 StoveKnobType)\n        (objectType Lettuce_bar__minus_01_dot_78_bar__plus_01_dot_02_bar__plus_01_dot_72 LettuceType)\n        (objectType Potato_bar__minus_04_dot_18_bar__plus_00_dot_96_bar__plus_03_dot_86 PotatoType)\n        (objectType Cup_bar__minus_03_dot_81_bar__plus_01_dot_46_bar__plus_05_dot_51 CupType)\n        (objectType DishSponge_bar__minus_02_dot_11_bar__plus_00_dot_92_bar__plus_01_dot_65 DishSpongeType)\n        (objectType Pencil_bar__minus_03_dot_48_bar__plus_00_dot_75_bar__plus_05_dot_16 PencilType)\n        (objectType Tomato_bar__minus_03_dot_49_bar__plus_00_dot_98_bar__plus_05_dot_49 TomatoType)\n        (objectType Egg_bar__minus_03_dot_68_bar__plus_00_dot_10_bar__plus_06_dot_38 EggType)\n        (objectType Lettuce_bar__minus_00_dot_18_bar__plus_01_dot_02_bar__plus_04_dot_94 LettuceType)\n        (objectType Tomato_bar__minus_04_dot_16_bar__plus_00_dot_85_bar__plus_03_dot_22 TomatoType)\n        (objectType Spatula_bar__minus_01_dot_61_bar__plus_00_dot_94_bar__plus_01_dot_72 SpatulaType)\n        (objectType Plate_bar__minus_01_dot_94_bar__plus_00_dot_92_bar__plus_01_dot_95 PlateType)\n        (objectType Fork_bar__minus_03_dot_73_bar__plus_00_dot_92_bar__plus_05_dot_49 ForkType)\n        (objectType StoveKnob_bar__minus_02_dot_79_bar__plus_01_dot_10_bar__plus_01_dot_56 StoveKnobType)\n        (objectType Cup_bar__minus_00_dot_57_bar__plus_00_dot_92_bar__plus_06_dot_26 CupType)\n        (objectType PaperTowelRoll_bar__minus_03_dot_78_bar__plus_00_dot_17_bar__plus_06_dot_29 PaperTowelRollType)\n        (objectType Fork_bar__minus_00_dot_26_bar__plus_00_dot_93_bar__plus_05_dot_60 ForkType)\n        (objectType ButterKnife_bar__minus_04_dot_13_bar__plus_00_dot_76_bar__plus_02_dot_32 ButterKnifeType)\n        (objectType Mug_bar__minus_00_dot_41_bar__plus_00_dot_96_bar__plus_06_dot_56 MugType)\n        (objectType Potato_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__plus_01_dot_95 PotatoType)\n        (objectType Egg_bar__minus_04_dot_16_bar__plus_00_dot_83_bar__plus_03_dot_41 EggType)\n        (objectType DishSponge_bar__minus_00_dot_50_bar__plus_00_dot_75_bar__plus_06_dot_49 DishSpongeType)\n        (objectType Pan_bar__minus_04_dot_41_bar__plus_00_dot_92_bar__plus_05_dot_42 PanType)\n        (objectType Bread_bar__minus_03_dot_86_bar__plus_01_dot_00_bar__plus_01_dot_80 BreadType)\n        (objectType PepperShaker_bar__minus_04_dot_31_bar__plus_00_dot_92_bar__plus_02_dot_30 PepperShakerType)\n        (objectType SaltShaker_bar__minus_00_dot_28_bar__plus_02_dot_13_bar__plus_06_dot_60 SaltShakerType)\n        (objectType Knife_bar__minus_00_dot_41_bar__plus_00_dot_95_bar__plus_05_dot_60 KnifeType)\n        (objectType Bread_bar__minus_00_dot_30_bar__plus_01_dot_15_bar__plus_03_dot_60 BreadType)\n        (objectType StoveKnob_bar__minus_02_dot_35_bar__plus_01_dot_10_bar__plus_01_dot_56 StoveKnobType)\n        (objectType Cup_bar__minus_00_dot_35_bar__plus_01_dot_07_bar__plus_03_dot_97 CupType)\n        (objectType Curtains_bar__minus_04_dot_52_bar__plus_02_dot_25_bar__plus_03_dot_39 CurtainsType)\n        (objectType SoapBottle_bar__minus_03_dot_86_bar__plus_00_dot_92_bar__plus_01_dot_57 SoapBottleType)\n        (objectType Pan_bar__minus_02_dot_47_bar__plus_00_dot_93_bar__plus_01_dot_71 PanType)\n        (objectType Fork_bar__minus_04_dot_52_bar__plus_00_dot_92_bar__plus_03_dot_20 ForkType)\n        (objectType Apple_bar__minus_03_dot_64_bar__plus_00_dot_12_bar__plus_06_dot_29 AppleType)\n        (objectType Spoon_bar__minus_01_dot_86_bar__plus_00_dot_92_bar__plus_01_dot_65 SpoonType)\n        (objectType Spoon_bar__minus_03_dot_26_bar__plus_00_dot_92_bar__plus_05_dot_41 SpoonType)\n        (objectType Plate_bar__minus_00_dot_30_bar__plus_01_dot_58_bar__plus_03_dot_47 PlateType)\n        (objectType Apple_bar__minus_03_dot_75_bar__plus_00_dot_12_bar__plus_06_dot_46 AppleType)\n        (objectType Lettuce_bar__minus_00_dot_30_bar__plus_01_dot_17_bar__plus_03_dot_23 LettuceType)\n        (objectType Egg_bar__minus_04_dot_08_bar__plus_00_dot_83_bar__plus_03_dot_35 EggType)\n        (objectType Sink_bar__minus_04_dot_19_bar__plus_00_dot_92_bar__plus_03_dot_28 SinkType)\n        (objectType Pencil_bar__minus_01_dot_78_bar__plus_00_dot_92_bar__plus_01_dot_80 PencilType)\n        (objectType StoveKnob_bar__minus_02_dot_97_bar__plus_01_dot_10_bar__plus_01_dot_56 StoveKnobType)\n        (objectType Pan_bar__minus_02_dot_92_bar__plus_00_dot_93_bar__plus_01_dot_71 PanType)\n        (objectType SaltShaker_bar__minus_03_dot_47_bar__plus_00_dot_74_bar__plus_01_dot_96 SaltShakerType)\n        (objectType WineBottle_bar__minus_03_dot_34_bar__plus_00_dot_92_bar__plus_02_dot_03 WineBottleType)\n        (objectType Knife_bar__minus_04_dot_26_bar__plus_00_dot_95_bar__plus_03_dot_86 KnifeType)\n        (objectType LightSwitch_bar__minus_01_dot_59_bar__plus_01_dot_17_bar__plus_01_dot_51 LightSwitchType)\n        (objectType Pot_bar__minus_00_dot_40_bar__plus_00_dot_91_bar__plus_05_dot_93 PotType)\n        (objectType Knife_bar__minus_00_dot_26_bar__plus_00_dot_95_bar__plus_04_dot_94 KnifeType)\n        (objectType ButterKnife_bar__minus_04_dot_18_bar__plus_00_dot_92_bar__plus_04_dot_90 ButterKnifeType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CounterTopType SaltShakerType)\n        (canContain CounterTopType BreadType)\n        (canContain CounterTopType DishSpongeType)\n        (canContain CounterTopType BowlType)\n        (canContain CounterTopType PotType)\n        (canContain CounterTopType WineBottleType)\n        (canContain CounterTopType MugType)\n        (canContain CounterTopType EggType)\n        (canContain CounterTopType ForkType)\n        (canContain CounterTopType SpoonType)\n        (canContain CounterTopType SoapBottleType)\n        (canContain CounterTopType LettuceType)\n        (canContain CounterTopType PotatoType)\n        (canContain CounterTopType PencilType)\n        (canContain CounterTopType ButterKnifeType)\n        (canContain CounterTopType CupType)\n        (canContain CounterTopType PlateType)\n        (canContain CounterTopType PepperShakerType)\n        (canContain CounterTopType TomatoType)\n        (canContain CounterTopType KnifeType)\n        (canContain CounterTopType AppleType)\n        (canContain CounterTopType PanType)\n        (canContain CounterTopType SpatulaType)\n        (canContain MicrowaveType MugType)\n        (canContain MicrowaveType PotatoType)\n        (canContain MicrowaveType EggType)\n        (canContain MicrowaveType AppleType)\n        (canContain MicrowaveType TomatoType)\n        (canContain MicrowaveType BreadType)\n        (canContain MicrowaveType CupType)\n        (canContain MicrowaveType PlateType)\n        (canContain MicrowaveType BowlType)\n        (canContain SinkBasinType DishSpongeType)\n        (canContain SinkBasinType BowlType)\n        (canContain SinkBasinType PotType)\n        (canContain SinkBasinType MugType)\n        (canContain SinkBasinType EggType)\n        (canContain SinkBasinType ForkType)\n        (canContain SinkBasinType SpoonType)\n        (canContain SinkBasinType LettuceType)\n        (canContain SinkBasinType PotatoType)\n        (canContain SinkBasinType ButterKnifeType)\n        (canContain SinkBasinType CupType)\n        (canContain SinkBasinType PlateType)\n        (canContain SinkBasinType TomatoType)\n        (canContain SinkBasinType KnifeType)\n        (canContain SinkBasinType AppleType)\n        (canContain SinkBasinType PanType)\n        (canContain SinkBasinType SpatulaType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain StoveBurnerType PotType)\n        (canContain StoveBurnerType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain StoveBurnerType PotType)\n        (canContain StoveBurnerType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain FridgeType BreadType)\n        (canContain FridgeType BowlType)\n        (canContain FridgeType PotType)\n        (canContain FridgeType WineBottleType)\n        (canContain FridgeType MugType)\n        (canContain FridgeType EggType)\n        (canContain FridgeType LettuceType)\n        (canContain FridgeType PotatoType)\n        (canContain FridgeType CupType)\n        (canContain FridgeType PlateType)\n        (canContain FridgeType TomatoType)\n        (canContain FridgeType AppleType)\n        (canContain FridgeType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CoffeeMachineType MugType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain StoveBurnerType PotType)\n        (canContain StoveBurnerType PanType)\n        (canContain StoveBurnerType PotType)\n        (canContain StoveBurnerType PanType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CounterTopType SaltShakerType)\n        (canContain CounterTopType BreadType)\n        (canContain CounterTopType DishSpongeType)\n        (canContain CounterTopType BowlType)\n        (canContain CounterTopType PotType)\n        (canContain CounterTopType WineBottleType)\n        (canContain CounterTopType MugType)\n        (canContain CounterTopType EggType)\n        (canContain CounterTopType ForkType)\n        (canContain CounterTopType SpoonType)\n        (canContain CounterTopType SoapBottleType)\n        (canContain CounterTopType LettuceType)\n        (canContain CounterTopType PotatoType)\n        (canContain CounterTopType PencilType)\n        (canContain CounterTopType ButterKnifeType)\n        (canContain CounterTopType CupType)\n        (canContain CounterTopType PlateType)\n        (canContain CounterTopType PepperShakerType)\n        (canContain CounterTopType TomatoType)\n        (canContain CounterTopType KnifeType)\n        (canContain CounterTopType AppleType)\n        (canContain CounterTopType PanType)\n        (canContain CounterTopType SpatulaType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CounterTopType SaltShakerType)\n        (canContain CounterTopType BreadType)\n        (canContain CounterTopType DishSpongeType)\n        (canContain CounterTopType BowlType)\n        (canContain CounterTopType PotType)\n        (canContain CounterTopType WineBottleType)\n        (canContain CounterTopType MugType)\n        (canContain CounterTopType EggType)\n        (canContain CounterTopType ForkType)\n        (canContain CounterTopType SpoonType)\n        (canContain CounterTopType SoapBottleType)\n        (canContain CounterTopType LettuceType)\n        (canContain CounterTopType PotatoType)\n        (canContain CounterTopType PencilType)\n        (canContain CounterTopType ButterKnifeType)\n        (canContain CounterTopType CupType)\n        (canContain CounterTopType PlateType)\n        (canContain CounterTopType PepperShakerType)\n        (canContain CounterTopType TomatoType)\n        (canContain CounterTopType KnifeType)\n        (canContain CounterTopType AppleType)\n        (canContain CounterTopType PanType)\n        (canContain CounterTopType SpatulaType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain GarbageCanType BreadType)\n        (canContain GarbageCanType DishSpongeType)\n        (canContain GarbageCanType WineBottleType)\n        (canContain GarbageCanType EggType)\n        (canContain GarbageCanType SoapBottleType)\n        (canContain GarbageCanType LettuceType)\n        (canContain GarbageCanType PotatoType)\n        (canContain GarbageCanType PencilType)\n        (canContain GarbageCanType TomatoType)\n        (canContain GarbageCanType AppleType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain DrawerType SaltShakerType)\n        (canContain DrawerType DishSpongeType)\n        (canContain DrawerType ForkType)\n        (canContain DrawerType SpoonType)\n        (canContain DrawerType SoapBottleType)\n        (canContain DrawerType PencilType)\n        (canContain DrawerType ButterKnifeType)\n        (canContain DrawerType PepperShakerType)\n        (canContain DrawerType KnifeType)\n        (canContain DrawerType SpatulaType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (canContain CabinetType SaltShakerType)\n        (canContain CabinetType DishSpongeType)\n        (canContain CabinetType BowlType)\n        (canContain CabinetType PotType)\n        (canContain CabinetType WineBottleType)\n        (canContain CabinetType MugType)\n        (canContain CabinetType SoapBottleType)\n        (canContain CabinetType CupType)\n        (canContain CabinetType PlateType)\n        (canContain CabinetType PepperShakerType)\n        (canContain CabinetType PanType)\n        (pickupable Bowl_bar__minus_00_dot_26_bar__plus_00_dot_92_bar__plus_04_dot_62)\n        (pickupable Potato_bar__minus_00_dot_22_bar__plus_01_dot_72_bar__plus_03_dot_67)\n        (pickupable Apple_bar__minus_03_dot_73_bar__plus_00_dot_98_bar__plus_05_dot_34)\n        (pickupable Spoon_bar__minus_04_dot_11_bar__plus_00_dot_92_bar__plus_04_dot_38)\n        (pickupable Tomato_bar__minus_04_dot_03_bar__plus_00_dot_98_bar__plus_02_dot_03)\n        (pickupable Lettuce_bar__minus_01_dot_78_bar__plus_01_dot_02_bar__plus_01_dot_72)\n        (pickupable Potato_bar__minus_04_dot_18_bar__plus_00_dot_96_bar__plus_03_dot_86)\n        (pickupable Cup_bar__minus_03_dot_81_bar__plus_01_dot_46_bar__plus_05_dot_51)\n        (pickupable DishSponge_bar__minus_02_dot_11_bar__plus_00_dot_92_bar__plus_01_dot_65)\n        (pickupable Pencil_bar__minus_03_dot_48_bar__plus_00_dot_75_bar__plus_05_dot_16)\n        (pickupable Tomato_bar__minus_03_dot_49_bar__plus_00_dot_98_bar__plus_05_dot_49)\n        (pickupable Egg_bar__minus_03_dot_68_bar__plus_00_dot_10_bar__plus_06_dot_38)\n        (pickupable Lettuce_bar__minus_00_dot_18_bar__plus_01_dot_02_bar__plus_04_dot_94)\n        (pickupable Tomato_bar__minus_04_dot_16_bar__plus_00_dot_85_bar__plus_03_dot_22)\n        (pickupable Spatula_bar__minus_01_dot_61_bar__plus_00_dot_94_bar__plus_01_dot_72)\n        (pickupable Plate_bar__minus_01_dot_94_bar__plus_00_dot_92_bar__plus_01_dot_95)\n        (pickupable Fork_bar__minus_03_dot_73_bar__plus_00_dot_92_bar__plus_05_dot_49)\n        (pickupable Cup_bar__minus_00_dot_57_bar__plus_00_dot_92_bar__plus_06_dot_26)\n        (pickupable PaperTowelRoll_bar__minus_03_dot_78_bar__plus_00_dot_17_bar__plus_06_dot_29)\n        (pickupable Fork_bar__minus_00_dot_26_bar__plus_00_dot_93_bar__plus_05_dot_60)\n        (pickupable ButterKnife_bar__minus_04_dot_13_bar__plus_00_dot_76_bar__plus_02_dot_32)\n        (pickupable Mug_bar__minus_00_dot_41_bar__plus_00_dot_96_bar__plus_06_dot_56)\n        (pickupable Potato_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__plus_01_dot_95)\n        (pickupable Egg_bar__minus_04_dot_16_bar__plus_00_dot_83_bar__plus_03_dot_41)\n        (pickupable DishSponge_bar__minus_00_dot_50_bar__plus_00_dot_75_bar__plus_06_dot_49)\n        (pickupable Pan_bar__minus_04_dot_41_bar__plus_00_dot_92_bar__plus_05_dot_42)\n        (pickupable Bread_bar__minus_03_dot_86_bar__plus_01_dot_00_bar__plus_01_dot_80)\n        (pickupable PepperShaker_bar__minus_04_dot_31_bar__plus_00_dot_92_bar__plus_02_dot_30)\n        (pickupable SaltShaker_bar__minus_00_dot_28_bar__plus_02_dot_13_bar__plus_06_dot_60)\n        (pickupable Knife_bar__minus_00_dot_41_bar__plus_00_dot_95_bar__plus_05_dot_60)\n        (pickupable Bread_bar__minus_00_dot_30_bar__plus_01_dot_15_bar__plus_03_dot_60)\n        (pickupable Cup_bar__minus_00_dot_35_bar__plus_01_dot_07_bar__plus_03_dot_97)\n        (pickupable SoapBottle_bar__minus_03_dot_86_bar__plus_00_dot_92_bar__plus_01_dot_57)\n        (pickupable Pan_bar__minus_02_dot_47_bar__plus_00_dot_93_bar__plus_01_dot_71)\n        (pickupable Fork_bar__minus_04_dot_52_bar__plus_00_dot_92_bar__plus_03_dot_20)\n        (pickupable Apple_bar__minus_03_dot_64_bar__plus_00_dot_12_bar__plus_06_dot_29)\n        (pickupable Spoon_bar__minus_01_dot_86_bar__plus_00_dot_92_bar__plus_01_dot_65)\n        (pickupable Spoon_bar__minus_03_dot_26_bar__plus_00_dot_92_bar__plus_05_dot_41)\n        (pickupable Plate_bar__minus_00_dot_30_bar__plus_01_dot_58_bar__plus_03_dot_47)\n        (pickupable Apple_bar__minus_03_dot_75_bar__plus_00_dot_12_bar__plus_06_dot_46)\n        (pickupable Lettuce_bar__minus_00_dot_30_bar__plus_01_dot_17_bar__plus_03_dot_23)\n        (pickupable Egg_bar__minus_04_dot_08_bar__plus_00_dot_83_bar__plus_03_dot_35)\n        (pickupable Pencil_bar__minus_01_dot_78_bar__plus_00_dot_92_bar__plus_01_dot_80)\n        (pickupable Pan_bar__minus_02_dot_92_bar__plus_00_dot_93_bar__plus_01_dot_71)\n        (pickupable SaltShaker_bar__minus_03_dot_47_bar__plus_00_dot_74_bar__plus_01_dot_96)\n        (pickupable WineBottle_bar__minus_03_dot_34_bar__plus_00_dot_92_bar__plus_02_dot_03)\n        (pickupable Knife_bar__minus_04_dot_26_bar__plus_00_dot_95_bar__plus_03_dot_86)\n        (pickupable Pot_bar__minus_00_dot_40_bar__plus_00_dot_91_bar__plus_05_dot_93)\n        (pickupable Knife_bar__minus_00_dot_26_bar__plus_00_dot_95_bar__plus_04_dot_94)\n        (pickupable ButterKnife_bar__minus_04_dot_18_bar__plus_00_dot_92_bar__plus_04_dot_90)\n        (isReceptacleObject Bowl_bar__minus_00_dot_26_bar__plus_00_dot_92_bar__plus_04_dot_62)\n        (isReceptacleObject Cup_bar__minus_03_dot_81_bar__plus_01_dot_46_bar__plus_05_dot_51)\n        (isReceptacleObject Plate_bar__minus_01_dot_94_bar__plus_00_dot_92_bar__plus_01_dot_95)\n        (isReceptacleObject Cup_bar__minus_00_dot_57_bar__plus_00_dot_92_bar__plus_06_dot_26)\n        (isReceptacleObject Mug_bar__minus_00_dot_41_bar__plus_00_dot_96_bar__plus_06_dot_56)\n        (isReceptacleObject Pan_bar__minus_04_dot_41_bar__plus_00_dot_92_bar__plus_05_dot_42)\n        (isReceptacleObject Cup_bar__minus_00_dot_35_bar__plus_01_dot_07_bar__plus_03_dot_97)\n        (isReceptacleObject Pan_bar__minus_02_dot_47_bar__plus_00_dot_93_bar__plus_01_dot_71)\n        (isReceptacleObject Plate_bar__minus_00_dot_30_bar__plus_01_dot_58_bar__plus_03_dot_47)\n        (isReceptacleObject Pan_bar__minus_02_dot_92_bar__plus_00_dot_93_bar__plus_01_dot_71)\n        (isReceptacleObject Pot_bar__minus_00_dot_40_bar__plus_00_dot_91_bar__plus_05_dot_93)\n        (openable Cabinet_bar__minus_03_dot_92_bar__plus_00_dot_40_bar__plus_02_dot_09)\n        (openable Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_05_dot_92)\n        (openable Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_06_dot_88)\n        (openable Microwave_bar__minus_02_dot_69_bar__plus_01_dot_43_bar__plus_01_dot_69)\n        (openable Cabinet_bar__minus_03_dot_64_bar__plus_00_dot_39_bar__plus_05_dot_03)\n        (openable Cabinet_bar__minus_03_dot_97_bar__plus_00_dot_39_bar__plus_02_dot_89)\n        (openable Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_05_dot_58)\n        (openable Cabinet_bar__minus_03_dot_96_bar__plus_00_dot_39_bar__plus_02_dot_12)\n        (openable Fridge_bar__minus_00_dot_32_bar_00_dot_00_bar__plus_03_dot_60)\n        (openable Cabinet_bar__minus_04_dot_11_bar__plus_01_dot_94_bar__plus_05_dot_32)\n        (openable Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_05_dot_27)\n        (openable Cabinet_bar__minus_03_dot_97_bar__plus_00_dot_39_bar__plus_03_dot_66)\n        (openable Drawer_bar__minus_01_dot_86_bar__plus_00_dot_78_bar__plus_01_dot_96)\n        (openable Cabinet_bar__minus_03_dot_04_bar__plus_00_dot_39_bar__plus_05_dot_03)\n        (openable Cabinet_bar__minus_03_dot_96_bar__plus_00_dot_39_bar__plus_02_dot_88)\n        (openable Cabinet_bar__minus_01_dot_54_bar__plus_00_dot_39_bar__plus_02_dot_09)\n        (openable Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_05_dot_60)\n        (openable Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_06_dot_57)\n        (openable Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_04_dot_62)\n        (openable Drawer_bar__minus_04_dot_13_bar__plus_00_dot_80_bar__plus_02_dot_50)\n        (openable Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_04_dot_30)\n        (openable Drawer_bar__minus_04_dot_09_bar__plus_00_dot_78_bar__plus_04_dot_66)\n        (openable Cabinet_bar__minus_03_dot_19_bar__plus_00_dot_39_bar__plus_02_dot_09)\n        (openable Cabinet_bar__minus_03_dot_04_bar__plus_01_dot_94_bar__plus_05_dot_32)\n        (openable Drawer_bar__minus_03_dot_19_bar__plus_00_dot_78_bar__plus_05_dot_16)\n        (openable Cabinet_bar__minus_03_dot_67_bar__plus_00_dot_39_bar__plus_05_dot_03)\n        (openable Cabinet_bar__minus_03_dot_96_bar__plus_00_dot_39_bar__plus_04_dot_34)\n        \n        (atLocation agent1 loc_bar__minus_4_bar_7_bar_0_bar_30)\n        \n        (cleanable Bowl_bar__minus_00_dot_26_bar__plus_00_dot_92_bar__plus_04_dot_62)\n        (cleanable Potato_bar__minus_00_dot_22_bar__plus_01_dot_72_bar__plus_03_dot_67)\n        (cleanable Apple_bar__minus_03_dot_73_bar__plus_00_dot_98_bar__plus_05_dot_34)\n        (cleanable Spoon_bar__minus_04_dot_11_bar__plus_00_dot_92_bar__plus_04_dot_38)\n        (cleanable Tomato_bar__minus_04_dot_03_bar__plus_00_dot_98_bar__plus_02_dot_03)\n        (cleanable Lettuce_bar__minus_01_dot_78_bar__plus_01_dot_02_bar__plus_01_dot_72)\n        (cleanable Potato_bar__minus_04_dot_18_bar__plus_00_dot_96_bar__plus_03_dot_86)\n        (cleanable Cup_bar__minus_03_dot_81_bar__plus_01_dot_46_bar__plus_05_dot_51)\n        (cleanable DishSponge_bar__minus_02_dot_11_bar__plus_00_dot_92_bar__plus_01_dot_65)\n        (cleanable Tomato_bar__minus_03_dot_49_bar__plus_00_dot_98_bar__plus_05_dot_49)\n        (cleanable Egg_bar__minus_03_dot_68_bar__plus_00_dot_10_bar__plus_06_dot_38)\n        (cleanable Lettuce_bar__minus_00_dot_18_bar__plus_01_dot_02_bar__plus_04_dot_94)\n        (cleanable Tomato_bar__minus_04_dot_16_bar__plus_00_dot_85_bar__plus_03_dot_22)\n        (cleanable Spatula_bar__minus_01_dot_61_bar__plus_00_dot_94_bar__plus_01_dot_72)\n        (cleanable Plate_bar__minus_01_dot_94_bar__plus_00_dot_92_bar__plus_01_dot_95)\n        (cleanable Fork_bar__minus_03_dot_73_bar__plus_00_dot_92_bar__plus_05_dot_49)\n        (cleanable Cup_bar__minus_00_dot_57_bar__plus_00_dot_92_bar__plus_06_dot_26)\n        (cleanable Fork_bar__minus_00_dot_26_bar__plus_00_dot_93_bar__plus_05_dot_60)\n        (cleanable ButterKnife_bar__minus_04_dot_13_bar__plus_00_dot_76_bar__plus_02_dot_32)\n        (cleanable Mug_bar__minus_00_dot_41_bar__plus_00_dot_96_bar__plus_06_dot_56)\n        (cleanable Potato_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__plus_01_dot_95)\n        (cleanable Egg_bar__minus_04_dot_16_bar__plus_00_dot_83_bar__plus_03_dot_41)\n        (cleanable DishSponge_bar__minus_00_dot_50_bar__plus_00_dot_75_bar__plus_06_dot_49)\n        (cleanable Pan_bar__minus_04_dot_41_bar__plus_00_dot_92_bar__plus_05_dot_42)\n        (cleanable Knife_bar__minus_00_dot_41_bar__plus_00_dot_95_bar__plus_05_dot_60)\n        (cleanable Cup_bar__minus_00_dot_35_bar__plus_01_dot_07_bar__plus_03_dot_97)\n        (cleanable Pan_bar__minus_02_dot_47_bar__plus_00_dot_93_bar__plus_01_dot_71)\n        (cleanable Fork_bar__minus_04_dot_52_bar__plus_00_dot_92_bar__plus_03_dot_20)\n        (cleanable Apple_bar__minus_03_dot_64_bar__plus_00_dot_12_bar__plus_06_dot_29)\n        (cleanable Spoon_bar__minus_01_dot_86_bar__plus_00_dot_92_bar__plus_01_dot_65)\n        (cleanable Spoon_bar__minus_03_dot_26_bar__plus_00_dot_92_bar__plus_05_dot_41)\n        (cleanable Plate_bar__minus_00_dot_30_bar__plus_01_dot_58_bar__plus_03_dot_47)\n        (cleanable Apple_bar__minus_03_dot_75_bar__plus_00_dot_12_bar__plus_06_dot_46)\n        (cleanable Lettuce_bar__minus_00_dot_30_bar__plus_01_dot_17_bar__plus_03_dot_23)\n        (cleanable Egg_bar__minus_04_dot_08_bar__plus_00_dot_83_bar__plus_03_dot_35)\n        (cleanable Pan_bar__minus_02_dot_92_bar__plus_00_dot_93_bar__plus_01_dot_71)\n        (cleanable Knife_bar__minus_04_dot_26_bar__plus_00_dot_95_bar__plus_03_dot_86)\n        (cleanable Pot_bar__minus_00_dot_40_bar__plus_00_dot_91_bar__plus_05_dot_93)\n        (cleanable Knife_bar__minus_00_dot_26_bar__plus_00_dot_95_bar__plus_04_dot_94)\n        (cleanable ButterKnife_bar__minus_04_dot_18_bar__plus_00_dot_92_bar__plus_04_dot_90)\n        \n        (heatable Potato_bar__minus_00_dot_22_bar__plus_01_dot_72_bar__plus_03_dot_67)\n        (heatable Apple_bar__minus_03_dot_73_bar__plus_00_dot_98_bar__plus_05_dot_34)\n        (heatable Tomato_bar__minus_04_dot_03_bar__plus_00_dot_98_bar__plus_02_dot_03)\n        (heatable Potato_bar__minus_04_dot_18_bar__plus_00_dot_96_bar__plus_03_dot_86)\n        (heatable Cup_bar__minus_03_dot_81_bar__plus_01_dot_46_bar__plus_05_dot_51)\n        (heatable Tomato_bar__minus_03_dot_49_bar__plus_00_dot_98_bar__plus_05_dot_49)\n        (heatable Egg_bar__minus_03_dot_68_bar__plus_00_dot_10_bar__plus_06_dot_38)\n        (heatable Tomato_bar__minus_04_dot_16_bar__plus_00_dot_85_bar__plus_03_dot_22)\n        (heatable Plate_bar__minus_01_dot_94_bar__plus_00_dot_92_bar__plus_01_dot_95)\n        (heatable Cup_bar__minus_00_dot_57_bar__plus_00_dot_92_bar__plus_06_dot_26)\n        (heatable Mug_bar__minus_00_dot_41_bar__plus_00_dot_96_bar__plus_06_dot_56)\n        (heatable Potato_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__plus_01_dot_95)\n        (heatable Egg_bar__minus_04_dot_16_bar__plus_00_dot_83_bar__plus_03_dot_41)\n        (heatable Bread_bar__minus_03_dot_86_bar__plus_01_dot_00_bar__plus_01_dot_80)\n        (heatable Bread_bar__minus_00_dot_30_bar__plus_01_dot_15_bar__plus_03_dot_60)\n        (heatable Cup_bar__minus_00_dot_35_bar__plus_01_dot_07_bar__plus_03_dot_97)\n        (heatable Apple_bar__minus_03_dot_64_bar__plus_00_dot_12_bar__plus_06_dot_29)\n        (heatable Plate_bar__minus_00_dot_30_bar__plus_01_dot_58_bar__plus_03_dot_47)\n        (heatable Apple_bar__minus_03_dot_75_bar__plus_00_dot_12_bar__plus_06_dot_46)\n        (heatable Egg_bar__minus_04_dot_08_bar__plus_00_dot_83_bar__plus_03_dot_35)\n        (coolable Bowl_bar__minus_00_dot_26_bar__plus_00_dot_92_bar__plus_04_dot_62)\n        (coolable Potato_bar__minus_00_dot_22_bar__plus_01_dot_72_bar__plus_03_dot_67)\n        (coolable Apple_bar__minus_03_dot_73_bar__plus_00_dot_98_bar__plus_05_dot_34)\n        (coolable Tomato_bar__minus_04_dot_03_bar__plus_00_dot_98_bar__plus_02_dot_03)\n        (coolable Lettuce_bar__minus_01_dot_78_bar__plus_01_dot_02_bar__plus_01_dot_72)\n        (coolable Potato_bar__minus_04_dot_18_bar__plus_00_dot_96_bar__plus_03_dot_86)\n        (coolable Cup_bar__minus_03_dot_81_bar__plus_01_dot_46_bar__plus_05_dot_51)\n        (coolable Tomato_bar__minus_03_dot_49_bar__plus_00_dot_98_bar__plus_05_dot_49)\n        (coolable Egg_bar__minus_03_dot_68_bar__plus_00_dot_10_bar__plus_06_dot_38)\n        (coolable Lettuce_bar__minus_00_dot_18_bar__plus_01_dot_02_bar__plus_04_dot_94)\n        (coolable Tomato_bar__minus_04_dot_16_bar__plus_00_dot_85_bar__plus_03_dot_22)\n        (coolable Plate_bar__minus_01_dot_94_bar__plus_00_dot_92_bar__plus_01_dot_95)\n        (coolable Cup_bar__minus_00_dot_57_bar__plus_00_dot_92_bar__plus_06_dot_26)\n        (coolable Mug_bar__minus_00_dot_41_bar__plus_00_dot_96_bar__plus_06_dot_56)\n        (coolable Potato_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__plus_01_dot_95)\n        (coolable Egg_bar__minus_04_dot_16_bar__plus_00_dot_83_bar__plus_03_dot_41)\n        (coolable Pan_bar__minus_04_dot_41_bar__plus_00_dot_92_bar__plus_05_dot_42)\n        (coolable Bread_bar__minus_03_dot_86_bar__plus_01_dot_00_bar__plus_01_dot_80)\n        (coolable Bread_bar__minus_00_dot_30_bar__plus_01_dot_15_bar__plus_03_dot_60)\n        (coolable Cup_bar__minus_00_dot_35_bar__plus_01_dot_07_bar__plus_03_dot_97)\n        (coolable Pan_bar__minus_02_dot_47_bar__plus_00_dot_93_bar__plus_01_dot_71)\n        (coolable Apple_bar__minus_03_dot_64_bar__plus_00_dot_12_bar__plus_06_dot_29)\n        (coolable Plate_bar__minus_00_dot_30_bar__plus_01_dot_58_bar__plus_03_dot_47)\n        (coolable Apple_bar__minus_03_dot_75_bar__plus_00_dot_12_bar__plus_06_dot_46)\n        (coolable Lettuce_bar__minus_00_dot_30_bar__plus_01_dot_17_bar__plus_03_dot_23)\n        (coolable Egg_bar__minus_04_dot_08_bar__plus_00_dot_83_bar__plus_03_dot_35)\n        (coolable Pan_bar__minus_02_dot_92_bar__plus_00_dot_93_bar__plus_01_dot_71)\n        (coolable WineBottle_bar__minus_03_dot_34_bar__plus_00_dot_92_bar__plus_02_dot_03)\n        (coolable Pot_bar__minus_00_dot_40_bar__plus_00_dot_91_bar__plus_05_dot_93)\n        \n        (isCool Cup_bar__minus_00_dot_35_bar__plus_01_dot_07_bar__plus_03_dot_97)\n        \n        \n        \n        (sliceable Potato_bar__minus_00_dot_22_bar__plus_01_dot_72_bar__plus_03_dot_67)\n        (sliceable Apple_bar__minus_03_dot_73_bar__plus_00_dot_98_bar__plus_05_dot_34)\n        (sliceable Tomato_bar__minus_04_dot_03_bar__plus_00_dot_98_bar__plus_02_dot_03)\n        (sliceable Lettuce_bar__minus_01_dot_78_bar__plus_01_dot_02_bar__plus_01_dot_72)\n        (sliceable Potato_bar__minus_04_dot_18_bar__plus_00_dot_96_bar__plus_03_dot_86)\n        (sliceable Tomato_bar__minus_03_dot_49_bar__plus_00_dot_98_bar__plus_05_dot_49)\n        (sliceable Egg_bar__minus_03_dot_68_bar__plus_00_dot_10_bar__plus_06_dot_38)\n        (sliceable Lettuce_bar__minus_00_dot_18_bar__plus_01_dot_02_bar__plus_04_dot_94)\n        (sliceable Tomato_bar__minus_04_dot_16_bar__plus_00_dot_85_bar__plus_03_dot_22)\n        (sliceable Potato_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__plus_01_dot_95)\n        (sliceable Egg_bar__minus_04_dot_16_bar__plus_00_dot_83_bar__plus_03_dot_41)\n        (sliceable Bread_bar__minus_03_dot_86_bar__plus_01_dot_00_bar__plus_01_dot_80)\n        (sliceable Bread_bar__minus_00_dot_30_bar__plus_01_dot_15_bar__plus_03_dot_60)\n        (sliceable Apple_bar__minus_03_dot_64_bar__plus_00_dot_12_bar__plus_06_dot_29)\n        (sliceable Apple_bar__minus_03_dot_75_bar__plus_00_dot_12_bar__plus_06_dot_46)\n        (sliceable Lettuce_bar__minus_00_dot_30_bar__plus_01_dot_17_bar__plus_03_dot_23)\n        (sliceable Egg_bar__minus_04_dot_08_bar__plus_00_dot_83_bar__plus_03_dot_35)\n        \n        (inReceptacle ButterKnife_bar__minus_04_dot_13_bar__plus_00_dot_76_bar__plus_02_dot_32 Drawer_bar__minus_04_dot_13_bar__plus_00_dot_80_bar__plus_02_dot_50)\n        (inReceptacle Potato_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__plus_01_dot_95 CounterTop_bar__minus_01_dot_87_bar__plus_00_dot_96_bar__plus_01_dot_80)\n        (inReceptacle Pencil_bar__minus_01_dot_78_bar__plus_00_dot_92_bar__plus_01_dot_80 CounterTop_bar__minus_01_dot_87_bar__plus_00_dot_96_bar__plus_01_dot_80)\n        (inReceptacle DishSponge_bar__minus_02_dot_11_bar__plus_00_dot_92_bar__plus_01_dot_65 CounterTop_bar__minus_01_dot_87_bar__plus_00_dot_96_bar__plus_01_dot_80)\n        (inReceptacle Spatula_bar__minus_01_dot_61_bar__plus_00_dot_94_bar__plus_01_dot_72 CounterTop_bar__minus_01_dot_87_bar__plus_00_dot_96_bar__plus_01_dot_80)\n        (inReceptacle Plate_bar__minus_01_dot_94_bar__plus_00_dot_92_bar__plus_01_dot_95 CounterTop_bar__minus_01_dot_87_bar__plus_00_dot_96_bar__plus_01_dot_80)\n        (inReceptacle Spoon_bar__minus_01_dot_86_bar__plus_00_dot_92_bar__plus_01_dot_65 CounterTop_bar__minus_01_dot_87_bar__plus_00_dot_96_bar__plus_01_dot_80)\n        (inReceptacle Lettuce_bar__minus_01_dot_78_bar__plus_01_dot_02_bar__plus_01_dot_72 CounterTop_bar__minus_01_dot_87_bar__plus_00_dot_96_bar__plus_01_dot_80)\n        (inReceptacle Pan_bar__minus_02_dot_92_bar__plus_00_dot_93_bar__plus_01_dot_71 StoveBurner_bar__minus_02_dot_92_bar__plus_00_dot_92_bar__plus_02_dot_01)\n        (inReceptacle SaltShaker_bar__minus_00_dot_28_bar__plus_02_dot_13_bar__plus_06_dot_60 Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_06_dot_81)\n        (inReceptacle Bowl_bar__minus_00_dot_26_bar__plus_00_dot_92_bar__plus_04_dot_62 CounterTop_bar__minus_00_dot_35_bar__plus_00_dot_96_bar__plus_05_dot_60)\n        (inReceptacle Knife_bar__minus_00_dot_41_bar__plus_00_dot_95_bar__plus_05_dot_60 CounterTop_bar__minus_00_dot_35_bar__plus_00_dot_96_bar__plus_05_dot_60)\n        (inReceptacle Lettuce_bar__minus_00_dot_18_bar__plus_01_dot_02_bar__plus_04_dot_94 CounterTop_bar__minus_00_dot_35_bar__plus_00_dot_96_bar__plus_05_dot_60)\n        (inReceptacle Pot_bar__minus_00_dot_40_bar__plus_00_dot_91_bar__plus_05_dot_93 CounterTop_bar__minus_00_dot_35_bar__plus_00_dot_96_bar__plus_05_dot_60)\n        (inReceptacle Knife_bar__minus_00_dot_26_bar__plus_00_dot_95_bar__plus_04_dot_94 CounterTop_bar__minus_00_dot_35_bar__plus_00_dot_96_bar__plus_05_dot_60)\n        (inReceptacle Mug_bar__minus_00_dot_41_bar__plus_00_dot_96_bar__plus_06_dot_56 CounterTop_bar__minus_00_dot_35_bar__plus_00_dot_96_bar__plus_05_dot_60)\n        (inReceptacle Cup_bar__minus_00_dot_57_bar__plus_00_dot_92_bar__plus_06_dot_26 CounterTop_bar__minus_00_dot_35_bar__plus_00_dot_96_bar__plus_05_dot_60)\n        (inReceptacle Fork_bar__minus_00_dot_26_bar__plus_00_dot_93_bar__plus_05_dot_60 CounterTop_bar__minus_00_dot_35_bar__plus_00_dot_96_bar__plus_05_dot_60)\n        (inReceptacle Pencil_bar__minus_03_dot_48_bar__plus_00_dot_75_bar__plus_05_dot_16 Drawer_bar__minus_03_dot_51_bar__plus_00_dot_78_bar__plus_05_dot_16)\n        (inReceptacle Pan_bar__minus_02_dot_92_bar__plus_00_dot_93_bar__plus_01_dot_71 StoveBurner_bar__minus_02_dot_92_bar__plus_00_dot_92_bar__plus_01_dot_71)\n        (inReceptacle Spoon_bar__minus_03_dot_26_bar__plus_00_dot_92_bar__plus_05_dot_41 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle Pan_bar__minus_04_dot_41_bar__plus_00_dot_92_bar__plus_05_dot_42 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle Bread_bar__minus_03_dot_86_bar__plus_01_dot_00_bar__plus_01_dot_80 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle PepperShaker_bar__minus_04_dot_31_bar__plus_00_dot_92_bar__plus_02_dot_30 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle SoapBottle_bar__minus_03_dot_86_bar__plus_00_dot_92_bar__plus_01_dot_57 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle Apple_bar__minus_03_dot_73_bar__plus_00_dot_98_bar__plus_05_dot_34 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle Tomato_bar__minus_03_dot_49_bar__plus_00_dot_98_bar__plus_05_dot_49 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle WineBottle_bar__minus_03_dot_34_bar__plus_00_dot_92_bar__plus_02_dot_03 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle Knife_bar__minus_04_dot_26_bar__plus_00_dot_95_bar__plus_03_dot_86 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle Spoon_bar__minus_04_dot_11_bar__plus_00_dot_92_bar__plus_04_dot_38 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle Fork_bar__minus_04_dot_52_bar__plus_00_dot_92_bar__plus_03_dot_20 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle Tomato_bar__minus_04_dot_03_bar__plus_00_dot_98_bar__plus_02_dot_03 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle Fork_bar__minus_03_dot_73_bar__plus_00_dot_92_bar__plus_05_dot_49 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle Potato_bar__minus_04_dot_18_bar__plus_00_dot_96_bar__plus_03_dot_86 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle ButterKnife_bar__minus_04_dot_18_bar__plus_00_dot_92_bar__plus_04_dot_90 CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94)\n        (inReceptacle Cup_bar__minus_03_dot_81_bar__plus_01_dot_46_bar__plus_05_dot_51 Cabinet_bar__minus_04_dot_11_bar__plus_01_dot_94_bar__plus_05_dot_32)\n        (inReceptacle SaltShaker_bar__minus_03_dot_47_bar__plus_00_dot_74_bar__plus_01_dot_96 Drawer_bar__minus_03_dot_42_bar__plus_00_dot_78_bar__plus_01_dot_96)\n        (inReceptacle Pan_bar__minus_02_dot_47_bar__plus_00_dot_93_bar__plus_01_dot_71 StoveBurner_bar__minus_02_dot_47_bar__plus_00_dot_92_bar__plus_01_dot_71)\n        (inReceptacle DishSponge_bar__minus_00_dot_50_bar__plus_00_dot_75_bar__plus_06_dot_49 Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_06_dot_57)\n        (inReceptacle Mug_bar__minus_00_dot_41_bar__plus_00_dot_96_bar__plus_06_dot_56 CoffeeMachine_bar__minus_00_dot_28_bar__plus_00_dot_91_bar__plus_06_dot_56)\n        (inReceptacle Egg_bar__minus_03_dot_68_bar__plus_00_dot_10_bar__plus_06_dot_38 GarbageCan_bar__minus_03_dot_71_bar_00_dot_00_bar__plus_06_dot_38)\n        (inReceptacle PaperTowelRoll_bar__minus_03_dot_78_bar__plus_00_dot_17_bar__plus_06_dot_29 GarbageCan_bar__minus_03_dot_71_bar_00_dot_00_bar__plus_06_dot_38)\n        (inReceptacle Apple_bar__minus_03_dot_75_bar__plus_00_dot_12_bar__plus_06_dot_46 GarbageCan_bar__minus_03_dot_71_bar_00_dot_00_bar__plus_06_dot_38)\n        (inReceptacle Apple_bar__minus_03_dot_64_bar__plus_00_dot_12_bar__plus_06_dot_29 GarbageCan_bar__minus_03_dot_71_bar_00_dot_00_bar__plus_06_dot_38)\n        (inReceptacle Tomato_bar__minus_04_dot_16_bar__plus_00_dot_85_bar__plus_03_dot_22 Sink_bar__minus_04_dot_19_bar__plus_00_dot_92_bar__plus_03_dot_28_bar_SinkBasin)\n        (inReceptacle Egg_bar__minus_04_dot_16_bar__plus_00_dot_83_bar__plus_03_dot_41 Sink_bar__minus_04_dot_19_bar__plus_00_dot_92_bar__plus_03_dot_28_bar_SinkBasin)\n        (inReceptacle Egg_bar__minus_04_dot_08_bar__plus_00_dot_83_bar__plus_03_dot_35 Sink_bar__minus_04_dot_19_bar__plus_00_dot_92_bar__plus_03_dot_28_bar_SinkBasin)\n        (inReceptacle Cup_bar__minus_00_dot_35_bar__plus_01_dot_07_bar__plus_03_dot_97 Fridge_bar__minus_00_dot_32_bar_00_dot_00_bar__plus_03_dot_60)\n        (inReceptacle Potato_bar__minus_00_dot_22_bar__plus_01_dot_72_bar__plus_03_dot_67 Fridge_bar__minus_00_dot_32_bar_00_dot_00_bar__plus_03_dot_60)\n        (inReceptacle Lettuce_bar__minus_00_dot_30_bar__plus_01_dot_17_bar__plus_03_dot_23 Fridge_bar__minus_00_dot_32_bar_00_dot_00_bar__plus_03_dot_60)\n        (inReceptacle Bread_bar__minus_00_dot_30_bar__plus_01_dot_15_bar__plus_03_dot_60 Fridge_bar__minus_00_dot_32_bar_00_dot_00_bar__plus_03_dot_60)\n        \n        \n        (receptacleAtLocation Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_03_dot_01 loc_bar__minus_2_bar_10_bar_0_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_04_dot_21 loc_bar__minus_5_bar_17_bar_1_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_04_dot_31 loc_bar__minus_4_bar_18_bar_1_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_05_dot_51 loc_bar__minus_4_bar_22_bar_1_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_05_dot_61 loc_bar__minus_4_bar_22_bar_1_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_00_dot_33_bar__plus_02_dot_25_bar__plus_06_dot_81 loc_bar__minus_4_bar_25_bar_1_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_04_dot_30 loc_bar__minus_6_bar_17_bar_1_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_05_dot_58 loc_bar__minus_6_bar_20_bar_1_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_05_dot_60 loc_bar__minus_6_bar_22_bar_1_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_00_dot_64_bar__plus_00_dot_39_bar__plus_06_dot_88 loc_bar__minus_6_bar_25_bar_1_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_01_dot_54_bar__plus_00_dot_39_bar__plus_02_dot_09 loc_bar__minus_9_bar_12_bar_2_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_01_dot_58_bar__plus_01_dot_93_bar__plus_01_dot_78 loc_bar__minus_4_bar_7_bar_3_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_03_dot_04_bar__plus_00_dot_39_bar__plus_05_dot_03 loc_bar__minus_11_bar_17_bar_0_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_03_dot_04_bar__plus_01_dot_94_bar__plus_05_dot_32 loc_bar__minus_13_bar_19_bar_0_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_03_dot_19_bar__plus_00_dot_39_bar__plus_02_dot_09 loc_bar__minus_12_bar_12_bar_2_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_03_dot_20_bar__plus_01_dot_93_bar__plus_01_dot_78 loc_bar__minus_13_bar_10_bar_2_bar__minus_15)\n        (receptacleAtLocation Cabinet_bar__minus_03_dot_64_bar__plus_00_dot_39_bar__plus_05_dot_03 loc_bar__minus_12_bar_17_bar_0_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_03_dot_67_bar__plus_00_dot_39_bar__plus_05_dot_03 loc_bar__minus_13_bar_16_bar_0_bar_45)\n        (receptacleAtLocation Cabinet_bar__minus_03_dot_92_bar__plus_00_dot_40_bar__plus_02_dot_09 loc_bar__minus_13_bar_12_bar_2_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_03_dot_96_bar__plus_00_dot_39_bar__plus_02_dot_12 loc_bar__minus_12_bar_10_bar_3_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_03_dot_96_bar__plus_00_dot_39_bar__plus_02_dot_88 loc_bar__minus_12_bar_13_bar_3_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_03_dot_96_bar__plus_00_dot_39_bar__plus_04_dot_34 loc_bar__minus_12_bar_17_bar_3_bar_60)\n        (receptacleAtLocation Cabinet_bar__minus_03_dot_97_bar__plus_00_dot_39_bar__plus_02_dot_89 loc_bar__minus_12_bar_13_bar_3_bar_45)\n        (receptacleAtLocation Cabinet_bar__minus_03_dot_97_bar__plus_00_dot_39_bar__plus_03_dot_66 loc_bar__minus_12_bar_12_bar_3_bar_45)\n        (receptacleAtLocation Cabinet_bar__minus_04_dot_11_bar__plus_01_dot_94_bar__plus_05_dot_32 loc_bar__minus_15_bar_19_bar_0_bar__minus_30)\n        (receptacleAtLocation Cabinet_bar__minus_04_dot_28_bar__plus_01_dot_93_bar__plus_02_dot_23 loc_bar__minus_14_bar_10_bar_3_bar__minus_15)\n        (receptacleAtLocation CoffeeMachine_bar__minus_00_dot_28_bar__plus_00_dot_91_bar__plus_06_dot_56 loc_bar__minus_4_bar_25_bar_1_bar_45)\n        (receptacleAtLocation CounterTop_bar__minus_00_dot_35_bar__plus_00_dot_96_bar__plus_05_dot_60 loc_bar__minus_4_bar_20_bar_1_bar_45)\n        (receptacleAtLocation CounterTop_bar__minus_01_dot_87_bar__plus_00_dot_96_bar__plus_01_dot_80 loc_bar__minus_8_bar_10_bar_2_bar_45)\n        (receptacleAtLocation CounterTop_bar__minus_04_dot_26_bar__plus_00_dot_96_bar__plus_04_dot_94 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (receptacleAtLocation Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_04_dot_62 loc_bar__minus_4_bar_21_bar_2_bar_60)\n        (receptacleAtLocation Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_05_dot_27 loc_bar__minus_4_bar_24_bar_2_bar_45)\n        (receptacleAtLocation Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_05_dot_92 loc_bar__minus_4_bar_21_bar_0_bar_60)\n        (receptacleAtLocation Drawer_bar__minus_00_dot_51_bar__plus_00_dot_78_bar__plus_06_dot_57 loc_bar__minus_4_bar_23_bar_0_bar_45)\n        (receptacleAtLocation Drawer_bar__minus_01_dot_86_bar__plus_00_dot_78_bar__plus_01_dot_96 loc_bar__minus_5_bar_9_bar_3_bar_60)\n        (receptacleAtLocation Drawer_bar__minus_03_dot_19_bar__plus_00_dot_78_bar__plus_05_dot_16 loc_bar__minus_15_bar_19_bar_1_bar_60)\n        (receptacleAtLocation Drawer_bar__minus_03_dot_42_bar__plus_00_dot_78_bar__plus_01_dot_96 loc_bar__minus_14_bar_10_bar_2_bar_60)\n        (receptacleAtLocation Drawer_bar__minus_03_dot_51_bar__plus_00_dot_78_bar__plus_05_dot_16 loc_bar__minus_14_bar_19_bar_0_bar_60)\n        (receptacleAtLocation Drawer_bar__minus_03_dot_80_bar__plus_00_dot_78_bar__plus_01_dot_96 loc_bar__minus_14_bar_10_bar_2_bar_60)\n        (receptacleAtLocation Drawer_bar__minus_03_dot_80_bar__plus_00_dot_78_bar__plus_05_dot_16 loc_bar__minus_15_bar_19_bar_0_bar_60)\n        (receptacleAtLocation Drawer_bar__minus_04_dot_09_bar__plus_00_dot_78_bar__plus_04_dot_66 loc_bar__minus_15_bar_14_bar_0_bar_30)\n        (receptacleAtLocation Drawer_bar__minus_04_dot_13_bar__plus_00_dot_80_bar__plus_02_dot_50 loc_bar__minus_13_bar_11_bar_3_bar_45)\n        (receptacleAtLocation Fridge_bar__minus_00_dot_32_bar_00_dot_00_bar__plus_03_dot_60 loc_bar__minus_5_bar_15_bar_1_bar_60)\n        (receptacleAtLocation GarbageCan_bar__minus_03_dot_71_bar_00_dot_00_bar__plus_06_dot_38 loc_bar__minus_10_bar_25_bar_3_bar_60)\n        (receptacleAtLocation Microwave_bar__minus_02_dot_69_bar__plus_01_dot_43_bar__plus_01_dot_69 loc_bar__minus_11_bar_10_bar_2_bar_15)\n        (receptacleAtLocation Sink_bar__minus_04_dot_19_bar__plus_00_dot_92_bar__plus_03_dot_28_bar_SinkBasin loc_bar__minus_15_bar_14_bar_3_bar_60)\n        (receptacleAtLocation StoveBurner_bar__minus_02_dot_47_bar__plus_00_dot_92_bar__plus_01_dot_71 loc_bar__minus_10_bar_10_bar_2_bar_45)\n        (receptacleAtLocation StoveBurner_bar__minus_02_dot_47_bar__plus_00_dot_92_bar__plus_02_dot_01 loc_bar__minus_10_bar_10_bar_2_bar_60)\n        (receptacleAtLocation StoveBurner_bar__minus_02_dot_92_bar__plus_00_dot_92_bar__plus_01_dot_71 loc_bar__minus_12_bar_10_bar_2_bar_45)\n        (receptacleAtLocation StoveBurner_bar__minus_02_dot_92_bar__plus_00_dot_92_bar__plus_02_dot_01 loc_bar__minus_12_bar_10_bar_2_bar_60)\n        (receptacleAtLocation Toaster_bar__minus_03_dot_58_bar__plus_00_dot_91_bar__plus_01_dot_76 loc_bar__minus_14_bar_10_bar_2_bar_45)\n        (objectAtLocation Spoon_bar__minus_04_dot_11_bar__plus_00_dot_92_bar__plus_04_dot_38 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation DishSponge_bar__minus_02_dot_11_bar__plus_00_dot_92_bar__plus_01_dot_65 loc_bar__minus_8_bar_10_bar_2_bar_45)\n        (objectAtLocation ButterKnife_bar__minus_04_dot_18_bar__plus_00_dot_92_bar__plus_04_dot_90 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation SaltShaker_bar__minus_00_dot_28_bar__plus_02_dot_13_bar__plus_06_dot_60 loc_bar__minus_4_bar_25_bar_1_bar__minus_30)\n        (objectAtLocation Pan_bar__minus_02_dot_47_bar__plus_00_dot_93_bar__plus_01_dot_71 loc_bar__minus_10_bar_10_bar_2_bar_45)\n        (objectAtLocation Pencil_bar__minus_01_dot_78_bar__plus_00_dot_92_bar__plus_01_dot_80 loc_bar__minus_8_bar_10_bar_2_bar_45)\n        (objectAtLocation Cup_bar__minus_00_dot_35_bar__plus_01_dot_07_bar__plus_03_dot_97 loc_bar__minus_5_bar_15_bar_1_bar_60)\n        (objectAtLocation Tomato_bar__minus_03_dot_49_bar__plus_00_dot_98_bar__plus_05_dot_49 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation Bread_bar__minus_00_dot_30_bar__plus_01_dot_15_bar__plus_03_dot_60 loc_bar__minus_5_bar_15_bar_1_bar_60)\n        (objectAtLocation Lettuce_bar__minus_00_dot_30_bar__plus_01_dot_17_bar__plus_03_dot_23 loc_bar__minus_5_bar_15_bar_1_bar_60)\n        (objectAtLocation Plate_bar__minus_00_dot_30_bar__plus_01_dot_58_bar__plus_03_dot_47 loc_bar__minus_5_bar_14_bar_1_bar_0)\n        (objectAtLocation Fork_bar__minus_03_dot_73_bar__plus_00_dot_92_bar__plus_05_dot_49 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation Potato_bar__minus_00_dot_22_bar__plus_01_dot_72_bar__plus_03_dot_67 loc_bar__minus_5_bar_15_bar_1_bar_60)\n        (objectAtLocation Knife_bar__minus_04_dot_26_bar__plus_00_dot_95_bar__plus_03_dot_86 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation Apple_bar__minus_03_dot_75_bar__plus_00_dot_12_bar__plus_06_dot_46 loc_bar__minus_10_bar_25_bar_3_bar_60)\n        (objectAtLocation Egg_bar__minus_04_dot_08_bar__plus_00_dot_83_bar__plus_03_dot_35 loc_bar__minus_15_bar_14_bar_3_bar_60)\n        (objectAtLocation Apple_bar__minus_03_dot_73_bar__plus_00_dot_98_bar__plus_05_dot_34 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation Lettuce_bar__minus_01_dot_78_bar__plus_01_dot_02_bar__plus_01_dot_72 loc_bar__minus_8_bar_10_bar_2_bar_45)\n        (objectAtLocation Tomato_bar__minus_04_dot_03_bar__plus_00_dot_98_bar__plus_02_dot_03 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation Knife_bar__minus_00_dot_26_bar__plus_00_dot_95_bar__plus_04_dot_94 loc_bar__minus_4_bar_20_bar_1_bar_45)\n        (objectAtLocation Egg_bar__minus_04_dot_16_bar__plus_00_dot_83_bar__plus_03_dot_41 loc_bar__minus_15_bar_14_bar_3_bar_60)\n        (objectAtLocation Cup_bar__minus_03_dot_81_bar__plus_01_dot_46_bar__plus_05_dot_51 loc_bar__minus_15_bar_19_bar_0_bar__minus_30)\n        (objectAtLocation Spoon_bar__minus_01_dot_86_bar__plus_00_dot_92_bar__plus_01_dot_65 loc_bar__minus_8_bar_10_bar_2_bar_45)\n        (objectAtLocation Fork_bar__minus_04_dot_52_bar__plus_00_dot_92_bar__plus_03_dot_20 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation Potato_bar__minus_04_dot_18_bar__plus_00_dot_96_bar__plus_03_dot_86 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation Pan_bar__minus_04_dot_41_bar__plus_00_dot_92_bar__plus_05_dot_42 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation Sink_bar__minus_04_dot_19_bar__plus_00_dot_92_bar__plus_03_dot_28 loc_bar__minus_15_bar_13_bar_3_bar_60)\n        (objectAtLocation Egg_bar__minus_03_dot_68_bar__plus_00_dot_10_bar__plus_06_dot_38 loc_bar__minus_10_bar_25_bar_3_bar_60)\n        (objectAtLocation Apple_bar__minus_03_dot_64_bar__plus_00_dot_12_bar__plus_06_dot_29 loc_bar__minus_10_bar_25_bar_3_bar_60)\n        (objectAtLocation Knife_bar__minus_00_dot_41_bar__plus_00_dot_95_bar__plus_05_dot_60 loc_bar__minus_4_bar_20_bar_1_bar_45)\n        (objectAtLocation Potato_bar__minus_01_dot_69_bar__plus_00_dot_96_bar__plus_01_dot_95 loc_bar__minus_8_bar_10_bar_2_bar_45)\n        (objectAtLocation StoveKnob_bar__minus_02_dot_35_bar__plus_01_dot_10_bar__plus_01_dot_56 loc_bar__minus_9_bar_10_bar_2_bar_30)\n        (objectAtLocation StoveKnob_bar__minus_02_dot_79_bar__plus_01_dot_10_bar__plus_01_dot_56 loc_bar__minus_11_bar_10_bar_2_bar_30)\n        (objectAtLocation StoveKnob_bar__minus_02_dot_97_bar__plus_01_dot_10_bar__plus_01_dot_56 loc_bar__minus_12_bar_10_bar_2_bar_30)\n        (objectAtLocation StoveKnob_bar__minus_02_dot_51_bar__plus_01_dot_10_bar__plus_01_dot_56 loc_bar__minus_10_bar_10_bar_2_bar_30)\n        (objectAtLocation Fork_bar__minus_00_dot_26_bar__plus_00_dot_93_bar__plus_05_dot_60 loc_bar__minus_4_bar_20_bar_1_bar_45)\n        (objectAtLocation Bowl_bar__minus_00_dot_26_bar__plus_00_dot_92_bar__plus_04_dot_62 loc_bar__minus_4_bar_20_bar_1_bar_45)\n        (objectAtLocation Plate_bar__minus_01_dot_94_bar__plus_00_dot_92_bar__plus_01_dot_95 loc_bar__minus_8_bar_10_bar_2_bar_45)\n        (objectAtLocation Lettuce_bar__minus_00_dot_18_bar__plus_01_dot_02_bar__plus_04_dot_94 loc_bar__minus_4_bar_20_bar_1_bar_45)\n        (objectAtLocation Bread_bar__minus_03_dot_86_bar__plus_01_dot_00_bar__plus_01_dot_80 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation Tomato_bar__minus_04_dot_16_bar__plus_00_dot_85_bar__plus_03_dot_22 loc_bar__minus_15_bar_14_bar_3_bar_60)\n        (objectAtLocation Curtains_bar__minus_04_dot_52_bar__plus_02_dot_25_bar__plus_03_dot_39 loc_bar__minus_15_bar_14_bar_3_bar__minus_30)\n        (objectAtLocation Window_bar__minus_04_dot_87_bar__plus_01_dot_63_bar__plus_03_dot_31 loc_bar__minus_15_bar_13_bar_3_bar_0)\n        (objectAtLocation WineBottle_bar__minus_03_dot_34_bar__plus_00_dot_92_bar__plus_02_dot_03 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation Spatula_bar__minus_01_dot_61_bar__plus_00_dot_94_bar__plus_01_dot_72 loc_bar__minus_8_bar_10_bar_2_bar_45)\n        (objectAtLocation PaperTowelRoll_bar__minus_03_dot_78_bar__plus_00_dot_17_bar__plus_06_dot_29 loc_bar__minus_10_bar_25_bar_3_bar_60)\n        (objectAtLocation Cup_bar__minus_00_dot_57_bar__plus_00_dot_92_bar__plus_06_dot_26 loc_bar__minus_4_bar_20_bar_1_bar_45)\n        (objectAtLocation Pencil_bar__minus_03_dot_48_bar__plus_00_dot_75_bar__plus_05_dot_16 loc_bar__minus_14_bar_19_bar_0_bar_60)\n        (objectAtLocation Pan_bar__minus_02_dot_92_bar__plus_00_dot_93_bar__plus_01_dot_71 loc_bar__minus_12_bar_10_bar_2_bar_45)\n        (objectAtLocation Pot_bar__minus_00_dot_40_bar__plus_00_dot_91_bar__plus_05_dot_93 loc_bar__minus_4_bar_20_bar_1_bar_45)\n        (objectAtLocation LightSwitch_bar__minus_01_dot_59_bar__plus_01_dot_17_bar__plus_01_dot_51 loc_bar__minus_4_bar_6_bar_3_bar_45)\n        (objectAtLocation SaltShaker_bar__minus_03_dot_47_bar__plus_00_dot_74_bar__plus_01_dot_96 loc_bar__minus_14_bar_10_bar_2_bar_60)\n        (objectAtLocation SoapBottle_bar__minus_03_dot_86_bar__plus_00_dot_92_bar__plus_01_dot_57 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation PepperShaker_bar__minus_04_dot_31_bar__plus_00_dot_92_bar__plus_02_dot_30 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation ButterKnife_bar__minus_04_dot_13_bar__plus_00_dot_76_bar__plus_02_dot_32 loc_bar__minus_13_bar_11_bar_3_bar_45)\n        (objectAtLocation DishSponge_bar__minus_00_dot_50_bar__plus_00_dot_75_bar__plus_06_dot_49 loc_bar__minus_4_bar_23_bar_0_bar_45)\n        (objectAtLocation Spoon_bar__minus_03_dot_26_bar__plus_00_dot_92_bar__plus_05_dot_41 loc_bar__minus_15_bar_19_bar_3_bar_60)\n        (objectAtLocation Mug_bar__minus_00_dot_41_bar__plus_00_dot_96_bar__plus_06_dot_56 loc_bar__minus_4_bar_25_bar_1_bar_45)\n        )\n    \n\n        (:goal\n            (and\n                (exists (?r - receptacle)\n                    (exists (?o - object)\n                        (and\n                            (heatable ?o)\n                            (objectType ?o CupType)\n                            (receptacleType ?r CounterTopType)\n                            (isHot ?o)\n                            (inReceptacle ?o ?r)\n                        )\n                    )\n                )\n            )\n        )\n    )\n    ", "solvable": true, "walkthrough": ["go to countertop 3", "take cup 2 from countertop 3", "go to microwave 1", "heat cup 2 with microwave 1", "go to countertop 1", "move cup 2 to countertop 1"]}