
(define (problem plan_trial_T20190906_222705_543880)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Box_bar__minus_00_dot_86_bar__plus_01_dot_01_bar__plus_01_dot_16 - object
        Box_bar__minus_05_dot_25_bar__plus_00_dot_67_bar__plus_02_dot_43 - object
        Chair_bar__minus_00_dot_35_bar__minus_00_dot_01_bar__plus_01_dot_66 - object
        Chair_bar__minus_00_dot_67_bar__minus_00_dot_01_bar__plus_00_dot_66 - object
        Chair_bar__minus_01_dot_93_bar__minus_00_dot_01_bar__plus_02_dot_41 - object
        Chair_bar__minus_05_dot_15_bar__minus_00_dot_01_bar__plus_00_dot_82 - object
        CreditCard_bar__minus_01_dot_34_bar__plus_00_dot_82_bar__plus_01_dot_03 - object
        CreditCard_bar__minus_01_dot_82_bar__plus_00_dot_82_bar__plus_01_dot_89 - object
        CreditCard_bar__minus_04_dot_00_bar__plus_00_dot_64_bar__plus_04_dot_69 - object
        FloorLamp_bar__minus_05_dot_66_bar__minus_00_dot_01_bar__plus_04_dot_68 - object
        HousePlant_bar__minus_05_dot_71_bar__plus_00_dot_58_bar__plus_03_dot_94 - object
        KeyChain_bar__minus_01_dot_27_bar__plus_00_dot_82_bar__plus_01_dot_39 - object
        KeyChain_bar__minus_01_dot_49_bar__plus_00_dot_82_bar__plus_01_dot_63 - object
        KeyChain_bar__minus_02_dot_50_bar__plus_00_dot_52_bar__plus_04_dot_63 - object
        Laptop_bar__minus_01_dot_08_bar__plus_00_dot_82_bar__plus_01_dot_70 - object
        Laptop_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_19 - object
        LightSwitch_bar__plus_00_dot_00_bar__plus_01_dot_29_bar__plus_03_dot_38 - object
        Newspaper_bar__minus_01_dot_02_bar__plus_00_dot_83_bar__plus_01_dot_28 - object
        Newspaper_bar__minus_01_dot_56_bar__plus_00_dot_83_bar__plus_01_dot_30 - object
        Painting_bar__minus_01_dot_62_bar__plus_01_dot_57_bar_00_dot_00 - object
        Painting_bar__minus_04_dot_16_bar__plus_01_dot_66_bar_00_dot_00 - object
        Pillow_bar__minus_04_dot_16_bar__plus_00_dot_54_bar__plus_00_dot_58 - object
        Plate_bar__minus_04_dot_06_bar__plus_00_dot_45_bar__plus_02_dot_72 - object
        RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_82_bar__plus_01_dot_81 - object
        RemoteControl_bar__minus_03_dot_91_bar__plus_00_dot_43_bar__plus_02_dot_06 - object
        Statue_bar__minus_03_dot_68_bar__plus_00_dot_65_bar__plus_04_dot_69 - object
        Statue_bar__minus_03_dot_83_bar__plus_00_dot_44_bar__plus_02_dot_39 - object
        Statue_bar__minus_04_dot_06_bar__plus_00_dot_44_bar__plus_02_dot_39 - object
        Statue_bar__minus_04_dot_14_bar__plus_00_dot_44_bar__plus_01_dot_90 - object
        Television_bar__minus_03_dot_76_bar__plus_01_dot_13_bar__plus_04_dot_58 - object
        Vase_bar__minus_03_dot_89_bar__plus_00_dot_64_bar__plus_04_dot_12 - object
        Watch_bar__minus_03_dot_76_bar__plus_00_dot_43_bar__plus_02_dot_55 - object
        Window_bar__minus_06_dot_04_bar__plus_01_dot_53_bar__plus_00_dot_86 - object
        Window_bar__minus_06_dot_04_bar__plus_01_dot_53_bar__plus_04_dot_24 - object
        Window_bar__minus_06_dot_04_bar__plus_01_dot_55_bar__plus_02_dot_10 - object
        CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45 - receptacle
        DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45 - receptacle
        Drawer_bar__minus_02_dot_56_bar__plus_00_dot_53_bar__plus_04_dot_48 - receptacle
        GarbageCan_bar__minus_00_dot_22_bar__minus_00_dot_01_bar__plus_04_dot_73 - receptacle
        SideTable_bar__minus_02_dot_56_bar__minus_00_dot_01_bar__plus_04_dot_74 - receptacle
        SideTable_bar__minus_03_dot_79_bar__minus_00_dot_01_bar__plus_04_dot_46 - receptacle
        Sofa_bar__minus_03_dot_66_bar__minus_00_dot_02_bar__plus_00_dot_46 - receptacle
        Sofa_bar__minus_05_dot_40_bar__minus_00_dot_02_bar__plus_02_dot_43 - receptacle
        loc_bar__minus_5_bar_10_bar_2_bar_30 - location
        loc_bar__minus_8_bar_12_bar_2_bar_60 - location
        loc_bar__minus_11_bar_9_bar_3_bar_45 - location
        loc_bar__minus_19_bar_18_bar_1_bar_60 - location
        loc_bar__minus_12_bar_15_bar_0_bar_60 - location
        loc_bar__minus_6_bar_17_bar_3_bar_60 - location
        loc_bar__minus_2_bar_14_bar_1_bar_30 - location
        loc_bar__minus_20_bar_18_bar_3_bar_60 - location
        loc_bar__minus_10_bar_5_bar_2_bar_0 - location
        loc_bar__minus_3_bar_9_bar_2_bar_45 - location
        loc_bar__minus_5_bar_17_bar_1_bar_60 - location
        loc_bar__minus_5_bar_10_bar_2_bar_45 - location
        loc_bar__minus_20_bar_16_bar_3_bar_60 - location
        loc_bar__minus_19_bar_8_bar_3_bar_0 - location
        loc_bar__minus_13_bar_7_bar_2_bar_45 - location
        loc_bar__minus_19_bar_5_bar_3_bar_0 - location
        loc_bar__minus_18_bar_10_bar_3_bar_60 - location
        loc_bar__minus_2_bar_9_bar_2_bar_60 - location
        loc_bar__minus_10_bar_6_bar_1_bar_60 - location
        loc_bar__minus_17_bar_5_bar_2_bar_0 - location
        loc_bar__minus_20_bar_17_bar_3_bar_0 - location
        loc_bar__minus_19_bar_5_bar_2_bar_60 - location
        loc_bar__minus_7_bar_14_bar_3_bar_30 - location
        )
    

(:init


        (receptacleType Sofa_bar__minus_05_dot_40_bar__minus_00_dot_02_bar__plus_02_dot_43 SofaType)
        (receptacleType Drawer_bar__minus_02_dot_56_bar__plus_00_dot_53_bar__plus_04_dot_48 DrawerType)
        (receptacleType SideTable_bar__minus_03_dot_79_bar__minus_00_dot_01_bar__plus_04_dot_46 SideTableType)
        (receptacleType Sofa_bar__minus_03_dot_66_bar__minus_00_dot_02_bar__plus_00_dot_46 SofaType)
        (receptacleType GarbageCan_bar__minus_00_dot_22_bar__minus_00_dot_01_bar__plus_04_dot_73 GarbageCanType)
        (receptacleType DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45 DiningTableType)
        (receptacleType SideTable_bar__minus_02_dot_56_bar__minus_00_dot_01_bar__plus_04_dot_74 SideTableType)
        (receptacleType CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45 CoffeeTableType)
        (objectType Newspaper_bar__minus_01_dot_02_bar__plus_00_dot_83_bar__plus_01_dot_28 NewspaperType)
        (objectType CreditCard_bar__minus_01_dot_34_bar__plus_00_dot_82_bar__plus_01_dot_03 CreditCardType)
        (objectType Statue_bar__minus_04_dot_06_bar__plus_00_dot_44_bar__plus_02_dot_39 StatueType)
        (objectType RemoteControl_bar__minus_03_dot_91_bar__plus_00_dot_43_bar__plus_02_dot_06 RemoteControlType)
        (objectType CreditCard_bar__minus_04_dot_00_bar__plus_00_dot_64_bar__plus_04_dot_69 CreditCardType)
        (objectType Box_bar__minus_00_dot_86_bar__plus_01_dot_01_bar__plus_01_dot_16 BoxType)
        (objectType Window_bar__minus_06_dot_04_bar__plus_01_dot_53_bar__plus_04_dot_24 WindowType)
        (objectType Television_bar__minus_03_dot_76_bar__plus_01_dot_13_bar__plus_04_dot_58 TelevisionType)
        (objectType Statue_bar__minus_03_dot_83_bar__plus_00_dot_44_bar__plus_02_dot_39 StatueType)
        (objectType KeyChain_bar__minus_01_dot_49_bar__plus_00_dot_82_bar__plus_01_dot_63 KeyChainType)
        (objectType Statue_bar__minus_04_dot_14_bar__plus_00_dot_44_bar__plus_01_dot_90 StatueType)
        (objectType Laptop_bar__minus_01_dot_08_bar__plus_00_dot_82_bar__plus_01_dot_70 LaptopType)
        (objectType Window_bar__minus_06_dot_04_bar__plus_01_dot_53_bar__plus_00_dot_86 WindowType)
        (objectType Statue_bar__minus_03_dot_68_bar__plus_00_dot_65_bar__plus_04_dot_69 StatueType)
        (objectType Pillow_bar__minus_04_dot_16_bar__plus_00_dot_54_bar__plus_00_dot_58 PillowType)
        (objectType Watch_bar__minus_03_dot_76_bar__plus_00_dot_43_bar__plus_02_dot_55 WatchType)
        (objectType Chair_bar__minus_01_dot_93_bar__minus_00_dot_01_bar__plus_02_dot_41 ChairType)
        (objectType CreditCard_bar__minus_01_dot_82_bar__plus_00_dot_82_bar__plus_01_dot_89 CreditCardType)
        (objectType Laptop_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_19 LaptopType)
        (objectType Vase_bar__minus_03_dot_89_bar__plus_00_dot_64_bar__plus_04_dot_12 VaseType)
        (objectType FloorLamp_bar__minus_05_dot_66_bar__minus_00_dot_01_bar__plus_04_dot_68 FloorLampType)
        (objectType Window_bar__minus_06_dot_04_bar__plus_01_dot_55_bar__plus_02_dot_10 WindowType)
        (objectType Box_bar__minus_05_dot_25_bar__plus_00_dot_67_bar__plus_02_dot_43 BoxType)
        (objectType Newspaper_bar__minus_01_dot_56_bar__plus_00_dot_83_bar__plus_01_dot_30 NewspaperType)
        (objectType Chair_bar__minus_05_dot_15_bar__minus_00_dot_01_bar__plus_00_dot_82 ChairType)
        (objectType RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_82_bar__plus_01_dot_81 RemoteControlType)
        (objectType LightSwitch_bar__plus_00_dot_00_bar__plus_01_dot_29_bar__plus_03_dot_38 LightSwitchType)
        (objectType KeyChain_bar__minus_02_dot_50_bar__plus_00_dot_52_bar__plus_04_dot_63 KeyChainType)
        (objectType Chair_bar__minus_00_dot_67_bar__minus_00_dot_01_bar__plus_00_dot_66 ChairType)
        (objectType HousePlant_bar__minus_05_dot_71_bar__plus_00_dot_58_bar__plus_03_dot_94 HousePlantType)
        (objectType Painting_bar__minus_01_dot_62_bar__plus_01_dot_57_bar_00_dot_00 PaintingType)
        (objectType Chair_bar__minus_00_dot_35_bar__minus_00_dot_01_bar__plus_01_dot_66 ChairType)
        (objectType KeyChain_bar__minus_01_dot_27_bar__plus_00_dot_82_bar__plus_01_dot_39 KeyChainType)
        (objectType Plate_bar__minus_04_dot_06_bar__plus_00_dot_45_bar__plus_02_dot_72 PlateType)
        (objectType Painting_bar__minus_04_dot_16_bar__plus_01_dot_66_bar_00_dot_00 PaintingType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain SideTableType WatchType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PlateType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (canContain GarbageCanType NewspaperType)
        (canContain DiningTableType WatchType)
        (canContain DiningTableType NewspaperType)
        (canContain DiningTableType VaseType)
        (canContain DiningTableType BoxType)
        (canContain DiningTableType KeyChainType)
        (canContain DiningTableType CreditCardType)
        (canContain DiningTableType LaptopType)
        (canContain DiningTableType PlateType)
        (canContain DiningTableType RemoteControlType)
        (canContain DiningTableType StatueType)
        (canContain SideTableType WatchType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PlateType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain CoffeeTableType WatchType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType VaseType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType PlateType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (pickupable Newspaper_bar__minus_01_dot_02_bar__plus_00_dot_83_bar__plus_01_dot_28)
        (pickupable CreditCard_bar__minus_01_dot_34_bar__plus_00_dot_82_bar__plus_01_dot_03)
        (pickupable Statue_bar__minus_04_dot_06_bar__plus_00_dot_44_bar__plus_02_dot_39)
        (pickupable RemoteControl_bar__minus_03_dot_91_bar__plus_00_dot_43_bar__plus_02_dot_06)
        (pickupable CreditCard_bar__minus_04_dot_00_bar__plus_00_dot_64_bar__plus_04_dot_69)
        (pickupable Box_bar__minus_00_dot_86_bar__plus_01_dot_01_bar__plus_01_dot_16)
        (pickupable Statue_bar__minus_03_dot_83_bar__plus_00_dot_44_bar__plus_02_dot_39)
        (pickupable KeyChain_bar__minus_01_dot_49_bar__plus_00_dot_82_bar__plus_01_dot_63)
        (pickupable Statue_bar__minus_04_dot_14_bar__plus_00_dot_44_bar__plus_01_dot_90)
        (pickupable Laptop_bar__minus_01_dot_08_bar__plus_00_dot_82_bar__plus_01_dot_70)
        (pickupable Statue_bar__minus_03_dot_68_bar__plus_00_dot_65_bar__plus_04_dot_69)
        (pickupable Pillow_bar__minus_04_dot_16_bar__plus_00_dot_54_bar__plus_00_dot_58)
        (pickupable Watch_bar__minus_03_dot_76_bar__plus_00_dot_43_bar__plus_02_dot_55)
        (pickupable CreditCard_bar__minus_01_dot_82_bar__plus_00_dot_82_bar__plus_01_dot_89)
        (pickupable Laptop_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_19)
        (pickupable Vase_bar__minus_03_dot_89_bar__plus_00_dot_64_bar__plus_04_dot_12)
        (pickupable Box_bar__minus_05_dot_25_bar__plus_00_dot_67_bar__plus_02_dot_43)
        (pickupable Newspaper_bar__minus_01_dot_56_bar__plus_00_dot_83_bar__plus_01_dot_30)
        (pickupable RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_82_bar__plus_01_dot_81)
        (pickupable KeyChain_bar__minus_02_dot_50_bar__plus_00_dot_52_bar__plus_04_dot_63)
        (pickupable KeyChain_bar__minus_01_dot_27_bar__plus_00_dot_82_bar__plus_01_dot_39)
        (pickupable Plate_bar__minus_04_dot_06_bar__plus_00_dot_45_bar__plus_02_dot_72)
        (isReceptacleObject Box_bar__minus_00_dot_86_bar__plus_01_dot_01_bar__plus_01_dot_16)
        (isReceptacleObject Box_bar__minus_05_dot_25_bar__plus_00_dot_67_bar__plus_02_dot_43)
        (isReceptacleObject Plate_bar__minus_04_dot_06_bar__plus_00_dot_45_bar__plus_02_dot_72)
        (openable Drawer_bar__minus_02_dot_56_bar__plus_00_dot_53_bar__plus_04_dot_48)
        
        (atLocation agent1 loc_bar__minus_7_bar_14_bar_3_bar_30)
        
        (cleanable Plate_bar__minus_04_dot_06_bar__plus_00_dot_45_bar__plus_02_dot_72)
        
        (heatable Plate_bar__minus_04_dot_06_bar__plus_00_dot_45_bar__plus_02_dot_72)
        (coolable Plate_bar__minus_04_dot_06_bar__plus_00_dot_45_bar__plus_02_dot_72)
        
        
        (toggleable FloorLamp_bar__minus_05_dot_66_bar__minus_00_dot_01_bar__plus_04_dot_68)
        
        
        
        
        (inReceptacleObject Newspaper_bar__minus_01_dot_02_bar__plus_00_dot_83_bar__plus_01_dot_28 Box_bar__minus_00_dot_86_bar__plus_01_dot_01_bar__plus_01_dot_16)
        (inReceptacle Pillow_bar__minus_04_dot_16_bar__plus_00_dot_54_bar__plus_00_dot_58 Sofa_bar__minus_03_dot_66_bar__minus_00_dot_02_bar__plus_00_dot_46)
        (inReceptacle Box_bar__minus_00_dot_86_bar__plus_01_dot_01_bar__plus_01_dot_16 DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45)
        (inReceptacle Newspaper_bar__minus_01_dot_02_bar__plus_00_dot_83_bar__plus_01_dot_28 DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45)
        (inReceptacle Laptop_bar__minus_01_dot_08_bar__plus_00_dot_82_bar__plus_01_dot_70 DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45)
        (inReceptacle Newspaper_bar__minus_01_dot_56_bar__plus_00_dot_83_bar__plus_01_dot_30 DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45)
        (inReceptacle CreditCard_bar__minus_01_dot_34_bar__plus_00_dot_82_bar__plus_01_dot_03 DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45)
        (inReceptacle RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_82_bar__plus_01_dot_81 DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45)
        (inReceptacle CreditCard_bar__minus_01_dot_82_bar__plus_00_dot_82_bar__plus_01_dot_89 DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45)
        (inReceptacle Television_bar__minus_03_dot_76_bar__plus_01_dot_13_bar__plus_04_dot_58 SideTable_bar__minus_03_dot_79_bar__minus_00_dot_01_bar__plus_04_dot_46)
        (inReceptacle Vase_bar__minus_03_dot_89_bar__plus_00_dot_64_bar__plus_04_dot_12 SideTable_bar__minus_03_dot_79_bar__minus_00_dot_01_bar__plus_04_dot_46)
        (inReceptacle CreditCard_bar__minus_04_dot_00_bar__plus_00_dot_64_bar__plus_04_dot_69 SideTable_bar__minus_03_dot_79_bar__minus_00_dot_01_bar__plus_04_dot_46)
        (inReceptacle Statue_bar__minus_03_dot_68_bar__plus_00_dot_65_bar__plus_04_dot_69 SideTable_bar__minus_03_dot_79_bar__minus_00_dot_01_bar__plus_04_dot_46)
        (inReceptacle Statue_bar__minus_03_dot_83_bar__plus_00_dot_44_bar__plus_02_dot_39 CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45)
        (inReceptacle Statue_bar__minus_04_dot_14_bar__plus_00_dot_44_bar__plus_01_dot_90 CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45)
        (inReceptacle Plate_bar__minus_04_dot_06_bar__plus_00_dot_45_bar__plus_02_dot_72 CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45)
        (inReceptacle Statue_bar__minus_04_dot_06_bar__plus_00_dot_44_bar__plus_02_dot_39 CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45)
        (inReceptacle RemoteControl_bar__minus_03_dot_91_bar__plus_00_dot_43_bar__plus_02_dot_06 CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45)
        (inReceptacle Watch_bar__minus_03_dot_76_bar__plus_00_dot_43_bar__plus_02_dot_55 CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45)
        (inReceptacle KeyChain_bar__minus_02_dot_50_bar__plus_00_dot_52_bar__plus_04_dot_63 Drawer_bar__minus_02_dot_56_bar__plus_00_dot_53_bar__plus_04_dot_48)
        (inReceptacle Box_bar__minus_05_dot_25_bar__plus_00_dot_67_bar__plus_02_dot_43 Sofa_bar__minus_05_dot_40_bar__minus_00_dot_02_bar__plus_02_dot_43)
        (inReceptacle Laptop_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_19 Sofa_bar__minus_05_dot_40_bar__minus_00_dot_02_bar__plus_02_dot_43)
        
        
        (receptacleAtLocation CoffeeTable_bar__minus_04_dot_00_bar__plus_00_dot_01_bar__plus_02_dot_45 loc_bar__minus_11_bar_9_bar_3_bar_45)
        (receptacleAtLocation DiningTable_bar__minus_01_dot_40_bar__plus_00_dot_00_bar__plus_01_dot_45 loc_bar__minus_10_bar_6_bar_1_bar_60)
        (receptacleAtLocation Drawer_bar__minus_02_dot_56_bar__plus_00_dot_53_bar__plus_04_dot_48 loc_bar__minus_12_bar_15_bar_0_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_00_dot_22_bar__minus_00_dot_01_bar__plus_04_dot_73 loc_bar__minus_5_bar_17_bar_1_bar_60)
        (receptacleAtLocation SideTable_bar__minus_02_dot_56_bar__minus_00_dot_01_bar__plus_04_dot_74 loc_bar__minus_6_bar_17_bar_3_bar_60)
        (receptacleAtLocation SideTable_bar__minus_03_dot_79_bar__minus_00_dot_01_bar__plus_04_dot_46 loc_bar__minus_19_bar_18_bar_1_bar_60)
        (receptacleAtLocation Sofa_bar__minus_03_dot_66_bar__minus_00_dot_02_bar__plus_00_dot_46 loc_bar__minus_13_bar_7_bar_2_bar_45)
        (receptacleAtLocation Sofa_bar__minus_05_dot_40_bar__minus_00_dot_02_bar__plus_02_dot_43 loc_bar__minus_18_bar_10_bar_3_bar_60)
        (objectAtLocation Statue_bar__minus_03_dot_83_bar__plus_00_dot_44_bar__plus_02_dot_39 loc_bar__minus_11_bar_9_bar_3_bar_45)
        (objectAtLocation Laptop_bar__minus_05_dot_25_bar__plus_00_dot_48_bar__plus_02_dot_19 loc_bar__minus_18_bar_10_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__minus_01_dot_58_bar__plus_00_dot_82_bar__plus_01_dot_81 loc_bar__minus_10_bar_6_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__minus_04_dot_00_bar__plus_00_dot_64_bar__plus_04_dot_69 loc_bar__minus_19_bar_18_bar_1_bar_60)
        (objectAtLocation Newspaper_bar__minus_01_dot_02_bar__plus_00_dot_83_bar__plus_01_dot_28 loc_bar__minus_10_bar_6_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__minus_01_dot_49_bar__plus_00_dot_82_bar__plus_01_dot_63 loc_bar__minus_5_bar_10_bar_2_bar_45)
        (objectAtLocation Box_bar__minus_00_dot_86_bar__plus_01_dot_01_bar__plus_01_dot_16 loc_bar__minus_10_bar_6_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_34_bar__plus_00_dot_82_bar__plus_01_dot_03 loc_bar__minus_10_bar_6_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__minus_02_dot_50_bar__plus_00_dot_52_bar__plus_04_dot_63 loc_bar__minus_12_bar_15_bar_0_bar_60)
        (objectAtLocation Statue_bar__minus_03_dot_68_bar__plus_00_dot_65_bar__plus_04_dot_69 loc_bar__minus_19_bar_18_bar_1_bar_60)
        (objectAtLocation Statue_bar__minus_04_dot_06_bar__plus_00_dot_44_bar__plus_02_dot_39 loc_bar__minus_11_bar_9_bar_3_bar_45)
        (objectAtLocation Box_bar__minus_05_dot_25_bar__plus_00_dot_67_bar__plus_02_dot_43 loc_bar__minus_18_bar_10_bar_3_bar_60)
        (objectAtLocation Chair_bar__minus_01_dot_93_bar__minus_00_dot_01_bar__plus_02_dot_41 loc_bar__minus_8_bar_12_bar_2_bar_60)
        (objectAtLocation Chair_bar__minus_05_dot_15_bar__minus_00_dot_01_bar__plus_00_dot_82 loc_bar__minus_19_bar_5_bar_2_bar_60)
        (objectAtLocation Chair_bar__minus_00_dot_35_bar__minus_00_dot_01_bar__plus_01_dot_66 loc_bar__minus_2_bar_9_bar_2_bar_60)
        (objectAtLocation Chair_bar__minus_00_dot_67_bar__minus_00_dot_01_bar__plus_00_dot_66 loc_bar__minus_3_bar_9_bar_2_bar_45)
        (objectAtLocation KeyChain_bar__minus_01_dot_27_bar__plus_00_dot_82_bar__plus_01_dot_39 loc_bar__minus_5_bar_10_bar_2_bar_30)
        (objectAtLocation Television_bar__minus_03_dot_76_bar__plus_01_dot_13_bar__plus_04_dot_58 loc_bar__minus_19_bar_18_bar_1_bar_60)
        (objectAtLocation Newspaper_bar__minus_01_dot_56_bar__plus_00_dot_83_bar__plus_01_dot_30 loc_bar__minus_10_bar_6_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_82_bar__plus_00_dot_82_bar__plus_01_dot_89 loc_bar__minus_10_bar_6_bar_1_bar_60)
        (objectAtLocation RemoteControl_bar__minus_03_dot_91_bar__plus_00_dot_43_bar__plus_02_dot_06 loc_bar__minus_11_bar_9_bar_3_bar_45)
        (objectAtLocation FloorLamp_bar__minus_05_dot_66_bar__minus_00_dot_01_bar__plus_04_dot_68 loc_bar__minus_20_bar_18_bar_3_bar_60)
        (objectAtLocation HousePlant_bar__minus_05_dot_71_bar__plus_00_dot_58_bar__plus_03_dot_94 loc_bar__minus_20_bar_16_bar_3_bar_60)
        (objectAtLocation Laptop_bar__minus_01_dot_08_bar__plus_00_dot_82_bar__plus_01_dot_70 loc_bar__minus_10_bar_6_bar_1_bar_60)
        (objectAtLocation Plate_bar__minus_04_dot_06_bar__plus_00_dot_45_bar__plus_02_dot_72 loc_bar__minus_11_bar_9_bar_3_bar_45)
        (objectAtLocation LightSwitch_bar__plus_00_dot_00_bar__plus_01_dot_29_bar__plus_03_dot_38 loc_bar__minus_2_bar_14_bar_1_bar_30)
        (objectAtLocation Pillow_bar__minus_04_dot_16_bar__plus_00_dot_54_bar__plus_00_dot_58 loc_bar__minus_13_bar_7_bar_2_bar_45)
        (objectAtLocation Statue_bar__minus_04_dot_14_bar__plus_00_dot_44_bar__plus_01_dot_90 loc_bar__minus_11_bar_9_bar_3_bar_45)
        (objectAtLocation Painting_bar__minus_04_dot_16_bar__plus_01_dot_66_bar_00_dot_00 loc_bar__minus_17_bar_5_bar_2_bar_0)
        (objectAtLocation Painting_bar__minus_01_dot_62_bar__plus_01_dot_57_bar_00_dot_00 loc_bar__minus_10_bar_5_bar_2_bar_0)
        (objectAtLocation Vase_bar__minus_03_dot_89_bar__plus_00_dot_64_bar__plus_04_dot_12 loc_bar__minus_19_bar_18_bar_1_bar_60)
        (objectAtLocation Watch_bar__minus_03_dot_76_bar__plus_00_dot_43_bar__plus_02_dot_55 loc_bar__minus_11_bar_9_bar_3_bar_45)
        (objectAtLocation Window_bar__minus_06_dot_04_bar__plus_01_dot_55_bar__plus_02_dot_10 loc_bar__minus_19_bar_8_bar_3_bar_0)
        (objectAtLocation Window_bar__minus_06_dot_04_bar__plus_01_dot_53_bar__plus_00_dot_86 loc_bar__minus_19_bar_5_bar_3_bar_0)
        (objectAtLocation Window_bar__minus_06_dot_04_bar__plus_01_dot_53_bar__plus_04_dot_24 loc_bar__minus_20_bar_17_bar_3_bar_0)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 StatueType)
                                    (receptacleType ?r DiningTableType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 StatueType)
                                            (receptacleType ?r DiningTableType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            