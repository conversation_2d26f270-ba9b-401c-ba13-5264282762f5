{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 40}, {"high_idx": 5, "image_name": "000000257.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000258.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000259.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000266.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 43}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|3|10|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [5.77032, 5.77032, 10.1014738, 10.1014738, 3.548714636, 3.548714636]], "coordinateReceptacleObjectId": ["SinkBasin", [5.5492, 5.5492, 9.356, 9.356, 3.5209168, 3.5209168]], "forceVisible": true, "objectId": "Cup|+01.44|+00.89|+02.53"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|6|2|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|0|7|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [5.77032, 5.77032, 10.1014738, 10.1014738, 3.548714636, 3.548714636]], "coordinateReceptacleObjectId": ["Cabinet", [-3.102313756, -3.102313756, 2.974496604, 2.974496604, 1.69071698, 1.69071698]], "forceVisible": true, "objectId": "Cup|+01.44|+00.89|+02.53", "receptacleObjectId": "Cabinet|-00.78|+00.42|+00.74"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+01.44|+00.89|+02.53"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [125, 106, 159, 156], "mask": [[31637, 12], [31933, 19], [32231, 23], [32529, 27], [32828, 29], [33127, 31], [33426, 33], [33726, 33], [34025, 35], [34325, 35], [34625, 35], [34925, 35], [35225, 35], [35525, 35], [35825, 34], [36126, 33], [36426, 33], [36726, 33], [37026, 32], [37327, 31], [37627, 31], [37927, 31], [38228, 30], [38528, 29], [38828, 29], [39128, 29], [39429, 28], [39729, 28], [40029, 27], [40329, 27], [40630, 26], [40930, 26], [41230, 25], [41530, 25], [41831, 24], [42131, 24], [42431, 24], [42731, 23], [43032, 22], [43332, 22], [43632, 22], [43933, 20], [44233, 20], [44534, 18], [44834, 18], [45134, 18], [45435, 17], [45735, 16], [46036, 14], [46338, 10], [46641, 4]], "point": [142, 130]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+01.44|+00.89|+02.53", "placeStationary": true, "receptacleObjectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 171], [26788, 172], [27087, 173], [27387, 173], [27686, 173], [27985, 174], [28285, 174], [28584, 175], [28883, 176], [29183, 175], [29482, 176], [29781, 177], [30081, 177], [30380, 177], [30679, 178], [30978, 179], [31278, 179], [31577, 180], [31876, 180], [32176, 180], [32475, 181], [32775, 181], [33075, 180], [33375, 180], [33675, 180], [33975, 180], [34275, 180], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 25], [51084, 24], [51384, 22], [51684, 21], [51984, 20], [52285, 18], [52585, 18], [52885, 17], [53185, 16], [53485, 16], [53785, 15], [54085, 14], [54386, 12], [54686, 12], [54986, 11], [55286, 10], [55586, 10], [55886, 10], [56187, 8], [56487, 8], [56787, 8], [57087, 7], [57387, 7], [57687, 7], [57987, 6], [58288, 5], [58588, 5], [58888, 5], [59188, 4]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 41], [23553, 109], [23795, 39], [23856, 106], [24094, 39], [24157, 105], [24394, 38], [24458, 104], [24693, 39], [24758, 104], [24992, 41], [25057, 104], [25292, 41], [25357, 104], [25591, 42], [25657, 104], [25890, 43], [25957, 104], [26190, 43], [26257, 103], [26489, 44], [26557, 103], [26788, 46], [26856, 104], [27087, 47], [27156, 104], [27387, 47], [27456, 104], [27686, 48], [27756, 103], [27985, 49], [28056, 103], [28285, 49], [28356, 103], [28584, 51], [28656, 103], [28883, 52], [28955, 104], [29183, 52], [29255, 103], [29482, 53], [29555, 103], [29781, 54], [29855, 103], [30081, 54], [30155, 103], [30380, 56], [30455, 102], [30679, 57], [30754, 103], [30978, 58], [31054, 103], [31278, 58], [31354, 103], [31577, 59], [31654, 103], [31876, 60], [31954, 102], [32176, 61], [32254, 102], [32475, 62], [32554, 102], [32775, 62], [32853, 103], [33075, 63], [33153, 102], [33375, 63], [33453, 102], [33675, 63], [33752, 103], [33975, 63], [34052, 103], [34275, 64], [34352, 103], [34576, 64], [34650, 104], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 32], [46281, 32], [46582, 31], [46882, 31], [47182, 31], [47482, 30], [47782, 30], [48082, 30], [48383, 29], [48683, 28], [48983, 28], [49283, 28], [49583, 28], [49883, 27], [50183, 27], [50484, 26], [50784, 26], [51084, 25], [51384, 25], [51684, 25], [51984, 25], [52285, 23], [52585, 23], [52885, 23], [53185, 23], [53485, 23], [53785, 22], [54085, 22], [54386, 21], [54686, 21], [54986, 20], [55286, 20], [55586, 20], [55886, 20], [56187, 18], [56487, 19], [56787, 21], [57087, 22], [57387, 17], [57406, 3], [57687, 17], [57706, 3], [57987, 17], [58006, 3], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [0.434951068, 0.434951068, 1.052, 1.052, 3.926775456, 3.926775456]], "forceVisible": true, "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [102, 55, 264, 137], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21102, 162], [21402, 162], [21702, 162], [22002, 161], [22302, 161], [22602, 161], [22903, 160], [23203, 160], [23503, 159], [23803, 159], [24103, 159], [24403, 159], [24703, 159], [25003, 158], [25303, 158], [25603, 158], [25903, 158], [26204, 156], [26504, 156], [26804, 156], [27104, 156], [27404, 156], [27704, 155], [28004, 155], [28304, 155], [28604, 155], [28904, 155], [29204, 154], [29505, 153], [29805, 153], [30105, 153], [30405, 152], [30705, 152], [31005, 152], [31305, 152], [31605, 152], [31905, 151], [32205, 151], [32505, 151], [32806, 150], [33106, 149], [33406, 149], [33706, 149], [34006, 149], [34306, 149], [34606, 148], [34906, 148], [35206, 148], [35506, 148], [35806, 148], [36107, 146], [36407, 146], [36707, 146], [37007, 146], [37307, 145], [37607, 145], [37907, 145], [38207, 145], [38507, 145], [38807, 144], [39108, 143], [39408, 143], [39708, 143], [40015, 131], [40315, 131], [40615, 130], [40915, 130]], "point": [183, 95]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+01.44|+00.89|+02.53"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [132, 79, 157, 116], "mask": [[23537, 16], [23834, 22], [24133, 24], [24432, 26], [24732, 26], [25033, 24], [25333, 24], [25633, 24], [25933, 24], [26233, 24], [26533, 24], [26834, 22], [27134, 22], [27434, 22], [27734, 22], [28034, 22], [28334, 22], [28635, 21], [28935, 20], [29235, 20], [29535, 20], [29835, 20], [30135, 20], [30436, 19], [30736, 18], [31036, 18], [31336, 18], [31636, 18], [31936, 18], [32237, 17], [32537, 17], [32837, 16], [33138, 15], [33438, 15], [33738, 14], [34038, 14], [34339, 13], [34640, 10]], "point": [144, 96]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+00.11|+00.98|+00.26"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [75, 55, 264, 198], "mask": [[16310, 8], [16320, 103], [16437, 8], [16610, 136], [16909, 139], [17208, 142], [17507, 145], [17807, 146], [18106, 149], [18405, 152], [18704, 155], [19004, 156], [19303, 159], [19602, 162], [19902, 163], [20202, 163], [20502, 163], [20802, 162], [21101, 163], [21401, 163], [21700, 164], [21999, 164], [22298, 165], [22598, 165], [22897, 166], [23196, 167], [23496, 166], [23795, 167], [24094, 168], [24394, 168], [24693, 169], [24992, 169], [25292, 169], [25591, 170], [25890, 171], [26190, 170], [26489, 171], [26788, 172], [27087, 173], [27387, 173], [27686, 173], [27985, 174], [28285, 174], [28584, 175], [28883, 176], [29183, 175], [29482, 176], [29781, 177], [30081, 177], [30380, 177], [30679, 178], [30978, 179], [31278, 179], [31577, 180], [31876, 180], [32176, 180], [32475, 181], [32775, 181], [33075, 180], [33375, 180], [33675, 180], [33975, 180], [34275, 180], [34576, 178], [34876, 178], [35176, 178], [35476, 178], [35776, 178], [36076, 177], [36376, 177], [36677, 176], [36977, 176], [37277, 175], [37577, 175], [37877, 175], [38177, 175], [38477, 175], [38778, 173], [39078, 173], [39378, 173], [39678, 173], [39978, 168], [40278, 168], [40579, 39], [40629, 4], [40719, 26], [40879, 39], [41020, 25], [41179, 38], [41479, 38], [41779, 38], [42079, 38], [42379, 37], [42680, 36], [42980, 36], [43280, 36], [43580, 35], [43880, 35], [44180, 35], [44481, 34], [44781, 33], [45081, 33], [45381, 33], [45681, 33], [45981, 31], [46281, 30], [46582, 27], [46882, 26], [47182, 25], [47482, 24], [47782, 23], [48082, 22], [48383, 20], [48683, 19], [48983, 19], [49283, 18], [49583, 18], [49883, 17], [50183, 17], [50484, 15], [50784, 15], [51084, 15], [51384, 15], [51684, 15], [51984, 15], [52285, 13], [52585, 13], [52885, 13], [53185, 13], [53485, 13], [53785, 14], [54085, 14], [54386, 14], [54686, 14], [54986, 14], [55286, 15], [55586, 15], [55886, 16], [56187, 15], [56487, 15], [56787, 16], [57087, 16], [57387, 16], [57687, 17], [57987, 17], [58288, 16], [58588, 15], [58888, 15], [59188, 15]], "point": [169, 125]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.78|+00.42|+00.74"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [184, 128, 299, 198], "mask": [[38287, 1], [38587, 113], [38887, 113], [39186, 114], [39486, 114], [39786, 114], [40086, 114], [40386, 114], [40686, 114], [40986, 113], [41286, 113], [41585, 113], [41885, 113], [42185, 112], [42485, 112], [42785, 111], [43085, 111], [43385, 110], [43684, 110], [43984, 110], [44284, 109], [44584, 109], [44884, 108], [45184, 108], [45484, 107], [45786, 105], [46088, 102], [46389, 101], [46691, 98], [46992, 96], [47293, 95], [47594, 93], [47895, 92], [48196, 90], [48497, 89], [48798, 87], [49098, 87], [49399, 85], [49699, 85], [50000, 83], [50300, 82], [50601, 81], [50901, 80], [51201, 80], [51501, 79], [51801, 79], [52101, 78], [52402, 77], [52702, 76], [53002, 76], [53302, 75], [53602, 74], [53901, 75], [54201, 74], [54500, 75], [54800, 74], [55100, 74], [55399, 74], [55699, 74], [55998, 74], [56298, 74], [56598, 73], [56897, 73], [57197, 73], [57497, 72], [57796, 73], [58096, 72], [58396, 72], [58696, 71], [58995, 72], [59295, 71]], "point": [241, 162]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+01.44|+00.89|+02.53", "placeStationary": true, "receptacleObjectId": "Cabinet|-00.78|+00.42|+00.74"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [186, 129, 299, 290], "mask": [[38590, 110], [38889, 111], [39189, 111], [39489, 110], [39789, 110], [40089, 109], [40389, 108], [40499, 1], [40689, 108], [40799, 1], [40988, 108], [41098, 2], [41288, 108], [41398, 2], [41588, 107], [41697, 3], [41888, 107], [41997, 3], [42188, 106], [42296, 4], [42488, 106], [42595, 5], [42788, 105], [42895, 5], [43087, 106], [43194, 6], [43387, 105], [43494, 6], [43687, 105], [43793, 7], [43987, 104], [44093, 7], [44287, 104], [44392, 8], [44587, 103], [44692, 8], [44887, 102], [44991, 9], [45186, 103], [45291, 9], [45486, 102], [45590, 10], [45786, 102], [45889, 11], [46088, 99], [46189, 11], [46389, 98], [46488, 12], [46691, 95], [46788, 12], [46992, 94], [47087, 13], [47293, 92], [47387, 13], [47594, 91], [47686, 14], [47895, 89], [47986, 14], [48196, 88], [48285, 15], [48497, 86], [48585, 15], [48798, 84], [48884, 16], [49098, 84], [49184, 16], [49399, 82], [49483, 17], [49699, 82], [49782, 18], [50000, 80], [50082, 18], [50300, 80], [50381, 19], [50601, 78], [50681, 19], [50901, 78], [50980, 20], [51201, 77], [51280, 20], [51501, 77], [51579, 21], [51801, 76], [51879, 21], [52101, 76], [52178, 22], [52402, 74], [52478, 22], [52702, 74], [52777, 23], [53002, 73], [53076, 24], [53302, 72], [53376, 24], [53602, 72], [53675, 25], [53901, 72], [53975, 25], [54201, 72], [54274, 26], [54500, 72], [54574, 26], [54800, 72], [54873, 27], [55100, 71], [55173, 27], [55399, 72], [55472, 28], [55699, 71], [55772, 28], [55998, 72], [56071, 29], [56298, 71], [56371, 29], [56598, 71], [56670, 30], [56897, 71], [56969, 31], [57197, 70], [57269, 31], [57497, 70], [57568, 32], [57796, 70], [57868, 32], [58096, 70], [58167, 33], [58396, 69], [58467, 33], [58696, 69], [58766, 34], [59066, 34], [59365, 35], [59665, 35], [59966, 34], [60266, 34], [60566, 34], [60867, 33], [61167, 33], [61468, 32], [61768, 32], [62068, 32], [62369, 31], [62669, 31], [62969, 31], [63270, 30], [63570, 30], [63870, 30], [64171, 29], [64471, 29], [64772, 28], [65072, 28], [65372, 28], [65673, 27], [65973, 27], [66273, 27], [66574, 26], [66874, 26], [67175, 25], [67475, 25], [67775, 25], [68076, 24], [68376, 24], [68676, 24], [68977, 23], [69277, 23], [69578, 22], [69878, 22], [70178, 22], [70479, 21], [70779, 21], [71079, 21], [71380, 20], [71680, 20], [71981, 19], [72281, 19], [72581, 19], [72882, 18], [73182, 18], [73482, 18], [73783, 17], [74083, 17], [74384, 16], [74684, 16], [74984, 16], [75285, 15], [75585, 15], [75885, 15], [76186, 14], [76486, 14], [76787, 13], [77087, 13], [77387, 13], [77688, 12], [77988, 12], [78288, 12], [78589, 11], [78889, 11], [79190, 10], [79490, 10], [79790, 10], [80091, 9], [80391, 9], [80691, 9], [80992, 8], [81292, 8], [81593, 7], [81893, 7], [82193, 7], [82494, 6], [82794, 6], [83094, 6], [83395, 5], [83695, 5], [83996, 4], [84296, 4], [84596, 4], [84897, 3], [85197, 3], [85497, 3], [85798, 2], [86098, 2], [86399, 1], [86699, 1], [86999, 1]], "point": [242, 195]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.78|+00.42|+00.74"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [180, 129, 299, 290], "mask": [[38590, 110], [38889, 111], [39189, 111], [39489, 110], [39789, 110], [40089, 109], [40389, 108], [40499, 1], [40689, 108], [40799, 1], [40988, 108], [41098, 2], [41288, 108], [41398, 2], [41588, 107], [41697, 3], [41888, 107], [41997, 3], [42188, 106], [42296, 4], [42488, 106], [42595, 5], [42788, 105], [42895, 5], [43087, 106], [43194, 6], [43387, 105], [43494, 6], [43687, 105], [43793, 7], [43987, 104], [44093, 7], [44287, 104], [44392, 8], [44587, 103], [44692, 8], [44887, 102], [44991, 9], [45186, 103], [45291, 9], [45486, 102], [45590, 10], [45786, 102], [45889, 11], [46086, 101], [46189, 11], [46386, 101], [46488, 12], [46686, 100], [46788, 12], [46986, 100], [47087, 13], [47285, 100], [47387, 13], [47585, 100], [47686, 14], [47885, 99], [47986, 14], [48185, 99], [48285, 15], [48485, 98], [48585, 15], [48785, 97], [48884, 16], [49085, 27], [49113, 69], [49184, 16], [49384, 24], [49418, 63], [49483, 17], [49684, 23], [49720, 61], [49782, 18], [49984, 22], [50021, 59], [50082, 18], [50284, 21], [50322, 58], [50381, 19], [50584, 21], [50623, 56], [50681, 19], [50884, 21], [50923, 56], [50980, 20], [51184, 21], [51223, 55], [51280, 20], [51483, 22], [51524, 54], [51579, 21], [51783, 22], [51823, 54], [51879, 21], [52083, 22], [52123, 54], [52178, 22], [52383, 22], [52423, 53], [52478, 22], [52683, 22], [52722, 54], [52777, 23], [52983, 22], [53021, 54], [53076, 24], [53283, 22], [53321, 53], [53376, 24], [53582, 23], [53620, 54], [53675, 25], [53882, 23], [53920, 53], [53975, 25], [54182, 23], [54219, 54], [54274, 26], [54482, 23], [54519, 53], [54574, 26], [54782, 23], [54818, 54], [54873, 27], [55082, 23], [55118, 53], [55173, 27], [55382, 23], [55417, 54], [55472, 28], [55682, 23], [55716, 54], [55772, 28], [55981, 25], [56016, 54], [56071, 29], [56281, 25], [56315, 54], [56371, 29], [56581, 27], [56614, 55], [56670, 30], [56881, 87], [56969, 31], [57181, 86], [57269, 31], [57481, 86], [57568, 32], [57781, 85], [57868, 32], [58080, 86], [58167, 33], [58380, 85], [58467, 33], [58680, 85], [58766, 34], [59066, 34], [59365, 35], [59665, 35], [59966, 34], [60266, 34], [60566, 34], [60867, 33], [61167, 33], [61468, 32], [61768, 32], [62068, 32], [62369, 31], [62669, 31], [62969, 31], [63270, 30], [63570, 30], [63870, 30], [64171, 29], [64471, 29], [64772, 28], [65072, 28], [65372, 28], [65673, 27], [65973, 27], [66273, 27], [66574, 26], [66874, 26], [67175, 25], [67475, 25], [67775, 25], [68076, 24], [68376, 24], [68676, 24], [68977, 23], [69277, 23], [69578, 22], [69878, 22], [70178, 22], [70479, 21], [70779, 21], [71079, 21], [71380, 20], [71680, 20], [71981, 19], [72281, 19], [72581, 19], [72882, 18], [73182, 18], [73482, 18], [73783, 17], [74083, 17], [74384, 16], [74684, 16], [74984, 16], [75285, 15], [75585, 15], [75885, 15], [76186, 14], [76486, 14], [76787, 13], [77087, 13], [77387, 13], [77688, 12], [77988, 12], [78288, 12], [78589, 11], [78889, 11], [79190, 10], [79490, 10], [79790, 10], [80091, 9], [80391, 9], [80691, 9], [80992, 8], [81292, 8], [81593, 7], [81893, 7], [82193, 7], [82494, 6], [82794, 6], [83094, 6], [83395, 5], [83695, 5], [83996, 4], [84296, 4], [84596, 4], [84897, 3], [85197, 3], [85497, 3], [85798, 2], [86098, 2], [86399, 1], [86699, 1], [86999, 1]], "point": [239, 195]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan18", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.25, "y": 0.900999844, "z": 1.5}, "object_poses": [{"objectName": "DishSponge_6d7ef995", "position": {"x": 1.13579154, "y": 0.9922643, "z": 3.04420853}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 1.58735752, "y": 1.57565, "z": 4.63338947}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": -0.09667797, "y": 0.760130465, "z": 6.1691184}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_73d89a5b", "position": {"x": 1.317894, "y": 0.988599956, "z": 4.2942}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_cefc2847", "position": {"x": -0.6172804, "y": 0.7725101, "z": 0.45141685}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": -0.5317665, "y": 0.810111, "z": 6.763239}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": -0.158254743, "y": 0.810111, "z": 6.291539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_8e8a26e1", "position": {"x": 1.57739961, "y": 2.20325, "z": 3.79425335}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 0.674817264, "y": 0.144326568, "z": 0.418435961}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": 0.7357707, "y": 0.802122533, "z": 0.5071131}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": 1.31450915, "y": 1.02240038, "z": 3.23104}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 0.9233889, "y": 0.992762148, "z": 0.6700103}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 1.15505934, "y": 0.140689611, "z": 3.365652}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_9f9e1c57", "position": {"x": 1.49008977, "y": 0.139665365, "z": 2.79405117}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 0.04679808, "y": 1.57459259, "z": 0.163176268}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 1.13578975, "y": 0.987700045, "z": 4.624859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": -0.3991838, "y": 0.84850204, "z": 6.89582157}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": 0.3052411, "y": 0.7550884, "z": 6.666539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": 1.20079792, "y": 1.57459259, "z": 0.251273155}, "rotation": {"x": 0.0, "y": 0.0, "z": -1.70754731e-06}}, {"objectName": "Apple_a939ee5b", "position": {"x": -0.620154858, "y": 0.8346215, "z": 6.67485046}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Bowl_9f9e1c57", "position": {"x": 0.147051811, "y": 0.759126246, "z": 6.34958649}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Bread_b7525b79", "position": {"x": 1.49322724, "y": 1.06230986, "z": 0.4407184}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_8021024b", "position": {"x": 0.3052411, "y": 0.810111, "z": 6.416539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_94d26365", "position": {"x": -0.6113827, "y": 1.02240038, "z": 0.5784087}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_8ae143f8", "position": {"x": -0.09667797, "y": 0.7606369, "z": 6.941612}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_a939ee5b", "position": {"x": -0.0341779739, "y": 0.8346215, "z": 6.63261461}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_5453e71d", "position": {"x": -0.377019346, "y": 0.7611296, "z": 6.261198}, "rotation": {"x": 0.0, "y": 225.000046, "z": 0.0}}, {"objectName": "Plate_1f77d579", "position": {"x": 1.49008977, "y": 0.141221583, "z": 3.2917285}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b7138966", "position": {"x": 1.13578975, "y": 0.987700045, "z": 4.853961}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6d4d89ae", "position": {"x": -0.0341779739, "y": 0.848502, "z": 6.787113}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_33d3f1ac", "position": {"x": 1.13578975, "y": 0.9927622, "z": 4.76232}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_925e7ba3", "position": {"x": 1.49786007, "y": 0.906034, "z": 2.62796783}, "rotation": {"x": 1.40334191e-14, "y": -9.659347e-06, "z": -2.25533177e-21}}, {"objectName": "PaperTowelRoll_bd2376ff", "position": {"x": 0.256298929, "y": 0.864373744, "z": 6.24033928}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}, {"objectName": "Pot_73d89a5b", "position": {"x": 1.562, "y": 0.988599956, "z": 3.848}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_5b11696a", "position": {"x": 2.09459472, "y": 1.55049694, "z": 5.626}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_8d8b47bb", "position": {"x": -0.413737565, "y": 0.7714228, "z": 0.5639391}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_cff2324e", "position": {"x": 0.6045078, "y": 0.138732433, "z": 0.6552941}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_08687688", "position": {"x": 0.6962644, "y": 0.9908586, "z": 0.486809283}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_8b4d7f57", "position": {"x": -0.308550477, "y": 1.04216564, "z": 0.120408714}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_6d7ef995", "position": {"x": 1.2923851, "y": 0.7713862, "z": 3.429143}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_8e8a26e1", "position": {"x": 1.44258, "y": 0.887178659, "z": 2.52536845}, "rotation": {"x": 2.27491e-21, "y": 89.99999, "z": 1.40334183e-14}}, {"objectName": "Spoon_cefc2847", "position": {"x": -0.467251956, "y": 0.7612178, "z": 6.541539}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_6d84c70f", "position": {"x": -0.378325045, "y": 0.7550884, "z": 6.698186}, "rotation": {"x": 0.0, "y": 134.999985, "z": 0.0}}], "object_toggles": [], "random_seed": 2868589554, "scene_num": 18}, "task_id": "trial_T20190907_150654_710990", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AJQGWGESKQT4Y_3F6KKYWMNEIM3JLD1NWG9ADLGBJDNM", "high_descs": ["Turn around and stand in front of the sink.", "Pick the glass up out of the sink.", "Go to the right and stand in front of the microwave.", "Put the glass in the microwave and shut the door and then open the door and get the glass out and shut the door again.", "Move to the right and face the lower cabinets to the right of the microwave.", "Open the furthest bottom, right cabinet and put the glass in and shut the door."], "task_desc": "Put a warmed glass in the cabinet.", "votes": [1, 1]}, {"assignment_id": "ACSS93E03ZUGX_3NLZY2D53S6Q4SMXUWMLWHGVUVNQLH", "high_descs": ["Turn around and face the sink.", "Pick the cup up from the sink.", "Turn around and go to the left to face the microwave.", "Open the microwave, place the cup in to the microwave, wait a few minutes, open the microwave, pick up the cup, and close the microwave.", "Move lower and open the cabinet below the microwave.", "Place the cup into the cabinet."], "task_desc": "Place the microwaved liquid in the cup in the cabinet.", "votes": [1, 1]}, {"assignment_id": "AWEPZMK7825JQ_3YJ6NA41JEXZ9QXIRYVJGXDTLAWJPH", "high_descs": ["Turn right, turn right again and walk to the sink.", "Pick up the cup that is inside the sink. ", "Turn around and then turn left and walk toward the microwave.", "Heat the cup in the microwave for seven seconds.", "Turn around and take a step back, then turn back around. ", "Place the cup inside of the cupboard at the bottom right. "], "task_desc": "Place a microwaved cup in the cupboard.", "votes": [1, 1]}]}}