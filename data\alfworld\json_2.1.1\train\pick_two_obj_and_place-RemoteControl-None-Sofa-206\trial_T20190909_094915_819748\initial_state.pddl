
(define (problem plan_trial_T20190909_094915_819748)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON>haker - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Bowl_bar__minus_01_dot_68_bar__plus_00_dot_17_bar__minus_01_dot_16 - object
        Bowl_bar__minus_01_dot_81_bar__plus_00_dot_76_bar__minus_01_dot_53 - object
        Bowl_bar__minus_01_dot_87_bar__plus_00_dot_76_bar__minus_01_dot_76 - object
        Box_bar__plus_00_dot_35_bar__plus_00_dot_69_bar__plus_00_dot_91 - object
        CreditCard_bar__plus_02_dot_15_bar__plus_00_dot_52_bar__minus_02_dot_06 - object
        CreditCard_bar__minus_00_dot_31_bar__plus_00_dot_55_bar__plus_00_dot_91 - object
        CreditCard_bar__minus_01_dot_94_bar__plus_00_dot_76_bar__minus_01_dot_06 - object
        FloorLamp_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_30 - object
        HousePlant_bar__minus_01_dot_65_bar__plus_00_dot_75_bar__minus_01_dot_79 - object
        KeyChain_bar__minus_00_dot_45_bar__plus_00_dot_76_bar__minus_02_dot_40 - object
        Laptop_bar__minus_01_dot_73_bar__plus_00_dot_75_bar__minus_00_dot_70 - object
        LightSwitch_bar__plus_01_dot_41_bar__plus_01_dot_29_bar__plus_01_dot_65 - object
        Painting_bar__plus_00_dot_17_bar__plus_01_dot_78_bar__plus_01_dot_93 - object
        Pillow_bar__minus_00_dot_50_bar__plus_00_dot_59_bar__plus_00_dot_96 - object
        RemoteControl_bar__minus_00_dot_27_bar__plus_00_dot_48_bar__minus_01_dot_00 - object
        RemoteControl_bar__minus_00_dot_42_bar__plus_00_dot_48_bar__minus_01_dot_00 - object
        Statue_bar__plus_00_dot_04_bar__plus_00_dot_48_bar__minus_01_dot_18 - object
        Statue_bar__plus_00_dot_34_bar__plus_00_dot_58_bar__minus_01_dot_12 - object
        Statue_bar__minus_00_dot_12_bar__plus_00_dot_49_bar__minus_00_dot_93 - object
        Television_bar__minus_00_dot_01_bar__plus_01_dot_23_bar__minus_02_dot_44 - object
        Vase_bar__minus_00_dot_12_bar__plus_00_dot_48_bar__minus_01_dot_06 - object
        Vase_bar__minus_00_dot_27_bar__plus_00_dot_48_bar__minus_01_dot_25 - object
        Vase_bar__minus_01_dot_59_bar__plus_00_dot_17_bar__minus_00_dot_53 - object
        Vase_bar__minus_01_dot_81_bar__plus_00_dot_77_bar__minus_01_dot_29 - object
        Window_bar__plus_00_dot_79_bar__plus_01_dot_54_bar__minus_02_dot_72 - object
        Window_bar__minus_00_dot_82_bar__plus_01_dot_54_bar__minus_02_dot_72 - object
        ArmChair_bar__plus_02_dot_29_bar__plus_00_dot_01_bar__minus_02_dot_02 - receptacle
        CoffeeTable_bar__plus_00_dot_02_bar_00_dot_00_bar__minus_01_dot_11 - receptacle
        GarbageCan_bar__plus_00_dot_74_bar__plus_00_dot_00_bar__minus_02_dot_53 - receptacle
        Shelf_bar__plus_00_dot_36_bar__plus_00_dot_17_bar__minus_02_dot_24 - receptacle
        Shelf_bar__plus_00_dot_36_bar__plus_00_dot_41_bar__minus_02_dot_24 - receptacle
        Shelf_bar__plus_00_dot_36_bar__plus_00_dot_56_bar__minus_02_dot_28 - receptacle
        Shelf_bar__minus_00_dot_01_bar__plus_00_dot_17_bar__minus_02_dot_39 - receptacle
        Shelf_bar__minus_00_dot_38_bar__plus_00_dot_17_bar__minus_02_dot_23 - receptacle
        Shelf_bar__minus_00_dot_38_bar__plus_00_dot_47_bar__minus_02_dot_25 - receptacle
        Shelf_bar__minus_01_dot_63_bar__plus_00_dot_16_bar__minus_01_dot_62 - receptacle
        Shelf_bar__minus_01_dot_64_bar__plus_00_dot_41_bar__minus_01_dot_62 - receptacle
        Shelf_bar__minus_01_dot_64_bar__plus_00_dot_47_bar__minus_00_dot_46 - receptacle
        Shelf_bar__minus_01_dot_64_bar__plus_00_dot_56_bar__minus_01_dot_62 - receptacle
        Shelf_bar__minus_01_dot_66_bar__plus_00_dot_16_bar__minus_00_dot_46 - receptacle
        Shelf_bar__minus_01_dot_74_bar__plus_00_dot_16_bar__minus_01_dot_05 - receptacle
        Shelf_bar__minus_01_dot_83_bar__plus_01_dot_32_bar__minus_02_dot_41 - receptacle
        Shelf_bar__minus_01_dot_83_bar__plus_01_dot_61_bar__minus_02_dot_41 - receptacle
        Sofa_bar__plus_00_dot_13_bar__plus_00_dot_00_bar__plus_01_dot_19 - receptacle
        TVStand_bar__plus_00_dot_00_bar__plus_00_dot_00_bar__minus_02_dot_39 - receptacle
        TVStand_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__minus_01_dot_06 - receptacle
        loc_bar_4_bar__minus_6_bar_3_bar_60 - location
        loc_bar_1_bar__minus_2_bar_0_bar_45 - location
        loc_bar__minus_4_bar__minus_3_bar_3_bar_60 - location
        loc_bar__minus_3_bar__minus_6_bar_2_bar_60 - location
        loc_bar_5_bar__minus_8_bar_1_bar_60 - location
        loc_bar__minus_2_bar__minus_7_bar_3_bar_45 - location
        loc_bar__minus_1_bar__minus_7_bar_3_bar_45 - location
        loc_bar_4_bar__minus_5_bar_2_bar_60 - location
        loc_bar__minus_3_bar__minus_7_bar_3_bar_0 - location
        loc_bar__minus_4_bar__minus_5_bar_2_bar_60 - location
        loc_bar_5_bar__minus_9_bar_3_bar_60 - location
        loc_bar__minus_3_bar_0_bar_3_bar_60 - location
        loc_bar__minus_3_bar__minus_3_bar_3_bar_60 - location
        loc_bar__minus_4_bar__minus_8_bar_2_bar_15 - location
        loc_bar__minus_4_bar__minus_8_bar_3_bar_15 - location
        loc_bar_4_bar__minus_5_bar_2_bar_45 - location
        loc_bar_4_bar__minus_4_bar_2_bar_45 - location
        loc_bar__minus_3_bar__minus_7_bar_3_bar_60 - location
        loc_bar_7_bar_4_bar_0_bar_30 - location
        loc_bar__minus_2_bar_0_bar_3_bar_45 - location
        loc_bar_5_bar__minus_6_bar_3_bar_60 - location
        loc_bar_1_bar_1_bar_0_bar_0 - location
        loc_bar__minus_6_bar_2_bar_0_bar_60 - location
        loc_bar_5_bar__minus_9_bar_2_bar_15 - location
        loc_bar__minus_1_bar__minus_7_bar_0_bar_30 - location
        )
    

(:init


        (receptacleType Shelf_bar__plus_00_dot_36_bar__plus_00_dot_41_bar__minus_02_dot_24 ShelfType)
        (receptacleType TVStand_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__minus_01_dot_06 TVStandType)
        (receptacleType Shelf_bar__minus_01_dot_83_bar__plus_01_dot_32_bar__minus_02_dot_41 ShelfType)
        (receptacleType Shelf_bar__minus_00_dot_38_bar__plus_00_dot_17_bar__minus_02_dot_23 ShelfType)
        (receptacleType ArmChair_bar__plus_02_dot_29_bar__plus_00_dot_01_bar__minus_02_dot_02 ArmChairType)
        (receptacleType Shelf_bar__minus_01_dot_63_bar__plus_00_dot_16_bar__minus_01_dot_62 ShelfType)
        (receptacleType Sofa_bar__plus_00_dot_13_bar__plus_00_dot_00_bar__plus_01_dot_19 SofaType)
        (receptacleType CoffeeTable_bar__plus_00_dot_02_bar_00_dot_00_bar__minus_01_dot_11 CoffeeTableType)
        (receptacleType Shelf_bar__minus_01_dot_64_bar__plus_00_dot_47_bar__minus_00_dot_46 ShelfType)
        (receptacleType Shelf_bar__plus_00_dot_36_bar__plus_00_dot_56_bar__minus_02_dot_28 ShelfType)
        (receptacleType Shelf_bar__minus_01_dot_83_bar__plus_01_dot_61_bar__minus_02_dot_41 ShelfType)
        (receptacleType Shelf_bar__plus_00_dot_36_bar__plus_00_dot_17_bar__minus_02_dot_24 ShelfType)
        (receptacleType Shelf_bar__minus_01_dot_64_bar__plus_00_dot_56_bar__minus_01_dot_62 ShelfType)
        (receptacleType Shelf_bar__minus_01_dot_64_bar__plus_00_dot_41_bar__minus_01_dot_62 ShelfType)
        (receptacleType Shelf_bar__minus_00_dot_01_bar__plus_00_dot_17_bar__minus_02_dot_39 ShelfType)
        (receptacleType TVStand_bar__plus_00_dot_00_bar__plus_00_dot_00_bar__minus_02_dot_39 TVStandType)
        (receptacleType Shelf_bar__minus_01_dot_74_bar__plus_00_dot_16_bar__minus_01_dot_05 ShelfType)
        (receptacleType GarbageCan_bar__plus_00_dot_74_bar__plus_00_dot_00_bar__minus_02_dot_53 GarbageCanType)
        (receptacleType Shelf_bar__minus_01_dot_66_bar__plus_00_dot_16_bar__minus_00_dot_46 ShelfType)
        (receptacleType Shelf_bar__minus_00_dot_38_bar__plus_00_dot_47_bar__minus_02_dot_25 ShelfType)
        (objectType HousePlant_bar__minus_01_dot_65_bar__plus_00_dot_75_bar__minus_01_dot_79 HousePlantType)
        (objectType Box_bar__plus_00_dot_35_bar__plus_00_dot_69_bar__plus_00_dot_91 BoxType)
        (objectType CreditCard_bar__minus_00_dot_31_bar__plus_00_dot_55_bar__plus_00_dot_91 CreditCardType)
        (objectType RemoteControl_bar__minus_00_dot_27_bar__plus_00_dot_48_bar__minus_01_dot_00 RemoteControlType)
        (objectType Painting_bar__plus_00_dot_17_bar__plus_01_dot_78_bar__plus_01_dot_93 PaintingType)
        (objectType CreditCard_bar__plus_02_dot_15_bar__plus_00_dot_52_bar__minus_02_dot_06 CreditCardType)
        (objectType Bowl_bar__minus_01_dot_68_bar__plus_00_dot_17_bar__minus_01_dot_16 BowlType)
        (objectType Window_bar__minus_00_dot_82_bar__plus_01_dot_54_bar__minus_02_dot_72 WindowType)
        (objectType Statue_bar__plus_00_dot_34_bar__plus_00_dot_58_bar__minus_01_dot_12 StatueType)
        (objectType Pillow_bar__minus_00_dot_50_bar__plus_00_dot_59_bar__plus_00_dot_96 PillowType)
        (objectType Television_bar__minus_00_dot_01_bar__plus_01_dot_23_bar__minus_02_dot_44 TelevisionType)
        (objectType Bowl_bar__minus_01_dot_87_bar__plus_00_dot_76_bar__minus_01_dot_76 BowlType)
        (objectType Vase_bar__minus_00_dot_12_bar__plus_00_dot_48_bar__minus_01_dot_06 VaseType)
        (objectType Laptop_bar__minus_01_dot_73_bar__plus_00_dot_75_bar__minus_00_dot_70 LaptopType)
        (objectType FloorLamp_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_30 FloorLampType)
        (objectType LightSwitch_bar__plus_01_dot_41_bar__plus_01_dot_29_bar__plus_01_dot_65 LightSwitchType)
        (objectType Vase_bar__minus_01_dot_81_bar__plus_00_dot_77_bar__minus_01_dot_29 VaseType)
        (objectType Window_bar__plus_00_dot_79_bar__plus_01_dot_54_bar__minus_02_dot_72 WindowType)
        (objectType Bowl_bar__minus_01_dot_81_bar__plus_00_dot_76_bar__minus_01_dot_53 BowlType)
        (objectType Statue_bar__minus_00_dot_12_bar__plus_00_dot_49_bar__minus_00_dot_93 StatueType)
        (objectType Statue_bar__plus_00_dot_04_bar__plus_00_dot_48_bar__minus_01_dot_18 StatueType)
        (objectType KeyChain_bar__minus_00_dot_45_bar__plus_00_dot_76_bar__minus_02_dot_40 KeyChainType)
        (objectType CreditCard_bar__minus_01_dot_94_bar__plus_00_dot_76_bar__minus_01_dot_06 CreditCardType)
        (objectType RemoteControl_bar__minus_00_dot_42_bar__plus_00_dot_48_bar__minus_01_dot_00 RemoteControlType)
        (objectType Vase_bar__minus_00_dot_27_bar__plus_00_dot_48_bar__minus_01_dot_25 VaseType)
        (objectType Vase_bar__minus_01_dot_59_bar__plus_00_dot_17_bar__minus_00_dot_53 VaseType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType CreditCardType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType CreditCardType)
        (canContain CoffeeTableType BowlType)
        (canContain CoffeeTableType VaseType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (canContain ShelfType BowlType)
        (canContain ShelfType VaseType)
        (canContain ShelfType BoxType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType RemoteControlType)
        (canContain ShelfType StatueType)
        (pickupable Box_bar__plus_00_dot_35_bar__plus_00_dot_69_bar__plus_00_dot_91)
        (pickupable CreditCard_bar__minus_00_dot_31_bar__plus_00_dot_55_bar__plus_00_dot_91)
        (pickupable RemoteControl_bar__minus_00_dot_27_bar__plus_00_dot_48_bar__minus_01_dot_00)
        (pickupable CreditCard_bar__plus_02_dot_15_bar__plus_00_dot_52_bar__minus_02_dot_06)
        (pickupable Bowl_bar__minus_01_dot_68_bar__plus_00_dot_17_bar__minus_01_dot_16)
        (pickupable Statue_bar__plus_00_dot_34_bar__plus_00_dot_58_bar__minus_01_dot_12)
        (pickupable Pillow_bar__minus_00_dot_50_bar__plus_00_dot_59_bar__plus_00_dot_96)
        (pickupable Bowl_bar__minus_01_dot_87_bar__plus_00_dot_76_bar__minus_01_dot_76)
        (pickupable Vase_bar__minus_00_dot_12_bar__plus_00_dot_48_bar__minus_01_dot_06)
        (pickupable Laptop_bar__minus_01_dot_73_bar__plus_00_dot_75_bar__minus_00_dot_70)
        (pickupable Vase_bar__minus_01_dot_81_bar__plus_00_dot_77_bar__minus_01_dot_29)
        (pickupable Bowl_bar__minus_01_dot_81_bar__plus_00_dot_76_bar__minus_01_dot_53)
        (pickupable Statue_bar__minus_00_dot_12_bar__plus_00_dot_49_bar__minus_00_dot_93)
        (pickupable Statue_bar__plus_00_dot_04_bar__plus_00_dot_48_bar__minus_01_dot_18)
        (pickupable KeyChain_bar__minus_00_dot_45_bar__plus_00_dot_76_bar__minus_02_dot_40)
        (pickupable CreditCard_bar__minus_01_dot_94_bar__plus_00_dot_76_bar__minus_01_dot_06)
        (pickupable RemoteControl_bar__minus_00_dot_42_bar__plus_00_dot_48_bar__minus_01_dot_00)
        (pickupable Vase_bar__minus_00_dot_27_bar__plus_00_dot_48_bar__minus_01_dot_25)
        (pickupable Vase_bar__minus_01_dot_59_bar__plus_00_dot_17_bar__minus_00_dot_53)
        (isReceptacleObject Box_bar__plus_00_dot_35_bar__plus_00_dot_69_bar__plus_00_dot_91)
        (isReceptacleObject Bowl_bar__minus_01_dot_68_bar__plus_00_dot_17_bar__minus_01_dot_16)
        (isReceptacleObject Bowl_bar__minus_01_dot_87_bar__plus_00_dot_76_bar__minus_01_dot_76)
        (isReceptacleObject Bowl_bar__minus_01_dot_81_bar__plus_00_dot_76_bar__minus_01_dot_53)
        
        
        (atLocation agent1 loc_bar__minus_1_bar__minus_7_bar_0_bar_30)
        
        (cleanable Bowl_bar__minus_01_dot_68_bar__plus_00_dot_17_bar__minus_01_dot_16)
        (cleanable Bowl_bar__minus_01_dot_87_bar__plus_00_dot_76_bar__minus_01_dot_76)
        (cleanable Bowl_bar__minus_01_dot_81_bar__plus_00_dot_76_bar__minus_01_dot_53)
        
        
        (coolable Bowl_bar__minus_01_dot_68_bar__plus_00_dot_17_bar__minus_01_dot_16)
        (coolable Bowl_bar__minus_01_dot_87_bar__plus_00_dot_76_bar__minus_01_dot_76)
        (coolable Bowl_bar__minus_01_dot_81_bar__plus_00_dot_76_bar__minus_01_dot_53)
        
        
        (toggleable FloorLamp_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_30)
        
        
        
        
        (inReceptacle HousePlant_bar__minus_01_dot_65_bar__plus_00_dot_75_bar__minus_01_dot_79 TVStand_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__minus_01_dot_06)
        (inReceptacle Vase_bar__minus_01_dot_81_bar__plus_00_dot_77_bar__minus_01_dot_29 TVStand_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__minus_01_dot_06)
        (inReceptacle Bowl_bar__minus_01_dot_87_bar__plus_00_dot_76_bar__minus_01_dot_76 TVStand_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__minus_01_dot_06)
        (inReceptacle Bowl_bar__minus_01_dot_81_bar__plus_00_dot_76_bar__minus_01_dot_53 TVStand_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__minus_01_dot_06)
        (inReceptacle CreditCard_bar__minus_01_dot_94_bar__plus_00_dot_76_bar__minus_01_dot_06 TVStand_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__minus_01_dot_06)
        (inReceptacle Laptop_bar__minus_01_dot_73_bar__plus_00_dot_75_bar__minus_00_dot_70 TVStand_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__minus_01_dot_06)
        (inReceptacle Bowl_bar__minus_01_dot_68_bar__plus_00_dot_17_bar__minus_01_dot_16 Shelf_bar__minus_01_dot_74_bar__plus_00_dot_16_bar__minus_01_dot_05)
        (inReceptacle Statue_bar__minus_00_dot_12_bar__plus_00_dot_49_bar__minus_00_dot_93 CoffeeTable_bar__plus_00_dot_02_bar_00_dot_00_bar__minus_01_dot_11)
        (inReceptacle RemoteControl_bar__minus_00_dot_27_bar__plus_00_dot_48_bar__minus_01_dot_00 CoffeeTable_bar__plus_00_dot_02_bar_00_dot_00_bar__minus_01_dot_11)
        (inReceptacle Vase_bar__minus_00_dot_12_bar__plus_00_dot_48_bar__minus_01_dot_06 CoffeeTable_bar__plus_00_dot_02_bar_00_dot_00_bar__minus_01_dot_11)
        (inReceptacle Statue_bar__plus_00_dot_04_bar__plus_00_dot_48_bar__minus_01_dot_18 CoffeeTable_bar__plus_00_dot_02_bar_00_dot_00_bar__minus_01_dot_11)
        (inReceptacle RemoteControl_bar__minus_00_dot_42_bar__plus_00_dot_48_bar__minus_01_dot_00 CoffeeTable_bar__plus_00_dot_02_bar_00_dot_00_bar__minus_01_dot_11)
        (inReceptacle Vase_bar__minus_00_dot_27_bar__plus_00_dot_48_bar__minus_01_dot_25 CoffeeTable_bar__plus_00_dot_02_bar_00_dot_00_bar__minus_01_dot_11)
        (inReceptacle Statue_bar__plus_00_dot_34_bar__plus_00_dot_58_bar__minus_01_dot_12 CoffeeTable_bar__plus_00_dot_02_bar_00_dot_00_bar__minus_01_dot_11)
        (inReceptacle Vase_bar__minus_01_dot_59_bar__plus_00_dot_17_bar__minus_00_dot_53 Shelf_bar__minus_01_dot_66_bar__plus_00_dot_16_bar__minus_00_dot_46)
        (inReceptacle Box_bar__plus_00_dot_35_bar__plus_00_dot_69_bar__plus_00_dot_91 Sofa_bar__plus_00_dot_13_bar__plus_00_dot_00_bar__plus_01_dot_19)
        (inReceptacle Pillow_bar__minus_00_dot_50_bar__plus_00_dot_59_bar__plus_00_dot_96 Sofa_bar__plus_00_dot_13_bar__plus_00_dot_00_bar__plus_01_dot_19)
        (inReceptacle CreditCard_bar__minus_00_dot_31_bar__plus_00_dot_55_bar__plus_00_dot_91 Sofa_bar__plus_00_dot_13_bar__plus_00_dot_00_bar__plus_01_dot_19)
        (inReceptacle CreditCard_bar__plus_02_dot_15_bar__plus_00_dot_52_bar__minus_02_dot_06 ArmChair_bar__plus_02_dot_29_bar__plus_00_dot_01_bar__minus_02_dot_02)
        (inReceptacle Television_bar__minus_00_dot_01_bar__plus_01_dot_23_bar__minus_02_dot_44 TVStand_bar__plus_00_dot_00_bar__plus_00_dot_00_bar__minus_02_dot_39)
        (inReceptacle KeyChain_bar__minus_00_dot_45_bar__plus_00_dot_76_bar__minus_02_dot_40 TVStand_bar__plus_00_dot_00_bar__plus_00_dot_00_bar__minus_02_dot_39)
        
        
        (receptacleAtLocation ArmChair_bar__plus_02_dot_29_bar__plus_00_dot_01_bar__minus_02_dot_02 loc_bar_5_bar__minus_8_bar_1_bar_60)
        (receptacleAtLocation CoffeeTable_bar__plus_00_dot_02_bar_00_dot_00_bar__minus_01_dot_11 loc_bar_5_bar__minus_6_bar_3_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_00_dot_74_bar__plus_00_dot_00_bar__minus_02_dot_53 loc_bar_5_bar__minus_9_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__plus_00_dot_36_bar__plus_00_dot_17_bar__minus_02_dot_24 loc_bar_4_bar__minus_5_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__plus_00_dot_36_bar__plus_00_dot_41_bar__minus_02_dot_24 loc_bar_4_bar__minus_5_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__plus_00_dot_36_bar__plus_00_dot_56_bar__minus_02_dot_28 loc_bar_4_bar__minus_4_bar_2_bar_45)
        (receptacleAtLocation Shelf_bar__minus_00_dot_01_bar__plus_00_dot_17_bar__minus_02_dot_39 loc_bar_4_bar__minus_6_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__minus_00_dot_38_bar__plus_00_dot_17_bar__minus_02_dot_23 loc_bar__minus_4_bar__minus_5_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__minus_00_dot_38_bar__plus_00_dot_47_bar__minus_02_dot_25 loc_bar__minus_3_bar__minus_6_bar_2_bar_60)
        (receptacleAtLocation Shelf_bar__minus_01_dot_63_bar__plus_00_dot_16_bar__minus_01_dot_62 loc_bar__minus_1_bar__minus_7_bar_3_bar_45)
        (receptacleAtLocation Shelf_bar__minus_01_dot_64_bar__plus_00_dot_41_bar__minus_01_dot_62 loc_bar__minus_3_bar__minus_7_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__minus_01_dot_64_bar__plus_00_dot_47_bar__minus_00_dot_46 loc_bar__minus_3_bar_0_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__minus_01_dot_64_bar__plus_00_dot_56_bar__minus_01_dot_62 loc_bar__minus_2_bar__minus_7_bar_3_bar_45)
        (receptacleAtLocation Shelf_bar__minus_01_dot_66_bar__plus_00_dot_16_bar__minus_00_dot_46 loc_bar__minus_2_bar_0_bar_3_bar_45)
        (receptacleAtLocation Shelf_bar__minus_01_dot_74_bar__plus_00_dot_16_bar__minus_01_dot_05 loc_bar__minus_3_bar__minus_3_bar_3_bar_60)
        (receptacleAtLocation Shelf_bar__minus_01_dot_83_bar__plus_01_dot_32_bar__minus_02_dot_41 loc_bar__minus_4_bar__minus_8_bar_3_bar_15)
        (receptacleAtLocation Shelf_bar__minus_01_dot_83_bar__plus_01_dot_61_bar__minus_02_dot_41 loc_bar__minus_3_bar__minus_7_bar_3_bar_0)
        (receptacleAtLocation Sofa_bar__plus_00_dot_13_bar__plus_00_dot_00_bar__plus_01_dot_19 loc_bar_1_bar__minus_2_bar_0_bar_45)
        (receptacleAtLocation TVStand_bar__plus_00_dot_00_bar__plus_00_dot_00_bar__minus_02_dot_39 loc_bar_5_bar__minus_9_bar_3_bar_60)
        (receptacleAtLocation TVStand_bar__minus_01_dot_74_bar__plus_00_dot_00_bar__minus_01_dot_06 loc_bar__minus_4_bar__minus_3_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__plus_02_dot_15_bar__plus_00_dot_52_bar__minus_02_dot_06 loc_bar_5_bar__minus_8_bar_1_bar_60)
        (objectAtLocation RemoteControl_bar__minus_00_dot_42_bar__plus_00_dot_48_bar__minus_01_dot_00 loc_bar_5_bar__minus_6_bar_3_bar_60)
        (objectAtLocation Bowl_bar__minus_01_dot_87_bar__plus_00_dot_76_bar__minus_01_dot_76 loc_bar__minus_4_bar__minus_3_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__minus_00_dot_31_bar__plus_00_dot_55_bar__plus_00_dot_91 loc_bar_1_bar__minus_2_bar_0_bar_45)
        (objectAtLocation Bowl_bar__minus_01_dot_68_bar__plus_00_dot_17_bar__minus_01_dot_16 loc_bar__minus_3_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Box_bar__plus_00_dot_35_bar__plus_00_dot_69_bar__plus_00_dot_91 loc_bar_1_bar__minus_2_bar_0_bar_45)
        (objectAtLocation Painting_bar__plus_00_dot_17_bar__plus_01_dot_78_bar__plus_01_dot_93 loc_bar_1_bar_1_bar_0_bar_0)
        (objectAtLocation HousePlant_bar__minus_01_dot_65_bar__plus_00_dot_75_bar__minus_01_dot_79 loc_bar__minus_4_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Bowl_bar__minus_01_dot_81_bar__plus_00_dot_76_bar__minus_01_dot_53 loc_bar__minus_4_bar__minus_3_bar_3_bar_60)
        (objectAtLocation LightSwitch_bar__plus_01_dot_41_bar__plus_01_dot_29_bar__plus_01_dot_65 loc_bar_7_bar_4_bar_0_bar_30)
        (objectAtLocation KeyChain_bar__minus_00_dot_45_bar__plus_00_dot_76_bar__minus_02_dot_40 loc_bar_5_bar__minus_9_bar_3_bar_60)
        (objectAtLocation Television_bar__minus_00_dot_01_bar__plus_01_dot_23_bar__minus_02_dot_44 loc_bar_5_bar__minus_9_bar_3_bar_60)
        (objectAtLocation Vase_bar__minus_01_dot_59_bar__plus_00_dot_17_bar__minus_00_dot_53 loc_bar__minus_2_bar_0_bar_3_bar_45)
        (objectAtLocation Statue_bar__plus_00_dot_04_bar__plus_00_dot_48_bar__minus_01_dot_18 loc_bar_5_bar__minus_6_bar_3_bar_60)
        (objectAtLocation Pillow_bar__minus_00_dot_50_bar__plus_00_dot_59_bar__plus_00_dot_96 loc_bar_1_bar__minus_2_bar_0_bar_45)
        (objectAtLocation Laptop_bar__minus_01_dot_73_bar__plus_00_dot_75_bar__minus_00_dot_70 loc_bar__minus_4_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Vase_bar__minus_00_dot_12_bar__plus_00_dot_48_bar__minus_01_dot_06 loc_bar_5_bar__minus_6_bar_3_bar_60)
        (objectAtLocation Statue_bar__plus_00_dot_34_bar__plus_00_dot_58_bar__minus_01_dot_12 loc_bar_5_bar__minus_6_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__minus_00_dot_27_bar__plus_00_dot_48_bar__minus_01_dot_00 loc_bar_5_bar__minus_6_bar_3_bar_60)
        (objectAtLocation FloorLamp_bar__minus_01_dot_57_bar__plus_00_dot_00_bar__plus_01_dot_30 loc_bar__minus_6_bar_2_bar_0_bar_60)
        (objectAtLocation Vase_bar__minus_01_dot_81_bar__plus_00_dot_77_bar__minus_01_dot_29 loc_bar__minus_4_bar__minus_3_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__minus_01_dot_94_bar__plus_00_dot_76_bar__minus_01_dot_06 loc_bar__minus_4_bar__minus_3_bar_3_bar_60)
        (objectAtLocation Vase_bar__minus_00_dot_27_bar__plus_00_dot_48_bar__minus_01_dot_25 loc_bar_5_bar__minus_6_bar_3_bar_60)
        (objectAtLocation Window_bar__minus_00_dot_82_bar__plus_01_dot_54_bar__minus_02_dot_72 loc_bar__minus_4_bar__minus_8_bar_2_bar_15)
        (objectAtLocation Window_bar__plus_00_dot_79_bar__plus_01_dot_54_bar__minus_02_dot_72 loc_bar_5_bar__minus_9_bar_2_bar_15)
        (objectAtLocation Statue_bar__minus_00_dot_12_bar__plus_00_dot_49_bar__minus_00_dot_93 loc_bar_5_bar__minus_6_bar_3_bar_60)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 RemoteControlType)
                                    (receptacleType ?r SofaType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 RemoteControlType)
                                            (receptacleType ?r SofaType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            