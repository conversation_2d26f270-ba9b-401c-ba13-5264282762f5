{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 10}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 3, "image_name": "000000063.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000064.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000065.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000066.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000067.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000068.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 3, "image_name": "000000074.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000075.png", "low_idx": 13}, {"high_idx": 3, "image_name": "000000076.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000077.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000080.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000083.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000084.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000085.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000091.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000093.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000094.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000095.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000096.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000097.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000098.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 18}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000104.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000107.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000108.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000109.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000110.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000111.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 19}, {"high_idx": 5, "image_name": "000000119.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000120.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000121.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000122.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000123.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000124.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000125.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000126.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000127.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000128.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000129.png", "low_idx": 20}, {"high_idx": 5, "image_name": "000000130.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000131.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000132.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000133.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000134.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000135.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000136.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000137.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000138.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000139.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000140.png", "low_idx": 21}, {"high_idx": 5, "image_name": "000000141.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000142.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000143.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000144.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000145.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000146.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000147.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000148.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000149.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000150.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000151.png", "low_idx": 22}, {"high_idx": 5, "image_name": "000000152.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000153.png", "low_idx": 23}, {"high_idx": 5, "image_name": "000000154.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000155.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000156.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000157.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000158.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000159.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000160.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000161.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000162.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000163.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000164.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000165.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000166.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 26}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 7, "image_name": "000000197.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000201.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000202.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000204.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000205.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000206.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000207.png", "low_idx": 30}, {"high_idx": 7, "image_name": "000000208.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000209.png", "low_idx": 31}, {"high_idx": 7, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000212.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000213.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000214.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000215.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000218.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000219.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000221.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 39}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000231.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000234.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000236.png", "low_idx": 40}, {"high_idx": 7, "image_name": "000000237.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000238.png", "low_idx": 41}, {"high_idx": 7, "image_name": "000000239.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000240.png", "low_idx": 42}, {"high_idx": 7, "image_name": "000000241.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000242.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000243.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000244.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000245.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000246.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000247.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000248.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000249.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000250.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000251.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000252.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000253.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000254.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000255.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000256.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000257.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000258.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000259.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000260.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000261.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000262.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000268.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000269.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000270.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000271.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000272.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000273.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000274.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000275.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000276.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000277.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000278.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000279.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000280.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000281.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000282.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000283.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000284.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000285.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000286.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000287.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000288.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000289.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000290.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000291.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000292.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000293.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000294.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000295.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000296.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000297.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000298.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000299.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000300.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000301.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000302.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000303.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000304.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000305.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000306.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000307.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000308.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000309.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000310.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000311.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000312.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000313.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000314.png", "low_idx": 52}, {"high_idx": 9, "image_name": "000000315.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000316.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000317.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000318.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000319.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000320.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000321.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000322.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000323.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000324.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000325.png", "low_idx": 53}, {"high_idx": 9, "image_name": "000000326.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000327.png", "low_idx": 54}, {"high_idx": 9, "image_name": "000000328.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000329.png", "low_idx": 55}, {"high_idx": 9, "image_name": "000000330.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000331.png", "low_idx": 56}, {"high_idx": 9, "image_name": "000000332.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000333.png", "low_idx": 57}, {"high_idx": 9, "image_name": "000000334.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000335.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000336.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000337.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000338.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000339.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000340.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000341.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000342.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000343.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000344.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000345.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000346.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000347.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000348.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000349.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000350.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000351.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000352.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000353.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000354.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000355.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000356.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000357.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000358.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000359.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000360.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000361.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000362.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000363.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000364.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000365.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000366.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000367.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000368.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000369.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000370.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000371.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000372.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000373.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000374.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000375.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000376.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000377.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000378.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000379.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000380.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000381.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000382.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000383.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000384.png", "low_idx": 67}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|2|9|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["butterknife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ButterKnife", [5.13638, 5.13638, 8.6142502, 8.6142502, 3.5453684, 3.5453684]], "coordinateReceptacleObjectId": ["DiningTable", [4.756, 4.756, 9.908, 9.908, 3.6872, 3.6872]], "forceVisible": true, "objectId": "ButterKnife|+01.28|+00.89|+02.15"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [4.47138, 4.47138, 9.0455, 9.0455, 3.7988596, 3.7988596]], "forceVisible": true, "objectId": "Bread|+01.12|+00.95|+02.26"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 3, "planner_action": {"action": "GotoLocation", "location": "loc|3|6|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["butterknife", "garbagecan"]}, "high_idx": 4, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ButterKnife", [5.13638, 5.13638, 8.6142502, 8.6142502, 3.5453684, 3.5453684]], "coordinateReceptacleObjectId": ["GarbageCan", [5.18714428, 5.18714428, 5.8631954, 5.8631954, 0.032000004, 0.032000004]], "forceVisible": true, "objectId": "ButterKnife|+01.28|+00.89|+02.15", "receptacleObjectId": "GarbageCan|+01.30|+00.01|+01.47"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 5, "planner_action": {"action": "GotoLocation", "location": "loc|2|9|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 6, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [4.47138, 4.47138, 9.0455, 9.0455, 3.7988596, 3.7988596]], "coordinateReceptacleObjectId": ["DiningTable", [4.756, 4.756, 9.908, 9.908, 3.6872, 3.6872]], "forceVisible": true, "objectId": "Bread|+01.12|+00.95|+02.26|BreadSliced_1"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 7, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 8, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 9, "planner_action": {"action": "GotoLocation", "location": "loc|3|6|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "garbagecan"]}, "high_idx": 10, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [4.47138, 4.47138, 9.0455, 9.0455, 3.7988596, 3.7988596]], "coordinateReceptacleObjectId": ["GarbageCan", [5.18714428, 5.18714428, 5.8631954, 5.8631954, 0.032000004, 0.032000004]], "forceVisible": true, "objectId": "Bread|+01.12|+00.95|+02.26|BreadSliced_1", "receptacleObjectId": "GarbageCan|+01.30|+00.01|+01.47"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 11, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ButterKnife|+01.28|+00.89|+02.15"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [171, 117, 176, 136], "mask": [[34971, 2], [35271, 3], [35571, 4], [35871, 4], [36171, 5], [36471, 5], [36771, 5], [37071, 6], [37371, 6], [37671, 6], [37972, 5], [38272, 5], [38572, 5], [38872, 5], [39172, 5], [39472, 5], [39772, 5], [40072, 4], [40372, 4], [40674, 2]], "point": [173, 125]}}, "high_idx": 1}, {"api_action": {"action": "SliceObject", "objectId": "Bread|+01.12|+00.95|+02.26"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [103, 136, 189, 184], "mask": [[40625, 43], [40917, 60], [41214, 66], [41512, 70], [41810, 73], [42109, 76], [42408, 78], [42707, 79], [43007, 80], [43306, 81], [43606, 81], [43906, 82], [44205, 83], [44505, 83], [44805, 84], [45105, 84], [45405, 84], [45705, 84], [46004, 85], [46304, 86], [46604, 86], [46904, 86], [47204, 86], [47504, 86], [47803, 87], [48103, 87], [48403, 87], [48703, 87], [49003, 87], [49303, 87], [49603, 87], [49903, 87], [50203, 87], [50503, 86], [50803, 86], [51104, 85], [51404, 85], [51704, 85], [52004, 85], [52303, 86], [52603, 86], [52904, 85], [53204, 85], [53504, 85], [53804, 85], [54105, 84], [54405, 83], [54706, 80], [55007, 73]], "point": [146, 159]}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ButterKnife|+01.28|+00.89|+02.15", "placeStationary": true, "receptacleObjectId": "GarbageCan|+01.30|+00.01|+01.47"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [108, 104, 206, 243], "mask": [[31046, 43], [31340, 51], [31638, 55], [31935, 59], [32233, 61], [32532, 63], [32831, 64], [33130, 65], [33429, 67], [33728, 68], [34027, 69], [34325, 72], [34624, 73], [34923, 74], [35223, 74], [35522, 75], [35822, 75], [36121, 77], [36420, 78], [36720, 78], [37019, 79], [37319, 79], [37618, 80], [37918, 80], [38217, 81], [38516, 83], [38816, 83], [39115, 84], [39415, 84], [39715, 84], [40014, 85], [40314, 85], [40614, 85], [40913, 86], [41213, 87], [41513, 87], [41812, 88], [42112, 88], [42411, 89], [42711, 89], [43011, 89], [43310, 90], [43610, 91], [43910, 91], [44210, 91], [44510, 91], [44810, 91], [45110, 91], [45410, 91], [45710, 91], [46010, 91], [46309, 93], [46609, 93], [46909, 93], [47209, 93], [47509, 93], [47809, 93], [48109, 93], [48409, 93], [48709, 93], [49009, 94], [49309, 94], [49609, 94], [49908, 95], [50208, 95], [50508, 95], [50808, 95], [51108, 95], [51409, 94], [51709, 95], [52009, 95], [52309, 95], [52609, 95], [52909, 95], [53210, 94], [53510, 94], [53810, 94], [54110, 37], [54151, 53], [54410, 36], [54452, 53], [54710, 36], [54752, 53], [55010, 36], [55052, 53], [55311, 35], [55353, 52], [55611, 33], [55654, 51], [55911, 32], [55955, 50], [56212, 30], [56256, 49], [56512, 30], [56557, 48], [56812, 29], [56857, 49], [57113, 28], [57157, 49], [57413, 28], [57458, 48], [57713, 28], [57758, 48], [58014, 27], [58058, 48], [58314, 27], [58358, 4], [58368, 38], [58614, 27], [58657, 4], [58670, 36], [58915, 26], [58957, 3], [58971, 35], [59215, 26], [59257, 3], [59271, 35], [59516, 25], [59557, 2], [59572, 34], [59816, 25], [59857, 2], [59872, 35], [60116, 26], [60157, 2], [60173, 34], [60417, 25], [60457, 1], [60473, 34], [60718, 24], [60757, 2], [60773, 34], [61019, 23], [61057, 2], [61072, 35], [61319, 23], [61357, 2], [61372, 35], [61620, 22], [61657, 3], [61671, 36], [61921, 22], [61956, 4], [61970, 37], [62221, 26], [62256, 5], [62269, 38], [62522, 85], [62822, 84], [63122, 84], [63423, 83], [63723, 83], [64023, 82], [64324, 81], [64624, 81], [64924, 81], [65225, 79], [65525, 78], [65825, 77], [66126, 75], [66426, 74], [66726, 73], [67027, 68], [67327, 68], [67628, 66], [67928, 66], [68228, 65], [68529, 64], [68830, 62], [69131, 60], [69431, 60], [69732, 58], [70033, 57], [70334, 55], [70634, 54], [70935, 53], [71236, 51], [71538, 49], [71840, 46], [72142, 43], [72445, 39], [72750, 33]], "point": [157, 172]}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 5}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 5}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 5}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 5}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 5}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+01.12|+00.95|+02.26|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [145, 136, 151, 184], "mask": [[40645, 6], [40945, 7], [41245, 7], [41545, 7], [41845, 7], [42145, 7], [42445, 7], [42745, 7], [43045, 7], [43345, 7], [43645, 7], [43945, 7], [44245, 7], [44545, 7], [44845, 7], [45145, 7], [45445, 7], [45745, 7], [46045, 7], [46345, 7], [46645, 7], [46945, 7], [47245, 7], [47545, 7], [47845, 7], [48145, 7], [48445, 7], [48745, 7], [49045, 7], [49345, 7], [49645, 7], [49945, 7], [50245, 7], [50545, 7], [50845, 7], [51145, 7], [51445, 7], [51745, 7], [52045, 7], [52345, 7], [52645, 7], [52945, 7], [53245, 7], [53545, 7], [53845, 7], [54145, 7], [54445, 7], [54745, 7], [55045, 7]], "point": [148, 159]}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 7}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 8}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+01.12|+00.95|+02.26|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 241], [28529, 241], [28829, 241], [29128, 241], [29427, 242], [29727, 242], [30026, 242], [30326, 242], [30625, 243], [30924, 243], [31224, 243], [31523, 243], [31823, 243], [32122, 244], [32421, 244], [32721, 244], [33020, 244], [33320, 244], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 8}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 110], [25050, 125], [25236, 103], [25359, 115], [25535, 99], [25663, 111], [25834, 97], [25966, 108], [26134, 95], [26268, 105], [26433, 95], [26569, 104], [26733, 94], [26870, 102], [27032, 94], [27171, 101], [27331, 94], [27471, 101], [27631, 94], [27772, 99], [27930, 95], [28072, 99], [28230, 94], [28372, 99], [28529, 95], [28673, 97], [28829, 95], [28973, 97], [29128, 96], [29273, 96], [29427, 98], [29572, 97], [29727, 98], [29872, 97], [30026, 99], [30172, 96], [30326, 99], [30471, 97], [30625, 100], [30771, 97], [30924, 101], [31071, 96], [31224, 101], [31371, 96], [31523, 102], [31671, 95], [31823, 102], [31971, 95], [32122, 103], [32271, 95], [32421, 104], [32571, 94], [32721, 105], [32871, 94], [33020, 106], [33171, 93], [33320, 106], [33471, 93], [33619, 107], [33770, 94], [33918, 110], [34069, 94], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 8}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 8}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-5.376, -5.376, 0.216, 0.216, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [51, 19, 286, 145], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17152, 233], [17453, 231], [17753, 231], [18053, 231], [18353, 230], [18654, 229], [18954, 228], [19254, 228], [19555, 227], [19855, 226], [20155, 226], [20455, 226], [20756, 224], [21056, 224], [21356, 223], [21656, 223], [21957, 222], [22257, 221], [22557, 221], [22858, 219], [23158, 219], [23458, 219], [23758, 218], [24059, 217], [24359, 217], [24659, 216], [24959, 216], [25260, 214], [25560, 214], [25860, 214], [26161, 212], [26461, 212], [26761, 211], [27061, 211], [27362, 210], [27662, 209], [27962, 209], [28263, 208], [28563, 207], [28863, 207], [29163, 206], [29464, 205], [29764, 205], [30064, 204], [30364, 204], [30665, 203], [30965, 202], [31265, 202], [31566, 200], [31866, 200], [32166, 200], [32466, 199], [32767, 198], [33067, 197], [33367, 197], [33668, 196], [33968, 195], [34268, 195], [34568, 195], [34869, 193], [35169, 193], [35469, 192], [35769, 192], [36070, 191], [36370, 190], [36670, 190], [36971, 188], [37271, 188], [37571, 188], [37871, 187], [38172, 186], [38472, 186], [38772, 185], [39073, 184], [39373, 184], [39673, 183], [39980, 171], [40280, 171], [40580, 171], [40881, 169], [41181, 169], [41481, 169], [41781, 168], [42082, 167], [42511, 6], [42811, 6], [43111, 6], [43413, 2]], "point": [168, 81]}}, "high_idx": 8}, {"api_action": {"action": "PickupObject", "objectId": "Bread|+01.12|+00.95|+02.26|BreadSliced_1"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [124, 84, 172, 114], "mask": [[25046, 4], [25339, 20], [25634, 29], [25931, 35], [26229, 39], [26528, 41], [26827, 43], [27126, 45], [27425, 46], [27725, 47], [28025, 47], [28324, 48], [28624, 49], [28924, 49], [29224, 49], [29525, 47], [29825, 47], [30125, 47], [30425, 46], [30725, 46], [31025, 46], [31325, 46], [31625, 46], [31925, 46], [32225, 46], [32525, 46], [32826, 45], [33126, 45], [33426, 45], [33726, 44], [34028, 41]], "point": [148, 98]}}, "high_idx": 8}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.34|+00.90|+00.05"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 19, 286, 263], "mask": [[5471, 11], [5484, 131], [5648, 11], [5770, 190], [6069, 192], [6369, 193], [6668, 195], [6967, 197], [7267, 198], [7566, 200], [7866, 200], [8165, 202], [8464, 204], [8764, 205], [9063, 207], [9362, 209], [9662, 210], [9961, 211], [10261, 212], [10560, 214], [10859, 216], [11159, 217], [11458, 219], [11757, 221], [12057, 221], [12356, 223], [12656, 224], [12955, 226], [13254, 228], [13554, 229], [13853, 231], [14152, 232], [14452, 233], [14751, 235], [15051, 236], [15351, 236], [15651, 236], [15951, 235], [16251, 235], [16552, 233], [16852, 233], [17151, 234], [17451, 233], [17750, 234], [18050, 234], [18349, 234], [18649, 234], [18948, 234], [19247, 235], [19547, 235], [19846, 235], [20146, 235], [20445, 236], [20744, 236], [21044, 236], [21343, 236], [21643, 236], [21942, 237], [22241, 237], [22541, 237], [22840, 237], [23140, 237], [23439, 238], [23739, 237], [24038, 238], [24337, 239], [24637, 238], [24936, 239], [25236, 238], [25535, 239], [25834, 240], [26134, 239], [26433, 240], [26733, 239], [27032, 240], [27331, 241], [27631, 240], [27930, 241], [28230, 241], [28529, 241], [28829, 241], [29128, 241], [29427, 242], [29727, 242], [30026, 242], [30326, 242], [30625, 243], [30924, 243], [31224, 243], [31523, 243], [31823, 243], [32122, 244], [32421, 244], [32721, 244], [33020, 244], [33320, 244], [33619, 245], [33918, 245], [34218, 245], [34517, 246], [34817, 245], [35116, 246], [35416, 245], [35715, 246], [36014, 247], [36314, 246], [36613, 247], [36913, 246], [37212, 247], [37511, 248], [37811, 247], [38110, 248], [38410, 248], [38709, 248], [39008, 249], [39308, 249], [39607, 249], [39907, 244], [40206, 245], [40506, 79], [40715, 36], [40805, 80], [41015, 35], [41104, 81], [41315, 35], [41404, 81], [41616, 34], [41703, 81], [41916, 33], [42003, 81], [42216, 33], [42302, 82], [42601, 83], [42901, 82], [43200, 83], [43500, 83], [43800, 82], [44100, 82], [44400, 82], [44700, 82], [45000, 81], [45300, 81], [45600, 81], [45900, 81], [46200, 80], [46500, 80], [46800, 80], [47100, 80], [47400, 79], [47700, 79], [48000, 79], [48300, 79], [48600, 78], [48900, 78], [49200, 78], [49500, 77], [49800, 77], [50100, 77], [50400, 77], [50700, 76], [51000, 76], [51300, 76], [51600, 76], [51900, 75], [52200, 75], [52500, 75], [52800, 75], [53100, 74], [53400, 74], [53700, 74], [54000, 74], [54300, 73], [54600, 73], [54900, 73], [55200, 72], [55500, 72], [55800, 72], [56100, 72], [56400, 71], [56700, 71], [57000, 71], [57300, 71], [57600, 70], [57900, 70], [58200, 70], [58500, 70], [58800, 69], [59100, 69], [59400, 69], [59700, 69], [60000, 68], [60300, 68], [60600, 68], [60900, 67], [61200, 67], [61500, 67], [61800, 67], [62100, 66], [62400, 66], [62700, 66], [63000, 66], [63300, 65], [63600, 65], [63900, 65], [64200, 65], [64500, 64], [64800, 64], [65100, 64], [65400, 64], [65700, 63], [66000, 63], [66300, 1], [66302, 61], [66600, 2], [66603, 59], [66900, 62], [67200, 62], [67500, 4], [67505, 57], [67800, 5], [67806, 55], [68100, 6], [68107, 54], [68400, 61], [68700, 61], [69000, 8], [69009, 51], [69300, 9], [69310, 50], [69600, 10], [69611, 49], [69900, 60], [70200, 59], [70500, 12], [70513, 46], [70800, 13], [70814, 45], [71101, 57], [71401, 57], [71702, 56], [72003, 13], [72017, 41], [72304, 13], [72318, 39], [72605, 52], [72906, 51], [73207, 50], [73508, 48], [73808, 48], [74109, 47], [74410, 46], [74711, 44], [75012, 10], [75025, 30], [75313, 9], [75325, 30], [75614, 7], [75626, 29], [75915, 5], [75927, 27], [76216, 2], [76228, 27], [76529, 29], [76829, 30], [77130, 23], [77154, 5], [77431, 22], [77455, 4], [77732, 21], [77755, 3], [78033, 19], [78333, 19], [78634, 18]], "point": [143, 134]}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 9}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 9}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 9}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 9}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 9}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 9}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 9}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 9}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 9}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 9}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 9}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 9}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 9}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|+01.12|+00.95|+02.26|BreadSliced_1", "placeStationary": true, "receptacleObjectId": "GarbageCan|+01.30|+00.01|+01.47"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [108, 104, 206, 238], "mask": [[31046, 43], [31340, 51], [31638, 55], [31935, 59], [32233, 61], [32532, 63], [32831, 64], [33130, 65], [33429, 67], [33728, 68], [34027, 69], [34325, 72], [34624, 73], [34923, 74], [35223, 74], [35522, 75], [35822, 75], [36121, 77], [36420, 78], [36720, 78], [37019, 79], [37319, 79], [37618, 80], [37918, 80], [38217, 81], [38516, 83], [38816, 83], [39115, 84], [39415, 84], [39715, 84], [40014, 85], [40314, 85], [40614, 85], [40913, 86], [41213, 87], [41513, 87], [41812, 88], [42112, 88], [42411, 89], [42711, 89], [43011, 89], [43310, 90], [43610, 91], [43910, 91], [44210, 91], [44510, 91], [44810, 91], [45110, 91], [45410, 91], [45710, 91], [46010, 91], [46309, 93], [46609, 93], [46909, 93], [47209, 93], [47509, 93], [47809, 93], [48109, 93], [48409, 93], [48709, 93], [49009, 94], [49309, 94], [49609, 94], [49908, 95], [50208, 52], [50263, 40], [50508, 52], [50563, 40], [50808, 52], [50864, 39], [51108, 52], [51164, 39], [51409, 51], [51464, 39], [51709, 51], [51764, 40], [52009, 51], [52064, 40], [52309, 51], [52363, 41], [52609, 51], [52663, 41], [52909, 51], [52962, 42], [53210, 49], [53262, 42], [53510, 49], [53562, 42], [53810, 49], [53861, 43], [54110, 37], [54151, 8], [54161, 43], [54410, 36], [54452, 7], [54461, 44], [54710, 36], [54752, 6], [54762, 43], [55010, 36], [55052, 6], [55062, 43], [55311, 35], [55353, 5], [55362, 43], [55611, 33], [55654, 4], [55661, 44], [55911, 32], [55955, 4], [55960, 45], [56212, 30], [56256, 49], [56512, 30], [56557, 48], [56812, 29], [56857, 49], [57113, 28], [57157, 49], [57413, 28], [57458, 48], [57713, 28], [57758, 48], [58014, 27], [58058, 48], [58314, 27], [58358, 4], [58368, 38], [58614, 27], [58657, 4], [58670, 36], [58915, 26], [58957, 3], [58971, 35], [59215, 26], [59257, 3], [59271, 35], [59516, 25], [59557, 2], [59572, 34], [59816, 25], [59857, 2], [59872, 35], [60116, 26], [60157, 2], [60173, 34], [60417, 25], [60457, 1], [60473, 34], [60718, 24], [60757, 2], [60773, 34], [61019, 23], [61057, 2], [61072, 35], [61319, 23], [61357, 2], [61372, 35], [61620, 22], [61657, 3], [61671, 36], [61921, 22], [61956, 4], [61970, 37], [62221, 26], [62256, 5], [62269, 38], [62522, 85], [62822, 84], [63122, 84], [63423, 83], [63723, 83], [64023, 82], [64324, 81], [64624, 81], [64924, 81], [65225, 79], [65525, 78], [65825, 77], [66126, 75], [66426, 74], [66726, 73], [67027, 68], [67327, 68], [67628, 66], [67928, 66], [68228, 65], [68529, 64], [68830, 62], [69131, 60], [69431, 60], [69732, 58], [70033, 57], [70334, 55], [70634, 10], [70653, 35], [70977, 11], [71285, 2]], "point": [157, 170]}}, "high_idx": 10}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan17", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -0.5, "y": 0.908999562, "z": 3.25}, "object_poses": [{"objectName": "Pan_a0065d6c", "position": {"x": -1.26391578, "y": 1.287967, "z": 1.197375}, "rotation": {"x": 0.0, "y": -0.000160509444, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": -1.28665, "y": 0.91150403, "z": -0.392702371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": 1.36722, "y": 0.8857041, "z": 2.261375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": 1.28637147, "y": 0.9564309, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": -1.12754762, "y": 0.77432996, "z": 0.245233089}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": 1.284095, "y": 0.8863421, "z": 2.15356255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": -1.01254034, "y": 0.74966836, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": -1.383174, "y": 0.750342965, "z": 2.273428}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": -0.144537389, "y": 0.7592729, "z": -0.5731131}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": 1.24557734, "y": 0.08718041, "z": 1.50386286}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": 1.284095, "y": 0.886848569, "z": 2.477}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.802, "y": 0.9393, "z": -0.47308147}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 1.21878171, "y": 0.117943287, "z": -0.05235876}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 1.128434, "y": 0.9123855, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": -1.10519874, "y": 0.7708927, "z": 2.273428}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": 1.117845, "y": 0.8876286, "z": 2.80043745}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": 1.128434, "y": 0.9128286, "z": -0.144580841}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": -1.19785714, "y": 0.813041151, "z": 2.64457226}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": 1.117845, "y": 0.9497149, "z": 2.261375}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": -0.91988194, "y": 0.8198685, "z": 2.273428}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": -1.2630949, "y": 1.65203, "z": 0.108386904}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": 1.36722, "y": 0.8826062, "z": 2.36918736}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": -1.294518, "y": 1.65203, "z": -0.481822163}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -1.09907246, "y": 0.3365608, "z": 1.19737542}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": 1.24557734, "y": 0.146455675, "z": 1.409267}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": 1.444309, "y": 0.971704, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_74101882", "position": {"x": 1.36534023, "y": 0.9749149, "z": 0.1391859}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Glassbottle_6018293c", "position": {"x": -0.91988194, "y": 0.7496167, "z": 2.83014464}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_9f8771c2", "position": {"x": 0.0417298935, "y": 0.7591047, "z": -0.65188694}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Kettle_c40d8d1f", "position": {"x": -1.357, "y": 0.7396263, "z": 3.024}, "rotation": {"x": 0.0, "y": 240.000229, "z": 0.0}}, {"objectName": "Tomato_57296338", "position": {"x": -1.47583234, "y": 0.7986288, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c933fa0b", "position": {"x": 0.726627469, "y": 2.15623665, "z": -0.7877707}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_e3d6b27e", "position": {"x": 1.03472006, "y": 0.8840117, "z": 2.692625}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_6438ff8b", "position": {"x": -1.26391578, "y": 0.82212615, "z": 1.197375}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_92502562", "position": {"x": 0.5029, "y": 0.9393, "z": -0.7406}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_e0c21b7c", "position": {"x": -1.01254034, "y": 0.750954866, "z": 1.90228331}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_6b7178a8", "position": {"x": 1.128434, "y": 0.912428737, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_2f163330", "position": {"x": -1.19785714, "y": 0.763899863, "z": 2.08785558}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_7e041f70", "position": {"x": -1.16246963, "y": 0.3400014, "z": 0.9880004}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_7daf87a0", "position": {"x": -0.968678534, "y": 0.972304, "z": -0.828023434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_572193ee", "position": {"x": 1.28637147, "y": 0.910747945, "z": 0.7067195}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_268c1c72", "position": {"x": -0.130184948, "y": 0.934092641, "z": -0.348743349}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_1ede3fc6", "position": {"x": -0.0203592032, "y": 0.792722642, "z": -0.691273868}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8429d040", "position": {"x": -0.694944441, "y": 1.65250361, "z": -0.7249964}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b6eaf348", "position": {"x": 1.444309, "y": 0.9078062, "z": 0.4229527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_edec52fd", "position": {"x": 1.36534023, "y": 0.9564309, "z": -0.144580841}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_432df619", "position": {"x": 1.13, "y": 0.9, "z": -0.305}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "DishSponge_4b57442b", "position": {"x": -0.91988194, "y": 0.7484642, "z": 2.08785558}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_a0065d6c", "position": {"x": -1.32097435, "y": 1.420867, "z": 0.7786249}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Spoon_1d6d1e51", "position": {"x": 1.444309, "y": 0.9126294, "z": -0.4283477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SprayBottle_4f8d0eee", "position": {"x": -1.10519874, "y": 0.749030352, "z": 3.015717}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_c297f602", "position": {"x": -1.325682, "y": 2.14849019, "z": 0.763267636}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3239913657, "scene_num": 17}, "task_id": "trial_T20190906_205800_856686", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AJNQ2PBD07FKE_320DUZ38GA3TWD4JOF5WMOZ1JZJGJF", "high_descs": ["Go straight a little bit , turn left towards the white table.", "pick up the knife off of the table.", "slice the bread with the knife", "turn to your right and go to the trash can on your left.", "Put the knife in the trash can.", "turn back to your left and go back to the white table on your right.", "Grab a slice of the bread off of the table.", "turn right, and then go forward and then turn left and go to the microwave.", "put the bread in the microwave and turn on the microwave and then open microwave back open.", "turn right, go forward, and turn right again until you are at the trash can again ", "put the bread in the trash can"], "task_desc": "Making toast to be thrown away.", "votes": [1, 1]}, {"assignment_id": "AT3C00TKZK13L_3K5TEWLKGYSGJZA8VSJIAONBTZ4VIT", "high_descs": ["Walk to the left towards the table", "Grab a knife from the table", "Slice the bread on the table", "Walk with the knife towards the trash can", "Put the knife inside the trash can", "Walk back towards the table", "Take a slice of bread", "Walk with the slice of bread towards the microwave", "Put the slice of bread inside the microwave and take it out", "Walk with the slice of bread towards the trash can", "Put the slice of bread inside the trash can"], "task_desc": "Cut a slice of bread, microwave it, put it in the trash can", "votes": [1, 1]}, {"assignment_id": "A3JZLYQ606HJJR_3OSWBBLG1HERFDZH3TJP7C4JH20XDM", "high_descs": ["Move to the white table across the room in front of the window. ", "Pick up the knife that is on the table.", "Slice the bread with the knife.", "Take the knife and move to the red trash bin to your left. ", "Put the knife in the trash bin. ", "Move to the white table on to the left in front of the window. ", "Pick up a slice of bread off of the table.", "Move to the right to the microwave across the room.", "Open the microwave door.  Put the bread inside.  Turn on the microwave.", "Open the door.  Pick up the bread.  Move across the room to the red trash bin. ", "Place the bread inside the trash bin. "], "task_desc": "Pick up knife, slice & cook bread, throw bread away in trash bin.", "votes": [1, 1]}]}}