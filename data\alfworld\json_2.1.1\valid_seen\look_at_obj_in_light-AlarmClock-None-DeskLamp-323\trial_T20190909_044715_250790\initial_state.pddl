
(define (problem plan_trial_T20190909_044715_250790)
(:domain alfred)
(:objects
agent1 - agent
Curtains - object
        SprayBottle - object
        Mirror - object
        HousePlant - object
        Watch - object
        TeddyBear - object
        WateringCan - object
        Bathtub - object
        LightSwitch - object
        Vase - object
        Candle - object
        Bread - object
        Statue - object
        HandTowel - object
        StoveKnob - object
        PaperTowelRoll - object
        Pan - object
        PepperShaker - object
        Painting - object
        CellPhone - object
        Plunger - object
        Spoon - object
        SaltShaker - object
        KeyChain - object
        Footstool - object
        Poster - object
        Television - object
        LaundryHamperLid - object
        Glassbottle - object
        AlarmClock - object
        WineBottle - object
        Bowl - object
        Sink - object
        TissueBox - object
        TennisRacket - object
        ToiletPaper - object
        ScrubBrush - object
        CreditCard - object
        Laptop - object
        Cloth - object
        Cup - object
        Chair - object
        BasketBall - object
        Kettle - object
        PaperTowel - object
        ShowerDoor - object
        BaseballBat - object
        Potato - object
        Pillow - object
        Lettuce - object
        RemoteControl - object
        Window - object
        DishSponge - object
        CD - object
        Boots - object
        Blinds - object
        Pen - object
        Newspaper - object
        Plate - object
        Mug - object
        ShowerGlass - object
        ToiletPaperRoll - object
        SoapBar - object
        FloorLamp - object
        SoapBottle - object
        Pot - object
        Pencil - object
        Spatula - object
        Towel - object
        Fork - object
        Ladle - object
        Knife - object
        Apple - object
        Box - object
        Tomato - object
        ButterKnife - object
        Egg - object
        Book - object
        DeskLamp - object
CurtainsType - otype
        SprayBottleType - otype
        MirrorType - otype
        HousePlantType - otype
        WatchType - otype
        TeddyBearType - otype
        WateringCanType - otype
        BathtubType - otype
        LightSwitchType - otype
        VaseType - otype
        CandleType - otype
        BreadType - otype
        StatueType - otype
        HandTowelType - otype
        StoveKnobType - otype
        PaperTowelRollType - otype
        PanType - otype
        PepperShakerType - otype
        PaintingType - otype
        CellPhoneType - otype
        PlungerType - otype
        SpoonType - otype
        SaltShakerType - otype
        KeyChainType - otype
        FootstoolType - otype
        PosterType - otype
        TelevisionType - otype
        LaundryHamperLidType - otype
        GlassbottleType - otype
        AlarmClockType - otype
        WineBottleType - otype
        BowlType - otype
        SinkType - otype
        TissueBoxType - otype
        TennisRacketType - otype
        ToiletPaperType - otype
        ScrubBrushType - otype
        CreditCardType - otype
        LaptopType - otype
        ClothType - otype
        CupType - otype
        ChairType - otype
        BasketBallType - otype
        KettleType - otype
        PaperTowelType - otype
        ShowerDoorType - otype
        BaseballBatType - otype
        PotatoType - otype
        PillowType - otype
        LettuceType - otype
        RemoteControlType - otype
        WindowType - otype
        DishSpongeType - otype
        CDType - otype
        BootsType - otype
        BlindsType - otype
        PenType - otype
        NewspaperType - otype
        PlateType - otype
        MugType - otype
        ShowerGlassType - otype
        ToiletPaperRollType - otype
        SoapBarType - otype
        FloorLampType - otype
        SoapBottleType - otype
        PotType - otype
        PencilType - otype
        SpatulaType - otype
        TowelType - otype
        ForkType - otype
        LadleType - otype
        KnifeType - otype
        AppleType - otype
        BoxType - otype
        TomatoType - otype
        ButterKnifeType - otype
        EggType - otype
        BookType - otype
        DeskLampType - otype
CabinetType - rtype
        ShelfType - rtype
        DiningTableType - rtype
        TowelHolderType - rtype
        PaintingHangerType - rtype
        TVStandType - rtype
        LaundryHamperType - rtype
        OttomanType - rtype
        SafeType - rtype
        CoffeeMachineType - rtype
        CounterTopType - rtype
        ToiletType - rtype
        FridgeType - rtype
        CoffeeTableType - rtype
        SinkBasinType - rtype
        GarbageCanType - rtype
        BathtubBasinType - rtype
        SideTableType - rtype
        CartType - rtype
        BedType - rtype
        DeskType - rtype
        SofaType - rtype
        ArmChairType - rtype
        MicrowaveType - rtype
        HandTowelHolderType - rtype
        ToasterType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        DrawerType - rtype
        DresserType - rtype


        AlarmClock_bar__plus_02_dot_04_bar__plus_00_dot_90_bar__minus_03_dot_32 - object
        AlarmClock_bar__plus_02_dot_69_bar__plus_01_dot_23_bar__plus_01_dot_40 - object
        AlarmClock_bar__minus_02_dot_48_bar__plus_00_dot_68_bar__minus_03_dot_30 - object
        Blinds_bar__plus_02_dot_93_bar__plus_02_dot_17_bar__minus_01_dot_00 - object
        Blinds_bar__minus_00_dot_80_bar__plus_02_dot_09_bar__minus_03_dot_63 - object
        Book_bar__plus_02_dot_35_bar__plus_00_dot_89_bar__minus_03_dot_16 - object
        Bowl_bar__plus_01_dot_84_bar__plus_00_dot_89_bar__minus_03_dot_24 - object
        CD_bar__plus_01_dot_87_bar__plus_00_dot_90_bar__minus_02_dot_98 - object
        CD_bar__plus_02_dot_61_bar__plus_00_dot_37_bar__plus_01_dot_76 - object
        CD_bar__minus_02_dot_44_bar__plus_00_dot_68_bar__minus_02_dot_92 - object
        CellPhone_bar__plus_01_dot_78_bar__plus_00_dot_36_bar__minus_03_dot_03 - object
        CellPhone_bar__minus_02_dot_48_bar__plus_00_dot_68_bar__minus_03_dot_15 - object
        Chair_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__minus_02_dot_88 - object
        CreditCard_bar__plus_02_dot_08_bar__plus_00_dot_08_bar__minus_03_dot_10 - object
        CreditCard_bar__plus_02_dot_61_bar__plus_00_dot_37_bar__plus_01_dot_34 - object
        CreditCard_bar__plus_02_dot_71_bar__plus_00_dot_92_bar__plus_01_dot_34 - object
        DeskLamp_bar__plus_02_dot_76_bar__plus_00_dot_89_bar__minus_03_dot_32 - object
        DeskLamp_bar__minus_02_dot_46_bar__plus_00_dot_67_bar__minus_00_dot_53 - object
        KeyChain_bar__plus_02_dot_76_bar__plus_01_dot_25_bar__plus_01_dot_77 - object
        KeyChain_bar__minus_02_dot_48_bar__plus_00_dot_08_bar__minus_03_dot_09 - object
        Laptop_bar__minus_01_dot_31_bar__plus_00_dot_61_bar__minus_01_dot_54 - object
        Laptop_bar__minus_01_dot_31_bar__plus_00_dot_62_bar__minus_02_dot_06 - object
        LightSwitch_bar__minus_00_dot_70_bar__plus_01_dot_02_bar__plus_00_dot_33 - object
        Mirror_bar__plus_00_dot_53_bar__plus_01_dot_42_bar__plus_02_dot_00 - object
        Mug_bar__plus_02_dot_04_bar__plus_00_dot_89_bar__minus_03_dot_04 - object
        Mug_bar__plus_02_dot_56_bar__plus_00_dot_89_bar__minus_03_dot_04 - object
        Mug_bar__minus_02_dot_53_bar__plus_00_dot_68_bar__minus_00_dot_28 - object
        Pencil_bar__plus_01_dot_34_bar__plus_00_dot_02_bar__minus_03_dot_38 - object
        Pencil_bar__plus_02_dot_69_bar__plus_01_dot_23_bar__plus_01_dot_28 - object
        Pen_bar__plus_02_dot_04_bar__plus_00_dot_90_bar__minus_03_dot_11 - object
        Pen_bar__plus_02_dot_21_bar__plus_00_dot_90_bar__minus_03_dot_39 - object
        Pen_bar__minus_02_dot_53_bar__plus_00_dot_69_bar__minus_03_dot_23 - object
        Pillow_bar__minus_01_dot_31_bar__plus_00_dot_68_bar__minus_02_dot_46 - object
        Pillow_bar__minus_01_dot_87_bar__plus_00_dot_68_bar__minus_02_dot_26 - object
        TeddyBear_bar__minus_00_dot_67_bar__plus_00_dot_61_bar__minus_01_dot_36 - object
        Window_bar__plus_02_dot_96_bar__plus_01_dot_51_bar__minus_01_dot_02 - object
        Window_bar__minus_00_dot_84_bar__plus_01_dot_39_bar__minus_03_dot_65 - object
        Bed_bar__plus_02_dot_40_bar__plus_00_dot_01_bar__minus_00_dot_07 - receptacle
        Bed_bar__minus_01_dot_64_bar__minus_00_dot_04_bar__minus_01_dot_86 - receptacle
        Desk_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__minus_03_dot_22 - receptacle
        Drawer_bar__plus_01_dot_88_bar__plus_00_dot_21_bar__minus_02_dot_96 - receptacle
        Drawer_bar__plus_01_dot_88_bar__plus_00_dot_49_bar__minus_02_dot_96 - receptacle
        Drawer_bar__plus_01_dot_88_bar__plus_00_dot_73_bar__minus_02_dot_96 - receptacle
        Drawer_bar__plus_02_dot_64_bar__plus_00_dot_16_bar__plus_01_dot_51 - receptacle
        Drawer_bar__plus_02_dot_64_bar__plus_00_dot_44_bar__plus_01_dot_51 - receptacle
        Drawer_bar__plus_02_dot_64_bar__plus_00_dot_72_bar__plus_01_dot_51 - receptacle
        Drawer_bar__plus_02_dot_64_bar__plus_00_dot_99_bar__plus_01_dot_51 - receptacle
        Drawer_bar__minus_02_dot_42_bar__plus_00_dot_17_bar__minus_00_dot_51 - receptacle
        Drawer_bar__minus_02_dot_42_bar__plus_00_dot_17_bar__minus_03_dot_15 - receptacle
        Drawer_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_00_dot_51 - receptacle
        Drawer_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_03_dot_15 - receptacle
        Dresser_bar__plus_02_dot_68_bar_00_dot_00_bar__plus_01_dot_51 - receptacle
        GarbageCan_bar__plus_01_dot_34_bar__plus_00_dot_00_bar__minus_03_dot_38 - receptacle
        Safe_bar__plus_02_dot_70_bar__plus_01_dot_23_bar__plus_01_dot_77 - receptacle
        SideTable_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_00_dot_51 - receptacle
        SideTable_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_03_dot_15 - receptacle
        loc_bar_0_bar__minus_8_bar_3_bar_45 - location
        loc_bar_7_bar_6_bar_1_bar_45 - location
        loc_bar__minus_4_bar__minus_12_bar_3_bar_45 - location
        loc_bar__minus_3_bar__minus_12_bar_2_bar__minus_30 - location
        loc_bar__minus_1_bar_1_bar_3_bar_60 - location
        loc_bar__minus_7_bar__minus_2_bar_3_bar_60 - location
        loc_bar__minus_5_bar__minus_3_bar_3_bar_45 - location
        loc_bar_9_bar_5_bar_1_bar_60 - location
        loc_bar_9_bar__minus_6_bar_1_bar_15 - location
        loc_bar_5_bar_0_bar_1_bar_60 - location
        loc_bar__minus_3_bar__minus_12_bar_2_bar_15 - location
        loc_bar_5_bar__minus_12_bar_2_bar_60 - location
        loc_bar_2_bar_6_bar_0_bar_30 - location
        loc_bar__minus_7_bar__minus_12_bar_3_bar_60 - location
        loc_bar_3_bar__minus_9_bar_1_bar_45 - location
        loc_bar_9_bar_6_bar_1_bar_45 - location
        loc_bar_4_bar__minus_10_bar_1_bar_45 - location
        loc_bar_8_bar__minus_10_bar_2_bar_60 - location
        loc_bar_6_bar_4_bar_1_bar_45 - location
        loc_bar__minus_6_bar__minus_3_bar_3_bar_45 - location
        loc_bar_9_bar__minus_6_bar_1_bar__minus_30 - location
        loc_bar_8_bar__minus_10_bar_1_bar_60 - location
        loc_bar_8_bar_6_bar_1_bar_45 - location
        loc_bar_6_bar_5_bar_1_bar_45 - location
        loc_bar__minus_5_bar__minus_12_bar_3_bar_45 - location
        loc_bar_1_bar_4_bar_1_bar_30 - location
        )
    

(:init


        (receptacleType Desk_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__minus_03_dot_22 DeskType)
        (receptacleType Drawer_bar__minus_02_dot_42_bar__plus_00_dot_17_bar__minus_00_dot_51 DrawerType)
        (receptacleType SideTable_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_00_dot_51 SideTableType)
        (receptacleType Bed_bar__minus_01_dot_64_bar__minus_00_dot_04_bar__minus_01_dot_86 BedType)
        (receptacleType Drawer_bar__plus_02_dot_64_bar__plus_00_dot_44_bar__plus_01_dot_51 DrawerType)
        (receptacleType Safe_bar__plus_02_dot_70_bar__plus_01_dot_23_bar__plus_01_dot_77 SafeType)
        (receptacleType Bed_bar__plus_02_dot_40_bar__plus_00_dot_01_bar__minus_00_dot_07 BedType)
        (receptacleType GarbageCan_bar__plus_01_dot_34_bar__plus_00_dot_00_bar__minus_03_dot_38 GarbageCanType)
        (receptacleType Drawer_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_00_dot_51 DrawerType)
        (receptacleType Drawer_bar__plus_01_dot_88_bar__plus_00_dot_73_bar__minus_02_dot_96 DrawerType)
        (receptacleType Drawer_bar__plus_02_dot_64_bar__plus_00_dot_99_bar__plus_01_dot_51 DrawerType)
        (receptacleType Drawer_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_03_dot_15 DrawerType)
        (receptacleType SideTable_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_03_dot_15 SideTableType)
        (receptacleType Drawer_bar__plus_01_dot_88_bar__plus_00_dot_49_bar__minus_02_dot_96 DrawerType)
        (receptacleType Drawer_bar__plus_02_dot_64_bar__plus_00_dot_16_bar__plus_01_dot_51 DrawerType)
        (receptacleType Drawer_bar__plus_02_dot_64_bar__plus_00_dot_72_bar__plus_01_dot_51 DrawerType)
        (receptacleType Drawer_bar__plus_01_dot_88_bar__plus_00_dot_21_bar__minus_02_dot_96 DrawerType)
        (receptacleType Dresser_bar__plus_02_dot_68_bar_00_dot_00_bar__plus_01_dot_51 DresserType)
        (receptacleType Drawer_bar__minus_02_dot_42_bar__plus_00_dot_17_bar__minus_03_dot_15 DrawerType)
        (objectType Chair_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__minus_02_dot_88 ChairType)
        (objectType CellPhone_bar__plus_01_dot_78_bar__plus_00_dot_36_bar__minus_03_dot_03 CellPhoneType)
        (objectType DeskLamp_bar__plus_02_dot_76_bar__plus_00_dot_89_bar__minus_03_dot_32 DeskLampType)
        (objectType Mug_bar__minus_02_dot_53_bar__plus_00_dot_68_bar__minus_00_dot_28 MugType)
        (objectType Mug_bar__plus_02_dot_56_bar__plus_00_dot_89_bar__minus_03_dot_04 MugType)
        (objectType TeddyBear_bar__minus_00_dot_67_bar__plus_00_dot_61_bar__minus_01_dot_36 TeddyBearType)
        (objectType LightSwitch_bar__minus_00_dot_70_bar__plus_01_dot_02_bar__plus_00_dot_33 LightSwitchType)
        (objectType Pillow_bar__minus_01_dot_87_bar__plus_00_dot_68_bar__minus_02_dot_26 PillowType)
        (objectType CellPhone_bar__minus_02_dot_48_bar__plus_00_dot_68_bar__minus_03_dot_15 CellPhoneType)
        (objectType Book_bar__plus_02_dot_35_bar__plus_00_dot_89_bar__minus_03_dot_16 BookType)
        (objectType Blinds_bar__minus_00_dot_80_bar__plus_02_dot_09_bar__minus_03_dot_63 BlindsType)
        (objectType Laptop_bar__minus_01_dot_31_bar__plus_00_dot_61_bar__minus_01_dot_54 LaptopType)
        (objectType KeyChain_bar__minus_02_dot_48_bar__plus_00_dot_08_bar__minus_03_dot_09 KeyChainType)
        (objectType Mug_bar__plus_02_dot_04_bar__plus_00_dot_89_bar__minus_03_dot_04 MugType)
        (objectType Window_bar__plus_02_dot_96_bar__plus_01_dot_51_bar__minus_01_dot_02 WindowType)
        (objectType Pen_bar__plus_02_dot_21_bar__plus_00_dot_90_bar__minus_03_dot_39 PenType)
        (objectType CD_bar__minus_02_dot_44_bar__plus_00_dot_68_bar__minus_02_dot_92 CDType)
        (objectType Pencil_bar__plus_01_dot_34_bar__plus_00_dot_02_bar__minus_03_dot_38 PencilType)
        (objectType Pillow_bar__minus_01_dot_31_bar__plus_00_dot_68_bar__minus_02_dot_46 PillowType)
        (objectType DeskLamp_bar__minus_02_dot_46_bar__plus_00_dot_67_bar__minus_00_dot_53 DeskLampType)
        (objectType Blinds_bar__plus_02_dot_93_bar__plus_02_dot_17_bar__minus_01_dot_00 BlindsType)
        (objectType Bowl_bar__plus_01_dot_84_bar__plus_00_dot_89_bar__minus_03_dot_24 BowlType)
        (objectType Pen_bar__plus_02_dot_04_bar__plus_00_dot_90_bar__minus_03_dot_11 PenType)
        (objectType CD_bar__plus_02_dot_61_bar__plus_00_dot_37_bar__plus_01_dot_76 CDType)
        (objectType AlarmClock_bar__plus_02_dot_04_bar__plus_00_dot_90_bar__minus_03_dot_32 AlarmClockType)
        (objectType CreditCard_bar__plus_02_dot_71_bar__plus_00_dot_92_bar__plus_01_dot_34 CreditCardType)
        (objectType CreditCard_bar__plus_02_dot_61_bar__plus_00_dot_37_bar__plus_01_dot_34 CreditCardType)
        (objectType Mirror_bar__plus_00_dot_53_bar__plus_01_dot_42_bar__plus_02_dot_00 MirrorType)
        (objectType AlarmClock_bar__minus_02_dot_48_bar__plus_00_dot_68_bar__minus_03_dot_30 AlarmClockType)
        (objectType KeyChain_bar__plus_02_dot_76_bar__plus_01_dot_25_bar__plus_01_dot_77 KeyChainType)
        (objectType AlarmClock_bar__plus_02_dot_69_bar__plus_01_dot_23_bar__plus_01_dot_40 AlarmClockType)
        (objectType Window_bar__minus_00_dot_84_bar__plus_01_dot_39_bar__minus_03_dot_65 WindowType)
        (objectType Laptop_bar__minus_01_dot_31_bar__plus_00_dot_62_bar__minus_02_dot_06 LaptopType)
        (objectType Pen_bar__minus_02_dot_53_bar__plus_00_dot_69_bar__minus_03_dot_23 PenType)
        (objectType Pencil_bar__plus_02_dot_69_bar__plus_01_dot_23_bar__plus_01_dot_28 PencilType)
        (objectType CreditCard_bar__plus_02_dot_08_bar__plus_00_dot_08_bar__minus_03_dot_10 CreditCardType)
        (objectType CD_bar__plus_01_dot_87_bar__plus_00_dot_90_bar__minus_02_dot_98 CDType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType AlarmClockType)
        (canContain DeskType BowlType)
        (canContain DeskType CDType)
        (canContain DeskType PencilType)
        (canContain DeskType CreditCardType)
        (canContain DeskType PenType)
        (canContain DeskType LaptopType)
        (canContain DeskType MugType)
        (canContain DeskType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType AlarmClockType)
        (canContain SideTableType BowlType)
        (canContain SideTableType CDType)
        (canContain SideTableType PencilType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType PenType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType MugType)
        (canContain SideTableType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType BookType)
        (canContain BedType PillowType)
        (canContain BedType LaptopType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain SafeType CellPhoneType)
        (canContain SafeType KeyChainType)
        (canContain SafeType CDType)
        (canContain SafeType CreditCardType)
        (canContain BedType CellPhoneType)
        (canContain BedType BookType)
        (canContain BedType PillowType)
        (canContain BedType LaptopType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain GarbageCanType PenType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType AlarmClockType)
        (canContain SideTableType BowlType)
        (canContain SideTableType CDType)
        (canContain SideTableType PencilType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType PenType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType MugType)
        (canContain SideTableType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DresserType CellPhoneType)
        (canContain DresserType KeyChainType)
        (canContain DresserType AlarmClockType)
        (canContain DresserType BowlType)
        (canContain DresserType CDType)
        (canContain DresserType PencilType)
        (canContain DresserType CreditCardType)
        (canContain DresserType PenType)
        (canContain DresserType LaptopType)
        (canContain DresserType MugType)
        (canContain DresserType BookType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CDType)
        (canContain DrawerType PencilType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (pickupable CellPhone_bar__plus_01_dot_78_bar__plus_00_dot_36_bar__minus_03_dot_03)
        (pickupable Mug_bar__minus_02_dot_53_bar__plus_00_dot_68_bar__minus_00_dot_28)
        (pickupable Mug_bar__plus_02_dot_56_bar__plus_00_dot_89_bar__minus_03_dot_04)
        (pickupable TeddyBear_bar__minus_00_dot_67_bar__plus_00_dot_61_bar__minus_01_dot_36)
        (pickupable Pillow_bar__minus_01_dot_87_bar__plus_00_dot_68_bar__minus_02_dot_26)
        (pickupable CellPhone_bar__minus_02_dot_48_bar__plus_00_dot_68_bar__minus_03_dot_15)
        (pickupable Book_bar__plus_02_dot_35_bar__plus_00_dot_89_bar__minus_03_dot_16)
        (pickupable Laptop_bar__minus_01_dot_31_bar__plus_00_dot_61_bar__minus_01_dot_54)
        (pickupable KeyChain_bar__minus_02_dot_48_bar__plus_00_dot_08_bar__minus_03_dot_09)
        (pickupable Mug_bar__plus_02_dot_04_bar__plus_00_dot_89_bar__minus_03_dot_04)
        (pickupable Pen_bar__plus_02_dot_21_bar__plus_00_dot_90_bar__minus_03_dot_39)
        (pickupable CD_bar__minus_02_dot_44_bar__plus_00_dot_68_bar__minus_02_dot_92)
        (pickupable Pencil_bar__plus_01_dot_34_bar__plus_00_dot_02_bar__minus_03_dot_38)
        (pickupable Pillow_bar__minus_01_dot_31_bar__plus_00_dot_68_bar__minus_02_dot_46)
        (pickupable Bowl_bar__plus_01_dot_84_bar__plus_00_dot_89_bar__minus_03_dot_24)
        (pickupable Pen_bar__plus_02_dot_04_bar__plus_00_dot_90_bar__minus_03_dot_11)
        (pickupable CD_bar__plus_02_dot_61_bar__plus_00_dot_37_bar__plus_01_dot_76)
        (pickupable AlarmClock_bar__plus_02_dot_04_bar__plus_00_dot_90_bar__minus_03_dot_32)
        (pickupable CreditCard_bar__plus_02_dot_71_bar__plus_00_dot_92_bar__plus_01_dot_34)
        (pickupable CreditCard_bar__plus_02_dot_61_bar__plus_00_dot_37_bar__plus_01_dot_34)
        (pickupable AlarmClock_bar__minus_02_dot_48_bar__plus_00_dot_68_bar__minus_03_dot_30)
        (pickupable KeyChain_bar__plus_02_dot_76_bar__plus_01_dot_25_bar__plus_01_dot_77)
        (pickupable AlarmClock_bar__plus_02_dot_69_bar__plus_01_dot_23_bar__plus_01_dot_40)
        (pickupable Laptop_bar__minus_01_dot_31_bar__plus_00_dot_62_bar__minus_02_dot_06)
        (pickupable Pen_bar__minus_02_dot_53_bar__plus_00_dot_69_bar__minus_03_dot_23)
        (pickupable Pencil_bar__plus_02_dot_69_bar__plus_01_dot_23_bar__plus_01_dot_28)
        (pickupable CreditCard_bar__plus_02_dot_08_bar__plus_00_dot_08_bar__minus_03_dot_10)
        (pickupable CD_bar__plus_01_dot_87_bar__plus_00_dot_90_bar__minus_02_dot_98)
        (isReceptacleObject Mug_bar__minus_02_dot_53_bar__plus_00_dot_68_bar__minus_00_dot_28)
        (isReceptacleObject Mug_bar__plus_02_dot_56_bar__plus_00_dot_89_bar__minus_03_dot_04)
        (isReceptacleObject Mug_bar__plus_02_dot_04_bar__plus_00_dot_89_bar__minus_03_dot_04)
        (isReceptacleObject Bowl_bar__plus_01_dot_84_bar__plus_00_dot_89_bar__minus_03_dot_24)
        (openable Drawer_bar__minus_02_dot_42_bar__plus_00_dot_17_bar__minus_00_dot_51)
        (openable Drawer_bar__plus_02_dot_64_bar__plus_00_dot_44_bar__plus_01_dot_51)
        (openable Safe_bar__plus_02_dot_70_bar__plus_01_dot_23_bar__plus_01_dot_77)
        (openable Drawer_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_00_dot_51)
        (openable Drawer_bar__plus_01_dot_88_bar__plus_00_dot_73_bar__minus_02_dot_96)
        (openable Drawer_bar__plus_02_dot_64_bar__plus_00_dot_99_bar__plus_01_dot_51)
        (openable Drawer_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_03_dot_15)
        (openable Drawer_bar__plus_01_dot_88_bar__plus_00_dot_49_bar__minus_02_dot_96)
        (openable Drawer_bar__plus_02_dot_64_bar__plus_00_dot_16_bar__plus_01_dot_51)
        (openable Drawer_bar__plus_02_dot_64_bar__plus_00_dot_72_bar__plus_01_dot_51)
        (openable Drawer_bar__plus_01_dot_88_bar__plus_00_dot_21_bar__minus_02_dot_96)
        (openable Drawer_bar__minus_02_dot_42_bar__plus_00_dot_17_bar__minus_03_dot_15)
        
        (atLocation agent1 loc_bar_1_bar_4_bar_1_bar_30)
        
        (cleanable Mug_bar__minus_02_dot_53_bar__plus_00_dot_68_bar__minus_00_dot_28)
        (cleanable Mug_bar__plus_02_dot_56_bar__plus_00_dot_89_bar__minus_03_dot_04)
        (cleanable Mug_bar__plus_02_dot_04_bar__plus_00_dot_89_bar__minus_03_dot_04)
        (cleanable Bowl_bar__plus_01_dot_84_bar__plus_00_dot_89_bar__minus_03_dot_24)
        
        (heatable Mug_bar__minus_02_dot_53_bar__plus_00_dot_68_bar__minus_00_dot_28)
        (heatable Mug_bar__plus_02_dot_56_bar__plus_00_dot_89_bar__minus_03_dot_04)
        (heatable Mug_bar__plus_02_dot_04_bar__plus_00_dot_89_bar__minus_03_dot_04)
        (coolable Mug_bar__minus_02_dot_53_bar__plus_00_dot_68_bar__minus_00_dot_28)
        (coolable Mug_bar__plus_02_dot_56_bar__plus_00_dot_89_bar__minus_03_dot_04)
        (coolable Mug_bar__plus_02_dot_04_bar__plus_00_dot_89_bar__minus_03_dot_04)
        (coolable Bowl_bar__plus_01_dot_84_bar__plus_00_dot_89_bar__minus_03_dot_24)
        
        
        (toggleable DeskLamp_bar__plus_02_dot_76_bar__plus_00_dot_89_bar__minus_03_dot_32)
        (toggleable DeskLamp_bar__minus_02_dot_46_bar__plus_00_dot_67_bar__minus_00_dot_53)
        
        
        
        
        (inReceptacle Laptop_bar__minus_01_dot_31_bar__plus_00_dot_61_bar__minus_01_dot_54 Bed_bar__minus_01_dot_64_bar__minus_00_dot_04_bar__minus_01_dot_86)
        (inReceptacle TeddyBear_bar__minus_00_dot_67_bar__plus_00_dot_61_bar__minus_01_dot_36 Bed_bar__minus_01_dot_64_bar__minus_00_dot_04_bar__minus_01_dot_86)
        (inReceptacle Pillow_bar__minus_01_dot_87_bar__plus_00_dot_68_bar__minus_02_dot_26 Bed_bar__minus_01_dot_64_bar__minus_00_dot_04_bar__minus_01_dot_86)
        (inReceptacle Pillow_bar__minus_01_dot_31_bar__plus_00_dot_68_bar__minus_02_dot_46 Bed_bar__minus_01_dot_64_bar__minus_00_dot_04_bar__minus_01_dot_86)
        (inReceptacle Laptop_bar__minus_01_dot_31_bar__plus_00_dot_62_bar__minus_02_dot_06 Bed_bar__minus_01_dot_64_bar__minus_00_dot_04_bar__minus_01_dot_86)
        (inReceptacle CreditCard_bar__plus_02_dot_61_bar__plus_00_dot_37_bar__plus_01_dot_34 Drawer_bar__plus_02_dot_64_bar__plus_00_dot_44_bar__plus_01_dot_51)
        (inReceptacle CD_bar__plus_02_dot_61_bar__plus_00_dot_37_bar__plus_01_dot_76 Drawer_bar__plus_02_dot_64_bar__plus_00_dot_44_bar__plus_01_dot_51)
        (inReceptacle Pen_bar__minus_02_dot_53_bar__plus_00_dot_69_bar__minus_03_dot_23 SideTable_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_03_dot_15)
        (inReceptacle AlarmClock_bar__minus_02_dot_48_bar__plus_00_dot_68_bar__minus_03_dot_30 SideTable_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_03_dot_15)
        (inReceptacle CD_bar__minus_02_dot_44_bar__plus_00_dot_68_bar__minus_02_dot_92 SideTable_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_03_dot_15)
        (inReceptacle CellPhone_bar__minus_02_dot_48_bar__plus_00_dot_68_bar__minus_03_dot_15 SideTable_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_03_dot_15)
        (inReceptacle CreditCard_bar__plus_02_dot_71_bar__plus_00_dot_92_bar__plus_01_dot_34 Drawer_bar__plus_02_dot_64_bar__plus_00_dot_99_bar__plus_01_dot_51)
        (inReceptacle Book_bar__plus_02_dot_35_bar__plus_00_dot_89_bar__minus_03_dot_16 Desk_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__minus_03_dot_22)
        (inReceptacle Bowl_bar__plus_01_dot_84_bar__plus_00_dot_89_bar__minus_03_dot_24 Desk_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__minus_03_dot_22)
        (inReceptacle DeskLamp_bar__plus_02_dot_76_bar__plus_00_dot_89_bar__minus_03_dot_32 Desk_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__minus_03_dot_22)
        (inReceptacle Pen_bar__plus_02_dot_04_bar__plus_00_dot_90_bar__minus_03_dot_11 Desk_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__minus_03_dot_22)
        (inReceptacle Mug_bar__plus_02_dot_04_bar__plus_00_dot_89_bar__minus_03_dot_04 Desk_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__minus_03_dot_22)
        (inReceptacle AlarmClock_bar__plus_02_dot_04_bar__plus_00_dot_90_bar__minus_03_dot_32 Desk_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__minus_03_dot_22)
        (inReceptacle Pen_bar__plus_02_dot_21_bar__plus_00_dot_90_bar__minus_03_dot_39 Desk_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__minus_03_dot_22)
        (inReceptacle Mug_bar__plus_02_dot_56_bar__plus_00_dot_89_bar__minus_03_dot_04 Desk_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__minus_03_dot_22)
        (inReceptacle CD_bar__plus_01_dot_87_bar__plus_00_dot_90_bar__minus_02_dot_98 Desk_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__minus_03_dot_22)
        (inReceptacle KeyChain_bar__minus_02_dot_48_bar__plus_00_dot_08_bar__minus_03_dot_09 Drawer_bar__minus_02_dot_42_bar__plus_00_dot_17_bar__minus_03_dot_15)
        (inReceptacle DeskLamp_bar__minus_02_dot_46_bar__plus_00_dot_67_bar__minus_00_dot_53 SideTable_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_00_dot_51)
        (inReceptacle Mug_bar__minus_02_dot_53_bar__plus_00_dot_68_bar__minus_00_dot_28 SideTable_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_00_dot_51)
        (inReceptacle CreditCard_bar__plus_02_dot_08_bar__plus_00_dot_08_bar__minus_03_dot_10 Drawer_bar__plus_01_dot_88_bar__plus_00_dot_21_bar__minus_02_dot_96)
        (inReceptacle AlarmClock_bar__plus_02_dot_69_bar__plus_01_dot_23_bar__plus_01_dot_40 Dresser_bar__plus_02_dot_68_bar_00_dot_00_bar__plus_01_dot_51)
        (inReceptacle Pencil_bar__plus_02_dot_69_bar__plus_01_dot_23_bar__plus_01_dot_28 Dresser_bar__plus_02_dot_68_bar_00_dot_00_bar__plus_01_dot_51)
        (inReceptacle KeyChain_bar__plus_02_dot_76_bar__plus_01_dot_25_bar__plus_01_dot_77 Dresser_bar__plus_02_dot_68_bar_00_dot_00_bar__plus_01_dot_51)
        (inReceptacle CellPhone_bar__plus_01_dot_78_bar__plus_00_dot_36_bar__minus_03_dot_03 Drawer_bar__plus_01_dot_88_bar__plus_00_dot_49_bar__minus_02_dot_96)
        (inReceptacle Pencil_bar__plus_01_dot_34_bar__plus_00_dot_02_bar__minus_03_dot_38 GarbageCan_bar__plus_01_dot_34_bar__plus_00_dot_00_bar__minus_03_dot_38)
        (inReceptacle KeyChain_bar__plus_02_dot_76_bar__plus_01_dot_25_bar__plus_01_dot_77 Safe_bar__plus_02_dot_70_bar__plus_01_dot_23_bar__plus_01_dot_77)
        
        
        (receptacleAtLocation Bed_bar__plus_02_dot_40_bar__plus_00_dot_01_bar__minus_00_dot_07 loc_bar_5_bar_0_bar_1_bar_60)
        (receptacleAtLocation Bed_bar__minus_01_dot_64_bar__minus_00_dot_04_bar__minus_01_dot_86 loc_bar_0_bar__minus_8_bar_3_bar_45)
        (receptacleAtLocation Desk_bar__plus_02_dot_32_bar__minus_00_dot_01_bar__minus_03_dot_22 loc_bar_8_bar__minus_10_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__plus_01_dot_88_bar__plus_00_dot_21_bar__minus_02_dot_96 loc_bar_3_bar__minus_9_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_01_dot_88_bar__plus_00_dot_49_bar__minus_02_dot_96 loc_bar_3_bar__minus_9_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_01_dot_88_bar__plus_00_dot_73_bar__minus_02_dot_96 loc_bar_4_bar__minus_10_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_02_dot_64_bar__plus_00_dot_16_bar__plus_01_dot_51 loc_bar_6_bar_4_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_02_dot_64_bar__plus_00_dot_44_bar__plus_01_dot_51 loc_bar_6_bar_5_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_02_dot_64_bar__plus_00_dot_72_bar__plus_01_dot_51 loc_bar_7_bar_6_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__plus_02_dot_64_bar__plus_00_dot_99_bar__plus_01_dot_51 loc_bar_8_bar_6_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_42_bar__plus_00_dot_17_bar__minus_00_dot_51 loc_bar__minus_5_bar__minus_3_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_42_bar__plus_00_dot_17_bar__minus_03_dot_15 loc_bar__minus_4_bar__minus_12_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_00_dot_51 loc_bar__minus_6_bar__minus_3_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_03_dot_15 loc_bar__minus_5_bar__minus_12_bar_3_bar_45)
        (receptacleAtLocation Dresser_bar__plus_02_dot_68_bar_00_dot_00_bar__plus_01_dot_51 loc_bar_9_bar_5_bar_1_bar_60)
        (receptacleAtLocation GarbageCan_bar__plus_01_dot_34_bar__plus_00_dot_00_bar__minus_03_dot_38 loc_bar_5_bar__minus_12_bar_2_bar_60)
        (receptacleAtLocation Safe_bar__plus_02_dot_70_bar__plus_01_dot_23_bar__plus_01_dot_77 loc_bar_9_bar_6_bar_1_bar_45)
        (receptacleAtLocation SideTable_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_00_dot_51 loc_bar__minus_7_bar__minus_2_bar_3_bar_60)
        (receptacleAtLocation SideTable_bar__minus_02_dot_42_bar__plus_00_dot_46_bar__minus_03_dot_15 loc_bar__minus_7_bar__minus_12_bar_3_bar_60)
        (objectAtLocation Mug_bar__plus_02_dot_04_bar__plus_00_dot_89_bar__minus_03_dot_04 loc_bar_8_bar__minus_10_bar_2_bar_60)
        (objectAtLocation CD_bar__plus_02_dot_61_bar__plus_00_dot_37_bar__plus_01_dot_76 loc_bar_6_bar_5_bar_1_bar_45)
        (objectAtLocation KeyChain_bar__plus_02_dot_76_bar__plus_01_dot_25_bar__plus_01_dot_77 loc_bar_9_bar_6_bar_1_bar_45)
        (objectAtLocation CellPhone_bar__plus_01_dot_78_bar__plus_00_dot_36_bar__minus_03_dot_03 loc_bar_3_bar__minus_9_bar_1_bar_45)
        (objectAtLocation AlarmClock_bar__plus_02_dot_69_bar__plus_01_dot_23_bar__plus_01_dot_40 loc_bar_9_bar_5_bar_1_bar_60)
        (objectAtLocation Laptop_bar__minus_01_dot_31_bar__plus_00_dot_61_bar__minus_01_dot_54 loc_bar_0_bar__minus_8_bar_3_bar_45)
        (objectAtLocation Pencil_bar__plus_02_dot_69_bar__plus_01_dot_23_bar__plus_01_dot_28 loc_bar_9_bar_5_bar_1_bar_60)
        (objectAtLocation Pen_bar__plus_02_dot_21_bar__plus_00_dot_90_bar__minus_03_dot_39 loc_bar_8_bar__minus_10_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__plus_02_dot_71_bar__plus_00_dot_92_bar__plus_01_dot_34 loc_bar_8_bar_6_bar_1_bar_45)
        (objectAtLocation AlarmClock_bar__plus_02_dot_04_bar__plus_00_dot_90_bar__minus_03_dot_32 loc_bar_8_bar__minus_10_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__plus_02_dot_61_bar__plus_00_dot_37_bar__plus_01_dot_34 loc_bar_6_bar_5_bar_1_bar_45)
        (objectAtLocation Pen_bar__minus_02_dot_53_bar__plus_00_dot_69_bar__minus_03_dot_23 loc_bar__minus_7_bar__minus_12_bar_3_bar_60)
        (objectAtLocation CD_bar__minus_02_dot_44_bar__plus_00_dot_68_bar__minus_02_dot_92 loc_bar__minus_7_bar__minus_12_bar_3_bar_60)
        (objectAtLocation Mug_bar__minus_02_dot_53_bar__plus_00_dot_68_bar__minus_00_dot_28 loc_bar__minus_7_bar__minus_2_bar_3_bar_60)
        (objectAtLocation Book_bar__plus_02_dot_35_bar__plus_00_dot_89_bar__minus_03_dot_16 loc_bar_8_bar__minus_10_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__plus_02_dot_08_bar__plus_00_dot_08_bar__minus_03_dot_10 loc_bar_3_bar__minus_9_bar_1_bar_45)
        (objectAtLocation Pen_bar__plus_02_dot_04_bar__plus_00_dot_90_bar__minus_03_dot_11 loc_bar_8_bar__minus_10_bar_2_bar_60)
        (objectAtLocation DeskLamp_bar__plus_02_dot_76_bar__plus_00_dot_89_bar__minus_03_dot_32 loc_bar_8_bar__minus_10_bar_2_bar_60)
        (objectAtLocation Pencil_bar__plus_01_dot_34_bar__plus_00_dot_02_bar__minus_03_dot_38 loc_bar_5_bar__minus_12_bar_2_bar_60)
        (objectAtLocation DeskLamp_bar__minus_02_dot_46_bar__plus_00_dot_67_bar__minus_00_dot_53 loc_bar__minus_7_bar__minus_2_bar_3_bar_60)
        (objectAtLocation LightSwitch_bar__minus_00_dot_70_bar__plus_01_dot_02_bar__plus_00_dot_33 loc_bar__minus_1_bar_1_bar_3_bar_60)
        (objectAtLocation TeddyBear_bar__minus_00_dot_67_bar__plus_00_dot_61_bar__minus_01_dot_36 loc_bar_0_bar__minus_8_bar_3_bar_45)
        (objectAtLocation Laptop_bar__minus_01_dot_31_bar__plus_00_dot_62_bar__minus_02_dot_06 loc_bar_0_bar__minus_8_bar_3_bar_45)
        (objectAtLocation AlarmClock_bar__minus_02_dot_48_bar__plus_00_dot_68_bar__minus_03_dot_30 loc_bar__minus_7_bar__minus_12_bar_3_bar_60)
        (objectAtLocation CellPhone_bar__minus_02_dot_48_bar__plus_00_dot_68_bar__minus_03_dot_15 loc_bar__minus_7_bar__minus_12_bar_3_bar_60)
        (objectAtLocation KeyChain_bar__minus_02_dot_48_bar__plus_00_dot_08_bar__minus_03_dot_09 loc_bar__minus_4_bar__minus_12_bar_3_bar_45)
        (objectAtLocation Pillow_bar__minus_01_dot_87_bar__plus_00_dot_68_bar__minus_02_dot_26 loc_bar_0_bar__minus_8_bar_3_bar_45)
        (objectAtLocation Pillow_bar__minus_01_dot_31_bar__plus_00_dot_68_bar__minus_02_dot_46 loc_bar_0_bar__minus_8_bar_3_bar_45)
        (objectAtLocation Mirror_bar__plus_00_dot_53_bar__plus_01_dot_42_bar__plus_02_dot_00 loc_bar_2_bar_6_bar_0_bar_30)
        (objectAtLocation CD_bar__plus_01_dot_87_bar__plus_00_dot_90_bar__minus_02_dot_98 loc_bar_8_bar__minus_10_bar_2_bar_60)
        (objectAtLocation Window_bar__minus_00_dot_84_bar__plus_01_dot_39_bar__minus_03_dot_65 loc_bar__minus_3_bar__minus_12_bar_2_bar_15)
        (objectAtLocation Window_bar__plus_02_dot_96_bar__plus_01_dot_51_bar__minus_01_dot_02 loc_bar_9_bar__minus_6_bar_1_bar_15)
        (objectAtLocation Chair_bar__plus_02_dot_58_bar__plus_00_dot_00_bar__minus_02_dot_88 loc_bar_8_bar__minus_10_bar_1_bar_60)
        (objectAtLocation Mug_bar__plus_02_dot_56_bar__plus_00_dot_89_bar__minus_03_dot_04 loc_bar_8_bar__minus_10_bar_2_bar_60)
        (objectAtLocation Blinds_bar__minus_00_dot_80_bar__plus_02_dot_09_bar__minus_03_dot_63 loc_bar__minus_3_bar__minus_12_bar_2_bar__minus_30)
        (objectAtLocation Blinds_bar__plus_02_dot_93_bar__plus_02_dot_17_bar__minus_01_dot_00 loc_bar_9_bar__minus_6_bar_1_bar__minus_30)
        (objectAtLocation Bowl_bar__plus_01_dot_84_bar__plus_00_dot_89_bar__minus_03_dot_24 loc_bar_8_bar__minus_10_bar_2_bar_60)
        )
    

        (:goal
             (and
                 (exists (?ot - object
                          ?r - receptacle
                          ?a - agent
                          ?l - location)
                     (and
                         (objectType ?ot DeskLampType)
                         (toggleable ?ot)
                         (isToggled ?ot)
                         (receptacleAtLocation ?r ?l)
                         (atLocation ?a ?l)
                         (inReceptacle ?ot ?r)
                     )
                 )
                 (exists (?o - object
                          ?a - agent)
                     (and
                         (objectType ?o AlarmClockType)
                         (holds ?a ?o)
                     )
                 )
             )
        )
    )
    