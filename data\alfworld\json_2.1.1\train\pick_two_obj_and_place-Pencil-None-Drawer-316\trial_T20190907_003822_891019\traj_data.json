{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000032.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000033.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000034.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000035.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000036.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000037.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000038.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 14}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 16}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 17}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 17}, {"high_idx": 4, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000132.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000133.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000135.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000136.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000137.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000138.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000139.png", "low_idx": 18}, {"high_idx": 4, "image_name": "000000140.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 19}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 20}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 21}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 22}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000167.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000168.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 24}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000182.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000183.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000184.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000185.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000186.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000187.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000188.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000189.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000190.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000191.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 25}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 25}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 26}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 27}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 36}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 37}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Pencil", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["pencil"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pencil", [-1.9481076, -1.9481076, 1.710108636, 1.710108636, 2.6656908, 2.6656908]], "coordinateReceptacleObjectId": ["SideTable", [-2.244, -2.244, 1.964, 1.964, 1.81736, 1.81736]], "forceVisible": true, "objectId": "Pencil|-00.49|+00.67|+00.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|0|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["pencil", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pencil", [-1.9481076, -1.9481076, 1.710108636, 1.710108636, 2.6656908, 2.6656908]], "coordinateReceptacleObjectId": ["Drawer", [-2.244, -2.244, 1.964, 1.964, 1.81736, 1.81736]], "forceVisible": true, "objectId": "Pencil|-00.49|+00.67|+00.43", "receptacleObjectId": "Drawer|-00.56|+00.45|+00.49"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|0|2|3|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["pencil"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Pencil", [-1.9481072, -1.9481072, 2.5726808, 2.5726808, 2.6656908, 2.6656908]], "coordinateReceptacleObjectId": ["SideTable", [-2.244, -2.244, 1.964, 1.964, 1.81736, 1.81736]], "forceVisible": true, "objectId": "Pencil|-00.49|+00.67|+00.64"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|1|0|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["pencil", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Pencil", [-1.9481072, -1.9481072, 2.5726808, 2.5726808, 2.6656908, 2.6656908]], "coordinateReceptacleObjectId": ["Drawer", [-2.244, -2.244, 1.964, 1.964, 1.81736, 1.81736]], "forceVisible": true, "objectId": "Pencil|-00.49|+00.67|+00.64", "receptacleObjectId": "Drawer|-00.56|+00.45|+00.49"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Pencil|-00.49|+00.67|+00.43"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [131, 173, 173, 174], "mask": [[51731, 43], [52034, 40]], "point": [152, 172]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-00.56|+00.45|+00.49"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [211, 69, 241, 176], "mask": [[20621, 4], [20921, 5], [21220, 6], [21520, 6], [21820, 6], [22119, 8], [22419, 8], [22719, 8], [23019, 8], [23318, 9], [23618, 10], [23918, 10], [24217, 11], [24517, 11], [24817, 11], [25116, 13], [25416, 13], [25716, 13], [26015, 14], [26315, 14], [26615, 15], [26915, 15], [27214, 16], [27514, 16], [27814, 16], [28113, 18], [28413, 18], [28713, 18], [29012, 19], [29312, 19], [29612, 20], [29912, 20], [30211, 21], [30511, 21], [30811, 21], [31111, 22], [31411, 22], [31711, 22], [32011, 22], [32311, 23], [32612, 22], [32912, 22], [33212, 22], [33512, 22], [33812, 23], [34112, 23], [34412, 23], [34713, 22], [35013, 22], [35313, 23], [35613, 23], [35913, 23], [36213, 23], [36514, 22], [36814, 23], [37114, 23], [37414, 23], [37714, 23], [38014, 23], [38314, 24], [38615, 23], [38915, 23], [39215, 23], [39515, 23], [39815, 24], [40115, 24], [40416, 23], [40716, 23], [41016, 23], [41316, 24], [41616, 24], [41916, 24], [42216, 24], [42517, 23], [42817, 24], [43117, 24], [43417, 24], [43717, 24], [44017, 25], [44317, 25], [44618, 24], [44918, 24], [45218, 23], [45518, 22], [45818, 22], [46118, 21], [46419, 19], [46719, 19], [47019, 18], [47319, 18], [47619, 17], [47919, 16], [48219, 16], [48520, 14], [48820, 14], [49120, 13], [49420, 12], [49720, 12], [50020, 11], [50320, 11], [50621, 9], [50921, 8], [51221, 8], [51521, 7], [51821, 6], [52121, 6], [52422, 4], [52723, 2]], "point": [226, 121]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pencil|-00.49|+00.67|+00.43", "placeStationary": true, "receptacleObjectId": "Drawer|-00.56|+00.45|+00.49"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [181, 69, 241, 176], "mask": [[20587, 4], [20886, 5], [21186, 5], [21486, 40], [21786, 40], [22086, 41], [22386, 41], [22685, 42], [22985, 42], [23285, 42], [23585, 43], [23885, 43], [24185, 43], [24484, 44], [24784, 44], [25084, 45], [25384, 45], [25684, 45], [25984, 45], [26283, 46], [26583, 47], [26883, 47], [27183, 47], [27483, 47], [27783, 47], [28083, 48], [28382, 49], [28682, 49], [28982, 49], [29282, 49], [29582, 50], [29882, 50], [30181, 51], [30481, 51], [30781, 51], [31081, 52], [31381, 52], [31681, 52], [31981, 52], [32282, 52], [32582, 52], [32882, 52], [33182, 52], [33482, 52], [33782, 53], [34082, 53], [34382, 53], [34682, 53], [34982, 53], [35282, 54], [35582, 54], [35882, 54], [36183, 53], [36483, 53], [36783, 54], [37083, 22], [37106, 2], [37118, 19], [37383, 17], [37405, 2], [37418, 19], [37683, 19], [37706, 1], [37718, 19], [37983, 18], [38019, 18], [38283, 17], [38305, 4], [38318, 20], [38583, 55], [38883, 55], [39183, 55], [39483, 55], [39784, 55], [40084, 55], [40384, 55], [40684, 55], [40984, 55], [41284, 56], [41584, 56], [41884, 56], [42184, 56], [42484, 56], [42784, 57], [43084, 57], [43384, 57], [43685, 56], [43985, 57], [44285, 57], [44585, 57], [44885, 57], [45185, 56], [45485, 55], [45785, 55], [46085, 54], [46385, 53], [46685, 53], [46985, 52], [47285, 52], [47586, 50], [47886, 49], [48186, 49], [48486, 48], [48786, 48], [49086, 47], [49386, 46], [49686, 46], [49986, 45], [50286, 45], [50586, 44], [50886, 43], [51186, 43], [51487, 41], [51787, 5], [52087, 5], [52387, 4], [52688, 2]], "point": [211, 121]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-00.56|+00.45|+00.49"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [181, 69, 241, 176], "mask": [[20587, 4], [20886, 5], [21186, 5], [21486, 40], [21786, 40], [22086, 41], [22386, 41], [22685, 42], [22985, 42], [23285, 42], [23585, 43], [23885, 43], [24185, 43], [24484, 44], [24784, 44], [25084, 45], [25384, 45], [25684, 45], [25984, 45], [26283, 46], [26583, 47], [26883, 47], [27183, 47], [27483, 47], [27783, 47], [28083, 48], [28382, 49], [28682, 49], [28982, 49], [29282, 49], [29582, 50], [29882, 50], [30181, 51], [30481, 51], [30781, 51], [31081, 52], [31381, 52], [31681, 52], [31981, 52], [32282, 52], [32582, 52], [32882, 52], [33182, 52], [33482, 52], [33782, 53], [34082, 53], [34382, 53], [34682, 53], [34982, 53], [35282, 54], [35582, 54], [35882, 54], [36183, 53], [36483, 53], [36783, 54], [37083, 22], [37106, 2], [37118, 19], [37383, 17], [37405, 2], [37418, 19], [37683, 19], [37706, 1], [37718, 19], [37983, 18], [38018, 19], [38283, 17], [38305, 4], [38318, 20], [38583, 55], [38883, 55], [39183, 55], [39483, 55], [39784, 55], [40084, 55], [40384, 55], [40684, 55], [40984, 36], [41021, 18], [41284, 36], [41322, 18], [41584, 36], [41622, 18], [41884, 37], [41922, 18], [42184, 37], [42222, 18], [42484, 37], [42522, 18], [42784, 37], [42822, 19], [43084, 37], [43123, 18], [43384, 37], [43423, 18], [43685, 56], [43985, 57], [44285, 57], [44585, 57], [44885, 57], [45185, 56], [45485, 55], [45785, 55], [46085, 54], [46385, 53], [46685, 53], [46985, 52], [47285, 52], [47586, 50], [47886, 49], [48186, 49], [48486, 48], [48786, 48], [49086, 47], [49386, 46], [49686, 46], [49986, 45], [50286, 45], [50586, 44], [50886, 43], [51186, 43], [51487, 41], [51787, 5], [52087, 5], [52387, 4], [52688, 2]], "point": [211, 121]}}, "high_idx": 3}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Pencil|-00.49|+00.67|+00.64"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [184, 141, 187, 178], "mask": [[42184, 1], [42484, 1], [42784, 1], [43084, 2], [43384, 2], [43684, 2], [43984, 2], [44284, 2], [44584, 2], [44884, 2], [45184, 2], [45484, 2], [45784, 2], [46084, 2], [46385, 1], [46685, 2], [46985, 2], [47285, 2], [47585, 2], [47885, 2], [48185, 2], [48485, 2], [48785, 2], [49085, 2], [49385, 2], [49685, 2], [49985, 2], [50285, 3], [50586, 2], [50886, 2], [51186, 2], [51486, 2], [51786, 2], [52086, 2], [52386, 2], [52686, 2], [52986, 2], [53286, 2]], "point": [185, 158]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-00.56|+00.45|+00.49"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [211, 69, 241, 176], "mask": [[20621, 4], [20921, 5], [21220, 6], [21520, 6], [21820, 6], [22119, 8], [22419, 8], [22719, 8], [23019, 8], [23318, 9], [23618, 10], [23918, 10], [24217, 11], [24517, 11], [24817, 11], [25116, 13], [25416, 13], [25716, 13], [26015, 14], [26315, 14], [26615, 15], [26915, 15], [27214, 16], [27514, 16], [27814, 16], [28113, 18], [28413, 18], [28713, 18], [29012, 19], [29312, 19], [29612, 20], [29912, 20], [30211, 21], [30511, 21], [30811, 21], [31111, 22], [31411, 22], [31711, 22], [32011, 22], [32311, 23], [32612, 22], [32912, 22], [33212, 22], [33512, 22], [33812, 23], [34112, 23], [34412, 23], [34713, 22], [35013, 22], [35313, 23], [35613, 23], [35913, 23], [36213, 23], [36514, 22], [36814, 23], [37114, 23], [37414, 23], [37714, 23], [38014, 23], [38314, 24], [38615, 23], [38915, 23], [39215, 23], [39515, 23], [39815, 24], [40115, 24], [40416, 23], [40716, 23], [41016, 23], [41316, 24], [41616, 24], [41916, 24], [42216, 24], [42517, 23], [42817, 24], [43117, 24], [43417, 24], [43717, 24], [44017, 25], [44317, 25], [44618, 24], [44918, 24], [45218, 23], [45518, 22], [45818, 22], [46118, 21], [46419, 19], [46719, 19], [47019, 18], [47319, 18], [47619, 17], [47919, 16], [48219, 16], [48520, 14], [48820, 14], [49120, 13], [49420, 12], [49720, 12], [50020, 11], [50320, 11], [50621, 9], [50921, 8], [51221, 8], [51521, 7], [51821, 6], [52121, 6], [52422, 4], [52723, 2]], "point": [226, 121]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Pencil|-00.49|+00.67|+00.64", "placeStationary": true, "receptacleObjectId": "Drawer|-00.56|+00.45|+00.49"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [181, 69, 241, 176], "mask": [[20587, 4], [20886, 5], [21186, 5], [21486, 40], [21786, 40], [22086, 41], [22386, 41], [22685, 42], [22985, 42], [23285, 42], [23585, 43], [23885, 43], [24185, 43], [24484, 44], [24784, 44], [25084, 45], [25384, 45], [25684, 45], [25984, 45], [26283, 46], [26583, 47], [26883, 47], [27183, 47], [27483, 47], [27783, 47], [28083, 48], [28382, 49], [28682, 49], [28982, 49], [29282, 49], [29582, 50], [29882, 50], [30181, 51], [30481, 51], [30781, 51], [31081, 52], [31381, 52], [31681, 52], [31981, 52], [32282, 52], [32582, 52], [32882, 52], [33182, 52], [33482, 52], [33782, 53], [34082, 53], [34382, 53], [34682, 53], [34982, 53], [35282, 54], [35582, 54], [35882, 54], [36183, 53], [36483, 53], [36783, 54], [37083, 25], [37118, 19], [37383, 19], [37405, 2], [37418, 19], [37683, 18], [37705, 2], [37719, 18], [37983, 19], [38019, 18], [38283, 17], [38305, 4], [38318, 20], [38583, 20], [38605, 33], [38883, 55], [39183, 55], [39483, 55], [39784, 55], [40084, 55], [40384, 55], [40684, 55], [40984, 36], [41021, 18], [41284, 36], [41322, 18], [41584, 36], [41622, 18], [41884, 37], [41922, 18], [42184, 37], [42222, 18], [42484, 37], [42522, 18], [42784, 37], [42822, 19], [43084, 37], [43123, 18], [43384, 37], [43423, 18], [43685, 56], [43985, 57], [44285, 57], [44585, 57], [44885, 57], [45185, 56], [45485, 55], [45785, 55], [46085, 54], [46385, 53], [46685, 53], [46985, 52], [47285, 52], [47586, 50], [47886, 49], [48186, 49], [48486, 48], [48786, 48], [49086, 47], [49386, 46], [49686, 46], [49986, 45], [50286, 45], [50586, 44], [50886, 43], [51186, 43], [51487, 41], [51787, 5], [52087, 5], [52387, 4], [52688, 2]], "point": [211, 121]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-00.56|+00.45|+00.49"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [181, 69, 241, 176], "mask": [[20587, 4], [20886, 5], [21186, 5], [21486, 40], [21786, 40], [22086, 41], [22386, 41], [22685, 42], [22985, 42], [23285, 42], [23585, 43], [23885, 43], [24185, 43], [24484, 44], [24784, 44], [25084, 45], [25384, 45], [25684, 45], [25984, 45], [26283, 46], [26583, 47], [26883, 47], [27183, 47], [27483, 47], [27783, 47], [28083, 48], [28382, 49], [28682, 49], [28982, 49], [29282, 49], [29582, 50], [29882, 50], [30181, 51], [30481, 51], [30781, 51], [31081, 52], [31381, 52], [31681, 52], [31981, 52], [32282, 52], [32582, 52], [32882, 52], [33182, 52], [33482, 52], [33782, 53], [34082, 53], [34382, 53], [34682, 53], [34982, 53], [35282, 54], [35582, 54], [35882, 54], [36183, 53], [36483, 53], [36783, 54], [37083, 25], [37118, 19], [37383, 17], [37401, 1], [37405, 2], [37418, 19], [37683, 18], [37705, 2], [37719, 18], [37983, 18], [38019, 18], [38283, 17], [38305, 4], [38318, 20], [38583, 20], [38605, 33], [38883, 55], [39183, 55], [39483, 55], [39784, 46], [39831, 8], [40084, 45], [40131, 8], [40384, 45], [40430, 9], [40684, 44], [40730, 9], [40984, 36], [41021, 7], [41030, 9], [41284, 36], [41322, 5], [41329, 11], [41584, 36], [41622, 5], [41629, 11], [41884, 37], [41922, 4], [41928, 12], [42184, 37], [42222, 4], [42228, 12], [42484, 37], [42522, 4], [42527, 13], [42784, 37], [42822, 3], [42827, 14], [43084, 37], [43123, 2], [43126, 15], [43384, 37], [43423, 1], [43426, 15], [43685, 56], [43985, 57], [44285, 57], [44585, 57], [44885, 57], [45185, 56], [45485, 55], [45785, 55], [46085, 54], [46385, 53], [46685, 53], [46985, 52], [47285, 52], [47586, 50], [47886, 49], [48186, 49], [48486, 48], [48786, 48], [49086, 47], [49386, 46], [49686, 46], [49986, 45], [50286, 45], [50586, 44], [50886, 43], [51186, 43], [51487, 41], [51787, 5], [52087, 5], [52387, 4], [52688, 2]], "point": [211, 121]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan316", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -0.5, "y": 0.901932836, "z": -1.0}, "object_poses": [{"objectName": "Pencil_afd1b200", "position": {"x": 1.51008677, "y": 0.477057278, "z": -0.7647059}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pencil_afd1b200", "position": {"x": 1.029933, "y": 0.6973266, "z": -2.05565429}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pencil_afd1b200", "position": {"x": -0.4870268, "y": 0.6664227, "z": 0.6431702}, "rotation": {"x": 0.0, "y": 180.000031, "z": 0.0}}, {"objectName": "CreditCard_2969ac05", "position": {"x": -0.72453326, "y": 0.090854764, "z": 0.503363967}, "rotation": {"x": 0.0, "y": 3.585849e-05, "z": 0.0}}, {"objectName": "CreditCard_2969ac05", "position": {"x": -0.72453326, "y": 0.3697557, "z": 0.5262183}, "rotation": {"x": 0.0, "y": 3.585849e-05, "z": 0.0}}, {"objectName": "KeyChain_bd0ed82d", "position": {"x": -0.615511, "y": 0.370035619, "z": 0.549072444}, "rotation": {"x": 0.0, "y": 270.000031, "z": 0.0}}, {"objectName": "KeyChain_bd0ed82d", "position": {"x": 1.4567802, "y": 0.473863482, "z": -0.813477039}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Laptop_c1715826", "position": {"x": 1.58017755, "y": 0.6919857, "z": -1.73533857}, "rotation": {"x": -3.474696e-07, "y": 18.3543167, "z": 2.15077443e-06}}, {"objectName": "Laptop_c1715826", "position": {"x": -1.34675694, "y": 0.628554463, "z": -0.74808085}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Box_771c8efc", "position": {"x": -0.0156201124, "y": 0.239, "z": -2.09146261}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Book_b7b0e878", "position": {"x": -1.87187588, "y": 0.629234433, "z": -1.29797554}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "BaseballBat_c1a967ec", "position": {"x": 1.798, "y": 0.639, "z": 0.42537415}, "rotation": {"x": 0.0, "y": 0.0, "z": 342.3163}}, {"objectName": "Pen_a89f75dd", "position": {"x": -0.708946168, "y": 0.66873467, "z": 0.600041747}, "rotation": {"x": 0.0, "y": 180.000031, "z": 0.0}}, {"objectName": "Pencil_afd1b200", "position": {"x": -0.4870269, "y": 0.6664227, "z": 0.427527159}, "rotation": {"x": 0.0, "y": 180.000031, "z": 0.0}}, {"objectName": "KeyChain_bd0ed82d", "position": {"x": -0.397466719, "y": 0.09100583, "z": 0.48050943}, "rotation": {"x": 0.0, "y": 90.00004, "z": 0.0}}, {"objectName": "Pillow_924304f5", "position": {"x": -1.34675694, "y": 0.7038552, "z": -0.198186278}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_2969ac05", "position": {"x": -0.5064889, "y": 0.3697557, "z": 0.457655251}, "rotation": {"x": 0.0, "y": 3.585849e-05, "z": 0.0}}, {"objectName": "CellPhone_a5eb5319", "position": {"x": -1.87187588, "y": 0.6314814, "z": -0.198186278}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_c1715826", "position": {"x": -1.34675694, "y": 0.628554463, "z": -1.29797554}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CD_028cd2e5", "position": {"x": 1.55981791, "y": 0.6935924, "z": -0.906795561}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_38c8cac2", "position": {"x": -0.4345138, "y": 0.6954199, "z": -2.06446362}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "AlarmClock_3dad19ce", "position": {"x": -0.48702684, "y": 0.6648126, "z": 0.556912959}, "rotation": {"x": 0.0, "y": 3.86373831e-05, "z": 0.0}}], "object_toggles": [], "random_seed": 3122507502, "scene_num": 316}, "task_id": "trial_T20190907_003822_891019", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3C81THYYSBGVD_3WAKVUDHUZXJH04C7B5GNDVJ12HU7L", "high_descs": ["Walk forward to the side table by the bed.", "Take the blue pencil from the table.", "Turn around, then face the table again and open the drawer.", "Put the blue pencil in the drawer and close it.", "Turn right, then left again to face the drawer.", "Pick up the blue pencil from behind the alarm clock.", "Turn left and face the drawer in front of you.", "Open the drawer and put the pencil inside and close it."], "task_desc": "Put pencil away in a drawer.", "votes": [1, 1, 1, 1, 1]}, {"assignment_id": "A1ZE52NWZPN85P_3SKEMFQBZ6M9EI4JN2TKVQIGBWI8KL", "high_descs": ["Move forwards towards the bedside cabinet", "Pick up a pencil off of the bedside cabinet", "Turn to your right three times so you are facing the bed but a little ways away", "Open the bedside cabinet drawer and put the pencil in, then close it.", "Turn to your right, and then left so you are facing the side of the bedside cabinet", "Pick up a pencil off of the bedside cabinet", "Return to where you were just standing previously", "Open the bedside cabinet drawer and put the pencil in, then close it."], "task_desc": "Place two pencils in a bedside cabinet", "votes": [1, 1, 1]}, {"assignment_id": "A3A0VPWPASCO9J_3HHRAGRYXBMSIE88QMRR23U0E7HO9G", "high_descs": ["Walk to the end table next to the bed.", "Pick up the blue pencil in front of the alarm clock.", "Walk to the side of the end table.", "Open the drawer in the end table, put the pencil inside, and close the drawer.", "Walk back up to the end table.", "Pick up the blue pencil to the right of the alarm clock.", "Walk back to the front of the end table.", "Open the drawer in the end table, put the pencil inside, and close the drawer."], "task_desc": "Put the pencils in the drawer of the end table.", "votes": [1, 1]}, {"assignment_id": "A3MLUEOP3CCLXL_3X4MXAO0BJ56RNDWBPINAI7GNGXRWZ", "high_descs": ["Walk to the black night stand in front of you", "Pick up the blue pencil that is closest to you", "Take a step to the right of the night stand ", "Open top drawer of the night stand and place pencil inside then close the drawer", "Walk back to the night stand in front of you", "Pick up the pencil that is behind the alarm clock", "Open top drawer of the night stand and place pencil inside on top of the previously placed pencil", "Close the drawer of the night stand. "], "task_desc": "Place pencils in the top drawer of a night stand ", "votes": [1, 1, 0]}]}}