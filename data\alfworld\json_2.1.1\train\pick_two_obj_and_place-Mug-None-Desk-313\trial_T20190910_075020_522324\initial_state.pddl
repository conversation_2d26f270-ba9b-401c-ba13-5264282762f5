
(define (problem plan_trial_T20190910_075020_522324)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON><PERSON><PERSON> - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        AlarmClock_bar__plus_00_dot_99_bar__plus_00_dot_70_bar__minus_01_dot_81 - object
        AlarmClock_bar__plus_01_dot_23_bar__plus_00_dot_70_bar__minus_01_dot_42 - object
        BaseballBat_bar__minus_02_dot_20_bar__plus_00_dot_04_bar__minus_01_dot_34 - object
        Blinds_bar__plus_00_dot_01_bar__plus_02_dot_15_bar__minus_02_dot_01 - object
        Book_bar__plus_00_dot_71_bar__plus_00_dot_70_bar__minus_01_dot_61 - object
        Book_bar__plus_01_dot_60_bar__plus_00_dot_71_bar__minus_00_dot_47 - object
        Box_bar__plus_01_dot_54_bar__plus_01_dot_65_bar__plus_00_dot_69 - object
        CD_bar__plus_00_dot_49_bar__plus_00_dot_70_bar__minus_01_dot_72 - object
        CD_bar__plus_00_dot_99_bar__plus_00_dot_70_bar__minus_01_dot_62 - object
        CellPhone_bar__minus_01_dot_78_bar__plus_00_dot_53_bar__minus_00_dot_81 - object
        Chair_bar__plus_00_dot_95_bar__plus_00_dot_00_bar__minus_01_dot_23 - object
        CreditCard_bar__plus_00_dot_34_bar__plus_00_dot_08_bar__minus_01_dot_37 - object
        CreditCard_bar__plus_01_dot_48_bar__plus_00_dot_70_bar__minus_01_dot_33 - object
        CreditCard_bar__plus_01_dot_49_bar__plus_00_dot_71_bar__minus_00_dot_23 - object
        HousePlant_bar__plus_01_dot_49_bar__plus_01_dot_40_bar__plus_00_dot_04 - object
        KeyChain_bar__plus_01_dot_57_bar__plus_00_dot_49_bar__minus_00_dot_46 - object
        KeyChain_bar__plus_01_dot_68_bar__plus_00_dot_71_bar__minus_00_dot_62 - object
        Lamp_bar__plus_01_dot_30_bar__plus_00_dot_69_bar__minus_01_dot_72 - object
        Laptop_bar__plus_00_dot_04_bar__plus_00_dot_69_bar__minus_01_dot_46 - object
        LightSwitch_bar__plus_00_dot_23_bar__plus_01_dot_29_bar__plus_00_dot_96 - object
        Mirror_bar__minus_00_dot_69_bar__plus_01_dot_50_bar__plus_00_dot_96 - object
        Mug_bar__plus_01_dot_51_bar__plus_00_dot_49_bar__minus_00_dot_25 - object
        Mug_bar__plus_01_dot_59_bar__plus_01_dot_41_bar__plus_00_dot_26 - object
        Painting_bar__minus_02_dot_40_bar__plus_01_dot_70_bar__minus_00_dot_86 - object
        Pencil_bar__plus_00_dot_25_bar__plus_00_dot_71_bar__minus_01_dot_33 - object
        Pencil_bar__plus_00_dot_99_bar__plus_00_dot_71_bar__minus_01_dot_33 - object
        Pen_bar__plus_00_dot_49_bar__plus_00_dot_71_bar__minus_01_dot_42 - object
        Pen_bar__plus_01_dot_45_bar__plus_00_dot_49_bar__minus_00_dot_60 - object
        Pen_bar__plus_01_dot_59_bar__plus_01_dot_42_bar__plus_00_dot_78 - object
        Pillow_bar__minus_01_dot_48_bar__plus_00_dot_60_bar__minus_00_dot_81 - object
        Pillow_bar__minus_02_dot_07_bar__plus_00_dot_60_bar__minus_00_dot_33 - object
        TeddyBear_bar__minus_02_dot_20_bar__plus_00_dot_50_bar__plus_00_dot_18 - object
        TennisRacket_bar__minus_02_dot_25_bar__plus_00_dot_32_bar__minus_01_dot_81 - object
        TissueBox_bar__plus_01_dot_53_bar__plus_00_dot_70_bar__minus_01_dot_64 - object
        Window_bar__plus_00_dot_05_bar__plus_01_dot_56_bar__minus_02_dot_08 - object
        Bed_bar__minus_01_dot_78_bar_00_dot_00_bar__minus_00_dot_03 - receptacle
        Desk_bar__plus_00_dot_63_bar__minus_00_dot_04_bar__minus_01_dot_63 - receptacle
        Drawer_bar__plus_00_dot_39_bar__plus_00_dot_13_bar__minus_01_dot_46 - receptacle
        Drawer_bar__plus_00_dot_39_bar__plus_00_dot_33_bar__minus_01_dot_46 - receptacle
        Drawer_bar__plus_00_dot_39_bar__plus_00_dot_54_bar__minus_01_dot_46 - receptacle
        Drawer_bar__minus_00_dot_03_bar__plus_00_dot_13_bar__minus_01_dot_46 - receptacle
        Drawer_bar__minus_00_dot_03_bar__plus_00_dot_33_bar__minus_01_dot_46 - receptacle
        Drawer_bar__minus_00_dot_03_bar__plus_00_dot_54_bar__minus_01_dot_46 - receptacle
        GarbageCan_bar__minus_00_dot_61_bar__plus_00_dot_00_bar__plus_00_dot_80 - receptacle
        Shelf_bar__plus_01_dot_54_bar__plus_00_dot_48_bar__minus_00_dot_47 - receptacle
        Shelf_bar__plus_01_dot_55_bar__plus_01_dot_40_bar__plus_00_dot_38 - receptacle
        SideTable_bar__plus_01_dot_56_bar__minus_00_dot_01_bar__minus_00_dot_47 - receptacle
        loc_bar_0_bar_2_bar_1_bar_15 - location
        loc_bar_0_bar__minus_4_bar_2_bar_0 - location
        loc_bar__minus_3_bar__minus_5_bar_3_bar_45 - location
        loc_bar_3_bar__minus_1_bar_1_bar_60 - location
        loc_bar__minus_2_bar__minus_2_bar_2_bar_45 - location
        loc_bar__minus_2_bar_1_bar_0_bar_60 - location
        loc_bar_4_bar__minus_1_bar_2_bar_45 - location
        loc_bar__minus_3_bar__minus_6_bar_3_bar_45 - location
        loc_bar_1_bar_2_bar_0_bar_45 - location
        loc_bar__minus_3_bar_1_bar_0_bar_15 - location
        loc_bar_0_bar__minus_4_bar_2_bar__minus_30 - location
        loc_bar__minus_3_bar__minus_3_bar_3_bar_0 - location
        loc_bar_4_bar__minus_2_bar_2_bar_60 - location
        loc_bar__minus_2_bar_0_bar_3_bar_45 - location
        loc_bar_4_bar__minus_2_bar_1_bar_60 - location
        loc_bar__minus_2_bar__minus_2_bar_2_bar_60 - location
        loc_bar_2_bar__minus_1_bar_1_bar_60 - location
        loc_bar_0_bar__minus_2_bar_2_bar_45 - location
        loc_bar_3_bar__minus_2_bar_2_bar_60 - location
        loc_bar_2_bar__minus_1_bar_2_bar_45 - location
        loc_bar_1_bar__minus_1_bar_1_bar_30 - location
        )
    

(:init


        (receptacleType Shelf_bar__plus_01_dot_55_bar__plus_01_dot_40_bar__plus_00_dot_38 ShelfType)
        (receptacleType GarbageCan_bar__minus_00_dot_61_bar__plus_00_dot_00_bar__plus_00_dot_80 GarbageCanType)
        (receptacleType Bed_bar__minus_01_dot_78_bar_00_dot_00_bar__minus_00_dot_03 BedType)
        (receptacleType Drawer_bar__plus_00_dot_39_bar__plus_00_dot_54_bar__minus_01_dot_46 DrawerType)
        (receptacleType Shelf_bar__plus_01_dot_54_bar__plus_00_dot_48_bar__minus_00_dot_47 ShelfType)
        (receptacleType Drawer_bar__plus_00_dot_39_bar__plus_00_dot_13_bar__minus_01_dot_46 DrawerType)
        (receptacleType Desk_bar__plus_00_dot_63_bar__minus_00_dot_04_bar__minus_01_dot_63 DeskType)
        (receptacleType Drawer_bar__minus_00_dot_03_bar__plus_00_dot_13_bar__minus_01_dot_46 DrawerType)
        (receptacleType SideTable_bar__plus_01_dot_56_bar__minus_00_dot_01_bar__minus_00_dot_47 SideTableType)
        (receptacleType Drawer_bar__minus_00_dot_03_bar__plus_00_dot_54_bar__minus_01_dot_46 DrawerType)
        (receptacleType Drawer_bar__minus_00_dot_03_bar__plus_00_dot_33_bar__minus_01_dot_46 DrawerType)
        (receptacleType Drawer_bar__plus_00_dot_39_bar__plus_00_dot_33_bar__minus_01_dot_46 DrawerType)
        (objectType BaseballBat_bar__minus_02_dot_20_bar__plus_00_dot_04_bar__minus_01_dot_34 BaseballBatType)
        (objectType Mug_bar__plus_01_dot_51_bar__plus_00_dot_49_bar__minus_00_dot_25 MugType)
        (objectType KeyChain_bar__plus_01_dot_57_bar__plus_00_dot_49_bar__minus_00_dot_46 KeyChainType)
        (objectType HousePlant_bar__plus_01_dot_49_bar__plus_01_dot_40_bar__plus_00_dot_04 HousePlantType)
        (objectType CD_bar__plus_00_dot_49_bar__plus_00_dot_70_bar__minus_01_dot_72 CDType)
        (objectType KeyChain_bar__plus_01_dot_68_bar__plus_00_dot_71_bar__minus_00_dot_62 KeyChainType)
        (objectType CellPhone_bar__minus_01_dot_78_bar__plus_00_dot_53_bar__minus_00_dot_81 CellPhoneType)
        (objectType Book_bar__plus_01_dot_60_bar__plus_00_dot_71_bar__minus_00_dot_47 BookType)
        (objectType TeddyBear_bar__minus_02_dot_20_bar__plus_00_dot_50_bar__plus_00_dot_18 TeddyBearType)
        (objectType Pen_bar__plus_00_dot_49_bar__plus_00_dot_71_bar__minus_01_dot_42 PenType)
        (objectType Chair_bar__plus_00_dot_95_bar__plus_00_dot_00_bar__minus_01_dot_23 ChairType)
        (objectType LightSwitch_bar__plus_00_dot_23_bar__plus_01_dot_29_bar__plus_00_dot_96 LightSwitchType)
        (objectType Laptop_bar__plus_00_dot_04_bar__plus_00_dot_69_bar__minus_01_dot_46 LaptopType)
        (objectType Pen_bar__plus_01_dot_45_bar__plus_00_dot_49_bar__minus_00_dot_60 PenType)
        (objectType CreditCard_bar__plus_00_dot_34_bar__plus_00_dot_08_bar__minus_01_dot_37 CreditCardType)
        (objectType Pen_bar__plus_01_dot_59_bar__plus_01_dot_42_bar__plus_00_dot_78 PenType)
        (objectType Pillow_bar__minus_01_dot_48_bar__plus_00_dot_60_bar__minus_00_dot_81 PillowType)
        (objectType Mirror_bar__minus_00_dot_69_bar__plus_01_dot_50_bar__plus_00_dot_96 MirrorType)
        (objectType CD_bar__plus_00_dot_99_bar__plus_00_dot_70_bar__minus_01_dot_62 CDType)
        (objectType Mug_bar__plus_01_dot_59_bar__plus_01_dot_41_bar__plus_00_dot_26 MugType)
        (objectType Painting_bar__minus_02_dot_40_bar__plus_01_dot_70_bar__minus_00_dot_86 PaintingType)
        (objectType CreditCard_bar__plus_01_dot_49_bar__plus_00_dot_71_bar__minus_00_dot_23 CreditCardType)
        (objectType AlarmClock_bar__plus_01_dot_23_bar__plus_00_dot_70_bar__minus_01_dot_42 AlarmClockType)
        (objectType Box_bar__plus_01_dot_54_bar__plus_01_dot_65_bar__plus_00_dot_69 BoxType)
        (objectType Book_bar__plus_00_dot_71_bar__plus_00_dot_70_bar__minus_01_dot_61 BookType)
        (objectType Pencil_bar__plus_00_dot_25_bar__plus_00_dot_71_bar__minus_01_dot_33 PencilType)
        (objectType Blinds_bar__plus_00_dot_01_bar__plus_02_dot_15_bar__minus_02_dot_01 BlindsType)
        (objectType TissueBox_bar__plus_01_dot_53_bar__plus_00_dot_70_bar__minus_01_dot_64 TissueBoxType)
        (objectType AlarmClock_bar__plus_00_dot_99_bar__plus_00_dot_70_bar__minus_01_dot_81 AlarmClockType)
        (objectType Pencil_bar__plus_00_dot_99_bar__plus_00_dot_71_bar__minus_01_dot_33 PencilType)
        (objectType TennisRacket_bar__minus_02_dot_25_bar__plus_00_dot_32_bar__minus_01_dot_81 TennisRacketType)
        (objectType CreditCard_bar__plus_01_dot_48_bar__plus_00_dot_70_bar__minus_01_dot_33 CreditCardType)
        (objectType Window_bar__plus_00_dot_05_bar__plus_01_dot_56_bar__minus_02_dot_08 WindowType)
        (objectType Pillow_bar__minus_02_dot_07_bar__plus_00_dot_60_bar__minus_00_dot_33 PillowType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType AlarmClockType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType CDType)
        (canContain GarbageCanType PencilType)
        (canContain GarbageCanType TissueBoxType)
        (canContain BedType BaseballBatType)
        (canContain BedType TennisRacketType)
        (canContain BedType LaptopType)
        (canContain BedType BookType)
        (canContain BedType CellPhoneType)
        (canContain BedType PillowType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType TissueBoxType)
        (canContain ShelfType PenType)
        (canContain ShelfType BookType)
        (canContain ShelfType CDType)
        (canContain ShelfType MugType)
        (canContain ShelfType BoxType)
        (canContain ShelfType CellPhoneType)
        (canContain ShelfType KeyChainType)
        (canContain ShelfType CreditCardType)
        (canContain ShelfType PencilType)
        (canContain ShelfType TissueBoxType)
        (canContain ShelfType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType TissueBoxType)
        (canContain DeskType PenType)
        (canContain DeskType BookType)
        (canContain DeskType CDType)
        (canContain DeskType MugType)
        (canContain DeskType BoxType)
        (canContain DeskType CellPhoneType)
        (canContain DeskType KeyChainType)
        (canContain DeskType CreditCardType)
        (canContain DeskType TennisRacketType)
        (canContain DeskType LaptopType)
        (canContain DeskType PencilType)
        (canContain DeskType TissueBoxType)
        (canContain DeskType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType TissueBoxType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType CDType)
        (canContain SideTableType MugType)
        (canContain SideTableType BoxType)
        (canContain SideTableType CellPhoneType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType BaseballBatType)
        (canContain SideTableType TennisRacketType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType PencilType)
        (canContain SideTableType TissueBoxType)
        (canContain SideTableType AlarmClockType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType TissueBoxType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType CDType)
        (canContain DrawerType CellPhoneType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType PencilType)
        (canContain DrawerType TissueBoxType)
        (pickupable BaseballBat_bar__minus_02_dot_20_bar__plus_00_dot_04_bar__minus_01_dot_34)
        (pickupable Mug_bar__plus_01_dot_51_bar__plus_00_dot_49_bar__minus_00_dot_25)
        (pickupable KeyChain_bar__plus_01_dot_57_bar__plus_00_dot_49_bar__minus_00_dot_46)
        (pickupable CD_bar__plus_00_dot_49_bar__plus_00_dot_70_bar__minus_01_dot_72)
        (pickupable KeyChain_bar__plus_01_dot_68_bar__plus_00_dot_71_bar__minus_00_dot_62)
        (pickupable CellPhone_bar__minus_01_dot_78_bar__plus_00_dot_53_bar__minus_00_dot_81)
        (pickupable Book_bar__plus_01_dot_60_bar__plus_00_dot_71_bar__minus_00_dot_47)
        (pickupable TeddyBear_bar__minus_02_dot_20_bar__plus_00_dot_50_bar__plus_00_dot_18)
        (pickupable Pen_bar__plus_00_dot_49_bar__plus_00_dot_71_bar__minus_01_dot_42)
        (pickupable Laptop_bar__plus_00_dot_04_bar__plus_00_dot_69_bar__minus_01_dot_46)
        (pickupable Pen_bar__plus_01_dot_45_bar__plus_00_dot_49_bar__minus_00_dot_60)
        (pickupable CreditCard_bar__plus_00_dot_34_bar__plus_00_dot_08_bar__minus_01_dot_37)
        (pickupable Pen_bar__plus_01_dot_59_bar__plus_01_dot_42_bar__plus_00_dot_78)
        (pickupable Pillow_bar__minus_01_dot_48_bar__plus_00_dot_60_bar__minus_00_dot_81)
        (pickupable CD_bar__plus_00_dot_99_bar__plus_00_dot_70_bar__minus_01_dot_62)
        (pickupable Mug_bar__plus_01_dot_59_bar__plus_01_dot_41_bar__plus_00_dot_26)
        (pickupable CreditCard_bar__plus_01_dot_49_bar__plus_00_dot_71_bar__minus_00_dot_23)
        (pickupable AlarmClock_bar__plus_01_dot_23_bar__plus_00_dot_70_bar__minus_01_dot_42)
        (pickupable Box_bar__plus_01_dot_54_bar__plus_01_dot_65_bar__plus_00_dot_69)
        (pickupable Book_bar__plus_00_dot_71_bar__plus_00_dot_70_bar__minus_01_dot_61)
        (pickupable Pencil_bar__plus_00_dot_25_bar__plus_00_dot_71_bar__minus_01_dot_33)
        (pickupable TissueBox_bar__plus_01_dot_53_bar__plus_00_dot_70_bar__minus_01_dot_64)
        (pickupable AlarmClock_bar__plus_00_dot_99_bar__plus_00_dot_70_bar__minus_01_dot_81)
        (pickupable Pencil_bar__plus_00_dot_99_bar__plus_00_dot_71_bar__minus_01_dot_33)
        (pickupable TennisRacket_bar__minus_02_dot_25_bar__plus_00_dot_32_bar__minus_01_dot_81)
        (pickupable CreditCard_bar__plus_01_dot_48_bar__plus_00_dot_70_bar__minus_01_dot_33)
        (pickupable Pillow_bar__minus_02_dot_07_bar__plus_00_dot_60_bar__minus_00_dot_33)
        (isReceptacleObject Mug_bar__plus_01_dot_51_bar__plus_00_dot_49_bar__minus_00_dot_25)
        (isReceptacleObject Mug_bar__plus_01_dot_59_bar__plus_01_dot_41_bar__plus_00_dot_26)
        (isReceptacleObject Box_bar__plus_01_dot_54_bar__plus_01_dot_65_bar__plus_00_dot_69)
        (openable Drawer_bar__plus_00_dot_39_bar__plus_00_dot_54_bar__minus_01_dot_46)
        (openable Drawer_bar__plus_00_dot_39_bar__plus_00_dot_13_bar__minus_01_dot_46)
        (openable Drawer_bar__minus_00_dot_03_bar__plus_00_dot_13_bar__minus_01_dot_46)
        (openable Drawer_bar__minus_00_dot_03_bar__plus_00_dot_54_bar__minus_01_dot_46)
        (openable Drawer_bar__minus_00_dot_03_bar__plus_00_dot_33_bar__minus_01_dot_46)
        (openable Drawer_bar__plus_00_dot_39_bar__plus_00_dot_33_bar__minus_01_dot_46)
        
        (atLocation agent1 loc_bar_1_bar__minus_1_bar_1_bar_30)
        
        (cleanable Mug_bar__plus_01_dot_51_bar__plus_00_dot_49_bar__minus_00_dot_25)
        (cleanable Mug_bar__plus_01_dot_59_bar__plus_01_dot_41_bar__plus_00_dot_26)
        
        (heatable Mug_bar__plus_01_dot_51_bar__plus_00_dot_49_bar__minus_00_dot_25)
        (heatable Mug_bar__plus_01_dot_59_bar__plus_01_dot_41_bar__plus_00_dot_26)
        (coolable Mug_bar__plus_01_dot_51_bar__plus_00_dot_49_bar__minus_00_dot_25)
        (coolable Mug_bar__plus_01_dot_59_bar__plus_01_dot_41_bar__plus_00_dot_26)
        
        
        
        
        
        
        
        (inReceptacleObject Pen_bar__plus_01_dot_59_bar__plus_01_dot_42_bar__plus_00_dot_78 Box_bar__plus_01_dot_54_bar__plus_01_dot_65_bar__plus_00_dot_69)
        (inReceptacle TissueBox_bar__plus_01_dot_53_bar__plus_00_dot_70_bar__minus_01_dot_64 Desk_bar__plus_00_dot_63_bar__minus_00_dot_04_bar__minus_01_dot_63)
        (inReceptacle Pen_bar__plus_00_dot_49_bar__plus_00_dot_71_bar__minus_01_dot_42 Desk_bar__plus_00_dot_63_bar__minus_00_dot_04_bar__minus_01_dot_63)
        (inReceptacle Laptop_bar__plus_00_dot_04_bar__plus_00_dot_69_bar__minus_01_dot_46 Desk_bar__plus_00_dot_63_bar__minus_00_dot_04_bar__minus_01_dot_63)
        (inReceptacle AlarmClock_bar__plus_01_dot_23_bar__plus_00_dot_70_bar__minus_01_dot_42 Desk_bar__plus_00_dot_63_bar__minus_00_dot_04_bar__minus_01_dot_63)
        (inReceptacle CD_bar__plus_00_dot_49_bar__plus_00_dot_70_bar__minus_01_dot_72 Desk_bar__plus_00_dot_63_bar__minus_00_dot_04_bar__minus_01_dot_63)
        (inReceptacle AlarmClock_bar__plus_00_dot_99_bar__plus_00_dot_70_bar__minus_01_dot_81 Desk_bar__plus_00_dot_63_bar__minus_00_dot_04_bar__minus_01_dot_63)
        (inReceptacle Pencil_bar__plus_00_dot_99_bar__plus_00_dot_71_bar__minus_01_dot_33 Desk_bar__plus_00_dot_63_bar__minus_00_dot_04_bar__minus_01_dot_63)
        (inReceptacle CD_bar__plus_00_dot_99_bar__plus_00_dot_70_bar__minus_01_dot_62 Desk_bar__plus_00_dot_63_bar__minus_00_dot_04_bar__minus_01_dot_63)
        (inReceptacle CreditCard_bar__plus_01_dot_48_bar__plus_00_dot_70_bar__minus_01_dot_33 Desk_bar__plus_00_dot_63_bar__minus_00_dot_04_bar__minus_01_dot_63)
        (inReceptacle Book_bar__plus_00_dot_71_bar__plus_00_dot_70_bar__minus_01_dot_61 Desk_bar__plus_00_dot_63_bar__minus_00_dot_04_bar__minus_01_dot_63)
        (inReceptacle Pencil_bar__plus_00_dot_25_bar__plus_00_dot_71_bar__minus_01_dot_33 Desk_bar__plus_00_dot_63_bar__minus_00_dot_04_bar__minus_01_dot_63)
        (inReceptacle CreditCard_bar__plus_00_dot_34_bar__plus_00_dot_08_bar__minus_01_dot_37 Drawer_bar__plus_00_dot_39_bar__plus_00_dot_13_bar__minus_01_dot_46)
        (inReceptacle KeyChain_bar__plus_01_dot_68_bar__plus_00_dot_71_bar__minus_00_dot_62 SideTable_bar__plus_01_dot_56_bar__minus_00_dot_01_bar__minus_00_dot_47)
        (inReceptacle Book_bar__plus_01_dot_60_bar__plus_00_dot_71_bar__minus_00_dot_47 SideTable_bar__plus_01_dot_56_bar__minus_00_dot_01_bar__minus_00_dot_47)
        (inReceptacle CreditCard_bar__plus_01_dot_49_bar__plus_00_dot_71_bar__minus_00_dot_23 SideTable_bar__plus_01_dot_56_bar__minus_00_dot_01_bar__minus_00_dot_47)
        (inReceptacle Pillow_bar__minus_02_dot_07_bar__plus_00_dot_60_bar__minus_00_dot_33 Bed_bar__minus_01_dot_78_bar_00_dot_00_bar__minus_00_dot_03)
        (inReceptacle TeddyBear_bar__minus_02_dot_20_bar__plus_00_dot_50_bar__plus_00_dot_18 Bed_bar__minus_01_dot_78_bar_00_dot_00_bar__minus_00_dot_03)
        (inReceptacle Pillow_bar__minus_01_dot_48_bar__plus_00_dot_60_bar__minus_00_dot_81 Bed_bar__minus_01_dot_78_bar_00_dot_00_bar__minus_00_dot_03)
        (inReceptacle CellPhone_bar__minus_01_dot_78_bar__plus_00_dot_53_bar__minus_00_dot_81 Bed_bar__minus_01_dot_78_bar_00_dot_00_bar__minus_00_dot_03)
        (inReceptacle Mug_bar__plus_01_dot_51_bar__plus_00_dot_49_bar__minus_00_dot_25 Shelf_bar__plus_01_dot_54_bar__plus_00_dot_48_bar__minus_00_dot_47)
        (inReceptacle Pen_bar__plus_01_dot_45_bar__plus_00_dot_49_bar__minus_00_dot_60 Shelf_bar__plus_01_dot_54_bar__plus_00_dot_48_bar__minus_00_dot_47)
        (inReceptacle Box_bar__plus_01_dot_54_bar__plus_01_dot_65_bar__plus_00_dot_69 Shelf_bar__plus_01_dot_55_bar__plus_01_dot_40_bar__plus_00_dot_38)
        (inReceptacle Pen_bar__plus_01_dot_59_bar__plus_01_dot_42_bar__plus_00_dot_78 Shelf_bar__plus_01_dot_55_bar__plus_01_dot_40_bar__plus_00_dot_38)
        (inReceptacle Mug_bar__plus_01_dot_59_bar__plus_01_dot_41_bar__plus_00_dot_26 Shelf_bar__plus_01_dot_55_bar__plus_01_dot_40_bar__plus_00_dot_38)
        (inReceptacle HousePlant_bar__plus_01_dot_49_bar__plus_01_dot_40_bar__plus_00_dot_04 Shelf_bar__plus_01_dot_55_bar__plus_01_dot_40_bar__plus_00_dot_38)
        
        
        (receptacleAtLocation Bed_bar__minus_01_dot_78_bar_00_dot_00_bar__minus_00_dot_03 loc_bar__minus_2_bar_0_bar_3_bar_45)
        (receptacleAtLocation Desk_bar__plus_00_dot_63_bar__minus_00_dot_04_bar__minus_01_dot_63 loc_bar_4_bar__minus_2_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_39_bar__plus_00_dot_13_bar__minus_01_dot_46 loc_bar_4_bar__minus_1_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__plus_00_dot_39_bar__plus_00_dot_33_bar__minus_01_dot_46 loc_bar_3_bar__minus_2_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__plus_00_dot_39_bar__plus_00_dot_54_bar__minus_01_dot_46 loc_bar_0_bar__minus_2_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_03_bar__plus_00_dot_13_bar__minus_01_dot_46 loc_bar_2_bar__minus_1_bar_2_bar_45)
        (receptacleAtLocation Drawer_bar__minus_00_dot_03_bar__plus_00_dot_33_bar__minus_01_dot_46 loc_bar__minus_2_bar__minus_2_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_00_dot_03_bar__plus_00_dot_54_bar__minus_01_dot_46 loc_bar__minus_2_bar__minus_2_bar_2_bar_45)
        (receptacleAtLocation GarbageCan_bar__minus_00_dot_61_bar__plus_00_dot_00_bar__plus_00_dot_80 loc_bar__minus_2_bar_1_bar_0_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_54_bar__plus_00_dot_48_bar__minus_00_dot_47 loc_bar_3_bar__minus_1_bar_1_bar_60)
        (receptacleAtLocation Shelf_bar__plus_01_dot_55_bar__plus_01_dot_40_bar__plus_00_dot_38 loc_bar_0_bar_2_bar_1_bar_15)
        (receptacleAtLocation SideTable_bar__plus_01_dot_56_bar__minus_00_dot_01_bar__minus_00_dot_47 loc_bar_2_bar__minus_1_bar_1_bar_60)
        (objectAtLocation Mug_bar__plus_01_dot_59_bar__plus_01_dot_41_bar__plus_00_dot_26 loc_bar_0_bar_2_bar_1_bar_15)
        (objectAtLocation Pencil_bar__plus_00_dot_25_bar__plus_00_dot_71_bar__minus_01_dot_33 loc_bar_4_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Pen_bar__plus_00_dot_49_bar__plus_00_dot_71_bar__minus_01_dot_42 loc_bar_4_bar__minus_2_bar_2_bar_60)
        (objectAtLocation CD_bar__plus_00_dot_99_bar__plus_00_dot_70_bar__minus_01_dot_62 loc_bar_4_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Pillow_bar__minus_02_dot_07_bar__plus_00_dot_60_bar__minus_00_dot_33 loc_bar__minus_2_bar_0_bar_3_bar_45)
        (objectAtLocation KeyChain_bar__plus_01_dot_57_bar__plus_00_dot_49_bar__minus_00_dot_46 loc_bar_4_bar__minus_2_bar_1_bar_60)
        (objectAtLocation CreditCard_bar__plus_00_dot_34_bar__plus_00_dot_08_bar__minus_01_dot_37 loc_bar_4_bar__minus_1_bar_2_bar_45)
        (objectAtLocation AlarmClock_bar__plus_01_dot_23_bar__plus_00_dot_70_bar__minus_01_dot_42 loc_bar_4_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Book_bar__plus_00_dot_71_bar__plus_00_dot_70_bar__minus_01_dot_61 loc_bar_4_bar__minus_2_bar_2_bar_60)
        (objectAtLocation CreditCard_bar__plus_01_dot_48_bar__plus_00_dot_70_bar__minus_01_dot_33 loc_bar_4_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Pen_bar__plus_01_dot_59_bar__plus_01_dot_42_bar__plus_00_dot_78 loc_bar_0_bar_2_bar_1_bar_15)
        (objectAtLocation Box_bar__plus_01_dot_54_bar__plus_01_dot_65_bar__plus_00_dot_69 loc_bar_0_bar_2_bar_1_bar_15)
        (objectAtLocation Chair_bar__plus_00_dot_95_bar__plus_00_dot_00_bar__minus_01_dot_23 loc_bar_4_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Mirror_bar__minus_00_dot_69_bar__plus_01_dot_50_bar__plus_00_dot_96 loc_bar__minus_3_bar_1_bar_0_bar_15)
        (objectAtLocation Book_bar__plus_01_dot_60_bar__plus_00_dot_71_bar__minus_00_dot_47 loc_bar_2_bar__minus_1_bar_1_bar_60)
        (objectAtLocation AlarmClock_bar__plus_00_dot_99_bar__plus_00_dot_70_bar__minus_01_dot_81 loc_bar_4_bar__minus_2_bar_2_bar_60)
        (objectAtLocation CellPhone_bar__minus_01_dot_78_bar__plus_00_dot_53_bar__minus_00_dot_81 loc_bar__minus_2_bar_0_bar_3_bar_45)
        (objectAtLocation TissueBox_bar__plus_01_dot_53_bar__plus_00_dot_70_bar__minus_01_dot_64 loc_bar_4_bar__minus_2_bar_2_bar_60)
        (objectAtLocation TeddyBear_bar__minus_02_dot_20_bar__plus_00_dot_50_bar__plus_00_dot_18 loc_bar__minus_2_bar_0_bar_3_bar_45)
        (objectAtLocation CreditCard_bar__plus_01_dot_49_bar__plus_00_dot_71_bar__minus_00_dot_23 loc_bar_2_bar__minus_1_bar_1_bar_60)
        (objectAtLocation KeyChain_bar__plus_01_dot_68_bar__plus_00_dot_71_bar__minus_00_dot_62 loc_bar_2_bar__minus_1_bar_1_bar_60)
        (objectAtLocation BaseballBat_bar__minus_02_dot_20_bar__plus_00_dot_04_bar__minus_01_dot_34 loc_bar__minus_3_bar__minus_5_bar_3_bar_45)
        (objectAtLocation TennisRacket_bar__minus_02_dot_25_bar__plus_00_dot_32_bar__minus_01_dot_81 loc_bar__minus_3_bar__minus_6_bar_3_bar_45)
        (objectAtLocation Painting_bar__minus_02_dot_40_bar__plus_01_dot_70_bar__minus_00_dot_86 loc_bar__minus_3_bar__minus_3_bar_3_bar_0)
        (objectAtLocation Pillow_bar__minus_01_dot_48_bar__plus_00_dot_60_bar__minus_00_dot_81 loc_bar__minus_2_bar_0_bar_3_bar_45)
        (objectAtLocation LightSwitch_bar__plus_00_dot_23_bar__plus_01_dot_29_bar__plus_00_dot_96 loc_bar_1_bar_2_bar_0_bar_45)
        (objectAtLocation Laptop_bar__plus_00_dot_04_bar__plus_00_dot_69_bar__minus_01_dot_46 loc_bar_4_bar__minus_2_bar_2_bar_60)
        (objectAtLocation CD_bar__plus_00_dot_49_bar__plus_00_dot_70_bar__minus_01_dot_72 loc_bar_4_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Pen_bar__plus_01_dot_45_bar__plus_00_dot_49_bar__minus_00_dot_60 loc_bar_3_bar__minus_1_bar_1_bar_60)
        (objectAtLocation HousePlant_bar__plus_01_dot_49_bar__plus_01_dot_40_bar__plus_00_dot_04 loc_bar_0_bar_2_bar_1_bar_15)
        (objectAtLocation Window_bar__plus_00_dot_05_bar__plus_01_dot_56_bar__minus_02_dot_08 loc_bar_0_bar__minus_4_bar_2_bar_0)
        (objectAtLocation Pencil_bar__plus_00_dot_99_bar__plus_00_dot_71_bar__minus_01_dot_33 loc_bar_4_bar__minus_2_bar_2_bar_60)
        (objectAtLocation Mug_bar__plus_01_dot_51_bar__plus_00_dot_49_bar__minus_00_dot_25 loc_bar_3_bar__minus_1_bar_1_bar_60)
        (objectAtLocation Blinds_bar__plus_00_dot_01_bar__plus_02_dot_15_bar__minus_02_dot_01 loc_bar_0_bar__minus_4_bar_2_bar__minus_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 MugType)
                                    (receptacleType ?r DeskType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 MugType)
                                            (receptacleType ?r DeskType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            