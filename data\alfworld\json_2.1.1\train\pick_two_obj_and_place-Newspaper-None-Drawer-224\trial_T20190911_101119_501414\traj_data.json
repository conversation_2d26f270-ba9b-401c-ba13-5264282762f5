{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 41}, {"high_idx": 5, "image_name": "000000213.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000214.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000215.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 42}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 44}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 45}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 46}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 47}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000266.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000267.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000268.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000269.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000270.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000271.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000272.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000273.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000274.png", "low_idx": 48}, {"high_idx": 6, "image_name": "000000275.png", "low_idx": 48}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 49}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000289.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000290.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000291.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000292.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000293.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000294.png", "low_idx": 50}, {"high_idx": 7, "image_name": "000000295.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000296.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000297.png", "low_idx": 51}, {"high_idx": 7, "image_name": "000000298.png", "low_idx": 51}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Newspaper", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["dresser"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-1|2|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["newspaper"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Newspaper", [-0.818952, -0.818952, 3.897994, 3.897994, 3.6507604, 3.6507604]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-0.0928, -0.0928, 6.8896, 6.8896, 0.0436, 0.0436]], "forceVisible": true, "objectId": "Newspaper|-00.20|+00.91|+00.97"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|9|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["newspaper", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Newspaper", [-0.818952, -0.818952, 3.897994, 3.897994, 3.6507604, 3.6507604]], "coordinateReceptacleObjectId": ["Drawer", [-0.6372252, -0.6372252, 7.766776, 7.766776, 2.872873784, 2.872873784]], "forceVisible": true, "objectId": "Newspaper|-00.20|+00.91|+00.97", "receptacleObjectId": "Drawer|-00.16|+00.72|+01.94"}}, {"discrete_action": {"action": "GotoLocation", "args": ["dresser"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-3|10|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["newspaper"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Newspaper", [0.270276, 0.270276, 9.727196, 9.727196, 3.6507604, 3.6507604]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-0.0928, -0.0928, 6.8896, 6.8896, 0.0436, 0.0436]], "forceVisible": true, "objectId": "Newspaper|+00.07|+00.91|+02.43"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-4|9|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["newspaper", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Newspaper", [0.270276, 0.270276, 9.727196, 9.727196, 3.6507604, 3.6507604]], "coordinateReceptacleObjectId": ["Drawer", [-0.6372252, -0.6372252, 7.766776, 7.766776, 2.872873784, 2.872873784]], "forceVisible": true, "objectId": "Newspaper|+00.07|+00.91|+02.43", "receptacleObjectId": "Drawer|-00.16|+00.72|+01.94"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Newspaper|-00.20|+00.91|+00.97"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [110, 105, 219, 153], "mask": [[31313, 99], [31613, 98], [31913, 98], [32213, 97], [32513, 96], [32813, 96], [33113, 95], [33413, 95], [33712, 95], [34012, 94], [34312, 94], [34612, 93], [34912, 93], [35212, 92], [35512, 92], [35812, 91], [36112, 90], [36412, 90], [36711, 90], [37011, 90], [37311, 89], [37611, 89], [37911, 88], [38211, 88], [38300, 1], [38511, 92], [38811, 94], [39111, 96], [39411, 99], [39711, 101], [40011, 103], [40310, 106], [40610, 108], [40910, 110], [41210, 110], [41510, 110], [41810, 110], [42110, 110], [42410, 110], [42710, 110], [43010, 110], [43310, 110], [43610, 110], [43910, 110], [44211, 108], [44510, 109], [44810, 110], [45110, 110], [45410, 110], [45710, 110]], "point": [164, 128]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-00.16|+00.72|+01.94"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [167, 167, 296, 203], "mask": [[49970, 127], [50270, 126], [50570, 126], [50870, 125], [51170, 124], [51469, 125], [51769, 124], [52069, 124], [52369, 123], [52669, 122], [52969, 122], [53269, 121], [53569, 121], [53869, 120], [54169, 119], [54469, 119], [54769, 118], [55068, 118], [55368, 118], [55668, 117], [55968, 117], [56268, 116], [56568, 115], [56868, 115], [57168, 114], [57468, 114], [57768, 113], [58068, 112], [58368, 112], [58668, 111], [58967, 112], [59267, 111], [59567, 110], [59867, 110], [60167, 109], [60467, 109], [60856, 19]], "point": [231, 184]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Newspaper|-00.20|+00.91|+00.97", "placeStationary": true, "receptacleObjectId": "Drawer|-00.16|+00.72|+01.94"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [170, 167, 299, 293], "mask": [[49972, 119], [50271, 121], [50571, 121], [50871, 122], [51171, 122], [51471, 123], [51771, 123], [52071, 124], [52371, 124], [52671, 125], [52971, 125], [53271, 126], [53570, 127], [53870, 128], [54170, 129], [54470, 129], [54770, 130], [55070, 130], [55370, 130], [55670, 130], [55970, 130], [56270, 130], [56570, 130], [56870, 130], [57170, 130], [57470, 130], [57770, 130], [58070, 130], [58370, 130], [58670, 130], [58970, 130], [59270, 130], [59570, 130], [59870, 130], [60171, 129], [60471, 129], [60856, 44], [61156, 44], [61456, 44], [61757, 43], [62057, 43], [62357, 43], [62658, 42], [62958, 42], [63259, 41], [63559, 41], [63859, 41], [64159, 41], [64459, 41], [64760, 40], [65060, 40], [65360, 40], [65660, 40], [65961, 39], [66261, 39], [66561, 39], [66861, 39], [67162, 38], [67462, 38], [67762, 38], [68062, 38], [68362, 38], [68663, 37], [68963, 37], [69263, 37], [69563, 37], [69864, 36], [70164, 36], [70464, 36], [70764, 36], [71064, 36], [71365, 35], [71665, 35], [71965, 35], [72265, 35], [72566, 34], [72866, 34], [73166, 34], [73466, 34], [73767, 33], [74067, 33], [74367, 33], [74667, 33], [74967, 33], [75268, 32], [75568, 32], [75868, 32], [76168, 32], [76469, 31], [76769, 31], [77069, 31], [77369, 31], [77669, 31], [77970, 30], [78270, 30], [78570, 30], [78870, 30], [79171, 29], [79471, 29], [79771, 29], [80071, 29], [80371, 29], [80671, 29], [80972, 28], [81272, 28], [81572, 28], [81872, 28], [82172, 28], [82473, 27], [82773, 27], [83073, 27], [83373, 27], [83673, 27], [83973, 27], [84274, 26], [84574, 26], [84874, 26], [85174, 26], [85474, 26], [85775, 25], [86075, 25], [86375, 25], [86675, 25], [86975, 25], [87276, 24], [87576, 24], [87876, 24]], "point": [261, 222]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-00.16|+00.72|+01.94"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [170, 167, 299, 293], "mask": [[49972, 119], [50271, 121], [50571, 121], [50871, 122], [51171, 122], [51471, 123], [51771, 123], [52071, 124], [52371, 124], [52671, 125], [52971, 125], [53271, 126], [53570, 127], [53870, 128], [54170, 129], [54470, 129], [54770, 130], [55070, 130], [55370, 130], [55670, 130], [55970, 130], [56270, 130], [56570, 130], [56870, 23], [56900, 100], [57170, 8], [57213, 87], [57470, 8], [57515, 85], [57770, 8], [57815, 85], [58070, 8], [58118, 82], [58370, 8], [58418, 82], [58670, 8], [58718, 82], [58970, 8], [59018, 82], [59270, 8], [59318, 82], [59570, 9], [59619, 81], [59870, 9], [59919, 81], [60171, 8], [60219, 81], [60471, 8], [60519, 81], [60771, 8], [60820, 80], [61071, 8], [61120, 80], [61371, 8], [61420, 80], [61671, 8], [61720, 80], [61971, 8], [62020, 80], [62271, 8], [62321, 79], [62571, 8], [62621, 79], [62871, 9], [62921, 79], [63171, 9], [63221, 79], [63471, 9], [63522, 78], [63771, 9], [63822, 78], [64071, 9], [64122, 78], [64371, 9], [64422, 78], [64672, 8], [64722, 78], [64972, 8], [65023, 77], [65272, 8], [65323, 77], [65572, 8], [65623, 77], [65872, 8], [65923, 77], [66172, 9], [66224, 76], [66472, 9], [66524, 76], [66772, 9], [66824, 76], [67072, 9], [67124, 76], [67372, 9], [67424, 76], [67672, 9], [67725, 75], [67972, 9], [68025, 75], [68272, 9], [68325, 75], [68572, 9], [68625, 75], [68872, 9], [68926, 74], [69173, 9], [69226, 74], [69473, 9], [69526, 74], [69773, 9], [69826, 74], [70073, 9], [70126, 74], [70373, 9], [70427, 73], [70673, 9], [70727, 73], [70973, 9], [71027, 73], [71273, 9], [71327, 73], [71573, 9], [71628, 72], [71873, 9], [71928, 72], [72173, 9], [72228, 72], [72473, 10], [72528, 72], [72773, 10], [72828, 72], [73073, 10], [73129, 71], [73373, 10], [73429, 71], [73674, 9], [73729, 71], [73974, 9], [74029, 71], [74274, 9], [74330, 70], [74574, 9], [74630, 70], [74874, 9], [74930, 70], [75174, 9], [75230, 70], [75474, 9], [75530, 70], [75774, 10], [75831, 69], [76074, 10], [76131, 69], [76374, 10], [76431, 69], [76674, 10], [76731, 69], [76974, 10], [77032, 68], [77274, 10], [77332, 68], [77574, 126], [77874, 126], [78174, 126], [78475, 125], [78775, 125], [79075, 125], [79375, 125], [79675, 125], [79975, 125], [80275, 125], [80575, 125], [80875, 125], [81175, 125], [81475, 125], [81775, 125], [82075, 125], [82375, 125], [82675, 125], [82974, 126], [83274, 126], [83574, 126], [83874, 126], [84174, 126], [84473, 127], [84773, 127], [85073, 127], [85373, 127], [85673, 127], [85973, 127], [86272, 128], [86572, 128], [86872, 128], [87172, 128], [87472, 128], [87773, 127]], "point": [234, 229]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "Newspaper|+00.07|+00.91|+02.43"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [146, 99, 193, 155], "mask": [[29552, 21], [29847, 34], [30147, 35], [30447, 36], [30747, 38], [31047, 39], [31347, 39], [31647, 39], [31947, 39], [32247, 39], [32547, 39], [32847, 40], [33147, 40], [33447, 40], [33747, 40], [34047, 40], [34347, 40], [34647, 41], [34947, 41], [35247, 41], [35547, 41], [35847, 41], [36147, 41], [36447, 41], [36747, 42], [37047, 42], [37347, 42], [37647, 42], [37947, 42], [38247, 42], [38547, 43], [38847, 43], [39147, 43], [39447, 43], [39747, 43], [40047, 43], [40347, 44], [40647, 44], [40947, 44], [41247, 44], [41547, 44], [41846, 45], [42146, 46], [42446, 46], [42746, 46], [43046, 46], [43346, 46], [43646, 46], [43946, 47], [44246, 47], [44546, 47], [44846, 47], [45146, 47], [45446, 47], [45746, 48], [46046, 48], [46348, 46]], "point": [169, 126]}}, "high_idx": 5}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-00.16|+00.72|+01.94"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [167, 167, 296, 203], "mask": [[49970, 127], [50270, 126], [50570, 126], [50870, 125], [51170, 124], [51469, 125], [51769, 124], [52069, 124], [52369, 123], [52669, 122], [52969, 122], [53269, 121], [53569, 121], [53869, 120], [54169, 119], [54469, 119], [54769, 118], [55068, 118], [55368, 118], [55668, 117], [55968, 117], [56268, 116], [56568, 115], [56868, 115], [57168, 114], [57468, 114], [57768, 113], [58068, 112], [58368, 112], [58668, 111], [58967, 112], [59267, 111], [59567, 110], [59867, 110], [60167, 109], [60467, 109], [60768, 107]], "point": [231, 184]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Newspaper|+00.07|+00.91|+02.43", "placeStationary": true, "receptacleObjectId": "Drawer|-00.16|+00.72|+01.94"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [170, 167, 299, 293], "mask": [[49972, 119], [50271, 121], [50571, 121], [50871, 122], [51171, 122], [51471, 123], [51771, 123], [52071, 124], [52371, 124], [52671, 125], [52971, 125], [53271, 126], [53570, 127], [53870, 128], [54170, 129], [54470, 129], [54770, 130], [55070, 130], [55370, 130], [55670, 130], [55970, 130], [56270, 130], [56570, 130], [56870, 23], [56900, 100], [57170, 8], [57213, 87], [57470, 8], [57515, 85], [57770, 8], [57815, 85], [58070, 8], [58118, 82], [58370, 8], [58418, 82], [58670, 8], [58718, 82], [58970, 8], [59018, 82], [59270, 8], [59318, 82], [59570, 9], [59619, 81], [59870, 9], [59919, 81], [60171, 8], [60219, 81], [60471, 8], [60519, 81], [60771, 8], [60820, 80], [61071, 8], [61120, 80], [61371, 8], [61420, 80], [61671, 8], [61720, 80], [62051, 49], [62352, 48], [62652, 48], [62953, 47], [63253, 47], [63554, 46], [63854, 46], [64155, 45], [64455, 45], [64755, 45], [65056, 44], [65356, 44], [65656, 44], [65957, 43], [66257, 43], [66557, 43], [66858, 42], [67158, 42], [67458, 42], [67759, 41], [68059, 41], [68359, 41], [68660, 40], [68960, 40], [69260, 40], [69561, 39], [69861, 39], [70161, 39], [70462, 38], [70762, 38], [71062, 38], [71363, 37], [71663, 37], [71963, 37], [72264, 36], [72564, 36], [72865, 35], [73165, 35], [73465, 35], [73766, 34], [74066, 34], [74366, 34], [74667, 33], [74967, 33], [75267, 33], [75568, 32], [75868, 32], [76168, 32], [76469, 31], [76769, 31], [77069, 31], [77370, 30], [77670, 30], [77970, 30], [78270, 30], [78571, 29], [78871, 29], [79171, 29], [79472, 28], [79772, 28], [80072, 28], [80373, 27], [80673, 27], [80973, 27], [81273, 27], [81574, 26], [81874, 26], [82174, 26], [82475, 25], [82775, 25], [83075, 25], [83375, 25], [83676, 24], [83976, 24], [84276, 24], [84577, 23], [84877, 23], [85177, 23], [85478, 22], [85778, 22], [86078, 22], [86378, 22], [86679, 21], [86979, 21], [87279, 21], [87580, 20], [87880, 20]], "point": [234, 205]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-00.16|+00.72|+01.94"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [170, 167, 299, 293], "mask": [[49972, 119], [50271, 32], [50306, 86], [50571, 31], [50606, 86], [50871, 31], [50907, 86], [51171, 30], [51207, 86], [51471, 30], [51507, 87], [51771, 29], [51807, 87], [52071, 29], [52107, 88], [52371, 29], [52408, 87], [52671, 29], [52708, 88], [52971, 28], [53008, 88], [53271, 28], [53308, 89], [53570, 29], [53608, 89], [53870, 29], [53909, 89], [54170, 28], [54209, 90], [54470, 28], [54509, 90], [54770, 28], [54809, 91], [55070, 28], [55109, 91], [55370, 28], [55410, 90], [55670, 27], [55710, 90], [55970, 27], [56010, 90], [56270, 27], [56310, 90], [56570, 27], [56610, 90], [56870, 23], [56911, 89], [57170, 8], [57213, 87], [57470, 8], [57515, 85], [57770, 8], [57815, 85], [58070, 8], [58118, 82], [58370, 8], [58418, 82], [58670, 8], [58718, 82], [58970, 8], [59018, 82], [59270, 8], [59318, 82], [59570, 9], [59619, 81], [59870, 9], [59919, 81], [60171, 8], [60219, 81], [60471, 8], [60519, 81], [60771, 8], [60820, 80], [61071, 8], [61120, 80], [61371, 8], [61420, 80], [61671, 8], [61720, 80], [61971, 8], [62020, 80], [62271, 8], [62321, 79], [62571, 8], [62621, 79], [62871, 9], [62921, 79], [63171, 9], [63221, 79], [63471, 9], [63522, 78], [63771, 9], [63822, 78], [64071, 9], [64122, 78], [64371, 9], [64422, 78], [64672, 8], [64722, 78], [64972, 8], [65023, 77], [65272, 8], [65323, 77], [65572, 8], [65623, 77], [65872, 8], [65923, 77], [66172, 9], [66224, 76], [66472, 9], [66524, 76], [66772, 9], [66824, 76], [67072, 9], [67124, 76], [67372, 9], [67424, 76], [67672, 9], [67725, 75], [67972, 9], [68025, 75], [68272, 9], [68325, 75], [68572, 9], [68625, 75], [68872, 9], [68926, 74], [69173, 9], [69226, 74], [69473, 9], [69526, 74], [69773, 9], [69826, 74], [70073, 9], [70126, 74], [70373, 9], [70427, 73], [70673, 9], [70727, 73], [70973, 9], [71027, 73], [71273, 9], [71327, 73], [71573, 9], [71628, 72], [71873, 9], [71928, 72], [72173, 9], [72228, 72], [72473, 10], [72528, 72], [72773, 10], [72828, 72], [73073, 10], [73129, 71], [73373, 10], [73429, 71], [73674, 9], [73729, 71], [73974, 9], [74029, 71], [74274, 9], [74330, 70], [74574, 9], [74630, 70], [74874, 9], [74930, 70], [75174, 9], [75230, 70], [75474, 9], [75530, 70], [75774, 10], [75831, 69], [76074, 10], [76131, 69], [76374, 10], [76431, 69], [76674, 10], [76731, 69], [76974, 10], [77032, 68], [77274, 10], [77332, 68], [77574, 126], [77874, 126], [78174, 126], [78475, 125], [78775, 125], [79075, 125], [79375, 125], [79675, 125], [79975, 125], [80275, 125], [80575, 125], [80875, 125], [81175, 125], [81475, 125], [81775, 125], [82075, 125], [82375, 125], [82675, 125], [82974, 126], [83274, 126], [83574, 126], [83874, 126], [84174, 126], [84473, 127], [84773, 127], [85073, 127], [85373, 127], [85673, 127], [85973, 127], [86272, 128], [86572, 128], [86872, 128], [87172, 128], [87472, 128], [87773, 127]], "point": [234, 229]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan224", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 1.0, "y": 0.916797757, "z": 2.5}, "object_poses": [{"objectName": "Statue_7e494874", "position": {"x": 3.16004586, "y": 0.5958878, "z": -0.574}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "Newspaper_1e8f1c02", "position": {"x": -0.204738, "y": 0.9126901, "z": 0.9744985}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_ec44dd4c", "position": {"x": -0.005741611, "y": 0.5400471, "z": -2.39291525}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "RemoteControl_b0bc5ae3", "position": {"x": -0.14561443, "y": 0.6646808, "z": 1.04367614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_c532ec98", "position": {"x": -2.885815, "y": 0.5613331, "z": 1.18768644}, "rotation": {"x": 0.0, "y": 270.0008, "z": 0.0}}, {"objectName": "Book_4e0dbe45", "position": {"x": 0.116, "y": 0.902580142, "z": 1.073}, "rotation": {"x": 0.0, "y": 29.9999466, "z": 0.0}}, {"objectName": "Box_ec78f573", "position": {"x": -0.204738, "y": 1.10026228, "z": 1.46026528}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_7e494874", "position": {"x": -3.29934573, "y": 0.5976248, "z": -0.423787683}, "rotation": {"x": 0.0, "y": 6.659434e-05, "z": 0.0}}, {"objectName": "CreditCard_5dc34411", "position": {"x": -0.244691774, "y": 0.411767781, "z": 0.936001658}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_c532ec98", "position": {"x": -2.78081179, "y": 0.365461469, "z": -1.874968}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WateringCan_cb2be04d", "position": {"x": 2.89399958, "y": 0.014939107, "z": -1.91600132}, "rotation": {"x": 0.0007926384, "y": 328.832336, "z": 0.000867469469}}, {"objectName": "Newspaper_1e8f1c02", "position": {"x": 0.067569, "y": 0.9126901, "z": 2.431799}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_4c89f135", "position": {"x": -2.16899252, "y": 1.19524324, "z": -2.1836822}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CellPhone_dad3503f", "position": {"x": -2.88582063, "y": 0.5640472, "z": 0.7750615}, "rotation": {"x": 0.0, "y": 270.0008, "z": 0.0}}, {"objectName": "RemoteControl_b0bc5ae3", "position": {"x": -0.204738, "y": 0.9108142, "z": 1.70314872}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_eef72b70", "position": {"x": -1.04643679, "y": 1.19524324, "z": -2.15178347}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pillow_fe37edd9", "position": {"x": -2.8127923, "y": 0.6234801, "z": 2.012936}, "rotation": {"x": 0.0, "y": 270.0008, "z": 0.0}}, {"objectName": "KeyChain_ec44dd4c", "position": {"x": -0.060494408, "y": 0.5400471, "z": -2.1903615}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}], "object_toggles": [], "random_seed": 2442495435, "scene_num": 224}, "task_id": "trial_T20190911_101119_501414", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A18X8ZAS0QDLKJ_3HMVI3QICM9MNN9FSWLWPOAJVFO1YC", "high_descs": ["Turn left then head towards the chair, turn right then head towards the couch, turn right towards the black cabinet.", "Pick up the newspaper on the black cabinet.", "Turn left then head towards the couch, turn right towards the door, then right towards the television. ", "Open the middle drawer in the black cabinet and place the newspaper inside. ", "Pick up the newspaper on the left side of the television.", "Take one step to the right, then stand if front of the television.", "Open the middle drawer in the black cabinet.", "Place the newspaper on top of the other one, the close the door."], "task_desc": "Place two newspapers in the drawer underneath the television.", "votes": [1, 1]}, {"assignment_id": "A10AVWALIHR4UQ_3MYYFCXHJ6O3TVLYL8ISGMAI23Q4GC", "high_descs": ["Turn to the left and move toward the black chairs in front of the window then turn right and move to the black dresser opposite from the couch.", "Pick up the newspaper located left of the book from the edge of the dresser.", "Move around the dresser, toward the gray door, then turn right and face where the dresser meets the corner of the room.", "Open the top center dresser drawer underneath the remote control, place the newspaper in the drawer, and then close the drawer.", "Move left, towards the light switch next to the door, then turn right and face the dresser.", "Pick up the newspaper to the left of the television.", "Turn right, move toward the couch, then turn left and face the center of the dresser.", "Open the top center dresser drawer underneath the remote control, place the newspaper on top of the newspaper already in the drawer, then close the drawer."], "task_desc": "Store two newspapers in a dresser.", "votes": [1, 1]}, {"assignment_id": "A2XSGALF8O9ISQ_3TMSXRD2X9HIYIEFDV384RP3K791WY", "high_descs": ["Turn to the left and walk down the hallway, turn to the right and walk into the living room, now turn right and face the black table", "Pick up the newspaper that is on the table", "Walk to the end of the table near the wall", "Open the top drawer on the second row from the wall and place the paper in it, now close the drawer", "Turn and look toward the door, then go back to the table and look at the other newspaper", "Pick up the newspaper on the table", "Turn around and look around the room, turn back to the table", "Open the top drawer in the second row from the wall and place the paper on top of the other paper, now close the drawer"], "task_desc": "Gather up the newspapers in the living room and place them in a drawer", "votes": [1, 1]}]}}