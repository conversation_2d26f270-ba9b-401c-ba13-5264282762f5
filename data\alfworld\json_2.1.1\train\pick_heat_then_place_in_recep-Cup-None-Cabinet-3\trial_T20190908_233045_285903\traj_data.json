{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000275.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000276.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000277.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000278.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000279.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000280.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000281.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000282.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000283.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000284.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000285.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000286.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000287.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000288.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000289.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000290.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000291.png", "low_idx": 48}, {"high_idx": 5, "image_name": "000000292.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000293.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000294.png", "low_idx": 49}, {"high_idx": 5, "image_name": "000000295.png", "low_idx": 49}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|1|8|1|15"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [3.754017828, 3.754017828, 8.92202948, 8.92202948, 7.01847076, 7.01847076]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [4.052, 4.052, 7.668, 7.668, 0.911987304, 0.911987304]], "forceVisible": true, "objectId": "Cup|+00.94|+01.75|+02.23"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|1|3|1|30"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|0|3|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [3.754017828, 3.754017828, 8.92202948, 8.92202948, 7.01847076, 7.01847076]], "coordinateReceptacleObjectId": ["Cabinet", [-5.84518432, -5.84518432, 1.8804, 1.8804, 3.100034236, 3.100034236]], "forceVisible": true, "objectId": "Cup|+00.94|+01.75|+02.23", "receptacleObjectId": "Cabinet|-01.46|+00.78|+00.47"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|+01.01|+00.23|+01.92"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 90000]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.94|+01.75|+02.23"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [32, 36, 79, 98], "mask": [[10540, 18], [10834, 34], [11132, 42], [11432, 45], [11732, 47], [12032, 48], [12332, 48], [12633, 47], [12933, 47], [13233, 47], [13533, 47], [13833, 47], [14133, 47], [14434, 46], [14734, 46], [15035, 45], [15336, 43], [15636, 42], [15937, 41], [16237, 41], [16538, 39], [16838, 39], [17139, 38], [17439, 38], [17739, 37], [18040, 36], [18340, 36], [18641, 35], [18941, 35], [19241, 34], [19542, 33], [19842, 33], [20142, 33], [20443, 32], [20743, 32], [21043, 32], [21343, 32], [21644, 30], [21944, 30], [22244, 30], [22544, 30], [22844, 30], [23145, 29], [23445, 29], [23745, 29], [24045, 29], [24345, 29], [24645, 30], [24946, 29], [25245, 30], [25545, 30], [25845, 31], [26145, 31], [26445, 31], [26745, 31], [27045, 31], [27345, 31], [27645, 31], [27945, 31], [28246, 30], [28546, 30], [28846, 15], [28862, 14], [29147, 6], [29154, 7], [29166, 6]], "point": [55, 66]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|+01.01|+00.23|+01.92"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 300], "mask": [[0, 18394], [18395, 299], [18695, 299], [18995, 286], [19304, 275], [19608, 271], [19910, 268], [20211, 266], [20512, 265], [20813, 264], [21113, 264], [21413, 264], [21713, 264], [22013, 264], [22313, 264], [22613, 264], [22913, 264], [23213, 265], [23513, 265], [23812, 267], [24112, 267], [24412, 267], [24712, 268], [25011, 269], [25311, 270], [25611, 270], [25910, 272], [26210, 272], [26509, 274], [26809, 274], [27109, 275], [27408, 276], [27708, 277], [28007, 278], [28307, 279], [28606, 281], [28905, 283], [29204, 286], [29503, 288], [29795, 16953], [46753, 186], [46961, 84], [47058, 173], [47269, 74], [47360, 166], [47574, 67], [47662, 160], [47878, 62], [47963, 157], [48180, 60], [48263, 154], [48483, 56], [48564, 151], [48785, 53], [48864, 149], [49087, 51], [49165, 147], [49388, 49], [49465, 146], [49689, 48], [49766, 144], [49990, 47], [50066, 143], [50291, 45], [50366, 142], [50592, 44], [50666, 142], [50892, 44], [50966, 142], [51192, 44], [51267, 141], [51492, 44], [51567, 141], [51792, 44], [51867, 141], [52092, 45], [52167, 141], [52392, 45], [52467, 141], [52692, 46], [52768, 140], [52992, 46], [53067, 141], [53292, 47], [53367, 141], [53592, 47], [53667, 141], [53892, 48], [53967, 141], [54192, 49], [54266, 142], [54492, 50], [54566, 142], [54792, 51], [54865, 143], [55092, 52], [55164, 145], [55391, 55], [55463, 146], [55691, 56], [55761, 148], [55991, 59], [56058, 151], [56291, 218], [56591, 219], [56890, 220], [57190, 221], [57489, 223], [57788, 224], [58088, 225], [58387, 227], [58686, 229], [58985, 230], [59285, 230], [59585, 231], [59884, 232], [60184, 233], [60483, 234], [60783, 235], [61082, 236], [61382, 236], [61682, 237], [61981, 238], [62281, 238], [62581, 239], [62880, 240], [63180, 240], [63480, 241], [63779, 242], [64079, 242], [64379, 243], [64678, 244], [64978, 244], [65278, 245], [65577, 246], [65877, 246], [66177, 246], [66477, 246], [66777, 246], [67077, 247], [67376, 248], [67676, 248], [67976, 248], [68276, 248], [68576, 248], [68876, 248], [69176, 248], [69476, 248], [69776, 249], [70075, 250], [70375, 250], [70675, 250], [70975, 250], [71275, 250], [71575, 249], [71876, 248], [72176, 248], [72476, 247], [72777, 246], [73077, 246], [73377, 246], [73677, 246], [73977, 246], [74277, 246], [74577, 246], [74877, 246], [75177, 246], [75477, 246], [75777, 247], [76076, 248], [76376, 248], [76676, 248], [76976, 248], [77276, 249], [77575, 250], [77875, 250], [78175, 250], [78475, 250], [78775, 251], [79074, 252], [79374, 253], [79673, 254], [79973, 255], [80272, 258], [80534, 2], [80564, 2], [80570, 268], [80862, 278], [81160, 282], [81458, 285], [81757, 287], [82056, 289], [82355, 292], [82653, 7347]], "point": [149, 149]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28219, 195], [28518, 197], [28817, 199], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 108], [46961, 48], [47131, 100], [47269, 40], [47431, 95], [47574, 35], [47731, 91], [47878, 30], [48032, 88], [48180, 28], [48332, 85], [48483, 25], [48632, 83], [48785, 23], [48932, 81], [49087, 21], [49233, 79], [49388, 20], [49533, 78], [49689, 19], [49833, 77], [49990, 18], [50134, 75], [50291, 16], [50434, 74], [50592, 15], [50734, 74], [50892, 15], [51034, 74], [51192, 15], [51335, 73], [51492, 15], [51635, 73], [51792, 15], [51935, 73], [52092, 15], [52235, 73], [52392, 14], [52536, 72], [52692, 14], [52836, 72], [52992, 14], [53136, 72], [53292, 14], [53437, 71], [53592, 14], [53737, 71], [53892, 14], [54037, 71]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.94|+01.75|+02.23", "placeStationary": true, "receptacleObjectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 211], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 139], [46961, 48], [47100, 131], [47269, 40], [47400, 126], [47574, 35], [47700, 122], [47878, 30], [48000, 120], [48180, 28], [48300, 117], [48483, 25], [48600, 115], [48785, 23], [48900, 113], [49087, 21], [49200, 112], [49388, 20], [49500, 111], [49689, 19], [49800, 110], [49990, 18], [50100, 109], [50291, 16], [50400, 108], [50592, 15], [50700, 108], [50892, 15], [51000, 108], [51192, 15], [51300, 108], [51492, 15], [51600, 108], [51792, 15], [51900, 108], [52092, 15], [52200, 108], [52392, 14], [52500, 43], [52692, 14], [52800, 42], [52992, 14], [53100, 41], [53292, 14], [53400, 41], [53592, 14], [53700, 40], [53892, 14], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 25], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 14], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 211], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 126], [32542, 73], [32700, 122], [32845, 70], [33000, 120], [33147, 68], [33300, 119], [33448, 67], [33600, 118], [33748, 67], [33900, 117], [34049, 66], [34200, 117], [34349, 65], [34500, 117], [34649, 65], [34800, 118], [34949, 65], [35100, 118], [35249, 65], [35400, 118], [35549, 65], [35700, 118], [35848, 66], [36000, 118], [36148, 66], [36300, 119], [36447, 66], [36600, 119], [36747, 66], [36900, 120], [37046, 67], [37200, 121], [37346, 67], [37500, 121], [37646, 67], [37800, 121], [37945, 68], [38100, 122], [38245, 68], [38400, 122], [38545, 68], [38700, 122], [38844, 68], [39000, 123], [39144, 68], [39300, 123], [39444, 68], [39600, 123], [39744, 68], [39900, 124], [40044, 68], [40200, 124], [40344, 68], [40500, 124], [40644, 68], [40800, 124], [40944, 68], [41100, 124], [41244, 67], [41400, 124], [41543, 68], [41700, 124], [41844, 67], [42000, 124], [42144, 67], [42300, 124], [42444, 67], [42600, 124], [42744, 67], [42900, 124], [43044, 67], [43200, 124], [43344, 66], [43500, 124], [43644, 66], [43800, 124], [43944, 66], [44100, 125], [44243, 67], [44400, 125], [44543, 67], [44700, 125], [44843, 67], [45000, 131], [45136, 74], [45300, 132], [45436, 74], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 209], [47700, 208], [48000, 208], [48300, 208], [48600, 208], [48900, 208], [49200, 208], [49500, 208], [49800, 208], [50100, 207], [50400, 207], [50700, 207], [51000, 207], [51300, 207], [51600, 207], [51900, 207], [52200, 206], [52500, 43], [52673, 33], [52800, 42], [52973, 33], [53100, 41], [53273, 33], [53400, 41], [53573, 33], [53700, 40], [53874, 32], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 25], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 14], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28219, 195], [28518, 197], [28817, 199], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [4.648, 4.648, 3.412, 3.412, 4.779836, 4.779836]], "forceVisible": true, "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28219, 195], [28518, 197], [28817, 199], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [15, 72, 216, 181], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27623, 189], [27921, 192], [28219, 195], [28518, 197], [28817, 199], [29116, 200], [29415, 202], [29715, 201], [30016, 200], [30316, 200], [30616, 200], [30916, 200], [31217, 199], [31517, 199], [31817, 198], [32117, 198], [32418, 197], [32718, 197], [33018, 197], [33318, 197], [33619, 196], [33919, 196], [34219, 195], [34520, 194], [34820, 194], [35120, 194], [35420, 194], [35721, 193], [36021, 193], [36321, 192], [36621, 192], [36922, 191], [37222, 191], [37522, 191], [37823, 190], [38123, 190], [38423, 190], [38723, 189], [39024, 188], [39324, 188], [39624, 188], [39924, 188], [40225, 187], [40525, 187], [40825, 187], [41125, 186], [41426, 185], [41726, 185], [42026, 185], [42327, 184], [42627, 184], [42927, 184], [43227, 183], [43528, 182], [43828, 182], [44128, 182], [44428, 182], [44729, 181], [45029, 181], [45329, 181], [45630, 179], [45930, 179], [46230, 179], [46530, 179], [46831, 178], [47131, 178], [47431, 178], [47731, 177], [48032, 176], [48332, 176], [48632, 176], [48932, 176], [49233, 175], [49533, 175], [49833, 175], [50134, 173], [50434, 173], [50734, 173], [51034, 173], [51335, 172], [51635, 172], [51935, 172], [52235, 171], [52536, 170], [52836, 170], [53136, 170], [53437, 169], [53737, 169], [54037, 137]], "point": [115, 125]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.94|+01.75|+02.23"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [117, 109, 148, 152], "mask": [[32526, 16], [32822, 23], [33120, 27], [33419, 29], [33718, 30], [34017, 32], [34317, 32], [34617, 32], [34918, 31], [35218, 31], [35518, 31], [35818, 30], [36118, 30], [36419, 28], [36719, 28], [37020, 26], [37321, 25], [37621, 25], [37921, 24], [38222, 23], [38522, 23], [38822, 22], [39123, 21], [39423, 21], [39723, 21], [40024, 20], [40324, 20], [40624, 20], [40924, 20], [41224, 20], [41524, 19], [41824, 20], [42124, 20], [42424, 20], [42724, 20], [43024, 20], [43324, 20], [43624, 20], [43924, 20], [44225, 18], [44525, 18], [44825, 18], [45131, 5], [45432, 4]], "point": [132, 129]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.16|+01.19|+00.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 72, 216, 244], "mask": [[21352, 145], [21650, 148], [21949, 150], [22248, 152], [22546, 154], [22845, 156], [23143, 159], [23442, 160], [23741, 162], [24039, 165], [24338, 166], [24637, 168], [24935, 171], [25234, 172], [25532, 175], [25831, 177], [26130, 178], [26428, 181], [26727, 183], [27026, 184], [27324, 187], [27611, 201], [27910, 203], [28208, 206], [28507, 208], [28805, 211], [29104, 212], [29402, 215], [29701, 215], [30000, 216], [30300, 216], [30600, 216], [30900, 216], [31200, 216], [31500, 216], [31800, 215], [32100, 215], [32400, 215], [32700, 215], [33000, 215], [33300, 215], [33600, 215], [33900, 215], [34200, 214], [34500, 214], [34800, 214], [35100, 214], [35400, 214], [35700, 214], [36000, 214], [36300, 213], [36600, 213], [36900, 213], [37200, 213], [37500, 213], [37800, 213], [38100, 213], [38400, 213], [38700, 212], [39000, 212], [39300, 212], [39600, 212], [39900, 212], [40200, 212], [40500, 212], [40800, 212], [41100, 211], [41400, 211], [41700, 211], [42000, 211], [42300, 211], [42600, 211], [42900, 211], [43200, 210], [43500, 210], [43800, 210], [44100, 210], [44400, 210], [44700, 210], [45000, 210], [45300, 210], [45600, 209], [45900, 209], [46200, 209], [46500, 209], [46800, 209], [47100, 209], [47400, 138], [47562, 47], [47700, 133], [47867, 41], [48000, 129], [48171, 37], [48300, 127], [48473, 35], [48600, 124], [48776, 32], [48900, 122], [49078, 30], [49200, 120], [49380, 28], [49500, 118], [49682, 26], [49800, 117], [49983, 25], [50100, 115], [50285, 22], [50400, 114], [50586, 21], [50700, 112], [50888, 19], [51000, 111], [51189, 18], [51300, 110], [51490, 17], [51600, 109], [51791, 16], [51900, 108], [52092, 15], [52200, 108], [52392, 14], [52500, 43], [52693, 13], [52800, 42], [52994, 12], [53100, 41], [53294, 12], [53400, 41], [53595, 11], [53700, 40], [53896, 10], [54000, 40], [54300, 39], [54600, 38], [54900, 38], [55200, 37], [55500, 37], [55800, 36], [56100, 35], [56400, 35], [56700, 34], [57000, 33], [57300, 33], [57600, 32], [57900, 32], [58200, 31], [58500, 30], [58800, 30], [59100, 29], [59400, 29], [59700, 28], [60000, 27], [60300, 27], [60600, 26], [60900, 25], [61200, 25], [61500, 24], [61800, 24], [62100, 23], [62400, 22], [62700, 22], [63000, 21], [63300, 21], [63600, 20], [63900, 19], [64200, 19], [64500, 18], [64800, 18], [65100, 17], [65400, 16], [65700, 16], [66000, 15], [66300, 14], [66600, 14], [66900, 13], [67200, 13], [67500, 12], [67800, 11], [68100, 11], [68400, 10], [68700, 10], [69000, 9], [69300, 8], [69600, 8], [69900, 7], [70200, 7], [70500, 6], [70800, 5], [71100, 5], [71400, 4], [71700, 3], [72000, 3], [72300, 2], [72600, 2], [72900, 1]], "point": [108, 157]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-01.46|+00.78|+00.47"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [176, 109, 299, 199], "mask": [[32582, 2], [32882, 11], [33182, 26], [33482, 41], [33782, 61], [34081, 119], [34381, 119], [34681, 119], [34981, 119], [35281, 119], [35581, 119], [35881, 119], [36181, 119], [36481, 119], [36780, 120], [37080, 120], [37380, 120], [37680, 120], [37980, 120], [38280, 120], [38580, 120], [38880, 120], [39180, 120], [39480, 120], [39779, 121], [40079, 121], [40379, 121], [40679, 121], [40979, 121], [41279, 121], [41579, 121], [41879, 121], [42179, 121], [42478, 122], [42778, 122], [43078, 122], [43378, 122], [43678, 122], [43978, 122], [44278, 122], [44578, 122], [44878, 122], [45177, 122], [45477, 122], [45777, 121], [46077, 121], [46377, 120], [46677, 119], [46977, 119], [47277, 118], [47577, 118], [47877, 117], [48176, 118], [48476, 117], [48776, 116], [49078, 114], [49380, 111], [49682, 109], [49983, 107], [50285, 105], [50586, 103], [50888, 100], [51189, 99], [51490, 97], [51791, 96], [52092, 94], [52392, 94], [52693, 92], [52994, 90], [53294, 90], [53595, 88], [53896, 87], [54196, 86], [54497, 85], [54797, 84], [55097, 83], [55397, 83], [55697, 82], [55997, 82], [56297, 81], [56597, 80], [56897, 80], [57197, 79], [57497, 79], [57797, 78], [58096, 78], [58396, 78], [58696, 77], [58996, 77], [59296, 76], [59596, 76]], "point": [237, 153]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.94|+01.75|+02.23", "placeStationary": true, "receptacleObjectId": "Cabinet|-01.46|+00.78|+00.47"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [175, 109, 299, 300], "mask": [[32581, 3], [32881, 12], [33181, 27], [33481, 42], [33780, 63], [34080, 120], [34380, 120], [34680, 120], [34980, 120], [35280, 120], [35580, 120], [35880, 120], [36180, 120], [36480, 120], [36779, 121], [37079, 121], [37379, 121], [37679, 121], [37979, 121], [38279, 121], [38579, 121], [38879, 121], [39179, 121], [39479, 121], [39778, 122], [40078, 122], [40378, 122], [40678, 122], [40978, 122], [41278, 122], [41578, 122], [41878, 122], [42178, 122], [42477, 123], [42777, 123], [43077, 122], [43377, 122], [43677, 121], [43977, 121], [44277, 23], [44310, 87], [44577, 21], [44612, 85], [44877, 20], [44914, 82], [45177, 19], [45215, 80], [45476, 20], [45516, 79], [45776, 20], [45817, 77], [46076, 20], [46117, 77], [46376, 21], [46417, 76], [46676, 21], [46718, 75], [46976, 21], [47018, 74], [47276, 21], [47317, 74], [47576, 21], [47617, 74], [47876, 21], [47917, 73], [48176, 22], [48216, 74], [48475, 23], [48516, 73], [48776, 22], [48815, 74], [49078, 20], [49114, 74], [49380, 19], [49413, 74], [49682, 18], [49712, 75], [49983, 18], [50010, 76], [50285, 20], [50306, 80], [50586, 99], [50888, 97], [51189, 95], [51490, 94], [51791, 92], [52092, 90], [52392, 90], [52693, 88], [52994, 87], [53294, 86], [53595, 85], [53896, 83], [54196, 82], [54497, 81], [54797, 80], [55097, 80], [55397, 79], [55697, 79], [55997, 78], [56297, 78], [56597, 77], [56897, 76], [57197, 76], [57497, 75], [57797, 75], [58096, 75], [58396, 75], [58696, 74], [58996, 8], [59296, 8], [59596, 8], [59896, 9], [60195, 10], [60495, 10], [60795, 10], [61095, 10], [61395, 10], [61694, 12], [61994, 12], [62293, 13], [62593, 13], [62893, 13], [63192, 14], [63492, 15], [63791, 16], [64090, 17], [64390, 17], [64689, 18], [64988, 20], [65287, 21], [65586, 22], [65885, 23], [66184, 24], [66483, 25], [66783, 26], [67082, 27], [67381, 28], [67681, 28], [67980, 29], [68280, 29], [68579, 31], [68879, 31], [69179, 31], [69478, 31], [69778, 31], [70078, 31], [70377, 31], [70677, 31], [70977, 31], [71277, 30], [71576, 31], [71876, 31], [72176, 30], [72476, 30], [72776, 30], [73076, 29], [73377, 28], [73677, 28], [73977, 27], [74277, 27], [74577, 27], [74877, 26], [75177, 26], [75477, 26], [75777, 25], [76077, 25], [76377, 25], [76677, 24], [76977, 24], [77276, 25], [77576, 24], [77876, 24], [78176, 24], [78476, 23], [78776, 23], [79076, 23], [79376, 22], [79676, 22], [79976, 22], [80276, 21], [80577, 20], [80877, 20], [81177, 19], [81477, 19], [81777, 19], [82077, 18], [82377, 18], [82677, 18], [82977, 17], [83277, 17], [83577, 17], [83877, 16], [84177, 16], [84477, 16], [84777, 15], [85077, 15], [85377, 15], [85677, 14], [85977, 14], [86276, 15], [86576, 14], [86876, 14], [87176, 14], [87476, 13], [87776, 13], [88076, 13], [88376, 12], [88678, 10], [88979, 9], [89279, 8], [89579, 8], [89879, 8]], "point": [237, 195]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-01.46|+00.78|+00.47"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [172, 109, 299, 300], "mask": [[32581, 3], [32881, 12], [33181, 27], [33481, 42], [33780, 63], [34080, 120], [34380, 120], [34680, 120], [34980, 120], [35280, 120], [35580, 120], [35880, 120], [36180, 120], [36480, 120], [36779, 121], [37079, 121], [37379, 121], [37679, 121], [37979, 121], [38279, 121], [38579, 121], [38879, 121], [39179, 121], [39479, 121], [39778, 122], [40078, 122], [40378, 122], [40678, 122], [40978, 122], [41278, 122], [41578, 122], [41878, 122], [42178, 122], [42477, 123], [42777, 123], [43077, 122], [43377, 122], [43677, 121], [43977, 121], [44277, 23], [44310, 87], [44577, 21], [44612, 85], [44877, 20], [44914, 82], [45177, 19], [45215, 80], [45476, 20], [45516, 79], [45776, 20], [45817, 77], [46076, 20], [46117, 77], [46376, 21], [46417, 76], [46676, 21], [46718, 75], [46976, 21], [47018, 74], [47276, 21], [47317, 74], [47576, 21], [47617, 74], [47876, 21], [47917, 73], [48176, 22], [48216, 74], [48475, 23], [48516, 73], [48775, 23], [48815, 74], [49075, 23], [49114, 74], [49375, 24], [49413, 74], [49675, 25], [49712, 12], [49728, 59], [49975, 26], [50010, 11], [50031, 55], [50275, 30], [50306, 14], [50333, 53], [50575, 44], [50634, 51], [50875, 44], [50935, 50], [51175, 44], [51235, 49], [51474, 45], [51536, 48], [51774, 45], [51836, 47], [52074, 45], [52136, 46], [52374, 45], [52436, 46], [52674, 45], [52736, 45], [52974, 46], [53035, 46], [53274, 46], [53335, 45], [53574, 46], [53634, 46], [53874, 45], [53932, 47], [54173, 46], [54231, 47], [54473, 45], [54530, 48], [54773, 45], [54830, 47], [55073, 45], [55130, 47], [55373, 45], [55429, 47], [55673, 46], [55729, 47], [55973, 47], [56028, 47], [56273, 48], [56328, 47], [56573, 101], [56873, 100], [57172, 101], [57472, 100], [57772, 100], [58072, 99], [58372, 99], [58672, 98], [58972, 32], [59272, 32], [59572, 32], [59872, 33], [60172, 33], [60472, 33], [60772, 33], [61072, 33], [61372, 33], [61672, 34], [61972, 34], [62272, 34], [62572, 34], [62872, 34], [63172, 34], [63473, 34], [63773, 34], [64073, 34], [64373, 34], [64673, 34], [64973, 35], [65273, 35], [65573, 35], [65873, 35], [66173, 35], [66473, 35], [66773, 36], [67073, 36], [67373, 36], [67674, 35], [67974, 35], [68274, 35], [68574, 36], [68874, 36], [69174, 36], [69474, 35], [69774, 35], [70074, 35], [70374, 34], [70674, 34], [70974, 34], [71274, 33], [71574, 33], [71875, 32], [72175, 31], [72475, 31], [72775, 31], [73075, 30], [73375, 30], [73675, 30], [73975, 29], [74275, 29], [74575, 29], [74875, 28], [75175, 28], [75475, 28], [75775, 27], [76076, 26], [76376, 26], [76676, 25], [76976, 25], [77276, 25], [77576, 24], [77876, 24], [78176, 24], [78476, 23], [78776, 23], [79076, 23], [79376, 22], [79676, 22], [79976, 22], [80276, 21], [80577, 20], [80877, 20], [81177, 19], [81477, 19], [81777, 19], [82077, 18], [82377, 18], [82677, 18], [82977, 17], [83277, 17], [83577, 17], [83877, 16], [84177, 16], [84477, 16], [84777, 15], [85077, 15], [85377, 15], [85677, 14], [85977, 14], [86276, 15], [86576, 14], [86876, 14], [87176, 14], [87476, 13], [87776, 13], [88076, 13], [88376, 12], [88678, 10], [88979, 9], [89279, 8], [89579, 8], [89879, 8]], "point": [235, 195]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan3", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": -0.5, "y": 1.12401652, "z": -0.5}, "object_poses": [{"objectName": "DishSponge_a15664d1", "position": {"x": -1.88644969, "y": 0.343378425, "z": 1.21397424}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": -1.23730922, "y": 1.32404137, "z": -3.09262371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -2.179573, "y": 1.32412946, "z": -1.94141138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": 0.899722457, "y": 0.4617294, "z": 0.6816628}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": 1.285346, "y": 1.33799994, "z": -2.42411566}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": 1.02584076, "y": 1.33799994, "z": -0.942471862}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.2394, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.749, "y": 1.3324, "z": -2.973}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.95454407, "y": 1.09187412, "z": -0.4510209}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.0945816, "y": 1.36382449, "z": -2.870881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": -1.68255043, "y": 0.344428062, "z": 1.42058778}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -2.00441241, "y": 1.36631966, "z": -1.18665123}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.66056263, "y": 1.35270047, "z": 1.4197371}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 0.9202187, "y": 1.55844033, "z": 1.47732091}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": 0.9457595, "y": 2.06274128, "z": 2.28850651}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -1.78949094, "y": 0.3421222, "z": -2.362065}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -1.817123, "y": 0.3421222, "z": 0.412196}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": -1.38195264, "y": 1.31800008, "z": -0.3328594}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": 0.5068307, "y": 1.39739525, "z": -1.312883}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.79031563, "y": 1.39912164, "z": -1.94141138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": 0.766335964, "y": 1.39912164, "z": -2.794527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.747982, "y": 0.339971423, "z": -1.9577744}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.965999663, "y": 1.3713994, "z": -1.53499949}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": -1.79031563, "y": 1.37188375, "z": 0.770108938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_dd060264", "position": {"x": 0.492037773, "y": 1.39739525, "z": -3.12197924}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pot_930b560d", "position": {"x": -0.6309, "y": 1.3317, "z": -3.3019}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Knife_bee62bfa", "position": {"x": -1.79031563, "y": 1.35270047, "z": -1.68147337}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_eef34b87", "position": {"x": -1.16594541, "y": 1.32130814, "z": -3.31436586}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_e8912d85", "position": {"x": 0.9202187, "y": 1.56427288, "z": 1.72473288}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SoapBottle_cf9afd50", "position": {"x": -1.66056311, "y": 1.32319188, "z": -1.68147337}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_61df51be", "position": {"x": 0.766335964, "y": 1.32354856, "z": -3.164938}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_f306e730", "position": {"x": 0.938504457, "y": 1.75461769, "z": 2.23050737}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Kettle_299f2c2a", "position": {"x": 0.5068307, "y": 1.32404137, "z": -2.794527}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_7c718722", "position": {"x": -1.4514004, "y": 1.36382449, "z": -2.870881}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pan_9d168802", "position": {"x": -1.88114953, "y": 0.327762932, "z": -2.03532434}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b13b6e3e", "position": {"x": 0.9443453, "y": 1.2028, "z": 1.2225256}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_560d3534", "position": {"x": -1.40105808, "y": 1.32392883, "z": -1.94141138}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_b5e01a1e", "position": {"x": -1.89176774, "y": 1.4003731, "z": -1.07311773}, "rotation": {"x": 1.28164923, "y": 359.6781, "z": 355.700836}}, {"objectName": "Spatula_f0a2d64c", "position": {"x": -1.648636, "y": 1.343986, "z": -1.3240732}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_ddd73f57", "position": {"x": -1.634, "y": 0.3844704, "z": 2.23422623}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_93b30017", "position": {"x": -1.23730922, "y": 1.35692108, "z": -2.76001024}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_d5570300", "position": {"x": 1.05303526, "y": 0.345585465, "z": -2.26769447}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "PepperShaker_c15f34b3", "position": {"x": -1.954416, "y": 0.338814139, "z": 1.42058778}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_a15664d1", "position": {"x": -1.82631838, "y": 1.06355417, "z": -0.4966268}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_e0793d4c", "position": {"x": -1.76220548, "y": 1.0521791, "z": -0.587838531}, "rotation": {"x": -1.40334191e-14, "y": 180.0, "z": 0.0}}, {"objectName": "Apple_e84ad935", "position": {"x": 1.09558511, "y": 1.80610383, "z": 2.171608}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_259b36f2", "position": {"x": 0.349309921, "y": 1.318, "z": -2.67849255}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1177101552, "scene_num": 3}, "task_id": "trial_T20190908_233045_285903", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "ACSS93E03ZUGX_36DSNE9QZ8PP9F7F867LSJS557UOJ0", "high_descs": ["Turn straight and turn right to face the refrigerator.", "Open the refrigerator and pick up the green cup inside.", "Move to the right to face the microwave.", "Microwave the green cup and then remove the green cup from the microwave.", "Turn around and go straight to face the counter to the right of the sink.", "Open the cabinet to the right and place the green cup inside of it."], "task_desc": "Place the microwaved green cup inside of the cabinet beneath the sink to the right.", "votes": [1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3KOPY89HMBT0NQETP8NT4GUUAG93JV", "high_descs": ["Walk to the wall then turn right and walk to the fridge.", "Open the fridge and grab the green cup that's in there then close the fridge.", "Take two steps to your right to face the microwave.", "Warm the green cup in the microwave then take it back out and close the door.", "Turn around and walk to the counter.", "Open the cupboard that's under the sink and to the right of it and put the cup down there before closing the door."], "task_desc": "Put a warm cup in the cupboard.", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_3ON104KXQNN7B6XWWLBR3BG8QC2W4S", "high_descs": ["Walk over to the wall, then turn right and walk up to the fridge.", "Open the fridge and take the green cup out of the fridge, then close the fridge.", "Turn right and walk forward, then turn left to face the microwave.", "Put the green cup in the microwave and heat it up for a couple seconds, then take it out and close the microwave.", "Turn right and walk forward, then hang a right and walk over to the counter.", "Open the lower cabinet with a black door and put the heated cup in the cabinet, then close the door."], "task_desc": "Put a heated green cup in a cabinet.", "votes": [1, 1]}]}}