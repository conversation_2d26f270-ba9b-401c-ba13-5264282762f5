{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 6}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 23}, {"high_idx": 4, "image_name": "000000141.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000143.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000144.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000145.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000148.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000149.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000150.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000151.png", "low_idx": 24}, {"high_idx": 4, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000155.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000158.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000159.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000160.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000161.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000162.png", "low_idx": 25}, {"high_idx": 4, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000165.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 5, "image_name": "000000167.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000168.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000169.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000170.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000171.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000172.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000173.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000174.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000175.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 5, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 6, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000184.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000185.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000190.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000191.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 6, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000195.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000196.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000197.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000201.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000202.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 6, "image_name": "000000204.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000205.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 7, "image_name": "000000208.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000209.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000210.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000211.png", "low_idx": 33}, {"high_idx": 7, "image_name": "000000212.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000213.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000214.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000215.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000216.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000217.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000218.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000219.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000220.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000221.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000222.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000223.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000224.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000225.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000226.png", "low_idx": 34}, {"high_idx": 7, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000228.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000229.png", "low_idx": 35}, {"high_idx": 7, "image_name": "000000230.png", "low_idx": 35}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "ToiletPaper", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|11|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-1.393856, -1.393856, 10.96252728, 10.96252728, 3.2379888, 3.2379888]], "coordinateReceptacleObjectId": ["CounterTop", [-1.108576296, -1.108576296, 7.72422504, 7.72422504, 3.1427888, 3.1427888]], "forceVisible": true, "objectId": "ToiletPaper|-00.35|+00.81|+02.74"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-11|7|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-1.393856, -1.393856, 10.96252728, 10.96252728, 3.2379888, 3.2379888]], "coordinateReceptacleObjectId": ["Drawer", [-8.997444, -8.997444, 1.1200004, 1.1200004, 1.276000144, 1.276000144]], "forceVisible": true, "objectId": "ToiletPaper|-00.35|+00.81|+02.74", "receptacleObjectId": "Drawer|-02.25|+00.32|+00.28"}}, {"discrete_action": {"action": "GotoLocation", "args": ["toilet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-11|9|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["toiletpaper"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["ToiletPaper", [-12.0699854, -12.0699854, 12.558772, 12.558772, 3.805352212, 3.805352212]], "coordinateReceptacleObjectId": ["<PERSON><PERSON><PERSON>", [-11.368, -11.368, 11.02, 11.02, 0.003943652, 0.003943652]], "forceVisible": true, "objectId": "ToiletPaper|-03.02|+00.95|+03.14"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-11|7|2|45"}}, {"discrete_action": {"action": "PutObject", "args": ["toiletpaper", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["ToiletPaper", [-12.0699854, -12.0699854, 12.558772, 12.558772, 3.805352212, 3.805352212]], "coordinateReceptacleObjectId": ["Drawer", [-8.997444, -8.997444, 1.1200004, 1.1200004, 1.276000144, 1.276000144]], "forceVisible": true, "objectId": "ToiletPaper|-03.02|+00.95|+03.14", "receptacleObjectId": "Drawer|-02.25|+00.32|+00.28"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-00.35|+00.81|+02.74"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [148, 147, 157, 175], "mask": [[43951, 4], [44249, 8], [44548, 9], [44848, 9], [45148, 10], [45448, 10], [45748, 10], [46048, 9], [46348, 9], [46648, 9], [46948, 9], [47248, 9], [47548, 9], [47848, 9], [48148, 9], [48448, 9], [48748, 9], [49048, 9], [49348, 9], [49648, 9], [49948, 9], [50248, 9], [50548, 9], [50848, 9], [51148, 9], [51448, 9], [51748, 9], [52049, 7], [52350, 5]], "point": [152, 160]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-02.25|+00.32|+00.28"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [25, 139, 120, 169], "mask": [[41425, 93], [41725, 93], [42026, 92], [42326, 92], [42627, 91], [42927, 91], [43227, 91], [43528, 91], [43828, 91], [44129, 90], [44429, 90], [44730, 89], [45030, 89], [45331, 88], [45631, 88], [45932, 88], [46232, 88], [46533, 87], [46833, 87], [47133, 87], [47434, 86], [47734, 86], [48035, 85], [48335, 85], [48636, 85], [48936, 85], [49237, 84], [49537, 84], [49838, 83], [50138, 83], [50439, 82]], "point": [72, 153]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-00.35|+00.81|+02.74", "placeStationary": true, "receptacleObjectId": "Drawer|-02.25|+00.32|+00.28"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 139, 117, 227], "mask": [[41430, 85], [41729, 86], [42029, 86], [42328, 87], [42628, 87], [42927, 88], [43227, 88], [43526, 90], [43826, 90], [44125, 91], [44425, 91], [44725, 91], [45024, 92], [45324, 92], [45623, 93], [45923, 94], [46222, 95], [46522, 95], [46821, 96], [47121, 96], [47420, 97], [47720, 97], [48019, 98], [48319, 99], [48618, 100], [48918, 100], [49217, 101], [49517, 101], [49816, 102], [50116, 101], [50415, 102], [50715, 102], [51014, 103], [51314, 103], [51613, 104], [51913, 104], [52212, 105], [52512, 104], [52811, 105], [53111, 105], [53410, 106], [53710, 106], [54010, 106], [54309, 107], [54609, 107], [54908, 108], [55208, 107], [55507, 108], [55807, 108], [56106, 109], [56406, 109], [56705, 110], [57005, 110], [57304, 111], [57604, 111], [57903, 111], [58203, 111], [58500, 114], [58800, 114], [59100, 114], [59400, 114], [59700, 114], [60000, 114], [60300, 113], [60600, 113], [60900, 113], [61200, 113], [61500, 113], [61800, 113], [62101, 112], [62402, 111], [62703, 110], [63003, 110], [63304, 109], [63605, 108], [63906, 107], [64206, 107], [64507, 107], [64808, 106], [65109, 105], [65409, 105], [65710, 104], [66011, 104], [66311, 104], [66612, 103], [66913, 102], [67214, 101], [67514, 101], [67815, 100]], "point": [58, 182]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-02.25|+00.32|+00.28"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 139, 117, 227], "mask": [[41430, 85], [41729, 86], [42029, 86], [42328, 87], [42628, 87], [42927, 88], [43227, 88], [43526, 90], [43826, 90], [44125, 91], [44425, 91], [44725, 91], [45024, 92], [45324, 92], [45623, 93], [45923, 94], [46222, 95], [46522, 95], [46821, 96], [47121, 96], [47420, 97], [47720, 97], [48019, 98], [48319, 99], [48618, 100], [48918, 100], [49217, 101], [49517, 101], [49816, 102], [50116, 101], [50415, 102], [50715, 102], [51014, 103], [51314, 103], [51613, 104], [51913, 104], [52212, 105], [52512, 104], [52811, 105], [53111, 105], [53410, 106], [53710, 106], [54010, 106], [54309, 107], [54609, 107], [54908, 108], [55208, 107], [55507, 108], [55807, 108], [56106, 109], [56406, 109], [56705, 110], [57005, 28], [57034, 81], [57304, 27], [57335, 80], [57604, 26], [57636, 79], [57903, 26], [57936, 78], [58203, 27], [58237, 77], [58500, 114], [58800, 114], [59100, 114], [59400, 114], [59700, 114], [60000, 114], [60300, 113], [60600, 113], [60900, 113], [61200, 113], [61500, 113], [61800, 113], [62101, 112], [62402, 111], [62703, 110], [63003, 110], [63304, 109], [63605, 108], [63906, 107], [64206, 107], [64507, 107], [64808, 106], [65109, 105], [65409, 105], [65710, 104], [66011, 104], [66311, 104], [66612, 103], [66913, 102], [67214, 101], [67514, 101], [67815, 100]], "point": [58, 182]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "ToiletPaper|-03.02|+00.95|+03.14"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [66, 73, 96, 112], "mask": [[21676, 12], [21973, 17], [22270, 22], [22569, 24], [22868, 25], [23167, 26], [23466, 27], [23766, 27], [24066, 27], [24366, 28], [24666, 28], [24967, 27], [25267, 27], [25567, 27], [25867, 27], [26168, 27], [26468, 27], [26768, 27], [27068, 27], [27369, 26], [27669, 26], [27969, 27], [28269, 27], [28570, 26], [28870, 26], [29170, 26], [29470, 26], [29771, 26], [30071, 26], [30371, 26], [30671, 26], [30972, 25], [31272, 25], [31572, 25], [31872, 24], [32173, 22], [32473, 21], [32775, 18], [33076, 14], [33381, 6]], "point": [81, 91]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-02.25|+00.32|+00.28"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [25, 139, 120, 169], "mask": [[41425, 93], [41725, 93], [42026, 92], [42326, 92], [42627, 91], [42927, 91], [43227, 91], [43528, 91], [43828, 91], [44129, 90], [44429, 90], [44730, 89], [45030, 89], [45331, 88], [45631, 88], [45932, 88], [46232, 88], [46533, 87], [46833, 87], [47133, 87], [47434, 86], [47734, 86], [48035, 85], [48335, 85], [48636, 85], [48936, 85], [49237, 84], [49537, 84], [49838, 83], [50138, 83], [50439, 82]], "point": [72, 153]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "ToiletPaper|-03.02|+00.95|+03.14", "placeStationary": true, "receptacleObjectId": "Drawer|-02.25|+00.32|+00.28"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 139, 117, 227], "mask": [[41430, 85], [41729, 86], [42029, 86], [42328, 87], [42628, 87], [42927, 88], [43227, 88], [43526, 90], [43826, 90], [44125, 91], [44425, 91], [44725, 91], [45024, 92], [45324, 92], [45623, 93], [45923, 94], [46222, 95], [46522, 95], [46821, 96], [47121, 96], [47420, 97], [47720, 97], [48019, 98], [48319, 99], [48618, 100], [48918, 100], [49217, 101], [49517, 101], [49816, 102], [50116, 101], [50415, 102], [50715, 102], [51014, 103], [51314, 103], [51613, 104], [51913, 104], [52212, 105], [52512, 104], [52811, 105], [53111, 105], [53410, 106], [53710, 106], [54010, 106], [54309, 107], [54609, 107], [54908, 108], [55208, 107], [55507, 108], [55807, 108], [56106, 109], [56406, 109], [56705, 110], [57005, 28], [57034, 81], [57304, 27], [57335, 80], [57604, 26], [57636, 79], [57903, 26], [57936, 78], [58203, 27], [58237, 77], [58500, 114], [58800, 114], [59100, 114], [59400, 112], [59700, 111], [60000, 110], [60300, 110], [60600, 109], [60900, 109], [61200, 109], [61500, 109], [61800, 109], [62101, 107], [62402, 106], [62703, 105], [63003, 105], [63304, 103], [63605, 102], [63906, 101], [64206, 101], [64507, 99], [64808, 98], [65109, 97], [65409, 97], [65710, 95], [66011, 94], [66311, 94], [66612, 93], [66913, 91], [67214, 90], [67514, 90], [67815, 89]], "point": [58, 182]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-02.25|+00.32|+00.28"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 139, 117, 227], "mask": [[41430, 85], [41729, 86], [42029, 86], [42328, 87], [42628, 87], [42927, 88], [43227, 88], [43526, 90], [43826, 90], [44125, 91], [44425, 91], [44725, 91], [45024, 92], [45324, 92], [45623, 93], [45923, 94], [46222, 95], [46522, 95], [46821, 96], [47121, 96], [47420, 97], [47720, 97], [48019, 98], [48319, 99], [48618, 100], [48918, 100], [49217, 101], [49517, 101], [49816, 102], [50116, 101], [50415, 102], [50715, 102], [51014, 103], [51314, 103], [51613, 23], [51644, 73], [51913, 22], [51946, 71], [52212, 21], [52246, 71], [52512, 20], [52547, 69], [52811, 20], [52847, 69], [53111, 20], [53147, 69], [53410, 20], [53448, 68], [53710, 19], [53748, 68], [54010, 20], [54049, 67], [54309, 21], [54349, 67], [54609, 21], [54650, 66], [54908, 23], [54950, 66], [55208, 23], [55251, 64], [55507, 25], [55551, 64], [55807, 25], [55851, 64], [56106, 27], [56152, 63], [56406, 28], [56452, 63], [56705, 29], [56751, 64], [57005, 28], [57034, 1], [57051, 64], [57304, 27], [57350, 65], [57604, 26], [57650, 65], [57903, 26], [57948, 66], [58203, 27], [58247, 67], [58500, 114], [58800, 114], [59100, 114], [59400, 114], [59700, 114], [60000, 114], [60300, 113], [60600, 113], [60900, 113], [61200, 113], [61500, 113], [61800, 113], [62101, 112], [62402, 111], [62703, 110], [63003, 110], [63304, 109], [63605, 108], [63906, 107], [64206, 107], [64507, 107], [64808, 106], [65109, 105], [65409, 105], [65710, 104], [66011, 104], [66311, 104], [66612, 103], [66913, 102], [67214, 101], [67514, 101], [67815, 100]], "point": [58, 182]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan423", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.25, "y": 0.9019853, "z": 2.5}, "object_poses": [{"objectName": "Candle_f9e80138", "position": {"x": -3.294139, "y": 0.54400754, "z": 0.85615474}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Candle_f9e80138", "position": {"x": -0.0914080441, "y": 0.812504768, "z": 1.5396874}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_01c27901", "position": {"x": -2.89249659, "y": 0.954541743, "z": 3.16648364}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBottle_01c27901", "position": {"x": -2.11709952, "y": 0.8135558, "z": 0.404999971}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_10d8a609", "position": {"x": -0.0343, "y": 1.53700006, "z": 2.30900025}, "rotation": {"x": 0.0, "y": 90.0003, "z": 0.0}}, {"objectName": "ToiletPaper_2c74e5b0", "position": {"x": -1.81649792, "y": 0.0148216533, "z": 0.165999979}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "HandTowel_10d8a609", "position": {"x": -3.1727, "y": 1.487, "z": 0.340999782}, "rotation": {"x": 0.0, "y": 270.000244, "z": 0.0}}, {"objectName": "Towel_1c670900", "position": {"x": -1.513, "y": 1.496, "z": 0.109}, "rotation": {"x": 0.0, "y": 1.70754709e-06, "z": 0.0}}, {"objectName": "Cloth_3812449a", "position": {"x": -0.3484639, "y": 0.811423659, "z": 2.61059165}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBar_57e826c8", "position": {"x": -1.997024, "y": 0.8130697, "z": 0.0925}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_2c74e5b0", "position": {"x": -3.01749635, "y": 0.951338053, "z": 3.139693}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Plunger_a91f9bf0", "position": {"x": -2.53099918, "y": 0.000522919, "z": 3.026999}, "rotation": {"x": -0.00118147617, "y": 0.000440507982, "z": 0.0007804241}}, {"objectName": "Candle_f9e80138", "position": {"x": -2.64249659, "y": 0.9539935, "z": 3.246858}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "SoapBottle_01c27901", "position": {"x": -0.2555344, "y": 0.2194396, "z": 1.62644863}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ToiletPaper_0148fbc8", "position": {"x": -0.348464, "y": 0.8094972, "z": 2.74063182}, "rotation": {"x": 1.6284477e-12, "y": 7.84645738e-26, "z": 5.521441e-12}}, {"objectName": "SprayBottle_ce702ff7", "position": {"x": -2.642496, "y": 0.955735147, "z": 3.1664834}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "ScrubBrush_a6463e50", "position": {"x": -2.33284664, "y": 0.0009859651, "z": 3.16339278}, "rotation": {"x": 5.079124e-05, "y": -9.908382e-05, "z": 0.0001796343}}], "object_toggles": [], "random_seed": 1237744861, "scene_num": 423}, "task_id": "trial_T20190907_111226_698552", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A3F7G1FSFWQPLE_3HMVI3QICM9MNN9FSWLWPOAJVS5Y1G", "high_descs": ["Turn around and take a step towards the door, then turn right and walk up to the counter.", "Pick up the cardboard toilet paper tube off of the counter.", "Turn right and begin walking to the shower, then turn right again and make your way over to the small counter near the tub.", "Open the bottom left drawer of the small counter and place the cardboard toilet paper tube inside, then close the drawer.", "Turn around and walk over to the toilet.", "Pick up the roll of toilet paper off of the toilet tank.", "Turn around and walk back up to the small counter.", "Ope the bottom left drawer of the small counter and place the toilet paper roll inside."], "task_desc": "Move two toilet paper to a drawer.", "votes": [1, 1, 1, 1, 1, 1, 1, 1]}, {"assignment_id": "A6U2C66WQ7QQN_3DEL4X4EL92UDOU8ZP63KXEK0E1XY2", "high_descs": ["Turn to the left, face the counter", "Pick up the empty toilet paper tube from the counter", "Turn to the right, walk across the bathroom to the other sink", "Open the drawer under the sink, put empty toilet paper tube into drawer", "Close the drawer under the sink, turn around", "Facing the toilet, pick up the roll of toilet paper", "Turn around to face the sink, open the drawer", "Put the roll of toilet paper in to the drawer, close the drawer"], "task_desc": "Put the toilet paper rolls into the drawer under the sink", "votes": [1, 1, 1, 1, 1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3E337GFOLCPPF3V43DXOFVGIT4VGNX", "high_descs": ["turn left and walk to the left side of the kitchen sink counter ahead", "grab the toilet paper holder off of the counter", "turn right and walk over to the other kitchen sink on the other side of the room to the right", "place the toilet paper holder in a pullout drawer on the bottom left part of the sink cabinet", "turn around and walk forwards to the toilet ahead", "grab a toilet paper roll off of the toilet", "turn around and walk to the sink cabinet ahead", "place the toilet paper roll in the cabinet bottom left of the sink"], "task_desc": "place a toilet paper roll and a toilet paper holder thing in the cabinet of the bathroom sink", "votes": [1, 1, 1, 1, 1, 1]}, {"assignment_id": "A1AKL5YH9NLD2V_3ZPBJO59KSIUTNZ2YP134QGPU41DHE", "high_descs": ["Turn left and walk to the tube next to the green rag on the counter.", "Pick up the tube from the counter.", "Turn right and walk to the sink next to the bathtub.", "Place the tube in the left drawer below the sink then close the drawer.", "Turn around and walk to the toilet.", "Pick up the toilet paper from the back of the toilet.", "Turn around and walk to the sink to the left of the bathtub.", "Place the toilet paper behind the tube in the left drawer below the sink and then close the drawer."], "task_desc": "Place a tube and a toilet paper roll in the left drawer below the sink next to the bathtub.", "votes": [1, 1]}, {"assignment_id": "A2JBNDG0U9IA6I_3H7XDTSHKF8F2X4IVEOBBET9MMVGWW", "high_descs": ["Turn around and walk to the bathroom counter left of the sink and face it", "Pick up the empty toilet paper roll from the counter", "Turn around walk to the small sink next to the tub", "Open the left bottom drawer and place the roll inside then close it", "Turn around and walk up to the toilet and face it", "Pick up the full toilet paper roll from the toilet lid", "Turn around and walk to the sink and face it", "Open the bottom left drawer and place the roll inside then close it"], "task_desc": "Putting away toilet paper rolls in the drawer", "votes": [1, 1]}]}}