{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000099.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000100.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000111.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000112.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000113.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000114.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000115.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000116.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000117.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000118.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000119.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000171.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000172.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000173.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000174.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000175.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000176.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000216.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000217.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000218.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000219.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000220.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000221.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000222.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000223.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000224.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000225.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000226.png", "low_idx": 43}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000239.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000240.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000241.png", "low_idx": 44}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "GarbageCan", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sinkbasin"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|2|-4|2|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [1.508504628, 1.508504628, -6.9822588, -6.9822588, 3.2462508, 3.2462508]], "coordinateReceptacleObjectId": ["SinkBasin", [1.5085048, 1.5085048, -6.62355136, -6.62355136, 3.0634928, 3.0634928]], "forceVisible": true, "objectId": "Egg|+00.38|+00.81|-01.75"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-4|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}}, {"discrete_action": {"action": "GotoLocation", "args": ["garbagecan"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-3|2|1|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "garbagecan"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [1.508504628, 1.508504628, -6.9822588, -6.9822588, 3.2462508, 3.2462508]], "coordinateReceptacleObjectId": ["GarbageCan", [-1.448, -1.448, 2.156, 2.156, 0.0, 0.0]], "forceVisible": true, "objectId": "Egg|+00.38|+00.81|-01.75", "receptacleObjectId": "GarbageCan|-00.36|+00.00|+00.54"}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+00.38|+00.81|-01.75"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [172, 146, 187, 162], "mask": [[43677, 6], [43975, 10], [44275, 11], [44574, 12], [44873, 14], [45173, 14], [45472, 15], [45772, 16], [46072, 16], [46372, 16], [46672, 15], [46973, 14], [47273, 14], [47574, 12], [47874, 11], [48176, 8], [48478, 4]], "point": [179, 153]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+00.38|+00.81|-01.75", "placeStationary": true, "receptacleObjectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 76], [25628, 164], [25822, 77], [25928, 164], [26122, 77], [26228, 163], [26421, 78], [26528, 163], [26720, 79], [26828, 162], [27020, 79], [27128, 162], [27319, 81], [27428, 162], [27619, 81], [27728, 161], [27918, 82], [28028, 161], [28217, 83], [28328, 160], [28517, 84], [28628, 160], [28816, 85], [28927, 160], [29115, 87], [29227, 160], [29415, 87], [29527, 161], [29714, 89], [29826, 162], [30013, 90], [30126, 162], [30313, 91], [30426, 162], [30612, 93], [30725, 163], [30911, 94], [31025, 163], [31211, 95], [31324, 164], [31510, 98], [31622, 166], [31809, 101], [31920, 168], [32109, 104], [32218, 170], [32408, 280], [32708, 280], [33007, 280], [33306, 281], [33606, 281], [33905, 281], [34204, 282], [34504, 282], [34803, 282], [35102, 283], [35402, 282], [35701, 283], [36000, 283], [36300, 283], [36600, 282], [36900, 282], [37200, 281], [37500, 281], [37800, 280], [38100, 280], [38400, 280], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 76], [25628, 164], [25822, 77], [25928, 164], [26122, 77], [26228, 163], [26421, 78], [26528, 163], [26720, 79], [26828, 162], [27020, 79], [27128, 162], [27319, 81], [27428, 162], [27619, 81], [27728, 161], [27918, 82], [28028, 161], [28217, 83], [28328, 160], [28517, 84], [28628, 160], [28816, 85], [28927, 160], [29115, 87], [29227, 160], [29415, 87], [29527, 161], [29714, 89], [29826, 162], [30013, 90], [30126, 162], [30313, 91], [30426, 162], [30612, 93], [30725, 163], [30911, 94], [31025, 163], [31211, 95], [31324, 164], [31510, 98], [31622, 166], [31809, 101], [31920, 168], [32109, 104], [32218, 21], [32246, 142], [32408, 130], [32547, 141], [32708, 128], [32848, 140], [33007, 129], [33149, 138], [33306, 129], [33450, 137], [33606, 128], [33751, 136], [33905, 129], [34051, 135], [34204, 129], [34352, 134], [34504, 129], [34652, 134], [34803, 130], [34952, 133], [35102, 131], [35252, 133], [35402, 131], [35552, 132], [35701, 132], [35852, 132], [36000, 133], [36152, 131], [36300, 133], [36452, 131], [36600, 133], [36752, 130], [36900, 134], [37051, 131], [37200, 134], [37351, 130], [37500, 135], [37650, 131], [37800, 136], [37949, 131], [38100, 137], [38248, 132], [38400, 139], [38546, 134], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-4.152, -4.152, -6.892, -6.892, 3.6, 3.6]], "forceVisible": true, "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [31, 29, 298, 177], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21933, 264], [22233, 264], [22533, 264], [22834, 262], [23134, 262], [23435, 260], [23735, 260], [24035, 259], [24336, 258], [24636, 258], [24936, 257], [25237, 256], [25537, 255], [25837, 255], [26138, 253], [26438, 253], [26738, 252], [27039, 251], [27339, 251], [27639, 250], [27940, 249], [28240, 248], [28540, 248], [28841, 246], [29141, 246], [29441, 247], [29742, 246], [30042, 246], [30343, 245], [30642, 246], [30942, 246], [31242, 246], [31542, 246], [31842, 246], [32142, 246], [32443, 245], [32743, 245], [33043, 244], [33343, 244], [33643, 244], [33944, 242], [34244, 242], [34544, 242], [34845, 240], [35145, 240], [35445, 239], [35746, 238], [36046, 237], [36346, 237], [36647, 235], [36947, 235], [37247, 234], [37548, 233], [37848, 232], [38148, 232], [38449, 231], [38749, 230], [39050, 229], [39350, 228], [39650, 228], [39951, 226], [40251, 226], [40551, 225], [40852, 224], [41152, 223], [41452, 223], [41753, 221], [42053, 221], [42354, 220], [42654, 219], [42954, 219], [43255, 217], [43555, 217], [43855, 216], [44156, 215], [44456, 214], [44756, 214], [45057, 212], [45357, 212], [45657, 211], [45958, 210], [46258, 210], [46559, 208], [46859, 208], [47159, 207], [47460, 206], [47760, 205], [48060, 205], [48361, 203], [48661, 203], [48962, 201], [49263, 191], [49569, 159], [50012, 16], [50312, 15], [50612, 15], [50912, 15], [51211, 15], [51511, 15], [51811, 15], [52111, 14], [52411, 14], [52712, 13], [53013, 11]], "point": [164, 102]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|+00.38|+00.81|-01.75"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [133, 108, 151, 129], "mask": [[32239, 7], [32538, 9], [32836, 12], [33136, 13], [33435, 15], [33734, 17], [34034, 17], [34333, 19], [34633, 19], [34933, 19], [35233, 19], [35533, 19], [35833, 19], [36133, 19], [36433, 19], [36733, 19], [37034, 17], [37334, 17], [37635, 15], [37936, 13], [38237, 11], [38539, 7]], "point": [142, 117]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.04|+00.90|-01.72"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 29, 298, 300], "mask": [[8654, 12], [8786, 165], [8953, 14], [9086, 182], [9385, 184], [9685, 185], [9984, 187], [10284, 187], [10583, 189], [10883, 190], [11183, 191], [11482, 193], [11782, 194], [12081, 195], [12381, 196], [12680, 198], [12980, 199], [13279, 201], [13546, 235], [13845, 237], [14145, 237], [14444, 239], [14743, 241], [15043, 242], [15342, 244], [15641, 246], [15941, 246], [16240, 248], [16539, 250], [16839, 251], [17138, 253], [17437, 255], [17737, 255], [18036, 257], [18335, 259], [18635, 260], [18934, 262], [19233, 264], [19533, 264], [19832, 266], [20131, 268], [20431, 268], [20731, 268], [21032, 267], [21332, 266], [21632, 266], [21931, 266], [22230, 267], [22530, 267], [22829, 267], [23128, 268], [23428, 267], [23727, 268], [24026, 268], [24326, 268], [24625, 269], [24924, 269], [25224, 269], [25523, 76], [25628, 164], [25822, 77], [25928, 164], [26122, 77], [26228, 163], [26421, 78], [26528, 163], [26720, 79], [26828, 162], [27020, 79], [27128, 162], [27319, 81], [27428, 162], [27619, 81], [27728, 161], [27918, 82], [28028, 161], [28217, 83], [28328, 160], [28517, 84], [28628, 160], [28816, 85], [28927, 160], [29115, 87], [29227, 160], [29415, 87], [29527, 161], [29714, 89], [29826, 162], [30013, 90], [30126, 162], [30313, 91], [30426, 162], [30612, 93], [30725, 163], [30911, 94], [31025, 163], [31211, 95], [31324, 164], [31510, 98], [31622, 166], [31809, 101], [31920, 168], [32109, 104], [32218, 170], [32408, 280], [32708, 280], [33007, 280], [33306, 281], [33606, 281], [33905, 281], [34204, 282], [34504, 282], [34803, 282], [35102, 283], [35402, 282], [35701, 283], [36000, 283], [36300, 283], [36600, 282], [36900, 282], [37200, 281], [37500, 281], [37800, 280], [38100, 280], [38400, 280], [38700, 279], [39000, 279], [39300, 278], [39600, 278], [39900, 277], [40200, 277], [40500, 276], [40800, 276], [41100, 275], [41400, 275], [41700, 274], [42000, 274], [42300, 274], [42600, 273], [42900, 273], [43200, 272], [43500, 272], [43800, 271], [44100, 271], [44400, 270], [44700, 270], [45000, 269], [45300, 269], [45600, 268], [45900, 268], [46200, 268], [46500, 267], [46800, 267], [47100, 74], [47316, 50], [47400, 73], [47617, 49], [47700, 73], [47917, 48], [48000, 73], [48217, 48], [48300, 73], [48517, 47], [48600, 72], [48818, 46], [48900, 72], [49118, 45], [49200, 72], [49418, 36], [49500, 71], [49800, 71], [50100, 71], [50400, 71], [50700, 70], [51000, 70], [51300, 70], [51600, 69], [51900, 69], [52200, 69], [52500, 69], [52800, 68], [53100, 68], [53400, 68], [53700, 67], [54000, 67], [54300, 67], [54600, 67], [54900, 66], [55200, 66], [55500, 66], [55800, 65], [56100, 65], [56400, 65], [56700, 65], [57000, 64], [57300, 64], [57600, 64], [57900, 63], [58200, 63], [58500, 63], [58800, 63], [59100, 62], [59400, 62], [59700, 62], [60000, 61], [60300, 61], [60600, 61], [60900, 61], [61200, 60], [61500, 60], [61800, 60], [62100, 59], [62400, 59], [62700, 59], [63000, 59], [63300, 58], [63600, 58], [63900, 58], [64200, 57], [64500, 57], [64800, 57], [65100, 57], [65400, 56], [65700, 56], [66000, 56], [66300, 55], [66600, 55], [66900, 55], [67200, 55], [67500, 54], [67800, 54], [68100, 54], [68400, 53], [68700, 53], [69000, 53], [69300, 53], [69600, 52], [69900, 52], [70200, 52], [70500, 51], [70800, 51], [71100, 51], [71400, 51], [71700, 50], [72000, 50], [72300, 50], [72600, 49], [72900, 49], [73200, 49], [73500, 49], [73800, 48], [74100, 48], [74400, 48], [74700, 47], [75000, 47], [75300, 47], [75600, 47], [75900, 46], [76200, 46], [76500, 46], [76800, 45], [77100, 45], [77400, 45], [77700, 45], [78000, 44], [78300, 44], [78600, 44], [78900, 43], [79200, 43], [79500, 43], [79800, 43], [80100, 42], [80400, 42], [80700, 42], [81000, 41], [81300, 41], [81600, 41], [81900, 41], [82200, 40], [82500, 40], [82800, 40], [83100, 39], [83400, 39], [83700, 39], [84000, 39], [84300, 38], [84600, 38], [84900, 38], [85200, 37], [85500, 37], [85800, 37], [86100, 37], [86400, 36], [86700, 36], [87000, 36], [87300, 35], [87600, 35], [87900, 35], [88200, 35], [88500, 34], [88800, 34], [89100, 34], [89400, 33], [89700, 33]], "point": [149, 156]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|+00.38|+00.81|-01.75", "placeStationary": true, "receptacleObjectId": "GarbageCan|-00.36|+00.00|+00.54"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [76, 153, 202, 244], "mask": [[45695, 92], [45991, 100], [46289, 104], [46588, 106], [46887, 108], [47187, 108], [47486, 110], [47785, 111], [48085, 112], [48384, 113], [48684, 113], [48984, 114], [49284, 114], [49584, 114], [49884, 114], [50183, 115], [50483, 115], [50783, 115], [51083, 115], [51383, 115], [51683, 115], [51983, 116], [52282, 117], [52582, 117], [52882, 117], [53182, 117], [53482, 117], [53782, 117], [54082, 117], [54381, 118], [54681, 118], [54981, 119], [55281, 119], [55581, 119], [55881, 119], [56181, 119], [56480, 120], [56780, 120], [57080, 120], [57380, 120], [57680, 120], [57980, 121], [58280, 121], [58579, 122], [58879, 122], [59179, 122], [59479, 122], [59779, 122], [60079, 47], [60131, 70], [60379, 45], [60432, 69], [60678, 45], [60733, 69], [60978, 45], [61034, 68], [61278, 44], [61334, 68], [61578, 44], [61634, 68], [61878, 43], [61934, 68], [62178, 43], [62234, 68], [62478, 43], [62534, 68], [62777, 44], [62835, 67], [63077, 43], [63135, 67], [63377, 43], [63435, 67], [63677, 43], [63735, 68], [63977, 43], [64035, 68], [64277, 44], [64335, 68], [64577, 44], [64636, 67], [64876, 45], [64936, 67], [65176, 46], [65236, 67], [65476, 46], [65525, 1], [65535, 68], [65776, 46], [65825, 1], [65835, 68], [66077, 46], [66124, 3], [66134, 68], [66377, 46], [66424, 5], [66432, 70], [66677, 125], [66978, 123], [67278, 123], [67578, 123], [67879, 121], [68179, 121], [68481, 118], [68783, 114], [69085, 110], [69387, 107], [69687, 107], [69988, 105], [70291, 100], [70593, 96], [70895, 51], [70953, 34], [71198, 45], [71257, 28], [71500, 41], [71559, 24], [71808, 31], [71861, 15], [72111, 27], [72162, 12], [72413, 23], [72464, 9], [72714, 21], [72765, 7], [73017, 17], [73066, 3]], "point": [139, 197]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan11", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": -2.25, "y": 0.9009992, "z": -0.25}, "object_poses": [{"objectName": "DishSponge_932ef12a", "position": {"x": -2.61406946, "y": 0.95178926, "z": 0.614945531}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": 0.9085692, "y": 0.934549868, "z": -1.64917481}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": -1.92895365, "y": 0.9672249, "z": 0.165200144}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": 1.84613431, "y": 0.9131588, "z": 0.3752191}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_b9b64351", "position": {"x": 0.167712927, "y": 0.9376496, "z": 0.3752191}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_b9b64351", "position": {"x": -2.340023, "y": 0.977574646, "z": 0.345098317}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": 0.701403737, "y": 0.918246448, "z": -1.582726}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": -2.61406946, "y": 1.00127089, "z": 0.435047448}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": -0.198482066, "y": 0.110317647, "z": -1.54564261}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_42ce5824", "position": {"x": -2.61406946, "y": 0.9485312, "z": 0.165200263}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": 1.60195827, "y": 0.9086062, "z": 0.4581}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": 1.75472069, "y": 0.104848027, "z": 0.3859118}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": -2.22249985, "y": 1.13467455, "z": -1.76346016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": -2.203, "y": 1.03509808, "z": 0.255149245}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": -1.79193056, "y": 1.04246557, "z": 0.2551492}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": -1.95999992, "y": 0.8710422, "z": -1.76346016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": -1.95999992, "y": 1.04680157, "z": -1.67773771}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": -2.06597686, "y": 0.947225034, "z": 0.255149215}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bread_59b513fb", "position": {"x": 1.30000424, "y": 0.97873944, "z": -1.53218317}, "rotation": {"x": 359.992676, "y": 0.05414445, "z": 0.0134352716}}, {"objectName": "Knife_b9b64351", "position": {"x": -2.47704625, "y": 0.977574646, "z": 0.255149275}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_834895c5", "position": {"x": 0.7531951, "y": 0.920098543, "z": -1.44982815}, "rotation": {"x": 0.0, "y": 6.83018925e-06, "z": 0.0}}, {"objectName": "Bowl_c96a3c07", "position": {"x": -1.79193056, "y": 0.9516571, "z": 0.52499634}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Kettle_7922e01e", "position": {"x": 1.361, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Cup_923adba4", "position": {"x": 0.0353682637, "y": 0.9107361, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_929aede3", "position": {"x": 1.658355, "y": 0.961345851, "z": -1.73768091}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_09398926", "position": {"x": 0.3000576, "y": 0.910996437, "z": 0.209457338}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_c3590fed", "position": {"x": 0.39424172, "y": 0.109544873, "z": -1.50970006}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Pot_da74902f", "position": {"x": 1.0071, "y": 0.962147355, "z": 0.5617}, "rotation": {"x": 0.0, "y": 269.9999, "z": 0.0}}, {"objectName": "Spatula_b4e2a06a", "position": {"x": 0.6496123, "y": 0.934549868, "z": -1.78207254}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "PaperTowelRoll_54f2d061", "position": {"x": -0.32939738, "y": 0.174490839, "z": 0.6126471}, "rotation": {"x": 0.0, "y": 89.99995, "z": 0.0}}, {"objectName": "Egg_7c2e9408", "position": {"x": 0.377126157, "y": 0.8115627, "z": -1.7455647}, "rotation": {"x": 1.40334191e-14, "y": 270.0, "z": 0.0}}, {"objectName": "Lettuce_22e053cf", "position": {"x": -1.95999992, "y": 0.6015422, "z": -1.76346016}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_42ce5824", "position": {"x": 1.60195827, "y": 0.9086062, "z": 0.706742644}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_213d034a", "position": {"x": 1.84613431, "y": 0.9086062, "z": 0.62386173}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_41d4caea", "position": {"x": -0.8720186, "y": 1.082603, "z": -1.80393767}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_d4975865", "position": {"x": 1.412741, "y": 0.90133214, "z": -1.40615845}, "rotation": {"x": 359.993927, "y": -1.87384671e-06, "z": -0.000153844827}}, {"objectName": "Potato_483d2614", "position": {"x": 0.753195047, "y": 0.9493862, "z": -1.71562362}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_4b842b85", "position": {"x": 1.022105, "y": 0.9591456, "z": 0.288545281}, "rotation": {"x": 0.0, "y": 219.19072, "z": 0.0}}, {"objectName": "DishSponge_932ef12a", "position": {"x": 1.82812929, "y": 1.32829893, "z": -1.84639466}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spoon_9fcd93ca", "position": {"x": -2.203, "y": 0.9533544, "z": 0.4350474}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Mug_841024df", "position": {"x": -2.31, "y": 0.775801659, "z": -1.84918261}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 3705659827, "scene_num": 11}, "task_id": "trial_T20190908_215937_642226", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2KAGFQU28JY43_320DUZ38GAD1H2QHKNUDNAJ3ELSJGX", "high_descs": ["Go to the sink ahead to your right. ", "Pull the egg in the back of the sink out. ", "Turn to your right and go to the microwave on your left, just before the refrigerator. ", "Place the egg in the microwave, heat it up, and remove the egg from the microwave. ", "Turn around and go to the green recycle bin, on the floor, across from you.", "Place the egg in the recycle bin. "], "task_desc": "Put a warm egg in the recycle bin. ", "votes": [1, 1]}, {"assignment_id": "A3F7G1FSFWQPLE_37TD41K0AK0HZTT25V78PKPBCDMCS7", "high_descs": ["<PERSON><PERSON> walking across the room, then hang a right and walk up to the sink.", "Pick up the egg out of the sink.", "Turn right and walk over to the microwave on the counter.", "Heat up the egg in the microwave, then take it out and close the microwave.", "Turn around and walk to the wall, then turn right and walk up to the green bin next to the counter.", "Put the heated egg in the green bin."], "task_desc": "Put a heated egg in a green bin.", "votes": [1, 1]}, {"assignment_id": "A255A9FFZD8PQW_3LJ7UR74RK4HOCOI4KK47S3HX0WN4Q", "high_descs": ["Go forward and turn to the right and face the sink.", "Pick up the egg from the sink.", "Turn right again and go forward to the microwave. Turn left to face the microwave.", "Place the egg in the microwave and heat it. Then take the egg out. ", "Turn around completely and go forward then turn to the right to face the green bin.", "Place the egg inside the green bin."], "task_desc": "Heat an egg and place it in a green bin.", "votes": [1, 1]}]}}