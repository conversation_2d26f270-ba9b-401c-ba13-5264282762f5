{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000091.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000092.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000093.png", "low_idx": 19}, {"high_idx": 0, "image_name": "000000094.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000095.png", "low_idx": 20}, {"high_idx": 0, "image_name": "000000096.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000097.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000098.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000099.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000100.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000101.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000102.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000103.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000104.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000105.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000106.png", "low_idx": 21}, {"high_idx": 0, "image_name": "000000107.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000108.png", "low_idx": 22}, {"high_idx": 0, "image_name": "000000109.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000110.png", "low_idx": 23}, {"high_idx": 0, "image_name": "000000111.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000112.png", "low_idx": 24}, {"high_idx": 0, "image_name": "000000113.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000114.png", "low_idx": 25}, {"high_idx": 0, "image_name": "000000115.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000116.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000117.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000118.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000121.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000122.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000123.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000124.png", "low_idx": 26}, {"high_idx": 0, "image_name": "000000125.png", "low_idx": 26}, {"high_idx": 1, "image_name": "000000126.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000127.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000128.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000129.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000132.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000133.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000134.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000135.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000136.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000137.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000138.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000139.png", "low_idx": 27}, {"high_idx": 1, "image_name": "000000140.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 34}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 46}, {"high_idx": 5, "image_name": "000000260.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000261.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000262.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000263.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000264.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000265.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000266.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000267.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000268.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000269.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000270.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000271.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000272.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000273.png", "low_idx": 47}, {"high_idx": 5, "image_name": "000000274.png", "low_idx": 47}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "CounterTop", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|-3|2|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [0.24434876, 0.24434876, -5.05504084, -5.05504084, 3.6379388, 3.6379388]], "coordinateReceptacleObjectId": ["CounterTop", [3.956, 3.956, -6.064, -6.064, 3.7868, 3.7868]], "forceVisible": true, "objectId": "Cup|+00.06|+00.91|-01.26"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|4|-3|1|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}}, {"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|4|1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "countertop"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [0.24434876, 0.24434876, -5.05504084, -5.05504084, 3.6379388, 3.6379388]], "coordinateReceptacleObjectId": ["CounterTop", [3.956, 3.956, 4.068, 4.068, 3.7844, 3.7844]], "forceVisible": true, "objectId": "Cup|+00.06|+00.91|-01.26", "receptacleObjectId": "CounterTop|+00.99|+00.95|+01.02"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.06|+00.91|-01.26"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [104, 65, 149, 126], "mask": [[19328, 1], [19621, 15], [19918, 21], [20216, 25], [20514, 29], [20812, 32], [21111, 34], [21410, 36], [21709, 38], [22008, 40], [22307, 42], [22606, 43], [22906, 43], [23206, 44], [23505, 45], [23805, 45], [24104, 46], [24405, 45], [24705, 45], [25005, 45], [25305, 45], [25605, 45], [25905, 45], [26205, 45], [26506, 43], [26806, 43], [27106, 43], [27406, 42], [27707, 41], [28008, 39], [28309, 37], [28610, 36], [28910, 36], [29211, 35], [29511, 35], [29811, 35], [30112, 33], [30412, 33], [30713, 32], [31013, 32], [31313, 32], [31614, 31], [31914, 31], [32214, 31], [32515, 29], [32815, 29], [33115, 29], [33416, 28], [33716, 28], [34017, 27], [34317, 27], [34617, 27], [34918, 26], [35218, 26], [35518, 25], [35819, 24], [36119, 23], [36420, 21], [36721, 19], [37023, 15], [37324, 13], [37628, 5]], "point": [126, 94]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.06|+00.91|-01.26", "placeStationary": true, "receptacleObjectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 249], [24050, 250], [24349, 251], [24649, 251], [24948, 252], [25248, 252], [25547, 253], [25847, 253], [26146, 254], [26446, 254], [26745, 255], [27045, 255], [27344, 256], [27644, 256], [27943, 257], [28243, 257], [28542, 258], [28842, 258], [29141, 259], [29441, 258], [29740, 259], [30039, 259], [30339, 259], [30638, 259], [30938, 259], [31237, 259], [31537, 259], [31836, 259], [32136, 259], [32435, 259], [32735, 259], [33034, 259], [33334, 259], [33633, 259], [33933, 259], [34232, 260], [34532, 259], [34831, 260], [35131, 259], [35430, 260], [35730, 259], [36029, 260], [36328, 260], [36628, 260], [36927, 260], [37227, 260], [37526, 260], [37826, 260], [38125, 260], [38425, 260], [38724, 260], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 73], [23863, 137], [24050, 73], [24163, 137], [24349, 74], [24464, 136], [24649, 74], [24763, 137], [24948, 75], [25063, 137], [25248, 75], [25363, 137], [25547, 76], [25663, 137], [25847, 76], [25963, 137], [26146, 78], [26263, 137], [26446, 78], [26562, 138], [26745, 79], [26862, 138], [27045, 80], [27162, 138], [27344, 81], [27461, 139], [27644, 82], [27760, 140], [27943, 84], [28060, 140], [28243, 84], [28360, 140], [28542, 85], [28659, 141], [28842, 85], [28959, 141], [29141, 86], [29259, 141], [29441, 87], [29559, 140], [29740, 88], [29859, 140], [30039, 89], [30159, 139], [30339, 89], [30459, 139], [30638, 90], [30758, 139], [30938, 91], [31058, 139], [31237, 92], [31358, 138], [31537, 92], [31658, 138], [31836, 93], [31958, 137], [32136, 93], [32258, 137], [32435, 95], [32557, 137], [32735, 95], [32857, 137], [33034, 96], [33157, 136], [33334, 96], [33457, 136], [33633, 97], [33757, 135], [33933, 98], [34057, 135], [34232, 99], [34357, 135], [34532, 99], [34656, 135], [34831, 100], [34956, 135], [35131, 100], [35256, 134], [35430, 102], [35556, 134], [35730, 102], [35856, 133], [36029, 103], [36156, 133], [36328, 104], [36456, 132], [36628, 104], [36756, 132], [36927, 106], [37055, 132], [37227, 107], [37354, 133], [37526, 109], [37653, 133], [37826, 110], [37951, 135], [38125, 114], [38249, 136], [38425, 260], [38724, 260], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [7.008, 7.008, -3.376, -3.376, 3.600000144, 3.600000144]], "forceVisible": true, "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [55, 26, 299, 160], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20457, 243], [20757, 243], [21057, 243], [21358, 242], [21658, 242], [21958, 242], [22258, 242], [22559, 241], [22859, 241], [23159, 241], [23459, 241], [23760, 240], [24060, 240], [24360, 240], [24661, 239], [24961, 239], [25261, 239], [25561, 239], [25862, 238], [26162, 238], [26462, 238], [26762, 238], [27063, 237], [27363, 237], [27663, 237], [27964, 236], [28264, 236], [28564, 236], [28864, 236], [29165, 235], [29465, 234], [29765, 234], [30066, 232], [30366, 232], [30666, 231], [30966, 231], [31267, 229], [31567, 229], [31867, 228], [32167, 228], [32468, 226], [32768, 226], [33068, 225], [33369, 224], [33669, 223], [33969, 223], [34269, 223], [34570, 221], [34870, 221], [35170, 220], [35470, 220], [35771, 218], [36071, 218], [36371, 217], [36672, 216], [36972, 215], [37272, 215], [37572, 214], [37873, 213], [38173, 212], [38473, 212], [38773, 211], [39074, 210], [39374, 209], [39674, 209], [39975, 207], [40275, 207], [40575, 206], [40875, 206], [41176, 204], [41476, 204], [41776, 204], [42076, 203], [42377, 202], [42677, 201], [42977, 201], [43278, 199], [43578, 199], [43878, 198], [44178, 198], [44485, 189], [44786, 187], [45086, 187], [45386, 186], [45686, 186], [45987, 184], [46287, 184], [46587, 183], [46887, 183], [47188, 182], [47488, 181], [47789, 180]], "point": [177, 92]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+00.06|+00.91|-01.26"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [123, 80, 163, 128], "mask": [[23824, 39], [24123, 40], [24423, 41], [24723, 40], [25023, 40], [25323, 40], [25623, 40], [25923, 40], [26224, 39], [26524, 38], [26824, 38], [27125, 37], [27425, 36], [27726, 34], [28027, 33], [28327, 33], [28627, 32], [28927, 32], [29227, 32], [29528, 31], [29828, 31], [30128, 31], [30428, 31], [30728, 30], [31029, 29], [31329, 29], [31629, 29], [31929, 29], [32229, 29], [32530, 27], [32830, 27], [33130, 27], [33430, 27], [33730, 27], [34031, 26], [34331, 26], [34631, 25], [34931, 25], [35231, 25], [35532, 24], [35832, 24], [36132, 24], [36432, 24], [36732, 24], [37033, 22], [37334, 20], [37635, 18], [37936, 15], [38239, 10]], "point": [143, 103]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|+01.75|+00.90|-00.84"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 26, 299, 300], "mask": [[7576, 12], [7591, 145], [7767, 13], [7875, 190], [8066, 15], [8175, 207], [8474, 209], [8774, 210], [9073, 212], [9372, 214], [9672, 215], [9971, 217], [10271, 218], [10570, 220], [10870, 221], [11169, 223], [11469, 224], [11768, 226], [12068, 227], [12367, 229], [12666, 231], [12966, 231], [13265, 233], [13565, 234], [13864, 236], [14164, 236], [14463, 237], [14763, 237], [15062, 238], [15362, 238], [15661, 239], [15960, 240], [16260, 240], [16559, 241], [16859, 241], [17158, 242], [17458, 242], [17757, 243], [18057, 243], [18356, 244], [18655, 245], [18955, 245], [19256, 244], [19556, 244], [19856, 244], [20156, 244], [20456, 244], [20756, 244], [21055, 245], [21355, 245], [21654, 246], [21954, 246], [22253, 247], [22553, 247], [22852, 248], [23152, 248], [23451, 249], [23751, 249], [24050, 250], [24349, 251], [24649, 251], [24948, 252], [25248, 252], [25547, 253], [25847, 253], [26146, 254], [26446, 254], [26745, 255], [27045, 255], [27344, 256], [27644, 256], [27943, 257], [28243, 257], [28542, 258], [28842, 258], [29141, 259], [29441, 258], [29740, 259], [30039, 259], [30339, 259], [30638, 259], [30938, 259], [31237, 259], [31537, 259], [31836, 259], [32136, 259], [32435, 259], [32735, 259], [33034, 259], [33334, 259], [33633, 259], [33933, 259], [34232, 260], [34532, 259], [34831, 260], [35131, 259], [35430, 260], [35730, 259], [36029, 260], [36328, 260], [36628, 260], [36927, 260], [37227, 260], [37526, 260], [37826, 260], [38125, 260], [38425, 260], [38724, 260], [39024, 260], [39323, 260], [39623, 260], [39922, 260], [40222, 260], [40521, 260], [40821, 260], [41120, 260], [41420, 260], [41719, 261], [42018, 261], [42318, 261], [42617, 261], [42917, 261], [43216, 261], [43516, 261], [43815, 261], [44115, 261], [44414, 260], [44714, 259], [45013, 260], [45313, 79], [45531, 41], [45612, 80], [45831, 41], [45912, 80], [46132, 39], [46211, 81], [46432, 39], [46511, 80], [46732, 38], [46810, 81], [47033, 37], [47110, 81], [47333, 37], [47409, 82], [47633, 36], [47708, 82], [47934, 35], [48008, 82], [48307, 83], [48607, 83], [48906, 84], [49206, 83], [49505, 84], [49805, 84], [50104, 85], [50404, 84], [50703, 85], [51003, 85], [51302, 86], [51602, 86], [51901, 86], [52201, 86], [52500, 87], [52800, 87], [53100, 86], [53400, 86], [53700, 86], [54000, 86], [54300, 86], [54600, 85], [54900, 85], [55200, 85], [55500, 85], [55800, 84], [56100, 84], [56400, 84], [56700, 84], [57000, 84], [57300, 83], [57600, 83], [57900, 83], [58200, 83], [58500, 83], [58800, 82], [59100, 82], [59400, 82], [59700, 82], [60000, 81], [60300, 81], [60600, 81], [60900, 81], [61200, 81], [61500, 80], [61800, 80], [62100, 80], [62400, 80], [62700, 79], [63000, 79], [63300, 79], [63600, 79], [63900, 79], [64200, 78], [64500, 78], [64800, 78], [65100, 78], [65400, 77], [65700, 77], [66000, 77], [66300, 77], [66600, 77], [66900, 76], [67200, 76], [67500, 76], [67800, 76], [68100, 75], [68400, 75], [68700, 75], [69000, 75], [69300, 75], [69600, 74], [69900, 74], [70200, 74], [70500, 74], [70800, 73], [71100, 73], [71400, 73], [71700, 73], [72000, 73], [72300, 72], [72600, 72], [72900, 72], [73200, 72], [73500, 71], [73800, 71], [74100, 71], [74400, 71], [74700, 71], [75000, 70], [75300, 70], [75600, 70], [75900, 70], [76200, 69], [76500, 69], [76800, 69], [77100, 69], [77400, 69], [77700, 68], [78000, 68], [78300, 68], [78600, 68], [78900, 67], [79200, 67], [79500, 67], [79800, 67], [80100, 67], [80400, 66], [80700, 66], [81000, 66], [81300, 66], [81600, 65], [81900, 65], [82200, 65], [82500, 65], [82800, 65], [83100, 64], [83402, 62], [83703, 61], [84004, 60], [84305, 58], [84606, 57], [84907, 56], [85208, 55], [85509, 54], [85811, 51], [86112, 50], [86413, 49], [86714, 48], [87015, 47], [87316, 45], [87617, 44], [87919, 42], [88220, 41], [88521, 39], [88822, 39], [89125, 38], [89429, 36], [89730, 36]], "point": [149, 150]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+00.06|+00.91|-01.26", "placeStationary": true, "receptacleObjectId": "CounterTop|+00.99|+00.95|+01.02"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 89, 299, 273], "mask": [[26400, 3], [26467, 128], [26629, 22], [26674, 29], [26767, 125], [26931, 20], [26973, 30], [27067, 124], [27233, 18], [27273, 30], [27366, 124], [27535, 15], [27572, 31], [27666, 123], [27837, 13], [27872, 32], [27965, 123], [28138, 12], [28171, 33], [28265, 122], [28440, 10], [28471, 33], [28564, 123], [28741, 9], [28770, 35], [28863, 124], [29042, 9], [29069, 37], [29162, 124], [29343, 9], [29369, 38], [29461, 125], [29643, 10], [29668, 40], [29759, 127], [29944, 11], [29967, 45], [30038, 1], [30056, 130], [30244, 14], [30265, 63], [30337, 109], [30449, 37], [30545, 83], [30636, 110], [30749, 38], [30845, 20], [30866, 63], [30932, 113], [31050, 37], [31145, 20], [31168, 61], [31231, 114], [31350, 37], [31445, 20], [31469, 59], [31530, 115], [31650, 37], [31745, 21], [31770, 58], [31830, 116], [31949, 39], [32045, 21], [32070, 57], [32129, 117], [32249, 39], [32345, 21], [32371, 56], [32429, 117], [32549, 39], [32645, 22], [32672, 54], [32729, 117], [32849, 40], [32944, 23], [32973, 53], [33028, 118], [33149, 40], [33244, 24], [33273, 52], [33328, 118], [33449, 41], [33543, 25], [33574, 51], [33628, 118], [33749, 42], [33842, 27], [33875, 49], [33927, 119], [34049, 43], [34142, 27], [34176, 48], [34227, 119], [34348, 45], [34441, 28], [34476, 47], [34527, 119], [34648, 46], [34740, 30], [34777, 45], [34827, 120], [34948, 48], [35039, 31], [35078, 44], [35126, 121], [35248, 49], [35338, 33], [35378, 43], [35426, 121], [35548, 51], [35637, 34], [35679, 42], [35726, 50], [35785, 62], [35848, 53], [35935, 37], [35980, 40], [36025, 48], [36087, 59], [36148, 56], [36233, 39], [36280, 40], [36325, 47], [36389, 57], [36448, 59], [36530, 43], [36581, 38], [36624, 46], [36690, 56], [36749, 63], [36826, 47], [36882, 37], [36924, 46], [36990, 56], [37049, 72], [37126, 48], [37182, 36], [37223, 46], [37291, 54], [37350, 71], [37426, 48], [37483, 36], [37522, 46], [37591, 53], [37650, 71], [37727, 48], [37784, 84], [37891, 53], [37951, 70], [38027, 48], [38085, 83], [38191, 52], [38251, 71], [38327, 49], [38385, 83], [38491, 52], [38551, 71], [38628, 49], [38686, 82], [38791, 52], [38844, 2], [38847, 1], [38849, 1], [38851, 71], [38928, 49], [38987, 81], [39091, 52], [39145, 1], [39147, 1], [39149, 1], [39151, 71], [39228, 50], [39282, 1], [39287, 81], [39391, 53], [39445, 1], [39447, 1], [39449, 1], [39451, 72], [39529, 49], [39581, 2], [39588, 81], [39691, 53], [39745, 1], [39747, 1], [39749, 1], [39751, 72], [39829, 50], [39881, 3], [39889, 80], [39990, 54], [40045, 1], [40047, 1], [40049, 1], [40051, 72], [40129, 50], [40180, 4], [40189, 81], [40289, 135], [40430, 55], [40490, 81], [40588, 136], [40730, 55], [40791, 81], [40888, 136], [41030, 56], [41091, 83], [41186, 138], [41331, 55], [41392, 84], [41484, 141], [41627, 1], [41631, 56], [41693, 83], [41779, 146], [41928, 1], [41932, 55], [41994, 82], [42079, 146], [42232, 56], [42294, 81], [42379, 148], [42530, 45], [42598, 77], [42678, 180], [42900, 75], [42978, 65], [43051, 106], [43200, 75], [43278, 63], [43354, 104], [43500, 74], [43578, 61], [43655, 103], [43800, 74], [43877, 61], [43956, 102], [44100, 74], [44177, 60], [44257, 101], [44400, 73], [44477, 59], [44558, 100], [44700, 73], [44777, 58], [44859, 99], [45000, 73], [45077, 58], [45159, 100], [45300, 72], [45376, 59], [45460, 99], [45600, 72], [45676, 59], [45760, 99], [45900, 72], [45976, 58], [46060, 99], [46200, 72], [46276, 58], [46360, 99], [46500, 71], [46575, 59], [46660, 99], [46800, 71], [46875, 59], [46960, 100], [47100, 71], [47175, 60], [47260, 100], [47400, 70], [47475, 60], [47560, 100], [47700, 70], [47775, 58], [47867, 93], [48000, 70], [48074, 55], [48171, 90], [48300, 70], [48374, 53], [48473, 88], [48600, 69], [48674, 51], [48775, 86], [48900, 69], [48974, 49], [49077, 85], [49200, 42], [49243, 26], [49274, 47], [49379, 83], [49500, 44], [49549, 20], [49573, 46], [49681, 81], [49800, 41], [49844, 6], [49861, 7], [49873, 44], [49983, 80], [50100, 45], [50150, 7], [50164, 4], [50174, 40], [50286, 77], [50400, 41], [50444, 6], [50588, 76], [50700, 45], [50750, 6], [50773, 5], [50889, 75], [51000, 41], [51045, 5], [51066, 2], [51072, 15], [51190, 75], [51300, 45], [51350, 5], [51364, 3], [51372, 25], [51491, 74], [51600, 50], [51662, 5], [51672, 36], [51792, 74], [51900, 56], [51960, 7], [51968, 2], [51972, 35], [52093, 73], [52200, 67], [52268, 3], [52272, 34], [52394, 73], [52500, 67], [52568, 2], [52571, 34], [52695, 72], [52800, 66], [52871, 33], [52996, 71], [53100, 66], [53171, 32], [53297, 71], [53400, 66], [53471, 31], [53598, 71], [53700, 68], [53770, 31], [53899, 71], [54000, 100], [54200, 70], [54300, 99], [54501, 70], [54600, 98], [54802, 70], [54900, 98], [55102, 70], [55200, 97], [55403, 70], [55500, 97], [55703, 71], [55800, 96], [56004, 70], [56100, 96], [56304, 71], [56400, 96], [56604, 72], [56700, 95], [56905, 71], [57000, 95], [57205, 72], [57300, 94], [57506, 72], [57600, 94], [57806, 73], [57900, 94], [58106, 76], [58200, 93], [58407, 81], [58500, 93], [58707, 87], [58800, 92], [59008, 184], [59308, 183], [59609, 182], [59909, 182], [60209, 182], [60509, 182], [60809, 182], [61109, 182], [61409, 182], [61709, 182], [62009, 182], [62309, 182], [62609, 182], [62909, 182], [63209, 182], [63509, 182], [63809, 182], [64109, 182], [64409, 182], [64709, 91], [65077, 23], [65378, 22], [65678, 22], [65979, 21], [66279, 21], [66579, 21], [66880, 20], [67180, 20], [67480, 20], [67781, 19], [68081, 19], [68382, 18], [68682, 18], [68982, 18], [69283, 17], [69583, 17], [69884, 16], [70184, 16], [70484, 16], [70785, 15], [71085, 15], [71386, 14], [71686, 14], [71986, 14], [72287, 13], [72587, 13], [72887, 13], [73188, 12], [73488, 12], [73789, 11], [74089, 11], [74389, 11], [74690, 10], [74990, 10], [75291, 9], [75591, 9], [75891, 9], [76192, 8], [76492, 8], [76793, 7], [77093, 7], [77393, 7], [77694, 6], [77994, 6], [78295, 5], [78595, 5], [78895, 5], [79196, 4], [79496, 4], [79796, 4], [80097, 3], [80397, 3], [80698, 2], [80998, 2], [81298, 2], [81599, 1], [81899, 1]], "point": [160, 158]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan14", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 0, "x": 0.5, "y": 0.9009992, "z": 3.0}, "object_poses": [{"objectName": "Potato_6d0d5c3c", "position": {"x": 0.9262093, "y": 0.9514022, "z": -1.24763918}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ButterKnife_38537d23", "position": {"x": 0.5539353, "y": 0.911242068, "z": -1.352079}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_bb5e3511", "position": {"x": 1.54872215, "y": 0.9098, "z": 0.075}, "rotation": {"x": 0.0, "y": 179.999741, "z": 0.0}}, {"objectName": "Fork_192d20c7", "position": {"x": 0.7327088, "y": 0.911148548, "z": 0.8276406}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Spoon_89592edf", "position": {"x": 0.476415873, "y": 0.9117294, "z": 1.18092084}, "rotation": {"x": 0.0, "y": 269.999817, "z": 0.0}}, {"objectName": "Spoon_89592edf", "position": {"x": 1.60700226, "y": 0.911729336, "z": 0.363397181}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 1.6070025, "y": 0.9255998, "z": 0.311538458}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 1.7578752, "y": 0.9255999, "z": 1.0926044}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Egg_85efe221", "position": {"x": 1.83075309, "y": 0.105368823, "z": 1.684}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Egg_85efe221", "position": {"x": 1.77, "y": 0.105368823, "z": 1.63615465}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_5ecc05ec", "position": {"x": 1.50158286, "y": 0.9088847, "z": 1.26924419}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Tomato_817a54e2", "position": {"x": -0.692971647, "y": 1.45274591, "z": -1.52440894}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_7266264e", "position": {"x": 1.75787616, "y": 0.9094071, "z": 0.827643633}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 1.52946115, "y": 0.905599952, "z": 0.5189732}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 1.80555415, "y": 1.64890611, "z": -0.305277556}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_8f8a19bf", "position": {"x": 1.72622287, "y": 0.9880765, "z": -1.6316098}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_f9995e03", "position": {"x": -0.7415557, "y": 1.67800152, "z": -1.66452444}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_e8bf9352", "position": {"x": 1.50158429, "y": 0.9122848, "z": 0.827642858}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "SoapBottle_fd471042", "position": {"x": 0.455365479, "y": 0.9093641, "z": -1.263759}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Cup_5ecc05ec", "position": {"x": 0.06108719, "y": 0.9094847, "z": -1.26376021}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Egg_85efe221", "position": {"x": -0.8387238, "y": 0.785807133, "z": -1.6294955}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_192d20c7", "position": {"x": 0.9889997, "y": 0.911148548, "z": 1.09260213}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Ladle_4b0a72cf", "position": {"x": 0.7327086, "y": 0.954962432, "z": 0.915960848}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Apple_5b80d804", "position": {"x": -0.6443876, "y": 1.74030852, "z": -1.55943775}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_b3498f3e", "position": {"x": 0.9890009, "y": 0.9056, "z": 0.7393211}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pan_7013969f", "position": {"x": 1.28159308, "y": 0.900000036, "z": 1.19070256}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_817a54e2", "position": {"x": 1.451921, "y": 0.9744553, "z": 0.415255427}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Spatula_83c41bbf", "position": {"x": 0.894013643, "y": 0.7757628, "z": -1.65477979}, "rotation": {"x": 0.0, "y": 90.0, "z": 1.40334191e-14}}, {"objectName": "Potato_6d0d5c3c", "position": {"x": 1.10124719, "y": 0.8008449, "z": -1.71005964}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_06a240e2", "position": {"x": 1.50158358, "y": 0.9430372, "z": 1.00428343}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "ButterKnife_38537d23", "position": {"x": 0.296, "y": 0.9, "z": -1.296}, "rotation": {"x": 0.0, "y": 14.5689316, "z": 0.0}}, {"objectName": "Bread_8f8a19bf", "position": {"x": 0.4764157, "y": 0.9874766, "z": 1.26924109}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Lettuce_e61d6e1d", "position": {"x": 0.2201252, "y": 1.01053238, "z": 0.8276391}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "PepperShaker_3caefad1", "position": {"x": 0.258226335, "y": 0.9062, "z": -1.26375961}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Pot_bb5e3511", "position": {"x": 1.807, "y": 0.9098, "z": 0.075}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "DishSponge_00484768", "position": {"x": 1.84400678, "y": 1.63887084, "z": 0.5430741}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_89592edf", "position": {"x": 0.3567974, "y": 0.9123294, "z": -1.79368091}, "rotation": {"x": 0.0, "y": 179.999832, "z": 0.0}}, {"objectName": "Bowl_7266264e", "position": {"x": 1.72622216, "y": 0.910007, "z": -1.37455606}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Mug_f9995e03", "position": {"x": 1.84167039, "y": 1.64890611, "z": -0.497722775}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 165445203, "scene_num": 14}, "task_id": "trial_T20190908_220006_461458", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3F7G1FSFWQPLE_37FMASSAYFI9VAY0MJHZAYD6LTZIBR", "high_descs": ["Walk over to the counter to the left of the fridge.", "Pick up the green cup off of the counter.", "Turn left and walk over to the microwave.", "Heat up the green cup in the microwave for a couple seconds then take it out and close the microwave.", "Turn left and walk over to the counter.", "Put the heated green cup on the counter to the left of the white plate."], "task_desc": "Put a heated green cup on the counter.", "votes": [1, 1, 1]}, {"assignment_id": "A1RLO9LNUJIW5S_3KYQYYSHYYYC6T7NYALAPLXS0NQDO4", "high_descs": ["Turn around and take two steps to your right then walk to the fridge then take a step to your left.", "Pick up the green cup that's in front of you.", "Turn left and walk to the microwave.", "Warm the cup in the microwave then take it back out and close the door.", "Turn left and walk to the counter.", "Put the cup to the right of the sugar container."], "task_desc": "Put a warm cup on the counter.", "votes": [1, 1, 1]}, {"assignment_id": "A1HKHM4NVAO98H_3SKEMFQBZ6WHZ7QHJAI1WC2I5RCK8E", "high_descs": ["turn around and walk to the kitchen counter to the left of the fridge at the end of the room", "grab the green cup up off of the kitchen counter there", "turn left and walk over to the microwave up ahead", "place the green cup inside of the microwave, cook the cup, then take it back out", "turn left and walk over to the kitchen counter space just up ahead", "place the cup down on the kitchen counter space"], "task_desc": "place a microwaved cup down on the kitchen counter space", "votes": [1, 0, 1]}]}}