{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000177.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000178.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000179.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000180.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000181.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000182.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000183.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000184.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000185.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000186.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000187.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000188.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000189.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000192.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000193.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000194.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000195.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000196.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000197.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000198.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000199.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000200.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000201.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000202.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000203.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000204.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000205.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000206.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000207.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000208.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000209.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000210.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000211.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000212.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000213.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000214.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000215.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000216.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000217.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000218.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000219.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000220.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000221.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000222.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000223.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000224.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000225.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000226.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000227.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000228.png", "low_idx": 38}, {"high_idx": 6, "image_name": "000000229.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000230.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000231.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000232.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000233.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000234.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000235.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000236.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000237.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000238.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000239.png", "low_idx": 39}, {"high_idx": 6, "image_name": "000000240.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000241.png", "low_idx": 40}, {"high_idx": 6, "image_name": "000000242.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000243.png", "low_idx": 41}, {"high_idx": 6, "image_name": "000000244.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000245.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000246.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000247.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000248.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000249.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000250.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000251.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000252.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000253.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000254.png", "low_idx": 42}, {"high_idx": 6, "image_name": "000000255.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000256.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000257.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000258.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000259.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000260.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000261.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000262.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000263.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000264.png", "low_idx": 43}, {"high_idx": 6, "image_name": "000000265.png", "low_idx": 43}, {"high_idx": 7, "image_name": "000000266.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000267.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000268.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000269.png", "low_idx": 44}, {"high_idx": 7, "image_name": "000000270.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000271.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000272.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000273.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000274.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000275.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000276.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000277.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000278.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000279.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000280.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000281.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000282.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000283.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000284.png", "low_idx": 45}, {"high_idx": 7, "image_name": "000000285.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000286.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000287.png", "low_idx": 46}, {"high_idx": 7, "image_name": "000000288.png", "low_idx": 46}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "TissueBox", "parent_target": "Drawer", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|3|2|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["tissuebox"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["TissueBox", [2.670467852, 2.670467852, 3.5751216, 3.5751216, 2.641326, 2.641326]], "coordinateReceptacleObjectId": ["SideTable", [4.412, 4.412, 4.472, 4.472, 0.0, 0.0]], "forceVisible": true, "objectId": "TissueBox|+00.67|+00.66|+00.89"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tissuebox", "drawer"]}, "high_idx": 3, "planner_action": {"action": "PutObject", "coordinateObjectId": ["TissueBox", [2.670467852, 2.670467852, 3.5751216, 3.5751216, 2.641326, 2.641326]], "coordinateReceptacleObjectId": ["Drawer", [-4.092, -4.092, 4.13040064, 4.13040064, 2.931000232, 2.931000232]], "forceVisible": true, "objectId": "TissueBox|+00.67|+00.66|+00.89", "receptacleObjectId": "Drawer|-01.02|+00.73|+01.03"}}, {"discrete_action": {"action": "GotoLocation", "args": ["shelf"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|3|0|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["tissuebox"]}, "high_idx": 5, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["TissueBox", [-4.9068246, -4.9068246, 4.808, 4.808, 0.517510892, 0.517510892]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-2.504, -2.504, 4.808, 4.808, 0.476, 0.476]], "forceVisible": true, "objectId": "TissueBox|-01.23|+00.13|+01.20"}}, {"discrete_action": {"action": "GotoLocation", "args": ["drawer"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-5|1|0|45"}}, {"discrete_action": {"action": "PutObject", "args": ["tissuebox", "drawer"]}, "high_idx": 7, "planner_action": {"action": "PutObject", "coordinateObjectId": ["TissueBox", [-4.9068246, -4.9068246, 4.808, 4.808, 0.517510892, 0.517510892]], "coordinateReceptacleObjectId": ["Drawer", [-4.092, -4.092, 4.13040064, 4.13040064, 2.931000232, 2.931000232]], "forceVisible": true, "objectId": "TissueBox|-01.23|+00.13|+01.20", "receptacleObjectId": "Drawer|-01.02|+00.73|+01.03"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 8, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "TissueBox|+00.67|+00.66|+00.89"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [100, 159, 152, 193], "mask": [[47529, 4], [47820, 3], [47825, 10], [47838, 3], [48102, 50], [48402, 51], [48702, 51], [49002, 51], [49302, 51], [49602, 51], [49902, 51], [50202, 51], [50501, 52], [50801, 52], [51101, 52], [51401, 52], [51701, 52], [52001, 52], [52301, 52], [52601, 52], [52901, 52], [53200, 53], [53500, 53], [53800, 53], [54100, 53], [54400, 53], [54700, 53], [55000, 53], [55300, 53], [55600, 53], [55901, 52], [56201, 51], [56502, 50], [56802, 50], [57103, 49], [57403, 49], [57704, 48]], "point": [126, 175]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.02|+00.73|+01.03"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [108, 136, 299, 171], "mask": [[40608, 192], [40908, 192], [41208, 192], [41508, 191], [41808, 191], [42108, 190], [42409, 188], [42709, 188], [43009, 187], [43309, 187], [43609, 186], [43909, 186], [44209, 185], [44510, 184], [44810, 183], [45110, 182], [45410, 182], [45710, 181], [46010, 181], [46311, 179], [46611, 179], [46911, 178], [47211, 178], [47511, 177], [47811, 177], [48111, 176], [48412, 174], [48712, 174], [49012, 173], [49312, 173], [49612, 172], [49912, 172], [50213, 170], [50513, 170], [50813, 169], [51113, 168]], "point": [203, 152]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "TissueBox|+00.67|+00.66|+00.89", "placeStationary": true, "receptacleObjectId": "Drawer|-01.02|+00.73|+01.03"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [99, 136, 299, 218], "mask": [[40608, 192], [40907, 193], [41207, 193], [41507, 193], [41807, 193], [42107, 193], [42407, 193], [42706, 194], [43006, 194], [43306, 194], [43606, 194], [43906, 194], [44206, 194], [44505, 195], [44805, 195], [45105, 195], [45405, 195], [45705, 195], [46004, 196], [46304, 196], [46604, 196], [46904, 196], [47204, 196], [47504, 196], [47803, 197], [48103, 197], [48403, 197], [48703, 197], [49003, 197], [49303, 197], [49602, 198], [49902, 198], [50202, 198], [50502, 198], [50802, 198], [51102, 198], [51401, 199], [51701, 199], [52001, 199], [52301, 199], [52601, 199], [52901, 199], [53200, 200], [53500, 200], [53800, 200], [54100, 200], [54400, 200], [54699, 201], [54999, 201], [55300, 200], [55600, 200], [55900, 200], [56200, 200], [56500, 200], [56801, 199], [57101, 199], [57401, 199], [57701, 199], [58002, 198], [58302, 198], [58602, 198], [58902, 198], [59202, 198], [59503, 197], [59803, 197], [60103, 197], [60403, 197], [60704, 196], [61004, 196], [61304, 196], [61604, 196], [61904, 196], [62205, 195], [62505, 195], [62805, 195], [63105, 195], [63520, 80], [63821, 79], [64121, 79], [64421, 79], [64721, 79], [65021, 79], [65321, 79]], "point": [199, 176]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.02|+00.73|+01.03"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [99, 136, 299, 218], "mask": [[40608, 144], [40771, 29], [40907, 145], [41071, 29], [41207, 145], [41372, 28], [41507, 144], [41672, 28], [41807, 144], [41973, 27], [42107, 143], [42273, 27], [42407, 143], [42574, 26], [42706, 144], [42874, 26], [43006, 143], [43175, 25], [43306, 143], [43475, 25], [43606, 135], [43743, 6], [43776, 24], [43906, 134], [44044, 4], [44076, 24], [44206, 134], [44345, 3], [44377, 23], [44505, 134], [44646, 1], [44677, 23], [44805, 133], [44978, 22], [45105, 132], [45278, 22], [45405, 131], [45578, 22], [45705, 129], [45878, 22], [46004, 130], [46177, 23], [46304, 129], [46477, 23], [46604, 128], [46776, 24], [46904, 128], [47076, 24], [47204, 128], [47375, 25], [47504, 130], [47675, 25], [47803, 131], [47974, 26], [48103, 130], [48274, 26], [48403, 130], [48573, 27], [48703, 130], [48873, 27], [49003, 133], [49172, 28], [49303, 136], [49472, 28], [49602, 141], [49771, 29], [49902, 141], [50071, 29], [50202, 141], [50370, 30], [50502, 142], [50670, 30], [50802, 142], [50969, 31], [51102, 142], [51269, 31], [51401, 144], [51568, 32], [51701, 144], [51868, 32], [52001, 144], [52167, 33], [52301, 145], [52467, 33], [52601, 145], [52766, 34], [52901, 145], [53066, 34], [53200, 147], [53365, 35], [53500, 147], [53665, 35], [53800, 200], [54100, 200], [54400, 200], [54699, 201], [54999, 201], [55300, 200], [55600, 200], [55900, 200], [56200, 200], [56500, 200], [56801, 199], [57101, 199], [57401, 199], [57701, 199], [58002, 198], [58302, 198], [58602, 198], [58902, 198], [59202, 198], [59503, 197], [59803, 197], [60103, 197], [60403, 197], [60704, 196], [61004, 196], [61304, 196], [61604, 196], [61904, 196], [62205, 195], [62505, 195], [62805, 195], [63105, 195], [63406, 194], [63706, 194], [64006, 194], [64306, 194], [64606, 194], [64907, 193], [65207, 193]], "point": [199, 176]}}, "high_idx": 3}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PickupObject", "objectId": "TissueBox|-01.23|+00.13|+01.20"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [137, 209, 170, 217], "mask": [[62537, 34], [62837, 34], [63137, 34], [63437, 34], [63737, 34], [64037, 34], [64338, 33], [64638, 33], [64938, 32]], "point": [153, 212]}}, "high_idx": 5}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "OpenObject", "objectId": "Drawer|-01.02|+00.73|+01.03"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [108, 136, 299, 171], "mask": [[40608, 192], [40908, 192], [41208, 192], [41508, 191], [41808, 191], [42108, 190], [42409, 188], [42709, 188], [43009, 187], [43309, 187], [43609, 186], [43909, 186], [44209, 185], [44510, 184], [44810, 183], [45110, 182], [45410, 182], [45710, 181], [46010, 181], [46311, 179], [46611, 179], [46911, 178], [47211, 178], [47511, 177], [47811, 177], [48111, 176], [48412, 174], [48712, 174], [49012, 173], [49312, 173], [49612, 172], [49912, 172], [50213, 170], [50513, 170], [50813, 169], [51113, 168]], "point": [203, 152]}}, "high_idx": 7}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "TissueBox|-01.23|+00.13|+01.20", "placeStationary": true, "receptacleObjectId": "Drawer|-01.02|+00.73|+01.03"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [99, 136, 299, 218], "mask": [[40608, 144], [40771, 29], [40907, 145], [41071, 29], [41207, 145], [41372, 28], [41507, 144], [41672, 28], [41807, 144], [41973, 27], [42107, 143], [42273, 27], [42407, 143], [42574, 26], [42706, 144], [42874, 26], [43006, 143], [43175, 25], [43306, 143], [43475, 25], [43606, 135], [43743, 6], [43776, 24], [43906, 134], [44044, 4], [44076, 24], [44206, 134], [44345, 3], [44377, 23], [44505, 134], [44646, 1], [44677, 23], [44805, 133], [44978, 22], [45105, 132], [45278, 22], [45405, 131], [45578, 22], [45705, 129], [45878, 22], [46004, 130], [46177, 23], [46304, 129], [46477, 23], [46604, 128], [46776, 24], [46904, 128], [47076, 24], [47204, 128], [47375, 25], [47504, 130], [47675, 25], [47803, 131], [47974, 26], [48103, 130], [48274, 26], [48403, 130], [48573, 27], [48703, 130], [48873, 27], [49003, 133], [49172, 28], [49303, 136], [49472, 28], [49602, 141], [49771, 29], [49902, 141], [50071, 29], [50202, 141], [50370, 30], [50502, 142], [50670, 30], [50802, 142], [50969, 31], [51102, 142], [51269, 31], [51401, 144], [51568, 32], [51701, 144], [51868, 32], [52001, 144], [52167, 33], [52301, 145], [52467, 33], [52601, 145], [52766, 34], [52901, 145], [53066, 34], [53200, 147], [53365, 35], [53500, 147], [53665, 35], [53800, 200], [54100, 200], [54400, 200], [54699, 201], [54999, 201], [55300, 200], [55600, 200], [55900, 200], [56200, 200], [56500, 200], [56801, 199], [57101, 199], [57401, 199], [57701, 199], [58002, 198], [58302, 198], [58602, 198], [58902, 198], [59202, 198], [59503, 197], [59803, 197], [60103, 197], [60403, 197], [60704, 196], [61004, 196], [61304, 196], [61604, 196], [61904, 196], [62205, 195], [62505, 195], [62805, 195], [63105, 195], [63520, 80], [63821, 79], [64121, 79], [64421, 79], [64721, 79], [65021, 79], [65321, 79]], "point": [199, 176]}}, "high_idx": 7}, {"api_action": {"action": "CloseObject", "objectId": "Drawer|-01.02|+00.73|+01.03"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [99, 136, 299, 218], "mask": [[40608, 144], [40771, 29], [40907, 145], [41071, 29], [41207, 145], [41372, 28], [41507, 144], [41672, 28], [41807, 144], [41973, 27], [42107, 143], [42273, 27], [42407, 143], [42574, 26], [42706, 144], [42874, 26], [43006, 143], [43175, 25], [43306, 143], [43475, 25], [43606, 135], [43743, 6], [43776, 24], [43906, 134], [44044, 4], [44076, 24], [44206, 134], [44345, 3], [44377, 23], [44505, 134], [44646, 1], [44677, 23], [44805, 133], [44978, 22], [45105, 132], [45278, 22], [45405, 131], [45578, 22], [45705, 129], [45878, 22], [46004, 130], [46180, 20], [46304, 129], [46480, 20], [46604, 128], [46781, 19], [46904, 128], [47081, 19], [47204, 128], [47382, 18], [47504, 130], [47682, 18], [47803, 131], [47983, 17], [48103, 130], [48283, 17], [48403, 130], [48584, 16], [48703, 130], [48884, 16], [49003, 133], [49185, 15], [49303, 136], [49485, 15], [49602, 141], [49786, 14], [49902, 141], [50086, 14], [50202, 141], [50387, 13], [50502, 141], [50687, 13], [50802, 140], [50988, 12], [51102, 139], [51288, 12], [51401, 139], [51588, 12], [51701, 138], [51888, 12], [52001, 137], [52187, 13], [52301, 137], [52487, 13], [52601, 137], [52786, 14], [52901, 138], [53086, 14], [53200, 139], [53385, 15], [53500, 139], [53684, 16], [53800, 139], [53984, 16], [54100, 139], [54283, 17], [54400, 200], [54699, 201], [54999, 201], [55300, 200], [55600, 200], [55900, 200], [56200, 200], [56500, 200], [56801, 199], [57101, 199], [57401, 199], [57701, 199], [58002, 198], [58302, 198], [58602, 198], [58902, 198], [59202, 198], [59503, 197], [59803, 197], [60103, 197], [60403, 197], [60704, 196], [61004, 196], [61304, 196], [61604, 196], [61904, 196], [62205, 195], [62505, 195], [62805, 195], [63105, 195], [63406, 194], [63706, 194], [64006, 194], [64306, 194], [64606, 194], [64907, 193], [65207, 193]], "point": [199, 176]}}, "high_idx": 7}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan216", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 180, "x": -1.75, "y": 0.9009992, "z": 0.75}, "object_poses": [{"objectName": "TissueBox_50e690f5", "position": {"x": 0.667616963, "y": 0.6603315, "z": 0.8937804}, "rotation": {"x": 0.0, "y": -1.70754731e-06, "z": 0.0}}, {"objectName": "TissueBox_50e690f5", "position": {"x": -1.22670615, "y": 0.129377723, "z": 1.202}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Box_6fa08eb0", "position": {"x": -3.78054142, "y": 1.305, "z": 0.457275361}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Vase_462d92e0", "position": {"x": -0.97179544, "y": 0.114890076, "z": 1.19714463}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "TissueBox_50e690f5", "position": {"x": -4.564468, "y": 0.07695126, "z": 1.13270032}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}, {"objectName": "Newspaper_932fe9f5", "position": {"x": 0.283747524, "y": 0.5532159, "z": -1.1515367}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "KeyChain_5b568144", "position": {"x": 1.74542785, "y": 0.650073767, "z": -1.639526}, "rotation": {"x": 358.912659, "y": 179.986176, "z": 0.8770518}}, {"objectName": "Vase_1d8d3594", "position": {"x": -1.27691031, "y": 0.9082607, "z": 1.28077233}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_b9ed9b88", "position": {"x": -0.359696567, "y": 0.902987838, "z": 1.18729341}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "RemoteControl_5b8bcfa9", "position": {"x": -0.169747531, "y": 0.4831681, "z": -1.31966329}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pillow_22286f86", "position": {"x": -1.82799935, "y": 0.585, "z": -1.25104988}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "CreditCard_a56cd598", "position": {"x": -1.21930194, "y": 0.633641243, "z": 1.21942413}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Laptop_fbe5dbee", "position": {"x": 1.26798213, "y": 0.656631947, "z": 1.118}, "rotation": {"x": 0.0, "y": -1.70754731e-06, "z": 0.0}}, {"objectName": "Vase_6ea54dcc", "position": {"x": -0.5166645, "y": 0.114890076, "z": 1.20104086}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Statue_d6a7b63c", "position": {"x": 0.967799544, "y": 0.7585242, "z": 1.19274}, "rotation": {"x": 0.0, "y": -1.70754731e-06, "z": 0.0}}], "object_toggles": [], "random_seed": 3006382448, "scene_num": 216}, "task_id": "trial_T20190908_150513_129789", "task_type": "pick_two_obj_and_place", "turk_annotations": {"anns": [{"assignment_id": "A24RGLS3BRZ60J_32Z9ZLUT1OBTI9CPK640G6CQSCGHO8", "high_descs": ["Turn to the left, then face the desk on the right.", "Pick up the tissues from the front left corner of the desk.", "Carry the tissues and turn to the left, then face the shelf on the right.", "Move the items together on the bottom shelf, and place the box of tissues on the middle shelf.", "Move the items from the bottom shelf where they had been.", "Pick up the tissues from the bottom shelf.", "Carry the tissues and look around to the right of the room.", "Place the tissues on the middle shelf."], "task_desc": "Move two boxes of tissues to the shelf.", "votes": [1, 1]}, {"assignment_id": "A98E8M4QLI9RS_3KRVW3HTZQCU8F5TIK1MSGUN6NISMP", "high_descs": ["turn to the left and move forward and turn left again to face the short table that is below the window", "pick up the tissue box from the left front corner of the short table", "turn to left and move to the taller table to the left of the short table with the white vase ", "open the drawer on the left side of the tall table and put the box of tissues in to the drawer, close the drawer", "move back slightly from the tall table", "pick up the box of tissues that is on the bottom shelf on the left side", "face the drawer on the left side of the tall table", "open the drawer, put the second box of tissues in to the drawer beside the first box and close the drawer"], "task_desc": "put two boxes of tissues in to the drawer of the tall table", "votes": [1, 1]}, {"assignment_id": "AISNLDPD2DFEG_3SB5N7Y3O6VUFWPVYFK75547F8C0GP", "high_descs": ["Turn left, go forward, turn right at the desk", "Pick up the tissue box on the desk", "Turn left, go forward, turn right at the sideboard", "Put the tissue box in the top left drawer of the sideboard", "Turn down to the bottom shelf of the sideboard", "Pick up the tissue box on the shelf", "Turn around, go forward a bit, turn around to face the sideboard", "Put the tissue box in the top left drawer of the sideboard"], "task_desc": "Put two tissue boxes into drawer", "votes": [1, 1]}]}}