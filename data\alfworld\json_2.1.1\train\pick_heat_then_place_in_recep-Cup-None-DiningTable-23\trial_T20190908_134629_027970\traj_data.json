{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000071.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000072.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000073.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000074.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000075.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000076.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000077.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000078.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000079.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000080.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000081.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000082.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000083.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000084.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000085.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000086.png", "low_idx": 17}, {"high_idx": 1, "image_name": "000000087.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000088.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000089.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000090.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000190.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000191.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000193.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000194.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000195.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000196.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000197.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000198.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000199.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000201.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000202.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000203.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000204.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000205.png", "low_idx": 30}, {"high_idx": 3, "image_name": "000000206.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000207.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000208.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000209.png", "low_idx": 31}, {"high_idx": 3, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000214.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000215.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000216.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000217.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000218.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000219.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000220.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000221.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000222.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000223.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 32}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 33}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000229.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000230.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000231.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000232.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000233.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000234.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000235.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000236.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000237.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000238.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000239.png", "low_idx": 34}, {"high_idx": 4, "image_name": "000000240.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000241.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000242.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000243.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000244.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000245.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000246.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000247.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000248.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000249.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000250.png", "low_idx": 35}, {"high_idx": 4, "image_name": "000000251.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000252.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000253.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000254.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000255.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000256.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000257.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000258.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000259.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000260.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000261.png", "low_idx": 36}, {"high_idx": 4, "image_name": "000000262.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000263.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000264.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000265.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000266.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000267.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000268.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000269.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000270.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000271.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000272.png", "low_idx": 37}, {"high_idx": 4, "image_name": "000000273.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000274.png", "low_idx": 38}, {"high_idx": 4, "image_name": "000000275.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000276.png", "low_idx": 39}, {"high_idx": 4, "image_name": "000000277.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000278.png", "low_idx": 40}, {"high_idx": 4, "image_name": "000000279.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000280.png", "low_idx": 41}, {"high_idx": 4, "image_name": "000000281.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000282.png", "low_idx": 42}, {"high_idx": 4, "image_name": "000000283.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000284.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000285.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000286.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000287.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000288.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000289.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000290.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000291.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000292.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000293.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 44}, {"high_idx": 5, "image_name": "000000305.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000306.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000307.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000308.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000309.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000310.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000311.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000312.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000313.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000314.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000315.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000316.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000317.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000318.png", "low_idx": 45}, {"high_idx": 5, "image_name": "000000319.png", "low_idx": 45}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "DiningTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-4|-12|1|60"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [-1.8411248, -1.8411248, -12.94620132, -12.94620132, 0.32004596, 0.32004596]], "coordinateReceptacleObjectId": ["Cabinet", [-2.3360976, -2.3360976, -13.587451, -13.587451, 1.5553813, 1.5553813]], "forceVisible": true, "objectId": "Cup|-00.46|+00.08|-03.24"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-12|2|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-5.284, -5.284, -15.412, -15.412, 6.084, 6.084]], "forceVisible": true, "objectId": "Microwave|-01.32|+01.52|-03.85"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-5|-7|3|30"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [-1.8411248, -1.8411248, -12.94620132, -12.94620132, 0.32004596, 0.32004596]], "coordinateReceptacleObjectId": ["DiningTable", [-9.73199272, -9.73199272, -6.78581428, -6.78581428, 3.5828, 3.5828]], "forceVisible": true, "objectId": "Cup|-00.46|+00.08|-03.24", "receptacleObjectId": "DiningTable|-02.43|+00.90|-01.70"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|-00.58|+00.39|-03.40"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [174, 169, 257, 219], "mask": [[50588, 1], [50888, 70], [51187, 71], [51487, 70], [51787, 69], [52087, 68], [52386, 69], [52686, 68], [52986, 67], [53285, 67], [53585, 66], [53885, 65], [54185, 65], [54484, 65], [54784, 64], [55084, 63], [55383, 63], [55683, 62], [55983, 62], [56282, 62], [56582, 61], [56882, 60], [57182, 59], [57481, 59], [57781, 59], [58081, 58], [58380, 58], [58680, 57], [58980, 56], [59280, 55], [59579, 56], [59879, 55], [60179, 54], [60478, 54], [60778, 53], [61078, 52], [61378, 52], [61677, 52], [61977, 51], [62277, 50], [62576, 50], [62876, 49], [63176, 49], [63476, 48], [63775, 48], [64075, 47], [64375, 46], [64674, 46], [64974, 46], [65274, 45], [65574, 44]], "point": [215, 193]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.46|+00.08|-03.24"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [182, 181, 201, 203], "mask": [[54187, 9], [54486, 11], [54785, 14], [55084, 16], [55384, 16], [55684, 17], [55983, 18], [56283, 19], [56583, 19], [56882, 20], [57183, 19], [57483, 19], [57783, 19], [58084, 17], [58383, 18], [58683, 17], [58983, 16], [59283, 14], [59583, 12], [59884, 11], [60185, 9], [60486, 7], [60789, 2]], "point": [191, 191]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|-00.58|+00.39|-03.40"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [179, 170, 276, 266], "mask": [[50888, 67], [50957, 2], [51188, 66], [51256, 4], [51488, 65], [51555, 5], [51787, 65], [51854, 7], [52087, 64], [52153, 8], [52387, 64], [52453, 8], [52687, 63], [52752, 9], [52986, 63], [53051, 11], [53286, 62], [53350, 12], [53586, 61], [53649, 13], [53885, 62], [53949, 13], [54185, 61], [54248, 14], [54485, 60], [54547, 16], [54784, 60], [54846, 17], [55084, 59], [55145, 18], [55384, 59], [55444, 19], [55684, 58], [55744, 20], [55983, 58], [56043, 21], [56283, 57], [56342, 22], [56583, 56], [56641, 23], [56882, 57], [56940, 25], [57182, 56], [57240, 25], [57482, 55], [57539, 26], [57782, 54], [57838, 27], [58081, 54], [58137, 29], [58381, 54], [58436, 30], [58681, 53], [58735, 31], [58980, 53], [59035, 31], [59280, 52], [59334, 32], [59580, 51], [59633, 34], [59879, 52], [59932, 35], [60179, 51], [60231, 36], [60479, 50], [60531, 36], [60779, 49], [60830, 38], [61081, 46], [61129, 39], [61383, 44], [61428, 40], [61684, 42], [61727, 41], [61986, 39], [62026, 43], [62286, 38], [62326, 43], [62587, 36], [62625, 44], [62888, 35], [62924, 45], [63189, 33], [63223, 47], [63489, 32], [63522, 48], [63790, 30], [63822, 48], [64091, 28], [64121, 49], [64392, 27], [64420, 50], [64692, 26], [64719, 52], [65018, 53], [65317, 54], [65617, 54], [65917, 55], [66217, 55], [66517, 55], [66817, 55], [67118, 55], [67418, 55], [67718, 55], [68018, 55], [68318, 55], [68618, 56], [68918, 56], [69218, 56], [69519, 55], [69819, 56], [70119, 56], [70419, 56], [70719, 56], [71019, 57], [71319, 57], [71619, 57], [71920, 56], [72220, 57], [72520, 57], [72820, 55], [73120, 53], [73420, 51], [73720, 49], [74020, 47], [74321, 43], [74621, 41], [74921, 39], [75221, 37], [75521, 35], [75821, 33], [76121, 30], [76422, 27], [76722, 25], [77022, 23], [77322, 21], [77622, 19], [77922, 16], [78222, 14], [78522, 12], [78823, 9], [79123, 7], [79423, 4], [79723, 2]], "point": [227, 217]}}, "high_idx": 1}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.46|+00.08|-03.24", "placeStationary": true, "receptacleObjectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 102], "mask": [[0, 1293], [1413, 179], [1714, 177], [2015, 176], [2315, 176], [2615, 177], [2914, 178], [3214, 178], [3514, 178], [3814, 178], [4114, 178], [4414, 178], [4714, 178], [5014, 178], [5314, 178], [5614, 178], [5914, 178], [6214, 178], [6514, 178], [6814, 178], [7114, 178], [7414, 178], [7714, 178], [8013, 179], [8313, 180], [8613, 180], [8913, 180], [9213, 180], [9513, 180], [9813, 180], [10113, 180], [10413, 180], [10713, 180], [11013, 180], [11313, 180], [11613, 180], [11913, 180], [12213, 180], [12513, 180], [12813, 180], [13112, 181], [13412, 181], [13712, 181], [14012, 87], [14100, 94], [14312, 87], [14400, 94], [14612, 87], [14700, 94], [14912, 87], [15000, 94], [15212, 87], [15300, 94], [15512, 87], [15600, 94], [15812, 87], [15900, 94], [16112, 86], [16200, 94], [16412, 86], [16500, 94], [16712, 86], [16800, 94], [17012, 86], [17100, 94], [17312, 86], [17400, 94], [17612, 86], [17700, 94], [17912, 86], [18000, 94], [18211, 86], [18300, 94], [18511, 86], [18600, 94], [18811, 86], [18900, 94], [19111, 86], [19200, 94], [19411, 86], [19500, 94], [19711, 86], [19800, 95], [20011, 86], [20100, 95], [20237, 35], [20311, 86], [20400, 95], [20520, 68], [20611, 85], [20700, 296], [21000, 296], [21300, 296], [21600, 296], [21900, 296], [22200, 296], [22500, 295], [22800, 295], [23100, 295], [23400, 295], [23700, 295], [24000, 295], [24300, 295], [24600, 294], [24900, 294], [25200, 294], [25500, 294], [25800, 59], [25861, 233], [26100, 44], [26161, 233], [26400, 29], [26461, 232], [26700, 15], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [149, 67]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 102], "mask": [[0, 1293], [1413, 179], [1714, 177], [2015, 176], [2315, 176], [2615, 177], [2914, 178], [3214, 178], [3514, 178], [3814, 178], [4114, 178], [4414, 178], [4714, 178], [5014, 178], [5314, 178], [5614, 178], [5914, 178], [6214, 178], [6514, 178], [6814, 178], [7114, 178], [7414, 178], [7714, 178], [8013, 179], [8313, 180], [8613, 180], [8913, 180], [9213, 180], [9513, 180], [9813, 180], [10113, 180], [10413, 180], [10713, 180], [11013, 180], [11313, 180], [11613, 180], [11913, 180], [12213, 180], [12513, 180], [12813, 180], [13112, 181], [13412, 181], [13712, 181], [14012, 87], [14100, 94], [14312, 87], [14400, 94], [14612, 87], [14700, 94], [14912, 87], [15000, 94], [15212, 87], [15300, 94], [15512, 87], [15600, 94], [15812, 87], [15900, 94], [16112, 86], [16200, 94], [16412, 86], [16500, 94], [16712, 86], [16800, 94], [17012, 86], [17100, 94], [17312, 86], [17400, 94], [17612, 86], [17700, 94], [17912, 86], [18000, 94], [18211, 86], [18300, 94], [18511, 86], [18600, 94], [18811, 86], [18900, 94], [19111, 86], [19200, 94], [19411, 86], [19500, 94], [19711, 86], [19800, 95], [20011, 86], [20100, 95], [20237, 35], [20311, 86], [20400, 95], [20520, 68], [20611, 85], [20700, 296], [21000, 296], [21300, 296], [21600, 296], [21900, 296], [22200, 296], [22500, 295], [22800, 295], [23100, 295], [23400, 295], [23700, 295], [24000, 295], [24300, 295], [24600, 294], [24900, 294], [25200, 294], [25500, 294], [25800, 59], [25861, 233], [26100, 44], [26161, 233], [26400, 29], [26461, 232], [26700, 15], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [149, 67]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-5.284, -5.284, -15.412, -15.412, 6.084, 6.084]], "forceVisible": true, "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-5.284, -5.284, -15.412, -15.412, 6.084, 6.084]], "forceVisible": true, "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [53, 1, 299, 102], "mask": [[53, 247], [354, 246], [654, 246], [954, 246], [1254, 246], [1554, 246], [1854, 246], [2154, 246], [2454, 246], [2754, 246], [3054, 246], [3354, 246], [3654, 246], [3955, 245], [4255, 245], [4555, 245], [4855, 245], [5155, 245], [5455, 245], [5755, 245], [6055, 245], [6355, 245], [6655, 245], [6955, 245], [7256, 244], [7556, 244], [7856, 244], [8156, 244], [8456, 244], [8756, 244], [9056, 244], [9356, 244], [9656, 244], [9956, 244], [10256, 244], [10556, 244], [10857, 243], [11157, 243], [11457, 243], [11757, 243], [12057, 243], [12357, 243], [12657, 243], [12957, 243], [13257, 243], [13557, 243], [13857, 242], [14157, 242], [14458, 241], [14758, 241], [15058, 241], [15358, 241], [15658, 241], [15958, 240], [16258, 240], [16558, 240], [16858, 240], [17158, 240], [17458, 240], [17759, 239], [18059, 238], [18359, 238], [18659, 238], [18959, 238], [19259, 238], [19559, 238], [19859, 238], [20159, 238], [20459, 237], [20759, 237], [21059, 237], [21360, 236], [21660, 236], [21960, 236], [22260, 236], [22560, 235], [22860, 235], [23160, 235], [23460, 235], [23760, 235], [24060, 235], [24360, 235], [24661, 233], [24961, 233], [25261, 233], [25561, 233], [25861, 233], [26161, 233], [26461, 232], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [176, 50]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|-00.46|+00.08|-03.24"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [133, 10, 171, 62], "mask": [[2843, 20], [3138, 30], [3435, 35], [3734, 38], [4033, 39], [4333, 39], [4633, 39], [4934, 37], [5235, 35], [5536, 33], [5837, 31], [6138, 30], [6438, 29], [6739, 27], [7040, 25], [7341, 23], [7642, 21], [7943, 19], [8244, 17], [8545, 15], [8846, 13], [9147, 11], [9448, 10], [9748, 9], [10049, 7], [10350, 5], [10651, 3], [10951, 3], [11251, 3], [11551, 3], [11851, 3], [12151, 3], [12451, 3], [12751, 3], [13051, 3], [13351, 3], [13651, 3], [13951, 3], [14251, 3], [14551, 3], [14851, 3], [15151, 3], [15451, 3], [15751, 3], [16051, 3], [16351, 3], [16651, 3], [16951, 3], [17251, 3], [17551, 3], [17850, 5], [18140, 25], [18440, 25]], "point": [152, 35]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-01.32|+01.52|-03.85"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 102], "mask": [[0, 1293], [1413, 179], [1714, 177], [2015, 176], [2315, 176], [2615, 177], [2914, 178], [3214, 178], [3514, 178], [3814, 178], [4114, 178], [4414, 178], [4714, 178], [5014, 178], [5314, 178], [5614, 178], [5914, 178], [6214, 178], [6514, 178], [6814, 178], [7114, 178], [7414, 178], [7714, 178], [8013, 179], [8313, 180], [8613, 180], [8913, 180], [9213, 180], [9513, 180], [9813, 180], [10113, 180], [10413, 180], [10713, 180], [11013, 180], [11313, 180], [11613, 180], [11913, 180], [12213, 180], [12513, 180], [12813, 180], [13112, 181], [13412, 181], [13712, 181], [14012, 87], [14100, 94], [14312, 87], [14400, 94], [14612, 87], [14700, 94], [14912, 87], [15000, 94], [15212, 87], [15300, 94], [15512, 87], [15600, 94], [15812, 87], [15900, 94], [16112, 86], [16200, 94], [16412, 86], [16500, 94], [16712, 86], [16800, 94], [17012, 86], [17100, 94], [17312, 86], [17400, 94], [17612, 86], [17700, 94], [17912, 86], [18000, 94], [18211, 86], [18300, 94], [18511, 86], [18600, 94], [18811, 86], [18900, 94], [19111, 86], [19200, 94], [19411, 86], [19500, 94], [19711, 86], [19800, 95], [20011, 86], [20100, 95], [20237, 35], [20311, 86], [20400, 95], [20520, 68], [20611, 85], [20700, 296], [21000, 296], [21300, 296], [21600, 296], [21900, 296], [22200, 296], [22500, 295], [22800, 295], [23100, 295], [23400, 295], [23700, 295], [24000, 295], [24300, 295], [24600, 294], [24900, 294], [25200, 294], [25500, 294], [25800, 59], [25861, 233], [26100, 44], [26161, 233], [26400, 29], [26461, 232], [26700, 15], [26762, 231], [27062, 231], [27362, 231], [27662, 231], [27962, 231], [28262, 231], [28562, 231], [28862, 230], [29162, 230], [29462, 230], [29762, 230], [30063, 229], [30363, 229]], "point": [149, 67]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|-00.46|+00.08|-03.24", "placeStationary": true, "receptacleObjectId": "DiningTable|-02.43|+00.90|-01.70"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 114, 299, 300], "mask": [[33929, 18], [33962, 17], [33995, 61], [34074, 21], [34109, 12], [34153, 47], [34228, 20], [34262, 18], [34295, 61], [34374, 21], [34409, 11], [34454, 46], [34527, 22], [34562, 18], [34595, 61], [34674, 20], [34709, 10], [34755, 45], [34826, 24], [34863, 17], [34895, 61], [34975, 19], [35009, 9], [35056, 44], [35124, 27], [35164, 16], [35195, 60], [35275, 19], [35309, 9], [35357, 43], [35423, 30], [35464, 16], [35495, 60], [35575, 20], [35609, 8], [35657, 43], [35722, 31], [35765, 15], [35795, 61], [35874, 22], [35909, 7], [35958, 42], [36021, 31], [36066, 14], [36095, 62], [36174, 24], [36209, 7], [36258, 42], [36320, 31], [36367, 13], [36395, 33], [36429, 30], [36472, 29], [36509, 7], [36559, 12], [36580, 4], [36619, 31], [36668, 11], [36695, 32], [36729, 35], [36767, 34], [36809, 7], [36860, 6], [36918, 31], [36969, 11], [36996, 31], [37028, 38], [37067, 34], [37109, 8], [37161, 5], [37217, 31], [37250, 1], [37270, 10], [37296, 31], [37328, 38], [37367, 33], [37409, 8], [37516, 31], [37571, 9], [37596, 31], [37628, 72], [37709, 8], [37764, 4], [37778, 1], [37815, 30], [37871, 9], [37896, 30], [37928, 72], [38009, 8], [38065, 5], [38072, 7], [38114, 29], [38171, 9], [38196, 30], [38227, 73], [38309, 9], [38365, 15], [38413, 29], [38472, 8], [38495, 31], [38527, 70], [38598, 3], [38608, 10], [38665, 16], [38712, 30], [38772, 8], [38793, 74], [38868, 29], [38899, 20], [38965, 16], [39011, 31], [39072, 8], [39089, 25], [39138, 29], [39168, 29], [39200, 19], [39264, 18], [39310, 33], [39372, 8], [39389, 25], [39438, 29], [39468, 29], [39501, 19], [39564, 19], [39608, 35], [39673, 7], [39689, 24], [39738, 29], [39768, 29], [39802, 18], [39862, 21], [39907, 36], [39973, 7], [39989, 24], [40038, 29], [40068, 30], [40103, 18], [40159, 25], [40206, 37], [40273, 7], [40290, 23], [40338, 29], [40368, 30], [40404, 18], [40457, 28], [40505, 38], [40573, 7], [40589, 24], [40638, 29], [40668, 31], [40705, 19], [40756, 29], [40804, 39], [40873, 8], [40888, 25], [40938, 29], [40968, 31], [41006, 19], [41054, 32], [41103, 41], [41173, 40], [41238, 29], [41268, 32], [41306, 20], [41354, 32], [41402, 42], [41474, 39], [41538, 29], [41569, 31], [41607, 21], [41648, 4], [41655, 32], [41701, 43], [41774, 93], [41869, 32], [41908, 22], [41945, 8], [41956, 31], [42000, 44], [42074, 93], [42170, 31], [42209, 24], [42243, 12], [42257, 30], [42300, 44], [42374, 93], [42470, 32], [42509, 47], [42558, 30], [42600, 43], [42674, 93], [42770, 32], [42810, 47], [42859, 30], [42900, 43], [42974, 93], [43070, 33], [43110, 48], [43160, 29], [43200, 43], [43279, 89], [43370, 33], [43407, 50], [43461, 29], [43500, 42], [43581, 87], [43670, 34], [43708, 48], [43763, 28], [43800, 42], [43883, 85], [43970, 34], [44008, 48], [44064, 29], [44100, 42], [44183, 85], [44270, 35], [44309, 47], [44365, 29], [44400, 41], [44484, 84], [44571, 34], [44610, 47], [44665, 30], [44700, 41], [44784, 84], [44871, 34], [44910, 50], [44966, 74], [45084, 84], [45171, 35], [45211, 129], [45384, 84], [45471, 35], [45511, 129], [45684, 84], [45771, 36], [45811, 129], [45983, 85], [46071, 36], [46112, 128], [46282, 87], [46371, 37], [46414, 126], [46581, 88], [46670, 38], [46715, 125], [46877, 39], [46924, 85], [47014, 126], [47177, 36], [47227, 83], [47313, 127], [47477, 35], [47528, 212], [47777, 33], [47829, 211], [48077, 32], [48130, 39], [48171, 169], [48377, 32], [48430, 39], [48472, 168], [48677, 31], [48731, 38], [48772, 168], [48978, 30], [49031, 39], [49071, 169], [49278, 30], [49330, 40], [49372, 168], [49578, 30], [49630, 11], [49659, 11], [49672, 169], [49878, 31], [49930, 3], [49967, 3], [49972, 169], [50178, 32], [50272, 169], [50478, 34], [50576, 166], [50777, 37], [50879, 163], [51076, 43], [51181, 161], [51375, 41], [51484, 42], [51537, 106], [51674, 40], [51786, 38], [51839, 104], [51972, 41], [52087, 35], [52141, 102], [52272, 40], [52388, 33], [52442, 102], [52572, 39], [52689, 31], [52743, 101], [52872, 38], [52990, 29], [53044, 100], [53172, 37], [53291, 28], [53344, 101], [53472, 36], [53592, 27], [53644, 101], [53772, 36], [53892, 26], [53944, 101], [54072, 36], [54192, 27], [54244, 102], [54372, 37], [54491, 28], [54543, 103], [54672, 37], [54791, 29], [54843, 104], [54971, 38], [55091, 30], [55142, 105], [55271, 38], [55391, 32], [55441, 107], [55570, 39], [55691, 35], [55739, 109], [55869, 40], [55991, 39], [56036, 113], [56168, 43], [56289, 161], [56467, 45], [56588, 163], [56765, 48], [56887, 166], [57062, 52], [57186, 229], [57484, 231], [57783, 232], [58017, 1], [58082, 232], [58316, 4], [58380, 234], [58616, 5], [58679, 235], [58916, 6], [58978, 236], [59216, 7], [59277, 237], [59515, 10], [59575, 239], [59815, 11], [59874, 253], [60173, 256], [60471, 259], [60770, 261], [61069, 263], [61368, 266], [61666, 269], [61965, 271], [62264, 274], [62562, 277], [62861, 279], [63160, 281], [63459, 284], [63757, 287], [64056, 289], [64355, 291], [64654, 294], [64952, 296], [65252, 148], [65423, 91], [65554, 100], [65669, 8], [65693, 7], [65723, 91], [65856, 98], [65969, 8], [65994, 6], [66022, 92], [66158, 96], [66269, 8], [66295, 5], [66321, 93], [66459, 95], [66571, 6], [66592, 2], [66596, 4], [66621, 93], [66760, 94], [66868, 1], [66872, 5], [66891, 4], [66896, 4], [66920, 94], [67060, 94], [67168, 2], [67172, 5], [67191, 4], [67196, 4], [67220, 95], [67361, 93], [67467, 4], [67472, 4], [67496, 4], [67519, 98], [67661, 92], [67767, 2], [67772, 4], [67789, 1], [67794, 6], [67819, 99], [67961, 92], [68071, 5], [68089, 11], [68118, 102], [68261, 92], [68365, 11], [68388, 12], [68417, 104], [68560, 93], [68665, 12], [68688, 12], [68717, 105], [68860, 94], [68964, 14], [68987, 14], [69016, 108], [69160, 95], [69264, 16], [69287, 14], [69316, 109], [69460, 96], [69564, 19], [69584, 18], [69615, 112], [69760, 98], [69862, 40], [69914, 114], [70060, 142], [70214, 115], [70359, 144], [70513, 118], [70658, 145], [70813, 119], [70957, 147], [71112, 122], [71256, 148], [71412, 123], [71555, 149], [71711, 126], [71854, 151], [72010, 128], [72153, 152], [72310, 129], [72452, 154], [72609, 132], [72752, 154], [72909, 132], [73059, 147], [73208, 130], [73362, 145], [73508, 128], [73664, 269], [73967, 265], [74268, 263], [74569, 260], [74871, 257], [75172, 255], [75473, 253], [75774, 252], [76074, 251], [76375, 250], [76675, 249], [76976, 248], [77276, 248], [77576, 248], [77876, 137], [78054, 70], [78193, 70], [78264, 49], [78328, 1], [78355, 69], [78494, 120], [78628, 1], [78655, 69], [78794, 120], [78927, 2], [78955, 69], [79095, 119], [79227, 2], [79255, 70], [79395, 29], [79427, 88], [79527, 3], [79555, 70], [79695, 27], [79730, 85], [79826, 4], [79855, 70], [79996, 26], [80031, 85], [80126, 4], [80155, 71], [80296, 25], [80332, 84], [80425, 5], [80456, 71], [80596, 25], [80633, 83], [80725, 5], [80756, 72], [80896, 25], [80933, 84], [81024, 6], [81043, 1], [81050, 79], [81196, 22], [81233, 84], [81324, 6], [81342, 88], [81496, 21], [81533, 85], [81623, 8], [81641, 90], [81796, 21], [81819, 2], [81833, 85], [81923, 8], [81939, 94], [82096, 22], [82119, 2], [82133, 85], [82222, 9], [82237, 98], [82396, 23], [82433, 86], [82522, 9], [82536, 101], [82695, 25], [82732, 87], [82821, 10], [82834, 105], [82995, 26], [83032, 88], [83121, 10], [83133, 112], [83255, 4], [83294, 26], [83331, 229], [83594, 27], [83631, 229], [83893, 28], [83930, 231], [84192, 30], [84230, 232], [84492, 31], [84529, 235], [84791, 34], [84828, 237], [85089, 278], [85388, 281], [85686, 287], [85983, 2817], [88801, 299], [89101, 299], [89402, 298], [89702, 298]], "point": [142, 212]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan23", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -3.75, "y": 0.9009995, "z": -3.25}, "object_poses": [{"objectName": "Pencil_9e9e972c", "position": {"x": -2.66682315, "y": 0.923815966, "z": -1.88492227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_7baac0ce", "position": {"x": -2.32463574, "y": 0.954812, "z": -2.11539078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_7baac0ce", "position": {"x": -2.2105732, "y": 0.954812, "z": -1.88492227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b4789065", "position": {"x": -0.398883581, "y": 0.7967616, "z": -2.00827861}, "rotation": {"x": 6.06642669e-21, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "DishSponge_b4789065", "position": {"x": -0.205475584, "y": 1.97008312, "z": -1.828536}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_cbf780b9", "position": {"x": -2.78088546, "y": 0.9244038, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_cbf780b9", "position": {"x": -2.2105732, "y": 0.9244038, "z": -2.11539078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_cdf5a5fc", "position": {"x": -2.66682315, "y": 0.938474953, "z": -1.19351614}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_a6c5bfa2", "position": {"x": -0.222142339, "y": 0.9041887, "z": -3.57017875}, "rotation": {"x": 0.4546499, "y": 0.00153037079, "z": 359.903381}}, {"objectName": "Spoon_a6c5bfa2", "position": {"x": -2.438698, "y": 0.9246045, "z": -1.19351614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_d25a57c3", "position": {"x": -2.438698, "y": 0.9465557, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_3611fe48", "position": {"x": -0.4602812, "y": 0.08001149, "z": -3.23655033}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_3611fe48", "position": {"x": -0.3085294, "y": 1.68983662, "z": -0.6653125}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Cup_3611fe48", "position": {"x": -2.78088546, "y": 0.92246145, "z": -1.19351614}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_53032dff", "position": {"x": -2.78088546, "y": 0.922030866, "z": -1.65445352}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_91c0655d", "position": {"x": -0.2950916, "y": 1.38988817, "z": -0.874687552}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_7d6a9ffb", "position": {"x": -2.32463574, "y": 1.01634932, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_895229e9", "position": {"x": -0.457379431, "y": 0.912106156, "z": -3.83909845}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_895229e9", "position": {"x": -0.5083166, "y": 0.9121062, "z": -3.368925}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b8151885", "position": {"x": -2.5527606, "y": 0.919781268, "z": -2.11539078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_80763932", "position": {"x": -2.66682315, "y": 0.9184751, "z": -2.11539078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_c99eb5e1", "position": {"x": -0.3439546, "y": 1.17233372, "z": -0.6653124}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_c99eb5e1", "position": {"x": -1.94380212, "y": 0.100445457, "z": -3.79954958}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_c99eb5e1", "position": {"x": -2.78088546, "y": 0.9969592, "z": -2.34585953}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_7d6a9ffb", "position": {"x": -0.392817944, "y": 1.48372459, "z": -0.560624838}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Glassbottle_76ad74d1", "position": {"x": -2.09651065, "y": 0.9241918, "z": -2.11539078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_6d28f5dd", "position": {"x": -0.06780811, "y": 0.948730946, "z": -2.30186462}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Pen_638e2261", "position": {"x": -2.2105732, "y": 0.9261279, "z": -1.65445352}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_3622ecde", "position": {"x": -0.3506, "y": 0.7977459, "z": -1.934519}, "rotation": {"x": 1.40334191e-14, "y": 1.93186916e-05, "z": 5.583066e-21}}, {"objectName": "SoapBottle_53032dff", "position": {"x": -0.3226328, "y": 1.52931356, "z": -3.6103332}, "rotation": {"x": 0.0, "y": 44.9999962, "z": 0.0}}, {"objectName": "Pencil_9e9e972c", "position": {"x": -2.09651065, "y": 0.923815966, "z": -1.88492227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_cbf780b9", "position": {"x": -0.716718554, "y": 0.0832487941, "z": -3.50641036}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_cdf5a5fc", "position": {"x": -0.600459456, "y": 0.930799842, "z": -3.697545}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bread_fbb38b83", "position": {"x": -0.414996475, "y": 0.953735, "z": -3.62577868}, "rotation": {"x": 0.0370568149, "y": 359.908173, "z": 0.3570828}}, {"objectName": "Tomato_3d47a8d4", "position": {"x": -0.295090348, "y": 1.16266084, "z": -0.455937624}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pan_8d50963e", "position": {"x": -0.343955249, "y": 1.098459, "z": -0.8746874}, "rotation": {"x": 0.0, "y": 0.000160509444, "z": 0.0}}, {"objectName": "Cup_3611fe48", "position": {"x": -0.348537356, "y": 1.68983662, "z": -0.8746873}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pot_45f18f99", "position": {"x": -1.1294, "y": 0.9357, "z": -3.4986}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_895229e9", "position": {"x": -0.690776765, "y": 0.7430744, "z": -3.6158607}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_7baac0ce", "position": {"x": -2.09651065, "y": 0.954812, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_b8151885", "position": {"x": -2.66682315, "y": 0.919781268, "z": -1.42398477}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_f1c487d3", "position": {"x": -2.5527606, "y": 0.923517168, "z": -0.963047445}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_b4789065", "position": {"x": -2.5527606, "y": 0.9230393, "z": -1.88492227}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_a6c5bfa2", "position": {"x": -2.66682315, "y": 0.9246045, "z": -2.34585953}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_dc085d43", "position": {"x": -2.09651065, "y": 0.9258191, "z": -0.963047445}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_d25a57c3", "position": {"x": -2.438698, "y": 0.9465557, "z": -1.65445352}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_80763932", "position": {"x": -0.671999454, "y": 0.9108, "z": -3.55599165}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_91c0655d", "position": {"x": -2.438698, "y": 0.922512949, "z": -0.963047445}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1769224563, "scene_num": 23}, "task_id": "trial_T20190908_134629_027970", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A2871R3LEPWMMK_32UTUBMZ7JNEHGI0LQGIPN30TOEVB6", "high_descs": ["Turn around and walk to the green counter ahead with the coffee maker on it.", "Open the bottom cabinet to the right of the white dishwasher under the cabinet, pick up the clear martini glass and close the door.", "Turn around and turn left to face the white stove.", "Put the glass inside the microwave above the stove, heat it for a few seconds, remove it and close the door.", "Turn around and hang a left before the fridge to reach the green table.", "Put the heated glass on the far right corner of the green table to the right of the potato."], "task_desc": "Place a heated glass on a table.", "votes": [1, 1]}, {"assignment_id": "A98E8M4QLI9RS_3EQHHY4HQVJTA3BWVLQJM00YWD45GQ", "high_descs": ["turn around and move across the room to the white dishwasher that is passed the stove on the right", "open the cabinet door that is to the right of the dishwasher and remove the martini glass from the cabinet and close the door", "turn to the right right to face the stove with the microwave above the stove", "open the door to the microwave, put the martini glass in to the microwave, close the door, heat the glass for a few seconds, open the door and remove the glass, close the door", "turn around and move forward and stop and face the dining table on the left", "put the glass on the right corner of the table"], "task_desc": "put a heated martini glass on to the table", "votes": [1, 1]}, {"assignment_id": "A2Q3FS9G8ITCN7_3PWWM24LHVPMIRMRU4CXWGGOOK182D", "high_descs": ["Turn around to the right and walk to the coffee maker. ", "Pick up the glass from the counter below the coffee maker. ", "Turn to the right to face the microwave above the stove. ", "Open the microwave, place the glass into the microwave, and close the microwave. Start the microwave. Open the microwave door, remove the glass, close the microwave door. ", "Turn around to the right and walk to the table on the side furthest from the door. ", "Place the glass on the right corner of the table. "], "task_desc": "Move a glass from a cabinet to a table. ", "votes": [1, 1]}]}}