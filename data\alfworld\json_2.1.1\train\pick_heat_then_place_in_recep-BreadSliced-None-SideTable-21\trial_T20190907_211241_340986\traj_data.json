{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 14}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 15}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 16}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 17}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 18}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 18}, {"high_idx": 1, "image_name": "000000056.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000057.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000058.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000059.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000060.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000061.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000062.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000063.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000064.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000065.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000066.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000067.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000068.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000069.png", "low_idx": 19}, {"high_idx": 1, "image_name": "000000070.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000101.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000102.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000103.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000104.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000105.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000106.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000107.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000108.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000109.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000110.png", "low_idx": 26}, {"high_idx": 4, "image_name": "000000111.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000112.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000113.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000114.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000115.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000116.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000117.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000118.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000119.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000120.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000121.png", "low_idx": 27}, {"high_idx": 4, "image_name": "000000122.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000123.png", "low_idx": 28}, {"high_idx": 4, "image_name": "000000124.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000125.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000126.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000127.png", "low_idx": 30}, {"high_idx": 5, "image_name": "000000128.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000129.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000130.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000131.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000132.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000133.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000134.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000135.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000136.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000137.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000138.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000139.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000140.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000141.png", "low_idx": 31}, {"high_idx": 5, "image_name": "000000142.png", "low_idx": 31}, {"high_idx": 6, "image_name": "000000143.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000144.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000145.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000146.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000147.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000148.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000149.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000150.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000151.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000152.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000153.png", "low_idx": 32}, {"high_idx": 6, "image_name": "000000154.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000155.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000156.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000157.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000158.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000159.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000160.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000161.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000162.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000163.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000164.png", "low_idx": 33}, {"high_idx": 6, "image_name": "000000165.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000166.png", "low_idx": 34}, {"high_idx": 6, "image_name": "000000167.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000168.png", "low_idx": 35}, {"high_idx": 6, "image_name": "000000169.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000170.png", "low_idx": 36}, {"high_idx": 6, "image_name": "000000171.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000172.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000173.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000174.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000175.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000176.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000177.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000178.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000179.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000180.png", "low_idx": 37}, {"high_idx": 6, "image_name": "000000181.png", "low_idx": 37}, {"high_idx": 7, "image_name": "000000182.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000183.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000184.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000185.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000186.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000187.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000188.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000189.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000190.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000191.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000192.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000193.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000194.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000195.png", "low_idx": 38}, {"high_idx": 7, "image_name": "000000196.png", "low_idx": 38}, {"high_idx": 8, "image_name": "000000197.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000198.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000199.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000200.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000201.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000202.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000203.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000204.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000205.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000206.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000207.png", "low_idx": 39}, {"high_idx": 8, "image_name": "000000208.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000209.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000210.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000211.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000212.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000213.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000214.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000215.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000216.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000217.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000218.png", "low_idx": 40}, {"high_idx": 8, "image_name": "000000219.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000220.png", "low_idx": 41}, {"high_idx": 8, "image_name": "000000221.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000222.png", "low_idx": 42}, {"high_idx": 8, "image_name": "000000223.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000224.png", "low_idx": 43}, {"high_idx": 8, "image_name": "000000225.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000226.png", "low_idx": 44}, {"high_idx": 8, "image_name": "000000227.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000228.png", "low_idx": 45}, {"high_idx": 8, "image_name": "000000229.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000230.png", "low_idx": 46}, {"high_idx": 8, "image_name": "000000231.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000232.png", "low_idx": 47}, {"high_idx": 8, "image_name": "000000233.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000234.png", "low_idx": 48}, {"high_idx": 8, "image_name": "000000235.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000236.png", "low_idx": 49}, {"high_idx": 8, "image_name": "000000237.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000238.png", "low_idx": 50}, {"high_idx": 8, "image_name": "000000239.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000240.png", "low_idx": 51}, {"high_idx": 8, "image_name": "000000241.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000242.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000243.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000244.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000245.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000246.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000247.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000248.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000249.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000250.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000251.png", "low_idx": 52}, {"high_idx": 8, "image_name": "000000252.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000253.png", "low_idx": 53}, {"high_idx": 8, "image_name": "000000254.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000255.png", "low_idx": 54}, {"high_idx": 8, "image_name": "000000256.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000257.png", "low_idx": 55}, {"high_idx": 8, "image_name": "000000258.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000259.png", "low_idx": 56}, {"high_idx": 8, "image_name": "000000260.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000261.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000262.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000263.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000264.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000265.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000266.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000267.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000268.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000269.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000270.png", "low_idx": 57}, {"high_idx": 8, "image_name": "000000271.png", "low_idx": 58}, {"high_idx": 8, "image_name": "000000272.png", "low_idx": 58}, {"high_idx": 9, "image_name": "000000273.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000274.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000275.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000276.png", "low_idx": 59}, {"high_idx": 9, "image_name": "000000277.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000278.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000279.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000280.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000281.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000282.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000283.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000284.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000285.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000286.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000287.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000288.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000289.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000290.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000291.png", "low_idx": 60}, {"high_idx": 9, "image_name": "000000292.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000293.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000294.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000295.png", "low_idx": 61}, {"high_idx": 9, "image_name": "000000296.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000297.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000298.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000299.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000300.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000301.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000302.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000303.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000304.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000305.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000306.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000307.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000308.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000309.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000310.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000311.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000312.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000313.png", "low_idx": 62}, {"high_idx": 9, "image_name": "000000314.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000315.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000316.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000317.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000318.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000319.png", "low_idx": 63}, {"high_idx": 9, "image_name": "000000320.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000321.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000322.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000323.png", "low_idx": 64}, {"high_idx": 9, "image_name": "000000324.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000325.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000326.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000327.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000328.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000329.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000330.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000331.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000332.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000333.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000334.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000335.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000336.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000337.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000338.png", "low_idx": 65}, {"high_idx": 9, "image_name": "000000339.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000340.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000341.png", "low_idx": 66}, {"high_idx": 9, "image_name": "000000342.png", "low_idx": 66}, {"high_idx": 10, "image_name": "000000343.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000344.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000345.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000346.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000347.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000348.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000349.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000350.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000351.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000352.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000353.png", "low_idx": 67}, {"high_idx": 10, "image_name": "000000354.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000355.png", "low_idx": 68}, {"high_idx": 10, "image_name": "000000356.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000357.png", "low_idx": 69}, {"high_idx": 10, "image_name": "000000358.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000359.png", "low_idx": 70}, {"high_idx": 10, "image_name": "000000360.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000361.png", "low_idx": 71}, {"high_idx": 10, "image_name": "000000362.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000363.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000364.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000365.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000366.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000367.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000368.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000369.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000370.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000371.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000372.png", "low_idx": 72}, {"high_idx": 10, "image_name": "000000373.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000374.png", "low_idx": 73}, {"high_idx": 10, "image_name": "000000375.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000376.png", "low_idx": 74}, {"high_idx": 10, "image_name": "000000377.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000378.png", "low_idx": 75}, {"high_idx": 10, "image_name": "000000379.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000380.png", "low_idx": 76}, {"high_idx": 10, "image_name": "000000381.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000382.png", "low_idx": 77}, {"high_idx": 10, "image_name": "000000383.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000384.png", "low_idx": 78}, {"high_idx": 10, "image_name": "000000385.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000386.png", "low_idx": 79}, {"high_idx": 10, "image_name": "000000387.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000388.png", "low_idx": 80}, {"high_idx": 10, "image_name": "000000389.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000390.png", "low_idx": 81}, {"high_idx": 10, "image_name": "000000391.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000392.png", "low_idx": 82}, {"high_idx": 10, "image_name": "000000393.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000394.png", "low_idx": 83}, {"high_idx": 10, "image_name": "000000395.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000396.png", "low_idx": 84}, {"high_idx": 10, "image_name": "000000397.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000398.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000399.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000400.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000401.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000402.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000403.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000404.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000405.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000406.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000407.png", "low_idx": 85}, {"high_idx": 10, "image_name": "000000408.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000409.png", "low_idx": 86}, {"high_idx": 10, "image_name": "000000410.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000411.png", "low_idx": 87}, {"high_idx": 10, "image_name": "000000412.png", "low_idx": 88}, {"high_idx": 10, "image_name": "000000413.png", "low_idx": 88}, {"high_idx": 11, "image_name": "000000414.png", "low_idx": 89}, {"high_idx": 11, "image_name": "000000415.png", "low_idx": 89}, {"high_idx": 11, "image_name": "000000416.png", "low_idx": 89}, {"high_idx": 11, "image_name": "000000417.png", "low_idx": 89}, {"high_idx": 11, "image_name": "000000418.png", "low_idx": 89}, {"high_idx": 11, "image_name": "000000419.png", "low_idx": 89}, {"high_idx": 11, "image_name": "000000420.png", "low_idx": 89}, {"high_idx": 11, "image_name": "000000421.png", "low_idx": 89}, {"high_idx": 11, "image_name": "000000422.png", "low_idx": 89}, {"high_idx": 11, "image_name": "000000423.png", "low_idx": 89}, {"high_idx": 11, "image_name": "000000424.png", "low_idx": 89}, {"high_idx": 11, "image_name": "000000425.png", "low_idx": 89}, {"high_idx": 11, "image_name": "000000426.png", "low_idx": 89}, {"high_idx": 11, "image_name": "000000427.png", "low_idx": 89}, {"high_idx": 11, "image_name": "000000428.png", "low_idx": 89}], "pddl_params": {"mrecep_target": "", "object_sliced": true, "object_target": "Bread", "parent_target": "SideTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|0|1|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["knife"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Knife", [-0.2072958948, -0.2072958948, 4.40497828, 4.40497828, 3.6948052, 3.6948052]], "coordinateReceptacleObjectId": ["DiningTable", [-2.096, -2.096, 3.804, 3.804, 0.024, 0.024]], "forceVisible": true, "objectId": "Knife|-00.05|+00.92|+01.10"}}, {"discrete_action": {"action": "GotoLocation", "args": ["bread"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-4|1|0|45"}}, {"discrete_action": {"action": "SliceObject", "args": ["bread"]}, "high_idx": 3, "planner_action": {"action": "SliceObject", "coordinateObjectId": ["Bread", [-4.22544908, -4.22544908, 3.3757744, 3.3757744, 3.7763332, 3.7763332]], "forceVisible": true, "objectId": "Bread|-01.06|+00.94|+00.84"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-1|1|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["knife", "sidetable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Knife", [-0.2072958948, -0.2072958948, 4.40497828, 4.40497828, 3.6948052, 3.6948052]], "coordinateReceptacleObjectId": ["SideTable", [3.0924, 3.0924, 1.7924, 1.7924, 2.8004, 2.8004]], "forceVisible": true, "objectId": "Knife|-00.05|+00.92|+01.10", "receptacleObjectId": "SideTable|+00.77|+00.70|+00.45"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 6, "planner_action": {"action": "GotoLocation", "location": "loc|-4|1|0|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["bread"]}, "high_idx": 7, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Bread", [-4.22544908, -4.22544908, 3.3757744, 3.3757744, 3.7763332, 3.7763332]], "coordinateReceptacleObjectId": ["DiningTable", [-2.096, -2.096, 3.804, 3.804, 0.024, 0.024]], "forceVisible": true, "objectId": "Bread|-01.06|+00.94|+00.84|BreadSliced_3"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 8, "planner_action": {"action": "GotoLocation", "location": "loc|-8|-11|2|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["bread"]}, "high_idx": 9, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}}, {"discrete_action": {"action": "GotoLocation", "args": ["sidetable"]}, "high_idx": 10, "planner_action": {"action": "GotoLocation", "location": "loc|-1|1|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["bread", "sidetable"]}, "high_idx": 11, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Bread", [-4.22544908, -4.22544908, 3.3757744, 3.3757744, 3.7763332, 3.7763332]], "coordinateReceptacleObjectId": ["SideTable", [3.0924, 3.0924, 1.7924, 1.7924, 2.8004, 2.8004]], "forceVisible": true, "objectId": "Bread|-01.06|+00.94|+00.84|BreadSliced_3", "receptacleObjectId": "SideTable|+00.77|+00.70|+00.45"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 12, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Knife|-00.05|+00.92|+01.10"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [91, 106, 166, 115], "mask": [[31660, 4], [31897, 70], [32191, 76], [32492, 75], [32793, 74], [33094, 39], [33144, 9], [33159, 7], [33396, 37], [33461, 3], [33704, 29], [34016, 17], [34332, 1]], "point": [128, 109]}}, "high_idx": 1}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "SliceObject", "objectId": "Bread|-01.06|+00.94|+00.84"}, "discrete_action": {"action": "SliceObject", "args": {"bbox": [88, 132, 176, 177], "mask": [[39422, 18], [39717, 31], [40012, 40], [40309, 46], [40607, 50], [40905, 54], [41203, 58], [41501, 62], [41800, 65], [42099, 68], [42397, 71], [42696, 73], [42995, 76], [43294, 78], [43593, 80], [43892, 81], [44192, 82], [44491, 84], [44790, 85], [45090, 86], [45390, 86], [45689, 87], [45989, 88], [46289, 88], [46588, 89], [46888, 89], [47188, 89], [47488, 89], [47788, 89], [48088, 89], [48389, 88], [48689, 88], [48989, 87], [49289, 87], [49590, 86], [49891, 84], [50192, 82], [50493, 80], [50794, 78], [51096, 74], [51398, 70], [51701, 65], [52003, 60], [52305, 55], [52608, 48], [52924, 5]], "point": [132, 153]}}, "high_idx": 3}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Knife|-00.05|+00.92|+01.10", "placeStationary": true, "receptacleObjectId": "SideTable|+00.77|+00.70|+00.45"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [53, 105, 160, 237], "mask": [[31275, 11], [31301, 57], [31574, 12], [31601, 57], [31874, 12], [31901, 58], [32174, 13], [32201, 58], [32473, 14], [32502, 57], [32773, 14], [32801, 58], [33073, 14], [33101, 58], [33372, 16], [33400, 14], [33415, 44], [33672, 17], [33699, 13], [33716, 43], [33971, 20], [33996, 15], [34017, 42], [34271, 20], [34293, 18], [34317, 42], [34571, 20], [34592, 18], [34618, 41], [34870, 21], [34892, 18], [34918, 41], [35170, 21], [35192, 18], [35217, 42], [35470, 20], [35501, 10], [35515, 44], [35769, 18], [35810, 2], [35814, 45], [36069, 14], [36113, 46], [36369, 11], [36415, 44], [36668, 10], [36717, 42], [36968, 9], [37019, 40], [37268, 7], [37321, 38], [37567, 7], [37622, 37], [37867, 6], [37924, 35], [38167, 5], [38225, 34], [38466, 5], [38525, 34], [38766, 4], [38826, 33], [39066, 4], [39127, 32], [39365, 4], [39427, 33], [39665, 4], [39727, 33], [39964, 4], [40028, 32], [40264, 4], [40328, 32], [40564, 4], [40628, 32], [40863, 5], [40928, 32], [41163, 5], [41228, 32], [41463, 5], [41528, 32], [41762, 6], [41827, 33], [42062, 6], [42127, 33], [42362, 7], [42426, 34], [42661, 9], [42725, 35], [42961, 10], [43023, 37], [43261, 11], [43321, 39], [43560, 14], [43619, 41], [43860, 17], [43916, 44], [44160, 20], [44212, 48], [44459, 101], [44759, 101], [45058, 102], [45358, 102], [45658, 102], [45957, 103], [46257, 103], [46557, 103], [46856, 105], [47156, 105], [47456, 105], [47755, 106], [48055, 106], [48355, 106], [48654, 107], [48954, 107], [49254, 107], [49553, 107], [49854, 106], [50154, 106], [50454, 12], [50472, 88], [50755, 10], [50772, 88], [51055, 9], [51072, 88], [51356, 8], [51371, 89], [51656, 8], [51671, 89], [51956, 8], [51971, 89], [52257, 8], [52271, 89], [52557, 8], [52570, 90], [52858, 7], [52870, 90], [53158, 8], [53170, 90], [53458, 8], [53469, 91], [53759, 8], [53769, 91], [54059, 8], [54069, 91], [54360, 7], [54369, 91], [54660, 100], [54960, 100], [55261, 99], [55561, 99], [55861, 99], [56162, 97], [56462, 97], [56763, 96], [57063, 96], [57363, 8], [57380, 51], [57437, 2], [57448, 11], [57664, 7], [57680, 51], [57737, 2], [57748, 11], [57964, 8], [57980, 51], [58037, 1], [58048, 11], [58265, 7], [58280, 51], [58337, 1], [58349, 10], [58565, 7], [58580, 51], [58637, 1], [58648, 11], [58865, 8], [58879, 52], [58936, 2], [58948, 11], [59166, 7], [59179, 52], [59236, 2], [59248, 11], [59466, 7], [59479, 52], [59536, 3], [59548, 11], [59767, 7], [59779, 54], [59834, 7], [59842, 1], [59848, 11], [60067, 7], [60078, 81], [60367, 8], [60378, 81], [60668, 7], [60678, 81], [60968, 7], [60978, 81], [61269, 7], [61277, 82], [61569, 7], [61577, 82], [61869, 7], [61877, 82], [62170, 89], [62470, 89], [62771, 88], [63071, 88], [63371, 87], [63672, 86], [63972, 86], [64273, 85], [64573, 7], [64654, 4], [64873, 7], [64954, 4], [65174, 6], [65254, 4], [65474, 7], [65554, 4], [65775, 6], [65854, 4], [66075, 6], [66154, 4], [66375, 7], [66454, 4], [66676, 6], [66754, 4], [66976, 6], [67054, 4], [67277, 6], [67354, 4], [67577, 6], [67654, 4], [67877, 7], [67954, 4], [68178, 6], [68254, 4], [68478, 6], [68554, 4], [68779, 6], [68854, 4], [69079, 6], [69154, 4], [69379, 6], [69454, 4], [69680, 4], [69754, 4], [69980, 4], [70054, 4], [70281, 3], [70354, 3], [70584, 1], [70883, 2]], "point": [106, 170]}}, "high_idx": 5}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 6}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 6}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.06|+00.94|+00.84|BreadSliced_3"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [149, 133, 155, 176], "mask": [[39750, 3], [40049, 6], [40349, 7], [40649, 7], [40949, 7], [41249, 7], [41549, 7], [41849, 7], [42149, 7], [42449, 7], [42749, 7], [43049, 7], [43349, 7], [43649, 7], [43949, 7], [44249, 7], [44549, 7], [44849, 7], [45149, 7], [45449, 7], [45749, 7], [46049, 7], [46349, 7], [46649, 7], [46949, 7], [47249, 7], [47549, 7], [47849, 7], [48149, 7], [48449, 7], [48749, 7], [49049, 7], [49349, 7], [49649, 7], [49949, 7], [50249, 7], [50549, 7], [50849, 7], [51149, 7], [51449, 7], [51749, 7], [52049, 7], [52349, 7], [52649, 6]], "point": [152, 153]}}, "high_idx": 7}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 8}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 8}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 163], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 9}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.06|+00.94|+00.84|BreadSliced_3", "placeStationary": true, "receptacleObjectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 163], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 188], [31250, 188], [31550, 188], [31849, 189], [32149, 189], [32448, 190], [32748, 191], [33047, 192], [33347, 192], [33646, 193], [33946, 192], [34245, 193], [34545, 193], [34844, 194], [35144, 194], [35443, 194], [35743, 194], [36043, 194], [36342, 195], [36642, 194], [36941, 195], [37241, 195], [37540, 195], [37840, 195], [38139, 196], [38439, 195], [38738, 196], [39038, 196], [39337, 197], [39637, 196], [39936, 197], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 163], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 188], [31250, 188], [31550, 188], [31849, 189], [32149, 189], [32448, 190], [32748, 191], [33047, 192], [33347, 192], [33646, 97], [33754, 85], [33946, 95], [34058, 80], [34245, 94], [34361, 77], [34545, 92], [34663, 75], [34844, 92], [34965, 73], [35144, 91], [35266, 72], [35443, 92], [35566, 71], [35743, 91], [35867, 70], [36043, 91], [36168, 69], [36342, 91], [36468, 69], [36642, 91], [36768, 68], [36941, 92], [37068, 68], [37241, 91], [37368, 68], [37540, 92], [37669, 66], [37840, 92], [37968, 67], [38139, 93], [38268, 67], [38439, 94], [38568, 66], [38738, 95], [38868, 66], [39038, 96], [39167, 67], [39337, 97], [39467, 67], [39637, 98], [39765, 68], [39936, 102], [40063, 70], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 163], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 9}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-8.052, -8.052, -14.756, -14.756, 2.761099816, 2.761099816]], "forceVisible": true, "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 163], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 9}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [64, 42, 242, 163], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 163], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25865, 177], [26166, 175], [26466, 175], [26766, 175], [27066, 175], [27367, 173], [27667, 173], [27967, 173], [28267, 172], [28567, 172], [28867, 172], [29167, 172], [29468, 170], [29768, 170], [30068, 170], [30368, 170], [30668, 170], [30968, 170], [31268, 170], [31568, 170], [31868, 170], [32168, 170], [32468, 170], [32768, 171], [33068, 171], [33368, 171], [33668, 171], [33968, 170], [34268, 170], [34568, 170], [34868, 170], [35168, 170], [35469, 168], [35769, 168], [36069, 168], [36369, 168], [36670, 166], [36970, 166], [37270, 166], [37571, 164], [37871, 164], [38171, 164], [38471, 163], [38772, 162], [39072, 162], [39372, 162], [39672, 161], [39973, 160], [40273, 160], [40573, 159], [40874, 158], [41174, 158], [41474, 157], [41774, 157], [42075, 156], [42375, 156], [42675, 155], [42976, 154], [43276, 154], [43576, 153], [43876, 153], [44177, 152], [44477, 151], [44777, 151], [45078, 150], [45378, 150], [45678, 149], [45978, 149], [46279, 143], [46583, 140], [46884, 138], [47184, 138], [47484, 138], [47785, 136], [48085, 136], [48385, 136], [48790, 8]], "point": [153, 101]}}, "high_idx": 9}, {"api_action": {"action": "PickupObject", "objectId": "Bread|-01.06|+00.94|+00.84|BreadSliced_3"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [132, 113, 168, 134], "mask": [[33743, 11], [34041, 17], [34339, 22], [34637, 26], [34936, 29], [35235, 31], [35535, 31], [35834, 33], [36134, 34], [36433, 35], [36733, 35], [37033, 35], [37332, 36], [37632, 37], [37932, 36], [38232, 36], [38533, 35], [38833, 35], [39134, 33], [39434, 33], [39735, 30], [40038, 25]], "point": [150, 122]}}, "high_idx": 9}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-02.01|+00.69|-03.69"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 42, 242, 253], "mask": [[12383, 140], [12682, 141], [12982, 142], [13281, 143], [13581, 144], [13880, 145], [14180, 146], [14480, 146], [14779, 148], [15079, 148], [15378, 150], [15678, 150], [15977, 152], [16277, 152], [16576, 154], [16876, 154], [17176, 155], [17475, 156], [17775, 157], [18074, 158], [18374, 159], [18673, 160], [18973, 161], [19272, 162], [19572, 163], [19872, 163], [20171, 164], [20471, 165], [20770, 166], [21070, 167], [21369, 168], [21669, 169], [21968, 170], [22268, 171], [22568, 171], [22867, 173], [23167, 173], [23466, 175], [23766, 175], [24065, 177], [24365, 177], [24664, 179], [24965, 178], [25265, 177], [25565, 177], [25861, 181], [26160, 181], [26459, 182], [26759, 182], [27059, 182], [27358, 182], [27658, 182], [27957, 183], [28257, 182], [28556, 183], [28854, 185], [29154, 185], [29453, 185], [29752, 186], [30052, 186], [30351, 187], [30651, 187], [30950, 188], [31250, 188], [31550, 188], [31849, 189], [32149, 189], [32448, 190], [32748, 191], [33047, 192], [33347, 192], [33646, 193], [33946, 192], [34245, 193], [34545, 193], [34844, 194], [35144, 194], [35443, 194], [35743, 194], [36043, 194], [36342, 195], [36642, 194], [36941, 195], [37241, 195], [37540, 195], [37840, 195], [38139, 196], [38439, 195], [38738, 196], [39038, 196], [39337, 197], [39637, 196], [39936, 197], [40236, 197], [40536, 196], [40835, 197], [41135, 197], [41434, 197], [41734, 197], [42033, 198], [42333, 198], [42632, 198], [42932, 198], [43231, 199], [43531, 198], [43830, 199], [44130, 199], [44429, 199], [44729, 199], [45029, 199], [45328, 200], [45628, 199], [45927, 200], [46227, 195], [46526, 197], [46826, 196], [47125, 197], [47425, 197], [47724, 60], [47785, 136], [48024, 60], [48085, 136], [48323, 61], [48385, 136], [48623, 61], [48922, 61], [49222, 61], [49522, 61], [49821, 62], [50121, 61], [50420, 62], [50720, 62], [51019, 63], [51319, 62], [51618, 63], [51918, 63], [52217, 64], [52517, 63], [52816, 64], [53116, 64], [53415, 65], [53715, 64], [54015, 64], [54314, 65], [54614, 65], [54913, 66], [55213, 65], [55512, 66], [55812, 66], [56111, 67], [56411, 66], [56710, 67], [57000, 3], [57010, 67], [57300, 3], [57309, 68], [57600, 3], [57609, 67], [57900, 4], [57908, 68], [58200, 76], [58500, 76], [58800, 75], [59100, 75], [59400, 75], [59700, 75], [60000, 74], [60300, 74], [60600, 74], [60901, 73], [61203, 70], [61503, 70], [61802, 71], [62102, 71], [62400, 1], [62402, 70], [62700, 72], [63000, 72], [63300, 72], [63600, 71], [63900, 71], [64200, 71], [64500, 71], [64800, 70], [65100, 70], [65400, 70], [65700, 70], [66000, 69], [66300, 69], [66600, 69], [66900, 69], [67200, 69], [67500, 68], [67800, 68], [68101, 67], [68402, 66], [68703, 64], [69004, 63], [69304, 63], [69605, 62], [69906, 60], [70207, 59], [70508, 58], [70809, 57], [71109, 56], [71410, 55], [71711, 54], [72012, 53], [72313, 51], [72613, 51], [72914, 50], [73215, 49], [73517, 46], [73819, 44], [74123, 42], [74427, 8], [74436, 30], [74730, 3], [74739, 28], [75043, 19], [75063, 3], [75345, 17], [75646, 16]], "point": [121, 146]}}, "high_idx": 9}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 10}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Bread|-01.06|+00.94|+00.84|BreadSliced_3", "placeStationary": true, "receptacleObjectId": "SideTable|+00.77|+00.70|+00.45"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [53, 105, 160, 237], "mask": [[31275, 11], [31301, 57], [31574, 12], [31601, 57], [31874, 12], [31901, 58], [32174, 13], [32201, 58], [32473, 14], [32502, 57], [32773, 14], [32802, 57], [33073, 14], [33102, 57], [33372, 16], [33402, 12], [33415, 44], [33672, 17], [33702, 10], [33716, 43], [33971, 20], [33997, 2], [34002, 9], [34017, 42], [34271, 20], [34292, 7], [34303, 8], [34317, 42], [34571, 20], [34592, 8], [34603, 7], [34618, 41], [34870, 21], [34892, 8], [34903, 7], [34918, 41], [35170, 20], [35192, 8], [35203, 7], [35217, 42], [35470, 20], [35503, 8], [35515, 44], [35769, 18], [35809, 3], [35814, 45], [36069, 14], [36113, 46], [36369, 11], [36415, 44], [36668, 10], [36717, 42], [36968, 9], [37019, 40], [37268, 7], [37321, 38], [37567, 7], [37622, 37], [37867, 6], [37924, 35], [38167, 5], [38225, 34], [38466, 5], [38525, 34], [38766, 4], [38826, 33], [39066, 4], [39127, 32], [39365, 4], [39427, 33], [39665, 4], [39727, 33], [39964, 4], [40028, 32], [40264, 4], [40328, 32], [40564, 4], [40628, 32], [40863, 5], [40928, 32], [41163, 5], [41228, 32], [41463, 5], [41527, 33], [41762, 6], [41827, 33], [42062, 6], [42127, 33], [42362, 7], [42426, 34], [42661, 9], [42725, 35], [42961, 9], [43023, 37], [43261, 11], [43321, 39], [43560, 14], [43619, 41], [43860, 17], [43916, 44], [44160, 19], [44212, 48], [44459, 47], [44507, 53], [44759, 47], [44808, 52], [45058, 49], [45108, 52], [45358, 49], [45408, 52], [45658, 49], [45708, 52], [45957, 50], [46008, 52], [46257, 103], [46557, 103], [46856, 105], [47156, 105], [47456, 105], [47755, 106], [48055, 106], [48355, 106], [48654, 107], [48954, 107], [49254, 107], [49553, 107], [49854, 106], [50154, 106], [50454, 12], [50472, 88], [50755, 10], [50772, 88], [51055, 9], [51072, 88], [51356, 8], [51371, 89], [51656, 8], [51671, 89], [51956, 8], [51971, 89], [52257, 8], [52271, 89], [52557, 8], [52570, 90], [52858, 7], [52870, 90], [53158, 8], [53170, 90], [53458, 8], [53469, 91], [53759, 8], [53769, 91], [54059, 8], [54069, 91], [54360, 7], [54369, 91], [54660, 100], [54960, 100], [55261, 99], [55561, 99], [55861, 99], [56162, 97], [56462, 97], [56763, 96], [57063, 96], [57363, 8], [57380, 51], [57437, 2], [57448, 11], [57664, 7], [57680, 51], [57737, 2], [57748, 11], [57964, 8], [57980, 51], [58037, 1], [58048, 11], [58265, 7], [58280, 51], [58337, 1], [58349, 10], [58565, 7], [58580, 51], [58637, 1], [58648, 11], [58865, 8], [58879, 52], [58936, 2], [58948, 11], [59166, 7], [59179, 52], [59236, 2], [59248, 11], [59466, 7], [59479, 52], [59536, 3], [59548, 11], [59767, 7], [59779, 54], [59834, 7], [59842, 1], [59848, 11], [60067, 7], [60078, 81], [60367, 8], [60378, 81], [60668, 7], [60678, 81], [60968, 7], [60978, 81], [61269, 7], [61277, 82], [61569, 7], [61577, 82], [61869, 7], [61877, 82], [62170, 89], [62470, 89], [62771, 88], [63071, 88], [63371, 87], [63672, 86], [63972, 86], [64273, 85], [64573, 7], [64654, 4], [64873, 7], [64954, 4], [65174, 6], [65254, 4], [65474, 7], [65554, 4], [65775, 6], [65854, 4], [66075, 6], [66154, 4], [66375, 7], [66454, 4], [66676, 6], [66754, 4], [66976, 6], [67054, 4], [67277, 6], [67354, 4], [67577, 6], [67654, 4], [67877, 7], [67954, 4], [68178, 6], [68254, 4], [68478, 6], [68554, 4], [68779, 6], [68854, 4], [69079, 6], [69154, 4], [69379, 6], [69454, 4], [69680, 4], [69754, 4], [69980, 4], [70054, 4], [70281, 3], [70354, 3], [70584, 1], [70883, 2]], "point": [106, 170]}}, "high_idx": 11}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan21", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": 0.75, "y": 0.869696259, "z": -3.25}, "object_poses": [{"objectName": "Potato_027e0a30", "position": {"x": -1.71959925, "y": 0.734842539, "z": -0.983721733}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_93a81026", "position": {"x": 0.1490823, "y": 0.901750147, "z": 0.9297057}, "rotation": {"x": 0.0, "y": 90.00033, "z": 0.0}}, {"objectName": "Spoon_93a81026", "position": {"x": 0.841818154, "y": 0.6665257, "z": 0.4481}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_cb0de209", "position": {"x": 0.6769987, "y": 0.0143482685, "z": 0.011385709}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_cb0de209", "position": {"x": -2.13780355, "y": 0.700419068, "z": -1.19986916}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_33d4fba0", "position": {"x": 0.671831965, "y": 0.782948434, "z": 0.00290554762}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_33d4fba0", "position": {"x": -0.0518239737, "y": 0.9237013, "z": 1.10124457}, "rotation": {"x": 0.0, "y": 0.000327849062, "z": 0.0}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": -0.25273174, "y": 0.949009538, "z": 1.0154767}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": -1.97052181, "y": 0.7501781, "z": -1.19986916}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_886fcaf3", "position": {"x": 0.9493945, "y": 1.46013677, "z": -0.146107078}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_886fcaf3", "position": {"x": 0.149083853, "y": 0.896926939, "z": 1.1870122}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Bread_42314d78", "position": {"x": -1.05636227, "y": 0.9440833, "z": 0.8439436}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Bread_42314d78", "position": {"x": 0.721681535, "y": 0.7080625, "z": 0.508934}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_0ad3089c", "position": {"x": 0.746874452, "y": 1.24533343, "z": -1.95800006}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Lettuce_0ad3089c", "position": {"x": 0.7856681, "y": 0.7519335, "z": -2.035625}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Mug_288ff1e3", "position": {"x": 0.9619548, "y": 0.6596, "z": 0.569767952}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_288ff1e3", "position": {"x": 0.669286966, "y": 1.36962759, "z": -1.88037479}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Apple_093f2b24", "position": {"x": 0.877196848, "y": 0.93709, "z": -0.314791471}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_093f2b24", "position": {"x": 0.7080801, "y": 0.519417763, "z": -2.11324978}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Pot_cde1951c", "position": {"x": -2.071, "y": 0.9274094, "z": 0.772}, "rotation": {"x": 0.0, "y": 266.284332, "z": 0.0}}, {"objectName": "Plate_2b33f66e", "position": {"x": -2.02399969, "y": 0.69248, "z": -1.44700074}, "rotation": {"x": 1.06787948e-05, "y": -0.000256137952, "z": -1.99874139e-05}}, {"objectName": "Fork_34f12e87", "position": {"x": 0.7204354, "y": 0.760416448, "z": -0.061030075}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_d7803c74", "position": {"x": -1.05636072, "y": 0.8999009, "z": 1.10125017}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Apple_093f2b24", "position": {"x": -1.29754388, "y": 0.10573864, "z": -3.57439852}, "rotation": {"x": 0.0, "y": 269.999878, "z": 0.0}}, {"objectName": "Pan_63693e0a", "position": {"x": -1.555, "y": 0.9306, "z": 1.049}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_c79ff911", "position": {"x": 0.6308474, "y": 0.8091614, "z": -0.625526845}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Spatula_8142ce7b", "position": {"x": -1.62047052, "y": 0.7152748, "z": -3.445499}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Potato_027e0a30", "position": {"x": 0.9509703, "y": 0.916653156, "z": -0.10800001}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_cb0de209", "position": {"x": 0.842995167, "y": 0.0143482685, "z": -0.184701681}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_886fcaf3", "position": {"x": -0.8554549, "y": 0.896926939, "z": 0.8439424}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "PepperShaker_87875872", "position": {"x": 1.04054928, "y": 1.46013677, "z": 0.5966235}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_0ad5c562", "position": {"x": 0.754, "y": 0.7553387, "z": -0.743}, "rotation": {"x": 0.0, "y": 44.9999161, "z": 0.0}}, {"objectName": "DishSponge_11e4138d", "position": {"x": 0.841601849, "y": 0.7721204, "z": -0.6255269}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Bread_42314d78", "position": {"x": 0.149081826, "y": 0.9440833, "z": 0.843936741}, "rotation": {"x": 0.0, "y": 270.000336, "z": 0.0}}, {"objectName": "Lettuce_0ad3089c", "position": {"x": 0.7151491, "y": 0.859162, "z": -0.7356198}, "rotation": {"x": 7.58303337e-22, "y": 270.0, "z": -1.40334191e-14}}, {"objectName": "Tomato_5a2cfc73", "position": {"x": 0.7468738, "y": 1.20711637, "z": -2.190875}, "rotation": {"x": 0.0, "y": 270.000183, "z": 0.0}}, {"objectName": "Spoon_93a81026", "position": {"x": 0.901886463, "y": 0.6657294, "z": 0.569767952}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_33d4fba0", "position": {"x": -0.8554545, "y": 0.923701346, "z": 0.929711342}, "rotation": {"x": 0.0, "y": 90.00033, "z": 0.0}}, {"objectName": "Mug_288ff1e3", "position": {"x": -1.80324006, "y": 0.6967894, "z": -0.983721733}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_737b77b7", "position": {"x": -2.03849554, "y": 0.6917894, "z": -1.00353038}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}], "object_toggles": [], "random_seed": 1455222156, "scene_num": 21}, "task_id": "trial_T20190907_211241_340986", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A1S1K7134S2VUC_3K4J6M3CXHAIVK1FU9SJTPWIU5NAGE", "high_descs": ["Turn right, move to the work table, right of the toaster.", "Pick up the knife from the table.", "Turn left, move to the work table, left of the toaster.", "Slice the bread with the knife.", "Turn right, move to the shelves at the end of the counter.", "Put the knife on the black shelves.", "Turn left, move to the work table, left of the toaster.", "Pick up a slice of bread from the work table.", "Turn left, move to the small table, in front of the microwave.", "Put the slice of bread into the microwave, cook it, pick it back up.", "Turn left, move to the shelves at the end of the counter.", "Put the bread slice on the black shelves."], "task_desc": "Slice and microwave a slice of bread.", "votes": [1, 1]}, {"assignment_id": "AFU00NU09CFXE_3U5JL4WY5NQEFHGOAIIW48AD5NJ4XP", "high_descs": ["Move to the white table with the toaster.", "Pick up the sharp knife from the table.", "Carry the knife to the left end of the table where there is a loaf of bread.", "Use the knife to slice half the loaf of bread.", "Turn and carry the knife to the small black table in the corner.", "Place the knife in front of the loaf of bread on the black table.", "Move back to the loaf of bread you just sliced on the white table.", "Pick up a slice from the center of the loaf.", "Carry the slice of bread to the microwave on the round table in the corner.", "Open the microwave, place the slice of bread inside, and turn it on and then remove, once heated.", "Carry the slice of bread to the small black table in the other corner where you left the knife.", "Place the heated slice on the black table to the right of the knife."], "task_desc": "Move a heated slice of bread to a small black table in the corner.", "votes": [1, 1]}, {"assignment_id": "A1CY7IOJ9YH136_3WRFBPLXRD5OLRJG8YQ4W1R9P983NE", "high_descs": ["turn right, go to white table with toaster", "pick up yellow handle knife from table", "go to the loaf of bread to the left of salt shaker", "slice bread on counter to the left of salt shaker", "turn right, go to black table next to counter with loaf of bread", "place knife across loaf of bread on black table", "turn left, go to loaf of bread on white table", "pick up slice of bread from counter", "turn around, go to microwave ", "open microwave, place bread on plate in microwave, close microwave door, microwave bread slice, open microwave, remove bread slice, close microwave", "turn around, go the black table at end of counter on the right", "place bread slice on black table to the right of loaf of bread"], "task_desc": "place microwaved bread slice next to loaf of bread on black table", "votes": [1, 1]}]}}