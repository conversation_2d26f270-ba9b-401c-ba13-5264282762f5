
(define (problem plan_trial_T20190909_081354_457269)
(:domain alfred)
(:objects
agent1 - agent
<PERSON><PERSON>haker - object
        HousePlant - object
        Candle - object
        SprayBottle - object
        Bowl - object
        Window - object
        CD - object
        Egg - object
        Glassbottle - object
        Sink - object
        Pillow - object
        Spoon - object
        SoapBottle - object
        TeddyBear - object
        ButterKnife - object
        Cup - object
        Plate - object
        RemoteControl - object
        Tomato - object
        Statue - object
        HandTowel - object
        Knife - object
        StoveKnob - object
        LightSwitch - object
        Pen - object
        Painting - object
        DishSponge - object
        Vase - object
        Mug - object
        ToiletPaper - object
        Box - object
        Mirror - object
        Ladle - object
        Fork - object
        Blinds - object
        Footstool - object
        KeyChain - object
        Cloth - object
        Laptop - object
        TissueBox - object
        PepperShaker - object
        FloorLamp - object
        WateringCan - object
        Apple - object
        Pan - object
        PaperTowel - object
        PaperTowelRoll - object
        Newspaper - object
        Television - object
        Chair - object
        CellPhone - object
        CreditCard - object
        Lettuce - object
        BasketBall - object
        Potato - object
        Curtains - object
        Boots - object
        Pencil - object
        AlarmClock - object
        ToiletPaperRoll - object
        Spatula - object
        Book - object
        Bread - object
        SoapBar - object
        Watch - object
        DeskLamp - object
        Kettle - object
        Pot - object
        ScrubBrush - object
        WineBottle - object
        ShowerDoor - object
        Bathtub - object
        LaundryHamperLid - object
        ShowerGlass - object
        Poster - object
        TennisRacket - object
        BaseballBat - object
        Towel - object
        Plunger - object
SaltShakerType - otype
        HousePlantType - otype
        CandleType - otype
        SprayBottleType - otype
        BowlType - otype
        WindowType - otype
        CDType - otype
        EggType - otype
        GlassbottleType - otype
        SinkType - otype
        PillowType - otype
        SpoonType - otype
        SoapBottleType - otype
        TeddyBearType - otype
        ButterKnifeType - otype
        CupType - otype
        PlateType - otype
        RemoteControlType - otype
        TomatoType - otype
        StatueType - otype
        HandTowelType - otype
        KnifeType - otype
        StoveKnobType - otype
        LightSwitchType - otype
        PenType - otype
        PaintingType - otype
        DishSpongeType - otype
        VaseType - otype
        MugType - otype
        ToiletPaperType - otype
        BoxType - otype
        MirrorType - otype
        LadleType - otype
        ForkType - otype
        BlindsType - otype
        FootstoolType - otype
        KeyChainType - otype
        ClothType - otype
        LaptopType - otype
        TissueBoxType - otype
        PepperShakerType - otype
        FloorLampType - otype
        WateringCanType - otype
        AppleType - otype
        PanType - otype
        PaperTowelType - otype
        PaperTowelRollType - otype
        NewspaperType - otype
        TelevisionType - otype
        ChairType - otype
        CellPhoneType - otype
        CreditCardType - otype
        LettuceType - otype
        BasketBallType - otype
        PotatoType - otype
        CurtainsType - otype
        BootsType - otype
        PencilType - otype
        AlarmClockType - otype
        ToiletPaperRollType - otype
        SpatulaType - otype
        BookType - otype
        BreadType - otype
        SoapBarType - otype
        WatchType - otype
        DeskLampType - otype
        KettleType - otype
        PotType - otype
        ScrubBrushType - otype
        WineBottleType - otype
        ShowerDoorType - otype
        BathtubType - otype
        LaundryHamperLidType - otype
        ShowerGlassType - otype
        PosterType - otype
        TennisRacketType - otype
        BaseballBatType - otype
        TowelType - otype
        PlungerType - otype
SafeType - rtype
        DrawerType - rtype
        CoffeeMachineType - rtype
        HandTowelHolderType - rtype
        SinkBasinType - rtype
        DresserType - rtype
        LaundryHamperType - rtype
        TVStandType - rtype
        BathtubBasinType - rtype
        CabinetType - rtype
        FridgeType - rtype
        DeskType - rtype
        ToiletType - rtype
        CartType - rtype
        SideTableType - rtype
        SofaType - rtype
        CoffeeTableType - rtype
        DiningTableType - rtype
        CounterTopType - rtype
        GarbageCanType - rtype
        ArmChairType - rtype
        ShelfType - rtype
        MicrowaveType - rtype
        ToasterType - rtype
        BedType - rtype
        PaintingHangerType - rtype
        TowelHolderType - rtype
        ToiletPaperHangerType - rtype
        StoveBurnerType - rtype
        OttomanType - rtype


        Book_bar__minus_02_dot_57_bar__plus_00_dot_59_bar__minus_02_dot_66 - object
        Book_bar__minus_05_dot_32_bar__plus_00_dot_48_bar__minus_05_dot_09 - object
        Box_bar__minus_02_dot_36_bar__plus_00_dot_26_bar__minus_01_dot_65 - object
        CreditCard_bar__minus_02_dot_93_bar__plus_00_dot_70_bar__minus_05_dot_24 - object
        CreditCard_bar__minus_03_dot_91_bar__plus_00_dot_08_bar__minus_05_dot_03 - object
        CreditCard_bar__minus_04_dot_38_bar__plus_00_dot_33_bar__minus_01_dot_46 - object
        FloorLamp_bar__minus_04_dot_82_bar__plus_00_dot_00_bar__minus_05_dot_12 - object
        HousePlant_bar__minus_05_dot_63_bar__plus_00_dot_48_bar__minus_05_dot_13 - object
        KeyChain_bar__minus_02_dot_75_bar__plus_00_dot_08_bar__minus_05_dot_05 - object
        KeyChain_bar__minus_02_dot_79_bar__plus_00_dot_59_bar__minus_02_dot_66 - object
        KeyChain_bar__minus_04_dot_36_bar__plus_00_dot_71_bar__minus_05_dot_05 - object
        Laptop_bar__minus_03_dot_22_bar__plus_00_dot_70_bar__minus_05_dot_09 - object
        LightSwitch_bar__minus_05_dot_76_bar__plus_01_dot_33_bar__plus_00_dot_01 - object
        Mirror_bar__minus_03_dot_40_bar__plus_02_dot_03_bar__plus_00_dot_00 - object
        Newspaper_bar__minus_04_dot_27_bar__plus_00_dot_34_bar__minus_02_dot_16 - object
        Painting_bar__minus_03_dot_37_bar__plus_01_dot_80_bar__minus_05_dot_39 - object
        Pen_bar__minus_02_dot_26_bar__plus_00_dot_59_bar__minus_02_dot_99 - object
        Pillow_bar__minus_04_dot_19_bar__plus_00_dot_44_bar__minus_03_dot_45 - object
        Pillow_bar__minus_04_dot_28_bar__plus_00_dot_45_bar__minus_01_dot_74 - object
        RemoteControl_bar__minus_02_dot_15_bar__plus_00_dot_59_bar__minus_02_dot_00 - object
        RemoteControl_bar__minus_02_dot_79_bar__plus_00_dot_59_bar__minus_02_dot_49 - object
        Statue_bar__minus_02_dot_36_bar__plus_00_dot_59_bar__minus_02_dot_49 - object
        Statue_bar__minus_02_dot_65_bar__plus_00_dot_71_bar__minus_05_dot_14 - object
        Television_bar__minus_00_dot_61_bar__plus_01_dot_67_bar__minus_02_dot_60 - object
        Vase_bar__minus_02_dot_15_bar__plus_00_dot_59_bar__minus_02_dot_33 - object
        Vase_bar__minus_02_dot_57_bar__plus_00_dot_59_bar__minus_02_dot_16 - object
        Vase_bar__minus_04_dot_22_bar__plus_00_dot_70_bar__minus_05_dot_05 - object
        Vase_bar__minus_04_dot_48_bar__plus_00_dot_70_bar__minus_05_dot_08 - object
        Watch_bar__minus_02_dot_68_bar__plus_00_dot_59_bar__minus_02_dot_33 - object
        Watch_bar__minus_03_dot_50_bar__plus_00_dot_70_bar__minus_05_dot_14 - object
        WateringCan_bar__minus_07_dot_99_bar__plus_00_dot_00_bar__minus_04_dot_63 - object
        Window_bar__minus_08_dot_24_bar__plus_01_dot_41_bar__minus_01_dot_51 - object
        Window_bar__minus_08_dot_24_bar__plus_01_dot_41_bar__minus_04_dot_08 - object
        ArmChair_bar__minus_04_dot_39_bar__plus_00_dot_00_bar__minus_03_dot_55 - receptacle
        CoffeeTable_bar__minus_02_dot_47_bar__plus_00_dot_00_bar__minus_02_dot_49 - receptacle
        Drawer_bar__minus_02_dot_98_bar__plus_00_dot_17_bar__minus_05_dot_01 - receptacle
        Drawer_bar__minus_02_dot_98_bar__plus_00_dot_48_bar__minus_05_dot_01 - receptacle
        Drawer_bar__minus_04_dot_02_bar__plus_00_dot_17_bar__minus_05_dot_01 - receptacle
        Drawer_bar__minus_04_dot_02_bar__plus_00_dot_48_bar__minus_05_dot_01 - receptacle
        Dresser_bar__minus_03_dot_51_bar__plus_00_dot_00_bar__minus_05_dot_10 - receptacle
        GarbageCan_bar__minus_00_dot_97_bar__plus_00_dot_00_bar__minus_05_dot_06 - receptacle
        SideTable_bar__minus_02_dot_02_bar__minus_00_dot_01_bar__minus_05_dot_08 - receptacle
        SideTable_bar__minus_05_dot_43_bar__minus_00_dot_01_bar__minus_05_dot_12 - receptacle
        Sofa_bar__minus_04_dot_36_bar__plus_00_dot_00_bar__minus_02_dot_09 - receptacle
        loc_bar__minus_31_bar__minus_6_bar_3_bar_30 - location
        loc_bar__minus_7_bar__minus_18_bar_3_bar_45 - location
        loc_bar__minus_16_bar__minus_18_bar_1_bar_45 - location
        loc_bar__minus_18_bar__minus_18_bar_2_bar_60 - location
        loc_bar__minus_31_bar__minus_16_bar_3_bar_30 - location
        loc_bar__minus_8_bar__minus_6_bar_3_bar_60 - location
        loc_bar__minus_6_bar__minus_19_bar_1_bar_60 - location
        loc_bar__minus_31_bar__minus_17_bar_2_bar_60 - location
        loc_bar__minus_13_bar__minus_15_bar_3_bar_60 - location
        loc_bar__minus_23_bar__minus_17_bar_2_bar_60 - location
        loc_bar__minus_13_bar__minus_17_bar_2_bar_60 - location
        loc_bar__minus_13_bar__minus_8_bar_3_bar_60 - location
        loc_bar__minus_14_bar__minus_3_bar_0_bar__minus_30 - location
        loc_bar__minus_16_bar__minus_18_bar_2_bar_60 - location
        loc_bar__minus_5_bar__minus_10_bar_1_bar_0 - location
        loc_bar__minus_5_bar__minus_10_bar_3_bar_60 - location
        loc_bar__minus_10_bar__minus_17_bar_2_bar_60 - location
        loc_bar__minus_12_bar__minus_18_bar_3_bar_45 - location
        loc_bar__minus_13_bar__minus_18_bar_2_bar__minus_15 - location
        loc_bar__minus_23_bar__minus_2_bar_0_bar_30 - location
        loc_bar__minus_29_bar__minus_17_bar_1_bar_30 - location
        )
    

(:init


        (receptacleType ArmChair_bar__minus_04_dot_39_bar__plus_00_dot_00_bar__minus_03_dot_55 ArmChairType)
        (receptacleType GarbageCan_bar__minus_00_dot_97_bar__plus_00_dot_00_bar__minus_05_dot_06 GarbageCanType)
        (receptacleType Drawer_bar__minus_02_dot_98_bar__plus_00_dot_48_bar__minus_05_dot_01 DrawerType)
        (receptacleType SideTable_bar__minus_02_dot_02_bar__minus_00_dot_01_bar__minus_05_dot_08 SideTableType)
        (receptacleType SideTable_bar__minus_05_dot_43_bar__minus_00_dot_01_bar__minus_05_dot_12 SideTableType)
        (receptacleType Drawer_bar__minus_04_dot_02_bar__plus_00_dot_48_bar__minus_05_dot_01 DrawerType)
        (receptacleType Dresser_bar__minus_03_dot_51_bar__plus_00_dot_00_bar__minus_05_dot_10 DresserType)
        (receptacleType Drawer_bar__minus_02_dot_98_bar__plus_00_dot_17_bar__minus_05_dot_01 DrawerType)
        (receptacleType CoffeeTable_bar__minus_02_dot_47_bar__plus_00_dot_00_bar__minus_02_dot_49 CoffeeTableType)
        (receptacleType Drawer_bar__minus_04_dot_02_bar__plus_00_dot_17_bar__minus_05_dot_01 DrawerType)
        (receptacleType Sofa_bar__minus_04_dot_36_bar__plus_00_dot_00_bar__minus_02_dot_09 SofaType)
        (objectType LightSwitch_bar__minus_05_dot_76_bar__plus_01_dot_33_bar__plus_00_dot_01 LightSwitchType)
        (objectType Newspaper_bar__minus_04_dot_27_bar__plus_00_dot_34_bar__minus_02_dot_16 NewspaperType)
        (objectType RemoteControl_bar__minus_02_dot_15_bar__plus_00_dot_59_bar__minus_02_dot_00 RemoteControlType)
        (objectType WateringCan_bar__minus_07_dot_99_bar__plus_00_dot_00_bar__minus_04_dot_63 WateringCanType)
        (objectType Statue_bar__minus_02_dot_65_bar__plus_00_dot_71_bar__minus_05_dot_14 StatueType)
        (objectType Book_bar__minus_02_dot_57_bar__plus_00_dot_59_bar__minus_02_dot_66 BookType)
        (objectType Vase_bar__minus_04_dot_48_bar__plus_00_dot_70_bar__minus_05_dot_08 VaseType)
        (objectType RemoteControl_bar__minus_02_dot_79_bar__plus_00_dot_59_bar__minus_02_dot_49 RemoteControlType)
        (objectType Laptop_bar__minus_03_dot_22_bar__plus_00_dot_70_bar__minus_05_dot_09 LaptopType)
        (objectType KeyChain_bar__minus_02_dot_79_bar__plus_00_dot_59_bar__minus_02_dot_66 KeyChainType)
        (objectType CreditCard_bar__minus_04_dot_38_bar__plus_00_dot_33_bar__minus_01_dot_46 CreditCardType)
        (objectType FloorLamp_bar__minus_04_dot_82_bar__plus_00_dot_00_bar__minus_05_dot_12 FloorLampType)
        (objectType Watch_bar__minus_02_dot_68_bar__plus_00_dot_59_bar__minus_02_dot_33 WatchType)
        (objectType Window_bar__minus_08_dot_24_bar__plus_01_dot_41_bar__minus_01_dot_51 WindowType)
        (objectType Watch_bar__minus_03_dot_50_bar__plus_00_dot_70_bar__minus_05_dot_14 WatchType)
        (objectType Pillow_bar__minus_04_dot_19_bar__plus_00_dot_44_bar__minus_03_dot_45 PillowType)
        (objectType Television_bar__minus_00_dot_61_bar__plus_01_dot_67_bar__minus_02_dot_60 TelevisionType)
        (objectType Box_bar__minus_02_dot_36_bar__plus_00_dot_26_bar__minus_01_dot_65 BoxType)
        (objectType Mirror_bar__minus_03_dot_40_bar__plus_02_dot_03_bar__plus_00_dot_00 MirrorType)
        (objectType Book_bar__minus_05_dot_32_bar__plus_00_dot_48_bar__minus_05_dot_09 BookType)
        (objectType Window_bar__minus_08_dot_24_bar__plus_01_dot_41_bar__minus_04_dot_08 WindowType)
        (objectType Vase_bar__minus_02_dot_57_bar__plus_00_dot_59_bar__minus_02_dot_16 VaseType)
        (objectType CreditCard_bar__minus_03_dot_91_bar__plus_00_dot_08_bar__minus_05_dot_03 CreditCardType)
        (objectType HousePlant_bar__minus_05_dot_63_bar__plus_00_dot_48_bar__minus_05_dot_13 HousePlantType)
        (objectType CreditCard_bar__minus_02_dot_93_bar__plus_00_dot_70_bar__minus_05_dot_24 CreditCardType)
        (objectType Statue_bar__minus_02_dot_36_bar__plus_00_dot_59_bar__minus_02_dot_49 StatueType)
        (objectType Pillow_bar__minus_04_dot_28_bar__plus_00_dot_45_bar__minus_01_dot_74 PillowType)
        (objectType KeyChain_bar__minus_04_dot_36_bar__plus_00_dot_71_bar__minus_05_dot_05 KeyChainType)
        (objectType Pen_bar__minus_02_dot_26_bar__plus_00_dot_59_bar__minus_02_dot_99 PenType)
        (objectType Vase_bar__minus_02_dot_15_bar__plus_00_dot_59_bar__minus_02_dot_33 VaseType)
        (objectType Vase_bar__minus_04_dot_22_bar__plus_00_dot_70_bar__minus_05_dot_05 VaseType)
        (objectType KeyChain_bar__minus_02_dot_75_bar__plus_00_dot_08_bar__minus_05_dot_05 KeyChainType)
        (objectType Painting_bar__minus_03_dot_37_bar__plus_01_dot_80_bar__minus_05_dot_39 PaintingType)
        (canContain ArmChairType BoxType)
        (canContain ArmChairType LaptopType)
        (canContain ArmChairType BookType)
        (canContain ArmChairType PillowType)
        (canContain ArmChairType RemoteControlType)
        (canContain ArmChairType KeyChainType)
        (canContain ArmChairType NewspaperType)
        (canContain ArmChairType CreditCardType)
        (canContain GarbageCanType PenType)
        (canContain GarbageCanType NewspaperType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType WatchType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain SideTableType PenType)
        (canContain SideTableType BookType)
        (canContain SideTableType WatchType)
        (canContain SideTableType NewspaperType)
        (canContain SideTableType VaseType)
        (canContain SideTableType BoxType)
        (canContain SideTableType KeyChainType)
        (canContain SideTableType CreditCardType)
        (canContain SideTableType LaptopType)
        (canContain SideTableType RemoteControlType)
        (canContain SideTableType StatueType)
        (canContain SideTableType WateringCanType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain DresserType PenType)
        (canContain DresserType BookType)
        (canContain DresserType WatchType)
        (canContain DresserType NewspaperType)
        (canContain DresserType VaseType)
        (canContain DresserType BoxType)
        (canContain DresserType KeyChainType)
        (canContain DresserType CreditCardType)
        (canContain DresserType LaptopType)
        (canContain DresserType RemoteControlType)
        (canContain DresserType StatueType)
        (canContain DresserType WateringCanType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain CoffeeTableType PenType)
        (canContain CoffeeTableType BookType)
        (canContain CoffeeTableType WatchType)
        (canContain CoffeeTableType NewspaperType)
        (canContain CoffeeTableType VaseType)
        (canContain CoffeeTableType BoxType)
        (canContain CoffeeTableType KeyChainType)
        (canContain CoffeeTableType CreditCardType)
        (canContain CoffeeTableType LaptopType)
        (canContain CoffeeTableType RemoteControlType)
        (canContain CoffeeTableType StatueType)
        (canContain CoffeeTableType WateringCanType)
        (canContain DrawerType PenType)
        (canContain DrawerType BookType)
        (canContain DrawerType WatchType)
        (canContain DrawerType NewspaperType)
        (canContain DrawerType KeyChainType)
        (canContain DrawerType CreditCardType)
        (canContain DrawerType RemoteControlType)
        (canContain DrawerType WateringCanType)
        (canContain SofaType BoxType)
        (canContain SofaType LaptopType)
        (canContain SofaType BookType)
        (canContain SofaType PillowType)
        (canContain SofaType RemoteControlType)
        (canContain SofaType KeyChainType)
        (canContain SofaType NewspaperType)
        (canContain SofaType CreditCardType)
        (pickupable Newspaper_bar__minus_04_dot_27_bar__plus_00_dot_34_bar__minus_02_dot_16)
        (pickupable RemoteControl_bar__minus_02_dot_15_bar__plus_00_dot_59_bar__minus_02_dot_00)
        (pickupable WateringCan_bar__minus_07_dot_99_bar__plus_00_dot_00_bar__minus_04_dot_63)
        (pickupable Statue_bar__minus_02_dot_65_bar__plus_00_dot_71_bar__minus_05_dot_14)
        (pickupable Book_bar__minus_02_dot_57_bar__plus_00_dot_59_bar__minus_02_dot_66)
        (pickupable Vase_bar__minus_04_dot_48_bar__plus_00_dot_70_bar__minus_05_dot_08)
        (pickupable RemoteControl_bar__minus_02_dot_79_bar__plus_00_dot_59_bar__minus_02_dot_49)
        (pickupable Laptop_bar__minus_03_dot_22_bar__plus_00_dot_70_bar__minus_05_dot_09)
        (pickupable KeyChain_bar__minus_02_dot_79_bar__plus_00_dot_59_bar__minus_02_dot_66)
        (pickupable CreditCard_bar__minus_04_dot_38_bar__plus_00_dot_33_bar__minus_01_dot_46)
        (pickupable Watch_bar__minus_02_dot_68_bar__plus_00_dot_59_bar__minus_02_dot_33)
        (pickupable Watch_bar__minus_03_dot_50_bar__plus_00_dot_70_bar__minus_05_dot_14)
        (pickupable Pillow_bar__minus_04_dot_19_bar__plus_00_dot_44_bar__minus_03_dot_45)
        (pickupable Box_bar__minus_02_dot_36_bar__plus_00_dot_26_bar__minus_01_dot_65)
        (pickupable Book_bar__minus_05_dot_32_bar__plus_00_dot_48_bar__minus_05_dot_09)
        (pickupable Vase_bar__minus_02_dot_57_bar__plus_00_dot_59_bar__minus_02_dot_16)
        (pickupable CreditCard_bar__minus_03_dot_91_bar__plus_00_dot_08_bar__minus_05_dot_03)
        (pickupable CreditCard_bar__minus_02_dot_93_bar__plus_00_dot_70_bar__minus_05_dot_24)
        (pickupable Statue_bar__minus_02_dot_36_bar__plus_00_dot_59_bar__minus_02_dot_49)
        (pickupable Pillow_bar__minus_04_dot_28_bar__plus_00_dot_45_bar__minus_01_dot_74)
        (pickupable KeyChain_bar__minus_04_dot_36_bar__plus_00_dot_71_bar__minus_05_dot_05)
        (pickupable Pen_bar__minus_02_dot_26_bar__plus_00_dot_59_bar__minus_02_dot_99)
        (pickupable Vase_bar__minus_02_dot_15_bar__plus_00_dot_59_bar__minus_02_dot_33)
        (pickupable Vase_bar__minus_04_dot_22_bar__plus_00_dot_70_bar__minus_05_dot_05)
        (pickupable KeyChain_bar__minus_02_dot_75_bar__plus_00_dot_08_bar__minus_05_dot_05)
        (isReceptacleObject Box_bar__minus_02_dot_36_bar__plus_00_dot_26_bar__minus_01_dot_65)
        (openable Drawer_bar__minus_02_dot_98_bar__plus_00_dot_48_bar__minus_05_dot_01)
        (openable Drawer_bar__minus_04_dot_02_bar__plus_00_dot_48_bar__minus_05_dot_01)
        (openable Drawer_bar__minus_02_dot_98_bar__plus_00_dot_17_bar__minus_05_dot_01)
        
        (atLocation agent1 loc_bar__minus_29_bar__minus_17_bar_1_bar_30)
        
        
        
        
        
        
        
        (toggleable FloorLamp_bar__minus_04_dot_82_bar__plus_00_dot_00_bar__minus_05_dot_12)
        
        
        
        
        (inReceptacle Statue_bar__minus_02_dot_65_bar__plus_00_dot_71_bar__minus_05_dot_14 Dresser_bar__minus_03_dot_51_bar__plus_00_dot_00_bar__minus_05_dot_10)
        (inReceptacle CreditCard_bar__minus_02_dot_93_bar__plus_00_dot_70_bar__minus_05_dot_24 Dresser_bar__minus_03_dot_51_bar__plus_00_dot_00_bar__minus_05_dot_10)
        (inReceptacle Watch_bar__minus_03_dot_50_bar__plus_00_dot_70_bar__minus_05_dot_14 Dresser_bar__minus_03_dot_51_bar__plus_00_dot_00_bar__minus_05_dot_10)
        (inReceptacle Vase_bar__minus_04_dot_22_bar__plus_00_dot_70_bar__minus_05_dot_05 Dresser_bar__minus_03_dot_51_bar__plus_00_dot_00_bar__minus_05_dot_10)
        (inReceptacle KeyChain_bar__minus_04_dot_36_bar__plus_00_dot_71_bar__minus_05_dot_05 Dresser_bar__minus_03_dot_51_bar__plus_00_dot_00_bar__minus_05_dot_10)
        (inReceptacle Vase_bar__minus_04_dot_48_bar__plus_00_dot_70_bar__minus_05_dot_08 Dresser_bar__minus_03_dot_51_bar__plus_00_dot_00_bar__minus_05_dot_10)
        (inReceptacle Laptop_bar__minus_03_dot_22_bar__plus_00_dot_70_bar__minus_05_dot_09 Dresser_bar__minus_03_dot_51_bar__plus_00_dot_00_bar__minus_05_dot_10)
        (inReceptacle HousePlant_bar__minus_05_dot_63_bar__plus_00_dot_48_bar__minus_05_dot_13 SideTable_bar__minus_05_dot_43_bar__minus_00_dot_01_bar__minus_05_dot_12)
        (inReceptacle Book_bar__minus_05_dot_32_bar__plus_00_dot_48_bar__minus_05_dot_09 SideTable_bar__minus_05_dot_43_bar__minus_00_dot_01_bar__minus_05_dot_12)
        (inReceptacle Pillow_bar__minus_04_dot_19_bar__plus_00_dot_44_bar__minus_03_dot_45 ArmChair_bar__minus_04_dot_39_bar__plus_00_dot_00_bar__minus_03_dot_55)
        (inReceptacle CreditCard_bar__minus_03_dot_91_bar__plus_00_dot_08_bar__minus_05_dot_03 Drawer_bar__minus_04_dot_02_bar__plus_00_dot_17_bar__minus_05_dot_01)
        (inReceptacle Newspaper_bar__minus_04_dot_27_bar__plus_00_dot_34_bar__minus_02_dot_16 Sofa_bar__minus_04_dot_36_bar__plus_00_dot_00_bar__minus_02_dot_09)
        (inReceptacle CreditCard_bar__minus_04_dot_38_bar__plus_00_dot_33_bar__minus_01_dot_46 Sofa_bar__minus_04_dot_36_bar__plus_00_dot_00_bar__minus_02_dot_09)
        (inReceptacle Pillow_bar__minus_04_dot_28_bar__plus_00_dot_45_bar__minus_01_dot_74 Sofa_bar__minus_04_dot_36_bar__plus_00_dot_00_bar__minus_02_dot_09)
        (inReceptacle KeyChain_bar__minus_02_dot_79_bar__plus_00_dot_59_bar__minus_02_dot_66 CoffeeTable_bar__minus_02_dot_47_bar__plus_00_dot_00_bar__minus_02_dot_49)
        (inReceptacle RemoteControl_bar__minus_02_dot_15_bar__plus_00_dot_59_bar__minus_02_dot_00 CoffeeTable_bar__minus_02_dot_47_bar__plus_00_dot_00_bar__minus_02_dot_49)
        (inReceptacle Watch_bar__minus_02_dot_68_bar__plus_00_dot_59_bar__minus_02_dot_33 CoffeeTable_bar__minus_02_dot_47_bar__plus_00_dot_00_bar__minus_02_dot_49)
        (inReceptacle Book_bar__minus_02_dot_57_bar__plus_00_dot_59_bar__minus_02_dot_66 CoffeeTable_bar__minus_02_dot_47_bar__plus_00_dot_00_bar__minus_02_dot_49)
        (inReceptacle RemoteControl_bar__minus_02_dot_79_bar__plus_00_dot_59_bar__minus_02_dot_49 CoffeeTable_bar__minus_02_dot_47_bar__plus_00_dot_00_bar__minus_02_dot_49)
        (inReceptacle Vase_bar__minus_02_dot_15_bar__plus_00_dot_59_bar__minus_02_dot_33 CoffeeTable_bar__minus_02_dot_47_bar__plus_00_dot_00_bar__minus_02_dot_49)
        (inReceptacle Statue_bar__minus_02_dot_36_bar__plus_00_dot_59_bar__minus_02_dot_49 CoffeeTable_bar__minus_02_dot_47_bar__plus_00_dot_00_bar__minus_02_dot_49)
        (inReceptacle Vase_bar__minus_02_dot_57_bar__plus_00_dot_59_bar__minus_02_dot_16 CoffeeTable_bar__minus_02_dot_47_bar__plus_00_dot_00_bar__minus_02_dot_49)
        (inReceptacle Pen_bar__minus_02_dot_26_bar__plus_00_dot_59_bar__minus_02_dot_99 CoffeeTable_bar__minus_02_dot_47_bar__plus_00_dot_00_bar__minus_02_dot_49)
        (inReceptacle KeyChain_bar__minus_02_dot_75_bar__plus_00_dot_08_bar__minus_05_dot_05 Drawer_bar__minus_02_dot_98_bar__plus_00_dot_17_bar__minus_05_dot_01)
        
        
        (receptacleAtLocation ArmChair_bar__minus_04_dot_39_bar__plus_00_dot_00_bar__minus_03_dot_55 loc_bar__minus_13_bar__minus_15_bar_3_bar_60)
        (receptacleAtLocation CoffeeTable_bar__minus_02_dot_47_bar__plus_00_dot_00_bar__minus_02_dot_49 loc_bar__minus_5_bar__minus_10_bar_3_bar_60)
        (receptacleAtLocation Drawer_bar__minus_02_dot_98_bar__plus_00_dot_17_bar__minus_05_dot_01 loc_bar__minus_7_bar__minus_18_bar_3_bar_45)
        (receptacleAtLocation Drawer_bar__minus_02_dot_98_bar__plus_00_dot_48_bar__minus_05_dot_01 loc_bar__minus_16_bar__minus_18_bar_1_bar_45)
        (receptacleAtLocation Drawer_bar__minus_04_dot_02_bar__plus_00_dot_17_bar__minus_05_dot_01 loc_bar__minus_16_bar__minus_18_bar_2_bar_60)
        (receptacleAtLocation Drawer_bar__minus_04_dot_02_bar__plus_00_dot_48_bar__minus_05_dot_01 loc_bar__minus_12_bar__minus_18_bar_3_bar_45)
        (receptacleAtLocation Dresser_bar__minus_03_dot_51_bar__plus_00_dot_00_bar__minus_05_dot_10 loc_bar__minus_13_bar__minus_17_bar_2_bar_60)
        (receptacleAtLocation GarbageCan_bar__minus_00_dot_97_bar__plus_00_dot_00_bar__minus_05_dot_06 loc_bar__minus_6_bar__minus_19_bar_1_bar_60)
        (receptacleAtLocation SideTable_bar__minus_02_dot_02_bar__minus_00_dot_01_bar__minus_05_dot_08 loc_bar__minus_10_bar__minus_17_bar_2_bar_60)
        (receptacleAtLocation SideTable_bar__minus_05_dot_43_bar__minus_00_dot_01_bar__minus_05_dot_12 loc_bar__minus_23_bar__minus_17_bar_2_bar_60)
        (receptacleAtLocation Sofa_bar__minus_04_dot_36_bar__plus_00_dot_00_bar__minus_02_dot_09 loc_bar__minus_13_bar__minus_8_bar_3_bar_60)
        (objectAtLocation RemoteControl_bar__minus_02_dot_79_bar__plus_00_dot_59_bar__minus_02_dot_49 loc_bar__minus_5_bar__minus_10_bar_3_bar_60)
        (objectAtLocation Statue_bar__minus_02_dot_65_bar__plus_00_dot_71_bar__minus_05_dot_14 loc_bar__minus_13_bar__minus_17_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_02_dot_79_bar__plus_00_dot_59_bar__minus_02_dot_66 loc_bar__minus_5_bar__minus_10_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__minus_03_dot_91_bar__plus_00_dot_08_bar__minus_05_dot_03 loc_bar__minus_16_bar__minus_18_bar_2_bar_60)
        (objectAtLocation Pillow_bar__minus_04_dot_28_bar__plus_00_dot_45_bar__minus_01_dot_74 loc_bar__minus_13_bar__minus_8_bar_3_bar_60)
        (objectAtLocation Watch_bar__minus_03_dot_50_bar__plus_00_dot_70_bar__minus_05_dot_14 loc_bar__minus_13_bar__minus_17_bar_2_bar_60)
        (objectAtLocation Book_bar__minus_02_dot_57_bar__plus_00_dot_59_bar__minus_02_dot_66 loc_bar__minus_5_bar__minus_10_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__minus_02_dot_93_bar__plus_00_dot_70_bar__minus_05_dot_24 loc_bar__minus_13_bar__minus_17_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_02_dot_75_bar__plus_00_dot_08_bar__minus_05_dot_05 loc_bar__minus_7_bar__minus_18_bar_3_bar_45)
        (objectAtLocation Box_bar__minus_02_dot_36_bar__plus_00_dot_26_bar__minus_01_dot_65 loc_bar__minus_8_bar__minus_6_bar_3_bar_60)
        (objectAtLocation Book_bar__minus_05_dot_32_bar__plus_00_dot_48_bar__minus_05_dot_09 loc_bar__minus_23_bar__minus_17_bar_2_bar_60)
        (objectAtLocation Mirror_bar__minus_03_dot_40_bar__plus_02_dot_03_bar__plus_00_dot_00 loc_bar__minus_14_bar__minus_3_bar_0_bar__minus_30)
        (objectAtLocation Newspaper_bar__minus_04_dot_27_bar__plus_00_dot_34_bar__minus_02_dot_16 loc_bar__minus_13_bar__minus_8_bar_3_bar_60)
        (objectAtLocation Watch_bar__minus_02_dot_68_bar__plus_00_dot_59_bar__minus_02_dot_33 loc_bar__minus_5_bar__minus_10_bar_3_bar_60)
        (objectAtLocation Pen_bar__minus_02_dot_26_bar__plus_00_dot_59_bar__minus_02_dot_99 loc_bar__minus_5_bar__minus_10_bar_3_bar_60)
        (objectAtLocation FloorLamp_bar__minus_04_dot_82_bar__plus_00_dot_00_bar__minus_05_dot_12 loc_bar__minus_18_bar__minus_18_bar_2_bar_60)
        (objectAtLocation LightSwitch_bar__minus_05_dot_76_bar__plus_01_dot_33_bar__plus_00_dot_01 loc_bar__minus_23_bar__minus_2_bar_0_bar_30)
        (objectAtLocation WateringCan_bar__minus_07_dot_99_bar__plus_00_dot_00_bar__minus_04_dot_63 loc_bar__minus_31_bar__minus_17_bar_2_bar_60)
        (objectAtLocation Pillow_bar__minus_04_dot_19_bar__plus_00_dot_44_bar__minus_03_dot_45 loc_bar__minus_13_bar__minus_15_bar_3_bar_60)
        (objectAtLocation CreditCard_bar__minus_04_dot_38_bar__plus_00_dot_33_bar__minus_01_dot_46 loc_bar__minus_13_bar__minus_8_bar_3_bar_60)
        (objectAtLocation Vase_bar__minus_04_dot_48_bar__plus_00_dot_70_bar__minus_05_dot_08 loc_bar__minus_13_bar__minus_17_bar_2_bar_60)
        (objectAtLocation KeyChain_bar__minus_04_dot_36_bar__plus_00_dot_71_bar__minus_05_dot_05 loc_bar__minus_13_bar__minus_17_bar_2_bar_60)
        (objectAtLocation Vase_bar__minus_04_dot_22_bar__plus_00_dot_70_bar__minus_05_dot_05 loc_bar__minus_13_bar__minus_17_bar_2_bar_60)
        (objectAtLocation Laptop_bar__minus_03_dot_22_bar__plus_00_dot_70_bar__minus_05_dot_09 loc_bar__minus_13_bar__minus_17_bar_2_bar_60)
        (objectAtLocation HousePlant_bar__minus_05_dot_63_bar__plus_00_dot_48_bar__minus_05_dot_13 loc_bar__minus_23_bar__minus_17_bar_2_bar_60)
        (objectAtLocation Statue_bar__minus_02_dot_36_bar__plus_00_dot_59_bar__minus_02_dot_49 loc_bar__minus_5_bar__minus_10_bar_3_bar_60)
        (objectAtLocation Vase_bar__minus_02_dot_57_bar__plus_00_dot_59_bar__minus_02_dot_16 loc_bar__minus_5_bar__minus_10_bar_3_bar_60)
        (objectAtLocation Painting_bar__minus_03_dot_37_bar__plus_01_dot_80_bar__minus_05_dot_39 loc_bar__minus_13_bar__minus_18_bar_2_bar__minus_15)
        (objectAtLocation RemoteControl_bar__minus_02_dot_15_bar__plus_00_dot_59_bar__minus_02_dot_00 loc_bar__minus_5_bar__minus_10_bar_3_bar_60)
        (objectAtLocation Television_bar__minus_00_dot_61_bar__plus_01_dot_67_bar__minus_02_dot_60 loc_bar__minus_5_bar__minus_10_bar_1_bar_0)
        (objectAtLocation Vase_bar__minus_02_dot_15_bar__plus_00_dot_59_bar__minus_02_dot_33 loc_bar__minus_5_bar__minus_10_bar_3_bar_60)
        (objectAtLocation Window_bar__minus_08_dot_24_bar__plus_01_dot_41_bar__minus_01_dot_51 loc_bar__minus_31_bar__minus_6_bar_3_bar_30)
        (objectAtLocation Window_bar__minus_08_dot_24_bar__plus_01_dot_41_bar__minus_04_dot_08 loc_bar__minus_31_bar__minus_16_bar_3_bar_30)
        )
    

                (:goal
                    (and
                        (exists (?r - receptacle)
                            (exists (?o1 - object)
                                (and
                                    (objectType ?o1 RemoteControlType)
                                    (receptacleType ?r ArmChairType)
                                    (inReceptacle ?o1 ?r)
                                    (exists (?o2 - object)
                                        (and
                                            (not (= ?o1 ?o2))
                                            (objectType ?o2 RemoteControlType)
                                            (receptacleType ?r ArmChairType)
                                            (inReceptacle ?o2 ?r)
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
            