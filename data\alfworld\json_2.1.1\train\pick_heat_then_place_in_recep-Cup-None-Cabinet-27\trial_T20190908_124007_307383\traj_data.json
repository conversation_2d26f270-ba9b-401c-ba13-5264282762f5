{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 1, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000052.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000053.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000054.png", "low_idx": 7}, {"high_idx": 1, "image_name": "000000055.png", "low_idx": 7}, {"high_idx": 2, "image_name": "000000056.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000057.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000058.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000059.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000060.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000061.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000062.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000063.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000064.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000065.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000066.png", "low_idx": 8}, {"high_idx": 2, "image_name": "000000067.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000068.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000069.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000070.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000071.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000072.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000073.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000074.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000075.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000076.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000077.png", "low_idx": 9}, {"high_idx": 2, "image_name": "000000078.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000079.png", "low_idx": 10}, {"high_idx": 2, "image_name": "000000080.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000081.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000082.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000083.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000084.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000085.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000086.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000087.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000088.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000089.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000090.png", "low_idx": 11}, {"high_idx": 2, "image_name": "000000091.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000092.png", "low_idx": 12}, {"high_idx": 2, "image_name": "000000093.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000094.png", "low_idx": 13}, {"high_idx": 2, "image_name": "000000095.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000096.png", "low_idx": 14}, {"high_idx": 2, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 2, "image_name": "000000099.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000100.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000101.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000102.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000103.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000104.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000105.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000106.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000107.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000108.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000109.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000110.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000111.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000112.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000113.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 21}, {"high_idx": 3, "image_name": "000000120.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000121.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000122.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000123.png", "low_idx": 22}, {"high_idx": 3, "image_name": "000000124.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000125.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000126.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000127.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000128.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000129.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000130.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000131.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000132.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000133.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000134.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000135.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000136.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000137.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000138.png", "low_idx": 23}, {"high_idx": 3, "image_name": "000000139.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000140.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000141.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000142.png", "low_idx": 24}, {"high_idx": 3, "image_name": "000000143.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000144.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000145.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000146.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000147.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000148.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000149.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000150.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000151.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000152.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000153.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000154.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000155.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000156.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000157.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000158.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000159.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000160.png", "low_idx": 25}, {"high_idx": 3, "image_name": "000000161.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000162.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000163.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000164.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000165.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000166.png", "low_idx": 26}, {"high_idx": 3, "image_name": "000000167.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000168.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000169.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000170.png", "low_idx": 27}, {"high_idx": 3, "image_name": "000000171.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000172.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000173.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000174.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000175.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000176.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000177.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000178.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000179.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000180.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000181.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000182.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000183.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000184.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000185.png", "low_idx": 28}, {"high_idx": 3, "image_name": "000000186.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000187.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000188.png", "low_idx": 29}, {"high_idx": 3, "image_name": "000000189.png", "low_idx": 29}, {"high_idx": 4, "image_name": "000000190.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000191.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000192.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000193.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000195.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000196.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000197.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000198.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000199.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000200.png", "low_idx": 30}, {"high_idx": 4, "image_name": "000000201.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000202.png", "low_idx": 31}, {"high_idx": 4, "image_name": "000000203.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000204.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000205.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000206.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000207.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000208.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000209.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000210.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000211.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000212.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000213.png", "low_idx": 32}, {"high_idx": 4, "image_name": "000000214.png", "low_idx": 33}, {"high_idx": 4, "image_name": "000000215.png", "low_idx": 33}, {"high_idx": 5, "image_name": "000000216.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000217.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000218.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000219.png", "low_idx": 34}, {"high_idx": 5, "image_name": "000000220.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000224.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000225.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000226.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000227.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000228.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000229.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000230.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000231.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000232.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000233.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000234.png", "low_idx": 35}, {"high_idx": 5, "image_name": "000000235.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000236.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000237.png", "low_idx": 36}, {"high_idx": 5, "image_name": "000000238.png", "low_idx": 36}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Cup", "parent_target": "Cabinet", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["countertop"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|6|2|1|45"}}, {"discrete_action": {"action": "PickupObject", "args": ["cup"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Cup", [9.20777512, 9.20777512, 3.2240512, 3.2240512, 3.7462616, 3.7462616]], "coordinateReceptacleObjectId": ["CounterTop", [8.2408, 8.2408, 2.3348, 2.3348, 3.8952, 3.8952]], "forceVisible": true, "objectId": "Cup|+02.30|+00.94|+00.81"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|2|8|3|45"}}, {"discrete_action": {"action": "HeatObject", "args": ["cup"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-1.244, -1.244, 8.312, 8.312, 3.7060904, 3.7060904]], "forceVisible": true, "objectId": "Microwave|-00.31|+00.93|+02.08"}}, {"discrete_action": {"action": "GotoLocation", "args": ["cabinet"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|3|7|1|45"}}, {"discrete_action": {"action": "PutObject", "args": ["cup", "cabinet"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Cup", [9.20777512, 9.20777512, 3.2240512, 3.2240512, 3.7462616, 3.7462616]], "coordinateReceptacleObjectId": ["Cabinet", [7.04371356, 7.04371356, 9.38795376, 9.38795376, 1.555381416, 1.555381416]], "forceVisible": true, "objectId": "Cup|+02.30|+00.94|+00.81", "receptacleObjectId": "Cabinet|+01.76|+00.39|+02.35"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+02.30|+00.94|+00.81"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [49, 80, 81, 128], "mask": [[23760, 10], [24056, 16], [24354, 19], [24653, 21], [24952, 23], [25251, 25], [25550, 27], [25850, 27], [26150, 28], [26450, 29], [26750, 29], [27049, 30], [27349, 31], [27649, 31], [27949, 31], [28249, 31], [28549, 32], [28849, 32], [29150, 31], [29450, 31], [29750, 31], [30051, 30], [30351, 30], [30651, 30], [30952, 28], [31253, 27], [31553, 26], [31854, 25], [32154, 24], [32455, 22], [32757, 19], [33058, 17], [33360, 13], [33662, 10], [33968, 8], [34268, 12], [34565, 17], [34864, 18], [35162, 19], [35462, 19], [35761, 20], [36060, 21], [36360, 20], [36660, 20], [36960, 20], [37261, 18], [37562, 17], [37863, 14], [38165, 9]], "point": [65, 103]}}, "high_idx": 1}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+02.30|+00.94|+00.81", "placeStationary": true, "receptacleObjectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 299, 272], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14156, 243], [14455, 243], [14754, 244], [15054, 244], [15353, 244], [15653, 244], [15952, 244], [16251, 245], [16551, 245], [16850, 245], [17150, 245], [17449, 246], [17748, 246], [18048, 90], [18141, 153], [18347, 83], [18449, 144], [18647, 80], [18752, 141], [18946, 78], [19055, 137], [19245, 77], [19357, 135], [19545, 76], [19658, 134], [19844, 76], [19959, 132], [20144, 75], [20260, 131], [20443, 75], [20561, 129], [20742, 76], [20861, 129], [21042, 75], [21161, 129], [21341, 76], [21461, 128], [21641, 76], [21761, 128], [21940, 77], [22061, 127], [22239, 79], [22360, 128], [22539, 79], [22660, 128], [22838, 81], [22959, 128], [23138, 82], [23258, 129], [23437, 84], [23558, 128], [23736, 85], [23858, 128], [24036, 85], [24157, 128], [24335, 87], [24457, 128], [24635, 87], [24757, 128], [24934, 88], [25057, 127], [25233, 90], [25356, 128], [25533, 90], [25655, 128], [25832, 92], [25955, 128], [26132, 93], [26254, 129], [26431, 94], [26553, 129], [26730, 96], [26853, 129], [27030, 97], [27152, 129], [27329, 99], [27451, 130], [27629, 100], [27751, 130], [27928, 101], [28050, 130], [28227, 103], [28349, 131], [28527, 104], [28648, 131], [28826, 107], [28946, 133], [29126, 253], [29425, 253], [29724, 254], [30024, 253], [30323, 254], [30623, 253], [30922, 254], [31221, 255], [31521, 254], [31820, 255], [32120, 254], [32419, 255], [32718, 256], [33018, 255], [33317, 256], [33617, 255], [33916, 256], [34215, 257], [34515, 256], [34814, 257], [35114, 256], [35413, 257], [35712, 257], [36012, 257], [36311, 258], [36611, 257], [36910, 258], [37209, 258], [37509, 258], [37808, 259], [38108, 258], [38407, 259], [38706, 259], [39006, 259], [39305, 260], [39605, 259], [39904, 260], [40203, 260], [40503, 260], [40802, 260], [41102, 260], [41401, 261], [41700, 261], [42000, 261], [42300, 261], [42600, 260], [42900, 260], [43200, 259], [43500, 259], [43800, 259], [44100, 86], [44187, 171], [44400, 85], [44487, 171], [44700, 85], [45000, 85], [45300, 85], [45600, 84], [45900, 84], [46200, 84], [46500, 84], [46800, 83], [47100, 83], [47400, 83], [47700, 83], [48000, 82], [48300, 82], [48600, 82], [48900, 82], [49200, 81], [49500, 81], [49800, 81], [50100, 81], [50400, 80], [50700, 80], [51000, 80], [51300, 80], [51600, 79], [51900, 79], [52200, 79], [52500, 79], [52800, 78], [53100, 78], [53400, 78], [53700, 78], [54000, 77], [54300, 77], [54600, 77], [54900, 77], [55200, 76], [55500, 76], [55800, 76], [56100, 76], [56400, 75], [56700, 75], [57000, 75], [57300, 75], [57600, 74], [57900, 74], [58200, 74], [58500, 74], [58800, 73], [59100, 73], [59400, 73], [59700, 73], [60000, 72], [60300, 72], [60600, 72], [60900, 72], [61200, 71], [61500, 71], [61800, 71], [62100, 71], [62400, 70], [62700, 70], [63000, 70], [63300, 70], [63600, 69], [63900, 69], [64200, 69], [64500, 69], [64800, 68], [65100, 68], [65400, 68], [65700, 68], [66000, 67], [66300, 67], [66600, 67], [66900, 67], [67200, 66], [67500, 66], [67800, 66], [68100, 66], [68400, 65], [68700, 65], [69000, 65], [69300, 65], [69600, 64], [69900, 64], [70200, 64], [70500, 64], [70800, 63], [71100, 63], [71400, 63], [71700, 63], [72000, 62], [72300, 62], [72600, 62], [72900, 62], [73200, 61], [73500, 61], [73800, 61], [74101, 60], [74402, 58], [74703, 57], [75004, 56], [75305, 55], [75606, 53], [75907, 52], [76208, 51], [76509, 50], [76809, 49], [77110, 48], [77411, 12], [77425, 33], [77712, 10], [77726, 32], [78013, 8], [78027, 30], [78314, 6], [78327, 30], [78615, 4], [78628, 29], [78929, 31], [79230, 32], [79531, 32], [79832, 24], [79857, 6], [80132, 24], [80158, 4], [80433, 22], [80734, 21], [81035, 20], [81336, 18]], "point": [149, 135]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 272], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14156, 243], [14455, 243], [14754, 244], [15054, 244], [15353, 244], [15653, 244], [15952, 244], [16251, 245], [16551, 245], [16850, 245], [17150, 245], [17449, 246], [17748, 246], [18048, 90], [18141, 37], [18198, 96], [18347, 83], [18449, 28], [18500, 93], [18647, 80], [18752, 24], [18801, 92], [18946, 78], [19055, 20], [19102, 90], [19245, 77], [19357, 18], [19403, 89], [19545, 76], [19658, 16], [19703, 89], [19844, 76], [19959, 15], [20003, 88], [20144, 75], [20260, 13], [20304, 87], [20443, 75], [20561, 12], [20604, 86], [20742, 76], [20861, 12], [20904, 86], [21042, 75], [21161, 11], [21204, 86], [21341, 76], [21461, 11], [21504, 85], [21641, 76], [21761, 11], [21804, 85], [21940, 77], [22061, 11], [22104, 84], [22239, 79], [22360, 11], [22404, 84], [22539, 79], [22660, 11], [22704, 84], [22838, 81], [22959, 12], [23004, 83], [23138, 82], [23258, 13], [23304, 83], [23437, 84], [23558, 13], [23604, 82], [23736, 85], [23858, 13], [23904, 82], [24036, 85], [24157, 14], [24204, 81], [24335, 87], [24457, 14], [24504, 81], [24635, 87], [24757, 14], [24803, 82], [24934, 88], [25057, 14], [25103, 81], [25233, 90], [25356, 15], [25403, 81], [25533, 90], [25655, 17], [25702, 81], [25832, 92], [25955, 17], [26002, 81], [26132, 93], [26254, 19], [26302, 81], [26431, 94], [26553, 20], [26601, 81], [26730, 96], [26853, 21], [26900, 82], [27030, 97], [27152, 22], [27199, 82], [27329, 99], [27451, 24], [27498, 83], [27629, 100], [27751, 25], [27797, 84], [27928, 101], [28050, 27], [28096, 84], [28227, 103], [28349, 30], [28395, 85], [28527, 104], [28648, 34], [28693, 86], [28826, 107], [28946, 37], [28988, 91], [29126, 157], [29288, 91], [29425, 158], [29588, 90], [29724, 153], [29889, 89], [30024, 151], [30192, 85], [30323, 151], [30494, 83], [30623, 150], [30795, 81], [30922, 150], [31096, 80], [31221, 151], [31396, 80], [31521, 151], [31697, 78], [31820, 152], [31997, 78], [32120, 152], [32297, 77], [32419, 154], [32597, 77], [32718, 155], [32896, 78], [33018, 157], [33195, 78], [33317, 159], [33495, 78], [33617, 161], [33792, 80], [33916, 166], [34089, 83], [34215, 257], [34515, 256], [34814, 257], [35114, 256], [35413, 257], [35712, 257], [36012, 257], [36311, 258], [36611, 257], [36910, 258], [37209, 258], [37509, 258], [37808, 259], [38108, 258], [38407, 259], [38706, 259], [39006, 259], [39305, 260], [39605, 259], [39904, 260], [40203, 260], [40503, 260], [40802, 260], [41102, 260], [41401, 261], [41700, 261], [42000, 261], [42300, 261], [42600, 260], [42900, 260], [43200, 259], [43500, 259], [43800, 259], [44100, 86], [44187, 171], [44400, 85], [44487, 171], [44700, 85], [45000, 85], [45300, 85], [45600, 84], [45900, 84], [46200, 84], [46500, 84], [46800, 83], [47100, 83], [47400, 83], [47700, 83], [48000, 82], [48300, 82], [48600, 82], [48900, 82], [49200, 81], [49500, 81], [49800, 81], [50100, 81], [50400, 80], [50700, 80], [51000, 80], [51300, 80], [51600, 79], [51900, 79], [52200, 79], [52500, 79], [52800, 78], [53100, 78], [53400, 78], [53700, 78], [54000, 77], [54300, 77], [54600, 77], [54900, 77], [55200, 76], [55500, 76], [55800, 76], [56100, 76], [56400, 75], [56700, 75], [57000, 75], [57300, 75], [57600, 74], [57900, 74], [58200, 74], [58500, 74], [58800, 73], [59100, 73], [59400, 73], [59700, 73], [60000, 72], [60300, 72], [60600, 72], [60900, 72], [61200, 71], [61500, 71], [61800, 71], [62100, 71], [62400, 70], [62700, 70], [63000, 70], [63300, 70], [63600, 69], [63900, 69], [64200, 69], [64500, 69], [64800, 68], [65100, 68], [65400, 68], [65700, 68], [66000, 67], [66300, 67], [66600, 67], [66900, 67], [67200, 66], [67500, 66], [67800, 66], [68100, 66], [68400, 65], [68700, 65], [69000, 65], [69300, 65], [69600, 64], [69900, 64], [70200, 64], [70500, 64], [70800, 63], [71100, 63], [71400, 63], [71700, 63], [72000, 62], [72300, 62], [72600, 62], [72900, 62], [73200, 61], [73500, 61], [73800, 61], [74101, 60], [74402, 58], [74703, 57], [75004, 56], [75305, 55], [75606, 53], [75907, 52], [76208, 51], [76509, 50], [76809, 49], [77110, 48], [77411, 12], [77425, 33], [77712, 10], [77726, 32], [78013, 8], [78027, 30], [78314, 6], [78327, 30], [78615, 4], [78628, 29], [78929, 31], [79230, 32], [79531, 32], [79832, 24], [79857, 6], [80132, 24], [80158, 4], [80433, 22], [80734, 21], [81035, 20], [81336, 18]], "point": [149, 135]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-1.244, -1.244, 8.312, 8.312, 3.7060904, 3.7060904]], "forceVisible": true, "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-1.244, -1.244, 8.312, 8.312, 3.7060904, 3.7060904]], "forceVisible": true, "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [60, 1, 299, 149], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14162, 237], [14462, 236], [14763, 235], [15063, 235], [15363, 234], [15663, 234], [15964, 232], [16264, 232], [16565, 231], [16865, 230], [17165, 230], [17465, 230], [17766, 228], [18066, 228], [18366, 227], [18666, 227], [18966, 226], [19267, 225], [19567, 225], [19867, 224], [20167, 224], [20468, 222], [20768, 222], [21068, 222], [21368, 221], [21669, 220], [21969, 219], [22269, 219], [22569, 219], [22870, 217], [23170, 217], [23470, 216], [23770, 216], [24071, 214], [24371, 214], [24671, 214], [24971, 213], [25272, 212], [25572, 211], [25872, 211], [26172, 211], [26473, 209], [26773, 209], [27073, 208], [27373, 208], [27673, 208], [27974, 206], [28274, 206], [28574, 205], [28874, 205], [29175, 204], [29475, 203], [29775, 203], [30075, 202], [30376, 201], [30676, 200], [30976, 200], [31276, 200], [31577, 198], [31877, 198], [32177, 197], [32477, 197], [32778, 196], [33078, 195], [33378, 195], [33678, 194], [33979, 193], [34279, 193], [34579, 192], [34879, 192], [35179, 191], [35480, 190], [35780, 189], [36080, 189], [36380, 189], [36681, 187], [36981, 187], [37281, 186], [37581, 186], [37882, 185], [38182, 184], [38482, 184], [38782, 183], [39083, 182], [39383, 182], [39683, 181], [39983, 181], [40284, 179], [40584, 179], [40884, 178], [41184, 178], [41485, 177], [41785, 176], [42085, 176], [42385, 176], [42685, 175], [42986, 174], [43286, 173], [43586, 173], [43886, 173], [44187, 171], [44487, 171]], "point": [179, 74]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Cup|+02.30|+00.94|+00.81"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [171, 61, 203, 114], "mask": [[18178, 20], [18477, 23], [18776, 25], [19075, 27], [19375, 28], [19674, 29], [19974, 29], [20273, 31], [20573, 31], [20873, 31], [21172, 32], [21472, 32], [21772, 32], [22072, 32], [22371, 33], [22671, 33], [22971, 33], [23271, 33], [23571, 33], [23871, 33], [24171, 33], [24471, 33], [24771, 32], [25071, 32], [25371, 32], [25672, 30], [25972, 30], [26273, 29], [26573, 28], [26874, 26], [27174, 25], [27475, 23], [27776, 21], [28077, 19], [28379, 16], [28682, 11], [28983, 5], [29283, 5], [29583, 5], [29877, 12], [30175, 17], [30474, 20], [30773, 22], [31072, 24], [31372, 24], [31672, 25], [31972, 25], [32272, 25], [32573, 24], [32873, 23], [33175, 20], [33476, 19], [33778, 14], [34082, 7]], "point": [187, 86]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-00.31|+00.93|+02.08"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 299, 272], "mask": [[81, 185], [381, 186], [680, 188], [980, 189], [1279, 191], [1578, 194], [1878, 195], [2177, 197], [2476, 199], [2776, 200], [3075, 202], [3375, 203], [3674, 205], [3973, 207], [4273, 208], [4572, 210], [4871, 212], [5171, 213], [5470, 215], [5770, 216], [6069, 218], [6368, 220], [6668, 221], [6967, 224], [7266, 226], [7566, 227], [7865, 229], [8165, 230], [8464, 232], [8763, 234], [9063, 235], [9362, 237], [9662, 238], [9961, 239], [10260, 240], [10560, 240], [10860, 240], [11160, 240], [11460, 240], [11760, 240], [12060, 240], [12361, 239], [12661, 239], [12961, 239], [13261, 239], [13562, 238], [13862, 237], [14156, 243], [14455, 243], [14754, 244], [15054, 244], [15353, 244], [15653, 244], [15952, 244], [16251, 245], [16551, 245], [16850, 245], [17150, 245], [17449, 246], [17748, 246], [18048, 90], [18141, 153], [18347, 83], [18449, 144], [18647, 80], [18752, 141], [18946, 78], [19055, 137], [19245, 77], [19357, 135], [19545, 76], [19658, 134], [19844, 76], [19959, 132], [20144, 75], [20260, 131], [20443, 75], [20561, 129], [20742, 76], [20861, 129], [21042, 75], [21161, 129], [21341, 76], [21461, 128], [21641, 76], [21761, 128], [21940, 77], [22061, 127], [22239, 79], [22360, 128], [22539, 79], [22660, 128], [22838, 81], [22959, 128], [23138, 82], [23258, 129], [23437, 84], [23558, 128], [23736, 85], [23858, 128], [24036, 85], [24157, 128], [24335, 87], [24457, 128], [24635, 87], [24757, 128], [24934, 88], [25057, 127], [25233, 90], [25356, 128], [25533, 90], [25655, 128], [25832, 92], [25955, 128], [26132, 93], [26254, 129], [26431, 94], [26553, 129], [26730, 96], [26853, 129], [27030, 97], [27152, 129], [27329, 99], [27451, 130], [27629, 100], [27751, 130], [27928, 101], [28050, 130], [28227, 103], [28349, 131], [28527, 104], [28648, 131], [28826, 107], [28946, 133], [29126, 253], [29425, 253], [29724, 254], [30024, 253], [30323, 254], [30623, 253], [30922, 254], [31221, 255], [31521, 254], [31820, 255], [32120, 254], [32419, 255], [32718, 256], [33018, 255], [33317, 256], [33617, 255], [33916, 256], [34215, 257], [34515, 256], [34814, 257], [35114, 256], [35413, 257], [35712, 257], [36012, 257], [36311, 258], [36611, 257], [36910, 258], [37209, 258], [37509, 258], [37808, 259], [38108, 258], [38407, 259], [38706, 259], [39006, 259], [39305, 260], [39605, 259], [39904, 260], [40203, 260], [40503, 260], [40802, 260], [41102, 260], [41401, 261], [41700, 261], [42000, 261], [42300, 261], [42600, 260], [42900, 260], [43200, 259], [43500, 259], [43800, 259], [44100, 86], [44187, 171], [44400, 85], [44487, 171], [44700, 85], [45000, 85], [45300, 85], [45600, 84], [45900, 84], [46200, 84], [46500, 84], [46800, 83], [47100, 83], [47400, 83], [47700, 83], [48000, 82], [48300, 82], [48600, 82], [48900, 82], [49200, 81], [49500, 81], [49800, 81], [50100, 81], [50400, 80], [50700, 80], [51000, 80], [51300, 80], [51600, 79], [51900, 79], [52200, 79], [52500, 79], [52800, 78], [53100, 78], [53400, 78], [53700, 78], [54000, 77], [54300, 77], [54600, 77], [54900, 77], [55200, 76], [55500, 76], [55800, 76], [56100, 76], [56400, 75], [56700, 75], [57000, 75], [57300, 75], [57600, 74], [57900, 74], [58200, 74], [58500, 74], [58800, 73], [59100, 73], [59400, 73], [59700, 73], [60000, 72], [60300, 72], [60600, 72], [60900, 72], [61200, 71], [61500, 71], [61800, 71], [62100, 71], [62400, 70], [62700, 70], [63000, 70], [63300, 70], [63600, 69], [63900, 69], [64200, 69], [64500, 69], [64800, 68], [65100, 68], [65400, 68], [65700, 68], [66000, 67], [66300, 67], [66600, 67], [66900, 67], [67200, 66], [67500, 66], [67800, 66], [68100, 66], [68400, 65], [68700, 65], [69000, 65], [69300, 65], [69600, 64], [69900, 64], [70200, 64], [70500, 64], [70800, 63], [71100, 63], [71400, 63], [71700, 63], [72000, 62], [72300, 62], [72600, 62], [72900, 62], [73200, 61], [73500, 61], [73800, 61], [74101, 60], [74402, 58], [74703, 57], [75004, 56], [75305, 55], [75606, 53], [75907, 52], [76208, 51], [76509, 50], [76809, 49], [77110, 48], [77411, 12], [77425, 33], [77712, 10], [77726, 32], [78013, 8], [78027, 30], [78314, 6], [78327, 30], [78615, 4], [78628, 29], [78929, 31], [79230, 32], [79531, 32], [79832, 24], [79857, 6], [80132, 24], [80158, 4], [80433, 22], [80734, 21], [81035, 20], [81336, 18]], "point": [149, 135]}}, "high_idx": 3}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "OpenObject", "objectId": "Cabinet|+01.76|+00.39|+02.35"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [33, 130, 145, 203], "mask": [[38733, 112], [39033, 112], [39333, 113], [39633, 113], [39934, 112], [40234, 112], [40534, 112], [40835, 111], [41135, 111], [41436, 110], [41736, 110], [42037, 109], [42337, 109], [42637, 109], [42938, 108], [43238, 108], [43539, 107], [43839, 107], [44139, 107], [44440, 106], [44740, 106], [45041, 105], [45341, 105], [45642, 104], [45942, 104], [46242, 104], [46543, 103], [46843, 103], [47144, 102], [47444, 102], [47745, 101], [48045, 101], [48345, 101], [48646, 100], [48946, 100], [49247, 99], [49547, 99], [49847, 99], [50148, 92], [50448, 87], [50749, 84], [51049, 82], [51350, 79], [51650, 77], [51950, 75], [52251, 72], [52551, 71], [52852, 69], [53152, 68], [53453, 66], [53753, 65], [54053, 64], [54354, 62], [54654, 61], [54955, 59], [55255, 59], [55555, 58], [55856, 56], [56156, 56], [56457, 54], [56757, 54], [57058, 52], [57358, 52], [57658, 51], [57959, 50], [58259, 49], [58560, 48], [58860, 48], [59161, 46], [59461, 46], [59761, 46], [60062, 44], [60362, 44], [60663, 42]], "point": [89, 165]}}, "high_idx": 5}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Cup|+02.30|+00.94|+00.81", "placeStationary": true, "receptacleObjectId": "Cabinet|+01.76|+00.39|+02.35"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [21, 130, 144, 289], "mask": [[38732, 2], [39030, 114], [39330, 5], [39336, 108], [39630, 114], [39929, 7], [39937, 107], [40229, 115], [40529, 115], [40829, 8], [40838, 107], [41129, 116], [41429, 9], [41439, 106], [41729, 116], [42029, 10], [42040, 105], [42329, 10], [42340, 105], [42629, 116], [42929, 11], [42941, 104], [43228, 117], [43528, 13], [43542, 103], [43828, 117], [44128, 117], [44428, 14], [44443, 102], [44728, 117], [45028, 15], [45044, 101], [45328, 117], [45628, 117], [45928, 16], [45945, 100], [46228, 117], [46527, 18], [46546, 99], [46827, 118], [47127, 118], [47427, 19], [47447, 98], [47727, 118], [48027, 20], [48048, 97], [48327, 118], [48627, 21], [48649, 96], [48927, 118], [49227, 118], [49527, 22], [49550, 95], [49826, 119], [50126, 24], [50151, 49], [50218, 22], [50426, 74], [50518, 17], [50726, 73], [50818, 15], [51026, 25], [51052, 47], [51118, 13], [51326, 73], [51418, 11], [51626, 26], [51653, 46], [51718, 9], [51926, 72], [52018, 7], [52226, 72], [52318, 5], [52526, 27], [52554, 44], [52618, 4], [52826, 72], [52917, 4], [53125, 29], [53155, 43], [53208, 12], [53425, 74], [53508, 11], [53725, 30], [53756, 44], [53808, 10], [54025, 75], [54109, 8], [54325, 75], [54409, 7], [54625, 31], [54657, 43], [54710, 5], [54925, 75], [55010, 4], [55225, 32], [55258, 42], [55310, 4], [55525, 75], [55611, 2], [55825, 75], [55911, 1], [56125, 33], [56159, 41], [56211, 1], [56424, 76], [56724, 35], [56760, 41], [57024, 77], [57324, 77], [57624, 36], [57661, 41], [57924, 79], [58008, 1], [58224, 37], [58262, 46], [58524, 84], [58824, 38], [58863, 45], [59124, 83], [59424, 83], [59723, 40], [59764, 43], [60023, 41], [60323, 41], [60623, 41], [60923, 41], [61223, 41], [61523, 41], [61823, 41], [62123, 42], [62423, 42], [62723, 42], [63022, 43], [63322, 43], [63622, 43], [63922, 43], [64222, 43], [64522, 43], [64822, 43], [65122, 43], [65422, 43], [65722, 43], [66022, 43], [66321, 44], [66621, 44], [66921, 44], [67221, 44], [67521, 44], [67821, 44], [68121, 44], [68421, 44], [68721, 44], [69021, 44], [69322, 43], [69622, 44], [69923, 43], [70224, 42], [70525, 41], [70825, 41], [71126, 40], [71427, 39], [71728, 38], [72028, 38], [72329, 37], [72630, 36], [72930, 36], [73231, 35], [73532, 34], [73833, 33], [74133, 33], [74434, 32], [74735, 31], [75035, 31], [75336, 30], [75637, 29], [75938, 28], [76238, 28], [76539, 27], [76840, 27], [77140, 27], [77441, 26], [77742, 25], [78043, 24], [78343, 24], [78644, 23], [78945, 22], [79245, 22], [79546, 21], [79847, 20], [80148, 19], [80448, 19], [80749, 18], [81050, 17], [81351, 16], [81651, 16], [81952, 15], [82253, 14], [82553, 14], [82854, 13], [83155, 12], [83456, 11], [83756, 11], [84057, 11], [84358, 10], [84658, 10], [84959, 9], [85260, 8], [85561, 7], [85861, 7], [86162, 6], [86463, 5]], "point": [82, 199]}}, "high_idx": 5}, {"api_action": {"action": "CloseObject", "objectId": "Cabinet|+01.76|+00.39|+02.35"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [21, 130, 145, 289], "mask": [[38732, 2], [39030, 114], [39330, 5], [39336, 108], [39630, 114], [39929, 7], [39937, 107], [40229, 115], [40529, 115], [40829, 8], [40838, 107], [41129, 116], [41429, 9], [41439, 106], [41729, 116], [42029, 10], [42040, 105], [42329, 10], [42340, 105], [42629, 116], [42929, 11], [42941, 104], [43228, 117], [43528, 13], [43542, 103], [43828, 117], [44128, 117], [44428, 14], [44443, 102], [44728, 117], [45028, 15], [45044, 101], [45328, 117], [45628, 117], [45928, 16], [45945, 100], [46228, 117], [46527, 18], [46546, 99], [46827, 118], [47127, 118], [47427, 19], [47447, 98], [47727, 118], [48027, 20], [48048, 97], [48327, 118], [48627, 21], [48649, 96], [48927, 118], [49227, 118], [49527, 22], [49550, 95], [49826, 119], [50126, 24], [50151, 49], [50218, 27], [50426, 74], [50518, 27], [50726, 73], [50818, 27], [51026, 25], [51052, 47], [51118, 27], [51326, 53], [51387, 12], [51418, 27], [51626, 26], [51653, 25], [51688, 11], [51718, 27], [51926, 51], [51989, 9], [52018, 27], [52226, 50], [52290, 8], [52318, 27], [52526, 27], [52554, 22], [52590, 8], [52618, 27], [52826, 49], [52891, 7], [52917, 28], [53125, 29], [53155, 20], [53191, 7], [53208, 37], [53425, 50], [53491, 8], [53508, 37], [53725, 30], [53756, 19], [53791, 9], [53808, 37], [54025, 50], [54091, 9], [54109, 36], [54325, 50], [54391, 9], [54409, 36], [54625, 31], [54657, 19], [54691, 9], [54710, 35], [54925, 51], [54991, 9], [55010, 35], [55225, 32], [55258, 19], [55290, 10], [55310, 35], [55525, 52], [55591, 9], [55611, 34], [55825, 53], [55892, 8], [55911, 35], [56125, 33], [56159, 21], [56193, 7], [56211, 35], [56424, 56], [56493, 7], [56511, 35], [56724, 35], [56760, 20], [56793, 8], [56811, 35], [57024, 56], [57092, 9], [57111, 35], [57324, 56], [57392, 9], [57410, 36], [57624, 36], [57661, 19], [57691, 11], [57709, 37], [57924, 57], [57990, 13], [58008, 38], [58224, 37], [58262, 21], [58288, 58], [58524, 122], [58824, 38], [58863, 83], [59124, 122], [59424, 122], [59723, 40], [59764, 82], [60023, 41], [60323, 41], [60623, 41], [60923, 41], [61223, 41], [61523, 41], [61823, 41], [62123, 42], [62423, 42], [62723, 42], [63022, 43], [63322, 43], [63622, 43], [63922, 43], [64222, 43], [64522, 43], [64822, 43], [65122, 43], [65422, 43], [65722, 43], [66022, 43], [66321, 44], [66621, 44], [66921, 44], [67221, 44], [67521, 44], [67821, 44], [68121, 44], [68421, 44], [68721, 44], [69021, 44], [69322, 43], [69622, 44], [69923, 43], [70224, 42], [70525, 41], [70825, 41], [71126, 40], [71427, 39], [71728, 38], [72028, 38], [72329, 37], [72630, 36], [72930, 36], [73231, 35], [73532, 34], [73833, 33], [74133, 33], [74434, 32], [74735, 31], [75035, 31], [75336, 30], [75637, 29], [75938, 28], [76238, 28], [76539, 27], [76840, 27], [77140, 27], [77441, 26], [77742, 25], [78043, 24], [78343, 24], [78644, 23], [78945, 22], [79245, 22], [79546, 21], [79847, 20], [80148, 19], [80448, 19], [80749, 18], [81050, 17], [81351, 16], [81651, 16], [81952, 15], [82253, 14], [82553, 14], [82854, 13], [83155, 12], [83456, 11], [83756, 11], [84057, 11], [84358, 10], [84658, 10], [84959, 9], [85260, 8], [85561, 7], [85861, 7], [86162, 6], [86463, 5]], "point": [83, 199]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan27", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 90, "x": 1.0, "y": 0.9010001, "z": 0.0}, "object_poses": [{"objectName": "WineBottle_2a35bd39", "position": {"x": 2.22505331, "y": 0.9392287, "z": 0.3613872}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Ladle_158e5727", "position": {"x": 1.85013545, "y": 0.791169465, "z": 1.92287481}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_6b8b37c4", "position": {"x": 2.002363, "y": 0.08177525, "z": 2.05768847}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a2d86e36", "position": {"x": 1.91749084, "y": 0.9383421, "z": 0.5837}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Spoon_9b26f0a9", "position": {"x": 0.00205624476, "y": 0.747452855, "z": 2.19738054}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_3133524b", "position": {"x": 0.6297573, "y": 0.795938134, "z": 2.64}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_343518b0", "position": {"x": 2.30429721, "y": 0.980000556, "z": 2.06362557}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7054e289", "position": {"x": 2.11626387, "y": 0.8591789, "z": -0.369625032}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Cup_7054e289", "position": {"x": 2.30194378, "y": 0.9365654, "z": 0.8060128}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_540cf8b1", "position": {"x": -0.119527027, "y": 0.816582, "z": 1.38609231}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_fac53a9c", "position": {"x": -0.349214524, "y": 0.8372559, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Tomato_fac53a9c", "position": {"x": 2.07607532, "y": 1.56550562, "z": -0.007124841}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": 2.02334619, "y": 0.33540082, "z": -0.09774998}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": 2.112262, "y": 1.3181982, "z": -0.369625032}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "SaltShaker_8e528c3b", "position": {"x": 1.84060025, "y": 0.9333, "z": 0.509595752}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_8e528c3b", "position": {"x": -0.425777048, "y": 0.7859637, "z": 1.049764}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": -0.195348382, "y": 0.9326999, "z": 2.451735}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": 1.84949386, "y": 0.07721096, "z": 2.05768847}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_598ee640", "position": {"x": -0.119527027, "y": 0.8527478, "z": 1.049764}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_598ee640", "position": {"x": 2.17974544, "y": 1.58099759, "z": -0.278999954}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Mug_5c2a3ea2", "position": {"x": 2.06402683, "y": 0.9892, "z": 2.4621613}, "rotation": {"x": 0.0, "y": 225.000214, "z": 0.0}}, {"objectName": "Mug_5c2a3ea2", "position": {"x": 2.20738077, "y": 0.5974135, "z": -0.00712481141}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_92427322", "position": {"x": -0.0429645181, "y": 0.840994835, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_92427322", "position": {"x": 0.17437005, "y": 0.987728, "z": 2.75788069}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_343518b0", "position": {"x": 1.22382593, "y": 0.837689161, "z": 2.52944}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_598ee640", "position": {"x": 1.55612528, "y": 0.9994841, "z": 2.51175642}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Fork_3133524b", "position": {"x": -0.04289989, "y": 0.7826108, "z": 0.37707752}, "rotation": {"x": 359.833344, "y": 0.00125360617, "z": 359.6518}}, {"objectName": "SoapBottle_e54ab207", "position": {"x": -0.0429644138, "y": 0.7820512, "z": 0.8815997}, "rotation": {"x": -0.00102988514, "y": -0.000124299477, "z": -0.000174416229}}, {"objectName": "Ladle_158e5727", "position": {"x": 1.9069, "y": 0.790092766, "z": 0.4460751}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Ladle_741f0242", "position": {"x": -0.0429645181, "y": 0.835329235, "z": 0.545271635}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bread_4be1a058", "position": {"x": -0.203237265, "y": 0.8245511, "z": 0.8132676}, "rotation": {"x": 0.391833246, "y": 359.740143, "z": 0.447935969}}, {"objectName": "SaltShaker_8e528c3b", "position": {"x": -0.3961984, "y": 0.9327, "z": 2.82771134}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_d7da2d9d", "position": {"x": 2.17974544, "y": 1.55944216, "z": -0.46025005}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Pan_f15c1655", "position": {"x": 2.1402, "y": 0.9351, "z": 1.5045}, "rotation": {"x": 0.0, "y": 180.000336, "z": 0.0}}, {"objectName": "WineBottle_2a35bd39", "position": {"x": 0.060256362, "y": 0.9386287, "z": 2.54838872}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_db580e26", "position": {"x": -0.349214524, "y": 0.8059667, "z": 1.049764}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_540cf8b1", "position": {"x": 2.02440429, "y": 0.7713503, "z": 2.19782972}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Tomato_fac53a9c", "position": {"x": 2.112262, "y": 1.36975622, "z": -0.09775029}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Apple_92427322", "position": {"x": -0.27265203, "y": 0.840991735, "z": 0.3771075}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "ButterKnife_a2d86e36", "position": {"x": -0.0429645032, "y": 0.782299, "z": 0.7134344}, "rotation": {"x": 359.993134, "y": -2.828302e-05, "z": -0.000184385746}}, {"objectName": "Pot_a2dda372", "position": {"x": 1.85448289, "y": 0.9379, "z": 1.497}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_1fe08306", "position": {"x": 1.84060025, "y": 0.933299959, "z": 0.657804251}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_7054e289", "position": {"x": 2.112262, "y": 1.31967878, "z": -0.5508751}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "DishSponge_6b8b37c4", "position": {"x": 1.11809385, "y": 0.7949528, "z": 2.58472013}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_922e45ca", "position": {"x": -0.312000215, "y": 0.7810681, "z": 0.4090001}, "rotation": {"x": -0.00171967619, "y": 0.000220757138, "z": 0.0006846211}}, {"objectName": "Spoon_9b26f0a9", "position": {"x": -0.196089536, "y": 0.7920931, "z": 1.21792817}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Mug_5c2a3ea2", "position": {"x": 2.24417472, "y": 1.49635053, "z": 2.51418519}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_dbaf3456", "position": {"x": -0.362220734, "y": 1.0325377, "z": 1.96106231}, "rotation": {"x": 0.0, "y": 89.99983, "z": 0.0}}], "object_toggles": [], "random_seed": 1908533914, "scene_num": 27}, "task_id": "trial_T20190908_124007_307383", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "A3HL2LL0LEPZT8_33NF62TLXMT6JOM0AEFSL2BWLWGKJW", "high_descs": ["Go left and forward to the counter with the toaster.", "Take the wine glass from the counter.", "Turn left, go forward to the sink, turn left, go forward to the microwave.", "Heat the glass in the microwave. Take the glass from the microwave. ", "Turn around, go forward a step.", "Put the glass in the bottom cabinet to the left of the oven to the left of the pepper mill."], "task_desc": "Put a hot wine glass in a cabinet.", "votes": [1, 1]}, {"assignment_id": "A2TUUIV61CR0C7_3LBXNTKX0UMNXC8YNL34M25VL0F9XB", "high_descs": ["Turn left, go slightly forward, then turn right toward the cabinet next to the refrigerator. ", "Pick up the wine glass at the left rear corner of the toaster.", "Turn left and walk to the sink, then turn left again and walk to the microwave.", "Open the microwave, place the wine glass inside, close the microwave and turn it on.  Turn it off again, open the microwave, remove the wine glass, and close the microwave door.", "Turn around so you are facing the stove and walk toward it.", "Open the cabinet door to the left of the stove, place the wine glass inside the cabinet to the left of the shaker in there, and close the cabinet door."], "task_desc": "Take the wine glass, warm it up in the microwave, and put it away.", "votes": [1, 1]}, {"assignment_id": "A2A028LRDJB7ZB_3YDTZAI2W07EA5HTZXM6ALQEUU8143", "high_descs": ["Turn left walk few steps then turn right to the toaster", "Pick up the glass beside the toaster", "Turn right then walk forward head to the microwave", "Open then microwave heat the glass then take it out and close the microwave", "Turn left move a few steps then turn left open the left side of the cabinet beside the oven", "Put the glass inside the cabinet then close it again"], "task_desc": "Put the heated glass in the cabinet", "votes": [1, 1]}]}}