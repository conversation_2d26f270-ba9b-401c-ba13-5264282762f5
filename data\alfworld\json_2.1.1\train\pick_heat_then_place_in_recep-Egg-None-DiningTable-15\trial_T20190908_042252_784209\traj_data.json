{"images": [{"high_idx": 0, "image_name": "000000000.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000001.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000002.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000003.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000004.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000005.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000006.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000007.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000008.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000009.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000010.png", "low_idx": 0}, {"high_idx": 0, "image_name": "000000011.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000012.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000013.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000014.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000015.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000016.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000017.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000018.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000019.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000020.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000021.png", "low_idx": 1}, {"high_idx": 0, "image_name": "000000022.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000023.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000024.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000025.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000026.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000027.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000028.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000029.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000030.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000031.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000032.png", "low_idx": 2}, {"high_idx": 0, "image_name": "000000033.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000034.png", "low_idx": 3}, {"high_idx": 0, "image_name": "000000035.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000036.png", "low_idx": 4}, {"high_idx": 0, "image_name": "000000037.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000038.png", "low_idx": 5}, {"high_idx": 0, "image_name": "000000039.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000040.png", "low_idx": 6}, {"high_idx": 0, "image_name": "000000041.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000042.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000043.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000044.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000045.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000046.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000047.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000048.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000049.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000050.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000051.png", "low_idx": 7}, {"high_idx": 0, "image_name": "000000052.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000053.png", "low_idx": 8}, {"high_idx": 0, "image_name": "000000054.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000055.png", "low_idx": 9}, {"high_idx": 0, "image_name": "000000056.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000057.png", "low_idx": 10}, {"high_idx": 0, "image_name": "000000058.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000059.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000060.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000061.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000062.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000063.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000064.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000065.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000066.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000067.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000068.png", "low_idx": 11}, {"high_idx": 0, "image_name": "000000069.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000070.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000071.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000072.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000073.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000074.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000075.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000076.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000077.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000078.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000079.png", "low_idx": 12}, {"high_idx": 0, "image_name": "000000080.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000081.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000082.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000083.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000084.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000085.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000086.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000087.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000088.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000089.png", "low_idx": 13}, {"high_idx": 0, "image_name": "000000090.png", "low_idx": 13}, {"high_idx": 1, "image_name": "000000091.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000092.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000093.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000094.png", "low_idx": 14}, {"high_idx": 1, "image_name": "000000095.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000096.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000097.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000098.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000099.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000100.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000101.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000102.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000103.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000104.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000105.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000106.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000107.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000108.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000109.png", "low_idx": 15}, {"high_idx": 1, "image_name": "000000110.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000111.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000112.png", "low_idx": 16}, {"high_idx": 1, "image_name": "000000113.png", "low_idx": 16}, {"high_idx": 2, "image_name": "000000114.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000115.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000116.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000117.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000118.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000119.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000120.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000121.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000122.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000123.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000124.png", "low_idx": 17}, {"high_idx": 2, "image_name": "000000125.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000126.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000127.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000128.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000129.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000130.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000131.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000132.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000133.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000134.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000135.png", "low_idx": 18}, {"high_idx": 2, "image_name": "000000136.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000137.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000138.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000139.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000140.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000141.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000142.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000143.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000144.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000145.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000146.png", "low_idx": 19}, {"high_idx": 2, "image_name": "000000147.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000148.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000149.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000150.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000151.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000152.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000153.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000154.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000155.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000156.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000157.png", "low_idx": 20}, {"high_idx": 2, "image_name": "000000158.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000159.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000160.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000161.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000162.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000163.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000164.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000165.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000166.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000167.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000168.png", "low_idx": 21}, {"high_idx": 2, "image_name": "000000169.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000170.png", "low_idx": 22}, {"high_idx": 2, "image_name": "000000171.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000172.png", "low_idx": 23}, {"high_idx": 2, "image_name": "000000173.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000174.png", "low_idx": 24}, {"high_idx": 2, "image_name": "000000175.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000176.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000177.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000178.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000179.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000180.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000181.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000182.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000183.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000184.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000185.png", "low_idx": 25}, {"high_idx": 2, "image_name": "000000186.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000187.png", "low_idx": 26}, {"high_idx": 2, "image_name": "000000188.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000189.png", "low_idx": 27}, {"high_idx": 2, "image_name": "000000190.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000191.png", "low_idx": 28}, {"high_idx": 2, "image_name": "000000192.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000193.png", "low_idx": 29}, {"high_idx": 2, "image_name": "000000194.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000195.png", "low_idx": 30}, {"high_idx": 2, "image_name": "000000196.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000197.png", "low_idx": 31}, {"high_idx": 2, "image_name": "000000198.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000199.png", "low_idx": 32}, {"high_idx": 2, "image_name": "000000200.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000201.png", "low_idx": 33}, {"high_idx": 2, "image_name": "000000202.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000203.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000204.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000205.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000206.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000207.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000208.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000209.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000210.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000211.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000212.png", "low_idx": 34}, {"high_idx": 2, "image_name": "000000213.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000214.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000215.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000216.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000217.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000218.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000219.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000220.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000221.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000222.png", "low_idx": 35}, {"high_idx": 2, "image_name": "000000223.png", "low_idx": 35}, {"high_idx": 3, "image_name": "000000224.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000225.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000226.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000227.png", "low_idx": 36}, {"high_idx": 3, "image_name": "000000228.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000229.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000230.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000231.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000232.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000233.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000234.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000235.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000236.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000237.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000238.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000239.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000240.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000241.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000242.png", "low_idx": 37}, {"high_idx": 3, "image_name": "000000243.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000244.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000245.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000246.png", "low_idx": 38}, {"high_idx": 3, "image_name": "000000247.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000248.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000249.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000250.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000251.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000252.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000253.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000254.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000255.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000256.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000257.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000258.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000259.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000260.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000261.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000262.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000263.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000264.png", "low_idx": 39}, {"high_idx": 3, "image_name": "000000265.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000266.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000267.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000268.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000269.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000270.png", "low_idx": 40}, {"high_idx": 3, "image_name": "000000271.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000272.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000273.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000274.png", "low_idx": 41}, {"high_idx": 3, "image_name": "000000275.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000276.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000277.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000278.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000279.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000280.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000281.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000282.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000283.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000284.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000285.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000286.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000287.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000288.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000289.png", "low_idx": 42}, {"high_idx": 3, "image_name": "000000290.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000291.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000292.png", "low_idx": 43}, {"high_idx": 3, "image_name": "000000293.png", "low_idx": 43}, {"high_idx": 4, "image_name": "000000294.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000295.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000296.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000297.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000298.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000299.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000300.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000301.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000302.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000303.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000304.png", "low_idx": 44}, {"high_idx": 4, "image_name": "000000305.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000306.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000307.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000308.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000309.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000310.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000311.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000312.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000313.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000314.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000315.png", "low_idx": 45}, {"high_idx": 4, "image_name": "000000316.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000317.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000318.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000319.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000320.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000321.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000322.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000323.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000324.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000325.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000326.png", "low_idx": 46}, {"high_idx": 4, "image_name": "000000327.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000328.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000329.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000330.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000331.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000332.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000333.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000334.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000335.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000336.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000337.png", "low_idx": 47}, {"high_idx": 4, "image_name": "000000338.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000339.png", "low_idx": 48}, {"high_idx": 4, "image_name": "000000340.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000341.png", "low_idx": 49}, {"high_idx": 4, "image_name": "000000342.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000343.png", "low_idx": 50}, {"high_idx": 4, "image_name": "000000344.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000345.png", "low_idx": 51}, {"high_idx": 4, "image_name": "000000346.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000347.png", "low_idx": 52}, {"high_idx": 4, "image_name": "000000348.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000349.png", "low_idx": 53}, {"high_idx": 4, "image_name": "000000350.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000351.png", "low_idx": 54}, {"high_idx": 4, "image_name": "000000352.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000353.png", "low_idx": 55}, {"high_idx": 4, "image_name": "000000354.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000355.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000356.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000357.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000358.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000359.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000360.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000361.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000362.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000363.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000364.png", "low_idx": 56}, {"high_idx": 4, "image_name": "000000365.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000366.png", "low_idx": 57}, {"high_idx": 4, "image_name": "000000367.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000368.png", "low_idx": 58}, {"high_idx": 4, "image_name": "000000369.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000370.png", "low_idx": 59}, {"high_idx": 4, "image_name": "000000371.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000372.png", "low_idx": 60}, {"high_idx": 4, "image_name": "000000373.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000374.png", "low_idx": 61}, {"high_idx": 4, "image_name": "000000375.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000376.png", "low_idx": 62}, {"high_idx": 4, "image_name": "000000377.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000378.png", "low_idx": 63}, {"high_idx": 4, "image_name": "000000379.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000380.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000381.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000382.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000383.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000384.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000385.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000386.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000387.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000388.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000389.png", "low_idx": 64}, {"high_idx": 4, "image_name": "000000390.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000391.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000392.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000393.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000394.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000395.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000396.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000397.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000398.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000399.png", "low_idx": 65}, {"high_idx": 4, "image_name": "000000400.png", "low_idx": 65}, {"high_idx": 5, "image_name": "000000401.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000402.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000403.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000404.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000405.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000406.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000407.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000408.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000409.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000410.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000411.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000412.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000413.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000414.png", "low_idx": 66}, {"high_idx": 5, "image_name": "000000415.png", "low_idx": 66}], "pddl_params": {"mrecep_target": "", "object_sliced": false, "object_target": "Egg", "parent_target": "DiningTable", "toggle_target": ""}, "plan": {"high_pddl": [{"discrete_action": {"action": "GotoLocation", "args": ["fridge"]}, "high_idx": 0, "planner_action": {"action": "GotoLocation", "location": "loc|-2|12|0|0"}}, {"discrete_action": {"action": "PickupObject", "args": ["egg"]}, "high_idx": 1, "planner_action": {"action": "PickupObject", "coordinateObjectId": ["Egg", [-1.760003448, -1.760003448, 16.545508, 16.545508, 6.30760096, 6.30760096]], "coordinateReceptacleObjectId": ["<PERSON><PERSON>", [-1.76, -1.76, 15.988, 15.988, 0.0562069416, 0.0562069416]], "forceVisible": true, "objectId": "Egg|-00.44|+01.58|+04.14"}}, {"discrete_action": {"action": "GotoLocation", "args": ["microwave"]}, "high_idx": 2, "planner_action": {"action": "GotoLocation", "location": "loc|-10|9|3|15"}}, {"discrete_action": {"action": "HeatObject", "args": ["egg"]}, "high_idx": 3, "planner_action": {"action": "HeatObject", "coordinateReceptacleObjectId": ["Microwave", [-13.94, -13.94, 8.732, 8.732, 5.723384, 5.723384]], "forceVisible": true, "objectId": "Microwave|-03.49|+01.43|+02.18"}}, {"discrete_action": {"action": "GotoLocation", "args": ["diningtable"]}, "high_idx": 4, "planner_action": {"action": "GotoLocation", "location": "loc|-2|2|3|60"}}, {"discrete_action": {"action": "PutObject", "args": ["egg", "diningtable"]}, "high_idx": 5, "planner_action": {"action": "PutObject", "coordinateObjectId": ["Egg", [-1.760003448, -1.760003448, 16.545508, 16.545508, 6.30760096, 6.30760096]], "coordinateReceptacleObjectId": ["DiningTable", [-5.38, -5.38, 1.332, 1.332, 0.0562074184, 0.0562074184]], "forceVisible": true, "objectId": "Egg|-00.44|+01.58|+04.14", "receptacleObjectId": "DiningTable|-01.35|+00.01|+00.33"}}, {"discrete_action": {"action": "NoOp", "args": []}, "high_idx": 6, "planner_action": {"action": "End", "value": 1}}], "low_actions": [{"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 0}, {"api_action": {"action": "OpenObject", "objectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [0, 11, 299, 300], "mask": [[3171, 10], [3448, 56], [3736, 81], [4029, 95], [4323, 107], [4617, 120], [4910, 133], [5204, 145], [5499, 156], [5794, 166], [6089, 176], [6384, 186], [6678, 197], [6973, 207], [7269, 216], [7565, 224], [7862, 230], [8158, 238], [8455, 244], [8751, 249], [9047, 253], [9344, 256], [9640, 260], [9937, 263], [10234, 266], [10531, 269], [10828, 272], [11124, 276], [11421, 279], [11718, 282], [12015, 285], [12312, 288], [12609, 291], [12906, 294], [13203, 297], [13501, 76499]], "point": [149, 154]}}, "high_idx": 1}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.44|+01.58|+04.14"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [157, 147, 170, 164], "mask": [[43963, 2], [44261, 6], [44560, 8], [44859, 10], [45158, 11], [45458, 12], [45757, 13], [46057, 13], [46357, 13], [46657, 14], [46957, 14], [47257, 13], [47557, 13], [47857, 13], [48158, 12], [48459, 10], [48759, 9], [49061, 6]], "point": [163, 154]}}, "high_idx": 1}, {"api_action": {"action": "CloseObject", "objectId": "<PERSON><PERSON>|-00.44|+00.01|+04.00"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 41, 299, 300], "mask": [[12157, 28], [12438, 68], [12729, 86], [13021, 103], [13313, 120], [13605, 136], [13898, 150], [14192, 162], [14487, 172], [14782, 183], [15076, 194], [15371, 204], [15665, 216], [15960, 226], [16256, 234], [16552, 242], [16847, 252], [17143, 257], [17438, 262], [17734, 266], [18031, 269], [18327, 273], [18624, 276], [18920, 280], [19217, 283], [19514, 286], [19810, 290], [20107, 293], [20404, 296], [20702, 48747], [69450, 295], [69755, 288], [70057, 285], [70359, 281], [70660, 279], [70961, 277], [71262, 275], [71563, 274], [71863, 273], [72164, 271], [72465, 270], [72765, 269], [73066, 268], [73366, 267], [73667, 266], [73967, 265], [74268, 264], [74568, 263], [74868, 263], [75169, 262], [75469, 262], [75769, 261], [76070, 260], [76370, 260], [76670, 260], [76970, 260], [77270, 260], [77570, 257], [77870, 255], [78170, 253], [78470, 252], [78770, 250], [79070, 249], [79370, 249], [79670, 248], [79970, 248], [80270, 247], [80570, 247], [80870, 246], [81169, 247], [81469, 247], [81769, 75], [81846, 169], [82068, 70], [82154, 161], [82368, 68], [82458, 157], [82668, 66], [82760, 154], [82967, 66], [83063, 151], [83266, 67], [83365, 149], [83566, 67], [83666, 148], [83865, 68], [83968, 146], [84164, 69], [84269, 145], [84463, 70], [84569, 145], [84763, 70], [84870, 143], [85064, 69], [85171, 142], [85363, 70], [85471, 142], [85663, 70], [85771, 142], [85963, 71], [86071, 142], [86263, 71], [86371, 142], [86562, 73], [86671, 142], [86862, 73], [86971, 142], [87162, 74], [87271, 143], [87462, 74], [87571, 143], [87761, 76], [87870, 144], [88061, 76], [88170, 144], [88361, 77], [88469, 145], [88660, 79], [88769, 145], [88960, 80], [89068, 146], [89259, 81], [89367, 147], [89559, 81], [89667, 148], [89858, 83], [89966, 34]], "point": [149, 169]}}, "high_idx": 1}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "LookUp", "forceAction": true}, "discrete_action": {"action": "LookUp_15", "args": {}}, "high_idx": 2}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [17, 14, 240, 126], "mask": [[3921, 168], [4090, 40], [4221, 168], [4390, 41], [4521, 210], [4820, 211], [5117, 224], [5417, 224], [5717, 224], [6017, 224], [6317, 224], [6618, 223], [6918, 223], [7218, 223], [7518, 223], [7818, 223], [8118, 222], [8418, 222], [8718, 222], [9018, 222], [9319, 221], [9619, 221], [9919, 221], [10219, 221], [10519, 221], [10819, 221], [11119, 221], [11419, 221], [11720, 219], [12020, 219], [12320, 219], [12620, 219], [12920, 219], [13220, 219], [13520, 219], [13820, 219], [14121, 218], [14421, 218], [14721, 218], [15021, 218], [15321, 217], [15621, 217], [15921, 217], [16221, 217], [16522, 216], [16822, 216], [17122, 216], [17422, 216], [17722, 216], [18022, 216], [18322, 216], [18622, 216], [18922, 215], [19223, 214], [19523, 214], [19823, 214], [20123, 214], [20423, 214], [20723, 214], [21023, 214], [21323, 214], [21624, 213], [21924, 213], [22224, 213], [22524, 212], [22824, 212], [23124, 212], [23424, 212], [23724, 212], [24025, 211], [24325, 211], [24625, 211], [24925, 211], [25225, 211], [25525, 211], [25825, 211], [26125, 210], [26425, 210], [26726, 209], [27026, 209], [27326, 209], [27626, 209], [27926, 209], [28226, 209], [28526, 209], [28826, 209], [29127, 208], [29427, 208], [29727, 207], [30027, 207], [30327, 207], [30627, 207], [30927, 207], [31227, 207], [31528, 206], [31828, 206], [32128, 206], [32428, 206], [32728, 206], [33028, 206], [33328, 205], [33628, 205], [33929, 204], [34229, 204], [34529, 204], [34829, 204], [35129, 204], [35429, 204], [35742, 178], [36062, 7], [36203, 7], [36362, 7], [36503, 6], [36662, 7], [36803, 6], [36962, 7], [37103, 6], [37262, 7], [37403, 6], [37563, 6], [37703, 6]], "point": [128, 69]}}, "high_idx": 3}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.44|+01.58|+04.14", "placeStationary": true, "receptacleObjectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 240, 131], "mask": [[0, 2], [300, 4], [600, 6], [900, 8], [1200, 9], [1500, 11], [1800, 13], [2100, 15], [2400, 17], [2700, 19], [3000, 21], [3300, 22], [3600, 24], [3900, 26], [4090, 40], [4200, 28], [4390, 41], [4500, 30], [4689, 42], [4800, 231], [5100, 241], [5400, 241], [5700, 241], [6000, 241], [6300, 241], [6600, 241], [6900, 241], [7200, 241], [7500, 241], [7800, 241], [8100, 240], [8400, 240], [8700, 240], [9000, 240], [9300, 240], [9600, 240], [9900, 240], [10200, 240], [10500, 240], [10800, 240], [11100, 240], [11400, 240], [11700, 239], [12000, 239], [12300, 239], [12600, 239], [12900, 239], [13200, 239], [13500, 239], [13800, 239], [14100, 239], [14400, 239], [14700, 239], [15000, 239], [15300, 238], [15600, 238], [15900, 238], [16200, 238], [16500, 238], [16800, 238], [17100, 238], [17400, 238], [17700, 238], [18000, 238], [18300, 238], [18600, 238], [18900, 237], [19200, 237], [19500, 89], [19605, 132], [19800, 85], [19913, 124], [20100, 85], [20217, 120], [20400, 85], [20519, 118], [20700, 85], [20814, 1], [20820, 117], [21000, 85], [21113, 4], [21120, 117], [21300, 85], [21413, 4], [21420, 10], [21456, 81], [21600, 85], [21713, 5], [21720, 5], [21762, 75], [21900, 85], [22013, 5], [22020, 5], [22062, 75], [22200, 85], [22313, 5], [22321, 4], [22362, 75], [22500, 85], [22614, 4], [22621, 4], [22662, 74], [22800, 85], [22914, 4], [22920, 5], [22962, 74], [23100, 85], [23214, 4], [23220, 5], [23262, 74], [23400, 86], [23514, 4], [23520, 5], [23562, 74], [23700, 86], [23814, 4], [23820, 5], [23861, 75], [24000, 86], [24114, 3], [24120, 6], [24161, 75], [24300, 86], [24414, 3], [24420, 6], [24461, 75], [24600, 86], [24714, 3], [24719, 8], [24760, 76], [24900, 86], [25014, 2], [25019, 9], [25059, 77], [25200, 86], [25314, 1], [25318, 10], [25359, 77], [25500, 86], [25618, 10], [25659, 77], [25800, 86], [25917, 12], [25958, 78], [26100, 86], [26216, 14], [26257, 78], [26400, 86], [26514, 17], [26556, 79], [26700, 86], [26814, 18], [26855, 80], [27000, 86], [27114, 18], [27155, 80], [27300, 86], [27414, 18], [27455, 80], [27600, 86], [27714, 21], [27752, 83], [27900, 86], [28014, 121], [28200, 87], [28314, 121], [28500, 87], [28614, 121], [28800, 235], [29100, 235], [29400, 235], [29700, 234], [30000, 234], [30300, 234], [30600, 234], [30900, 234], [31200, 234], [31500, 234], [31800, 234], [32100, 234], [32400, 234], [32700, 234], [33000, 234], [33300, 233], [33600, 233], [33900, 233], [34200, 233], [34500, 233], [34800, 233], [35100, 233], [35400, 233], [35700, 33], [35742, 178], [36000, 30], [36062, 7], [36203, 7], [36300, 27], [36362, 7], [36503, 6], [36600, 24], [36662, 7], [36803, 6], [36900, 21], [36962, 7], [37103, 6], [37200, 18], [37262, 7], [37403, 6], [37500, 15], [37563, 6], [37703, 6], [37800, 13], [38100, 10], [38400, 7], [38700, 4], [39000, 1]], "point": [120, 65]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 240, 131], "mask": [[0, 2], [300, 4], [600, 6], [900, 8], [1200, 9], [1500, 11], [1800, 13], [2100, 15], [2400, 17], [2700, 19], [3000, 21], [3300, 22], [3600, 24], [3900, 26], [4090, 40], [4200, 28], [4390, 41], [4500, 30], [4689, 42], [4800, 231], [5100, 241], [5400, 241], [5700, 241], [6000, 241], [6300, 241], [6600, 241], [6900, 241], [7200, 241], [7500, 241], [7800, 241], [8100, 240], [8400, 240], [8700, 240], [9000, 240], [9300, 240], [9600, 240], [9900, 240], [10200, 240], [10500, 240], [10800, 240], [11100, 240], [11400, 240], [11700, 239], [12000, 239], [12300, 239], [12600, 239], [12900, 239], [13200, 239], [13500, 239], [13800, 239], [14100, 239], [14400, 239], [14700, 239], [15000, 239], [15300, 238], [15600, 238], [15900, 238], [16200, 238], [16500, 238], [16800, 238], [17100, 238], [17400, 238], [17700, 238], [18000, 238], [18300, 238], [18600, 238], [18900, 237], [19200, 237], [19500, 89], [19605, 132], [19800, 85], [19913, 124], [20100, 85], [20217, 120], [20400, 85], [20519, 118], [20700, 85], [20814, 1], [20820, 117], [21000, 85], [21113, 4], [21120, 117], [21300, 85], [21413, 4], [21420, 10], [21456, 81], [21600, 85], [21713, 5], [21720, 5], [21762, 75], [21900, 85], [22013, 5], [22020, 5], [22062, 75], [22200, 85], [22313, 5], [22321, 4], [22362, 75], [22500, 85], [22614, 4], [22621, 4], [22662, 74], [22800, 85], [22914, 4], [22920, 5], [22962, 74], [23100, 85], [23214, 4], [23220, 5], [23262, 74], [23400, 86], [23514, 4], [23520, 5], [23563, 73], [23700, 86], [23814, 4], [23820, 5], [23864, 72], [24000, 86], [24114, 3], [24120, 6], [24164, 72], [24300, 86], [24414, 3], [24420, 6], [24465, 71], [24600, 86], [24714, 3], [24719, 8], [24765, 71], [24900, 86], [25014, 2], [25019, 9], [25066, 70], [25200, 86], [25314, 1], [25318, 10], [25366, 70], [25500, 86], [25618, 10], [25666, 70], [25800, 86], [25917, 12], [25966, 70], [26100, 86], [26216, 14], [26267, 68], [26400, 86], [26514, 17], [26567, 68], [26700, 86], [26814, 18], [26867, 68], [27000, 86], [27114, 18], [27167, 68], [27300, 86], [27414, 18], [27466, 69], [27600, 86], [27714, 21], [27766, 69], [27900, 86], [28014, 36], [28066, 69], [28200, 87], [28314, 36], [28366, 69], [28500, 87], [28614, 37], [28665, 70], [28800, 151], [28964, 71], [29100, 152], [29263, 72], [29400, 153], [29562, 73], [29700, 155], [29861, 73], [30000, 234], [30300, 234], [30600, 234], [30900, 234], [31200, 234], [31500, 234], [31800, 234], [32100, 234], [32400, 234], [32700, 234], [33000, 234], [33300, 233], [33600, 233], [33900, 233], [34200, 233], [34500, 233], [34800, 233], [35100, 233], [35400, 233], [35700, 33], [35742, 178], [36000, 30], [36062, 7], [36203, 7], [36300, 27], [36362, 7], [36503, 6], [36600, 24], [36662, 7], [36803, 6], [36900, 21], [36962, 7], [37103, 6], [37200, 18], [37262, 7], [37403, 6], [37500, 15], [37563, 6], [37703, 6], [37800, 13], [38100, 10], [38400, 7], [38700, 4], [39000, 1]], "point": [120, 65]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOn", "coordinateReceptacleObjectId": ["Microwave", [-13.94, -13.94, 8.732, 8.732, 5.723384, 5.723384]], "forceVisible": true, "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "ToggleObjectOn", "args": {"bbox": [17, 14, 240, 126], "mask": [[3921, 168], [4090, 40], [4221, 168], [4390, 41], [4521, 210], [4820, 211], [5117, 224], [5417, 224], [5717, 224], [6017, 224], [6317, 224], [6618, 223], [6918, 223], [7218, 223], [7518, 223], [7818, 223], [8118, 222], [8418, 222], [8718, 222], [9018, 222], [9319, 221], [9619, 221], [9919, 221], [10219, 221], [10519, 221], [10819, 221], [11119, 221], [11419, 221], [11720, 219], [12020, 219], [12320, 219], [12620, 219], [12920, 219], [13220, 219], [13520, 219], [13820, 219], [14121, 218], [14421, 218], [14721, 218], [15021, 218], [15321, 217], [15621, 217], [15921, 217], [16221, 217], [16522, 216], [16822, 216], [17122, 216], [17422, 216], [17722, 216], [18022, 216], [18322, 216], [18622, 216], [18922, 215], [19223, 214], [19523, 214], [19823, 214], [20123, 214], [20423, 214], [20723, 214], [21023, 214], [21323, 214], [21624, 213], [21924, 213], [22224, 213], [22524, 212], [22824, 212], [23124, 212], [23424, 212], [23724, 212], [24025, 211], [24325, 211], [24625, 211], [24925, 211], [25225, 211], [25525, 211], [25825, 211], [26125, 210], [26425, 210], [26726, 209], [27026, 209], [27326, 209], [27626, 209], [27926, 209], [28226, 209], [28526, 209], [28826, 209], [29127, 208], [29427, 208], [29727, 207], [30027, 207], [30327, 207], [30627, 207], [30927, 207], [31227, 207], [31528, 206], [31828, 206], [32128, 206], [32428, 206], [32728, 206], [33028, 206], [33328, 205], [33628, 205], [33929, 204], [34229, 204], [34529, 204], [34829, 204], [35129, 204], [35429, 204], [35742, 178], [36062, 7], [36203, 7], [36362, 7], [36503, 6], [36662, 7], [36803, 6], [36962, 7], [37103, 6], [37262, 7], [37403, 6], [37563, 6], [37703, 6]], "point": [128, 69]}}, "high_idx": 3}, {"api_action": {"action": "ToggleObjectOff", "coordinateReceptacleObjectId": ["Microwave", [-13.94, -13.94, 8.732, 8.732, 5.723384, 5.723384]], "forceVisible": true, "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "ToggleObjectOff", "args": {"bbox": [17, 14, 240, 126], "mask": [[3921, 168], [4090, 40], [4221, 168], [4390, 41], [4521, 210], [4820, 211], [5117, 224], [5417, 224], [5717, 224], [6017, 224], [6317, 224], [6618, 223], [6918, 223], [7218, 223], [7518, 223], [7818, 223], [8118, 222], [8418, 222], [8718, 222], [9018, 222], [9319, 221], [9619, 221], [9919, 221], [10219, 221], [10519, 221], [10819, 221], [11119, 221], [11419, 221], [11720, 219], [12020, 219], [12320, 219], [12620, 219], [12920, 219], [13220, 219], [13520, 219], [13820, 219], [14121, 218], [14421, 218], [14721, 218], [15021, 218], [15321, 217], [15621, 217], [15921, 217], [16221, 217], [16522, 216], [16822, 216], [17122, 216], [17422, 216], [17722, 216], [18022, 216], [18322, 216], [18622, 216], [18922, 215], [19223, 214], [19523, 214], [19823, 214], [20123, 214], [20423, 214], [20723, 214], [21023, 214], [21323, 214], [21624, 213], [21924, 213], [22224, 213], [22524, 212], [22824, 212], [23124, 212], [23424, 212], [23724, 212], [24025, 211], [24325, 211], [24625, 211], [24925, 211], [25225, 211], [25525, 211], [25825, 211], [26125, 210], [26425, 210], [26726, 209], [27026, 209], [27326, 209], [27626, 209], [27926, 209], [28226, 209], [28526, 209], [28826, 209], [29127, 208], [29427, 208], [29727, 207], [30027, 207], [30327, 207], [30627, 207], [30927, 207], [31227, 207], [31528, 206], [31828, 206], [32128, 206], [32428, 206], [32728, 206], [33028, 206], [33328, 205], [33628, 205], [33929, 204], [34229, 204], [34529, 204], [34829, 204], [35129, 204], [35429, 204], [35742, 178], [36062, 7], [36203, 7], [36362, 7], [36503, 6], [36662, 7], [36803, 6], [36962, 7], [37103, 6], [37262, 7], [37403, 6], [37563, 6], [37703, 6]], "point": [128, 69]}}, "high_idx": 3}, {"api_action": {"action": "OpenObject", "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "OpenObject", "args": {"bbox": [17, 14, 240, 126], "mask": [[3921, 168], [4090, 40], [4221, 168], [4390, 41], [4521, 210], [4820, 211], [5117, 224], [5417, 224], [5717, 224], [6017, 224], [6317, 224], [6618, 223], [6918, 223], [7218, 223], [7518, 223], [7818, 223], [8118, 222], [8418, 222], [8718, 222], [9018, 222], [9319, 221], [9619, 221], [9919, 221], [10219, 221], [10519, 221], [10819, 221], [11119, 221], [11419, 221], [11720, 219], [12020, 219], [12320, 219], [12620, 219], [12920, 219], [13220, 219], [13520, 219], [13820, 219], [14121, 218], [14421, 218], [14721, 218], [15021, 218], [15321, 217], [15621, 217], [15921, 217], [16221, 217], [16522, 216], [16822, 216], [17122, 216], [17422, 216], [17722, 216], [18022, 216], [18322, 216], [18622, 216], [18922, 215], [19223, 214], [19523, 214], [19823, 214], [20123, 214], [20423, 214], [20723, 214], [21023, 214], [21323, 214], [21624, 213], [21924, 213], [22224, 213], [22524, 212], [22824, 212], [23124, 212], [23424, 212], [23724, 212], [24025, 211], [24325, 211], [24625, 211], [24925, 211], [25225, 211], [25525, 211], [25825, 211], [26125, 210], [26425, 210], [26726, 209], [27026, 209], [27326, 209], [27626, 209], [27926, 209], [28226, 209], [28526, 209], [28826, 209], [29127, 208], [29427, 208], [29727, 207], [30027, 207], [30327, 207], [30627, 207], [30927, 207], [31227, 207], [31528, 206], [31828, 206], [32128, 206], [32428, 206], [32728, 206], [33028, 206], [33328, 205], [33628, 205], [33929, 204], [34229, 204], [34529, 204], [34829, 204], [35129, 204], [35429, 204], [35742, 178], [36062, 7], [36203, 7], [36362, 7], [36503, 6], [36662, 7], [36803, 6], [36962, 7], [37103, 6], [37262, 7], [37403, 6], [37563, 6], [37703, 6]], "point": [128, 69]}}, "high_idx": 3}, {"api_action": {"action": "PickupObject", "objectId": "Egg|-00.44|+01.58|+04.14"}, "discrete_action": {"action": "PickupObject", "args": {"bbox": [149, 77, 166, 100], "mask": [[22957, 2], [23254, 7], [23553, 10], [23852, 12], [24152, 12], [24451, 14], [24751, 14], [25050, 16], [25350, 16], [25649, 17], [25949, 17], [26249, 18], [26549, 18], [26849, 18], [27149, 18], [27449, 17], [27749, 17], [28050, 16], [28350, 16], [28651, 14], [28951, 13], [29252, 11], [29553, 9], [29855, 6]], "point": [157, 87]}}, "high_idx": 3}, {"api_action": {"action": "CloseObject", "objectId": "Microwave|-03.49|+01.43|+02.18"}, "discrete_action": {"action": "CloseObject", "args": {"bbox": [0, 1, 240, 131], "mask": [[0, 2], [300, 4], [600, 6], [900, 8], [1200, 9], [1500, 11], [1800, 13], [2100, 15], [2400, 17], [2700, 19], [3000, 21], [3300, 22], [3600, 24], [3900, 26], [4090, 40], [4200, 28], [4390, 41], [4500, 30], [4689, 42], [4800, 231], [5100, 241], [5400, 241], [5700, 241], [6000, 241], [6300, 241], [6600, 241], [6900, 241], [7200, 241], [7500, 241], [7800, 241], [8100, 240], [8400, 240], [8700, 240], [9000, 240], [9300, 240], [9600, 240], [9900, 240], [10200, 240], [10500, 240], [10800, 240], [11100, 240], [11400, 240], [11700, 239], [12000, 239], [12300, 239], [12600, 239], [12900, 239], [13200, 239], [13500, 239], [13800, 239], [14100, 239], [14400, 239], [14700, 239], [15000, 239], [15300, 238], [15600, 238], [15900, 238], [16200, 238], [16500, 238], [16800, 238], [17100, 238], [17400, 238], [17700, 238], [18000, 238], [18300, 238], [18600, 238], [18900, 237], [19200, 237], [19500, 89], [19605, 132], [19800, 85], [19913, 124], [20100, 85], [20217, 120], [20400, 85], [20519, 118], [20700, 85], [20814, 1], [20820, 117], [21000, 85], [21113, 4], [21120, 117], [21300, 85], [21413, 4], [21420, 10], [21456, 81], [21600, 85], [21713, 5], [21720, 5], [21762, 75], [21900, 85], [22013, 5], [22020, 5], [22062, 75], [22200, 85], [22313, 5], [22321, 4], [22362, 75], [22500, 85], [22614, 4], [22621, 4], [22662, 74], [22800, 85], [22914, 4], [22920, 5], [22962, 74], [23100, 85], [23214, 4], [23220, 5], [23262, 74], [23400, 86], [23514, 4], [23520, 5], [23562, 74], [23700, 86], [23814, 4], [23820, 5], [23861, 75], [24000, 86], [24114, 3], [24120, 6], [24161, 75], [24300, 86], [24414, 3], [24420, 6], [24461, 75], [24600, 86], [24714, 3], [24719, 8], [24760, 76], [24900, 86], [25014, 2], [25019, 9], [25059, 77], [25200, 86], [25314, 1], [25318, 10], [25359, 77], [25500, 86], [25618, 10], [25659, 77], [25800, 86], [25917, 12], [25958, 78], [26100, 86], [26216, 14], [26257, 78], [26400, 86], [26514, 17], [26556, 79], [26700, 86], [26814, 18], [26855, 80], [27000, 86], [27114, 18], [27155, 80], [27300, 86], [27414, 18], [27455, 80], [27600, 86], [27714, 21], [27752, 83], [27900, 86], [28014, 121], [28200, 87], [28314, 121], [28500, 87], [28614, 121], [28800, 235], [29100, 235], [29400, 235], [29700, 234], [30000, 234], [30300, 234], [30600, 234], [30900, 234], [31200, 234], [31500, 234], [31800, 234], [32100, 234], [32400, 234], [32700, 234], [33000, 234], [33300, 233], [33600, 233], [33900, 233], [34200, 233], [34500, 233], [34800, 233], [35100, 233], [35400, 233], [35700, 33], [35742, 178], [36000, 30], [36062, 7], [36203, 7], [36300, 27], [36362, 7], [36503, 6], [36600, 24], [36662, 7], [36803, 6], [36900, 21], [36962, 7], [37103, 6], [37200, 18], [37262, 7], [37403, 6], [37500, 15], [37563, 6], [37703, 6], [37800, 13], [38100, 10], [38400, 7], [38700, 4], [39000, 1]], "point": [120, 65]}}, "high_idx": 3}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateLeft", "forceAction": true}, "discrete_action": {"action": "RotateLeft_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "MoveAhead", "forceAction": true, "moveMagnitude": 0.25}, "discrete_action": {"action": "MoveAhead_25", "args": {}}, "high_idx": 4}, {"api_action": {"action": "RotateRight", "forceAction": true}, "discrete_action": {"action": "RotateRight_90", "args": {}}, "high_idx": 4}, {"api_action": {"action": "LookDown", "forceAction": true}, "discrete_action": {"action": "LookDown_15", "args": {}}, "high_idx": 4}, {"api_action": {"action": "PutObject", "forceAction": true, "objectId": "Egg|-00.44|+01.58|+04.14", "placeStationary": true, "receptacleObjectId": "DiningTable|-01.35|+00.01|+00.33"}, "discrete_action": {"action": "PutObject", "args": {"bbox": [0, 1, 201, 208], "mask": [[13, 25], [59, 12], [124, 2], [152, 32], [312, 26], [360, 12], [374, 2], [424, 2], [452, 32], [612, 27], [660, 12], [674, 1], [725, 1], [752, 32], [912, 27], [960, 12], [974, 1], [1025, 1], [1052, 32], [1211, 29], [1261, 11], [1273, 2], [1325, 1], [1352, 32], [1511, 29], [1560, 12], [1573, 2], [1625, 1], [1652, 32], [1810, 30], [1860, 11], [1873, 2], [1925, 1], [1952, 32], [2110, 31], [2160, 11], [2173, 3], [2225, 1], [2252, 33], [2409, 32], [2459, 12], [2473, 3], [2525, 1], [2552, 33], [2709, 33], [2758, 12], [2773, 3], [2825, 2], [2852, 33], [3008, 35], [3057, 13], [3073, 3], [3124, 3], [3152, 33], [3308, 36], [3354, 16], [3373, 3], [3424, 3], [3452, 33], [3607, 63], [3673, 3], [3724, 3], [3752, 33], [3907, 62], [3973, 3], [4024, 3], [4052, 33], [4207, 62], [4273, 2], [4324, 3], [4352, 33], [4506, 22], [4536, 33], [4573, 2], [4624, 3], [4652, 33], [4806, 18], [4839, 29], [4873, 2], [4924, 3], [4952, 34], [5105, 17], [5141, 27], [5173, 2], [5223, 4], [5252, 34], [5405, 15], [5443, 25], [5473, 2], [5523, 4], [5552, 34], [5704, 14], [5745, 23], [5773, 2], [5823, 4], [5852, 13], [5869, 17], [6004, 13], [6046, 21], [6072, 3], [6122, 5], [6152, 12], [6171, 15], [6303, 13], [6347, 21], [6372, 3], [6422, 6], [6452, 12], [6471, 15], [6603, 12], [6647, 22], [6671, 4], [6721, 7], [6752, 12], [6771, 15], [6902, 13], [6948, 27], [7021, 7], [7052, 12], [7071, 15], [7202, 12], [7248, 27], [7320, 8], [7352, 12], [7371, 15], [7502, 12], [7548, 27], [7620, 8], [7652, 11], [7671, 16], [7801, 13], [7848, 28], [7920, 8], [7952, 11], [7971, 16], [8101, 12], [8149, 27], [8220, 8], [8252, 10], [8272, 15], [8400, 13], [8449, 27], [8519, 1], [8572, 15], [8700, 13], [8749, 28], [8872, 15], [9000, 13], [9049, 28], [9172, 15], [9300, 14], [9349, 29], [9472, 15], [9600, 14], [9649, 29], [9752, 9], [9772, 15], [9900, 14], [9949, 30], [10052, 9], [10072, 15], [10200, 15], [10250, 29], [10327, 2], [10352, 9], [10372, 16], [10500, 15], [10550, 30], [10617, 12], [10652, 9], [10672, 16], [10800, 16], [10850, 31], [10916, 13], [10952, 9], [10972, 16], [11100, 17], [11150, 29], [11181, 1], [11216, 13], [11252, 9], [11272, 16], [11400, 17], [11450, 25], [11515, 14], [11552, 8], [11572, 16], [11700, 18], [11750, 22], [11813, 16], [11852, 8], [11872, 16], [12000, 19], [12051, 20], [12112, 17], [12152, 8], [12173, 15], [12300, 20], [12351, 18], [12394, 2], [12409, 20], [12452, 8], [12473, 15], [12600, 21], [12651, 17], [12695, 34], [12752, 8], [12773, 15], [12900, 22], [12953, 14], [12997, 32], [13052, 8], [13073, 16], [13200, 22], [13253, 13], [13297, 32], [13351, 8], [13373, 16], [13500, 23], [13553, 12], [13598, 32], [13651, 8], [13673, 16], [13800, 24], [13854, 10], [13898, 32], [13951, 9], [13972, 17], [14100, 23], [14154, 9], [14198, 32], [14251, 9], [14272, 17], [14400, 17], [14455, 8], [14499, 32], [14550, 11], [14571, 18], [14700, 16], [14744, 2], [14756, 7], [14799, 33], [14849, 13], [14870, 19], [15000, 15], [15042, 5], [15057, 5], [15099, 34], [15148, 41], [15300, 14], [15336, 10], [15357, 5], [15399, 36], [15446, 43], [15600, 15], [15636, 10], [15658, 4], [15699, 91], [15900, 14], [15937, 10], [15958, 4], [15999, 91], [16200, 13], [16238, 9], [16259, 4], [16299, 91], [16500, 13], [16538, 9], [16559, 4], [16599, 91], [16800, 12], [16838, 10], [16860, 3], [16899, 91], [17100, 12], [17139, 9], [17160, 4], [17199, 91], [17400, 12], [17439, 9], [17461, 3], [17499, 91], [17700, 12], [17739, 9], [17761, 3], [17799, 91], [18000, 12], [18039, 10], [18062, 3], [18099, 91], [18300, 12], [18339, 10], [18362, 3], [18399, 92], [18600, 12], [18639, 10], [18663, 3], [18699, 92], [18900, 13], [18939, 11], [18963, 4], [18999, 67], [19075, 16], [19200, 13], [19239, 11], [19264, 3], [19299, 64], [19377, 14], [19500, 13], [19539, 11], [19564, 4], [19599, 63], [19680, 11], [19800, 13], [19838, 13], [19865, 4], [19899, 61], [19981, 10], [20100, 13], [20138, 13], [20165, 4], [20198, 61], [20282, 9], [20400, 12], [20438, 13], [20465, 5], [20498, 60], [20583, 8], [20700, 12], [20738, 13], [20765, 6], [20798, 59], [20884, 7], [21000, 12], [21037, 15], [21065, 6], [21097, 59], [21184, 8], [21300, 11], [21317, 1], [21337, 15], [21396, 59], [21485, 7], [21600, 7], [21617, 2], [21636, 16], [21695, 60], [21786, 6], [21900, 5], [21917, 3], [21935, 20], [21993, 61], [22086, 6], [22200, 5], [22216, 7], [22234, 19], [22292, 61], [22387, 5], [22500, 5], [22516, 36], [22588, 65], [22687, 5], [22800, 4], [22816, 34], [22888, 65], [22987, 5], [23100, 4], [23116, 32], [23189, 63], [23288, 4], [23400, 4], [23416, 31], [23491, 61], [23588, 4], [23700, 3], [23715, 30], [23792, 60], [23888, 5], [24000, 3], [24015, 29], [24093, 58], [24188, 5], [24300, 3], [24315, 28], [24394, 57], [24488, 5], [24600, 3], [24615, 27], [24695, 56], [24788, 5], [24900, 3], [24915, 27], [24995, 56], [25088, 5], [25200, 2], [25214, 27], [25296, 55], [25388, 5], [25500, 2], [25514, 27], [25596, 55], [25688, 5], [25800, 2], [25814, 27], [25897, 54], [25988, 5], [26100, 2], [26114, 27], [26198, 53], [26287, 6], [26400, 2], [26413, 27], [26498, 53], [26587, 7], [26700, 1], [26713, 27], [26798, 53], [26887, 7], [27000, 1], [27013, 27], [27099, 52], [27186, 8], [27300, 1], [27313, 26], [27399, 52], [27486, 8], [27600, 1], [27613, 26], [27700, 52], [27785, 9], [27900, 1], [27912, 27], [28001, 51], [28085, 9], [28212, 26], [28301, 52], [28384, 10], [28512, 26], [28602, 51], [28683, 11], [28812, 26], [28902, 52], [28983, 11], [29111, 27], [29203, 52], [29282, 13], [29411, 27], [29503, 53], [29581, 14], [29711, 26], [29804, 53], [29880, 15], [30011, 26], [30104, 54], [30178, 17], [30310, 27], [30404, 56], [30477, 18], [30610, 27], [30705, 58], [30775, 20], [30909, 28], [31005, 62], [31071, 24], [31209, 28], [31305, 90], [31508, 29], [31605, 90], [31807, 30], [31905, 91], [32106, 31], [32205, 91], [32405, 32], [32505, 91], [32703, 35], [32805, 91], [33002, 36], [33105, 91], [33300, 38], [33404, 92], [33600, 38], [33704, 92], [33900, 39], [34004, 92], [34200, 39], [34303, 94], [34500, 40], [34603, 94], [34800, 40], [34902, 95], [35100, 40], [35202, 95], [35400, 41], [35501, 96], [35700, 41], [35801, 96], [36000, 41], [36100, 97], [36300, 42], [36400, 97], [36600, 42], [36701, 96], [36900, 43], [37011, 11], [37047, 51], [37206, 37], [37347, 51], [37647, 51], [37947, 51], [38109, 14], [38195, 29], [38246, 52], [38407, 24], [38494, 104], [38706, 37], [38793, 105], [39004, 43], [39091, 107], [39301, 47], [39390, 108], [39600, 48], [39689, 110], [39900, 49], [39988, 111], [40200, 49], [40286, 113], [40500, 50], [40584, 115], [40800, 50], [40882, 117], [41100, 51], [41181, 118], [41400, 52], [41480, 119], [41700, 53], [41778, 121], [42000, 54], [42077, 122], [42300, 55], [42375, 125], [42600, 56], [42673, 127], [42900, 57], [42971, 129], [43200, 59], [43268, 132], [43500, 200], [43800, 200], [44100, 200], [44400, 200], [44700, 200], [45000, 201], [45300, 201], [45600, 201], [45900, 201], [46200, 201], [46500, 201], [46800, 201], [47100, 201], [47400, 201], [47700, 202], [48000, 202], [48300, 202], [48600, 202], [48900, 202], [49200, 202], [49500, 202], [49800, 202], [50100, 202], [50400, 202], [50700, 201], [51023, 9], [51155, 6], [51324, 9], [51455, 6], [51625, 9], [51755, 6], [51926, 9], [52055, 6], [52227, 8], [52355, 6], [52528, 8], [52655, 6], [52829, 8], [52955, 6], [53130, 8], [53255, 5], [53431, 8], [53555, 5], [53732, 8], [53855, 5], [54033, 8], [54155, 5], [54334, 8], [54455, 5], [54635, 8], [54755, 5], [54936, 8], [55055, 5], [55237, 8], [55355, 5], [55538, 8], [55655, 5], [55839, 7], [55955, 5], [56140, 7], [56255, 5], [56441, 7], [56555, 4], [56742, 7], [56855, 4], [57043, 7], [57154, 5], [57344, 7], [57454, 5], [57645, 7], [57754, 5], [57946, 7], [58054, 5], [58247, 7], [58354, 5], [58548, 7], [58654, 5], [58849, 7], [58954, 5], [59150, 6], [59254, 5], [59451, 6], [59554, 5], [59752, 6], [59854, 5], [60053, 6], [60154, 4], [60354, 6], [60454, 4], [60655, 7], [60754, 4], [60956, 6], [61054, 5], [61256, 7], [61354, 5], [61557, 7], [61653, 5], [61858, 6], [61954, 4], [62159, 4], [62254, 4]], "point": [104, 101]}}, "high_idx": 5}]}, "scene": {"dirty_and_empty": false, "floor_plan": "FloorPlan15", "init_action": {"action": "TeleportFull", "horizon": 30, "rotateOnTeleport": true, "rotation": 270, "x": -1.5, "y": 0.914953351, "z": 2.25}, "object_poses": [{"objectName": "Potato_08de48c7", "position": {"x": -2.12637734, "y": 0.9478668, "z": 4.14679575}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Potato_08de48c7", "position": {"x": -1.79379869, "y": 0.821923, "z": 3.62537217}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "DishSponge_73731673", "position": {"x": -1.16132855, "y": 0.08615894, "z": 3.72432113}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "ButterKnife_657c04b2", "position": {"x": -2.40283132, "y": 0.915542, "z": 3.6751442}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -1.65551174, "y": 0.8924014, "z": 0.04945594}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -1.50461912, "y": 0.9164286, "z": 4.231027}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fd79f50b", "position": {"x": -3.65481567, "y": 0.9307999, "z": 1.10602665}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Spoon_58cf1806", "position": {"x": -3.36803436, "y": 0.9166294, "z": 3.66382527}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_58cf1806", "position": {"x": -1.65551162, "y": 0.892602, "z": 0.461618662}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Fork_72bf05b8", "position": {"x": -0.994908631, "y": 0.8920212, "z": 0.04945585}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pot_46b03e61", "position": {"x": -3.1432, "y": 0.9668, "z": 1.9715}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -1.43904543, "y": 0.9506632, "z": 3.78304267}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -0.440000862, "y": 1.57690024, "z": 4.136377}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -3.47555423, "y": 1.45890832, "z": 1.45450842}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -1.66600478, "y": 0.789343, "z": 3.662686}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_a5e935d4", "position": {"x": -3.27244067, "y": 0.9194459, "z": 2.84426212}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Plate_a5e935d4", "position": {"x": -3.176847, "y": 0.9197459, "z": 1.357}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Knife_4b738b3b", "position": {"x": -1.25914979, "y": 0.9150003, "z": -0.0535848141}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Knife_4b738b3b", "position": {"x": -1.49311328, "y": 0.8859901, "z": 0.46467486}, "rotation": {"x": 270.0, "y": 264.5338, "z": 0.0}}, {"objectName": "Tomato_66cbdca0", "position": {"x": -1.12702918, "y": 0.9601291, "z": 0.5646593}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Tomato_66cbdca0", "position": {"x": -3.55922174, "y": 0.9844565, "z": 1.27334213}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SoapBottle_da34a2c3", "position": {"x": -3.55922174, "y": 0.9157029, "z": 1.44065785}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Bowl_0e75bb4f", "position": {"x": -3.537117, "y": 1.54518771, "z": 2.22469831}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}, {"objectName": "SaltShaker_d1c7827d", "position": {"x": -3.18697357, "y": 0.7434514, "z": 3.32660341}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "SaltShaker_d1c7827d", "position": {"x": -3.47555423, "y": 1.45542765, "z": 1.15218687}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_94539d9a", "position": {"x": -1.3912704, "y": 0.8877788, "z": 0.5646593}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Lettuce_94931e65", "position": {"x": -0.5568124, "y": 1.0722692, "z": 3.98898935}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Mug_0af3d2bb", "position": {"x": -0.08956197, "y": 0.7178867, "z": 3.86935949}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Mug_0af3d2bb", "position": {"x": -3.45919847, "y": 1.99013674, "z": 2.537209}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_8bd3d3d2", "position": {"x": -1.3912704, "y": 0.951445639, "z": 0.04945591}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Egg_0c310241", "position": {"x": -1.1929, "y": 0.122954339, "z": 3.85227823}, "rotation": {"x": 0.0, "y": 270.0, "z": 0.0}}, {"objectName": "Knife_4b738b3b", "position": {"x": -3.463628, "y": 0.9393277, "z": 1.18968439}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Fork_72bf05b8", "position": {"x": -3.12439513, "y": 0.738989532, "z": 2.92970157}, "rotation": {"x": 0.114745982, "y": 135.8847, "z": 359.636658}}, {"objectName": "Bread_0fed2699", "position": {"x": -1.52304292, "y": 0.966316938, "z": 0.296989024}, "rotation": {"x": 1.2271117, "y": 279.173553, "z": 0.04918379}}, {"objectName": "Potato_08de48c7", "position": {"x": -1.25914979, "y": 0.9238395, "z": 0.04945588}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Plate_a5e935d4", "position": {"x": -3.0059998, "y": 0.904594362, "z": 3.677001}, "rotation": {"x": 0.0198281538, "y": -0.000184940931, "z": 0.00131354562}}, {"objectName": "Pot_46b03e61", "position": {"x": -3.4247, "y": 0.9668, "z": 2.3743}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Kettle_a09ab4be", "position": {"x": -3.145, "y": 0.06965663, "z": 2.9208}, "rotation": {"x": 0.0, "y": 270.000122, "z": 0.0}}, {"objectName": "WineBottle_2242b994", "position": {"x": -1.3912704, "y": 0.8924014, "z": 0.461618632}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Bowl_0e75bb4f", "position": {"x": -0.08956301, "y": 0.9851263, "z": 4.051571}, "rotation": {"x": 0.0, "y": 179.999664, "z": 0.0}}, {"objectName": "Tomato_66cbdca0", "position": {"x": -1.78763235, "y": 0.9601291, "z": 0.255537331}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Cup_69fc67c7", "position": {"x": -1.92159271, "y": 0.789343, "z": 3.774628}, "rotation": {"x": 1.40334191e-14, "y": 0.0, "z": 0.0}}, {"objectName": "Spatula_fd79f50b", "position": {"x": -3.36803436, "y": 0.9307999, "z": 1.27334213}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SoapBottle_da34a2c3", "position": {"x": -3.55922174, "y": 0.9157029, "z": 1.10602665}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Lettuce_94931e65", "position": {"x": -0.994908631, "y": 0.9775047, "z": 0.2555372}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "Pan_7e5e2cad", "position": {"x": -2.61477637, "y": 0.902653933, "z": 3.62041235}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Apple_8bd3d3d2", "position": {"x": -1.25914979, "y": 0.951445639, "z": 0.255537271}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "SaltShaker_d1c7827d", "position": {"x": -3.18504786, "y": 0.7413068, "z": 1.12590623}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "PepperShaker_94539d9a", "position": {"x": -1.25914979, "y": 0.8877788, "z": 0.152496576}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "ButterKnife_657c04b2", "position": {"x": -0.9949086, "y": 0.8915147, "z": 0.358577877}, "rotation": {"x": 0.0, "y": 90.0, "z": 0.0}}, {"objectName": "DishSponge_73731673", "position": {"x": -3.55922174, "y": 0.915364265, "z": 1.60797334}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0}}, {"objectName": "Spoon_58cf1806", "position": {"x": -1.65551162, "y": 0.892602, "z": 0.152496547}, "rotation": {"x": 0.0, "y": 180.0, "z": 0.0}}, {"objectName": "Mug_0af3d2bb", "position": {"x": -3.47386122, "y": 1.54129863, "z": 2.06840754}, "rotation": {"x": 0.0, "y": 89.99987, "z": 0.0}}], "object_toggles": [], "random_seed": 1959654887, "scene_num": 15}, "task_id": "trial_T20190908_042252_784209", "task_type": "pick_heat_then_place_in_recep", "turk_annotations": {"anns": [{"assignment_id": "AJQGWGESKQT4Y_35L9RVQFCRZGAPAOKKRSEQKDGF7UH2", "high_descs": ["Turn around and go left to face the fridge.", "Open the fridge and pick the egg up from the fridge shelf and shut the door.", "Go left and cross the room to stand in front of the stove.", "Put the egg in the microwave above the stove and shut the door and then open it and take the egg out and then shut the door.", "Turn around and cross the room and then go right and turn to the right to face the white table.", "Put the egg on the table to the left of the tomato."], "task_desc": "Put a heated egg on a table.", "votes": [1, 1]}, {"assignment_id": "ACSS93E03ZUGX_3K2755HG5VKAFR8W3ZD3154CE17FDP", "high_descs": ["Turn around and then go left to face the black refrigerator.", "Open the refrigerator, pick up the egg that's inside, and close the refrigerator.", "Turn to the left and go straight to the microwave above the stove top.", "Open the microwave, place the egg inside of it, close the microwave, wait a second, open the microwave, pick up the egg, and close the microwave.", "Turn around, go straight, and then move to the right to face the left end of the white table.", "Place the egg on the table."], "task_desc": "Place the microwaved egg on the white table.", "votes": [1, 1]}, {"assignment_id": "A3UF6XXFFRR237_3VNXK88KKFZN8YH5RYD5XVO8B999VH", "high_descs": ["walk over to the fridge", "open the fridge and remove the egg", "walk the egg over to the microwave above the stove", "cook the egg in the microwave with the mug then remove", "take the cooked egg to the left side of the white table", "place the egg on the table to the inside of the tomato"], "task_desc": "cook an egg in the microwave and put it on the table", "votes": [1, 1]}]}}